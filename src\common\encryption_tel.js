import {
  ajax,
  showModal,
  config
} from './index'
import store from '../store/index'

var num = 0;
var second = 30
let content = "", telnum = 0, type = "",staticInfoId=''
/**
 * 中间号拨打
 * @param {string||number} id  type 的id 置业顾问则是置业顾问id 家装则是家装店铺的id 其他则是用户id
 * @param {string||number} mid  type 的mid 家装则是家装店铺的id 其他则是对应用户的id
 * @param {string||number} tel   真实电话号码
 * @param {string} type   被叫号码的身份 经纪人：'agent'||3 置业顾问: 'adviser'||2 家装：'decoration'||5,楼盘：'build'||1 ，商业地产： 'estate'||6 ，住宅信息： 4
 * @param {string} from     1：经纪人详情（经纪人） 2： 置业顾问详情（ 置业顾问）3： 置业顾问列表（ 置业顾问） 4： 楼盘详情（ 置业顾问详情） 5： 出租房详情（ 经纪人） 6： 出售房详情（ 经纪人） 7： 聊天界面（ 置业顾问 + 经济人) 8：经纪人列表 9：楼盘活动页面 10： 家装公司 11：家装案例列表 12：家装案例详情 13：小区房价详情 15:求购列表 16：求租列表 17：资讯详情 18：预售列表 19：预售详情 20：预售榜单列表 21：楼市报页面  22： 土地列表 23： 土地详情 24： 土地榜单列表 25 搜索榜单 26：小区相册列表 27：VR详情 28：楼市圈  29：问答 30：荐房单 31：商业地产(出售) 32：商业地产（出租） 33：商业地产（转让）
 * @param {string} source  'list' 在经纪人 或 置业顾问 列表页“拨打电话”  'detail' 在经纪人 或 置业顾问 详情页“拨打电话”    非必须
 * @param {string} info_id  楼盘信息或房源信息 ID ，非必须
 * @param {string||number} isstatis  是否统计 默认统计 不统计的暂时为400号码
 * @param {string||number} bid  楼盘id
 * 
 *  @param {string||number} call_scene_id,//  普通会员独有参数 对应的信息ID call_scene_id
    @param {string||number} call_scene_type  //普通会员独有参数 call_scene_type : 1楼盘2小区
 */
// const encryptionTel = function(id, mid, tel, type, from, source = "", info_id = "", isstatis = 1, bid='', options={}) {
const encryptionTel = function(options={}) {
  let {id, mid, tel, type, from, source='', info_id='', isstatis=1, bid='',isShare=0,call_scene_type = 0,call_scene_id = 0 } = options
  store.state.allowOpen = true
  type = type
  uni.showLoading({
    title: "请稍侯"
  })
  if (from==4){
    staticInfoId = bid
  } else if (from == 5 || from == 6) {
    staticInfoId =info_id
  }
  if ((type =="agent" || type == 3)&&isShare ==1){
    info_id =0
  }
  var params = {
    type: type, //被叫号码的身份 经纪人：'agent'||3 置业顾问: 'adviser'||6 家装：'decoration'||5 ,楼盘：'build'||1
    callee_id: id, //置业顾问则是置业顾问id 家装则是家装店铺的id 其他则是用户id
    source: source, //'list' 在经纪人 或 置业顾问 列表页“拨打电话”  'detail' 在经纪人 或 置业顾问 详情页“拨打电话”    非必须
    info_id: info_id, //楼盘信息或房源信息 ID ，非必须，如果是经纪人分享后的页面info_id不能传，因为信息详情是拨打的信息内填写的电话号码（根据info_id查出来的）
    bid: bid, //楼盘id
    call_scene_id,  //  普通会员独有参数 对应的信息ID call_scene_id
    call_scene_type  //普通会员独有参数 call_scene_type : 1楼盘2小区
  }
  // if (from == 31 || from == 32 || from == 33) {  //商业地产
  //   params.type = 'estate'
  // }
  ajax.get("call/getVirtualNumber", params, res => {
    if (res.data.code == 1) {
      uni.hideLoading()
      if (res.data.realNumber) { //如果是真实号码则直接拨打
        if (params.type === 'agent' || params.type === 3 || params.type === 'estate' || params.type === 6) {
          showModal({
            title: '友情提示',
            content: "接通后，提及在" + (store.state.siteName || config.projectName) + "看到的信息，获得更好的服务！",
            confirm: () => {
              uni.makePhoneCall({
                phoneNumber: res.data.realNumber
              });
              if (isstatis == 1) {
                statistics(mid, from, staticInfoId)
              }
            }
          })
        } else {
          uni.makePhoneCall({
            phoneNumber: res.data.realNumber
          });
          if (isstatis == 1) {
            statistics(mid, from, staticInfoId)
          }
        }
        return
      }
      if(options.success){
        res.data.from = from
        res.data.mid = mid
        res.data.info_id = staticInfoId
        res.data.isstatis = isstatis
        options.success(res)
        return
      }
      if (res.data.expireSeconds) {
        num = res.data.expireSeconds + "秒后未拨打请返回重新拨打"
      } else {
        num = ""
      }

      uni.hideLoading();
      telnum = res.data.virtualNumber;
      if (type == 'agent' || type == 3) {
        content = res.data.tip||("接通后，提及在" + (store.state.siteName || config.projectName) + "看到的信息，获得更好的服务！")
      } else {
        content = res.data.tip ||("拨通后请说明来自" + (store.state.siteName || config.projectName) + " 本次通话已进行加密 " + num)
      }
      showModal({
        title: '友情提示',
        content: content,
        confirm: () => {
          uni.makePhoneCall({
            phoneNumber: telnum
          });
          if (isstatis == 1) {
            statistics(mid, from, staticInfoId)
          }

        }
      })
    } else if (res.data.code == 101) { //已经获取了且号码还有效
      uni.hideLoading();
      if (options.success) {
        res.data.from = from
        res.data.mid = mid
        res.data.info_id = staticInfoId
        res.data.isstatis = isstatis
        options.success(res)
        return
      }
      telnum = res.data.virtualNumber;
      uni.makePhoneCall({
        phoneNumber: telnum
      });
      if (isstatis == 1) {
        statistics(mid, from, staticInfoId)
      }

    } else if (res.data.code === -1 || res.data.code === 2||res.data.code === -5|| res.data.code === -10){
      uni.hideLoading();
      options.fail && options.fail(res)
    } else {
      uni.hideLoading();
      uni.showToast({
        title: res.data.msg,
        icon: 'none'
      })
    }
  }, err => { }, { disableAutoHandle: options.intercept_login||false})
  
}
const statistics = (mid, from, info_id) => {
  ajax.get("im/callUpStatistics", {
    id: mid,
    type: from,
    info_id
  }, res => {
    console.log(res.data);
  }, err => {
    console.log(err)
  }, { disableAutoHandle: true })
}
// const time =(num=30)=>{
//   if (timer) clearInterval(timer)
//   let timer = setInterval(() => {

//     if (num <=0){
//        clearInterval(timer)
//        return
//     } 
//     num -- 
//     second= num 
//     return  second
//   }, 1000);

// }
module.exports = encryptionTel