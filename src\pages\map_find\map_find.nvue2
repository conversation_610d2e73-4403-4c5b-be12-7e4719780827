<template>
<div class="find-map">
    <map id="my_map" ref="my_map" class="my-map" :style="{height:(window_height)-44+'px',width:window_width+'px'}" :scale="scale" :latitude="params.lat" :longitude="params.lng" :markers="covers" @regionchange="regionChange" @markertap="handelMark($event)" @callouttap="handelMark($event)"></map>
    <div class="list-item" @click="goTo()" v-if="item.id" :style="{width:window_width+'px'}">
        <div class="img-box list-img">
            <image class="img" :src="item.img | imgUrl" resize="cover" @load="onImageLoad"></image>
        </div>
        <div class="list-info">
            <div class="info-title-box"><text class="info-title">{{item.title}}</text></div>
            <div v-if="params.type==3" class="info-content-box"><text class="info-content">租{{item.count||0}}套</text></div>
            <div v-else-if="params.type==2" class="info-content-box"><text class="info-content">售{{item.count||0}}套</text></div>
            <div v-else-if="params.type==1" class="info-content-box"><text class="info-content">{{item.name}}</text></div>
            <div class="info-price hourse-price">
                <div class="price-box">
                    <text class="total text">{{(item.price&&item.price!='0')?item.price:(params.type==1?'待定':'面议')}}</text>
                    <text v-if="item.price&&item.price!='0'" class="acreage text">{{params.type==3?'元/月':'元/平米'}}</text>
                </div>
                <block v-if="item.house_status==1">
                    <div class="acreage">比上月</div>
                    <m-icon type="up" color="#f65354"></m-icon>
                    <div class="up">{{item.house_value}}%</div>
                </block>
                <block v-else-if="item.house_status==2">
                    <div class="acreage">比上月</div>
                    <m-icon type="down" color="#179B16"></m-icon>
                    <div class="down">{{item.house_value}}%</div>
                </block>
                <block v-else-if="item.house_status==0">
                    <div class="acreage">比上月</div>
                    <div class="acreage">持平</div>
                </block>

            </div>
            <div class="address-row">
                <div class="address"><text class="address-text">{{item.address}}</text></div>
                <div class="distance"><text class="address-text">{{item.distance}}km</text></div>
            </div>

        </div>
    </div>
	<div class="mask" @click="stopMove"  :style="{width:window_width+'px',height:show_mask?(systemInfo.windowHeight+'px'):'0px'}"></div>
	<scroller
	class="filter"
	:class="{show: nowTab === 1}"
	id="filter1"
	>
	<div class="cascade_box" :style="{width:window_width+'px'}">
		<div class="first cate">
			<div class="cascade_list">
				<div class="cascade_item"  v-for="item in first_area" :key="item.areaid" @click="onSelectArea(1,item)">
					<text  class="top_text" :class="{selected:selectted_area.length>0&&selectted_area[0]===item.areaid}">{{item.areaname}}</text>
				</div>
			</div>
		</div>
		<div class="second cate">
			<div class="cascade_list">
				<div class="cascade_item"  v-for="item in second_area" :key="item.areaid" @click="onSelectArea(2,item)"><text class="top_text" :class="{selected:selectted_area.length>1&&selectted_area[1]===item.areaid}">{{item.areaname}}</text></div>
			</div>
		</div>
		<div class="third cate">
			<div class="cascade_list">
				<div class="cascade_item"  v-for="item in third_area" :key="item.areaid" @click="onSelectArea(3,item)"><text class="top_text" :class="{selected:selectted_area.length>2&&selectted_area[2]===item.areaid}">{{item.areaname}}</text></div>
			</div>
		</div>
	</div>
	</scroller>
	<scroller
	class="filter"
	:class="{show: nowTab === 2}"
	id="filter2"
	>
	<div class="filter-list" :style="{width:systemInfo.safeArea.width+'px'}">
		<div class="filter-item" v-for="item in price" :key="item.id" @click="selectPrice(item.value, item.name)">
			<text class="my-tag top_text" :class="{'info':item.id != params.type}">{{item.name}}</text>
		</div>
	</div>
	</scroller>
	<scroller
	class="filter"
	:class="{show: nowTab === 3}" id="filter3">
	<div class="filter-list" :style="{width:systemInfo.safeArea.width+'px'}">
	<div class="filter-item" v-for="item in distance" :key="item.id" @click="selectDistance(item.value, item.name)" >
		<text class="my-tag top_text" :class="{'info':item.id != params.type}">{{item.name}}</text>
	</div>
	</div>
	</scroller>
	<scroller
	class="filter_more"
	:class="{show: nowTab === 4}"
	id="filter4"
	>
	<div class="filter-list filter-list_more" :style="{width:systemInfo.safeArea.width+'px'}">
		<div class="more-screen-item" >
			<text class="more_title">类型</text>
			<div class="options">
				<div class="options-item" @click="selectOption({type_id:item.id},'type_id')"  v-for="(item,index) in types" :key="index"><text class="opt_text" :class="params.type_id==item.id?'active_green':''">{{item.typename}}</text></div>
			</div>
		</div>
		<div class="more-screen-item">
			<text class="more_title">销售状态</text>
			<div class="options">
				<div class="options-item" @click="selectOption({status:item.id},'status')"  v-for="(item,index) in status" :key="index"><text class="opt_text" :class="params.status==item.id?'active_green':''">{{item.leixing}}</text></div>
			</div>
		</div>
		<div class="more-screen-item">
			<text class="more_title">装修</text>
			<div class="options">
				<div class="options-item" @click="selectOption({renovation_id:item.id},'renovation_id')"  v-for="(item,index) in renovation" :key="index"><text class="opt_text" :class="params.renovation_id==item.id?'active_green':''">{{item.jiaofang}}</text></div>
			</div>
		</div>
		<div class="more-screen-item">
			<text class="more_title">状态</text>
			<div class="options">
				<div class="options-item" @click="selectOption({progress_id:item.id},'progress_id')"  v-for="(item,index) in progress" :key="index"><text class="opt_text" :class="params.progress_id==item.id?'active_green':''">{{item.jindu}}</text></div>
			</div>
		</div>
		<div class="padding-20 btns">
			<text class ="btn btn_res" size="small" type="default" @click="resetMore">重置</text>
			<text class ="btn btn_sel" size="small" type="primary" @click="selectMore()">确定</text>
		</div>
	</div>
	</scroller>
	<div class="tabs" :style="{width:window_width+'px'}">
		<div v-for="tab in tabs" :key="tab.type" class="tab-item" @click="switchTab(tab)">
			<div class="tab_text-box" :class="{tab_text_box_active:params.type==tab.type}">
				<text class="tab_text" :class="{active:params.type==tab.type}">{{tab.name}}</text>
			</div>
		</div>
	</div>
	<div class="filter-tab" :style="{width:window_width+'px'}">
		
		<div  class="filter-tab-item" @click="switchTabInfo(1)">
			<text class ="filter_text">{{areaName }}</text>
			<text class="iconfont sanjiao">&#xe63d;</text>
		</div>
		<div v-if="params.type===1" class="filter-tab-item" @click="switchTabInfo(2)" >
			<text class ="filter_text">{{priceName }}</text>
			<text class="iconfont sanjiao">&#xe63d;</text>
		</div>
		<div class="filter-tab-item" @click="switchTabInfo(3)" >
			<text class ="filter_text">{{distanceName }}</text>
			<text class="iconfont sanjiao">&#xe63d;</text>
		</div>
		<div v-if="params.type===1" class="filter-tab-item filter-tab-item4" @click="switchTabInfo(4)" >
			<text class ="filter_text">更多</text>
			<text class="iconfont sanjiao">&#xe63d;</text>
		</div>
	</div>
	
	
</div>
</template>

<script>
import {
    ajax
} from '../../common/index.js'
import {
    navigateTo,
    debounce,
    formatImg
} from "../../common/index.js"
import { mapState } from 'vuex'
export default {
    data() {
      return {
				item: {},
				flag: false,
				tabs: [{
								type: 1,
								name: "新房"
						},
						{
								type: 2,
								name: "二手房"
						},
						{
								type: 3,
								name: "出租房"
						}
				],
				params: {
						lat: null,
						lng: null,
						distance: 3,
						type: 1
				},
				nowTab:0,
				areaName:"全部区域",
				priceName:'价格',
				distanceName:"距离",
				area_list:[],
				area:[],
				price:[],
				status:[],
				types:[],
				renovation:[],
				progress:[],
				distance:[],
				first_area:[],
				second_area:[],
				third_area:[],
				selectted_area:[''],
				scale: 14,
				covers: [],
				window_width:"",
				window_height:"",
				show_mask:false,
      }
    },
    created() {
        console.log(this.$store.state.systemInfo)
		this.window_width = this.$store.state.systemInfo.safeArea.width
		this.window_height = this.$store.state.systemInfo.safeArea.height
        this.getLocation()
		this.getScreen()
		
    },
    mounted() {},
    onReady() {
        this.map = uni.createMapContext("my_map", this);
        this.map.includePoints({
            padding: [20, 20, 20, 20]
        })
		var domModule = weex.requireModule("dom");
			domModule.addRule('fontFace', {  
					'fontFamily': 'iconfont',  /* project id 1117313 */
					'src':"url('http://at.alicdn.com/t/font_1117313_80ipebmwyye.ttf')",
			}) 
    },
    filters: {
        imgUrl(img) {
            return formatImg(img, 'w_320')
        }
    },
	computed:{
		...mapState(['systemInfo']),
	},
    methods: {
        switchTab(e) {
            this.item = {}
            this.params.type = e.type
			this.nowTab=0
            if (this.params.type === 1) {
                this.scale = 14
                this.params.distance = 3
            } else {
                this.scale = 15
                this.params.distance = 1
            }
            uni.showLoading({
                title: "正在获取房源",
                mask: true
            })
            // this.mapData.covers = []
            // this.map.clearOverlays();
            // this.map.ctx.attr.markers = []
            this.covers = []
            this.getLocation()
        },
		switchTabInfo(index){
			if(this.nowTab == index){
				this.show_mask=false
				this.nowTab = 0
			}else{
				this.show_mask =true
				this.nowTab = index
			}
		},
		getScreen(){
			ajax.get('build/buildCondition',{},(res)=>{
				res.data.area.push({areaid:'',parentid:0 ,mapx:'',mapy:'',areaname:"全部区域"})
				this.area_list =res.data.area
				this.creatArea(this.area_list)
				this.price = res.data.price
				this.distance = res.data.distance
				this.status = res.data.status
				this.types = res.data.types
				this.renovation = res.data.renovation
				this.progress = res.data.progress
				this.showTab=true
			},(err)=>{
				console.log(err)
			})
		},
		creatArea(arr){
			this.first_area = arr.filter(item=>item.parentid===0)
		},
		onSelectArea(i,e){
			let _this = this
			const getData = function(ev){
				if(ev.tx_mapx&&ev.tx_mapy){
					_this.params.lat = ev.tx_mapy
					_this.params.lng = ev.tx_mapx
				}else{
					_this.params.lat = _this.center_point[0]
					_this.params.lng = _this.center_point[1]
				}
				_this.params.areaid = ev.areaid
				_this.areaName = e.areaname
				_this.nowTab = 0
				_this.getCovers()
			}
			switch(i){
				case 1:
					this.selectted_area = [e.areaid]
					// 筛选出当前区域下的二级区域
					this.second_area = this.area_list.filter(item=>item.parentid===e.areaid)
					if(this.second_area.length===0){
						this.show_mask=false
						getData(e)
					}
					break;
				case 2:
					this.selectted_area[1] = e.areaid
					if(this.selectted_area.length>2){
						this.selectted_area.splice(2,1)
					}
					// 筛选出当前区域下的三级区域
					this.third_area = this.area_list.filter(item=>item.parentid===e.areaid)
					if(this.third_area.length===0){
						this.show_mask=false
						getData(e)
					}
					break;
				case 3:
					this.selectted_area[2] = e.areaid
					this.show_mask=false
					getData(e)
			}
		},
        getLocation() {
            ajax.get('index/mappoint', {}, res => {
                if (res.data.code == 1) {
                    console.log(res.data.cfg_mappoint)
					this.center_point = res.data.cfg_mappoint.split(",")
                    let point = res.data.cfg_mappoint.split(",")
                    this.params.lat = point[0]
                    this.params.lng = point[1]
                    this.getCovers()
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
		selectPrice(value, name){
			this.params.price = value
			this.priceName = name
			this.nowTab = 0
			this.show_mask=false
			this.getCovers()
		},
		selectDistance(id, name){
			this.show_mask=false
			this.params.distance = id/1000
			this.distanceName = name
			this.nowTab = 0
			this.getCovers()
		},
		selectOption(obj,type){
			switch(type)
			{
			case "type_id":
				if(this.params.type_id == obj.type_id){
					obj.type_id = ""
				}
			case "renovation_id":
				if(this.params.renovation_id == obj.renovation_id){
					obj.renovation_id = ""
				}
			case "status":
				if(this.params.status == obj.status){
					obj.status = ""
				}
			case "progress_id":
				if(this.params.progress_id == obj.progress_id){
					obj.progress_id = ""
				}
			}
			this.params = Object.assign({},this.params,obj)
		},
		selectMore(){
			this.nowTab = 0
			this.show_mask=false
			this.getCovers()
		},
		resetMore(){
			this.params.type_id = ""
			this.params.renovation_id = ""
			this.params.progress_id = ""
			this.params.status=""
		},
        getCovers() {
            let getCovers = () => {
                this.covers = []
                ajax.get("map/mapList.html", this.params, (res) => {
                    uni.hideLoading()
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: "none"
                        })
                        this.covers = []
                        console.log(JSON.stringify(this.covers))
                        return
                    }
                    let icon = '/static/icon/none.png'
                    this.lists = res.data.list

                    let covers = res.data.list.map((item, index) => {
                        let content, bgColor
                        if (this.params.type === 3) {
                            content = item.title + ((item.price && item.price != "0") ? '\n' + item.price + '元/月' : '')
                        } else {
                            content = item.title + ((item.price && item.price != "0") ? '\n' + item.price + '元/m²' : '')
                        }
                        switch (item.leixing) { //1: 待售；2: 在售；3:尾盘；4:售完
                            case 1:
                                bgColor = "#17bfff"
                                break
                            case 2:
                                bgColor = "#70d298"
                                break
                            case 3:
                                bgColor = "#ff7213"
                                break
                            case 4:
                                bgColor = "#d3b03d"
                                break
                            default:
                                bgColor = "#6478a6"
                        }
                        let ob = {
                            id: index + 1,
                            width: 30,
                            height: 30,
                            iconPath: icon,
                            latitude: item.lat,
                            longitude: item.lng,
                            callout: {
                                content: content,
                                padding: 10,
                                borderRadius: 8,
                                bgColor: bgColor,
                                color: "#ffffff",
                                display: 'ALWAYS',
                                textAlign: "center"
                            }
                        }
                        return ob
                    })
                    this.covers = covers
                    // console.log(JSON.stringify(this.covers))
                })
            }
            getCovers()
        },
        regionChange(e) {
            if (e.causedBy == "scale" || e.causedBy == "drag"||e.detail.causedBy=="scale"||e.detail.causedBy=="drag") {
                uni.showLoading({
                    title: "正在获取房源",
                    mask: true
                })
                this.covers = []
                // 获取中心点经纬度
                this.map.getCenterLocation({
                    success: (res) => {
                        this.params.lat = res.latitude
                        this.params.lng = res.longitude
                        debounce(this.getCovers, 200)()
                        // this.getCovers()
                    }
                })
            }
        },
        handelMark(e) {
            if (!e.detail.markerId) {
                return
            }
            this.item = this.lists[e.detail.markerId - 1]
        },
        goTo() {
            if (!this.item || !this.item.id) {
                return
            }
            if (this.params.type == 1) {
                navigateTo('/pages/new_house/detail?id=' + this.item.id + '&title=' + this.item.title)
            } else {
                navigateTo('/pages/house_price/detail?id=' + this.item.id + '&title=' + this.item.title)
            }
        },
		stopMove(){
			this.show_mask=false
			this.nowTab=0
		}
    }
}
</script>

<style scoped lang="scss">
.tabs {
    height: 40px;
	background-color: #fff;
    flex-direction: row;
	position: fixed;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
}

.tab-item {
    flex: 1;
    justify-content: center;
    align-items: center;
}

.active {
    color: #f65354;
}

.tab_text-box {
    height: 40px;
    padding: 10px 15px;
}

.tab_text_box_active {
    border-bottom-width: 2px;
    border-bottom-color: #F65354;
}

.tab_text {
    font-size: 14px;
}

.list-item {
    flex-direction: row;
    position: fixed;
    height: 120px;
    background-color: #fff;
    bottom: 0;
    padding: 15px 5px;
}

.list-info {
    flex: 2;
}

.info-title {
    font-size: 16px;
}

.info-content-box {
    margin-bottom: 8px;
    margin-top: 8px;
}

.info-content {
    color: #666666;
    font-size: 13px;
}

.price-box {
    flex-direction: row;
    align-items: center;
}

.total {
    color: #F65354;
    font-weight: bold;
    font-size: 14px;
}

.acreage {
    color: #666666;
    font-size: 13px;
}

.address-row {
    margin-top: 8px;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.address {
    flex: 3;
    margin-right: 1px;
}

.distance {
    flex: 1;
}

.address-text {
    font-size: 13px;
    color: #666666;
    lines: 1;
    text-overflow: ellipsis;
}

.list-img {
    width: 240upx;
    height: 190upx;
    margin-right: 18upx;
    position: relative;
    flex: 1;
    overflow: hidden;
}

.img {
    width: 240upx;
    height: 190upx;
}
.filter-tab {
	flex-direction: row;
	border-bottom-width: 2upx;
	border-bottom-color: #f3f3f3;
	border-bottom-style:solid;
	// width: 100%;
	margin: 5px 0;
	height: 80upx;
	background-color: #fff;
	position: fixed;
	top: 35px;
}
.filter-tab-item {
    flex: 1;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}
.active{
	color: #f65354;
}
.filter_text{
	font-size: 13px;
}
.cascade_box{
	flex-direction: row;
	align-items: flex-start;
	justify-content: space-between;
}
	.cate{
		flex: 1;
		overflow: hidden;
	}
.cascade_item{
	
		padding: 26rpx 32rpx;
		text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		// white-space: nowrap;

	}
.selected{
	color: $uni-color-primary;
}
.filter {
	align-items: center;
	justify-content: flex-start;
	padding: 20rpx 10rpx;
	height: 300px;
	background-color: #fff;
	position: fixed;
	top: 160rpx;
	border-top-width: 2upx;
	border-color: #999999;
	transform: translate(0,-150%);
	transition-property: transform;
	transition-duration: 300ms;
}
.show {
	transform: translate(0,0);
}
.filter-list {
    justify-content: flex-start;
}
.filter-list_more {
	justify-content: flex-start;
}
.filter-item {
	padding: 26rpx 32rpx;
}
.top_text{
	font-size: 28upx;
	lines: 1;
	text-overflow: ellipsis;
}
.filter_more{
	align-items: center;
	justify-content: flex-start;
	padding: 20rpx 10rpx;
	height: 280px;
	border-top-width: 2upx;
	border-color: #999999;
	background-color: #fff;
	position: fixed;
	top: 160rpx;
	transform: translate(0,-150%);
	
	transition-property: transform;
	transition-duration: 300ms;
}
.more-screen-item {
    padding: 26rpx 32rpx;
}
.more_title {
    padding: 5px;
    font-size: 13px;
    color: #333;
}
.options {
    flex-wrap: wrap;
	flex-direction: row;
}
.options-item {
    margin: 10upx;
    text-align: center;
    font-size: 12px;
    color: #666;
}
.opt_text{
	font-size: 12px;
	background-color: #f3f3f3;
	padding: 7px 5px;
	border-radius: 3px;
}
.btns{
	flex-direction: row;
	justify-content: center;
	padding: 20upx 80upx;
}
.btn{
	flex: 1;
	padding: 10px 20px;
	text-align: center;
	border-radius: 5px;
}
.btn_res{
	margin-right: 60upx;
	color: #000000;
	background-color:#F8F8F8 ;
}
.btn_sel{
	color: #fff;
	background-color:$uni-color-primary ;
}
.active_green{
	color: #179B16;
	border-width:2upx;
	border-style: solid;
	border-color:#179B16 ;
}
.iconfont {
    font-family: iconfont;
}
.sanjiao{
	color: #666;
	font-size: 12px;
	width: 15px;
	// height:10px;
}
.mask{
	position: absolute;
	left: 0px;
	right: 0px;
	top: 40px;
	bottom: 0;
	background-color:rgba(0,0,0, 0.2);
}
</style>
