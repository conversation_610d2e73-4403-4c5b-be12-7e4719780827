<template>
<view>
    <my-popup ref="followpop" position="center" :height="login_box_height+'px'" :touch_hide="false" @click="stopMove">
        <view class="info" id="follow-pop" @click="stopMove">
            <view class="top">
                <view class="top-info flex-column">
                    <view class="info-title flex-row">
                        <view class="info-left"></view>
                        <view v-if="type=='dingyue'" class="info-con">{{title}}</view>
                        <view v-if="type=='denglu'" class="info-con">查看内容需要登录</view>
                        <view v-if="type=='bangshouji'" class="info-con">查看内容需要绑定手机号</view>
                    </view>
                    <view class="info-tips">我们将为您提供以下服务</view>
                </view>

            </view>
            <view class="middle">
                <view class="middle-info flex-row">
                    <view class="mid-img flex-row">
                        <image :src="jiageImage" mode="widthFix"></image>
                        <!-- <my-icon type="jiage" size="40rpx" color="#FB656A"></my-icon> -->
                    </view>
                    <view class="mid-info flex-column">
                        <view class="mid-title">
                            {{hangqing_title}}
                        </view>
                        <view class="mid-tips">
                            {{hangqing_sub_title}}
                        </view>
                    </view>
                </view>
                <view class="middle-info middle-info-mid flex-row">
                    <view class="mid-img flex-row">
                        <image :src="kaipanImage" mode="widthFix"></image>
                        <!-- <my-icon type="jiage" size="40rpx" color="#FB656A" ></my-icon> -->
                    </view>
                    <view class="mid-info flex-column">
                        <view class="mid-title">
                            业内必备行情报告
                        </view>
                        <view class="mid-tips">
                            周报/月报/年报
                        </view>
                    </view>
                </view>
                <view class="middle-info flex-row">
                    <view class="mid-img flex-row">
                        <image :src="xiaokongImage" mode="widthFix"></image>
                        <!-- <my-icon type="jiage" size="40rpx" color="#FB656A" ></my-icon> -->
                    </view>
                    <view class="mid-info flex-column">
                        <view class="mid-title">
                            全面了解市场行情
                        </view>
                        <view class="mid-tips">
                            权威数据榜单/数据报表
                        </view>
                    </view>
                </view>
                <view  v-if='type=="dingyue"' class="btn" @click="submit">
                    立即订阅
                </view>
                <view  v-if='type=="denglu"' class="btn" @click="login">
                    立即登录
                </view>
                <view  v-if='type=="bangshouji"' class="btn" @click="bindPhone">
                    立即绑定
                </view>
                <!-- <button class="btn" open-type="getUserInfo" @getuserinfo="getInfo" >
                    立即订阅
                </button> -->
                <view class="close" @click="hide()">
                    取消
                </view>
            </view>
            
        </view>
        
    </my-popup>
    <!-- <tel-pop ref="telpop" @phoneNum="getPhoneNumber"></tel-pop>  -->
    
</view>
</template>

<script>
import myPopup from './myPopup.vue'
import myIcon from './myIcon.vue'
// import telPop from './telPop'
import dragVerify from "./dragVerify.vue"
import {config} from "../common/config.js"
import {
		mapState,
		mapMutations
	} from 'vuex'
export default {
    data() {
        return {
            // sending: false,
            login_box_height: 'initial',
            xiaokongImage:config.imgDomain+'/images/new_icon/pop/<EMAIL>?x-oss-process=style/m_120',
            kaipanImage:config.imgDomain+'/images/new_icon/pop/<EMAIL>?x-oss-process=style/m_120',
            jiageImage:config.imgDomain+'/images/new_icon/pop/<EMAIL>?x-oss-process=style/m_120',
            // time: 0,
            // show_verify: false,
            // code_token: '',
            // verify_img: '',
            // verify_fail: false,
            // verify_success: false,
            // show_sms_code:false
        }
    },
    props: {
        is_login:{
            type: [Number],
            default: 1,   // 1 未授权登录 2 已授权 未绑定手机号  
        },
        type:{
            type: [String],
            default:"dingyue" ,    
        },
        title:{
            type: String,
            defaultL: '订阅数据通知'
        },
        hangqing_title: {
            type: String,
            default: '数据更新变化通知'
        },
        hangqing_sub_title: {
            type: String,
            default: '同步最新市场数据'
        }
    },
    components: {
        myIcon,
        myPopup,
        // telPop,
        dragVerify
    },
    computed: {
        siteName(){
            return this.$store.state.siteName
        }
    },
    methods: {
        ...mapMutations(['getUserInfo','setAllowOpen']),
        showPopup() {
            this.$nextTick(()=>{
                const query = uni.createSelectorQuery().in(this);
                setTimeout(() => {
                    query.select('#follow-pop').boundingClientRect(data => {
                        this.login_box_height = data.height
                        this.$refs.followpop.show()
                    }).exec(); 
                }, 50);
            })
        },
        hide(){
            this.$refs.followpop.hide()
            this.$emit("hideOk")
        },
        stopMove(){

        },
        submit(){
            this.$emit("dingyue")
        },
        login(){
            this.$emit("login")
        },
        bindPhone(){
            this.$emit("bindPhone")
        }
    }
}
</script>

<style scoped lang="scss">
.flex-row{
    display: flex;
    flex-direction: row;
}
.info{
    
    width: 80vw;
    margin: 0 auto;
    box-sizing: border-box;
    .top{
        background-image: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
        border-radius: 8px 8px 0 0;
        padding: 32upx 0;
        color: #fff;
        .top-info{
            .info-title{
                padding-left: 48upx;
                .info-left{
                    width: 4upx;
                    height: 100%;
                    background: #fff;
                    margin-right: 8upx;
                }
                .info-con{
                    font-size: 36upx;
                }
            }
            .info-tips{
                padding-left: 60upx;
                font-size: 22upx;
            }
        }


    }
    .middle{
        background: #fff;
        padding: 48upx 48rpx 48upx 48rpx;
        border-radius: 0 0 16rpx 16rpx;
        .middle-info{
            .mid-img{
                width: 64upx;
                height: 64upx;
                border-radius: 50%;
                justify-content: center;
                align-items: center;
                overflow: hidden;
                margin: 0 20rpx;
                // background: rgba(251,166,101,0.20);
                image{
                    width: 100%;
                }
            }
            .mid-info{
                justify-content: space-between;
                
                .mid-title{
                    font-size:  28upx;
                    color: #333333;
                }
                .mid-tips{
                    font-size: 22upx;
                    color: #999999;
                }

            }
            &.middle-info-mid{
                margin: 48upx 0;
            }
        }
        .btn{
            margin-top: 48rpx;
            background-image: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
            box-shadow: 0 4px 16px 0 rgba(255,80,0,0.30);
            border-radius: 44upx;
            text-align: center;
            color: #fff;
            font-size: 32rpx;
            padding: 20rpx 0;
        }
        .close{
            margin-top: 32rpx;
            font-size: 28rpx;
            color: #999999;
            text-align: center;

        }
    }   
    
}
</style>