<template>
  <view class="enturst_container">
    <view class="header">
      <image class="avatar" :src="to_user.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
      <view class="user_name">你好，我是{{to_user.cname||''}}</view>
      <view class="close_btn" @click="$emit('close')">
        <my-icon type="ic_guanbi" color="#fff" size="44rpx"></my-icon>
      </view>
    </view>
    <view class="content">
      <view class="tip">委托我为您提供以下服务</view>
      <view class="service_list flex-box">
        <view class="service_item" v-for="(item, index) in service_list" :key="index">
          <view class="icon-box"><my-icon type="wancheng" size="20rpx" color="#fb656a"></my-icon></view>
          <text>{{item}}</text>
        </view>
      </view>
    </view>
    <view class="footer">
      <!-- #ifdef MP-WEIXIN -->
      <button class="sub_btn" v-if="user_login_status==2" open-type="getPhoneNumber" @getphonenumber="onGetPhonenumber">同意委托</button>
      <view class="sub_btn" v-if="user_login_status==3" @click="handleEnturst">同意委托</view>
      <!-- #endif -->
      <!-- #ifndef MP-WEIXIN || MP-BAIDU -->
      <view class="sub_btn" @click="handleEnturst">同意委托</view>
      <!-- #endif -->
      <view class="tip" @click="toPrivacy">同意委托即代表同意《{{siteName}}隐私政策》</view>
    </view>
  </view>
</template>

<script>
import myIcon from '@/components/myIcon'
import {mapState} from 'vuex'
export default {
  components: {
    myIcon
  },
  data () {
    return {
      service_list:[
        "快速服务咨询",
        "优质房源推荐",
        "详细政策解读",
        "预约实地带看"
      ]
    }
  },
  props:{
    to_user: {
      type: [Object, Array],
      default: ()=>{
        return {}
      }
    },
    isDetail:{
      type:[String,Number],
      default:0,   // 经纪人直接委托 传1 ，置业顾问 直接委托 传2
    }
  },
  computed:{
    ...mapState(['siteName', 'user_login_status']),
  },
  methods: {
    // 执行委托接口
    handleEnturst(){
      // #ifndef MP-WEIXIN || MP-BAIDU
      if(this.user_login_status<3){
        console.log("提交需要登录的弹窗")
        this.$emit('popup_login')
        return
      }
      // #endif
      let params={
          sid: this.to_user.adviser_id||this.to_user.agent_id||this.to_user.infoDetailId,
          sharetype: this.to_user.adviser_id?1:2
      }
      if (this.isDetail>0){
        params.is_detail=this.isDetail
      }
      this.$ajax.get('im/authorizeTel.html', params, res=>{
        if(res.data.code === 1){
          uni.showToast({
            title: res.data.msg||'委托成功'
          })
          this.$emit('success')
        }else{
          uni.showToast({
            title: res.data.msg||'委托失败',
            icon: 'none'
          })
          this.$emit('fail')
        }
      }, err=>{
        
      })
    },
    onGetPhonenumber(res){
        if(res.target.encryptedData&&res.target.iv){
            this.bindPhone(res.target.encryptedData, res.target.iv, ()=>{
              this.handleEnturst()
            })
        }else{
            this.$navigateTo('/user/bind_phone/bind_phone')
        }
    },
    bindPhone(encryptedData, iv, callback){
      this.$ajax.get('member/getWxPhoneNumber',{encryptedData,iv},(res)=>{
        if(res.data.code == 1){
          if(res.data.token){ //绑定手机号成功后台返回一个新的token
            uni.setStorageSync('token',res.data.token)
            this.$store.state.user_login_status = 3
          }
          callback&&callback()
        }
      })
    },
    toPrivacy(){
      this.$navigateTo('/user/my/reg_infos')
    }
  }
}
</script>

<style scoped lang="scss">
.enturst_container{
  height: 600rpx;
  width: 80%;
  height: 694rpx;
  margin-left: 10%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  .header{
    height: 260rpx;
    color: #fff;
    background-image: linear-gradient(270deg, #F7918F 0%, #FB656A 99%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    .avatar{
      width: 96rpx;
      height: 96rpx;
      margin-bottom: 24rpx;
      border-radius: 50%;
    }
    .user_name{
      font-size: 32rpx;
    }

    .close_btn{
      width: 80rpx;
      height: 80rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .content{
    box-sizing: border-box;
    height: 250rpx;
    background-color: #fff;
    justify-content: center;
    padding: 24rpx;
    .tip{
      text-align: center;
      font-size: 22rpx;
      margin: 18rpx 0;
      color: #999;
    }
    .service_list{
      flex-wrap: wrap;
      justify-content: center;
      .service_item{
        padding: 12rpx;
        text-align: center;
        min-width: 43%;
        max-width: 43%;
        display: flex;
        align-items: center;
        font-size: 26rpx;
        .icon-box{
          margin-right: 10rpx;
          width: 32rpx;
          height: 32rpx;
          border-radius: 50%;
          background-color: rgba($color:$uni-color-primary,$alpha: 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  .footer{
    padding: 0 48rpx;
    .sub_btn{
      margin: 0 12rpx;
      text-align: center;
      padding: 0 32rpx;
      height: 86rpx;
      font-size: 32rpx;
      line-height: 86rpx;
      border-radius: 12rpx;
      background-color: $uni-color-primary;
      box-shadow: 0 4px 16px 0 rgba($color:$uni-color-primary,$alpha: 0.40);
      color: #fff;
    }
    .tip{
      text-align: center;
      font-size: 22rpx;
      margin-top: 24rpx;
      color: #999;
    }
  }
}
</style>