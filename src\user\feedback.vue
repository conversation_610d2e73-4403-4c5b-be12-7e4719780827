<template>
  <view>
    <view class="block">
      <my-input
        label="联系方式"
        v-model="params.tel"
        type="number"
        placeholder="请输入手机号"
      ></my-input>
    </view>
    <view class="block">
      <view class="textarea">
        <view class="label">反馈内容</view>
        <textarea placeholder="请输入" v-model="params.content" maxlength="200"></textarea>
        <view class="text-right">{{params.content.length}}/200</view>
      </view>
    </view>
    <view class="btn-box">
      <view class="btn" @click="subData">提交反馈</view>
    </view>
  </view>
</template>

<script>
import myInput from '../components/form/newInput'
export default {
  components: {
    myInput
  },
  data () {
   return {
     params:{
       tel:'',
       content:''
     }
    }
  },
  methods: {
    subData(){
      this.$ajax.post('member/feedBack.html',this.params,res=>{
        uni.showToast({
          title:res.data.msg,
          icon:res.data.code === 1?'success':'none'
        })
        if(res.data.code === 1){
          setTimeout(() => {
            this.$navigateBack()
          }, 1500)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.block{
  padding: 24rpx 48rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}
.textarea{
  .label{
    font-size: 22rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #666;
  }
  textarea{
    padding: 20rpx;
    box-sizing: border-box;
    width: 100%;
    border: 1rpx solid #f5f5f5;
  }
  .text-right{
    margin-top: 24rpx;
    text-align: right;
    font-size: 24rpx;
    color: #999;
  }
}
.btn-box{
  .btn{
		margin: 24rpx 48rpx;
		line-height: 88rpx;
		border-radius: 44rpx;
		text-align: center;
		font-size: 32rpx;
		font-weight: bold;
		background-color: $uni-color-primary;
		box-shadow: 0 4px 12px 0 rgba(251,101,106,0.40);
		color: #fff;
	}
}
</style>