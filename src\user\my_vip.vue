<template>
  <view class="vip-benefits">
    <view class="header" :style="{ backgroundImage: `url(${ossDomain}/icon/pay/vipbg.png)` }">
      <view class="user-datainfo" :style="{ backgroundImage: `url(${ossDomain}/icon/pay/vip_head.png)` }">
        <view class="user-info flex-row">
          <image class="user_img" :src="(vip_info.prelogo || defaultAvatar) | imageFilter('w_120')" mode="aspectFill">
          </image>
          <view class="flex-1">
            <!-- <text class="user_name">{{ vip_info.cname }}</text>
                      <text class="user_id">NO.1234567786543</text> -->
            <text class="user_name">{{ vip_info.cname }}</text>
            <text class="user_id">NO.{{ vip_info.id }}</text>
          </view>
        </view>
        <view class="vip-badge"
          :style="{ backgroundImage: `url(${ossDomain}/icon/pay/vip_rigth.png) `, backgroundRepeat: 'no-repeat', }">
          VIP会员
        </view>
        <view class="user_data flex-row">
          <text class="time">VIP会员 {{ vip_info.levelup_time }}</text>
          <!-- <text class="time">超过10002人已加入</text> -->
        </view>
      </view>
    </view>
    <view class="user-vip  flex-row">
      <image :src="`${ossDomain}/icon/pay/vip-ljt.png`"></image>
      <text class="vipqy">会员权益</text>
      <image :src="`${ossDomain}/icon/pay/vip-rjt.png`"></image>
    </view>
    <view class="vip-info">
      <template>
        <view class="info-description flex-row">
          <span class="d-ms">专属特权</span>
          <span class="d-msz d-msnum">当前权益</span>
          <span class="d-ms d-msnum">普通用户</span>
        </view>
        <view class="info-description flex-row">
          <span class="d-ms">免费精选</span>
          <span class="d-msz d-msnum">{{ vipuser.fixed_refresh || 0 }}/条</span>
          <span class="d-ms d-msnum">{{ vipother_level.fixed_refresh || 0 }}/条</span>

        </view>
        <view class="info-description flex-row">
          <span class="d-ms">免费精选刷新</span>
          <span class="d-msz d-msnum">{{ vipuser.fixed_refresh_times || 0 }}/次</span>
          <span class="d-ms d-msnum">{{ vipother_level.fixed_refresh_times || 0 }}/次</span>

        </view>
        <view class="info-description flex-row">
          <span class="d-ms">每天发布量</span>
          <span class="d-msz d-msnum">{{ vipuser.perday_maxpost || 0 }}/条</span>
          <span class="d-ms d-msnum">{{ vipother_level.perday_maxpost || 0 }}/条</span>

        </view>
        <view class="info-description flex-row">
          <span class="d-ms">每天刷新</span>
          <span class="d-msz d-msnum">{{ vipuser.perday_maxrefresh || 0 }}/条</span>
          <span class="d-ms d-msnum">{{ vipother_level.perday_maxrefresh || 0 }}/条</span>

        </view>
        <view class="info-description flex-row">
          <span class="d-ms">每天免费刷新</span>
          <span class="d-msz d-msnum">{{ vipuser.refresh_times || 0 }}/次</span>
          <span class="d-ms d-msnum">{{ vipother_level.refresh_times || 0 }}/次</span>

        </view>
        <view class="info-description flex-row">
          <span class="d-ms">总发布量</span>
          <span class="d-msz d-msnum">{{ vipuser.total_maxpost || 0 }}/条</span>
          <span class="d-ms d-msnum">{{ vipother_level.total_maxpost || 0 }}/条</span>
        </view>

      </template>
    </view>

    <view class="vip-infot flex-row">
      <view class="vip-sy" v-for="(item, index) in tab" :key="index">
        <view class="bg">
          <image :src="item.icon"></image>
        </view>
        <span class="bgms">{{ item.name }}</span>
        <template v-if="item.name == '精选信息'">
          <span class="bgmst">{{ item.mask }}{{ vipuser.fixed_refresh_times }}次</span>
        </template>
        <template v-else-if="item.name == '发布信息'">
          <span class="bgmst">{{ item.mask }}{{ vipuser.perday_maxpost }}条</span>
        </template>
        <template v-else-if="item.name == '刷新信息'">
          <span class="bgmst">{{ item.mask }}{{ vipuser.refresh_times }}条</span>
        </template>
        <template v-else>
          <span class="bgmst">{{ item.mask }}</span>
        </template>
      </view>
    </view>
    <view class="btn" v-if="vip_info.levelid > 1" @click="$navigateTo('/user/member_upgrade')">立即续费</view>
    <view class="btn" v-else-if="!vip_info.is_vip" @click="$navigateTo('/user/member_upgrade?is_personal=1')">立即开通
    </view>
  </view>
</template>

<script>
import { config } from '@/common/config.js'
export default {
  data() {
    return {
      tab: [
        {
          name: '尊贵身份',
          icon: config.imgDomain + '/icon/pay/vip.png',
          mask: 'VIP会员亮标识'
        },
        {
          name: '房源电话',
          icon: config.imgDomain + '/icon/pay/vip_tel.png',
          mask: '免费查看房源电话'
        },
        {
          name: '发布信息',
          icon: config.imgDomain + '/icon/pay/vip_fb.png',
          mask: '每天发布'
        },
        {
          name: '精选信息',
          icon: config.imgDomain + '/icon/pay/vip_jx.png',
          mask: '每周设置精选'
        },
        {
          name: '刷新信息',
          icon: config.imgDomain + '/icon/pay/vip_sx.png',
          mask: '每天可重置刷新'
        },
        {
          name: '学校施教',
          icon: config.imgDomain + '/icon/pay/vip_dw.png',
          mask: '查看学校施教范围'
        }
      ],
      defaultAvatar: this.$store.state.defaultAvatar,
      vip_info: {

      },
      user_info: {},
      vipother_level: {},
      vipuser: {},

    }
  },
  onLoad() {
    console.log(config.imgDomain, '44465456');
    this.getUserInfo()
    this.getVipInfo()
    this.getVipinterests()
  },
  computed: {
    login_status() {
      return this.$store.state.user_login_status
    },
    ossDomain() {
      return config.imgDomain
    }
  },
  methods: {
    getUserInfo() {
      this.$ajax.get('member/index.html', {}, res => {
        if (res.data.code === 1) {
          this.user_info = res.data.user
        }
      }, err => {
        console.log(err)
      }, { disableAutoHandle: true })
    },

    getVipInfo() {
      this.$ajax.get('member/memberShip.html', {}, res => {
        if (res.data.code === 1) {
          this.vip_info = res.data.user
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    getVipinterests() {
      this.$ajax.get('PayGuide/memberShip', {}, res => {
        if (res.data.code === 1) {
          this.vipother_level = res.data.other_level
          this.vipuser = res.data.user
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.uni-page-head-btn {
  background-color: red;
}

page {
  background-color: #1E1F25;
  font-family: "PingFang SC";

}

view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.flex-row {
  flex-direction: row;
}

.vip-benefits {
  background-color: #1E1F25;
  color: #fff;
  padding: 34rpx;
}

.header {
  position: relative;
  background-size: 100%;
  width: 100%;
  top: -70rpx;
  height: 530rpx;

  .user-datainfo {
    position: absolute;
    background-size: cover;
    width: 100%;
    top: 200rpx;
    border: 2px solid #1E1F25;
    border-radius: 12px;
    padding: 48rpx;

    .user-info {
      display: flex;
      align-items: center;
      position: relative;
      padding-bottom: 24rpx;

      :after {
        content: "";
        position: absolute;
        height: 1rpx;
        left: 0;
        right: 0;
        bottom: 0;
        -webkit-transform: scaleY(.5);
        transform: scaleY(.5);
        background-color: rgba($color: #E8BB72, $alpha: 1);
      }

      .user_img {
        width: 128rpx;
        height: 128rpx;
        border-radius: 50%;
        margin-right: 24rpx;
      }


      .user_name {
        margin-bottom: 16rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        font-weight: 700;

      }

      .user_id {
        color: #A06B14;
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
      }
    }

    .vip-badge {
      position: absolute;
      background-size: contain;
      background-position: right top;
      width: 100%;
      top: 0;
      right: 0;
      color: #fff;
      padding: 10rpx 40rpx;
      font-weight: bold;
      color: #A06B14;
      text-align: right;

    }
  }

  .user_data {
    justify-content: space-between;
    align-items: center;
    padding: 28rpx 0 0 0;

    .time {
      color: #A06B14;
      font-size: 22rpx;
    }

  }

}

.user-vip {

  justify-content: center;
  align-items: center;
  position: relative;
  top: -50rpx;

  .vipqy {
    color: #E5BA72;
    font-size: 40rpx;
    font-style: normal;
    font-weight: 500;
    padding: 0rpx 20rpx;
  }

  image {
    width: 68rpx;
    height: 26rpx;
  }

}

.vip-info {
  width: 100%;
  // height: 562rpx;
  flex-shrink: 0;
  border-radius: 12px;
  border: 1px solid rgba(229, 186, 114, 0.60);
  background: linear-gradient(131deg, rgba(243, 212, 160, 0.20) 435.97%, rgba(244, 219, 179, 0.10) 9681.61%);
  //三列
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx;

  .info-description {
    display: flex;
    justify-content: space-around;
    align-items: center;
    //三个列
    // padding: 20rpx 40rpx 0px 0px;
    line-height: 84rpx;
    letter-spacing: 2rpx;

    .d-ms,
    .d-msz,
    .d-ms {
      display: block;
      width: 200rpx;
      font-size: 30rpx;
      font-style: normal;
      text-align: center;
      /* 设置固定宽度 */
      white-space: nowrap;
      /* 防止文本换行 */
      overflow: hidden;
      /* 隐藏超出部分 */
      text-overflow: ellipsis;
      /* 在超出时显示省略号 */
    }

    .d-ms {
      color: #E5BA72;
      font-weight: 500;
    }

    .d-msz {
      color: rgba(255, 255, 255, 0.60);
      font-weight: 400;
    }

    .d-msnum {
      margin-left: 30rpx;
    }

  }

  .d-pt {
    padding-left: 30rpx;
  }
}

.vip-info3 {
  width: 100%;
  // height: 562rpx;
  flex-shrink: 0;
  border-radius: 12px;
  border: 1px solid rgba(229, 186, 114, 0.60);
  background: linear-gradient(131deg, rgba(243, 212, 160, 0.20) 435.97%, rgba(244, 219, 179, 0.10) 9681.61%);
  //三列
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx;

  .info-description {
    display: flex;
    justify-content: space-around;
    align-items: center;
    //三个列
    // padding: 20rpx 40rpx 0px 0px;
    line-height: 84rpx;
    letter-spacing: 2rpx;

    .d-ms {
      color: #E5BA72;
      font-size: 32rpx;
      font-style: normal;
      font-weight: 500;
      white-space: nowrap;
      /* 防止文本换行 */
      overflow: hidden;
      /* 隐藏超出部分 */
      text-overflow: ellipsis;
      /* 在超出时显示省略号 */
      max-width: 30%;
      /* 确保文本不会超出容器宽度 */
    }

    .d-msz {
      color: rgba(255, 255, 255, 0.60);
      font-size: 32rpx;
      font-style: normal;
      font-weight: 400;
    }

  }

  .d-pt {
    padding-left: 30rpx;
  }
}

.vip-info2 {
  width: 100%;
  // height: 562rpx;
  flex-shrink: 0;
  border-radius: 12px;
  border: 1px solid rgba(229, 186, 114, 0.60);
  background: linear-gradient(131deg, rgba(243, 212, 160, 0.20) 435.97%, rgba(244, 219, 179, 0.10) 9681.61%);
  //三列
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx;

  .info-description {
    display: flex;
    justify-content: space-around;
    align-items: center;
    //三个列
    // padding: 20rpx 40rpx 0px 0px;
    line-height: 84rpx;
    letter-spacing: 2rpx;

    .d-ms {
      color: #E5BA72;
      font-size: 32rpx;
      font-style: normal;
      font-weight: 500;

    }

    .d-msz {
      color: rgba(255, 255, 255, 0.60);
      font-size: 32rpx;
      font-style: normal;
      font-weight: 400;

    }

  }

  .d-pt {
    padding-left: 30rpx;
  }
}

.vip-infot {
  width: 100%;
  height: 540rpx;
  flex-shrink: 0;
  border-radius: 12px;
  border: 1px solid rgba(229, 186, 114, 0.60);
  background: linear-gradient(131deg, rgba(243, 212, 160, 0.20) 435.97%, rgba(244, 219, 179, 0.10) 9681.61%);
  margin-top: 48rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;

  .vip-sy {
    text-align: center;
    width: 33%;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 60rpx;

    image {
      width: 42rpx;
      height: 40rpx;
    }

    .bg {
      width: 82rpx;
      height: 80rpx;
      background-color: rgba(30, 31, 37, 0.2);
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;

    }

    .bgms {
      color: #E5BA72;
      font-size: 28rpx;
      font-weight: 500;
      letter-spacing: 1px;
      font-weight: bold;
    }

    .bgmst {
      color: #E5BA72;
      font-size: 22rpx;
      font-weight: 400;
      letter-spacing: 1rpx;
    }
  }

}

.btn {
  color: #A06B14;
  height: 88rpx;
  line-height: 88rpx;
  padding: 0 24rpx;
  border-radius: 60rpx;
  font-size: 40rpx;
  font-weight: 400;
  font-weight: bold;
  background-image: linear-gradient(102deg, #E5BA72 0%, #FCE9CA 100%);
  text-align: center;
  margin-top: 94rpx;
  letter-spacing: 2rpx;

}
</style>