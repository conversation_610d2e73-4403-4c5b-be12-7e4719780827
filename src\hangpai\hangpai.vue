<template>
    <view class="page">
        <view class="page-img">
            <view></view>
            <view class="logo-one">720˚航拍</view>
            <view class="logo-two">楼盘走遍一览无余</view>
            <view class="logo-three">空中看房真实直观</view>
            <view class="search-box">
                <view class="search-icon" v-if="float == 1"><my-icon type="ic_sousuo" color="#999999"></my-icon></view>
                <text class="tex" v-if="float == 1">搜索</text>
                <input type="text" v-model="val" confirm-type="search" @blur="handleblur()" @focus="handlefocus()"
                    @input="handleSearch()" />
            </view>
        </view>
        <view class="page-top">
            <my-icon type="ic_wode" color="#c28524" style="margin-left: 24rpx;margin-right: 20rpx;"></my-icon>
            <text class="tex-one">1554人已查看</text>
            <text class="tex-two">“张三”刚刚查看了楼盘航拍</text>
        </view>
        <view class="page-center">
            <view :class="{ active: com == index }" v-for="(item, index) in getlist" :key="index" @click="handelist(index)">
                {{ item.value }}
            </view>
        </view>
        <view class="page-bottom">
            <view class="page-bottom-one">共12个航拍视频</view>
            <view class="page-bottom-two">
                <view class="page-bottom-top">
                    <image src="../static/icon/video.png"></image>
                    <text>更新时间：2023-03-21</text>
                </view>
                <view class="page-bottom-center">
                    <text>人和天地</text>
                </view>
                <view class="page-bottom-bottom">
                    <text>均价</text>
                    <text>11080</text>
                    <text>元/m2</text>
                    <text>｜城东</text>
                </view>
                <view class="beijing">
                    航拍看房
                </view>
                <view style="height: 18rpx;"></view>
            </view>
            <view class="page-bottom-two">
                <view class="page-bottom-top">
                    <image src="../static/icon/video.png"></image>
                    <text>更新时间：2023-03-21</text>
                </view>
                <view class="page-bottom-center">
                    <text>人和天地</text>
                </view>
                <view class="page-bottom-bottom">
                    <text>均价</text>
                    <text>11080</text>
                    <text>元/m2</text>
                    <text>｜城东</text>
                </view>
                <view class="beijing">
                    航拍看房
                </view>
                <view style="height: 18rpx;"></view>
            </view>
            <view class="page-bottom-two">
                <view class="page-bottom-top">
                    <image src="../static/icon/video.png"></image>
                    <text>更新时间：2023-03-21</text>
                </view>
                <view class="page-bottom-center">
                    <text>人和天地</text>
                </view>
                <view class="page-bottom-bottom">
                    <text>均价</text>
                    <text>11080</text>
                    <text>元/m2</text>
                    <text>｜城东</text>
                </view>
                <view class="beijing">
                    航拍看房
                </view>
                <view style="height: 18rpx;"></view>
            </view>
        </view>
    </view>
</template>
<script>
import myIcon from "../components/myIcon.vue"
export default {
    components: {
        myIcon
    },
    data() {
        return {
            float: 1,
            val: "",
            com: 0,
            getlist: [
                {
                    value: "航拍视频",
                },
                {
                    value: "航炮VR",
                },
                {
                    value: "航拍工程进度",
                }
            ],
        }
    },
    onLoad() {

    },
    methods: {
        handleSearch() {
            console.log("2112")
        },
        handlefocus() {
            if (this.val == "") {
                this.float = 0
            }
        },
        handleblur() {
            if (this.val == "") {
                this.float = 1
            }
        },
        handelist(index) {
            this.com = index
        }
    },
}
</script>

<style scoped lang="scss">
.page {
    background-color: #f8f8f8;
}

.page-img {
    width: 100%;
    height: 420rpx;
    background-image: url('../static/icon/assress.png');
    background-size: 100% 100%;
    position: relative;

    .logo-one {
        color: #FFFFFF;
        text-shadow: 0px 2px 2px 0px #0000003F;
        font-family: PingFang SC;
        font-weight: semibold;
        font-size: 72rpx;
        line-height: normal;
        position: absolute;
        left: 172rpx;
        margin-top: 80rpx;
    }

    .logo-two {
        position: absolute;
        right: 108rpx;
        bottom: 118rpx;
        color: #FB693B;
        text-shadow: 0px 4rpx 2rpx 0px #0000003F;
        font-family: PingFang SC;
        font-weight: semibold;
        font-size: 32rpx;
        line-height: normal;
    }

    .logo-three {
        position: absolute;
        right: 34rpx;
        bottom: 74rpx;
        color: #FFFFFF;
        text-shadow: 0px 4rpx 1rpx 0rpx #0000003F;
        font-family: PingFang SC;
        font-weight: semibold;
        font-size: 32rpx;
        line-height: normal;

    }
}

.search-box {
    width: 670rpx;
    background-color: white;
    border-radius: 40rpx;
    height: 80rpx;
    position: absolute;
    bottom: -40rpx;
    left: 40rpx;

    .search-icon {
        width: 36rpx !important;
        height: 36rpx !important;
        position: absolute;
        top: 22rpx;
        left: 50%;
        margin-left: -36rpx;
    }

    .tex {
        color: #D1D1D1;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 26rpx;
        line-height: normal;
        position: absolute;
        top: 24rpx;
        left: 360rpx;
    }

    input {
        position: absolute;
        top: 22rpx;
        left: 84rpx;
        font-size: 26rpx;
    }
}

.page-top {
    width: 654rpx;
    height: 72rpx;
    border-radius: 4rpx;
    background: #FCEAE9;
    margin-left: 42rpx;
    margin-top: 78rpx;
    display: flex;
    align-items: center;

    .tex-one {
        color: #C28524;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 26rpx;
        line-height: normal;
        margin-right: 34rpx;
    }

    .tex-two {
        color: #FB772D;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 26rpx;
        line-height: normal;
    }
}

.page-center {
    width: 652rpx;
    height: 72rpx;
    margin-left: 50rpx;
    border-radius: 36rpx;
    border: 2rpx solid #FB772D;
    margin-top: 40rpx;
    display: flex;
    box-sizing: border-box;

    view {
        width: 216rpx;
        height: 72rpx;
        border-radius: 30rpx;
        display: flex;
        justify-content: center;
        line-height: 72rpx;
        color: #FB772D;
        font-size: 26rpx;
    }
}

.active {
    background-color: #FB772D !important;
    color: white !important;
}

.page-bottom {
    margin-top: 40rpx;

    .page-bottom-one {
        text-align: center;
        color: #BDBDBD;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 26rpx;
        line-height: normal;
        margin-bottom: 40rpx;
    }
}

.page-bottom-two {
    width: 690rpx;
    margin: 0 auto;
    background-color:white;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
}

.page-bottom-top {
    width: 690rpx;
    height: 380rpx;
    background-image: url('../static/icon/assress.png');
    border-radius: 10rpx 10rpx 0px 0px;
    box-shadow: inset 0px -20rpx 60rpx 0px #000000;
    position: relative;

    image {
        width: 100rpx;
        height: 100rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-left: -50rpx;
        margin-top: -50rpx;
    }

    text {
        color: #FFFFFF;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 22rpx;
        line-height: normal;
        position: absolute;
        bottom: 20rpx;
        left: 28rpx;
    }
}

.page-bottom-center {
    margin-top: 18rpx;
    margin-left: 28rpx;
    color: #141414;
    font-family: PingFang SC;
    font-weight: medium;
    font-size: 32rpx;
    line-height: normal;
}

.page-bottom-bottom {
    margin-top: 8rpx;
    margin-left: 28rpx;
    position: relative;

    text:nth-child(1) {
        color: #FC4C4C;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 22rpx;
        line-height: normal;
        margin-right: 12rpx;
    }

    text:nth-child(2) {
        color: #FC4C4C;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 28rpx;
        line-height: normal;
        margin-right: 12rpx;
    }

    text:nth-child(3) {
        color: #FC4C4C;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 22rpx;
        line-height: normal;
        margin-right: 12rpx;
    }

    text:nth-child(4) {
        color: #8A929F;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 22rpx;
        line-height: normal;
    }
}
    .beijing{
        width: 146rpx;
        height: 48rpx;
        background-color: #FB772D;
        position: relative;
        bottom: 50rpx;
        left: 510rpx;
        text-align: center;
        color: white;
        line-height: 48rpx;
        border-radius: 24rpx;
    }
</style>