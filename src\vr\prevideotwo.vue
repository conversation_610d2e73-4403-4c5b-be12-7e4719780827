<template>
  <view class="page"  >   
    <view class="icon" @click="handletapicon">
      <my-icon type="ic_back" color="white" size="56rpx"></my-icon>
    </view>
    <!-- <view class="iconto" @click="handleCreat">
      <my-icon type="ic_fenxiang" color="white"  size="34rpx"></my-icon>
    </view> -->
       <!-- #ifdef H5 -->
    <view class="video" v-show="videos.length>0">
      <swiper :style="{'height':height}" class="banner" :indicator-dots="false"  :autoplay="false"  @change="onChange" :vertical="true" >
        <swiper-item v-for="(video,index) in videos" :key="index" >
          <view class="video_item" :class ='{"video_play_scale":show_pinglun}'>
         
            <!-- <view class="video-js" :ref="'video_play' + index" :id='"video_play" + index' style="width: 100%; height: 100%">
            </view> -->
            <video  class ="video_play"   
            :src="video.path" 
            style="width:100%;height: 100%;"  
            :id='"video_play"+index'  
            :show-fullscreen-btn="false" 
            :poster="video.cover"          
            :controls="false" :show-play-btn="false" 
            :show-center-play-btn="false"  
            :vslide-gesture-in-fullscreen="false"
             :enable-progress-gesture="false"
            :disable-touch ='show_pinglun' 
             :show-mute-btn="false" :title="title" 
            :direction="0" 
            x5-video-player-type="h5"
            :autoplay="autoplay"
            @play="playVideo" 
            @pause="pauseVideo" 
            preload="none"
            @waiting="waitingVideo" object-fit="contain"
            webkit-playsinline="" 
            :custom-cache="false"    
            >
                  <cover-image  v-show ="controls" class ="video-icon" @click ="firstPlayingAudio" src="/static/icon/video.png" ></cover-image>  
            </video>
            <!-- #endif -->
             <!-- #ifdef H5 -->
            <cover-view class="fix_right">
                <cover-view  class="prelogo" @click ="handletap">
                  <cover-image mode='widthFix' :src='video_logo.author_prelogo | imageFilter("m_80")' v-if="memberObj"></cover-image>
                </cover-view>
                <cover-view class="right_oper" @click ="collect">
                  <cover-view class="right_img">
                    <my-icon type="ic_zan" color="#fff" v-if="is_praise==0" size="52rpx"></my-icon>
                    <my-icon type="ic_zan_red" color="#d81e06"  v-if="is_praise==1" size="52rpx"></my-icon>
                    <!-- <cover-image mode='widthFix' v-if='is_follow==0'  :src='"/yidongduan/vr_video/collect.png" | imageFilter("m_80")'></cover-image>
                    <cover-image mode='widthFix' v-if='is_follow==1'  :src='"/yidongduan/vr_video/collect_ac.png" | imageFilter("m_80")'></cover-image> -->
                  </cover-view>
                  <cover-view class="right_con" :class="{red:is_praise==1}" >
                   点赞
                  </cover-view>
                </cover-view>   
              <cover-view class="right_oper" @click="shares">
                <cover-view class="right_img">
                  <cover-image mode='widthFix'
                    :src='"/yidongduan/vr_video/share.png" | imageFilter("m_80")'></cover-image>
                </cover-view>
                <cover-view class="right_con">
                  转发
                </cover-view>
              </cover-view>
              <cover-view class="right_oper" @click="ask">
                <cover-view class="right_img">
                  <cover-image mode='widthFix' :src='"/yidongduan/vr_video/ask.png" | imageFilter("m_80")'></cover-image>
                </cover-view>
                <cover-view class="right_con">
                  咨询
                </cover-view>
              </cover-view>
              <cover-view class="right_oper" @click="tel">
                <cover-view class="right_img">
                  <cover-image mode='widthFix' :src='"/yidongduan/vr_video/tel.png" | imageFilter("m_80")'></cover-image>
                </cover-view>
                <cover-view class="right_con">
                  电话
                </cover-view>
              </cover-view>
               </cover-view>
              <!-- <cover-view class="right_oper" @click='showPinglun'>
                  <cover-view class="right_img">
                    <cover-image mode='widthFix'  :src='"/yidongduan/vr_video/pinglun.png" | imageFilter("m_80")'></cover-image>
                  </cover-view>
                  <cover-view class="right_con">
                    评论
                  </cover-view>
                </cover-view> -->
            <!-- <template v-if ='video.info'> -->
              <view class="fix_bottom">
                <view class="title">@{{ logo}}</view>
                <view class="info_labels flex-row items-center">
                  <template v-for ="(item,idx) in lables">
                    <text :class="['info_label'+idx]" class ="info_label" v-if ='idx<3'  :key ="idx">{{item}}</text>
                  </template>
                </view>
                <view class="info_c">
                  {{address}}
                </view>
                <view class="info_btn" @click ="handletap">
                 查看详情
                </view>

              </view>
              

            </view>
            <!-- </template> -->
            <view class="pinglun" :class="{ active: show_pinglun }">
              <view class="title flex-row items-center">
                <view class="title_c">
                  12条评论
                </view>
                <view class="close" @click='show_pinglun = false'>
                  <image :src="'/zhaofang/<EMAIL>' | imageFilter('m_80')" mode='widthFix'></image>
                </view>
              </view>
              <view class="pinglun_list">
                <!-- <yVideoSlide :data ='pinglun_list'></yVideoSlide> -->
                <!-- <view class="pinglun_item flex-row " v-for ='(item,index) in pinglun_list' :key ="index">
                  <view class="pinglun_prelogo">
                    <image mode="widthFix" :src='item.prelogo'></image>
                  </view>
                  <view class="pinglun_con">
                    <view class="cname">
                      {{item.name}}
                    </view>
                    <view class="p_content">
                      {{item.content}}
                    </view>
                    <view class="p_oper flex-row items-center">
                      <view class="ctime">刚刚</view>
                      <view class="reply">
                        回复
                      </view>
                    </view>
                    
                  </view>
                  <template v-if ="item.children.length">
                    <pinglun :data="item"></pinglun>
                  </template>
                </view> -->
              </view>
              <!-- <input v-if ='show_pinglun'  class ="inp" :placeholder="placeholder" type="text"> -->

            </view>

        </swiper-item>
      </swiper>
    </view>
        <!-- #endif -->
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
  </view>
</template>

<script>
// #ifdef H5
// import videojs from 'video.js'
// import 'video.js/dist/video-js.min.css'
// import 'videojs-flash'
import shareTip from '@/components/shareTip'

// #endif
import allTel from '@/common/all_tel.js'
import getChatInfo from '@/common/get_chat_info'
import checkLogin from '@/common/utils/check_login'
import myIcon from '../components/myIcon.vue'
export default{
  components:{
    myIcon,
    // sharePop
    shareTip
  },
  data() {
    return {
      title: '',
      height: "100vh",
      float: '',
      popvideo:'',
      autoplay:'',
      videos: [
      ],
      query: {
        catid: '',
      },
      type: 1,
      loupan: '',
      monued: [],
      str: '',
      fload: 0,
      params: {
        page: 1,
        info_id: '',  

      },
      show_share_tip:false,
      pinglun_list: [
        {
          nickname: 'q',
          avatar: 'https://images.tengfangyun.com/images/icon/def_head.jpg?x-oss-process=style/w_80',
          content: "点赞",
          id: 1,
          replies: 1,
          commentChildren: [
            {
              nickname: 'b',
              id: 2,
              content: "回复q",
              avatar: 'https://images.tengfangyun.com/images/icon/def_head.jpg?x-oss-process=style/w_80',
              commentChildren: [
                {
                  nickname: 'b',
                  id: 3,
                  avatar: 'https://images.tengfangyun.com/images/icon/def_head.jpg?x-oss-process=style/w_80',
                  content: "回复b",
                },
              ]
            },


          ]
        }
      ],
      creat:{},
      detail:{},
      tel_res:{},
      current:0,
      show_pinglun:false ,
      placeholder:"回复：",
      show_tel_pop:false,
      memberObj:{},
      memberObjto:{},
      is_follow:0,
      id:'',
      todolist:{},
      uid:'',
      add:0,
      logo:'',
      lables:'',
      descs:'',
      address:'',
      page:1,
      is_praise:'',
      active:1,
      video_logo:'',
      video_path:'',
      commit:1,
      video_id:'',
      panduan:'',
      controls:true
    }
  },
  computed: {
    is_open_im() {
      return this.$store.state.im.ischat
    }
  },
  onLoad(options) {
    if (options.id) {
      this.params.info_id = options.id
      this.odd = options.id
      // options.index 
    }
    if (options.type) {
      this.type = options.type
    }
    // if (options.video_id) {
    //   this.params.video_id = options.video_id
    // }
    // if (options.catid) {
    //   this.query.catid = options.catid
    // }
    if (options.index) {
      this.storing = options.index
    }
    // this.getList()
    // this.getDetailto(this.params.info_id)
    this.getDetail()
    // this.onChange({target:{current:0}})
    // console.log(uni.getSystemInfoSync());
  },
  methods:{
    firstPlayingAudio(){
      let platform = uni.getSystemInfoSync().platform;
      if(platform === 'ios'){
        console.log("2222222222222")
      this.videoContext.play()
      this.controls =false
      // 苹果
      } else if(platform === 'android'){
            if (this.isClicked) return
          this.isClicked =true 
          console.log(this.videoContext,"123123");
          this.videoContext&& this.videoContext.play() // 安卓
      }
    },
    setTitle() {
      setTimeout(() => {
        this.title = '设置的title'
      }, 300);
    },
    // 点赞
    collect(){
      // console.log(this.odd)
      // console.log(this.is_praise)
      // this.autoplay =true
      if(this.is_praise==0){
        this.$ajax.post('video/videoPraise',{ id: parseInt(this.odd) },res=>{
          this.is_praise=1
        })
      }
      if(this.is_praise==1){
        this.$ajax.post('video/cancelVideoPraise',{ id: parseInt(this.odd) },res=>{

          this.is_praise=0
        })
      }
    },
    ask(){
      console.log(this.creat,)
      var user_id = this.memberObj.mid||this.memberObj.uid||this.memberObj.id
      var identity_id = this.memberObj.adviser_id||this.memberObj.uid||this.memberObj.id
      if(this.float==4){
        if(this.creat.infoData.is_adviser==0&&this.creat.infoData.is_agent==1){
          let id = this.creat.infoData.member.id
          this.$navigateTo("/pages/agent/detail?id="+id)
        }else if(this.creat.infoData.is_adviser==1&&this.creat.infoData.is_agent==0){
          let id = this.creat.infoData.member.adviser_id
          this.$navigateTo("/pages/consultant/detail?id="+id)
        }else if(this.creat.infoData.is_adviser==1&&this.creat.infoData.is_agent==1) {
          let id = this.creat.infoData.member.adviser_id
            this.$navigateTo("/pages/consultant/detail?id="+id)
        }else{
          // this.$navigateTo("/pages/consultant/detail?id="+id)
      //     uni.showToast({
      //   title: '普通会员',
      //   icon: 'none', 
      //   duration: 2000
      // }); 
      }      
        }
      this.advAsk({user_id:user_id,identity_id:identity_id})
    },
    advAsk(e) {
      if (this.is_open_im == 1) {
        //开聊天
        // #ifdef MP-WEIXIN
        if (this.float==1){
            this.$store.state.buildInfo = {
              id: this.creat.infoData.build.id,
              title: this.creat.infoData.build.title,
              type: 'build',
              image: this.creat.infoData.build.img
            }
          getChatInfo(e.user_id, 3, this.id)
        }
        if (this.float==2 ){
          let text = `${this.creat.infoData.house.fangjia?this.creat.infoData.house.fangjia+'万':'面议'}`
          let type='ershou'
          if (this.creat.infoData.house.parentid==2){
            text =`${this.creat.infoData.house.zujin?this.creat.infoData.house.zujin+'元/月':'面议'}`
            type='renting'
          }  
          this.$store.state.buildInfo = {
            id: this.creat.infoData.house.id,
            title:this.creat.infoData.house.title,
            type: type,
            image: this.creat.infoData.house.img,
            desc: `${this.creat.infoData.house.shi}室${this.creat.infoData.house.ting}厅${this.creat.infoData.house.wei}卫/${this.creat.infoData.house.mianji}m²/${this.creat.infoData.house.chaoxiang||''}`,
            price:``
          }
          console.log(this.memberObj,this.memberObj.id,this.creat.infoData.house.parentid)
          getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id,this.creat.infoData.house.parentid==1? 6:7)
        }
        if (this.float==3){   
           this.$store.state.buildInfo = {
             id: this.creat.infoData.estate.id,
             title: this.creat.infoData.estatetitle,
             type: 'commercial',
             catid: this.creat.infoData.estate.catid,
             image: this.creat.infoData.estate.img,
             desc: `${this.creat.infoData.estate.mianji}${this.creat.infoData.estate.mianji_unit}`,
             price:`${this.creat.infoData.estate.price?this.creat.infoData.estate.price+this.creat.infoData.estate.price_unit:'面议'}`
           }
           getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id, this.creat.infoData.estate.parentid==1?32:(this.creat.infoData.estate.parentid==2?33:34))
        }
        // this.$store.state.buildInfo = {
        //   id: this.id,
        //   title: this.detail.title,
        //   type: 'build',
        //   image: this.img[0]
        // }

        // #endif
        // #ifndef MP-WEIXIN
        checkLogin({
          success: (res)=>{
            if(this.float ==1 ){
              this.$store.state.buildInfo = {
                id: this.creat.infoData.build.id,
                title: this.creat.infoData.build.title,
                type: 'build',
                image: this.creat.infoData.build.img
              }
              getChatInfo(e.user_id, 3,this.creat.infoData.build.id.id)
            }
            if (this.float==2 ){
              // let thumb
              // this.imgs = this.detail.imgs
              // if(this.imgs.length>0){
              //   thumb = this.imgs[0]
              // }
              let text = `${this.creat.infoData.house.fangjia?this.creat.infoData.house.fangjia+'万':'面议'}`
              let type='ershou'
              if (this.creat.infoData.house.parentid==2){
                text =`${this.creat.infoData.house.zujin?this.creat.infoData.house.zujin+'元/月':'面议'}`
                type='renting'
              }

              this.$store.state.buildInfo = {
                id: this.creat.infoData.house.id,
                title: this.creat.infoData.house.title,
                type: type,
                image:  this.creat.infoData.house.img,
                desc: `${this.creat.infoData.house.shi}室${this.creat.infoData.house.ting}厅${this.creat.infoData.house.wei}卫/${this.creat.infoData.house.mianji}m²/${this.creat.infoData.house.chaoxiang||''}`,
                price:``
              }
              getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id,this.creat.infoData.house.parentid==1? 6:7)
            }
            if (this.float==3 ){
               this.$store.state.buildInfo = {
                id: this.creat.infoData.estate.id,
                title: this.creat.infoData.estate.title,
                type: 'commercial',
                catid: this.creat.infoData.estate.catid,
                image: this.creat.infoData.estate.img,
                desc: `${this.creat.infoData.estate.mianji}${this.creat.infoData.estate.mianji_unit}`,
                price:`${this.creat.infoData.estate.price?this.creat.infoData.estate.price+this.creat.infoData.estate.price_unit:'面议'}`
              }
              getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id,this.creat.infoData.estate.parentid==1?32:(this.creat.infoData.estate.parentid==2?33:34))
            }

          },
          fail: (res) => {
            // TODO
            this.$navigateTo("/user/login/login")
          },
          complete: (res) => {
            this.$store.state.user_login_status = res.status
          }
        })
        // #endif
      } else if (this.is_open_im == 0) {
        //不开聊天
        if (this.type == 1) {
          this.consuDetail(e.identity_id)
        } else {
          //  this.toAgentDetail(e.identity_id)
        }

      }
    },
    // toAgentDetail(id){
    //   this.$navigateTo('/pages/agent/detail?id=' + id)
    // },
    //转到顾问详情
    consuDetail(id) {
      if (!id) return
      if (this.is_open_adviser == 1 && this.detail.open_adviser == 1) {
        this.$navigateTo('/pages/consultant/detail?id=' + id)
      } else {
        console.log("没开启聊天且不是置业顾问,不跳转详情")
      }
    },
    // #ifdef H5  
    tel(){
      console.log(this.float)
      // if(this.type==1){
        // console.log(this.loupan,"321312")
        console.log(this.float)
        console.log(this.creat)
        this.callBuildMiddleNumber()
      // }
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    // 拨打楼盘虚拟号码
    callBuildMiddleNumber() {
        if(this.creat.infoData.length==0){
          uni.showToast({
            title: '此楼盘没有绑定联系电话',
            icon: 'none'
          }) 
          return  
      }
      if (this.float==1){
            if(this.creat.infoData.mountMembers.length>0){
                  if(this.creat.infoData.mountMembers[0].isAdviser==1&&this.creat.infoData.mountMembers[0].isAgent==1){
                    this.callMiddleNumber(2,this.creat.infoData.mountMembers[0].id,8,this.creat.video.id,'video_detail','')
                  }else if(this.creat.infoData.mountMembers[0].isAdviser==1&&this.creat.infoData.mountMembers[0].isAgent==0){
                    this.callMiddleNumber(2,this.creat.infoData.mountMembers[0].id,8,this.creat.video.id,'video_detail','')
                  }else if(this.creat.infoData.mountMembers[0].isAdviser==0&&this.creat.infoData.mountMembers[0].isAgent==1){
                    this.callMiddleNumber(3,this.creat.infoData.mountMembers[0].id,8,this.creat.video.id,'video_detail','')
                  }else{
                    this.callMiddleNumber(0,this.creat.infoData.mountMembers[0].id,8,this.creat.video.id,'video_detail','')
                  }
            }else{
              this.callMiddleNumber(1,this.creat.infoData.build.id,8,this.creat.video.id,'video_detail',this.creat.infoData.build.id) 
            }
      }
      if(this.float==2){
        console.log("32112")
          this.tel_params={
            type: 4,
            callee_id: this.creat.infoData.house.id,
            scene_type: 8,
            scene_id: this.creat.video.id,
            source:'video_detail'
          }
          allTel(this.tel_params)      
        // this.callMiddleNumber(4, this.detail.id,1,this.detail.id,'build_detail',this.id)
      }
      if(this.float==3 ) {
        this.tel_params={
          type: 6,
          callee_id: this.creat.infoData.estate.id,
          scene_type: 8,
          scene_id: this.creat.video.id,
          source:'video_detail'
        }
        allTel(this.tel_params)
      }
      if(this.float==4) {
        if(this.creat.infoData.is_adviser==1){
          this.callMiddleNumber(2,this.creat.infoData.member.adviser_id,8,this.creat.video.id,'video_detail','')
        }else if(this.creat.infoData.is_agent==1){
          this.callMiddleNumber(3,this.creat.infoData.member.id,8,this.creat.video.id,'video_detail','')
        }else {
          this.callMiddleNumber(0,this.creat.infoData.member.id,8,this.creat.video.id,'video_detail','')
        }
       
      }
    },
    // 请求虚拟号接口
    callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
      this.tel_params = {
        type,
        callee_id,
        scene_type,
        scene_id,
        source,
        bid,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
        // #endif
      // #ifdef MP-WEIXIN
      allTel(this.tel_params)
      // #endif
      // #ifndef MP-WEIXIN
      // 全局开启中间号且楼盘开启中间号需要检测登录
      if(this.is_open_middle_num == 1 && this.detail.use_middle_call > 0){
        this.tel_params.intercept_login = true
        this.tel_params.fail = (res)=>{
          if(res.data.code === -1){
            this.$store.state.user_login_status = 1
            this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
          }
          if(res.data.code === 2){
            this.$store.state.user_login_status = 2
            this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
          }
        }
        allTel(this.tel_params)
      }else{
        allTel(this.tel_params)
      }
    },
     // #endif
   // #ifdef H5
   shares(){
      // let link = '';
      // let time =parseInt(+new Date()/1000)
      // if (this.currentUserInfo.sid){
      //   link="https://"+window.location.host+"/h5/pages/new_house/detail?id="+this.detail.id +"&isShare=1&shareType="+this.currentUserInfo.shareType+"&shareId="+this.currentUserInfo.sid+'&cusId=' +this.current_adviser_id +'&isshare=' +this.isshare+"&f_time="+time
      // }else {
        // link = window.location.href
      // }
      console.log(this.share.link,"sharessharessharesshares")
      this.show_share_tip =true
      // this.copyWechatNum(  this.share.link, ()=>{
      //   uni.showToast({
      //     title: '复制成功,去发送给好友吧',
      //     icon: 'none'
      //   })
      // })
    },
    // #endif
    // #ifndef H5
    copyWechatNum(cont) {
      uni.setClipboardData({
        data: cont,
        success: res => {
          // uni.showToast({
          //   title: "复制成功",
          //   icon: "none"
          // })
        }
      })
    },
    // #endif
    // #ifdef H5
    copyWechatNum(cont, callback) {
      let oInput = document.createElement('textarea')
      oInput.value = cont
      document.body.appendChild(oInput)
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      oInput.blur()
      oInput.remove()
      if(callback) callback()
    },
    // #endif
     // #ifdef H5
     onChange(e,c){
      let id=  'video_play'+e.target.current
      this.current = e.target.current
      if( this.loadmore && this.current== this.videos.length-1 &&this.videos.length>=1 ) {
        if (this.isloading ) return 
        this.isloading =true
        this.page +=1
         this.getDetail(this.videos[this.current].id)
      }
      if(this.commit==2){
        this.getDetailto(this.videos[this.current].id)
      }
      let platform = uni.getSystemInfoSync().platform;
    if(platform === 'ios'){
      console.log("我是苹果哈哈哈哈或或或或或")
      this.videoContext = uni.createVideoContext(id,this); 
        this.videos.map((item,index)=>{
          let temp = "video_play" + index;
          // console.log(id,'ididididdidi')
          if(temp !=id){
             uni.createVideoContext(temp, this).pause();
          }
        })
        try {
          if (this.autoplay){
            this.controls = true
          }
          
        } catch (error) {
        }
        if (!this.isClicked && this.commit ==1){
           this.autoplay=false
        }else {
           this.autoplay=true
           this.controls = true
        }
        this.commit =2
      //  安卓
     } else if(platform === 'android'){
      this.videoContext = uni.createVideoContext(id,this); 
        this.videos.map((item,index)=>{
          let temp = "video_play" + index;
          if(temp !=id){
             uni.createVideoContext(temp, this).pause();
          }
        })
        try {
          if (this.autoplay){
            this.videoContext.play()
          }       
        } catch (error) {
        }
        if (!this.isClicked && this.commit ==1){
           this.autoplay=false
        }else {
           this.autoplay=true
          this.videoContext.play()
        }
        this.commit =2
      } 
        
    },
    handletap() {
        let type=''
        console.log(this.float)
        console.log(this.creat)
      if(this.float==1){
        this.$navigateTo("/pages/new_house/detail?id=" +this.creat.infoData.build.id);
        return
      }
      if (this.float == 2&&this.creat.infoData.length!=0) {
        if (this.detail.parentid == 1) {
          type = 'ershou'
        } else {
          type = 'renting'
        }
        this.$navigateTo("/pages/"+type+"/detail?id=" +this.creat.infoData.house.id)
        return
      }
      if(this.float==3){
        if (this.creat.infoData.estate.parentid ==1){
          type='sale'
        }else if(this.creat.infoData.estate.parentid ==2) {
          type='rent'
        }else {
          type='transfer'
        }
        this.$navigateTo("/commercial/"+type+"/detail?id=" +this.creat.infoData.estate.id)
        }
      if(this.float==4){
        if(this.creat.infoData.is_adviser==0&&this.creat.infoData.is_agent==1){
          let id = this.creat.infoData.member.id
          console.log(id,"hhhh")
          this.$navigateTo("/pages/agent/detail?id="+id)
        }else if(this.creat.infoData.is_adviser==1&&this.creat.infoData.is_agent==0){
          let id = this.creat.infoData.member.adviser_id
          this.$navigateTo("/pages/consultant/detail?id="+id)
        }else if(this.creat.infoData.is_adviser==1&&this.creat.infoData.is_agent==1) {
          let id = this.creat.infoData.member.adviser_id
            this.$navigateTo("/pages/consultant/detail?id="+id)
        }else{
          console.log("22222")
          // this.$navigateTo("/pages/consultant/detail?id="+id)
      //     uni.showToast({
      //   title: '普通会员',
      //   icon: 'none', 
      //   duration: 2000
      // }); 
      }
       
        }
        
        },
    getList(){
      this.$ajax.get('video/getVideoList',{catid:this.query.catid,page:this.page,filter_id:this.odd,type:this.type},(res=>{
        if (res.data.code ==1){
            if(this.commit ==1){
              res.data.list.unshift(this.detail)
              
            }
            this.popvideo =1
          if (this.page ==1) {
            this.videos = res.data.list
            

          } else {
            this.videos = this.videos.concat(res.data.list) 
          }
          // console.log(this.videos)
          // this.todolist = this.videos
          this.isloading =false
          if(res.data.list.length==0) {
            this.loadmore =false
          }else {
            this.loadmore =true
          }
          if (this.page ==1){
            this.$nextTick(()=>{
              this.onChange({target:{current:0}})
            })
            
          }
        }else {
          uni.showToast({
            title:res.data.msg,
            icon:"none"
          })
        }
      }))  
  },
  getDetail(vid,coback){
      let url ='video/videoDetail'
      if(this.active==1){
        vid = this.odd
      }
      this.$ajax.get(url,{id:vid},res=>{
        if(res.data.code==1){
        if(res.data.share) {
          this.share = res.data.share
        }else {
          this.share ={}
        }
        this.share.link="https://"+window.location.host+"/h5/vr/prevideotwo?id="+vid+"&type="+this.float
         this.getWxConfig()
          this.active=2
        this.detail  = res.data.video
        this.video_path = res.data.video.path
        this.video_logo= res.data.video
        this.is_praise = res.data.is_praise
        this.float = res.data.video.info_type
        this.address = res.data.video.descp
        this.lables = ''
        this.creat = res.data
        this.video_logo= res.data.video
        this.logo = res.data.video.title
        this.cover =res.data.video.cover
        this.getList()
       uni.setNavigationBarTitle({
            title: this.logo,
        })
        this.video_id = vid
        if (this.float==1){
          this.detail  = res.data.video
          this.lables = res.data.infoData.build.build_types
          this.memberObj = res.data.infoData.mountMembers[0]
        }
        if (this.float==2&& res.data.infoData.length!=0 ){
          this.lables = res.data.infoData.house.label
          this.memberObj = res.data.infoData.agent
          this.memberObjto = res.data.infoData.house
        }
        if ( this.float==3){
          
          var arr = []
          for (var key in  res.data.infoData.estate.label){
              console.log(key,res.data.infoData.estate.label[key].name,"hhhhhhhh")
              arr.push(res.data.infoData.estate.label[key].name)
                
          }
          console.log(arr,"Arrarrarrarr")
          this.lables = arr
          // this.detail  = res.data.estate
          this.memberObj = res.data.infoData.agent
        }
        if(this.float ==4){
          console.log(res.data.video.title)
          this.memberObj = res.data.infoData.member
        }
        }
     
      })
    },
    getDetailto(vid){
      let url ='video/videoDetail'
      if(this.active==1){
        vid = this.odd
      }
      this.$ajax.get(url,{id:vid},res=>{
        if(res.data.code==1){
          this.active=2
          this.video_logo= res.data.video
        this.video_path = res.data.video.path
        this.is_praise = res.data.is_praise
        this.float = res.data.video.info_type
        this.address = res.data.video.descp
        this.video_logo= res.data.video
        this.lables = ''
        this.creat = res.data
        this.logo = res.data.video.title
       uni.setNavigationBarTitle({
            title: this.logo,
        })
        if(res.data.share) {
          this.share =res.data.share
        }else {
          this.share = {}
        }
        this.share.link="https://"+window.location.host+"/h5/vr/prevideotwo?id="+vid+"&type="+this.float
         this.getWxConfig()
        this.video_id = vid
        if (this.float==1){
          this.detail  = res.data.video
          this.lables = res.data.infoData.build.build_types
            this.memberObj = res.data.infoData.mountMembers[0]
        }
        if (this.float==2&& res.data.infoData.length!=0 ){
          this.lables = res.data.infoData.house.label
          this.memberObj = res.data.infoData.agent
          this.memberObjto = res.data.infoData.house
        }
        if ( this.float==3){     
          var arr = []
          for (var key in  res.data.infoData.estate.label){
              console.log(key,res.data.infoData.estate.label[key].name,"hhhhhhhh")
              arr.push(res.data.infoData.estate.label[key].name)       
          }
          console.log(arr,"Arrarrarrarr")
          this.lables = arr
          // this.detail  = res.data.estate
          this.memberObj = res.data.infoData.agent
        }
        if(this.float ==4){
          console.log(res.data.video.title)
          this.memberObj = res.data.infoData.member
        }
        }
     
      })
    },

    waitingVideo() {
    },
    playVideo(index) {
      this.videoContext = null
      this.videoContext = uni.createVideoContext('video_play' + index);
      this.videoContext.play()
      this.controls =false
    },

    pauseVideo() {

    },
    showPinglun() {
      // this.videoContext.requestFullScreen()
      this.show_pinglun = true
    },
    // handleCreat(){
    //   this.$refs.share_popup.show()
    //   this.$navigateTo(`${location.origin}/wapi/poster/branch?type=1&id=${this.id}&shareId=${this.currentUserInfo.sid}&&shareType=${this.currentUserInfo.shareType}`)
    // },
    handletapicon(){
      this.$navigateBack()
    }
    }
}
//  #endif
</script>

<style lang="scss" scoped>
// #ifdef h5
@import './static/css/video-js.css';

//  #endif
.page {
  background: #000;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.video_item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  flex-direction: column;
  position: relative;

  &.video_play_scale {
    align-items: flex-start;
    justify-content: flex-start;

    .video_play {
      height: 300rpx;
    }
  }
}

video {
  z-index: 1;
}

.fix_right {
  position: absolute;
  top: 50vh;
  right: 48rpx;
  transform: translateY(-50%);
  z-index: 1000;

  .prelogo {
    width: 80rpx;
    height: 80rpx;
    margin-bottom: 60rpx;
    overflow: hidden;
    border-radius: 50%;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .right_oper {
    margin-bottom: 48rpx;
    text-align: center;

    .right_img {
      width: 48rpx;
      height: 48rpx;
      margin: 0 auto;
      overflow: hidden;

      image {
        width: 100%;
      }
    }

    .right_con {
      margin-top: 10rpx;
      font-size: 24rpx;
      color: #F8F8F8;

      &.red {
        color: red;
      }
    }
  }
}

.fix_bottom {
  z-index: 111;
  position: absolute;
  bottom: 0;
  height: 400rpx;
  left: 0;
  right: 0;
  padding: 20rpx 48rpx;

  .title {
    color: #FFFFFF;
    font-size: 36rpx;
    font-weight: 600;
    padding: 18rpx 0;
    width: 70%;
  }

  .info_labels {
    // color: #FFD952;
    font-size: 24rpx;
    margin: 20rpx 0;

    .info_label {
      margin-right: 5rpx;
      line-height: 1.3;
      font-size: 22rpx;
      color: #fff;


      // border: 2rpx solid #FFD952;
      padding: 4rpx 8rpx;

      // border-radius: 4rpx;
      &.info_label0 {
        border-radius: 4rpx;
        background: linear-gradient(90deg, #FF3030 0%, #FF8939 100%);
        // background: radial-gradient(circle at 50% 50%, #FF3030 0%, #FF4C33 47%, #FF8939 98%);
        // box-shadow: 0 -4rpx 8rpx 0 #F20401;
      }

      &.info_label1 {
        border-radius: 4rpx;
        background: linear-gradient(90deg, #FF1D61 0%, #EF3C87 100%);

      }

      &.info_label2 {
        border-radius: 4rpx;
        background: linear-gradient(90deg, #0A81F3 0%, #4DBCFD 100%);
      }

    }
  }

  .info_c {
    color: #fff;
    font-size: 24rpx;
    margin-top: 16rpx;
    overflow: hidden;
    width: 100%;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3; // 表示需要显示的行数
    -webkit-box-orient: vertical;
  }

  .info_btn {
    display: inline-block;
    margin-top: 20rpx;
    padding: 5rpx 10rpx;
    font-size: 22rpx;
    color: #fff;
    background: #FFFFFF19;
  }
}

.pinglun {
  border-radius: 20rpx 20rpx 0px 0px;
  background: #FFFFFF;
  width: 100%;
  // position: absolute;
  // bottom:0;
  // left: 0;
  // right: 0;
  position: relative;
  height: 0;
  overflow-y: auto;
  transition: 0.3s;

  &.active {
    height: 600rpx;
    transition: 0.3s;
  }

  .title {
    position: relative;
    padding: 20rpx 0;
    justify-content: center;

    .title_c {
      color: #222222;
      font-size: 24rpx;
      text-align: center;
    }

    .close {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 40rpx;
      height: 40rpx;

      image {
        width: 100%;
      }

    }
  }

  .pinglun_list {
    padding: 28rpx 40rpx 80rpx;
    position: relative;

    .pinglun_item {
      margin-bottom: 40rpx;

      .pinglun_prelogo {
        width: 72rpx;
        height: 72rpx;
        min-width: 72rpx;
        margin-right: 24rpx;
        overflow: hidden;
        border-radius: 50%;

        image {
          width: 100%;
        }
      }

      .pinglun_con {
        .cname {
          color: #ABA6A6;
          font-size: 26rpx;
        }

        .p_content {
          color: #222222;
          font-size: 28rpx;
        }

        .p_oper {
          margin-top: 20rpx;

          .ctime {
            color: #ABA6A6;
            margin-right: 40rpx;
            font-size: 26rpx;
          }

          .reply {
            color: #737373;
            font-size: 26rpx;
          }
        }
      }
    }
  }

  .inp {
    position: absolute;
    background: #F3F3F5;
    height: 72rpx;
    line-height: 72rpx;
    bottom: 20rpx;
    left: 48rpx;
    right: 48rpx;
    border-radius: 48rpx;
  }

  // .video_play{
  //   &.video_play_scale{

  //   }

  // }
}

.icon{
        z-index: 100;
        position: absolute;
        top: 30rpx;
        left: 20rpx; 
}
.iconto{
  position: absolute;
  z-index: 100;
  top: 30rpx;
  right: 25rpx;
}
.video-icon {
    width: 100rpx;
    height: 100rpx;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    position: absolute;
    margin: auto;
}
</style>
