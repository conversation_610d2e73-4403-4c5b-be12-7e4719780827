.liked{
	/* display: inline-block; */
	position: relative;
	margin-right: 10upx;
	width: 34upx;
	height: 34upx;
}
.post-username{
	font-size:32upx;
	font-weight: 600;
	color: #333;
}
.gallery_img {
	width: 100%;
	height: 100%
}
.post_header {
	width: 80upx !important;
	height: 80upx !important;
	border-radius: 50%;
}
#moments {
	background: #fff;
}
#moments .moments__post {
	background: #fff;
	display: block;
	padding: 20upx;
	position: relative;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
}
#moments .moments__post .post_right {
	font-size: 32upx;
	display: table-cell;
	padding-left: 20upx;
	width: 100%;
}
#moments .paragraph{
	margin-top: 10upx;
	line-height: 1.6;
	word-break: break-all;
}
#moments .moments__post .thumbnails {
	width: 100%;
	margin-top: 8upx;
	display: flex;
	flex-wrap: wrap;
}
#moments .moments__post .thumbnails .thumbnail {
	width: 30%;
	height: 180upx;
	margin: 4upx;
	background: #757575;
	overflow: hidden;
}
.my-gallery{
	width: 68%;
	max-height: 600upx;
	font-size: 0;
	margin: 4upx;
	/* background: #757575; */
	overflow: hidden
}
.my-gallery-video{
	width: 490rpx;
	margin: 4upx;
	font-size: 0;
	position: relative;
	overflow: hidden
}
.video-icon {
	width: 120rpx;
	height: 120rpx;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.4);
}
#moments .moments__post .toolbar {
	position: relative;
	top: 10upx;
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center
}
#moments .moments__post .toolbar .timestamp {
	color: #757575;
	font-size: 22upx;
}
#moments .moments__post .like {
	width: auto;
	height: auto;
	padding: 10upx;
	position: absolute;
	right: 60upx;
	display: flex;
	align-items: center;
}
#moments .moments__post .comment {
	width: auto;
	height: auto;
	padding: 10upx;
	position: absolute;
	right: 0upx;
	display: flex;
	align-items: center;
}
#moments .moments__post .toolbar image{
	padding-left: 20upx;
	width: 40upx;
	height: 40upx;
}
.post-footer{
	margin-top: 30upx;
	box-sizing: border-box;
	background-color: #f3f3f5;
	width: 100%;
}
#moments .moments__post .like-list.footer_content{
	padding-top: 10upx;
}
#moments .moments__post .footer_content {
	padding: 0 10upx 10upx 10upx;
	position: relative;
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap
}
#moments .moments__post .footer_content .nickname {
	color: #36648B;
	font-size: 24upx
}
#moments .moments__post .footer_content .comment-nickname {
	color: #36648B;
	font-size: 24upx
}
#moments .moments__post .footer_content .comment-content {
	color: #333333;
	font-size: 24upx
}
#moments .del{
	padding: 10upx;
	font-size: 22upx;
	color: #36648B;
}
.foot {
	position: fixed;
	width: 100%;
	height: 90upx;
	min-height: 90upx;
	left: 0upx;
	bottom: 0upx;
	overflow: hidden;
}