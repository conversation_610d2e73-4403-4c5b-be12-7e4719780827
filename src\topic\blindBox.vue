<template>
  <!-- :style="{backgroundImage:`url(${huawenBackground})`}" -->
  <view class="page" :style="{background:blindBox.bg_color ||'#f00'}" v-if = "show">
    <view class="topImg">
      <image mode ="widthFix" :src ="blindBox.bg_image| imageFilter('w_6401')">
      </image>
    </view>

    <view class="top">
      <view class="choujiangji_t" :style="{backgroundImage:`url(${choujiang_top})`}">
        <view class="choujiang_top">
          {{blindBox.participant_count ||0}}人参与/{{blindBox.view_count||0}}人浏览
        </view>
        <view class="may_jiangpin_c "> </view>
          <view class="may_jiangpin flex-row items-center">
            <view class="left_icon">
             <image mode ="widthFix" :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter('w_320')"></image>
            </view>
            <view class="may_text">
              可能开出的奖品
            </view>
          </view>
      </view>
      <view class="choujiangji_m"  :style="{backgroundImage:`url(${choujiang_middle})`}">
        <view class="swiper">
          <view class="swiper_c">
            <swiper
              class="banner"
              :indicator-dots="indicatordots" :circular="true" :duration="300" indicator-active-color="#fff"
            >
              <template v-for="(item,index) in jiangpinList" >
                <swiper-item  :key="index">
                  <view class ="swiper_con">
                    
                    <view class ="swiper_item" :class ="jiangpin.id" v-for= "jiangpin in item" :key="jiangpin.id">
                        <view class="jp_img">
                          <image mode="widthFix" :src="jiangpin.cover"></image>
                        </view>
                        <view class="jp_name">
                          {{jiangpin.title}}
                        </view>
                        
                    </view>
                    <view class="swiper_item_img">
                      <image :src="`/yidongduan/blindBox/<EMAIL>`"></image>
                    </view>
                  </view>
                </swiper-item>
              </template>
              
            </swiper>
          </view>
        </view> 
      </view>
      <view class="choujiangji_b"  :style="{backgroundImage:`url(${choujiang_bottom})`}">
        <view class="choujiang_desc">
          <view class="choujiang_desc_text" v-if ='activity_status ==1'>
            {{blindBox.stime}} - {{blindBox.etime}} 每天 {{blindBox.day_stime}} 可拆盒
          </view>
          <view class="choujiang_desc_text" v-if ='activity_status ==2'>
            活动已结束
          </view>
          <view class="choujiang_desc_text" v-if ='activity_status ==0'>
            活动未开始
          </view>
          <view class="choujiang_desc_num" >
            <view class ="choujiang_desc_num_c" :style="{backgroundImage:`url(${choujiang_num_bg})`}">
              今日可拆次数: {{Number(userBoxData.box_surplus_num).toFixed(1)}}
            </view>
            
          </view>


        </view>
        <view class="choujiang_content" >
          <view class="choujiang_content_info">
            场次NO{{activity_number}},剩余{{surplus}}个
          </view>
          <view class="blind_box" :style="{backgroundImage:`url(${choujiang_box})`}">
            <view class="blind_box_con" >
              <view class="blind_box_con" ref = 'blind_box_con' scroll-x="true"   v-if ="visibleData.length" @scroll="handleScroll"  @touchstart ="touchStart"   @touchend = "touchEnd" @scrolltolower ="scrollToLower" @scrolltoupper = "scrollToUpper" :show-scrollbar="true">
              <view class ='blind_box_con_c' :style="{ width:width+'rpx','animation-duration':animationDuration+'s'}"   :class="{stop_scroll:isParse}">
              <template  v-for ='(items,idx) in visibleData'>
              <view class="blind_box_item  items-center" :id ="'jiangchi'+(idx+1)"  :key ="idx"   >
                <template  v-for ="(item,index) in items" >
                <view class="blind_item" @click.prevent.stop  ='clickJiangpin(item)' :key ="index" >
                  <view class="blind_item_num" >
                   {{item.title}}
                  </view>
                  <view class="blind_item_img" :style ="{backgroundImage:`url(${blind_lihe})`}">
                    <view class="logo_img" v-if ="blindBox.blind_box_cover" :style ="{backgroundImage:`url(${blindBox.blind_box_cover})`}">
                    </view>
                    <view class="open_status" >
                    <view class="status_name" :class ="{is_open :item.is_opened}">
                      {{item.is_opened?"已抽":"未抽"}}
                    </view>
                  </view>
                  </view>
                </view>
                </template>
              </view>
              </template>
               </view>
                <!-- <view class='inline-block' :style="{display:inline-block,width:leftZhanweiW+'px'}"></view> -->
               <!-- </view> -->
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="task" v-if ="activity_status==1">
        <view class="task_title">
          轻松赚盲盒碎片
        </view>
        <view class="task1">
          <view class="task_item flex-row items-center" v-if ="blindBox.is_visit_collect">
            <view class="task_item_icon">
              <image mode="widthFix" :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter"></image>
            </view>
            <view class="task_item_middle flex-1">
              <view class="task_item_middle_title">
                每日访问得盲盒
              </view>
              <view class="task_item_middle_info">
                +{{blindBox.visit_collect_num}}盲盒
              </view>
            </view>
            <view class="task_item_oper" @click ="finishTask('visit')" :class ="{finished:userTaskData&&userTaskData.visit}">
              {{userTaskData&&userTaskData.visit?"明日再来":'未完成'}}
            </view>

          </view>
           <view class="task_item flex-row items-center" v-if ="blindBox.is_subscribe_wechat_collect" >
            <view class="task_item_icon">
              <image mode="widthFix" :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter"></image>
            </view>
            <view class="task_item_middle flex-1">
              <view class="task_item_middle_title">
                关注微信公众号
              </view>
              <view class="task_item_middle_info">
                额外赠送{{blindBox.subscribe_wechat_collect_num}}盲盒
              </view>
            </view>
            <view class="task_item_oper" :class ="{finished:userTaskData&&userTaskData.subscribe_wechat}" @click ="finishTask('subscribe_wechat')">
               {{userTaskData&&userTaskData.subscribe_wechat?"已关注":'去关注'}}
            </view>

          </view>
        </view>
        <view class="task1">
          <view class="task_item flex-row items-center" v-if ="blindBox.is_view_video_collect" @click ="checkTask('view_video')">
            <view class="task_item_icon">
              <image mode="widthFix" :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter"></image>
            </view>
            <view class="task_item_middle flex-1">
              <view class="task_item_middle_title">
                完整观看视频
              </view>
              <view class="task_item_middle_info">
                +{{blindBox.view_video_collect_num}}盲盒
              </view>
            </view>
            <view class="task_item_oper" :class ="{finished:userTaskData&&userTaskData.view_video}">
              {{userTaskData&&userTaskData.view_video?"已完成":'去观看'}}
            </view>

          </view>
           <view class="task_item flex-row items-center" @click ="checkTask('view_build')" v-if ="blindBox.is_view_build_collect">
            <view class="task_item_icon">
              <image mode="widthFix" :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter"></image>
            </view>
            <view class="task_item_middle flex-1">
              <view class="task_item_middle_title">
                浏览关联的楼盘
              </view>
              <view class="task_item_middle_info">
                +{{blindBox.view_build_collect_num}}盲盒
              </view>
            </view>
            <view class="task_item_oper" :class ="{finished:userTaskData&&userTaskData.view_build}">
             {{userTaskData&&userTaskData.view_build?"已完成":'去浏览'}}
            </view>

          </view>
          <view class="task_item flex-row items-center" @click ="checkTask('view_content')"   v-if ="blindBox.is_view_content_collect">
            <view class="task_item_icon">
              <image mode="widthFix" :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter"></image>
            </view>
            <view class="task_item_middle flex-1">
              <view class="task_item_middle_title">
                浏览品牌介绍15秒
              </view>
              <view class="task_item_middle_info">
                +{{blindBox.view_content_collect_num ||0}}盲盒
              </view>
            </view>
            <view class="task_item_oper" :class ="{finished:userTaskData&&userTaskData.view_content}">
             {{userTaskData&&userTaskData.view_content?"已完成":'去浏览'}}
            </view>

          </view>
        </view>
        <view class="task1">
          <view class="task_item flex-row items-center"  @click ="toInvite" v-if='blindBox.is_help_collect'>
            
              <view class="task_item_icon">
                <image mode="widthFix" :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter"></image>
              </view>
              <view class="task_item_middle flex-1" >
                <view class="task_item_t flex-row flex-1 items-center" >
                  <view class="task_item_t_l flex-1">
                    <view class="task_item_middle_title">
                      邀请好友得盲盒
                    </view>
                    <view class="task_item_middle_info">
                      + {{blindBox.help_collect_num ||0}}盲盒
                    </view>
                  </view>
                  <view class="task_item_oper" >
                    <!-- :class ="{finished:userTaskData&&userTaskData.help }" -->
                    <!-- <view class="task_item_oper_c"> -->
                      去邀请{{userTaskData&&userTaskData.help?userTaskData.help :0}}/{{blindBox.every_help_person}}
                    <!-- </view> -->
                      
                  </view>
                  </view>
                <view class="task_item_b">
                  <view class="task_item_b_prelogo flex-row items-center" > 
                    <template v-if ="userHelpData.length">
                      <view class="task_item_b_prelogo_img "  v-for ="(item,index) in userHelpData" :key ="index">
                         <image class ="logo_item" :src ="item.prelogo  | imageFilter('w_80')" mode ="widthFix"></image>
                      </view>
                     
                    </template>
                    <view class="task_item_b_prelogo_img " >
                         <image class ="logo_item"  :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter('m_80')"  mode ="widthFix"></image>
                      </view>
                  </view>
                </view>
              </view>
              
            </view>
            
            

          
        </view>
        <view class="task_right_icon">
          <image :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter('m_120')" mode="widthFix" ></image>
        </view>
      </view>

      <view class="task">
        
        <view class="task_title">
          逛一逛
        </view>
        <view class="other task1 flex-row items-center">
          <view class="other_item flex-1"  @click ="toSeeVideo">
            <view class="other_icon">
              <image  mode="widthFix"  :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter('w_320')">

              </image>
            </view>
            <view class="other_name">
              精选视频
            </view>
          </view>
          <view class="other_item flex-1"  @click ="toSeeBuild">
            <view class="other_icon">
              <image  mode="widthFix"  :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter('w_320')">

              </image>
            </view>
            <view class="other_name">
              楼盘
            </view>
          </view>
          <view class="other_item flex-1"  @click ="toSeePinpai">
            <view class="other_icon">
              <image mode="widthFix" :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter('w_320')">

              </image>
            </view>
            <view class="other_name">
              品牌介绍
            </view>
          </view>

          
        </view>
        <view class="task_right_icon">
          <image :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter('m_120')" mode="widthFix" ></image>
        </view>
      </view>

      
    </view>
    <view class="tanmu">
      <view class="tanmu_c" id ="tanmu">
        <template v-for ="(item) in tanmuList">
          <!-- <template v-if ="index<tanmuList.length/2"> -->
            <view class="tanmu_item"  :key ="item.id">
              <view class="prelogo">
                <image :src="item.prelogo"  alt="">
              </view>
              <view class="desc">{{item.desc}}</view> 
            </view>
          <!-- </template> -->
          <!-- <template v-if ="index>=tanmuList.length/2">
            <view class="tanmu_item"  :key ="item.id">
              <view class="prelogo">
                <image :src="item.prelogo"  alt="">
              </view>
              <view class="desc">{{item.name}}{{item.desc}}</view> 
            </view>
          </template> -->
        </template>
        
      </view>
    </view>

    <!-- 右侧按钮 -->
    <view class="right_oper">
      <view class="right_oper_share" @click="toShare">
        分享
      </view>
      <view class="right_oper_rule right_oper_share" @click ="showRules">
        规则
      </view>
      <view class="right_oper_prixe right_oper_share" @click ="toMyPrize">
        我的奖品
      </view>
    </view>
    <my-popup ref="qrcode_popup" position="top">
			<view class="qrcode-box">
        
				<!-- #ifdef H5 -->
				<view class="img-box">
          <view class="qrcode-box_title" :style="{backgroundImage:`url(${rule_bg})`}">
            关注微信公众号
          </view>
					<image @longtap="saveQrcode" class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="title">长按保存图片</view>
						<view class="tip">相册选取，识别关注</view>
					</view>
				</view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view class="img-box">
          <view class="qrcode-box_title" :style="{backgroundImage:`url(${rule_bg})`}">
            关注微信公众号
          </view>
					<image class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="tip">长按识别二维码关注公众号</view>
					</view>
				</view>
				<!-- #endif -->
				<view class="icon-box" @click="$refs.qrcode_popup.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
    <my-popup ref="show_login" position="top">
			<view class="qrcode-box">
        <view class="img-box">
          <view class="login_img">
            <image mode="widthFix" :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter('w_320')"></image>
          </view>
          <view class="tip">我们需要你的授权，以继续操作</view>
          <view class="login_btn flex-row items-center j-center">
            <!-- <view class="icon"> -->
              <myIcon type="weixin" size="32rpx" color="#Fff"></myIcon>
            <!-- </view> -->
            <view class="w_login" @click ="shouquanLogin">
              微信授权登录
            </view>
          </view>
        </view>
				<view class="icon-box" @click="$refs.show_login.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
    <my-popup ref="show_rule" position="top">
			<view class="qrcode-box ">
				<view class="img-box">
          <view class="qrcode-box_title" :style="{backgroundImage:`url(${rule_bg})`}">
            活动规则
          </view>
					<view class="rule_con " v-html="blindBox.rule_content">

          </view>
				</view>
				<view class="icon-box" @click="$refs.show_rule.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
    <my-popup ref="show_jiangpin" position="center">
			<view class="qrcode-box center">
        
				<view class="img-box">
          <view class="jiangpin_img">
            <image mode="widthFix" :src="`/yidongduan/blindBox/<EMAIL>` |imageFilter " alt="">
          </view>
          
         
           
					<view class="jiangpin_title">
            <!-- 没中奖 或者抽奖机会用完-->
            <template v-if='this.userBoxData.box_surplus_num<1' >
              很遗憾
            </template>
            <template v-else>

              {{prize_info.id?'恭喜您获奖了':'很遗憾'}}
            </template>
            
          </view>
          <!-- 中奖图片 -->
          <view class="jiangpin_logo" v-if ="prize_info.cover" >
            <image mode ="widthFix" :src="prize_info.cover |imageFilter"></image>
          </view>
          <view class="jiangpin_sub_title">
            <template v-if='this.userBoxData.box_surplus_num<1'>
               您的抽奖次数已用完，您可以参与任务获得更多抽奖机会
            </template>
           <template v-else>
              {{prize_info.title}}
           </template>
          </view>
          <view class="jiangpin_btn flex-row flex-1" @click='closePrize'>
             <!-- 没中奖 或者抽奖机会用完-->
            <template v-if='this.userBoxData.box_surplus_num<1 ||!prize_info.id ' >
              朕知道了
            </template>
            <template v-else>
              开心收下
            </template>
            <!--  -->
            
          </view>
				</view>
				<view class="icon-box" @click="closePrize">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>

    <my-popup ref="show_zhuli" position="center">
			<view class="qrcode-box center">
        
				<view class="img-box">
          <view class="jiangpin_img">
            <image mode="widthFix" :src="`/yidongduan/blindBox/<EMAIL>` |imageFilter " alt="">
          </view>
          
         
           
					<view class="jiangpin_title">
            {{zhuliInfo.title}}
            
          </view>
          <!-- 中奖图片 -->
          
          <view class="jiangpin_sub_title">
           {{zhuliInfo.reason}}
           
          </view>
          <view class="jiangpin_btn flex-row flex-1" @click='$refs.show_zhuli.hide()'>
             我知道了
          </view>
				</view>
				<view class="icon-box"  @click='$refs.show_zhuli.hide()'>
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>

    <my-popup ref="show_help" position="center">
			<view class="qrcode-box center">
        
				<view class="img-box">
          <view class="jiangpin_img">
            <image mode="widthFix" :src="`/yidongduan/blindBox/<EMAIL>` |imageFilter " alt="">
          </view>
          
         
           
					<view class="jiangpin_title">
            {{helpInfo.title}}
            
          </view>
          <!-- 中奖图片 -->
          
          <view class="jiangpin_sub_title">
           {{helpInfo.reason}}
           
          </view>
          <view class="jiangpin_btn flex-row flex-1" @click='confirmHelp'>
             帮TA助力
          </view>
				</view>
				<view class="icon-box"  @click='$refs.show_help.hide()'>
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
    <!-- #ifdef H5 -->
      <!-- H5海报 -->
      <view id="card">
        <view class="card_img-box">
          <image :src="blindBox.share_poster | imageFilter('w_8001')" mode="widthFix"></image>
          <view class="chat_card">
            <image mode ="widthFix" :src="qrcode1"></image>
          </view>
        </view>
        
        
      </view>
      <!-- #endif -->
     <my-popup ref="show_invite"  position="bottom">
			<view class="qrcode-box w_f">
         <view class="postor-box"  v-if="cardImg">
            <image class="card-img" :src="cardImg" mode="widthFix" @click.stop.prevent="saveCard"></image>
          </view>
				<view class="img-box">
         
          
          <view class="invite_type flex-row items-center">
            <view class="invite_type_item" @click.stop.prevent ="toInvite">
              <view class="invite_type_image">
                <image :src="`/yidongduan/blindBox/<EMAIL>`| imageFilter('m_80')" mode ="widthFix"></image>
              </view>
              <view class="invite_type_name" >
                微信转发
              </view>
            </view>
            <view class="invite_type_item " @click.stop.prevent ="saveCard">
              <view class="invite_type_image">
                <image :src="`/yidongduan/blindBox/<EMAIL>`| imageFilter('m_80')" mode ="widthFix"></image>
              </view>
              <view class="invite_type_name">
                保存海报
              </view>
            </view>
            
          </view>
          <view class="jiangpin_btn flex-row flex-1" @click='$refs.show_invite.hide()'>
             取消
          </view>
				</view>
			</view>
		</my-popup>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
     <my-popup ref="sub_form_popup" position="center" height="800rpx" :touch_hide="false">
        <view class="sub_box" id="sub_box">
            <view class="sub_header">
                <view class="sub_title">完善兑奖信息</view>
                <view class="icon">
                <image  mode="widthFix"  :src="'/images/new_icon/baoming_tg.png' | imageFilter('m_320')"></image>
                </view>
            </view>
            <view class="form_box">
                <view class="sub_content">所填信息不会公开 仅用于活动兑奖</view>
                <view class="sub_form">
                    <input v-model="name" class="sub_tel" maxlength="10" type="text" placeholder="称呼" @input="inputName" />
                    <input v-model="tel"  class="sub_tel" maxlength="11" type="number" placeholder="手机号" @input="inputPhone" />
                    <view class="btn-box">
                        <button class="default" @click="subData()">提交</button>
                        <view class="close_btn" @click="closeSub()">取消</view>
                    </view>
                </view>
            </view>
        </view> 
    </my-popup>
    
  </view>
</template>

<script>
import myPopup from "@/components/myPopup.vue"
import myIcon from "@/components/myIcon.vue"
import { isArray } from '../common/utils'
import checkLogin from '@/common/utils/check_login'
// #ifdef H5
import html2canvas from '@/common/html2canvas.min.js'

// #endif
import getLocation from './get_location'
import shareTip from "@/components/shareTip.vue"
import {
  formatImg,
  config
} from '@/common/index.js'
export default {
  components: {
    myPopup,
    myIcon,
    shareTip
  },
  data () {
    return {
    
      indicatordots:false,
      huawenBackground: "",
      show: true,
      jiangpinList: [
        
      ],
      choujiang_middle:"",
      choujiang_bottom:"",
      choujiang_num_bg:"",
      choujiang_box:"",
      rule_bg:"",
      tanmuList: [],
      userTaskData:[],
      blindBox:{},
      choujiang_top: '',
      jiangchi_tranformX:0,
      jiangchiW:0,
      blindBoxList:[],
      userBoxData:{
        box_surplus_num:0
      },
      userHelpData:[],
      qrcode: "",
      rule: ``,
      cardImg:'',
      qrcode1:"",
      zhuliInfo:{
        title:"",
        reason:''
      },
      box_params:{
        page:1,
        rows:50,
        id:""
      },
      activity_number:1,
      surplus:0,
      prize_info:{}, //奖品信息
      helpInfo:{},
      show_share_tip:false,
      width:0,
      isParse:false,
      name:'',
      tel:"",
      scrollLeft:0,
      activity_status:1 , //0 未开始 1 进行中 2 已结束
    }
    
  },
  computed: {
    // getTransform() {
    //   return `translate3d(0,${this.startOffset}px,0)`;
    // },
     visibleData() { //可视区域截取相关数据
        if (this.blindBoxList.length==0) return []
        return this.transBlindBox(this.blindBoxList)
        // return this.transBlindBox(this.blindBoxList.slice(this.start, Math.min(this.end, this.blindBoxList.length)))

    },
    animationDuration(){
      return this.visibleData.length/3
    }
    
  },
  onLoad (options) {
   this.choujiang_top = config.imgDomain + '/yidongduan/blindBox/<EMAIL>'
    this.choujiang_middle = config.imgDomain + '/yidongduan/blindBox/<EMAIL>'
    this.choujiang_bottom = config.imgDomain + '/yidongduan/blindBox/<EMAIL>'
    this.choujiang_num_bg = config.imgDomain + '/yidongduan/blindBox/<EMAIL>'
    this.choujiang_box = config.imgDomain + '/yidongduan/blindBox/<EMAIL>'
    this.rule_bg = config.imgDomain + '/yidongduan/blindBox/<EMAIL>'
    this.wechat_bg = config.imgDomain + '/yidongduan/blindBox/<EMAIL>'
    this.blind_lihe =config.imgDomain + '/yidongduan/blindBox/<EMAIL>'
    // this.getCity()
    uni.showLoading()
    if (options.share_uid){
      this.share_uid =options.share_uid
    }
    if (options.id){
      this.id = options.id
      this.getDetail()
      this.getBoxList()
    }else {
      uni.hideLoading()
      uni.showToast({
        title:"id不能为空",
        icon:'none'
      })
      return 
    }
    uni.$on("getDataAgain",()=>{
      this.getDetail()
      this.getBoxList()
    })
    uni.$on("finished",({status,task_name})=>{
      console.log(status);
      if (status){
        this.finishTask(task_name)
        
      }
    })

     // #ifdef h5
       	var ua = window.navigator.userAgent.toLowerCase();
         if (ua.match(/MicroMessenger/i) == 'micromessenger') {
           this.isWechat =1
         }else {
            this.isWechat =0
         }
        if(this.isWechat){
          // 公众号
          if(wxApi.getCode().code!=undefined){ //如果获取到了微信code
            console.log(wxApi.getCode().code)
            this.getToken(wxApi.getCode().code)
            return
          }
        }
        // #endif
  },
  onUnload () {
    uni.$off("getDataAgain")
    uni.$off("finished")
    clearInterval(this.timer)
    clearInterval(this.timer1)
  },
  mounted () {
    // this.showLogin()
    // this.showJiangpin()
    // this.toInvite()
  },
  methods: {
    // 公众号授权登录 获取token
    getToken(code){
        // #ifdef H5
				this.$ajax.get('/wechat/Index/getMemberInfo.html',{code:code,goufangjin_sid:uni.getStorageSync('goufang_sid')},res=>{
					if(res.data.code == 1){
						this.getUserInfo(res.data.user)
						// 存储token
						uni.setStorageSync('token',res.data.token)
						// uni.showToast({
						// 	title:"登录成功"
						// })
						this.$store.state.user_login_status = 2
						if(res.data.user.tel){
							this.$store.state.user_login_status = 3
						}
						
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
				})
        // #endif
			},
    getCity(options={}){
      uni.showLoading({
        title:'获取位置信息中...'
      })
     return new Promise((resolve,reject)=>{
        this.$store.state.getPosition(this.wx,(res)=>{
          console.log("jsonp");
          this.lat = res.lat
          this.lng = res.lng
            getLocation({
              latitude: res.lat,
              longitude: res.lng,
              map_key: this.blindBox.txmapwapkey||'',
              success: res=>{
                uni.hideLoading()
                this.current_city = res.city
                resolve(res.city)
                return this.current_city 
              },
              fail: err=>{
                uni.hideLoading()
                console.log(err)
                reject(err)
                // options.fail && options.fail(err)
              }
            })
          })
      })
    },
    transBlindBox(box){
      let box0=[],box1=[],box2=[],arr=[]
      
      for (let index = 0; index < box.length; index++) {
        const element = box[index];
        if (index%3==0){
          box0.push(element)
        }
        if (index%3==1){
          box1.push(element)
        }
        if (index%3==2){
          box2.push(element)
        }
      }
      arr.push(box0,box1,box2)
      return arr

      // this.getWidth()
    },
    getBoxList(){
      this.box_params.id = this.id

       this.isLoadingBox =true
       if (this.loadMore ==false) {
         this.isloading =false
         return 
       }

      this.$ajax.get('blind_box/getTodayBoxes',this.box_params,res=>{
					if(res.data.code == 1){
           if(this.box_params.page ==1){
              this.blindBoxList =[].concat(res.data.boxes )
            }else {
              for (let index = 0; index < res.data.boxes .length; index++) {
              const element = res.data.boxes [index];
                this.blindBoxList.push(element)
              }
            }
            if(res.data.boxes.length){
                this.$nextTick(()=>{
                  this.getWidth()
                  if (this.box_params.page ==1){
                   this.runJiangchi()
                  }
                })
            }
             this.isLoadingBox =false
            // this.transBlindBox(this.blindBoxList)
						//  this.blindBoxList = this.blindBoxList.concat(res.data.boxes )
						if (res.data.boxes.length<this.box_params.rows){
              this.loadMore =false
            }else {
              this.loadMore =true
            }
            
					}else{
            this.loadMore =false
            this.isLoadingBox =false
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
				})
    },
    async checkArea(){
      let current_city =this.current_city ||''
      if (!this.current_city&&this.blindBox.limit_areas ){
        current_city= await this.getCity().catch(()=>{})
      }
      return new Promise((res,rej)=>{
        if (!this.blindBox.limit_areas) {
          res({limit:0})
          return 
        }
        let params = {
          current_city
        }
        if (!current_city) {
          uni.showToast({
            title:'当前活动需要获取您的位置请开启定位',
            icon:"none"
          })
          reject({code:0})
          return 
        }else {
          params.limit =1
          res(params)
          return 
        }
      })
    },
    async toVisit(){
      if (this.$store.state.user_login_status ==1){
        if (this.isWechat){
          this.shouquanLogin()
        }else {
          this.$navigateTo("/user/login/login")
        }
        return 
      }
      if (this.userTaskData.visit){
        return 
      }
      let params = {
        id:this.id
      }
      let status = await this.checkArea().catch((err)=>{
        if(err.code ==0){
          return 
        }
      })
      if(status.limit){
        params.area = status.current_city
      }
      this.$ajax.post('blind_box/visitCollect',params,(res)=>{
        if(res.code ==1){
          uni.showToast({
            title:res.msg||"任务已完成",
            icon:'none'
          })
          this.$set(this.userBoxData,"box_surplus_num",Number(this.userBoxData.box_surplus_num)+Number(this.blindBox.visit_collect_num))
          this.$set(this.userTaskData,"visit",1)
        }else {
          uni.showToast({
            title:res.msg||"",
            icon:'none'
          })
        }
      })
    },
   async checkTask(type){
     if (this.$store.state.user_login_status ==1){
        if (this.isWechat){
          this.shouquanLogin()
        }else {
          this.$navigateTo("/user/login/login")
        }
        return 
      }
      if (this.userTaskData[type]){
        return 
      }
      let obj ={
        view_video:'/topic/preview_video?id='+this.id+"&from=task",
        view_content:'/topic/pinpai_info?id='+this.id+"&from=task",
        view_build:'/topic/buildList?id='+this.id+"&from=task",
      }
      let params = {
          id:this.id,
          task_name:type
        }
        if ( this.blindBox.limit_areas){
          console.log(1232);
          let status = await this.checkArea().catch((err)=>{
            if(err.code ==0){
              return 
            }
          })

          if(status.limit){
            params.area = status.current_city
          }
        }
        this.$ajax.get('blind_box/checkCollectTask',params,res=>{
          console.log(res);

          if(res.data.code ==1){
            this.$navigateTo(obj[type])
            // uni.showToast({
            //   title:res.msg ||'任务已完成',
            //   icon:"none"
            // })
            // this.$set(this.userTaskData,type,1)
            // this.$set(this.userBoxData,'box_surplus_num',Number(this.userBoxData.box_surplus_num)+Number(this.blindBox[type+'_collect_num']))
          }else {
            if (res.data.gzhewm){
              this.qrcode = res.data.gzhewm
              this.$refs.qrcode_popup.show()
            }
            uni.showToast({
              title:res.data.msg ,
              icon:"none"
            })
          }
        })
    },
    async finishTask(type){
      if (this.$store.state.user_login_status ==1){
        if (this.isWechat){
          this.shouquanLogin()
        }else {
          this.$navigateTo("/user/login/login")
        }
        return 
      }
      if (this.userTaskData[type]){
        return 
      }
    
      let params = {
          id:this.id,
          task_name:type
        }
        if ( this.blindBox.limit_areas){
          let status = await this.checkArea().catch((err)=>{
            if(err.code ==0){
              return 
            }
          })
          if(status.limit){
            params.area = status.current_city
          }
        }
        this.$ajax.post('blind_box/complateCollectTask',params,res=>{
          console.log(res);
          if(res.data.code ==1){
            uni.showToast({
              title:res.msg ||'任务已完成',
              icon:"none"
            })
            if(type=='view_build'){
              uni.removeStorageSync("blind_info"+this.id)
            }
            this.$set(this.userTaskData,type,1)
            this.$set(this.userBoxData,'box_surplus_num',Number(this.userBoxData.box_surplus_num)+Number(this.blindBox[type+'_collect_num']))
            
          }else {
            uni.showToast({
              title:res.data.msg ,
              icon:"none"
            })
            if (res.data.gzhewm){
              this.qrcode = res.data.gzhewm
              this.$refs.qrcode_popup.show()
            }
          }
        })


    },
    shouquanLogin(){
     // #ifdef H5
      //  公众号授权
      const redirect_uri = window.location.origin + "/h5/topic/blindBox?id="+this.id;
				this.$ajax.get('/wap/index/wxAppId',{},res=>{
					if(res.data.appid){
						wxApi.author(redirect_uri,res.data.appid)
					}else{
						uni.showToast({
							title:res.data.msg||'公众号参数配置错误',
							icon:"none"
						})
					}
				})
      // #endif
    },
   
    checkLoginStatus(){
      checkLogin({
        fail: (res) => {
          console.log('没登录')
          if (this.toLogin == false) return
          this.toLogin = false
          if (this.$store.state.user_login_status == 1) {
            // uni.setStorageSync('backUrl', window.location.href)
            // this.$navigateTo('/user/login/login')
          }
        },
        success: () => {
          
        },
        complete: (res) => {
          this.$store.state.user_login_status = res.status
        },
      })
    },
    // 获取奖品关闭弹框
    closePrize(){
      if (this.isClicking) return 
      this.isClicking =true
      if(this.isJian ) {
        this.$set(this.userBoxData,'box_surplus_num',this.userBoxData.box_surplus_num>1?(Number(this.userBoxData.box_surplus_num)-1) :0)
      }
      
      this.$refs.show_jiangpin.hide()
      clearInterval(this.timer1)
      this.runJiangchi()
      setTimeout(() => {
         this.isClicking =false
      }, 500);
    },
    transDate(time){
        let data  = new Date(time*1000)
        let y  = data.getFullYear()
        let m = ((data.getMonth()+1)+'').padStart(2,"0")
        let d  = (data.getDate()+'').padStart(2,"0")
        return y+"-"+m+"-"+d
    },
    transJiangpin(list){
      let arr =[] ;
      for (let index = 0; index < list.length; index+=6) {
        arr.push(list.slice(index,index+6));
      }

      return arr
    },
    getDetail(){
      let params = {
        id:this.id
      }
      if (this.share_uid ){
        params.share_uid = this.share_uid
      }
      this.$ajax.get("blind_box/blindBoxDetail",params,res=>{
        console.log(res)
        let data =res.data 
        if (data.code ==1){
          // this.show =true 
          this.blindBox =  data.info //盲盒数据
          this.blindBox.txmapwapkey = data.txmapwapkey  //地图key
          this.activity_status = data.activity_status
          this.jiangpinList =this.transJiangpin(data.prizes) //奖品列表
           this.jiangpinList.push(data.prizes);
          if (this.jiangpinList.length > 1) {
           
            this.indicatordots=true
          }
          this.userTaskData = data.userTaskData //用户任务数据
          this.tanmuList =data.winPrizes  //跑马灯数据
          this.openedBoxs = data.openedBoxs //已拆盲盒数据
          this.userBoxData =isArray(data.userBoxData) ?{box_surplus_num:0} :data.userBoxData
          this.userHelpData = data.userHelpData
          this.activity_number = data.activity_number
          this.user_id =  data.uid||''
          // 设置盲盒列表
          // this.blindBoxList = data.boxes 
          uni.hideLoading()
          let is_help =sessionStorage.getItem("blind_help")
          if (this.blindBox.help_collect_num && this.share_uid &&this.share_uid!=this.user_id&&!is_help){
            this.helpInfo ={
              title:'您的好友邀请您为TA助力',
              reson:""
            }
            this.$refs.show_help.show()
            sessionStorage.setItem("blind_help",1)
          }
          this.surplus = data.boxesData.surplus_count
          // this.surplus = data.boxes.filter(item=>!item.is_opened).length
          // this.blindBoxList =this.setBlindBOxList(this.blindBox.blind_box_number)
          // this.runJiangchi() 
          // this.runTanmu("tanmu")
          //设置标题
          this.share ={
            title:this.blindBox.share_title,
            content:this.blindBox.share_desc,
            pic:this.blindBox.share_pic
          }
          this.share.link =window.location.origin+'/h5/topic/blindBox?id='+this.id
          if (this.user_id) {
            this.share.link += '&share_uid=' +this.user_id
          }
          this.getWxConfig(['getLocation','updateAppMessageShareData','updateTimelineShareData'], (wx)=>{
            this.wx = wx
          })
          uni.setNavigationBarTitle({
            title: data.info.title
          })
          // if (this.blindBox.help_collect_num && this.share_uid) {
          //   this.showZhuli()
          // }
          
          // 时间戳转日期
           this.blindBox.stime =this.transDate(this.blindBox.stime)
           this.blindBox.etime =this.transDate(this.blindBox.etime)
        }else {
          uni.hideLoading()
        }
      },()=>{
        uni.hideLoading()
      })
    },
    setTanmu (el) {
      return new Promise ((res,rej)=>{
      this.$nextTick(() => {
          uni.createSelectorQuery().in(this).select(`#${el}`).boundingClientRect(data => {
              res(data)
          }).exec()
        })
        
      })
    },
    confirmHelp(){
      this.$refs.show_help.hide()
      this.showZhuli()
    },
    async showZhuli(){

      if (this.$store.state.user_login_status ==1){
        if (this.isWechat){
          this.shouquanLogin()
        }else {
          this.$navigateTo("/user/login/login")
        }
        return 
      }

      let params = {
          id:this.id,
          share_uid:this.share_uid
        }
        let status = await this.checkArea().catch((err)=>{
          if(err.code ==0){
            return 
          }
        })
        if(status.limit){
          params.area = status.current_city
        }
        this.$ajax.post('blind_box/help',params,res=>{
          if (res.data.code ==1){
              this.zhuliInfo = {
                title:"助力成功",
                reason:res.data.msg
              }
          }else {
            this.zhuliInfo = {
              title:"助力失败",
              reason:res.data.msg
            }
          }
          this.$refs.show_zhuli.show()
        })
      // this.$refs.show_zhuli.show()
    },
    showJiangpin(){
      this.$refs.show_jiangpin.show()
    },
    
    throttle(fn, delay) {
      let previous = 0;
      // 使用闭包返回一个函数并且用到闭包函数外面的变量previous
      return function() {
          let args = arguments;
          let now = new Date();
          if(now - previous > delay) {
              fn.apply(this, args);
              previous = now;
          }
      }
    },
    async getWidth(){
      let {width:w1} = await this.setTanmu("jiangchi1")
        let  {width:w2} = await this.setTanmu("jiangchi2")
        let  {width:w3} = await this.setTanmu("jiangchi3")
        this.width = Math.max(w1,w2,w3)
    },
    async runJiangchi () {
        
        // this.getHeight()
        // console.log(w1,w2,w3,width);
          if(this.isParse) {
            clearInterval(this.timer1)
            return 
          }
          this.timer1 = setInterval(() => {
            //  console.log(this.$refs.blind_box_con.$el.scrollLeft); 
            if (this.scrollLeft+uni.getSystemInfoSync().screenWidth -100 >=this.width){
              this.scrollToLower()
            }
            if(this.$refs.blind_box_con) {
              this.$refs.blind_box_con.$el.scrollLeft +=0.5
            }
          }, 17);
        
    },
    scroll(e){
        this.scrollLeft =this.$refs.blind_box_con.$el.scrollLeft
        if (this.scrollLeft+uni.getSystemInfoSync().screenWidth - 50 >=this.width){
          this.scrollToLower()
        }
    },
    handleScroll(e){
      if (this.isScrolling) return 
      this.isScrolling =true
      setTimeout(() => {
        this.scroll(e)
        this.isScrolling =false
      }, 50);
    },
    scrollToLower(){
      if (this.loadMore && !this.isLoadingBox ){
        this.box_params.page++
        this.getBoxList()
      }else {
        clearInterval(this.timer)
      }
    },
    scrollToUpper(){
    },
    touchStart(e){
      this.isParse =true 
      clearInterval(this.timer1)
    },
    touchMove(e){
      this.isParse =true 
      clearInterval(this.timer1)
    },
    touchEnd(e){
      // setTimeout(() => {
        this.isParse=false
        this.runJiangchi()
      // }, 1000);
    },
    clickJiangpin(item){
      if (this.activity_status!=1){
        return
      }
       if (this.$store.state.user_login_status == 1){
        //  没有登陆逻辑处理
        // #ifdef H5
       	
        if(this.isWechat){
          // 公众号
          this.showLogin()
        }else {
          this.$navigateTo("/user/login/login")
        }
        // #endif
        return
       }else if (this.$store.state.user_login_status == 2){
         if (this.blindBox.is_need_bind_phone) {
           this.toPath("/user/bind_phone/bind_phone")
           return 
         }
       }
       if (this.userBoxData.box_surplus_num<1) {
         this.showJiangpin()
         return 
       }
      clearInterval(this.timer1)
      // 获取接口获取抽取的奖品
      this.isJian =true
      this.getPrize(item)
    },

   async getPrize(item){
     
     let {current_city,limit}= await this.checkArea().catch(()=>{})
     let params = {
       id:this.id,
       num:item.num,

     }
     if(limit==1){
       params.area = current_city
     }
     if (this.isGetting==true) return 
     this.isGetting =true
      this.$ajax.post("blind_box/openBox",params,res=>{
        if (res.data.code ==1){
          item.is_opened=true
          this.isJian =true
          if (!Array.isArray(res.data.prize)){
            this.prize_info = res.data.prize
             this.showJiangpin()
          }else {
             this.prize_info={
               title:"很遗憾您未中奖"
             }
             this.showJiangpin()
          }
          this.isGetting =false
        }else {
          this.isGetting =false
          uni.showToast({
            title:res.data.msg,
            icon:"none"
          })
          if (res.data.gzhewm){
             this.isJian =false
            this.qrcode = res.data.gzhewm
            this.$refs.qrcode_popup.show()
          }else if (res.data.is_need_perfect_casher_info){
            this.isJian =false
            this.$refs.sub_form_popup.show()
          }
        }
      },()=>{
        this.isGetting =false
      })
      // this.showJiangpin()
    },
    // 保存二维码
    saveQrcode () {
      uni.request({
        url: this.qrcode,
        method: 'GET',
        responseType: 'arraybuffer',
        success: (res) => {
          let base64 = uni.arrayBufferToBase64(res);
          const userImageBase64 = 'data:image/jpg;base64,' + base64;
          uni.downloadFile({
            filePath: userImageBase64,
            success: result => {
              uni.showToast({
                title: '保存成功，在微信从相册中选取识别吧',
                icon: 'none',
                duration: 4000
              })
            },
            fail: err => {
              console.log(err)
              uni.showToast({
                title: '保存失败，请重试',
                icon: 'none'
              })
            }
          })
        }
      });
    },
    // 授权
    showLogin () {
      this.$refs.show_login.show()
    },
    showRules () {
      this.$refs.show_rule.show()
    },
    toMyPrize(){
      this.$navigateTo("/topic/my_prize?id="+this.id)
    },
    toPath(url){
      this.$navigateTo(url)
    },
    toSeePinpai(){
      let url ='/topic/pinpai_info?id='+this.id
      this.toPath(url)
    },
    toSeeVideo(){
      let url ='/topic/preview_video?id='+this.id
      this.toPath(url) 
    },
    toSeeBuild(){
      let url ='/topic/buildList?id='+this.id
      this.toPath(url) 
    },
    inputName(e) {
          this.name = e.detail.value
      },
      inputPhone(e) {
          this.tel = e.detail.value
      },
      subData(){
        if (!this.name){
          uni.showToast({
            title:"请输入用户名",
            icon:"none"
          })
          return 
        }
        if (!this.tel){
          uni.showToast({
            title:"请输入手机号",
            icon:"none"
          })
          return 
        }
        this.$ajax.post("blind_box/perfectCasherInfo",{
          tel:this.tel,
          name:this.name,
          id:this.id
        },res=>{
          if (res.data.code ==1){
            uni.showToast({
              title:res.data.msg,
              icon:'none'
            })
            this.$refs.sub_form_popup.hide()
          }else {
            uni.showToast({
              title:res.data.msg,
              icon:'none'
            })
          }
        })
    },
    closeSub(){
      this.$refs.sub_form_popup.hide()
    },
    toInvite(){
      if (this.$store.state.user_login_status == 1){
        //  没有登陆逻辑处理
        // #ifdef H5
       	
        if(this.isWechat){
          // 公众号
          this.showLogin()
        }else {
          this.$navigateTo("/user/login/login")
        }
        // #endif
        return
       }else if (this.$store.state.user_login_status == 2){
         if (this.blindBox.is_need_bind_phone) {
           this.toPath("/user/bind_phone/bind_phone")
           return 
         }
       }
      this.show_share_tip =true
      this.$refs.show_invite&& this.$refs.show_invite.hide()
      this.share.link =window.location.origin+'/h5/topic/blindBox?id='+this.id
      this.share.link+='&share_uid='+(this.user_id||'')
         this.copyWechat(this.share.link)
         this.getWxConfig(['getLocation','updateAppMessageShareData','updateTimelineShareData'], (wx)=>{
            this.wx = wx
          })
       
      // this.handleCreat()
      // this.$refs.show_invite.show()
        // this.show_invite =true
    },
    copyWechat(cont){
        // #ifndef H5
        uni.setClipboardData({
            data:cont,
            success:res=>{
                uni.showToast({
                    title:"复制成功",
                    icon:"none"
                })
            }
        })
        // #endif
        // #ifdef H5
        let oInput = document.createElement('input');
        oInput.value = cont;
        document.body.appendChild(oInput);
        oInput.select(); // 选择对象;
        document.execCommand("Copy"); // 执行浏览器复制命令
        uni.showToast({
            title:"复制成功",
            icon:"none"
        })
        oInput.remove()
        // #endif
    },

    saveCard(){
				uni.downloadFile({
					// filePath: this.cardImg,
           url:this.cardImg,
					success: (result) => {
						uni.showToast({
						title: "保存成功，从相册中分享给好友吧",
						icon: "none",
						duration: 4000
						})
						},
						fail: (err) => {
						console.log(err)
						uni.showToast({
							title: "海报保存失败，请重试",
							icon: "none"
						})
					}
				})
			},
      toShare(){
        uni.showLoading({
          title:"加载中。。。"
        })
        if (!this.qrcode1 ){
          this.$ajax.get("blind_box/getShareQrCode",{id:this.id},res=>{
            if(res.data.code ==1){
              this.qrcode1 = formatImg(res.data.qrcode) 
              
              uni.hideLoading()
              setTimeout(() => {
                this.handleCreat()
              }, 300);
              
            }else {
              uni.hideLoading()
              uni.showToast({
                title:res.data.msg,
                icon:"none"
              })
            }
          })
        }else {
          uni.hideLoading()
          this.handleCreat()
        }
      },
      handleCreat() {
        // #ifdef APP-PLUS
        // this.showPopup = true
        // this.$refs.share_popup.show()
          return;
        // #endif
        // this.$refs.share_popup.hide()
        if (this.cardImg) {
          this.$refs.show_invite.show()
          return;
        }
        uni.showLoading({
          title: "正在生成海报",
          mask: true
        });
        // #ifdef H5
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0
        })
          console.log( this.qrcode1);
          this.$nextTick(() => {
            let widthTemp = document.querySelector(`#card`).clientWidth;
            let heightTemp = document.querySelector(`#card`).clientHeight;
              html2canvas(document.querySelector("#card"), {
              useCORS: true,
              logging: true,
              scrollY:0,
              scrollX:0,
              // width:widthTemp,
              height:heightTemp-2,
              backgroundColor:"none"
            }).then(canvas => {
              uni.hideLoading()
              console.log(canvas.toDataURL());
              this.cardImg = canvas.toDataURL()
              this.$refs.show_invite.show()
            });
          })
        // #endif
        // #ifdef MP
        drawCard.canvasImg(formatImg(this.blindBox.share_poster, 'w_8001')).then((imgInfo) => {
          // imgInfo：下一步绘制主图时的一些参数
          this.creatCard(imgInfo)
        }, (res) => {
          console.log(err)
          uni.showToast({
            title: "海报保存失败，请重试",
            icon: "none"
          })
        })
        // #endif
      },
  }
}
</script>

<style lang="scss" scoped>
.topImg {
  width: 100%;
  image {
    width: 100%;
  }
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.items-center {
  align-items: center;
}
.j-center {
  justify-content: center;
}
.top {
  .choujiangji_t {
    margin: 0 24rpx;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .choujiang_top {
      text-align: center;
      // margin-bottom: 48rpx;
      padding: 24rpx 0 24rpx;
      font-size: 22rpx;
      color: #7f502c;
    }
    .may_jiangpin_c {
      width: 100%;
      height: 14rpx;
    }
    .may_jiangpin {
      padding: 20rpx 0 60rpx 32rpx;
      // padding-left: 48rpx;
      .left_icon {
        width: 34rpx;
        height: 34rpx;
        margin-right: 10rpx;
        overflow: hidden;
        image {
          width: 100%;
        }
      }
      .may_text {
        font-size: 32rpx;
        color: #ffffff;
      }
    }
  }
  .choujiangji_m {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: 0 24rpx;
  }
  .choujiangji_b {
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 590px;
    .choujiang_desc {
      .choujiang_desc_text{
        padding: 24rpx 0;
        text-align: center;
        font-size: 24rpx;
        color: #FFFFFF;
      }
      .choujiang_desc_num {
        text-align: center;
        .choujiang_desc_num_c {
          display: inline-block;
          padding: 24rpx 100rpx;
          font-size: 28rpx;
          color: #FFFFFF;
          background-size: 100% 100%;
          background-repeat: no-repeat;
        }
       
      }
    }
    .choujiang_content{
      margin-top: 100rpx;
      position: relative;
      .choujiang_content_info {
        padding: 0 48rpx;
        font-size: 22rpx;
        color: #FFFFFF;
      }
      
    }
    @keyframes dmAnimation2 {
      0% {
        transform: translateX(0);
      }
      100% {
        // transform: translateX(calc((-100vw + 100rpx)));
        transform: translateX(calc(((-100%) + 100vw - 100rpx)));
      }
    }
    .blind_box_con_c {
          // width: 100%;
          // display: inline-block;
          // width: auto;
          // position: absolute;
          // left: 0;
          // bottom: 0;
          // right: 0;
          // top: 0;
          // animation-name:  dmAnimation2 ;
          // animation-delay:3s;
          // overflow-x: scroll;
          &.stop_scroll{
             animation-play-state: paused;
          }
        }
    .blind_box {
       margin: 24rpx 28rpx;
      position: relative;
      .blind_box_con {
        margin: 0 24rpx;
        padding-top: 40rpx;
        box-sizing: border-box;
        // width: auto;
        min-height: 768rpx;
        position: relative;
        overflow: scroll;
      }
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .blind_box_item{
        display: inline-flex;
        padding-bottom: 60rpx;
        white-space: nowrap;
        // overflow-anchor:auto;
        .blind_item {
          text-align: center;
          // display: inline-block;
          padding: 8rpx 12rpx 16rpx;
          height: 148rpx;
          position: relative;
          .blind_item_num{
            color: #7f502c;
            font-size: 24rpx;
            padding: 8rpx 0;
          }
          .blind_item_img{
            width: 124rpx;
            height: 124rpx;
            position: relative;
            background-size: 100%;
            background-repeat: no-repeat;
            // overflow: hidden;
            image{
              width: 100%;
            }
            .logo_img {
              position: absolute;
              left: 0;
              bottom: 0;
              height: 90rpx;
              width: 90rpx;
              border-radius: 50%;
              background-size: 100%;
              background-repeat: no-repeat;
              overflow: hidden;
              background-size: 100%;
              background-repeat: no-repeat;
              image {
                width: 100%;
              }
            }
            .open_status {
              position: absolute;
              bottom: 0;
              right: 0;
              .status_name {
                font-size: 24rpx;
                padding: 2rpx 20rpx;
                border-radius: 20rpx;
                background-image: linear-gradient(90deg, #Fef6e5 0% , #F6dcac 100%);
                &.is_open {
                  background: #f0f0f0;
                }
              }
            }
          }
          .lihe_logo_img {
            position: absolute;
            left: 12rpx;
            bottom: 24rpx;
            height: 100rpx;
            width: 100rpx;
            overflow: hidden;
            image {
              width: 100%;
            }
          }
          
        }
      }
    }
  }
  .text {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 0;
    right: 0;
    top: 28rpx;
    color: #fff;
  }
  .swiper {
    padding-bottom: 20rpx;
    .swiper_c {
      margin: 10rpx 24rpx;
      width: 80vw;
      margin: 0 auto;

      .swiper_title {
        padding: 40rpx 0 20rpx;
        // margin-top: 20rpx;
        margin-left: 2vw;

        // margin-left: 5vw;
        font-size: 28rpx;
        color: #fff;
      }
    }
    .banner {
      height: 510rpx;
      .swiper_con {
        display: flex;
        // width: calc(100vw - 48rpx);
        flex-wrap: wrap;
         position: relative;
        .swiper_item_img {
          position: absolute;
          top: 0;
          right: 0;
          left: 0;
          bottom: 0;
        }
        // justify-content: space-around;
      }
      .swiper_item {
        display: flex;
        flex-direction: column;
        background: #fee1b6;
        justify-content: center;
        align-items: center;
        width: calc(72vw / 3);
        height: calc(72vw / 3);
        border-radius: 10rpx;
        margin: 32rpx 0 32rpx 2vw;
        background: #ffffff;
        color: #7F502C;
       
        
        // &:nth-child(3n+1){
        //   margin-left: 2vw;
        // }
        .jp_img {
          width: 120rpx;
          height: 120rpx;
          overflow: hidden;
          image {
            width: 100%;
          }
        }
      }
    }
  }
  .task {
    margin: 24rpx 24rpx 0;
    padding: 0 24rpx 24rpx;
    position: relative;
    background: #FE9F48;
    box-shadow: inset 0 0 40rpx 0 rgba(255,255,255,0.50);
    border-radius: 48rpx;
    .task_right_icon{
      position: absolute;
      top: 0;
      right: 0;
      width: 130rpx;
      height: 130rpx;
      image {
        width: 100%;
      }
    }
    .task_title{
      padding: 24rpx 0;
      font-size: 32rpx;
      color: #FFFFFF;
    }
  }
  .task1{
    background: #FFFFFF;
    border-radius: 48rpx;
    margin-bottom: 24rpx;
    position: relative;
    z-index: 1;
    .task_item {
      padding: 24rpx;
      .task_item_icon {
        height: 88rpx;
        width: 88rpx;
        margin-right: 24rpx;
        image {
          width: 100%;
        }

      }
      .task_item_middle{
        overflow: hidden;
        .task_item_middle_title {
          font-size: 32rpx;
          color: #7F502C;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .task_item_middle_info{
          font-size: 22rpx;
          margin-top: 12rpx;
          color: #EB3C0E;
        }
      }
      .task_item_oper{
        // .task_item_oper_c {
          font-size: 22rpx;
          color:#fff;
          // color: #7F502C;
          padding: 16rpx 44rpx;
          background-image: linear-gradient(90deg, #FA7427 0%, #F24011 100%);
          box-shadow: 0 5px 10px -4px rgba(244,73,21,0.60), inset 0 0 3px 0 rgba(255,255,255,0.42);
          border-radius: 16px;
        // }
        &.finished{
           color: #7F502C;
           background: #F0F0F0;
        }
        
      }
      .task_item_b{
        margin-top: 24rpx;
        .task_item_b_prelogo {
          .task_item_b_prelogo_img {
            width: 64rpx;
            height: 64rpx;
            overflow: hidden;
            border-radius: 50%;
            margin-right: 24rpx;
            .logo_item {
              width: 100%;
            }
          }
          
        }
      }
    }
  }
  .other {
    padding: 24rpx;
    .other_item {
      padding: 24rpx 0;
      text-align: center;
      .other_icon {
        width: 88rpx;
        height: 88rpx;
        margin: 0 auto;
        overflow: hidden;
        image {
          width: 100%;
        }
      }
      .other_name {
        margin-top: 16rpx;
        font-size: 28rpx;
        color: #7F502C;
      }
    }
  }
}
// @keyframes dmAnimation2 {
//   0% {
//     transform: translateX(0);
//   }
//   100% {
//     transform: translateX(-130%);
//   }
// }
 @keyframes dmAnimation {
      0% {
        transform: translateX(0);
      }
      100% {
        // transform: translateX(calc((-100vw + 100rpx)));
        transform: translateX(-100vw);
      }
    }
.tanmu {
  position: fixed;
  top: 110rpx;
  left: 30rpx;
  z-index: 12;
  // width: 100%;
  .tanmu_c {
    animation: 10s dmAnimation linear infinite normal;
    display: flex;
    align-items: center;
    .tanmu_item {
      margin-right: 28rpx;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 32rpx;
      padding: 12rpx 16rpx;
      display: flex;
      align-items: center;
      white-space: nowrap;
      .prelogo {
        width: 32rpx;
        height: 32rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 16rpx;
        image {
          width: 100%;
        }
      }
      .desc {
        color: #fff;
        font-size: 28rpx;
      }
    }
  }
}

.qrcode-box {
  position: relative;
  margin-top: 15vh;
  &.center {
    margin-top: 50vh;
    transform: translateY(-50%);
  }
  &.w_f {
    
    .img-box{
      border-top-right-radius: 40rpx;
      border-top-left-radius: 40rpx;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
      padding: 0;
      padding-top: 60rpx;
      padding-bottom: 20rpx;
      width: 100%;
    }
  }
  .qrcode-box_title {
    padding: 54rpx 68rpx;
    position: relative;
    margin: -12rpx -12rpx 0;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    font-size: 36rpx;
    color: #fff;
    &:before {
      content: "";
      position: absolute;
      left: 20rpx;
      top: 54rpx;
      height: 50rpx;
      width: 8rpx;
      background: #fff;
    }
  }
  .img-box {
    width: 584rpx;
    padding: 12rpx;
    margin: auto;
    position: relative;
    background-color: #fff;
    border-radius: 15rpx;
    .jiangpin_img{
      position: absolute;
      top: -120rpx;
      z-index: 10;
      left:50%;
      transform: translateX(-50%);
      width: 240rpx;
      height: 240rpx;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
    .jiangpin_sub_title {
      font-size: 11px;
      text-align: center;
      color: #7F502C;
      margin-top: 24rpx;

    }
    .jiangpin_logo {
      width: 240rpx;
      height: 240rpx;
      overflow: hidden;
      margin: 24rpx auto 0 ;
      image {
        width: 100%;
      }
    }
    .jiangpin_btn {
      margin: 85rpx 40rpx 40rpx;
      font-size: 32rpx;
      padding: 24rpx 0;
      text-align: center;
      justify-content: center;
      color: #FFFFFF;
      background-image: linear-gradient(90deg, #FA7427 0%, #F24011 100%);
      box-shadow: 0 10rpx 20rpx -8rpx rgba(244,73,21,0.60), inset 0 0 6rpx 0 rgba(255,255,255,0.42);
      border-radius: 8rpx;

    }
    .jiangpin_title {
      margin-top: 140rpx;
      text-align: center;
      font-size: 32rpx;
      color: #7F502C;
    }
    // overflow: hidden;
    .title {
      font-size: 36rpx;
      font-weight: bold;
      text-align: center;
      color: #7f502c;
    }
    .tip {
      padding: 24rpx;
      padding-bottom: 48rpx;
      text-align: center;
      font-size: 28rpx;
      color: #7f502c;
    }
  }
  .qrcode {
    width: 320rpx;
    margin: 0 auto;
    height: 320rpx;
    display: block;
  }
  .icon-box {
    position: absolute;
    bottom: -80rpx;
    width: 52rpx;
    height: 52rpx;
    left: 0;
    right: 0;
    margin: auto;
  }
  .login_img {
    width: 290rpx;
    height: 290rpx;
    // background: #7f502c;
    margin: 20rpx auto 0;
    image{
      width: 100%;
    }
  }

  .login_btn {
    margin: 20rpx 40rpx;
    padding: 20rpx 0;
    color: #fff;
    font-size: 32rpx;
    background-image: linear-gradient(90deg, #fa7427 0%, #f24011 100%);
    box-shadow: 0 5px 10px -4px rgba(244, 73, 21, 0.6),
      inset 0 0 3px 0 rgba(255, 255, 255, 0.42);
    border-radius: 4px;
    .w_login {
      margin-left: 15rpx;
    }
  }
  .rule_con {
    max-height: 630rpx;
    min-height: 630rpx;
    overflow-y: auto;
    padding: 48rpx;
    color: #7f502c;
  }
}

.right_oper {
  position: fixed;
  top: 200rpx;
  right: 0;
  width: 56rpx;
  z-index:12;
  .right_oper_share {
    // width: 56rpx;
    white-space: wrap;
    padding: 16rpx;
    font-size: 22rpx;
    color: #FFFFFF;
    background: rgba(0,0,0,0.70);
    border-radius: 8px 0 0 8px;
    margin-bottom: 16rpx;
  }
}
.invite_type{
  .invite_type_item{
    text-align: center;
    flex: 1;
    .invite_type_image {
      width: 96rpx;
      height: 96rpx;
      margin: 0 auto;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
    .invite_type_name  {
      font-size: 28rpx;
      color: #7F502C;
      margin-top: 24rpx;
    }
  }
}



/* #ifdef H5 */
// H5海报
#card {
  width: 100%;
  position: fixed;
  left: -110vw;
  .card_img-box {
    width: 100%;
    height: 100%;
    position: relative;
    .chat_card {
      position: absolute;
      right: 40rpx;
      bottom: 40rpx;
      width: 220rpx;
      height: 220rpx;

    }

    overflow: hidden;
  }

  .card_img-box image {
    width: 100%;
    height: 100%;
  }

 





  .card-footer {
    margin: 40upx;
    font-size: 34px;
    line-height: 50upx;
    color: #333;

    .text {
      padding: 20upx;
    }

    .tip {
      font-size: 26upx;
      color: #666
    }

    .qrcode {
      width: 30vw;
      height: 30vw
    }
  }
}
.postor-box {
  .card-img {
    width: 80%;
    margin: 0 10%;
    padding: 40upx 0;
  }
}
/* #endif */

canvas.hide {
  position: fixed;
  left: -100vw;
}
.sub_box{
  background-color: #fff;
  margin: 0 40rpx;
  // height: 500rpx;
  border-radius: 16rpx;
  position: relative;
//   overflow-y: hidden;
  margin-top: 32rpx;
  .sub_header{
      padding: 24rpx 48rpx;
      color: #fff;
      background-image: linear-gradient(-41deg, #F7918F 0%, #FB656A 100%);
      position: relative;
      border-top-left-radius: 16rpx;
      border-top-right-radius: 16rpx;
      .sub_title{
        margin-bottom: 16rpx;
        font-size: 40rpx;
        font-weight: bold;
    }
    .sub_tip{
        font-size: 24rpx;
    }
    .icon{
        width: 188rpx;
        height: 188rpx;
        position: absolute;
        top: -32rpx;
        right: 48rpx;
        image {
            width: 100%;
            height: 100%;
        }
    }
  }
  .form_box{
      padding: 30rpx 48rpx;
  }
  .sub_content{
    font-size: 32rpx;
    line-height: 1.5;
    color: #333;
  }
  .sub_form{
    margin-top: 25rpx;
    .sms_code_inp{
        align-items: center;
        margin-bottom: 20upx;
    }
    .sub_tel{
        margin-bottom: 20rpx;
    }
    .entrustSelect{
        height: 80upx;
        background: #f5f5f5;
        margin-bottom: 20upx;
        display: flex;
        padding: 0 20rpx;
        color: #888;
        align-items: center;
        justify-content: space-between;
    }
    input{
      padding: 20rpx;
      font-size: 28rpx;
      background-color: #f5f5f5;
    }
    .send-code{
      margin-left: 10rpx;
      color: $uni-color-primary;
      &.disable{
        color: #888;
      }
    }
    .btn-box{
      padding: 10px 0 0 0;
      button{
        font-size: 34rpx;
        font-weight: bold;
        height: 88rpx;    
        line-height: 88rpx;    
        background: #FB656A;
        box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
        border-radius: 44rpx;
      }
      .close_btn{
          padding: 24rpx;
          text-align: center;
          color: #999;
      }
    }
  }
  .verify_block{
    position: absolute;
    left: 0;
    right: 0;
    top: 150rpx;
    bottom: 40rpx;
    background-color: #fff;
    z-index: 2;
  }
}
</style>