<template>
  <view class="friend-info">
    <view class="flex-box">
        <view class="header" v-show="open" @click="toDetail()">
            <image :src="infoData.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
        </view>
        <view class="info-box flex-1">
            <view class="info" v-show="open">
                <view class="title flex-box">
                    <view class="name">{{infoData.nickname||'　'}}</view>
                    <!-- <view class="build-name" v-if="infoData.build_names">{{infoData.build_names}}</view> -->
                    <image class="level-icon" v-if="infoData.adviser" :src="'/images/new_icon/ic_huiyuan%403x.png'| imageFilter('m_320')"></image>
                    <view class="level-name" v-if="infoData.level_name">{{infoData.level_name}}</view>
                </view>
                <view class="friend-data" v-if="staInfo">
                    <!-- <text>活跃度:{{infoData.active||'　'}}</text> -->
                    <text>咨询量:{{infoData.traffic_volume||'　'}}</text>
                </view>
                <view v-else style="height:38upx"></view>
            </view>
        </view>
    </view>
    <slot name="options" :infoData="infoData"></slot>
    <slot></slot>
</view>
</template>

<script type="text/ecmascript-6">
  export default {
    props:{
        infoData:Object,
        staInfo:{
            type:Boolean,
            default:true
        },
        open:{
            type:Boolean,
            default:true
        }
    },
    data () {
      return {
          
      }
    },
    methods: {
        toDetail(){
            if(this.infoData.adviser&&this.infoData.adviser>0){
                this.$navigateTo('/pages/consultant/detail?id='+this.infoData.adviser)
                return
            }
            if(this.infoData.levelid>1&&this.infoData.id){
                this.$navigateTo('/pages/agent/detail?id='+this.infoData.id)
                return
            }
            console.log("普通用户")
        }
    }
  }
</script>

<style lang="scss">

.friend-info{
    // margin: 0 24upx;
    position: relative;
    padding: 24upx 48rpx;
    background-color: #ffffff;
    // border-radius: 16upx;
    // border: 1upx solid #f7f7f7;
    // box-shadow: 0 0 18upx #eee;
    .header{
        min-width: 80upx;
        width: 80upx;
        height: 80upx;
        overflow: hidden;
        border-radius: 50%;
        margin-right: 20upx;
        image{
            width: 100%;
            height: 100%;
        }
    }
    .info-box{
        overflow: hidden;
        .info{
            margin-bottom: 10upx;
        }
        .friend-data text{
            margin-right: 40upx;
            font-size: 24upx;
            color: #999;
        }
    }
    .title{
        overflow: hidden;
        align-items: flex-end;
        margin-bottom: 10upx;
    }
    .name{
        max-width: 30%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 32upx;
        color: #333;
        font-weight: bold;
    }
    .build-name{
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-left: 10upx;
        font-size: 24upx;
        color: #999;
        max-width: 50%;
    }
    .level-icon{
        width: 32rpx;
        height: 32rpx;
        margin-left: 20rpx;
    }
    .level-name{
        margin-top: 10upx;
        margin-left: 6upx;
        padding: 2upx 8upx;
        border-radius: 8upx;
        height: 30upx;
        line-height: 28upx;
        font-size: 22upx;
        // background-color: #f96063;
        color: $uni-color-primary;
    }
}
</style>