<template>
<view class="page">
<uni-list>
    <uni-list-item v-for="(item, index) in statementList" :key="index" :title="item.content" @click="handleEdit(item)"></uni-list-item>
</uni-list>
<my-dialog ref="dialog" :show="show_dialog" @confirmButton="subData" @close="show_dialog = false" title="编辑快捷语">
    <view class="text-box">
        <textarea v-if="show_dialog" fixed maxlength="120" placeholder="请输入快捷语" v-model="statement.content"></textarea>
    </view>
</my-dialog>
</view>
</template>

<script>
import {uniList,uniListItem} from '@dcloudio/uni-ui'
import {showModal} from '../../common/index'
import myDialog from "../../components/dialog.vue"
export default {
    data() {
        return {
            statementList:[],
            statement:{},
            show_dialog:false
        }
    },
    components: {
        uniList,
        uniListItem,
        myDialog
    },
    onLoad(options){
        this.to_id = options.to_id||""
        this.getStatement()
    },
    methods: {
        getStatement(){
            this.$ajax.get('im/expressLanguage.html',{to_id:this.to_id},res=>{
                if(res.data.code === 1){
                    this.statementList = res.data.express_language
                    uni.$emit("updateStatement",this.statementList)
                }
            })
        },
        showAction(index){
            uni.showActionSheet({
                itemList:['编辑', '删除'],
                success: (res)=> {
                    switch (res.tapIndex){
                        case 0:
                            console.log("去编辑")
                            this.handleEdit(this.statementList[index])
                            break;
                        case 1:
                            showModal({
                                content:"确定要删除此快捷回复吗?",
                                confirm:()=>{
                                    this.handleDel(this.statementList[index])
                                }
                            })
                            break;
                    }
                },
                fail: (res)=> {
                    console.log(res.errMsg);
                }
            })
        },
        handleEdit(statement){
            this.statement = Object.assign({},statement)
            this.show_dialog = true
        },
        subData(){
            if(this.statement===''){
                uni.showToast({
                    title:"请输入内容",
                    icon:'none'
                })
                return
            }
            this.$ajax.post('im/editExpressLanguage',this.statement,res=>{
                if(res.data.code===1){
                    uni.showToast({
                        title:res.data.msg
                    })
                    this.getStatement()
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            })
        },
        handleDel(id){
            console.log(id)
        }
    }
}
</script>

<style lang="scss">
.text-box{
    padding: 20upx;
    textarea{
        text-align: left;
        width: 100%;
        height: 180upx;
        border: 1upx solid #f3f3f3;
        padding: 10upx;
        box-sizing: border-box;
    }
}
</style>
