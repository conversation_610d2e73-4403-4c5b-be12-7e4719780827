<template>
	<view class="agent-container" v-if ="showData">
		<view class="card-box" id="card-box">
			<view class="bg"></view>
			<view class="adviser-card" >
				<view class="adviser-info flex-row">
					<view class="info">
						<view class="name-row flex-row"><text>{{detail.name}}</text>  </view> 
                        <view class="company-name flex-row">{{detail.company_name}}</view>
						<view class="builds">
							<my-icon type="weizhi" color="#999" size="28rpx"></my-icon>
							<text class="text">{{detail.address}}</text>
						</view>
					</view>
					<view class="icon_mid" v-if ="detail.is_company_master||detail.is_store_master"  @click="$navigateTo(`/shops/editShop?id=${detail.id}`)">
                    <my-icon type="ic_xiugai" size="40upx" color="#999"></my-icon>
                </view>
					<view class="header-img-box">
						<image class="header-img" :src="detail.image | imageFilter('w_240')" mode="widthFix"></image>
					</view>
				</view>
				<view class="adviser-data flex-row">
					<view class="autograph" >
                        <view class="total-house">{{detail.total}}</view>
						<view class="label">房源</view>
						
					</view>
                    <view class="column"></view>
					<view class="autograph" >
                        <view class="sale-house flex-row">{{detail.twice}}<text>套</text></view>
						<view class="label">出售</view>
						
					</view>
                    <view class="column"></view>
					<view class="autograph">
                        <view class="sale-house flex-row">{{detail.rent}} <text>套</text></view>
						<view class="label">出租</view>
						
					</view>
				</view>
			</view>
		</view>
        <view class="shop-agent">
            <view class="shop-agent-title flex-row">
                <view class="title">门店经纪人</view>
                <view class="more" @click="goAgentList">全部经纪人</view>
            </view>
            <swiper class="adviser-swiper" :duration="260" display-multiple-items="2" next-margin="170rpx">
                <swiper-item v-for="(item,idx) in agentList" :key="idx">
                    <view class="swiper-item" :class="{first:idx===0}" @click="toAgentDetail(item.id)">
                        <view class="avatar-box">
                            <view class="img-box">
                            
                            <image class="header_img" :src="item.prelogo | imageFilter('w_240')" mode="aspectFill"></image>
                         
                            </view>
                            <image
                            v-if="idx<3"
                            class="brand"
                            :src="`https://images.tengfangyun.com/images/new_icon/No${idx+1}.png?x-oss-process=style/m_240`"
                            ></image>
                        </view>
                        <view class="info">
                            <view class="name">{{item.cname}}</view>
                            <view class="build_name">
                            <text>{{item.tname}}</text>
                            </view>
                        </view>
                        <view class="btn_box flex-row" @click.prevent.stop="handleAsk(item.id,item.id,'agent',item.is_manager)">
                            <my-icon type="ic_zixun1" size="32rpx" color="#fff"></my-icon>
                            <text class="zx">{{detail.is_company_master&&item.ismanger==0?'设店长':'咨询'}}</text>
                        </view>
                    </view>
                </swiper-item>
				<swiper-item v-if ="agentList.length<2"></swiper-item>
            </swiper>
        </view>
		<!-- <view class="search-box">
			<my-search
				mode="small_round"
				v-model="params.keyword"
				@confirm="handleSearch"
			></my-search>
		</view> -->
		<!-- 房源列表 -->
		<view class="house-list">
			<view class="house-cate flex-row">
				<view
					class="cate-item"
					:class="{ active: params.cate_id === 1 }"
					@click="getErshou"
					>二手房</view
				>
				<view
					class="cate-item"
					:class="{ active: params.cate_id === 2 }"
					@click="getRenting"
					>租房</view
				>
				<view class="house_count flex-row">
					<text class="label">房源：</text>
					<text>{{ fangyuanNum }}</text>
				</view>
			</view>
			<block v-if="params.cate_id == 1">
				<ershou :listsData="listData" type="1" ref="ershou"></ershou>
			</block>
			<block v-if="params.cate_id == 2">
				<ershou :listsData="listData" type="2"></ershou>
			</block>
		</view>
        <view class="show-more" @click="goAllHouse">
            查看门店所有房源
        </view>
        <!-- 位置及周边 -->
        <view class="block" v-if="detail.tx_lat&&detail.tx_lng" @click="goMap(detail.tx_lat,detail.tx_lng)">
            <view class="labels flex-row">
				<text class="label">门店地址</text>
				<text class="label-text" @click.prevent.stop="showLocation">导航</text>
			</view>
			 <!-- -->
            <mapNearby :scale="mapData.scale" :cirles ="cirles"   :enableZoom="false" :enableScroll ='false'  :lat="detail.tx_lat" :lng="detail.tx_lng" :markers="mapData.covers"   @clickCate="getCovers" ></mapNearby>
        </view>
		<!-- <uni-load-more
			:status="get_status"
			:content-text="content_text"
		></uni-load-more> -->
		<!-- #ifdef H5 -->
		<view id="card">
			<view class="header-box">
				<view class="header">
					<image :src="detail.img | imgUrl" mode="aspectFill"></image>
					<view class="info">
						<text class="name">{{ detail.cname || "暂无昵称" }}</text>
						<my-icon
							v-if="detail.levelid == 3"
							type="huiyuan"
							color="#ffffff"
							size="19"
						></my-icon>
						<my-icon
							v-if="detail.levelid == 2"
							type="jingxuan"
							color="#ffffff"
							size="19"
						></my-icon>
					</view>
					<view class="position" @click="toPosition">
						<my-icon type="chengshi" color="#ffffff" size="17"></my-icon>
						<text class="address"
							>{{ detail.tname || "暂未设置名称"
							}}{{ detail.address ? " - " + detail.address : "" }}</text
						>
					</view>
				</view>
			</view>
			<view class="card_info-box" v-if="listData.length > 0">
				<view class="title">{{ listData[0].name }}</view>
				<view class="card_info flex-row">
					<view class="flex-1 text-left"
						>{{ listData[0].shi }}室{{ listData[0].ting }}厅{{
							listData[0].wei
						}}卫</view
					>
					<view class="flex-1 text-center"
						>{{ listData[0].mianji || "" }}㎡</view
					>
					<view class="flex-1 text-right price">{{
						listData[0].fangjia ? listData[0].fangjia + "万" : "面议"
					}}</view>
				</view>
			</view>
			<view class="card-footer flex-row">
				<view class="text flex-1">
					<view class="">买房，卖房，认真为您服务</view>
					<view class="">扫码与我联系</view>
					<view class="tip">长按识别小程序码查看更多详情</view>
				</view>
				<image class="qrcode" :src="qrcode" mode="aspectFill"></image>
			</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef MP -->
		<canvas canvas-id="my-canvas" style="width: 320px;height:480px"></canvas>
		<!-- #endif -->

		<my-popup ref="share" position="bottom" @handleHide="showPopup = false">
			<!-- #ifndef APP-PLUS -->
			<view class="img-box" @click="hidePopup" v-if="cardImg">
				<image
					class="card-img"
					:src="cardImg"
					mode="widthFix"
					@click.stop.prevent="saveCard(cardImg)"
				></image>
			</view>
			<!-- #endif -->
			<view class="share-box flex-box">
				<!-- #ifdef H5 -->
				<!-- <a class="flex-1 item" :href="cardImg" :download="`poster_${id}.jpg`">
					<my-icon type="tupian" size="32"></my-icon>
					<view class="text">下载海报</view>
				</a> -->
				<view class="tip">长按图片——保存到手机——分享给好友或朋友圈</view>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<!-- #ifdef MP-BAIDU -->
				<button class="flex-1 item" @click="baiduShareImg">
					<my-icon type="weixin" size="32"></my-icon>
					<view class="text">分享好友</view>
				</button>
				<!-- #endif -->
				<!-- #ifndef MP-BAIDU -->
				<button open-type="share" class="flex-1 item" @click="hidePopup">
					<my-icon type="weixin" size="32"></my-icon>
					<view class="text">分享好友</view>
				</button>
				<!-- #endif -->
				<view class="flex-1 item" @click="saveCard(cardImg)">
					<my-icon type="tupian" size="32"></my-icon>
					<view class="text">下载海报</view>
				</view>
				<!-- #endif -->
				<!-- #ifdef APP-PLUS -->
				<button
					open-type="share"
					class="flex-1 item"
					@click="appShare('WXSceneSession')"
				>
					<image
						style="height:64upx;width:64upx"
						src="https://images.tengfangyun.com/images/icon/wechat.png"
					></image>
					<view class="text">分享给好友</view>
				</button>
				<view class="flex-1 item" @click="appShare('WXSenceTimeline')">
					<image
						style="height:64upx;width:64upx"
						src="https://images.tengfangyun.com/images/icon/time_line.png"
					></image>
					<view class="text">分享到朋友圈</view>
				</view>
				<!-- #endif -->
			</view>
		</my-popup>
		<sub-form :sub_type="7" sub_title="委托" ref='sub_form' @onsubmit="handleSubForm"></sub-form>
		<chat-tip></chat-tip>
			<share-pop ref="show_share_pop" @copyLink="copyLink" @appShare="appShare" :showHaibao="false" @showCopywriting='showCopywriting'></share-pop>
			<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
			<!-- #ifndef MP-WEIXIN -->
			<login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip"></login-popup>
			<!-- #endif -->
		<!-- 底部操作菜单 -->
		<view class="bottom-bar flex-row">
			<view class="bar-left flex-row flex-1">
				<view class="icon-btn" @click="showSharePop" v-if="hasWechat">
					<my-icon type="ic_fenxiang" size="50rpx"></my-icon>
					<text>分享</text>
				</view>
			</view>
			<view class="bar-left flex-row flex-1">
				<view class="icon-btn" @click="showSubform">
					<my-icon type="weituo" size="50rpx"></my-icon>
					<text>委托</text>
				</view>
			</view>
			<view class="bar-right flex-row flex-4">
				<!-- <view class="bar-btn btn1 flex-1" @click="handleChat()">在线咨询</view> -->
				<view class="bar-btn flex-1" @click="handleTel()">电话咨询</view>
			</view>
		</view>
	</view>
</template>

<script>
import myIcon from "../components/myIcon.vue";
import mySearch from "../components/mySearch.vue";
import ershou from "../components/ershou.vue";
import myPopup from "../components/myPopup.vue";
import mapNearby from "../components/mapNearby.vue";
import subForm from "../components/subForm.vue";
import {uniLoadMore} from "@dcloudio/uni-ui";
import sharePop from "../components/sharePop";
import shareTip from "../components/shareTip";
import wxApi from '../common/mixin/wx_api'
import {
	formatImg,
	getSceneParams,
	config,
	isIos,
} from "../common/index.js";
import getChatInfo from "../common/get_chat_info";
import encryptionTel from "../common/encryption_tel";
// #ifndef MP-WEIXIN
import loginPopup from '../components/loginPopup'
// #endif
import { getLonAndLat } from '@/common/utils/getLonAndLat'
export default {
	data() {
		return {
			get_status: "loading",
			content_text: {
				contentdown: "",
				contentrefresh: "正在加载...",
				contentnomore: "没有更多数据了",
			},
			params: {
				id:'',
				cate_id:1
			},
			showData:false,
			detail: {},
			listData: [{name:''}],
			cardImg: "",
			qrcode: "",
			login_tip:'',
			mapData:{
					covers:[],
					scale:12,
			},
			agentList:[
			],
			show_share_tip:false,
			share:{},
			link:'',
			moteNum:0,
			fangyuanNum:0
		};
	},
	computed: {
		is_open_im() {
			//是否开通了聊天功能
			return this.$store.state.im.ischat;
		},
		is_open_middle_num() {
			//是否开通了中间号功能
			return this.$store.state.im.istelcall;
		},
		login_status() {
			//当前用户的登录状态
			return this.$store.state.user_login_status;
		},
		level_icon() {
			//级别的图标
			if (!isNaN(this.detail.levelid - 1) && this.detail.levelid - 1 >= 0) {
				return this.level_icons[this.detail.levelid - 1].icon;
			} else {
				return "/images/new_icon/<EMAIL>";
			}
		},
		level_color() {
			//级别的颜色
			if (
				this.is_zan &&
				!isNaN(this.detail.levelid - 1) &&
				this.detail.levelid - 1 >= 0
			) {
				return this.level_icons[this.detail.levelid - 1].color;
			} else {
				return "#dedede";
			}
		},
		hasWechat() {
			return this.$store.state.hasWechat;
		},
		imchat(){
			return  this.$store.state.im.ischat
		},
		oneKm(){
      return getLonAndLat(this.detail.tx_lng,this.detail.tx_lat,0,1000)
    },
    twoKm(){
      return getLonAndLat(this.detail.tx_lng,this.detail.tx_lat,0,2000)
    },
    threeKm(){
      return getLonAndLat(this.detail.tx_lng,this.detail.tx_lat,0,3000)
    },
    cirles(){
      if(this.detail &&this.detail.tx_lat) {
        return [
          {
							longitude:this.detail.tx_lng,
							latitude:this.detail.tx_lat,
							color:"#ff0000",
							radius:1000,
							strokeWidth:1,
						},
						{
							longitude:this.detail.tx_lng,
							latitude:this.detail.tx_lat,
							color:"#ff9c00",
							radius:2000,
							strokeWidth:1
						},
						{
							longitude:this.detail.tx_lng,
							latitude:this.detail.tx_lat,
							color:"#fee500",
							fillColor:"#00000026",
							radius:3000,
							strokeWidth:1
						}
        ]
      }
		}

	},
	components: {
		myIcon,
		mySearch,
		ershou,
        myPopup,
        mapNearby,
		subForm,
		sharePop,
		uniLoadMore,
		shareTip,
		// #ifndef MP-WEIXIN
        loginPopup,
        // #endif
	},
	// #ifndef H5
	onShow() {
		if (this.reload) {
			this.reload = false;
			this.getData(this.id);
		}
	},
	// #endif
	onLoad(options) {
		if (JSON.stringify(this.$store.state.tempData) != "{}") {
			this.detail = this.$store.state.tempData;
		}
		if (options.id) {
			this.params.id = options.id;
			// this.getDetail(options.id);
			this.getData();
		}
		// this.getLocation()
	},
	onUnload() {
		this.$store.state.tempData = {};
	},
	filters: {
		imgUrl(val) {
			return formatImg(val, "w_400");
		},
	},
	methods: {
		// getLocation(){
		// 		let url;
		// 		if(isIos()){
		// 			url = this.$store.state.firstUrl
		// 		}else{
		// 			url = window.location.href
		// 		}
		// 		this.$ajax.get('/wechat/index/signature.html',{url:url},res=>{
		// 			if(res.data.code == 1){
		// 				res.data.config.jsApiList = ['getLocation','updateAppMessageShareData','updateTimelineShareData']
		// 				wx.config(res.data.config)
		// 				this.$store.state.getPosition(wx,()=>{
		// 					this.getData()
		// 				})
		// 			}
		// 		})
		// 	},
		getData() {
			this.get_status = "loading";
			this.listData=[]
			this.showData=false
			this.$ajax.get("agentCompany/getStoreDetail.html", this.params, (res) => {
				if (res.data.code == 1) {
					this.detail =res.data.store
					this.detail.total=res.data.total
					this.detail.twice=res.data.twice
					this.detail.rent=res.data.rent
					this.agentList=res.data.member
					let ershouArr=res.data.data.filter(item=>item.text =="二手房")
					let rentArr=res.data.data.filter(item=>item.text =="租房")
						this.listData=ershouArr[0].list
						this.fangyuanNum=ershouArr[0].count
						this.ershouData=ershouArr[0].list
						this.ershouFangyuanNum=ershouArr[0].count
						this.rentData=rentArr[0].list
						this.rentFangyuanNum=rentArr[0].count
					if (res.data.share){
						this.share=res.data.share
						this.getWxConfig()
					}else {
						this.share={
							title:this.detail.name,
							content: this.detail.introduce,
							pic:this.detail.image 
						}
						this.getWxConfig()
					}
                    // this.listData = this.listData.concat(res.data.house);
                      // 设置地图中心点
                    if (this.detail.tx_lat && this.detail.tx_lng) {
                        // this.getCovers('商业')
                        this.mapData.covers = [
                            {
                            latitude: this.detail.tx_lat,
                            longitude: this.detail.tx_lng,
                            width: 30,
                            height: 30,
                            iconPath: '/static/icon/center.png'
                            }
                        ]
					}
					this.getWxConfig([
						'openLocation',
						'updateAppMessageShareData',
						'updateTimelineShareData'
					],wx=>{
						this.wx = wx
					})
					this.get_status = "more";
				} else {
					this.get_status = "noMore";
				}
				if (res.data.visitor_id) {
					this.visitor_id = res.data.visitor_id;
				}
				this.showData=true
			});
        },
         // 获取地图附近周边
        getCovers(e, type = 4) {

            let params = {
                id: this.params.id,
                keywords: e?e.type:'',
                type: type
            }
            let api ='map/mapNearbyMatches.html'
            
            this.$ajax.get(
                api,
                params,
                res => {
                if (res.data.code != 1) {
                    return
                }
                if (!res.data.done&&this.moteNum <5 &&!e){
									this.moteNum ++
									this.getCovers(e,type)
									return 
								}
								let covers=[]
								res.data.matches.map(cover => {
									let icon,color,bgColor,title
									switch(cover.keyword)
									{
									case '商业':
										icon = '/static/icon/foot.png'
										bgColor= "#ffbabc"
										title="商"
										color="#fff"
										break
									case '教育':
										icon = '/static/icon/edu.png'
										title="教"
										bgColor="#34dec1"
										color="#fff"
										break
									case '医疗':
										icon = '/static/icon/yiliao.png'
										title="医"
										bgColor="#feb9bb"
										color="#fff"
										break
									case '交通':
										icon = '/static/icon/jiaotong.png'
										bgColor="#66d1fa"
										title ="交"
										color="#fff"
										break
									default:
										icon = '/static/icon/center.png'
									}
									if (cover.data&&cover.data.length) {
											cover.data.map(item=>{
											let ob = {
													width: 30,
													height: 30,
													iconPath: icon,
													latitude: item.location.lat,
													longitude: item.location.lng,
													title: item.title,
													address: item.address,
													_distance: item._distance,
													callout: {
														content:  ((e && e.scale<=14) || !e)?title:item.title,
														padding: 5,
														fontSize:10,
														boxShadow:'none',
														bgColor,
														color,
														borderRadius: 4,
														borderColor:bgColor,
														display:'ALWAYS'
													},
													distance: parseInt(item._distance)
												}
												covers.push(ob)
												return item
											})
									}
									
									return cover
								})
                covers.push({
                    latitude: this.detail.lat,
                    longitude: this.detail.lng,
                    width: 30,
                    height: 30,
                    iconPath: '/static/icon/center.png'
                })
								covers.push({
									latitude: this.oneKm.lat,
									id:"a"+1,
									longitude: this.oneKm.lon,
									width: -1,
									height:-1,
									label: {
										content:'1公里',
										padding:2,
										borderRadius:2,
										bgColor:"inherit",
										color:"#ff0000",
										display:'ALWAYS',
										fontSize:10,
										borderWidth:0,
										x:-15,
										y:5,
										anchorX:-15,
										anchorY:5,
										borderColor:'#ffffff'
									},
									iconPath: '/static/icon/center.png'
								})
								covers.push({
									latitude: this.twoKm.lat,
									longitude: this.twoKm.lon,
									width: -1,
									height: -1,
									id:"a"+2,
									label: {
										content:'2公里',
										padding:2,
										borderRadius:2,
										bgColor:"inherit",
										color:"#ff9c00",
										display:'ALWAYS',
										fontSize:10,
										borderWidth:0,
										x:-15,
										y:5,
										anchorX:-15,
										anchorY:5
									},
									iconPath: '/static/icon/center.png'
								})
								covers.push({
									latitude: this.threeKm.lat,
									longitude: this.threeKm.lon,
									width: -1,
									height: -1,
									id:"a"+3,
									label: {
										content:'3公里',
										padding:2,
										borderRadius:2,
										bgColor:"inherit",
										color:"#fee500",
										display:'ALWAYS',
										fontSize:10,
										borderWidth:0,
										x:-15,
										y:5,
										anchorX:-15,
										anchorY:5
										},
									iconPath: '/static/icon/center.png'
								})
                this.mapData.covers = covers
                },
                err => {}
            )
        },
			
		// getDetail(id) {
		// 	this.$ajax.get("member/agentInfo.html", { id }, (res) => {
		// 		if (res.data.code == 1) {
		// 			this.detail = res.data.agent;
		// 			this.mid = res.data.mid;
		// 		}
		// 	});
		// },
		showSharePop(){
			this.getShortLink()
			this.$refs.show_share_pop.show()
		},
		getShortLink(){
			this.link=window.location.href
			this.$ajax.get('build/shortUrl.html', {page_url: this.link}, res=>{
                if(res.data.code === 1){
                this.link = res.data.short_url
                }
            })

		},
		showCopywriting(){
			const text = `【门店名称】${this.detail.name||''}
【门店地址】${this.detail.address}
【查看链接】${this.link}`
			this.copyWechatNum(text, ()=>{
				this.copy_success = true
			})
		},
		// #ifdef H5
		copyLink(){
			this.show_share_tip=true
		},
		// #endif
			// #ifndef H5
		copyWechatNum(cont) {
			uni.setClipboardData({
				data: cont,
				success: res => {
				// uni.showToast({
				//   title: "复制成功",
				//   icon: "none"
				// })
				}
			})
		},
		// #endif
		// #ifdef H5
		copyWechatNum(cont, callback) {
			let oInput = document.createElement('textarea')
			oInput.value = cont
			document.body.appendChild(oInput)
			oInput.select() // 选择对象;
			oInput.setSelectionRange(0, oInput.value.length);
			document.execCommand('Copy') // 执行浏览器复制命令
			uni.showToast({
				title: '复制成功',
				icon: 'none'
			})
			oInput.blur()
			oInput.remove()

			if(callback) callback()
		},
		// #endif

		getErshou() {
			this.params.page = 1;
			this.params.cate_id = 1;
			this.listData=this.ershouData
			this.fangyuanNum=this.ershouFangyuanNum
		},
		 //  关闭登录弹窗时
        handleCloseLogin() {
            if (this.$store.state.user_login_status === 1) {
                uni.removeStorageSync('token')
                this.$navigateTo('/user/login/login')
            }
            if (this.$store.state.user_login_status === 2) {
                this.$navigateTo('/user/bind_phone/bind_phone')
            }
        },	
			
		getRenting() {
			this.params.page = 1;
			this.params.cate_id = 2;
			this.listData=this.rentData
			this.fangyuanNum=this.rentFangyuanNum
		},
		toDetail(e) {
			if (!e.detail.id) {
				return;
			}
			this.$store.state.tempData = e.detail;
			if (this.params.cate_id == 1) {
				this.$navigateTo("/pages/ershou/detail?id=" + e.detail.id);
			} else if (this.params.cate_id == 2) {
				this.$navigateTo("/pages/renting/detail?id=" + e.detail.id);
			}
		},
		goAllHouse() {
			if (this.params.cate_id == 1) {
				this.$navigateTo("/pages/ershou/ershou?id=" + this.params.id);
			} else if (this.params.cate_id == 2) {
				this.$navigateTo("/pages/renting/renting?id=" + this.params.id);
			}
		},
		goAgentList(){
			this.$navigateTo("/shops/agent_list?id=" + this.params.id);
		},
		handleAsk(mid,id,type,is_manager){
			if (this.detail.is_company_master&&is_manager==0){
				this.setMange(mid)
				return 
			}
			if(type){
				this.chat_tempinfo = { mid, id, type }
			}
			if (this.imchat==0){
				if (this.chat_tempinfo.type =="adviser"){
				this.consuDetail(this.chat_tempinfo.id)
				return 
				}else {
					this.$navigateTo('/pages/agent/detail?id='+this.chat_tempinfo.id)
					return 
				}
			}
			// #ifdef MP-WEIXIN
			if (type =="adviser"){
				getChatInfo(this.chat_tempinfo.mid, 9)
			}else if (type =="agent"){
				getChatInfo(this.chat_tempinfo.id, 9)
			}
			// #endif
			// #ifndef MP-WEIXIN
			this.$ajax.get('member/checkUserStatus',{},res=>{
				if(res.data.code === 1){
					if (this.chat_tempinfo.type =="adviser"){
					getChatInfo(this.chat_tempinfo.mid, 9)
					}else if (this.chat_tempinfo.type =="agent"){
					getChatInfo(this.chat_tempinfo.id, 9)
					}
				}else{
				this.$store.state.user_login_status = res.data.status
				this.login_tip = '为方便您及时接收消息通知，请输入手机号码'
				this.$refs.login_popup.showPopup()
				}
			})
		// #endif
		},
		setMange(uid){
			this.$ajax.get('agentCompany/setStoreManager',{uid,store_id:this.params.id},res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title:res.data.msg
					})
					setTimeout(() => {
						this.getData()
					}, 1000);
                }else {
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            })
		},
		handleSearch(e) {
			this.params.page = 1;
			this.getData();
		},
		getQrCode() {
			return new Promise((resolve, reject) => {
				this.$ajax.get(
					"news/wxacode.html",
					{ type: 5, id: this.params.id },
					(res) => {
						if (res.data.code == 1) {
							resolve(res);
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: "none",
							});
							reject(res);
						}
					},
					(err) => {
						uni.showToast({
							title: "获取二维码失败",
							icon: "none",
						});
						reject(err);
					}
				);
			});
		},
		toHome() {
			uni.switchTab({
				url: "/pages/index/index",
			});
		},
		// #ifdef APP-PLUS
		appShare(type = "WXSceneSession") {
			var _this = this;
			uni.share({
				provider: "weixin",
				type: 0,
				title: _this.share.title || "",
				scene: type,
				imageUrl: formatImg(_this.share.pic, "w_220") || "",
				summary: _this.share.content,
				href: config.apiDomain + "/h5/shops/detail?id=" + this.params.id,
				success: function(res) {
					// console.log("success:" + JSON.stringify(res));
					// _this.onShare();
					uni.showToast({
						title: "分享成功",
						icon: "none",
					});
				},
				fail: function(err) {
					uni.showToast({
						title: "分享失败",
						icon: "none",
					});
					console.log("fail:" + JSON.stringify(err));
				},
			});
		},
		// #endif
		goMap(lat,lng){
			this.$navigateTo("/propertyData/map/map?id=" + this.detail.id + "&type=" + 4 + "&lat=" + lat + "&lng=" +lng);
		},
		downImg(img) {
			uni.getImageInfo({
				src: img,
				success: (res) => {
					this.saveCard(res.path);
				},
				fail: (err) => {
					uni.showToast({
						title: "图片下载失败,请检查下载域名白名单",
						icon: "none",
						duration: 2000,
					});
				},
			});
		},
		saveCard(img) {
			// #ifdef H5
			uni.showToast({
				title: "请长按海报保存到手机",
				icon: "none",
			});
			// #endif
			// #ifndef H5
			uni.saveImageToPhotosAlbum({
				filePath: img,
				success: (result) => {
					uni.showToast({
						title: "保存成功，从相册中分享给好友吧",
						icon: "none",
						duration: 4000,
					});
				},
				fail: (err) => {
					console.log(err);
					uni.showToast({
						title: "保存失败，请重试",
						icon: "none",
					});
				},
			});
			// #endif
		},
		hidePopup() {
			this.showPopup = false;
			this.$refs.share.hide();
		},
		toPosition() {
			if (this.detail.lat > 0 && this.detail.lng > 0) {
				uni.openLocation({
					latitude: parseFloat(this.detail.lat),
					longitude: parseFloat(this.detail.lng),
					name: this.detail.tname,
					address: this.detail.address,
					success: function() {
						console.log("success");
					},
				});
			}
		},
		handelCollect() {
			uni.showToast({
				title: "收藏成功",
				icon: "none",
			});
		},
		showSubform() {
			this.$refs.sub_form.showPopup();
		},
		handleSubForm(options){
			let {name,selectIndex,tel,desc}=options
			// this.form.cate_id = this.chil_type
			this.$ajax.post('house/entrustHouse.html',{cate_id:selectIndex,name,tel,des:desc}, res=>{
				uni.hideLoading()
				if(res.data.code === 1){
				uni.showToast({
					title: res.data.msg
				})
				this.$refs.sub_form.hide()
				}else{
				uni.showToast({
					title: res.data.msg,
					icon: 'none'
				})
				}
			})
		},
        toAgentDetail(id){
			this.$navigateTo(`/pages/agent/detail?id=${id}`)
        },
		handleChat() {
			if (this.is_open_im === 0) {
				this.showWechat();
				return;
			}
			if (!uni.getStorageSync("token")) {
				this.$navigateTo("/user/login/login");
				this.reload = true;
				return;
			}
			getChatInfo(this.params.id, 1);
		},
		makePhone(tel) {
			uni.makePhoneCall({
				phoneNumber: tel,
			});
		},
		handleTel() {
			// if (!uni.getStorageSync("token")) {
			// 	this.$navigateTo("/user/login/login");
			// 	this.reload = true;
			// 	return;
			// }
			// #ifndef MP-WEIXIN
            this.$ajax.get('member/checkUserStatus', {}, res => {
                if (res.data.code === 1) {
					uni.makePhoneCall({
						phoneNumber: this.detail.tel,
					});
					// this.$ajax.get(
					// 	'im/callUpStatistics',
					// 	{
					// 	id: e.user_id,
					// 	tel: parseInt(e.tel),
					// 	type: 3
					// 	},
					// 	res => {}
					// )
                } else {
                this.$store.state.user_login_status = res.data.status
                this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
                this.$refs.login_popup.showPopup()
                }
            })
            // #endif
		},
		// #ifdef MP-BAIDU
		baiduShareImg() {
			swan.shareFile({
				filePath: this.cardImg,
				// success:res=>{
				// 	uni.showToast({
				// 		title:"分享成功"
				// 	})
				// },
				fail: (err) => {
					uni.showToast({
						title: "分享失败",
						icon: "none",
					});
				},
			});
		},
		// #endif
		doNot() {},
		showLocation(){
			if (this.wx) {
				this.wx.openLocation({
					latitude:parseFloat(this.detail.tx_lat),
					longitude:parseFloat(this.detail.tx_lng),
					name:this.detail.name,
					address:this.detail.address,
					scale: 18,
					success: function() {
						console.log('success')
					}
				})
			} else {
				uni.openLocation({
					latitude:parseFloat(this.detail.tx_lat),
					longitude:parseFloat(this.detail.tx_lng),
					name:this.detail.name,
					address:this.detail.address
				})
			}
		},
	},

	onShareAppMessage(res) {
		// this.onShare();
		let title = this.detail.name;
		return {
			title: title || "",
			// #ifdef MP-BAIDU
			content: this.detail.address || "",
			// #endif
			imageUrl: this.detail.image ? formatImg(this.detail.image, "w_6401") : "",
		};
	},
	// #ifdef APP-PLUS
	onBackPress() {
		if (this.showPopup) {
			this.showPopup = false;
			this.$refs.share.hide();
			return true;
		}
	},
	// #endif
};
</script>

<style scoped lang="scss">
view {
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}
.flex-row {
	flex-direction: row;
}
.labels{
	font-size: 40upx;
	margin: 48upx 0;
	justify-content: space-between;
	align-items: center;
	.label-text{
		font-size: 22rpx;
		color: #999;
	}
}
.block{
	margin: 0 48upx;
}

.agent-container {
	padding-bottom: 130rpx;
	background-color: #fff;
}
.card-box {
	width: 100%;
	// height: 346rpx;
	padding: 48rpx 48rpx 0 48rpx;
	position: relative;
	z-index: 1;
	background-color: #fff;
	// margin-bottom: 160rpx;
	.bg {
		height: 346rpx;
		background-color: #fa6469;
		position: absolute;
		top: 0;
		width: 100%;
		left: 0;
		z-index: -1;
	}
}
// 头部的卡片
.adviser-card {
	// position: absolute;
	// left: 48rpx;
	// right: 48rpx;
	border-radius: 28rpx;
	margin-bottom: 24rpx;
	background-color: #fff;
	border: 1px solid #d8d8d8;
	box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.05);
	// &.jin {
	//   background: linear-gradient(135deg, #E5BA72 0%, #F4DBB3 100%);
	// }
	// &.yin {
	//   background: linear-gradient(270deg, #D1DBE9 0%, #B3C3DA 100%);
	// }
	// &.tong {
	//   background: linear-gradient(90deg, #e3af79 0%, #f9cfaa 100%);
	// }
	.level-icon {
		width: 108rpx;
		height: 108rpx;
		position: absolute;
		top: 48rpx;
		right: 72rpx;
	}
	.adviser-info {
		padding: 24rpx 48rpx;
		position: relative;
		z-index: 3;
		// &::after {
		// 	content: "";
		// 	position: absolute;
		// 	bottom: 0;
		// 	left: 48rpx;
		// 	right: 48rpx;
		// 	height: 1rpx;
		// 	background-color: rgba($color: #000000, $alpha: 0.05);
		// }
		.header-img-box {
			width: 128rpx;
			height: 128rpx;
			margin-left: 16rpx;
			border-radius:8upx;
			overflow: hidden;
			position: relative;
			background-color: #f3f3f3;
			.header-img {
				width: 100%;
				position: absolute;
			}
		}
		.info {
			flex: 1;
			color: #333;
			overflow: hidden;
			.name-row {
				align-items: flex-end;
				margin-bottom: 24rpx;
				font-weight: 600;
				font-size: 32upx;
				text{
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis
				}
			}
            .company-name{
                color: #999;
                // margin-top: 24upx;
            }
			.name {
				font-size: 32rpx;
				margin-right: 16rpx;
				font-weight: bold;
			}
			.level_name {
				height: 32rpx;
				line-height: 32rpx;
				padding: 0 15rpx;
				border-radius: 16rpx;
				font-size: 22rpx;
				color: #999;
			}
			.builds {
				flex-direction: row;
			    align-items: baseline;
				// overflow: hidden;
				// white-space: nowrap;
				// text-overflow: ellipsis;
				font-size: 28rpx;
				color:#999;
                margin-top: 24upx;
				.text {
					margin-left: 14rpx;
				}
			}
			.activity {
				display: block;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				font-size: 22rpx;
			}
		}
	}

	.adviser-data {
		padding: 24rpx 48rpx 48upx;
		justify-content: space-between;
        .column{
            height:70upx;
            width: 2upx;
            background-color: #999;
            align-self: center;
        }
	}
	.autograph {
		// line-height: 1.5;
		
		font-size: 22rpx;
		// display: -webkit-box;
		overflow: hidden;
        text-align: center;
		text-overflow: ellipsis;
		// -webkit-line-clamp: 2;
		// -webkit-box-orient: vertical;
		color: #333;
		.label {
			color: #999;
            margin-top: 16upx;
		}
        .total-house{
            color: #FA6469;
            font-size: 36upx;
        }
        .sale-house{
            color: #333;
            font-size: 36upx;
            align-items: baseline;
            text{
                font-size: 22upx;
                color: #151515;
            }
        }
	}

	.browse_user_list {
		align-items: center;
		padding: 24rpx 48rpx;
		.browse_user {
			.prelogo {
				width: 28rpx;
				height: 28rpx;
				margin-left: -14rpx;
				border-radius: 50%;
				background-color: #f5f5f5;
			}
		}
		.browse_num {
			margin-left: 16rpx;
			font-size: 22rpx;
			color: #fff;
		}
	}
}

.search-box {
	padding: 20rpx 48rpx;
	background-color: #fff;
	position: sticky;
	top: var(--window-top);
	width: 100%;
	z-index: 9;
	transition: 0.26s;
}
.shop-agent{
    background: #fff;
    margin-top: 24upx;
    .shop-agent-title{
        padding: 0 48upx;
        align-items: baseline;
        .title{
            font-size: 40upx;
            color: #333;
            font-weight: 600;
        }
        .more{
            font-size: 22upx;
            color: #999;
            margin-left: auto;
        }
    }
}
.adviser-swiper{
  margin-top: 48rpx;
  height: 294rpx;
  padding-left: 48upx;
  .swiper-item{
    align-items: center;
    height: 100%;
    padding: 16rpx 20rpx;
    border: 1px solid #eee;
    box-shadow: 0 0 16rpx 0 rgba(0,0,0,0.05);
    border-radius: 8rpx;
    margin-right: 30rpx;
    &.first{
      background-image: url(https://images.tengfangyun.com/images/new_icon/bg_touxiang%403x.png);
      background-size: 136rpx;
      background-repeat: no-repeat;
      background-position: 46rpx 14rpx;
    }
    .avatar-box{
      margin-bottom: 30rpx;
      height: 90upx;
      // overflow: hidden;
      width: 90upx;
      display: block;
      position: relative;
      .img-box{
        width: 100%;
        height: 100%;
        border-radius: 50%;
        overflow: hidden;
        background-color: #dedede;
      }
      .header_img{
        position: absolute;
        width: 100%;
        top: 0;
		height: 100%;
		border-radius: 50%;
      }
      .brand {
        width: 120rpx;
        height: 48rpx;
        position: absolute;
        left: -16rpx;
        bottom: -22rpx;
      }
    }
    .info{
      flex: 1;
      line-height: 1;
      text-align: center;
      width: 100%;
      overflow: hidden;
      .name{
        display: block;
        font-size: 32rpx;
        margin-bottom: 15rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .build_name{
        flex-direction: row;
        align-items: center;
        font-size: 22rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #999999;
        .level_icon{
          width: 30rpx;
          height: 30rpx;
          margin-right: 10rpx;
        }
        >text{
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .btn_box{
      justify-content: center;
      align-items: center;
      font-size: 22rpx;
      color: #fff;
      height: 46rpx;
      border-radius: 23rpx;
      padding: 0 18rpx;
      background: linear-gradient(90deg, #FB656A 30%, #FBAC65 100%);
      >.zx{
        margin-left: 10rpx;
      }
    }
  }
}

// 房源列表
.house-list {
	padding: 24rpx 48rpx;
	background-color: #fff;
}
.house-cate {
	align-items: flex-end;
	padding: 24rpx 0 0;
	height: 92rpx;
	position: relative;
	.cate-item {
		margin-right: 48rpx;
		padding: 10rpx 0;
		font-size: 32rpx;
		position: relative;
		transition: 0.1s;
		color: #333;
		&.active {
			font-size: 40rpx;
			font-weight: bold;
			// color: $uni-color-primary;
			// &::after {
			//   content: '';
			//   position: absolute;
			//   height: 6rpx;
			//   border-radius: 3rpx;
			//   left: 20rpx;
			//   right: 20rpx;
			//   bottom: 0;
			//   background-color: $uni-color-primary;
			// }
		}
	}
	.house_count {
		position: absolute;
		right: 0;
		height: 40rpx;
		line-height: 40rpx;
		top: 0;
		bottom: 0;
		margin: auto;
		font-size: 24rpx;
		.label {
			color: #999;
		}
	}
}
.show-more{
    margin: 0 48upx;
    text-align: center;
    height: 80upx;
    line-height: 80upx;
    color: rgba(251,101,106,1);
    background-image: linear-gradient(135deg, rgba(247,145,143,0.1) 0%, rgba(251,101,106,0.1) 100%);
}

// 底部菜单
// 底部菜单
.bottom-bar {
	background-color: #fff;
	height: 110rpx;
	padding: 15rpx 48rpx;
	z-index: 10;
	.icon-btn {
		// width: 100rpx;
		align-items: center;
		padding: 0;
		margin: 0;
		background-color: #fff;
		line-height: initial;
		display: flex;
		flex-direction: column;
		& ~ .icon-btn {
			margin-left: 48rpx;
		}
		text {
			line-height: 1.5;
			font-size: 22rpx;
			color: #333;
		}
	}
	.bar-btn {
		// width: 220rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		font-size: 30rpx;
		padding: 0;
		margin: 0;
		border-radius: 0;
		color: #fff;
        background: linear-gradient(90deg, #fb656a 0%, #fbac65 100%);
        box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.3);
        border-radius: 40rpx;
		// &.btn1 {
		// 	background: #fbac65;
		// 	box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.05);
		// 	border-top-left-radius: 40rpx;
		// 	border-bottom-left-radius: 40rpx;
		// }
		// &.btn2 {
		// 	background: linear-gradient(90deg, #fb656a 0%, #fbac65 100%);
		// 	box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.3);
		// 	border-top-right-radius: 40rpx;
		// 	border-bottom-right-radius: 40rpx;
		// }
	}
}

.share-box {
	padding: 20upx;
	width: 86vw;
	margin-left: 7vw;
	// margin-bottom: 90upx;
	background-color: #fff;
	.tip {
		padding: 10px;
		width: 100%;
		font-weight: 700;
		box-sizing: border-box;
		text-align: center;
	}
	button {
		line-height: initial;
		padding: 10upx 20px;
		background-color: #fff;
	}
	.wechat-img {
		width: 100%;
		height: 60vw;
	}
	.item {
		text-align: center;
		padding: 10upx 20px;
		line-height: inherit;
	}
}

.card-img {
	width: 80%;
	margin: 0 10%;
	padding: 40upx 0;
}
.btn-box {
	padding: $uni-spacing-row-base;
}
.btn-box .btn.btn-lg {
	width: 100%;
	padding: 10upx;
	border-radius: 10upx;
	height: 80upx;
	text-align: center;
	line-height: 60upx;
	box-sizing: border-box;
	font-size: $uni-font-size-lg;
	color: #fff;
	background-color: $uni-color-primary;
}

canvas {
	position: absolute;
	left: -100vw;
}

/* #ifdef H5 */
#card {
	padding-bottom: 30px;
	width: 100%;
	position: fixed;
	// z-index: 99;
	left: -110vw;
	.header-box {
		justify-content: center;
		align-items: center;
		// text-align: center;
		padding: 24upx;
		box-sizing: border-box;
		width: 100%;
		height: 62vw;
		padding: 60upx 24upx;
		color: #fff;
		background-color: $uni-color-primary;
		.header {
			width: 100%;
			justify-content: center;
			align-items: center;
			image {
				height: 180upx;
				width: 180upx;
				border: 6upx solid #fe7958;
				border-radius: 50%;
				margin-bottom: 20upx;
			}
			.name {
				font-size: 32upx;
				font-weight: bold;
				margin-right: 10upx;
			}
			.info {
				margin: 10upx 0;
			}
			.position {
				display: inline-block;
				max-width: 100%;
				padding: 10upx 6upx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				font-size: 30upx;
			}
		}
	}
}
// .card_img-box{
// 	width: 100%;
// 	height: 70vw
// }
// .card_img-box image{
// 	width: 25%;
// 	height: 25vw;
// 	margin-top: 15vw;
// 	margin-left: 37.5vw;
// 	border-radius: 50%;
// }
.card_info-box {
	margin: 40upx;
	padding: 20upx 30upx;
	font-size: 30upx;
	color: #555;
	background-color: #f3f3f3;
}
.text-right {
	text-align: right;
}
.card_info-box {
	.title {
		font-size: 38upx;
		line-height: 1.3;
		height: 110upx;
		overflow: hidden;
		text-overflow: ellipsis;
		margin-bottom: 40upx;
		color: #000;
	}
	.card_info {
		align-items: center;
	}
	.price {
		font-weight: bold;
		color: #f65354;
	}
}
.card-footer {
	margin: 30upx;
	font-size: 34px;
	line-height: 50upx;
	.text {
		padding: 20upx;
		color: #333;
	}
	.tip {
		font-size: 26upx;
		color: #666;
	}
	.qrcode {
		width: 30vw;
		height: 30vw;
	}
}
/* #endif */
</style>
