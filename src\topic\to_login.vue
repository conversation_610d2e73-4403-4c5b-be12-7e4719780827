
<template>
	<view class="page">
        <!-- <view class="loading" v-if ='loading'>
            正在跳转中。。。
        </view> -->
    </view>
</template>

<script>
 // #ifdef MP
 import {config} from '../common/index'
 // #endif
export default {
	components: {},
	data() {
		return {
            loading:false,
            user_info:{}
        };
	},
	computed: {},
	onLoad(options) {
        this.$store.state.allowOpen =false
        this.checkLogin();
        uni.$on('getDataAgain', ()=>{
            this.checkLogin();
        })
	},
	onUnLoad() {
        uni.$off("getDataAgain");
        this.$store.state.allowOpen =true
	},
	methods: {
        checkLogin(){
            this.loading =true
            this.$ajax.get('poster/checkPosterUser', {}, res=>{
                console.log(res);
                
                if (res.data.code ==-1){
                    this.loading =false
                    uni.setStorageSync('backUrl', window.location.href)
					this.$navigateTo('/user/login/login')
                }else if(res.data.code ==1) {
                    this.loading =false
                    this.user_info.id =res.data.id
                    this.user_info.type =res.data.type
                    // #ifdef H5
                    let url =window.location.origin
                    // #endif
                    // #ifndef H5
                    let url =config.apiDomain
                    // #endif
                    console.log(url);
                    this.$navigateTo(url+'/wapi/poster/cloud?type='+this.user_info.type+'&id='+this.user_info.id+'&suiji=1&header_from=2')
                }else {
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            },{disableAutoHandle:'true'})
        }
    },
};
</script>

<style scoped lang="scss">
.page{
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    .loading{
        font-size: 40rpx;
    }
}
</style>

