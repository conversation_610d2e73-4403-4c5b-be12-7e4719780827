<template>
  <view class="house flex-row bottom-line" @click="$emit('click', { type: type, detail: itemData })">
    <view v-if="itemData.upgrade_type == 2 && endTime" class="zhiding">
      <text class="spantext">置顶中</text>
      <text class="spantext">有效期至</text>
      <text class="spantext">{{ itemData.upgrade_time }}</text>
    </view>
    <view class="house-box">
      <view class="img-box" :class="{ 'cut_price': itemData.is_cut_price == 1 && itemData.cut_price > 0 }">
        <!-- <view class="level-box">
        <text class="level level2" v-if="itemData.info_level === 2">精选</text>
      </view> -->
        <view class="img_con">
          <image class="img" :src="itemData.img | imgUrl" lazy-load mode="aspectFill"></image>
          <image v-if="itemData.is_vr || itemData.vr" class="video-icon"
            :src="'/yidongduan/icon/vr.png' | imageFilter('m_320')"></image>
          <image v-else-if="itemData.is_video == 1" class="video-icon"
            :src="'/yidongduan/icon/video.png' | imageFilter('m_320')"></image>
          <view v-if="itemData.parentid == 1" class="img-label img-label-sale">出售</view>
          <view v-if="itemData.parentid == 2" class="img-label img-label-rent">出租</view>
          <view v-if="itemData.parentid == 3" class="img-label img-label-transfer">转让</view>
          <view v-if="itemData.is_cut_price == 1 && itemData.cut_price > 0" class="cut_price_info">
            直降{{ itemData.cut_price }}{{ itemData.cut_price_unit }}</view>
          <image class="has_help_hb" v-if="itemData.has_help_hb" :src="help_hb"></image>
        </view>

      </view>
      <view class="info">
        <view class="title" :class="titleRow == 2 ? 'row2' : 'row1'">
          <text v-if="itemData.upgrade_type == 2 && !endTime" class="ding">顶</text>
          <text v-if="itemData.info_level === 2" class="jing">精</text>
          <text :class="{
            red: itemData.ifred,
            bold: itemData.ifbold,
          }">{{ itemData.title }}</text>
        </view>
        <view class="center-info">
          <text class="mj" v-if="itemData.mianji">{{ itemData.mianji }}{{ itemData.mianji_unit }}</text>
          <text class="type">{{ itemData.community_name || itemData.areaname }}</text>
        </view>
        <view class="labels">
          <!-- <text v-if="itemData.is_cut_price==1&&itemData.cut_price >0" class="cut_price_info">直降{{itemData.cut_price}}{{itemData.cut_price_unit}}</text> -->
          <template v-if="itemData.label && itemData.label.length > 0">
            <text class="label" :style="{ color: label.color, borderColor: label.color }"
              v-for="(label, index) in itemData.label" :key="index">{{ label.name }}
            </text>
          </template>
        </view>
        <view class="bottom-info flex-box">
          <view class="bottom-left">
            <template v-if="type == 'sale'">
              <text class="mianyi" v-if="itemData.price == '面议' || itemData.price == '0' || !itemData.price">面议</text>
              <text class="price" v-else>{{ itemData.price }}</text>
              <block v-if="itemData.price !== '面议' && itemData.price != '0' && itemData.price">
                <text class="price-unit">{{ itemData.price_unit }}</text>
                <!-- <text class="average_price">{{ itemData.danjia }}{{itemData.catid == 5 ? '元/亩' : '元/m²'}}</text> -->
                <text class="average_price">{{ itemData.danjia }}{{ itemData.danjia_unit }}</text>
              </block>
            </template>
            <template v-if="type === 'rent' || type === 'transfer'">
              <text class="mianyi" v-if="itemData.price == '面议' || itemData.price == '0' || !itemData
                .price">面议</text>
              <text class="price" v-else>{{ itemData.price }}</text>
              <text class="price-unit" v-if="itemData.price !== '面议' && itemData.price != '0' && itemData.price">{{
                itemData.price_unit }}</text>
              <text class="average_price"
                v-if="itemData.catid == 2 && itemData.zujin_type == 2 && itemData.zujin_month > 0">{{
                  itemData.zujin_month }}元/月</text>
            </template>
          </view>
          <view class="bottom-right" v-if="showTime">
            <text class="u-time">{{ itemData.begintime }}</text>
          </view>
        </view>
        <view class="agent_info flex-row" v-if="showBottom && itemData.levelid > 1">
          <image class="header_img" :src="itemData.prelogo | imageFilter('w_80')"></image>
          <text class="c_name">{{ itemData.cname }}</text>
          <text class="b_name flex-1">{{ itemData.tname }}</text>
        </view>
        <view class="hongbao" v-if="itemData.hb_is_open">
          <image :src="'/yidongduan/icon/hongbao.png' | imageFilter('m_320')" mode="aspectFit"></image>
          <text class="text">红包</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { formatImg, config } from '../../common/index.js'
export default {
  components: {},
  data() {
    return {}
  },
  props: {
    itemData: Object,
    type: {
      type: String,
      default: 'ershou',
    },
    titleRow: {
      type: [Number, String],
      default: 2,
    },
    showTime: {
      type: Boolean,
      default: true,
    }, endTime: {
      type: Boolean,
      default: false,
    },
    showBottom: {
      type: Boolean,
      default: true,
    }
  },
  filters: {
    imgUrl(val) {
      return formatImg(val, 'w_240')
    },
  },
  computed: {
    help_hb() {
      return config.imgDomain + "/hongbao/fangyuan_list_hongbao.png"
    }
  },
  methods: {},
}
</script>

<style scoped lang="scss">
.house {
  display: flex;
  flex-direction: column;
  // padding: 40rpx 0;
  padding: 10px 0px 20px 0px;

  .fy-right {
    text-align: right;

    .fyend {
      font-size: 22rpx;
      color: #999;
      margin-right: 5rpx;
    }

    .fyend-time {
      font-size: 22rpx;
      color: #fb656a;
    }
  }

  /* 默认是垂直方向 */
  .house-box {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap
  }

  .zhiding {
    display: inline-block;
    width: 97%;
    padding: 2px 8px;
    align-items: center;
    border-radius: 4px;
    background-color: #FFF3E8;
    margin-bottom: 24rpx;

    .spantext {
      color: #F53F3F;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      padding-right: 10rpx;
    }
  }

  .img-box {
    width: 204rpx;
    height: 172rpx;
    margin-right: 16rpx;
    position: relative;
    border-radius: 8rpx;

    .img_con {
      position: relative;
      width: 204rpx;
      height: 172rpx;
      border-radius: 8rpx;
      overflow: hidden;
      background: #fff;
      z-index: 1;
    }

    &.cut_price {
      padding: 4rpx;

      .cut_price_info {
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 3;
        display: inline-block;
        padding: 8rpx 12rpx;
        font-size: 22rpx;
        line-height: 1;
        border-top-right-radius: 8rpx;
        background: #FF6069;
        background-size: 100% 100%;
        color: #fff;
      }

      &:after {
        content: '';
        position: absolute;
        top: -3px;
        bottom: -3px;
        left: -3px;
        right: -3px;
        background: linear-gradient(to bottom, #FF6069 0%, #FFA857 100%);
        border-radius: 10rpx;
        z-index: 0;
      }
    }

    .has_help_hb {
      height: 44rpx;
      width: 84rpx;
      position: absolute;
      right: 0;
      bottom: 0;
      z-index: 3;
    }

    .img {
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 1;

    }

    .level-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;

      .level {
        display: block;
        margin-bottom: 5rpx;
        padding: 2rpx 10rpx;
        font-size: 22rpx;
        border-bottom-left-radius: 20rpx;
        color: #fff;

        &.level1 {
          background: linear-gradient(132deg, #f7918f 0%, #fb656a 100%);
        }

        &.level2 {
          background: linear-gradient(135deg, #69d4bb 0%, #00caa7 100%);
        }
      }
    }

    .video-icon {
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      left: 50%;
      bottom: 50%;
      z-index: 2;
      transform: translate(-50%, 50%);
    }

    .img-label {
      position: absolute;
      top: 0;
      right: 0;
      width: 68rpx;
      height: 36rpx;
      font-size: 22rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      border-radius: 0 0 0 8rpx;
      z-index: 2;

      &.img-label-sale {
        background: linear-gradient(180deg, #FF4F3BFF 0%, #FF7154FF 100%);
      }

      &.img-label-rent {
        background: linear-gradient(-152.68deg, #00B374FF 0%, #22E29BFF 100%);
      }

      &.img-label-transfer {
        background: linear-gradient(180deg, #0A81F3FF 0%, #4DBCFDFF 100%);
      }
    }
  }

  .info {
    flex: 1;
    overflow: hidden;

    .title {
      font-size: 32rpx;
      line-height: 1.5;
      margin-top: -6rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;

      .jing {
        background: linear-gradient(135deg, #69d4bb, #00caa7);
      }

      .red {
        color: #fb656a;
      }

      .bold {
        font-weight: bold;
      }

      &.row1 {
        max-height: 90rpx;
        margin-bottom: 10rpx;
      }

      &.row2 {
        min-height: 45rpx;
      }
    }

    .ding,
    .jing {
      display: inline-block;
      padding: 6rpx 10rpx;
      margin-right: 10rpx;
      line-height: 1;
      font-size: 22rpx;
      border-radius: 4rpx;
      color: #fff;
    }

    .ding {
      background: linear-gradient(to right, #f7918f 0%, #fb656a 100%);
    }

    .jing {
      background: linear-gradient(135deg, #69d4bb 0%, #00caa7 100%);
    }

    .center-info {
      display: flex;
      align-items: center;
      margin-top: 5rpx;
      font-size: 22rpx;

      .jiange {
        margin: 0 4rpx;
        color: #999;

        &.jiange-margin {
          margin: 0 12rpx;
        }
      }

      &.need {
        .price_box {
          margin-left: 48rpx;
        }

        .label {
          font-size: 22rpx;
          color: #999;
        }

        .area {
          font-size: 22rpx;
          color: #333;
        }

        .in_price {
          font-size: 22rpx;
          color: $uni-color-primary;
        }
      }

      .area {
        margin-left: 16rpx;
        color: #999;
      }

      .type {
        overflow: hidden;
        white-space: nowrap;
        flex: 1;
        text-align: right;
        text-overflow: ellipsis;
        color: #333;
      }

      .cx {
        margin-right: 4rpx;
      }

      .mj {
        margin-right: 4rpx;
      }
    }

    .labels {
      margin-top: 16rpx;
      line-height: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      .cut_price_info {
        display: inline-block;
        padding: 8rpx 12rpx;
        font-size: 22rpx;
        line-height: 1;
        border-radius: 4rpx;
        background: #FF6069;
        margin-right: 4rpx;
        color: #fff;
      }

      .label {
        display: inline-block;
        line-height: 1;
        font-size: 22rpx;
        padding: 4rpx 8rpx;
        border: 1rpx solid #d8d8d8;
        color: #999;
        border-radius: 4rpx;

        ~.label {
          margin-left: 16rpx;
        }
      }
    }

    .bottom-info {
      margin-top: 16rpx;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      flex-wrap: wrap;

      text {
        font-size: 22rpx;
      }

      .mianyi {
        font-size: 32rpx;
        font-weight: bold;
        margin-right: 10rpx;
        color: #fb656a;
      }

      .price {
        font-size: 34rpx;
        line-height: 1;
        font-weight: bold;
        color: #fb656a;
      }

      .price-unit {
        font-size: 26rpx;
        margin: 0 16rpx 0 8rpx;
      }

      .average_price {
        color: #999;
      }

      .bottom-right {
        flex-shrink: 0;
      }

      .u-time {
        line-height: 1;
        position: relative;
        font-size: 22rpx;
        color: #999;
      }
    }
  }

  .agent_info {
    display: flex;
    margin-top: 16rpx;
    align-items: center;
    font-size: 22rpx;
    color: #999;

    .header_img {
      width: 36rpx;
      height: 36rpx;
      border-radius: 50%;
      background-color: #f5f5f5;
    }

    .c_name,
    .b_name {
      margin-left: 16rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.hongbao {
  display: flex;
  align-items: center;
  margin-top: 16rpx;

  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 20rpx;
  }

  .text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #FF5B5B;
  }
}
</style>