<template>
  <view class="container">
    <view class="type_box" v-for="(type, index) in type_list" :key="index">
      <view class="label">{{type.label}}</view>
      <view class="type_list flex-row">
        <view class="type_item flex-row" v-for="(item, idx) in type.items" :key="idx" @click="onSelect(item.type)">
          <view class="type_text">
            <text class="title">{{item.title}}</text>
            <text class="tip">{{item.tip}}</text>
          </view>
          <image class="type_icon" :src="item.icon | imageFilter('w_120')"></image>
        </view>
        <view class="type_item placeholder"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data () {
    return {
      type_list:[
        {
          label:'出售居民住宅',
          items:[
            {
              type:1,
              title:"二手房",
              tip:'普通居民住宅',
              icon:`/images/new_icon/ic_ershoufang.png`
            },
            {
              type:2,
              title:"车位/车库",
              tip:'露天车位/车库等',
              icon:`/images/new_icon/ic_chewei.png`
            }
          ]
        },
        {
          label:'出售商业用地',
          items:[
            {
              type:3,
              title:"商铺",
              tip:'卖场底商等',
              icon:`/images/new_icon/ic_shangpu.png`
            },
            {
              type:4,
              title:"写字楼",
              tip:'写字楼、商务楼等',
              icon:`/images/new_icon/ic_xiezilou.png`
            },
            {
              type:5,
              title:"厂房",
              tip:'厂房、仓库等',
              icon:`/images/new_icon/ic_changfang.png`
            }
          ]
        }
      ]
    }
  },
  methods: {
    onSelect(type){
      console.log(type)
    }
  }
}
</script>

<style scoped lang="scss">
page{
  background-color: #fff;
}
.container{
  padding: 48rpx 32rpx;
  color: #333;
}

view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}
.type_box{
  margin: 20rpx 0;
  .label{
    padding: 16rpx;
    font-size: 32rpx;
    color: #666;
  }
  .type_list{
    justify-content: space-between;
    flex-wrap: wrap;
    .type_item{
      flex: 1;
      min-width: 40%;
      align-items: center;
      padding: 20rpx;
      border: 1rpx solid #dedede;
      border-radius: 6rpx;
      margin: 0 16rpx 50rpx 16rpx;
      &.placeholder{
        border: 0;
        height: 0;
        padding: 0;
        padding: 0 20rpx;
        margin: 0 16rpx;
      }
      .type_text{
        flex: 1;
      }
      .title{
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 15rpx;
      }
      .tip{
        font-size: 22rpx;
        color: #999;
      }
      .type_icon{
        width: 70rpx;
        height: 70rpx;
      }
    }
  }
}
</style>