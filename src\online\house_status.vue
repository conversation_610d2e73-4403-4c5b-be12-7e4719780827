<template>
<view class="fangyuan">
<view class="row-title" id="title">
    <text>{{title}}</text>
</view>
<view :class="{fixed_top:fixed_top}">
    <tab-bar ref="tabbar" :tabs="unit_list" :theme="2" :fixedTop="false" :equispaced="false" :maxNum="3" small @click="onClickTab" :nowIndex="nowIndex"></tab-bar>
    <view class="status_tip flex-box">
        <!-- <view class="label">状态说明:</view> -->
        <view class="status_list flex-1 flex-box">
            <view class="status_item flex-box" v-for="(status, index) in status_list" :key="index">
                <view class="color" :style="{backgroundColor:status.color}"></view>
                <view class="text">{{status.name}}</view>
            </view>
        </view>
    </view>
</view>
<view class="table-box" :class="{pdt_180:fixed_top}">
    <view class="table-data">
        <view class="floor flex-box" v-for="floor in floor_list" :key="floor.id">
            <view class="td floor_name">{{floor.floor}}楼</view>
            <view class="td house_name" v-for="(house, index) in floor.house" :key="index" :style="{backgroundColor:house.color}" @click="toHouse(house.id)">{{house.name}}</view>
        </view>
    </view>
</view>
<view class="remark">
    <text style="color:#f00">备注信息：</text>
    <text>{{remark}}</text>
</view>
<view class="mianze">
    <text>免责声明：</text>
    <text>{{mianze}}</text>
</view>
</view>
</template>

<script>
import tabBar from "../components/tabBar.vue"
import {
  navigateTo,
} from '../common/index.js'
import {wxShare} from '../common/mixin'
export default {
    data() {
        return {
            title:"",
            remark:"",
            mianze:"",
            floor_list:[],
            unit_list:[],
            status_list:[],
            fixed_top:false,
            nowIndex:0
        }
    },
    mixins: [wxShare],
    components: {
        tabBar
    },
    onLoad(options){
        if(options.id){
            this.number_id = options.id
            this.getData()
        }
        uni.$on("getDataAgain",this.getData)
    },
    onUnload(){
        uni.$off("getDataAgain")
        this.$store.state.allowOpen = true
    },
    onReady(){
        const query = uni.createSelectorQuery()
        query.select('#title').boundingClientRect(data => {
            this.title_height = data.height+uni.upx2px(20)
        }).exec();
    },
    methods:{
        getData(){
            this.$ajax.get('building/unitList.php',{number_id:this.number_id},res=>{
                if(res.data.mianze){
                    this.mianze = res.data.mianze
                }
                if(res.data.remark){
                    this.remark = res.data.remark
                }
                if(res.data.beian){
                    this.status_list = res.data.beian
                }
                this.title = res.data.title||''
                if(res.data.code === 1){
                    if(res.data.unit.length>0){
                        this.unit_list = res.data.unit.map(item=>{
                            return {id:item.id,name:item.unit}
                        })
                        this.getHouse(this.unit_list[0].id)
                    }
                }else{
                    uni.showToast({
                        title: res.data.msg||'获取数据失败',
                        icon: 'none'
                    })
                }
                if(res.data.share&&res.data.share.title){
                    this.share = res.data.share
                    this.getWxConfig()
                }
            })
        },
        onClickTab(e){
            this.nowIndex = e.index
            this.getHouse(e.id)
        },
        getHouse(id){
            uni.showLoading({
                title:"加载中..."
            })
            this.$ajax.get('building/fangyuan.php',{number_id:this.number_id,unit_id:id},res=>{
                if(res.data.code === 1){
                    this.floor_list = res.data.list
                }else{
                    uni.showToast({
                        tile: res.data.msg||'获取失败',
                        icon: 'none'
                    })
                }
                uni.hideLoading()
            },err=>{
                console.log(err)
                uni.hideLoading()
            })
        },
        toHouse(id){
            if(!id){
                uni.showToast({
                    title:"暂无该房源信息",
                    icon:'none'
                })
                return
            }
            navigateTo(`/online/house_info?number_id=${this.number_id}&house_id=${id}`)
        }
    },
    onPageScroll(e){
        if(e.scrollTop>=this.title_height){
            this.fixed_top = true
        }else{
            this.fixed_top = false
        }
    }
}
</script>

<style scoped lang="scss">
.row-title{
    padding: 25rpx 20rpx;
    line-height: 1.2;
    font-size: 34rpx;
    font-weight: bold;
    background-color: #fff;
    margin-bottom: 20rpx;
    text{
        padding-left: 25rpx;
        border-left: 3px solid #f65354;
    }
}
.fixed_top{
    position: fixed;
    // #ifdef H5
    top:44px;
    // #endif
    // #ifndef H5
    top:0;
    // #endif
    width: 100%;
}
.status_tip{
    padding: 20rpx;
    align-items: center;
    margin-bottom: 15rpx;
    background-color: #fff;
    .label{
        margin-right: 10rpx;
    }
    .status_list{
        align-items: center;
        flex-wrap: wrap;
        .status_item{
            margin-right: 15rpx;
            align-items: center;
            .color{
                width: 30rpx;
                height: 30rpx;
                margin-right: 5rpx;
            }
            .text{
                color: #666;
            }
        }
    }
}
.table-box{
    margin-top: 10px;
    width: 100%;
    padding: 0 20rpx;
    box-sizing: border-box;
    overflow-y: hidden;
    &.pdt_180{
        padding-top: 188rpx;
    }
}
.table-data{
    display: inline-block;
    color: #fff;
    .th,.td{
        padding: 10px 0;
        box-sizing: border-box;
        text-align: center;
        border-right: 1rpx solid #f3f3f3;
        border-bottom: 1rpx solid #f3f3f3;
    }
    .unit_name{
        background-color: #6699ff;
    }
    .unit_label,.floor_name{
        width: 120rpx;
        background-color: #33cccc;
    }
    .house_name{
        width: 100rpx;
        background-color: #999;
    }
}

.remark{
    margin-top: 20rpx;
    padding: 20rpx;
    background-color: #ffffc2;
}

.mianze{
    margin-top: 20rpx;
    padding: 20rpx;
    background-color: #eeeeee;
}
</style>
