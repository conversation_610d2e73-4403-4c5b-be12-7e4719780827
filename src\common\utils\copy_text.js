// 复制内容
const copyText = function (cont, callback) {
  // #ifdef H5
  let oInput = document.createElement('textarea')
  oInput.value = cont
  document.body.appendChild(oInput)
  oInput.select() // 选择对象;
  oInput.setSelectionRange(0, oInput.value.length);
  document.execCommand('Copy') // 执行浏览器复制命令
  oInput.blur()
  oInput.remove()
  if (callback) callback()
  // #endif
  // #ifndef H5
  uni.setClipboardData({
    data: cont,
    success: () => {
      if (callback) callback()
    }
  })
  // #endif
}

module.exports = copyText