<template>
	<view class="content p-top-90">
		<!-- <view class="top-box">
			<search @input="handleInput" @confirm="handleSearch" placeholder="搜索"></search>
		</view> -->
		<view class="screen-tab flex-box">
			<view class="screen-tab-item flex-1 text-center" @click="switchTab(1)">
				<text>{{typeName}}</text>
				<my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
			</view>
			<view class="screen-tab-item flex-1 text-center" @click="switchTab(2)">
				<text>{{areaName}}</text>
				<my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
			</view>
		</view>
		<scroll-view scroll-y  class="screen-panel" :class="nowTab==1?'show':''" @touchmove.stop.prevent="stopMove">
			<block v-for="(item,index) in types" :key="index">
				<uni-list-item :title="item.catname" show-arrow="false" @click="selectType(item.catid, item.catname)"></uni-list-item>
			</block>
		</scroll-view>
		<scroll-view scroll-y  class="screen-panel" :class="nowTab==2?'show':''" @touchmove.stop.prevent="stopMove">
			<my-area v-if="area_list.length>0" :area_list="area_list" @onselect="onSelect"></my-area>
		</scroll-view>
		<view class="house-item" v-for="(item, index) in listsData" :key="index">
			<demand-item :item-data="item" type="rest_house" @click="toDetail" @ontel="handleTel"></demand-item>
		</view>
		<view class="mask" :class="nowTab>0?'show':''" @click="nowTab=0" @touchmove.stop.prevent="stopMove"></view>
		<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
		<chat-tip></chat-tip>
		<tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
	</view>
</template>

<script>
	import encryptionTel from '../../common/encryption_tel.js'
	import search from "../../components/search.vue"
	import demandItem from "../../components/demandItem.vue"
	import {uniLoadMore, uniListItem} from '@dcloudio/uni-ui'
	import myIcon from '../../components/myIcon'
	import myArea from '../../components/myArea'
	import {wxShare} from '../../common/mixin'
	import {showModal, config} from '../../common/index.js'
	export default{
		components:{
			search,
			demandItem,
			uniLoadMore,
			uniListItem,
			myIcon,
			myArea
		},
		data(){
			return{
				get_status:"loading",
				content_text:{
					contentdown:"",
					contentrefresh:"正在加载...",
					contentnomore:"没有更多数据了"
				},
				typeName:"房产类型",
				areaName:"区域选择",
				nowTab:0,
				area_list:[],
				types:[],
				params:{
					cate_id:3,
					page:1,
					rows:20
				},
				listsData:[],
				tel_res: {},
				show_tel_pop:false
			}
		},
		mixins:[wxShare],
		onLoad(options){
			if(options.key){
				this.params.keyword = options.key
			}
			this.getData()
			this.getScreen()
		},
		methods:{
			getData(){
				this.params.lat = this.$store.state.position.lat
				this.params.lng = this.$store.state.position.lng
				if(this.params.page == 1){
					this.listsData=[]
				}
				this.get_status = "loading"
				this.$ajax.get('house/infoList.html',this.params,(res)=>{
					if(res.data.code == 1){
						this.listsData = this.listsData.concat(res.data.house)
						if(res.data.house.length<this.params.rows){
							this.get_status = "noMore"
						}else{
							this.get_status = "more"
						}
						if(res.data.share){
							this.share = res.data.share
						}
					}else{
						this.get_status = "noMore"
					}
					if(res.data.share){
						this.share = res.data.share
						this.getWxConfig()
					}else{
						this.share = {}
					}
					uni.stopPullDownRefresh();
				},(err)=>{
					console.log(err)
					uni.stopPullDownRefresh();
				})
			},
			getScreen(){
				this.$ajax.get('house/houseCondition.html',{catid:this.params.cate_id},(res)=>{
					this.area_list =res.data.area
					this.types = res.data.cates
					this.showTab=true
				},(err)=>{
					console.log(err)
				})
			},
			handleTel(tel_params){
				this.tel_params = tel_params
				// this.tel_params.success = (res)=>{
				// 	this.tel_res = res.data
				// 	this.show_tel_pop = true
				// }
				// this.tel_params.fail = (res)=>{
				// 	switch(res.data.code){
				// 		case -1:
				// 			this.reload = true
				// 			this.$navigateTo('/user/login/login')
				// 			break
				// 		case 2:
				// 			this.reload = true
				// 			// #ifdef H5 || APP-PLUS || MP-BAIDU
				// 			this.$navigateTo('/user/login/login')
				// 			// #endif
				// 			// #ifdef MP-WEIXIN
				// 			this.$navigateTo('/user/bind_phone/bind_phone')
				// 			// #endif
				// 			break
				// 		case -5:
				// 			showModal({
				// 				title: "安全验证，防恶意骚扰已开启",
				// 				content: "验证后可免费发布查看信息。",
				// 				confirm: () => {
				// 					if (res.data.is_agent){
				// 						this.$navigateTo('/user/member_upgrade')
				// 					}else{
				// 						this.$navigateTo('/user/member_upgrade?is_personal=1')
				// 					}
				// 				}
				// 			})
				// 			break
				// 		case -10:
				// 			console.log("账号被封禁")
				// 			uni.showToast({
				// 				title: res.data.msg,
				// 				icon: 'none'
				// 			})
				// 			break
				// 		default:
				// 			uni.showToast({
				// 				title: res.data.msg,
				// 				icon: 'none'
				// 			})
				// 	}
				// }
				// encryptionTel(this.tel_params)
				this.checkTelUseMoney(tel_params.info_id)
			},
			checkTelUseMoney(info_id){
				uni.showLoading({
					title: '',
					mask: true
				})
				this.$ajax.get('information/checkcornbylooktel.html', {info_id}, res=>{
					uni.hideLoading()
					var deduction_cron = parseInt(res.data.deduction_cron)
					if(res.data.code === 1){
						if(deduction_cron>0){
							showModal({
								title: "温馨提示",
								content: res.data.msg,
								confirm: () => {
									this.getTel(info_id)
								}
							})
						}
						if(deduction_cron===0){
							showModal({
								title: '友情提示',
								content: "接通后，提及在" + (this.$store.state.siteName || config.projectName) + "看到的信息，获得更好的服务！",
								confirm: () => {
									this.getTel(info_id)
								}
							})
						}
					}else{
						if(deduction_cron===-1){
							showModal({
									title: '温馨提示',
									content: res.data.msg,
									confirmText: '去充值',
									confirm: () => {
										this.$navigateTo('/user/recharge')
									}
								})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon:'none'
							})
						}
					}
				})
			},
			getTel(info_id){
				this.$ajax.get('information/getInfoTel.html', {info_id}, res=>{
					switch(res.data.code){
						case 1:
							uni.makePhoneCall({
								phoneNumber: res.data.tel
							})
							this.statisticsTel()
							break;
						case -5:
							showModal({
								title: "安全验证，防恶意骚扰已开启",
								content: "验证后可免费发布查看信息。",
								confirm: () => {
									if (res.data.is_agent){
										this.$navigateTo('/user/member_upgrade')
									}else{
										this.$navigateTo('/user/member_upgrade?is_personal=1')
									}
								}
							})
							break
						case -10:
							console.log("账号被封禁")
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							break
						default:
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
					}
				})
			},
			statisticsTel(){
				this.$ajax.get("im/callUpStatistics", {
					id: this.tel_params.mid,
					type: this.tel_params.from,
					info_id: this.tel_params.info_id
				}, res => {
					console.log(res.data);
				}, err => {
					console.log(err)
				}, { disableAutoHandle: true })
			},
			retrieveTel(){
				encryptionTel(this.tel_params)
			},
			handleInput(e){
				this.keyword = e.detail.value
			},
			handleSearch(e){
				this.params.keyword = e.detail.value
				this.params.page = 1
				this.getData()
			},
			switchTab(index){
				if(this.nowTab == index){
					this.nowTab = 0
				}else{
					this.nowTab = index
				}
			},
			selectType(type_id,type_name){
				this.params.type_id = type_id
				this.typeName = type_name
				this.nowTab = 0
				this.params.page = 1
				this.getData()
			},
			onSelect(e){
				this.params.areaid = e.id
				this.areaName = e.name
				this.nowTab = 0
				this.params.page = 1
				this.getData()
			},
			toDetail(e){
				this.$store.state.tempData = e.detail
				this.$navigateTo('/needPage/rest_house/detail?id='+e.detail.id)
			},
			stopMove(){}
		},
		onPullDownRefresh(){
			this.params.page = 1
			this.getData()
		},
		onReachBottom(){
			this.params.page = this.params.page+1
			this.getData()
		},
		// #ifdef H5 || APP-PLUS
		onNavigationBarButtonTap(option){
			this.$navigateTo('/pages/add/detail?catid=3')
		},
		// #endif
	}
</script>
<style lang="scss">
	.p-top-90{
		padding-top: 80upx;
	}
	/* #ifdef H5 */
	.top-box{
		position: fixed;
		top:44px;
		width: 100%;
		box-shadow:0 0 18rpx #dedede;
		background-color: #fff;
		z-index: 100;
	}
	/* #endif */
	/* #ifndef H5 */
	.top-box{
		position: fixed;
		top:var(--window-top);
		width: 100%;
		box-shadow:0 0 18rpx #dedede;
		background-color: #fff;
		z-index: 100;
	}
	/* #endif */
		

.screen-tab{
	top: var(--window-top);
	box-sizing: border-box;
	padding: 0 48rpx;
}
.screen-panel {
	top: var(--window-top);
	margin-top: 75rpx;
}

	.house-item{
		padding: 0 48rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
	}

	.list-info .total{
		float: initial;
	}
</style>