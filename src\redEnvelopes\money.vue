<template>
  <view>
    <view class="top">
      <image :src="topImg" mode="widthFix"></image>
    </view>
    <view class="title">
      <view class="title-text">拼手气红包</view>
      <view class="pin">拼</view>
    </view>
    <view class="money">
      <view class="num">{{money}}</view>
      <view class="unit">元</view>
    </view>
    <view class="tips">已存入微信零钱</view>
    <view v-if="!not_back" class="btn" @click="returnPage()">确定</view>
  </view>
</template>

<script>
import { config } from '../common/index'
export default {
  data() {
    return {
      topImg: config.imgDomain + '/hongbao/ling_top.png',
      money: '',
      not_back: false
    }
  },
  onLoad(options) {
    if (options.money) {
      this.money = options.money
    }
    if (options.not_back) {
      this.not_back = true
    }
  },
  onShow() {
    if (this.not_back) {
      var a = document.getElementsByClassName('uni-page-head-hd')[0]
      if (a) {
        a.style.display = 'none';
      }
    }
  },
  methods: {
    returnPage() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.top image {
  width: 100%;
}
.title {
  margin-top: 52rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  .title-text {
    font-size: 38rpx;
    color: #000000;
    font-weight: bold;
  }
  .pin {
    font-size: 20rpx;
    color: #BDA581;
    border: 1rpx solid #BDA581;
    margin-left: 6rpx;
    padding: 0 1rpx;
  }
}
.money {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  margin-top: 48rpx;
  .num {
    font-size: 136rpx;
    color: #E5CD9F;
  }
  .unit {
    font-size: 28rpx;
    color: #BDA581;
    margin-bottom: 28rpx;
    margin-left: 6rpx;
  }
}
.tips {
  font-size: 28rpx;
  color: #BDA581;
  text-align: center;
}
.btn {
  width: 420rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  margin: 184rpx auto;
  border-radius: 44rpx;
  background: linear-gradient(180deg, #FF4F3B 0%, #FF7154 100%);
  font-size: 30rpx;
  color: #fff;
}
</style>