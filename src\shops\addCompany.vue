<template>
	<view class="addshop">
        <view class="imgs">
            <!-- <image src="https://images.tengfangyun.com/images/new_icon/<EMAIL>" mode="widthFixed"></image> -->
        </view>
        <view class="info">
            <view class="title">基本信息</view>
            <view class="tips">请填写公司相关信息</view>
            <input class="sub_tel"   type="text" placeholder="公司名称" @input="inputName" />
            <input class="sub_tel"  type="text" placeholder="公司介绍" @input="inputDesc" />
            <input class="sub_tel"  type="text" placeholder="公司地址" @input="inputAddress" />
            <!-- <view class="sub_lngLat" @click="GetLatLng">{{position}}</view> -->
            <input class="sub_tel"  maxlength="12" type="number" placeholder="联系电话" @input="inputTel" />
            <input class="sub_tel"  maxlength="10" type="text" placeholder="店长姓名" @input="inputShoperName" />

            
        </view>
        <view class="logo">
            <view class="title">logo</view>
            <view class="tips">请上传公司logo</view>
            <view class="upload-box">
                <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadDon">
                    <image class="img" mode="aspectFill" v-if="params.logo" :src="params.logo | imageFilter('w_240')"></image>
                    <view v-else class="upload-btn flex-box">
                        <my-icon type="ic_jia" size="96rpx" color="#d8d8d8"></my-icon>
                    </view>
                </my-upload>
            </view>
        </view>
        <view class="submit" @click='submit'>立即提交</view>
    </view>
</template>

<script>
    import myIcon from "../components/myIcon"
    import myUpload from "../components/form/myUpload"
	// import {checkUserStatus, showModal} from "../../common/index.js"
	export default {
		components:{
            myIcon,
            myUpload
		},
		data() {
			return {
                params:{
                    // tx_lat:'',//腾讯纬度
                    // tc_lng:'',//腾讯经度
                    name:'',// 公司名称
                    introduce:'',// 公司介绍
                    address:'',// 公司地址
                    tel:"", //公司tel
                    manager_name:'',//店长姓名
                    logo:'',// 门店图片
                }
			};
		},
		onLoad(){
        },
		methods:{
			inputName(e){
                this.params.name =e.detail.value
            },
            inputTel(e){
                this.params.tel =e.detail.value
            },
            inputDesc(e){
                this.params.introduce =e.detail.value
            },
            inputAddress(e){
                this.params.address =e.detail.value
            },
            inputShoperName(e){
                this.params. manager_name =e.detail.value
            },
            uploadDon(e){
                this.params.logo=e.files.join(',')
            },
            submit(){
                this.$ajax.post('agentCompany/applyAgentCompany',this.params,res=>{
					if(res.data.code === 1){
						uni.showToast({
							title:res.data.msg,
                            icon:"none"
						})
						setTimeout(()=>{
							this.$navigateBack()
						},2000)
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:'none'
						})
					}
				})
            }
			
			
			
			
		}
	}
</script>

<style lang="scss">
	.addshop{
        background: #fff;
        .imgs{
            width: 100%;
            height: 224upx;
            overflow: hidden;
            background-image: url('https://images.tengfangyun.com/images/new_icon/<EMAIL>');
            background-repeat: no-repeat;
            background-size: 100% 100%
            // image{
            //     width: 100%;
            // }
        }
        .info{
            padding: 0 48upx;
            .title{
                font-size: 40upx;
                color: #333333;
                font-weight: 600;
            }
            .tips{
                margin: 16upx 0 48upx;
                color: #999;
                font-size: 28upx;
            }
            input{
                padding: 24rpx;
                font-size: 28rpx;
                background-color: #f5f5f5;
                margin-bottom: 24upx;
            }
        }
        .logo{
            padding: 0 48upx;
            .title{
                font-size: 40upx;
                color: #333333;
                font-weight: 600;
            }
            .tips{
                margin: 16upx 0 48upx;
                color: #999;
                font-size: 28upx;
            }
            .upload-btn{
                width: 200rpx;
                height: 200rpx;
                align-items: center;
                justify-content: center;
                background: #F5F5F5

            }
            .img{
                width:200rpx;
                height:200rpx;
            }

        }
        .submit{
            margin: 106upx 48upx 24upx;
            padding: 28upx 0;
            text-align: center;
            background: #FB656A;
            color: #fff;
            font-size: 32upx;
            box-shadow: 0 4px 12px 0 rgba(251,101,106,0.40);
            border-radius: 22px;
        }

    }
</style>
