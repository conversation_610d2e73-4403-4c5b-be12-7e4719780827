<template>
  <view class="my-search flex-row" :class="{small_round:mode==='small_round'}" @click="$emit('click')">
    <slot name="left">
      <my-icon type="ic_sousuo" color="#999"></my-icon>
    </slot>
    <input
      class="input"
      type="text"
      :placeholder="placeholder"
      :disabled="disabled"
      :focus="focus"
      confirm-type="search"
      v-model="now_value"
      @input="handleInput"
      @confirm="confirm"
    />
    <slot name="right" />
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
export default {
  components: {
    myIcon
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入搜索内容'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    focus: {
      type: Boolean,
      default: false
    },
    value: {
      default: ''
    },
    mode: {
      type: String,
      default: 'default'
    }
  },
  model: {
    event: 'input',
    prop: 'value'
  },
  computed: {
    now_value: {
      get: function() {
        return this.value
      },
      set: function() {}
    }
  },
  data() {
    return {}
  },
  methods: {
    handleInput(e) {
      this.$emit('input', e.detail.value)
    },
    confirm(e) {
      this.$emit('confirm', e.detail.value)
    }
  }
}
</script>

<style scoped lang="scss">
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}
.my-search {
  flex-direction: row;
  align-items: center;
  padding: 0 20rpx;
  font-size: 28rpx;
  height: 64rpx;
  border-radius: 32rpx;
  background-color: #eee;
  &.small_round{
    border-radius: 8rpx;
  }
  .input {
    flex: 1;
    padding: 10rpx 20rpx;
    height: 44rpx;
    font-size: 26rpx;
  }
}
</style>
