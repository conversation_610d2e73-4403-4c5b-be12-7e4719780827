
<template>
<view class="page">
    <view class="header">
      <title-bar backgroundColor="#fb656a" title="塞红包">
      </title-bar>
    </view>
    <view class="con">
      <view class="tabbar">
        <tab-bar :tabs="navs" :nowIndex="currentIndex"  :fixedTop="false" @click="switchTab"></tab-bar>
      </view>
      <view class="hb_content">
        <template v-if ="currentType ==0">
          <view class="tips"> 
            房源发布方通过塞红包到房源页，网友通过转发朋友圈，邀请好友助力领取拼手气红包，可有效增加房源曝光率和上客率。
          </view>
          <view class="form">
            <view class="form_item flex-row">
              <view class="label">
                红包金额
              </view>
              <input class ="flex-1" type="text" :placeholder="moneyPlaceholder" v-model ="saiParams.money">
              <view class="unit">元</view>
            </view>
            <view class="form_item flex-row">
              <view class="label">
                红包数量
              </view>
              <input class ="flex-1" type="text" :placeholder="countPlaceholder" v-model ="saiParams.hb_count">
              <view class="unit">个</view>
            </view>
            <!-- <view class="form_item flex-row">
              <view class="label">
                支付方式
              </view>
              <picker  class ="flex-1" :range="[{name:'微信',type:'1'},{'name':'支付宝',type:'2'}]" @change="typeChange" range-key='name'>
                <view class="pay_type">
                  {{saiParams.pay_type?payType:'请选择支付方式'}}
                </view>
              </picker>
              <view class="unit"><myIcon type="ic_into" color="#999" size="26"></myIcon></view>
            </view> -->
            <view class="money_tips flex-row">
              <view class="icon">
                <myIcon type="jinggao" color="#999" size="22"></myIcon>
              </view>
              <view class="text">含手续费{{shouxufei}}元，本次塞红包将获赠金币{{hb_corn}}个</view>
            </view>
            <view class="btns" @click ="saiHongbao">
              <view class="btn">
                塞钱进红包
              </view>
            </view>
          </view>
        </template>
        <template v-if ="currentType ==1">
          <view class="con_hb_log">
            <view class="con_hb_log_info">
              <view class="con_hb_log_info_title flex-row">
                <view class="img">
                  <image :src="hb_small" mode="widthFix"></image>
                </view>
                <view class="info_title">
                  {{hbInfo.info_title}}
                </view>
              </view>
              <view class="con_hb_log_info_money flex-row">
                <text class="info_name">
                  余额
                </text>
                <text class="info_money_c">
                  {{Number(hbInfo.hb_money) - Number(hbInfo.get_money)}}
                </text>
                <text class="info_name info_unit">
                  元
                </text>
              </view>
              <view class="con_hb_log_info_info flex-row ">
                <text>总金额：{{hbInfo.hb_money}}元</text>
              </view>
            </view>
            <view class="con_hb_log_list">
              <view class="con_hb_log_list_title bottom-line">
                领取 {{hbInfo.get_count}}/{{hbInfo.sai_count}}
              </view>
              <view class="con_hb_log_list_con">
                <view class="con_hb_log_list_con_item bottom-line flex-row" v-for ="(item,index) in hbLogList" :key="index">
                  <view class="con_item_left">
                    <image :src="item.prelogo"  mode="widthFix"></image>
                  </view>
                  <view class="con_item_right flex-1">
                    <view class="con_item_right_top flex-row">
                      <text class="con_item_right_top_info">{{item.username}}领取红包</text>
                      <text class='con_item_right_top_money'>{{item.get_money}}元</text>
                    </view> 
                    <view class="con_item_right_bottom">
                      {{item.get_time}}
                    </view>
                  </view>
                  
                </view>
                <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
              </view>
            </view>
          </view>
        </template>
        <template v-if ="currentType ==2">
          <view class="con_shb_log">
            <view class="con_shb_log_list">
              <template v-if ="saiHbLogList.length">
                <view class="con_shb_log_list_item flex-row" v-for ='(item,index) in saiHbLogList' :key ="index">
                  <view class="con_shb_log_list_item_left ">
                    <image :src='saiHongbaoSrc' mode="widthFix">

                    </image>
                  </view>
                  <view class="con_shb_log_list_item_middle">
                    <view class="con_shb_log_list_item_middle_top">
                      红包金额：{{item.hb_money}}元
                    </view>
                    <view class="con_shb_log_list_item_middle_bottom">
                      <text>{{item.ctime}}</text>
                      <text class ="ml24">支付方式：{{item.pay_type==1?"微信":'支付宝'}}</text>
                    </view>
                  </view>
                  <view class="con_shb_log_list_item_right">
                    <view class="item_right_btn" :class="{'lingwan':item.received_status==1 || item.is_refund}" @click ="toTuikuan(item)">{{item.received_status==1?'已领完':(item.is_refund?'已退款':'退款')}}</view>
                  </view>
                </view>
              </template>
              <template v-else >
                <view class="no_data">没有更多数据了</view>
              </template>
            </view>
          </view>
        </template>
      </view>
    </view>
</view>
</template>

<script>
import titleBar from "@/components/titleBar"
import tabBar from "@/components/tabBar"
import myIcon from "@/components/myIcon"
import {uniLoadMore} from '@dcloudio/uni-ui'
import {config} from '@/common/index'
export default {
  components:{titleBar,tabBar,myIcon,uniLoadMore},
  data(){
    return {
      navs:[
        {
          name:'塞红包',
          type:0
        },
        // {
        //   name:'塞红包记录',
        //   type:2
        // },
      ],
      currentIndex:0,
      currentType:0,
      saiParams:{
        info_id:'',
        info_type:'',
        hb_count:"",
        money:'',
        pay_type:"1"
      },
      hbLogParams:{
        page:1,
        rows:20,
        info_id:'',
        info_type:'',
      },
      hbLogList:[],
      payType:'微信',
      moneyPlaceholder:'请输入',
      countPlaceholder:'请输入',
      get_status:"loading",
      content_text:{
          contentdown:"",
          contentrefresh:"正在加载...",
          contentnomore:"没有更多数据了"
      },
      get_status1:"loading",
      content_text:{
          contentdown:"",
          contentrefresh:"正在加载...",
          contentnomore:"没有更多数据了"
      },
      saiHbLogList:[]
    }
  },
  onLoad(options){
    if(options.id ){
      this.saiParams.info_id = options.id
      this.id = options.id
    }
    if (options.type){
      this.saiParams.info_type = options.type
      this.type= options.type
    }
    this.getWxConfig(['chooseWXPay'],
      wx => {
      console.log('执行回调')
      this.wx = wx
    })
    this.getHbConfig()
    
  },
  computed: {
    shouxufei(){
          if (!this.saiParams.money) return 0
          let fuwufee =Number(this.saiParams.money) *this.hbConfig.help_sai_service_rate/100
          return Math.round(fuwufee*Math.pow(10, 2))/Math.pow(10, 2)
      },
      hb_corn(){
          if (!this.saiParams.money) return 0
          if (!this.hbConfig.help_sai_give_gold) return 0
          let cornfee =Number(this.saiParams.money) /this.hbConfig.help_sai_give_gold
          return parseInt(cornfee)
      },
      hb_small(){
        return config.imgDomain+'/hongbao/saihongbao/<EMAIL>?x-oss-process=style/m_240'
      },
      saiHongbaoSrc(){
        return config.imgDomain+'/hongbao/saihongbao/<EMAIL>?x-oss-process=style/m_240'
      }
  },
    watch:{
      "saiParams.money"(val,oval){
          this.money = Math.round((Number(val) + this.shouxufei)*Math.pow(10, 2))/Math.pow(10, 2)
          let  min_count =Math.floor(Number(this.saiParams.money)/ Number(this.hbConfig.help_max_money));
          this.min_count = min_count>0?min_count:1
          this.max_count =Math.floor(Number(this.saiParams.money)/ Number(this.hbConfig.help_min_money));
          // this.saiParams.hb_count = this.max_count
          this.$set(this.saiParams,"hb_count",this.max_count)
      }
  },
  methods: {
    // 支付方式切换
    typeChange(e){
      console.log(e);
      if (e.detail.value ==1){
        this.payType = '支付宝'
        
      }else {
        this.payType = '微信'
        
      }
      this.saiParams.pay_type =e.detail.value +1
    },
    // tab切换
    switchTab(e){
      console.log(e);
      this.isChange = true
      this.currentIndex =e.index
      this.currentType= this.navs[e.index].type
      if ( this.currentType ==0){
        this.getHbConfig()
      }else if (this.currentType==1){
        this.hbLogParams.page=1
        this.getHbLogs()
      }else if (this.currentType==2){
        this.getSaiHongbaoLog()
      }
    },
    //  获取红包设置
    getHbConfig(){
      this.$ajax.get("wxMoney/saiHbConfig",{info_id:this.id,info_type:this.type},res=>{
        console.log(res);
        if (res.data.code ==1){
          this.hbConfig =  res.data.config
          this.hbInfo = res.data.hb_info
          if (res.data.hb_info.id &&res.data.hb_info.sai_count>0 &&!this.isChange){
            this.navs=[
              {
                name:'塞红包',
                type:0
              },
              {
                name:'进行中',
                type:1
              },
              // {
              //   name:'塞红包记录',
              //   type:2
              // },
            ]
        
            this.currentIndex=1
            this.currentType =1
            this.getHbLogs()
          }else {
            this.currentIndex=0
            this.currentType =0
          }
        }
      })
    },
    // 获取红包记录
    getHbLogs(){
      if (this.hbLogParams.page ==1){
        this.hbLogList =[]
      }
      this.$ajax.get("wxMoney/getLog",{info_id:this.id,info_type:this.type,page:this.hbLogParams.page,rows:this.hbLogParams.rows},res=>{
        if (res.data.code ==1){
            this.hbLogList =this.hbLogList.concat(res.data.list)
            if (res.data.list.length<this.hbLogParams.rows){
              this.get_status ="nomore"
            }else {
              this.get_status ="more"
            }
        }else {
          this.get_status ="nomore"
        }
      })
    },
    // 塞钱进红包
    saiHongbao(){
      if (Number(this.saiParams.money) <Number(this.hbConfig.help_sai_min_money)) {
        uni.showToast({
          title: "塞红包金额不能小于"+this.hbConfig.help_sai_min_money+'元',
          icon:'none'
        });
        return
      }
      if (Number(this.saiParams.hb_count)<Number(this.min_count)) {
        uni.showToast({
          title: "塞红包份数不能小于"+this.min_count+'份',
          icon:'none'
        });
        return
      }
      if (Number(this.saiParams.hb_count)>Number(this.max_count)) {
        uni.showToast({
          title: "塞红包份数不能大于"+this.max_count+'份',
          icon:'none'
        });
        return
      }
      this.checkHbStatus()
    },
    checkHbStatus(){
      // wxMoney/saiHb
      this.$ajax.post("wxMoney/saiHb",this.saiParams,res=>{
        if (res.data.code ==1){
          // #ifdef H5
        var ua = window.navigator.userAgent.toLowerCase();
        //通过正则表达式匹配ua中是否含有MicroMessenger字符串
        if(ua.match(/MicroMessenger/i) == 'micromessenger'){
          // 公众号
          let pay_info = res.data.data
                  console.log(this.wx);
          this.wx.chooseWXPay({
              // provider: 'wxpay',
              timestamp:pay_info.timeStamp,
              nonceStr:pay_info.nonceStr,
              package:pay_info.package,
              signType:pay_info.signType,
              paySign:pay_info.paySign,
              success: (res)=> {
                  uni.showToast({
                      title:"支付成功"
                  })
                  this.$refs.sub_form.closeSub()
                  setTimeout(()=>{
                    this.$navigateTo('/user/order_list')
                  }, 1500)
                  // this.$refs.upgrade_success.show()
              },
              fail: function (err) {
                console.log(err);
                uni.showToast({
                    title:err.err_desc||(err.errMsg=='requestPayment:fail cancel'?'已取消支付':err.errMsg),
                    icon:"none",
                    duration:5000
                })
              }
          })
        }else{
          window.open(res.data.url)
        }
          
          // #endif
        }
        
      })
    },
    // 获取塞红包记录
    getSaiHongbaoLog(){
      this.$ajax.get("wxMoney/mySaiLog",{info_id:this.id,info_type:this.type},res=>{
        console.log(res);
        if (res.data.code ==1){
          // this.saiHbLogList.concat(res.data.list)
            this.saiHbLogList =res.data.list
            // if (res.data.list.length<this.hbLogParams.rows){
            //   this.get_status1 ="nomore"
            // }else {
            //   this.get_status1 ="more"
            // }
        }else {
          // this.get_status1 ="nomore"
        }
      })
    },
    // 退款
    toTuikuan (item) {
      if (item.is_refund ||item.received_status==1) return 
      uni.showModal({
        content:"确定要退款吗？",
        success:(res)=>{
          if (res.confirm){
            this.$ajax.post('wxMoney/refund',{sai_id:item.id},res=>{
              uni.showToast({
                title: res.data.msg,
                icon:'none'
              });
              if (res.data.code ==1){
                this.getSaiHongbaoLog()
              }
            })
          }else if (res.cancel){
            uni.showToast({
                title: '已取消申请退款',
                icon:'none'
              });
          }
          
        }
      })
    },
  },
  onReachBottom(){
    if (this.currentIndex==1&&this.get_status=='more'){
      this.hbLogParams.page ++
      this.getHbLogs()
    }
    // if (this.currentIndex==2&&this.get_status1=='more'){
    //   this.shbLogParams.page ++
    //   this.getSaiHongbaoLog()
    // }
  }
}
</script>

<style lang="scss" scoped>
  .header{
    background: #FB656A;
  }
  .flex-row{
    display: flex;
    flex-direction: row;
  }
  .con {
    display: flex;
    flex-direction: column;

    .hb_content {
      flex:1;
      background: #fff;
      .tips {
        background: #F8F8F8;
        font-size: 11px;
        color: #FB656A;
        padding: 24rpx 48rpx;
      }
      .form{
        padding: 24rpx 48rpx;
        .form_item {
          padding: 24rpx 0;
          align-items: center;
          .label {
            margin-right: 24rpx;
          }
        }
        .money_tips{
          margin-top: 24rpx;
          font-size: 22rpx;
          color: #999;
          align-items: center;
          .icon {
            margin-right: 8rpx;
          }
          .text{
            color: #999;
            font-size: 22rpx;
          }
        }
        .btns{
          position: fixed;
          bottom: 0;
          left: 0;
          right: 0;
          background: #FFFFFF;
          padding:8rpx  48rpx;
          .btn {
            background: #FB656A;
            text-align: center;
            box-shadow: 0px 8rpx 24rpx 0 rgba(251,101,106,0.4);
            border-radius: 8rpx;
            font-size: 32rpx;
            color: #FFFFFF;
            padding: 28rpx 0;
          }
        }
      }
      .con_hb_log{
        margin-top: 24rpx;
        &_info{
          padding:48rpx;
          background: #fff;
          &_title{
            width: 100%;
            overflow: hidden;
            align-items: center;
            justify-content: center;
            .img{
              width: 48rpx;
              height: 48rpx;
              overflow: hidden;
              image{
                width: 100%;
                height: 100%;
              }
            }
            .info_title {
              font-size: 32rpx;
              margin-left: 12rpx;
              max-width: calc(100% - 156rpx);
              font-weight: 600;
              color: #333333;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          &_money{
            font-size: 28rpx;
            color: #999999;
            padding: 20rpx 0;
            align-items: center;
            justify-content: center;
            
            .info_name{

            }
            .info_money_c{
              font-size: 100rpx;
              color: #FB656A;
              font-weight: 600;
              margin:0 24rpx;
            }
          }
          &_info{
            justify-content: center;
            align-items: center;
            font-size: 28rpx;
            color: #999999;
          }
        }
        &_list {
          margin-top: 24rpx;
          padding: 0 48rpx;
          background: #fff;
          &_title{
            padding: 24rpx 0;
            font-size: 28rpx;
            color: #999999;
          }
          &_con {
            &_item{
              align-items: center;
              padding: 24rpx 0;
              width: 100%;
              overflow: hidden;
              .con_item_left{
                width: 88rpx;
                height: 88rpx;
                margin-right: 24rpx;
                overflow: hidden;
                border-radius: 50%;
                image{
                  width: 100%;
                  height: 100%;
                }
              }
              .con_item_right{
                
                &_top{
                  align-items: center;
                  justify-content: space-between;
                  font-size: 28rpx;
                  color: #333333;
                }
                &_bottom{
                  margin-top: 18rpx;
                  font-size:22rpx;
                  color: #999999;
                }
              }
            }
          }
        }
      }
      .con_shb_log{
        &_list{
          background: #fff;
          padding: 0 48rpx;
          .no_data{
            padding: 48rpx;
            color: #999;
            text-align: center;
          }
          &_item{
            padding: 24rpx 0;
            align-items: center;
            &_left {
              width: 64rpx;
              height: 64rpx;
              overflow: hidden;
              image{
                width: 100%;
                height: 100%;
              }
            }
            &_middle{
              margin: 0 24rpx;
              flex: 1;
              &_top{
                font-size: 28rpx;
                color: #333333;
              }
              &_bottom{
                margin-top: 16rpx;
                font-size: 22rpx;
                color: #999;
                .ml24{
                  margin-left: 24rpx;
                }
              }
            }
            &_right{
              .item_right_btn{
                padding: 8rpx 34rpx;
                font-size: 22rpx;
                color: #FB656A;
                background: #FFF0F0;
                border-radius: 30rpx;
                &.lingwan{
                  background: #F8F8F8;
                  color: #999;
                }
              }
            }

          }
        }
      }
    }
  }
</style>
