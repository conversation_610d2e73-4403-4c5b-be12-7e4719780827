<template>
<view class="consulist">
  <!-- 顶部导航 -->
  <view class="top_nav flex-row bottom-line">
      <view class="nav_item" v-for="(item, index) in top_navs" :key="index" :class="{active:current_nav_index===index}" @click="scroppTo(item.id, index)">{{item.name}}</view>
  </view>
  <!-- 分栏 -->
  <!-- <view class="tab-list flex-row bottom-line" :style="{top:advTop}">
				<view class="tab-item" :class="{active:show_tab==='adv_list'}" @click="switchTab('adv_list')">置业顾问</view>
				<view class="tab-item" :class="{active:show_tab==='news_list'}" @click="switchTab('join_adv')">顾问入驻</view>
	</view> -->
  <!-- 列表 -->
   <!-- 置业顾问 -->
  <view class="block container">
    <!-- <view class="label">买新房 找新房置业顾问</view> -->
    <view class="advier-list">
      <view
        class="adviser-item flex-row"
        v-for="item in list"
        :key="item.id"
        @click="consuDetail(item)"
      >
        <view class="header_img">
          <image mode="widthFix" :src="item.prelogo | imageFilter('w_120')"></image>
        </view>
        <view class="info">
          <view class="name flex-row">
            <text class="text">{{ item.cname || item.typename }}</text>
          </view>
          <view class="data">
            <text>{{ item.traffic_volume }}人咨询过他</text>
            <!-- <text>{{item.build_names||item.introduce||''}}</text> -->
          </view>
        </view>
        <view class="adviser-right">
          <view class="btn-list flex-row">
            <view class="btn">
              <chat-btn
                :user_login_status="login_status"
                :user_id="item.mid||item.uid||item.id"
                :identity_id="item.adviser_id||item.uid||item.id"
                @ok="advAsk"
              >
                <view class="icon-box">
                  <my-icon type="ic_zixun1" size="45rpx" color="#ff656c"></my-icon>
                </view>
              </chat-btn>
            </view>
            <view class="btn" v-if='switch_adviser_tel'>
              <tel-btn :user_id="item.mid||item.uid||item.id" :identity_id="item.adviser_id||item.uid||item.id" :tel="item.tel" @ok="handleTel($event, item)">
                <view class="icon-box">
                  <my-icon type="ic_dianhua1" size="45rpx" color="#ff656c"></my-icon>
                </view>
              </tel-btn>
            </view>
          </view>
        </view>
      </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <view class="comment_btn flex-row" @click="join" v-if="!current_is_adviser &&is_show_apply_adviser==1">
            <!-- <my-icon type="ic_pinglun" color="#FB656A"></my-icon> -->
            <text>顾问入驻</text>
    </view>
  </view>
  
 
  <!-- 底部操作菜单 -->
    <view class="bottom-bar flex-row">
      <view class="bar-left flex-row flex-1">
        <!-- <view class="icon-btn" @click="handleFollow">
          <my-icon :type="is_follow?'ic_guanzhu_red':'ic_guanzhu'" :color="is_follow?'#ff656b':'#666'" size="50rpx"></my-icon>
          <text>关注</text>
        </view> -->
        <view class="icon-btn" v-if="navs[0].is_show&&(navs[0].operation===1||navs[0].operation===2)" @click="toYuyue(3, navs[0])">
          <my-icon type="yuyue" color="#666" size="50rpx"></my-icon>
          <text>{{navs[0].name}}</text>
        </view>
        <view class="icon-btn" v-if="navs[0].is_show&&(navs[0].operation===3||navs[0].operation===4)" @click="cusList( navs[0].operation)">
          <my-icon type="ic_zixun" color="#666" size="50rpx"></my-icon>
          <text>{{navs[0].name}}</text>
        </view>
        <view class="icon-btn" v-if="navs[1].is_show===1" @click="toContrast()">
          <text class="badge" v-if="login_status>1&&contrastCount>0">{{contrastCount>99?'99+':contrastCount}}</text>
          <text class="badge" v-if="login_status<=1&&$store.state.temp_huxing_contrast_ids.length>0">{{$store.state.temp_huxing_contrast_ids.length>99?'99+':$store.state.temp_huxing_contrast_ids.length}}</text>
          <my-icon type="pk" color="#666" size="50rpx"></my-icon>
          <text>{{navs[1].name}}</text>
        </view>
      </view>
      <!-- 置业顾问按钮 -->
      <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show===1&&(navs[2].operation===1||navs[2].operation===2)" @click="toYuyue(3, navs[2])">{{navs[2].name}}</view>
      <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show===1&&(navs[2].operation===3||navs[2].operation===4)" @click="cusList(navs[2].operation)">{{navs[2].name}}</view>
      <!-- 咨询售楼处按钮 -->
      <view class="flex-1" :class="{alone:navs[2].is_show===0}" v-if="navs[3].is_show===1" @click="handleTel()">
        <view class="bar-btn btn2">{{navs[3].name}}</view>
      </view>
    </view>


   <!-- #ifndef MP-WEIXIN -->
   <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip"></login-popup>
   <!-- #endif -->
   <sub-form  :sub_type="sub_type" :sub_mode="sub_mode" ref='sub_form' @onsubmit="handleSubForm"></sub-form>
   <chat-tip></chat-tip>
   <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
</view>
</template>

<script>
import {
  uniIcons,
  uniLoadMore
} from '@dcloudio/uni-ui'
import myIcon from '../../components/myIcon'
// #ifndef MP-WEIXIN
import loginPopup from '../../components/loginPopup'
// #endif
import chatBtn from '../../components/open-button/chatBtn'
import telBtn from '../../components/open-button/telBtn'
import getChatInfo from '../../common/get_chat_info'
import subForm from '../../components/subForm'
import {
  formatImg,
  showModal
} from '../../common/index.js'
import {
  mapState,
} from 'vuex'
import allTel from '../../common/all_tel.js'
import {wxShare} from '../../common/mixin'
export default {
  data() {
    return {
      id: "",
      list: [],

      page: 1,
      rows: 10,
      seo:{},
      share:{},
      top_navs:[
          {
						name: '置业顾问',
						id: 'zhiyeguwen'
					},
					
					{
						name: '楼盘',
						id: 'build'
					},
					{
						name: '动态',
						id: 'news'
					},
					{
						name: '点评',
						id: 'comment'
          },
          
					
					{
						name: '户型',
						id: 'huxing'
					},
					
        ],
      show_tab:"adv_list",
      current_is_adviser:1,
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了"
      },
      build:{},
      title:"",
      consu:1,//是否楼盘开启置业顾问 
      login_tip:'',
      current_nav_index:0,
      contrastCount: 0,
      sub_type:0,
      adviser_id:0,
      navs: [
        {},
        {},
        {},
        {}
      ],
      tel_res: {},
      show_tel_pop: false,
      is_show_apply_adviser:1
    }
  },
  mixins: [wxShare],
  components: {
    uniIcons,
    myIcon,
    uniLoadMore,
    chatBtn,
    telBtn,
    subForm,
    // #ifndef MP-WEIXIN
    loginPopup
    // #endif
  },
  filters: {
    imgUrl(val, param = "") {
      return formatImg(val, param)
    },
  },
  computed: {
    // 置业顾问电话显示更改小程序合并的时候注意不是本页面
    ...mapState(['tel400jing','switch_adviser_tel']),
    imconsu() {
        return this.$store.state.im.adviser
    },
    is_open_im() {
        return this.$store.state.im.ischat 
    },
    imistel() {
        return this.$store.state.im.istelcall 
    },
    glabol_middle_tel(){
        return this.$store.state.im.istelcall
    },
    login_status() {
      return this.$store.state.user_login_status
    },
    sub_mode() {
        return this.$store.state.sub_form_mode 
    },
    advTop(){
          // #ifdef MP 
          return this.$store.state.systemInfo.statusBarHeight+45
          // #endif 
            // #ifndef MP 
          return '90px'
          // #endif 
    }
  },
  onLoad(options) {
    if (options.open) {
      this.consu= options.open
    }
   
    if (options.id) {
      this.bid = options.id
      this.id = options.id
      this.getData()
    }
     if (options.title) {
      this.title = options.title
    }
    this.getNav()
    //  uni.setNavigationBarTitle({
    //    title:this.title?`${ this.title}顾问列表`:'顾问列表'
    //  })
  },
  onUnload() {
    this.$store.state.buildInfo = {}
  },
  methods: {
    getData(){
      if(this.page === 1){
        this.list = []
      }
      this.$ajax.get('build/getMounters', {id: this.id, page:this.page, rows:this.rows}, res=>{
        this.top_navs[0].name=res.data.mountTitle
        if (res.data.share){
          this.share =res.data.share
          this.getWxConfig()
        }
        if(res.data.code === 1){
          uni.setNavigationBarTitle({
            title:`${res.data.buildTitle}${res.data.mountTitle}`
          })
        this.is_show_apply_adviser = res.data.customSetting?res.data.customSetting.is_show_apply_adviser:1
        this.current_is_adviser = res.data.reg
          if (res.data.mountMembers.length < this.rows) {
            this.get_status = "noMore"
          } else {
            this.get_status = "more"
          }
          this.list = this.list.concat(res.data.mountMembers)
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
          this.get_status = "noMore"
        }
      })
    },
    getNav(){
      this.$ajax.get('build/buildNav.html',{bid:this.id},res=>{
        console.log(res.data)
        if(res.data.code === 1){
          this.navs = res.data.navs
          this.build=res.data.build
        }
      })
    },
    toYuyue(type, nav){
      if(nav.operation===2&&nav.group_id){
        // 跳转团购报名
        this.$navigateTo(`/pages/groups/detail?id=${nav.group_id}`)
      }else{
        this.toSubForme(type)
      }
    },
    toContrast(){
      if(this.login_status>1){
        this.$navigateTo('/contrast/house_list')
      }else{
        this.$navigateTo(`/contrast/house_list?no_login=1`)
      }
    },
    toSubForme(type) {
      this.sub_type = type
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e) {
      //提交报名
      e.from = '顾问列表'
      e.bid = this.id
      e.type = this.sub_type || ''
      this.$ajax.post('build/signUp.html', e, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          // 没开启引导登录模式或已经绑定手机号了
          if (this.sub_mode!==2||res.data.status === 3) {
            //提示报名成功
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            this.$refs.sub_form.closeSub()
          } else {
            this.$refs.sub_form.getVerify()
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    scroppTo(id, index){
				
				if(id==='build'){
					this.$navigateTo(`/pages/new_house/detail?id=${this.id}`)
					return 
				}
				if(id==='news'){
					this.$navigateTo(`/pages/new_house/buildNews?bid=${this.id}`)
					return 
				}
				if(id==='huxing'){
					this.$navigateTo(`/pages/new_house/house_type_list?bid=${this.id}`)
					return 
				}
				if(id==='comment'){
        this.$navigateTo(`/pages/new_house/comment?bid=${this.id}&open=${this.consu}`)
					return 
				}
				
      },
      switchTab(type){
				// this.show_tab = type
				if(type==='adv_list'&&this.share_list.length===0){
          this.page = 0
          this.getData()
				}
				if(type==='join_adv'){
					this.join()
				}
			},
    //复制
    copy(str) {
      // #ifdef H5
      const textString = str.toString();
      let input = document.querySelector('#copy-input');
      if (!input) {
        input = document.createElement('input');
        input.id = "copy-input";
        input.readOnly = "readOnly"; // 防止ios聚焦触发键盘事件
        input.style.position = "absolute";
        input.style.left = "-1000px";
        input.style.zIndex = "-1000";
        document.body.appendChild(input)
      }

      input.value = textString;
      // ios必须先选中文字且不支持 input.select();
      selectText(input, 0, textString.length);
      if (document.execCommand('copy')) {
        document.execCommand('copy');
        uni.showToast({
          title: "已复制 打开微信添加好友吧",
          icon: "none"
        })
      }
      input.blur();

      function selectText(textbox, startIndex, stopIndex) {
        if (textbox.createTextRange) { //ie
          const range = textbox.createTextRange();
          range.collapse(true);
          range.moveStart('character', startIndex); //起始光标
          range.moveEnd('character', stopIndex - startIndex); //结束光标
          range.select(); //不兼容苹果
        } else { //firefox/chrome
          textbox.setSelectionRange(startIndex, stopIndex);
          textbox.focus();
        }
      }

      // #endif
      // #ifndef H5
      uni.setClipboardData({
        data: str,
        success: function () {
          console.log('success');
        },
      });
      // #endif
    },
   
    join(){
      if (this.current_is_adviser==1){  //已经注册
        this.$navigateTo("/pages/consultant/detail?id="+this.adviser_id)
      return

      }
      if (this.imconsu == 1 ){
        if (this.$store.state.home_switchs.allow_advsier_register){
            this.$navigateTo("/user/consultant/add?share=1")
        }else {
            uni.showToast({
              title:"暂未开放 敬请期待",
              icon:"none"
            })
        }
        
      }
    },
    checkLogin(tip, callback) {
      this.$ajax.get('member/checkUserStatus', {}, res => {
        if (res.data.code === 1) {
          callback&&callback()
        }else if(res.data.status == 1){
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
        }else if(res.data.status == 2){
          this.$store.state.user_login_status = res.data.status
          this.login_tip = tip
          this.$refs.login_popup.showPopup()
        }
      })
    },
     // 和置业顾问发起聊天
    advAsk(e) {
      if (this.is_open_im == 0) {
        // 判断id是不是置业顾问id
        // 根据id查询出挂载列表中的置业顾问
        var current_adviser = this.list.find(item=>item.adviser_id===e.identity_id)
        if(current_adviser&&!current_adviser.isGuwen){
          this.$navigateTo('/pages/consultant/detail?id=' + e.identity_id)
        }else{
          console.log("没开启聊天且不是置业顾问,不跳转详情")
        }
        return
      }
      // #ifdef MP-WEIXIN
      getChatInfo(e.user_id, 4, this.bid)
      //  #endif
      // #ifndef MP-WEIXIN
      this.checkLogin('为方便您及时接收消息通知，请输入手机号码', ()=>{
        getChatInfo(e.user_id, 4, this.bid)
      })
      //  #endif
    },
    // 拨打电话
    handleTel(e, options={}) {
      // 如果有身份id则拨打置业顾问电话
      if(e&&e.identity_id){
        if(options.isAgent){
          e.isAgent = 1
        }
        if(options.isAdviser){
          e.isAdviser = 1
        }
        this.callAdviserMiddleNumber(e)
        return
      }
      let phoneNumber=""
          // 如果没有开启虚拟号功能
          if(this.build.use_middle_call==0||this.glabol_middle_tel==0){
              if(this.build.phone&&this.build.sellmobile_part){
                  phoneNumber = this.build.phone+','+this.build.sellmobile_part.trim()
                  if(this.tel400jing){
                      phoneNumber+="#"
                  }
                  showModal({
                      title:"温馨提示",
                      content:"请拨打"+this.build.phone+"后转拨分机号"+this.build.sellmobile_part,
                      confirm: (res)=> {
                          uni.makePhoneCall({
                              phoneNumber: phoneNumber
                          });
                      }
                  })  
              }else if(this.build.tel){
                  uni.makePhoneCall({
                      phoneNumber: this.build.tel,
                      success:()=>{
                        // this.statistics()
                      }
                  });
              }else{
                  uni.showToast({
                    title:"此楼盘没有绑定联系电话",
                    icon:'none'
                  })
              }
          }else{
            this.tel_params = {
              type: 1,
              callee_id: this.id,
              scene_type: 1,
              scene_id: this.id,
              success: (res)=>{
                this.tel_res = res.data
                this.show_tel_pop = true
              }
            }
            allTel(this.tel_params)
          }
      
    },
    // 拨打置业顾问虚拟号码
    callAdviserMiddleNumber(e) {
      console.log('拨打置业顾问虚拟号码')
      var call_adviser = {}
      if(e.identity_id){
        call_adviser = e
      }else if(this.cusArr&&this.cusArr.id){
        call_adviser = this.cusArr
      }else if(this.adviser_list.length>0){
        call_adviser = this.adviser_list[0]
      }
      console.log(call_adviser)
      var user_id = call_adviser.user_id||call_adviser.mid||call_adviser.uid||call_adviser.id
      var identity_id = call_adviser.identity_id||call_adviser.adviser_id||call_adviser.uid||call_adviser.id
      var tel_type = "",bid
      if(call_adviser.isAgent){
        tel_type = 3
        bid = this.id
      }
      if(call_adviser.isAdviser){
        tel_type = 2
      }
        if (!call_adviser.isAgent && !call_adviser.isAdviser) {
            tel_type = 0
        }
      this.callMiddleNumber(tel_type,identity_id,1,this.id,'',bid)
    },
    
    // 请求虚拟号接口
    callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
      this.tel_params = {
        type,
        callee_id,
        scene_type,
        scene_id,
        source,bid,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        },
        fail: (res)=>{
          if(res.data.code === -1){
            this.$store.state.user_login_status = 1
            uni.removeStorageSync('token')
            this.$navigateTo('/user/login/login')
          }
          if(res.data.code === 2){
            this.$store.state.user_login_status = 2
            this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
            this.$refs.login_popup.showPopup()
          }
        }
      }
      // #ifdef MP-WEIXIN
      allTel(this.tel_params)
      // #endif
      // #ifndef MP-WEIXIN
      allTel(this.tel_params)
      // #endif
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    cusList(operation){
        return;
        // if(operation===4){
        //   console.log("和置业顾问发起聊天")
        //   return
        // } 
        if (!uni.getStorageSync('token')) {
          this.$navigateTo('/user/login/login')
          return
        }
        this.$navigateTo('/pages/consultant/consuList?id=' + this.id)
      },
		
    handleCloseLogin(){
      if(this.$store.state.user_login_status===1){
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if(this.$store.state.user_login_status===2){
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    // 到详情页
    consuDetail(item) {
      if(item.isAdviser&&item.adviser_id){
        this.$navigateTo("/pages/consultant/detail?id="+item.adviser_id)
      }
    },

  },
  // 触底加载
  onReachBottom() {
    this.page++;
    this.getData()
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.page = 0;
    this.key = "";
    this.getData()
  },
  onShareAppMessage(){
      return {
			  title: this.share.title||"",
        content: this.share.content||"",
        imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):"",
        path:"/pages/consultant/consuList?id="+this.id
			}
   
  }
}
</script>

 
<style lang="scss" scoped>
.consulist {
  padding-bottom: 100upx;
  view{
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }
  .flex-row{
      display: flex;
      flex-direction: row;
    }
	.top_nav{
		position: fixed;
		// #ifdef H5
		top: 44px;
		// #endif
		// #ifndef H5
		top: 0;
		// #endif
		z-index: 990;
		width: 100%;
    padding: 0 48upx;
    // left: 48upx;
    // right: 48upx;
		height: 90rpx;
		background-color: #fff;
		align-items: center;
		justify-content: space-between;
		.nav_item{
			flex: 1;
			height: 84rpx;
			margin:0 20rpx;
			line-height: 84rpx;
			border-bottom: 4rpx solid #fff;
			text-align: center;
      white-space: nowrap;
			transition: 0.26s;
			&.active{
				color: $uni-color-primary;
				border-color: $uni-color-primary;
			}
		}
	}
.tab-list{
  padding: 0 48rpx;
  justify-content: space-between;
  // position: sticky;
  position: fixed;
  // top: var(--window-top);
  width: 100%;
  background-color: #fff;
  z-index: 2;
  .tab-item{
    flex: 1;
    padding: 24rpx;
    text-align: center;
    position: relative;
    &.active{
      color: $uni-color-primary;
      &::after{
        content: "";
        height: 8rpx;
        border-radius: 4rpx;
        background-color: $uni-color-primary;
        position: absolute;
        bottom: 0;
        width: 48rpx;
        left: 0;
        right: 0;
        margin: auto
      }
    }
  }
}
.container{
  padding: 100upx 48upx 48rpx;
  background: #fff;
}
// 置业顾问列表
.advier-list {
  // padding: 150upx 48upx 0;
  // background-color: #fff;
  .adviser-item {
    justify-content: space-between;
    align-items: flex-start;
    padding: 30rpx 0;
    .header_img {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 15rpx;
      overflow: hidden;
      background-color: #f3f3f3;
    }
    image {
      width: 100%;
    }
    .info {
      flex: 1;
      overflow: hidden;
      .name {
        display: flex;
        align-items: center;
        margin-bottom: 6rpx;
        .text {
          // flex: 1;
          font-size: 32rpx;
        }
      }
      .mgl-20 {
        margin-left: 20rpx;
      }
      .data {
        display: inline-block;
        margin-bottom: 6rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 22rpx;
        color: #999;
      }
    }

    .adviser-right {
      align-items: flex-end;
    }

    .btn-list {
      align-items: center;
      text {
        color: #999;
      }
      .btn {
        width: 64rpx;
        height: 64rpx;
        ~ .btn {
          margin-left: 30rpx;
        }
        .icon-box {
          width: 64rpx;
          height: 64rpx;
          justify-content: center;
          text-align: center;
          border-radius: 50%;
          background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
        }
        .icon {
          width: 64rpx;
          height: 64rpx;
        }
      }
    }
  }
}
.comment_btn{
    padding: 24rpx 0;
    margin: 0 auto;
    width: calc(100% - 96upx);
    justify-content: center;
    color: $uni-color-primary;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
    >text{
      margin-left: 16rpx;
    }
}

// 底部菜单
.bottom-bar {
    background-color: #fff;
    height: 110rpx;
    padding: 15rpx 48rpx;
    left: 0;
    z-index: 10;
    .bar-left{
        padding-right: 10rpx;
        justify-content: flex-start;
    }
    .icon-btn {
        // width: 100rpx;
        align-items: center;
        padding: 0;
        margin: 0;
        background-color: #fff;
        line-height: initial;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        // flex: 1;
        padding-right: 32rpx;
        overflow: hidden;
        position: relative;
        // & ~ .icon-btn {
        //   margin-left: 24rpx;
        // }
        .header_img{
            width: 50rpx;
            height: 50rpx;
            border-radius: 50%;
        }
        text {
            line-height: 1;
            font-size: 22rpx;
            color: #999;
            display: inline-block;
            width: 100%;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .badge{
          display: inline-block;
          box-sizing: border-box;
          width: auto;
          position: absolute;
          top: 0;
          left: 32rpx;
          // right: 38rpx;
          height: 28rpx;
          padding: 0 8rpx;
          min-width: 28rpx;
          border-radius: 14rpx;
          font-size: 22rpx;
          background-color: $uni-color-primary;
          color: #fff;
        }
    }
    .bar-btn {
        // width: 220rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        font-size: 30rpx;
        padding: 0;
        margin: 0;
        border-radius: 0;
        color: #fff;
        &.btn1 {
            background: #FBAC65;
            box-shadow: 0 0 4px 0 rgba(0,0,0,0.05);
            border-top-left-radius: 40rpx;
            border-bottom-left-radius: 40rpx;
        }
        &.btn2 {
            background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
            box-shadow: 0 0 4px 0 rgba(255,80,0,0.30);
            border-top-right-radius: 40rpx;
            border-bottom-right-radius: 40rpx;
        }
    }
}
}
</style>
