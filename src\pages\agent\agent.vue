<template>
  <view :class="{padb:( user_is_agent == 0&&is_show_apply_agent==1)}" >
    <view class="header-box" :style="{ backgroundColor: theme_color }">
      <image
        @click="toActivity"
        class="bg_img"
        mode="widthFix"
        :src="theme_img | imageFilter('w_8601')"
      ></image>
      <!-- 搜索栏 -->
      <view class="search-box flex-row">
        <my-icon type="ic_sousuo" color="#fff" size="40rpx"></my-icon>
        <input
          class="inp"
          type="search"
          v-model="params.keywords"
          @confirm="handleSearch"
          :placeholder="placeholder"
          placeholder-style="font-size:26rpx;color:#fff"
        />
      </view>
      <!-- 前三名 -->
      <view class="adviser-card flex-row">
        <view class="adviser-item" @click="goDetail(top_three[1].id)">
          <view class="header-img">
            <view class="img-box">
              <image mode="widthFix" class="avatar" :src="(top_three[1].img ||top_three[1].image||top_three[1].logo)| imageFilter('w_120')"></image>
            </view>
            <image
              mode="widthFix"
              class="brand"
              src="https://images.tengfangyun.com/images/new_icon/No2.png"
            ></image>
          </view>
          <text class="cname">{{ top_three[1].cname||top_three[1].name }}</text>
          <!-- <text class="activity" v-if ="params.type==1">总活跃度：{{ top_three[1].active_num }}</text> -->
        </view>
        <view class="adviser-item first" @click="goDetail(top_three[0].id)">
          <view class="header-img">
            <view class="img-box">
              <image mode="widthFix" class="avatar" :src="(top_three[0].img ||top_three[0].image||top_three[0].logo) | imageFilter('w_120')"></image>
            </view>
            <image
              mode="widthFix"
              class="brand"
              src="https://images.tengfangyun.com/images/new_icon/No1.png"
            ></image>
          </view>
          <text class="cname">{{ top_three[0].cname||top_three[0].name }}</text>
          <!-- <text class="activity" v-if ="params.type==1">总活跃度：{{ top_three[0].active_num }}</text> -->
        </view>
        <view class="adviser-item" @click="goDetail(top_three[2].id)">
          <view class="header-img">
            <view class="img-box">
              <image mode="widthFix" class="avatar" :src="(top_three[2].img||top_three[2].image||top_three[2].logo) | imageFilter('w_120')"></image>
            </view>
            <image
              mode="widthFix"
              class="brand"
              src="https://images.tengfangyun.com/images/new_icon/No3.png"
            ></image>
          </view>
          <text class="cname">{{ top_three[2].cname||top_three[2].name }}</text>
          <!-- <text class="activity" v-if ="params.type==1">总活跃度：{{ top_three[2].active_num }}</text> -->
        </view>
      </view>
    </view>
    <!-- 列表的分类 -->
    <view class="cate-box">
      <view class="cate-list flex-row">
        <view
          class="cate-item"
          :class="{ active: params.type === cate.type }"
          :style="{ backgroundColor: params.type === cate.type ? theme_color : '' }"
          v-for="cate in cate_list"
          :key="cate.type"
          @click="switchCate(cate.type)"
          >{{ cate.name }}</view
        >
      </view>
    </view>
    <!-- 经纪人列表 -->
    <view class="advier-list">
      <view
        class="adviser-item flex-row bottom-line"
        v-for="(item, index) in agent_list"
        :key="item.id"
        @click="goDetail(item.id)"
      >
        <view class="adviser_index">{{ index | indexFormat }}</view>
        <view class="header_img">
          <image mode="widthFix" :src="(item.img||item.image||item.logo)| imageFilter('w_120')"></image>
        </view>
        <view class="info">
          <view class="name flex-row">
            <text class="text">{{ item.cname ||item.name || '经纪人' }}</text>
          </view>
          <!-- <view class="data flex-row" v-if ="params.type==1">
            <text>活跃度 {{ item.active_num }}</text>
          </view> -->
          <!-- <view class="data flex-row" v-if="params.type==1">
            <text> {{ item.tname }}</text>
          </view> -->
          <view class="data">
            <block v-if="params.type==1">
              <text v-if='item.tname'>{{ item.tname }}</text>
              <text v-else >二手房 {{ item.house_count ||0 }}套</text>
            </block>
            <block v-if="params.type==2">
              <text>经纪人 {{ item.agent_num||0 }}人</text>
              <!-- <text class="mgl-20">房源 {{ item.house_count ||item.twice||0 }}套</text> -->
            </block>
            <block v-if="params.type==3">
              <text>经纪人 {{ item.agent_num ||0 }}人</text>
              <text class="mgl-20">门店 {{ item.store_num||0 }}个</text>
            </block>
            <!-- <text v-if='params.type==1||params.type==2'>二手房 {{ item.house_count ||item.twice||0 }}</text>
            <text v-if='params.type==3'>经纪人 {{ item.house_count ||item.twice||0 }}</text> -->
            <!-- <text class="mgl-20">出租房 {{ item.renting_count||item.rent||0 }}</text> -->
          </view>
        </view>
        <view class="adviser-right">
          <view class="btn-list flex-row">
            <view class="btn" v-if ="params.type==1">
              <chat-btn
                :user_login_status="login_status"
                :user_id="item.id"
                :identity_id="item.id"
                :from_type="4"
                @ok="advAsk"
              >
                <view class="icon-box" :style="{ background: filterColor(theme_color, 0.12) }">
                  <my-icon type="ic_zixun1" size="45rpx" :color="theme_color || '#ff656c'"></my-icon>
                </view>
                <!-- <image class="icon" src="https://images.tengfangyun.com/images/icon/ic_zixun.png"></image> -->
              </chat-btn>
            </view>
            <view class="btn">
              <tel-btn :user_id="item.id" :identity_id="item.id" :tel="item.tel" @ok="handleTel">
                <view class="icon-box" :style="{ background: filterColor(theme_color, 0.12) }">
                  <my-icon type="ic_dianhua1" size="45rpx" :color="theme_color || '#ff656c'"></my-icon>
                </view>
                <!-- <image class="icon" src="https://images.tengfangyun.com/images/icon/ic_dianhua.png"></image> -->
              </tel-btn>
            </view>
          </view>
          <view v-if="item.star!==undefined" class="zan flex-row" @click.prevent.stop="handleZan(item.id, index)">
            <my-icon type="ic_zan" :color="item.isstar ? theme_color : '#d8d8d8'" size="28rpx"></my-icon>
            <text class="zan_num" :style="{ color: item.isstar ? theme_color : '#d8d8d8' }">{{
              item.star
            }}</text>
          </view>
        </view>
      </view>
      <!-- TODO 小程序 app要判断审核模式 -->
      <view class="join" v-if="user_is_agent === 0&&is_show_apply_agent==1">
        <view
          class="join-btn"
          :style="{
            background: theme_color,
            boxShadow: '0 8rpx 32rpx 0 ' + filterColor(theme_color, 0.4) + ''
          }"
         
          @click="joinAgent"
          >立即入驻</view
        >
      </view>
      
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <!-- #ifndef MP-WEIXIN -->
    <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip"></login-popup>
    <!-- #endif -->
    <chat-tip></chat-tip>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import {wxShare} from '../../common/mixin'
import myIcon from '../../components/myIcon'
import { uniLoadMore } from '@dcloudio/uni-ui'
// #ifndef MP-WEIXIN
import loginPopup from '../../components/loginPopup'
// #endif
import { formatImg } from '../../common/index.js'
import chatBtn from '../../components/open-button/chatBtn'
import telBtn from '../../components/open-button/telBtn'
import allTel from '../../common/all_tel.js'
import getChatInfo from '../../common/get_chat_info.js'
const top_three = [
  {
    id: null,
    cname: '',
    img: '',
    tname: ''
  },
  {
    id: null,
    cname: '',
    img: '',
    tname: ''
  },
  {
    id: null,
    cname: '',
    img: '',
    tname: ''
  }
]

export default {
  components: {
    myIcon,
    uniLoadMore,
    // #ifndef MP-WEIXIN
    loginPopup,
    // #endif
    chatBtn,
    telBtn
  },
  data() {
    return {
      themes: [
        {
          img: 'https://images.tengfangyun.com/images/new_icon/adviser_list_bg1.png',
          color: 'rgba(251,101,106,1)'
        },
        {
          img: 'https://images.tengfangyun.com/images/new_icon/adviser_list_bg2.png',
          color: 'rgba(0,202,167,1)'
        },
        {
          img: 'https://images.tengfangyun.com/images/new_icon/adviser_list_bg3.png',
          color: 'rgba(76,199,246,1)'
        }
      ],
      cate_list: [
        {
          name: '活跃榜',
          type: 1
        },
        {
          name: '优选门店',
          type: 2
        },
        {
          name: '实力榜',
          type: 3
        }
      ],
      params: {
        page: 1,
        rows: 10,
        type: 1,
        keywords: '',
        store_id:0
      },
      get_status: '',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      },
      top_three: top_three, //前三名经纪人
      agent_list: [], //经纪人列表
      login_tip: '',
      // placeholder:''
      tel_res: {},
      user_is_agent:1,
      show_tel_pop:false,
      is_show_apply_agent:1
    }
  },
  computed: {
    agent_list_style() {
      if (this.$store.state.agentStyles && this.$store.state.agentStyles.toptitle) {
        uni.setNavigationBarTitle({
          title: this.$store.state.agentStyles.toptitle
        })
      } else {
        uni.setNavigationBarTitle({
          title: '经纪人排行榜'
        })
      }
      return this.$store.state.agentStyles
    },
    placeholder(){
      if (this.params.type==1){
        return "请输入需要查询的经纪人"
      }
       if (this.params.type==2){
        return "请输入需要查询的门店"
      }
       if (this.params.type==3){
        return "请输入需要查询的公司"
      }
    },
    theme_color(){
      if(this.$store.state.agentStyles&&this.$store.state.agentStyles.topcolor){
        return this.$store.state.agentStyles.topcolor
      }else{
        return this.themes[this.params.type-1].color
      }
    },
    theme_img(){
      if(this.$store.state.agentStyles&&this.$store.state.agentStyles.topimage){
        return this.$store.state.agentStyles.topimage
      }else{
        return this.themes[this.params.type-1].img
      }
    },
    is_open_im() {
      return this.$store.state.im.ischat
    },
    is_open_middle_num() {
      return this.$store.state.im.istelcall
    },
    login_status() {
      return this.$store.state.user_login_status
    }
  },
  onLoad(option) {
    if (option.id) {
      this.params.store_id=option.id
    }
    uni.showLoading({
      title: '加载中...',
      mask: true
    })
    this.getData()
  },
  filters: {
    indexFormat(index) {
      return index + 4 < 10 ? '0' + (index + 4) : index + 4
    },
    formatActive(data, cate_type) {
      if (!data) {
        return 0
      }
      if (cate_type === 1 && data.rank && parseInt(data.rank)) {
        return parseInt(data.active) + parseInt(data.rank)
      }
      return data.active
    }
  },
  methods: {
    filterColor(color, opacity) {
      if (!color) {
        return 'rgba(255,101,107,' + opacity + ')'
      }
      return color.replace(/1\)/, opacity + ')')
    },
    // 获取经纪人列表数据
    getData() {
      this.get_status = 'loading'
      if (this.params.page === 1) {
        this.top_three = top_three
        this.agent_list = []
      }
      let url =""
      if(this.params.type==1){
        url='agent/agentMemberList'
      }else if (this.params.type==2){
        url ="agentCompany/agentStoreList"
      }else if (this.params.type==3){
        url ="agentCompany/companyList.html"
      }
      this.$ajax.get(url, this.params, res => {
        uni.hideLoading()
        this.$store.state.user_login_status = res.data.status
        if (res.data.share) {
          this.share = res.data.share
          this.getWxConfig()
        }else{
          if(this.params.type==1){
            this.share={
              title:'经纪人排行榜',
              content:'经纪人排行榜',
              pic:''
            }
          }else if (this.params.type==2){
            this.share={
              title:'门店排行榜',
              content:'门店排行榜',
              pic:''
            }
          }else if (this.params.type==3){
            this.share={
              title:'公司排行榜',
              content:'公司排行榜',
              pic:''
            }
            
          }
          this.getWxConfig()
        }
        if (res.data.code === 1) {
          if(this.params.type ==1){
            this.is_show_apply_agent = res.data.customSetting?res.data.customSetting.is_show_apply_agent:1
          }
          if (res.data.list.length < this.params.rows) {
            this.get_status = 'noMore'
          } else {
            this.get_status = 'more'
          }

          if (this.params.page === 1 && res.data.list.length > 0) {
            this.top_three = res.data.list.splice(0, 3)
            if (this.top_three.length === 0) this.top_three = top_three
            if (this.top_three.length === 1) this.top_three.push(top_three[1])&&this.top_three.push(top_three[2])
            if (this.top_three.length === 2) this.top_three.push(top_three[2])
          }
          this.agent_list = this.agent_list.concat(res.data.list)
          this.user_is_agent = res.data.is_agent
        } else {
          this.get_status = 'noMore'
          this.params.page >= 1 && this.params.page-- //默认第一页为 0
        }
        uni.stopPullDownRefresh()
      })
    },
    handleSearch() {
      this.params.page = 1
      this.getData()
    },
    checkLogin(tip, callback) {
      this.$ajax.get('member/checkUserStatus', {}, res => {
        if (res.data.code === 1) {
          callback&&callback()
        }else if(res.data.status == 1){
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
        }else if(res.data.status == 2){
          this.$store.state.user_login_status = res.data.status
          this.login_tip = tip
          this.$refs.login_popup.showPopup()
        }
      })
    },
    // 和经纪人发起聊天
    advAsk(e) {
      if (this.is_open_im == 0) {
        this.$navigateTo('/pages/agent/detail?id=' + e.user_id)
        return
      }
      // #ifdef MP-WEIXIN
      getChatInfo(e.user_id, 8)
      //  #endif
      // #ifndef MP-WEIXIN
      this.checkLogin('为方便您及时接收消息通知，请输入手机号码', ()=>{
        getChatInfo(e.user_id, 8)
      })
      //  #endif
    },
    // 切换经纪人排名分类
    switchCate(cate_type) {
      this.params.type = cate_type
      this.params.page = 1
      this.getData()
    },
    // 点赞
    handleZan(id, index) {
      this.$ajax.get(
        'adviser/adv_addstar',
        {
          id: id
        },
        res => {
          if (res.data.code == 1) {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            if (this.list[index].isstar != 0) {
              this.list[index].isstar = 0
              this.list[index].star = res.data.NowStar
            } else {
              this.list[index].isstar = 9999
              this.list[index].star = res.data.NowStar
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
          }
        }
      )
    },
    // 拨打经纪人电话
    handleTel(e) {
      
      if (this.is_open_middle_num == 0||this.params.type!=1) {
        // #ifndef MP-WEIXIN
            this.$ajax.get('member/checkUserStatus', {}, res => {
                if (res.data.code === 1) {
                    uni.makePhoneCall({
                    phoneNumber: e.tel
                  })
                  this.$ajax.get(
                      'im/callUpStatistics',
                      {
                        id: e.user_id,
                        tel: parseInt(e.tel),
                        type: 3
                      },
                      res => {}
                    )
                } else {
                this.$store.state.user_login_status = res.data.status
                this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
                this.$refs.login_popup.showPopup()
                return
                }
            })
            // #endif

            
        
        return
      }
      this.tel_params = {
        type: 3,
        callee_id:e.user_id,
        scene_type:3,
        scene_id:e.user_id,
        source: 'agent_list',
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      // #ifdef MP-WEIXIN
      // 在模板处已经判断过是否登录和绑定手机号了
      allTel(this.tel_params)
      // #endif
      // #ifndef MP-WEIXIN
      this.tel_params.intercept_login = true
      this.tel_params.fail = (res)=>{
        if(res.data.code === -1){
          this.$store.state.user_login_status = 1
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
        }
        if(res.data.code === 2){
          this.$store.state.user_login_status = 2
          this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
          this.$refs.login_popup.showPopup()
        }
      }
      allTel(this.tel_params)
      // #endif
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    // 关闭登录弹窗时
    handleCloseLogin() {
      if (this.$store.state.user_login_status === 1) {
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if (this.$store.state.user_login_status === 2) {
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    goDetail(id) {
      if (!id) return
      if(this.params.type==1){
        this.$navigateTo('/pages/agent/detail?id=' + id)
      }else if (this.params.type==2){
        this.$navigateTo('/shops/detail?id=' + id)
      }else if (this.params.type==3){
        this.$navigateTo('/shops/myCompany?id=' + id)
        //  url ="agentCompany/companyList.html"
      }
      // this.$navigateTo('/pages/agent/detail?id=' + id)
    },
    joinAgent(){
      this.$navigateTo("/user/member_upgrade")
    },
    //跳转到活动页面
    toActivity() {
      if (this.agent_list_style && this.agent_list_style.tourl_app) {
        //#ifdef MP
        this.$navigateTo(this.agent_list_style.tourl_app)
        //#endif
      } else if (this.agent_list_style && this.agent_list_style.tourl) {
        //#ifdef H5
        this.$navigateTo(this.agent_list_style.tourl)
        // location.href = obj.agent_list_style.tourl
        //#endif
      }
    }
  },
  // 触底加载
  onReachBottom() {
    this.params.page++
    this.getData()
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.params.page = 1
    this.params.searchtxt = ''
    this.getData()
  },
  onShareAppMessage() {
    if (this.agent_list_style && this.agent_list_style.toptitle) {
      return {
        title: this.agent_list_style.toptitle,
        content: this.share.content || '',
        imageUrl: this.share.pic ? formatImg(this.share.pic, 'w_6401') : ''
      }
    } else if (this.share) {
      return {
        title: this.share.title || '',
        content: this.share.content || '',
        imageUrl: this.share.pic ? formatImg(this.share.pic, 'w_6401') : ''
      }
    }
  },
  // #ifdef APP_PLUS
  onBackPress() {
    // 收起软键盘
    plus.key.hideSoftKeybord()
  }
  // #endif
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}
.padb{
  padding-bottom: 150rpx;
}
// 排行榜头部
.header-box {
  width: 100%;
  min-height: 560rpx;
  // background-size: 100%;
  // background-repeat: no-repeat;
  position: relative;
  background-color: #f78b8a;
  .bg_img {
    width: 100%;
  }
  // 搜索栏
  .search-box {
    align-items: center;
    position: absolute;
    left: 48rpx;
    right: 48rpx;
    // #ifdef H5
    // margin-top: 44px;
    // #endif
    top: 12rpx;
    padding: 6rpx 20rpx;
    border-radius: 8rpx;
    background-color: rgba($color: #ffffff, $alpha: 0.6);
    .inp {
      margin-left: 20rpx;
    }
  }

  // 前三名经纪人
  .adviser-card {
    position: absolute;
    bottom: 0;
    left: 48rpx;
    right: 48rpx;
    justify-content: space-between;
    align-items: flex-end;
    .adviser-item {
      width: 200rpx;
      height: 272rpx;
      padding: 24rpx 6rpx 30rpx 6rpx;
      background-color: #fff;
      border-top-left-radius: 24rpx;
      border-top-right-radius: 24rpx;
      align-items: center;
      overflow: hidden;
      &.first {
        height: 328rpx;
        width: 254rpx;
        margin: 0 2rpx;
        padding-top: 45rpx;
        background-image: url(https://images.tengfangyun.com/images/new_icon/bg_touxiang%403x.png);
        background-size: 196rpx;
        background-repeat: no-repeat;
        background-position: 30rpx 34rpx;
        .header-img {
          position: relative;
          width: 128rpx;
          height: 128rpx;
          margin-bottom: 45rpx;
          .brand {
            left: -36rpx;
          }
        }
        .img-box {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          background-color: #f3f3f3;
          overflow: hidden;
          .avatar {
            width: 100%;
          }
        }
      }
      .header-img {
        position: relative;
        width: 96rpx;
        height: 96rpx;
        margin-bottom: 45rpx;
        .brand {
          width: 200rpx;
          position: absolute;
          left: -52rpx;
          bottom: -50rpx;
        }
      }
      .img-box {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: #f3f3f3;
        overflow: hidden;
        .avatar {
          width: 100%;
        }
      }
      .cname {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 30rpx;
        margin-bottom: 10rpx;
        max-width: 100%;
      }
      .activity {
        font-size: 22rpx;
        color: #999;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
      }
    }
  }
}

// 排行分类
.cate-box {
  padding: 20rpx 72rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  .cate-list {
    justify-content: space-between;
    line-height: 64rpx;
    border-radius: 32rpx;
    background-color: #f3f3f3;
    .cate-item {
      padding: 0 36rpx;
      border-radius: 32rpx;
      position: relative;
      &.active {
        background-color: $uni-color-primary;
        color: #fff;
      }
      &.active::after {
        content: ' ';
        width: 0;
        height: 0;
        position: absolute;
        left: 0;
        right: 0;
        margin: auto;
        bottom: -48rpx;
        border: 20rpx solid;
        border-color: transparent transparent #ffffff transparent;
      }
    }
  }
}

// 经纪人列表
.advier-list {
  padding: 20rpx 48rpx;
  background-color: #fff;
  .adviser-item {
    justify-content: space-between;
    align-items: flex-start;
    padding: 30rpx 0;
    .adviser_index {
      margin-top: 20rpx;
      margin-right: 20rpx;
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
    }
    .header_img {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 15rpx;
      overflow: hidden;
      background-color: #f3f3f3;
    }
    image {
      width: 100%;
    }
    .info {
      flex: 1;
      overflow: hidden;
      .name {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;
        .text {
          // flex: 1;
          font-size: 32rpx;
        }
      }
      .mgl-20 {
        margin-left: 20rpx;
      }
      .data {
        display: inline-block;
        margin-bottom: 12rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 28rpx;
        color: #999;
      }
    }

    .adviser-right {
      align-items: flex-end;
      align-self: center
    }

    .btn-list {
      align-items: center;
      text {
        color: #999;
      }
      .btn {
        width: 64rpx;
        height: 64rpx;
        ~ .btn {
          margin-left: 30rpx;
        }
        .icon-box {
          width: 64rpx;
          height: 64rpx;
          justify-content: center;
          text-align: center;
          border-radius: 50%;
        }
        .icon {
          width: 64rpx;
          height: 64rpx;
        }
      }
    }
    .zan {
      align-items: center;
      margin-top: 20rpx;
      .zan_num {
        margin-left: 6rpx;
        font-size: 22rpx;
        color: #d8d8d8;
      }
    }
  }
}
.join {
  position: fixed;
  width: 100vw;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 40rpx 0;
  background: #fff;
}
.join-btn {
  width: 360rpx;
  height: 88rpx;
  margin: auto;
  text-align: center;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  border-radius: 44rpx;
  font-weight: bold;
  color: #fff;
}
</style>
