<template>
  <view>
    <cover-view>
     <!-- <cover-view class="list_title flex-row" v-if ="showtitle">
         <cover-image class= "list_title_img" :src="'/ditu/'+ item.name +'_icon.png' | imageFilter('m_80')"></cover-image>
        <cover-view >{{item.title}}</cover-view>
      </cover-view> -->
      <cover-view  class="item" >
        <cover-view class="left flex-1">
          <cover-view class="title">{{item.title}}</cover-view>
          <!-- <cover-view class="address">{{item.address}}</cover-view> -->
        </cover-view>
        <cover-view class="distance">{{item.distance | distanceFormat}}</cover-view>
      </cover-view>
    </cover-view>
  </view>
</template>

<script>
export default {
  data(){
    return  {

    }
  },
  props:{
    item:Object,
    showtitle:{
      type:[Boolean , String],
      default:false
    }
  },
  filters:{
			distanceFormat(val){
				if(!val){
					return ''
				}
				if(val*1000<1000){
					return Math.ceil(val*1000)+'m'
				}else{
					return (val).toFixed(1)+'km'
				}
			}


  }
}
</script>

<style lang ="scss" scoped>
.list_title{
	font-family: PingFangSC-Medium;
	font-size: 28rpx;
	padding: 8rpx 0;
	font-weight: 600;
	color: #333333;
	align-items: center;

	.list_title_img {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
		object-fit: cover;
	}
}
.item{
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			align-items: center;
			width: 100%;
			overflow: hidden;
			/* margin-bottom: 24rpx; */
			font-size: 24rpx;
      .left {
        padding: 12rpx 0;
      }
			.title{
				margin-bottom: 8rpx;
				flex-shrink: 0;
				font-size: 22rpx;
        font-family: PingFangSC-Medium;
        font-weight: 200;
        color: #333333;
			}
			.address{
				// margin: 0 12rpx;
				flex: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				color: #999;
			}
			.distance{
				margin-left: 12rpx;
				flex-shrink: 0;
        font-size: 22rpx;
				color: #333;
			}
		}

</style>