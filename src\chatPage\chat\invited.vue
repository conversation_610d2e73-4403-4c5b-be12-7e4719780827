<template>
<view class="page">
    <view class="header">
        <image class="bg_img" mode="aspectFill" :src="'/images/new_icon/invited_bg.png' | imageFilter('w_8601')"></image>
        <view class="title">邀请好友</view>
        <view class="tip">邀请好友，一起得好礼</view>
    </view>
    <view class="invited-card">
        <view class="agent-info flex-row">
            <view class="prelogo">
            <image :src="infoData.prelogo" mode="aspectFill"></image>
            </view>
            <view class="info">
                <view class="name flex-row">
                    <text class="user_name">{{infoData.nickname}}</text>
                    <image class="level_icon" :src="infoData.levelid | levelIcon"></image>
                    <view class="level_name">{{infoData.level_name}}</view>
                </view>
                <view class="data flex-row">
                    <text>{{infoData.build_names}}</text>
                    <!-- <text>咨询量：{{infoData.traffic_volume}}</text> -->
                </view>
            </view>
            <view class="gap left_gap"></view>
            <view class="gap right_gap"></view>
        </view>
        <view class="invited-box">
            <view class="label">专属邀请码</view>
            <view class="invited">{{invited}}</view>
             <view class="desc">
                分享邀请码给好友，成功加入<text>{{projectName}}</text>，成为<text class="name">认证置业顾问</text>。邀请有礼，每成功邀请一名加入<text class="reward">活跃度+{{active_num||''}}。</text>
            </view>
            <!-- #ifdef H5 -->
            <view class="invited-btn" @click="copyInvited(invited)">点击复制</view>
            <!-- #endif -->
            <!-- #ifdef MP -->
            <button class="invited-btn" open-type="share">立即分享</button>
            <!-- #endif -->
            <!-- #ifdef APP-PLUS -->
            <view class="invited-btn" @click="showAppShare()">立即分享</view>
            <!-- #endif -->
        </view>
    </view>

    <my-popup ref="popup" position="bottom" @handleHide="showPopup=false">
        <view class="share-box flex-box">
        <!-- #ifdef APP-PLUS -->
        <button open-type="share" class="flex-1 item" @click="appShare('WXSceneSession')">
            <image style="height:64upx;width:64upx" src="https://images.tengfangyun.com/images/icon/wechat.png"></image>
            <view class="text">分享给好友</view>
        </button>
        <view class="flex-1 item" @click="appShare('WXSenceTimeline')">
            <image style="height:64upx;width:64upx" src="https://images.tengfangyun.com/images/icon/time_line.png"></image>
            <view class="text">分享到朋友圈</view>
        </view>
        <!-- #endif -->
        </view>
    </my-popup>
</view>
</template>

<script>
import {config,formatImg} from "../../common/index.js"
import myPopup from '../../components/myPopup.vue'
import {wxShare} from '../../common/mixin'
export default {
    data() {
        return {
            invited:"",
            active_num:"",
            infoData:{},
            projectName:config.projectName,
            get_num:0,
            share:{}
        }
    },
    mixins:[wxShare],
    components: {
        myPopup
    },
    onLoad(options){
        // if(options.adviser){
        //     this.adviser = options.adviser
        //     this.getData(this.adviser)
        // }
        // this.user_id = options.user_id||null
        this.getMyInfo()
    },
    filters:{
        levelIcon(val){
            return ''
        }
    },
    methods: {
        getData(id){
            this.$ajax.get('Adviser/getInviteCode.html',{id}, res=>{
                if(res.data.code === 1){
                    this.invited = res.data.invitecode
                    this.active_num = res.data.ActNum
                    if(res.data.share){
                        if(this.invited){
                            res.data.share.link=window.location.origin+'/h5/pages/my/invited?invited='+this.invited+'&user_id='+this.infoData.id
                        }
                        this.share = res.data.share
                        this.getWxConfig()
                    }
                }else{
                    this.get_num++
                    if(this.get_num>=10){
                        uni.showToast({
                            title:res.data.msg,
                            icon:'none'
                        })
                        return
                    }
                    this.getData()
                }
            })
        },
         getMyInfo(){
            this.$ajax.get('im/contactDetails.html',{},res=>{
                if(res.data.code === 1){
                    this.infoData = res.data.member
                    if(this.infoData.adviser){
                        this.getData(this.infoData.adviser)
                    }else{
                        uni.showToast({
                            title:"您还不是置业顾问,不能邀请",
                            icon:'none'
                        })
                        setTimeout(()=>{
                            uni.navigateBack()
                        },2500)
                    }
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            })
        },
        copyInvited(content){
            // #ifdef H5
            let oInput = document.createElement('input');
            oInput.value = content;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            document.execCommand("Copy"); // 执行浏览器复制命令
            uni.showToast({
                title:"复制成功",
                icon:"none"
            })
            oInput.remove()
            // #endif
            // #ifndef H5
            uni.setClipboardData({
                data: content,
                success: function () {
                    uni.showToast({
                        title:"复制成功"
                    })
                }
            });
            // #endif
        },
        onShare(){
            if(this.infoData.id){
                this.$ajax.get('tasks/doTaskReward.html',{task_id:8},()=>{})
            }
        },
        // #ifdef APP-PLUS
        showAppShare(){
            this.showPopup = true
            this.$refs.popup.show()
        },
        appShare(type = 'WXSceneSession') {
            uni.share({
                provider: 'weixin',
                type: 0,
                title: this.share.title||'',
                scene: type,
                imageUrl: formatImg(this.share.pic,'w_6401') || '',
                summary: this.share.content||'',
                href: config.apiDomain + '/h5/user/my/invited?invited='+this.invited+'&user_id='+this.infoData.id,
                success: (res)=> {
                    // console.log("success:" + JSON.stringify(res));
                    uni.showToast({
                        title: '分享成功',
                        icon: 'none'
                    })
                },
                fail: function (err) {
                    uni.showToast({
                        title: '分享失败',
                        icon: 'none'
                    })
                    // console.log("fail:" + JSON.stringify(err));
                }
            })
        },
        // #endif
    },
    onShareAppMessage(){
            return {
                path:'/user/my/invited?invited='+this.invited+'&user_id='+this.infoData.id,
                title: this.share.title || '',
                content:this.share.content || '',
                imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):''
            }
    },
    // #ifdef APP-PLUS
    onBackPress() {
        if(this.showPopup) {
            this.showPopup = false;
            this.$refs.popup.hide()
            return true;
        }
    }
    // #endif
}
</script>

<style lang="scss">
.page{
    min-height: calc(100vh - 44px);
    background-color: #ff696a;
}
view{
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}
.flex-row{
    flex-direction: row;
}

.header{
    padding: 56rpx 48rpx 80rpx 48rpx;
    color: #fff;
    position: relative;
    z-index:2;
    .bg_img{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1
    }
    .title{
        line-height: 1;
        font-size: 52rpx;
        padding-left: 24rpx;
        margin-bottom: 24rpx;
        border-left: 8rpx solid #fff;
    }
    .tip{
        padding-left: 24rpx;
    }
}

.invited-card{
    background-color: #fff;
    margin: 0 48rpx;
    padding: 0 48rpx;
    border-radius: 16rpx;
}
.agent-info{
    padding: 48rpx 0;
    border-bottom: 1rpx dashed #d8d8d8;
    margin-bottom: 24rpx;
    position: relative;
    .gap{
        width: 32rpx;
        height: 32rpx;
        border-radius: 50%;
        background-color: #ff696a;
        position: absolute;
        bottom: -16rpx;
        &.left_gap{
            left: -64rpx;
        }
        &.right_gap{
            right: -64rpx;
        }
    }
    .prelogo{
        width: 96rpx;
        min-width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
        margin-right: 24rpx;
        overflow: hidden;
        image{
            width: 100%;
            height: 100%;
        }
    }
    .info{
        line-height: 1;
        .name{
            align-items: center;
            margin-bottom: 24rpx;
        }
        .user_name{
            font-size: 36rpx;
        }
        .level_icon{
            width: 32rpx;
            height: 32rpx;
        }
        .level_name{
            font-size: 22rpx;
            color: #999;
        }
        .data{
            font-size: 22rpx;
            color: #40465D;
            >text{
                margin-right: 24rpx;
            }
        }
    }
}
.invited-box{
    padding: 24rpx 0;
    padding-bottom: 48rpx;
    .label{
        margin-bottom: 24rpx;
        text-align: center;
        color: #999;
    }
    .invited{
        padding: 48rpx;
        letter-spacing: 6rpx;
        font-size: 52rpx;
        text-align: center;
        border-radius: 8rpx;
        background-color: #f5f5f5;
        color: #ff696a;
        font-weight: bold;
    }
    .desc{
        display: block;
        margin: 50upx 0;
        font-size: 28upx;
        line-height: 1.6;
        .name{
            color: #ff656a
        }
        .reward{
            color: #ff656a;
        }
    }
    .invited-btn{
        line-height: 64rpx;
        border-radius: 32rpx;
        padding: 0 32rpx;
        width: 180rpx;
        margin: 0 auto;
        text-align: center;
        font-size: 28rpx;
        color: #fff;
        background-color: $uni-color-primary;
        box-shadow: 0 4px 12px 0 rgba(251,101,106,0.40);
    }
}


.btn-box{
    .btn{
        width: 70%;
        background-color: #00cc33;
        font-size: 40upx;
        height: 82upx;
        line-height: 82upx;
        color: #fff;
    }
}
.share-box {
    padding: 20upx 0;
    background-color: #fff;

    button {
      line-height: initial;
      padding: 10upx 20upx;
      background-color: #fff;
    }

    .item {
      text-align: center;
      padding: 10upx 20upx;
      line-height: inherit;
    }
}
</style>
