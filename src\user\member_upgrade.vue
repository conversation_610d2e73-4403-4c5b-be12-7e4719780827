<template>
<view id="upgrade">
    <view class="header">
        <image class="bg" mode="aspectFill" :src="'/images/new_icon/vip_bg.png' | imageFilter('m_320')"></image>
        <view class="user-info flex-row">
            <image class="left" :src="userInfo.prelogo | imageFilter('w_80')"></image>
            <view class="right flex-1" v-if="userInfo.cname">
                <view class="user_name">{{userInfo.cname}}</view>
                <view class="level_name" :class="{is_vip: userInfo.is_vip||(userInfo.levelid>1&&!userInfo.vip_invalid)}">
                    <view class="flex-row">
                        <image class="level_icon" v-if="userInfo.is_vip||(userInfo.levelid>1&&!userInfo.vip_invalid)" :src="'/images/new_icon/<EMAIL>' | imageFilter('m_80')"></image>
                        <image class="level_icon" v-else :src="'/images/new_icon/<EMAIL>' | imageFilter('m_80')"></image>
                        <text>{{userInfo.level_name||'暂未开启开通会员'}}</text>
                    </view>
                </view>
            </view>
            <view class="tologin" v-else @click="toLogin()">点击登录</view>
        </view>
        <view class="vip_type_list flex-row">
            <view class="vip_type" :class="{active:params.group_id==item.value}" @click="switchTab(item,index)" v-for="(item, index) in tabs" :key="index">{{item.name}}</view>
        </view>
    </view>
    <view class="vip-box">
        <view class="footer flex-row">
            <text class="label">{{showList.levelname}}权益：</text>
            <template v-if="showList.perday_maxpost">
                <text>每天可发布</text>
                <text class="value">{{showList.perday_maxpost}}</text>
                <text>条信息；</text>
            </template>
            <template v-if="showList.perday_maxrefresh">
                <text>每天可刷新</text>
                <text class="value">{{showList.perday_maxrefresh}}</text>
                <text>条信息；</text>
            </template>
            <template v-if="showList.total_maxpost">
                <text>总共可发布</text>
                <text class="value">{{showList.total_maxpost}}</text>
                <text>条信息；</text>
            </template>
            <template v-if="showList.fixed_refresh&&!is_personal">
                <text>可设置精选信息条数：</text>
                <text class="value">{{showList.fixed_refresh}}</text>
                <text>条；</text>
            </template>
            <template v-if="showList.fixed_refresh&&is_personal">
                <text>每周赠送设置精选信息次数：</text>
                <text class="value">{{showList.fixed_refresh}}</text>
                <text>次；</text>
            </template>
        </view>
        <view class="footer" v-if="is_personal">{{showList.level_desc||'升级后可免费查看房源电话'}}</view>
        <view class="vip_tip" v-if="userInfo.levelid>1&&!userInfo.vip_invalid">您现在是{{userInfo.level_name}}，将在{{userInfo.levelup_time}}，您可以选择续费</view>
        <view class="vip_tip" v-if="userInfo.levelid>1&&userInfo.vip_invalid">您的{{userInfo.level_name}}已过期，您可以选择续费</view>
        <view class="label">请选择续费时长</view>
        <view class="vip-list flex-row">
            <view class="vip-item" :class="{isselect:params.upgrade_id==item.upgrade_id}" v-for="(item,index) in showList.types" :key="index" @click="handleSelect(index)">
                <text class="name">{{item.name}}</text>
                <template v-if ="is_personal&&tabIndex==0">
                    <view class="price line_through flex-row">
                        <text>原价{{showList.personal_vip_yuanjia}}元</text>
                        <!-- <text class="unit">￥</text>
                        <text class="value">{{item.money}}</text> -->
                    </view>
                    <view class="price price_bot flex-row">
                        <text class="unit">￥</text>
                        <text class="value">{{item.money}}</text>
                    </view>
                </template>
                <template v-else >
                    <view class="price flex-row">
                        <text class="unit">￥</text>
                        <text class="value">{{item.money}}</text>
                    </view>
                    <!-- <view class="tip" v-if="item.is_give">
                        <text>赠送{{item.give_num}}{{item.is_give===1?'金币':(item.is_give===2?'积分':'')}}</text>
                    </view> -->
                    <view class="tip">
                        <text>平均每天{{(item.money/item.days).toFixed(2)}}</text>
                    </view>
                </template>
            </view>
            <view class="vip-item placeholder"></view>
        </view>
        <view class="gave-box" v-if="current_meal.upgrade_id&&current_meal.key_total">
            <view class="label">套餐赠送</view>
            <view class="gave_list flex-row" :class="{row_line:current_meal.key_total>2}">
                <view class="gave_item" v-if="current_meal.integral">
                    <view class="icon">
                        <my-icon type="jifen" color="#f6dcb6" size="40rpx"></my-icon>
                    </view>
                    <view>
                        <view class="gave_name">赠送【积分】</view>
                        <view class="flex-row data">
                            <text class="value">{{current_meal.integral}}</text>
                            <text>积分</text>
                        </view>
                    </view>
                </view>
                <view class="gave_item" v-if="current_meal.corn">
                    <view class="icon">
                        <my-icon type="jinbi1" color="#f6dcb6" size="40rpx"></my-icon>
                    </view>
                    <view>
                        <view class="gave_name">赠送【金币】</view>
                        <view class="flex-row data">
                            <text class="value">{{current_meal.corn}}</text>
                            <text>金币</text>
                        </view>
                    </view>
                </view>
                <view class="gave_item" v-if="current_meal.info_selected">
                    <view class="icon">
                        <my-icon type="jingxuan" color="#f6dcb6" size="40rpx"></my-icon>
                    </view>
                    <view>
                        <view class="gave_name">赠送【精选】</view>
                        <view class="flex-row data">
                            <text class="value">{{current_meal.info_selected}}</text>
                            <text>次</text>
                        </view>
                    </view>
                </view>
                <view class="gave_item" v-if="current_meal.info_top">
                    <view class="icon">
                        <my-icon type="zhiding" color="#f6dcb6" size="40rpx"></my-icon>
                    </view>
                    <view>
                        <view class="gave_name">赠送【置顶】</view>
                        <view class="flex-row data">
                            <text class="value">{{current_meal.info_top}}</text>
                            <text>次</text>
                        </view>
                    </view>
                </view>
                <view class="gave_item seat"></view>
            </view>
        </view>
        <view class="pay_type">
			<view class="title">使用金币或余额</view>
            <checkbox-group @change="onCheckChange">
                <label class="pay_item flex-box" v-if="userInfo.money_own&&pay_by_corn!==0">
                    <view class="pay_name flex-row">
                        <my-icon type="jinbi" color="#f9cf5e" size="56rpx"></my-icon>
                        <text class="text">使用金币</text>
                        <text class="tip">(剩余{{userInfo.money_own}})</text>
                    </view>
                    <checkbox value="is_corn" :checked="params.is_corn==1" color="#FB656A"></checkbox>
                </label>
                <label class="pay_item flex-box" v-if="userInfo.shequ_moeny">
                    <view class="pay_name flex-row">
                        <my-icon type="yue" color="#ff656b" size="56rpx"></my-icon>
                        <text class="text">使用余额</text>
                        <text class="tip">(剩余{{userInfo.shequ_moeny}})</text>
                    </view>
                    <checkbox value="is_balance" :checked="params.is_balance==1" color="#FB656A"></checkbox>
                </label>
            </checkbox-group>
            <view class="pay_ment flex-row">
                <text>还需支付</text>
                <text>{{pay_amount}}元</text>
            </view>
		</view>
        <my-popup ref="upgrade_success" position="center" height="880rpx">
            <view class="success-box">
                <view class="success-header">
                    <image mode="widthFix" :src="'/images/new_icon/tip_xing.png' | imageFilter('m_6401')"></image>
                </view>
                <view class="success-info">
                    <text class="title">恭喜您</text>
                    <text class="sub_title">{{"已升级为"+showList.levelname}}</text>
                    <view class="rights-title-box flex-row">
                        <view class="line"></view>
                        <view class="rights-title">会员权益</view>
                        <view class="line"></view>
                    </view>
                    <view class="rights-list flex-row">
                        <view class="rights-item">
                            <view class="icon_box">
                                <my-icon type="ic_shuaxin_" color="#666"></my-icon>
                            </view>
                            <text>每天可刷新{{showList.perday_maxrefresh}}条</text>
                        </view>
                        <view class="rights-item">
                            <view class="icon_box">
                                <my-icon type="jiahao" color="#666"></my-icon>
                            </view>
                            <text>每天可发布{{showList.perday_maxpost}}条</text>
                        </view>
                    </view>
                </view>
                <view class="btn-box">
                <view class="btn" @click="$refs.upgrade_success.hide()">确定</view>
                </view>
                <view class="close-icon-box" @click="$refs.upgrade_success.hide()">
                    <my-icon type="guanbi" color="#fff" size="50rpx"></my-icon>
                </view>
            </view>
        </my-popup>
        <view class="button" @click="handlePay">{{pay_amount>0?'去支付':'完成'}}</view>
    </view>
</view>
</template>

<script>
import myIcon from '../components/myIcon'
import myPopup from '../components/myPopup'
import {showModal} from "../common/index.js"
export default {
    components:{
        myIcon,
        myPopup
    },
    data(){
        return{
            userInfo:{
                prelogo:"/images/new_icon/defalut_avatar.png",
                mamber:"",
                time:""
            },
            params:{
                group_id:"",
                upgrade_id:"",
                is_corn:"",
                is_balance:""
            },
            pay_by_corn: 0,
            is_personal: null,
            pay_type:null,
            totalPrice:"",
            jinbi:100,
            yue:20,
            tabs:[],
            list:[],
            tabIndex:0,
            showList:{
                types:[],
                levelname:''
            }
        }
    },
    onLoad(options){
        this.is_personal = options.is_personal||null
        // #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
        uni.hideShareMenu()
        // #endif
        if(this.is_personal){
            this.getPersonalItems()
        }else{
            this.getVipItems()
        }
        this.getWxConfig(
            ['chooseWXPay', 'hideOptionMenu'],
            wx => {
            console.log('执行回调')
            this.wx = wx
            },
        )
        uni.$on('getDataAgain',()=>{
            if(this.is_personal){
                this.getPersonalItems()
            }else{
                this.getVipItems()
            }
        })
    },
    computed:{
        pay_amount(){
           if(!this.totalPrice){
                return 0
            }
            let pay_amount = parseFloat(this.totalPrice)
            if(this.params.is_balance){
                pay_amount-=this.userInfo.shequ_moeny
            }
            if(this.params.is_corn){
                pay_amount-=this.userInfo.money_own
            }
            if(pay_amount>0){
                return pay_amount.toFixed(2)
            }else{
                return 0
            } 
        },
        current_meal(){
            let current = this.showList.types.find(item=>item.upgrade_id == this.params.upgrade_id)
            if(current){
                let key_list = ['corn', 'integral', 'info_selected', 'info_top']
                let key_total = 0
                for(let key in current){
                    if(key_list.includes(key)&&current[key]){
                        key_total++
                    }
                }
                current.key_total = key_total
                return current
            }else{
                return {}
            }
        }
    },
    filters:{
        format(val){
            if(!val){
                return ""
            }
            if(val.toFixed(2)>0){
                return '平均每天'+val.toFixed(2)+'元'
            }else{
                return ""
            }
        }
    },
    methods:{
        getVipItems(){
            this.$ajax.get('upgrade/agentUpgradeList.html',{},res=>{
                // console.log(res.data)
                if(res.data.user){
                    this.userInfo = res.data.user
                }
                if(res.data.code == 1){
                    this.tabs = res.data.list.map((item)=>{
                        return {name:item.levelname,value:item.id}
                    })
                    this.list = res.data.list
                    let index = this.list.findIndex(item=>item.id === res.data.user.levelid)
                    if(index>-1){
                        this.showList = this.list[index]
                    }else{
                        this.showList = this.list[0]
                    }
                    this.params.group_id = this.showList.id
                    this.handleSelect(0)
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                    setTimeout(()=>{
                        this.$navigateBack()
                    }, 1500)
                }
            })
        },
        // 获取个人vip升级选项
        getPersonalItems(){
            this.$ajax.get('upgrade/personalUpgrade.html',{},res=>{
                if(res.data.user){
                    this.userInfo = res.data.user
                }
                if(res.data.code == 1){
                    // this.tabs = res.data.list.map((item)=>{
                    //     return {name:item.levelname,value:item.id}
                    // })
                    res.data.list.name = res.data.list.levelname
                    res.data.list.value = res.data.list.id
                    // res.data.list[1].name = "升级经纪人"
                    // res.data.list[1].value =0
                    // this.tabs[0] ={
                    //     name:res.data.list.levelname,
                    //     value:res.data.list.id
                    // }
                    // this.tabs[1]={
                    //     name:'成为经纪人',
                    //     value :0
                    // }
                    this.tabs = [res.data.list]
                    this.list = [res.data.list]
                    // if(this.list.length>0){
                    //     this.showList = this.list[0]
                        
                    //     this.params.group_id = this.showList.id
                    //     this.handleSelect(0)
                    // }
                    this.getAgentItems()
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            })
        },
        getAgentItems(){
            this.$ajax.get('upgrade/agentUpgradeList.html',{},res=>{
                // console.log(res.data)
                if(res.data.user){
                    this.userInfo = res.data.user
                }
                if(res.data.code == 1){
                    this.tabs = this.tabs.concat(res.data.list.map((item)=>{
                        return {name:item.levelname,value:item.id}
                    })) 
                    this.list =this.list.concat(res.data.list) 
                    let index = this.list.findIndex(item=>item.id === res.data.user.levelid)
                    if(index>-1){
                        this.showList = this.list[index]
                    }else{
                        this.showList = this.list[0]
                    }
                    this.params.group_id = this.showList.id
                    this.handleSelect(0)
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                    setTimeout(()=>{
                        this.$navigateBack()
                    }, 1500)
                }
            })
        },
        pickerChange(e){
            console.log(e)
        },
        // 选择升级vip项
        handleSelect(index){
            this.params.upgrade_id = this.showList.types[index].upgrade_id
            this.totalPrice = this.showList.types[index].money
            this.pay_by_corn = this.showList.types[index].pay_by_corn
            this.params.is_corn = ""
            this.params.is_balance = ""
            // this.is_personal = this.showList.is_personal
            this.tips = this.showList.tips
        },
        // 切换vip类型
        switchTab(e,index){
            this.params.group_id = e.value
            this.params.upgrade_id = ""
            this.totalPrice = ""
            this.tabIndex =index
            this.showList = this.list[index]
            if(this.list[index].types.length>0){
                this.handleSelect(0)
            }
        },
        // 选择是否使用金币或余额
        onCheckChange(e){
            if(e.detail.value.includes("is_corn")){
                this.params.is_corn = 1
            }else{
                this.params.is_corn = ''
            }
            if(e.detail.value.includes('is_balance')){
                this.params.is_balance = 1
            }else{
                this.params.is_balance = ''
            }
        },
        toLogin(){
            uni.removeStorageSync('token')
            this.$navigateTo("/user/login/login")
        },
        handlePay(){
            if(!this.params.group_id||this.params.upgrade_id===undefined){
                uni.showToast({
                    title:"请选择会员类型或时长",
                    icon:"none"
                })
                return
            }
            if(this.is_personal===1&&!this.tabIndex){
                showModal({
                    title: '提示',
                    content: this.tips||'',
                    cancelText: '取消',
                    confirmText: '确定',
                    confirm: () => {
                        this.pay()
                    },
                    fail: () => {},
                    complete: () => {}
                });
                return
            }
            this.pay()
        },
        pay(){
            uni.showLoading({
                title:"正在处理..."
            })
            this.$ajax.get('Upgrade/upgrade.html',this.params,res=>{
                if(res.data.code !== 1){
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                    return
                }
                // 如果使用金币或余额直接支付成功则提示充值成功，否则执行调起对应的支付
                if(res.data.pay_status === 1){
                    uni.showToast({
                        title:res.data.msg
                    })
                    setTimeout(()=>{
                       this.$navigateBack()
                        
                    },2000)
                }else{
                    uni.hideLoading()
                    // 余额或金币无法完成抵扣则获取支付信息继续支付
                    this.order_id = res.data.order_id
                    this.getPayinfo()
                }
            },error=>{
                console.log(error)
                uni.hideLoading()
            })
        },
        getPayinfo(){
            this.mpWxPay(this.order_id)
        },
        /**
         * 微信小程序支付
         */
        mpWxPay(order_id){
            this.$ajax.get('Upgrade/wxpay.html',{order_id:order_id},res=>{
                if(res.data.code === 1){
                    let pay_info = res.data.data
                    this.wx.chooseWXPay({
                        // provider: 'wxpay',
                        timestamp:pay_info.timeStamp,
                        nonceStr:pay_info.nonceStr,
                        package:pay_info.package,
                        signType:pay_info.signType,
                        paySign:pay_info.paySign,
                        success: (res)=> {
                            // uni.showToast({
                            //     title:"支付成功"
                            // })
                            // setTimeout(()=>{
                            //     uni.navigateBack()
                            // },1500)
                            this.$refs.upgrade_success.show()
                        },
                        fail: function(err) {
                            console.log('支付失败：', err)
                            uni.showToast({
                                title: err.err_desc || err.errMsg,
                                icon: 'none',
                                duration: 5000
                            })
                        }
                    })
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
page{
    background-color: #fff;
}
view{
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}
.flex-row{
    flex-direction: row;
}

.header{
    position: relative;
    .bg{
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }
}

.vip_type_list{
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 0;
    .vip_type{
        flex: 1;
        padding: 20rpx;
        text-align: center;
        font-size: 26rpx;
        font-weight: bold;
        border-top-left-radius: 20rpx;
        border-top-right-radius: 20rpx;
        background-color: #4b4b50;
        opacity: 0.7;
        color: #999;
        &.active{
            background-image: linear-gradient(90deg, #E5BA72 0%, #F4DBB3 100%);
            opacity: 1;
            color: #333;
        }
    }
}

.user-info{
    align-items: center;
    padding: 48rpx 72rpx 120rpx 72rpx;
    background-color: #1E1F25;
    .left{
        width: 128rpx;
        height: 128rpx;
        border-radius: 50%;
        margin-right: 20rpx;
    }
    .right{
        display: block;
        .user_name{
            margin-bottom: 10rpx;
            line-height: 1.5;
            font-size: 32rpx;
            color: #fff;
        }
        .level_name{
            display: inline-block;
            padding: 0 20rpx;
            height: 48rpx;
            line-height: 48rpx;
            border-radius: 24rpx;
            background-color: rgba($color: #ffffff, $alpha: 0.35);
            color: #333;
            &.is_vip{
                color: #E5BA72;
            }
            .flex-row{
                align-items: center;
            }
            .level_icon{
                width: 32rpx;
                height: 32rpx;
                margin-right: 10rpx;
            }
        }
    }
    .tologin{
        font-size: 32rpx;
        font-weight: bold;
        color: #e5ba72;
        position: relative;
        z-index: 2;
    }
}


.vip-box{
    padding: 48rpx;
    .vip_tip{
        margin-top: 24rpx;
        margin-bottom: 24rpx;
        color: $uni-color-primary;
    }
    .label{
        margin: 24rpx 0;
        color: #333;
    }
    .vip-list{
        flex-wrap: wrap;
        justify-content: space-between;
    }
    .vip-item{
        width: 31.2%;
        padding: 48rpx 5rpx;
        border-radius: 24rpx;
        background-color: #f3f3f3;
        text-align: center;
        position: relative;
        margin-bottom: 24rpx;
        &.isselect{
            background-color: #fcf1e2;
            border: 4rpx solid #E5BA72;
            .tip{
                background-color: #E5BA72;
            }
        }
        &.placeholder{
            height: 0;
            border: 0;
            padding: 0;
        }
        .name{
            font-size: 30rpx;
            font-weight: bold;
            margin-bottom: 8rpx;
        }
        .price{
            justify-content: center;
            align-items: flex-end;
            margin-bottom: 15rpx;
            color: #E5BA72;
            &.line_through{
                margin-top: 20rpx;
                text-decoration: line-through;
                padding-bottom: 40rpx;
            }
            &.price_bot{
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 60rpx;
                background:#E5BA72;
                color: #fff;
                margin-bottom: 0;
                border-bottom-left-radius: 20rpx;
                border-bottom-right-radius: 20rpx;
                display: flex;
                flex-direction: row;
                align-items: center;
                .unit{
                    position: relative;
                    bottom: 0;
                    font-size: 42rpx;
                }
            }
            .unit{
                position: relative;
                bottom: 10rpx;
                font-size: 22rpx;
            }
            .value{
                font-size: 42rpx;
            }
        }
        .tip{
            white-space: nowrap;
            text-align: center;
            padding: 0 8rpx;
            height: 48rpx;
            line-height: 48rpx;
            border-radius: 24rpx;
            background-color: #eed6b5;
            color: #fff;
            text{
                font-size: 22rpx;
            }
        }
    }

    .footer{
        display: block;
        white-space: break-spaces;
        view{
            display: inline;
        }
        margin-top: 20rpx;
        color: #999999;
        .label{
            font-size: 28rpx;
            font-weight: bold;
            color: #333;
        }
        .value{
            color: #E5BA72;
        }
    }

    .pay_type{
        margin-top: 50rpx;
        .title{
            font-size: 40rpx;
            font-weight: bold;
            margin-bottom: 24rpx;
            color: #333;
        }
        .pay_item{
            justify-content: space-between;
            align-items: center;
            padding: 20rpx 0;
            .pay_name{
                .text{
                    margin-left: 20rpx;
                }
                .tip{
                    color: #999999;
                }
            }
        }
    }

    .pay_ment{
        justify-content: flex-end;
        color: #333;
    }

    .pay_container{
        border-top-left-radius: 16rpx;
        border-top-right-radius: 16rpx;
        background-color: #f5f5f5;
        .pay_box{
            padding: 0 48rpx;
        }
        .btn{
            margin-top: 90rpx;
            margin-bottom: 20rpx;
            padding: 20rpx;
            background-color: #fff;
            text-align: center;
            color: $uni-color-primary;
        }
    }

    
    .button{
        margin-top: 48rpx;
        width: 100%;
        text-align: center;
        height: 88rpx;
        line-height: 88rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: bold;
        background-image: linear-gradient(135deg, #E5BA72 0%, #F4DBB3 100%);
        color: #333;
    }
}

.success-box{
  margin: 128rpx 68rpx 120rpx 68rpx;
  background-image: linear-gradient(180deg, #40424C 0%, #1E1F25 100%);
  border-radius: 16rpx;
  position: relative;
  .success-header{
    padding: 24rpx 0;
    height: 100rpx;
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
    position: relative;
    >image{
        position: absolute;
        top: -128rpx;
        left: 0;
        right: 0;
        margin: auto;
        width: 100%;
    }
  }
  .success-info{
    padding: 24rpx 48rpx;
    .title{
      font-size: 40rpx;
      text-align: center;
      color: #E5BA72;
    }
    .sub_title{
        margin-top: 16rpx;
        text-align: center;
        color: #fff;
    }
    .rights-title-box{
        margin-top: 48rpx;
        align-items: center;
        justify-content:center;
        .line{
            height: 2rpx;
            width: 80rpx;
            background-color: #E5BA72;
        }
        .rights-title{
            margin: 0 16rpx;
            color: #E5BA72;
        }
    }
    .rights-list{
        margin-top: 24rpx;
        justify-content:space-between;
        .rights-item{
            align-items: center;
            font-size: 22rpx;
            color: #E5BA72;
            .icon_box{
                height: 42rpx;
                width: 42rpx;
                border-radius: 50%;
                background-color: #efcc99;
                align-items: center;
                justify-content: center;
                margin-bottom: 16rpx;
            }
        }
    }
  }
  .btn-box{
    margin: 0;
    padding: 48rpx;
    .btn{
      line-height: 88rpx;
      font-size: 32rpx;
      color: #fff;
      background-image: linear-gradient(135deg, #E5BA72 0%, #F4DBB3 100%);
      border-radius: 44rpx;
      text-align: center;
    }
  }
  .close-icon-box{
    width: 50rpx;
    height: 50rpx;
    left: 0;
    right: 0;
    margin: auto;
    position: absolute;
    bottom: -100rpx;
  }
}

.gave-box{
    margin-top: 24rpx;
    .label{
        font-size: 40rpx;
        font-weight: bold;
        counter-reset: #333;
    }
    .gave_list{
        flex-wrap: wrap;
        background-color: #F4DBB3;
        border-radius: 16rpx;
        position: relative;
        &.row_line{
            &::after{
                content: "";
                height: 1rpx;
                -webkit-transform: scaleY(.5);
                transform: scaleY(.5);
                position: absolute;
                left: 24rpx;
                right: 24rpx;
                top: 0;
                bottom: 0;
                margin: auto;
                background-color: #E7C793;
            }
        }
        &::before{
            content: "";
            width: 1rpx;
            -webkit-transform: scaleX(.5);
            transform: scaleX(.5);
            position: absolute;
            top: 24rpx;
            bottom: 24rpx;
            left: 0;
            right: 0;
            margin: auto;
            background-color: #E7C793;
        }
        .gave_item{
            flex: 1;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            min-width: 45%;
            padding: 24rpx;
            font-size: 24rpx;
            color: #9A702A;
            &.seat{
                padding: 0 24rpx;
            }
            .icon{
                width: 64rpx;
                height: 64rpx;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                margin-right: 24rpx;
                background-color: #BD8F42;
            }
            .gave_name{
                font-size: 24rpx;
            }
            .data{
                font-size: 24rpx;
                margin-top: 10rpx;
                align-items: center;
                // justify-content: center;
                .value{
                    font-size: 32rpx;
                    margin-right: 6rpx;
                    font-weight: bold;
                }
            }
        }
    }
}
</style>