<template>
	<view class="video-box">
		<video :src="url" id="video"  :vslide-gesture-in-fullscreen ="false" :enable-play-gesture="false" :enable-progress-gesture="false" :show-fullscreen-btn="false" :show-play-btn ="false"  :show-center-play-btn ="false" :controls='false' @ended="videoEnd"  :autoplay="autoplay" x5-playsinline  @loadedmetadata ='videoTimeUpdateEvent($event)'>

			<view class="time flex-row items-center j-center" v-if='from&&time>0' >
				<!-- <view class="time_c  flex-row items-center j-center" > -->
					<template v-if ="time===0">
						<text>任务已完成</text>
					</template>
					<template v-if='time>0' >
						<text>{{minute}}</text>
						<text>分钟</text>
						<text>{{second}}</text>
						<text>秒</text>
						<text>后完成任务</text>
						
					</template>
					
				<!-- </view> -->
			</view>
		</video>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				autoplay:false,
				url:"",
				time:"0",
				from:""
			}
		},
		computed:{
			minute(){
				return Math.floor(this.time/60)
			},
			second(){
				return Math.ceil(this.time%60)
			}
		},
		onLoad(options){
			// #ifdef MP
			uni.hideShareMenu()
			// #endif
			// if(options.url){
			// 	this.url = options.url
			// }else {
			// 	this.url ='https://images.tengfangyun.com/attachment/builds/20221216/0ddd4dc4b01ecef0e774fdb2edad9e03a7f62cb3.mp4'
			// }
			if (options.from){
				this.from =options.from
			}
			if (options.id){
				this.id = options.id
				this.getData()
			}
			// this.videoId = uni.createVideoContext("video")
		},
		mounted(){
			// this.$nextTick(()=>{
			
			// 	this.videoId.seek(0)
			// 	this.videoId.play()
			// })
		},
		beforeDestroy(){
			if (this.from ){
				uni.$emit("finished",{status:this.isFinished,task_name:"view_video"})
			}
			this.videoContext =null
			this.timer&&clearInterval(this.timer)
		},
		methods: {
			videoEnd(){
					console.log(12321);
					this.isFinished =true
					uni.showToast({
						title:"任务已完成",
						icon :'none'
					})
			},
			videoTimeUpdateEvent(e){
				console.log(e);
				this.time =e.detail.duration? e.detail.duration:"0"
				// this.$forceUpdate()
				this.timer = setInterval(() => {
						if (this.time==0){
							clearInterval(this.timer)
							return 
						}
						this.time--
				}, 1000);
				// this.time =e.detail.duration? Math.ceil(e.detail.duration-e.detail.currentTime):'0'
			},
			getData(){
				this.$ajax.get("blind_box/blindBoxVideo",{id:this.id},(res)=>{
					console.log(res);
					if (res.data.code ==1){
						if (res.data.video_type){
								this.url =res.data.video_path
								
						}else {
							this.url =res.data.video_link
						}
						this.videoContext = uni.createVideoContext('video',this)
						this.$nextTick(()=>{
							this.videoContext.play()
						})
						
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.video-box{
	position: absolute;
	top: 0;
	bottom: 0;
	width: 100%
}
video{
	height: 100%;
	width: 100%;
}
.time {
	position: absolute;
	color: #fff;
	transform: translateX(-50%);
	margin-left: 50%;
	top: 10vw;
	padding: 20rpx 20rpx;
	border-radius: 25rpx;
	border: 2rpx solid #f3f3f3;
	background: transparent;
	// left:0;
	// top: 15vh;
	// right: 0;
	// height:50rpx;

	// .time_c {
	// 	height: 100%;
	// 	display: inline-block;
	// 	padding: 10rpx 20rpx;
	// 	margin: 0 auto;
	// 	color: #fff;
	// }
}

</style>
