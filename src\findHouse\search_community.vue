<template>
	<view class="community">
		<!-- 搜索  -->
		<view class="search flex-row">
			<view class="inp flex-1">
				<input
					type="text"
					placeholder="请输入小区名称"
					v-model="params.keywords"
					@input="handelInput"
					@confirm="handelSearch"
				/>
				<view class="icon">
					<myIcon type="ic_sousuo" color="#999999" size="40rpx"></myIcon>
				</view>
			</view>
			<!-- <view class="submit" @click="handelSearch">确定</view> -->
		</view>

		<!-- 搜索结果 -->
		<view class="community_list">
			<template v-if="communityList.length > 0">
				<view
					class="community_item add flex-row"
					v-for="item in communityList"
					:key="item.id"
					@click="selectItem(item)"
				>
					<view class="title">{{ item.title }}</view>
					<view
						class="icon"
						:style="{
							background: item.isChecked ? '#2D84FB' : '#fff',
							borderColor: item.isChecked ? '#2D84FB' : '#A0A0A0',
						}"
					>
						<myIcon type="wancheng" color="#fff" size="28rpx"></myIcon>
					</view>
				</view>
			</template>
			<template v-else>
				<view
					class="community_item no_data add flex-row"
					@click="selectItem(item)"
				>
					<view class="title">暂无数据</view>
				</view>
			</template>
		</view>
		<!-- 确认按钮 -->
		<view class="btn  flex-row" @click="save">
			<view class="save flex-1">保存</view>
		</view>
	</view>
</template>

<script>
import myIcon from "../components/myIcon";
export default {
	components: {
		myIcon,
	},
	data() {
		return {
			params: {
				keywords: "",
			},
			cids: "",
			communityList: [],
		};
	},
	onLoad(options) {
		if (options.cids) {
			this.cids = options.cids;
		}
	},
	methods: {
		handelInput(e) {
			this.params.keywords = e.detail.value;
			let timer = setTimeout(() => {
				this.getCommunityList();
			}, 500);
		},
		handelSearch(e) {
			this.getCommunityList(e);
		},
		getCommunityList(e) {
			if (!this.params.keywords) {
				this.communityList = [];
				return;
			}

			this.$ajax.get(
				"index/searchCommunity.html",
				{
					type: 4,
					keywords: this.params.keywords,
				},
				(res) => {
					let ids = this.cids.split(",");
					if (res.data.code == 1) {
						res.data.list.map((item) => {
							item.isChecked = false;
							ids.map((id) => {
								if (id == item.id) {
									item.isChecked = true;
								}
							});
							return item;
						});
						this.communityList = res.data.list;
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: "none",
						});
					}
				}
			);
			// setTimeout(() => {
			//   this.communityList =[
			//     {
			// 					areaid: 250,
			// 					areaname: "蕉城区",
			// 					areaorder: 0,
			// 					label: undefined,
			// 					isChecked: 0,
			// 					mapx: "",
			// 					mapy: "",
			// 					parentid: 249,
			// 					tx_mapx: "",
			// 					tx_mapy: "",
			//           zdx_id: 1846,
			//           id:1,
			//           name:'大同天下',
			//           isChecked:'true'
			// 				},
			// 				{
			// 					areaid: 251,
			// 					areaname: "福安市",
			// 					areaorder: 0,
			// 					isChecked: 0,
			// 					label: undefined,
			// 					mapx: "",
			// 					mapy: "",
			// 					parentid: 249,
			// 					tx_mapx: "",
			// 					tx_mapy: "",
			//           zdx_id: 1847,
			//           id:2,
			//           name:'保利',
			//           isChecked:false
			// 				},
			//   ]
			// }, 500);
		},
		save() {
			let arr = [];
			this.communityList.map((item) => {
				if (item.isChecked == true) {
					arr.push(item);
				}
			});
			this.$navigateBack();
			uni.$emit("getChooseCommunityAgain", {
				type: "xiaoqu",
				data: arr,
			});
		},
		selectItem(item) {
			item.isChecked = !item.isChecked;
		},
	},
};
</script>

<style scoped lang="scss">
.flex-row {
	display: flex;
	flex-direction: row;
}
page{
	background: #fff;
}
.community {
	padding-bottom: 200rpx;
}
.search {
	justify-content: space-between;
	align-items: center;
	box-sizing: border-box;
	padding: 32rpx;
	border-bottom: 2rpx solid #f7f7f7;
	background: #fff;
	.inp {
		background: #f3f2f7;
		position: relative;
		padding: 20rpx 0 20rpx 60rpx;
		overflow: hidden;
		font-size: 22rpx;
		border-radius: 40rpx;
		.icon {
			position: absolute;
			left: 10rpx;
			width: 22rpx;
			height: 22rpx;
			top: 18rpx;
		}
		input {
			font-size: 22rpx;
		}
	}
	.submit {
		margin-left: 40rpx;
		color: #2d84fb;
		font-size: 28rpx;
	}
}

.add {
	justify-content: space-between;
	align-items: center;
	padding: 32rpx 44rpx;
	background: #fff;
	margin-top: 2rpx;
	border-bottom: 2rpx solid #f7f7f7;
	&.no_data {
		justify-content: center;
	}
	.title {
		font-size: 28rpx;
		color: #000000;
	}
	.icon {
		width: 40rpx;
		height: 40rpx;
		background: #fff;
		border-radius: 50%;
		overflow: hidden;
		border: 2rpx solid;
		display: flex;
		justify-content: center;
		align-items: center;
	}
}
.btn {
	position: fixed;
	bottom: 48rpx;
	width: 100%;
	.save {
		background: #ff656b;
		margin: 0 32rpx;
		border-radius: 5px;
		padding: 26rpx 0;
		color: #fff;
		font-size: 36rpx;
		text-align: center;
		justify-content: center;
		align-items: center;
	}
}
</style>
