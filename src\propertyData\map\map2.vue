<template>
	<view class="page">
		<map id="map" :scale="mapData.scale" :latitude="lat" :longitude="lng" :markers="mapData.covers"></map>

		<cover-view class="right_menu">
			<cover-image class="item" @click="opemLocation()" :src="'/build/v_3/map_property/nav.png' | imageFilter('m_320')">导航</cover-image>
			<cover-image v-if="type==1" class="item" @click="sendAddressToPhone()" :src="'/build/v_3/map_property/mobile.png' | imageFilter('m_320')">发送</cover-image>
			<cover-image class="item" @click="handleShare" :src="'/build/v_3/map_property/share.png' | imageFilter('m_320')">分享</cover-image>
		</cover-view>
		<cover-view class="cover_container">
			<cover-view>
				<cover-view class="options_btn">
					<cover-image class="icon" :class="{icon_reversal: icon_reversal}" :src="'/build/v_3/map_property/open.png' | imageFilter('m_320')"></cover-image>
				</cover-view>
				<cover-view class="map-cate-list">
					<cover-view v-for="(cate, index) in cate_list" :key="index" class="cate-item" :class="{active: current === cate.name}" @click="getCovers(cate.name, cate.children)">
						<cover-view class="text">{{cate.name}}</cover-view>
					</cover-view>
				</cover-view>
				<cover-view class="chil_cate">
					<cover-view v-for="(item, index) in current_chil_cate" :key="index" class="chil_cate-item" :class="{active: chil_cate===item}" @click="getCovers(current, current_chil_cate, item)">{{item}}</cover-view>
				</cover-view>
			</cover-view>
			<cover-view class="res_list">
				<cover-view v-if="mapData.covers.length<=1" class="nodata">暂无数据</cover-view>
				<cover-view v-else class="item" v-for="(item, index) in mapData.covers" :key="index" v-show="item.title" @click="moveTo(item.latitude, item.longitude)">
					<cover-view class="left flex-1">
						<cover-view class="title">{{item.title}}</cover-view>
						<cover-view class="address">{{item.address}}</cover-view>
					</cover-view>
					<cover-view class="distance">{{item._distance | distanceFormat}}</cover-view>
				</cover-view>
			</cover-view>
		</cover-view>
		<send-address-to-phone
      ref="sub_send_form"
      :sub_mode="sub_mode"
      @onsubmit="handleSubFormPhone"
      @signUp ="handleSubFormTel"
      :login_status="login_status"
    ></send-address-to-phone>
	</view>
</template>

<script>
	// #ifdef H5 || MP-BAIDU
	import {
	setSeo
	} from '../../common/mixin'
	// #endif
	import SendAddressToPhone from '../../components/sendAddressToPhone.vue'
	import copyText from '../../common/utils/copy_text'
	export default {
		components:{
			SendAddressToPhone
		},
		data() {
			return {
				id:"",
				type:1,
				lat:"",
				lng:"",
				current: '交通',
				chil_cate: '',
				current_chil_cate: [],
				cate_list: [
					{
						name: '交通',
						children: [
							'公交',
							'其他'
						]
					},
					{
						name: '教育',
						children: [
							'幼儿园',
							'小学',
							'中学',
							'高中'
						]
					},
					{
						name: '医疗',
						children: [
							'综合',
							'药店',
							'其他'
						]
					},
					{
						name: '商业',
						children: [
							'购物',
							'金融',
							'机构'
						]
					}
				],
				mapData:{
					scale:16,
					covers: [],
				},
				cover_container_translate: uni.upx2px(500),
				animationData: {},
				icon_reversal: true
			};
		},
		// #ifdef H5 || MP-BAIDU
		mixins: [setSeo],
		// #endif
		computed: {
			login_status() {
				return this.$store.state.user_login_status
			},
			sub_mode() {
				return this.$store.state.sub_form_mode 
			},
		},
		onLoad(options){
			this.lat = options.lat
			this.lng = options.lng
			this.title = options.title||''
			let ar = [options.lat,options.lng]
			this.mapData.cover = ar
			this.type = options.type
			this.map = uni.createMapContext("map",this);
			this.map.includePoints({
				padding:[20,20,20,20]
			})
			if(options.id){
				this.id = options.id
				this.getCovers(this.current, this.cate_list[0].children)
			}
		},
		filters:{
			distanceFormat(val){
				if(!val){
					return ''
				}
				if(val<1000){
					return Math.ceil(val)+'m'
				}else{
					return (val/1000).toFixed(1)+'km'
				}
			}
		},
		methods:{
			getCovers(keywords=this.current, chil_cate, chil_cate_name=''){
				let api
				// #ifndef MP-BAIDU
				api = "build/peripheryByMap.html"
				// #endif
				// #ifdef MP-BAIDU
				api = "build/peripheryByMapApp.html"
				// #endif
				this.current = keywords
				this.current_chil_cate = chil_cate
				this.chil_cate = chil_cate_name||this.current_chil_cate[0]
				uni.showLoading({
					title: '加载中...'
				})
				this.$ajax.get(api,{id:this.id, keywords,type:this.type, filter: this.chil_cate},(res)=>{
					uni.hideLoading()
					this.title = res.data.title
					uni.setNavigationBarTitle({
						title:`${res.data.title||''}地图周边`
					})
					// #ifdef H5 || MP-BAIDU
					if (res.data.seo) {
						this.seo = res.data.seo
					}
					// #endif
					if(res.data.code != 1){
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						return
					}
					let icon;
					switch(keywords)
					{
					case '商业':
						icon = '/static/icon/foot.png'
						break
					case '教育':
						icon = '/static/icon/edu.png'
						break
					case '医疗':
						icon = '/static/icon/yiliao.png'
						break
					case '交通':
						icon = '/static/icon/jiaotong.png'
						break
					default:
						icon = '/static/icon/center.png'
					}
					let covers = res.data.data.map((item)=>{
						let distance = parseInt(item._distance)
						let ob = {
							width: 30,
							height: 30,
							iconPath: icon,
							latitude: item.location.lat,
							longitude: item.location.lng,
							title: item.title,
							address: item.address,
							_distance: item._distance,
							callout: {content:item.title,padding:5,borderRadius:4,display:'ALWAYS'},
							distance: distance
						}
						return ob
					})
					covers.push({
						latitude: this.lat,
						longitude: this.lng,
						width: 30,
						height: 30,
						callout: {content:this.title,padding:5,borderRadius:4,bgColor:"#f65354",color:"#ffffff",display:'ALWAYS'},
						iconPath: '/static/icon/center.png'
					})
					this.mapData.covers = covers
					this.map.moveToLocation({
						latitude: parseFloat(this.lat)-0.0015,
						longitude: parseFloat(this.lng)
					})
					this.$nextTick(()=>{
						uni.pageScrollTo({
							scrollTop: uni.upx2px(500),
							duration: 200
						})
					})
				})
			},
			onTouchStart(e){
				this.touch_start = e.touches[0].clientY
				this.current_translate = this.cover_container_translate
			},
			onTouchMove(e){
				let difference = this.current_translate+(e.touches[0].clientY - this.touch_start)
				if(difference<=0){
					difference = 0
				}
				if(difference>=uni.upx2px(500)){
					difference = uni.upx2px(500)
				}
				this.cover_container_translate = difference
			},
			onTouchEnd(e){
				if(this.cover_container_translate<130){
					this.handleAnimation(this.cover_container_translate, 0)
				}else{
					this.handleAnimation(this.cover_container_translate, uni.upx2px(500))
				}
				console.log(this.cover_container_translate)
			},
			handleAnimation(value, last_value){
				var timer = setInterval(()=>{
					if(last_value===0){
						this.cover_container_translate-=3
						if(this.cover_container_translate<=0){
							this.cover_container_translate = 0
							clearInterval(timer)
						}
					}
					if(last_value>value){
						this.cover_container_translate+=3
						if(this.cover_container_translate>=last_value){
							this.cover_container_translate = last_value
							clearInterval(timer)
						}
					}
				}, 6)
			},
			opemLocation(){
				uni.openLocation({
					latitude: parseFloat(this.lat),
					longitude: parseFloat(this.lng),
					name: this.title,
					address: this.address||''
				})
			},
			sendAddressToPhone(){
				this.$refs.sub_send_form.showPopup()
			},
			handleSubFormPhone(e){
				//提交报名
				e.from ='发送地址到手机'
				// e.name=""
				// e.bid = this.id
				
				e.build_id =this.id
				this.$ajax.get('member/authRegister', e, res => {
					uni.hideLoading()
					if (res.data.code === 1) {
							//提示报名成功 微信小程序需要单独处理
							// #ifndef MP-WEIXIN
								if (res.data.token) {
										uni.setStorageSync('token', res.data.token)
								}
								this.$store.state.user_login_status =3
								//#endif
								uni.showToast({
									title: res.data.msg,
									icon: 'none'
								})
							this.$refs.sub_send_form.closeSub()
							// if(this.login_status==1&&res.data.token ){
							//   let token =res.data.token 
							//   uni.setStorageSync("token",token)
							//   this.$store.state.user_login_status=3
							// }
						// } else {
						//   this.$refs.sub_form.getVerify()
						// }
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
					}
				})
			},
			handleSubFormTel(e) {
				//提交报名
				e.from ='地图周边页面'
				e.bid = this.id
				e.type = this.sub_type || ''
				console.log(e)
				this.$ajax.post('build/signUp.html', e, res => {
				})
			},
			handleShare(){
				let content = `${this.title}地图周边:${window.location.href}`
				copyText(content, ()=>{
					uni.showToast({
						title: '复制成功，去发送给好友吧',
						icon: 'none'
					})
				})
			},
			moveTo(lat, lng){
				this.map.moveToLocation({
					latitude: parseFloat(lat)-0.0015,
					longitude: parseFloat(lng)
				})
			}
		},
		onPageScroll(e){
			if(e.scrollTop === 0){
				this.icon_reversal = true
			}else{
				this.icon_reversal = false
			}
		},
		onShareAppMessage(){
			return {
				title :`${this.title}地图周边`,
				path:"/propertyData/map/map?id=" + this.id + '&type=1&lat=' + this.lat + '&lng=' + this.lng
			}
		}
	}
</script>

<style lang="scss">
.page{
	height: calc(100vh - 44px);
	overflow: hidden;
}
	map{
		position: fixed;
		width: 100%;
		top: 0;
		bottom: 0;
		height: auto;
	}
	.grid-box{
		position: absolute;
		width: 100%;
		bottom: 0;
	}
	.cate-icon{
		width: 48rpx;
		height: 48rpx;
	}

	.cover_container{
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #fff;
		transform: translateY(500rpx);
		.options_btn{
			display: flex;
			justify-content: center;
			padding-top: 24rpx;
			.icon{
				width: 48rpx;
				&.icon_reversal{
					transform: rotateX(180deg);
				}
			}
		}
	}
	.map-cate-list {
    padding: 16rpx 0;
    display: flex;
    align-items: center;
		justify-content: space-around;
    background-color: #fff;
    // box-shadow: 0 0 12rpx 0 rgba(0, 0, 0, 0.15);
    .cate-item {
      // flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
			padding: 8rpx;
      color: #999;
			position: relative;
      &.active{
        color: $uni-color-primary;
        &:after{
          content: '';
          position: absolute;
          bottom: 0;
          left: 12rpx;
          right: 12rpx;
          height: 4rpx;
          background-color: $uni-color-primary;
        }
      }
      .image {
        width: 48rpx;
        height: 48rpx;
        margin-right: 6rpx;
      }
      .text {
        font-size: 24rpx;
      }
    }
  }
	
	.chil_cate{
		padding: 24rpx 48rpx;
		display: flex;
		align-items: center;
		.chil_cate-item{
			height: 48rpx;
			line-height: 48rpx;
			padding: 0 24rpx;
			border-radius: 24rpx;
			background-color: #F8F8F8;
			~.chil_cate-item{
				margin-left: 24rpx;
			}
			&.active{
				background-color: #FFE3E3;
				color: #FF3939;
			}
		}
	}

	.res_list{
		padding: 0 48rpx;
		box-sizing: border-box;
		height: 500rpx;
		width: 100%;
		.nodata{
			text-align: center;
			padding: 24rpx;
			color: #999;
		}
		.item{
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			align-items: flex-end;
			width: 100%;
			overflow: hidden;
			margin-bottom: 24rpx;
			font-size: 24rpx;
			.title{
				margin-bottom: 8rpx;
				flex-shrink: 0;
				font-size: 26rpx;
			}
			.address{
				// margin: 0 12rpx;
				flex: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				color: #999;
			}
			.distance{
				margin-left: 12rpx;
				flex-shrink: 0;
				color: #999;
			}
		}
	}
	
	.right_menu{
		position: absolute;
		right: 24rpx;
		bottom: 300rpx;
		.item{
			width: 120rpx;
			height: 120rpx;
			// margin-bottom: 24rpx;
			// display: flex;
			// align-items: center;
			// justify-content: center;
			// border-radius: 50%;
			// font-size: 20rpx;
			// background: #FFFFFF;
			// border: 0.5px solid #DADADA;
			// box-shadow: 0px 0px 10px 0px #00000019;
		}
	}
</style>
