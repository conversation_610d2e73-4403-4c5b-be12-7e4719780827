
<template>
  <view class="user_comments_lists">
    <view class="top row">
      <view class="user_box row">
        <image :src="item.prelogo | imageFilter('w_120')" mode="aspectFill" @click="toAgentDetail(item,'jiedu')"></image>
        <view class="user_name flex-row flex-1" @click="toAgentDetail(item,'jiedu')">
          <text class="cname">{{ item.cname }}</text>
          <text class="label" v-if="item.agent_isvalid">经纪人</text>
        </view>
        <view class="btns flex-row" v-if="item.agent_isvalid">
          <view class="btn" @click="handleChat({uid:item.uid})">在线咨询</view>
          <view class="btn" @click="handleTel(item)">拨打电话</view>
        </view>
      </view>
    </view>
    <view class="user_right bottom-line">
      <view class="txt">{{ item.content }}</view>
      <view class="media_box">
        <!-- 上传的图片 -->
        <view v-if="!item.isvideo&&item.medias&&item.medias.length>0" class="imgs_box row">
          <!-- 单图 -->
          <view class="img_item_alone" v-if="item.medias.length === 1" @click="previewImage(item.medias[0], item.medias)">
            <image :src="item.medias[0] | imageFilter('w_6401')" mode="widthFix"></image>
          </view>
          <!-- 多图 -->
          <view class="img_item" v-else v-for="(img, idx) in item.medias" :key="idx">
            <image
              mode="aspectFill"
              :src="img | imageFilter('w_240')"
              @click="previewImage(img,item.medias)"
            ></image>
          </view>
          <view class="img_item perch"></view>
          <view class="img_item perch"></view>
        </view>
        <!-- 上传的视频 -->
        <view v-else class="video_box" @click="viewVideo(item.medias[0])">
          <image class="video_item" :src="item.medias[0] | imageFilter('w_400')" mode="aspectFill"></image>
          <view class="bofang">
          <myIcon
            type="ic_video"
            size="66rpx"
            color="#fff"
          ></myIcon>
        </view>
        </view>
        <slot></slot>
      </view>
    </view>
    
  </view>
</template>

<script>
import myIcon from '../../components/myIcon'
import {formatImg} from '../../common/index'
export default { 
  components:{
    myIcon
  },
  // from 调用来源 1 小区专家列表 2 小区房价 详情页
		props:{
      from :{
				type:Number,
				default:1
			},
      item:{
        type:Object,
        default:{}
      },
    },
    methods: {
      toAgentDetail(item){
        this.$emit('toAgentDetail',item)
      },
      handleChat(item){
        if (this.from ==1){   // 专家列表传参是id  小区传参是item 区别处理下
          this.$emit("handleChat",item.uid)
        }else if (this.from ==2){
          this.$emit("handleChat",item)
        }
      },
      handleTel(item){
        this.$emit("handleTel",item)
      },
      previewImage(img,img_list){
        img_list = img_list.map(item=>formatImg(item,'w_800'))
        uni.previewImage({
          urls: img_list,
          current: formatImg(img,'w_800')
        })
      },
      viewVideo(url){
        this.$navigateTo(`/vr/preview_video?url=${url}`)
      },
    }
}
</script>

<style lang="scss" scoped>
.user_comments_lists {
  
    // margin-bottom: 28rpx;
    .top {
      view{
        display: flex;
      }
      &.row{
        padding:0;
      }
      margin-top: 24rpx;
      margin-bottom: 10rpx;
      justify-content: space-between;
      align-items: center;
      .user_box {
        flex: 1;
        align-items: center;
        width: 100%;
        &.row{
          padding:0;
        }
        image {
          margin-right: 16rpx;
          width: 64rpx;
          height: 64rpx;
          border-radius: 50%;
          background-color: #dedede;
        }
        .user_name {
          font-size: 30rpx;
          // font-weight: bold;
          align-items: center;
          overflow: hidden;
          .cname{
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .label{
            margin-left: 12rpx;
            font-size: 24rpx;
            font-weight: initial;
            line-height: 1;
            padding: 6rpx 0;
            min-width: 90rpx;
            text-align: center;
            border-radius: 4rpx;
            background: linear-gradient(to right, #f7918f, #fb656a);
            color: #fff;
            &.gf{
              min-width: 72rpx;
              background: linear-gradient(to right, #7bbcf8, #419ef5);
            }
          }
        }
        .btns{
          justify-content: flex-end;
          .btn{
            font-size: 26rpx;
            padding: 10rpx 24rpx;
            border-radius: 6rpx;
            color: #fb656a;
            border: 1rpx solid #fb656a;
            ~.btn{
              margin-left: 20rpx;
            }
          }
        }
      }
      .up_time {
        color: #999;
        font-size: 22rpx;
      }
    }
    .user_right {
      margin-left: 74rpx;
      .txt {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
        // 文本溢出隐藏
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      .media_box {
        flex-wrap: wrap;
        .imgs_box {
          // width: 100%;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          &.row {
            padding:0;
          }
          .img_item_alone{
            margin-top: 24rpx;
            width: 78%;
            max-height: 600upx;
            position: relative;
            overflow: hidden;
            image{
              width: 100%;
              height: 100%;
              // margin-bottom: 24rpx;
              overflow: unset;
            }
          }
          .img_item{
            width: 178rpx;
            height: 178rpx;
            margin-top: 24rpx;
            &.perch{
              height: 0;
              margin: 0;
            }
          }
          image {
            width: 100%;
            height: 100%;
          }
        }
        .video_box {
          position: relative;
          width: 480rpx;
          height: 320rpx;
          image {
            margin-top: 24rpx;
            width: 100%;
            height: 100%;
          }
          .bofang {
            position: absolute;
            border-radius: 50%;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.5);
          }
        }
      }
      .infor_box {
        justify-content: space-between;
        flex: 1;
        font-size: 22rpx;
        margin-top: 16rpx;
        margin-bottom: 24rpx;
        .left {
          align-items: center;
          &.row{
            font-size: 22rpx;
          }
          .community_name {
            color: #fb656a;
            margin-right: 16rpx;
          }
          .shop_name {
            color: #999;
          }
          .up_time {
            margin-left: 16rpx;
            color: #999;
            font-size: 22rpx;
          }
        }
        .right {
          align-items: center;
          color: #d8d8d8;
        }
      }
      .house_type{
        margin-top: 24rpx;
        padding: 24rpx;
        border-radius: 8rpx;
        background-color: #f8f8f8;
        image{
          width: 128rpx;
          height: 128rpx;
          margin-right: 24rpx;
          border-radius: 8rpx;
        }
        .house_info{
          .title{
            margin-bottom: 10rpx;
            font-size: 32rpx;
            color: #333;
          }
          .desc{
            margin-bottom: 8rpx;
            font-size: 22rpx;
            color: #666;
            .huxing_desc{
              margin-right: 16rpx;
            }
          }
          .price{
            align-items: center;
            .label{
              margin-right: 12rpx;
              font-size: 24rpx;
              color: #666;
            }
            .value{
              font-weight: bold;
              font-size: 32rpx;
              color: $uni-color-primary;
            }
            .unit{
              margin-left: 8rpx;
              font-size: 24rpx;
              color: #666;
            }
          }
        }
      }
    }
  }
</style>
