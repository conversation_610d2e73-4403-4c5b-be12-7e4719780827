<template>
  <view class="address_box">
    <view class="flex-row">
      <view class="area_name flex-1" :class="{active:first_area.areaid}">{{first_area.areaname||'请选择'}}</view>
      <view class="area_name flex-1" v-if="second_list.length>0" :class="{active:second_area.areaid}">{{second_area.areaname||'请选择'}}</view>
      <view class="area_name flex-1" v-if="third_area.length>0" :class="{active:third_area.areaid}">{{third_area.areaname||'请选择'}}</view>
    </view>
    <view class="address_list flex-row">
      <scroll-view scroll-y class="cate first flex-1">
        <view class="item bottom-line" :class="{active:first_area.areaid==item.areaid}" @click="onSelectArea(1, item)" v-for="(item, index) in first_list" :key="index">{{item.areaname}}</view>
      </scroll-view>
      <scroll-view scroll-y class="cate second flex-1" v-if="second_list.length>0">
        <view class="item bottom-line" :class="{active:second_area.areaid==item.areaid}" @click="onSelectArea(2, item)" v-for="(item, index) in second_list" :key="index">{{item.areaname}}</view>
      </scroll-view>
      <scroll-view scroll-y class="cate third flex-1" v-if="third_list.length>0">
        <view class="item bottom-line" :class="{active:third_area.areaid==item.areaid}" @click="onSelectArea(3, item)" v-for="(item, index) in third_list" :key="index">{{item.areaname}}</view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  components: {

  },
  data () {
   return {
      first_list:[],
      second_list:[],
      third_list:[],
      first_area:{},
      second_area:{},
      third_area:{},
      selectted:[]
     }
  },
  watch:{
    data_list:{
      deep: true,
      handler(val){
        this.getFirst(val)
      }
    },
    selectted_area:{
      deep: true,
      handler(val){
        this.selectted = val
        console.log("当前选中值：", val)
      }
    }
  },
  props:{
    data_list: {
      type: Array,
      default:()=>{return []}
    },
    selectted_area: {
      type: Array,
      default: ()=>{return []}
    }
  },
  created(){
  },
  methods: {
    getFirst(data){
      this.first_list = data
    },
    onSelectArea(i,e){
      switch(i){
        case 1:
          this.first_area = e
          this.second_area = {}
          this.selectted= [{
              value:e.areaid,
              name:e.areaname
            }]
          this.third_area = {}
          this.third_list = []
          // 筛选出当前区域下的二级区域
          if(e.children&&e.children.length>0){
            this.second_list = e.children
          }else{
            this.second_list = []
            this.$emit('onselect', this.selectted)
          }
          break;
        case 2:
          this.second_area = e
          this.third_area = {}
          this.selectted[1] = {
            value:e.areaid,
            name:e.areaname
          }
          this.selectted.splice(2, 1)
          // 筛选出当前区域下的三级区域
          if(e.children&&e.children.length>0){
            this.third_list = e.children
          }else{
            this.third_list = []
            this.$emit('onselect', this.selectted)
          }
          break;
        case 3:
          this.third_area = e
          this.selectted[2] = {
            value:e.areaid,
            name:e.areaname
          }
          this.$emit('onselect', this.selectted)
      }
    },
  }
}
</script>

<style scoped lang="scss">
view{
  display: flex;
  flex-direction: column;
  box-sizing: border;
}
.flex-row{
  flex-direction: row;
}

.address_box{
  background-color: #fff;
  .area_name{
    padding: 20rpx;
    text-align: center;
    border-top: 4rpx solid $uni-color-primary;
    background-image: linear-gradient(0deg, rgba(246, 246, 246, 0) 0%, rgba(251, 101, 106, 0.1) 100%);
    ~.area_name{
      margin-left: 24rpx;
    }
    &.active{
      color: $uni-color-primary;
      border-top: 4rpx solid $uni-color-primary;
      background-image: linear-gradient(0deg, rgba(246, 246, 246, 0) 0%, rgba(251, 101, 106, 0.1) 100%);
    }
  }
  .cate{
    padding: 20rpx;
    max-height: 30vh;
    overflow-y: scroll;
    transition: 0.26s;
  }
  .item{
    text-align: center;
    padding: 20rpx 10rpx;
    &.active{
      color: $uni-color-primary;
    }
  }
}
</style>