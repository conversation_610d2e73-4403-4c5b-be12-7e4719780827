/*
 * @Description: 
 * @Author: lei
 * @Date: 2022-01-15 18:52:16
 */

'use strict'

/**
 * <AUTHOR> QQ:1017771330
 * @date 2020-07-29 14:06:21
 * @desc 将打包后的代码自动部署到服务器
 */

const path = require('path')
const fs = require('fs')
const Client = require('ssh2-sftp-client')

class SftpUpload {
  constructor(files_dir, remote_dir) {
    this.files_dir = files_dir
    this.remote_dir = remote_dir
  }

  upload(ip, port = '22', username, password, ignore) {
    function getJsonFiles(jsonPath) {
      const jsonFiles = []
      function findJsonFile(file_path) {
        const files = fs.readdirSync(file_path)
        files.forEach(item => {
          const fPath = path.join(file_path, item)
          const stat = fs.statSync(fPath)
          if (stat.isDirectory() === true) {
            findJsonFile(fPath)
          }
          if (stat.isFile() === true) {
            jsonFiles.push(fPath)
          }
        })
      }
      findJsonFile(jsonPath)
      return jsonFiles
    }
    return new Promise((resolve, reject) => {
      const sftp = new Client()
      sftp
        .connect({
          host: ip,
          port,
          username,
          password
        })
        .then(() => {
          console.log('服务器连接成功，正在上传...')
          return new Promise((resolve, reject) => {
            let fileList = getJsonFiles(this.files_dir)
            const len = fileList.length
            let num = 0
            ;(async () => {
              for (let i = 0; i < fileList.length; i++) {
                let romotePath
                const item = fileList[i]
                romotePath =
                  this.remote_dir +
                  item
                    .replace(this.files_dir.replace(/\//g, '\\'), '')
                    .replace(/\\/g, '/')
                if (ignore !== item.replace(/\\/g, '/') || ignore === null) {
                  await sftp.put(item, romotePath)
                  num++
                  if (num === len) {
                    resolve()
                  }
                } else {
                  num++
                  if (num === len) {
                    resolve()
                  }
                }
              }
            })()
          })
        })
        .then(() => {
          console.log(`${ip}代码上传完成`)
          sftp.end()
          resolve()
        })
        .catch(err => {
          console.log(err, `${ip}catch error`)
          sftp.end()
          reject(err)
        })
    }) 
  }
}

var file_dir = path.resolve(__dirname, '../dist/build/h5')
var sftp = new SftpUpload(file_dir, '/www/wwwroot/fang/public/h5')
sftp.upload('*************', 22, 'root', 'Tfy-=vfr41234')
