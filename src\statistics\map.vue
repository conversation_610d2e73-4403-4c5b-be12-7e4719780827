<template>
	<view class="page">
		<!-- #ifndef H5 -->
		<view
			class="map-box"
			:style="{ marginTop: systemInfo.statusBarHeight + 'px' }"
		>
			<map
				class="qqmap"
				id="map"
				:scale="scale"
				base="weixing"
				:longitude="mapcenter.longitude"
				:latitude="mapcenter.latitude"
				:markers="school_point"
				:polygons="polygons"
				@regionchange="onRegionchange"
			></map>
		</view>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<view class="map-box">
			<qqMap
				:mapkey="qqmapkey"
				:scale="scale"
				base="weixing"
				:longitude="mapcenter.longitude"
				:latitude="mapcenter.latitude"
				:markers="school_point"
				:polygons="polygons"
				@regionchange="onRegionchange"
			></qqMap>
		</view>
		<view class="yushou-info">
			<view
				class="yushou-title flex-box flex-grow"
				v-if="currentSchool.zt == 1"
			>
				<view class="yushou-title-left"
					>成交时间：{{ currentSchool.cjrq }}</view
				>
				<view class="yushou-area"
					>溢价<text class="red">{{ currentSchool.yjl }}</text
					>%</view
				>
				<view class="share_icon" @click="showSharePop">
					<my-icon type="ic_fenxiang" size="32rpx" color="#fff"></my-icon>
				</view>
			</view>
			<view
				class="yushou-title flex-box flex-grow"
				v-if="currentSchool.zt == 2"
			>
				<view class="yushou-title-left">报名中</view>
			</view>
			<view
				class="yushou-title flex-box flex-grow"
				v-if="currentSchool.zt == 3"
			>
				<view class="yushou-title-left">拍卖中</view>
			</view>
			<view
				class="yushou-title flex-box flex-grow"
				v-if="currentSchool.zt == 4"
			>
				<view class="yushou-title-left">流拍</view>
			</view>

			<view class="xiangmumingcheng">{{ currentSchool.tdwz }}</view>

			<view class="data-box bg-grey">
				<view class="gongsimingcheng" v-if="currentSchool.cjdw">{{
					currentSchool.cjdw
				}}</view>
				<view
					class="box flex-box"
					:class="{ 'bottom-line': currentSchool.zt == 1 }"
				>
					<view class="text-center flex-1 fenge">
						<view class="data-title">总面积</view>
						<view class="data-data"
							><text class="data-datas">{{
								showMushu == 1 ? currentSchool.mushu : currentSchool.mianji
							}}</text></view
						>
						<view class="data-title mtop12">{{
							showMushu == 1 ? "亩" : "m²"
						}}</view>
					</view>
					<view class="text-center flex-1 fenge">
						<view class="data-title">起拍价</view>
						<view class="data-data"
							><text class="data-datas">{{
								currentSchool.qishijia
							}}</text></view
						>
						<view class="data-title mtop12">万元</view>
					</view>
					<view class="text-center flex-1">
						<view class="data-title">最大</view>
						<view class="data-data"
							><text class="data-datas">{{
								currentSchool.rongjilv
							}}</text></view
						>
						<view class="data-title mtop12">容积率</view>
					</view>
				</view>
				<view class="box flex-box" v-if="currentSchool.zt == 1">
					<view class="text-center flex-1 fenge">
						<view class="data-title">成交价</view>
						<view class="data-data"
							><text class="data-datas red">{{
								currentSchool.chengjiaojia
							}}</text></view
						>
						<view class="data-title mtop12">万元</view>
					</view>
					<view class="text-center flex-1 fenge">
						<view class="data-title">地价 </view>
						<view class="data-data"
							>约<text class="data-datas red">{{
								currentSchool.dijia
							}}</text></view
						>
						<view class="data-title mtop12">万元/亩</view>
					</view>

					<view class="text-center flex-1">
						<view class="data-title">楼面价 </view>
						<view class="data-data"
							>约<text class="data-datas red">{{
								currentSchool.loumianjia
							}}</text></view
						>
						<view class="data-title mtop12">元/m²</view>
					</view>
				</view>
			</view>
		</view>
		<!-- #endif -->
		<share-pop
			ref="show_share_pop"
			@handleCreat="handleCreat"
			:showHaibao="false"
			@copyLink="copyLink"
			@showCopywriting="showCopywriting"
		></share-pop>
		<!-- #ifndef MP-WEIXIN -->

		<!-- 登录弹窗 -->
		<login-popup
			ref="login_popup"
			@onclose="handleCloseLogin"
			:sub_content="login_tip"
		></login-popup>
		<!-- #endif -->
		<chat-tip></chat-tip>
		<dingyue
			ref="dingyue"
			@dingyue="dingyue"
			:type="type"
			@login="toLogin"
			@bindPhone="toBind"
		></dingyue>
        <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
	</view>
</template>

<script>
import myTag from "@/components/myTag";
import myIcon from "@/components/myIcon";
import sharePop from "@/components/sharePop";
import dingyue from "@/components/dingyue";
// #ifdef H5
import qqMap from "@/components/qqMap";
import listItem from "../components/listItem";
// #endif
// #ifndef MP-WEIXIN
import loginPopup from "../components/loginPopup";
// #endif
import { mapActions, mapState } from "vuex";
import {
	isArray,
	navigateTo,
	formatImg,
	compareVersion,
	showModal,
} from "../common/index";
export default {
	components: {
		myTag,
		myIcon,
		// #ifdef H5
		qqMap,
		listItem,
		// #endif
		// #ifndef MP-WEIXIN
		loginPopup,
		// #endif
		sharePop,
		dingyue,
	},
	data() {
		return {
			school_point: [],
			polygons: [],
			currentSchool: {},
			scale: 15,
			// #ifdef H5
			qqmapkey: __uniConfig.qqMapKey,
			// #endif
			mapcenter: {
				latitude: "",
				longitude: "",
			},
			params: {
				lat: "",
				lng: "",
				id: "",
				sid: "",
				sharetype: "",
				forward_time:''
			},
			userLoginStatus: null,
			menu_button_width: 0,
			satisfyVersion: 0,
			baidu_map_all_getdata: true,
			current_user_info: {},
			sharers_info: {},
			login_tip: "",
			showMushu: 0,
			type: "dingyue",
			show_share_tip:false
		};
	},
	computed: {
		...mapState(["systemInfo"]),
	},
	onLoad(options) {
		if (options.shareId) {
			this.shareId = options.shareId;
			this.shareType = options.type;
			this.params.cid = options.shareId;
			this.params.sharetype = options.type;
			this.params.forward_time =options.f_time||''
		}
		if (options.lat&&options.lng){
			this.params.lat = options.lat;
			this.params.lng = options.lng;
		}
		if (options.id) {
			this.params.id = options.id;
		}
		this.init();
		// #ifndef H5
		this.map = uni.createMapContext("map", this);
		// #endif
		// #ifdef MP
		this.menu_button_width = uni.getMenuButtonBoundingClientRect().width;
		this.satisfyVersion = compareVersion("2.7.0");
		// #endif
		// #ifdef MP-BAIDU
		this.satisfyVersion = -1;
		// #endif
		uni.$on("getDataAgain", () => {
			this.getData();
		});
	},
	onUnload() {
		uni.$off("getDataAgain");
	},
	onShow() {
		if (this.$store.state.updatePageData) {
			this.init();
			this.$store.state.updatePageData = false;
		}
	},
	methods: {
		...mapActions(["getLocation"]),
		init() {
					this.mapcenter.latitude=this.params.lat
					this.mapcenter.longitude=this.params.lng
					this.getData();
		},
		getData() {
			if (!this.mapcenter.latitude || !this.mapcenter.longitude) {
				return;
			}
			uni.showLoading({
				title: "正在获取...",
			});
			this.type="dingyue"
			this.$ajax.get(
				"build/tudiDetail.html",
				this.params,
				(res) => {
					uni.setNavigationBarTitle({
						title: res.data.detail.tdbh + "位置示意图",
					});
					if (res.data.tupai_calculation_method) {
						this.showMushu = res.data.tupai_calculation_method;
					}

					if (res.data.shareUser) {
						//当前用户信息
						this.current_user_info = res.data.shareUser;
						if (res.data.shareUser.adviser_id) {
							this.current_user_info.identity = 1;
							this.current_user_info.identity_id =
								res.data.shareUser.adviser_id;
						} else if (res.data.shareUser.agent_id) {
							this.current_user_info.identity = 2;
							this.current_user_info.identity_id = res.data.shareUser.agent_id;
						}
					}
					if (res.data.share_user) {
						//分享者信息
						this.sharers_info = res.data.share_user;
						if (res.data.share_user.adviser_id) {
							this.sharers_info.identity = 1;
						} else if (res.data.share_user.agent_id) {
							this.sharers_info.identity = 2;
						}
					}
					this.$ajax.get("member/checkUserStatus", {}, (res) => {
						if (res.data.code !== 1) {
							this.$store.state.user_login_status = res.data.status;
							if (this.$store.state.user_login_status==1){
								this.type = "denglu";
								uni.setStorageSync('backUrl', window.location.href)
								this.$store.state.updatePageData = true;
								this.showDingyuePop();
							}else if (this.$store.state.user_login_status==2){
								this.type='bangshouji'
								this.$store.state.updatePageData=true
								this.showDingyuePop()
							}
						}
					});

					if (res.data.code === 1) {
						if (res.data.scale) {
							this.scale = res.data.scale;
						}
						this.currentSchool = res.data.detail;
						// 生成地图标记点
						this.school_point = [
							{
								id: this.currentSchool.id,
								school_id: this.currentSchool.id,
								latitude: this.currentSchool.lat,
								longitude: this.currentSchool.lng,
								polygon: this.currentSchool.tupai_range,
								width: 32,
								height: 32,
								callout: {
									content: this.currentSchool.tdbh + "示意图",
									padding: 10,
									borderRadius: 8,
								},
							},
						];
						this.allowRegionchangeGetData = false;
						setTimeout(() => {
							this.allowRegionchangeGetData = true;
						}, 500);
						this.mapcenter = {
							latitude: this.currentSchool.lat,
							longitude: this.currentSchool.lng,
						};
						const polygonData = this.school_point[0].polygon;
						this.setPolygons(polygonData);
					} else {
						this.school_point = [];
						this.polygons = [];
						uni.showToast({
							title: res.data.msg,
							icon: "none",
						});
					}
					if (res.data.share) {
						this.share = res.data.share || {};
					}else {
                        this.share={
                            title:this.currentSchool.tdbh,
                            content:this.currentSchool.tdwz,
                            pic:''
                        }
                    }
                    this.share.link=this.getShareLink()
                    this.getWxConfig()
					uni.hideLoading();
					setTimeout(() => {
						this.allowRegionchangeGetData = true;
						this.baidu_map_all_getdata = true;
					}, 500);
				},
				(err) => {
					uni.hideLoading();
					console.log(err);
					setTimeout(() => {
						this.allowRegionchangeGetData = true;
						this.baidu_map_all_getdata = true;
					}, 500);
				}
			);
		},
		showDingyuePop() {
			this.$refs.dingyue.showPopup();
        },
        showSharePop(){
			this.getShortLink()
			this.$refs.show_share_pop.show()
		},
		toLogin() {
			this.$refs.dingyue.hide();
			this.$navigateTo("/user/login/login");
		},
		toBind(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/bind_phone/bind_phone")
		},
        // 获取分享链接
		getShareLink(){
			let time =parseInt(+new Date()/1000)
			let link = window.location.href
			if (this.current_user_info.identity) { //当前用户是 置业顾问或者经纪人  
					link = `${window.location.origin}${window.location.pathname}?id=${this.params.id}&shareId=${this.current_user_info.identity_id}&type=${this.current_user_info.identity}&f_time=${time}`
			}
			return link
		},
		getShortLink(){
            this.link=this.getShareLink()
            this.$ajax.get('build/shortUrl.html', {page_url:this.link }, res=>{
                if(res.data.code === 1){
					this.link = res.data.short_url
                }
            })
        },
		// 复制分享链接
		copyLink(){
			this.show_share_tip=true
			// this.copyText(this.getShareLink(), ()=>{
			// 	uni.showToast({
			// 	title: '复制成功,去发送给好友吧',
			// 	icon: 'none'
			// 	})
			// })
		},
		// 复制分享内容
		showCopywriting(){
			console.log("复制内容")
			let mianji=this.showMushu==1?(this.currentSchool.mushu+"亩"): (this.currentSchool.mianji+"m²")
			const content = `【地块编号】${this.currentSchool.tdbh}\n【开发公司】${this.currentSchool.cjdw}\n【地块位置】${this.currentSchool.tdwz}m²\n【土地面积】${mianji}\n【起拍价】${this.currentSchool.qishijia}\n【链   接】${this.getShareLink()}`
			this.copyText(content, ()=>{
					uni.showToast({
					title: '复制成功,去发送给好友吧',
					icon: 'none'
					})
			})
		},
		// 复制内容
		copyText(cont, callback) {
			let oInput = document.createElement('textarea')
			oInput.value = cont
			document.body.appendChild(oInput)
			oInput.select() // 选择对象;
			oInput.setSelectionRange(0, oInput.value.length);
			document.execCommand('Copy') // 执行浏览器复制命令
			oInput.blur()
			oInput.remove()
			if(callback) callback()
		},
		setPolygons(polygonData) {
			if (!isArray(polygonData)) {
				this.polygons = [];
				return;
			}
			const color = "#f65354";
			this.polygons = polygonData.map((items) => {
				const points = items.paths.map((item) => {
					return {
						latitude: parseFloat(item.latitude),
						longitude: parseFloat(item.longitude),
					};
				});
				return {
					id: items.id,
					points,
					strokeWidth: 4,
					strokeColor: color,
					fillColor: `${color}33`,
				};
			});
		},
		onRegionchange(e) {
			if (!this.allowRegionchangeGetData || !this.baidu_map_all_getdata) {
				return;
			}
			// #ifdef MP-WEIXIN
			if (e.causedBy === "gesture" || e.causedBy === "update") {
				return;
			}
			// #endif
			const handle = function() {
				// #ifdef H5
				this.params.lat = e.lat;
				this.params.lng = e.lng;
				// this.getData();
				// #endif

				// #ifndef H5
				// 获取中心点经纬度
				this.map.getCenterLocation({
					success: (res) => {
						this.params.lat = res.latitude;
						this.params.lng = res.longitude;
						// #ifdef MP-BAIDU
						this.baidu_map_all_getdata = false;
						// #endif
						this.mapcenter = {
							latitude: res.latitude,
							longitude: res.longitude,
						};
						// this.getData();
					},
					fail: (err) => {
						console.log(err);
					},
				});
				// #endif
			};
			if (this.timer) {
				clearTimeout(this.timer);
			}
			this.timer = setTimeout(() => {
				handle.call(this);
			}, 300);
		},
	},
	onShareAppMessage() {
		if (this.share) {
			return {
				title: this.share.title || "",
				content: this.share.content || "",
				imageUrl: this.share.pic ? formatImg(this.share.pic, "w_6401") : "",
			};
		}
	},
};
</script>

<style scoped lang="scss">
.fixed-top {
	width: 100%;
	position: fixed;
	top: var(--window-top);
	z-index: 100;
	background-color: #fff;
}

.map-box {
	// padding-top: 160rpx;
	position: absolute;
	width: 100%;
	top: 0;
	bottom: 400rpx;
	transition: 0.3s;
	.qqmap {
		width: 100%;
		height: 100%;
	}
	.cate_box {
		width: 64rpx;
		position: fixed;
		right: 30rpx;
		bottom: 320rpx;
		border-radius: 32rpx;
		overflow: hidden;
		box-shadow: 0 5rpx 8rpx #ccc;
		z-index: 8;
		background-color: #fff;
		.cate {
			width: 58rpx;
			padding: 10rpx 15rpx;
			margin: 3rpx;
			line-height: 1.1;
			font-size: 28rpx;
			border-radius: 29rpx;
			align-items: center;
			justify-content: space-around;
			color: #666;
			&.active {
				color: #fff;
				background: linear-gradient(to bottom, #ff6834, #ff8e09);
			}
			&.cover-active {
				color: #fff;
				background-color: #ff6834;
			}
		}
	}
}
.yushou-title {
	padding: 24rpx 0;
	font-size: 22rpx;
	color: #999999;
	&.flex-grow {
		align-items: center;
	}

	.yushou-title-left {
		flex: 1;
	}
	.yushou-area {
		margin-left: auto;
	}
	.share_icon {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 24rpx;
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		background-color: rgba(0, 0, 0, 0.5);
	}
	.red {
		color: #f00;
	}
}
.xiangmumingcheng {
	font-size: 36rpx;
	color: #333;
	font-weight: 600;
}

.yushou-info {
	position: absolute;
	bottom: 0;
	width: 100%;
	padding: 24rpx 48rpx;
	border-radius: 48rpx 48rpx 16rpx 16rpx;
	background: #fff;
	box-sizing: border-box;
}
.data-box {
	position: relative;
	margin-top: 60rpx;
	.gongsimingcheng {
		position: absolute;
		left: 50%;
		transform: translateX((-50%));
		color: #fff;
		top: -50rpx;
		white-space: nowrap;
		padding: 10rpx 20rpx;
		border-radius: 20px;
		background-image: linear-gradient(90deg, #fb656a, #fbac65);
		font-size: 28rpx;
		margin: 24rpx 0;
	}
	&.bg-grey {
		background: #f8f8f8;
		&:last-child {
			margin-right: 0;
		}
	}
	padding: 32upx 0;
	background-color: #ffffff;
	.box {
		padding: 24rpx 0;
	}

	.data-title {
		margin-bottom: 4upx;
		font-size: 26rpx;
		color: #666;
		&.mtop12 {
			margin-top: 4rpx;
			margin-bottom: 0;
		}
	}
	.data-data {
		font-size: 22rpx;
		// font-weight: bold;
		color: #666;
	}
	.data-datas {
		font-size: 32upx;
		font-weight: bold;
		color: #333;
	}
	.red {
		color: $uni-color-primary;
	}
}
.fenge {
	position: relative;
	&:after {
		content: "";
		position: absolute;
		top: 20%;
		bottom: 20%;
		right: 0;
		width: 1px;
		-webkit-transform: scaleX(0.5);
		transform: scaleX(0.5);
		background-color: $uni-border-color;
	}
}

.up_vip {
	width: 508rpx;
	height: 72rpx;
	line-height: 72rpx;
	background: #ff6735;
	color: #fff;
	border-radius: 50rpx;
	margin: 60rpx auto;
	font-size: 32rpx;
	background-image: linear-gradient(to left, #fb8a65, #ff6735);
}
// #ifndef H5
cover-view {
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}
.cate {
	display: block;
	white-space: pre-wrap;
}
.e-text {
	display: inline;
}

.item-box {
	flex-direction: row;
	overflow: hidden;
	padding: 30rpx;
	&.item-hover {
		background-color: $uni-bg-color-hover;
	}
	.image-box {
		width: 240rpx;
		height: 220rpx;
		border-radius: 10rpx;
		overflow: hidden;
		image {
			width: 100%;
			height: 100%;
		}
		cover-image {
			width: 100%;
			height: 100%;
		}
	}
	.info-box {
		margin-left: 32rpx;
		flex: 1;
		overflow: hidden;
		.content {
			flex-direction: row;
			align-items: center;
			background-color: initial;
			.info {
				flex: 1;
				margin-right: 20rpx;
			}
		}
		.title {
			flex-direction: row;
			align-content: flex-start;
			justify-content: space-between;
			.text-box {
				flex-direction: row;
				align-items: flex-end;
				margin-bottom: 20rpx;
				.text {
					flex: 1;
					line-height: 1.5;
					font-size: $uni-font-size-lg;
					overflow: hidden;
					text-overflow: ellipsis;
					-webkit-line-clamp: 1;
					-webkit-box-orient: vertical;
					display: -webkit-box;
					&.line-clamp_2 {
						-webkit-line-clamp: 2;
					}
				}
				.text2 {
					margin-bottom: 10rpx;
					margin-left: 10rpx;
					font-size: $uni-font-size-sm;
					color: #999;
				}
			}
		}
		.tags {
			display: inline-block;
			margin-bottom: 30rpx;
			.tag {
				margin-right: 8rpx;
				// #ifndef H5
				padding: 2rpx 18rpx;
				// #endif
				// #ifdef H5
				padding: 6rpx 18rpx;
				// #endif
				font-size: $uni-font-size-sm;
				line-height: 1;
				border-radius: 4rpx;
				flex-shrink: 0;
				color: $uni-color-primary;
				background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
			}
		}
		.desc {
			margin-bottom: 24rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			-webkit-line-clamp: 1;
			-webkit-box-orient: vertical;
			display: -webkit-box;
			font-size: $uni-font-size-base;
			color: #666;
		}
		.address {
			flex-direction: row;
			align-items: center;
			overflow: hidden;
			font-size: $uni-font-size-base;
			color: #666;
			.text {
				flex: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.distance {
				margin-left: 5rpx;
			}
		}
	}
}

cover-view.filter {
	transform: translateY(0);
}
cover-view.filter-list {
	display: block;
	white-space: pre-wrap;
}
cover-view.filter-item {
	display: inline-block;
}

.my-tag {
	display: inline-block;
	padding: 10rpx 18rpx;
	font-size: $uni-font-size-sm;
	line-height: 1;
	border-radius: 4rpx;
	flex-shrink: 0;
	color: $uni-color-primary;
	background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
	~ .my-tag {
		// margin-left: 20rpx;
	}
	&.success {
		color: $uni-color-success;
		background-color: rgba($color: $uni-color-success, $alpha: 0.1);
	}
	&.info {
		color: $uni-text-color-grey;
		background-color: rgba($color: $uni-text-color-grey, $alpha: 0.1);
	}
	&.danger {
		color: $uni-color-error;
		background-color: rgba($color: $uni-color-error, $alpha: 0.1);
	}
	&.small {
		border-radius: 3rpx;
		padding: 6rpx 14rpx;
		font-size: 22rpx;
	}
	&.big {
		padding: 10rpx 20rpx;
		border-radius: 8rpx;
		font-size: $uni-font-size-base;
	}
}
// #endif
// #ifdef MP-BAIDU
cover-view.cate_box .cate2 {
	height: 100rpx;
}
cover-view.cate_box .cate3 {
	height: 140rpx;
}
// #endif
</style>
