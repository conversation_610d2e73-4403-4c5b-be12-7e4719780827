{"name": "tfw_new", "version": "4.7.0", "private": true, "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "deploy:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build && node ./deploy/index.js", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": " cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js"}, "dependencies": {"@dcloudio/uni-app-plus": "^2.0.1-33920220208001", "@dcloudio/uni-h5": "^2.0.1-33920220208001", "@dcloudio/uni-helper-json": "^1.0.7", "@dcloudio/uni-i18n": "^2.0.1-33920220208001", "@dcloudio/uni-mp-360": "^2.0.1-33920220208001", "@dcloudio/uni-mp-alipay": "^2.0.1-33920220208001", "@dcloudio/uni-mp-baidu": "^2.0.1-33920220208001", "@dcloudio/uni-mp-jd": "^2.0.1-33920220208001", "@dcloudio/uni-mp-kuaishou": "^2.0.1-33920220208001", "@dcloudio/uni-mp-lark": "^2.0.1-33920220208001", "@dcloudio/uni-mp-qq": "^2.0.1-33920220208001", "@dcloudio/uni-mp-toutiao": "^2.0.1-33920220208001", "@dcloudio/uni-mp-vue": "^2.0.1-33920220208001", "@dcloudio/uni-mp-weixin": "^2.0.1-33920220208001", "@dcloudio/uni-quickapp-native": "^2.0.1-33920220208001", "@dcloudio/uni-quickapp-webview": "^2.0.1-33920220208001", "@dcloudio/uni-stat": "^2.0.1-33920220208001", "@vue/shared": "^3.2.30", "echarts": "^5.4.2", "flyio": "^0.6.14", "js-audio-recorder": "^1.0.6", "jweixin-module": "^1.6.0", "umtrack-wx": "^2.6.3", "uniapp-config": "^1.0.11", "vconsole": "^3.3.4", "video.js": "^8.0.4", "videojs-flash": "^2.2.1", "vue": "^2.6.10", "vue-jsonp": "^2.0.0", "vuex": "^3.6.2", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@dcloudio/uni-automator": "^2.0.1-33920220208001", "@dcloudio/uni-cli-i18n": "^2.0.1-33920220208001", "@dcloudio/uni-cli-shared": "^2.0.1-33920220208001", "@dcloudio/uni-migration": "^2.0.1-33920220208001", "@dcloudio/uni-template-compiler": "^2.0.1-33920220208001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.1-33920220208001", "@dcloudio/vue-cli-plugin-uni": "^2.0.1-33920220208001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.1-33920220208001", "@dcloudio/webpack-uni-mp-loader": "^2.0.1-33920220208001", "@dcloudio/webpack-uni-pages-loader": "^2.0.1-33920220208001", "@types/html5plus": "*", "@types/uni-app": "*", "@vue/cli-plugin-babel": "3.5.1", "@vue/cli-service": "^4.5.8", "babel-plugin-import": "^1.13.3", "cross-env": "^7.0.3", "eslint": "^6.8.0", "eslint-plugin-vue": "^6.1.2", "jest": "^25.5.4", "mini-types": "*", "miniprogram-api-typings": "^2.8.0-2", "node-sass": "^4.14.1", "postcss-comment": "^2.0.0", "regenerator-runtime": "^0.12.1", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.10", "webpack": "^4.40.3"}, "browserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}}