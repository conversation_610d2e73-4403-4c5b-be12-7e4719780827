<template>
<view class="page" @touchstart="firstPlayingAudio" :class="{pad_bottom:(showBottomBar&&type!==4),bgcolor:(type==3),tejia_mode:(type==3),taizhou_mode:(type==4),new_year_mode:(type==5||type==6)}" :style="{backgroundColor:backColorType4,backgroundImage:`url(${huawenBackground})`}" v-if = "show">
    <view class="audio-box" :class="{rotate:playing}" @click="switchAudio()" v-if="audio">
        <!-- <audio :src="audio" loop></audio> -->
        <my-icon type="yinyue" size="20" color="#fff"></my-icon>
    </view>
    <template v-if="type==1">
        <image class="min-img" :src="main_img | imgUrl('w_8601')" mode="widthFix"></image>
        <view style="padding:48rpx" v-if="content&&content_position===2">
        <!-- #ifndef MP -->
        <view  class="activity-content"  v-html="content"></view>
        <!-- #endif -->
        <!-- #ifdef MP -->
        <u-parse :html="content" @linkpress="navigate" :tag-style="tagStyle"></u-parse>
        <!-- #endif -->
        </view>
        <view class="build-list" :style="{marginTop:backColorType4?'80rpx':0}">
            <view class="build-box bottom-line" v-for="(build,idx) in build_list" :key="build.id">
                <view class="row-title" v-if='!backColorType4'>{{build_list.length>1?build.title:'项目详情'}}</view>
                <view class="build-item" @click="toBuild(build.id)">
                    <image class="img" :src="build.img | imgUrl('w_8001')" mode="aspectFill"></image>
                    <view class="info">
                        <view class="flex-box title-row">
                            <view class="title">{{build.title}}</view>
                            <!-- <view v-if="build.price&&build.price!='0'" class="price-box">约<text class="price">{{build.price}}</text>元/平方米</view> -->
                            <view class="price-box">{{build.price_type}}：<text class="price">{{build.build_price||build.price}}</text>{{build.price_unit}}</view>
                            <!-- <view v-else class="price-box"><text class="price">价格待定</text></view> -->
                        </view>
                        <view class="address">{{build.address}}</view>
                        <view class="labels">
                            <text class="label">{{build.leixing}}</text>
                            <text v-for="(label, index) in build.huxing" :key="index" class="label">{{label.shi+'室(约'+label.mianji+'m²)'}}</text>
                        </view>
                    </view>
                </view>
                <template>  <!--  修改显示状态  由 后台设置楼盘优惠显示改为全显示 没有设置楼盘优惠的话默认 线上预约专享，年底大优惠线上预约专享，年底大优惠  -->
                    <view class="row-title"  v-if='!backColorType4'>领取线上优惠</view>
                    <view class="activity flex-box">
                        <view class="act_left">
                            <view class="title">获取优惠</view>
                            <view class="sub_title" v-if="build.discount">{{build.discount}}</view>
                            <view class="sub_title" v-else >线上预约专享，年底大优惠线上预约专享，年底大优惠</view>
                        </view>
                        <view class="act_right" >
                            <text class="canyu" @click="toSubForme(build.id, build.groupCount)">预约优惠</text>
                        </view>
                    </view>
                </template>
                <view class="row-title" v-if="is_adviser==1&& build.advisers>0 &&!backColorType4">在线联系置业顾问</view>
                <!-- 新 -->
                <view class="adviser-list" v-if="is_adviser==1">
                    <view class="adviser-item flex-box" v-for="adviser in build.advisers" :key="adviser.id">
                        <view class="flex-box">
                            <image class="head-img" :src="adviser.prelogo | imgUrl('w_120')" mode="aspectFill"></image>
                            <view class="name">{{adviser.cname}}</view>
                        </view>
                        <view class="flex-box">
                            <view v-if ="switch_adviser_tel" @click="handleTel(adviser, build.id)" class="icon-box"><my-icon type="dianhua1" color="#f65354" size="26"></my-icon></view>
                            <view v-if="has_chat" @click="toChat(adviser.mid)" class="icon-box"><my-icon type="zixun" color="#f65354" size="27"></my-icon></view>
                        </view>
                        
                    </view>
                    <view class="show_more" v-if="build.showMore" @click="showMoreAdv(idx)">{{build.text}}</view>
                    
                </view>
            </view>
            <view :class="{'border-box-shaw':backColorType4}">
            <view v-if="content&&content_position===1" class="activity-content"  v-html="content"></view>
            </view>
            <view v-if ="is_bottom_signup" :class="{'border-box-shaw':backColorType4}"> 
                <view class="center-title">线上报名</view>
                <form class="form-box" @submit="formSubmit">
                    <view class="row form-row flex-box">
                        <view class="label">你的姓名</view>
                        <input name="name" maxlength="10" placeholder="请输入姓名" />
                    </view>
                    <view class="row form-row flex-box">
                        <view class="label">手机号</view>
                        <input name="tel" type="number" maxlength="11" placeholder="请输入手机号" />
                    </view>
                    <view class="btn-box">
                        <view class="agreement flex-box" >
                                <view class="check_box" @click="agree_agreement = !agree_agreement">
                                    <my-icons v-if="agree_agreement" type="ic_xuanze" color="#fb656a" size="36rpx"></my-icons>
                                    <text v-else class="no_checked"></text>
                                </view>
                                <view class="agreement_title">
                                    <text>我已阅读并同意</text>
                                    <text class="title" @click="$navigateTo('/user/agreement?type=policy')">《{{siteName||projectName}}隐私政策》</text>
                                </view>
                            </view>
                        <button formType="submit" class="default">立即报名</button>
                        
                    </view>
                    <view class="text-center" style="font-size: 24rpx;color: #999;padding-bottom:40rpx">您的信息将被严格保密，请准确填写</view>
                </form>
            </view>
        </view>
        
        

        <view class="flex-box bottom-bar top-line" v-if="showBottomBar"  :class="{'adv-share':shareUserInfo.adviser_id&&is_adviser==1&&is_open_adviser==1}">
           <template v-if ="shareUserInfo.adviser_id&&is_adviser==1&&is_open_adviser==1">
                <view class="adverser-info flex-row" @click ='toAdviserDetail'> 
                        <view class="adviser-header">
                            <image :src="shareUserInfo.prelogo|imgUrl('w_80')" alt=""></image>
                        </view>
                        <view class="adviser-info-con">
                            <view class="adviser-info-name">
                                {{shareUserInfo.cname}}
                            </view>
                            <view class="adviser-info-type">
                                置业顾问
                            </view>
                        </view>
                    </view>
                    
                    <view class="adv-btns flex-1 adv-ask" @click="askAdviser"><view>在线咨询</view></view>
                    <view class="adv-btns flex-1 adv-tel" @click ="telAdviser"><view>拨打电话</view></view>
            </template>
        <!-- <view class="flex-1 flex-box text-center to-buy top-line right-line" @click="toHome()" v-if="detail.use_middle_number==1">
            <my-icon type="home" color="#333333" size="22"></my-icon>
            <view>首页</view>
        </view> -->
        <template v-else >
        <!-- #ifdef H5 -->
        <view class="flex-3 flex-box text-center to-buy top-line right-line" @click="handleShare()">
            <my-icon type="fenxiang1" color="#333333" size="22"></my-icon>
        </view>
        <!-- #endif -->
        <!-- #ifdef MP -->
        <button open-type="share" class="flex-3 flex-box text-center to-buy top-line right-line">
            <my-icon type="fenxiang1" color="#333333" size="22"></my-icon>
        </button>
        <!-- #endif -->

        <view v-if="is_adviser==1" class="flex-4 flex-box text-center to-consu" @click="toAdviserList()">
            <view>置业顾问</view>
        </view>
        <view  v-if ="switch_adviser_tel" :class="is_adviser==1?'flex-4':'flex-8'" class="flex-box text-center to-tel" @click="tolTel()">
            <view>电话咨询</view>
        </view>
        </template>
        </view>
        <view class="share_mask" v-if="show_share" @click="show_share=false">
            <view class="icon-box"><image class="icon" src="https://images.tengfangyun.com/images/icon/share_tip_icon.png" mode="widthFix"></image></view>
            <!-- <view class="tip">点击右上角分享给好友或朋友圈</view> -->
        </view>
        <sub-form show_policy :default_check="agree_agreement" :groupCount="groupCount" :sub_type="sub_type" :sub_title="sub_title" :sub_content="sub_content" ref='sub_form' @onsubmit="handleSubForm"></sub-form>
    </template>
    <template v-if="type==2">
        <view class="img">
            <image class="min-img" :src="main_img | imgUrl('w_8601')" mode="widthFix"></image>
        </view>
        <!-- 中间模块 -->
        <view class="middle">
            <view class="show_block flex-box">
                <view class="show_block_infos flex-1">
                    <view class="num">{{block_info.house_count}}</view>
                    <view class="show_block_info">特价房源</view>
                </view>
                <view class="show_block_infos flex-1">
                    <view class="num">{{block_info.flow_number}}</view>
                    <view class="show_block_info">关注人数</view>
                </view>
                <view class="show_block_infos flex-1">
                    <view class="num">{{block_info.group_count}}</view>
                    <view class="show_block_info">报名人数</view>
                </view>


            </view>
            <view class="news-box flex-box">
                <!-- <image src="https://images.tengfangyun.com/images/icon/toutiao.png" mode=""></image> -->
                <swiper class="flex-1" :duration="300" :circular="true" :autoplay="true" :vertical="true">
                <swiper-item v-for="(news,index) in newsList" :key="index">
                    <navigator animation-type="pop-in" animation-duration="260"  hover-class="navigator-hover">
                    <view class="swiper-item">{{news}}</view>
                    </navigator>
                </swiper-item>
                </swiper>
            </view>
        </view>
        <!-- 列表 -->
        <view v-if="content&&content_position===2" class="activity-content" v-html="content"></view>
        <view class='youhui_list'>
            <view class="row-title" v-if="is_show_title">{{title}}</view>
            <view class="build-box" v-for="build in build_list" :key="build.id">
                
                <view class="build-item bottom-line" @click="toBuild(build.id)">
                    <view class="top flex-box">
                        <image class="youhui_img" :src="build.img | imgUrl('w_8001')" mode="aspectFill"></image>
                        <view class="info">
                            <view class="title-row">
                                <view class="title">{{build.title}}</view>
                                
                                <!-- <view v-else class="price-box"><text class="price">价格待定</text></view> --> 
                            </view>
                            <view class="address"><text style="margin:0 8rpx 0 0">{{build.typename | formatType}}</text>{{build.mianji}}</view>
                            <view class="price-box">{{build.price_type}}：<text class="price">{{build.build_price||build.price}}</text>{{build.price_unit}}</view>
                            <view class="attrs flex-box"><view class="hui">惠</view><view class="hui_info">{{build.discount}}</view></view>
                            <!-- <view class="labels">
                                <text class="label">{{build.leixing}}</text>
                                <text v-for="(label, index) in build.huxing" :key="index" class="label">{{label.shi+'室(约'+label.mianji+'m²)'}}</text>
                            </view> -->
                        </view>
                    </view>
                    <view class="bot flex-box">
                        <view class="baoming"><text>{{build.groupCount}}</text>人报名</view>
                        <view class="right flex-box">
                            <view v-if="is_adviser==1" class="tel" @click.prevent.stop="goCusList(build.id)">在线咨询</view>
                            <view class="bot_right flex-box">
                                <view class="guanzhu"><text>{{build.hot_points}}</text>人关注</view>
                                <view class="qiang" @click.prevent.stop="toSubForme(build.id,build.groupCount)">马上抢</view>    
                            </view>
                        </view>

                    </view>
                </view>
            </view>
            <view v-if="content&&content_position===1" class="activity-content" v-html="content"></view>
            <view class="flex-box bottom-bar top-line" v-if="showBottomBar">
                <view class="flex-4 flex-box text-center " @click="tolTel">
                    <view class= "tel_num">{{build_tel}}</view>
                </view>
                <view v-if ="switch_adviser_tel" class="flex-3 flex-box text-center to-tel to-tel-youhui" @click="tolTel()">
                    <view>电话咨询</view>
                </view>
            </view>

        </view>
        <sub-form show_policy :groupCount="groupCount" :sub_type="sub_type" :sub_mode="sub_mode" :sub_title="sub_title" :sub_content="sub_content" ref='sub_form' @onsubmit="handleSubForm"></sub-form>
    </template>
    <template v-if="type==3">
        <view class="img tejia_img">
            <image class="min-img" :src="main_img | imgUrl('w_8601')" mode="widthFix"></image>
        <!-- <image class="min-img" :src="main_img | imgUrl('w_8601')" mode="widthFix"></image> -->
        </view>
        <view class="img bmbtn"  @click='showPop=true;agree_agreement=true'>
            <image class="min-img bm-img" :src='imageUrl+"index_02.png"' mode="widthFix"></image>
        </view>
        <view class="img thTitle" >
            <image class="min-img th-img" :src='imageUrl+"title.jpg"' mode="widthFix"></image>
        </view>
        <view class="tehui" :style="{backgroundImage:`url(${tehuiBackground})`}">
            <view class="list">
                <view class="item" v-for="item in rec_build_list" :key="item.id"  @click="toBuild(item.id)">
					<view class="img">
						<!-- <view class="tag">河景美宅</view> -->
						<view class="pic"><image mode="" :src="item.img|imgUrl"></image></view>
						<view class="btmBox">
							<view class="tagTitle">惠</view>
							<view class="price"><text>{{item.discount}}</text></view>
						</view>
					</view>
					<view class="txt">
						<view class="houseName"><b>{{item.title}}</b></view>
						<view id="lp381" class="btn" @click.prevent.stop="baomingHasId(item.id)"><text>立即报名</text></view>
					</view>
                </view>
            </view>
        </view>
        <view class="tehuiBtm">
            <image class="min-img bm-img" :src="imageUrl+'tehuiBtm.jpg'" mode="widthFix"></image>
        </view>
        <view class="otherList" :style="{backgroundImage:`url(${otherBackground})`}">
            <view class="item" v-for ="item in build_list" :key ="item.id"  @click="toBuild(item.id)">
                <view class="topInfo">
                        <view class="pic">
                            <image :src="item.img|imgUrl" mode="widthFix" style="display: block;"></image>
                        </view>		
                        <view class="txt">
                            <h4>{{item.title}}</h4>
                            <view class="tagAndPrice">	
                                <view class="tags">
                                    <text>{{item.leixing}}</text>
                                    <text>{{item.areaname}}</text>
                                </view>	
                                <view class="price">
                                    <text>{{item.price_type}}</text>
                                    <span class="strong">{{item.build_price}}</span>
                                    <text>{{item.price_unit}}</text>
                                </view>
                            </view>
                            <view class="hui">
                                <text class="strong">惠</text>
                                <text class="hui_con">{{item.discount}}</text>
                            </view>		
                            <view class="add">{{item.address}}</view>		
                        </view>
                </view>	
                <view class="group">	
                    <view class="img" >
                        <view class="i"  v-for="(img, index) in item.avatars" :key="index">
                            <image mode="widthFix" :src="img|imgUrl" style="display: block;"></image>
                        </view>
                        <!-- <view class="other">…</view> -->
                        <view class="t">{{item.groupCount}}人已参与</view>	
                    </view>	
                    <view id="lpa502" class="btn"  @click.prevent.stop="baomingHasId(item.id)">
                        <span>立即报名</span>
                    </view>	
                </view>	
            </view>
        </view>
        <view class="OTHERbTM" style="margin-bottom: -32rpx;"> 
            <image class="min-img om-img" :src="imageUrl+'otherListbTM.jpg'" mode="widthFix"></image>
        </view>
        <!-- 推荐有奖 -->
        <view class="tuijianBtn" @click="showTuijian=true;agree_agreement=true"> 
            <image class="min-img tj-img" :src="imageUrl+'tuijian.jpg'" mode="widthFix"></image>
        </view>
        <!-- 置业顾问 -->
        <view class="guwen" v-if="is_adviser==1">
            
            <view class="guwenTitle"><image :src="imageUrl+'zixunshit.jpg'" mode="widthFix"></image></view>
            <view class="lp_index_c" :style="{backgroundImage:`url(${otherBackground})`}">
            <view class="swiper-box">
                <swiper class="banner" :indicator-dots="true" :circular="true"  :autoplay ="true" :current="swiperCurrent" :duration="300" indicator-active-color="#f65354" :display-multiple-items ="3">
                    
                    <swiper-item class="banner_item" v-for="adv in advList" :key ="adv.id" >
                        <view class="item">
                            <!-- 头像 -->
                            <view class="ico">
                                <image :src="adv.prelogo| imgUrl" mode ="widthFix"></image>
                            </view>
                            <view class="item_name">
                                <view class="strong">{{adv.cname}}</view>
                                <view class="tel" v-if ="switch_adviser_tel">{{adv.tel}}</view>
                            </view>
                            <view class="btn_box">
                                <view v-if ="switch_adviser_tel" class="item_tel btn" style="background:linear-gradient(294deg, #ff7e24, #f84c32 40%, #ed0246)"  @click="handleTel(adv,'')" >
                                    <my-icon type="dianhua" size="20"  color="#fff"></my-icon>
                                </view>
                                <view class="item_chat btn" @click="toChat(adv.mid)"  >
                                    <my-icon type="weixin3" size="20" color="#fff"></my-icon>
                                </view>

                            </view>

                        </view> 
                        
                    </swiper-item>
                </swiper>
			
            </view>
            </view>
        </view>
        <view class="baomingBox">
			<view class="t"><image mode="widthFix" style="width:100%" :src="imageUrl+'baomingt.jpg'"></image></view>
			<view class="c" :style="{backgroundImage:`url(${tehuiBackground})`}">
                <!-- <form  @submit="tejiaBaoming()"> -->
                    <view class="item"><input class="inp" type="text" maxlength="10" v-model ="params.name" name="myname" value="" placeholder="请输入您的姓名"></view>
                    <view class="item"><input class="inp" type="number" maxlength="11" v-model ="params.tel" name="tel" placeholder="请输入您的电话" value=""></view>
                    <view class="baomingbtn">
                        <view class="agreement mbtm0 flex-box">
                            <view class="check_box" @click="agree_agreement = !agree_agreement">
                                <my-icons v-if="agree_agreement" type="ic_xuanze" color="#fb656a" size="36rpx"></my-icons>
                                <text v-else class="no_checked"></text>
                            </view>
                            <view class="agreement_title">
                                <text>我已阅读并同意</text>
                                <text class="title" @click="$navigateTo('/user/agreement?type=policy')">《{{siteName||projectName}}隐私政策》</text>
                            </view>
                        </view>
                        <button class ="baomingbtn_btn"  @click="tejiaBaoming()">立即报名</button>
                    </view>
                <!-- </form> -->
			</view>
			<view class="baomingbtm"><image class="mini-img" mode ="widthFix" style="width:100%;display:block;" :src="imageUrl+'baomingbtm.jpg'"></image></view>
		</view>
        <view class="footer">{{siteName}}  {{siteUrl}}</view>
        <view class="BtmMenu">
			<view class="BtmMenuitem" @click="goBuildList">
			
					<view class="ico"><my-icon type="jiudian" color="#fff" size="24"></my-icon></view>
					<view class="name">更多楼盘</view>
			</view>

			<view class="BtmMenuitem" @click="baomingHasId()">
					<view class="ico"><my-icon type="huodongxiangqu" color="#fff" size="24"></my-icon></view>
					<view class="name">我要报名</view>
	
			</view>
			<view class="BtmMenuitem" @click="tolTel">
				
					<view class="ico"><my-icon type="kefu" color="#fff" size="24"></my-icon></view>
                    <view class="name">咨询热线</view>
	
			</view>
		</view>
        <view class="showBMpop" v-if ="showPop">
			<view class="showBMpopInner">
				<view class="t"><image :src="imageUrl+'bm_t.png'" alt="" mode="widthFix" style="width:100%"></image></view>
				<view class="c">
					<view class="item"><input type="text" class="inp" maxlength="10" v-model ="params.name" name="myname" value="" placeholder="请输入您的姓名"></view>
					<view class="item"><input type="number" maxlength="11" class="inp" name="tel" v-model ="params.tel" placeholder="请输入您的电话" value=""></view>
					<view class="baomingbtn">
                        <view class="agreement flex-box">
                            <view class="check_box" @click="agree_agreement = !agree_agreement">
                                <my-icons v-if="agree_agreement" type="ic_xuanze" color="#fb656a" size="36rpx"></my-icons>
                                <text v-else class="no_checked"></text>
                            </view>
                            <view class="agreement_title">
                                <text>我已阅读并同意</text>
                                <text class="title" @click="$navigateTo('/user/agreement?type=policy')">《{{siteName||projectName}}隐私政策》</text>
                            </view>
                        </view>
                        <button @click="tejiaBaoming">立即报名</button>
                        
                    </view>
				</view>
				<view class="line"></view>
				<view class="close" @click ='close'><image :src="imageUrl+'close.png'"></image></view>
			</view>
		</view>
        <view class="showTuijian" v-if ="showTuijian">
			<view class="showTuijianWrap">
				<view class="tuijianPop" :style="{backgroundImage:`url(${tuijianBackground})`}">
					<view class="close"  @click="showTuijian=false;agree_agreement=true"><image mode ="widthfix" :src="imageUrl+'close.png'"></image></view>
					<view class="tuijianForm">
						<view class="item"><label>姓名：</label><input name="myname" v-model="tuijian.name" maxlength="10" type="text" placeholder="请输入您的姓名"></view>
						<view class="item"><label>电话：</label><input name="tel" v-model="tuijian.tel" type="number" maxlength="11" placeholder="请输入您的电话"></view>
						<view class="item"><label>被推荐人：</label><input name="tuijian" v-model="tuijian.des" type="text" placeholder="被推荐人的姓名和电话"></view>
						<view class="tuijianBtn">
                            <button @click="subTuijian"></button>
                            <view class="agreement flex-box" >
                                <view class="check_box" @click="agree_agreement = !agree_agreement">
                                    <my-icons v-if="agree_agreement" type="ic_xuanze" color="#fb656a" size="36rpx"></my-icons>
                                    <text v-else class="no_checked"></text>
                                </view>
                                <view class="agreement_title">
                                    <text>我已阅读并同意</text>
                                    <text class="title" @click="$navigateTo('/user/agreement?type=policy')">《{{siteName||projectName}}隐私政策》</text>
                                </view>
                            </view>
                        </view>
					</view>
				</view>
			</view>
		</view>
    </template>
    <template v-if="type==4">
        <view class="header">
            <image class="top-img" :src ="main_img | imgUrl('w_6401')" mode ="widthFix"></image>
            <view class="tellinputbox">
				<view class="txt1" :style ="{color:titleColorType4}">省钱买好房</view>
				<view class="inputborder flex-row">
					<span class="flex-row"><text>手机号</text><b>*</b>：</span>
					<input type="number" placeholder="请输入手机号" v-model ="type4Tel" class="input" id="subscribePhone" name="subscribePhone">
				</view>
				<view class="code flex-row" v-if ="login_status != 3">
					<view class="inputborder">
						<input type="text" placeholder="请输入验证码" class="input" v-model ="type4Code" id="dyfydtcode" name="code">
					</view>
                    <!-- <view class="send-code" :class="sending?'disable':''" @click="getVerify()">{{time?time+'s':'获取验证码'}}</view> -->
					<button class="codebutton" :class="sending?'disable':''"  @click ="getVerify">{{time?time+'s':'获取验证码'}}</button>
				</view>
                <view class="agreement type4 flex-box">
                    <view class="check_box" @click="agree_agreement = !agree_agreement">
                        <my-icons v-if="agree_agreement" type="ic_xuanze" color="#fb656a" size="36rpx"></my-icons>
                        <text v-else class="no_checked"></text>
                    </view>
                    <view class="agreement_title">
                        <text>我已阅读并同意</text>
                        <text class="title" @click="$navigateTo('/user/agreement?type=policy')">《{{siteName||projectName}}隐私政策》</text>
                    </view>
                </view>
				<button class="telbutton" @click="subFormeType4('',0)">订阅房源动态</button>
				<view class="txt2">订阅后可获得房源详情，优惠降价提前知悉</view>
			</view>
        </view>
        <view class="navbox" v-if="showBottomBar">
            <my-grid @click="toNav" :options="options" column-num="4" :fontSize="14" :show-border="false" ></my-grid>
		</view>
        <view class="buildwrap">
            <view class="buildtips1" :style ="{color:titleColorType4}">— 高性价比楼盘热销榜 —</view>
            <view class="tagbox">
                <text :style="(titleColorType4&&backColorType4)?('border:2rpx solid '+titleColorType4+';color:'+titleColorType4):'background: #4275ac;'">海量房源</text><text :style="(titleColorType4&&backColorType4)?('border:2rpx solid '+titleColorType4+';color:'+titleColorType4):'background: #4275ac;'">真实底价</text><text :style="(titleColorType4&&backColorType4)?('border:2rpx solid '+titleColorType4+';color:'+titleColorType4):'background: #4275ac;'">真实成交价</text>
            </view>
            <view class="builditem" v-for ="(build,index) in build_list" :key ="build.id">
				<view class="b-img" @click="toBuild(build.id)">
                    <image :src="build.img" mode ="widthFix" style="display: inline;"></image>
                    <!-- 咨询 电话联系  楼盘名称  价格 -->
                    <view class="b-data">
                        <p class="b-count flex-row">
							<view>
                                本月已有<br>{{build.groupCount}}人关注
                            </view>
                            <view  class ="b-tell flex-row" @click.stop.prevent="callBuild(build)">
                                <my-icon type="dianhua1" color ="#ffffff" ></my-icon>
                                <text class ="tell-title">咨询热线</text>
                            </view>
                        </p>
						<p class="b-title">
                            <span class="b-price">
								{{build.price_type}}{{build.build_price}}{{build.price_unit}}起
							</span>
                            <b>{{build.title}}</b>
                        </p>
					</view>
						<!-- 本期卖点根据逗号分隔  绝对定位 据中显示-->
                    <p class="salepoint">
                        <!-- <span v-for ="(label,idx) in build.labels" :key="idx">{{label}}</span> -->
                        <!-- <span></span> -->
                    </p>
				</view>
				<p class="b-intro long-intro">
					<span>地址：</span>
						{{build.address}}
				</p>
				<p class="b-intro">
					<span>楼盘优势：</span> {{build.label}}
				</p>
				<p class="b-intro">
					<span>主力户型：</span>
						<block v-for="(huxing,i) in build.huxing" :key="i">
                            {{huxing.title}}({{huxing.count}}) <span v-if ="i<(build.huxing.length-1)">、</span>  
                        </block>
				</p>
				<p class="b-intro">
					<span>建筑面积：</span>{{build.jzsize}}
				</p>
				<view class="tellinputbox">
					<view class="inputborder flex-box">
						<span>楼盘名称<b>*</b>：</span>
						<select class="build-select" v-model ="build.bid" @change ="changeSelectBuild(build)">
							<option value="0">---请选择---</option>
                            <option :value ="item.id" v-for ="(item) in buildsList" :key ="item.id" :selected ="item.id == build.id">{{item.name}}</option>
						</select>
					</view>
					<view class="inputborder">
						<span class="flex-row"> <text>手机号</text><b>*</b>：</span>
						<input type="number" name="tel" placeholder="请输入手机号" v-model ="build.signTel" class="input">
					</view>
                    <view class="agreement type4 flex-box">
                        <view class="check_box" @click="changeAgreement(build.agree_agreement,index)">
                            <my-icons v-if="build.agree_agreement" type="ic_xuanze" color="#fb656a" size="36rpx"></my-icons>
                            <text v-else class="no_checked"></text>
                        </view>
                        <view class="agreement_title">
                            <text>我已阅读并同意</text>
                            <text class="title" @click="$navigateTo('/user/agreement?type=policy')">《{{siteName||projectName}}隐私政策》</text>
                        </view>
                    </view>
					<button class="telbutton" @click="subFormeType4(build,1,index)">获取最新报价</button>
				</view>
			</view>
            <view class="tellinputbox tellinputbox2">
				<view class="txt1">找不到满意的楼盘？<br>填写信息，为您精确匹配房源</view>
                <view class="inputborder flex-row" @click ="goSelect">
					<span class="flex-row"> <text>意向楼盘</text><b>*</b>：</span>
					<input type="tel" disabled placeholder="请选择楼盘" v-model ="buildNameType4" class="input" id="subscribePhone" name="subscribePhone">
				</view>
                <view class="inputborder flex-row">
					<span class="flex-row"> <text>姓名</text><b>*</b>：</span>
					<input type="tel" placeholder="请输入姓名" v-model ="userNameType4" class="input" id="subscribePhone" name="subscribePhone">
				</view>
				<view class="inputborder flex-row">
					<span class="flex-row"><text>手机号</text><b>*</b>：</span>
					<input type="number"  placeholder="请输入手机号" v-model ="moreBuildTelType4" class="input" id="subscribePhone" name="subscribePhone">
				</view>
				<view class="agreement type4 flex-box">
                    <view class="check_box" @click="agree_agreement = !agree_agreement">
                        <my-icons v-if="agree_agreement" type="ic_xuanze" color="#fb656a" size="36rpx"></my-icons>
                        <text v-else class="no_checked"></text>
                    </view>
                    <view class="agreement_title">
                        <text>我已阅读并同意</text>
                        <text class="title" @click="$navigateTo('/user/agreement?type=policy')">《{{siteName||projectName}}隐私政策》</text>
                    </view>
                </view>
				<button class="telbutton" @click="subFormeType4('',2)">立即匹配房源</button>
				<!-- <view class="txt2">订阅后可获得房源详情，优惠降价提前知悉</view> -->
			</view>
        </view>
        <view class="footer-type4" :style="{'color':titleColorType4}">本页面由{{siteName}}提供</view>
        <view class="footer-type4" :style="{'color':titleColorType4}">客服电话：{{siteTel}}</view>
        <myPopup ref='select_build'>
            <view style="background-color:#fff">
            <view class="top-box flex-row">
                <search @input="inputValue" @confirm="searchBuild"  placeholder="请输入楼盘名称"></search>
                <view class="search"  @click="searchBuild">
                <text>搜索</text>
                </view> 
            </view>
            <scroll-view scroll-y class="buildAll" @touchmove.stop.prevent="stopMove" @scrolltolower="loadLazy">    
                <view class="uni-list">
                <radio-group @change="radioChange">
                    <label class="uni-list-cell uni-list-cell-pd flex-box" v-for="(item, index) in buildListSearch" :key="item.id">
                    <view>
                        <radio :value="''+item.id" :checked="index === current" />
                    </view>
                    <view>{{item.title}}</view>
                    </label>
                </radio-group>
                </view>
                <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
            </scroll-view>
            </view>
        </myPopup>
        <view class="verify_block" v-if="show_verify">
            <drag-verify ref="verify" :verify_img="verify_img" :verify_fail="verify_fail" :verify_success="verify_success" @ondragend="onDragEnd" @onrefreshend="onRefreshEnd" @onrefresh="onRefresh"></drag-verify>
        </view>
        <view class="footer-type4" :style="{'color':titleColorType4}">本页面由{{siteName}}提供</view>
        <view class="footer-type4" :style="{'color':titleColorType4}">客服电话：{{siteTel}}</view>
    </template>
    <template v-if ="type == 5 ||type == 6">
        <view class="img">
            <!-- <image class="min-img" :src="main_img | imgUrl('w_8601')" mode="widthFix"></image> -->
            <image class="min-img" :src="main_img | imgUrl('w_6401')" mode="widthFix"></image>
            

        </view>
        
        <view class="y-content"  >
            <!-- 倒计时 -->

                <view class="time-rever" v-if ="currentLeftTime>0">
                
                    <view class="time-rever-title">
                        <image :src="'/activity/typeBg/newYear/timeBgTop1.png'|formatImgUrl" mode="widthFix"></image>
                    </view>
                    <view class="time-rever-content flex-row" :style="{backgroundImage:`url(${middleUrl})`}">
                        <span v-if="day!=0" class="item-bg">{{day}}</span>
                        <span v-if ="day!=0">天</span>
                        <span class="item-bg">{{hours<10?'0'+hours:hours}}</span>
                        <span>:</span>
                        <span class="item-bg">{{minutes<10?'0'+minutes:minutes}}</span>
                        <span>:</span>
                        <span class="item-bg">{{seconds<10?'0'+seconds:seconds}}</span>
                    </view>
                    

                </view>
                <view class="jianju time-rever" v-if ="currentLeftTime>0" :style="{backgroundImage:`url(${jianjuBackground})`}">
                    <view class="time-rever-title" >
                        <image :src="'/activity/typeBg/newYear/bottom1.png'|formatImgUrl" mode="widthFix"></image>
                        <!-- <span>倒计时</span> -->
                    </view>

                    

                </view>

                <!-- 新年优惠 -->
                <view class="time-rever new-year" v-if ="youhuiTitle">
                    <view class="time-rever-title" >
                        <!-- <image :src="'/activity/typeBg/newYear/youhuiBgTop1.png'|formatImgUrl" mode="widthFix"></image> -->
                        <image class="image_border" src="../../static/icon/shangxian.png" mode="widthFix" />
                        <view v-if="activity_label_text" class="iamge_text">{{activity_label_text}}</view>
                        <image v-else class="image_title" :src="activity_label_img | imgUrl('w_240')" mode="widthFix" />
                    </view>
                    <view class="new-year-content flex-row" :style="{backgroundImage:`url(${middleUrl})`}">
                        <view class="new_year_left flex-1">
                            <view class="new_year_left_title">{{youhuiTitle}}</view>
                        </view>
                        <view class="new_year_right" @click ="showBaomingType5()">
                            <view class ="getHui">立即领取</view>
                        </view>
                    </view>

                </view>
                <view class="jianju time-rever"  v-if ="youhuiTitle" :style="{backgroundImage:`url(${jianjuBackground})`}">
                    <view class="time-rever-title">
                        <image :src="'/activity/typeBg/newYear/bottom1.png'|formatImgUrl" mode="widthFix"></image>
                        <!-- <span>倒计时</span> -->
                    </view>
                    

                </view>
                <!-- 视频 -->
                <view class="time-rever new-year"  v-if ="(videoUrl||videoUrlExt)" >
                    <view class="time-rever-title" >
                        <!-- <image :src="'/activity/typeBg/newYear/zhufuBgTop1.png'|formatImgUrl" mode="widthFix"></image> -->
                        <image class="image_border" src="../../static/icon/shangxian.png" mode="widthFix" />
                        <view v-if="video_label_text" class="iamge_text">{{video_label_text}}</view>
                        <image v-else class="image_title" :src="video_label_img | imgUrl('w_240')" mode="widthFix" />
                    </view>
                    <view class="new-year-content new-year-video flex-row" :style="{backgroundImage:`url(${middleUrl})`}" :class  ="{'showVideo':showVideo}">
                        <cover-view class="video_box">
                            <video  v-if ='videoUrl' :src="videoUrl" @play ="playVideo"  @error="videoErrorCallback" :poster="videoUrl | imgUrl('w_6401')" controls x5-playsinline></video>
                            <iframe width='100%' :src="videoUrlExt" v-if ="videoUrlExt" frameborder="0"></iframe>
                        </cover-view>
                    </view>

                </view>
                <view class="jianju time-rever" v-if = "(videoUrl||videoUrlExt)" :style="{backgroundImage:`url(${jianjuBackground})`}">
                    <view class="time-rever-title" >
                        <image :src="'/activity/typeBg/newYear/bottom1.png'|formatImgUrl" mode="widthFix"></image>
                    </view>

                    

                </view>

                <!-- 品牌项目-->
                <view class="time-rever new-year" v-if ="build_list.length>0">
                    <view class="time-rever-title" v-if ="build_list.length>0">
                        <!-- <image :src="'/activity/typeBg/newYear/xiangmuBgTop1.png'|formatImgUrl" mode="widthFix"></image> -->
                        <image class="image_border" src="../../static/icon/shangxian.png" mode="widthFix" />
                        <view v-if="project_label_text" class="iamge_text">{{project_label_text}}</view>
                        <image v-else class="image_title" :src="project_label_img | imgUrl('w_240')" mode="widthFix" />
                    </view>
                    <view class="new-year-content flex-row" :style="{backgroundImage:`url(${middleUrl})`}">
                        <view class="build_box" v-if ="build_list.length<2">
                            <block v-for ="build in build_list" :key ="build.id">
                            <view class="build flex-row"  @click="toBuild(build.id)">
                                <view class="build-img">
                                    <image :src="build.img | imgUrl('w_240')" mode="widthFix"></image>
                                </view>
                                <view class="build-info">
                                    <view class="build-title">
                                        {{build.title}}
                                    </view>
                                    <view class="build-labels">
                                        <span v-for ="(labels,idx) in build.cates" :key ="idx">{{labels}}</span>
                                        <!-- <span>花园阳光房</span> -->
                                    </view>
                                    <view class="build-price">
                                        <text class="build-price-con">{{build.build_price}}</text>
                                        <text class="build-unit">{{build.price_unit}}</text>
                                    </view>
                                    <view class="build-youhui" @click.prevent.stop="showBaomingType5($event,build)">
                                        获取优惠
                                    </view>
                                </view>
                            </view>
                            <view class="youhui-con flex-row" v-if ="build.discount">
                                <text class="hui-con-hui">惠</text>
                                <text class="">{{build.discount}}</text>
                            </view>
                        </block>

                        </view>
                        <view class="build_box_more flex-row" v-if ="build_list.length>1">
                            <view class="builds" v-for ="build in build_list" :key ="build.id" @click ="toBuild(build.id)">
                                <view class="builds-img">
                                    <image :src="build.img | imgUrl('w_240')" mode="widthFix"></image>
                                </view>
                                <view class="build-info">
                                    <view class="build-title">
                                        {{build.title}}
                                    </view>
                                    <view class="build-labels">
                                        <span v-for ="(labels,idx) in build.cates" :key ="idx">{{labels}}</span>
                                    </view>
                                    <view class="build-price">
                                        <text class="build-price-con">{{build.build_price}}</text>
                                        <text class="build-unit">{{build.price_unit}}</text>
                                    </view>
                                    <view class="youhui-con flex-row" v-if ="build.discount">
                                        <text class="hui-con-hui">惠</text>
                                        <text class="">{{build.discount}}</text>
                                    </view>
                                    <view class="build-youhui" @click.prevent.stop="showBaomingType5($event,build)">
                                        获取优惠
                                    </view>
                                </view>
                            </view>

                        </view>
                    </view>
                </view>

                <view class="jianju time-rever" :style="{backgroundImage:`url(${jianjuBackground})`}">
                    <view class="time-rever-title" v-if ="build_list.length>0">
                        <image :src="'/activity/typeBg/newYear/bottom1.png'|formatImgUrl" mode="widthFix"></image>
                    </view>
                </view>
                <!-- 单个楼盘带出当前楼盘的置业顾问 -->
                <view class="time-rever new-year new-year-adviser"  v-if ="build_list.length==1&&is_adviser==1&&new_year_advisers.length>0&&!shareUserInfo.adviser_id">
                    <view class="time-rever-title" >
                        <image :src="'/activity/typeBg/newYear/guwenTopBg.png'|formatImgUrl" mode="widthFix"></image>
                    </view>
                    <view class="new-year-content flex-row" :style="{backgroundImage:`url(${middleUrl})`}">
                        <view class="adviser-list" v-if="is_adviser==1">
                            <view class="adviser-item flex-box" v-for="adviser in new_year_advisers" :key="adviser.id">
                                <view class="flex-box">
                                    <image class="head-img" :src="adviser.prelogo | imgUrl('w_120')" mode="aspectFill"></image>
                                    <view class="name">{{adviser.cname}}</view>
                                </view>
                                <view class="flex-box">
                                    <view v-if ="switch_adviser_tel" @click="handleTel(adviser, build_list[0].id)" class="icon-box"><my-icon type="dianhua1" color="inherit" size="26"></my-icon></view>
                                    <view v-if="has_chat" @click="toChat(adviser.mid)" class="icon-box"><my-icon type="zixun" color="inherit" size="27"></my-icon></view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <!-- <view class="time-rever-title">
                        <image :src="'/activity/typeBg/newYear/bottom1.png'|formatImgUrl" mode="widthFix"></image>
                    </view> -->

                </view>
                <view class="jianju time-rever"  v-if ="build_list.length==1&&is_adviser==1&&new_year_advisers.length>0&&!shareUserInfo.adviser_id" :style="{backgroundImage:`url(${jianjuBackground})`}">
                    <view class="time-rever-title">
                        <image :src="'/activity/typeBg/newYear/bottom1.png'|formatImgUrl" mode="widthFix"></image>
                        <!-- <span>倒计时</span> -->
                    </view>
                    

                </view>

                <!-- 富文本 -->
                <view class="time-rever new-year richTextarea"  v-if ="content">
                    <view class="time-rever-title" >
                        <image :src="'/activity/typeBg/newYear/richText1.png'|formatImgUrl" mode="widthFix"></image>
                    </view>
                    <view class="new-year-content flex-row" :style="{backgroundImage:`url(${middleUrl})`}">
                        <!-- #ifndef MP -->
                        <view class="activity-content" v-html="content"></view>
                        <!-- #endif -->
                        <!-- #ifdef MP -->
                        <u-parse :html="content" @linkpress="navigate" :tag-style="tagStyle"></u-parse>
                        <!-- #endif -->
                    </view>
                    <view class="time-rever-title">
                        <image :src="'/activity/typeBg/newYear/bottom1.png'|formatImgUrl" mode="widthFix"></image>
                    </view>

                </view>
                <view class="footer-type5 ">本页面由{{siteName}}提供</view>
                <view class="footer-type5">客服电话：{{siteTel}}</view>

                
                <view class="bottom-bars flex-row"  v-if='showBottomBar' :class="{'adv-share':shareUserInfo.adviser_id&&is_adviser==1&&is_open_adviser==1}">
                    <template v-if ="shareUserInfo.adviser_id&&is_adviser==1&&is_open_adviser==1">
                    <view class="adverser-info flex-row" @click ='toAdviserDetail'> 
                        <view class="adviser-header">
                            <image :src="shareUserInfo.prelogo|imgUrl('w_80')" alt=""></image>
                        </view>
                        <view class="adviser-info-con">
                            <view class="adviser-info-name">
                                {{shareUserInfo.cname}}
                            </view>
                            <view class="adviser-info-type">
                                置业顾问
                            </view>
                        </view>
                    </view>
                    
                    <view class="adv-btns flex-1 adv-ask" @click="askAdviser"><view>在线咨询</view></view>
                    <view v-if ="switch_adviser_tel" class="adv-btns flex-1 adv-tel" @click ="telAdviser"><view>拨打电话</view></view>
                    </template>
                    <view class="bottom-bars flex-row" v-else :style="{backgroundColor:backColorType4,justifyContent:youhuiTitle?'space-between':'center'}"  >
                        <view class="zixun bot-bar-btn flex-1" v-if ="switch_adviser_tel" @click ="tolTel" >拨打电话</view>
                        <view class="huoquyouhui bot-bar-btn flex-1" v-if ="youhuiTitle" @click ="showBaomingType5()">获取优惠</view>
                    </view>
                    <!-- <view class="zixun bot-bar-btn flex-1" @click ="tolTel" >拨打电话</view>
                    <view class="huoquyouhui bot-bar-btn flex-1" v-if ="youhuiTitle" @click ="showBaomingType5()">获取优惠</view> -->
                </view>
                
                <view class="showBMpop" v-if ="showYouhui">
                    <view class="pop_con">
                    <view class="showBMpopInner" style="backgroundImage:url('https://images.tengfangyun.com/activity/typeBg/newYear/tankuangall.png')">
                        <view class="inner-box" >
                            <!-- <view class="t"><image :src="'/activity/typeBg/newYear/youhuitankuangBgTop.png'|formatImgUrl" alt="" mode="widthFix" style="width:100%"></image></view> -->
                            <!-- <view class="c" :style ="{backgroundImage:`url(${middleUrl})`}"> -->
                            <view class="c"> 
                            <view class="pop-youhui-info">
                                <view class="pop-info-title">{{popDetail.title}}</view>
                                <view class="pop-info-subtitle" v-if ="popDetail.count">已有{{popDetail.count}}人领取</view>
                            </view>
                                <view class="item flex-row"><label >姓名：</label><input type="text" class="inp" maxlength="10" v-model ="params.name" name="myname" value="" placeholder="请输入您的姓名"></view>
                                <view class="item flex-row">
                                    <label >电话：</label>
                                    <input type="number" maxlength="11" class="inp" name="tel" v-model ="params.tel" placeholder="请输入您的电话" value=""></view>
                                <view class="baomingbtn">
                                    <view class="agreement flex-box">
                                        <view class="check_box" @click="agree_agreement = !agree_agreement">
                                            <my-icons v-if="agree_agreement" type="ic_xuanze" color="#fb656a" size="36rpx"></my-icons>
                                            <text v-else class="no_checked"></text>
                                        </view>
                                        <view class="agreement_title">
                                            <text>我已阅读并同意</text>
                                            <text class="title" @click="$navigateTo('/user/agreement?type=policy')">《{{siteName||projectName}}隐私政策》</text>
                                        </view>
                                    </view>
                                    <button @click="tejiaBaoming">领取优惠</button>
                                </view>
                            </view>
                            <!-- <view class="t"><image :src="'/activity/typeBg/newYear/bottom1.png'|formatImgUrl" alt="" mode="widthFix" style="width:100%"></image></view> -->
                            </view>
                            <!-- <view class="line"></view> -->
                            <view class="close" @click ='closeYouhui'><image :src="'/activity/3/close.png'|formatImgUrl"></image></view>
                        </view>
                    </view>
                </view>
                <!-- #ifndef MP-WEIXIN -->
                <!-- 登录弹窗 -->
                <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
                <!-- #endif -->
        </view>
    </template>
    <view class="sign_btn" v-if ="is_suspension_signup" @click="showSubForm()">
      <image :src="'/exhibition/icon/sign_btn.png' | formatImgUrl('w_120')" mode="widthFix"></image>
    </view>
    <sub-form :groupCount="groupCount" show_policy :default_check="agree_agreement" :sub_type="sub_type" :sub_title="sub_title" :sub_content="sub_content" ref='sub_form_all_type' @onsubmit="handleSubFormAllType"></sub-form>
   <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
</view>
</template>

<script>
import {
  navigateTo,
  formatImg,
  isIos
} from '../../common/index.js'
import {
  config
} from '../../common/config.js'
import myIcons from '../../components/myIcon.vue'
import myIcon from '../../components/icon.vue'
import myInput from '../../components/form/myInput.vue'
import allTel from '../../common/all_tel.js'
import getChatInfo from '../../common/get_chat_info'
// import {wxShare} from '../../common/mixin'
import wx from "weixin-js-sdk"
import dragVerify from '@/components/dragVerify'
import myPopup from "../../components/myPopup"
import search from "../../components/search.vue"
import {
    uniList,
    uniListItem,
    uniLoadMore
} from '@dcloudio/uni-ui'
import myGrid from '@/components/myGrid'
import subForm from '../../components/subForm'
import checkLogin from '../../common/utils/check_login'
// #ifndef MP-WEIXIN
import loginPopup from '../../components/loginPopup'
// #endif
export default {
    data() {
        return {
            main_img:"",
            activity_label_img:"https://images.tengfangyun.com/activity/typeBg/newYear/activity_label_img_default.png",
            video_label_img:"https://images.tengfangyun.com/activity/typeBg/newYear/video_label_img_default.png",
            project_label_img:"https://images.tengfangyun.com/activity/typeBg/newYear/project_label_img_default.png",
            activity_label_text:'',
            video_label_text:'',
            project_label_text:'',
            build_list:[],
            title:'',
            playing:false,
            content:"",
            showCount:0, //显示的置业顾问的个数
            showBottomBar:0,
            show_share:false,
            audio:"",
            type:"",
            timer:null,
            content_position: 0,
            is_adviser: 0,
            is_show_title: 0, //模式2时是否显示标题
            siteName:'',
            siteUrl:'',
            block_info:{},
            sub_type: 3,
            sub_title:'',
            sub_content:'',
            groupCount:'',
            newsList:[],
            show:false,
            swiperCurrent:0,
            rec_build_list:[],  //特价房源数组
            advList:[],      //置业顾问轮播数组
            showPop:false,
            showTuijian:false,
            params:{
                name:'',
                tel:'',
                bid:'',
            },
            type4Tel:"",
            type4Code:'',
            verify_img:'',
            code_token:'',
            show_verify:false,
            verify_success:false,
            verify_fail:false,
            buildNameType4:"",
            buildIdType4:"",
            backColorType4:"",
            titleColorType4:'',
            searchBuilds:'',
            showYouhui:false,
            page:1,
            rows:20,
            current:0,
            buildListSearch:[],
            userNameType4:"",
            siteTel:'',
            moreBuildTelType4:"",
            get_status: "",
            content_text: {
                contentdown: "",
                contentrefresh: "正在加载...",
                contentnomore: "没有更多数据了"
            },
            endTime:0,
            sms_token:'',
            time:0,
            sending:false,
            tuijian:{
                name:'',
                tel:'',
                des:''
            },
            imageUrl:'',
            page_url:'',
            options:[],
            day:0,
            hours:0,
            minutes:0,
            seconds:0,
            videoUrl:'',
            videoUrlExt:'',
            playingVideo:false,
            videoContext:null,
            popDetail:{
                title:'牛气冲天 购房立享优惠',
                count:0
            },
            currentLeftTime:0,
            showVideo:true,
            shareId:'',
            shareType:"",
            currentUserInfo:{
                sid:"",
                shareType:''
            },
            shareUserInfo:{
                adviser_id:"",
                shareType:''
            },
            toLogin:true,
            login_tip:'',
            id:'',
            new_year_advisers:[],
            tel_res: {},
            show_tel_pop: false,
            projectName:"",
            agree_agreement:true,
            is_bottom_signup:true,
            is_suspension_signup:true
        }
    },
    // mixins: [wxShare],
    computed: {
        istelcall(){
            return this.$store.state.im.istelcall
        },
        has_chat(){
            return this.$store.state.im.ischat
        },
        is_open_adviser() {
            return this.$store.state.im.adviser
        },
    
        login_status() {
            console.log(this.$store.state.user_login_status);
            return this.$store.state.user_login_status 
        },
        tehuiBackground(){
            return this.imageUrl+'tehuiBg.jpg'
        },
        huawenBackground(){
            if (this.backColorType4){
                return ''
            }
            if (this.type ==5||this.type ==6){
                return config.imgDomain+'/activity/typeBg/newYear/huawen.png'
            }else {
                return ''
            }
            
        },
        jianjuBackground(){
            return config.imgDomain+'/activity/typeBg/newYear/jianju.png'
        },
        otherBackground(){
            return this.imageUrl+'otherListBg.jpg'
        },
        tuijianBackground(){
            return this.imageUrl+'tuijianyoujiang.png'
        },
        sub_mode() {
            return this.$store.state.sub_form_mode 
        },
        tel400jing() {
            return this.$store.state.tel400jing 
        },
        middleUrl(){
            return config.imgDomain+'/activity/typeBg/newYear/middle1.png'
        },
        bottomUrl(){
            return config.imgDomain+'/activity/typeBg/newYear/bottom1.png'
        },
        buildsList(){
            return this.build_list.map(build=>{
                return {
                    id:build.id,
                    name:build.title
                }
            })
        },
        switch_adviser_tel(){
            return this.$store.state.switch_adviser_tel
        }        
    },
    components: {
        myIcon,
        myIcons,
        subForm,
        myInput,
        myGrid,
        myInput,
        dragVerify,
        myPopup,
        search,
        uniList,
        uniListItem,
        uniLoadMore,
        loginPopup,
    },
    filters: {
        imgUrl(val, param = "") {
            return formatImg(val, param)
        },
        formatImgUrl(val){
            return config.imgDomain+val
        },
        formatType(val){
            if (!val) return ""
            let name =[], typename=''
            name =val.split(" ")
            typename=name[0]
            return typename

        }
    },
    onLoad(options){
        let url;
        if(options.shareId){
            this.shareId = options.shareId
            this.shareType=options.shareType
            this.share_time =options.f_time
        }
        if(isIos()){
            url = this.$store.state.firstUrl
        }else{
            url = window.location.href
        }
        this.page_url=this.formatData(options)
        this.$ajax.get('/wechat/index/signature.html',{url:url},res=>{
            if(res.data.code == 1){
                res.data.config.jsApiList = ['updateAppMessageShareData','updateTimelineShareData']
                wx.config(res.data.config)
                if(options.id){
                    this.id =options.id
                    this.getData(options.id)
                }
            }
        })
        this.options=[
            {
                    url:"/pages/new_house/new_house",
                    image: formatImg(config.imgDomain+"/images/icon/icon3/<EMAIL>", 'w_240'),
                    text:"全部楼盘",
                    isTabbar:0,
                },
                {
                    url:"/pages/community/news_list",
                    image: formatImg(config.imgDomain+"/images/icon/icon3/<EMAIL>",'w_240'),
                    text:"楼盘动态",
                    isTabbar:0,
                },
                {
                    url:"/topic/find_house",
                    image:formatImg(config.imgDomain+"/images/icon/icon3/<EMAIL>",'w_240'),
                    text:"帮我找房",
                    isTabbar:1,
                },
                {
                    url:"/pages/calculator/calculator",
                    image:formatImg(config.imgDomain+"/images/icon/icon3/ic_jisuanqi.png",'w_240'),
                    text:"房贷计算",
                    isTabbar:0,
                },
        ]
        if (this.type==5||this.type==6){
            uni.$on('getDataAgain', ()=>{
                this.getData(this.id)
            })
        }
        if ((this.type==5||this.type==6) &&this.videoUrl){
            this.videoContext = uni.createVideoContext('video')
        }
        this.projectName =config.projectName
    },
    onHide(){
        this.innerAudioContext&&this.innerAudioContext.pause()
    },
    onShow(){
        if(this.playing){
            this.innerAudioContext.play()
        }
        if (uni.getStorageSync('smallBuild')) {
            let smallBuild = JSON.parse(uni.getStorageSync('smallBuild'))
            this.buildIdType4 = smallBuild.id
            this.buildNameType4 = smallBuild.title 
        }
        uni.removeStorageSync('smallBuild')
    },
    watch:{
        endTime(){
            if (this.type ==5 ||this.type == 6 ){
                clearInterval(this.timer);
                this.timeRun()
            }
            
        }
    },
    onUnload(){
        if (this.innerAudioContext){
            this.innerAudioContext.destroy()
        }
        if (this.videoContext){
            this.videoContext.destroy()
        }
        clearInterval(this.timer);
		this.timer = null;
        // this.removeOnceAudio()
        
    },

    methods:{
        firstPlayingAudio(){
            if (this.isClicked) return 
            if (!this.playing&&this.innerAudioContext){
                this.innerAudioContext.play()
                this.isClicked =true
            }else{
                this.isClicked =true
            }
        },
        getData(id){
            this.$ajax.get('build/activity.html',{id,sid:this.shareId,sharetype:this.shareType, forward_time:this.share_time ||''},res=>{
                if(res.data.code === 1){
                    this.title = res.data.activity.title
                    uni.setNavigationBarTitle({
                        title: this.title
                    })
                    this.type = res.data.activity.model
                    this.is_adviser = res.data.activity.is_adviser
                    this.is_show_title = res.data.activity.is_show_title
                    this.content_position = res.data.activity.textarea_pos
                    this.is_suspension_signup  = res.data.activity.is_suspension_signup==1?true:false
                    this.is_bottom_signup  = res.data.activity.is_bottom_signup==1?true:false
                    var default_agree_agreement = res.data.activity.default_agree_agreement==1?true:false
                    this.agree_agreement =default_agree_agreement
                    this.main_img = res.data.activity.pic
                    if (res.data.activity.activity_label_img) {
                        this.activity_label_img = res.data.activity.activity_label_img
                    }
                    if (res.data.activity.video_label_img) {
                        this.video_label_img = res.data.activity.video_label_img
                    }
                    if (res.data.activity.project_label_img) {
                        this.project_label_img= res.data.activity.project_label_img
                    }
                    this.activity_label_text = res.data.activity.activity_label_text
                    this.video_label_text = res.data.activity.video_label_text
                    this.project_label_text = res.data.activity.project_label_text
                    this.build_list = res.data.activity.builds
                    this.showBottomBar = res.data.activity.is_nav
                    this.build_tel = res.data.activity.tel
                    
                    this.content = res.data.activity.content
                    
                    if (res.data.activity.model ==1){
                        this.showCount= res.data.activity.advcount
                        this.backColorType4=res.data.activity.bg_color
                        this.getAdvLen()
                    }else if (res.data.activity.model ==2){
                        this.block_info={
                            house_count:res.data.activity.house_count, //房源数量
                            group_count:res.data.activity.group_count, //团购人数
                            flow_number:res.data.activity.flow_number,  //关注人数
                        }
                        this.newsList =res.data.groups
                        
                        
                    }else if (res.data.activity.model ==3){
                            this.rec_build_list =res.data.activity.rec_buildids
                            if ( res.data.activity.builds.length>0){
                                this.advList= res.data.activity.builds[0].adviserAll 
                            }
                            this.imageUrl= res.data.sourceUrl
                            this.siteName=res.data.sitename;
                            this.siteUrl=res.data.siteurl
                    }else if(res.data.activity.model ==4){
                    
                        this.build_list.map(build=>{
                            build.agree_agreement = default_agree_agreement
                            build.bid =build.id
                            build.label=build.labels?build.labels.join(","):''
                            return build
                        })
                        if (res.data.activity.navList){
                            this.options=res.data.activity.navList
                        }
                        this.showBottomBar = res.data.activity.is_nav
                        this.backColorType4=res.data.activity.bg_color
                        this.titleColorType4=res.data.activity.title_color
                        this.siteName=res.data.sitename||'';
                        this.siteTel =res.data.sitetel
                    }else if (this.type ==5 ||this.type == 6){
                        this.backColorType4=res.data.activity.bg_color
                        this.endTime =res.data.activity.countdown_time
                        this.videoUrl=res.data.activity.video_path
                        this.videoUrlExt =res.data.activity.video_path_exter
                        this.siteName=res.data.sitename||'';
                        this.siteTel =res.data.sitetel
                        this.youhuiTitle =res.data.activity.poster_title
                        this.new_year_advisers =res.data.activity.advisers
                        if(this.youhuiTitle){
                            this.popDetail.title =this.youhuiTitle
                        }
                    }
                    if (this.shareId){
                        // 获取登陆状态
                        this.$ajax.get('member/checkUserStatus', {}, res => {
                            if (res.data.code === 1) {
                            } else {
                            if (this.toLogin==false) return 
                                this.toLogin=false
                                this.$store.state.user_login_status = res.data.status
                                if (this.$store.state.user_login_status ==1){
                                    uni.setStorageSync('backUrl', window.location.href)
                                    this.$navigateTo("/user/login/login")
                                }
                            }
                        })
                    }
                    // 接收分享者信息 当前用户信息
                    if (res.data.shareUser.adviser_id>0){   //当前用户信息
                        this.currentUserInfo.sid=res.data.shareUser.adviser_id
                        this.currentUserInfo.shareType=1
                        // this.share.link=config.apiDomain+"/pages/new_house/activity?id="+this.id+""
                    }
                    if (res.data.share_user.adviser_id>0){   //分享者信息
                        this.shareUserInfo=res.data.share_user
                    }
                    this.show =true
                    if(res.data.share){
                        this.share = res.data.share
                        this.setShare()
                    }
                    wx.ready(() => {   //需在用户可能点击分享按钮前就先调用
                        
                        if(res.data.activity.audio_path){
                            this.audio = res.data.activity.audio_path
                            this.playAudio(this.audio)
                            // this.playOnceAudio()
                        }
                    })
                    setTimeout(() => {
                        if ( res.data.activity.if_must_sign_up ==1){
                            this.toSubFormeAllType()
                        }
                    }, 500);
                    

                }else{
                    uni.showToast({
                        title:res.data.msg||"获取数据失败",
                        icon:"none"
                    })
                }
            })
        },
        setShare(){
            let shareLink= window.location.href
            let time=parseInt(+new Date()/1000)
            // if (this.type==5 ||this.type==6){
                if (this.currentUserInfo.sid){
                    console.log(this.currentUserInfo.sid);
                    shareLink =window.location.origin+"/h5/pages/new_house/activity?id="+this.id+"&shareId="+this.currentUserInfo.sid+"&shareType="+this.currentUserInfo.shareType+"&f_time="+time

                }
            // }
            this.share.link = shareLink
            console.log(shareLink);
            this.getWxConfig()
            // wx.updateAppMessageShareData({ 
            //     title: this.share.title||'', // 分享标题
            //     desc: this.share.content||"", // 分享描述
            //     link: shareLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
            //     imgUrl: this.share.pic+'?x-oss-process=style/w_220', // 分享图标
            //     success: function () {
            //         // 设置成功	
            //     }
            // })
            // wx.updateTimelineShareData({ 
            //     title: this.share.title, // 分享标题
            //     link: shareLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
            //     imgUrl:this.share.pic+'?x-oss-process=style/w_220', // 分享图标
            //     success: function () {
            //     // 设置成功
            //     }
            // })
        },
        toNav(e) {
            this.$navigateTo(this.options[e.index].url)
            
            
        },
        
        //跳到楼盘置业顾问列表
        goCusList(id){
            navigateTo("/pages/consultant/consuList?id="+id)
        },
        getAdvLen(index,all=false){
            if (this.showCount!=0){ 
                this.build_list.map((item,idx)=>{
                    
                    if (!all){
                        if (item.adviser.length> this.showCount){
                            item.clicks =true
                            item.showMore = true;
                            item.text =item.clicks? "查看更多":"收起"
                            item.advisers = item.adviser.slice(0,this.showCount)
                        }else {
                            item.showMore = false;
                            item.advisers = item.adviser
                        }
                        return 
                    }
                    //点击事件
                    if (index==idx){
                        if (item.adviser.length> this.showCount){
                            item.clicks =  !item.clicks
                            item.showMore = true;
                            item.text =item.clicks? "查看更多":"收起"
                            item.advisers = item.clicks?item.adviser.slice(0,this.showCount):item.adviser
                            this.$forceUpdate()
                        }else {
                            item.showMore = false;
                            item.advisers = item.adviser
                        }
                    }
                })
            }else {
                this.build_list.map(item=>{
                        item.advisers= item.adviser
                    })
            }  
        },
        showMoreAdv(idx){
            this.getAdvLen(idx,true)
        },
        
        toBuild(id){
            // uni.getEnv((res)=>{
            //     this.env = res
            // })
            wx.miniProgram.getEnv((res)=>{
                if (res.miniprogram){
                    wx.miniProgram.navigateTo({url:'/pages/new_house/detail?id='+id})
                }else {
                    navigateTo('/pages/new_house/detail?id='+id)
                }
            })
            
                
            
                
            
           
            
        },
        toSubForme(bid, groupCount){
            this.bid = bid
            this.groupCount = groupCount||''
            this.sub_title = "预约优惠报名"
            this.sub_content = "为方便通知到您最新的信息，请输入您的手机号码"
            this.$refs.sub_form.showPopup()
        },
        handleSubForm(e){
            e.from="活动页"
            e.bid = this.bid
            e.type = this.sub_type||''
            this.$ajax.post('build/signUp.html',e,res=>{
                uni.hideLoading()
                if(res.data.code === 1){
                    if(this.sub_mode!==2||res.data.status === 3){ //提示报名成功
                        uni.showToast({
                            title:res.data.msg,
                            icon:"none"
                        })
                        this.$refs.sub_form.closeSub()
                    }else if(res.data.status === 1){
                        uni.removeStorageSync('token')
                        navigateTo('/user/login/login')
                    }else if(res.data.status === 2){
                        this.$refs.sub_form.getVerify()
                    }
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            })
        },
        toChat(id){
            this.$store.state.allowOpen = true
            getChatInfo(id, 10)
        },
        handleTel(adviser, bid){
            this.$store.state.allowOpen = true
            if(this.istelcall){
                this.callMiddleNumber(2,adviser.id,2,adviser.id)
            }else{
                uni.makePhoneCall({
                    phoneNumber: adviser.tel
                });
            }
        },
        tolTel(){
            if (this.build_tel){
                uni.makePhoneCall({
                    phoneNumber: this.build_tel
                });
            }else {
                uni.showToast({
                    title:"暂未填写手机号",
                    icon:"none"
                })
            }
        },
        delay(time){
            return new Promise(resolve=>{
                setTimeout(() => {
                    resolve()
                }, time);
            })
        },
        async playAudio(src){
            this.innerAudioContext = uni.createInnerAudioContext();
            // this.innerAudioContext.autoplay = true;
            this.innerAudioContext.loop = true;
            this.innerAudioContext.src = src;
            // this.innerAudioContext.pause()
            this.innerAudioContext.onPlay(() => {
                console.log('开始播放');
                this.playing = true
            });
            this.innerAudioContext.onError((res) => {
                console.log("播放失败")
                console.log(res.errMsg);
                console.log(res.errCode);
            });
            // let i = 0
            // while (!this.playing&&i<30) {
            //     i++
                this.innerAudioContext.play()
            //     await this.delay(500)
            // }
        },
        playVideo(e){
            this.innerAudioContext&&this.innerAudioContext.pause()
            this.playing=false
        },
        switchAudio(){
            if(this.playing){
                this.innerAudioContext.pause()
            }else{
                this.innerAudioContext.play()
                if(this.videoContext){
                    this.videoContext.pause()
                }
            }
            this.playing = !this.playing
        },
        showSection(params){
          let item_list = this.build_list.map(item=>item.title)
            uni.showActionSheet({
                itemList: item_list,
                success: (res) => {
                  params.bid=this.build_list[parseInt(res.tapIndex)].id
                  this.subForme(params)
                },
                fail: function (res) {
                    console.log(res.errMsg);
                    uni.hideLoading();
                }
            });
        },
        toSubFormeAllType(){
            this.sub_title = "预约优惠报名"
            this.sub_content = "为方便通知到您最新的信息，请输入您的手机号码"
            this.$refs.sub_form_all_type.showPopup()
        },
        handleSubFormAllType(e){
            this.$store.state.allowOpen = true
            let params = e
            params.from="活动页"
            params.page_url=this.page_url
            if(this.build_list.length===0){
                this.subForme(params)
                return
            }
            if(this.build_list.length===1){
                params.bid= this.build_list[0].id
                this.subForme(params)
                return 
            }
            if (this.build_list.length>1){
              this.showSection(params)
            }
        },
        showSubForm(){
          this.toSubFormeAllType()
        },
        formSubmit(e){
            if(e.detail.value.tel.length<11||e.detail.value.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return
            }
            if (!this.agree_agreement){
                uni.showToast({
                    title:"请先同意用户协议",
                    icon:"none"
                })
                return
            }
            this.$store.state.allowOpen = true
            let params = e.detail.value
            params.from="小程序活动页"
            params.page_url=this.page_url
            if(this.build_list.length===0){
                this.subForme(params)
                return
            }
            if(this.build_list.length===1){
                params.bid= this.build_list[0].id
                this.subForme(params)
                return 
            }
            let item_list = this.build_list.map(item=>item.title)
            uni.showActionSheet({
                itemList: item_list,
                success: (res) => {
                    params.bid= this.build_list[parseInt(res.tapIndex)].id
                    this.subForme(params)
                },
                fail: function (res) {
                    console.log(res.errMsg);
                }
            });
        },
        subForme(params,buildIdx=''){
            // uni.showLoading({
            //     title:"正在提交",
            //     mask:true
            // })
            let _this=this
            this.$ajax.post('build/signUp.html',params,res=>{
                uni.showToast({
                    title:res.data.msg,
                    icon:"none"
                }) 
                if(_this.type==3&&res.data.code==1){

                    setTimeout(() => {
                        _this.showPop = false;
                    }, 500);
                }
                if(_this.type==4&&res.data.code==1){
                    this.buildIdType4=''
                    this.buildNameType4=''
                    this.moreBuildTelType4=''
                    this.userNameType4=''
                    this.sms_token=''
                    this.type4Code=''
                    this.type4Tel=''
                    if (buildIdx>=0){
                        this.build_list[buildIdx].signTel=''
                    }
                }
                if((_this.type==5||_this.type==6)&&res.data.code==1){
                    this.params.bid=''
                    this.params.tel =""
                    this.params.name =""
                    this.popDetail.title=this.youhuiTitle
                    this.popDetail.count=0
                    this.showYouhui =false
                    this.showVideo=true
                }
                this.$refs.sub_form_all_type.hide()
            })
        },
        close(){
            this.showPop=false;
            this.agree_agreement =true
            this.params.bid =""
            this.params.tel =""
            this.params.name =""
            this.$refs.sub_form_all_type.hide()
        },
        baomingHasId(id){
            this.showPop=true
            this.agree_agreement=true
            if(id){
                this.params.bid =id
            }else{
                this.params.bid =''
            }
            
        },
        tejiaBaoming(){
            if(this.params.tel.length!=11||this.params.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none",
                })
                return;
            }
            if(!this.agree_agreement){
                uni.showToast({
                    title:"请先同意隐私政策",
                    icon:"none",
                })
                return;
            }
            this.$store.state.allowOpen = true
            this.params.from="小程序活动页"
            this.params.page_url =this.page_url
            if (this.type ==5||this.type ==6){
                if (this.shareUserInfo.adviser_id>0){
                    this.params.share_uid=this.shareUserInfo.adviser_id
                    this.params.is_adviser=1
                }
            }
            this.subForme(this.params)
            
        },
        // 封装参数
        formatData(option){
            let arr =[],url='';
            let pages= getCurrentPages()
            let currPage=pages[pages.length-1]
            // #ifdef APP-PLUS
            url =currPage.$getAppWebview();
            // #endif
            // #ifndef APP-PLUS
            url ='/'+ currPage.route;
            console.log(currPage);
            
            // #endif
            for (const key in option) {
                if (option.hasOwnProperty(key)) {
                    const element = option[key];
                    arr.push(`${key}=${element}`)
                }
            }
			// #ifndef APP-PLUS
            return url+"?"+arr.join("&")
			 // #endif
		   // #ifdef APP-PLUS
		   return "/"+url.__uniapp_route+"?"+arr.join("&")
		   // #endif
        },

        subTuijian(){
            let _this = this
            if(this.tuijian.tel.length!=11||this.tuijian.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none",
                })
                return
            }
            if(!this.agree_agreement){
                uni.showToast({
                    title:"请先同意隐私政策",
                    icon:"none",
                })
                return
            }
            uni.showLoading({
                title:"正在提交",
                mask:true
            })
            this.tuijian.from="小程序活动页"
            this.tuijian.page_url =this.page_url
            this.$ajax.post('build/signUp.html',_this.tuijian,res=>{
                uni.showToast({
                    title:res.data.msg,
                    icon:"none"
                })
                if(res.data.code ==1){
                    setTimeout(() => {
                        _this.showTuijian = false;
                    }, 500);
                }
            })

        },
        handleShare(){
            this.show_share = true
        },
        goBuildList(){
            navigateTo("/pages/new_house/new_house")
        },

        toAdviserList(){
            if(this.build_list.length===0){
                uni.showToast({
                    title:"暂无置业顾问",
                    icon:'none'
                })
                return
            }
            if(this.build_list.length===1){
                navigateTo('/pages/consultant/consuList?id=' + this.build_list[0].id)
                return 
            }
            let item_list = this.build_list.map(item=>item.title)
            uni.showActionSheet({
                itemList: item_list,
                success: (res) => {
                    navigateTo('/pages/consultant/consuList?id=' + this.build_list[parseInt(res.tapIndex)].id)
                },
                fail: function (res) {
                    console.log(res.errMsg);
                }
            });
        },
        changeSelectBuild(e){},
         // 获取滑块验证码
        getVerify() {
            if (this.sending) {
                return
            }
            if (!this.type4Tel) {
                uni.showToast({
                    title: '请输入手机号',
                    icon: 'none'
                })
                return
            }
            this.$ajax.get('member/slideToken', {}, res => {
                if (res.data.code === 1) {
                    this.verify_img = res.data.url
                    this.code_token = res.data.imgCode
                    this.show_verify = true
					// #ifdef APP-PLUS
					if(uni.getSystemInfoSync().platform == 'ios'){
						plus.key.hideSoftKeybord()
					}
					// #endif
					
                }
            }, err => {

            })
        },
        onRefresh(){
            this.getVerify()
        },
        // 滑块验证码重置完成的回调
        onRefreshEnd() {
            this.verify_success = false
            this.verify_fail = false
        },
        // 用户滑动验证码结束的回调
        onDragEnd(value){
            this.sendSmsCode(value)
        },
        sendSmsCode(verify_code) {
            if(!verify_code){
                uni.showToast({
                    title:"请先滑动验证",
                    icon:"none"
                })
                return
            }
            if(!this.type4Tel){
                uni.showToast({
                    title:'请输入手机号',
                    icon:'none'
                })
                return
            }
            if(this.type4Tel.length<11||this.type4Tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return
            }
            this.$ajax.get('member/checkSlideCode', {
                tel: this.type4Tel,
                imgCode: this.code_token,
                code: verify_code
            }, res => {
                if (res.data.code === 1) {
                    this.verify_success = true
                    this.sms_token = res.data.code_token || ''
                    uni.showToast({
                        title: res.data.msg
                    })

                        // 隐藏拖动验证码
                        this.show_verify = false
                        // }, 50);
                        
                    // })
                    this.time = 60
                    this.sending = true
                    if (this.timer) {
                        clearInterval(this.timer)
                    }
                    this.timer = setInterval(() => {
                        if (this.time <= 0) {
                            clearInterval(this.timer)
                            this.sending = false
                            return
                        }
                        this.time--
                    }, 1000)
                } else {
                    this.verify_fail = true
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                    this.getVerify()
                }
            }, err => {
                this.verify_fail = true
                uni.showToast({
                    titley: '验证失败',
                    icon: 'none'
                })
                this.getVerify()
            })
        },
        searchBuild() {
            uni.hideKeyboard()
            this.page = 1
            this.getBuilds() 
        },
        stopMove() {

        },
        loadLazy() {
            if(this.get_status === "noMore"){
                return
            }
            this.page++
            this.getBuilds()
        },
        radioChange(evt) {
            for (let i = 0; i < this.buildListSearch.length; i++) {
                if (this.buildListSearch[i].id == evt.target.value) {
                    this.current = i;
                    this.buildIdType4 = this.buildListSearch[i].id
                    this.buildNameType4 = this.buildListSearch[i].title
                    this.$refs.select_build.hide()
                    break;
                }
            }

        },
        goSelect() {
            this.$refs.select_build.show()
            this.getBuilds()
        },
        inputValue(e) {
            this.searchBuilds = e.detail.value
        },
        // 获取楼盘信息
        getBuilds() {
            if(this.page === 1){
                this.buildListSearch = []
            }
            this.$ajax.get('Adviser/getBuildList', {
                page: this.page,
                rows: this.rows,
                key: this.searchBuilds
            }, (res) => {
                if (res.data.code == 1) {
                if (res.data.data.length < this.rows) {
                    this.get_status = "noMore"
                } else {
                    this.get_status = "more"
                }
                this.buildListSearch = this.buildListSearch.concat(res.data.data)
                } else {
                this.get_status = "noMore"
                }
            })
        },
        subFormeType4(build, type,buildIdx=''){
            let params={},agreement=false
            console.log(this.type4Tel);
            switch (type) {
                case 1:  // build 存在
                    params.title="获取报价"
                    params.bid =build.bid
                    params.tel = build.signTel
                    params.name =this.siteName+"网友"
                    agreement =build.agree_agreement
                    break;
                case 0:
                    params.title="订阅房源动态"
                    params.bid='',
                    params.code_token=this.sms_token
                    params.code=this.type4Code
                    params.name =this.siteName+"网友"
                    params.tel=this.type4Tel
                    this.sub_type=5
                    agreement =this.agree_agreement
                    break;
                case 2:
                    params.title="匹配房源"
                    params.bid=this.buildIdType4,
                    params.tel=this.moreBuildTelType4
                    params.name =this.userNameType4
                    this.sub_type=3
                    agreement =this.agree_agreement
                    break;
                default:
                    break;
            }
            params.from="活动页"
            
            params.page_url =this.page_url
            params.type =this.sub_type
            if (!params.tel){
                uni.showToast({
                    title:"手机号不能为空",
                    icon:'none'
                })
                return 
            }
            if (!params.name){
                uni.showToast({
                    title:"用户名不能为空",
                    icon:'none'
                })
                return 
            }
            if(params.tel.length<11||params.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return
            }
            if(type==0&&!params.code&&this.$store.state.user_login_status!=3){
                uni.showToast({
                    title:"请输入验证码",
                    icon:'none'
                })
                return 
            }
            
            if(type==2&&!params.bid){
                uni.showToast({
                    title:"请选择楼盘",
                    icon:'none'
                })
                return 
            }
            if(type==0&&this.$store.state.user_login_status==3){
                delete params.code
                delete params.code_token
            }
            if (!agreement){
                uni.showToast({
                    title:"请先同意隐私政策",
                    icon:'none'
                })
                return 
            }
            console.log(buildIdx);
            this.subForme(params,buildIdx)

        },
        
        callBuild(build){
            // useMiddleCall 后台已经判断全局和当前楼盘是否开启中间号了
            if (build.useMiddleCall==1){
                this.callBuildMiddleNumber(build)
            }else{
                this.callBuildNumber(build)
            }
        },

        // 拨打楼盘真实号码
        callBuildNumber(build) {
            console.log('拨打楼盘真实号码') 
            var  phoneNumber=''
            //后台已经判断没有400 直接返回楼盘小号了 用一个字段tel
            if (build.tel) {
                phoneNumber = build.tel
                uni.makePhoneCall({
                    phoneNumber: phoneNumber,
                    success: () => {
                        // this.statistics()
                    }
                })
            } else {
                uni.showToast({
                    title: '此楼盘没有绑定联系电话',
                    icon: 'none'
                })
            }
        },
        // 拨打楼盘虚拟号码
        callBuildMiddleNumber(build) {
            console.log('拨打楼盘虚拟号码')
             //后台已经判断没有400 直接返回楼盘小号了 用一个字段tel 如果开启了中间号tel为空
            // #ifdef MP-WEIXIN
            this.callMiddleNumber(1,build.id,1,build.id)
            // #endif
            // #ifndef MP-WEIXIN
            // 全局开启中间号且楼盘开启中间号需要检测登录
            if(build.useMiddleCall==1){
                this.$ajax.get('member/checkUserStatus', {}, res => {
                    if (res.data.code === 1) {
                        this.callMiddleNumber(1,build.id,1,build.id)
                    } else {
                        this.$store.state.user_login_status = res.data.status
                        if (this.$store.state.user_login_status==2){
                            this.$navigateTo("/user/bind_phone/bind_phone")
                        }else if (this.$store.state.user_login_status==1){
                            uni.setStorageSync('backUrl', window.location.href)
                            this.$navigateTo("/user/login/login")
                        }
                    }
                })
            }else{
                this.callMiddleNumber(1,build.id,1,build.id)
            }
            // #endif
        },
        callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid){
            this.tel_params = {
                type,
                callee_id,
                scene_type,
                scene_id,
                source,
                bid,
                success: (res)=>{
                    this.tel_res = res.data
                    this.show_tel_pop = true
                }
            }
            allTel(this.tel_params)
        },
        retrieveTel(){
            allTel(this.tel_params)
        },
        videoErrorCallback(){

        },
        showBaomingType5(e,build){
            if (build){
                console.log(build);
                this.popDetail.title =build.discount
                this.popDetail.count =build.groupCount||0
                this.params.bid=build.id||""
            }
            this.showYouhui=true
            this.agree_agreement =true
            this.showVideo=false
        },
        closeYouhui(){
            this.popDetail.title=this.youhuiTitle
            this.popDetail.count =0
            this.showYouhui=false
            this.showVideo=true
        },
        timeRun(){
            let leftTime = this.GetDateDiff()
            this.getCountdownTime(leftTime)
        },
        //计算两个时间差
        GetDateDiff(endTime) {
            this.currentLeftTime =parseInt((this.endTime - (new Date().getTime()/1000)) )
            return parseInt((this.endTime - (new Date().getTime()/1000)) );
        },
        //计算活动结束时间
        getCountdownTime(leftTime){
            let time = leftTime
            if (time>0) {
                this.timer = setInterval(() => {
                    if(time == 0){
                        clearInterval(this.timer)
                        this.h = 0
                        this.m = 0
                        this.s = 0
                    }else{
                        this.day = parseInt(leftTime/3600/24);
                        this.hours = parseInt((time/3600)%24);
                        this.minutes = parseInt((time/60)%60);
                        this.seconds = parseInt(time%60);
                        time --
                        this.currentLeftTime =time
                    }
                },1000)
            }
        },
        
        toAdviserDetail(){
            this.$navigateTo("/pages/consultant/detail?id="+this.shareUserInfo.adviser_id)
        },
        askAdviser(){
            if (this.has_chat == 1) {
                //开聊天
                // #ifdef MP-WEIXIN
                getChatInfo(this.shareUserInfo.mid, 10)
                // #endif
                // #ifndef MP-WEIXIN
                checkLogin({
                success: (res)=>{
                    getChatInfo(this.shareUserInfo.mid, 10)
                },
                fail: (res)=>{
                    this.showLoginPopup('为方便您及时接收消息通知，请输入手机号码')
                },
                complete:(res)=>{
                    this.$store.state.user_login_status = res.status
                }
                })
                // #endif
            } else if (this.has_chat == 0) {
                //不开聊天
                this.$navigateTo("/pages/consultant/detail?id="+this.shareUserInfo.adviser_id)
            }
        },
        handleCloseLogin() {
            if (this.$store.state.user_login_status === 1) {
                uni.removeStorageSync('token')
                this.$navigateTo('/user/login/login')
            }
            if (this.$store.state.user_login_status === 2) {
                this.$navigateTo('/user/bind_phone/bind_phone')
            }
        },
        onLoginSuccess(res){
            this.$store.state.user_login_status = 3
            // if(this.weituo_is_show){
            //     console.log("登录成功后继续执行委托接口")
            //     this.$refs.enturst_box.handleEnturst()
            // }
        },
        telAdviser(){
            this.$store.state.allowOpen = true
            if(this.istelcall==1){
                // this.callMiddleNumber(this.shareUserInfo.adviser_id, this.shareUserInfo.mid, this.shareUserInfo.tel, 'adviser', 9, '' ,1)
                this.callMiddleNumber(3,this.shareUserInfo.adviser_id,3,this.shareUserInfo.adviser_id,)
            }else{
                if (this.$store.state.user_login_status==1){
                    this.$navigateTo("/user/login/login")
                    return 
                }else if (this.$store.state.user_login_status==2){
                    this.$navigateTo("/user/bind_phone/bind_phone")
                    return 
                }
                uni.makePhoneCall({
                    phoneNumber: this.shareUserInfo.tel
                });
            }
        },
        showLoginPopup(tip){
            this.login_tip = tip
            this.$refs.login_popup.showPopup()
        },
        changeAgreement(agreement,index){
            this.$set(this.build_list[index],'agree_agreement',!agreement)
            this.$forceUpdate()
        }
    },
}
</script>

<style scoped lang="scss">
@keyframes rotate{
    0%{-webkit-transform:rotate(0deg);}
    25%{-webkit-transform:rotate(90deg);}
    50%{-webkit-transform:rotate(180deg);}
    75%{-webkit-transform:rotate(270deg);}
    100%{-webkit-transform:rotate(360deg);}
}
@-webkit-keyframes rotate{
    0%{-webkit-transform:rotate(0deg);}
    25%{-webkit-transform:rotate(90deg);}
    50%{-webkit-transform:rotate(180deg);}
    75%{-webkit-transform:rotate(270deg);}
    100%{-webkit-transform:rotate(360deg);}
}
@keyframes fade {
    from {
        opacity: 1.0;
    }
    50% {
        opacity: 0.1;
    }
    to {
        opacity: 1.0;
    }
}

@-webkit-keyframes fade {
    from {
        opacity: 1.0;
    }
    50% {
        opacity: 0.1;
    }
    to {
        opacity: 1.0;
    }
}
.flex-row{
    display: flex;
    flex-direction: row;
}
.border-box-shaw{
    
    box-shadow: 0 0 9px #dedede;
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 40rpx;
    overflow: hidden;
}
.page{
    background-color: #fff;
    padding-bottom: 30rpx;
    max-width: 1000px;
    margin: 0 auto;
    &.pad_bottom{
        padding-bottom: 140rpx;
    }
    &.bgcolor{
        background: #5f11b5;
        background-attachment: fixed;
    }
    &.taizhou_mode{
        background: #e5f4fb;
    }
    .audio-box{
        position: fixed;
        right: 20rpx;
        top: 60rpx;
        width: 70rpx;
        height: 70rpx;
        border-radius: 50%;
        background-color: rgba($color: #333, $alpha: 0.6);
        z-index: 1;
        color: #fff;
        text-align: center;
        line-height: 70rpx;
        &.rotate{
            animation:rotate 3s linear infinite
        }
        .icon-yinyue{
            line-height: 70rpx;
        }
    }
    .min-img{
        width: 100%;
        display: block;
    }
    .build-list{
        padding: 0 36rpx;
    }
    .youhui_list{
        padding: 0 36rpx;
        position: relative;
        margin-top: -20rpx;
        .build-box{
            margin-bottom: 10rpx;
            padding: 10rpx 0 20rpx;
        }
        .build-item{
            box-shadow: none;
            .youhui_img{
                width: 250rpx;
                height: 190rpx;
                border-radius: 10rpx;
            }
            .top{
                .info{
                    padding: 0 0 0 36rpx;
                    max-width: 60vw;
                    overflow: hidden;
                    .title-row{
                        margin-bottom: 10rpx;
                    }
                    .price-box{
                        color: #333333;
                        font-weight: bold;
                    }
                    .address{
                        font-size: 24rpx;
                        margin-bottom: 2rpx;
                    }
                    .title-row{
                        margin-bottom: 10upx;
                    }
                    .attrs{
                        padding: 10rpx 0;
                        .hui{
                            padding: 3rpx 6rpx;
                            margin-right: 10rpx;
                            font-size: 28rpx;
                            color: #ffffff;
                            background: #EB3D26;
                            border-radius: 6rpx;
                        }
                        .hui_info{
                            color: #F36940;
                            font-size: 28rpx;
                            display: inline-block;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            // max-width: 70%;
                        }
                    }
                }
            }
            .bot{
                justify-content: space-between;
                align-items: center;
                font-size: 18rpx;
                color: #f4283a;
                padding: 30rpx 0;
                .right{
                    .tel{
                        padding: 10rpx 20rpx;
                        color: #333;
                        background: #F8F8F8;
                        border-radius: 20rpx;
                        margin-right: 20rpx;

                    }
                    .bot_right{
                        .guanzhu{
                            padding: 10rpx 20rpx;
                            background: #FFECEC;
                            border-radius: 20rpx;
                            color: #F82C2C;
                        }
                        .qiang{
                            padding: 10rpx 30rpx;
                            border-radius: 40rpx;
                            background: #F70000;
                            color: #fff;
                            margin-left: -15rpx;
                        }
                    }
                }
            }
        }
    }
    .middle{
        // width: 100vw;
        margin: 0 36rpx;
        background:#ffffff;
        border-radius: 30rpx;
        margin-top: -80rpx;
        margin-bottom: 24rpx;
        z-index: 5;
        position: relative;
        .show_block{
            width: 100%;
            height: 160rpx;
            border-radius: 30rpx;
            justify-content: space-around;
            background: #FEF9F8;
            align-items: center;
            z-index: 5;
            position: relative;
            .show_block_infos{
                height: 100%;
                text-align: center;
                box-sizing: border-box;
                padding: 20rpx 0;
                .num{
                    color: #F36940;
                    font-size: 50rpx;
                    font-weight: bold;

                }
                .show_block_info{
                    color: #666666;
                    font-size: 24rpx;
                    padding: 10rpx 0;

                }


            }

        }
        .news_wrap{
            height: 60rpx;
            line-height: 60rpx;
        }
    }
    .row-title{
        padding-left: 26rpx;
        line-height: 1.2;
        font-size: 44rpx;
        font-weight: bold;
        border-left: 6rpx solid $uni-color-primary;
        margin: 50rpx 0;
    }
    .build-box{
        // padding-bottom: 26rpx;
        margin-bottom: 90rpx;
    }
    .build-item{
        box-shadow: 0 0 18rpx #dedede;
        background-color: #fff;
        border-radius: 8rpx;
        overflow: hidden;
        .img{
            width: 100%;
            height: 50vw;
        }
        .info{
            padding: 28rpx
        }
        .title-row{
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15rpx;
        }
        
        .title{
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
        .price-box{
            color: $uni-color-primary;
            font-size: $uni-font-size-sm;
            .price{
                margin-right: 5rpx;
                font-size: $uni-font-size-blg;
                font-weight: bold;
            }
        }
        .address{
            margin-bottom: 15rpx;
            color: #666;
        }
        .labels{
            font-size: 0
        }
        .label{
            padding: 0 8rpx;
            margin-bottom: 10rpx;
            font-size: 22rpx;
            margin-right: 10rpx;
            border-radius: 4rpx;
            color: #53d2ab;
            border: 3rpx solid #e1faff;
            display: inline-block;
        }
    }
    .news-box {
        padding: 0 0 0 40rpx;
        background-color: #ffffff;
        margin-top: -20rpx;
        box-shadow: 0 4rpx 18rpx #EFE5E2;
        border-bottom-left-radius: 30rpx;
        border-bottom-right-radius: 30rpx;
        z-index: 1;

    }

    .news-box image {
        width: 83rpx;
        height: 40rpx;
        margin-top: 15rpx;
        margin-left: 15rpx;
    }

    .news-box swiper {
        height: 60rpx;
        // background-color: #ffffff;
    }

    .news-box swiper view {
        padding: 10rpx;
        height: 50rpx;
        line-height: 50rpx;
        font-size: $uni-font-size-sm;
        color: #F36940;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .tel_num{
        width: 100%;
        line-height: 120rpx;
        color: #EB3D26;
        font-size: 50rpx;
    }

    .activity{
        padding: 20rpx 10rpx;
        margin:10rpx 0;
        border-radius: 10rpx;
        // background: linear-gradient(to right,#ee1140, #fd7128);
        background: #fff0ee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .act_left {
            flex: 1;
            padding: 20rpx 40rpx;
            max-width: 400rpx;
            .title {
                color :#fb582d;
                font-size: 32rpx;
                font-weight: bold;
                width: 98%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .sub_title{
                color :#fa6339;
                font-size: 25rpx;
                padding: 20rpx 0rpx;
                width: 98%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .act_right{
            flex :1;
            text-align: center;
            .canyu {
                padding: 10rpx 30rpx;
                border-radius: 40rpx;
                background: #fff;
                color:#f4283a;
            } 
        }
    }

    .adviser-list{
        box-shadow: 0 0 18rpx #dedede;
        background-color: #fff;
        .adviser-item{
            justify-content: space-between;
            align-items: center;
            padding: 24rpx 32rpx;
            .head-img{
                width: 90rpx;
                height: 90rpx;
                border-radius: 50%;
                overflow: hidden;
                margin-right: 32rpx;
            }
            .name{
                font-size: 30rpx;
                line-height: 1.8;
                font-weight: bold;
            }
        }
        .icon-box{
            margin-left: 28rpx;
        }
        .show_more{
            text-align: center;
            padding: 0 0 10rpx;
        }
    }


    .center-title{
        text-align: center;
        line-height: 1.2;
        font-size: 44rpx;
        font-weight: bold;
        margin: 50rpx 0;
    }
    
    .form-box{
        .label-row{
            padding-left: 34rpx;
            font-size: 26rpx;
            color: #999999
        }
    }
    .bottom-bar {
        height: 120rpx;
        background-color: #fff;
        max-width: 1000px;
        left: 0;
        right:0;
        width: auto;
        .to-buy {
            align-items: center;
            justify-content: center;
            color: #666666;
            background-color: #fff;
        }
        .to-consu {
            align-items: center;
            justify-content: center;
            color: #fff;
            margin: 20rpx 24rpx 20rpx 20rpx;
            background: #00c0eb;
            border-radius: 10rpx;
            // background-color: $uni-color-primary;
        }
        .to-tel{
            align-items: center;
            justify-content: center;
            color: #fff;
            background-color: $uni-color-primary;
            margin: 20rpx 24rpx 20rpx 20rpx;
            border-radius: 10rpx;
            
        }
        .to-tel.to-tel-youhui view{
            font-size: 38upx;
        }
        &.adv-share{
                padding: 20rpx 48rpx;
                background: #fff;
                box-sizing: border-box;
                .adv-btns{
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                    font-size: 15px;
                    padding: 0;
                    margin: 0;
                    border-radius: 0;
                    color: #fff;
                    &.adv-ask{
                            background: #fbac65;
                            box-shadow: 0 0 4px 0 rgba(0,0,0,.05);
                            border-top-left-radius: 20px;
                            border-bottom-left-radius: 20px;
                    }
                    &.adv-tel{
                            background: linear-gradient(90deg,#fb656a,#fbac65);
                            box-shadow: 0 0 4px 0 rgba(255,80,0,.3);
                            border-top-right-radius: 20px;
                            border-bottom-right-radius: 20px;
                    }
                }
                .adverser-info{
                    justify-content: flex-start;
                    align-items: center;
                    flex: 1;
                    .adviser-header{
                            height: 64rpx;
                            width: 64rpx;
                            margin-right: 8rpx;
                            overflow: hidden;
                            border-radius: 64rpx;
                            image {
                                height: 64rpx;
                                width: 64rpx;
                                overflow: hidden;
                                border-radius: 64rpx; 
                            }
                    }
                    .adviser-info-name{
                        font-size: 24rpx;
                        line-height: 1;
                        max-width: 216rpx;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                    .adviser-info-type{
                        font-size: 24rpx;
                        margin-top:6rpx;
                        line-height: 1.5;
                    }
                }
            }
    }
    .share_mask{
        position: fixed;
        width: 100%;
        top: 0;
        bottom: 0;
        background-color: rgba($color: #000000, $alpha: 0.5);
        z-index: 99;
        .icon-box{
            width: 120rpx;
            position: absolute;
            top: 20rpx;
            right: 60rpx;
            animation: fade 800ms infinite;
            -webkit-animation: fade 800ms infinite;
        }
        .icon{
            width: 100%;
        }
    }
    button{
        padding: 0
    }
    // 特惠模式
    &.tejia_mode{
        image{
            display: block;
        }
        .tehui{
                // background-image:url('https://images.tengfangyun.com/attachment/focus/20200613/502aec242ee96759457173024e9d486428b71e1d.jpeg');
                background-size: 100%;
                .list{
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    padding: 0 75rpx;
                    .item{
                        width: 46%;
                        background-image: linear-gradient(to right, #b843f4, #e22299);
                        border-radius: 10rpx;
                        overflow: hidden;
                        padding: 1%;
                        margin: 0 1vw 3vw 0;
                        .img{
                            padding: 6rpx;
                            background-image: linear-gradient(to right, #b843f4, #e22299);
                            position: relative;
                            overflow: hidden;
                            padding-bottom: 20rpx;
                            .tag{
                                height: .64rem;
                                line-height: .64rem;
                                position: absolute;
                                padding: 0 .4rem;
                                border-bottom-left-radius: .27rem;
                                background-image: linear-gradient(to right, #b843f4, #e22299);
                                color: #fff;
                                font-size: 20rpx;
                                font-weight: bold;
                                top: .07rem;
                                right: 0;
                            }
                        
                        .pic{
                            border-radius: 10rpx;
                            image{
                                border-radius: 10rpx;
                                width: 100%;
                                // width:272rpx;
                                height:272rpx;
                            }
                        }
                        .btmBox{
                            display: flex;
                            justify-content: space-between;
                            height: 40rpx;
                            line-height: 40rpx;
                            .tagTitle{
                                height: 72rpx;
                                line-height:72rpx;
                                border: 5rpx solid #c03be4;
                                box-sizing: border-box;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                font-size: 34rpx;
                                background-image: linear-gradient(#f4ddb3, #f1c795);
                                color: #523104;
                                border-radius: 100%;
                                width: 72rpx;
                                position: relative;
                                top: -40rpx;
                            }
                            .price{
                                // display: flex;
                                align-items: center;
                                color: #ffdc5d;
                                padding-right: 5rpx;
                                font-size:24rpx;
                                max-width: 65%;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                        }
                        }
                        .txt{
                                background: #FFF;
                                padding-bottom: 21rpx;
                                border-radius: 2rpx;
                                .houseName{
                                    padding: 17rpx 21rpx;
                                    display: flex;
                                    justify-content: center;
                                    color: #333;
                                    align-items: center;
                                }
                                .btn{
                                    text{
                                        display: block;
                                        margin: 0 27rpx 21rpx 27rpx;
                                        text-align: center;
                                        height: 62rpx;
                                        line-height: 62rpx;
                                        border-radius: 31rpx;
                                        color: #fff;
                                        background-image: linear-gradient(to right, #fa2134, #fd2a7b);
                                        font-size: 28rpx;
                                        font-weight: bold;
                                    }
                                }
                        }
                    }
                }
            }
        .tehuiBtm {
            margin-top: -2rpx;
        }
        .otherList{
            padding: 0 45rpx 7rpx 45rpx;
            // background: url('https://images.tengfangyun.com/attachment/focus/20200615/e48797d362e7ea14a55cc10492d716f1c2bdb387.jpeg') repeat-y;
            background-repeat: repeat-y;
            background-size: 100%;
            .item{
                border-radius: 14rpx;
                background: #fff;
                overflow: hidden;
                margin-bottom:27.5rpx;
                padding: 17.5rpx;
                .topInfo{
                    display: flex;
                    .pic{
                        width: 170rpx;
                        height: 160rpx;
                        padding-right: 15rpx;
                        overflow: hidden;
                        image{
                            width: 100%;
                            min-height: 170rpx;
                            // height: 100%
                        }
                    }
                    .txt{
                        flex: 1;
                        max-width: calc(100% - 200rpx);
                        h4{
                            font-size: 34rpx;
                        }
                        .tagAndPrice{
                            justify-content: space-between;
                            display: flex;
                            align-items: center;
                            .tags{
                                text{
                                    display: inline-block;
                                    padding: 3.5rpx 7rpx;
                                    border-radius: 2rpx;
                                    font-size: 24rpx;
                                    background: #d9dde8;
                                    color: #616264;
                                }
                                text:nth-child(1) {
                                    background: #ff2b39;
                                    color: #fff;
                                    margin-right:4rpx;
                                }
                            }
                            .price{
                                display: flex;
                                align-items: center;
                                font-size:28rpx;
                                white-space: nowrap;
                                .strong{
                                    font-size:30rpx;
                                    color: #ff2b39;
                                }
                            }
                            
                        }
                        .hui{
                            padding: 2rpx;
                            background: #f5f5f5;
                            font-size: 24rpx;
                            color: #f4645a;
                            font-weight: bold;
                            display: flex;
                            margin: 2rpx 0;
                            align-items: center;
                            .strong{
                                display: inline-block;
                                padding: 3px;
                                text-align: center;
                                line-height: 25rpx;
                                color: #fff;
                                border-radius: 3px;
                                background: #f4645a;
                                margin-right:5rpx;
                            }
                            .hui_con{
                                // flex: 1;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                // max-width: 70%;

                            }
                        }
                        .add{
                            font-size: 26rpx;
                            color: #4b4b4b;
                        }
                    }
                }
                .group{
                    margin:17rpx;
                    padding: 27rpx 0 0 0;
                    border-top:  2rpx solid #E0E0E0 ;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .img{
                        display: flex;
                        .i{
                            width: 65rpx;
                            height: 65rpx;
                            box-sizing: border-box;
                            border:  2rpx solid #fff ;
                            border-radius: 100%;
                            overflow: hidden;
                            position: relative;
                            left: 0;

                            image{
                                width: 100%;
                                display: block;
                            }
                        }
                        .i:nth-child(2){
                            left: -30rpx;
                        }
                        .i:nth-child(3){
                            left: -60rpx;
                        }
                        .other{
                            width: 65rpx;
                            height:65rpx;
                            line-height: 65rpx;
                            box-sizing: border-box;
                            border: 1px solid #fff;
                            border-radius: 100%;
                            overflow: hidden;
                            position: relative;
                            background: #E0E0E0;
                            font-size: 24rpx;
                            left: -60rpx;
                            text-align: center;
                        }
                        .t{
                            font-size: 24rpx;
                            line-height:65rpx;
                            position: relative;
                            left:-20rpx;
                        }
                    }
                    .btn{
                        width: 145rpx;
                        height: 62rpx;
                        line-height: 62rpx;
                        text-align: center;
                        background-image: linear-gradient(to right, #fa2134, #f92a7a);
                        border-radius: 8rpx;
                        color: #fff;
                        text-align: center;
                        font-size: 26rpx;
                        font-weight: bold;
                    }
                }
            }
        }
        .guwen{
            .guwenTitle {
                font-size: 32rpx;
                font-weight: bold;
                color: #fff;
                text-align: center;
                image{
                    width: 100%;
                }
            }
            .lp_index_c {
                // background: url('https://images.tengfangyun.com/attachment/focus/20200615/e48797d362e7ea14a55cc10492d716f1c2bdb387.jpeg') repeat-y;
                background-repeat: repeat-y;
                background-size: 100%;
                padding: 0;
                margin: -1px 0;
            }
            .item{
                background: #fff;
                border-radius: 7rpx;
                margin-bottom: 18rpx;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 20rpx 10rpx;
                width: 85%;
                overflow: hidden;
                .ico{
                    width: 110rpx;
                    height: 110rpx;
                    overflow: hidden;
                    border-radius: 100%;
                    image{
                        width: 100%;
                        // display: block;

                    }
                }
                .item_name{
                    flex: 1;
                    text-align: center;
                    padding: 16rpx 0;
                    width: 100%;
                    overflow: hidden;
                    .strong{
                        font-size: 32rpx;
                        text-align: center;
                        font-weight: bold;
                        width: 95%;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;


                    }
                    .tel{
                        display: block;
                        color: #999999;
                        padding-top: 10rpx;
                        font-size: 26rpx;
                    }

                }
                .btn_box{
                    display: flex;
                    justify-content: center;
                    .btn{
                        flex:1;
                        border-radius: 50%;
                        padding: 10rpx;
                        &.item_tel{
                            background: linear-gradient(294deg, #ff7e24, #f84c32 40%, #ed0246);
                            margin-right: 10rpx;
                        }
                        &.item_chat{
                            background: linear-gradient(294deg, #4caf50, #31a736 40%, #7bc549);
                        }
                    }
                }
            }
            .banner{
                height: 400rpx;
                padding: 0 20rpx 0 45rpx;
            }
        }
        .baomingBox {
            .c {
                /* background: #fff; */
                border-radius: 7rpx;
                padding: 24rpx 80rpx;
                // background-image:url('https://images.tengfangyun.com/attachment/focus/20200613/502aec242ee96759457173024e9d486428b71e1d.jpeg');
                // background: url(../img/tehuiBg.jpg) repeat-y;
                background-size: 100%;
                margin: -2rpx 0;
                .inp {
                    background: #fff;
                    padding: 20rpx;
                    border: none;
                    width: 100%;
                    height: 100%;
                    outline: none;
                    box-sizing: border-box;
                    font-size: 32rpx;
                    margin-bottom: 32rpx;
                    border-bottom: 2rpx solid #5F11B5;
                }
                button {
                    background-image: linear-gradient(294deg, #ff7e24, #f84c32 40%, #ed0246);
                    border: none;
                    border-radius: 12rpx;
                    width: 100%;
                    box-sizing: border-box;
                    margin-top:70rpx;
                    font-size: 30rpx;
                    color: #fff;
                    &.baomingbtn_btn{
                        margin-top: 40rpx;
                    }
                }
            }
        }
        .footer {
            padding-bottom: 170rpx;
            color: #fff;
            padding-top: 20rpx;
            text-align: center;
        }
        .BtmMenu{
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            max-width: 1000px;
            margin:0 auto;
            background-image: linear-gradient(294deg, #ff7e24, #f84c32 40%, #ed0246);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
            -moz-box-shadow: 0px -2rpx 10rpx #d40033;
            -webkit-box-shadow: 0px -2rpx 10rpx #d40033;
            box-shadow: 0 -2rpx 10rpx #d40033;
            .BtmMenuitem {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 20rpx 0;
                font-size: 48rpx;
                color: #ffffff;
            }
        }
        .showBMpop {
            background: rgba(0, 0, 0, .8);
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 998;
        }

        .showBMpop .showBMpopInner {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            max-width: 1000px;
            margin: 0 auto;
        }

        .showBMpop .showBMpopInner .t {
            width: 80%;
            border-top-right-radius: 12rpx;
            border-top-left-radius: 12rpx;
            overflow: hidden;
        }

        .showBMpop .showBMpopInner .c {
            width: 80%;
            background: #fff;
            border-bottom-right-radius: 12rpx;
            border-bottom-left-radius: 12rpx;
            padding: 32rpx 28rpx;
            box-sizing: border-box;
        }

        .showBMpop .showBMpopInner .c input {
            padding: 30rpx 13rpx;
            width: 100%;
            box-sizing: border-box;
            border:  2rpx solid #6858de ;
            border-radius: 6.5rpx;
            margin-bottom: 20rpx;
            font-size: 32rpx;
            
        }
        .showBMpop .showBMpopInner .c .inp {
            padding: 30rpx 13rpx;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
            border:  2rpx solid #6858de ;
            border-radius: 6.5rpx;
            margin-bottom: 20rpx;
            font-size: 32rpx;
            
        }
        

        .showBMpop .showBMpopInner .c button {
            padding: 10rpx 13rpx;
            width: 100%;
            box-sizing: border-box;
            border-radius: 13rpx;
            margin-bottom: 20rpx;
            font-size: 32rpx;
            background-image: linear-gradient(294deg, #ff7e24, #f84c32 40%, #ed0246);
            border: none;
            color: #fff;
            font-weight: bold;
        }

        .showBMpop .showBMpopInner .line {
            width: 4rpx;
            background: #fff;
            height: 80rpx;
        }

        .showBMpop .showBMpopInner .close image {
            width: 72rpx;
            height: 72rpx;
        }

        .showTuijian{
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: rgba(0,0,0,.7);
            z-index: 998;
        }
        .showTuijianWrap{
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
        }
        .tuijianPop{
            position: relative;
            width: 100%;
            height:666rpx;
            // background: url('https://images.tengfangyun.com/attachment/focus/20200615/e7907526a19bcddf68776e78ee8ea9001cae26f6.png') no-repeat;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            max-width: 1000px;
            margin: 0 auto;
            
        }
        .tuijianPop .close{
            position: absolute;
            width: 47rpx;
            height: 47rpx;
            right: 47rpx;
            top: 0;
            image{
                width:100%;
                height: 100%;
            }
        }
        .tuijianPop .tuijianForm{
            padding: 204rpx 138rpx 0 138rpx;
        }
        .tuijianPop .tuijianForm .item{
            display: flex;
            
            align-items: center;
            height: 56rpx;
            background: #f6e8fd;
            border-radius:13.2rpx;
            max-width: 700px;
            margin: 0 auto 10rpx;
        }
        .tuijianPop .tuijianForm .item label{
            display: inline-block;
            padding: 0 13rpx;
            white-space: nowrap;
            width: 113rpx;
            text-align: center;
            font-size: 25rpx;

        }
        .tuijianPop .tuijianForm .item input{
            flex: 1;
            line-height: 100%;
            height: 100%;
            border: none;
            background: none;
        }
        .tuijianPop .tuijianForm .tuijianBtn{
            text-align: center;
            padding-top: 65rpx;
        }
        .tuijianPop .tuijianForm .tuijianBtn button{
            width: 352rpx;
            height: 86rpx;
            border: none;
            background: url('https://images.tengfangyun.com/attachment/focus/20200615/faad47d49002678262ffa734deb7691e29a39066.png') no-repeat;
            background-size: 100% 100%;
        }

        }
}
.flex-8{
    flex: 8;
}
.activity-content{
    margin-bottom: 32rpx;
    flex: 1;
    max-width: 100%;
    overflow: hidden;
}
input, select, textarea {
    outline: 0;
    -webkit-appearance: none;
    resize: none;
}
.tellinputbox {
    background: #fff;
    padding: 40rpx;
    border-radius: 8rpx;
    &.tellinputbox2 {
        margin-top:40rpx;
        box-shadow: 0 6rpx 20rpx rgba(0,0,0,.3);
        margin-bottom: 40rpx;
    }
    .txt1 {
        text-align: center;
        font-size: 40rpx;
        color: #4275ac;
        line-height: 50rpx;
        margin-bottom: 30rpx;
    }
    .inputborder {
        border: 2rpx solid #bbb;
        line-height: 76rpx;
        height: 80rpx;
        font-size: 30rpx;
        box-sizing: border-box;
        padding: 0 20rpx 0 180rpx;
        width: 100%;
        border-radius: 8rpx;
        position: relative;
        align-items: center;
        ~.inputborder{
            margin-top: 20rpx;
        }
        .input {
            border: 0;
            line-height: 76rpx;
            background: 0 0;
            color: #3C3B36;
            font-size: 30rpx;
            height: 100%;
        }
        span{
            position: absolute;
            left: 20rpx;
            color: #333;
            display: flex;
            align-items: center;
            b{
                color: #f35e5e;
                font-size: 36rpx;
                // margin-top: 10rpx;
                margin-left: 4rpx;
                vertical-align: top;
                display: inline-block;
                line-height: 36rpx;
            }
        }
        
    }
    .telbutton {
        line-height: 80rpx;
        height: 80rpx;
        background-image: linear-gradient(294deg,#ff7e24,#f84c32 40%,#ed0246);
        color: #fff;
        border: 0;
        border-radius: 8rpx;
        margin-top: 24rpx;
        font-size: 40rpx;
    }
}
.salepoint {
    position: absolute;
    left: 20%;
    right: 30%;
    top: 90rpx;
    text-align: center;
    font-size: 34rpx;
    line-height: 50rpx;
    color: #fff;
    font-weight: 700;
    text-shadow: 0 2rpx 2rpx rgba(0,0,0,.5);
    span {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.build-select {
    width: 100%;
    display: block;
    height: 76rpx;
    line-height: 76rpx;
    border: 0;
    background: 0 0;
    font-size: 30rpx;
    // background: url(../img/icon/xiala-icon.png) center right no-repeat;
    background-size: 20rpx;
}

.header{
    -moz-flex-grow: 0;
    -webkit-flex-grow: 0;
    -ms-flex-grow: 0;
    flex-grow: 0;
    -moz-flex-shrink: 0;
    -webkit-flex-shrink: 0;
    -ms-flex-shrink: 0;
    flex-shrink: 0;
    width: 100%;
    position: relative;
    margin-bottom: 50rpx;
    padding-bottom: 200rpx;
    .top-img{
        width: 100%;
        min-height: 600rpx;
    }

    .tellinputbox {
        position: absolute;
        left: 30rpx;
        right:30rpx;
        bottom: 0;
        background: #fff;
        padding: 40rpx;
        border-radius: 8rpx;
        box-shadow: 0 6rpx 20rpx rgba(0,0,0,.3);
        .txt1 {
            text-align: center;
            font-size: 40rpx;
            color: #4275ac;
            line-height: 50rpx;
            margin-bottom: 30rpx;
        }
        .txt2 {
            text-align: center;
            font-size: 24rpx;
            color: #222;
            line-height: 32rpx;
            margin-top: 24rpx;
        }
        .inputborder {
            border: 2rpx solid #d5714f;
            line-height: 76rpx;
            height: 80rpx;
            font-size: 30rpx;
            box-sizing: border-box;
            padding: 0 20rpx 0 180rpx;
            width: 100%;
            border-radius: 8rpx;
            position: relative;
            justify-content: flex-start;
            align-items: center;
            span{
                b{
                    color: #f35e5e;
                    font-size: 36rpx;
                    // margin-top:10rpx;
                    margin-left: 4rpx;
                    vertical-align: top;
                    display: inline-block;
                    line-height: 36rpx;
                }
            }
            .input {
                border: 0;
                line-height: 76rpx;
                background: 0 0;
                color: #3C3B36;
                font-size: 30rpx;
                height: 100%;
            }
        }
        .codebutton {
            line-height: 80rpx;
            height: 80rpx;
            border-radius: 8rpx;
            font-size: 32rpx;
            background: #fff;
            color: #3C3B36;
            border: 2rpx solid #ddd;
            outline: 0;
            float: right;
            width: 36.5%;
        }
        .code {
            margin-top: 20rpx;
            height: 80rpx;
            justify-content: space-between;
            align-items: center;
            .inputborder {
                padding-left: 10px;
                width: 60%;
                float: left;
            }
        }
        .telbutton {
            background: #d5714f;
            color: #fff;
            letter-spacing: 6rpx;
            line-height: 80rpx;
            height: 80rpx;
            border: 0;
            border-radius: 8rpx;
            margin-top:24rpx;
            font-size: 40rpx;
        }
    }
}
.navbox {
    background: #fff;
    padding: 30rpx 20rpx 20rpx;
    margin: -10rpx 0 40rpx;
    box-shadow: 0 6rpx 20rpx rgba(0,0,0,.2);
}
.buildwrap {
    padding-left: 30rpx;
    padding-right: 30rpx;
    .buildtips1 {
        color: #4275ac;
        font-size: 40rpx;
        line-height: 60rpx;
        text-align: center;
        padding: 0 0 20rpx;
    }
    .tagbox {
        line-height: 0;
        text-align: center;
        margin-bottom: -20rpx;
        text {
            display: inline-block;
            font-size: 28rpx;
            line-height:44rpx;
            padding: 0 16rpx;
            margin: 0 10rpx 20rpx;
            color: #fff;
            border-radius: 4rpx;
            
        }
    }
    .builditem{
        background: #fff;
        border-radius: 8rpx;
        box-shadow: 0 6rpx 20rpx rgba(0,0,0,.1);
        margin-top: 40rpx;
        overflow: hidden;
        .b-img {
            -moz-flex-grow: 0;
            -webkit-flex-grow: 0;
            -ms-flex-grow: 0;
            flex-grow: 0;
            -moz-flex-shrink: 0;
            -webkit-flex-shrink: 0;
            -ms-flex-shrink: 0;
            flex-shrink: 0;
            width: 100%;
            height: 440rpx;
            position: relative;
            margin-bottom: 30rpx;
            image {
                width: 100%;
            }
            
        }
        .b-data {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            .b-count {
                padding:0 30rpx 20rpx;
                line-height: 36rpx;
                color: #fff;
                text-shadow: 2rpx 2rpx 4rpx #000;
                position: relative;
                justify-content: space-between;
                align-items: center;
                .b-tell {
                    position: absolute;
                    right: 30rpx;
                    bottom:20rpx;
                    background: #FF4D30;
                    background: linear-gradient(90deg,#FC694F 0,#FD321D 100%);
                    box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0,.4);
                    border-radius: 8rpx;
                    height:60rpx;
                    font-size: 24rpx;
                    line-height: 60rpx;
                    padding: 0 16rpx;
                    text-shadow: none;
                    align-items: center;
                    justify-content: flex-start;
                    .tell-title{
                        margin-left: 10rpx;
                    }
                }
            }
            .b-title {
                background: rgba(0,0,0,.7);
                line-height: 96rpx;
                color: #fff;
                font-size: 32rpx;
                padding: 0 30rpx;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                display: block;
                .b-price {
                    color: #ff9a78;
                    float: right;
                }
            }
            
        }
        .b-intro {
            line-height: 36rpx;
            font-size: 24rpx;
            color: #333;
            margin-top: 12rpx;
            padding: 0 30rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
            &.long-intro {
                white-space: normal;
            }
        }
        
    }
}
.verify_block{
    position: fixed;
    left: 0;
    right: 0;
    top: 40vh;
    // bottom: 160rpx;
    background-color: #fff;
    z-index: 2
  }

.top-box{
    align-items: center;
    padding: 30rpx 10rpx;
    search{
        flex: 1;
    }
    .search {
        color: #28bdfb;
        border-radius: 40rpx;
        border: 3rpx solid #28bdfb;
        padding: 8rpx 20rpx;
        margin: 5rpx 20rpx;
        align-items: center;
    }

    .search text {
        color: #28bdfb;
        font-size: 24rpx;
    }
}
.buildAll {
    height: 60vh;
    width: 100%;
    overflow-y: auto;
    background: #fff;
    .uni-list {
        radio-group {
            padding-left: 50rpx;
        }

        .uni-list-cell {
            justify-content: flex-start;
            align-items: center;
            padding: 20rpx 0;
        }
    }
}
.footer-type4{
    color:#4275ac;
    padding-top: 20rpx;
    text-align: center;
}
.footer-type5{
    color:#ffe3a1;
    padding-top: 20rpx;
    text-align: center;
}
.new_year_mode{
    background: #BA3F3B;
}

.y-content{
    padding: 20rpx 0;
    background-position: 100% 50%;
    background-repeat: repeat;
        .time-rever{
            position: relative;
            padding: 0 48rpx;
            // background-position: 100% 100%;
            // background-repeat: no-repeat;
            
            .time-rever-content{
                background-position: 100% 100%;
                background-repeat: repeat-y;
                background-size: contain;
                // height: 76rpx;
                padding: 40rpx 20rpx;
                margin-top: -2rpx;
                position: relative;
                align-items: center;
                justify-content: space-between;
                span{
                    // z-index: 2;
                    color: #FFE3A1;
                }
                .item-bg{
                    padding: 20rpx 10rpx 20rpx 30rpx;
                    letter-spacing: 20rpx;
                    border-radius: 10rpx;
                    background-color: #A52C29;
                }
            }
            .time-rever-title{
                width: 100%;
                position: relative;
                image{
                    width: 100%;
                    height:100%;
                    display: block;
                    // margin-left: 50%;
                    // transform: translateX(-50%);
                    // z-index: 3;
                }
                .image_border{
                    width: 100%;
                    height:100%;
                    display: block;
                }
                .image_title{
                    position: absolute;
                    top: 0;
                    width: 180rpx;
                    left: 36%;
                }
                .iamge_text{
                    position: absolute;
                    top: 33%;
                    left: 37%;
                    border: 4rpx solid #e5ae73;
                    text-align: center;
                    height: 60rpx;
                    line-height: 60rpx;
                    color: #ffe3a1;
                    border-radius: 40rpx;
                    width: 160rpx;
                }
            }
        }
        .new-year{
            &.new-year-adviser{
                .new-year-content{
                    padding: 0 20rpx;
                }
            }
            .new-year-content{
                background-position: 100% 100%;
                background-repeat: repeat-y;
                background-size: contain;
                padding: 40rpx 20rpx;
                margin-top: -1px;
                color: #FFE3A1;
                position: relative;
                -webkit-box-align: center;
                -webkit-align-items: center;
                align-items: center;
                -webkit-box-pack: justify;
                -webkit-justify-content: space-between;
                justify-content: space-between;
                .adviser-list{
                    width: 100%;
                    background: none;
                    box-shadow: none;
                }
                &.new-year-video{
                    padding:0;
                    height:0;
                    overflow: hidden;
                    transition: 0.5s;
                    &.showVideo{
                        height: auto;
                        padding: 40rpx 50rpx;
                    }
                }
                .new_year_left{
                    max-width: 70%;
                    padding-left: 40rpx;
                }
                .new_year_right{
                    padding-right: 40rpx;
                    .getHui{
                        padding: 10rpx 20rpx;
                        background: #FFE3A1;
                        color: #A32C28;
                        border-radius: 40rpx;
                    }
                }
                .new_year_left_subtitle{
                    margin-top: 10rpx;
                    max-width: 90%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;

                }
                .new_year_left_title{
                    max-width: 95%;
                    overflow : hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }
                .video_box{
                    width: 100%;
                    video{
                        width: 100%;
                    }
                    div{
                        width: 100%;
                        height: 200px;
                    }
                }
                .build_box{
                    width: 100%;
                    background: #fff;
                    padding: 10rpx;
                    border-radius: 10rpx;
                    
                }
                .build_box_more{
                    flex-wrap: wrap;
                    flex:1;
                    .builds{
                        width: 42%;
                        margin-right: 2%;
                        margin-bottom: 10rpx;
                        background: #fff;
                        border-radius: 8rpx;
                        padding: 20rpx;
                        &:nth-child(2n){
                            margin-right: 0;
                        }
                        .builds-img{
                            width: 100%;
                            height:156rpx;
                            overflow: hidden;
                            image{
                                width: 100%;
                            }
                            
                        }
                    }
                }


            }
        }

        .build{
            background: #fff;
            padding: 10rpx;
            border-radius: 10rpx;
            .build-img{
                width: 260rpx;
                min-width: 260rpx;
                height:210rpx;
                border-radius: 8rpx;
                overflow: hidden;
                position: relative;
                margin-right: 20rpx;
                image{
                    width: 100%;
                    height: 100%;
                    min-height: 210rpx;
                }

            }
        }
    

        .build-info {
            .build-title{
                font-size: 36rpx;
                font-weight: 600;
                color: #151515;
            }
            .build-labels{
                margin: 10rpx 0;
                height: 40rpx;
                // white-space: nowrap;
                overflow: hidden;
            }
            .build-labels span{
                display: inline-block;
                padding: 4rpx;
                border: 2rpx solid #999;
                color: #999;
                font-size: 20rpx;
                margin-right: 8rpx;
                margin-bottom: 12rpx;
                border-radius: 4rpx;
                
            }
            .build-price{
                color: #666;
                font-size: 28rpx;
                .build-price-con{
                    font-size: 32rpx;
                    color:#FA4C52;
                    font-weight: 400;
                    margin-right: 4rpx;
                }

            }
            .build-youhui{
                margin: 10rpx auto;
                border-radius: 50rpx;
                padding: 10rpx 0;
                text-align: center;
                background-image: linear-gradient(to right,#FBAA65,#FB656A);
            }
        }

        .youhui-con{
            align-items: flex-start;
            font-size: 24rpx;
            color: #FA696D;
            height: 80rpx;
            line-height: 1.8;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            padding-left: 8rpx;
            .hui-con-hui{
                padding: 2rpx 4rpx;
                background:#FA555B;
                color: #fff;
                margin-right: 10rpx;
                overflow : hidden;

            }
        }
        .jianju{
            background-repeat: no-repeat;
            background-position: 100% 10% ;
            background-size: contain;
            padding-bottom: 48px;
        }
        .time-rever-bottom{
            margin-top: 96rpx;
        }
        // .richTextarea{
        //     margin-top: 40rpx;
        // }
        .bottom-bars{
            position: fixed;
            left:0;
            right: 0;
            bottom: 0;
            padding: 20rpx 96rpx;
            background: #BA3F3B;
            justify-content: space-between;
            align-items: center;
            background: none;
            z-index: 40;
            .bot-bar-btn{
                padding:20rpx 40rpx;
                background: #F9CE93;
                color: #631A19;
                text-align: center;
                border-radius: 40rpx;
                &.zixun{
                    margin-right: 10rpx;
                }
            }
            &.adv-share{
                padding: 20rpx 48rpx;
                background: #fff;
                // box-sizing: border-box;
                .adv-btns{
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                    font-size: 15px;
                    padding: 0;
                    margin: 0;
                    border-radius: 0;
                    color: #fff;
                    &.adv-ask{
                            background: #fbac65;
                            box-shadow: 0 0 4px 0 rgba(0,0,0,.05);
                            border-top-left-radius: 20px;
                            border-bottom-left-radius: 20px;
                    }
                    &.adv-tel{
                            background: linear-gradient(90deg,#fb656a,#fbac65);
                            box-shadow: 0 0 4px 0 rgba(255,80,0,.3);
                            border-top-right-radius: 20px;
                            border-bottom-right-radius: 20px;
                    }
                }
                .adverser-info{
                    justify-content: flex-start;
                    align-items: center;
                    flex: 1;
                    .adviser-header{
                            height: 64rpx;
                            width: 64rpx;
                            margin-right: 8rpx;
                            overflow: hidden;
                            border-radius: 64rpx;
                            image {
                                height: 64rpx;
                                width: 64rpx;
                                overflow: hidden;
                                border-radius: 64rpx; 
                            }
                    }
                    .adviser-info-name{
                        font-size: 24rpx;
                        line-height: 1;
                        max-width: 216rpx;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                    .adviser-info-type{
                        font-size: 24rpx;
                        margin-top:6rpx;
                        // line-height: 1;
                    }
                }
            }
        }
        .showBMpop {
            background: rgba(0, 0, 0, .8);
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 998;
            .pop_con{
                width: 100%;
                height: 100%;
                margin: 0 auto;
                position: relative;
            }
        }


        .showBMpop .showBMpopInner {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 900rpx;
            max-width: 1000px;
            margin: 0 auto;
            background-position: 100% 50%;
            background-repeat: no-repeat;
            background-size: cover;
            position: absolute;
            top: 50%;
            left: 0%;
            right: 0%;
            transform: translateY(-50%);
        }

        .showBMpop .showBMpopInner .t {
            width: 100%;
            // border-top-right-radius: 12rpx;
            // border-top-left-radius: 12rpx;
            overflow: hidden;
            image{
                display: block;
            }
        }
        .showBMpop .showBMpopInner .inner-box{
            border-radius: 16rpx;
            width: 80%;
            margin: 0 auto;
        }

        .showBMpop .showBMpopInner .c {
            width: 100%;
            // margin-top: -180rpx;
            // background: #fff;
            // border-bottom-right-radius: 12rpx;
            // border-bottom-left-radius: 12rpx;
            padding: 32rpx 64rpx;
            box-sizing: border-box;
            background-position: 100%;
            background-repeat: repeat-y;
            background-size: contain;
            .pop-youhui-info{
                text-align: center;
                padding: 20rpx 0 40rpx;
                color: #FFE3A1;
                font-size: 28rpx;
                .pop-info-title{
                    font-size: 36rpx;
                    font-weight: 500;
                    margin-bottom: 20rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }

            }
        }
         .showBMpop .showBMpopInner .c .item{
            padding: 30rpx 60rpx;
            width: 100%;
            box-sizing: border-box;
            border-radius: 60rpx;
            margin-bottom: 20rpx;
            background: #FFF;
            justify-content: flex-start;
            align-items: center;
         }
          .showBMpop .showBMpopInner .c .item label{
              white-space: nowrap;
              flex-wrap: nowrap;
          }
        .showBMpop .showBMpopInner .c input {
            
            font-size: 22rpx;
            border: none;
            
        }
        .showBMpop .showBMpopInner .c .inp {
            // padding: 30rpx 13rpx;
            // width: 100%;
            // height: 100%;
            // box-sizing: border-box;
            // border:  2rpx solid #6858de ;
            // border-radius: 6.5rpx;
            // margin-bottom: 20rpx;
            // font-size: 32rpx;
            
        }
        
        // .showBMpop .showBMpopInner .c .baomingbtn{
        //     margin-top: 130rpx;
            
        // }
        .showBMpop .showBMpopInner .c button {
            padding: 10rpx 13rpx;
            width: 100%;
            margin-top: 40rpx;
            box-sizing: border-box;
            border-radius: 80rpx;
            margin-bottom: 20rpx;
            font-size: 32rpx;
            background: #F9CE93;
            // background-image: linear-gradient(294deg, #ff7e24, #f84c32 40%, #ed0246);
            border: none;
            color: #B63332;
            font-weight: bold;
        }

        .showBMpop .showBMpopInner .line {
            width: 4rpx;
            background: #fff;
            height: 80rpx;
        }
        .showBMpop .showBMpopInner .close{
            // margin-top: 20rpx;
            bottom: -90rpx;
            position: absolute;
        }
        .showBMpop .showBMpopInner .close image {
            width: 72rpx;
            height: 72rpx;
        }
}
.sign_btn{
  position: fixed;
  right: 24rpx;
  bottom: 320rpx;
  z-index: 3;
  image{
    width: 128rpx;
    // height: 128rpx;
  }
}
 ::v-deep .popup-box.center{
    max-width: 1000px;
}
 ::v-deep .popup-box.bottom{
    max-width: 1000px;
}
 ::v-deep uni-actionsheet .uni-actionsheet{
  z-index:1001
}

.agreement{
		color: #999;
        // align-items: center;
        margin-bottom: 12px;
        justify-content: center;
		.title{
			text-decoration:underline
		}
        &.mbtm0{
            margin-bottom: 0;
        }
        &.type4{
            margin-top:20rpx;
        }
	}
	.check_login{
		padding: 24rpx 48rpx;
		text-align: center;
		color: #666;
	}
	.check_box{
		margin-right: 15rpx;
        width: 40rpx;
        height: 40rpx;
	}
	.no_checked{
		display: inline-block;
		width: 36rpx;
		height: 36rpx;
		border-radius: 50%;
		box-sizing: border-box;
		border: 4rpx solid #dedede;
	}
</style>
