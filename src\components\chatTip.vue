<template>
    <view class="chat_tip" :class="{show}" @click="toFriendList()">
        <text>您有{{num>99?'99+':num}}条未读消息</text>
        <my-icon type="ic_into" color="#f65354" size="36rpx"></my-icon>
    </view>
</template>

<script>
    import myIcon from './myIcon'
    export default {
        data () {
            return {
                num:0,
                show:false
            }
        },
        created(){
            // uni.$once('showChatTip',(num)=>{
            //     console.log(num)
            //     this.num = num
            //     if(this.num>0){
            //         this.show = true
            //         setTimeout(()=>{
            //             this.show = false
            //         },10000)
            //     }
            // })
            this.getUnReadCount()
        },
        components: {
            myIcon
        },
        methods: {
            toFriendList(){
                this.show = false
                uni.switchTab({
                    url:'/pages/index/chat_list'
                })
            },
            getUnReadCount(){
				// this.timer = setInterval(()=>{
					if(!uni.getStorageSync('token')){
						return
					}
					this.$ajax.get('im/unRead',{},res=>{
						if(res.data.uncount){
                            this.num = res.data.uncount
                            uni.setTabBarBadge({
                                index:3,
                                text:res.data.uncount>99?'99+':res.data.uncount+''
                            })
                            setTimeout(()=>{
                                this.show = true
                            },2000)
                            // setTimeout(()=>{
                            //     this.show = false
                            // },12000)
                            // clearInterval(this.timer)
						}
					},err=>{
						console.log(err)
					}, {disableAutoHandle:true})
				// },10000)
			}
        }
    }
</script>

<style scoped lang="scss">
.chat_tip{
    flex-direction: row !important;
    align-items: center;
    height: 66upx;
    line-height: 40upx;
    padding: 13upx 20upx;
    box-sizing: border-box;
    position: fixed;
    z-index: 990;
    bottom: 250upx;
    right: -500upx;
    border: 1upx solid #f7f7f7;
    color: $uni-color-primary;
    font-size: 26upx;
    background-color: #f7f7f7;
    border-top-left-radius: 33upx;
    border-bottom-left-radius: 33upx;
    box-shadow: 0 0 10upx #bbb;
    transition: 0.36s;
    &.show{
        right: 0;
    }
}
</style>