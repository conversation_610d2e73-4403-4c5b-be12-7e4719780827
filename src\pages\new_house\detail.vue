<template>
  <view class="new_house_content" :class="{style2: page_style == 2}" v-if ="showCon&&loading">
    <view class="top_nav flex-row bottom-line" :style="{opacity:opacity}">
      <view class="nav_item" v-for="(item, index) in top_navs" :key="index" :class="{active:current_nav_index===index}" @click="scroppTo(item.id, index)">{{item.name}}</view>
    </view>
    <!-- 焦点图 -->
    <view class="focus-box">
      <swiper
        class="banner"
        :indicator-dots="false"
        :circular="true"
        :duration="300"
        indicator-active-color="#f65354"
        @change="swiperChange"
        :current="swiperCurrent"
      >
        <swiper-item v-for="item in focus" :key="item.id">
          <view v-if="item.type === 'live'&&weapp_appid&&item.room" class="swiper-item">
            <image :src="(item.cover || img[0]) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/live.png" mode="widthFix"></image>
            <div style="width: 100%;height: 100%;position: absolute;left: 0; right: 0;overflow: hidden">
              <wx-open-launch-weapp :username="weapp_appid" :path="'/online/next?roomId='+item.room">
              <script type="text/wxtag-template">
                <div class="weapp" style="width: 750px; height: 680px"></div>
              </script>
              </wx-open-launch-weapp>
            </div>
          </view>
          <view v-if="item.type === 'vr'" class="swiper-item" @click="toVr">
            <image :src="(item.cover || detail.vr_cover || img[0]) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/vr_b.png" mode="widthFix"></image>
          </view>
          <view v-if="item.type === 'video'" class="swiper-item" @click="preVideo()">
            <image :src="(item.cover || item.url) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/video.png" mode="widthFix"></image>
          </view>
          <view v-if="item.type === 'img'" class="swiper-item" @click="preImgs(img, 0, 0)">
            <image :src="item.url | imageFilter('w_6401')" mode="aspectFill"></image>
          </view>
          <view v-if="item.type === 'huxing'" class="swiper-item" @click="preImgs(img, 8, 0)">
            <image :src="item.url | imageFilter('w_6401')" mode="aspectFill"></image>
          </view>
        </swiper-item>
      </swiper>
      <view class="img-total" :class="{style2: page_style===2}">共{{ focusLen }}张</view>
      <view class="cate-box" :class="{style2: page_style===2}">
        <view class="cate-list flex-row" :class="{no_bg: swiper_type_count<2}">
          <view  class="cate" :class="cateActive == cateName.value ? 'active' : ''" v-for ="(cateName, i) in cusBtn " :key ="i" @click="switchFocus(cateName.value,i)">
            {{cateName.name}}
          </view>
          <!-- <view
            v-if="live_num > 0"
            class="cate"
            @click="switchFocus('live')"
            :class="cateActive == 'live' ? 'active' : ''"
            >直播</view
          >
          <view
            v-if="vr_num > 0"
            class="cate"
            @click="switchFocus('vr')"
            :class="cateActive == 'vr' ? 'active' : ''"
            >VR</view
          >
          <view
            v-if="video_num > 0"
            class="cate"
            @click="switchFocus('video')"
            :class="cateActive == 'video' ? 'active' : ''"
            >视频</view
          >
          <view
            v-if="img.length > 0"
            class="cate"
            @click="switchFocus('img')"
            :class="cateActive == 'img' ? 'active' : ''"
            >图片</view
          > -->
        </view>
      </view>
      <view class="card-btn fixed card-btn-bg flex-box" style="left:20rpx" @click.prevent.stop="$navigateBack()">
        <my-icon type="ic_back" color="#fff" size="44rpx"> </my-icon>
      </view>
      <view class="card-btn fixed card-btn-bg flex-box" @click.prevent.stop="showSharePop">
        <my-icon type="ic_fenxiang" color="#fff" size="34rpx"> </my-icon>
      </view>
      <!-- 微信公众号开放标签 -->
      <!-- <div class="card-btn live-bar flex-box" v-if="weapp_appid&&live_num===1">
        <my-icon type="zhibo" color="#fff" size="40rpx" > </my-icon>
        <wx-open-launch-weapp class="flex-1" :username="weapp_appid" :path="'/online/next?roomId='">
        <script type="text/wxtag-template">
          <style>
            #horse{
                overflow: hidden;
                text-overflow:ellipsis;
                white-space:nowrap;
                flex:1;
                height: 26px;
                color:#fff;
                max-width: 100%;
            }
            .horse_title{
              padding: 3px 10px;
              height: 25px;
              line-height: 25px;
              max-width: 70%;
            }
          </style>
          <div id="horse">
            <span class="horse_title">{{horseText}}</span> 
          </div>
        </script>
        </wx-open-launch-weapp>
      </div> -->
      <view v-if="hb_info.is_open" class="hongbao"   @click="toHbHelp()">
        <view class="hb_content" :animation="num == 0 ? showpic : hidepic" >
          <image class ="hb_img"   src ='/static/icon/1.png'></image>
          
        </view>
        <view class="hb_content hb_content1" :animation="num == 1 ?showpic : hidepic ">
          <image class ="hb_img"   src ='/static/icon/2.png'></image>
        </view>
        
        <!-- <view class="hb_content" :animation="animationData">
          <image  src ='/static/icon/1.png'></image>
          <image  src ='/static/icon/2.png'></image>
        </view> -->
        
        <!-- <image :src="hongbao_gif" mode="aspectFill" @click="toHbHelp()"></image> -->
        <!-- <image :src="saihongbao_gif" mode="aspectFill" @click="toHongbao()"></image> -->
      </view>
    </view>
    <!-- 认证 -->
    <view class="container" v-if="detail.flagship_is_show">
      <view class="rz_strip flex-row" style="padding-top: 24rpx; padding-bottom: 24rpx">
        <image class="rz_icon" :src="'/build/icon/rz.png' | imageFilter('m_320')"></image>
        <view class="text_block">
          <text class="text">官方认证</text>
          <text class="text label" v-if="detail.flagship_name">{{detail.flagship_name}}</text>
          <text v-show="!detail.flagship_name&&detail.build_cate" class="text label" v-for="(label, index) in detail.build_cate" :key="index">{{ label }}</text>
        </view>
      </view>
    </view>
      <!-- 楼盘标题 -->
      <view class="build_title-box flex-row container" id="build">
        <view class="title-row flex-1">
          <view class="build_title flex-row">
            <text class="title">{{ detail.title }}</text>
            <!-- <text class="status" :class="'status' + detail.leixing">{{ detail.status_name }}</text> -->
             <text class="status" :style="{background:detail.status_color}">{{ detail.status_name }}</text>
            <!-- <my-icon type="ic_zoushi" color="#ff656b" size="42rpx"></my-icon> -->
          </view>
          <view class="build_label flex-row">
            <view class="rank" v-if="detail.ranks&&detail.ranks.id">
              <image class="icon" :src="detail.ranks.icon"></image>
              <view class="text">{{detail.ranks.title}}</view>
            </view>
            <text class="label" v-for="(label, idx) in detail.build_type" :key="idx">{{ label }}</text>
          </view>
        </view>
        <view v-if="page_style===1" class="jump_btn flex-row" @click="$navigateTo('/pages/new_house/info?id=' + id)">
          <text>详情</text>
          <my-icon type="ic_into" size="32rpx" color="#999"></my-icon>
        </view>
      </view>
      <!-- 样式2 -->
      <template v-if="page_style===2">
      <view class="address_row flex-row bottom-line">
        <view class="text flex-1">
          <text>{{detail.address}}</text>
        </view>
        <view class="right flex-row" @click="openLocation">
          <image class="icon" :src="'/build/v_3/location.png' | imageFilter('m_320')"></image>
          <text>导航</text>
          <!-- <my-icon type="ic_into" size="28rpx" color="#989898"></my-icon> -->
        </view>
      </view>
      <template v-if="modules.nav&&modules.nav.is_show">
      <view class="options1 flex-row">
        <view class="item" v-for="(item, index) in options1_list" :key="index" @click="toOptionPath(item)">
          <image class="icon" :src="item.icon | imageFilter('m_320')"></image>
          <text class="text">{{item.title}}</text>
        </view>
      </view>
      <!-- <view class="options1 options2 flex-row">
        <view class="item" v-for="(item, index) in options2_list" :key="index" @click="toOptionPath(item)">
          <image class="icon" :src="item.icon | imageFilter('m_320')"></image>
          <text class="text">{{item.text}}</text>
        </view>
      </view> -->
      <view class="options1 options3 flex-row">
        <view class="item" v-for="(item, index) in options2_list" :key="index" @click="toOptionPath1(item)">
          <image class="icon" :src="item.icon | imageFilter('m_320')"></image>
          <text class="text">{{item.title}}</text>
        </view>
      </view>
      </template>
      <view class="container" style="margin-top: 0">
        <!-- <view class="label">
          <text>开盘信息</text>
        </view> -->
        <view class="info_list">
          <view class="item">
            <text class="label">参考{{detail.price_type}}:</text>
            <view style="display: block">
              <text class="value highlight">{{detail.build_price}}{{detail.price_unit}}</text>
              <text v-if="detail.price_desc" class="text" style="margin-left: 12rpx; color: #999">{{detail.price_desc}}</text>
              <view style="display: inline-block;" class="btn highlight" v-if="is_open_im&&detail.open_adviser">
                <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 9)">
                  <view class="flex-row items-center">
                    <text>[询底价]</text>
                  </view>
                </chatBtn>
              </view>
            </view>
          </view>
          <!-- <view v-if="detail.price_desc" class="item" style="margin-top:-8rpx">
            <text class="label"></text>
            <view class="price_desc">
              <text class="text">{{detail.price_desc}}</text>
              <view class="btn-item" v-if="is_open_im&&detail.open_adviser">
                <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 9)">
                  <view class="flex-row items-center">
                    <text>[询底价]</text>
                  </view>
                </chatBtn>
              </view>
            </view>
          </view> -->
          <view class="item">
            <text class="label">参考总价:</text>
            <view class="flex-row">
              <text class="value highlight">{{detail.reference_price}}{{detail.reference_price==='不详'?'':'起'}}</text>
              <view style="margin-left: 8rpx" @click="showTip">
                <my-icon type="tishifu" color="#d8d8d8" size="32rpx" lineHeight="48rpx"></my-icon>
              </view>
              <view class="btn highlight" v-if="is_open_im&&detail.open_adviser">
                <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 5)">
                  <view class="flex-row items-center">
                    <text>[咨询首付比例]</text>
                  </view>
                </chatBtn>
              </view>
              <view class="btn highlight" v-else @click="toSubForme(2)">
                <text>[调价通知我]</text>
              </view>
            </view>
          </view>
          <view class="item">
            <text class="label">最新开盘:</text>
            <view class="flex-row">
              <text class="value">{{detail.kptime||'不详'}}</text>
              <view class="highlight btn" @click="toSubForme(1)">[开盘通知我]</view>
            </view>
          </view>
          <view class="item">
            <view class="flex-row">
              <text class="label">主力户型:</text>
              <text class="value">建面约{{detail.mj?((detail.mj+'')||'').replace('m²',''):'不详'}}<text class="unit" v-if="detail.mj">m²</text></text>
              <view class="highlight btn" @click="$navigateTo(`/pages/new_house/house_type_list?bid=${detail.id}`)">[全部户型]</view>
            </view>
          </view>
          <!-- <view class="item">
            <text class="label">楼盘地址:</text>
            <view style="display: block">
              <text class="value">{{detail.address}}</text>
              <text class="highlight btn" @click="sendAddressToPhone">[发送到手机]</text>
            </view>
          </view> -->
        </view>
      </view>
      </template>
      <!-- 风格1新版楼盘信息 -->
      <!-- <view v-if="page_style === 1" class="build_info-box container">
        <view class="info_row flex-row">
          <view class="label">参考{{detail.price_type}}</view>
          <view style="display: block">
            <text class="value highlight">{{detail.build_price}}{{detail.price_unit}}</text>
            <text v-if="detail.price_desc" class="text" style="margin-left: 12rpx; color: #999">{{detail.price_desc}}</text>
            <view style="display: inline-block;" class="btn highlight" v-if="is_open_im&&detail.open_adviser">
              <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 9)">
                <view class="flex-row items-center">
                  <text>[询底价]</text>
                </view>
              </chatBtn>
            </view>
          </view>
        </view>
        <view class="info_row flex-row">
          <text class="label">参考总价</text>
          <view class="flex-row">
            <text class="value highlight">{{detail.reference_price}}{{detail.reference_price==='不详'?'':'起'}}</text>
            <view style="margin-left: 8rpx" @click="showTip">
              <my-icon type="tishifu" color="#d8d8d8" size="32rpx" lineHeight="48rpx"></my-icon>
            </view>
            <view class="btn highlight" v-if="is_open_im&&detail.open_adviser">
              <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 5)">
                <view class="flex-row items-center">
                  <text>[咨询首付比例]</text>
                </view>
              </chatBtn>
            </view>
            <view class="btn highlight" v-else @click="toSubForme(2)">
              <text>[调价通知我]</text>
            </view>
          </view>
        </view>
        <view class="info_row flex-row">
          <view class="label">最新开盘</view>
          <view class="value">
            <text>{{detail.kptime||'不详'}}</text>
            <view class="highlight btn" @click="toSubForme(1)">[开盘通知我]</view>
          </view>
        </view>
        <view class="info_row flex-row">
          <view class="label">主力户型</view>
          <view class="value">建面约{{detail.mj?(detail.mj||'').replace('m²',''):'不详'}}<text class="unit" v-if="detail.mj">m²</text>
          <view class="btn highlight" @click="$navigateTo(`/pages/new_house/house_type_list?bid=${detail.id}`)">[全部户型]</view>
          </view>
        </view>
        <view class="info_row flex-row">
          <view class="label">开发商</view>
          <view class="value">
            <text>{{detail.kfs}}</text>
          </view>
        </view>
        <view class="info_row flex-row">
          <view class="label">楼盘地址</view>
          <view class="value flex-1">
            <text>{{detail.address}}</text>
          </view>
          <view class="right" @click="openLocation">
            <text>去售楼部</text>
            <view style="display: inline-block">
              <view class="daohang_box">
                <my-icon type="daohang" size="22rpx" color="#fff"></my-icon>
              </view>
            </view>
          </view>
        </view>
      </view> -->
      <template v-if="page_style === 1">
        <view class="build_info-box flex-row container">
          <view class="info_item">
            <view class="data flex-row">
              <text class="value">{{detail.build_price}}</text>
              <text class="unit">{{detail.price_unit}}</text>
            </view>
            <view class="label flex-row" @click="$navigateTo('/propertyData/price_trend/price_trend?id=' + id + '&type=1')">
              <text class="text">参考{{detail.price_type}}</text>
              <view class="icon-box">
                <my-icon type="ic_zoushi" color="#d8d8d8" size="24rpx"></my-icon>
              </view>
            </view>
          </view>
          <view class="info_item left-line right-line center">
            <view class="data flex-row">
              <text class="value">{{detail.reference_price||'不详'}}</text>
              <text class="unit" v-if="detail.reference_price&&detail.reference_price!='不详'">起</text>
            </view>
            <view class="label flex-row">
              <text class="text">参考总价</text>
              <view class="icon-box" @click="showTip">
                <my-icon type="tishifu" color="#d8d8d8" size="28rpx"></my-icon>
              </view>
            </view>
          </view>
          <view class="info_item">
            <view class="data flex-row" v-if="detail.mj_ranges!=''">
              <text class="value" @click="jm">点击查看</text>
            </view>
            <view class="data flex-row" v-else>
              <text class="value">{{detail.mj?(detail.mj||'').replace('m²',''):'不详'}}</text>
              <text class="unit" v-if="detail.mj">m²</text>
            </view>
            <view class="label flex-row">
              <text class="text">建面范围</text>
            </view>
          </view>
        </view>
        <view class="price_desc" v-if="detail.price_desc">
          <text class="text">价格说明：{{detail.price_desc}}</text>
          <view class="btn-item" v-if="is_open_im&&detail.open_adviser">
            <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 1)">
              <view class="flex-row items-center">
                <text>[询底价]</text>
                <!-- <my-icon type="ic_into" color="#ff656b" size="32rpx"></my-icon> -->
              </view>
            </chatBtn>
          </view>
        </view>
      </template>

      <!-- 400电话 -->
      <view class="tel-box2 container" v-if="page_style===2&&detail.use_middle_number && show_build_tel" @click="handleTel()">
        <view class="tel_block flex-row">
          <view class="tel_left">
            <my-icon type="ic_tel" size="20rpx" color="#fff"></my-icon>
          </view>
          <view class="tel_right">
            <view class="text-box">
              <text class="tel">致电售楼处了解更多信息</text>
            </view>
            <view class="btn">咨询</view>
            <!-- <view class="text-box" v-if="detail.use_middle_call == 0 || is_open_middle_num == 0">
              <text class="tel">{{
                detail.phone && detail.sellmobile_part ? detail.phone + ' 转 ' + detail.sellmobile_part : detail.tel
              }}</text>
              <text class="tip">最新政策，更多优惠详情，请致电售楼处</text>
            </view>
            <view class="text-box" v-else-if="detail.use_middle_call > 0 && is_open_middle_num == 1">
              <text class="tel">点击查看售楼处电话</text>
              <text class="tip">平台将使用隐私加密 保护您的真实号码</text>
            </view> -->
          </view>
        </view>
        <view class="tel_bg"></view>
      </view>
      <template v-if="page_style===1">
      <!-- 楼盘地址 -->
      <view class="address-box flex-row container" @click="openLocation">
        <my-icon type="ic_map" size="36rpx"></my-icon>
        <text class="address">{{ detail.address }}</text>
        <view class="jump_btn flex-row">
          <text>导航</text>
          <my-icon type="ic_into" size="32rpx" color="#999"></my-icon>
        </view>
      </view>
      <!-- 获取优惠 -->
      <view class="coupon-box container" v-if="detail.build_discount&&detail.build_discount.content">
        <image class="bg_img" :src="'/images/new_icon/quan.png' | imageFilter('m_8601')"></image>
        <view class="coupon_container flex-row">
          <text class="coupon_name flex-1">{{detail.build_discount.content}}</text>
          <view class="desc">
            <text class="btn" v-if="detail.build_discount.group_id" @click="$navigateTo(`/pages/groups/detail?id=${detail.build_discount.group_id}`)">获取优惠</text>
            <text class="btn" v-else @click="toSubForme(3, 5)">获取优惠</text>
            <text class="coupon_content" v-if="detail.build_discount.endtime">结束时间{{detail.build_discount.endtime}}</text>
          </view>
        </view>
      </view>
      <!-- 400电话 -->
      <view class="tel-box container" v-if="detail.use_middle_number && show_build_tel" @click="handleTel()">
        <view class="tel_block flex-row">
          <view class="tel_left">
            <view class="text-box" v-if="detail.use_middle_call == 0 || is_open_middle_num == 0">
              <text class="tel">{{
                detail.phone && detail.sellmobile_part ? detail.phone + ' 转 ' + detail.sellmobile_part : detail.tel
              }}</text>
              <text class="tip">最新政策，更多优惠详情，请致电售楼处</text>
            </view>
            <view class="text-box" v-else-if="detail.use_middle_call > 0 && is_open_middle_num == 1">
              <text class="tel">点击查看售楼处电话</text>
              <text class="tip">平台将使用隐私加密 保护您的真实号码</text>
            </view>
          </view>
          <view class="tel_right">
            <my-icon type="ic_tel" size="46rpx" color="#fff"></my-icon>
          </view>
        </view>
        <view class="tel_bg"></view>
      </view>
      </template>
      <!-- 榜单 -->
      <!-- <view class="rank container" v-if="detail.ranks&&detail.ranks.id" :style="{backgroundImage: `url(${rank_bg})`}" @click="toRanks(detail.ranks)">
        <image class="img" mode="widthFix" :src="detail.ranks.icon"></image>
        <view class="text">
          <text>{{detail.ranks.title}}</text>
        </view>
        <my-icon type="ic_into" size="32rpx" color="#fff"></my-icon>
      </view> -->

      <!-- 预售进度 -->
      <view v-if="modules.presell&&modules.presell.is_show&&modules.presell.list.length>0" class="container">
        <view class="label">楼盘时刻</view>
        <view class="progress_container" @click='toSaleNews'>
          <SalesProgress :list="modules.presell.list" />
        </view>
        <view class="tip" v-if="modules.presell.current_descp">{{modules.presell.current_descp}}</view>
        <!-- <view class="labels flex-row">
          <view class="item hightlight">一房一价</view>
          <view class="item hightlight">报名规则</view>
          <view class="item">复核名单</view>
          <view class="item">摇号结果</view>
        </view> -->
      </view>

      <!-- 获取优惠 -->
      <view class="container" v-if="page_style===2&&detail.build_discount&&detail.build_discount.content">
        <view class="label">优惠活动</view>
        <view class="coupon-box2">
          <image class="bg_img" :src="'/build/v_3/coupon1.png' | imageFilter('m_8601')"></image>
          <view class="coupon_container flex-row">
            <view class="flex-1">
              <text class="coupon_name">{{detail.build_discount.content}}</text>
              <text class="coupon_content" v-if="detail.build_discount.endtime">结束时间{{detail.build_discount.endtime}}</text>
              <view class="peoples flex-row">
                <view class="headers flex-row">
                  <image v-for="(item, index) in group_headers" :key="index" mode="aspectFill" :src="item | imageFilter('w_80')" class="header_img"></image>
                </view>
                <view class="text">{{detail.groupCount}}人已获得优惠</view>
              </view>
            </view>
            <view class="desc">
              <text class="btn" v-if="detail.build_discount.group_id" @click="$navigateTo(`/pages/groups/detail?id=${detail.build_discount.group_id}`)">获取优惠</text>
              <text class="btn" v-else @click="toSubForme(3, 5)">获取优惠</text>
            </view>
          </view>
        </view>
      </view>
      <template v-if="page_style === 2">
      <!-- 购房群 -->
      <view class="group flex-row container" v-if="wxqunopen">
        <view class="left">
          <view class="title">买房，哪些楼盘值得购买？</view>
          <view class="peoples flex-row">
            <view class="headers flex-row">
              <image v-for="(item, index) in group_headers" :key="index" mode="aspectFill" :src="item | imageFilter('w_80')" class="header_img"></image>
            </view>
            <view class="text">{{detail.groupCount}}人已加入群聊</view>
          </view>
        </view>
        <view class="btn" @click="showJoinGroup">立即进群</view>
      </view>
      <view v-if="modules.detail&&modules.detail.is_show" class="container info_list_box" style="margin-top: 24rpx">
        <view class="label">
          <text>楼盘详细信息</text>
          <text class="more" @click="$navigateTo('/pages/new_house/info?id=' + id)">更多楼盘详情</text>
        </view>
        <view class="info_list">
          <!-- <view class="item">
            <text class="label">参考{{detail.price_type}}:</text>
            <text class="value highlight">{{detail.build_price}}{{detail.price_unit}}</text>
            <view class="btn highlight" @click="toSubForme(2)">[调价通知我]</view>
          </view> -->
          <view class="item">
            <text class="label">建筑类型:</text>
            <text class="value" v-if="detail.build_type">{{detail.build_type.join(' ')}}</text>
          </view>
          <view class="flex-row">
            <view class="flex-1">
              <view class="item">
                <text class="label">物业类型:</text>
                <text class="value">{{detail.other||'待更新'}}</text>
              </view>
              <view class="item">
                <text class="label">交房标准:</text>
                <text class="value">{{detail.jflx||'待更新'}}</text>
              </view>
              <view class="item">
                <text class="label">占地面积:</text>
                <text class="value">{{detail.zdsize?detail.zdsize+(detail.zdsize_unit==0?"m²":'亩'):'待更新'}}</text>
              </view>
              <view class="item">
                <text class="label">建筑面积:</text>
                <text class="value">{{detail.jzsize?(detail.jzsize||'').replace('m²','')+'m²':'不详'}}</text>
              </view>
            </view>
            <view class="right" style="flex-shrink: 0" v-if="wxsmcode">
              <image class="wxsmcode_img" mode="widthFix" :src="wxsmcode" />
            </view>
          </view>
          <!-- <view class="item">
            <text class="label">面积区间:</text>
            <text class="value">建面约{{detail.jzsize?(detail.jzsize||'').replace('m²',''):'不详'}}<text class="unit" v-if="detail.jzsize">m²</text></text>
          </view> -->
          <view class="item">
            <text class="label">容积率:</text>
            <text class="value">{{detail.dfl||'待更新'}}</text>
          </view>
          <view v-show="show_details_all">
          <view class="item">
            <text class="label">绿化率:</text>
            <text class="value">{{detail.lhl||'待更新'}}</text>
          </view>
          <view class="item">
            <text class="label">房屋产权:</text>
            <text class="value">{{detail.cqnx||'待更新'}}</text>
          </view>
          <view class="item">
            <text class="label">预售证:</text>
            <text class="value">{{yushou.xkzh||'待更新'}}</text>
          </view>
          <view class="item">
            <text class="label">总户数:</text>
            <text class="value">{{detail.total_hushu||'待更新'}}</text>
          </view>
          <view class="item">
            <text class="label">物业公司:</text>
            <text class="value">{{detail.wygs||'待更新'}}</text>
          </view>
          <view class="item">
            <text class="label">物业费:</text>
            <text class="value">{{detail.wyf||'待更新'}}</text>
          </view>
          <view class="item">
            <text class="label">开发商:</text>
            <text class="value">{{detail.kfs||'待更新'}}</text>
          </view>
          <!-- <view class="item">
            <text class="label">售楼处:</text>
            <text class="value">{{detail.selladdress||'待更新'}}</text>
          </view> -->
          <view class="item">
            <text class="label">楼盘地址:</text>
            <view style="display: block">
              <text class="value">{{detail.address}}</text>
              <text class="highlight btn" @click="sendAddressToPhone">[发送到手机]</text>
            </view>
          </view>
          </view>
          <view class="open_close" @click="show_details_all = !show_details_all">{{show_details_all?'收起全部信息':'展开全部信息'}} <my-icon :type="show_details_all?'ic_close':'ic_open'" size="36rpx" color="#6F6F6F"></my-icon></view>
        </view>
      </view>
      </template>

      <!-- 荆门的销控表按钮 -->
      <view class="jm_url" v-if="detail.jm_url" @click="$navigateTo(detail.jm_url)">
        <text>点击查看楼盘销控表</text>
      </view>
      <!-- 报名按钮 -->
      <view class="btn_list-box flex-row container">
        <view class="btn-item flex-row" @click="toSubForme(1)">
          <my-icon type="tongzhi" color="#ff656b" size="42rpx"></my-icon>
          <text>认筹开盘通知</text>
        </view>
        <view class="btn-item flex-row" @click="toSubForme(2)">
          <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon>
          <text>调价通知我</text>
        </view>
      </view>
      <!-- 最新动态 -->
      <view class="container news" id="news" v-if="modules.news&&modules.news.is_show&&((news&&news.id)||(yushou&&yushou.id)||(build_open&&build_open.title)||detail.sub_content)">
        <view class="label">
          <text>楼盘动态</text>
          <text class="more" @click="$navigateTo('/pages/new_house/buildNews?bid=' + id)">更多</text>
        </view>
        <view class="news_list">
          <view class="news_item" v-if="news&&news.id" @click="$navigateTo('/pages/news/detail?id=' + news.id)">
            <view class="time flex-row">
              <text class="attr">资讯</text>
              <text>{{news.ctime}}</text>
            </view>
            <view class="title">{{news.title}}</view>
            <view class="sub_title">{{news.sub_title}}</view>
          </view>
          <view class="news_item official" v-for="(share, index) in adviser_share" :key="index">
            <view class="time flex-row" @click="$navigateTo('/pages/consultant/detail?id=' + share.adviser_id)">
              <text class="attr">官方</text>
              <text>{{share.ctime}}</text>
            </view>
            <view class="adviser_info" @click="$navigateTo('/pages/consultant/detail?id=' + share.adviser_id)">
              <image class="prelogo" mode="aspectFill" :src="share.prelogo | imageFilter('w_80')"></image>
              <text class="text">{{share.cname}}</text>
              <text class="label">置业顾问</text>
              <chat-btn :user_login_status="login_status" :user_id="share.uid" :identity_id="share.adviser_id" :from_type="3" @ok="advAsk">
                <view class="chat_btn flex-row">
                  <my-icon type="ic_zixun1" color="#fff" size="32rpx"></my-icon>
                  <text>咨询</text>
                </view>
              </chat-btn>
            </view>
            <shareItem :share="share" mode="small" :show_bottom_line="index<adviser_share.length-1" :show_date="false" @click="$navigateTo(`/pages/community/detail?id=${share.id}`)" @clickvoice="voice_playing_index===index?voice_playing_index=-1:voice_playing_index=index" @voiceEnded="voice_playing_index=-1" @voiceError="voice_playing_index=-1" @clickpraise="handlePraise($event, index)" :voice_playing="voice_playing_index==index" />
            <!-- <view class="sub_title">
              <view>{{share.content}}</view>
              <view class="image_list">
                <image class="share_img" mode="aspectFill" v-for="img in share.img" :key="img" :src="img | imageFilter('w_80')"></image>
                <view class="share_empty"></view>
                <view class="share_empty"></view>
                <view class="share_empty"></view>
                <view class="share_empty"></view>
              </view>
            </view> -->
          </view>
          <view class="news_item kaipan" v-if="build_open&&build_open.title">
            <view class="time flex-row">
              <view class="time_box flex-row"> 
                <text class="attr">{{build_open.label||'开盘'}}</text>
                <text>{{build_open.update_time || ''}}</text>
              </view>
              <view v-if="navs[2].is_show===1&&(navs[2].operation===1||navs[2].operation===2)" @click.stop.prevent="toYuyue(3, navs[2])">
                  <view class="chat_box">
                    <view class="chat_btn flex-row">
                      <my-icon type="ic_zixun1" color="#fff" size="32rpx"></my-icon>
                      <text>咨询</text>
                    </view>
                  </view>
              </view>
              <view v-if="navs[2].is_show===1&&(navs[2].operation===3||navs[2].operation===4)" @click.stop.prevent="cusList(navs[2].operation)">
                  <view class="chat_box">
                    <view class="chat_btn flex-row">
                      <my-icon type="ic_zixun1" color="#fff" size="32rpx"></my-icon>
                      <text>咨询</text>
                    </view>
                  </view>
              </view>
            </view>
            <view class="title">{{build_open.title}}</view>
            <view class="sub_title">{{build_open.descp}}</view>
          </view>
          <view class="news_item jiaofang" v-if="detail.sub_content" @click="$navigateTo('/pages/new_house/info?id=' + detail.id)">
            <view class="time flex-row">
              <text class="attr">项目</text>
              <text>{{detail.add_time}}</text>
            </view>
            <!-- <view class="title">{{build_open.title}}</view> -->
            <view class="sub_title">{{detail.sub_content}}</view>
          </view>
          <view class="news_item cert" v-if="yushou&&yushou.id" @click="toYushou(yushou.id)">
            <view class="time flex-row">
              <view class="time_box flex-row"> 
                <text class="attr">证件</text>
                <text>{{yushou.tjrq}}</text>
              </view>
              <view v-if="navs[2].is_show===1&&(navs[2].operation===1||navs[2].operation===2)" @click.stop.prevent="toYuyue(3, navs[2])">
                <view class="chat_box">
                  <view class="chat_btn flex-row">
                    <my-icon type="ic_zixun1" color="#fff" size="32rpx"></my-icon>
                    <text>咨询</text>
                  </view>
                </view>
              </view>
              <view v-if="navs[2].is_show===1&&(navs[2].operation===3||navs[2].operation===4)" @click.stop.prevent="cusList(navs[2].operation)">
                <view class="chat_box">
                  <view class="chat_btn flex-row">
                    <my-icon type="ic_zixun1" color="#fff" size="32rpx"></my-icon>
                    <text>咨询</text>
                  </view>
                </view>
              </view>
            </view>
            <view class="title">{{yushou.xkzh}}</view>
            <view class="sub_title1 flex-row" v-if="yushou.xmmc">
              <text>项目名称</text>
              <view>{{yushou.xmmc}}</view>
            </view>
            <view class="sub_title1 flex-row" v-if="yushou.xsts">
              <text>销售套数</text>
              <view>{{yushou.xsts}}</view>
            </view>
            <view class="sub_title1 flex-row" v-if="yushou.hprq">
              <text>领证时间</text>
              <view>{{yushou.hprq}}</view>
            </view>            
            <view class="sub_title1 flex-row" v-if="yushou.xkzh">
              <text>预售许可</text>
              <view>{{yushou.xkzh}}</view>
            </view>            
            <view class="sub_title1 flex-row" v-if="yushou.gsmc">
              <text>开发公司</text>
              <view>{{yushou.gsmc}}</view>
            </view>
            <!-- <view class="sub_title">{{yushou.xmmc}}{{yushou.fenlei}}</view> -->
          </view>
          <view class="add_build_news">
            <view class="add_build_news_btn" @click ="toSubForme(5)">
              订阅最新楼盘动态
            </view>
          </view>
        </view>
      </view>
      <!-- <template v-if="page_style === 1"> -->
      <!-- 选房大厅 -->
      <view class="louceng container" id="house" v-if="house_list.length > 0">
        <view class="label">
          <text>在线选房</text>
        </view>
        <view class="louceng_list">
          <swiper class="house_swiper" :style="{ height: house_list[0].length > 2 ? '67vw' : '32vw' }" next-margin="100rpx">
            <swiper-item v-for="(houses, index) in house_list" :key="index">
              <choose-house
                :house_list="houses"
                theme="custom"
                :show_ceng="false"
                @onClick="handleClick"
              ></choose-house>
            </swiper-item>
          </swiper>
        </view>
      </view>
      <!-- </template> -->
      <!-- 广告位 -->
      <view class="container" v-if="advs&&advs.length>0">
        <swiper class="ext_swiper" autoplay :interval="3000">
          <swiper-item v-for="(item, index) in advs" :key="index" @click="$navigateTo(item.wap_link)">
            <image :src="item.image | imageFilter('w_8601')" mode="widthFix"></image>
            <view class="marker"  v-if="item.is_show_label==1">广告</view>
          </swiper-item>
        </swiper>
      </view>
      <!-- 热卖房源 -->
      <template v-if="modules.hot_house&&modules.hot_house.is_show&&modules.hot_house.list.length>0">
        <view class="container hot_sale" :style="{backgroundImage: `url('${hot_bg_img}')`}">
          <view class="label">
            <text>热卖房源（{{modules.hot_house.count}}）</text>
            <text class="more pd-r-48" @click="$navigateTo(`/online/sale_list?build_id=${detail.id}&sand_id=${sand_id}`)">更多房源</text>
          </view>
          <view class="texts flex-row">
            <view class="text">
              <view class="icon">
                <my-icon type="ic_shoucang_red" color="#fff" size="22rpx"></my-icon>
              </view>
              <text>一房一价</text>
            </view>
            <view class="text">
              <view class="icon">
                <my-icon type="ic_shoucang_red" color="#fff" size="22rpx"></my-icon>
              </view>
              <text>价格透明</text>
            </view>
          </view>
          <view class="house_list_container">
            <view class="house_list">
              <SaleHouse :list="modules.hot_house.list" />
            </view>
          </view>
        </view>
      </template>
      <!-- 发现好房 -->
      <view class="build_box container" v-if="find_build.length>0">
        <view class="label">
          <text>发现好房</text>
          <text class="more pd-r-48" @click="$navigateTo('/pages/new_house/new_house')">更多</text>
        </view>
        <swiper class="build-swiper" :duration="260" display-multiple-items="2" next-margin="120rpx">
          <swiper-item v-for="(item,idx) in find_build" :key="idx">
            <view class="swiper-item" @click="$navigateTo('/pages/new_house/detail?id='+item.id)">
              <image class="img" :src="item.img | imageFilter('w_240')" mode="aspectFill"></image>
              <text class="title">{{item.title}}</text>
              <text class="price">{{item.build_price}}{{item.price_unit}}</text>
            </view>
          </swiper-item>
        </swiper>
      </view>
      <template v-if="page_style===1">
      <!-- 购房群 -->
      <view class="group flex-row container" v-if="wxqunopen">
        <view class="left">
          <view class="title">买房，哪些楼盘值得购买？</view>
          <view class="peoples flex-row">
            <view class="headers flex-row">
              <image v-for="(item, index) in group_headers" :key="index" mode="aspectFill" :src="item | imageFilter('w_80')" class="header_img"></image>
            </view>
            <view class="text">{{detail.groupCount}}人已加入群聊</view>
          </view>
        </view>
        <view class="btn" @click="showJoinGroup">立即进群</view>
      </view>
      </template>
      <!-- 户型图 -->
      <view class="house_img-box container" id="huxing" v-if="houseTypePic.length>0">
        <view class="label">
          <text>户型图</text>
          <text class="more" @click="$navigateTo(`/pages/new_house/house_type_list?bid=${detail.id}`)">更多</text>
        </view>
        <swiper class="house_img-list" :duration="300" :display-multiple-items="2" next-margin="88rpx">
          <swiper-item v-for="(item, idx) in houseTypePic" :key="idx">
            <view class="swiper-item" @click="$navigateTo(`/pages/new_house/photo?bid=${detail.id}&img_id=${item.id}`)">
              <view class="img-box">
                <image :src="item.img  | imageFilter('w_320')" mode="aspectFill"></image>
                <image v-if="item.vr" class="vr-icon" src="/static/icon/vr_b.png" mode="widthFix"></image>
              </view>
              <view class="house_type">{{ item.desc }}</view>
              <view class="aligin-end flex-row">
                <text class="stw">{{ item.shi || 0}}室{{ item.ting || 0}}厅{{ item.wei || 0}}卫</text>
                <text class="status" :class="item.status_class" v-if="item.sale_status">{{
                  item.sale_status_text
                }}</text>
              </view>
              <view class="mj-box flex-row">
                <view class="flex-row">
                  <text>约</text>
                  <text class="mianji">{{ item.mianji || '--'}}㎡</text>
                </view>
                <view class="db_btn" @click.prevent.stop="addContrast(item.id)">+对比</view>
              </view>
              <view class="add_type_price">
                <view class="add_type_price_btn" @click.prevent.stop ="toSubForme(9)">
                  获取户型报价
                </view>
              </view>
            </view>
          </swiper-item>
          <swiper-item v-if="houseTypePic.length < 2"></swiper-item>
        </swiper>
      </view>
      <!-- 楼盘测评报告 -->
      <view class="test-box container" >
        <view class="label">
          <text>楼盘测评报告</text>
        </view>
        <view class="bg3" @click="toAnalyse">
          <image :src="'/ditu/img/bg3.png' | imageFilter('w_6401')" mode="widthFix" />
          <view class="flex-row"> 
            <text>限时免费解锁楼盘价值分析报告</text>
            <view>立即解锁</view>
          </view>
        </view>
        <view class="test_con flex-row" @click="toAnalyse">
          <view class="test_con_item flex-1">
            <view class="test_con_item_icon">
              <image mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
            </view>
            <view class="test_con_item_title">
              项目优势
            </view>
            <view class="test_con_item_sub_title">
              优劣势点评
            </view>
          </view>
          <view class="test_con_item flex-1">
            <view class="test_con_item_icon">
              <image mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
            </view>
            <view class="test_con_item_title">
              户型分析
            </view>
            <view class="test_con_item_sub_title">
              哪个值得买
            </view>
          </view>
          <view class="test_con_item flex-1">
            <view class="test_con_item_icon">
              <image mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
            </view>
            <view class="test_con_item_title">
              功能配套
            </view>
            <view class="test_con_item_sub_title">
              社区内外配套
            </view>
          </view>
          <view class="test_con_item flex-1">
            <view class="test_con_item_icon">
             <image mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
            </view>
            <view class="test_con_item_title">
              区域分析
            </view>
            <view class="test_con_item_sub_title">
              区位交通教育
            </view>
          </view>
        </view>
        <view class="test_submit" @click ="showTestSubmit">
          免费领取
        </view>
        
      </view>
      <!-- 周边 -->
      <view class="map_location-box container" v-if="!hideMap" id ="zhoubian">
        <view class="label">
          <text>周边</text>
          <text class="more" @click="viewMap()">地图</text>
        </view>
        <view class="flex-row map_address">
          <text class="label">项目地址：</text>
          <text class="value">{{detail.address}}</text>
        </view>
        <!-- <mapNearby :scale="mapData.scale" :lat="detail.yzhou" :lng="detail.xzhou" :markers="mapData.covers" @clickMap="viewMap()" @clickCate="getCovers"></mapNearby> -->
        <mapNearby :scale="mapData.scale" :cirles ="cirles" :enableZoom="false" :enableScroll ='false' :lat="detail.yzhou" :lng="detail.xzhou" :markers="mapData.covers" @clickMap="viewMap()" @clickCate="getCovers"></mapNearby>
      </view>
      <!-- 发送地址  导航 -->
      <view class="btn_list-box flex-row container">
        <!-- <view class="btn-item flex-row" >
          <my-icon type="tongzhi" color="#ff656b" size="42rpx"></my-icon>
          <text>发送地址到手机</text>
        </view> -->
        <!-- #ifdef MP-WEIXIN -->
        <!-- <chat-btn :user_login_status="login_status" user_id="" identity_id="" @ok="sendAddressToPhone(12)"> -->
        <view class="btn-item flex-row" @click ='sendAddressToPhone' v-if="adress_code ==1">
          <view class="img">
            <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
          </view> 
          <!-- <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon> -->
            <text>发送地址到手机</text>
          
        </view>
       <!--  </chat-btn> -->
        <chat-btn :user_login_status="login_status" user_id="" identity_id="" @ok="openLocation"> 
        <view class="btn-item flex-row" >
          <view class="img">
            <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
          </view>
          <!-- <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon> -->
            <text>导航至项目地址</text>
          
        </view>
        </chat-btn>
        <!-- #endif -->
        <!-- #ifndef MP-WEIXIN -->
        <view class="btn-item flex-row" @click ="sendAddressToPhone()"  v-if="adress_code ==1">
          <view class="img">
            <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
          </view>
          <!-- <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon> -->
            <text>发送地址到手机</text>
          
        </view>
        <view class="btn-item flex-row" @click ="openLocation">
          <view class="img">
            <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
          </view>
          <!-- <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon> -->
          <text>导航至项目地址</text>
        </view>
        <!-- #endif -->
      </view>
      <!-- 置业顾问挂载-->
      <view class="block container" v-if ="consuList&&consuList.length>0&&!sid">
        <view class="label">
          <text>{{mountTitle}}</text>
          <text class="more" @click="cusList()">更多</text>
        </view>
        <view class="advier-list">
          
          <view
            class="adviser-item flex-row"
            v-for="item in consuList"
            :key="item.id"
            @click="consuDetail(item.adviser_id)"
          >
            <view class="header_img">
              <image mode="widthFix" :src="item.prelogo | imageFilter('w_120')"></image>
            </view>
            <view class="info">
              <view class="name flex-row">
                <text class="text">{{ item.cname || item.typename }}</text>
              </view>
              <view class="data">
                <text>{{ item.traffic_volume }}人咨询过他</text>
                <!-- <text v-if="service_show_zixunliang==1">{{ item.traffic_volume }}人咨询过他</text>
                <text v-else>{{item.build_names||item.introduce||''}}</text> -->
              </view>
            </view>
            <view class="adviser-right">
              <view class="btn-list flex-row">
                <view class="btn">
                  <chat-btn
                    :user_login_status="login_status"
                    :user_id="item.mid||item.uid||item.id"
                    :identity_id="item.adviser_id||item.uid||item.id"
                    :from_type="3"
                    @ok="advAsk"
                  >
                    <view class="icon-box">
                      <my-icon type="ic_zixun1" size="45rpx" color="#ff656c"></my-icon>
                    </view>
                  </chat-btn>
                </view>
                <view class="btn" v-if="(item.adviser_id && switch_adviser_tel) || !item.adviser_id ">
                  <tel-btn :user_id="item.mid||item.uid||item.id" :identity_id="item.adviser_id||item.uid||item.id" :tel="item.tel" @ok="handleTel($event, item)">
                    <view class="icon-box">
                      <my-icon type="ic_dianhua1" size="45rpx" color="#ff656c"></my-icon>
                    </view>
                  </tel-btn>
                </view>
              </view>
            </view>
          </view>

        </view>
        <!-- 聊天咨询按钮 -->
          <view class="btn_list-box flex-row" v-if="is_open_im&&detail.open_adviser">
            <view class="btn-item">
              <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 1)">
                <view class="flex-row">
                  <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon>
                  <text>咨询房价</text>
                </view>
              </chatBtn>
            </view>
            <view class="btn-item">
              <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 2)">
                <view class="flex-row">
                  <my-icon type="huxing" color="#ff656b" size="42rpx"></my-icon>
                  <text>咨询户型</text>
                </view>
              </chatBtn>
            </view>
          </view>
      </view>
      <!-- 电子沙盘 -->
      <view id="loudong" class="top-20 loudong container" v-if="sand_navs.length > 0">
        <view class="label" @click="toLoudong()">楼栋信息</view>
        <tab-bar v-if="sand_navs.length > 1" :tabs="sand_navs" :nowIndex="tabNowIndex" @click="handelCate" :fixedTop="false"></tab-bar>
        <view class="loudong_card">
          <template v-if="sand_info.width">
              <movable
              height="55vw"
              :map_img="sand_info.img"
              :map_info="sand_info"
              :mark_point="sand_point"
              :filterList ="filterList"
              @clickPoint="onClickPoint"
            ></movable>
          </template>
        </view>
      </view>
        <!-- 报名按钮 -->
      <view class="btn_list-box flex-row container" v-if="sand_navs.length > 0">
        <view class="btn-item flex-row" @click="toSubForme(10)">
          <view class="img">
            <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
          </view>
          <!-- <my-icon type="tongzhi" color="#ff656b" size="42rpx"></my-icon> -->
          <text>余房查询</text>
        </view>
        <view class="btn-item flex-row" @click="toSubForme(11)">
          
          <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon>
          <text>成交价查询</text>
        </view>
      </view>
      <!-- 用户点评 -->
      <view class="comment-box container" id="comment">
        <view class="label">
          <text>用户点评</text>
          <text class="more" @click="toCommentList" v-if="commentList.length > 0">更多</text>
        </view>
        <view class="comment-list">
          <view
            v-if="comment_first&&comment_first.is_show&&comment_first.content"
            class="comment-item flex-row"
          >
            <image class="header_img" :src="comment_first.prelogo | imageFilter('w_80')"></image>
            <view class="comment_info">
              <view>
                <view class="name flex-row">
                  <text class="user_name">{{siteName}}</text>
                  <image class="level" mode="widthFix" :src="'/build/v_3/official.png' | imageFilter"></image>
                </view>
                <view class="comment_title">
                  <text>{{comment_first.content}}</text>
                  <text>加我微信拉你进群：</text>
                  <text class="highlight">{{wxq.number}}</text>
                  <text class="copy_btn" @click="copyWechatNum(wxq.number)">复制微信</text>
                </view>
              </view>
              <view class="btn_row" v-if="wxqunopen">
                <view class="btn" @click="showJoinGroup">
                  <view class="icon_box">
                    <my-icon type="weixin" color="#fff" size="26rpx"></my-icon>
                  </view>
                  <text>加入群聊</text>
                </view>
                <view class="btn" @click="showQrcode" style="margin-left:10rpx">
                  <view class="icon_box">
                    <my-icon type="weixin" color="#fff" size="26rpx"></my-icon>
                  </view>
                  <text>立即进群</text>
                </view>
              </view>
            </view>
          </view>
          <view
            class="comment-item flex-row"
            v-for="(comment, index) in commentList"
            :key="index"
            @click="toCommunity(comment.id, index)"
          >
            <image class="header_img" :src="comment.img | imageFilter('w_80')"></image>
            <view class="comment_info">
              <view>
                <text class="user_name">{{ comment.cname || comment.mcname || comment.nickname }}</text>
                <text class="comment_title">{{ comment.content }}</text>
              </view>
              <view class="comment_time">
                <text class="time">{{ comment.time }}</text>
                <view class="options-box flex-row">
                  <view class="comment_num">
                    <my-icon type="ic_xiaoxi" size="30rpx" color="#d8d8d8"></my-icon>
                    <text>{{ comment.reply_count }}</text>
                  </view>
                  <view class="praise_num" @click.stop.prevent="handlePraise(index)">
                    <my-icon type="ic_zan" size="30rpx" :color="comment.is_praise?'#ff656b':'#d8d8d8'"></my-icon>
                    <text>{{ comment.praise || 0 }}</text>
                  </view>
                </view>
              </view>
              <view class="reply-list" v-if="comment.reply.length > 0">
                <view class="reply-item flex-row" v-for="(reply, idx) in comment.reply" :key="idx">
                  <view class="name_info flex-row">
                    <image class="reply_user_header_img" :src="reply.prelogo | imageFilter('w_80')"></image>
                    <text class="prely_user_name">{{ reply.cname }}</text>
                    <text class="identity" v-if="reply.IsAdv">置业顾问</text>
                    <text>回复：</text>
                  </view>
                  <text class="reply_content">{{ reply.content }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="comment_btn flex-row" @click="$navigateTo('/user/community/add_post?id=' + detail.id)">
          <my-icon type="ic_pinglun" color="#FB656A"></my-icon>
          <text>我要评论</text>
        </view>
      </view>
      <!-- 楼盘问答 -->
      <view class="house-question comment-box container" v-if="question">
        <view class="label">
          <text>大家都在问</text>
          <text class="more" @click="$navigateTo(`/ask/list?id=` + id)" v-if="question">更多</text>
        </view>
        <view class="question-box" v-if="question" @click="$navigateTo('/ask/detail?id=' + question.id)">
          <view class="question">
            <view class="title-icon question-icon">问</view>
            <view class="question-span">
              <view class="question-content">{{ question.question }}</view>
              <view class="question-info">
                <view>{{ question.cname }}</view>
                <view>{{ question.update_time }}</view>
              </view>
            </view>
          </view>
          <view class="answer" v-if="question.answer_list && question.answer_list.length">
            <view class="title-icon answer-icon">答</view>
            <view class="answer-content">
              <view class="answer-text">
                <view
                  class="text"
                  :class="{ 'show-all': question.answer_list[0].moreState == 2 }"
                  id="questionView"
                  @click="toDetail(question.answer_list[0].question_id)"
                >
                  <text id="questionText">{{ question.answer_list[0].answer_content }}</text>
                </view>
              </view>
              <view class="answer-info">
                <view class="answer-author">
                  <image :src="question.answer_list[0].prelogo | imageFilter('w_120')" class="author-icon"></image>
                  <text class="answer-name">{{ question.answer_list[0].answer_cname }}</text>
                  <text v-if="question.answer_list[0].position === 1">购房咨询师</text>
                  <text v-if="question.answer_list[0].position === 2">置业顾问</text>
                  <text v-if="question.answer_list[0].position === 3">经纪人</text>
                </view>
                <view class="answer-num" @click.stop="praise(question.answer_list[0])">
                  <my-icon type="ic_zan" :color="!question.answer_list[0].is_praise ? '#cccccc' : '#F65354'"></my-icon>
                  <text>有用({{ question.answer_list[0].praise_count }})</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-if="question" class="more-question comment_btn" @click="showConsult">
          <my-icon type="ic_pinglun" color="#FB656A"></my-icon>
          <text>我要提问</text>
        </view>
        <view v-if="!question" class="no_qa">
          <image class="no_qa-img" :src="no_qaSrc | imageFilter('w_8601')" mode="widthFix"></image>
          <view class="no_qa-content">这个楼盘还没有相关问题，赶快提问吧</view>
          <view class="no_qa-btn" @click="showConsult">
            <my-icon type="ic_pinglun" color="#fff" size="26"></my-icon>
            <text>我要提问</text>
          </view>
        </view>
        <my-popup ref="consult" position="center" height="auto" :touch_hide="true" @click="stopMove">
          <question-popup :bid="id" :isLogin="isLogin" @closePopup="closeConsult"></question-popup>
        </my-popup>
      </view>
      <!-- 推荐楼盘 -->
      <view class="recommend_house-box container" id="tuijian" v-if="recommend_house.length > 0">
        <view class="label">推荐楼盘</view>
        <view class="recommend_house-list">
          <block v-for="(item, index) in recommend_house" :key="index">
            <new-house-item :item-data="item" type="new_house"></new-house-item>
          </block>
        </view>
      </view>

      <!-- 免责声明 -->
      <view class="shengming container">
        <view class="shengming_title">免责声明</view>
        <view class="shengming_content" v-html="disclaimer"></view>
      </view>

      <!-- 底部操作菜单 -->
      <view class="bottom-bar flex-row" v-if ='shareUserInfo.id||shareUserInfo.mid'>
        <view class="flex-box foot_left flex-row" @click="shareDetail" >
          <view class="left">
            <image :src="shareUserInfo.prelogo" mode ="aspectFill"></image>
          </view>
          <view class="right">
              <text class="name">{{shareUserInfo.cname}}</text>
              <view class="sub_name flex-box">
                <text style="margin-right:16upx;">{{shareType==2?"经纪人":'置业顾问'}}</text>
              </view>

          </view>
      </view>
       <!-- #ifndef MP -->
       <view  class=" flex-box bar-btn btn1 flex-1" @click="shareAsk()">
        <view>在线咨询</view>
      </view>
      <view  v-if='(switch_adviser_tel &&shareType ==1) ||shareType==2' class=" flex-box bar-btn btn2 flex-1" @click="shareTel()">
        <view>拨打电话</view>
      </view>

      <!-- #endif -->
      <!-- #ifdef MP-->
        <button v-if="login_status==1" open-type="getUserInfo" @getuserinfo="onGetUserInfo" class=" flex-box text-center to-consu view_attr" @click="saveChatInfo(cusArr.mid,cusArr.id)">
          <view>在线咨询</view>
        </button>
        <button v-else-if="login_status==2" open-type="getPhoneNumber" @getphonenumber="onGetPhonenumber" class=" flex-box text-center to-consu view_attr" @click="saveChatInfo(cusArr.mid,cusArr.id)">
          <view>在线咨询</view>
        </button>
        <view v-else class=" flex-box text-center to-consu" @click="cusList()">
          <view>在线咨询</view>
        </view>

        <template v-if='(switch_adviser_tel &&shareType ==1) ||shareType==2'>
        <button v-if="login_status==1" open-type="getUserInfo" @getuserinfo="onGetUserInfo" class="flex-box text-center to-tel view_attr" @click="saveTelInfo()">
            <view>拨打电话</view>
          </button>
          <button v-else-if="login_status==2" open-type="getPhoneNumber" @getphonenumber="onGetPhonenumber" class="flex-box text-center to-tel view_attr" @click="saveTelInfo()">
            <view>拨打电话</view>
          </button>
          <view v-else class="flex-box text-center to-tel" @click="handelTel()">
            <view>拨打电话</view>
          </view>
        </template>
      <!-- #endif -->

      </view>
      <view class="bottom-bar flex-row" v-else>
        <view class="bar-left flex-row flex-1">
          <view v-if="show_build_tel && cusArr && cusArr.mid && is_open_adviser && detail.open_adviser" class="icon-btn" @click="consuDetail(cusArr.adviser_id)">
            <image :src="cusArr.prelogo | imageFilter('w_120')" class="header_img"></image>
            <text>{{cusArr.cname || cusArr.typename}}</text>
          </view>
          <view v-else class="icon-btn" @click="handleFollow">
            <my-icon :type="is_follow?'ic_guanzhu_red':'ic_guanzhu'" :color="is_follow?'#ff656b':'#666'" size="50rpx"></my-icon>
            <text>关注</text>
          </view>
          <view class="icon-btn" v-if="navs[0].is_show&&(navs[0].operation===1||navs[0].operation===2)" @click="toYuyue(3, navs[0])">
            <my-icon type="yuyue" color="#666" size="50rpx"></my-icon>
            <text>{{navs[0].name}}</text>
          </view>
          <view class="icon-btn" v-if="navs[0].is_show&&(navs[0].operation===3||navs[0].operation===4)" @click="cusList( navs[0].operation)">
            <my-icon type="ic_zixun" color="#666" size="50rpx"></my-icon>
            <text>{{navs[0].name}}</text>
          </view>
          <view class="icon-btn" v-if="navs[1].is_show===1" @click="toContrast">
            <text class="badge" v-if="login_status>1&&contrastCount>0">{{contrastCount>99?'99+':contrastCount}}</text>
            <text class="badge" v-if="login_status<=1&&$store.state.temp_huxing_contrast_ids.length>0">{{$store.state.temp_huxing_contrast_ids.length>99?'99+':$store.state.temp_huxing_contrast_ids.length}}</text>
            <my-icon type="pk" color="#666" size="50rpx"></my-icon>
            <text>{{navs[1].name}}</text>
          </view>
        </view>
        <!-- 置业顾问按钮 -->
        <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show===1&&(navs[2].operation===1||navs[2].operation===2)" @click="toYuyue(3, navs[2])">{{navs[2].name}}</view>
        <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show===1&&(navs[2].operation===3||navs[2].operation===4)" @click="cusList(navs[2].operation)">{{navs[2].name}}</view>
        <!-- 咨询售楼处按钮 -->
        <view class="flex-1" :class="{alone:navs[2].is_show===0}" v-if="navs[3].is_show===1">
          <tel-btn :user_login_status="login_status" @ok="handleTel">
          <view class="bar-btn btn2">{{navs[3].name}}</view>
        </tel-btn>
        </view>
      </view>
      
      <!-- 分享选项 -->
      <share-pop ref="share_popup" @copyLink="show_share_tip=true" @appShare="appShare" @handleCreat='handleCreat' @showCopywriting='showCopywriting'></share-pop>
    <!-- 复制分享文案 -->
    <my-popup ref="text_popup" position="center" :height="text_popup_height">
      <view class="copy-text-box" id="copy-text">
        <view class="title">{{detail.title}}</view>
        <view class="info-box">
          <view class="info-row flex-row">
            <text class="label">参考{{detail.price_type}}：</text>
            <text class="value">{{detail.build_price}}{{detail.price_unit}}</text>
          </view>
          <view class="info-row flex-row" v-if="detail.build_discount&&detail.build_discount.content">
            <text class="label">优惠信息：</text>
            <text class="value highlight">{{detail.build_discount.content}}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">参考总价：</text>
            <text class="value">{{getTotalPrice(detail.minPrice, detail.maxPrice)}}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">主力户型：</text>
            <text class="value">{{(detail.mj||'')}}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">项目地址：</text>
            <text class="value">{{(detail.address)}}</text>
          </view>
          <view class="button disabled-btn flex-row" v-if="copy_success">
            <my-icon type="check-circle" size="30rpx" color="#999"></my-icon>
            <text class="text">文本已复制</text>
          </view>
          <view class="button" v-else @click="copywriting">复制文本</view>
        </view>
      </view>
    </my-popup>

    <!--购房群 -->
    <my-popup ref="qrCode" position="bottom">
      <view class="qr-code">
        <!-- <view class="title">{{detail.title}}微信群</view> -->
        <view class="title">购房交流群</view>
        <view class="desc flex-box">
          <view class="wechat-img">
            <image :src="wxqunewm" mode="widthFix"></image>
          </view>
          <view class="info">
            <!-- #ifndef H5 -->
            <view class="save-btn" @click="saveImg()">保存到相册 扫码加入</view>
            <!-- #endif -->
            <!-- #ifdef H5 -->
            <view class="save-btn">长按左侧图片 识别二维码</view>
            <!-- <a class="save-btn" :href="wxqunewm" :download="`wechat_${id}.jpg`">长按左侧图片 识别二维码</a> -->
            <!-- #endif -->
            <view class="tip"
              >或添加客服号<text class="color-red">{{ wxqunkefu }}</text
              >为好友，拉你进群</view
            >
            <view class="copy-btn" @click="copyWechatNum(wxqunkefu)">点击复制微信号</view>
          </view>
        </view>
      </view>
    </my-popup>
    
    <my-popup ref="test_submit" position="bottom">
      <view class="test_submit_con">
        <view class="test_submit_close" @click="$refs.test_submit.hide()">×</view>
        <view class="test_submit_title flex-row">
        <view class="title">专属置业报告</view>
        <!-- <view class="test_submit_sub_title">已有11人领取</view> -->
        </view>
        <view class="test_submit_info">为方便您接收置业报告信息,请输入手机号</view>
        
        <!-- <view class="desc flex-box"> -->
          <input type="text" placeholder="请输入手机号" v-model="submit_tel">
        <!-- </view> -->
        <view class="submit_test_info" @click ="submit_test_con">确认提交</view>
      </view>
    </my-popup>
    <!-- #ifndef MP-BAIDU || MP-TOUTIAO  -->
    <hb ref="hb" :popupData="popup"></hb>
    <!-- #endif -->

    <!-- 报名弹窗 -->
    <sub-form
      :groupCount="detail.groupCount"
      :sub_type="sub_type"
      :sub_mode="sub_mode"
      ref="sub_form"
      @onsubmit="handleSubForm"
      :login_status="login_status"
    ></sub-form>
    <send-address-to-phone
      ref="sub_send_form"
      :sub_mode="sub_mode"
      @onsubmit="handleSubFormPhone"
      @signUp ="handleSubFormTel"
      :login_status="login_status"
    ></send-address-to-phone>
    <!-- #ifndef MP-WEIXIN -->
    <enturstBtn v-if="shareUserInfo.agent_id||shareUserInfo.adviser_id" :to_user="shareUserInfo" @click="$refs.enturst_popup.show()" />
    <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false" @show="weituo_is_show=true" @hide="weituo_is_show=false">
      <enturstBox ref="enturst_box" @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="showLoginPopup('为方便您及时接收消息通知，请输入手机号码')" :to_user="shareUserInfo" />
    </my-popup>
    <!-- 登录弹窗 -->
    <login-popup ref="login_popup" @onclose="toLoginPage" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
    <!-- #endif -->
    <my-popup ref="tip_popup" position="center" height="396rpx">
      <view class="tip_container">
        <view class="title">温馨提示</view>
        <view class="desc">由楼盘均价乘以面积计算得出，实际总价请咨询售楼处</view>
        <view class="btn" @click="$refs.tip_popup.hide()">好的，知道了</view>
      </view>
    </my-popup>
    <chat-tip></chat-tip>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <full-screen :show_full="show_full" :bg_img="detail.flagship_bg_img" @hide="show_full=false">
      <view class="full_box">
        <view class="build_icon" v-if="detail.flagship_logo">
          <image mode="aspectFill" :src="detail.flagship_logo | imageFilter('w_240')" ></image>
        </view>
        <view class="media_box">
          <image class="icon_btn" v-if="vr_num>0" @click="toVr" src="/static/icon/vr_b.png" mode="widthFix"></image>
          <view class="icon_btn font" @click="preImgs(img, 0, 0)">
            <my-icon type="image" color="#fff" size="44rpx" lineHeight="80rpx"></my-icon>
          </view>
        </view>
        <view class="full_content">
          <view class="bg">
          <image :style="{height: systemInfo.windowHeight+'px'}" :src="detail.flagship_bg_img | imageFilter('w_1200')" mode="aspectFill"></image>
          </view>
          <view class="rz_strip flex-row">
            <image class="rz_icon" :src="'/build/icon/rz.png' | imageFilter()"></image>
            <view class="text_block">
              <text class="text">官方认证</text>
              <text class="text" v-if="detail.flagship_name">{{detail.flagship_name}}</text>
              <text v-show="!detail.flagship_name&&detail.build_cate" class="text label" v-for="(label, index) in detail.build_cate" :key="index">{{ label }}</text>
            </view>
          </view>
          <view class="title_row flex-row">
            <view class="flex-row flex-1">
              <text class="title">{{ detail.title }}</text>
              <text class="status" :class="'status' + detail.leixing">{{ detail.status_name }}</text>
            </view>
            <!-- <view class="price">{{detail.price}}{{detail.price_unit}}</view> -->
          </view>
          <view class="build_info">
            <view class="info_row flex-row">
              <view class="left flex-row">
                <text class="label">楼盘地址：</text>
                <text class="flex-1 value">{{detail.address}}</text>
              </view>
              <view class="right" @click="openLocation">
                <my-icon type="weizhi" color="#fff" size="28rpx" lineHeight="40rpx"></my-icon>
                <text style="margin-left: 6rpx; position: relative; top: -3rpx">去这里</text>
                <my-icon type="ic_into" color="#fff" size="28rpx" lineHeight="40rpx"></my-icon>
              </view>
            </view>
            <view class="info_row flex-row">
              <view class="left flex-row">
                <text class="label">免费咨询：</text>
                <text v-if="(detail.use_middle_call == 0 || is_open_middle_num == 0)&&show_build_tel" class="flex-1 value">{{
                  detail.phone && detail.sellmobile_part ? detail.phone + ' 转 ' + detail.sellmobile_part : detail.tel
                }}</text>
              </view>
              <view class="right">
                <image @click="handleTel()" class="icon_btn" :src="'/build/icon/tel.png' | imageFilter()"></image>
                <image @click="cusList(4)" class="icon_btn" :src="'/build/icon/chat.png' | imageFilter()"></image>
              </view>
            </view>
            <view class="info_row flex-row" v-if="detail.kptime">
              <view class="flex-row">
                <text class="label">开盘时间：</text>
                <text class="value">{{detail.kptime}}</text>
              </view>
            </view>
            <view class="info_row flex-row" v-if="detail.build_discount">
              <view class="flex-row">
                <text class="label">优惠活动：</text>
                <text class="value">{{detail.build_discount.content||''}}</text>
              </view>
            </view>
            <view class="tip">向上滑查看更多</view>
          </view>
        </view>
      </view>
    </full-screen>
    <!-- 建面弹窗 -->
    <my-popup ref="jm_popup" position="center" :height="jm_height">
      <view class="tip_container jm_tip">
        <view class="title">建面范围</view>
        <view class="jm_box">
          <view class="jm desc" v-for="(item,index) in detail.mj_ranges" :key="index">
            <view>{{item}}</view>
          </view>
        </view>
        <view class="btn" @click="$refs.jm_popup.hide()">好的，知道了</view>
      </view>
    </my-popup>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
    <my-popup ref="group_pop" position="center" height="712rpx">
      <GroupPop :group_headers="group_headers" :scroll_list="group_list" :detail="wxq" :bid="id" @close="$refs.group_pop.hide()" />
    </my-popup>
    
    <hongbao v-if="hb_result" ref="hongbao" :money="hb_info.hb_money" :expire_seconds="hb_result.expire_seconds" @openHb="openHb"></hongbao>
  </view>
</template>

<script>
import { formatImg, showModal, getSceneParams, config } from '../../common/index.js'
import myIcon from '../../components/myIcon.vue'
import myPopup from '../../components/myPopup.vue'
// #ifndef MP-BAIDU || MP-TOUTIAO
import hb from '../../components/hb.vue'
// #endif
import tabBar from '../../components/tabBar.vue'
import movable from '../../components/moveableScale.vue'
import chooseHouse from '../../components/chooseHouse.vue'
import chatBtn from '../../components/open-button/chatBtn'
import telBtn from '../../components/open-button/telBtn'
import subForm from '../../components/subForm'
import mapNearby from '../../components/mapNearby'
// import mapNear from '../../components/mapNear'
// #ifndef MP-WEIXIN
import loginPopup from '../../components/loginPopup'
// #endif
import shareTip from '../../components/shareTip.vue'
import allTel from '../../common/all_tel.js'
import getChatInfo from '../../common/get_chat_info'
import checkLogin from '../../common/utils/check_login'
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
import shareItem from '@/components/shareItem'
import { mapState } from 'vuex'
import questionPopup from '../../components/questionPopup.vue'
import SendAddressToPhone from '../../components/sendAddressToPhone.vue'
import FullScreen from '../../components/fullScreen.vue'
import sharePop from '../../components/sharePop'
import SalesProgress from './components/SalesProgress'
import SaleHouse from './components/SaleHouse'
import GroupPop from './components/GroupPop'
import hongbao from '@/components/hongbao'
import getLocation from '../../common/get_location'
import { getLonAndLat } from '@/common/utils/getLonAndLat'
export default {
  components: {
    myIcon,
    myPopup,
    // mapNear,
    // #ifndef MP-BAIDU || MP-TOUTIAO
    hb,
    // #endif
    movable,
    tabBar,
    chooseHouse,
    chatBtn,
    telBtn,
    subForm,
    mapNearby,
    // #ifndef MP-WEIXIN
    loginPopup,
    // #endif
    shareTip,
    enturstBtn,
    enturstBox,
    shareItem,
    questionPopup,
    SendAddressToPhone,
    FullScreen,
    sharePop,
    SalesProgress,
    SaleHouse,
    GroupPop,
    hongbao,
  },
  data() {
    return {
      jm_height:'',
      id: '',
      showCon:false,
      detail: {
        title: '',
        address: ''
      },
      is_follow:0, //是否已关注该楼盘
      opacity:0,
      // show_zhibo:false,
      isshare: 0,
      consuList: [], //置业顾问列表
      cusId: '', //判断是不是置业顾问分享过来的
      cusArr: {}, //分享的置业顾问信息数组
      current_adviser_index: 0, //置业顾问列表点击更换的index 默认第一个
      popup: {},
      img: [],
      focus: [],
      focusLen: '',
      live_num: 0,
      vr_num: 0,
      video_num: 0,
      swiper_type_count: 0,
      swiperCurrent: 0,
      cateActive: '',
      news:{},
      build_open:{},
      yushou: {}, //最新预售列表
      houseTypePic: [],
      commentList: [],
      wxq: {}, //加群信息
      show_build_tel: false,
      disclaimer: '',
      now_user_is_adviser: 0, //当前用户是否室置业顾问
      current_adviser_id: '', //本人置业顾问分享的id
      adviser_share: [], //置业顾问的分享
      voice_playing_index: -1,
      sand_info: {},
      sand_point: [], // 楼栋楼号
      sand_navs: [],
      tabNowIndex:0,
      online_info: {},
      house_list: [], //在线选房列表
      sale_house_list: [], //销售房源列表
      sub_type: 0,
      adress_code:'', //是否显示手机发送地址
      login_tip: '',
      hideMap: true,
      mapData: {
        scale: 12,
        covers: []
      },
      link:'',
      satisfyVersion: 0,
      recommend_house: [],
      advs:[],
      find_build: [],
      current_nav_index:0,
      cusBtn:[],  //设置的轮播按钮排序
      top_navs:[
        {
          name: '楼盘',
          id: 'build'
        },
        {
          name: '动态',
          id: 'news'
        },
        
        {
          name: '户型',
          id: 'huxing'
        },
        {
          name: '周边',
          id: 'zhoubian'
        },
        {
          name: '楼栋',
          id: 'loudong'
        },
        {
          name: '点评',
          id: 'comment'
        },
      ],
      contrastCount: 0,
      wxqunopen: 0,
      group_headers: [],
      mountTitle: "置业顾问",
      navs: [
        {},
        {},
        {},
        {}
      ],
      text_popup_height:'',
      copy_success: false,
      show_share_tip: false,
      service_show_zixunliang:0,
      currentUserInfo:{},
      sid:'',
      shareType:'',
      shareUserInfo:{},
      toLogin:true,
      weituo_is_show: false, //委托弹窗是否显示
      question: {},
      bestIconSrc: config.imgDomain + '/images/new_icon/bast_answer.png',
      no_qaSrc: config.imgDomain + '/images/new_icon/no_qa.png',
      isLogin: false,  //是否登录
      submit_tel:"",
      show_full: false,
      flagship: {},
      now_point:0,
      offset_x:0,
      offset_y:0,
      tel_res: {},
      show_tel_pop:false,
      page_style: 0,
      options1_list: [],
      options2_list: [],
      modules: {
        detail: {},
        hot_house: {
          list: []
        },
        presell: {
          list: []
        },
        nav: {},
        news: {}
      },
      group_list: [],
      comment_first: {},
      rank_bg: config.imgDomain+'/build/v_3/ranks/bg.png',
      show_details_all: false, //是否展开楼盘详细信息
      wxsmcode: '', //小程序码
      hongbao_gif: config.imgDomain+'/hongbao/linghongbao.png',
      saihongbao_gif: config.imgDomain+'/hongbao/saihongbao.gif',
      hb: '',
      hb_info: {},
      hb_share_query: '',
      hb_result: {},
      current_city: '',
      task_id: '',
      map_key: '',
      num:0,
      showpic:{},
      hidepic:{},
      moteNum:0,
      tuiguang_mp3:"/static/icon/voice/tuiguang_bg.mp3",
      filterList:[],
      wxqunewm: '',
      loading:false,
      wxqunkefu: '',
    }
  },
  filters:{
    iconformat(val){
      return config.imgDomain+val+'?x-oss-process=style/m_240'
    },
    imgUrl(val, param = "") {
        return formatImg(val, param)
    },
  },
  computed: {
    ...mapState(['tel400jing',"siteName", "systemInfo",'switch_adviser_tel']),
    audit_mode() {
      return this.$store.state.audit_mode
    },
    is_open_adviser() {
      return this.$store.state.im.adviser
    },
    is_open_im() {
      return this.$store.state.im.ischat
    },
    is_open_middle_num() {
      return this.$store.state.im.istelcall
    },
    grid() {
      return this.$store.state.im.isNav
    },
    hasWechat() {
      return this.$store.state.hasWechat
    },
    login_status() {
      return this.$store.state.user_login_status
    },
    weapp_appid(){
      return this.$store.state.weapp_appid
    },
    sub_mode() {
      return this.$store.state.sub_form_mode 
    },
    hot_bg_img(){
      return formatImg(`/build/v_3/hot_house_bg.png`)
    },
    cirles(){
      if(this.detail &&this.detail.yzhou) {
        return [
          {
							longitude:this.detail.xzhou,
							latitude:this.detail.yzhou,
							color:"#ff0000",
							radius:1000,
							strokeWidth:1,
						},
						{
							longitude:this.detail.xzhou,
							latitude:this.detail.yzhou,
							color:"#ff9c00",
							radius:2000,
							strokeWidth:1
						},
						{
							longitude:this.detail.xzhou,
							latitude:this.detail.yzhou,
							color:"#fee500",
							fillColor:"#00000026",
							radius:3000,
							strokeWidth:1
						}
        ]
      }
    },
    oneKm(){
      return getLonAndLat(this.detail.xzhou,this.detail.yzhou,0,1000)
    },
    twoKm(){
      return getLonAndLat(this.detail.xzhou,this.detail.yzhou,0,2000)
    },
    threeKm(){
      return getLonAndLat(this.detail.xzhou,this.detail.yzhou,0,3000)
    }
  },
  onLoad(options) {
    // #ifdef MP-WEIXIN
    wx.showShareMenu({
      withShareTicket:true,
      menus:['shareAppMessage','shareTimeline']
    })
    // #endif
    this.offset_x = uni.upx2px(120/2)
    this.offset_y = uni.upx2px(50+8)
    //  app 端没安装微信处理
    // #ifdef APP-PLUS
    if (!this.hasWechat) {
      let webView = this.$mp.page.$getAppWebview()
      webView.setTitleNViewButtonStyle(0, {
        type: 'none',
        width: 0
      })
    }
    // #endif
    // 如果是置业顾问分享的链接
    if (options.cusId) {
      this.cusId = options.cusId
    }
    if (options.isshare) {
      this.isshare = options.isshare || 0
    }
    if (options.shareId) {
      this.sid = options.shareId,
      this.shareType =options.shareType
      this.share_time =options.f_time||''
    }
    // #ifdef MP
    if (options.scene) {
      const params = getSceneParams(decodeURIComponent(options.scene))
      if (params.id) {
        this.id = params.id
        this.getData(this.id)
      }
      return
    }
    // #endif
    if (JSON.stringify(this.$store.state.tempData) != '{}') {
      Object.assign(this.detail, this.$store.state.tempData)
      document.title = this.detail.title
      if(this.detail.flagship_is_show){
        this.show_full = true
        this.showCon = true
      }
      // uni.setNavigationBarTitle({
      //   title: this.detail.title
      // })
    } else if (options.title) {
      this.detail.title = decodeURIComponent(options.title)
      document.title = this.detail.title
      // uni.setNavigationBarTitle({
      //   title: this.detail.title
      // })
    }
    if (options.id) {
      this.id = options.id
      this.getData(this.id)
    }
    if(options.position) this.page_position = options.position
    if(options.hb) this.hb = options.hb
    // 登录状态
    // this.loginState()
    uni.$on('getDataAgain', ()=>{
      this.getData(this.id)
      // this.loginState()
      // this.checkHb()
    })
    // this.animation = uni.createAnimation(
    //    {delay: 2100}
    // )
    // this.interval =0
    // setInterval(() => {
    //   this.animation.translateX('-120rpx').step({duration:700})
    //   .translateX('0').step({duration:700})
    //   .translateX('-120rpx').step({duration:700})
    //   this.animationData = this.animation
    //   this.interval += 2200
    // }, this.interval);
    // this.animationData = this.animation.export()
    var  animation = uni.createAnimation(
        { timingFunction: "ease",}
    )
    animation.opacity(1).step({ duration: 1000}).translateX('-120rpx').step({ duration: 1000}); 
       //描述动画
    this.showpic = animation.export(); //输出动画
    animation.opacity(0).step({ duration: 1000 ,delay:1000}).translateX('120rpx').step({ duration: 1000,delay:1000});
      this.hidepic = animation.export();
    this.setInterval1 =setInterval(function(){
      this.num++;
      if (this.num == 2) {
        this.num = 0;
      }
      //淡入
      animation.opacity(1).step({ duration: 1000}).translateX('-120rpx').step({ duration: 1000}); 
       //描述动画
      this.showpic = animation.export(); //输出动画
      //淡出
      animation.opacity(0).step({ duration: 1000 ,delay:1000}).translateX('120rpx').step({ duration: 1000,delay:1000});
      this.hidepic = animation.export();
    }.bind(this), 4000);
    // this.checkHb()
    if( this.show_full==false){
      this.gettap()
    }
  },
  onHide(){
    if (this.innerAudioContext){
        this.innerAudioContext.destroy()
    }
  },

  onUnload() {
    if (this.innerAudioContext){
        this.innerAudioContext.destroy()
    }
    if (this.setInterval1){
      clearInterval(this.setInterval1)
    }
    uni.$off('getDataAgain')
    this.$store.state.tempData = {}
    this.$store.state.buildInfo = {}
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  onPageScroll(e){
    let top = 44
    this.scrollTop = e.scrollTop
    let opacity = 0
    if(this.scrollTop>top){
      opacity = (this.scrollTop-top)/44/4
    }
    if(opacity>1)opacity=1
    this.opacity = opacity
    if(this.active_tab){
      return
    }
    if(this.time&&new Date().getTime()-this.time<220){
      return
    }else{
      this.time = new Date().getTime()
    }
    const query = uni.createSelectorQuery().in(this);
    query.select('#build').fields({rect:true,scrollOffset:true,size:true},data => {
      if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
        this.current_nav_index = 0
      }
    }).exec();
    query.select('#news').fields({rect:true,scrollOffset:true,size:true},data => {
      if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
        let _index = this.top_navs.findIndex(item=>item.id === 'news')
        if(_index>-1){
          this.current_nav_index = _index
        }
      }
    }).exec();
    // query.select('#house').fields({rect:true,scrollOffset:true,size:true},data => {
    //   if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
    //     this.current_nav_index = 2
    //   }
    // }).exec();
    
    query.select('#huxing').fields({rect:true,scrollOffset:true,size:true},data => {
      if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
        let _index = this.top_navs.findIndex(item=>item.id === 'huxing')
        if(_index>-1){
          this.current_nav_index = _index
        }
        // if(this.house_list.length>0){
        //   this.current_nav_index = 3
        // }else{
        //   this.current_nav_index = 2
        // }
      }
    }).exec();
    query.select('#zhoubian').fields({rect:true,scrollOffset:true,size:true},data => {
      if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
        let _index = this.top_navs.findIndex(item=>item.id === 'zhoubian')
        if(_index>-1){
          this.current_nav_index = _index
        }
        //  if(this.houseTypePic.length>0){
        //   this.current_nav_index = 3
        // }else{
        //   this.current_nav_index = 2
        // }
      }
    }).exec();
    query.select('#loudong').fields({rect:true,scrollOffset:true,size:true},data => {
      if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
        let _index = this.top_navs.findIndex(item=>item.id === 'loudong')
        if(_index>-1){
          this.current_nav_index = _index
        }
        //  if(this.houseTypePic.length>0){
        //   this.current_nav_index = 3
        // }else{
        //   this.current_nav_index = 2
        // }
      }
    }).exec();
    query.select('#comment').fields({rect:true,scrollOffset:true,size:true},data => {
      if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
        let _index = this.top_navs.findIndex(item=>item.id === 'comment')
        if(_index>-1){
          this.current_nav_index = _index
        }
        // if(this.houseTypePic.length>0){
        //   this.current_nav_index = 4
        // }else{
        //   this.current_nav_index = 3
        // }
      }
    }).exec();
  },
  methods: {
    scroppTo(id, index){
      this.current_nav_index = index
      this.active_tab = true
      setTimeout(()=>{
        this.active_tab = false
      },280)
      const query = uni.createSelectorQuery().in(this);
      query.select('#'+id).fields({rect:true,scrollOffset:true},data => {
        uni.pageScrollTo({
          duration:120,
          scrollTop:(this.scrollTop||0)+data.top - 44
        })
      }).exec();
    },
    gettap() {
            uni.showLoading({
                title: '加载中'
            });
    },
    getData(id) {
      this.$ajax.get(
        'build/buildDetailNew',
        {
          id: id,
          isshare: this.isshare,
          sid:this.sid,
          sharetype:this.shareType,
          forward_time:this.share_time||''
        },
        res => {
          if (res.data.code === 404) {
            uni.redirectTo({
              url: '/pages/news/detail?id=' + this.id
            })
            return
          }
          this.$store.state.user_login_status = res.data.status
          if (res.data.code != 1) {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            uni.hideLoading();
            return
          }
          if(res.data.build_address_code){
         
             this.adress_code= res.data.build_address_code
             console.log( this.adress_code,"  this.bulid_adress  this.bulid_adress  this.bulid_adress")
          }
          if(res.data.build.flagship_is_show){
            this.show_full = true
          }
        
          this.getNav()
          this.getOnlineHouse(id)
          this.isshare = 0
          Object.assign(this.detail, res.data.build)
          document.title = res.data.build.title
          // 合并图片视频和全景图
          this.img_num = res.data.focus.length
          this.swiper_type_count = 0
          if(this.img_num){
            this.swiper_type_count++
          }
          this.video_num = res.data.videosList.length
          if(this.video_num){
            this.swiper_type_count++
          }
          let lives = []
          // 只有微信小程序和公众号才有跳转直播功能
          if(this.detail.live_room&&this.detail.live_cover){
            this.live_num = 1
            this.swiper_type_count++
            lives = [{
              room: this.detail.live_room,
              time: this.detail.live_time,
              cover: this.detail.live_cover,
              type: 'live'
            }]
          }
          let vr_list = []
          // 如果有之前的单个vr
          if(res.data.vrs&&res.data.vrs.length>0){
            this.vr_num = res.data.vrs.length
            vr_list = res.data.vrs.map(item => {
              return {
                type: 'vr',
                url: item
              }
            })
          }else{
            // 否则使用楼盘绑定的vr
            this.vr_type = 'list'
            this.vr_num = res.data.buildVrs.length
            vr_list = res.data.buildVrs.map(item=>{
              return {
                type: 'vr',
                url: item.path,
                cover: item.cover
              }
            })
          }
          if(this.vr_num){
            this.swiper_type_count++
          }
          let imgs = res.data.focus.map(item => {
            return {
              type: 'img',
              url: item
            }
          })
          let videos = res.data.videosList.map(item => {
            return {
              type: 'video',
              url: item.path,
              cover: item.pic
            }
          })
          let huxingImgs = res.data.huxingImgs.map(item => {
            return {
              type: 'huxing',
              url: item
            }
          })
          var cusBtn =res.data.custom_wap_build_detail
                      .filter(item=>item.is_show ==1)
                      .map(item=>{
                        switch (item.value) {
                          case 'vr':
                            item.list = vr_list
                            break;
                        case 'live':
                            item.list = lives
                            break;
                        case 'video':
                            item.list = videos
                            break;
                        case 'img':
                            item.list = imgs
                            break;
                        case 'huxing':
                            item.list = huxingImgs
                            break;
                        default:
                            break;
                        }
                        return item
                      })
                      .filter(item=>item.list.length>0)
          this.cusBtn = cusBtn
          this.focus =this.flatten(cusBtn.map(item=>item.list)) 
          this.options1_list = res.data.navs
          this.options2_list = res.data.subnavs
          // this.focus = [...lives, ...vr_list, ...videos, ...imgs];
          // this.focusLen = this.focus.length
          this.focusLen = res.data.buildImgCount
          if (this.focus.length > 0) {
            this.cateActive = this.focus[0].type
          }
          this.img = res.data.focus.concat(res.data.huxingImgs);
          // 页面模块是否显示等处理判断
          if(res.data.modules){
            if(!res.data.modules.page_style||res.data.modules.page_style&&res.data.modules.page_style.is_show === 0){
              this.page_style = 1
            }else{
              this.page_style = Number(res.data.modules.page_style.value)
            }
            this.modules = res.data.modules
            if(this.modules.presell){
              let presell_current = this.modules.presell.list.find(item=>item.is_ongoing===1)
              if(presell_current){
                this.modules.presell.current_descp = presell_current.descp
              }
            }
          }else{
            this.page_style = 1
          }

          if(!this.modules.news||(this.modules.news&&!this.modules.news.is_show)){
            this.top_navs = this.top_navs.filter(item=>item.id!=='news')
          }
          this.is_follow = res.data.is_follow
          // uni.setNavigationBarTitle({
          //   title: res.data.build.title
          // })
           // 获取用户信息
          this.currentUserInfo=res.data.shareUser
          if (this.currentUserInfo.adviser_id>0){
              this.currentUserInfo.shareType=1
              this.currentUserInfo.sid=this.currentUserInfo.adviser_id
          }else if (this.currentUserInfo.agent_id>0) {
            this.currentUserInfo.shareType=2
            this.currentUserInfo.sid=this.currentUserInfo.agent_id
          }else {
            this.currentUserInfo={
              sid:this.sid,
              shareType:this.shareType
            }
          }
          this.shareUserInfo=res.data.share_user
          // 设置地图中心点
          if (this.detail.xzhou && this.detail.yzhou) {
            // this.getCovers()
            this.mapData.covers = [
              {
                latitude: this.detail.yzhou,
                longitude: this.detail.xzhou,
                width: 30,
                height: 30,
                iconPath: '/static/icon/center.png'
              }
            ]
            this.hideMap = false
          } else {
            this.hideMap = true
          }
          this.news = res.data.news
          this.yushou = res.data.build_ysxk||{}
          // this.adviser_share = res.data.adviser_share
          this.adviser_share = res.data.circle_share
          this.build_open = res.data.open
          this.houseTypePic = res.data.houseTypePic.map(item => {
            item.status_class = this.getClass(item.sale_status)
            return item
          })
          console.log(this.houseTypePic,"this.houseTypePicthis.houseTypePic")
          this.advs = res.data.adv
          this.wxqunopen = res.data.wxqunopen
          this.group_headers = res.data.headImage||[]
          this.contrastCount = res.data.contrastCount
          this.find_build = res.data.findBuild
          this.service_show_zixunliang=res.data.open_adviser_service
          this.commentList = res.data.comment
          this.wxsmcode = res.data.wxsmcode
          this.disclaimer = res.data.disclaimer
          this.hb_is_open = res.data.hb_is_open
          if (res.data.popup&&res.data.popup.img_popup) {
            this.popup = res.data.popup

            // #ifndef MP-BAIDU || MP-TOUTIAO
            setTimeout(() => {
              this.$refs.hb.showHb()
            }, 600)
            // #endif
          }
          this.showCon =true

          this.group_list = res.data.wxqunAddedRolls||[]

          // 评论引导加群内容
          this.comment_first = {
            prelogo: res.data.manager_prelogo,
            content: res.data.system_build_comment_content,
            is_show: res.data.if_show_system_build_comment
          }

          if (this.sid){
            // 获取登陆状态
            this.$ajax.get('member/checkUserStatus', {}, res => {
                if (res.data.code === 1) {
                  this.loginState()
                } else {
                  
                  if(this.$store.state.user_login_status==1){
                    if (this.toLogin==false) return 
                    this.toLogin=false
                    uni.setStorageSync('backUrl', window.location.href)
                    this.$navigateTo("/user/login/login")
                  }else {
                    this.loginState()
                  }
                }
                })
          }else {
            this.loginState()
          }

          // 沙盘分类
          if (res.data.sands.length > 0) {
            this.sand_navs = res.data.sands.map(item => {
              return { id: item.id, name: item.title }
            })
            this.getSandInfo(res.data.sands[0].id)
          }else{
            this.top_navs = this.top_navs.filter(item=>item.id!=='loudong')
          }

          // 判断是不是置业顾问 且是不是当前置业顾问绑定的楼盘
          if (res.data.adviser && res.data.adviser.build_ids && res.data.adviser.id) {
            //是置业顾问
            let builds = res.data.adviser.build_ids.split(',')
            if (builds && builds.indexOf(this.detail.id.toString()) >= 0) {
              //是当前置业顾问绑定的楼盘
              this.isshare = 1
              this.now_user_is_adviser = 1
              this.current_adviser_id = res.data.adviser.id
            } else {
              this.isshare = 0
              this.now_user_is_adviser = 0
              this.current_adviser_id = ''
            }
          } else {
            //不是置业顾问
            this.isshare = 0
          }
          this.wxqunewm = formatImg(res.data.wxqunewm, 'w_8001')
          this.wxqunkefu = res.data.wxqunkefu
          this.wxq = {
            qrcode: formatImg(res.data.wxqunewm, 'w_8001'),
            number: res.data.wxqunkefu,
            count: res.data.build.groupCount
          }
          let link=''
          let time =parseInt(+new Date()/1000)
           if (this.currentUserInfo.sid){
               link=window.location.origin+"/h5/pages/new_house/detail?id="+this.id +"&isShare=1&shareType="+this.currentUserInfo.shareType+"&shareId="+this.currentUserInfo.sid+"&f_time="+time
            }else {
              // link = window.location.href
              link = window.location.href.split('&hb=')[0]
            }
            // if (this.hb_share_query) {
            //   link += `&${this.hb_share_query}`
            // }
          this.share = res.data.share&&(res.data.share.title||res.data.share.content)?res.data.share: {
            title:res.data.build.title+'【线上售楼处】',
            content:res.data.build.title+'线上售楼处全面开启，24小时不打烊',
          }
          this.share.pic=res.data.build.img
          this.share.link = link
          this.getWxConfig()
          // if (this.is_open_adviser == 1 && this.detail.open_adviser == 1) {
          //   this.getConsuData()
          // } else {
          //   this.show_build_tel = true
          // }
          if (!this.is_open_adviser == 1 || !this.detail.open_adviser == 1) {
            this.show_build_tel = true
          }
          // 处理置业顾问显示
          if (res.data.mountMembers.length>0) {
            this.mountTitle = res.data.mountTitle
            let list = res.data.mountMembers
            let num = res.data.showCount //显示的置业顾问个数
            if (this.cusId != '') {
              //分享者信息处理
              const current_index = list.findIndex(item=>item.adviser_id === parseInt(this.cusId))
              if(current_index>=0){
                this.cusArr = list.splice(current_index,1)[0]
                list.unshift(this.cusArr)
              }
            } else if (this.now_user_is_adviser == 1) {
              //当前置业顾问绑定的楼盘 放在第一位
              const current_index = list.findIndex(item=>item.adviser_id === this.current_adviser_id)
              if(current_index>=0){
                this.cusArr = list.splice(current_index,1)[0]
                list.unshift(this.cusArr)
              }
            }else {
              this.cusArr = {}
            }
            this.consuList = list.splice(0, num) //显示的数组
          } else {
            this.cusArr = {}
          }
          this.show_build_tel = true
          // 楼盘问答
          if (res.data.build_question){
            this.question = res.data.build_question;
          }
          uni.hideLoading();
          this.loading = true
          // console.log(this.question);
        }
      )
    },
    checkLogin(tip, callback) {
      this.$ajax.get('member/checkUserStatus', {}, res => {
        if (res.data.code === 1) {
          callback&&callback()
        } else {
          this.$store.state.user_login_status = res.data.status
          this.login_tip = tip
          this.$refs.login_popup.showPopup()
        }
      })
    },
    // onClickMask(){
    //         this.show_zhibo=false
    // },
    getNav(){
      this.$ajax.get('build/buildNav.html',{bid:this.id},res=>{
        if(res.data.code === 1){
          this.navs = res.data.navs
        }
      })
    },
    getOnlineHouse(build_id) {
      this.$ajax.get('online/recOnlineHouses', { build_id }, res => {
        if (res.data.code === 1) {
          this.sale_house_list = Array.from(res.data.onlineHouses)
          let len = res.data.onlineHouses.length
          let temp_house_list = []
          for (let i = 0; i < len; i += 4) {
            temp_house_list.push(res.data.onlineHouses.splice(0, 4))
          }
          this.house_list = temp_house_list
          this.online_info = res.data.online
        }
        this.$nextTick(() => {
          if(this.house_list.length>0&&this.page_position==='huxing'){
            this.scroppTo(this.page_position, 3)
          }else if(this.page_position==='huxing'){
            this.scroppTo(this.page_position, 2)
          }
        })
      })
    },
    handelCate(e) {
      this.tabNowIndex = e.index
      this.getSandInfo(e.id)
    },
    getSandInfo(sand_id) {
      this.sand_id = sand_id
      this.$ajax.get('build/getBuildSand.php', { build_id: this.id, sand_id }, res => {
        if (res.data.code === 1) {
          // 获取沙盘信息
          if (res.data.sand && res.data.sand.pic) {
            let padding = uni.upx2px(20)
            let windowWidth = this.systemInfo.windowWidth
            let box_height = (windowWidth * 3) / 5
            let box_width = windowWidth - padding * 2
            let top = '0px'
            let left = '0px'
            if (res.data.sand.height - box_height > res.data.sand.height / 4) {
              top = 0 - res.data.sand.height / 4 + 'px'
            }
            if (res.data.sand.width - box_width > 0) {
              left = 0 - (res.data.sand.width / 2 - box_width / 2) + 'px'
            }
            this.sand_info = {
              img: res.data.sand.pic, // 楼盘背景
              width: res.data.sand.width,
              height: res.data.sand.height,
              top: top,
              left: left,
              margin_left: '0px'
            }
            // this.sand_info = {
            //     img:res.data.sand.pic,
            //     width:res.data.sand.width,
            //     height:res.data.sand.height,
            // }
            if(res.data.buildStatus) {
              this.filterList = res.data.buildStatus
            }
            res.data.sand.item.map(item=>{
                item.activeColor= "rgba(255,101,107,0.8)"
                 item.arrowColor= " 8rpx solid " +item.color
                return item
            })
            this.sand_point = res.data.sand.item
          }
        }
      })
    },
    handleClick(e) {
      this.$navigateTo(`/online/house_detail?id=${e.id}&online_id=${this.online_info.id}`)
    },
    toOnlineDetail() {
      if (this.online_info.id) {
        this.$navigateTo(`/online/detail?id=${this.online_info.id}`)
      }
    },
    getClass(val) {
      let clas = ''
      switch (val) {
        case 1:
          clas = 'status1'
          break
        case 2:
          clas = 'status2'
          break
        case 3:
          clas = 'status3'
          break
        case 4:
          clas = 'status4'
          break
      }
      return clas
    },
    switchFocus(type) {
      this.cateActive = type
      let i = this.cusBtn.findIndex(item=>item.value== type)
      if (i ==0){
        this.swiperCurrent = 0
      }else {
        let c = 0;
        for (let index = 0; index < i; index++) {
          c+= this[this.cusBtn[index].value+'_num']
        }
        this.swiperCurrent = c
      }

      // switch (type) {
      //   case 'live':
      //     this.swiperCurrent = 0
      //     break
      //   case 'vr':
      //     this.swiperCurrent = this.live_num
      //     break
      //   case 'video':
      //     this.swiperCurrent = this.live_num+this.vr_num
      //     break
      //   case 'img':
      //     this.swiperCurrent = this.live_num+this.vr_num+this.video_num
      //     break
      //   default:
      //     this.swiperCurrent = 0
      // }
    },
    // 转到楼盘价值分析报告 
    toAnalyse(){
      this.$navigateTo('../../propertyData/analyse?id='+this.id)
    },
    //转到顾问详情
    consuDetail(id) {
      if (!id) return
      // 判断id是不是置业顾问id
      // 根据id查询出挂载列表中的置业顾问
      var current_adviser = this.consuList.find(item=>item.adviser_id===id)
      if(id === this.cusArr.adviser_id||(current_adviser&&!current_adviser.isGuwen)){
        if (this.is_open_adviser == 1 && this.detail.open_adviser == 1) {
          this.$navigateTo('/pages/consultant/detail?id=' + id)
        }
      }else{
        console.log("没开启聊天且不是置业顾问,不跳转详情")
      }
    },
    //转到顾问列表
    cusList(operation) {
      if(operation===4){
        console.log("和置业顾问发起聊天")
        if(this.cusArr&&this.cusArr.id){
          var user_id = this.cusArr.mid||this.cusArr.uid||this.cusArr.id
          var identity_id = this.cusArr.adviser_id||this.cusArr.uid||this.cusArr.id
          this.advAsk({user_id:user_id,identity_id:identity_id})
        }else if(this.consuList.length>0){
          var user_id = this.consuList[0].mid||this.consuList[0].uid||this.consuList[0].id
          var identity_id = this.consuList[0].adviser_id||this.consuList[0].uid||this.consuList[0].id
          this.advAsk({user_id:user_id,identity_id:identity_id})
        }else{
          uni.showToast({
            title: "该楼盘还没有置业顾问",
            icon: 'none'
          })
        }
        return
      }
      // if(!uni.getStorageSync('token')){
      // 		this.$navigateTo('/user/login/login')
      // 		return
      //   }
      this.$store.state.buildInfo = {
        id: this.id,
        title: this.detail.title,
        type: 'build',
        image: this.img[0]
      }
      this.$navigateTo('/pages/consultant/consuList?id=' + this.id)
    },
    viewMap(e) {
      if (this.detail.yzhou > 0 && this.detail.xzhou > 0) {
        this.$navigateTo('/propertyData/map/map?id=' + this.id + '&type=1&lat=' + this.detail.yzhou + '&lng=' + this.detail.xzhou)
      } else {
        uni.showToast({
          title: '未标记地图位置',
          icon: 'none'
        })
      }
    },
    // 将户型加入对比
    addContrast(img_id){
      this.$ajax.get('build/addContrast.html',{img_id},res=>{
        if(res.data.code === -1){
          this.$store.state.user_login_status = 1
          // 检测是否已添加
          if(this.$store.state.temp_huxing_contrast_ids.includes(img_id)){
            uni.showToast({
              title:"该户型已经添加",
              icon:'none'
            })
            return
          }
          this.$store.state.temp_huxing_contrast_ids.push(img_id)
          return
        }
        if(res.data.code === 1){
          uni.showToast({
            title:res.data.msg
          })
          this.contrastCount ++
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      },err=>{

      },{disableAutoHandle:true})
    },
    toContrast(){
      if(this.login_status>1){
        this.$navigateTo('/contrast/house_list')
      }else{
        this.$navigateTo(`/contrast/house_list?no_login=1`)
      }
    },
    handleCreat() {
      this.$navigateTo(`${location.origin}/wapi/poster/branch?type=1&id=${this.id}&header_from=2&shareId=${this.currentUserInfo.sid}&&shareType=${this.currentUserInfo.shareType}`)
    },
    jm(){
      uni.createSelectorQuery().in(this).select(".jm_tip").boundingClientRect(data => {
			  this.jm_height = data.height +'px'
			}).exec()
      this.$refs.jm_popup.show()
    },
    showCopywriting(){
      const query = uni.createSelectorQuery().in(this)
      query.select('#copy-text').fields({rect:true,scrollOffset:true,size:true},data => {
        this.text_popup_height = data.height+'px'
      }).exec();
      this.copy_success = false
      this.$refs.text_popup.show()
      this.$refs.share_popup.hide()
    },
    copywriting(){
        const text = `【楼盘名称】${this.detail.title}
【参考${this.detail.price_type}】${this.detail.build_price}${this.detail.price_unit}
${this.detail.build_discount&&this.detail.build_discount.content?'【优惠信息】'+this.detail.build_discount.content+'\n':''}【参考总价】${this.getTotalPrice(this.detail.minPrice, this.detail.maxPrice)}
【主力户型】${this.detail.mj||''}
【项目地址】${this.detail.address}
【访问链接】${this.link}`
        this.copyWechatNum(text, ()=>{
          this.copy_success = true
        })
    },
    getShortLink(){
      let time =parseInt(+new Date()/1000)
      if (this.currentUserInfo.sid){
        this.link="https://"+window.location.host+"/h5/pages/new_house/detail?id="+this.detail.id +"&isShare=1&shareType="+this.currentUserInfo.shareType+"&shareId="+this.currentUserInfo.sid+"&f_time="+time
      }else {
        // this.link = window.location.href
        this.link = window.location.href.split('&hb=')[0]
      }
      // 红包分享加参
      // if (this.hb_share_query) {
      //   this.link += `&${this.hb_share_query}`
      // }
      this.$ajax.get('build/shortUrl.html', {page_url: this.link}, res=>{
        if(res.data.code === 1){
          this.link = res.data.short_url
        }
      })
    },
    getTotalPrice(min, max){
      if(min&&max){
        return `${min}-${max}万`
      }
      if(min&&!max){
        return `${min}万起` 
      }
      if(!min&&max){
        return `最高${min}万` 
      }
      if(!min&&!max){
        return `不详` 
      }
    },
    showLoginPopup(tip){
      if(this.show_full){
        this.toLoginPage()
        return
      }
      this.login_tip = tip
      this.$refs.login_popup.showPopup()
    },
    advAsk(e) {
      if (this.is_open_im == 1) {
        //开聊天
        // #ifdef MP-WEIXIN
        this.$store.state.buildInfo = {
          id: this.id,
          title: this.detail.title,
          type: 'build',
          image: this.img[0]
        }
        getChatInfo(e.user_id, 3, this.id)
        // #endif
        // #ifndef MP-WEIXIN
        checkLogin({
          success: (res)=>{
            this.$store.state.buildInfo = {
              id: this.id,
              title: this.detail.title,
              type: 'build',
              image: this.img[0]
            }
            getChatInfo(e.user_id, 3, this.id)
          },
          fail: (res)=>{
            if(res.status == 1){
              uni.removeStorageSync('token')
              this.$navigateTo('/user/login/login')
            }else if(res.status == 2){
              this.showLoginPopup('为方便您及时接收消息通知，请输入手机号码')
            }
          },
          complete:(res)=>{
            this.$store.state.user_login_status = res.status
          }
        })
        // #endif
      } else if (this.is_open_im == 0) {
        //不开聊天
        this.consuDetail(e.identity_id)
      }
    },
    // 跳转到分享者详情
    shareDetail(){
      if (this.shareType==2){
        this.$navigateTo("/pages/agent/detail?id="+this.sid)
      }else if (this.shareType==1){
         this.$navigateTo("/pages/consultant/detail?id="+this.sid)
      }
    },
    // 拨打分享者电话
    shareTel(){
      // 开启中间号
      // if (this.is_open_middle_num == 1){
          this.callMiddleNumber(this.shareType==2?"3":"2",this.shareType==2?this.shareUserInfo.agent_id:this.shareUserInfo.adviser_id,1,this.shareType==2?0:this.detail.id,'build_detail',this.id)
      // }else{
      //     uni.makePhoneCall({
      //       phoneNumber:this.shareUserInfo.tel ,
      //     })
      //     this.$ajax.get(
      //     'im/callUpStatistics',
      //     {
      //       id: this.shareUserInfo.mid,
      //       tel: this.shareUserInfo.tel,
      //       type: 4
      //     },
      //     res => {
      //       console.log(res)
      //     })
      // }
    },
    // 和分享者聊天
    shareAsk(){
      let e ={
        user_id:this.shareUserInfo.mid,
      }
      if (this.is_open_im == 1) {
        //开聊天
        // #ifdef MP-WEIXIN
        this.$store.state.buildInfo = {
          id: this.id,
          title: this.detail.title,
          type: 'build',
          image: this.img[0]
        }
        getChatInfo(e.user_id, 3, this.id)
        // #endif
        // #ifndef MP-WEIXIN
        checkLogin({
          success: (res)=>{
            this.$store.state.buildInfo = {
              id: this.id,
              title: this.detail.title,
              type: 'build',
              image: this.img[0]
            }
            getChatInfo(e.user_id, 3, this.id)
            },
          fail: (res)=>{
            if(res.status == 1){
              uni.removeStorageSync('token')
              this.$navigateTo('/user/login/login')
            }else if(res.status == 2){
              this.showLoginPopup('为方便您及时接收消息通知，请输入手机号码')
            }
          },
          complete:(res)=>{
            this.$store.state.user_login_status = res.status
          }
        })
        // #endif
      } else if (this.is_open_im == 0) {
        //不开聊天
        this.shareDetail()
        // this.consuDetail(e.identity_id)
      }
    },
    flatten(arr) {  
        return arr.reduce((result, item)=> {
            return result.concat(Array.isArray(item) ? this.flatten(item) : item);
        }, []);
    },
    toLoginPage() {
      if (this.$store.state.user_login_status === 1) {
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if(this.$store.state.user_login_status===2){
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    onLoginSuccess(res){
      this.$store.state.user_login_status = 3
      if(this.weituo_is_show){
        console.log("登录成功后继续执行委托接口")
        this.$refs.enturst_box.handleEnturst()
      }
    },
    showQrcode() {
      if(!this.wxqunopen){
        uni.showToast({
          title: '该楼盘没开启购房群',
          icon: 'none'
        })
        return
      }
      this.$refs.qrCode.show()
    },
    showJoinGroup(){
      if(!this.wxqunopen){
        uni.showToast({
          title: '该楼盘没开启购房群',
          icon: 'none'
        })
        return
      }
      this.$refs.group_pop.show()
    },
    openLocation() {
         // #ifdef MP-WEIXIN
          uni.openLocation({
              latitude: parseFloat(this.detail.yzhou),
              longitude: parseFloat(this.detail.xzhou),
              name: this.detail.title,
              address: this.detail.address
            })
         // #endif
         // #ifndef MP-WEIXIN
          this.checkLogin('当前操作需要绑定手机号，请输入您的手机号', ()=>{
            uni.openLocation({
              latitude: parseFloat(this.detail.yzhou),
              longitude: parseFloat(this.detail.xzhou),
              name: this.detail.title,
              address: this.detail.address
            })
          })
         // #endif
      
    },
    toHome() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    },
    toYuyue(type, nav){
      if(nav.operation===2&&nav.group_id){
        // 跳转团购报名
        this.$navigateTo(`/pages/groups/detail?id=${nav.group_id}`)
      }else{
        this.toSubForme(type)
      }
    },
    sendAddressToPhone(){
      this.$refs.sub_send_form.showPopup()
    },
    handleSubFormPhone(e){
       //提交报名
      e.from ='发送地址到手机'
      // e.name=""
      // e.bid = this.id
      
      e.build_id =this.id
      this.$ajax.get('member/authRegister', e, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
            //提示报名成功  微信小程序登录需要单独处理
            // #ifndef MP-WEIXIN
              if (res.data.token) {
                  uni.setStorageSync('token', res.data.token)
              }
              this.$store.state.user_login_status =3
              //#endif
              uni.showToast({
                title: res.data.msg,
                icon: 'none'
              })
            this.$refs.sub_send_form.closeSub()
            // if(this.login_status==1&&res.data.token ){
            //   let token =res.data.token 
            //   uni.setStorageSync("token",token)
            //   this.$store.state.user_login_status=3
            // }
          // } else {
          //   this.$refs.sub_form.getVerify()
          // }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    toSubForme(type, operation) {
      this.sub_operation = operation || ''
      this.sub_type = type
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e) {
      //提交报名
      e.from ='楼盘页'
      e.bid = this.id
      e.type = this.sub_type || ''
      if(this.sub_operation) e.operation = this.sub_operation
      if(this.shareUserInfo&&this.shareUserInfo.adviser_id){ // 如果是置业顾问分享的
        e.share_uid = this.shareUserInfo.adviser_id
        e.is_adviser = 1
      }else if(this.shareUserInfo&&this.shareUserInfo.agent_id){ // 如果是经纪人分享的
        e.share_uid = this.shareUserInfo.agent_id
        e.is_adviser = 2
      }
      this.$ajax.post('build/signUp.html', e, res => {
        uni.hideLoading()
					if(res.data.code === 1){
						// 没开启引导登录模式或已经绑定手机号了
            if (this.sub_mode!==2||res.data.status === 3) {
							uni.showToast({
								title:res.data.msg,
								icon:"none"
							})
							this.$refs.sub_form.closeSub()
						}else if(res.data.status === 1){
              uni.removeStorageSync('token')
							this.$navigateTo('/user/login/login')
						}else if(res.data.status === 2){
							this.$refs.sub_form.getVerify()
						}
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
				})
    },
    handleSubFormTel(e) {
      //提交报名
      e.from ='楼盘页'
      e.bid = this.id
      e.type = this.sub_type || ''
      this.$ajax.post('build/signUp.html', e, res => {
      })
    },
    // 处理关注或取消关注楼盘
    handleFollow(){
      this.$store.state.allowOpen = true
      if(this.is_follow){
        this.cancelFollow()
      }else{
        this.follow()
      }
    },
    // 关注楼盘
    follow(){
      this.$ajax.get('build/followBuild.html',{bid:this.detail.id},res=>{
        if(res.data.code === 1){
          uni.showToast({
            title:res.data.msg
          })
          this.is_follow = 1
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    },
    showSharePop(){
      this.getShortLink()
      this.$refs.share_popup.show()
    },
    getSendMsg(e, type) {
      this.$store.state.buildInfo = {
        id: this.id,
        title: this.detail.title,
        type: 'build',
        image: this.img[0]
      }
      // #ifdef MP-WEIXIN
      this.$ajax.get('im/getUserReply.html',{page_from:type,bid:this.detail.id},res=>{
        if(res.data.mid){
          this.$store.state.autoSendMsg = res.data.content||''
          getChatInfo(res.data.mid, 3, this.detail.id)
        }
      })
      // #endif
      // #ifndef MP-WEIXIN
      this.checkLogin('当前操作需要绑定手机号，请输入您的手机号', ()=>{
        this.$ajax.get('im/getUserReply.html',{page_from:type,bid:this.detail.id},res=>{
            if(res.data.mid){
              this.$store.state.autoSendMsg = res.data.content||''
              getChatInfo(res.data.mid, 3, this.detail.id)
            }
          })
      })
      // #endif
    },
    // 取消关注楼盘
    cancelFollow(){
      this.$ajax.get('build/cancelFollowBuild.html',{bid:this.detail.id},res=>{
        if(res.data.code === 1){
          uni.showToast({
            title:res.data.msg
          })
          this.is_follow = 0
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    },
    changeMoveable(e){
    },
    scale(e){
    },
    swiperChange(e) {
      this.swiperCurrent = e.detail.current
      this.cateActive = this.focus[this.swiperCurrent].type
    },
    // 执行拨打电话时间
    handleTel(e, options={}) {
      
      // 如果有身份id则拨打置业顾问电话
      if(e&&e.identity_id){
        if(options.isAgent){
          e.isAgent = 1
        }
        if(options.isAdviser){
          e.isAdviser = 1
        }
        this.callAdviserMiddleNumber(e)
        return
      }
      // 如果关闭显示楼盘电话且有置业顾问则拨打置业顾问电话，否则拨打楼盘电话
      if(this.detail.use_middle_number===0&&this.detail.open_adviser == 1 &&
      this.consuList.length > 0){
        console.log("关闭显示楼盘电话且有置业顾问")
        this.callAdviserMiddleNumber()
      }else{
        console.log("开启显示楼盘电话或没有置业顾问")
        // 如果没开启虚拟号
        this.callBuildMiddleNumber()
      }
    },
    // 拨打置业顾问真是号码
    // callAdviserNumber(e) {
    //   console.log('拨打置业顾问真实号码')
    //   uni.makePhoneCall({
    //       phoneNumber: e.tel,
    //       success:()=>{
    //         this.statistics()
    //       } 
    //   })
    // },
    // 拨打置业顾问虚拟号码
    callAdviserMiddleNumber(e) {
      console.log('拨打置业顾问虚拟号码')
      var call_adviser = {}
      if(e&&e.identity_id){
        call_adviser = e
      }else if(this.cusArr&&this.cusArr.id){
        call_adviser = this.cusArr
      }else if(this.consuList.length>0){
        call_adviser = this.consuList[0]
      }
      // var user_id = call_adviser.user_id||call_adviser.mid||call_adviser.uid||call_adviser.id
      var identity_id = call_adviser.identity_id||call_adviser.adviser_id||call_adviser.uid||call_adviser.id
      var tel_type = ""
      if(call_adviser.isAgent){
        tel_type = '3'
      }
      if(call_adviser.isAdviser){
        tel_type = '2'
      }
      if (!call_adviser.isAgent &&!call_adviser.isAdviser) {
        tel_type = 0
      }
      this.callMiddleNumber(tel_type,identity_id,1,this.id,'build_detail',this.id)
    },
    showTestSubmit(){
      this.$refs.test_submit.show()
    },
    submit_test_con(){
      let name=''
      if (this.sub_mode!=1){
        name =this.siteName+"网友"
      }
      let url =""
       // #ifdef H5
            url = this.$route.fullPath
            // #endif
            // #ifndef H5
            var pages = getCurrentPages()
            var currentPage = pages[pages.length - 1] //获取当前页面的对象
            url = currentPage.route //当前页面url
            var options = currentPage.options //当前页面url参数
            let i = 0
            url='/'+url;
            for(let key in options){
                if(i===0){
                    url+=`?${key}=${options[key]}`
                }else{
                    url+=`&${key}=${options[key]}`
                }
                i++
            }
            // #endif
      if (this.submit_tel.length!=11||this.submit_tel[0]!=1){
        uni.showToast({
          title:"手机号格式不正确",
          icon:'none'
        })
        return 
      }
      // e.type = this.sub_type || ''
      this.$ajax.post('build/signUp.html', {
        name,
        tel:this.submit_tel,
        url,
        bid:this.id,
        from :'楼盘页',
      }, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
            //提示报名成功
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
            this.submit_tel =''
            this.$refs.test_submit.hide(),
            setTimeout(() => {
              this.toAnalyse()
            }, 600);
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    // 拨打楼盘真实号码
    // callBuildNumber() {
    //   console.log('拨打楼盘真实号码')
    //   let phoneNumber=""
    //   if (this.detail.phone && this.detail.sellmobile_part) {
    //     phoneNumber = this.detail.phone + ',' + this.detail.sellmobile_part.trim()
    //     if (this.tel400jing) {
    //       phoneNumber += '#'
    //     }
    //     showModal({
    //       title: '温馨提示',
    //       content: '请拨打' + this.detail.phone + '后转拨分机号' + this.detail.sellmobile_part,
    //       confirm: res => {
    //         uni.makePhoneCall({
    //           phoneNumber: phoneNumber
    //         })
    //       }
    //     })
    //   } else if (this.detail.tel) {
    //     phoneNumber = this.detail.tel
    //     uni.makePhoneCall({
    //       phoneNumber: phoneNumber,
    //       success: () => {
    //         // this.statistics()
    //       }
    //     })
    //   } else {
    //     uni.showToast({
    //       title: '此楼盘没有绑定联系电话',
    //       icon: 'none'
    //     })
    //   }
    // },
    // 拨打楼盘虚拟号码
    callBuildMiddleNumber() {
      console.log('拨打楼盘虚拟号码')
      let phoneNumber=""
      if (this.detail.tel) {
        phoneNumber = this.detail.tel
      } else if (this.detail.phone && this.detail.sellmobile_part) {
        phoneNumber = this.detail.phone + ',' + this.detail.sellmobile_part.trim()
        if (this.tel400jing) {
          phoneNumber += '#'
        }
      }
      this.callMiddleNumber(1,this.id,1,this.id,'build_detail',this.id)
    },
    // 请求虚拟号接口
    callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
      this.tel_params = {
        type,
        callee_id,
        scene_type,
        scene_id,
        source,
        bid,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      // #ifdef MP-WEIXIN
      allTel(this.tel_params)
      // #endif
      // #ifndef MP-WEIXIN
      // 全局开启中间号且楼盘开启中间号需要检测登录
      if(this.is_open_middle_num == 1 && this.detail.use_middle_call > 0){
        this.tel_params.intercept_login = true
        this.tel_params.fail = (res)=>{
          if(res.data.code === -1){
            this.$store.state.user_login_status = 1
            uni.removeStorageSync('token')
            this.$navigateTo('/user/login/login')
          }
          if(res.data.code === 2){
            this.$store.state.user_login_status = 2
            this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
          }
        }
        allTel(this.tel_params)
      }else{
        allTel(this.tel_params)
      }
      // #endif
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    // 统计拨打电话
    statistics() {
      this.$ajax.get(
        'im/callUpStatistics',
        {
          id: this.cusArr.mid,
          tel: this.cusArr.tel,
          type: 4,
          info_id:this.id
        },
        res => {
          console.log(res)
        }
      )
    },
    preImgs(imgs, type, index) {
      this.$navigateTo('/pages/new_house/photos?id=' + this.detail.id + '&type=' + type + '&index=' + index)
    },
    preVideo() {
      this.$navigateTo("/vr/prevideo?id="+this.id +"&type=1")
      // this.$navigateTo('/vr/videos?id=' + this.id)
    },
    toVr() {
      if(this.vr_type === 'list'){
        this.$navigateTo(`/vr/list?bid=${this.id}`)
      }else{
        this.$navigateTo('/vr/detail?buildid=' + this.id)
      }
    },
    toLive(e){
      this.$navigateTo('/online/next?roomId=' + e.room)
    },
    toCommentList() {
      // return
      this.$navigateTo('/pages/new_house/comment?bid=' + this.detail.id + '&open=' + this.detail.open_adviser)
    },
    // 点赞评论
    handlePraise(index){
      const currentCommnet = this.commentList[index]
      this.$ajax.get('news/praise.html',{id:currentCommnet.id},res=>{
        if(res.data.code === 1){
          uni.showToast({
            title:res.data.msg,
            mask:true
          })
          if(currentCommnet.is_praise){
            this.commentList[index].is_praise = 0
          }else{
            this.commentList[index].is_praise = 1
          }
          this.commentList[index].praise = res.data.praise.length
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none',
            mask:true
          })
        }
      })
    },
    toCommunity(id, index) {
      this.$store.state.tempData = {
        title: this.commentList[index].title,
        nickname: this.commentList[index].nickname,
        content: this.commentList[index].content,
        img: []
      }
      this.$navigateTo('/pages/comment_list/comment_detail?id=' + id)
    },
    saveImg() {
      uni.getImageInfo({
        src: this.wxqunewm,
        success: image => {
          uni.saveImageToPhotosAlbum({
            filePath: image.path,
            success: () => {
              uni.showToast({
                title: '保存成功'
              })
            }
          })
        }
      })
    },
    stopMove(){

    },
    copyWechatNum(cont, callback) {
      let oInput = document.createElement('textarea')
      oInput.value = cont
      document.body.appendChild(oInput)
      oInput.style.opacity = 0
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      oInput.blur()
      oInput.remove()
      if(callback) callback()
    },
    toLoudong() {
      if(!this.sand_id){
        uni.showToast({
          title: '暂无楼栋信息',
          icon: 'none'
        })
        return
      }
      this.$navigateTo(`/online/loudong?build_id=${this.id}&sand_id=${this.sand_id}`)
    },
    onClickPoint(e) {
      this.$navigateTo(`/online/loudong?sand_id=${this.sand_id}&build_id=${this.id}&id=${this.sand_point[e].id}`)
    },
    /**
     * <AUTHOR>
     * @date 2020-07-11 15:12:29
     * @desc 获取地图周边数据
     * @param {String} keywords - 关键字
     * @param {Number} type - 类型 1:楼盘  2: 二手房和出租房 3:小区
     */
    getCovers(e , type = 1) {
      let params = {
        id: this.id,
        keywords:e?e.type:'',
        type: 1
      }
      let api='map/mapNearbyMatches.html'
      
      this.$ajax.get(
        api,
        params,
        res => {
          if (res.data.code != 1) {
            return
          }

				  if (!res.data.done&&this.moteNum <5 &&!e){
						this.getCovers(e,type)
						return 
					}
					let covers =[]
					 
					res.data.matches.map((cover)=>{
						let icon,color,bgColor,title
						switch(cover.keyword)
						{
						case '商业':
							icon = '/static/icon/foot.png'
							bgColor=  "#ffbabc"
							title="商"
							color="#fff"
							break
						case '教育':
							icon = '/static/icon/edu.png'
							title="教"
							bgColor="#34dec1"
							color="#fff"
							break
						case '医疗':
							icon = '/static/icon/yiliao.png'
							title="医"
							bgColor="#feb9bb"
							color="#fff"
							break
						case '交通':
							icon = '/static/icon/jiaotong.png'
							bgColor="#66d1fa"
							title ="交"
							color="#fff"
							break
						default:
							icon = '/static/icon/center.png'
						}
						if (cover.data&&cover.data.length) {
								cover.data.map(item=>{
									let distance = parseInt(item._distance)
									let ob = {
										width: 30,
										height: 30,
										iconPath: icon,
										// name:title,
										latitude: item.location.lat,
										longitude: item.location.lng,
										title: item.title,
										name:title,
										// showtitle:this.currentScale<13?false:true,
										id:item.id+''+cover.keyword +cover.filter,
										address: item.address,
										_distance: item._distance,
										callout: {
											content: ((e && e.scale<=14) || !e)?title:item.title,
											padding: 5,
											fontSize:10,
											boxShadow:'none',
											bgColor,
											color,
											borderRadius: 4,
											borderColor:bgColor,
											display:'ALWAYS'
										},
										distance: distance
									}
									covers.push(ob)
									return item
								})
						}
						
						
						return cover
					})
          covers.push({
            latitude: this.detail.lat,
            longitude: this.detail.lng,
            width: 30,
            height: 30,
            iconPath: '/static/icon/center.png'
          })
          covers.push({
						latitude: this.oneKm.lat,
						id:"a"+1,
						longitude: this.oneKm.lon,
						width: -1,
						height:-1,
						label: {
							content:'1公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff0000",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5,
							borderColor:'#ffffff'
						},
						iconPath: '/static/icon/center.png'
					})
					covers.push({
						latitude: this.twoKm.lat,
						longitude: this.twoKm.lon,
						width: -1,
						height: -1,
						id:"a"+2,
						label: {
							content:'2公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff9c00",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
						},
						iconPath: '/static/icon/center.png'
					})
					covers.push({
						latitude: this.threeKm.lat,
						longitude: this.threeKm.lon,
						width: -1,
						height: -1,
						id:"a"+3,
						label: {
							content:'3公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#fee500",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
							},
						iconPath: '/static/icon/center.png'
					})
          this.mapData.covers = covers
        },
        err => {}
      )
    },
    
    toYushou(id){
      if(id>0){
        this.$navigateTo('/pages/yushou/detail?id=' + id)
      }
    },
    doTask(){
      if (this.detail.open_adviser == 1 && this.is_open_adviser == 1 && this.now_user_is_adviser == 1) {
        //当前用户是置业顾问 而且开启了置业顾问板块
        this.$ajax.get('tasks/doTaskReward.html', { task_id: 16 }, res => {
          console.log(res)
        })
      }
    },
    showTip(){
      this.$refs.tip_popup.show()
    },
    // 楼盘问答点赞
  praise(answer) {
      this.$ajax.post('buildQuestion/answerPraise', { answer_id: answer.answer_id }, (res) => {
        if(res.data.msg === '点赞成功') {
          answer.praise_count += 1;
          answer.is_praise = 1;
        } else if (res.data.msg === '取消点赞成功') {
          answer.praise_count -= 1;
          answer.is_praise = 0;
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
  },
  // 判断登录
  loginState() {
    checkLogin({
      success: (res) => {
        this.isLogin = true
        this.checkHb()
      },
      fail: (res) => {
        this.isLogin = false
        this.checkHb()
      },
      complete: (res) => {
        this.$store.state.user_login_status = res.status
      },
    })
  },
  showConsult() {
      this.$ajax.get('buildQuestion/isTouristMode', {}, (res) => {
        // 1游客模式
        if (res.data.data !== '1' && this.login_status==1) {
          this.$navigateTo('/user/login/login')
        } else {
          this.$refs.consult.show();
        }
      })
    },
    closeConsult(){
      this.$refs.consult.hide()
    },
    toOptionPath(e){
      if(!e.path&&e.event){
        this[e.event]()
        return
      }
      let real_path = e.path.replace('{build_id}', this.detail.id).replace('{lat}', this.detail.yzhou).replace('{lng}', this.detail.xzhou).replace('{open_adviser}', this.detail.open_adviser?'1':'0').replace('{sand_id}', this.sand_id||'')
      this.$navigateTo(real_path)
    },
    toOptionPath1(e){
      if(!e.path&&e.event){
        this[e.event]()
        return
      }

      this.$navigateTo(e.path)
    },
    toRanks(e){
      this.$navigateTo(`/statistics/search_list?id=${e.id}`)
    },
    toHongbao(){
      this.$navigateTo(`/redEnvelopes/add?id=${this.id}&type=1`)
    },
    checkHb(){
      this.$ajax.get('WxMoney/checkHb', { info_id: this.id, info_type: 1, hb: this.hb}, res => {
        
        if (res.data.code == -1) {
          uni.setStorageSync('backUrl', window.location.href)
          this.$navigateTo("/user/login/login")
        }
        if (res.data.help_fail_desc) {
          uni.showToast({
            title: res.data.help_fail_desc,
            icon: 'none'
          })
          return 
        }
        if (res.data.code == 1) {
          this.hb_info = res.data.hb_info
          this.is_help_link = res.data.is_help_link   // 是否是助力链接进来的 如果是 （值为1）弹出红包弹框
          this.$nextTick(()=> {
            if (this.is_help_link ==1){
              // this.timeDownStart()
              this.$refs.hongbao.showPopup()    //页面不主动弹出领取弹框改为入口打开显示弹框 或者通过助力链接打开时主动弹出
            }
            
          })
          // this.hb_share_query = res.data.hb_share_query
          this.map_key = res.data.txmapwapkey
          if (this.hb_info.is_open) {
            if (res.data.help_task && res.data.help_task.id) {
              this.task_id = res.data.help_task.id
            } else if (this.isLogin) {

              // this.createHb()
            }
            // if (this.hb_share_query) {
            //   this.share.link += `&${this.hb_share_query}`
            // }
            if (this.hb_info.limit_area) {
              this.getWxConfig(['getLocation','updateAppMessageShareData','updateTimelineShareData'], (wx)=>{
                this.wx = wx
              })
            } else {
              this.getWxConfig()
            }
            // if (this.hb_info.limit_area) {
            //   this.getCity()
            // }
          }
        }
      }, err => {console.log(err)}, {disableAutoHandle: true})
    },
    createHb(){
      let form = {
        info_id: this.id,
        info_type: 1,
        hb:this.hb
      }
      this.$ajax.post('WxMoney/createhb', form, res => {
        if (res.data.code == 1) {
          this.hb_result = res.data.hb_result
          this.$nextTick(()=> {
            if (this.is_help_link ==1){
              this.timeDownStart()
              this.$refs.hongbao.showPopup()    //页面不主动弹出领取弹框改为入口打开显示弹框 或者通过助力链接打开时主动弹出
            }
            
          })
        } else if (res.data.help_fail_desc){
          uni.showToast({
            title: res.data.help_fail_desc,
            icon: 'none'
          })
        }
      })
    },
    getCity(options={}) {
      this.$store.state.getPosition(this.wx, (res)=>{
        this.lat = res.lat
        this.lng = res.lng
        getLocation({
          latitude: res.lat,
          longitude: res.lng,
          map_key: this.map_key||'',
          success: cityRes=>{
            this.current_city = cityRes.city
            options.success && options.success(res)
          },
          fail: err=>{
            console.log(err)
            options.fail && options.fail(err)
          }
        })
      })
    },
    playAudio(){
        this.innerAudioContext = uni.createInnerAudioContext();
        // this.innerAudioContext.autoplay = true;
        this.innerAudioContext.loop = false;
        this.innerAudioContext.src = this.tuiguang_mp3;
        // this.innerAudioContext.pause()
        this.innerAudioContext.onPlay(() => {
            console.log('开始播放');
            this.playing = true
        });
        this.innerAudioContext.onEnded(() => {
            console.log('播放结束');
            this.playing = false
        });
        this.innerAudioContext.onError((res) => {
            this.playing = false
            console.log("播放失败")
            console.log(res.errMsg);
            console.log(res.errCode);
        });
        this.innerAudioContext.play()
    },
    openHb() {
      if (!this.playing) this.playAudio()
      if (this.hb_info.limit_area && !this.current_city) {
        uni.showLoading({
            title: '获取位置信息中，请稍等'
        });
        this.getCity({
          success: () => {
            uni.hideLoading()
            this.getHb()
          }, fail: (err) => {
            console.log(err)
            uni.hideLoading()
            this.getHb()
          }
        })
      } else {
        this.getHb()
      }
    },
    getHb() {
      let form = {
        info_id: this.id,
        info_type: 1,
        hb:this.hb,
        area: this.current_city,
      }
      this.$ajax.post('WxMoney/help', form, (res) => {
         uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        if (res.data.code == 1) {
          this.$refs.hongbao.hidenPopup()
          //  let  link = this.share.link.split("&hb=")[0]
          //       this.share.link = link +`&${res.data.hb_share_query}`
          //       this.getWxConfig()
          //       setTimeout(() => {
                    // this.$navigateTo(`/redEnvelopes/index?info_id=${this.id}&info_type=4&task_id=${this.task_id}`)
                // }, 500);

          
        }else {
          if (res.data.state==1){
            this.$refs.hongbao.hidenPopup()
          }else if (res.data.state==2) {
            this.$refs.hongbao.hidenPopup()
            setTimeout(() => {
              showModal({
                content:res.data.msg +',您也可以参与领取红包',
                confirm: res => {
                  this.$navigateTo(`/redEnvelopes/index?info_id=${this.id}&info_type=1`)
                }
              })
            }, 1000);
            
          }
          // uni.showToast({
          //   title: res.data.msg,
          //   icon: 'none'
          // })
        }
      })
    },
    toSaleNews(){
      console.log(1123);
      this.$navigateTo(`/pages/new_house/buildNews?bid=${this.detail.id}&type=sales_news&title=${encodeURIComponent(this.detail.title)}`)
    },
    // 倒计时
    timeDownStart() {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        if (this.hb_result.expire_seconds > 0) {
          this.hb_result.expire_seconds--
        } else {
          clearInterval(this.timer)
        }
      }, 1000)
    },
    toHbHelp() {
      this.$navigateTo(`/redEnvelopes/index?shareType=${this.currentUserInfo.shareType}&info_id=${this.id}&info_type=1&task_id=${this.task_id}`)
    }
  },
  onShareAppMessage() {
    this.doTask()
    let link='';
    let time =parseInt(+new Date()/1000)
    if (this.currentUserInfo.sid){
        link= "/pages/new_house/detail?id="+this.detail.id +"&isShare=1&shareType="+this.currentUserInfo.shareType+"&shareId="+this.currentUserInfo.sid+'&cusId=' +this.current_adviser_id +'&isshare=' +this.isshare+"&f_time="+time
      }else {
        link = '/pages/new_house/detail?id=' + this.detail.id+'&cusId=' +this.current_adviser_id +'&isshare=' +this.isshare
      }
  },
  // #ifdef APP-PLUS
  onNavigationBarButtonTap(option) {
    if (option.index == 0) {
      this.showPopup = true
      // this.$refs.popup.show()
      this.$refs.share_popup.show()
    }
  },
  onBackPress() {
    if (this.showPopup) {
      this.showPopup = false
      // this.$refs.popup.hide()
      this.$refs.share_popup.hide()
      return true
    }
  },
  // #endif
}
</script>

<style scoped lang="scss">
.jm_box{
    margin-top: 20rpx;
    line-height: 60rpx;
}
.jm_tip{
  height: auto!important;
}
.new_house_content {
  padding-bottom: 160rpx;
  color: #333;
  background-color: #fff;
}
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.top_nav{
  position: fixed;
  top: -2rpx;
  z-index: 98;
  width: 100%;
  height: 90rpx;
  background-color: #fff;
  align-items: center;
  justify-content: space-between;
  .nav_item{
    flex: 1;
    height: 84rpx;
    margin:0 20rpx;
    line-height: 84rpx;
    border-bottom: 4rpx solid #fff;
    text-align: center;
    transition: 0.26s;
    &.active{
      color: $uni-color-primary;
      border-color: $uni-color-primary;
    }
  }
}

// 顶部焦点图
.focus-box {
  position: relative;
  swiper.banner {
    height: 75vw;
  }
  .swiper-item {
    height: 100%;
    position: relative;
  }
  .swiper-item image {
    width: 100%;
    height: 100%;
  }
  .swiper-item image.video-icon {
    width: 16vw;
    height: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0);
  }
  .img-total {
    position: absolute;
    padding: 4upx 20upx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    border-radius: 20upx;
    right: 20rpx;
    bottom: 24rpx;
    color: #fff;
    &.style2{
      right: initial;
      left: 20rpx;
    }
  }
  .cate-box {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 24upx;
    display: block;
    text-align: center;
    font-size: 0;
    &.style2{
      .cate-list{
        padding: 6rpx;
        border-radius: 20rpx;
        color: #fff;
        background-color: rgba($color: #000000, $alpha: 0.5);
        &.no_bg{
          background: none;
        }
      }
      .cate{
        padding: 4rpx 24rpx;
        border-radius: 20rpx;
        &.active{
          background: #FF3939;
        }
      }
    }
    .cate-list {
      display: inline-block;
      border-radius: 6rpx;
      overflow: hidden;
      background-color: #fff;
    }
  }
  .cate {
    display: inline-block;
    padding: 8rpx 20rpx;
    font-size: 22rpx;
    // background-color: #fff;
    &.active {
      background: linear-gradient(45deg, #fd9ea3, #fb656a);
      color: #fff;
    }
  }
}

.card-btn {
  position: absolute;
  z-index: 998;
  top: 30rpx;
  right: 20upx;
  width: 60upx;
  height: 60upx;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  padding: 15upx;
  background: rgba($color: #000000, $alpha: 0.6);
}

// 直播提示条
.live-bar {
  flex-direction: row;
  align-items: center;
  flex: 1;
  position: absolute;
  z-index: 1000;
  top: 30rpx;
  /*  #ifdef APP-PLUS  */
  position: fixed;
  top: 160upx;
  /*  #endif  */
  right: 120upx;
  width: 65vw;
  height: 52upx;
  line-height: 52upx;
  overflow: hidden;
  align-items: center;
  padding: 5upx 10upx;
  border-radius: 52upx;
  background-image: linear-gradient(to right, #fc5665, #a70ae3);
  #horse {
    overflow: hidden;
    flex: 1;
    height: 52upx;
    color: #fff;
    max-width: 82%;
  }
  .horse_title {
    padding: 0 20upx;
    max-width: 100%;
  }
  .close {
    color: #fff;
    font-size: 30upx;
    position: absolute;
    right: 20upx;
    padding: 10upx;
  }
}

.container {
  margin: 24rpx 48rpx 0 48rpx;
  .bg3{
    margin-bottom: 30rpx;
    >view{
      background: #eaf1fb;
      padding: 34rpx 24rpx 16rpx 24rpx;
      border-radius: 8rpx;
      align-items: center;
      margin-top: -16rpx;
      font-size: 22rpx;
      text{
        color: #2e84fb;
        flex:1;
      }
      view{
        color: #fff;
        background: #2e84fb;
        border-radius: 40rpx;
        padding: 16rpx 32rpx;
        font-size: 22rpx;
      }
    }
    image{
      width:100%;
    }
  }
  >.label {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    line-height: 1;
    padding: 24rpx 0;
    margin-bottom: 24rpx;
    font-size: 40rpx;
    font-weight: bold;
    .more {
      padding: 8rpx;
      font-size: 22rpx;
      font-weight: initial;
      color: #999;
      &.pd-r-48{
        padding-right: 48rpx;
      }
    }
  }
}

.style2 .container{
  >.label{
    margin-bottom: 12rpx;
    font-size: 32rpx;
  }
}

// 楼盘标题
.build_title-box {
  margin: 24rpx 48rpx;
  align-items: center;
  .title-row {
    box-sizing: border-box;
    width: calc(100% - 100rpx);
  }
  .build_title {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 16rpx;
    align-items: center;
    .title {
      margin-right: 16rpx;
    }
  }
  .build_label {
    flex-wrap: wrap;
    align-items: center;
    .label {
      flex-shrink: 0;
      line-height: 1;
      margin-bottom: 8rpx;
      padding: 4rpx 10rpx;
      font-size: 22rpx;
      color: #999;
      border: 1rpx solid #d8d8d8;
      border-radius: 4rpx;
      ~.label{
        margin-left: 8rpx;
      }
    }
  }
  .jump_btn{
    line-height: 1;
    align-items: center;
    font-size: 22rpx;
    color: #999;
  }
}

// 地址条
.address_row{
  margin: 0 48rpx 24rpx 48rpx;
  padding-bottom: 32rpx;
  align-items: center;
  .icon{
    width: 36rpx;
    height: 36rpx;
    margin-right: 12rpx
  }
  .text{
    overflow: hidden;
    margin-right: 24rpx;
    >text{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .right{
    align-items: center;
    font-size: 24rpx;
    color: #989898;
  }
}

.options1{
  padding: 24rpx 48rpx 0 48rpx;
  justify-content: space-between;
  &.options3{
    margin-bottom: 48rpx;
    padding: 24rpx 24rpx 0 24rpx;
    flex-wrap:wrap;
    justify-content: flex-start;
    >.item{
      min-width:20%;
      .text{
        margin-top: -10rpx;
      }
    }

  }
  >.item{
    // flex: 1;
    align-items: center;
    .icon{
      width: 92rpx;
      height: 92rpx;
    }
    .text{
      margin-top: 10rpx;
      font-size: 24rpx;
      color: #141414;
    }
  }
}

.container{
  >.info_list{
    .item{
      flex-shrink: 0;
      min-width: 50%;
      flex-direction: row;
      line-height: 48rpx;
      margin-bottom: 24rpx;
      // &:last-child{
      //   margin-bottom: 0;
      // }
      .highlight{
        color: $uni-color-primary;
      }
      .label{
        flex-shrink: 0;
        width: 130rpx;
        text-align: justify;
        text-align-last: justify;
        margin-right: 16rpx;
        font-size: 30rpx;
        color: #6f6f6f;
        position: relative;
        bottom: -2rpx;
      }
      .value{
        flex-shrink: 0;
        flex: 1;
        font-size: 32rpx;
        color: #000;
        // font-weight: bold;
        &.highlight{
          color: $uni-color-primary;
        }
      }
      .btn{
        margin-left: 16rpx;
      }
    }
    .open_close{
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      font-size: 26rpx;
      color: #6F6F6F;
    }
    .wxsmcode_img{
      width: 200rpx;
    }
  }
}

.container.hot_sale{
  margin-top: 24rpx;
  margin-right: 0;
  margin-left: 0;
  padding-left: 48rpx;
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: 0 0;
  .label{
    margin-bottom: 0;
  }
  .texts{
    margin-bottom: 24rpx;
    .text{
      margin-right: 12rpx;
      flex-direction: row;
      align-items: center;
      .icon{
        width: 46rpx;
        height: 46rpx;
        border-radius: 50%;
        align-items: center;
        justify-content: center;
        background-color: #FF3939;
        transform: scale(0.6);
      }
    }
  }
  .house_list{
    width: 100%;
    padding-right: 96rpx;
    box-sizing: border-box;
    overflow-y: hidden;
  }
}

// 预售进度
.sales_progress{
  width: 100%;
  padding-left: 48rpx;
  box-sizing: border-box;
  .progress_container{
    padding-right: 48rpx;
    width: 100%;
    overflow-y: hidden;
  }
  .tip{
    padding: 24rpx 0;
    padding-right: 48rpx;
    color: #6f6f6f;
  }
  .labels{
    padding-right: 48rpx;
    justify-content: space-between;
    .item{
      padding: 12rpx 24rpx;
      font-size: 24rpx;
      background-color: #f2f2f2;
      &.hightlight{
        color: $uni-color-primary;
        background-color: rgba($color: $uni-color-primary, $alpha: 0.08);
      }
    }
  }
}

// 楼盘销售状态标签
.status {
  line-height: 1;
  font-size: 28rpx;
  padding: 6rpx 10rpx;
  border-radius: 4rpx;
  color: #fff;
  background-color: #53d2ab;
  font-weight: initial;
  &.status1 {
    color: #fff;
    background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
  }
  &.status2 {
    color: #fff;
    background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
  }
  &.status3 {
    color: #fff;
    background: linear-gradient(to right, #ff8533 0%, #ff7213 100%);
  }
  &.status4 {
    color: #fff;
    background: linear-gradient(to right, #ccc 0%, #ccc 100%);
  }
}

// 获取优惠
.coupon-box2{
  height: 194rpx;
  line-height: 1;
  position: relative;
  .bg_img{
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
  .coupon_container{
    padding: 0 32rpx;
    align-items: center;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
  .coupon_name{
    color:#fff;
    flex: 1;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    margin-right: 16rpx;
    font-weight: bold;
  }
  .coupon_content{
    margin-top: 16rpx;
    font-size: 24rpx;
    flex: 1;
    color: #989898;
  }
  .peoples{
    margin-top: 24rpx;
    align-items: center;
    .text{
      font-size: 22rpx;
      color: #fff;
    }
  }
  .headers{
    margin-right: 16rpx;
  }
  .header_img{
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    ~.header_img{
      margin-left: -12rpx;
    }
  }
  .btn{
    // padding: 0 24rpx;
    height: 48rpx;
    width: 144rpx;
    box-sizing: border-box;
    line-height: 48rpx;
    text-align: center;
    border-radius: 4rpx;
    font-size: 22rpx;
    border: 1rpx solid #ff5757;
    background-color: #fff;
    color: #ff5757;
  }
}
.coupon-box{
  margin-top: 0;
  height: 140rpx;
  line-height: 1;
  color: #fff;
  position: relative;
  .bg_img{
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
  .coupon_container{
    padding: 0 48rpx;
    align-items: center;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
  .coupon_name{
    flex: 1;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    margin-right: 16rpx;
  }
  .coupon_content{
    margin-top: 16rpx;
    font-size: 22rpx;
    flex: 1;
  }
  .btn{
    padding: 0 24rpx;
    height: 48rpx;
    line-height: 48rpx;
    border-radius: 24rpx;
    background-color: #fff;
    color: $uni-color-primary;
  }
}


// 在线选房
.louceng {
  margin-top: 24rpx;
  margin-right: 0;
  background-color: #fff;
  .label{
    margin-bottom: 20rpx;
  }
  .louceng_list {
    .house_swiper {
      height: 55vw;
    }
  }
}

// 楼盘信息
// .build_info-box{
//   justify-content: space-between;
//   margin-top: 0;
//   .info_row{
//     flex-grow: 1;
//     line-height: 48rpx;
//     overflow: hidden;
//     margin-bottom: 24rpx;
//     .label{
//         flex-shrink: 0;
//         width: 130rpx;
//         text-align: justify;
//         text-align-last: justify;
//         margin-right: 16rpx;
//         font-size: 30rpx;
//         color: #6f6f6f;
//       }
//     >.value{
//       // flex-shrink: 0;
//       flex: 1;
//       // line-height: 1;
//       align-items: flex-start;
//       flex-direction: row;
//       overflow: hidden;
//       // >text{
//       //   white-space: nowrap;
//       //   overflow: hidden;
//       //   text-overflow: ellipsis;
//       // }
//     }
//     .value{
//       font-size: 32rpx;
//       color: #000;
//       .price{
//         font-size: 40rpx;
//         ~.unit{
//           position: relative;
//           left: 6rpx;
//           top: 2rpx;
//           font-size: 26rpx;
//         }
//       }
//     }
//     .btn{
//       margin-left: 16rpx;
//     }
//     .highlight{
//       color: $uni-color-primary;
//     }
//     .right{
//       display: inline-block;
//       // display: flex;
//       flex-shrink: 0;
//       flex-direction: row;
//       align-items: center;
//       margin-left: 16rpx;
//       color: $uni-color-primary;
//     }
//     .daohang_box{
//       margin-left: 12rpx;
//       width: 36rpx;
//       height: 36rpx;
//       border-radius: 50%;
//       display: flex;
//       align-items: center;
//       justify-content: center;
//       background-image: linear-gradient(290deg, #FB656A, #FBAC65);
//     }
//   }
// }

// 楼盘信息
.build_info-box{
  margin-bottom: 24rpx;
  justify-content: space-between;
  .info_item{
    flex-grow: 1;
    // min-width: 206rpx;
    // flex: 1;
    &.center{
      margin: 0 24rpx;
      padding: 0 24rpx;
      // min-width: 200rpx;
      // flex-shrink: 0;
    }
    .data{
      line-height: 1;
      color: $uni-color-primary;
      align-items: flex-end;
      margin-bottom: 16rpx;
    }
    .value{
      font-size: 36rpx;
      font-weight: bold;
    }
    .unit{
      margin-left: 8rpx;
      font-size: 24rpx;
    }
    .label{
      align-items: center;
      font-size: 28rpx;
      color: #999;
      .icon-box{
        margin-left: 16rpx;
      }
    }
  }
}



// 报名按钮
.btn_list-box {
  margin-top: 32rpx;
  .btn-item {
    padding: 20rpx 5rpx;
    flex: 1;
    align-items: center;
    justify-content: center;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.05);
    color: $uni-color-primary;
    ~ .btn-item {
      margin-left: 14rpx;
    }
    text {
      font-size: 32rpx;
      margin-left: 16rpx;
    }
    .img{
      width: 40rpx;
      height: 40rpx;
      overflow: hidden;
      .img_c{
        width: 100%;
        height: 100%;
      }
    }
  }
}

// 楼盘地址
.address-box {
  margin-top: 24rpx;
  padding: 24rpx 0;
  justify-content: space-between;
  align-items: center;
  .address {
    display: inline-block;
    flex: 1;
    margin-left: 20rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .jump_btn {
    font-size: 22rpx;
    align-items: center;
    color: #999;
  }
}


// 400电话
.tel-box2{
  // margin-bottom: 24rpx;
  background: #fff;
  border: 1rpx solid #ffa800;
  border-radius: 8rpx;
  position: relative;
  background-color: #fff7e9;
  .tel_block{
    align-items: center;
    padding: 22rpx 32rpx;
    // background-color: #fff;
    border-radius: 55rpx;
    line-height: 1;
    .tel_right{
      margin-left: 24rpx;
      flex-direction: row;
      align-items: center;
      flex: 1;
      justify-content: space-between;
      .btn{
        margin-left: 24rpx;
        display: inline-block;
        height: 48rpx;
        line-height: 48rpx;
        width: 108rpx;
        border-radius: 4rpx;
        text-align: center;
        background-image: linear-gradient(180deg, #ffa800 0, #ff7a00 100%);
        font-size: 22rpx;
        color: #fff;
      }
    }
    .tel{
      // font-size: 36rpx;
      font-weight: bold;
      // background: linear-gradient(to right,#ff706b,#fdaa5e);
      // -webkit-background-clip: text;
      // color: transparent;
      // margin-bottom: 16rpx;
    }
    .tip{
      font-size: 22rpx;
      color: #999;
    }
  }
  .tel_left{
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
    border-radius: 18rpx;
    background-image: linear-gradient(180deg, #ffa800 0, #ff7a00 100%);
    // background: linear-gradient(to right,#ff706b,#fdaa5e);
  }
  // .tel_bg{
  //   height: 100%;
  //   width: 100%;
  //   border-radius: 56rpx;
  //   background:linear-gradient(to right,#ff706b,#fdaa5e);
  //   box-shadow: 0 2px 4px 0 rgba(#ff706b,0.2);
  // }
}
.tel-box{
  margin-top: 24rpx;
  height: 112rpx;
  background: #fff;
  position: relative;
  .tel_block{
    align-items: center;
    justify-content: space-between;
    top: 1rpx;
    bottom: 1rpx;
    left: 1rpx;
    right: 1rpx;
    padding: 24rpx 48rpx;
    position: absolute;
    background-color: #fff;
    border-radius: 55rpx;
    line-height: 1;
    .tel{
      font-size: 36rpx;
      font-weight: bold;
      background: linear-gradient(to right,#ff706b,#fdaa5e);
      -webkit-background-clip: text;
      color: transparent;
      margin-bottom: 16rpx;
    }
    .tip{
      font-size: 22rpx;
      color: #999;
    }
  }
  .tel_right{
    align-items: center;
    justify-content: center;
    width: 66rpx;
    height: 66rpx;
    border-radius: 33rpx;
    background: linear-gradient(to right,#ff706b,#fdaa5e);
  }
  .tel_bg{
    height: 100%;
    width: 100%;
    border-radius: 56rpx;
    background:linear-gradient(to right,#ff706b,#fdaa5e);
    box-shadow: 0 2px 4px 0 rgba(#ff706b,0.2);
  }
}

// 排行榜
.rank{
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 22rpx;
  ~.label{
    margin-left: 8rpx;
  }
  .icon{
    width: 140rpx;
    height: 36rpx;
    position: relative;
    z-index: 1;
  }
  .text{
    height: 36rpx;
    line-height: 36rpx;
    padding-left: 38rpx;
    padding-right: 12rpx;
    position: relative;
    margin-left: -28rpx;
    font-size: 22rpx;
    color: #565656;
    background: linear-gradient(90deg, #FCF6DB 0%, #FBD38B 100%);
  }
}
// .rank{
//   padding: 24rpx;
//   margin-bottom: 24rpx;
//   align-items: center;
//   flex-direction: row;
//   border-radius: 4rpx;
//   background-size: 100% 100%;
//   .img{
//     width: 200rpx;
//     margin-right: 12rpx;
//     flex-shrink: 0;
//   }
//   .text{
//     margin-left: 12rpx;
//     display: flex;
//     flex-direction: row;
//     align-items: center;
//     margin-right: 12rpx;
//     flex: 1;
//     overflow: hidden;
//     text-overflow: ellipsis;
//     white-space: nowrap;
//     font-size: 30rpx;
//     color: #fff;
//     position: relative;
//     &::before{
//       content: '';
//       height: 6rpx;
//       width: 6rpx;
//       margin-right: 12rpx;
//       border-radius: 50%;
//       background-color: #fff;
//     }
//   }
// }

// 最新动态
.news{
  margin-top: 24rpx;
}
.news_list{
  .news_item{
    padding-left: 20rpx;
    padding-bottom: 24rpx;
    position: relative;
    &::before{
      content:'';
      position: absolute;
      width: 2rpx;
      top: -10rpx;
      bottom: 0;
      left: -0.5px;
      background-color: #f5f5f5;
    }
    &::after{
      content:'';
      position: absolute;
      width: 20rpx;
      height: 20rpx;
      border-radius: 50%;
      background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
      top: 10rpx;
      left: -10rpx;
    }
    &.kaipan{
      &::after{
        background-image: linear-gradient(180deg, #69D4BB 0%, #00CAA7 100%);
      }
      .time{
        .attr{
          background-image: linear-gradient(180deg, #69D4BB 0%, #00CAA7 100%);
        }
      }
    }
    &.jiaofang{
      &::after{
        background-image: linear-gradient(180deg, #8CD3FC 0%, #4CC7F6 100%);
      }
      .time{
        .attr{
          background-image: linear-gradient(180deg, #8CD3FC 0%, #4CC7F6 100%);
        }
      }
    }
    &.cert{
      &::after{
        background-image: linear-gradient(180deg, #FCD88C 0%, #F6CE4C 100%);
      }
      .time{
        .attr{
          background-image: linear-gradient(180deg, #FCD88C 0%, #F6CE4C 100%);
        }
      }
    }
    &.official{
      &::after{
        background-image: linear-gradient(180deg, #ff9767 0%, #fd7737 100%);
      }
      .time{
        .attr{
          background-image: linear-gradient(180deg, #ff9767 0%, #fd7737 100%);
        }
      }
      .adviser_info{
        flex-direction: row;
        align-items: center;
        margin-bottom: 24rpx;
        line-height: 1;
        .text{
          display: inline-block;
          max-width: 256rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .label{
          padding: 6rpx 8rpx;
          font-size: 22rpx;
          border-radius: 6rpx;
          color: #fff;
          margin-left: 16rpx;
          background-image: linear-gradient(180deg, #8CD3FC 0%, #1ec3fa 100%);
        }
      }
      .prelogo{
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 16rpx;
      }
      .chat_btn{
        margin-left: 24rpx;
        align-items: center;
        font-size: 22rpx;
        color: #fff;
        height: 40rpx;
        border-radius: 22rpx;
        padding: 4rpx 12rpx;
        background: linear-gradient(90deg, #FB656A 30%, #FBAC65 100%);
      }
      .image_list{
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        .share_img{
          width: 106rpx;
          min-width: 106rpx;
          height: 106rpx;
          margin-top: 16rpx;
        }
        .share_empty{
          width: 106rpx;
          min-width: 106rpx;
          height: 0;
        }
      }
    }

    .time{
      align-items: center;
      padding-left: 20rpx;
      margin-bottom: 24rpx;
      font-size: 22rpx;
      color: #999;
      .attr{
        line-height: 1;
        padding: 4rpx 10rpx;
        margin-right: 10rpx;
        font-size: 24rpx;
        color: #fff;
        background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
        border-top-left-radius: 8rpx;
        border-bottom-right-radius: 8rpx;
      }
    }
    .title{
      font-weight: bold;
      font-size: 32rpx;
      padding: 0 20rpx;
      margin-bottom: 24rpx;
    }
    .sub_title{
      padding: 0 20rpx;
      font-size: 28rpx;
      color: #666;
      margin-bottom: 24rpx;
    }
    .sub_title1{
      margin-bottom: 20rpx;
      padding: 0 20rpx;
      text{
        color: #666;
        margin-right: 16rpx;
      }
      view{
        color: #000;
        font-weight: 500;
        flex: 1;
      }
    }
  }
  .add_build_news{
    .add_build_news_btn{
      text-align: center;
      padding: 10px 2px;
      background-color: rgba(255, 101, 107, 0.05);
      color: #ff656b;
    }
  }
}

// 轮播广告图
.ext_swiper{
  margin-top: 48rpx;
  height: 140rpx;
  swiper-item{
    height: 100%;
    background-color: #f5f5f5;
    border-radius:16rpx;
    overflow: hidden;
    position: relative;
    >image{
      height: 100%;
      width: 100%;
    }
    .marker{
      line-height: 1;
      padding: 4rpx 10rpx;
      position: absolute;
      right: 12rpx;
      bottom: 10rpx;
      font-size: 20rpx;
      border-radius: 4rpx;
      background-color: rgba($color: #000000, $alpha: 0.5);
      color: #fff;
    }
  }
}
.style2 .ext_swiper{
  margin-top: 24rpx;
}

// 发现好房
.build_box{
  margin-right: 0;
  margin-top: 24rpx;
}
.build-swiper{
  height: 294rpx;
  .swiper-item{
    height: 100%;
    border-radius: 8rpx;
    margin-right: 30rpx;
    line-height: 1;
    .img{
      width: 100%;
      height: 200rpx;
      margin-bottom: 16rpx;
      border-radius: 8rpx;
    }
    .title{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 32rpx;
      margin-bottom: 16rpx;
      font-weight: bold;
      color: #333;
    }
    .price{
      font-size: 32rpx;
      color: $uni-color-primary;
    }
  }
}

// 购房群
.group{
  margin-top: 48rpx;
  padding: 24rpx 32rpx;
  border-radius: 8rpx;
  background: #fff7e9;
  border: 1px solid #ffa800;
  // background: $uni-color-primary;
  // box-shadow: 0 2px 6px 0 rgba($uni-color-primary,0.40);
  // color: #fff;
  align-items: center;
  line-height: 1;
  .left{
    flex: 1;
    .title{
      margin-bottom: 24rpx;
    }
    .peoples{
      align-items: center;
      .text{
        font-size: 22rpx;
      }
    }
    .headers{
      margin-right: 16rpx;
    }
  }
  .header_img{
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    &.header_img{
      margin-left: -20rpx;
    }
  }
  .btn{
    height: 48rpx;
    line-height: 48rpx;
    padding: 0 24rpx;
    // border-radius: 24rpx;
    // background-color: #fff;
    // color: $uni-color-primary;
    color:#fff;
    background-image: linear-gradient(180deg, #ffa800 0, #ff7a00 100%);
    border-radius:4rpx;
    font-size: 22rpx;
  }
}

// 户型图
.house_img-box {
  margin-top: 24rpx;
  margin-right: 0;
  .label{
    padding-right: 48rpx;
  }
  .house_img-list {
    height: 440rpx;
    .swiper-item {
      margin-right: 24rpx;
      .add_type_price{
        margin-top: 10rpx;
        .add_type_price_btn{
          text-align: center;
          padding: 10px 2px;
          background-color: rgba(255, 101, 107, 0.05);
          color: #ff656b;
        }
      }
      .img-box {
        height: 200rpx;
        position: relative;
      }
      image {
        width: 100%;
        height: 100%;
      }
      .vr-icon{
        position: absolute;
        width: 60rpx;
        height: 60rpx;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
      }
      .house_type {
        display: block;
        margin-top: 20rpx;
        font-size: 32rpx;
        // height: 44rpx;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .aligin-end {
        align-items: flex-end;
      }
      .stw {
        align-items: center;
        margin-top: 10rpx;
        margin-right: 10rpx;
        font-size: 28rpx;
        color: #999;
      }
      .status{
        font-size: 22rpx;
        padding: 4rpx 10rpx;
      }
      .mj-box {
        align-items: center;
        justify-content:space-between;
      }
      .db_btn{
        line-height: 30rpx;
        padding: 0 8rpx;
        border-radius: 15rpx;
        font-size: 22rpx;
        border: 1rpx solid $uni-color-primary;
        color: $uni-color-primary;
      }
      .mianji {
        margin-left: 10rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: $uni-color-primary;
      }
    }
  }
}

// 周边
.map_location-box {
  margin-top: 24rpx;
  .map_address{
    position: relative;
    top: -24rpx;
    .label{
      flex-shrink: 0;
      color: #999;
    }
    .value{
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 购房咨询师
.block{
  margin-top: 24upx;
  .label{
    margin-bottom: 0;
  }
}
.advier-list {
  background-color: #fff;
  .adviser-item {
    justify-content: space-between;
    align-items: flex-start;
    padding: 30rpx 0;
    .header_img {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 15rpx;
      overflow: hidden;
      background-color: #f3f3f3;
    }
    image {
      width: 100%;
    }
    .info {
      flex: 1;
      overflow: hidden;
      .name {
        display: flex;
        align-items: center;
        margin-bottom: 6rpx;
        .text {
          // flex: 1;
          font-size: 32rpx;
        }
      }
      .mgl-20 {
        margin-left: 20rpx;
      }
      .data {
        display: inline-block;
        margin-bottom: 6rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 22rpx;
        color: #999;
      }
    }

    .adviser-right {
      align-items: flex-end;
    }

    .btn-list {
      align-items: center;
      text {
        color: #999;
      }
      .btn {
        width: 64rpx;
        height: 64rpx;
        ~ .btn {
          margin-left: 30rpx;
        }
        .icon-box {
          width: 64rpx;
          height: 64rpx;
          justify-content: center;
          text-align: center;
          border-radius: 50%;
          background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
        }
        .icon {
          width: 64rpx;
          height: 64rpx;
        }
      }
    }
  }
}
// 电子沙盘
.loudong {
  margin-top: 24rpx;
  .loudong_card {
    // padding: 20rpx;
    touch-action: none;
  }
}

// 用户点评
.comment-box {
  margin-top: 24rpx;
  .comment-item {
    margin-bottom: 48rpx;
    .header_img {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      margin-right: 10rpx;
    }
    .comment_info {
      flex: 1;
      .user_name {
        font-size: 22rpx;
        color: #999;
        margin-bottom: 6rpx;
      }
      .level{
        width: 40rpx;
        position: relative;
        left: 6rpx;
        top: 5rpx;
      }
      .comment_title {
        display: block;
        margin-bottom: 16rpx;
        line-height: 1.5;
      }
      .comment_time {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        font-size: 22rpx;
        color: #999;
      }
      .options-box {
        align-items: center;
        font-size: 22rpx;
        color: #d8d8d8;
        text {
          margin-left: 10rpx;
        }
        .comment_num {
          flex-direction: row;
          align-items: center;
        }
        .praise_num {
          flex-direction: row;
          align-items: center;
          margin-left: 26rpx;
        }
      }
      .btn_row{
        flex-direction: row;
        justify-content: flex-end;
        .icon_box{
          align-items: center;
          justify-content: center;
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          margin-right: 6rpx;
          background-color: #ff4a4a;
        }
        .btn{
          flex-direction: row;
          align-items: center;
          width: 170rpx;
          height: 48rpx;
          padding: 6rpx;
          border-radius: 24rpx;
          font-size: 22rpx;
          color: #ff4a4a;
          background-color: #ffe3e3;
        }
      }
      .highlight{
        color: #ff4a4a;
      }
      .copy_btn{
        display: inline-block;
        margin-left: 12rpx;
        line-height: 46rpx;
        padding: 0 12rpx;
        border-radius: 23rpx;
        font-size: 22rpx;
        background-color: $uni-color-primary;
        color: #fff;
      }
    }

    .reply-list {
      margin-top: 20rpx;
      padding: 10rpx 16rpx;
      background-color: #f2f2f2;
    }
    .reply-item {
      font-size: 22rpx;
      padding: 8rpx;
      padding-left: 50rpx;
      align-items: flex-start;
      display: block;
      .name_info{
        align-items: center;
        display: inline-block;
        font-size: 22rpx;
      }
      .reply_user_header_img {
        width: 40rpx;
        height: 40rpx;
        margin-left: -50rpx;
        position: relative;
        top: 8rpx;
        border-radius: 50%;
        margin-right: 10rpx;
      }
      .identity{
        font-size: 22rpx;
        line-height: 1;
        padding: 4rpx 8rpx;
        margin: 0 10rpx;
        color: $uni-color-primary;
        background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
      }
      .reply_content{
        flex: 1;
      }
    }
  }
  .comment_btn{
    padding: 24rpx;
    justify-content: center;
    color: $uni-color-primary;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
    >text{
      margin-left: 16rpx;
    }
  }
}

// 推荐楼盘列表
.recommend_house-box {
  margin-top: 24rpx;
}

// 免责声明
.shengming {
  margin-top: 32rpx;
  color: #999;
  .shengming_title {
    font-size: 30rpx;
    margin-bottom: 16rpx;
  }
  .shengming_content {
    font-size: 26rpx;
    line-height: 1.8;
  }
}

// 底部菜单
.bottom-bar {
  background-color: #fff;
  height: 110rpx;
  padding: 15rpx 48rpx;
  left: 0;
  z-index: 10;
  .bar-left{
    padding-right: 10rpx;
    justify-content: flex-start;
  }
  .icon-btn {
    // width: 100rpx;
    align-items: center;
    padding: 0;
    margin: 0;
    background-color: #fff;
    line-height: initial;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // flex: 1;
    padding-right: 32rpx;
    overflow: hidden;
    position: relative;
    // & ~ .icon-btn {
    //   margin-left: 24rpx;
    // }
    .header_img{
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
    }
    text {
      line-height: 1;
      font-size: 22rpx;
      color: #999;
      display: inline-block;
      width: 100%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .badge{
      display: inline-block;
      box-sizing: border-box;
      width: auto;
      position: absolute;
      top: 0;
      left: 32rpx;
      // right: 38rpx;
      height: 28rpx;
      padding: 0 8rpx;
      min-width: 28rpx;
      border-radius: 14rpx;
      font-size: 22rpx;
      background-color: $uni-color-primary;
      color: #fff;
    }
  }
  .alone{
    border-radius:22rpx;
  }
  .bar-btn {
    // width: 220rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    padding: 0;
    margin: 0;
    border-radius: 0;
    color: #fff;
    &.btn1 {
      background: #FBAC65;
      box-shadow: 0 0 4px 0 rgba(0,0,0,0.05);
      border-top-left-radius: 40rpx;
      border-bottom-left-radius: 40rpx;
    }
    &.btn2 {
      background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
      box-shadow: 0 0 4px 0 rgba(255,80,0,0.30);
      border-top-right-radius: 40rpx;
      border-bottom-right-radius: 40rpx;
    }
  }
}


.build-detail .share-box {
  margin-bottom: 120upx;
}
.share-box {
  padding: 20upx 0;
  // margin-bottom: 90upx;
  background-color: #fff;

  .tip {
    padding: 10px;
    width: 100%;
    font-weight: 700;
    box-sizing: border-box;
    text-align: center;
  }

  button {
    line-height: initial;
    padding: 10upx 20upx;
    background-color: #fff;
  }

  .item {
    text-align: center;
    padding: 10upx 20upx;
    line-height: inherit;
  }
}
.card-img {
  width: 80%;
  margin: 0 10%;
  padding: 40upx 0;
}

// 复制文案
.copy-text-box{
  padding: 24rpx 32rpx;
  background-color: #fff;
  width: 600rpx;
  // height: 100%;
  margin-left: 75rpx;
  border-radius: 16rpx;
  .title{
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }
  .info-row{
    line-height: 1.6;
    color: #333;
    .label{
      color: #999;
    }
    .value{
      flex: 1;
      &.highlight{
        color: $uni-color-primary;
      }
    }
  }
  .button{
    align-items: center;
    justify-content: center;
    line-height: 64rpx;
    width: 236rpx;
    text-align: center;
    border-radius: 32rpx;
    margin: 32rpx auto;
    background: #FB656A;
    box-shadow: 0 2px 8px 0 rgba(251,101,106,0.40);
    color: #fff;
  }
  .disabled-btn{
    background-color: #f5f5f5;
    box-shadow: none;
    color: #999;
    >.text{
      margin-left: 12rpx;
    }
  }
}
.jm_url{
  padding: 24rpx;
  margin: 0 48rpx;
  margin-top: 32rpx;
  text-align: center;
  font-size: 36rpx;
  background-color: $uni-color-primary;
  color: #fff;
  border-radius: 16rpx;
}
.foot_left {
  justify-content: flex-start;
  align-items: center;
  // padding: 26rpx 5rpx 26rpx 20rpx;
  flex: 1;
  .left {
    height:65upx;
    width: 65upx;
    overflow: hidden;
    border-radius:65upx;
    image {
      height: 65upx;
      width: 65upx;
      border-radius:65upx;
      overflow: hidden;
      position: relative;
    }
  }
  .right {
    height: 100%;
    // width: 100%;
    overflow: hidden;
    margin-left: 10upx;
    display: flex;
    padding: 6rpx 0;
    flex-direction: column;
    justify-content: space-between;
    

    .name {
      font-size: 25upx;
      //  height: 15upx;
      line-height: 1;
       max-width: 220upx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .sub_name {
      font-size: 22upx;
      color: #666;
      //  height: 20upx;
      // margin-top: 10upx;
      line-height: 1;
      justify-content: flex-start;
      align-items: center;
      padding: 10rpx；

    }

  }

}
.tip_container{
  width: 80%;
  height: 396rpx;
  box-sizing: border-box;
  padding: 24rpx 48rpx;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  border-radius: 16rpx;
  background-color: #fff;
  .title{
    text-align: center;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  .desc{
    font-size: 28rpx;
    color: #666;
  }
  .btn{
    padding: 24rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: $uni-color-primary;
  }
}
// 楼盘问答
.question-box {
      background: #ffffff;
      .title-icon {
      width: 48rpx;
      height: 48rpx;
      line-height: 48rpx;
      text-align: center;
      border-radius: 16rpx 0 16rpx 0;
      color: #ffffff;
      font-size: 22rpx;
    }
    .question {
      display: flex;
      flex-direction: row;
      padding-bottom: 40rpx;
      .question-icon {
        background: linear-gradient(90deg, #ffa857 0%, #ff6069 100%);
      }
      .question-span {
        flex: 1;
        font-family: PingFangSC-Medium;
        padding-left: 20rpx;
        .question-content {
          font-size: 32rpx;
          color: #333333;
          letter-spacing: 0;
          text-align: justify;
          line-height: 48rpx;
          font-weight: bold;
        }
      }
      .question-info {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding-top: 20rpx;
        font-size: 22rpx;
        color: #999999;
        view {
          font-size: 22rpx;
        }
      }
    }
    .answer {
      display: flex;
      flex-direction: row;
      // &:last-child .answer-content {
      //     border-bottom: 1rpx solid #d8d8d8;
      //   }
      .answer-icon {
        background: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
      }
      .answer-content {
        position: relative;
        margin-left: 20rpx;
        flex: 1;
        padding-bottom: 40rpx;
        .answer-text {
          position: relative;
          .text {
            color: #666666;
            line-height: 42rpx;
            font-size: 28rpx;
          }
          .more {
            text-align: right;
            font-size: 22rpx;
            color: #fb656a;
          }
        }
        .answer-info {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          color: #999999;
          padding-top: 20rpx;
          .answer-author {
            display: flex;
            flex-direction: row;
            line-height: 64rpx;
            font-size: 22rpx;
            .author-icon {
              width: 64rpx;
              height: 64rpx;
              border-radius: 50%;
            }
            .answer-name {
              padding-left: 20rpx;
              padding-right: 40rpx;
            }
          }
          .answer-num {
            display: flex;
            flex-direction: row;
            line-height: 64rpx;
            font-size: 22rpx;
            align-items: center;
            justify-content: center;
            text {
              padding-left: 10rpx;
            }
          }
        }
      }
    }
}
.more-question {
  text-align: center;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.no_qa {
  display: flex;
  flex-direction: column;
  align-items: center;
  .no_qa-img {
    width: 350rpx;
  }
  .no_qa-content {
    font-size: 26rpx;
    color: #999;
  }
  .no_qa-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 200rpx;
    height: 50rpx;
    margin-top: 20rpx;
    color: #fff;
    font-size: 26rpx;
    background: linear-gradient(90deg, #ffa857 0%, #ff6069 100%);
    border-radius: 40rpx;
    text-align: center;
    text {
      margin-left: 5rpx;
    }
  }
}
.no_comment {
  margin-bottom: 20rpx;
}
.test_con_item{

  text-align: center;
  &_title{
    margin: 20rpx auto 10rpx;
    color: #333;
    font-weight: 600;
  }
  &_sub_title{
    font-size: 24rpx;
  }
  .test_con_item_icon{
    width: 80rpx;
    margin: 0 auto;
    height: 80rpx;
    image {
      width: 100%;
      height: 100%;
    }
  } 
}  
.test_submit {
  margin-top: 40rpx;
  text-align: center;
  padding: 10px 2px;
  background-color: rgba(255, 101, 107, 0.05);
  color: #ff656b;
}
.test_submit_con {
  background: #fff;
  padding: 60rpx;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  .test_submit_close{
    position: absolute;
    top: 20rpx;
    left:50rpx;
    width:50rpx;
    height: 50rpx;
    text-align: center;
    font-size: 50rpx;
  }
  .test_submit_title{
    margin-top: 40rpx;
    justify-content: space-between;
    align-items: center;
  }
  .title{
    font-size: 36rpx;
    color: #333;
    font-weight: 600;
    
  }
  .test_submit_sub_title{
    font-size: 24rpx;
    color: #666;
    // margin-top: 10rpx;
  }
  .test_submit_info{
    // font-weight: 600;
    margin-top: 20rpx;
    font-size:28rpx;
    color: #666;
  }
  input{
    background:#f3f3f3;
    border-radius: 60rpx;
    height: 60rpx;
    font-size: 28rpx;
    padding: 10rpx 20rpx;
    margin: 40rpx 10rpx 20rpx;
  }
  .submit_test_info{
    width: 95%;
    margin: 30rpx auto 20rpx;
    padding: 20rpx 0;
    text-align: center;
    border-radius: 60rpx;
    background-color: rgba(255, 101, 107, 0.05);
    color: #ff656b;
  }
}

.rz_strip{
  align-items: center;
  line-height: 1;
  padding: 10rpx 20rpx;
  background-image: linear-gradient(90deg, #F0D4B0 0%, #FBEFDB 100%);
  border-radius: 4rpx;
  border-radius: 4rpx;
  color: #373131;
  .rz_icon{
    width: 15px;
    height: 15px;
    margin-right: 4rpx;
  }
  .text_block{
    flex: 1;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .text{
      line-height: 1;
      padding: 0 16rpx;
      font-size: 26rpx;
      position: relative;
      &.label{
        &:after{
          content: "";
          position: absolute;
          left: 0;
          top: 8rpx;
          bottom: 6rpx;
          width: 4rpx;
          -webkit-transform: scaleX(.5);
          transform: scaleX(.5);
          background-color: #373131;
        }
      }
    }
  }
}

.full_box{
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  bottom: 0;
  .full_content{
    padding: 48rpx;
    border-radius: 24px 24px 0 0;
    color: #fff;
    background-color: rgba($color: #333333, $alpha: 0.2);
    // backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
    .bg{
      border-radius: 24px 24px 0 0;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      filter: blur(4px);
      overflow: hidden;
      image{
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  }
  .build_icon{
    width: 128rpx;
    height: 128rpx;
    border-radius: 8rpx;
    padding: 4rpx;
    background-color: #F0D4B0;
    position: absolute;
    top: -64rpx;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    overflow: hidden;
    image{
      border-radius: 8rpx;
      width: 100%;
      height: 100%;
    }
  }
  .media_box{
    padding: 10rpx;
    position: absolute;
    right: 24rpx;
    top: -300rpx;
    .icon_btn{
      width: 80rpx;
      height: 80rpx;
      margin: 24rpx;
      &.font{
        text-align: center;
        border-radius: 50%;
        background-color: rgba($color: #000000, $alpha: 0.4);
      }
    }
  }
  .rz_strip{
    margin-top: 48rpx;
  }
  .title_row{
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #d8d8d8;
    >.flex-row{
      overflow: hidden;
      align-items: center;
    }
    .title{
      font-size: 40rpx;
      font-weight: bold;
      margin-right: 12rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .price{
      margin-left: 8rpx;
    }
  }
  .build_info{
    margin-top: 24rpx;
    .info_row{
      padding: 12rpx 0;
      justify-content: space-between;
      .label{
        display: inline-block;
        // width: 180rpx;
      }
      .value{
        flex: 1;
      }
    }
    .left{
      overflow: hidden;
      flex: 1;
      .value{
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
    .right{
      margin-left: 8rpx;
      display: inline-block;
      font-size: 22rpx;
      .icon_btn{
        width: 48rpx;
        height: 48rpx;
        ~.icon_btn{
          margin-left: 24rpx;
        }
      }
    }
  }
  .tip{
    text-align: center;
    margin-top: 24rpx;
    padding: 24rpx;
    font-size: 22rpx;
  }
}

// 价格说明
.price_desc{
  margin: 0 48rpx;
  display: flex;
  flex-direction: row ;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16rpx 24rpx;
  padding-right: 22rpx;
  border-radius: 10rpx;
  // border: 1rpx solid $uni-color-primary;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.05);
  color: #999;
  // box-shadow: 0 2rpx 8rpx 0 rgba(251, 101, 106, 0.4);
  position: relative;
  &:after{
    content: '';
    position: absolute;
    z-index: 2;
    width: 32rpx;
    height: 32rpx;
    top: -18rpx;
    transform: rotate(45deg);
    // border-top: 1rpx solid $uni-color-primary;
    // border-left: 1rpx solid $uni-color-primary;
    background-color: #fff7f7;
  }
  .items-center{
    align-items: center;
  }
  .text{
    flex: 1;
  }
  .btn-item{
    margin-left: 10rpx;
    color: #ff656b;
    // float: right;
  }
}

.weapp{
  width: 100vw;
  height: 75vw;
}
.hongbao {
  position: absolute;
  right: 20rpx;
  width:120rpx;
  height:120rpx;
  white-space: nowrap;
  overflow: hidden;
  bottom: 24rpx;
  .hb_content{
    width:120rpx;
    height:120rpx;
    display: block;
    position: absolute;
    left:120rpx;
    top:0;

    // .hb_img{
    //   opacity:0;
    //   transition: opacity 1500ms ease 1500ms;
    //   &.showHb{
    //     opacity:1;
    //     transition: opacity 1500ms ease 1500ms;
    //   }
    // }
  }
  image {
    position:absolute;
    left: 0;
    top:0;
    width: 120rpx;
    height: 120rpx;
    // transition: opacity 1500ms ease 1500ms;
  }
}

.chat_box{
  .chat_btn{
    margin-left: 24rpx;
    align-items: center;
    font-size: 22rpx;
    color: #fff;
    height: 40rpx;
    border-radius: 22rpx;
    padding: 4rpx 12rpx;
    background: linear-gradient(90deg, #FB656A 30%, #FBAC65 100%);
  }
}
.time_box{
  flex:1
}
// 购房群
.qr-code {
  padding: 30upx;
  padding-bottom: 140upx;
  background-color: #fff;

  .title {
    padding: 20upx 0;
    text-align: center;
    font-size: 36upx;
    margin-bottom: 20upx;
  }
  .desc {
    flex-direction: row;
  }

  .info {
    flex: 1;
    padding: 10upx 0;

    .save-btn {
      text-align: center;
      padding: 12upx 20upx;
      border: 1upx solid #61c351;
      border-radius: 10upx;
    }

    .tip {
      flex: 1;
      display: inline-block;
      padding: 20upx 0;
    }

    .color-red {
      color: #f44;
    }

    .copy-btn {
      text-align: center;
      padding: 12upx 20upx;
      background-color: #f3f3f3;
      border-radius: 10upx;
    }
  }

  .wechat-img {
    width: 40%;
    margin-right: 10upx;

    image {
      width: 100%;
      height: 100%;
    }
  }
}

</style>
