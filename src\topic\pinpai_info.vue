<template>
  <view class="page">
    <view class="info">
      <view class="info_c" v-html ="content">

      </view>
    </view>
    <view class="jindu" v-if ="show_step">
       浏览{{read_time}}秒后完成任务
    </view>
  </view>
</template>

<script>
export default {
  data(){
    return {
      content:'',
       read_time:15,
      show_step:false
    }
  },
  onLoad(options){
    if (options.id){
      this.id = options.id
      this.getInfo()
    }
 if (options.from){
      this.from = options.from
      this.show_step =true
      this.timer = setInterval(() => {
        if (this.read_time==0) {
          clearInterval(this.timer)
          uni.showToast({
            title:'当前浏览任务已完成',
            icon:"none"
          })
          this.show_step =false
          return 
        }
        this.read_time--
      }, 1000);
    }

   
  },
  beforeDestroy(){
    if (this.from && !this.show_step){
      uni.$emit("finished",{
        status:1,
        task_name:'view_content'
      })
    }
    
        
  },
  onUnload(){
    clearInterval(this.timer)
  },
  methods:{
    getInfo(){
    this.$ajax.get("blind_box/blindBoxContent",{id:this.id},(res=>{
      console.log(res);
      if (res.data.code ==1){
        this.content = res.data.content
      }else {
        uni.showToast({
          title:res.data.msg,
          icon:"none"
        })
      }
    }))
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  padding: 48rpx;
  background: #fff;
  min-height: calc(100vh - 185rpx);
  .info {
    .info_c {
      ::v-deep img {
        max-width: 100%;
        object-fit: cover;
      }
    }
  }
}
.jindu {
  position: fixed;
  bottom: 200rpx;
  margin-left: 50%;
  transform: translateX(-50%);
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  border: 2rxp solid #45A2FF;
  background: #45A2FF;
  color: #fff;
}
</style>