<template>
	<view>
		<block v-for="(item, index) in listsData" :key="index">
			<list-item :item-data="item" :info-list="infoList[index]" type="info" @click="toDetail"></list-item>
		</block>
	</view>
</template>

<script>
	import listItem from '../components/listItem.vue'
	import {navigateTo,config} from '../common/index.js'
	export default {
		props:{
			listsData:Array,
			infoList:Array
		},
		components:{
			listItem
		},
		data() {
			return {
				
			}
		},
		methods:{
			toDetail(e){
				// this.$store.state.tempData = e.detail
				if (e.detail.info_url!=""){  //资讯转过来的  返回一个字符串资讯id 跳转到资讯详情页 其他情况下返回空 跳转到条目详情
					navigateTo('/pages/news/detail?id='+e.detail.info_url)
				}else if(e.detail.id){
					 // #ifdef H5
                    navigateTo('/topic/info_detail?id='+e.detail.id)
                    // #endif 
                    // #ifdef MP
                    let url =encodeURIComponent(config.apiDomain+'/m/topic/info_detail?id='+e.detail.id) 
                    navigateTo('/pages/web_view/web_view?url='+url)
                    // #endif
					// navigateTo('/topic/info_detail?id='+e.detail.id)
				}
			}
		}
	}
</script>

<style lang="scss">
	.bottom-line::after{
		left: $uni-spacing-row-base;
		right: $uni-spacing-row-base;
	}
	.ding{
		font-size:$uni-font-size-sm;
		border-radius: 6upx;
		margin-right: 10upx;
		padding: 1upx 8upx;
		color: #fff;
		background-color: #f40
	}

</style>
