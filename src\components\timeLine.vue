<template>
<view class="time_line">
    <view class="item" :class="{current:item.is_finish!==0}" v-for="(item, index) in lineData" :key="index">
        <template v-if="custom">
            <slot :slotItem="item" :slotIndex="index"></slot>
        </template>
        <template v-else>
            <view class="title">{{item.content}}</view>
            <view class="time">{{item.time}}</view>
        </template>
    </view>
</view>
</template>

<script>
export default {
    props:{
        lineData:Array,
        custom:{
            type:[Boolean],
            default:false
        }
    },
    data() {
        return {}
    },
}
</script>

<style lang="scss" scope>
.time_line{
    padding: 20upx 30upx;
    .item{
        position: relative;
        padding: 0 20upx 36upx 32upx;
        border-left: 4upx solid #3399ff;
        .title{
            font-size: 28upx;
            margin-bottom: 15upx;
            line-height: 1.5;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            display: -webkit-box;
        }
        .time{
            font-size: 24upx;
            font-weight: bold;
            color: #999;
        }
    }
    .item::after{
        content: "";
        height:36upx;
        width: 36upx;
        box-sizing: border-box;
        border-radius: 50%;
        position: absolute;
        border: 4rpx solid #3399ff;
        background-color: #fff;
        left: -20upx;
        top: -8rpx;
    }
    .current::before{
        content: "";
        height:20upx;
        width: 20upx;
        border-radius: 50%;
        background-color: #3399ff;
        position: absolute;
        left: -12upx;
        top: 0;
        z-index: 2;
    }
}

</style>
