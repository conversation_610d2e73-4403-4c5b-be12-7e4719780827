<template>
<view class="page">
    <!-- #ifdef MP-WEIXIN || MP-BAIDU -->
    <view :class="bgwhite==true?'top flex-box bgwhite':'top flex-box'">
        <view class="c-left"></view>
        <view class="inp-box-def search-box flex-1">
            <uni-icons type="search" color="#666666" size="26"></uni-icons>
            <input type="text" v-model="params.keyword" confirm-type="search" @confirm="handleSearch" placeholder="找装修公司" />
        </view>
        <view class="c-right"></view>
    </view>
    <!-- #endif -->
    <my-swiper :focus="focus" :autoplay="true" :interval="4000" :indicatorDots="true" :circular="true" indicatorActiveColor="#f65354" height="44vw"></my-swiper>
    <!-- 新闻滚动条 -->
    <view class="news-box flex-box top-line">
        <view class="label">平台播报</view>
        <swiper class="flex-1" :duration="200" :circular="true" :autoplay="true" :vertical="true">
            <swiper-item v-for="news in newsList" :key="news.id">
                <navigator animation-type="pop-in" animation-duration="260" :url="'/home/<USER>/detail?id='+news.shopid+'&type=3'" hover-class="navigator-hover">
                    <view class="swiper-item">{{news.title}}</view>
                </navigator>
            </swiper-item>
        </swiper>
        <view class="label" @click="toUserCenter">我要入驻</view>
    </view>
    <view class="top-20">
        <tab-bar :tabs="typeList" :nowIndex="nowTabIndex" :fixedTop="false" ref="tab_bar" @click="switchTab"></tab-bar>
    </view>
    <view class="shop-list">
        <view class="list-item flex-box bottom-line" v-for="item in shopList" :key="item.id" @click="toShop(item.id)">
            <view class="img-box">
                <image :src="item.shoplogo | imgUrl" mode="aspectFill"></image>
                <view v-if="item.vip==2" class="icon">
                    <my-icon type="v" size="22" color="#ffd31a"></my-icon>
                </view>
            </view>
            <view class="info-box flex-1">
                <view class="title flex-box">
                    <view class="text flex-1">{{item.shopname}}</view>
                    <view class="guanzhu">{{item.hit||0}}关注</view>
                </view>
                <view class="labels flex-box">
                    <view class="lab-item">案例{{item.count1||0}}</view>
                    <view class="lab-item">工地{{item.count2||0}}</view>
                </view>
                <view class="promotion">{{item.promotion}}</view>
                <view class="address">
                    <my-icon type="chengshi" color="#C9A770" size="13"></my-icon>{{item.shopaddr}}
                </view>
            </view>
        </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
</view>
</template>

<script>
import mySwiper from '../../components/mySwiper'
import tabBar from '../../components/tabBar'
import myIcon from '../../components/icon'
import {
    navigateTo,
    formatImg
} from '../../common/index.js'
import {
    uniIcons,
    uniLoadMore
} from '@dcloudio/uni-ui'
import {wxShare} from '../../common/mixin'
export default {
    data() {
        return {
            bgwhite: false,
            focus: [],
            newsList: [],
            typeList: [],
            nowTabIndex:0,
            shopList: [],
            params:{},
            share:{},
            get_status: "loading",
            content_text: {
                contentdown: "",
                contentrefresh: "正在加载...",
                contentnomore: "没有更多数据了"
            }
        };
    },
    mixins:[wxShare],
    onLoad(options) {
        // console.log(options)
        this.params = {
            page: 1,
            rows: 20
        }
        if(options.type){
            this.params.shoptype = options.type
        }
        if(options.key){
            this.params.keyword = options.key
            console.log(this.params.keyword)
        }
        // for(let i = 0;i<this.typeList.length;i++){
        //     if(this.typeList[i].type == options.type){
        //         uni.setNavigationBarTitle({
        //             title:this.typeList[i].name
        //         })
        //     }
        // }
        this.getFocus()
        this.getData()
    },
    components: {
        mySwiper,
        tabBar,
        myIcon,
        uniIcons,
        uniLoadMore
    },
    onReady() {
        // let nowTabIndex = -1;
        // this.typeList.forEach((item, index) => {
        //     if (item.type == this.params.shoptype) {
        //         nowTabIndex = index
        //     }
        // })
        // this.nowTabIndex = nowTabIndex
    },
    filters: {
        imgUrl(val){
            return formatImg(val,'w_240')
        }
    },
    methods: {
        getData() {
            this.get_status = "loading"
            if (this.params.page == 1) {
                this.shopList = []
            }
            this.$ajax.get("memberShop/shopList.html", this.params, res => {
                if(res.data.shoptype&&res.data.shoptype.length>0){
                    this.$nextTick(()=>{
                        this.typeList = res.data.shoptype.map((item, index)=>{
                            if(item.id==this.params.shoptype){
                                uni.setNavigationBarTitle({
                                    title:item.name
                                })
                                this.nowTabIndex = index
                            }
                            return {name:item.name,type:item.id}
                        })
                    })
                }
                if(res.data.news&&res.data.news.length>0){
                    this.newsList = res.data.news
                }
                if (res.data.code == 1) {
                    this.shopList = this.shopList.concat(res.data.list)
                    if (res.data.list.length < this.params.rows) {
                        this.get_status = "noMore"
                    } else {
                        this.get_status = "more"
                    }
                } else {
                    this.get_status = "noMore"
                    this.params.page > 1 ? this.params.page-- : this.params.page = 1
                }
                if(res.data.share){
                    this.share = res.data.share
                    this.getWxConfig()
                }else{
                    this.share = {}
                }
                uni.stopPullDownRefresh();
            }, err => {
                console.log(err)
                uni.stopPullDownRefresh();
            });
        },
        getFocus(){
            this.$ajax.get('memberShop/focus',{},res=>{
                if(res.data.code == 1){
                    this.focus = res.data.focus
                }
            })
        },
        switchTab(e) {
            this.nowTabIndex = e.id
            this.params.page = 1
            this.params.shoptype = e.type
            uni.setNavigationBarTitle({
                title:e.name
            })
            this.getData()
        },
        toShop(id) {
            navigateTo('/home/<USER>/detail?id=' + id)
        },
        toUserCenter(){
            navigateTo('/home/<USER>/center')
        },
        handleSearch(e){
            this.params.page = 1
            this.params.keyword = e.detail.value||''
            this.getData()
        }
    },
    onNavigationBarSearchInputConfirmed(e) {
        this.params.page = 1
        this.params.keyword = e.text||''
        this.getData()
    },
    onPullDownRefresh() {
        this.params.page = 1
        this.getData()
    },
    onPageScroll(e) {
        if (e.scrollTop > 20) {
            this.bgwhite = true
        } else {
            this.bgwhite = false
        }
    },
    onReachBottom() {
        this.params.page++
        this.getData()
    },
	onShareAppMessage() {
		if (this.seo){
			return {
			  title: this.seo.title||"家装",
			  content:this.seo.description||"家装",
			  path:"/home/<USER>/list?type="+ this.params.shoptype,
			  imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):""
			}
		}else{
			return {
			  title: "家装",
			  content:"家装",
			  path:"/home/<USER>/list?type="+ this.params.shoptype,
			}
		}
		
	
}
	
};
</script>

<style lang="scss">
.top {
    width: 100%;
    padding: 15upx;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    z-index: 9;
    border-bottom: 1upx solid rgba(255, 255, 255, 0);
    transition: 0.3s;
    .uni-icon {
        height: 60upx;
        width: 60upx;
        line-height: 60upx;
        position: absolute;
        z-index: 2;
        margin-left: 10upx;
    }
}

.bgwhite {
    background-color: #fff;
    border-bottom: 1upx solid $uni-border-color;
    box-shadow: 0 0 26upx #dedede;
}

.search-box {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.7);
    border: 1upx solid rgba(255, 255, 255, 0.5);
    transition: 0.3s;

    input {
        height: 60upx;
        padding-left: 70upx;
        font-size: $uni-font-size-sm;
        border-radius: 6upx;
    }
}

.bgwhite .search-box {
    border: 1upx solid $uni-border-color;
}
.news-box {
    padding: 20upx;
    background-color: #ffffff;

    .label {
        height: 50upx;
        padding: 10upx;
        line-height: 50upx;
        font-size: $uni-font-size-blg;
        font-weight: bold;
        color: #00c07b;
        background-color: #ffffff;
    }

    swiper {
        height: 50upx;
        padding: 10upx;
        line-height: 50upx;
        background-color: #ffffff;

        view {
            font-size: $uni-font-size-base;
            color: #666
        }
    }

    .highlight {
        color: #00c07b;
        margin-right: 20upx;
    }
}

.shop-list {
    .list-item {
        padding: 30upx $uni-spacing-row-base;
        background-color: #ffffff;

        .img-box {
            height: 180upx;
            width: 180upx;
            min-width: 180upx;
            margin-right: 20upx;
            position: relative;

            image {
                width: 100%;
                height: 100%;
            }

            .icon {
                position: absolute;
                left: -10upx;
                top: -10upx;
            }
        }

        .info-box {
            overflow: hidden;

            .title {
                line-height: 1.5;
                margin-bottom: 10upx;
                color: #999;

                .text {
                    font-size: 30upx;
                    margin-right: 6upx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    color: #555;
                }

                .guanzhu {
                    font-size: 26upx;
                }
            }

            .labels {
                .lab-item {
                    padding: 2upx 10upx;
                    border: 1upx solid #89a0b4;
                    font-size: 26upx;
                    margin-right: 18upx;
                    color: #89a0b4
                }
            }

            .promotion {
                line-height: 1.8;
                font-size: 26upx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #f44;
            }

            .address {
                line-height: 1.8;
                font-size: 26upx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #C9A770
            }
        }
    }
}
</style>
