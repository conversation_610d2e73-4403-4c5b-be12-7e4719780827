<template>
<view class="page">
    <!-- #ifdef MP-WEIXIN || MP-BAIDU -->
    <view :class="bgwhite==true?'top flex-box bgwhite':'top flex-box'">
        <view class="c-left"></view>
        <view class="inp-box-def search-box flex-1">
            <uni-icon type="search" color="#666666" size="26"></uni-icon>
            <input type="text" v-model="keywords" confirm-type="search" @confirm="handleSearch()" placeholder="找装修公司" />
        </view>
        <view class="c-right"></view>
    </view>
    <!-- #endif -->
    <my-swiper :focus="focus" :autoplay="true" :interval="4000" :indicatorDots="true" :circular="true" indicatorActiveColor="#f65354" height="44vw"></my-swiper>
    <view class="big-row bottom-line">
        <my-icon type="yinliang" color="#f44" size="20"></my-icon>
        <text style="margin-left:10upx">浏览：</text>
        <text class="highlight">{{browse_num}}</text>
        <text>预约：</text>
        <text class="highlight">{{order_num}}</text>
    </view>
    <!-- 宫格 -->
    <swiper class="grid" :indicator-dots="gridDots" :current="gridCurrent" @change="changeGridSwiper" :duration="360" indicator-active-color="#f65354">
        <swiper-item v-for="(options,idx) in optionsList" :key="idx">
            <my-grid @click="toNav" :options="options" column-num="5" :fontSize="14" :show-border="false" className="round"></my-grid>
        </swiper-item>
    </swiper>
    <!-- 新闻滚动条 -->
    <view class="news-box flex-box top-line">
        <view class="label">平台播报</view>
        <swiper class="flex-1" :duration="200" :circular="true" :vertical="true" :autoplay="true">
            <swiper-item v-for="news in newsList" :key="news.id">
                <navigator animation-type="pop-in" animation-duration="260" :url="'/home/<USER>/detail?id='+news.shopid+'&type=3'" hover-class="navigator-hover">
                    <view class="swiper-item">{{news.title}}</view>
                </navigator>
            </swiper-item>
        </swiper>
    </view>
    <view class="entrance-box flex-box top-20 bottom-line">
        <view class="flex-1 right-line entrance-l">
            <image @click="toQuote" class="" src="https://images.tengfangyun.com/images/icon/pre_1542191885uc3ko.jpg" mode="aspectFill"></image>
        </view>
        <view class="flex-1 entrance-r">
            <navigator animation-type="pop-in" animation-duration="260" url="/home/<USER>/list?type=66" hover-class="navigator-hover">
                <view class="bottom-line small flex-box">
                    <view class="flex-1">
                        <view class="entrance-title">找工长/师傅</view>
                        <view>简装/维修/服务</view>
                    </view>
                    <view>
                        <image src="https://images.tengfangyun.com/images/icon/67.png"></image>
                    </view>
                </view>
            </navigator>
            <navigator animation-type="pop-in" animation-duration="260" url="/home/<USER>/index" hover-class="navigator-hover">
                <view class="small flex-box">
                    <view class="flex-1">
                        <view class="entrance-title">免费设计</view>
                        <view>百变户型 多重方案</view>
                    </view>
                    <view class="icon-box">
                        <image src="https://images.tengfangyun.com/images/icon/62.png"></image>
                    </view>
                </view>
            </navigator>
        </view>
    </view>
    <my-swiper v-if="advList.length>0" :focus="advList" :indicatorDots="true" :autoplay="true" :interval="4500" :circular="true" indicatorActiveColor="#f65354" height="18vw"></my-swiper>
    <view class="plate-box top-20">
        <view class="plate-title">
            <text>品牌推荐</text>
            <view class="c-right more" @click="More()">更多<uni-icons type="arrowright" size="16"></uni-icons>
            </view>
        </view>
        <view class="brand-list">
            <view class="brand-item" v-for="brand in brandList" :key="brand.id" @click="toShop(brand.id)">
                <image :src="brand.commendlogo | imgUrl" mode="aspectFill"></image>
            </view>
        </view>
    </view>
    <view class="top-20">
        <tab-bar :tabs="tabs" :nowIndex="nowTabIndex" :fixedTop="false" ref="tab_bar" @click="switchTab"></tab-bar>
    </view>
    <view class="data-list">
        <home-list-item v-for="item in dataList" :key="item.id" from="index" :item="item"></home-list-item>
    </view>
</view>
</template>

<script>
import {
    uniIcons
} from '@dcloudio/uni-ui'
import mySwiper from '../../components/mySwiper'
import myGrid from '../../components/myGrid'
import myIcon from '../../components/icon'
import {
    navigateTo,
    formatImg
} from '../../common/index.js'
import homeListItem from '../../components/homeListItem'
import tabBar from '../../components/tabBar'
import {wxShare} from '../../common/mixin'
export default {
    data() {
        return {
            bgwhite: false,
            focus: [],
            browse_num: "",
            order_num: "",
            advList: [],
            gridCurrent: 0,
            gridDots:false,
            optionsList: [],
            newsList: [],
            brandList: [],
            dataList: [],
			share:{},
            tabs: [{
                name: "效果图",
                type: 1
            }, {
                name: "看工地",
                type: 2
            }, {
                name: "优惠活动",
                type: 3
            }],
            nowTabIndex:0,
            keywords:""
        }
    },
    mixins:[wxShare],
    components: {
        uniIcons,
        mySwiper,
        myGrid,
        homeListItem,
        myIcon,
        tabBar
    },
    onLoad() {
        this.getData()
    },
    filters: {
        imgUrl(img) {
            return formatImg(img)
        }
    },
    methods: {
        getData() {
            this.$ajax.get('memberShop/index', {}, res => {
                if (res.data.code == 1) {
                    uni.setNavigationBarTitle({
                        title:res.data.indexname
                    })
                    this.focus = res.data.focusTop
                    this.browse_num = res.data.clickCount
                    this.order_num = res.data.orderCount
                    this.advList = res.data.focusCentral
                    this.newsList = res.data.news
                    this.brandList = res.data.commendBrand
                    this.dataList = res.data.caselist
                    let optionsList = res.data.nav.map((item,index)=>{
						return {
							text:item.name,
							image:formatImg(item.icon, 'w_240'),
							url:item.url
						}
					})
					let len = optionsList.length
					let tempOptionsList = []
					for(let i=0;i<len; i+=10){
						tempOptionsList.push(optionsList.splice(0,10))
					}
					this.optionsList = tempOptionsList
					if(this.optionsList.length>1){
						this.gridDots = true
					}
                    if(res.data.share){
						this.share = res.data.share
					}else{
						this.share = {}
					}
					this.getWxConfig()
                }
            })
        },
        changeGridSwiper(e) {
            this.gridCurrent = e.detail.current
        },
        toNav(e) {
            navigateTo(this.optionsList[this.gridCurrent][e.index].url)
        },
        toShop(id) {
            navigateTo('/home/<USER>/detail?id=' + id)
        },
        switchTab(e) {
            navigateTo('/home/<USER>/list?type=' + e.type)
        },
        toQuote() {
            navigateTo('/home/<USER>/index')
        },
        More(){
            navigateTo('/home/<USER>/list?type=')
        },
        handleSearch(key=this.keywords){
            console.log(key)
            navigateTo('/home/<USER>/list?key='+key+'&type=62')
        }
    },
    onPullDownRefresh() {
        this.getData()
    },
    onPageScroll(e) {
        if (e.scrollTop > 20) {
            this.bgwhite = true
        } else {
            this.bgwhite = false
        }
    },
    onNavigationBarSearchInputConfirmed(e) {
        this.handleSearch(e.text)
    },
	onShareAppMessage() {
		if (this.share){
			return {
			  title: this.share.title||"家装",
			  content:this.share.content||"家装",
			  path:"/home/<USER>/index",
			  imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):""

			}
		}else{
			return {
			  title: "家装",
			  content:"家装",
			  path:"/home/<USER>/index",
			}
		}
		
	}
}
</script>

<style lang="scss">
.top {
    width: 100%;
    padding: 15upx;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    z-index: 9;
    border-bottom: 1upx solid rgba(255, 255, 255, 0);
    transition: 0.3s;
    .uni-icon {
        height: 60upx;
        width: 60upx;
        line-height: 60upx;
        position: absolute;
        z-index: 2;
        margin-left: 10upx;
    }
}

.bgwhite {
    background-color: #fff;
    border-bottom: 1upx solid $uni-border-color;
    box-shadow: 0 0 26upx #dedede;
}

.search-box {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.7);
    border: 1upx solid rgba(255, 255, 255, 0.5);
    transition: 0.3s;

    input {
        height: 60upx;
        padding-left: 70upx;
        font-size: $uni-font-size-sm;
        border-radius: 6upx;
    }
}

.bgwhite .search-box {
    border: 1upx solid $uni-border-color;
}

.big-row {
    padding: 24upx 32upx;
    font-size: 30upx;
    background-color: #fff;
}

.uni-grid-item-text {
    font-size: $uni-font-size-base;
}

swiper.adv-list {
    height: 18vw;
}

swiper.grid {
    height:48vw;
    padding: 24upx 0;
    background-color: #ffffff;
}

.news-box {
    padding: 20upx;
    background-color: #ffffff;
}

.news-box .label {
    height: 50upx;
    padding: 10upx;
    line-height: 50upx;
    font-size: $uni-font-size-blg;
    font-weight: bold;
    color: #00c07b;
    background-color: #ffffff;
}

.highlight {
    color: #00c07b;
    margin-right: 20upx;
}

.news-box swiper {
    height: 50upx;
    padding: 10upx;
    line-height: 50upx;
    background-color: #ffffff;
}

.news-box swiper view {
    font-size: $uni-font-size-base;
    color: #666
}

.entrance-box {
    width: 100%;
    height: 32vw;
    background-color: #fff;

    .small {
        height: 16vw;
        padding: 0 20upx;
        box-sizing: border-box;
        align-items: center;
        font-size: 24upx;
        color: #999
    }

    .entrance-l {
        width: 50%;

        image {
            width: 100%;
            height: 100%;
        }
    }

    .entrance-r {
        width: 50%;

        .entrance-title {
            margin-bottom: 6upx;
            font-size: 32upx;
            color: #666;
        }

        image {
            width: 13vw;
            height: 13vw;
        }
    }

    .icon-box {
        font-size: 0
    }
}

.plate-box {
    padding: 0 15upx 0 15upx;
    background-color: #ffffff;
}

.plate-title {
    padding: $uni-spacing-row-base;
}

.plate-title text {
    font-weight: bold;
    color: #333;
    font-size: $uni-font-size-blg;
}

.brand-list {
    display: flex;
    flex-wrap: nowrap;
    height: 100upx;
    width: 100%;
    padding-bottom: 20upx;
    overflow-y: hidden;

    .brand-item {
        height: 100%;
        width: 160upx;
        box-sizing: border-box;
        border: 1upx solid #f7f7f7;
        min-width: 160upx;
        margin-right: 12upx;
        padding: 10upx;

        image {
            width: 100%;
            height: 100%;
        }
    }
}

.handle-bar {
    padding: 10upx 0;
    line-height: 50upx;
    position: absolute;
    top: 0;
    display: flex;
    right: 44upx;
    width: 0;
    transition: 0.3s;
    border-radius: 8upx;
    background-color: #4d5154;

    .bar-item {
        flex: 1;
        min-width: 33.333%;
        overflow: hidden;
        // text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
        color: #fff;
        transform: 0.3s;
    }
}

.handle-bar.show {
    width: 480upx;
}
</style>
