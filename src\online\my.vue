<template>
<view class="page">
    <view class="user-info flex-box" v-if="user_info.is_adviser===0">
        <image class="user-img" :src="user_info.prelogo | imgUrl" mode="aspectFill"></image>
        <view class="info">
            <template v-if="user_info.cname || user_info.tel">
                <view class="user-id">{{user_info.cname || ''}}</view>
                <view class="user-id">{{user_info.tel || ''}}</view>
            </template>
            <view class="user-id" v-else @click="toLogin">去登录</view>
        </view>
    </view>
    <view class="header-block"  v-if="user_info.is_adviser===1">
        <view class="online_name" v-if="online_name">{{online_name}}</view>
        <view class="adviser-info flex-box">
            <image class="user-img" :src="user_info.prelogo | imgUrl" mode="aspectFill"></image>
            <view class="info">
                <view class="user-id" v-if="user_info.cname || user_info.tel">{{user_info.cname || user_info.tel}}</view>
                <view class="user-id" v-else @click="toLogin">去登录</view>
                <view class="user-shenfen">
                    <!-- <my-icon type="huiyuan" color="#ffffff" size="18"></my-icon> -->
                    <view class="shenfen-text">{{user_info.level_name||user_info.tel}}</view>
                    <uni-icons type="arrowright" color="#ffffff"></uni-icons>
                </view>
            </view>
        </view>
        <view class="user-data">
            <view v-for="nav in top_navs" :key="nav.id" class="data-item" hover-class="navigator-hover" @click="navigate(`${nav.url}?online_id=${online_id}`)">
                <view class="value">{{nav.number}}</view>
                <view class="title">{{nav.title}}</view>
            </view>
        </view>
    </view>
    <view class="lists">
        <uni-list-item :thumb="nav.icon?(nav.icon+'?x-oss-process=style/w_80'):''" v-for="(nav,index) in nav_list" :key="index" :title="nav.title" @click="navigate(`${nav.url}?online_id=${online_id}`)"></uni-list-item>
    </view>
    <!-- #ifndef H5 -->
    <!-- <view class="btn-box">
        <button class="default" @click="scanCode()">扫码</button>
    </view> -->
    <!-- #endif -->
    <view class="bottom-menu flex-box">
        <view class="bar-item" @click="navigate('/online/detail?id='+online_id)">
            <my-icon type="home" size="22"></my-icon>
            <view class="text">大厅</view>
        </view>
        <view class="bar-item" @click="navigate('/online/choose?online_id='+online_id)">
            <my-icon type="jiudian" size="22"></my-icon>
            <view class="text">选房</view>
        </view>
        <view class="bar-item" @click="navigate('/online/adviser?online_id='+online_id)">
            <my-icon type="xiaoxi" size="22"></my-icon>
            <view class="text">咨询</view>
        </view>
        <view class="bar-item active">
            <my-icon type="shikebiao" size="22" color="#f65354"></my-icon>
            <view class="text">订单</view>
        </view>
    </view>
</view>
</template>

<script>
import myIcon from "../components/icon.vue"
import {
    uniIcons,
    uniListItem,
} from "@dcloudio/uni-ui"
import {navigateTo,formatImg} from '../common/index.js'
export default {
    data() {
        return {
            online_id:"",
            user_info:{},
            top_navs:[],
            nav_list:[],
            info:{},
            online_name:""
        }
    },
    components: {
        myIcon,
        uniIcons,
        uniListItem,
    },
    onLoad(options){
        if (options.online_id){
            this.online_id = options.online_id
        }
        this.getData()
        uni.$on("getDataAgain",this.getData)
    },
    onUnload(){
        uni.$off("getDataAgain",this.getData)
    },
    filters:{
        imgUrl(val){
            return formatImg(val,'w_80')
        }
    },
    methods:{
        getData(){
            this.$ajax.get('onlineMy/index', {online_id:this.online_id}, (res) => {	
                this.online_name = res.data.online_name
                if (res.data.code == 1) {
                    this.user_info = res.data.user
                    this.top_navs = res.data.topNav
                    this.nav_list = res.data.nav
                    this.info = res.data.info
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            }, (err) => {
            })
        },
        // #ifndef H5
        scanCode(){
            uni.scanCode({
                success: res=> {
                    console.log('条码类型：' + res.scanType);
                    console.log('条码内容：' + res.result);
                }
            });
        },
        // #endif
        toLogin(){
            navigateTo("/user/login/login")
        },
        navigate(url){
            navigateTo(url)
        }
    }
}
</script>

<style scoped lang="scss">
.user-info {
    width: 100%;
    padding: 50upx;
    padding-top: 100rpx;
    box-sizing: border-box;
    align-items: center;
    height: 32vw;
    background-color: #ff0066;
    color: #fff;
    .user-img {
        width: 120upx;
        height: 120upx;
        margin-right: 24upx;
        border-radius: 50%;
    }

    .user-id {
        font-size: 34upx;
        padding: 5upx 0;
        margin-bottom: 5upx;
    }
}




.header-block {
    width: 100%;
    padding: 50upx;
    padding-top: 100rpx;
    box-sizing: border-box;
    height: 52vw;
    background-color: #186ec5;
    color: #fff;
    position: relative;
}

.adviser-info {
    align-items: center;

    .user-img {
        width: 120upx;
        height: 120upx;
        margin-right: 24upx;
        border-radius: 50%;
    }

    .user-id {
        font-size: 36upx;
        padding: 10upx 0;
        margin-bottom: 10upx;
    }

    .user-shenfen {
        color: #fff;
        padding: 8upx 20upx;
        // height: 46upx;
        // line-height: 46upx;
        display: flex;
        align-items: center;
        border-radius: 18upx;
        background-color: rgba($color: #000000, $alpha: 0.4);

        .shenfen-text {
            font-size: $uni-font-size-sm;
            display: inline-block;
            // width: 260upx;
            padding: 0 20upx;
        }
    }
}


.user-data {
    // margin-left: 80upx;
    margin-top: 28upx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .data-item {
        padding: 10upx 25upx;  
        display: inline-block;
        text-align: center;
        &.navigator-hover{
            border-radius: 6rpx;
            background-color: rgba($color: #000000, $alpha: 0.3);
        }

        .value {
            font-size: $uni-font-size-blg;
            font-weight: bold;
        }

        .title {
            font-size: 22upx;
        }
    }
}

.lists{
    margin-top:20rpx;
    background-color: #fff;
}

.bottom-menu{
    position: fixed;
    width: 100%;
    padding: 8rpx 0;
    box-sizing: border-box;
    bottom: 0;
    background-color: #fff;
    border-top: 1rpx solid #dedede;
    .bar-item{
        line-height: 1;
        flex: 1;
        text-align: center;
        color: #333;
        .text{
            margin-top: 8rpx;
        }
        &.active{
            color:  $uni-color-primary;
        }
    }
}

.online_name{
    position: absolute;
    height: 44px;
    line-height: 44px;
    left: 90rpx;
    right: 50rpx;
    text-align: center;
    top:0;
    font-size: 32rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
