<template>
    <view>
        <view class="top">
            <image :src="'/ditu/img/fxbg.png' | imageFilter('w_6401')" mode="widthFix" />
        </view>
        <view class="info border">
            <view class="flex-row">
                <image class="img" :src="build.img | imageFilter('w_320')" mode="aspectFill" />
                <view style="flex:1">
                    <view class="flex-row tit">
                        <text class="name">{{build.title}}</text>
                        <view class="tag" :style="{background:build.status_color}">{{build.status_name}}</view>
                    </view>
                    <view class="area">{{build.areaname}}</view>
                    <view class="price">{{build.price_title}}{{build.price_value}}{{build. price_unit}}</view>
                </view>
            </view>
            <view class="options1 flex-row">
            <view class="item" v-for="(item, index) in options_list" :key="index" @click="toOptionPath(item)">
                <image class="icon" :src="item.icon | imageFilter('w_320')"></image>
                <text class="text">{{item.text}}</text>
            </view>
            </view>
        </view>
        <view class="xq border">
            <view class="title">小区概况</view>
            <view class="flex-row list1">
                <view>
                    <text>开盘</text>
                    <view>{{build.kptime || '暂无'}}</view>
                </view>
                <view>
                    <text>建面</text>
                    <view>{{build.jm + 'm²' || '暂无'}}</view>
                </view>
                <view>
                    <text>产权年限</text>
                    <view>{{build.cqnx || '暂无'}}</view>
                </view>
                <view>
                    <text>物业类别</text>
                    <view class="flex-row"><text v-for="(item,index) in build.build_types" :key="index">{{item || '暂无'}}、</text></view>
                </view>
            </view>
            <view class="more flex-row" @click="toDetail">
                <text>点击查看更多楼盘基础信息</text>   
                <my-icon type="ic_into" size="30rpx" color="#2d84fb"></my-icon>
            </view>
        </view>
        <view class="fx border">
            <view class="title">房价分析 <text class="unit">（元/m²）</text></view>
            <view class="mini_chart">
                <view class="mi-charts" :style="{height:cHeight12+'px',width:cWidth1+'px'}" >
                <!--#ifdef H5-->
                <canvas
                    canvasId="canvasLineTop"
                    class="charts_top"
                    :style="{
                    position: 'relative',
                    width: cWidth1 * pixelRatio + 'px',
                    height: cHeight1 * pixelRatio + 'px',
                    transform: 'scale(' + 1 / pixelRatio + ')',
                    'margin-left': (-cWidth1 * (pixelRatio - 1)) / 2 + 'px',
                    top: (-cHeight1 * (pixelRatio - 1)) / 2 + 'px',
                    }"
                    
                ></canvas>
                <!--#endif-->
                <!--#ifndef H5-->
                <canvas canvasId="canvasLineTop" class="charts_top" ></canvas>
                <!--#endif-->
                </view>
            </view>
            <template v-if="fangjiaAnalyse.introduce != ''">
                <view class="info" v-for="(item,index) in fangjiaAnalyse.introduce" :key="index">{{item}}</view>
            </template>
            <view class="more flex-row" @click="toPath(1)">
                <text>点击查看更多详细房价分析</text> 
                <my-icon type="ic_into" size="30rpx" color="#2d84fb"></my-icon>
            </view>
        </view>
        <view class="fx border">
            <view class="title">存量分析 <text class="unit">（套）</text></view>
            <view class="mini_chart">
                <view class="mi-charts" :style="{height:cHeight12+'px',width:cWidth1+'px'}" >
                <!--#ifdef H5-->
                <canvas
                    canvasId="canvasColumn"
                    class="charts_top"
                    :style="{
                    position: 'relative',
                    width: cWidth1 * pixelRatio + 'px',
                    height: cHeight1 * pixelRatio + 'px',
                    transform: 'scale(' + 1 / pixelRatio + ')',
                    'margin-left': (-cWidth1 * (pixelRatio - 1)) / 2 + 'px',
                    top: (-cHeight1 * (pixelRatio - 1)) / 2 + 'px',
                    }"
                ></canvas>
                <!--#endif-->
                <!--#ifndef H5-->
                <canvas canvasId="canvasColumn" class="charts_top" ></canvas>
                <!--#endif-->
                </view>
            </view>
            <template v-if="stockAnalyse.introduce != ''">
                <view class="info" v-for="(item,index) in stockAnalyse.introduce" :key="index">{{item}}</view>
            </template>
            <view class="more flex-row" @click="toPath(2)">
                <text>点击查看更多详细存量分析</text> 
                <my-icon type="ic_into" size="30rpx" color="#2d84fb"></my-icon>
            </view>
        </view>
        <view class="bk fx border">
            <view class="title flex-row">
                <text>板块价值分析 
                    <text v-if="plateId == 1" class="unit">（万元/亩）</text>
                    <text v-else class="unit">（元/m²）</text>
                </text>
                <view class="tab">
                    <text :class="{on:plateId == 1}" @click="plate(1)">楼面价</text>
                    <text :class="{on:plateId == 2}" @click="plate(2)">地价</text>
                </view>
            </view>
            <view class="mini_chart">
                <view class="mi-charts" :style="{height:cHeight12+'px',width:cWidth1+'px'}" >
                <!--#ifdef H5-->
                <canvas
                    canvasId="canvasLine"
                    class="charts_top"
                    :style="{
                    position: 'relative',
                    width: cWidth1 * pixelRatio + 'px',
                    height: cHeight1 * pixelRatio + 'px',
                    transform: 'scale(' + 1 / pixelRatio + ')',
                    'margin-left': (-cWidth1 * (pixelRatio - 1)) / 2 + 'px',
                    top: (-cHeight1 * (pixelRatio - 1)) / 2 + 'px',
                    }"
                    
                ></canvas>
                <!--#endif-->
                <!--#ifndef H5-->
                <canvas canvasId="canvasLine" class="charts_top" ></canvas>
                <!--#endif-->
                </view>
            </view>
            <template v-if="plateAnalyse.introduce != ''">
                <template v-if="plateId == 1">
                    <view class="info" v-for="(item,index) in plateAnalyse.introduce" :key="index">{{item}}</view>
                </template>
                <template v-if="plateId == 2">
                    <view class="info" v-for="(item,index) in plateAnalyse.introduce2" :key="index">{{item}}</view>
                </template>
            </template>
            <view class="more flex-row" @click="toPath(3)">
                <text>点击查看更多详细板块分析</text> 
                <my-icon type="ic_into" size="30rpx" color="#2d84fb"></my-icon>
            </view>
        </view>
        <view class="fx border">
            <view class="title">教育资源 <text class="unit">（个）</text></view>
            <view class="mini_chart">
                <view class="mi-charts" :style="{height:cHeight12+'px',width:cWidth1+'px'}" >
                <!--#ifdef H5-->
                <canvas
                    canvasId="canvasColumn2"
                    class="charts_top"
                    :style="{
                    position: 'relative',
                    width: cWidth1 * pixelRatio + 'px',
                    height: cHeight1 * pixelRatio + 'px',
                    transform: 'scale(' + 1 / pixelRatio + ')',
                    'margin-left': (-cWidth1 * (pixelRatio - 1)) / 2 + 'px',
                    top: (-cHeight1 * (pixelRatio - 1)) / 2 + 'px',
                    }"
                    
                ></canvas>
                <!--#endif-->
                <!--#ifndef H5-->
                <canvas canvasId="canvasColumn2" class="charts_top" ></canvas>
                <!--#endif-->
                </view>
            </view>
            <template v-if="eduAnalyse.introduce != ''">
                <view class="info" v-for="(item,index) in eduAnalyse.introduce" :key="index">{{item}}</view>
            </template>
            <view class="more flex-row" @click="toPath(4)">
                <text>点击查看更多详细教育资源</text> 
                <my-icon type="ic_into" size="30rpx" color="#2d84fb"></my-icon>
            </view>
        </view>
        <view class="jt fx border">
            <view class="title">交通出行</view>
            <!-- 两个交通工具 -->
            <view class="bus" v-if="traffic&&traffic.items&&traffic.items[1]"> 
                <image class="img1" :src="'/ditu/img/o3.png' | imageFilter('w_6401')" mode="widthFix" />
                <view class="tag" :class="'tag' + (index+2)" v-for="(item,index) in traffic.items" :key="index">
                    <image v-if="item.title == '公交站'" :src="'/ditu/img/gj.png' | imageFilter('w_320')" mode="widthFix" />
                    <image v-else :src="'/ditu/img/jt.png' | imageFilter('w_320')" mode="widthFix" />
                    <view>{{item.count}}个{{item.title}}</view>
                </view>
            </view>
            <view class="bus" v-else>
                <image class="img1" :src="'/ditu/img/o1.png' | imageFilter('w_6401')" mode="widthFix" />
                <view class="tag tag1" v-for="(item,index) in traffic.items" :key="index">
                    <image v-if="item.title == '公交站'" :src="'/ditu/img/gj.png' | imageFilter('w_320')" mode="widthFix" />
                    <image v-else :src="'/ditu/img/jt.png' | imageFilter('w_320')" mode="widthFix" />
                    <view>{{item.count}}个{{item.title}}</view>
                </view>
            </view>
            <view class="info" v-for="(item,index) in traffic.introduce" :key="index">{{item}}</view>
            <view class="more flex-row" @click="toPath(5)">
                <text>点击查看更多详细交通配置</text> 
                <my-icon type="ic_into" size="30rpx" color="#2d84fb"></my-icon>
            </view>
        </view>
        <view class="jt fx border">
            <view class="title">生活配套</view>
            <view class="bus">
                <image class="img1" :src="'/ditu/img/o3.png' | imageFilter('w_6401')" mode="widthFix" />
                <view class="tag" :class="'tag'+ (index+2)" v-for="(item,index) in lifeSupport.items" :key="index">
                    <image v-if="item.title == '医院'" :src="'/ditu/img/yy.png' | imageFilter('w_320')" mode="widthFix" />
                    <image v-else :src="'/ditu/img/gw.png' | imageFilter('w_320')" mode="widthFix" />
                    <view>{{item.count}}个{{item.title}}</view>
                </view>
            </view>
            <view class="info" v-for="(item,index) in lifeSupport.introduce" :key="index">{{item}}</view>
            <view class="more flex-row" @click="toPath(5)">
                <text>点击查看更多详细生活配套</text> 
                <my-icon type="ic_into" size="30rpx" color="#2d84fb"></my-icon>
            </view>
        </view>
        <view class="jt fx border">
            <view class="title">生态环境</view>
            <view class="bus">
                <image class="img1" :src="'/ditu/img/o2.png' | imageFilter('w_6401')" mode="widthFix" />
                <view class="tag tag1" v-for="(item,index) in environment.items" :key="index">
                    <image :src="'/ditu/img/gy.png' | imageFilter('w_320')" mode="widthFix" />
                    <view>{{item.count}}个{{item.title}}</view>
                </view>
            </view>
            <view class="info" v-for="(item,index) in environment.introduce" :key="index">{{item}}</view>
            <view class="more flex-row" @click="toPath(5)">
                <text>点击查看更多详细生态环境</text> 
                <my-icon type="ic_into" size="30rpx" color="#2d84fb"></my-icon>
            </view>
        </view>
        <view class="adviser fx border" v-if="mountMembers != ''">
            <view class="title">优秀{{mountTitle}}</view>
            <view class="list">
                <view class="info flex-row" v-for="(item,index) in mountMembers" :key="index">
                    <view class="title1 flex-row">
                        <image class="pic" :src="item.prelogo | imageFilter('w_320')" mode="aspectFill" />
                        <text class="name">{{item.cname}}</text>
                    </view>
                    <view class="contact flex-row">
                        <tel-btn :user_id="item.mid||item.uid||item.id" :identity_id="item.adviser_id||item.uid||item.id" :tel="item.tel" @ok="handleTel($event, item)">
                            <view class="flex-row">
                                <image :src="'/ditu/img/dh.png' | imageFilter('w_320')" mode="widthFix" />
                                <text>电话</text>
                            </view>
                        </tel-btn>
                        <view class="flex-row" @click="handleChat(item.uid)">
                            <image :src="'/ditu/img/xx.png' | imageFilter('w_320')" mode="widthFix" />
                            <text>咨询</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
        <my-popup ref="test_submit" position="center" :height="'620rpx'"  :touch_hide='false'>
            <view class="sub_box" @touchmove.stop.prevent="stopMove">
                <view class="sub_header">
                    <view class="sub_title">专属置业报告</view>
                    <!-- <view class="sub_tip">已有11人领取</view> -->
                    <view class="icon">
                        <image  mode="widthFix"  :src="'/images/new_icon/baoming_tg.png' | imageFilter('w_320')"></image>
                    </view>
                </view>
                <view class="form_box">
                    <view class="sub_content">为方便您接收置业报告信息,请输入手机号</view>
                    <view class="sub_form">
                         <input v-if ="sub_mode==0 || sub_mode ==2" class="sub_tel" ype="text" placeholder="请输入称呼" v-model="submit_name"/>
                        <input class="sub_tel" ype="text" placeholder="请输入手机号" v-model="submit_tel"/>
                        <view class="btn-box">
                            <button class="default" @click ="submit_test_con">立即领取</button>
                        </view>
                    </view>
                </view>
            </view>
        </my-popup>
    </view>
</template>

<script>
import myIcon from "@/components/myIcon.vue";
import getChatInfo from '@/common/get_chat_info';
import myPopup from '@/components/myPopup.vue';
import allTel from '@/common/all_tel.js'
import telBtn from '@/components/open-button/telBtn';
import checkLogin from '@/common/utils/check_login'
// #ifndef MP
import uCharts from "@/components/u-charts/u-charts.js";
// #endif
// #ifdef MP
import uCharts from "@/components/wx-charts/wxcharts.js";
// #endif
export default {
    components: {
        myIcon,
        getChatInfo,
        myPopup,
        telBtn,
    },
    data(){
        return{
            // #ifdef H5
            cWidth: "",
            cHeight: "",
            cWidth1: "",
            cHeight1: "",
            cHeight12:"",
            // #endif
            pixelRatio: 1,
            id:'',
            options_list: [
                {
                icon: '/ditu/fangchanfenxi.png',
                text: '房价分析',
                path: '/propertyData/map/analysis?id={build_id}&type=1&lat={lat}&lng={lng}&from=1'
                },
                {
                icon: '/ditu/bankuaijiazhi.png',
                text: '版块价值',
                path: '/propertyData/map/analysis?id={build_id}&type=1&lat={lat}&lng={lng}&from=2'
                },
                {
                icon: '/ditu/cunliangfenxi.png',
                text: '存量分析',
                path: '/propertyData/map/analysis?id={build_id}&type=1&lat={lat}&lng={lng}&from=3',
                
                },
                {
                icon: '/ditu/jiaoyufenxi.png',
                text: '教育分析',
                // path: '/online/loudong?build_id={build_id}&sand_id={sand_id}',
                path: '/propertyData/map/analysis?id={build_id}&type=1&lat={lat}&lng={lng}&from=4'
                },
                {
                icon: '/ditu/jiaoyupeitao.png',
                text: '配套分析',
                path: '/propertyData/map/map?id={build_id}&type=1&lat={lat}&lng={lng}'
                },
            ],
            build:{
                jm:''
            },
            fangjiaAnalyse:[], //房价分析
            stockAnalyse:[], //存量分析
            plateAnalyse:[], //板块价值分析
            eduAnalyse:[], //教育资源
            environment:[], //公园
            lifeSupport:[] , //生活配套
            traffic:[], //公交站
            mountMembers:[], //置业顾问 
            opt:{
                canvasLine:null,
                canvasLineTop:null,
                canvasColumn:null,
                canvasColumn2:null
            },
            plateId:1,
            isSignUp:"",
            submit_tel:"",
            for:0,
            submit_name:'',
            mountTitle:'',
            tel_res: {},
            show_tel_pop:false,
        }
    },
    computed: {
        is_open_im() {
            return this.$store.state.im.ischat
        },
        sub_mode() {
            return this.$store.state.sub_form_mode
        },
    },
    onLoad(options) {
        //#ifdef H5
        uni.getSystemInfo({
        success: (res) => {
            if (res.pixelRatio > 1) {
            //正常这里给2就行，如果pixelRatio=3性能会降低一点
            //_self.pixelRatio =res.pixelRatio;
            this.pixelRatio = 2;
            }
        },
        });
        //#endif
        this.cWidth = uni.upx2px(650);
        this.cHeight = uni.upx2px(450);
        this.cWidth1 = uni.upx2px(650);
        this.cHeight1 = uni.upx2px(450);
        this.cHeight12 = uni.upx2px(400);
        if (options.id) {
            this.id = options.id
        }
		this.loginState()
        // this.getLineData()
        // this.analyse()
        uni.$on('getDataAgain', () => {
            this.getLineData()
            this.analyse()
        })
    },
    onUnload() {
        uni.$off('getDataAgain', () => {})
    },
    methods:{
        loginState() {
			checkLogin({
				success: (res) => {
						this.getLineData()
                        this.analyse()
				},
				fail: (res) => {
                    console.log(res);
					if (this.$store.state.user_login_status == 1) {
                        if (this.toLogin == false) return
					    this.toLogin = false
						this.$navigateTo('/user/login/login')
					}else if (this.$store.state.user_login_status == 2){
        				this.$navigateTo('/user/bind_phone/bind_phone')
					}
					else {
						this.getLineData()
                        this.analyse()
					}
				},
				complete: (res) => {
					this.$store.state.user_login_status = res.status
				},
			})
		},
        getLineData() {
            this.$ajax.get("build/buildWorthReport", { id: this.id }, (res) => {
                if (res.data.code == 1) {
                    this.isSignUp = res.data.isSignUp
                    if (this.isSignUp == 0) {
                        this.$refs.test_submit.show()
                    }
                     if (res.data.build) {
                        this.build = res.data.build
                    }
                    // 置业顾问
                    if (res.data.mountMembers) {
                        this.mountMembers = res.data.mountMembers
                        this.mountTitle = res.data.mountTitle
                    }
                    // 房价
                    if (res.data.fangjiaAnalyse) {
                        this.fangjiaAnalyse = res.data.fangjiaAnalyse
                        let lineData = {},categories =[],data =[]
                        res.data.fangjiaAnalyse.statistics.map(item=>{
                            categories.push(item.distance +'KM')
                            data.push(item.coummunityPrice)
                        })
                        lineData.categories = categories;
                        lineData.series = [
                            {
                                data:data,
                                name:'',
                                textSize:28,
                                textColor:"#333333",
                                textOffset: 2,
                            }
                        ]
                        this.showLine("canvasLineTop", lineData,{ 
                            dataPointShapeType:"hollow",
                            type:"line", //柱状图为column
                            axisLine:false,
                            disableGridY:true,
                            disableGridX:false,
                            disabledY:true,
                            legend:false,
                            width:this.cWidth1,
                            height:this.cHeight1,
                            showArrow:false,
                            showBox:false,
                            splitLine:false,
                        });
                    }                           
                    // 存量分析
                    if (res.data.stockAnalyse) {
                        this.stockAnalyse = res.data.stockAnalyse
                        let lineData = {},categories =[],data =[]
                        res.data.stockAnalyse.statistics.map(item=>{
                            categories.push(item.distance +'KM')
                            data.push(item.housesCount)
                        })
                        lineData.categories = categories;
                        lineData.series = [
                            {
                                data:data,
                                name:'',
                                textSize:28,
                                textColor:"#333333",
                                textOffset: -2,
                            }
                        ]
                        this.showLine("canvasColumn", lineData,{ 
                            type:"column", //柱状图为column
                            axisLine:false,
                            disableGridY:true,
                            disableGridX:true,
                            disabledY:true,
                            legend:false,
                            width:this.cWidth1,
                            height:this.cHeight1,
                            showArrow:false,
                            showBox:false,
                            splitLine:false,
                        });
                    }   
                    // 教育资源
                    if (res.data.eduAnalyse) {
                        this.eduAnalyse = res.data.eduAnalyse
                        let lineData = {},categories =[],data =[]
                        res.data.eduAnalyse.statistics.map(item=>{
                            categories.push(item.schoolCateTitle)
                            data.push(item.schoolCount)
                        })
                        lineData.categories = categories;
                        lineData.series = [
                            {
                                data:data,
                                name:'',
                                textSize:28,
                                textColor:"#333333",
                                textOffset: -2,
                            }
                        ]
                        this.showLine("canvasColumn2", lineData,{ 
                            type:"column", //柱状图为column
                            axisLine:false,
                            disableGridY:true,
                            disableGridX:true,
                            disabledY:true,
                            legend:false,
                            width:this.cWidth1,
                            height:this.cHeight1,
                            showArrow:false,
                            showBox:false,
                            splitLine:false,
                        });
                    }
                    if (res.data.plateAnalyse) {
                        this.plateAnalyse = res.data.plateAnalyse
                        this.stock()
                    }
                    if(res.data.share){
                        this.share = res.data.share
                        this.getWxConfig()
                    }
                    // console.log(res.data,"data");
                    // lineData.categories = ["12月","11月","10月","09月","08月","07月"]
                    // lineData.series = [{data:[13000,14000,13200,13400,13800,14000],"name":"比会员天禧"}]
                    // this.showLine("canvasLine", lineData);   多个图表可以定义多个变量 通过改变  this.showLine 的第三个参数里的值来改变显示样式 
                }
            });
        },
        // 板块价值
        stock(){
            let lineData = {},categories =[],data =[]
            this.plateAnalyse.statistics.map(item=>{
                categories.push(item.distance +'KM')
                if (this.plateId == 1) {
                    data.push(item.muAvgPrice)
                }else if(this.plateId == 2){
                    data.push(item.floorAvgPrice)
                }
            })
            lineData.categories = categories;
            lineData.series = [
                {
                    data:data,
                    name:'',
                    textSize:28,
                    textColor:"#333333",
                    textOffset: -2,
                }
            ]
            this.showLine("canvasLine", lineData,{ 
                dataPointShapeType:"hollow",
                type:"line", //柱状图为column
                axisLine:false,
                disableGridY:true,
                disableGridX:false,
                disabledY:true,
                legend:false,
                width:this.cWidth1,
                height:this.cHeight1,
                showArrow:false,
                showBox:false,
                splitLine:false
            });
        },
        showLine(canvasId, chartData,options) {
            let _self = this;
            var canvas = new uCharts({
                $this:_self,
                canvasId: canvasId,
                type: options&&options.type?options.type:"area",
                fontSize: 11,
                padding:[10,50,10,50],
                dataPointShapeType:options&&options.dataPointShapeType||"solid",
                background: "#F8f8f8",
                pixelRatio: _self.pixelRatio,
                categories: chartData.categories,
                animation: true,
                series: chartData.series,
                colors:["#2D84FB"], //2版本以后改为color
                legend:{
                show:(options&&options.legend==false)?false:true,
                },
                labelDataOffsetY:20,
                extra: {
                    column: {
                        type: "group",
                        width: 80,
                    },
                    // area: {
                    //     type: "curve",
                    //     addLine: false
                    // },
                    line: {
                        type: "straight",
                        width: 2
                    },
                    tooltip:{

                    }

                },
                // tooltip:{
                //     // showArrow:true,
                //     showBox:options&&options.showBox==false?false:true,
                //     showArrow:options&&options.showArrow==false?false:true,
                //     splitLine:options&&options.splitLine==false?false:true,
                // },
                xAxis: {
                    disableGrid: options&&options.disableGridX?true:false,
                    axisLine:options&&options.axisLine==false?false:true,
                    fontColor:"#333333",
                    boundaryGap:'justify',
                    // rotateLabel: chartData.series.data+'元'
                },
                yAxis: {
                    // format: function(val) {
                    //   return val + "元";
                    // },
                    showTitle:true,
                    data:[
                        {
                        // "type": "value",
                        // "title": "元/m²",
                        // "max":(_self.max_line||12000)+2000,
                        "min":0,
                        // "unit": "",
                        // "format":''
                        }
                    ],
                    disabled:options&&options.disabledY?true:false,
                    disableGrid:options&&options.disableGridY?true:false
                },
                width:options&&options.width?options.width* _self.pixelRatio: _self.cWidth * _self.pixelRatio,
                height:options&&options.height?options.height* _self.pixelRatio: _self.cHeight * _self.pixelRatio,
                dataLabel: true,
                enableScroll: false,
                dataPointShape: true,
            });
            this.$set(this.opt,canvasId,canvas)
        },
        // touchLine(e) {
        //     this.opt.canvasLine.showToolTip(e, {
        //         format: function(item, category) {
        //         return category + " " + item.name + ":" + item.data +"元/m";
        //         },
        //     });
        // },
        analyse(){
            this.$ajax.get('build/buildWorthReportMatches',{id: this.id,},res => {
                if (res.data.code == 1) {
                    this.environment = res.data.environment
                    this.lifeSupport = res.data.lifeSupport
                    this.traffic = res.data.traffic
                    if (this.for < 2 && !res.data.done) {
                        this.for ++
                        this.analyse()
                    }
                    
                }
            })
        },
        toOptionPath(e){
            console.log(e);
            if(!e.path&&e.event){
                this[e.event]()
                return
            }
            let real_path = e.path.replace('{build_id}', this.build.id).replace('{lat}', this.build.lat).replace('{lng}', this.build.lng)
            this.$navigateTo(real_path)
        },
        toPath(index){
            let path;
            switch (index){
                case 1:
                    path = '/propertyData/map/analysis?id={build_id}&type=1&lat={lat}&lng={lng}&from=1'
                    break
                case 2:
                    path = '/propertyData/map/analysis?id={build_id}&type=1&lat={lat}&lng={lng}&from=3'
                    break
                case 3:
                    path = '/propertyData/map/analysis?id={build_id}&type=1&lat={lat}&lng={lng}&from=2'
                    break
                case 4:
                    path = '/propertyData/map/analysis?id={build_id}&type=1&lat={lat}&lng={lng}&from=4'
                    break
                case 5:
                    path = '/propertyData/map/map?id={build_id}&type=1&lat={lat}&lng={lng}'
                    break
                default:
                    break
            }
            let real_path = path.replace('{build_id}', this.build.id).replace('{lat}', this.build.lat).replace('{lng}', this.build.lng)
            this.$navigateTo(real_path)
        },
        toDetail(){
            this.$navigateTo('../pages/new_house/info?id='+this.id)
        },
        // 执行拨打电话时间
        handleTel(e, options) {
            // 如果有身份id则拨打置业顾问电话
            if(e&&e.identity_id){
                if(options.isAgent){
                e.isAgent = 1
                }
                if(options.isAdviser){
                e.isAdviser = 1
                }
                this.callAdviserMiddleNumber(e)
                return
            }
        },
        // 拨打置业顾问虚拟号码
        callAdviserMiddleNumber(e) {
        console.log('拨打置业顾问虚拟号码')
        var call_adviser = {}
        call_adviser = e
        var identity_id = call_adviser.identity_id||call_adviser.adviser_id||call_adviser.uid||call_adviser.id
        var tel_type = ""
        if(call_adviser.isAgent){
            tel_type = 3
        }
        if(call_adviser.isAdviser){
            tel_type = 2
        }
        if (!call_adviser.isAgent && !call_adviser.isAdviser) {
            tel_type = 0
        }
        this.callMiddleNumber(tel_type,identity_id,tel_type,identity_id)
        },
        // 请求虚拟号接口
        callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
            this.tel_params = {
                type,
                callee_id,
                scene_type,
                scene_id,
                source,
                bid,
                success: (res)=>{
                this.tel_res = res.data
                this.show_tel_pop = true
                }
            }
            // #ifdef MP-WEIXIN
            allTel(this.tel_params)
            // #endif
            // #ifndef MP-WEIXIN
            // 全局开启中间号且楼盘开启中间号需要检测登录
            // if(this.is_open_middle_num == 1 && this.detail.use_middle_call > 0){
            //     this.tel_params.intercept_login = true
            //     this.tel_params.fail = (res)=>{
            //     if(res.data.code === -1){
            //         this.$store.state.user_login_status = 1
            //         this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
            //     }
            //     if(res.data.code === 2){
            //         this.$store.state.user_login_status = 2
            //         this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
            //     }
            //     }
            //     encryptionTel(this.tel_params)
            // }else{
                allTel(this.tel_params)
            // }
            // #endif
        },
        
        handleChat(uid){
            getChatInfo(uid)
        },
        // 价值板块分析切换
        plate(index){
            this.plateId = index
            this.$nextTick(()=>{
				this.stock()
			})
        },
        submit_test_con(){
            let url =""
            // #ifdef H5
            url = this.$route.fullPath
            // #endif
            // #ifndef H5
            var pages = getCurrentPages()
            var currentPage = pages[pages.length - 1] //获取当前页面的对象
            url = currentPage.route //当前页面url
            var options = currentPage.options //当前页面url参数
            let i = 0
            url='/'+url;
            for(let key in options){
                if(i===0){
                    url+=`?${key}=${options[key]}`
                }else{
                    url+=`&${key}=${options[key]}`
                }
                i++
            }
            // #endif
            if (this.submit_tel.length!=11||this.submit_tel[0]!=1){
                uni.showToast({
                title:"手机号格式不正确",
                icon:'none'
                })
                return 
            }
            // e.type = this.sub_type || ''
            this.$ajax.post('build/signUp.html', {
                name:this.submit_name,
                tel:this.submit_tel,
                url,
                bid:this.id,
                from :'楼盘分析报告',
            }, res => {
                uni.hideLoading()
                uni.showToast({
                    title: res.data.msg,
                    icon: 'none'
                })
                if (res.data.code === 1) {
                    this.submit_tel =''
                    this.$refs.test_submit.hide()
                }
            })
        },
        stopMove(){

        },
    }
}
</script>
<style lang="scss" scoped>
page{
    background: #1e1f25;
    padding-bottom: 24rpx;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.unit{
    font-size: 22rpx;
}
.top{
    image{
        width: 100%;
    }
}
.adviser{
    .list{
        margin-bottom: 24rpx;
        .info{
            background: #fbf3e6;
            border-radius: 8rpx;
            justify-content: space-between;
            padding: 40rpx;
            align-items: center;
            margin-top: 24rpx;
            .title1{
                align-items: center;
                flex: 1;
                .name{
                    flex: 1;
                    font-size:32rpx;
                    font-weight: 500;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    word-break: break-all;
                }
                .pic{
                    width: 80rpx;
                    height: 80rpx;
                    border-radius: 50%;
                    margin-right: 24rpx;
                }
            }
            .contact{
                .flex-row{
                    background: #fff;
                    border-radius: 40rpx;
                    padding: 14rpx 24rpx;
                    align-items: center;
                    margin-left: 24rpx;
                    image{
                        width: 30rpx;
                        margin-right: 14rpx;
                    }
                }
            }

        }
    }
}
.jt{
    .bus{
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        margin: 24rpx 0;
        .img1{
            width: 100%;
        }
        .tag{
            position: absolute;
            text-align: center;
            image{
                width: 44rpx;
            }
        }
        .tag2{
            margin-top: -8%;
            margin-left: -35%;
        }
        .tag3{
            margin-top: 8%;
            margin-left: 27%;
        }
    }
}
.bk{
    .title{
        .tab{
            display: flex;
            border-radius: 4rpx;
            background: #f8f8f8;
            text{
                font-size: 22rpx;
                width: 44px;
                text-align: center;
                padding: 2px 0;
                margin: 2rpx;
                color: #333;
            }
            .on{
                color: #2d84fb;
                font-weight: 500;
                background: #fff;
                border-radius: 4rpx;
            }
        }
    }
}
.fx{
    .chart{
        margin: 24rpx;
    }
}
.xq{
    .list1{
        justify-content: space-between;
        flex-wrap: wrap;
        margin: 24rpx 0;
        >view{
            background: #fbf3e6;
            width: 48.5%;
            border-radius: 8rpx;
            margin-bottom: 24rpx;
            padding: 24rpx;
            box-sizing: border-box;
            view{
                font-size: 32rpx;
                font-weight: 500;
                margin-top: 10rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                word-break: break-all;
            }
            >text{
                color: #999;
            }
        }
    }
}
.border{
    background: #fff;
    margin: 24rpx;
    border-radius: 16rpx;
    padding: 24rpx;
    .more{
        color: #2d84fb;
        justify-content: center;
        margin: 24rpx 0;
        align-items: center;
        text{
            margin-right: 10rpx;
        }
    }
    .title{
        font-size: 32rpx;
        font-weight: 500;
        align-items: center;
        text{
            flex: 1;
        }
    }
}
.info{
    margin-top: 0;
    .price{
        color: #FB656A;
    }
    .area{
        color: #999999;
        margin: 14rpx 0;
    }
    .tit{
        align-items: center;
        .tag{
            font-size: 22rpx;
            color: #fff;
            padding: 2rpx 10rpx;
            margin-left: 24rpx;
            border-radius: 4rpx;
        }
        .name{
            flex: 1;
            font-size: 40rpx;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            word-break: break-all;
        }
    }

    .img{
        width: 200rpx;
        height: 180rpx;
        margin-right: 24rpx;
    }
}
.options1{
  padding: 24rpx 0 24rpx 0;
  justify-content: space-between;
  >.item{
    // flex: 1;
    align-items: center;
    display: flex;
    flex-direction: column;
    .icon{
      width: 92rpx;
      height: 92rpx;
    }
    .text{
      margin-top: -10rpx;
      font-size: 22rpx;
      color: #141414;
    }
  }
}
  .mini_chart{
    height: 450rpx;
    width: 100%;
    position: relative;
    // border: 2rpx solid #D8D8D8;
    // border-radius: 8rpx;
    align-items: center;
    margin: 24rpx 0;
    .mi-charts{
        // height: 400rpx;
        width: 100%;
        background: #f8f8f8;
        // margin: 24rpx 0 60rpx 0;
        border-radius: 8rpx;
    }
    .price_press{
      flex: 1;
      padding: 20rpx;
      display: flex;
      flex-direction: column;
      border-left: 2rpx solid #F8F8F8;
      align-items: center;
      justify-content: center;
      .press_name{
        .press_direction{
          font-size: 32rpx;
          color: #333333;
          font-weight: bold;
        }
      }
      .press_con {
        margin-top: 16rpx;
        font-size: 22rpx;
        color: #999999;
      }
    }
  }
.sub_box{
  background-color: #fff;
  margin: 0 40rpx;
  border-radius: 16rpx;
  position: relative;
//   overflow-y: hidden;
  margin-top: 32rpx;
  .sub_header{
      padding: 24rpx 48rpx;
      color: #fff;
      background-image: linear-gradient(-41deg, #F7918F 0%, #FB656A 100%);
      position: relative;
      border-top-left-radius: 16rpx;
      border-top-right-radius: 16rpx;
      .sub_title{
        margin-bottom: 16rpx;
        font-size: 40rpx;
        font-weight: bold;
    }
    .sub_tip{
        font-size: 24rpx;
    }
    .icon{
        width: 188rpx;
        height: 188rpx;
        position: absolute;
        top: -32rpx;
        right: 48rpx;
        image {
            width: 100%;
            height: 100%;
        }
    }
  }
  .form_box{
      padding: 30rpx 48rpx;
  }
  .sub_content{
    font-size: 32rpx;
    line-height: 1.5;
    color: #333;
  }
  .sub_form{
    margin-top: 25rpx;
    .sub_tel{
        margin-bottom: 20rpx;
        padding: 10px;
        font-size: 14px;
        background-color: #f5f5f5;
    }
    input{
      padding: 20rpx;
      font-size: 28rpx;
      background-color: #f5f5f5;
    }
    .btn-box{
      padding: 10px 0 0 0;
      button{
          font-size: 34rpx;
          font-weight: bold;
          height: 88rpx;    
          line-height: 88rpx;    
        background: #FB656A;
        box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
        border-radius: 44rpx;
      }
      .close_btn{
          padding: 24rpx;
          text-align: center;
          color: #999;
      }
    }
  }
  
}
</style>
