
<template>
  <view class="page">
    <view class="icon">
      <image :src="imgSrc" mode ="aspectFit"></image>
    </view>
  </view>
</template>

<script>
export default {
  data(){
    return {

    }
  },
  computed:{
    imgSrc(){
      return this.$store.state.appIcon
    },
  }
}
</script>

<style scoped lang="scss">
.page{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
  }
  .icon{
    margin-top: 60rpx;
    width: 400rpx;
    height: 200rpx;
    overflow: hidden;
  }

  .icon image {
    width: 100%;
    height: 100%;
  }
</style>
