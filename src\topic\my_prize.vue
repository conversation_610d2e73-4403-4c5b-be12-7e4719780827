<template>
  <view class="my_prize">
    <view class="prize_list"  v-if ="list.length&&show">
      <view class="prize_item flex-row items-center"  v-for ="item in list" :key ="item.id" >
        <view class="prize_img">
          <image mode ="widthFix" :src ="item.cover |imageFilter"></image>
        </view>
        <view class="prize_info flex-1">
          <div class="prize_name">
            {{item.prize_title}}
          </div>
          <div class="prize_num">
           获奖时间:{{item.ctime}}
          </div>
        </view>
        <view class="oper">
          <view class="heyan" @click ="toHexiao(item)" :class ="{duihuan:item.is_write_off}">
            {{item.is_write_off?'已兑换':'查看核验码'}}
          </view>
        </view>
      </view>
       <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
    <view class="empty" v-if="list.length==0&&show" >
      <view class="empty_img">
        <image mode ="widthFix" :src="`/yidongduan/blindBox/<EMAIL>` | imageFilter "></image>
      </view>
      <view class="empty_name">
        您还没获得奖品哟～
      </view>
      <view class="empty_btns flex-row items-center">
        <view class="empty_btn" @click ="toIndex">
          去拆盲盒
        </view>
      </view>

    </view>
    

    <my-popup ref="qrcode_popup" position="bottom">
			<view class="qrcode-box">
        
			
				<view class="img-box">

					<image  class="qrcode" :src="current.write_off_qrcode | imageFilter" mode="aspectFill"></image>
					<view>
						<view class="title">{{current.prize_title}}</view>
						<view class="tip">凭核验码联系现场工作人员兑奖</view>
					</view>
          <view class="line">

          </view>
          <view class="title title_info">
            兑奖券详情
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              兑奖期限
            </view>
             <view class="duihuan_info_value">
              {{current.cash_stime}} - {{current.cash_etime}} 
            </view>
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              兑奖地址
            </view>
             <view class="duihuan_info_value">
              {{current.cash_address}}
            </view>
          </view>

          <view class="title title_info">
            核验流程
          </view>
          <view class="duihuan_info flex-row items-center">
            1.用户在指定时间内持核销码至指定地点兑奖

          </view>
          <view class="duihuan_info flex-row items-center">
        
2.核销员打开微信扫一扫进行扫码核销

          </view>
          <view class="duihuan_info flex-row items-center">
           
3.核销成功，用户领取奖品
          </view>
          
				</view>
			</view>
		</my-popup>
     <my-popup ref="sub_form_popup" position="center" height="800rpx" :touch_hide="false">
        <view class="sub_box" id="sub_box">
            <view class="sub_header">
                <view class="sub_title">完善兑奖信息</view>
                <view class="icon">
                <image  mode="widthFix"  :src="'/images/new_icon/baoming_tg.png' | imageFilter('m_320')"></image>
                </view>
            </view>
            <view class="form_box">
                <view class="sub_content">所填信息不会公开 仅用于活动兑奖</view>
                <view class="sub_form">
                    <input v-model="name" class="sub_tel" maxlength="10" type="text" placeholder="称呼" @input="inputName" />
                    <input v-model="tel"  class="sub_tel" maxlength="11" type="number" placeholder="手机号" @input="inputPhone" />
                    <view class="btn-box">
                        <button class="default" @click="subData()">提交</button>
                        <view class="close_btn" @click="closeSub()">取消</view>
                    </view>
                </view>
            </view>
        </view> 
    </my-popup>
  </view>
</template>

<script>
import myPopup from "@/components/myPopup.vue"
import myIcon from "@/components/myIcon.vue"
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
  components:{
    myPopup,
    myIcon,
    uniLoadMore
  },
  data(){
    return{
      qrcode:'',
      params:{
        page:1,
        rows:20
      },
      show:false,
      list:[],
      current:{},
      get_status:"",
      content_text:{
          contentdown:"",
          contentrefresh:"正在加载...",
          contentnomore:"没有更多数据了"
      },
      name:'',
      tel:""
    } 
  },
  onLoad(options){
    if (options.id){
      this.params.id = options.id
      this.getList()
    }else {
      uni.showToast({
        title:'活动id不能为空',
        icon:"none"
      })
    }
   
  },
  mounted(){
    //  this.showQrcode()
  },
  methods:{
    showQrcode(){
      this.$nextTick(()=>{
        this.$refs.qrcode_popup.show()
      })
      
    },
    getList(){
      if (this.params.page ==1){
        this.list = []
      }
      this.get_status ='contentrefresh'
      this.$ajax.get("blind_box/userPrizeList",this.params,res=>{
        console.log(res);
        if (res.data.code ==1){
          this.list =this.list.concat(res.data.list) 
          if (res.data.list.length<this.rows){
            this.get_status = "contentnomore"
          }else {
             this.get_status = ""
          }
          this.show =true
        }else {
          this.show =true
          uni.showToast({
            title:res.data.msg,
            icon:"none"
          })
        }
      })
    },
    toIndex(){
      this.$navigateTo("/topic/blindBox?id="+this.params.id)
    },
    inputName(e) {
          this.name = e.detail.value
      },
      inputPhone(e) {
          this.tel = e.detail.value
      },
      subData(){
        if (!this.name){
          uni.showToast({
            title:"请输入用户名",
            icon:"none"
          })
          return 
        }
        if (!this.tel){
          uni.showToast({
            title:"请输入手机号",
            icon:"none"
          })
          return 
        }
        this.$ajax.post("blind_box/perfectCasherInfo",{
          tel:this.tel,
          name:this.name,
          id:this.params.id
        },res=>{
          if (res.data.code ==1){
            uni.showToast({
              title:res.data.msg,
              icon:'none'
            })
            this.$refs.sub_form_popup.hide()
            this.toHexiao(this.current)
          }else {
            uni.showToast({
              title:res.data.msg,
              icon:'none'
            })
          }
        })
    },
    closeSub(){
      this.$refs.sub_form_popup.hide()
    },
    toHexiao(item){
      this.current= item||{}
      this.$ajax.get("blind_box/userPrizeWriteOffCode",{id:item.id},res=>{
        console.log(res);
        if(res.data.code==1){
          this.current.write_off_qrcode = res.data.write_off_qrcode
          this.$forceUpdate()
          // setTimeout(() => {
          //   this.$nextTick(()=>{
            this.showQrcode()
          //   })
          // }, 500);
        }else {
          if(res.data.code==0 && res.data.is_need_perfect_casher_info){
            this.$refs.sub_form_popup.show()
            return 
          }
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    },
    scrollToLower(){
      if (this.get_status!=="contentnomore"){
        this.params.page++
        this.getList()
      }
    }
  },
  onReachBottom(){
    if (this.get_status!=="contentnomore"){
        this.params.page++
        this.getList()
      }
  }
}
</script>

<style lang="scss" scoped>
.my_prize {
  background: #f3f3f3;
  // overflow: auto;
}
.flex-row{
  display: flex;
  flex-direction: row;
}
.items-center {
  align-items: center;
}
.j-center{
  justify-content: center;
}
.flex-1 {
  flex: 1;
}
.prize_list{
  margin: 80rpx 48rpx 0; 
  // height: calc(100vh - 400rpx);
  // margin: 0 48rpx ; 
  // height:calc(100% - 96rpx);
  // width: calc(100vw - 192rpx);
  background: #fff;
  border-radius: 20rpx;
  padding: 48rpx;
  .prize_item {
    padding: 24rpx 0;
    border-bottom: 1px solid #D8D8D8;
    .prize_img{
      width: 110rpx;
      min-width: 110rpx;
      height:110rpx;
      margin-right: 24rpx;
      image {
        width: 100%;
      }
    }
    .prize_info {
      overflow: hidden;
      .prize_name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 32rpx;
        color: #7F502C;
      }
      .prize_num {
        font-size: 28rpx;
        color: #7F502C;
        margin-top: 16rpx;
      }

    }
    .oper {
      .heyan{
        font-size: 22rpx;
        color: #FFFFFF;
        padding: 16rpx 34rpx;
        min-width: 100rpx;
        background-image: linear-gradient(90deg, #FA7427 0%, #F24011 100%);
        box-shadow: 0 10rpx 20rpx -8rpx rgba(244,73,21,0.60), inset 0 0 6rpx 0 rgba(255,255,255,0.42);
        text-align: center;
        border-radius:32rpx;
      }
    }
  }

}
.empty {
  text-align: center;
  width: 80vw;
  margin: 40rpx auto;
  height: 80vh;
  padding-top: 120rpx;
  background: #fff;
  border-radius: 20rpx;
  .empty_img {
    width: 240rpx;
    height: 240rpx;
    margin: 0 auto;
    overflow: hidden;
    image {
      width: 100%;
    }
  }
  .empty_name {
    margin-top: 48rpx;
    font-size: 32rpx;
    color: #7F502C;
  }
  .empty_btns{
    justify-content: center;
    margin-top: 86rpx;
    .empty_btn {
      font-size: 32rpx;
      color: #FFFFFF;
      padding: 22rpx 106rpx;

      background-image: linear-gradient(90deg, #FA7427 0%, #F24011 100%);
      box-shadow: 0 10rpx 20rpx -8rpx rgba(244,73,21,0.60), inset 0 0 6rpx 0 rgba(255,255,255,0.42);
      border-radius: 44rpx;
    }
  }
}

  .qrcode-box{
	position: relative;
	margin-top: 0;
  
	.img-box{
		padding: 12rpx;
    padding-top: 48rpx;
		margin: auto;
    position: relative;
		background-color: #fff;
		border-radius: 15rpx;
		overflow: hidden;
		.title{
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
      color: #7F502C;
      &.title_info{
        text-align: left;
        margin-top: 48rpx;
        padding: 0 48rpx;
      }
		}
    .duihuan_info{
      margin: 24rpx 0;
      padding: 0 48rpx;
      font-size: 22rpx;
      color: #7F502C;
      .duihuan_info_name{
        white-space: nowrap;
        margin-right: 20rpx;
      }
      
    }
    .empty{
      height: 300rpx;
      width: 100vw;
     
    }
    
		.tip{
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			font-size: 28rpx;
      color: #7F502C;
		}
    .line {
      width: calc(100vw - 48rpx);
      height:2rpx;
      margin: 0 auto;
      background: #D8D8D8;
    }
	}
	.qrcode{
		width: 320rpx;
    margin: 0 auto;
		height: 320rpx;
    display: block;
	}
	.icon-box{
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
  
  
  
}

.sub_box{
  background-color: #fff;
  margin: 0 40rpx;
  // height: 500rpx;
  border-radius: 16rpx;
  position: relative;
//   overflow-y: hidden;
  margin-top: 32rpx;
  .sub_header{
      padding: 24rpx 48rpx;
      color: #fff;
      background-image: linear-gradient(-41deg, #F7918F 0%, #FB656A 100%);
      position: relative;
      border-top-left-radius: 16rpx;
      border-top-right-radius: 16rpx;
      .sub_title{
        margin-bottom: 16rpx;
        font-size: 40rpx;
        font-weight: bold;
    }
    .sub_tip{
        font-size: 24rpx;
    }
    .icon{
        width: 188rpx;
        height: 188rpx;
        position: absolute;
        top: -32rpx;
        right: 48rpx;
        image {
            width: 100%;
            height: 100%;
        }
    }
  }
  .form_box{
      padding: 30rpx 48rpx;
  }
  .sub_content{
    font-size: 32rpx;
    line-height: 1.5;
    color: #333;
  }
  .sub_form{
    margin-top: 25rpx;
    .sms_code_inp{
        align-items: center;
        margin-bottom: 20upx;
    }
    .sub_tel{
        margin-bottom: 20rpx;
    }
    .entrustSelect{
        height: 80upx;
        background: #f5f5f5;
        margin-bottom: 20upx;
        display: flex;
        padding: 0 20rpx;
        color: #888;
        align-items: center;
        justify-content: space-between;
    }
    input{
      padding: 20rpx;
      font-size: 28rpx;
      background-color: #f5f5f5;
    }
    .send-code{
      margin-left: 10rpx;
      color: $uni-color-primary;
      &.disable{
        color: #888;
      }
    }
    .btn-box{
      padding: 10px 0 0 0;
      button{
        font-size: 34rpx;
        font-weight: bold;
        height: 88rpx;    
        line-height: 88rpx;    
        background: #FB656A;
        box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
        border-radius: 44rpx;
      }
      .close_btn{
          padding: 24rpx;
          text-align: center;
          color: #999;
      }
    }
  }
  .verify_block{
    position: absolute;
    left: 0;
    right: 0;
    top: 150rpx;
    bottom: 40rpx;
    background-color: #fff;
    z-index: 2;
  }
}
</style>