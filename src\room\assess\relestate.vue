<template>
    <view class="page">
        <view class="hedaer_tap">
            <view class="hedaer_tap_one">{{ nav.name }}</view>
            <view class="hedaer_tap_two">{{logo.shi}}{{logo.ting}}厅{{logo.wei}}卫｜{{ logo.mianji }} 平｜{{ logo.chaoxiang }}</view>
            <view class="header_center">
                <view class="header_kuang">
                    <view class="footer_tap">
                    </view>
                    <view class="footer-one">
                        <text class="tex-one">{{ home.price }}</text>
                        <text class="text-b" style="color: #232323;font-size: 28rpx;margin-left: 4rpx;">万</text>
                        <text class="text-c" style="color:#232323;margin-left:18rpx">{{ home.avg_price }}元/m2</text>
                    </view>
                    <view class="footer-two" v-if="flag = true&&home.rangePrices.length">
                        估值参考范围{{ home.rangePrices[0] }}-{{ home.rangePrices[1] }}万
                    </view>
                </view>
            </view>
        </view>
        <view class="list-footer">
            <view style="color: #232323;font-size: 40rpx;font-family: PingFang SC;
                font-weight: medium">价格走势</view>
            <view style="display: flex;justify-content: space-between;align-items: self-end;">
                <view style="margin-left:32rpx;">
                    <text class="title_one"></text>
                    <text style="font-size: 24rpx;">同房屋</text>
                </view>
                <view style="margin-left:32rpx;">
                    <text class="title_two"></text>
                    <text style="font-size: 24rpx;">同小区</text>
                </view>
                <view style="margin-left:32rpx;">
                    <text class="title_three"></text>
                    <text style="font-size: 24rpx;">同城区</text>
                </view>
            </view>
        </view>
        <view classs="echart">
            <!-- <echarts :msgw="thelist"></echarts> -->
            <!-- <view id="chartLineBox" style="width: 100%;height: 540rpx;margin-top: -60rpx;"> </view> -->
            <view class="qiun-columns">    
                        <view class="qiun-charts" >
			        <canvas canvas-id="canvasLineA"  id="canvasLineA"  class="charts"></canvas>
		                </view>
	            </view>
        </view>
        <view class="logo_left" style="font-size:40rpx;font-family: PingFang SC;
    font-weight: medium;margin-left: 48rpx;margin-bottom: 24rpx">
            小区动态
        </view>
        <view class="frame" >
            <view class="frame_one"  v-for="(item,index) in email" :key="item.id">
                <view>{{item.label}}</view>
                <view>{{ item.value }}</view>
            </view>
        </view>
        <view class="logo_left"
            style="font-size:40rpx;font-family: PingFang SC;font-weight: medium;margin-left: 48rpx;margin-bottom: 24rpx;margin-top: 48rpx;">
            周边在售房源
        </view>
        <view class="bttom_regions" v-for="(item, index) in list" :key="item.id" style="margin-bottom: 40rpx;">
            <image :src="item.img" class="im"></image>
        </view>
        <view class="btn">
            <view>
                <button class="btn_one" @click="handletap()">出租房屋</button>
            </view>
            <view>
                <button class="btn_two" @click="handlesell()" style="margin-bottom: 40rpx;">出售房屋</button>
            </view>
        </view>
    </view>
</template>
<script>
import uCharts from '../../components/u-charts/u-charts.js';
var _self;
	var canvaLineA=null;
export default {
    data() {
        return {
            cWidth:'',
            opts:"",
            chartData:"",
			cHeight:'',
			pixelRatio:1,
            id:0,
            caid:0,
            logo:"",
            title: {
            },
            flag: true,
            list: [],
            nav: {

            },
            home: {
                rangePrices: []
            },
            email: [],
            thelist:[],
            price:[],
            year:[],

        }
    },
    onLoad(e) {
        console.log(e)
        this.id = e.query
        _self = this;
			// uni.upx2px(750) 这是uni-app自带的自适应，以750的尺寸为基准。动态变化
			this.cWidth=uni.upx2px(650);
			this.cHeight=uni.upx2px(450);
        
    },
    onReady(){
        this.handleGetList()
    },
    methods: {
        handletap() {
            this.$navigateTo('/pages/renting/renting?')
          
        },
        onPickerChange(e) {
      e.forEach(item => {
        item.forEach(el => {
          this.params[el.identifier] = el.value
        })
      })
      // this.setTitle()
    },
        handlesell() {
            this.$navigateTo('/pages/new_house/new_house')
        },

        // 获取列表数据
        handleGetList() {
            this.$ajax.get(`/wapi/fangjia/communityFangjiaEstimate?entrust_id=${this.id}`, {}, res => {
                if (res.data.code == 1) {
                    this.nav = res.data.community
                    this.logo = res.data.entrustData
                    this.list = res.data.saleList
                    this.home = res.data.communityAvgPriceData
                    this.email = res.data.communityDynamics
                    this.id = res.data.community.id
                    this.caid = res.data.community.areaid
                    this.year = res.data.areaAvgPriceList
                    if (res.data.share) {
                    this.share = res.data.share
                    }
                    this.getWxConfig()
                    // console.log(this.year)
                    let arr = []
                    let add =[]
                    let date = []
                    this.year.forEach(item => {
                        arr.push({month:item.month})
                    })    
                        for(let i=0;i<arr.length;i++){
                            add.push(arr[i].month)
                        }
                        this.thelist = add
                        // console.log(this.thelist,"321321")
                        let mdd = []
                        let mod = []
                        this.year.forEach(item => {
                            mdd.push({price:item.price})
                        })
                    for(let i=0;i<mdd.length;i++){
                            mod.push(mdd[i].price)
                        }
                        for(let i=0;i<add.length;i++){
                            // console.log(add[i].split('-'))
                            date.push(parseInt(add[i].split('-')[1])+"月")
                        }
                        // console.log(mod,"金钱")
                        this.thelist = date
                        this.price = mod
                        this.getServerData();
                }
            })
        },
        getServerData(){
					this.chartData = {
						categories: this.thelist,
						series: [{
							name: "价格",
							data: [,31000,,42000,,52300],         
							color: "#dee5fc"
						}],
					}
					_self.showLineA("canvasLineA", this.chartData);
			},
            showLineA(canvasId,chartData){
                canvaLineA=new uCharts({
					$this:_self,
					canvasId: canvasId,
					// 图标类型
					type: 'column',
                    // background:"linear-gradient(180deg, #446EEF00 0%, #446CEF 100%);",
					fontSize:11,
                    width:200,
					legend:{show:false},
					dataLabel:false,
					dataPointShape:false,
					pixelRatio:_self.pixelRatio,
					categories: chartData.categories,
					series: chartData.series,
					animation: true,
                    dataLabel:false,
                    gridType:'dash',
					context:uni.createCanvasContext(canvasId, _self), // 这里很重要
					// x轴显示的内容
					xAxis: {
                        disableGrid: true,
                        xAxisLabel:false,
                        format:false,
                        type: "group",
                        // fontColor: "#979797",
                        //  width: 20,
                        activeBgOpacity: 0.08
					},
					// y轴显示的内容
					yAxis: {
                        // disabled:false,
                        gridType:"dash",
                        min:this.price[0],
                        max:this.price[5],
                        fontColor: "#979797",
					},
					width: _self.cWidth*_self.pixelRatio,
					height: _self.cHeight*_self.pixelRatio,
				});
				
			},
    },

}
</script>
<style scoped lang="scss">
.page {
    background: white;
    min-height: calc(100vh - 88rpx);
}

.hedaer_tap {
    width: 100%;
    height: 354rpx;
    background-image: url('../../static/icon/assress.png');
    // background: blue;
    padding-top: 40rpx;
    box-sizing: border-box;
    z-index: 9999;

    .hedaer_tap_one {
        margin-left: 48rpx;
        font-size: 44rpx;
        color: #ffffff;
    }

    .hedaer_tap_two {
        margin-left: 48rpx;
        font-size: 28rpx;
        color: #ffffff;
        margin-top: 8rpx;
    }
}

.header_center {
    width: 688rpx;
    height: 296rpx;
    border-radius: 10px;
    background: linear-gradient(180deg, #FFFFFF3F 0%, #BFBFBFBA 53%, #BFBFBFDD 100%);
    backdrop-filter: blur(4px);
    box-shadow: 0px 0px 4px 0px #0000000C;
    z-index: 1;
    margin: 0 auto;
    margin-top: 44rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.header_kuang {
    width: 634rpx;
    height: 244rpx;
    background: #FFFFFFE5;
    backdrop-filter: blur(2px);
    z-index: 1;
    border: solid 1.8rpx #ffffff;
    position: relative;
    padding-top: 66rpx;
    box-sizing: border-box;
}

.footer_tap {
    width: 244rpx;
    height: 102rpx;
    position: absolute;
    top: 0;
    right: 0;
    border-radius: 10px;
    background: #FFFFFF19;
}

.footer-one {
    margin-left: 32rpx;
    margin-bottom: 6rpx;

    .text-a {
        font-weight: bold;
        font-size: 64rpx;
        color: #232323;
    }
}

.footer-two {
    margin-left: 32rpx;
    color: #979797;
}

.list-footer {
    width: 654rpx;
    margin: 0 auto;
    margin-top: 190rpx;
    display: flex;
    justify-content: space-between;
}

.title_one {
    display: inline-block;
    background: #446EEF;
    border: 2rpx solid #FFFFFF;
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    box-shadow: 0px 0px 8rpx 8rpx #446EEF;
    margin-right: 14rpx;
    box-sizing: border-box;
}

.title_two {
    display: inline-block;
    background: #68C191;
    border: 2rpx solid #FFFFFF;
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    box-shadow: 0px 0px 8rpx 8rpx #68C191;
    margin-right: 14rpx;
    box-sizing: border-box;
}

.title_three {
    display: inline-block;
    background: #D22E25;
    border: 2rpx solid #FFFFFF;
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    box-shadow: 0px 0px 8rpx 8rpx #D22E25;
    margin-right: 14rpx;
    box-sizing: border-box;
}

.echart {
    margin-left: 48rpx;
}

.frame {
    width: 654rpx;
    border-radius: 5px;
    background: #FFFFFF;
    border: 0.5px solid #D3D3D3;
    box-shadow: 0px 0px 4px 0px #00000019;
    box-sizing: border-box;
    margin: 0 auto;
    padding: 32rpx 30rpx;
    padding-bottom: 0px;
    display: flex;
    box-sizing: border-box;
    justify-content: space-between;
    flex-wrap: wrap;
}
.frame_one{
    width: 40%;
    margin-bottom: 30rpx;
    // margin-right: 20rpx;
    view:nth-child(1){
        color: #979797;
font-family: PingFang SC;
font-weight: regular;
font-size: 24rpx;
line-height: normal;
    }
    view:nth-child(2){
        color: #232323;
font-family: PingFang SC;
font-weight: medium;
font-size: 48rpx;
line-height: normal;
    }
}

.bttom_regions {
    width: 702rpx;
    // background: pink;
    // margin: 0 auto;
    height: 238rpx;
    // display: flex;
    margin-left: 24rpx;
    margin-top: 12rpx;
    display: inline;

    .im {
        width: 340rpx;
        height: 236rpx;
        border-radius: 10rpx 10rpx 0rpx 0rpx;
    }
}

.btn {
    margin: 0 auto;
    display: flex;
    width: 654rpx;
    justify-content: space-between;
    margin-top: 42rpx;
    margin-bottom: 82rpx;

    uni-button {
        width: 320rpx;
        background-color: pink;
        border-radius: 10rpx;
        color: white;
        font-size: 32rpx;
    }

    .btn_one {
        background-color: #ff6b00;
    }

    .btn_two {
        background-color: #446EEF;
    }
}

.tex-one {
    color: #232323;
    font-size: 64rpx;
    font-family: PingFang SC;
    font-weight: medium;
}
.qiun-charts {
		width: 750upx;
		height: 450upx;
		background-color: #FFFFFF;
        color: #979797;
        position: relative;
        top: 30rpx;
        left: 30rpx;
        margin-bottom: 40rpx;
	}
	
	.charts {
		width: 750upx;
		height: 450upx;
		background-color: #FFFFFF;
        color: #979797;
	}
</style>