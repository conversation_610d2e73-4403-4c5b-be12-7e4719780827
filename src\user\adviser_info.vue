<template>
  <view>
    <view class="block">
      <view class="info-row avatar-row">
        <view class="label">头像</view>
        <view class="right flex-row">
          <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadHeaderImg">
            <image class="img" mode="aspectFill" v-if="(params.adviser&&params.adviser.uncheck_prelogo)||params.prelogo" :src="((params.adviser&&params.adviser.uncheck_prelogo)||params.prelogo )| imageFilter('w_120')"></image>
            <view v-else class="upload-btn">
              <my-icon type="ic_jia" size="46rpx" color="#d8d8d8"></my-icon>
            </view>
          </my-upload>
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="block">
      <view class="info-row">
        <view class="label">姓名</view>
        <view class="right flex-row">
          <input type="text" v-model="params.cname" placeholder="请输入" placeholder-style="text-align:right;font-size:32rpx;color:#999">
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="block">
      <view class="info-row">
        <view class="label">微信号</view>
        <view class="right flex-row">
          <input type="text" v-model="params.wechat" placeholder="请输入" placeholder-style="text-align:right;font-size:32rpx;color:#999">
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <!-- <view class="block">
      <view class="info-row">
        <view class="label">楼盘信息</view>
        <view class="buildname flex-1" @click="buildsShow">{{(params.build_names)?(params.build_names):"请选择楼盘信息"}}</view>
        <view class="icon-box">
          <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
        </view>
      </view>
    </view> -->
    <view class="block">
      <view class="info-row">
        <view class="label">服务时间</view>
        <view class="right flex-row">
          <!-- <input type="text" v-model="params.build_ids" placeholder="请输入" placeholder-style="text-align:right;font-size:32rpx;color:#999"> -->
          <my-select oneRow :value="params.service_type" @change="pickerChange" :range="service_times" name="cid"></my-select>
          <!-- <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view> -->
        </view>
      </view>
    </view>
    <view class="block">
      <view class="info-row">
        <view class="label">转发标题</view>
        <view class="right flex-row">
          <input type="text" v-model="params.card_title" placeholder="请输入转发标题" placeholder-style="text-align:right;font-size:32rpx;color:#999">
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="block">
      <view class="info-column">
        <view class="label">个性签名</view>
        <textarea type="text" v-model="params.minfo" placeholder="请输入" placeholder-style="font-size:32rpx;color:#999"></textarea>
      </view>
    </view>
    <view class="block">
      <view class="upload-row">
        <view class="title flex-row">
          <text>微信二维码</text>
          <!-- <text class="tip">至少上传3张{{photos.indoor_images.length+photos.indoor_videos.length}}/10</text> -->
        </view>
        <view class="upload-box">
          <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadQrCode">
            <image class="img" mode="aspectFill" v-if="params.wechat_img" :src="params.wechat_img | imageFilter('w_240')"></image>
            <view v-else class="upload-btn">
              <my-icon type="ic_jia" size="96rpx" color="#d8d8d8"></my-icon>
            </view>
          </my-upload>
        </view>
      </view>
    </view>
    <view class="block" v-if='isChecking'>
      <view class="info-row">
        <view class="label">审核状态</view>
        <view class="right flex-row red">
          正在审核中
        </view>
      </view>
    </view>
    <myPopup ref='select_build'>
      <view style="background-color:#fff">
        <view class="top-box flex-row">
          <search @input="inputValue" @confirm="searchBuild"  placeholder="请输入楼盘名称"></search>
          <view class="search"  @click="searchBuild">
            <text>搜索</text>
          </view> 
        </view>
        <scroll-view scroll-y class="buildAll" @touchmove.stop.prevent="stopMove" @scrolltolower="loadLazy">    
          <view class="uni-list">
            <radio-group @change="radioChange">
              <label class="uni-list-cell uni-list-cell-pd flex-box" v-for="(item, index) in buildList" :key="item.id">
                <view>
                  <radio :value="''+item.id" :checked="index === current" />
                </view>
                <view>{{item.title}}</view>
              </label>
            </radio-group>
          </view>
          <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
        </scroll-view>
      </view>
    </myPopup>
    <!-- <view class="btn ordinary" @click="perView">预览</view> -->
    <view class="btn" @click="onSubmit">立即提交</view>
  </view>
</template>

<script>
import myIcon from "../components/myIcon"
import myUpload from '../components/form/myUpload'
import mySelect from '../components/form/mySelect'
import search from "../components/search.vue"
import myPopup from "../components/myPopup"
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
  components: {
    myIcon,
    myUpload,
    mySelect,
    search,
    myPopup,
    uniLoadMore
  },
  data () {
    return {
      service_times:[
        {value:0,
         name:"全天接听" 
        },
        {value:1,
         name:"晚10点到早8点拒接" 
        },
        {value:2,
         name:"全天拒接" 
        }
      ],
      get_status: "",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了"
      },
      current: "",
      page: 1,
      buildList: [],
      searchBuilds: '',
      params:{
        cname:'',
        wechat:'',
        prelogo:'',
        wechat_img:'',
        service_type:0,
        minfo:''
      },
      isChecking:false,
    }
  },
  onLoad(){
    this.getData()
  },
  methods: {
    getData() {
      this.$ajax.get("adviser/adviserEdit.html", {}, res => {

        if (res.data.code == 1) {
          if (this.$store.state.user_info.adviser&&this.$store.state.user_info.adviser.uncheck==1){
              this.isChecking =true
              if (this.$store.state.user_info.adviser.uncheck_cname){
                res.data.data.cname  =this.$store.state.user_info.adviser.uncheck_cname
              }
              if (this.$store.state.user_info.adviser.uncheck_prelogo){
                res.data.data.prelogo  =this.$store.state.user_info.adviser.uncheck_prelogo
              }
            }
          this.params = res.data.data
        }

      })
    },
    uploadHeaderImg(e){
      if (this.params.adviser&&this.params.adviser.uncheck_prelogo){
          this.params.adviser.uncheck_prelogo =''
        }
      this.params.prelogo = e.files.join(',')
    },
    uploadQrCode(e){
      this.params.wechat_img = e.files.join(',')
    },
    perView(){
      console.log('预览')
    },
    pickerChange(e) {
      this.params.service_type = e.value
    },
    buildsShow() {
      this.select = "true"
      this.$refs.select_build.show()
      this.getBuilds()
    },
    inputValue(e) {
      this.searchBuilds = e.detail.value
    },
    searchBuild() {
      uni.hideKeyboard()
      this.page = 1
      this.getBuilds() 
    },
    loadLazy() {
      if(this.get_status === "noMore"){
        return
      }
      this.page++
      this.getBuilds()
    },
    radioChange(evt) {
      for (let i = 0; i < this.buildList.length; i++) {
        if (this.buildList[i].id == evt.target.value) {
          this.current = i;
          this.params.build_ids = this.buildList[i].id
          this.params.build_names = this.buildList[i].title
          this.select = "false"
          this.$refs.select_build.hide()
          break;
        }
      }

    },
    // 获取楼盘信息
    getBuilds() {
      if(this.page === 1){
        this.buildList = []
      }
      this.$ajax.get('Adviser/getBuildList', {
        page: this.page,
        rows: 20,
        key: this.searchBuilds
      }, (res) => {
        if (res.data.code == 1) {
          if (res.data.data.length < this.rows) {
            this.get_status = "noMore"
          } else {
            this.get_status = "more"
          }
          this.buildList = this.buildList.concat(res.data.data)
        } else {
          this.get_status = "noMore"
          // uni.showToast({
          //   title: res.data.msg,
          //   icon: "none"
          // })
        }
      })
    },
    onSubmit(){
      console.log(this.params)
      if (!this.params.cname) {
        uni.showToast({
          title: "请输入姓名",
          icon: "none"
        })
        return false
      }
      if (!this.params.prelogo) {
        uni.showToast({
          title: "请上传头像",
          icon: "none"
        })
        return
      }
      if (!this.params.wechat) {
        uni.showToast({
          title: "请输入微信号",
          icon: "none"
        })
        return
      }
      if (!this.params.wechat_img) {
        uni.showToast({
          title: "请上传二维码",
          icon: "none"
        })
        return
      }
      uni.showLoading({
        title:"正在提交...",
        mask:true
      })
      this.$ajax.post('adviser/adviserEdit.html', this.params, (res) => {
        uni.hideLoading()
        if (res.data.code == 1) {
          uni.showToast({
            title: res.data.msg,
            mask:true,
            icon: "none"
          })
          setTimeout(() => {
            uni.$emit('onChangeWechat',this.params)
            this.$navigateBack()
          }, 1500);
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
        }
      },err=>{
        uni.hideLoading()
      })
    },
    stopMove(){}
  }
}
</script>

<style scoped lang="scss">
view{
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.flex-row{
  flex-direction: row;
}
.block{
  padding: 0 48rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  .info-row{
    width: 100%;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 48rpx 0;
    &.avatar-row{
      padding: 24rpx 0;
      .upload-btn{
        width: 96rpx;
        height: 96rpx;
      }
      .img{
        width: 96rpx;
        height: 96rpx;
      }
    }
    input{
      width: 85%;
      text-align: right;
    }
  }
  .info-column{
    padding: 48rpx 0;
    .label{
      margin-bottom: 20rpx;
    }
    textarea{
      width: 100%;
      height: 200rpx;
    }
  }
  .label{
    width: 160rpx;
    font-size: 32rpx;
    color: #666;
  }
  .right{
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    &.red{
      color: #f00;
    }
    .icon-box{
      margin-left: 16rpx;
    }
  }
  .title{
    justify-content: space-between;
    align-items: center;
    font-size: 32rpx;
    margin-bottom: 24rpx;
    color: #666;
  }
  .upload-row{
    padding: 48rpx 0;
  }
  .upload-btn{
    width: 25vw;
    height:25vw;
    align-items: center;
    justify-content: center;
    border-radius: 8rpx;
    background-color: #f5f5f5;
  }
  .img{
    width: 25vw;
    height: 25vw;
    border-radius: 8rpx;
  }
}

.top-box{
    align-items: center;
    padding: 30upx 10upx;
    search{
      flex: 1;
    }
    .search {
    color: #28bdfb;
    border-radius: 40upx;
    border: 3upx solid #28bdfb;
    padding: 8upx 20upx;
    margin: 5upx 20upx;
    align-items: center;
  }

  .search text {
    color: #28bdfb;
    font-size: 24upx;
  }
}

.buildAll {
  height: 60vh;
  width: 100%;
  overflow-y: auto;
  background: #fff;
  .uni-list {
    radio-group {
      padding-left: 50upx;
    }

    .uni-list-cell {
      justify-content: flex-start;
      align-items: center;
      padding: 20upx 0upx;
    }
  }
}

.btn{
  margin: 24rpx 48rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  background-color: $uni-color-primary;
  box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
  &.ordinary{
    background-color: #fff;
    color: $uni-color-primary;
    border: 1rpx solid $uni-color-primary;
  }
}
</style>