<template>
  <view class="school_detail">
    <my-swiper :focus="focus" height="60vw" customIndicator @click="onClickSwiper"></my-swiper>
    <view class="main-info row">
      <image
        class="logo"
        mode="aspectFill"
        :src="detail.pic | imgUrl('w_240')"
      />
      <view class="info">
        <view class="name">{{ detail.name }}</view>
        <view class="address">{{ detail.address }}</view>
      </view>
      <view
        class="radiate left-line text-center"
        @click="navigateTo(`/school/teach_range?school_id=${id}`)"
      >
        <my-icon type="fanwei" size="25" color="#ff6735"></my-icon>
        <view class="text">施教范围</view>
      </view>
    </view>
    <view class="views row">
      <view class="pre_logos row">
        <image
          class="pre_logo"
          mode="aspectFill"
          v-for="(pre_logo, index) in views"
          :key="index"
          :src="pre_logo | imgUrl('w_80')"
        >
        </image>
        <image
          class="pre_logo"
          v-if="views.length >= 10"
          mode="aspectFill"
          src="/static/icon/more.png"
        >
        </image>
      </view>
      <view class="view_num">共{{ detail.browse_count||0}}人浏览</view>
    </view>

    <view class="card">
      <view class="title">
        <text>基本信息</text>
      </view>
      <view class="content">
        <view class="row" v-if="detail.type_name">
          <view class="label">学校性质：</view>
          <view class="value">
            <text class="school_type">{{ detail.type_name }}</text>
            <my-tag class="tag" size="small" v-for="(label_item,index) in detail.label" :key="index">{{label_item}}</my-tag>
          </view>
        </view>
        <view class="row" v-if="detail.tel">
          <view class="label">官方电话：</view>
          <view class="value">{{ detail.tel }}</view>
        </view>
        <view class="row" v-if="detail.web_site">
          <view class="label">官方网站：</view>
          <view class="value">{{ detail.web_site }}</view>
        </view>
        <view class="row">
          <view class="label">最后更新：</view>
          <view class="value">{{ detail.utime }}</view>
        </view>
      </view>
      <view
        class="footer"
        @click="navigateTo(`/school/introduce?school_id=${id}`)"
        >查看学校简介</view
      >
    </view>

    <!-- 招生信息 -->
    <view class="card" v-if="range">
      <view class="title">
        <text>招生信息</text>
        <view v-if="range.time" class="tag">{{ range.time }}</view>
      </view>
      <view class="content">
        <view class="row" v-if="range.target">
          <view class="label">招生对象：</view>
          <view class="value">{{ range.target }}</view>
        </view>
        <view class="row" v-if="range.target_count">
          <view class="label">招生人数：</view>
          <view class="value">{{ range.target_count }}</view>
        </view>
        <view class="row" v-if="range.tree">
          <view class="label">学校费用：</view>
          <view class="value">{{ range.tree }}</view>
        </view>
        <view class="row" v-if="range.descp">
          <view class="label">对口地段：</view>
          <view class="value">{{ range.descp }}</view>
        </view>
      </view>
      <view
        class="footer"
         @click="navigateTo(`/school/regulations?school_id=${id}`)"
        >查看最新招生简章</view
      >
    </view>
    <!-- 关联小区 -->
    <view class="community_list">
      <view class="title-row label">
        <text class="text">邻校小区</text>
        <!-- <view
          class="more"
          @click="navigateTo(`/school/communitys?school_id=${id}`)"
        >
          <text>查看更多</text>
          <my-icon type="you" color="#999999" size="28rpx"></my-icon>
        </view> -->
      </view>
      <list-item
        v-for="item in community_list"
        :key="item.id"
        :title_row="1"
        :image="item.pic"
        :title="item.name"
        :sub_title="item.areaName"
        :address="item.address"
        @click="navigateTo(`/pages/house_price/detail?id=${item.id} `)"
      >
        <template v-slot:prominent>
          <view class="school_count">
            <my-icon type="guanlian" color="#ff6735"></my-icon>
            <text class="text">附近有{{ item.schoolCount }}所学校</text>
          </view>
        </template>
      </list-item>
      <no-data v-if="nocommunity" tip="邻校小区待更新"></no-data>
    </view>

    <view class="fixed-bottom">
      <footer-nav
        @makePhoneCall="handleMakePhoneCall()"
        openType="share"
        tel
      ></footer-nav>
    </view>

    <!-- 查询次数 邀请好友-->
    <myPopup :show="show_invit" position="top" @hide="show_invit = false">
      <invitePop invite @close="toHome"></invitePop>
    </myPopup>
  </view>
</template>

<script>
import mySwiper from './components/mySwiper'
import myIcon from '../components/icon'
import myTag from '../components/myTag'
import listItem from './components/listItem'
import noData from '../components/noData'
import footerNav from './components/footerNav'
import myPopup from './components/myPopup'
import invitePop from './components/invitePop'
import wxApi from '../common/mixin/wx_api';
import {
  navigateTo,
  formatImg
} from '../common/index.js'
export default {
  components: {
    mySwiper,
    myIcon,
    myTag,
    listItem,
    noData,
    footerNav,
    myPopup,
    invitePop
  },
  mixins:[wxApi],
  data() {
    return {
      // 查询次数，邀请好友
      show_invit: false,
      id: null,
      focus: [],
      detail: {},
      range:{},
      isFollow: 0,
      views: [],
      nocommunity:false,
      community_list:[],
      news_list: [],
      house_type: 'rent',
      second_house_list: [],
      new_house_list: []
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id
      this.getData()
    }
  },
  filters: {
    imgUrl(val,param) {
      if (!val) {
        return ""
      }
      return formatImg(val, param)
    }
  },
  methods: {
    // 点击取消跳转首页
    toHome() {
      uni.switchTab({
        url: '/'
      })
    },

    getData() {
      this.$ajax.get('school/schoolDetail.html', { id: this.id }, res => {
        if (res.data.code === 1) {
          this.detail = res.data.data.baseInfo
          document.title = this.detail.name
          // uni.setNavigationBarTitle({
          //   title:this.detail.name
          // })
          this.isFollow = res.data.data.isFollow
          this.news_list = res.data.data.news
          this.community_list = res.data.data.village
          if (this.community_list.length === 0) {
            this.nocommunity = true
          }
          this.views = res.data.data.browse_log
          this.focus = res.data.data.schoolImages.images.map(item => {
            return {
              path: formatImg(item.pic,'w_8001')
            }
          })
          // if (res.data.data.baseInfo.master_pic) {
          //   this.focus.unshift({ path: formatImg(res.data.data.baseInfo.master_pic,'w_8001') })
          // }
          this.range = res.data.data.schoolRange
        } else if (res.data.code === -2) {
          this.show_invit = true
        }
        if (res.data.share) {
          this.share = res.data.share
        }else{
          this.share = {
            title: res.data.data.baseInfo.forward_title,
            content: res.data.data.baseInfo.forward_desc,
            pic: res.data.data.baseInfo.forward_pic
          }
        }
        this.getWxConfig()
      })
    },
    handleFollow() {
      this.$ajax.get('/school/schoolFollow.html', { id: this.id }, res => {
        if (res.data.code === 1) {
          this.isFollow = 1
          uni.showToast({
            title: res.data.message
          })
        } else {
          uni.showToast({
            title: res.data.message,
            icon: 'none'
          })
        }
      })
    },
    handleCancelFollow() {
      this.$ajax.get(
        '/school/schoolCancelFollow.html',
        { id: this.id },
        res => {
          if (res.data.code === 1) {
            this.isFollow = 0
            uni.showToast({
              title: res.data.message
            })
          } else {
            uni.showToast({
              title: res.data.message,
              icon: 'none'
            })
          }
        }
      )
    },
    navigateTo(url){
      navigateTo(url)
    },
    getRentHouse() {
      this.house_type = 'rent'
    },
    getSecondHouse() {
      this.house_type = 'second'
    },
    getNewHouse() {
      this.house_type = 'new'
    },
    onClickSwiper(index){
      uni.previewImage({
        urls: this.focus.map(item => formatImg(item.path)),
        current: formatImg(this.focus[index].path)
      })
    },
    handleMakePhoneCall() {
      if(!this.detail.tel){
        uni.showToast({
          title:'学校电话待更新',
          icon:'none'
        })
        return
      }
      uni.makePhoneCall({
        phoneNumber: this.detail.tel,
        success: () => {
          console.log('触发拨打电话')
        }
      })
    }
  },
  // #ifdef H5
  onNavigationBarButtonTap(option){
    if(option.index==0){
      uni.switchTab({
        url:'/'
      })
    }
  },
  // #endif
}
</script>

<style scoped lang="scss">
page{
  background-color: #fff;
}
view{
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  &.row {
    flex-direction: row;
  }
}
@import '../static/css/school_detail';
</style>
