<template>
  <view class="plate-box swiper-plate container">
    <view class="plate-title">
      <view class="cate">{{ question.title }}<text class="question-num">（共{{ question.count }}条提问）</text></view>
      <view class="c-right more" @click="$navigateTo('/ask/list')">查看全部</view>
    </view>
    <swiper class="question-swiper" :duration="260" next-margin="60rpx" v-if="question.list && question.list.length > 0">
      <swiper-item v-for="(item, index) in question.list" :key="index" @click="$navigateTo(`/ask/detail?id=${item.id}`)">
        <view class="question-item">
          <view class="item-title">
            <image class="title-logo" mode="aspectFit" :src="'/images/new_icon/qa_point.png' | imageFilter('m_320')"></image>
            <text>热议</text>
          </view>
          <view class="item-info">
            <view>{{ item.answer_count }}人已回答</view>
            <view>{{ item.update_time }}</view>
          </view>
          <view class="item-content">{{ item.content }}</view>
          <view class="item-btn" @click.stop="$navigateTo(`/ask/answer?id=${item.id}&question=${item.content}`)">我来回答</view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
export default {
  props: {
    question: {
      type: Object,
      default: ()=> {}
    },
  },
}
</script>

<style lang="scss" scoped>
.plate-box {
  margin-bottom: 48rpx;
  background-color: #ffffff;
  &.mgb0 {
    margin-bottom: 0;
  }
  .plate-title {
    height: 52rpx;
    line-height: 1;
    justify-content: space-between;
    align-items: flex-end;
    display: flex;
    &.flex-item-center {
      align-items: center;
    }
    .cate {
      align-items: flex-end;
      max-width: 100%;
      font-weight: bold;
      font-size: 40rpx;
      color: #333;
      .question-num {
        font-size: 28rpx;
      }
    }
    .more {
      font-size: 26upx;
      line-height: 40upx;
      color: #666666;
    }
  }
  .bottom-more {
    padding-top: 20rpx;
    text-align: center;
    color: #999999;
  }
}
.container {
  padding: 0 48rpx;
  &.swiper-plate {
    padding-right: 0;
    .plate-title {
      padding-right: 48rpx;
    }
  }
}
.question-swiper {
  margin-top: 48rpx;
  height: 352rpx;
  .question-item {
    position: relative;
    box-sizing: border-box;
    width: 620rpx;
    height: 352rpx;
    border-radius: 20rpx;
    padding: 20rpx 20rpx 0 20rpx;
    border: 1px solid #eee;
    box-shadow: 0 0 16rpx 0 rgba(0, 0, 0, 0.05);
    .item-title {
      display: flex;
      align-items: center;
      font-size: 22rpx;
      color: #333;
      font-weight: bold;
      .title-logo {
        width: 32rpx;
        height: 32rpx;
        margin-right: 6rpx;
      }
    }
    .item-info {
      display: flex;
      justify-content: space-between;
      color: #999;
      margin-top: 24rpx;
      view {
        font-size: 22rpx;
      }
    }
    .item-content {
      margin-top: 20rpx;
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
      letter-spacing: 2rpx;
      overflow: hidden;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    .item-btn {
      position: absolute;
      bottom: 48rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 524rpx;
      height: 64rpx;
      line-height: 64rpx;
      text-align: center;
      font-size: 22rpx;
      color: #fff;
      background: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
      box-shadow: 0 6rpx 12rpx 0 rgba(38, 113, 255, 0.3);
      border-radius: 40rpx;
    }
  }
}
</style>