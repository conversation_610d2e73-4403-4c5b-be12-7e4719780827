<template>
  <view class="build" :class="{has_bg_color: bg_color!==''}">
    <view class="tab_container">
      <tab-bar :tabs="cates" :bgColor="bg_color" :fixedTop="false" lineHeight="88rpx" height="88rpx"  :showLine="false" :nowIndex="build_cate_index">
        <text class="cate_item" v-for="(item, index) in cates" :key="index" :id="'i' + index" :class="{active:build_cate_index===index}" @click="onClickBuildCate({detail:{current:index,item}})">{{item.cate_name}}</text>
      </tab-bar>
    </view>
    <view class="build_list" id="build_list">
      <template v-if ="buildInfo.display_type==0">
        <view class="build_item" v-for="build in list" :key="build.id" @click="toDetail(build)">
        <view class="img_box" :class="{in_pc: !$store.state.in_mobile}">
          <image class="img" :src="build.pic | imageFilter('w_240')" mode="aspectFill" alt="">
        </view>
        <view class="title">{{build.title}}</view>
        <view class="info_row">
          <text class="label">价格：</text>
          <text class="value">{{build.price}}</text>
        </view>
        <view class="info_row">
          <text class="label">户型：</text>
          <text class="value">{{build.house_type_range}}</text>
        </view>
        <view class="info_row">
          <text class="label">位置：</text>
          <text>{{build.area}}</text>
        </view>
        <view class="btn_group flex-box">
          <view class="btn left" @click.stop.prevent="makePhone(build)">一键咨询</view>
          <view class="btn right" @click.stop.prevent="$emit('sign', build)">立即报名</view>
        </view>
      </view>
      <view class="build_item empty"></view>
      </template>
      <template v-if ="buildInfo.display_type==2">
        <view class="bg_white w100 mt20" v-html='buildInfo.content'></view>
      </template>
      <template v-if ="buildInfo.display_type==1">
        <view></view>
      </template>
      
    </view>
  </view>
</template>

<script>
import wx from "weixin-js-sdk"
import tabBar from "../../components/tabBar.vue"
export default {
  name: 'exBuild',
  components: {
    tabBar
  },
  props:{
    bg_color: {
      type: String,
      default: ''
    },
    cates: {
      type: Array,
      default: ()=>[]
    },
    list: {
      type: Array,
      default: ()=>[]
    },
    ex_id:{
      type: [Number, String],
      default: ''
    },
    buildCon:{
      type: [Object],
      default: ()=>{}
    }
  },
  watch:{
    buildCon:{
      handler(val){
        this.buildInfo = val
      },
      immediate:true
      // 
    },
    list:{
       handler(val){
         console.log(val,111);
        this.buildList =val
      },
      immediate:true
    }
  },
  data () {
    return {
      build_cate_index: 0,
      buildInfo:{},
      buildList:[]
    }
  },
  methods: {
    onClickBuildCate(e) {
      this.build_cate_index = e.detail.current
      this.$emit('switchCate', e.detail.current,e.detail.item)
    },
    toDetail(build){
        wx.miniProgram.getEnv((res)=>{
          if (res.miniprogram){
            if(build.build_id){
              wx.miniProgram.navigateTo({url:'/pages/new_house/detail?id='+build.build_id})
            }else{
              this.$navigateTo('/exhibition/detail?id='+build.id+'&ex_id='+this.ex_id)
            }
          }else {
              if(build.build_id){
                this.$navigateTo('/pages/new_house/detail?id='+build.build_id)
              }else{
                this.$navigateTo('/exhibition/detail?id='+build.id+'&ex_id='+this.ex_id)
              }
          }
      })
      
      
    },
    makePhone(build){
      if(build.tel){
        uni.makePhoneCall({
          phoneNumber: build.tel
        })
      }else{
        uni.showToast({
          title: '暂无此楼盘电话',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.bg_white{
  background: #fff;
}
.w100{
  width: 100%;
}
.mt20{
  margin-top: 20rpx;
}
.build{
  &.has_bg_color{
    .cate_item{
      color: #fff;
    }
    .build_list{
      .build_item{
        border: none;
      }
    }
  }
  .tab_container{
    padding: 24rpx 0 0 48rpx;
  }
  .cate_item{
    margin-right: 48rpx;
    font-size: 40rpx;
    color: #353535;
    &.active{
      font-weight: bold;
    }
  }
  .build_list{
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 48rpx;
    .build_item{
      flex: 1;
      min-width: 40%;
      margin-bottom: 24rpx;
      box-sizing: border-box;
      padding: 24rpx;
      border-radius: 8rpx;
      border: 1rpx solid #d8d8d8;
      background-color: #fff;
      &:nth-child(even){
        margin-left: 24rpx;
      }
      &.empty{
        padding-top: 0;
        padding-bottom: 0;
        height: 0;
        border: none;
      }
      .img_box{
        width: 100%;
        height: 26.6vw;
        &.in_pc{
          max-height: 200rpx;
        }
        .img{
          width: 100%;
          height: 100%;
        }
      }
      .title{
        line-height: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: bold;
        font-size: 24rpx;
        color: #353535;
      }
      .info_row{
        margin-bottom: 6rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 22rpx;
        color: #999;
        .label{
          color: #999;
        }
        .value{
          color: $uni-color-primary;
        }
      }
      .btn{
        margin-top: 24rpx;
        width: 100%;
        height: 64rpx;
        line-height: 64rpx;
        text-align: center;
        border-radius: 8rpx;
        font-size: 22rpx;
        color: #fff;
        &.left{
          background-image: linear-gradient(90deg, #45A2FF 0%, #02A2FF 100%);
        }
        &.right{
          margin-left: 24rpx;
          background-image: linear-gradient(90deg, #FFA857 0%, #FF6069 100%);
          // box-shadow: 0 2px 7px -1px rgba(255,102,104,0.50);
        }
      }
    }
  }
}
</style>