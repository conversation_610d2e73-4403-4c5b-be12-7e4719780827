<template>
<view class="content topic">
    <topic :listsData="lists" ></topic>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <!-- <view v-if ="topicSkin==1">
        <view class="top_header">
            <view class="img"><image :src="img|imgUrl('w_6401')" mode="aspectFill"></image></view>
            <view class="v_center header_title">{{c_title}}</view>
            <view class="v_center sub_header_title">{{c_text}}</view>
        </view>
        <topic-list :TopNew="topNew" :MainNewTop="MainNewTop" :MainNewList ="MainNewList" :ShopNewTop ="ShopNewTop" :ShopNewList ="ShopNewList"  :title1="title1" :title2="title2" :title3="title3"></topic-list> -->
   
    <!-- 推荐楼盘 -->
        <!-- <view class="loupan"  v-if= "builds.length>0">
            <uni-list>
                <uni-list-item className="title" title="推荐楼盘" :show-badge="false"  ></uni-list-item>  
               <new-house :listsData="builds" :infoList="infoList"></new-house>
            </uni-list>
        </view> 
    </view>-->
    <!-- <view v-if ="topicSkin!=1"> -->
        <!-- <web-view :src ="url"></web-view> -->
    <!-- </view> -->
    <chat-tip></chat-tip>
</view>
</template>

<script>
import {wxShare} from '../common/mixin'
// import newHouse from "../components/newHouse.vue"
 import topic from "./components/topic.vue"
// import topicList from "./components/topicList.vue"
// import search from "../components/search.vue"
// import myIcon from "../components/icon.vue"
import {
  uniLoadMore,
  uniList,
  uniListItem
} from '@dcloudio/uni-ui'
import myDialog from "../components/dialog.vue"
import {
    checkAuth,
    formatImg,
    navigateTo
} from "../common/index.js"
export default {
components: {
    // newHouse,
    topic,
    uniLoadMore,
    uniList,
    uniListItem,
    // search,
    myDialog,
    // myIcon,
    // topicList
},
data() {
    return {
        // img:"",
        listsData: [],
        show_dialog: false,
        isshow:false,
        seo:{},
        builds:[],
        lists:[],
        url:'',
        params:{
            page:1,
            rows:20
        },
        get_status: "loading",
        content_text: {
            contentdown: "",
            contentrefresh: "正在加载...",
            contentnomore: "没有更多数据了"
        },
    }
    },
mixins: [wxShare],
onLoad(options) {
    this. getData()
},
filters: {
    imgUrl(val, param = "") {
        return formatImg(val, param)
    }
},


methods: {
    getData() {
        if (this.params.page ==1) this.lists=[]
        this.get_status = "loading"
        this.$ajax.get('topic/lists', this.params, (res) => {
            // #ifdef H5 || MP-BAIDU
            if (res.data.seo) {
                let seo = res.data.seo
                if (res.data.share&&res.data.share.pic) {
                    seo.image = formatImg(res.data.share.pic, 'w_8001')
                }
                this.seo = seo
            }
            // #endif
            if (res.data.code == 1) {
                if (res.data.listUrl){
                    this.url =res.data.listUrl
                    let urlArr = res.data.listUrl.split("?")
                    let ids=[],url='';
                    if (urlArr[1]){
                        ids =urlArr[1].split("=")
                        url =urlArr[0]+"@@@"+ids[0]+"@@"+ids[1] 
                    }else {
                        url= this.url
                    }
                    
                    uni.redirectTo({
                        url:'/pages/web_view/web_view?url='+encodeURIComponent(url)
                    })
                    return 
                }
                if (res.data.share) {
                    this.share = res.data.share
                }else{
                    this.share={
                        title:"专题列表",
                        content:'专题列表',
                        pic:''
                    }
                }
                this.getWxConfig()
                this.lists=this.lists.concat(res.data.list.data)
                    if (res.data.list.data.length < this.params.rows) {
                        this.get_status = "noMore"
                    } else {
                        this.get_status = "more"
                    }
            } else {
                this.get_status = "noMore"
            }
        }, (err) => {
            console.log(err)
        })
    },
    
},
onReachBottom(){
    if ( this.get_status == "more"){
        this.params.page++
        this.getData()
    }
},
onShareAppMessage() {
    if (this.share) {
        return {
        title: this.share.title||"",
        content:this.share.content||"",
        imageUrl: this.share.pic?formatImg(this.share.pic, 'w_6401'):""
        }
    }
}
}
</script>

<style lang="scss">
.swiper-box {
    position: relative;
}
.topic{
    background: #fff;
    overflow-y: auto;
    padding: 0 26upx;
}
.topic .swiper-box image{
    width: 100%;
}
.loupan{
    padding: 10upx;
}
.top_header{
    height: 336upx;
    width: 100%;
    overflow: hidden;
    position:relative;
    .img{
        width: 100%;
        height: 100%;
        image{
            width: 100%;
            height: 100%;
        }
    }
}


.v_center{
    position: absolute;
    top: 50%;
    left: 50%;
    display:inline-block;
    transform: translate(-50%);
    z-index:1000;
    color:#ffffff;
}
.v_center.header_title{
    font-size:70upx;
     top:calc(50% - 85upx);
}
.v_center.sub_header_title{
    top:calc(50% + 10upx );
}
</style>
