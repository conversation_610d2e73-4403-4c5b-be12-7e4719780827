<template>
  <view class="content share">
    <view class="search-title flex-box">
      <view class="input-box flex-box flex-1">
        <view class="flex-box select right-line" @click="showActionType">
          <text>{{ name }}</text>
          <my-icon type="ic_open" size="32rpx"></my-icon>
        </view>
        <view class="inp-box-def search-box flex-1">
          <input
            type="text"
            confirm-type="search"
            @input="handelInput"
            @confirm="handelSearch"
            :value="params.keywords"
            :maxlength="20"
            placeholder-style="font-size:28rpx"
            :placeholder="placeholder"
          />
        </view>
      </view>
      <view class="c-right search-btn" @click="showActionShare">
        <text>{{ shareName }}</text>
        <my-icon type="ic_open" size="32rpx"></my-icon>
      </view>
    </view>
    <swiper v-if="adv.length>0" class="ext_swiper" circular autoplay :interval="3000" indicator-dots indicator-color="rgba(208,208,206,1.000)" indicator-active-color="rgba(255, 101, 107)">
      <swiper-item v-for="(item, index) in adv" :key="index" @click="toLink(item.link)">
        <image :src="item.image | imageFilter('w_8601')" mode="aspectFill"></image>
        <view class="marker" v-if="item.is_show_label==1">广告</view>
      </swiper-item>
    </swiper>
    <view class="tab-list flex-row bottom-line" :style="{ top: advTop }">
      <!-- <view class="tab-item active" @click="switchTab('share_list')">楼市圈</view>
      <view class="tab-item" @click="switchTab('news_list')">楼盘动态</view>
      <view class="tab-item" @click="switchTab('community_photos')" v-if="switch_community_expert===1">小区专家</view>
      <view class="tab-item" @click="switchTab('news_loushi')" v-if="switch_community_expert===0">楼市资讯</view>  -->
      <view  class="tab-item" v-for="(item,index) in navs" :class="{'active':float==index} "  :key="index+1">  
               <navigator :url="item.path"> {{ item.label }}</navigator>
      </view>
    </view>
    <view class="lists-box" id="moments">
      <view class="cate_list">
        <view class="cate_item" :class="{active: params.type === item.id}" v-for="item in type_list" :key="item.id" @click="onSelectType(item.id)">{{item.title}}({{item.count}})</view>
        <view class="cate_item" :class="{active: params.cate_id === item.id}" v-for="item in label_list" :key="item.id+'_'" @click="onSelectLabel(item.id)">{{item.title}}({{item.count}})</view>
      </view>
      <view v-for="(share, index) in posts" :key="share.id">
        <view class="adviser-box flex-row" @click="consuDetail(share.adviser_id)">
          <image
            class="prelogo"
            :src="share.prelogo | imageFilter('w_80')"
            @click="consuDetail(share.adviser_id)"
          ></image>
          <view class="adviser-info" @click.prevent.stop="consuDetail(share.adviser_id)">
            <view class="name flex-row">
              <text class="text">{{ share.cname }}</text>
              <image v-if="share.adviser_levelid" class="level_icon" :src="share.adviser_levelid | levelIcon"></image>
            </view>
            <view class="level">{{ share.adviser_level_name||share.verified_name||'' }}</view>
          </view>
          <view class="btn-list flex-row" v-if="share.adviser_id">
            <view class="btn chat" @click.prevent.stop="handleAsk(share.uid, share.adviser_id)">微聊</view>
            <view class="btn tel" @click.prevent.stop="handleTel(share)">电话咨询</view>
          </view>
        </view>
        <share-item @click="toCommentDetail" @clickvoice="onClickVoice" @voiceEnded="voice_playing_index=-1" @voiceError="voice_playing_index=-1" @clickpraise="handlePraise($event, index)" :share="share" :voice_playing="voice_playing_index==index">
          <!-- <template v-slot:voice="{shareData, voice}">
            <view class="voice felx-1" @click="playVoice(voice.path)">
              <image class="play_vioce_icon" :src="voice_playing?'/static/icon/voice/play_voice_black.gif':'/static/icon/voice/voice_icon_black.png'"></image>
              <text>{{parseInt(voice.duration/1000)}}''</text>
            </view>
          </template> -->
        </share-item>
      </view>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
    <view class="flex-box bottom-bar">
      <view class="bottom-bar-left flex-box">
        <view class="icon-btn text-center" @click="showSharePop">
          <my-icon type="ic_fenxiang" color="#333333" size="36upx"></my-icon>
          <view>分享</view>
        </view>
      </view>
      <!-- <view class="flex-box text-center bottom-bar-right"> -->
      <!-- <my-icon type="release" size="22" color="#ffffff"></my-icon> -->
      <view class="text-center flex-1 to-baoming tel-phone" @click="addConsultant">申请认证</view>
      <view class="to-baoming flex-1 text-center submit-baoming" @click="addPost">发布动态</view>
      <!-- </view> -->
    </view>
    <!-- <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more> -->
    <chat-tip></chat-tip>
    <share-pop
      ref="show_share_pop"
      @copyLink="copyLink"
      @appShare="appShare"
      :showHaibao="false"
      @showCopywriting="showCopywriting"
    ></share-pop>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import shareItem from '../../components/shareItem.vue'
import myIcon from '../../components/myIcon.vue'
import getChatInfo from '../../common/get_chat_info'
import { uniLoadMore } from '@dcloudio/uni-ui'
// #ifdef H5
import {formatImg, showModal } from '../../common/index.js'
// #endif
// #ifndef H5
import {formatImg, showModal, config } from '../../common/index.js'
// #endif
import allTel from '../../common/all_tel.js'
import sharePop from '../../components/sharePop.vue'
export default {
  data() {
    return {
      params: {
        page: 1,
        cate: 1,
        type: 0,
        cate_id: 0,
        sort: 1,
        keywords: ''
      },
      voice_playing_index: -1,
      type_list:[],
      label_list:[],
      placeholder: '请输入楼盘名称搜索',
      get_status: 'loading',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      },
      options: ['楼盘', '置业顾问'],
      shareOptions: ['最新分享', '最热分享'],
      name: '楼盘',
      shareName: '最新分享',
      posts: [],
      nickname: '',
      buildid: '',
      index: '',
      adv: [],
      advInfo: {},
      tel_res: {},
      float:0,
      navs:[],
      show_tel_pop: false
    }
  },
  components: {
    uniLoadMore,
    myIcon,
    shareItem,
    sharePop
  },
  mounted() {
    uni.getStorage({
      key: 'posts',
      success: function(res) {
        this.posts = res.data
      }
    })
  },
  computed: {
    imconsu() {
      return this.$store.state.im.adviser
    },
    imchat() {
      return this.$store.state.im.ischat
    },
    // istelcall
    istelcall() {
      return this.$store.state.im.istelcall
    },
    switch_community_expert() {
      return this.$store.state.switch_community_expert
    },
    advTop() {
      // #ifndef H5
      return 0
      // #endif
      // #ifdef H5
      return '44px'
      // #endif
    }
  },
  onLoad(options) {
    this.getData()
    this.handleBudiling()
    uni.$on('addreply', data => {
      if (this.to_detail_index !== undefined && this.to_detail_index !== null) {
        this.posts[this.to_detail_index].reply.push(data)
      }
      this.to_detail_index = null
    })
    uni.$on('getDataAgain', this.getDataAgain)
  },
  onUnload() {
    uni.$off('addreply')
    uni.$off('getDataAgain')
    this.$store.state.allowOpen = true
  },
  onShow() {

    if (this.$store.state.updatePageData) {
      this.params.page == 1
      this.getData()
      this.$store.state.updatePageData = false
    }
  },
  onHide(){
    // this.voice_playing_index = -1
  },
  onReachBottom() {
    if(this.get_status!=='more'){
      return
    }
    //监听上拉触底事件
    this.params.page++
    this.getData()
  },
  onPullDownRefresh() {
    //监听下拉刷新动作
    this.params.page = 1
    this.getData()
  },
  filters: {
    imgUrl(img, param = '') {
      return formatImg(img, param)
    },
    levelIcon(val) {
      if (!val) {
        return ''
      }
      switch (val) {
        case 1:
          return formatImg('/images/new_icon/<EMAIL>', 'm_80')
        case 2:
          return formatImg('/images/new_icon/<EMAIL>', 'm_80')
        case 3:
          return formatImg('/images/new_icon/<EMAIL>', 'm_80')
        default :
          return ''
      }
    }
  },
  methods: {
    handleBudiling(){
          console.log("222244444444")
          // 获取楼市去信息
          this.$ajax.get('building_circle/navs', {},res=>{
              console.log(res)
              if(res.data.code==1){
                this.navs = res.data.navs
              }
          })
    },
    handelInput(e) {
      this.params.keywords = e.detail.value
    },
    toCommentDetail(id) {
      this.$navigateTo(`/pages/community/detail?id=${id}`)
    },

    addConsultant() {
      this.checkStatus(()=>{
        if(this.advInfo.adviser_id){
          this.consuDetail(this.advInfo.adviser_id)
        }else{
          uni.showToast({
            title: "当前账号已认证",
            icon: 'none'
          })
        }
      })
    },
    handelSearch() {
      this.posts = []
      this.params.page = 1
      // this.sort = 1   //搜索的时候搜最新分享
      this.getData()
    },
    // 搜索选择分类
    showActionType() {
      uni.showActionSheet({
        itemList: this.options,
        success: res => {
          this.name = this.options[res.tapIndex]
          this.params.cate = res.tapIndex + 1
          this.placeholder = '请输入' + this.name + '名称搜索'
        },
        fail: res => {
          console.log(res.errMsg)
        }
      })
    },
    // 选择排序
    showActionShare() {
      this.shareStyle = true
      uni.showActionSheet({
        itemList: this.shareOptions,
        success: res => {
          this.shareName = this.shareOptions[res.tapIndex]
          this.params.sort = res.tapIndex + 1
          //  this.builds=[];
          this.params.page = 1
          this.params.keywords = ''
          this.get_status = 'more'
          this.getData()
        },
        fail: res => {
          console.log(res.errMsg)
        }
      })
    },
    onSelectType(type){
      if(this.params.type === type){
        return
      }
      if(!type){
        this.params.cate_id = 0
      }
      this.params.type = type
      this.params.page = 1
      this.getData()
    },
    onSelectLabel(cate_id){
      if(this.params.type===0){
        this.params.type = ""
      }
      if(this.params.cate_id === cate_id){
        this.params.cate_id = 0
      }else{
        this.params.cate_id = cate_id
      }
      this.params.page = 1
      this.getData()
    },
    //转到顾问详情
    consuDetail(id) {
      if (id == 0) return
      this.$navigateTo('/pages/consultant/detail?id=' + id)
    },
    switchTab(type,index) {
      if (type === '/pages/community/community' && this.posts.length === 0) {
        this.params.page = 1
        this.getData()
      }
      if (type === '/pages/community/news_list') {
        this.$navigateTo('/pages/community/news_list')
      }
      if (type === '/pages/house_price/photos') {
        this.$navigateTo('/pages/house_price/photos')
      }
      if (type === '/pages/ershou/ershou') {
        this.$navigateTo('/pages/ershou/ershou')
      }
    },
    //聊天
    handleAsk(id, ids) {
      //uid advId
      if (this.imchat == 1) {
        //开聊天
        getChatInfo(id, 29)
      } else if (this.imchat == 0) {
        //不开聊天
        this.consuDetail(ids)
      }
    },
    //id, mid, tel, type, from, source = "", info_id = "", isstatis = 1
    handleTel(adviser_info) {
      this.tel_params = {
        type: 2,
        callee_id: adviser_info.adviser_id,
        scene_type: 2,
        scene_id: adviser_info.adviser_id,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      allTel(this.tel_params)
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    getDataAgain() {
      this.params.page = 1
      this.getData()
    },
    getData() {
      if (this.params.page == 1) {
        this.posts = []
      }
      this.get_status = 'loading'

      this.$ajax.get('building_circle/lists', this.params, res => {
        if(res.data.share&&res.data.share.title){
          this.share = res.data.share
          this.getWxConfig()
        }
        if(res.data.navigationBarTitle){
          uni.setNavigationBarTitle({
            title:`${res.data.navigationBarTitle}`
          })
        }else {
          uni.setNavigationBarTitle({
            title:"楼市圈"
          })
        }
        if (res.data.popup) {
          this.popup = res.data.popup
          setTimeout(() => {
            this.$refs.hb.showHb()
          }, 500)
        }
        if(res.data.verfiyCode!==undefined){
          this.verfiyCode = res.data.verfiyCode
          this.verfiyMsg = res.data.verfiyMsg
        }
        if (res.data.member) {
          this.advInfo = res.data.member
        }
        this.buildid = res.data.member ? res.data.member.build_ids : ''
        if (res.data.code == 0||res.data.lists.length == 0) {
          this.get_status = 'noMore'
          if (this.params.page < 1) {
            this.params.page = 1
          }
          return
        }
        if (res.data.adv&&res.data.adv.adv) {
          this.adv = res.data.adv.adv
        }
        if(res.data.types){
          this.type_list = res.data.types
        }
        if(res.data.cates){
          this.label_list = res.data.cates
        }
        // this.adv=[{
        // 	image: "https://images.tengfangyun.com/attachment/other/20190928/53ffb262062da7d51b3010805a13d8427e42549c.jpeg",
        // 	link: "/pages/ershou/ershou"
        // }]
        uni.stopPullDownRefresh()
        this.posts = this.posts.concat(res.data.lists)
        this.get_status = 'more'
      })
    },
    showSharePop() {
      this.$refs.show_share_pop.show()
    },
    showCopywriting() {
      let link = ''
      // #ifdef H5
      link = window.location.href
      // #endif
      // #ifndef H5
      link = config.apiDomain + '/h5/pages/groups/detail?id=' + this.id
      // #endif
      const text = `【楼市圈】${this.share.title}
【访问链接】${link}`
      this.copyWechatNum(text, () => {
        this.copy_success = true
      })
    },
    // #ifdef H5
    copyLink() {
      let link = window.location.href
      this.copyWechatNum(link, () => {
        uni.showToast({
          title: '复制成功,去发送给好友吧',
          icon: 'none'
        })
      })
    },
    // #endif
    // #ifndef H5
    copyWechatNum(cont) {
      uni.setClipboardData({
        data: cont,
        success: res => {
          // uni.showToast({
          //   title: "复制成功",
          //   icon: "none"
          // })
        }
      })
    },
    // #endif
    // #ifdef H5
    copyWechatNum(cont, callback) {
      let oInput = document.createElement('textarea')
      oInput.value = cont
      document.body.appendChild(oInput)
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length)
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      oInput.blur()
      oInput.remove()
      if (callback) callback()
    },
    // #endif

    toLink(url) {
      this.$navigateTo(url)
    },
    showTips() {
      showModal({
        title: '免责声明',
        content:
          '  本页面内容，旨在满足广大用户的信息需求而采集提供，如有异议请及时联系我们，本页面内容不代表本网站观点或意见，仅供用户参考以借鉴',
        showCancel: false,
        confirmText: '我知道了'
      })
    },
    toDetail(id, index) {
      // 如果type是2则跳转到新闻资讯详情
      if (this.posts[index].type == 2) {
        this.$navigateTo('/pages/news/detail?id=' + id + '&title=' + this.posts[index].title)
      } else {
        this.to_detail_index = index
        this.$store.state.tempData = this.posts[index]
        this.$navigateTo('/pages/community/detail?id=' + id)
      }
    },
    handlePraise(e, index){
      this.$ajax.post('building_circle/praise',{id: e.id, value: e.ispraise?0:1},res=>{
        if(res.data.code == 1){
          if (e.ispraise === 0) {
            this.posts[index].ispraise = 1
            this.posts[index].praise_count+=1
          } else {
            this.posts[index].ispraise = 0
            this.posts[index].praise_count-=1
          }
        }
        uni.showToast({
          title:res.data.msg,
          icon:'none'
        })
      })
    },
    onClickVoice(src){
      // 判断点击的哪个语音
      var voice_playing_index = this.posts.findIndex(item=>item.attached&&item.attached.length>0&&item.attached[0].path == src)
      if(this.voice_playing_index === voice_playing_index){
        this.voice_playing_index = -1
      }else{
        this.voice_playing_index = voice_playing_index
      }
    },
    delPost(id, index) {
      //删除帖子
      showModal({
        title: '提示',
        content: '确定要删除吗',
        confirm: () => {
          this.$ajax.get('news/deletedCommunityReply', { id }, res => {
            if (res.data.code == 1) {
              this.posts.splice(index, 1)
            }
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
          })
        }
      })
    },
    addPost() {
      this.checkStatus(()=>{
        this.$navigateTo('/user/consultant/addpost?buildid=' + (this.buildid||''))
      })
    },
    checkStatus(callback){
      switch (this.verfiyCode){
        case -1 || -9:
          this.$navigateTo('/user/login/login')
          break
        case 0:
          uni.showToast({
            title: this.verfiyMsg||'',
            icon: 'none'
          })
          break
        case 1:
          callback&&callback()
          break
        case 2:
          this.$navigateTo('/user/bind_phone/bind_phone')
          break
        case 3:
          uni.showToast({
            title: this.verfiyMsg||'',
            icon: 'none'
          })
          showModal({
            title: '提示',
            content: this.verfiyMsg ||'你还不是楼市圈会，是否去认证？',
            confirmText: '去认证',
            confirm: ()=>{
              this.$navigateTo('/user/community_auth')
            }
          })
          break
        default:
          uni.showToast({
            title: this.verfiyMsg||'',
            icon: 'none'
          })
      }
    }
  },
  onShareAppMessage() {
    if (this.share) {
      return {
        title: this.share.title,
        // #ifdef MP-BAIDU
        content: this.share.content,
        // #endif
        imageUrl: this.share.pic ? formatImg(this.share.pic, 'w_6401') : ''
      }
    }
  }
}
</script>

<style lang="scss">
.share {
  background: #fff;
}
.top {
  top: var(--window-top);
  position: fixed;
  width: 100%;
  z-index: 1;
}
.select {
  line-height: 60upx;
  margin-left: 10upx;
}
.looknum {
  color: #757575;
  font-size: 22rpx;
}
.ding {
  font-size: $uni-font-size-sm;
  border-radius: 4upx;
  margin-left: 10upx;
  padding: 1upx 10upx;
  color: #f40;
  background-color: #ffda77;
}
.add-post {
  position: fixed;
  right: 36upx;
  bottom: 180upx;
  height: 90upx;
  width: 90upx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.9);
  z-index: 96;
}
.title-box {
  align-items: center;
  justify-content: space-between;
}
.title-box image {
  width: 38upx;
  height: 38upx;
  margin-left: 10upx;
}
.title-box .nickname {
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.title-box .nickname .title {
  flex: 3;
  // display: flex;
  // justify-content: flex-start;
  // flex-direction: column;
}
.title-box .nickname .title .time {
  margin-top: 10upx;
  font-size: 24upx;
}
.title-box .nickname .concat {
  flex: 1;
  justify-content: space-between;
  align-items: center;
}
.title-box .nickname .concat .tel,
.title-box .nickname .concat .zixun {
  background: #eaf3fd;
  padding: 10upx;
  border-radius: 50%;
}
.look-more {
  text-align: center;
  padding: 10upx;
  color: #36648b;
}
.re_con {
  color: #333;
}

/* #ifdef H5 */
.search-title {
  width: 100%;
  padding: 15rpx 48rpx;
  box-sizing: border-box;
  // position: fixed;
  // top: 44px;
  z-index: 99;
  background-color: #fff;
}
/* #endif */
/* #ifndef H5 */
.search-title {
  width: 100%;
  padding: 15rpx 48rpx;
  box-sizing: border-box;
  // position: fixed;
  // top: var(--window-top);
  z-index: 99;
  background-color: #fff;
  border-bottom: 1upx solid $uni-border-color;
  box-shadow: 0 0 26upx #dedede;
}
/* #endif */
.input-box {
  align-items: center;
  padding: 0 24rpx;
  background-color: #eee;
  border-radius: 8rpx;
}
.select {
  align-items: center;
  line-height: 32upx;
  margin: 16rpx 0;
  padding-right: 15upx;
}
.search-box {
  flex: 1;
}
.search-btn {
  line-height: 60upx;
  padding-left: 15upx;
  color: #666;
}
.search-box input {
  height: 100%;
  padding: 6rpx 10rpx;
  box-sizing: border-box;
  font-size: $uni-font-size-base;
}
.banner-box {
  padding: 32upx 48upx;
  .banner-item {
    border-radius: 8upx;
    overflow: hidden;
  }
}
.tag_map {
  margin: 0 3px;
  color: #999999;
  font-size: 24upx;
  line-height: 1;
  padding: 4px;
}
.toolbars {
  min-height: 40upx;
}
.timestamp {
  font-size: 24upx;
  color: #999;
}
.nickimg {
  width: 50upx;
  height: 50upx;
  border-radius: 50%;
  margin-left: -16upx;
}
.nickimg:nth-of-type(1) {
  margin-left: 0upx;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.tab-list {
  padding: 0 48rpx;
  justify-content: space-between;
  position: sticky;
  background-color: #fff;
  z-index: 2;
  .tab-item {
    flex: 1;
    padding: 24rpx;
    text-align: center;
    position: relative;
    color: #666;
    &.active {
      color: $uni-color-primary;
      &::after {
        content: '';
        height: 8rpx;
        border-radius: 4rpx;
        background-color: $uni-color-primary;
        position: absolute;
        bottom: 0;
        width: 48rpx;
        left: 0;
        right: 0;
        margin: auto;
      }
    }
  }
}
.lists-box {
  padding: 24rpx 48rpx;
  padding-bottom: 120rpx;
  .adviser-box {
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
    margin-bottom: 24rpx;
    .prelogo {
      margin-right: 24rpx;
      width: 54rpx;
      height: 54rpx;
      border-radius: 27rpx;
      background-color: #f5f5f5;
    }
    .adviser-info {
      flex: 1;
      overflow: hidden;
      line-height: 1;
      .name {
        align-items: center;
        font-size: 26rpx;
        margin-bottom: 16rpx;
        .text {
          margin-right: 16rpx;
          display: inline-block;
          max-width: 180rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .level_icon {
        width: 28rpx;
        height: 28rpx;
      }
      .level {
        font-size: 24rpx;
        color: #999;
      }
    }
    .btn-list {
      line-height: 1;
      .btn {
        margin-left: 24rpx;
        padding: 16rpx 16rpx;
        min-width: 108rpx;
        text-align: center;
        color: $uni-color-primary;
        border: 1rpx solid $uni-color-primary;
        border-radius: 8rpx;
        box-shadow: 0 2px 4px 0 rgba(251, 101, 106, 0.1);
      }
    }
  }
}

.bottom-bar {
  background-color: #fff;
  height: 110upx;
  padding: 15upx 48upx;
  justify-content: space-between;
  box-sizing: border-box;
  .icon-btn {
    line-height: initial;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // flex: 1;
    padding-right: 32rpx;
    overflow: hidden;
    position: relative;
  }
}
.bottom-bar-right {
  flex: 1;
  align-items: center;
  justify-content: space-between;
}
.bottom-bar-left {
  flex: 1;
  // justify-content: space-between;
  // padding-right: 40upx;
}
.to-baoming {
  align-items: center;
  justify-content: center;
  color: #fff;
  line-height: 80upx;
  background-color: $uni-color-primary;
  flex: 1;
  font-size: 28upx;
}
.submit-baoming {
  background: linear-gradient(90deg, #fb656a 0%, #fbac65 100%);
  box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.3);
  border-top-right-radius: 19px;
  border-bottom-right-radius: 19px;
}
.tel-phone {
  background: #fbac65;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.05);
  border-top-left-radius: 19px;
  border-bottom-left-radius: 19px;
}


.cate_list{
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 24rpx;
  .cate_item{
    padding: 6rpx 16rpx;
    font-size: 24rpx;
    border-radius: 4rpx;
    margin: 12rpx;
    background-color: #f8f8f8;
    color: #999;
    &.active{
      background-color: rgba($color: $uni-color-primary, $alpha: 0.15);
      color: $uni-color-primary;
    }
  }
}

// .voice{
//   display: flex;
//   flex-direction: row;
//   align-items: center;
//   padding: 20rpx 24rpx;
//   border-radius: 12rpx;
//   width: 100%;
//   margin-right: 64rpx;
//   box-sizing: border-box;
//   background-color: #f8f8f8;
//   color: #333;
//   .play_vioce_icon{
//     margin-right: 16rpx;
//     width: 40rpx;
//     height: 40rpx;
//   }
// }
// 轮播广告图
.ext_swiper{
  width: 100%;
  padding: 24rpx;
  height: 248rpx;
  box-sizing: border-box;
  swiper-item{
    height: 100%;
    background-color: #f5f5f5;
    border-radius:8rpx;
    overflow: hidden;
    position: relative;
    >image{
      height: 100%;
      width: 100%;
    }
    .marker{
      line-height: 1;
      padding: 4rpx 10rpx;
      position: absolute;
      right: 12rpx;
      bottom: 10rpx;
      font-size: 20rpx;
      border-radius: 4rpx;
      background-color: rgba($color: #000000, $alpha: 0.5);
      color: #fff;
    }
  }
}
</style>
