<template>
    <view class="page">
        <view class="page_top">
            <view class="page_center">
                <text @click="handleSecond()" :class="{ 'active': flag == 0 }">二手房</text>
                <text @click="handleNew()" :class="{ 'active': flag == 1 }">新房</text>
            </view>
            <view :class="{ 'page_bottom': flag == 0, 'page_bottomtwo': flag == 1 }">

            </view>
        </view>
        <view class="search_top" v-show="flag == 0">
            <view class="home_top">
                <image src="../../static/icon/home.png"   style="height: 500rpx;width: 500rpx;"></image>
                <view v-for="(item, index) in  secondHouseList" :key="item.value" style="position: absolute;" class="cantioner">
                    <view  :class="labIndex==item.value?`bulehome  home${item.one}`:`home home${item.one}`" @click="handleHome(item)">
                        <text class="tex">
                            {{ item.title }}
                        </text>
                    </view>
                </view>
                <view v-for="(item,index) in  secondHouseHome" :key="item.value" style="position: absolute;" class="cantionerto">
                    <view  :class="labIndex==item.value?`bulehometo  home${item.one}`:`hometo home${item.one}`" @click="handleHome(item)">
                        <text class="tex">
                            {{ item.title }}
                        </text>
                    </view>
                </view>
            </view>
            <view class="footer">
                <view class="footer_top">
                    <text>{{ index+1}}</text>
                    <text>{{handlelist[index].title}}</text>
                </view>
                <view class="footer_bottom">
                  {{  handlelist[index].content }}
                </view>
            </view>
            
        </view>
        <view class="search_top" v-show="flag == 1">
            <view class="home_top">
                <image src="../../static/icon/home.png" style="height: 500rpx;width: 500rpx;"></image>
                <view v-for="(item, index) in newHouseList" :key="item.value" style="position: absolute;" class="cantioner">
                    <view  :class="labIndexto==item.value?`bulehome  home${item.one}`:`home home${item.one}`" @click="handleHometo(item)">
                        <text class="tex">
                            {{ item.title }}
                        </text>
                    </view>
                </view>
                <view v-for="(item,index) in   newHouseHome" :key="item.value" style="position: absolute;" class="cantionerto">
                    <view  :class="labIndexto==item.value?`bulehometo  home${item.one}`:`hometo home${item.one}`" @click="handleHometo(item)">
                        <text class="tex">
                            {{ item.title }}
                        </text>
                    </view>
                </view>
            </view>
            <view class="footer">
                <view class="footer_top">
                    <text>{{active+1}}</text>
                    <text>{{ handlelistto[active].title }}</text>
                </view>
                <view class="footer_bottom">
                        {{  handlelistto[active].content }}
                </view>
            </view>
        </view>

    </view>
</template>

<script>
import { uniIcons } from "@dcloudio/uni-ui";
import myIcon from "@/components/myIcon";
export default {
    data() {
        return {
            flag: 0,
            retive: true,
            labIndex:0,
            labIndexto:0,
            list:[],
            index:0,
            handlelist:[],
            active:0,
            handlelistto:[],
            secondHouseHome: [
                {
                    value: 6,
                    one:"seven",
                },
                {
                    value: 7,   
                    one:"eight"             
                },
                {
                    value:8, 
                    one:"ninew"            
                },
                {
                    value: 9,
                    one:"tenw"                 
                },
                {
                    value: 10,
                    one:"eleven"
                },
                {
                    value: 11,
                    one:"towever"
                   
                }
            ],
            secondHouseList: [
                {
                    value: 0,
                    one:"one"
                },
                {
                    value: 1,
                    // title: "确定房源",
                    one:"two"
                  
                },
                {
                    value: 2,
                    // title: "签约合同",
                    one:"three" 
                },
                {
                    value: 3,
                    // title: "看房接待",
                   one:"four"
                   
                },
                {
                    value: 4,
                    // title: "审核及签约",
                    one:"five"    
                },
                {
                    value: 5,
                    // title: "定金",
                    one:"six" 
                },
            ],
            newHouseList:[
            {
                    value: 0,
                    title: "买房准备",
                    one:"newone"
                },
                {
                    value: 1,
                    title: "看房选房",
                    one:"newtwo"
                  
                },
                {
                    value: 2,
                    title: "验资",
                    one:"newthree" 
                },
                {
                    value: 3,
                    title: "认筹",
                   one:"newfour"
                   
                },
                {
                    value: 4,
                    title: "认购",
                    one:"newfive"    
                },
            ],
            newHouseHome:[
                     {
                    value: 5,
                    title: "办理产证",
                    one:"newsix"
                },
                {
                    value: 6,
                    title: "验房收房",
                    one:"newseven"
                  
                },
                {
                    value: 7,
                    title: "缴税",
                    one:"neweight"
                  
                },
                {
                    value: 8,
                    title: "办理贷款",
                    one:"newnine"
                    
                },
                {
                    value: 9,
                    title: "签约",
                    one:"newten"
                },
            ]
        }

    },
    components: {
        myIcon,
        uniIcons,
        // circle,
    },
    methods: {
        // 二手房
        handleSecond() {
            this.flag = 0
        },
        // 新房
        handleNew() {
            this.flag = 1
        },
        handleHome(val){
            console.log(val);
            this.labIndex=val.value
            this.index = val.value
        },
        handleHometo(val){
            console.log(val);
            this.labIndexto=val.value
            this.active = val.value
        },
        // 查看房子
        getlisthome(){
            this.$ajax.get('fangjia/transactionServices',{},res=>{
                console.log(res)
                if(res.data.code==1){
                        // let ranm = []
                        this.handlelist = res.data.esfServices  
                        this.handlelistto = res.data.newServices
                        // console.log(  res.data.esfServices  )
                      console.log(this.handlelist)
                    for(let i=0;i<6;i++){
                        this.secondHouseList[i].title= res.data.esfServices[i].title
                        this.secondHouseList.splice(0,0)
                    }
                    for( let i=0;i<5;i++){
                        this.newHouseList[i].title = res.data.newServices[i].title
                        this.newHouseList.splice(0,0)
                    }
                     this.secondHouseHome[0].title = res.data.esfServices[6].title
                     this.secondHouseHome[1].title = res.data.esfServices[7].title
                     this.secondHouseHome[2].title = res.data.esfServices[8].title
                     this.secondHouseHome[3].title = res.data.esfServices[9].title
                     this.secondHouseHome[4].title = res.data.esfServices[10].title
                     this.secondHouseHome[5].title = res.data.esfServices[11].title
                     this.newHouseHome[0].title = res.data.newServices[5].title
                     this.newHouseHome[1].title = res.data.newServices[6].title
                     this.newHouseHome[2].title = res.data.newServices[7].title
                     this.newHouseHome[3].title = res.data.newServices[8].title
                     this.newHouseHome[4].title = res.data.newServices[9].title
                }
            })
        }
    },
    onLoad(){
        this.getlisthome()
    },
}
</script>

<style scoped lang="scss">
body {
    background-color: white !important;
}
.homeninew{
    position: relative;
    top: 146rpx;
    left: 260rpx;
    display: flex;
    align-items: center; 
    .tex{
        margin-left: 62rpx;
    }
}
.hometenw{
    position: relative;
    top: 250rpx;
    left: 260rpx;
    display: flex;
    align-items: center; 
    .tex{
        margin-left: 62rpx;
    }
}
.home,.bulehome {
    width: 190rpx;
    height: 56rpx;
    background-image: url('../../static/icon/number.png');
    background-size: 100% 100%;
    line-height: 56rpx;
    .tex {
        // margin-left: 52rpx;
        color: #000000;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 24rpx;
        line-height: normal;
        display: block;
    }
}
.bulehome {
        background-image: url('../../static/icon/numbertwo.png');

    .tex {
        // margin-left: 52rpx;
        color: #fff;
    }
}
.hometo,.bulehometo{
    width: 190rpx;
    height: 56rpx;
    background-image: url('../../static/icon/numberone.png');
    background-size: 100% 100%;
    line-height: 56rpx;

    .tex {
        // margin-left: 52rpx;
        color: #000000;
        font-family: PingFang SC;
        font-weight: medium;
        font-size: 24rpx;
        line-height: normal;
        display: block;
    }
}

.bulehometo {
    background-image: url('../../static/icon/numberthree.png');
    .tex {
        color: #fff;
    }
}
.homenewone{
    margin-right: 520rpx;
    margin-top: 422rpx;
    display: flex;
    align-items: center; 
    text{
        margin-left: 30rpx;
    }
}
.homenewtwo{
    margin-right: 520rpx;
    margin-top: 270rpx;
    display: flex;
    align-items: center; 
    text{
        margin-left: 30rpx;
    }
}
.homenewthree{
    margin-right: 520rpx;
    margin-top: 154rpx;
    display: flex;
    align-items: center; 
    text{
        margin-left: 52rpx;
    }
}
.homenewfour{
    margin-right: 400rpx;
    margin-top: 50rpx;
    display: flex;
    align-items: center; 
    text{
        margin-left: 52rpx;
    }
}
.homenewfive{
    margin-right: 246rpx;
    margin-top: -28rpx;
    display: flex;
    align-items: center; 
    text{
        margin-left: 52rpx;
    }
}
.homenewsix{
    position: absolute;
    top: -28rpx;
    left: 26rpx;
    display: flex;
    align-items: center; 

    text{
        margin-left: 92rpx;
    }
}
.homenewseven{
    position: absolute;
    top: 50rpx;
    left: 100rpx;
    display: flex;
    align-items: center; 
    text{
        margin-left: 62rpx;
    } 
}
.homeneweight{
    position: absolute;
    top: 154rpx;
    left: 160rpx;
    display: flex;
    align-items: center; 
    text{
        margin-left: 92rpx;
    }
}
.homenewnine{
    position: absolute;
    top: 270rpx;
    left: 160rpx;
    display: flex;
    align-items: center; 
    text{
        margin-left: 62rpx;
    }
}
.homenewten{
    position: absolute;
    top: 422rpx;
    left: 160rpx;
    display: flex;
    align-items: center; 
    text{
        margin-left: 62rpx;
    }
}
 .homeone{
    margin-right: 520rpx;
    margin-top: 422rpx;
    display: flex;
    align-items: center; 
   
    text{
        margin-left: 30rpx;
    }
}
.hometwo{
    margin-right: 520rpx;
    margin-top: 330rpx;
    display: flex;
    align-items: center;
    text{
        margin-left: 30rpx;
    }
}
.homethree{
    margin-right: 520rpx;
    margin-top: 238rpx;
    display: flex;
    align-items: center;
    text{
        margin-left: 30rpx;
    }
}
.homefour{
    margin-right: 520rpx;
    margin-top: 146rpx;
    display: flex;
    align-items: center;
    text{
        margin-left: 30rpx;
    }
}
.homefive{
    margin-right: 430rpx!important;
    margin-top: 54rpx;
    display: flex;
    align-items: center;
    text{
        margin-left: 20rpx;
    }
}
.homesix{
    margin-right: 246rpx!important;
    margin-top: -28rpx;
    display: flex;
    align-items: center;
    text{
        margin-left: 52rpx;
    }
}
.homeseven{
    position: absolute;
    top: -28rpx;
    left: 26rpx;
    display: flex;
    align-items: center; 
    .tex{
        margin-left: 90rpx;
    }
}
.homeeight{
    position: absolute;
    top: 54rpx;
    left: 116rpx;
    display: flex;
    align-items: center; 
    .tex{
        margin-left: 76rpx;
    }
}
.homeeleven{
    position: absolute;
    top: 330rpx;
    left: 160rpx;
    display: flex;
    align-items: center; 
    .tex{
        margin-left: 62rpx;
    }
}
.hometowever{
    position: absolute;
    top: 422rpx;
    left: 160rpx;
    display: flex;
    align-items: center; 
    .tex{
        margin-left: 90rpx;
    }
}
.hometen{
    position: absolute;
    top: 146rpx;
    left: 160rpx;
    display: flex;
    align-items: center; 
    .tex{
        margin-left: 62rpx;
    }
}

.home_top {
    display: flex;
    justify-content: center;
    margin-top: 127rpx;
    width: 100%;
    height: 600rpx;
    position: relative;
}

.footer {
    width: 652rpx;
    height: 500rpx;
    border-radius: 20rpx;
    border: 0.5px solid #DCDCDC;
    margin: 0 auto;
    margin-top: 64rpx;
    padding: 44rpx 0 0 0;
    ;

    .footer_top {
        margin-bottom: 12rpx;

        text:nth-child(1) {
            display: inline-block;
            width: 40rpx;
            height: 40rpx;
            background-color: #2D66F3;
            border-radius: 50%;
            margin-left: 38rpx;
            background-color: 2d66f3;
            color: #ffffff;
            font-size: 34rpx;
            text-align: center;
            line-height: 40rpx;
            margin-right: 6rpx;
        }

        text:nth-child(2) {
            color: #000000;
            font-family: PingFang SC;
            font-weight: medium;
            font-size: 36rpx;
            line-height: normal;

        }
    }

    .footer_bottom {
        width: 562rpx;
        margin-left: 38rpx;
        color: #4E4E4E;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
        line-height: normal;

    }
}

.cust {
    position: absolute;
    top: -80rpx;
    right: -80rpx;
}

.page {
    overflow: hidden;
    min-height: calc(100vh - 90rpx);
    background: white;
    border-top: 1rpx solid rgba(240, 240, 240, 1);

    .btn uni-button {
        background-color: #446EEF !important;
        width: 654rpx !important;
        height: 96rpx;
    }

    .btn {
        margin-top: 88rpx;
    }

    .page_top {
        width: 100%;
        height: 116rpx;
        font-size: 32rpx;
        // line-height: 116rpx;
        border-bottom: 1rpx solid rgba(240, 240, 240, 1);
        ;
    }

    .page_center {
        padding: 36rpx 0 0 0;
        box-sizing: border-box;
    }

    .page_center text:nth-child(1) {
        margin-left: 214rpx;
        color: #979797;
    }

    .page_center text:nth-child(2) {
        margin-left: 130rpx;
        color: #979797;
    }

    .page_bottom {
        background-color: #446eef;
        width: 60rpx;
        height: 8rpx;
        border-radius: 4px;
        margin-top: 10rpx;
        margin-left: 232rpx;
    }

    .page_bottomtwo {
        background-color: #446eef;
        width: 60rpx;
        height: 8rpx;
        border-radius: 4px;
        margin-top: 10rpx;
        margin-left: 442rpx;
    }

    .active {
        color: #232323 !important;
        font-weight: bold !important;
    }
}

.search-box {
    width: 654rpx;
    height: 116rpx;
    // background: red;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid rgba(240, 240, 240, 1);

    .text-a {
        display: flex;
        justify-content: space-between;
        align-items: center;

        view:nth-child(1) {
            font-size: 32rpx;
            color: #3a3a3a;
            margin-right: 20rpx;
        }

        view:nth-child(2) {
            color: #979797;
            font-size: 28rpx;
        }
    }

    .text-b {
        display: flex;
        justify-content: space-between;
        align-items: center;

        view:nth-child(1) {
            font-size: 32rpx;
            color: #3a3a3a;
            margin-right: 12rpx;
        }

        view:nth-child(2) {
            color: #979797;
            font-size: 28rpx;
        }

    }

    .one {
        font-size: 32rpx;
        color: #3a3a3a;
    }

    .two {
        font-size: 28rpx;
        color: #979797;
    }
}

view:nth-child(1) {
    color: #232323;
    font-size: 32rpx;
}

.uni-icons {
    color: #979797;
    width: 24rpx;
    height: 24rpx;
}

.bold {
    font-weight: bold;
}

.search-box-title .uni-input-form {
    width: 10rpx !important;
}

.search-box-title .uni-input-input {
    width: 10rpx !important;
}

.echart {
    width: 240rpx;
    height: 240rpx;
    background-color: #f3f3f3;
    margin: 0 auto;
    margin-top: 48rpx;
    border-radius: 50%;
    position: relative;
}

.footer-bottom {
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin-top: 20rpx;
}

.footer-left {
    margin-top: 28rpx;

    text:nth-child(1) {
        color: #979797;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
        margin-right: 20rpx;
        margin-left: 236rpx;
    }

    text:nth-child(2) {
        color: #3A3A3A;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
    }
}

.footer-radious {
    margin-top: 28rpx;

    text:nth-child(1) {
        width: 18rpx;
        height: 18rpx;
        display: inline-block;
        border-radius: 50%;
        margin-left: 256rpx;
        margin-right: 18rpx;
    }

    text:nth-child(2) {
        color: #979797;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
        margin-right: 20rpx;
    }
}

.footer-one {
    margin-top: 28rpx;

    text:nth-child(1) {
        margin-left: 178rpx;
        width: 18rpx;
        height: 18rpx;
        border-radius: 50%;
        display: inline-block;
        margin-right: 12rpx;
    }

    text:nth-child(2) {
        color: #979797;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
        margin-right: 20rpx;
    }

    text:nth-child(3) {
        color: #3A3A3A;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
    }
}

.footer-two {
    margin-top: 28rpx;

    text:nth-child(1) {
        margin-left: 236rpx;
        width: 18rpx;
        height: 18rpx;
        border-radius: 50%;
        display: inline-block;
        margin-right: 12rpx;
    }

    text:nth-child(2) {
        margin-right: 20rpx;
        color: #979797;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
    }

    text:nth-child(3) {
        color: #3A3A3A;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
    }
}</style>