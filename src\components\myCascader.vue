<template>
  <view class="cascader_box">
    <view class="flex-row">
      <view class="cascader_name flex-1" :class="{active:first_select.value}">{{first_select.name||'请选择'}}</view>
      <view class="cascader_name flex-1" v-if="second_list.length>0" :class="{active:second_select.value}">{{second_select.name||'请选择'}}</view>
      <view class="cascader_name flex-1" v-if="third_select.length>0" :class="{active:third_select.value}">{{third_select.name||'请选择'}}</view>
    </view>
    <view class="cascader_list flex-row">
      <scroll-view scroll-y class="cate first flex-1">
        <view class="item bottom-line" :class="{active:first_select.value==item.value}" @click="onSelectCascader(1, item)" v-for="(item, index) in first_list" :key="index">{{item.name}}</view>
      </scroll-view>
      <scroll-view scroll-y class="cate second flex-1" v-if="second_list.length>0">
        <view class="item bottom-line" :class="{active:second_select.value==item.value}" @click="onSelectCascader(2, item)" v-for="(item, index) in second_list" :key="index">{{item.name}}</view>
      </scroll-view>
      <scroll-view scroll-y class="cate third flex-1" v-if="third_list.length>0">
        <view class="item bottom-line" :class="{active:third_select.value==item.value}" @click="onSelectCascader(3, item)" v-for="(item, index) in third_list" :key="index">{{item.name}}</view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  data () {
   return {
      first_list:[],
      second_list:[],
      third_list:[],
      first_select:{},
      second_select:{},
      third_select:{},
      selectted:[]
     }
  },
  watch:{
    data_list:{
      deep: true,
      handler(val){
        this.getFirst(val)
      }
    },
    selectted_item:{
      deep: true,
      handler(val){
        this.selectted = val
      }
    }
  },
  props:{
    data_list: {
      type: Array,
      default:()=>{return []}
    },
    selectted_item: {
      type: Array,
      default: ()=>{return []}
    }
  },
  methods: {
    getFirst(data){
      this.first_list = data
    },
    onSelectCascader(i,e){
      switch(i){
        case 1:
          this.first_select = e
          this.second_select = {}
          this.selectted= [{
              value:e.value,
              name:e.name,
              _current: e
            }]
          this.third_select = {}
          this.third_list = []
          // 筛选出当前区域下的二级区域
          if(e.children&&e.children.length>0){
            this.second_list = e.children
          }else{
            this.second_list = []
            this.$emit('onselect', this.selectted)
          }
          break;
        case 2:
          this.second_select = e
          this.third_select = {}
          this.selectted[1] = {
            value:e.value,
            name:e.name,
            _current: e
          }
          this.selectted.splice(2, 1)
          // 筛选出当前区域下的三级区域
          if(e.children&&e.children.length>0){
            this.third_list = e.children
          }else{
            this.third_list = []
            this.$emit('onselect', this.selectted)
          }
          break;
        case 3:
          this.third_select = e
          this.selectted[2] = {
            value:e.value,
            name:e.name,
            _current: e
          }
          this.$emit('onselect', this.selectted)
      }
    },
    clearData(){
      this.second_list = [],
      this.third_list = [],
      this.first_select = {},
      this.second_select = {},
      this.third_select = {},
      this.selectted = []
      this.$emit('clearData')
    }
  }
}
</script>

<style lang="scss" scoped>
view{
  display: flex;
  flex-direction: column;
  box-sizing: border;
}
.flex-row{
  flex-direction: row;
}

.cascader_box{
  background-color: #fff;
  .cascader_name{
    padding: 20rpx;
    text-align: center;
    border-top: 4rpx solid $uni-color-primary;
    background-image: linear-gradient(0deg, rgba(246, 246, 246, 0) 0%, rgba(251, 101, 106, 0.1) 100%);
    ~.cascader_name{
      margin-left: 24rpx;
    }
    &.active{
      color: $uni-color-primary;
      border-top: 4rpx solid $uni-color-primary;
      background-image: linear-gradient(0deg, rgba(246, 246, 246, 0) 0%, rgba(251, 101, 106, 0.1) 100%);
    }
  }
  .cate{
    padding: 20rpx;
    max-height: 30vh;
    overflow-y: scroll;
    transition: 0.26s;
  }
  .item{
    text-align: center;
    padding: 20rpx 10rpx;
    &.active{
      color: $uni-color-primary;
    }
  }
}
</style>