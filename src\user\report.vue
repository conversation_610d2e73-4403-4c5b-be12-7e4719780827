<template>
  <view>
    <view class="form-block">
      <view class="form-item">
        <label>客户姓名</label>
        <input type="text" v-model="params.name" placeholder="请输入客户姓名" />
      </view>
    </view>
    <view class="form-block">
      <view class="form-item">
        <label>客户性别</label>
        <view class="checkbox-group">
          <view class="checkbox" @click="params.sex = 1">
            <view class="icon-box" :class="{ active: params.sex === 1 }">
              <my-icon type="wancheng" size="22rpx" lineHeight="32rpx" color="#fff"></my-icon>
            </view>
            <text>男</text>
          </view>
          <view class="checkbox" @click="params.sex = 0">
            <view class="icon-box" :class="{ active: params.sex === 0 }">
              <my-icon type="wancheng" size="22rpx" lineHeight="32rpx" color="#fff"></my-icon>
            </view>
            <text>女</text>
          </view>
        </view>
      </view>
    </view>
    <view class="form-block">
      <view class="form-item">
        <label>手机号码</label>
        <view class="checkbox-group">
          <view class="checkbox" @click="(all_tel=true)&&(params.phone='')">
            <view class="icon-box" :class="{ active: all_tel }">
              <my-icon type="wancheng" size="22rpx" lineHeight="32rpx" color="#fff"></my-icon>
            </view>
            <text>全号</text>
          </view>
          <view class="checkbox" @click="(all_tel=false)&&(params.phone='')">
            <view class="icon-box" :class="{ active: !all_tel }">
              <my-icon type="wancheng" size="22rpx" lineHeight="32rpx" color="#fff"></my-icon>
            </view>
            <text>隐号</text>
          </view>
        </view>
      </view>
      <view class="form-item">
        <view class="tel_row" v-if="all_tel">
          <view class="tel_area">+86</view>
          <view class="num-box flex-1">
            <input class="all_tel" type="number" maxlength="11" v-model="params.phone" />
            <view class="line-row">
              <view class="line-item-box">
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
              </view>
            </view>
          </view>
        </view>
        <view class="tel_row" v-else>
          <view class="tel_area">+86</view>
          <view class="num-box flex-3">
            <input type="number" v-model="tel_before" maxlength="3" :focus="first_focus" @input="onInputFirst" />
            <view class="line-row">
              <view class="line-item-box">
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
              </view>
            </view>
          </view>
          <view class="xing flex-2">****</view>
          <view class="num-box flex-4">
            <input type="number" v-model="tel_after" maxlength="4" :focus="last_focus" @input="onInputLast" />
            <view class="line-row">
              <view class="line-item-box">
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
                <view class="line"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="form-block">
      <!-- <view class="form-item">
        <my-select label="推荐项目" oneRow></my-select>
      </view> -->
      <view class="form-item">
        <label>推荐项目</label>
        <view class="select" @click="showSearchPanel">
          <text class="value" v-if="params.project_id">{{ project_name }}</text>
          <text class="placeholder" v-else>请选择</text>
          <my-icon type="ic_into" color="#999"></my-icon>
        </view>
      </view>
      <my-popup ref="popup">
        <view class="search-panel">
          <view class="search-box flex-row">
            <input class="flex-1" type="search" @confirm="getProject" v-model="search_key" placeholder="请输入项目名称" />
            <view class="search_btn" @click="getProject()">搜索</view>
          </view>
          <scroll-view scroll-y class="build-list">
            <view>
              <view
                class="build-item bottom-line"
                v-for="item in build_list"
                :key="item.id"
                @click="onSelectBuild(item)"
                >{{ item.build_name }}</view
              >
            </view>
            <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
          </scroll-view>
        </view>
      </my-popup>
    </view>
    <view class="form-block">
      <!-- <view class="form-item">
        <my-select label="推荐项目" oneRow></my-select>
      </view> -->
      <view class="form-item">
        <label>预计到场时间</label>
        <view class="flex-row">
          <picker style="margin-right:16px" mode="date" :value="dateStr" :start="startDate" :end="endDate" @change="bindDateChange">
            <view class="select">
              <text class="value" v-if="dateStr">{{ dateStr }}</text>
              <text class="placeholder" v-else>请选择日期</text>
            </view>
          </picker>
          <picker mode="time" :value="timeStr" start="09:00" end="21:00" @change="bindTimeChange">
            <view class="select">
              <text class="value" v-if="timeStr">{{ timeStr }}</text>
              <text class="placeholder" v-else>请选择时间</text>
              <my-icon type="ic_into" color="#999"></my-icon>
            </view>
          </picker>
        </view>
      </view>
    </view>
    <view class="form-block">
      <view class="form-item">
        <view class="flex-1">
          <label>描述</label>
          <textarea class="textarea" v-model="params.remark" placeholder="和项目助理留言，其他要求说明！" rows="10"></textarea>
        </view>
      </view>
    </view>
    <view class="btn" @click="submit">立即提交</view>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
import myPopup from '../components/myPopup'
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
  components: {
    myIcon,
    myPopup,
    uniLoadMore
  },
  data() {
    return {
      get_status:"loading",
      content_text:{
        contentdown:"",
        contentrefresh:"正在搜索...",
        contentnomore:"没有查找到相关项目"
      },
      tel_before: '',
      tel_after: '',
      first_focus: false,
      last_focus: false,
      all_tel: false,
      search_key: '',
      build_list: [],
      dateStr: '',
      timeStr: '',
      params: {
        name:"",
        sex: 1,
        phone: '',
        project_id: '',
        visit_time: '',
        remark: ''
      }
    }
  },
  computed: {
    startDate() {
      return this.getDate('start')
    },
    endDate() {
      return this.getDate('end')
    }
  },
  onReady(){
    this.getProject()
  },
  methods: {
    onInputFirst(e) {
      if (e.detail.value.length >= 3) {
        this.first_focus = false
        this.last_focus = true
      } else {
        this.first_focus = true
        this.last_focus = false
      }
    },
    onInputLast(e) {
      if (e.detail.value.length == 0) {
        this.first_focus = true
        this.last_focus = false
      } else {
        this.first_focus = false
        this.last_focus = true
      }
    },
    showSearchPanel() {
      this.$refs.popup.show()
    },
    getProject() {
      this.build_list = []
      this.get_status = "loading"
      this.$ajax.get('report/searchProject', { build_name: this.search_key }, res => {
        if (res.data.code === 1) {
          this.build_list = res.data.list
          this.get_status = "more"
        }else{
          this.get_status = "noMore"
        }
      })
    },
    onSelectBuild(item) {
      this.params.project_id = item.project_id
      this.project_name = item.build_name
      this.$refs.popup.hide()
    },
    bindDateChange(e){
      this.dateStr = e.detail.value
    },
    bindTimeChange(e){
      this.timeStr = e.detail.value
    },
    getDate(type) {
      const date = new Date()
      let year = date.getFullYear()
      let month = date.getMonth() + 1
      let day = date.getDate()

      if (type === 'start') {
        year = year - 60
      } else if (type === 'end') {
        year = year + 2
      }
      month = month > 9 ? month : '0' + month
      day = day > 9 ? day : '0' + day
      return `${year}-${month}-${day}`
    },
    submit() {
      uni.showLoading({
        title:"正在提交...",
        mask:true
      })
      this.params.visit_time = `${this.dateStr} ${this.timeStr}`
      if(!this.all_tel){
        console.log(this.tel_after)
        this.params.phone = `${this.tel_before}****${this.tel_after}`
      }
      console.log(this.params)
      this.$ajax.post('report/reportProject', this.params, res => {
        uni.hideLoading()
        if(res.data.code === 1){
          uni.showToast({
            title: res.data.msg,
            mask: true
          })
          setTimeout(()=>{
            this.$navigateBack()
          }, 2000)
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  &.flex-row {
    flex-direction: row;
  }
}
.form-block {
  margin-bottom: 16rpx;
}
.form-item {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 48rpx;
  background-color: #ffff;
  label {
    font-size: 32rpx;
    color: #666;
  }
  input {
    text-align: right;
  }
  .checkbox-group {
    flex-direction: row;
  }
  .checkbox {
    flex-direction: row;
    align-items: center;
    font-size: 32rpx;
    color: #666;
    ~ .checkbox {
      margin-left: 48rpx;
    }
    .icon-box {
      width: 32rpx;
      height: 32rpx;
      line-height: 32rpx;
      margin-right: 16rpx;
      text-align: center;
      border-radius: 4rpx;
      background-color: #e8e8e8;
      &.active {
        background-color: $uni-color-primary;
      }
    }
  }

  .select {
    flex-direction: row;
    align-items: center;
    font-size: 32rpx;
    color: #333;
    .placeholder {
      color: #999;
    }
  }
}

.tel_row {
  flex-direction: row;
  align-items: center;
  width: 100%;
  .num-box {
    position: relative;
    .line-row {
      width: 100%;
      position: absolute;
      bottom: 0;
    }
    .line-item-box {
      position: relative;
      flex-direction: row;
      align-items: flex-end;
      .line {
        height: 1rpx;
        flex: 1;
        margin-left: 16rpx;
        background-color: #999;
      }
    }
  }
  input {
    margin-left: 32rpx;
    text-align: left;
    width: 100%;
    letter-spacing: 44rpx;
    &.all_tel {
      margin-left: 24rpx;
      letter-spacing: 34rpx;
    }
  }
  .tel_area {
    font-size: 32rpx;
    width: 80rpx;
  }
  .xing {
    // width: 80rpx;
    font-size: 32rpx;
    text-align: center;
    color: #999;
  }
}

.search-panel {
  width: 100%;
  background-color: #fff;
}
.search-box {
  align-items: center;
  padding: 24rpx;
  input {
    height: 42rpx;
    font-size: 28rpx;
    padding: 5px 10px;
    border: 1rpx solid #dedede;
    border-radius: 8rpx;
    margin-right: 16rpx;
  }
  .search_btn {
    padding: 5px 16px;
    border-radius: 4px;
    background-color: $uni-color-primary;
    color: #fff;
  }
}
.build-list {
  max-height: 50vh;
  min-height: 30vh;
  padding: 24rpx 24rpx;
  .build-item {
    padding: 24rpx;
  }
}
.textarea{
  margin-top: 16rpx;
  width: 100%;
}
.btn{
  margin: 48rpx;
  line-height: 88rpx;
  background: #FB656A;
  box-shadow: 0 8rpx 24rpx 0 rgba(251,101,106,0.40);
  border-radius: 44rpx;
  border-radius: 44rpx;
  text-align: center;
  font-size: 32rpx;
  color: #fff;
}
.nomore{
  padding: 24rpx;
  color: #999;
}
</style>
