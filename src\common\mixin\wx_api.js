
import { isIos } from '../index'
import wx from 'weixin-js-sdk'
import { statistics } from '../statistics'

export default {
  methods: {
    getWxConfig(jsApiList, callback) {
      statistics()
      let url
      if (isIos()) {
        url = this.$store.state.firstUrl
      } else {
        url = window.location.href
      }
      this.$ajax.get('/wechat/index/signature.html', { url }, res => {
        if (res.data.code === 1) {
          res.data.config.jsApiList = jsApiList || [
            'updateAppMessageShareData',
            'updateTimelineShareData'
          ]
          res.data.config.openTagList = ['wx-open-launch-weapp']
          res.data.config.debug = false
          wx.config(res.data.config)
          this.wxInit(callback)
        }
      })
    },
    wxInit(callback) {
      console.log('开始初始化')
      wx.ready(() => {
        console.log('初始化完成')
        if (callback) {
          callback(wx)
        }
        if (this.share) {
          this.initShare()
          this.postMessage()
        }
      })
    },
    initShare() {
      // 需在用户可能点击分享按钮前就先调用
      wx.updateAppMessageShareData({
        title: this.share.title || '', // 分享标题
        desc: this.share.content, // 分享描述
        link: this.share.link || window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        imgUrl: this.share.pic + '?x-oss-process=style/w_220', // 分享图标
        success: function() {
          // 设置成功
          // console.log("设置成功")
        }
      })
      wx.updateTimelineShareData({
        title: this.share.title || '', // 分享标题
        link: this.share.link2 || this.share.link || window.location.href, // 分享链接(link2，分享朋友圈不同链接)，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        imgUrl: this.share.pic + '?x-oss-process=style/w_220', // 分享图标
        success: () => {
          // 设置成功
          // console.log("设置成功")
          console.log(this.share)
        }
      })
    },
    postMessage (type = "share", datas = this.share) {
      // this.share.path  定义为跳转到小程序的路径 通过webview 页面跳转到h5 页面 然后 在小程序分享的时候可以跳转到小程序页面   
      wx.miniProgram.postMessage({
        data: {
                action: 'message',
                type: type,
                share: datas,
                title: "",
                fun:()=>{}
            }
      })
    }
  },
  onUnload(){
    wx.miniProgram.navigateBack({
      delta: 1
    })
    // wx.miniProgram.postMessage({
    //   data:{
    //     action: 'unload',
    //     type:"navigateBack",
    //     share: this.share||{},
    //     fun:function(){
    //         wx.miniProgram.navigateBack({
    //         delta: 1
    //       })
    //     }
    //   }
    // })

  }
}
