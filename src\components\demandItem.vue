<template>
  <view class="need-item" @click="$emit('click', { type: type, detail: itemData })">
    <view class="top flex-row" v-if="!on_type">
      <view class="publisher flex-row">
        <image class="header_img" mode="aspectFill" :src="itemData.prelogo | imageFilter('w_120')"></image>
        <text class="user_name">{{ itemData.cname || '' }}</text>
        <!-- <text class="user_name">{{itemData.user.cname}}</text> -->
      </view>
      <view class="time" v-if="from == 'mendian'">{{ itemData.createtime }}</view>
      <view class="time" v-else>{{ itemData.begintime }}</view>
    </view>
    <view v-if="itemData.upgrade_type == 2 && endTime" class="zhiding">
      <text class="spantext">置顶中</text>
      <text class="spantext">有效期至</text>
      <text class="spantext">{{ itemData.upgrade_time }}</text>
    </view>
    <view class="title-box">
      <text class="level level1" v-if="itemData.upgrade_type == 2 && !endTime">置顶</text>
      <text class="level level2" v-if="itemData.is_recommend">精选</text>
      <text class="title" :class="{ red: itemData.ifred, bold: itemData.ifbold }">{{ itemData.title }}</text>
    </view>
    <view class="line bottom-line" v-if="!on_type"></view>
    <view class="footer flex-row">
      <view class="area">
        <text class="label">意向区域：</text>
        <text class="value">{{ itemData.areaname || '' }}</text>
      </view>
      <view class="price" v-if="type === 'buy_house'">
        <text class="label">意向价格：</text>
        <text class="value" v-if="itemData.qgprice">{{ itemData.qgprice || '' }}万</text>
        <text class="value" v-else>面议</text>
      </view>
      <view class="price" v-if="type === 'rest_house'">
        <text class="label">意向价格：</text>
        <text class="value" v-if="itemData.qzprice">{{ itemData.qzprice || '' }}元/月</text>
        <text class="value" v-else>面议</text>
      </view>
      <view class="vacancy" style="width:180rpx" v-if="on_type === 'manage'"></view>
      <view class="contact flex-row" v-else>
        <view class="icon-box" @click.stop.prevent="advAsk(itemData)" v-if="is_open_im">
          <my-icon type="ic_zixun1" size="45rpx" color="#ff656c"></my-icon>
        </view>
        <view class="icon-box" @click.stop.prevent="handleTel(itemData)">
          <my-icon type="ic_dianhua1" size="45rpx" color="#ff656c"></my-icon>
        </view>
        <text>{{ itemData.area || '' }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from './myIcon'
import getChatInfo from '../common/get_chat_info.js'
export default {
  components: {
    myIcon
  },
  props: {
    itemData: Object,
    type: {
      type: String,
      default: "buy_house",
    },
    on_type: {
      type: String,
      default: ''
    },
    from: {
      type: String,
      default: "",
    },
    endTime: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {

    }
  },
  computed: {
    is_open_im() {
      return this.$store.state.im.ischat
    },
  },
  methods: {
    // 拨打发布者人电话
    handleTel(e) {
      // if (this.$store.state.im.istelcall == 0) {
      //   uni.makePhoneCall({
      //     phoneNumber: e.tel
      //   })
      //   this.$ajax.get(
      //     'im/callUpStatistics',
      //     {
      //       id: e.uid,
      //       tel: parseInt(e.tel),
      //       type: this.type === 'buy_house'?'15':'16'
      //     },
      //     res => {}
      //   )
      //   return
      // }
      this.tel_params = {
        id: e.uid,
        mid: e.uid,
        tel: e.tel,
        type: 'agent',
        from: this.type === 'buy_house' ? '15' : '16',
        source: 'list',
        info_id: e.id,
        bid: '',
        intercept_login: true
      }
      this.$emit('ontel', this.tel_params)
    },
    // 和发布者聊天
    advAsk(e) {
      if (this.is_open_im == 0) {
        return
      }
      getChatInfo(e.uid, this.type === 'buy_house' ? '17' : '18')
    },
  }
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.flex-row {
  flex-direction: row;
}


.need-item {
  padding: 24rpx 0;
  background-color: #fff;

  .top {
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .time {
      font-size: 22rpx;
      color: #999;
    }
  }

  .publisher {
    align-items: center;

    .header_img {
      width: 64rpx;
      height: 64rpx;
      margin-right: 20rpx;
      border-radius: 50%;
      background-color: #dedede;
    }

    .user_name {
      font-weight: 22rpx;
    }
  }

  .zhiding {
    display: inline-block;
    width: 97%;
    padding: 2px 8px;
    align-items: center;
    border-radius: 4px;
    background-color: #FFF3E8;
    margin-bottom: 24rpx;

    .spantext {
      color: #F53F3F;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      padding-right: 10rpx;
    }
  }

  .title-box {
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    margin-bottom: 15rpx;

    .level {
      margin-right: 10rpx;
      padding: 2rpx 10rpx;
      line-height: 1;
      font-size: 22rpx;
      color: #fff;

      &.level1 {
        background: linear-gradient(132deg, #F7918F 0%, #FB656A 100%);
      }

      &.level2 {
        background: linear-gradient(135deg, #69D4BB 0%, #00CAA7 100%);
      }
    }

    .title {
      line-height: 1.5;
      font-size: 32rpx;
      color: #333;

      &.red {
        color: #f44;
      }

      &.bold {
        font-weight: bold;
      }
    }
  }

  .line {
    margin: 24rpx 0;
    // height: 1rpx;
    // background-color: #d8d8d8;
  }

  .footer {
    justify-content: space-between;
    align-items: center;

    .label {
      color: #999;
      font-size: 22rpx;
    }

    .value {
      font-size: 22rpx;
    }

    .area {
      display: block;
    }

    .price {
      display: block;

      .value {
        color: $uni-color-primary;
      }
    }

    .icon-box {
      margin-left: 24rpx;
      width: 64rpx;
      height: 64rpx;
      border-radius: 60%;
      justify-content: center;
      text-align: center;
      background-color: rgba(251, 101, 106, 0.2);
    }
  }
}
</style>