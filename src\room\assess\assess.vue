<template>
    <view class="page">
        <!-- <image src="../1.png.png"></image> -->
        <view class="assess_nav">
            <!-- <image src="../1.png.png" class="im"></image> -->
            <view class="assess_text">{{name}}市·{{this.date}}月分参考均价</view>
            <view class="assess_text_center">
                <text class="text_one">{{ home.price }}</text>
                <text class="text_two">元/平</text>
            </view>
            <view class="assess_bottom">
                <view class="logo_top">
                    <view class="logo_top_text">
                        <view>昨日新增房(套)</view>
                        <view>{{ home.deal_count }}</view>
                    </view>
                </view>
                <view class="logo_top">
                    <view class="logo_top_text">
                        <view>昨日新增客(人)</view>
                        <view>{{ home.deal_count }}</view>
                    </view>
                </view>
                <view class="logo_top">
                    <view class="logo_top_text">
                        <view>昨日带看量(次)</view>
                        <view>{{ home.deal_count }}</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="title">
            <view class="title_top">
                <text>价格走势</text>
                <view class="title_one">
                    <text></text>
                    <text>{{name}}市</text>
                </view>
                
            </view>
            <view>
                <view class="qiun-columns">    
                        <view class="qiun-charts" >
			        <canvas canvas-id="canvasLineA"  id="canvasLineA" class="charts"></canvas>
		                </view>
	            </view>
            </view>
          
        </view>       
        <view class="logo">
            <text>数据来源：腾房云Saas系统提供</text>
            <text>更新时间：{{ time }}</text>
        </view>
        <view class="btn" style="margin-top: 76rpx;">
            <button class="mini-btn" type="primary" @click="assresstap()">查看小区房价</button>
            <view style="height: 80rpx;"></view>
        </view>
    </view>
</template>
  
<script>
import { json, text } from 'body-parser';
import { read, rmSync } from 'fs';
import uCharts from '../../components/u-charts/u-charts.js';
import {
  formatImg,
} from '../../common/index.js'
var _self;
	var canvaLineA=null;
export default {
    name: '',
    components: { text,},
    // mixins: [setSeo],
    data() {
        return {
        	cWidth:'',
			cHeight:'',
			pixelRatio:1,
            list:{},
            name:"",
            time:"",
            home:{
            },
            date:"",
            year:[],
            arr:[],
            price:[],
            thelist:[],
}
    },
    onLoad(){
        _self = this;
			// uni.upx2px(750) 这是uni-app自带的自适应，以750的尺寸为基准。动态变化
			this.cWidth=uni.upx2px(650);
			this.cHeight=uni.upx2px(450);
    }, 
    onReady() {
        this.getAssessList()
  },
methods:{
        getServerData(){
					this.chartData = {
						categories: this.thelist,
						series: [{
							name: "月份",
                            tyle:'column',
							data: [,11000,,12000,,12100],         
							color: "#dee5fc",
                            lineStyle: { opacity: 0, color:'#69707F'}// 画线的样式,opacity表示,不需要画标线,只要文字就行
                            // linearType
						},
                    ],
					}
					_self.showLineA("canvasLineA", this.chartData);
			},
			// 展示图标的函数 接收参数，一个块的id,一个数据
			showLineA(canvasId,chartData){
                canvaLineA=new uCharts({
					$this:_self,
					canvasId: canvasId,
					// 图标类型
					type: 'column',
                    enableScroll: false,
                    // background:"linear-gradient(180deg, #446EEF00 0%, #446CEF 100%);",
					fontSize:11,
					legend:{show:false},
					dataPointShape:false,
					pixelRatio:_self.pixelRatio,
					categories: chartData.categories,
					series: chartData.series,
					animation: true,
                    dataLabel:false,
					context:uni.createCanvasContext(canvasId, _self), // 这里很重要
					// x轴显示的内容
					xAxis: {
                        disableGrid: true,
                        xAxisLabel:false,
                        format:false,
                        // fontColor: "#979797",
                        gridType: "dash",
                        axisLine:false,
                        //  width: 20,
                        activeBgOpacity: 0.08
					},
					// y轴显示的内容
					yAxis: {
                        gridType:"dash",
                        disabled:false,
                        axisLine:false,
                        min:this.price[0],
                        max:this.price[5],
                        // fontColor: "#979797",
					},
					width: _self.cWidth*_self.pixelRatio,
					height: _self.cHeight*_self.pixelRatio,
				});
				
			},
        //跳转查看小区房价页面
        assresstap(){
            this.$navigateTo('/room/assess/apprarser')

        },
        //房价评估接口
        getAssessList(){
            this.$ajax.get('fangjia/fangjiaEstimate',{},res=>{
                console.log(res,"房价评估")
                if(res.data.code == 1){
                    if (res.data.share) {
                    this.share = res.data.share
                    }
                    this.getWxConfig()
                    this.name=res.data.siteCity
                    this.time= res.data.avgPriceNearestData.month
                    this.home = res.data.avgPriceNearestData
                    let ss = this.time.split("-")
                    console.log(parseInt(ss[1]))
                    // let cc = ss[1]<10?
                    this.date=parseInt(ss[1])
                    this.year = res.data.avgPriceList
                    console.log(this.year)
                    let arr = []
                    let add =[]
                    let date = []
                    this.year.forEach(item => {
                        arr.push({month:item.month})
                    })
                        for(let i=0;i<arr.length;i++){
                            add.push(arr[i].month)
                        }
                        console.log(add,"日期")
                        let mdd = []
                        let mod = []
                        this.year.forEach(item => {
                            mdd.push({price:item.price})
                        })
                    for(let i=0;i<mdd.length;i++){
                            mod.push(mdd[i].price)
                        }
                        for(let i=0;i<add.length;i++){
                            console.log(add[i].split('-'))
                            date.push(parseInt(add[i].split('-')[1])+"月")
                        }
                        // console.log(mod,"金钱")
                        this.thelist = date
                        this.price = mod
                        this.getServerData();
                } 
            })
        },
        
}

}
</script>
  
<style scoped lang="scss">
.page{
    background: white;
    min-height: calc(100vh - 88rpx);
}
// .im{
//     position: absolute;
//     top: 0;
//     left: 0;
//     width: 100%;
//     height: 354rpx;
//     // z-index: -10;
// }
.assess_nav {
    background-image: url('../../static/icon/assress.png');
    width: 100%;
    height: 354rpx;
    padding: 64rpx 48rpx 0;
    box-sizing: border-box;
    .assess_text {
        font-size: 28rpx;
        color: #ffffff;
        z-index: 9999;
    }

    .assess_text_center {
        margin-top: 20rpx;
        z-index: 9999;
    }

    .text_one {
        font-size: 48rpx;
        color: #ffffff;
        font-weight: medium;
        letter-spacing: 0px;
        margin-bottom: 162rpx;
        z-index: 9999;
    }

    .text_two {
        margin-bottom: 168rpx;
        font-size: 28rpx;
        color: #ffffff;
        margin-left: 12px;
    }
    .assess_bottom {
        width: 654rpx;
        height: 172rpx;
        background: white;
        margin-top: 68rpx;
        border-radius: 10rpx;
        box-shadow: 0rpx 8rpx 10rpx 0rpx #00000019;
        display: flex;
    }
    .logo_top{
        flex:1;
        height: 100%;
        // background: yellow;
        .logo_top_text{
            margin-left: 36rpx;
            margin-top: 36rpx;
            border-right: solid 1px #efefef;
        }
        .logo_top_text view:nth-child(1){
            font-size: 22rpx;
            color: #979797;
        }
        .logo_top_text view:nth-child(2){
            font-size: 40rpx;
            color: #232323;
            margin-top: 14rpx;
            font-weight: medium;
        font-family: PingFang SC;
        }
    }
}
.title{
    margin: 0 auto;
    margin-top: 124rpx;
    width: 654rpx;
    z-index: 9999;
    .title_top{
    display: flex;
    justify-content: space-between;
    z-index: 9999;
    text:nth-child(1){
        color:#232323;
        font-size:40rpx;
        font-weight: medium;
        font-family: PingFang SC;
;
     
    }
    .title_one text:nth-child(1){
    display: inline-block;
    background: #D22E25;
    border: 2rpx solid #FFFFFF;
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    box-shadow: 0px 0px 8rpx 8rpx #D22E25;
    margin-right: 14rpx;
    box-sizing: border-box;
    }
    .title_two text:nth-child(2){
        color: #232323;
        font-size: 24rpx;
    }
    } 

}
.btn .mini-btn{
        background-color: #446EEF!important;
        width: 654rpx!important;
        height: 96rpx;
        // font-size: 32rpx;
    }
.logo{
    width: 654rpx;
    // background: red;
    margin:0 auto;
    display: flex;
    justify-content: space-between;
    color: #979797;
    font-size: 22rpx;
}
.qiun-charts {
		width: 750upx;
		height: 450upx;
		background-color: #FFFFFF;
        // color: #979797;
        position: relative;
        color: #979797;
        top: 20rpx;
        left: -10rpx;
        margin-bottom: 40rpx;
	}
	
	.charts {
		width: 750upx;
		height: 450upx;
        color: #979797;
		background-color: #FFFFFF;
    }
</style>