<template>
<view class="recharge">
	<view class="header flex-row">
		<view class="info left">
			<view class="title">金币</view>
			<view class="integral">{{jinbi}}</view>
			<view class="btn" @click="$navigateTo('/user/recharge_detail')">查看明细</view>
		</view>
		<view class="info right">
			<view class="title">余额</view>
			<view class="integral">{{shequ_money}}</view>
			<view class="btn" @click="$navigateTo('/user/withdraw_funds')">申请提现</view>
		</view>
	</view>

	<view class="select_type flex-row">
		<view class="select_option" :class="{active:showTab===1}" @click="switchTab(1)">充金币</view>
		<view class="select_option" :class="{active:showTab===2}" @click="switchTab(2)">充积分</view>
		<view class="select_option" :class="{active:showTab===3}" @click="switchTab(3)">套餐</view>
		<view class="select_option" :class="{active:showTab===4}" @click="switchTab(4)">套餐余量</view>
	</view>
	<view class="options-box flex-row" v-if="showTab==1||showTab==2">
		<view class="options" :class="{active:item.id === set_meal_id}" @click="selectSetMeal(item)" v-for="item in set_meal_list" :key="item.id">
			<text class="title">{{item.name}}</text>
			<text class="tip">售价：{{item.money}}元</text>
		</view>
		<view class="options" :class="{selected:show_custom}" @click="(show_custom = !show_custom)&&(set_meal_id=0)">
			<view class="more flex-row">
				<text class="text">自定义</text>
				<my-icon :type="show_custom?'ic_close':'ic_open'" :color="show_custom?'#fff':'#999'"></my-icon>
			</view>
		</view>
		<view class="options vacancy"></view>
	</view>
	<view  v-if="showTab==3" class="spread-box">
		<scroll-view scroll-x>
			<view class="spread-list flex-row">
				<view class="spread-item" :class="{active:current_spread.upgrade_id===item.upgrade_id}" v-for="item in spread_list" :key="item.upgrade_id" @click="onSelectSparead(item)">
					<view class="spread-header" :style="{backgroundImage: `url('${item.bg_pic||default_header}')`}">
						<view>
							<view class="name">{{item.name}}</view>
							<view class="flex-row aligin-center">
								<view class="time">有效期{{item.days}}天</view>
							</view>
						</view>
						<view class="price_row flex-row">
							<view>
								<text v-if="item.limit_buy">{{item.limit_buy_time}}天内不可重复购买</text>
							</view>
							<view class="price-box flex-row">
								<text>￥</text>
								<text class="price">{{item.money}}</text>
							</view>
						</view>
						<view class="check_box">
							<my-icon v-if="current_spread.upgrade_id===item.upgrade_id" size="44rpx" type="ic_xuanze" color="#fff"></my-icon>
						</view>
					</view>
					<view class="info gave_list">
						<view class="descp">{{item.descp}}</view>
						<view class="gave_item l" v-if="item.corn">
							<text>赠送金币</text>
							<text>{{item.corn}}个</text>
						</view>
						<view class="gave_item" v-if="item.integral">
							<text>赠送积分</text>
							<text>{{item.integral}}个</text>
						</view>
						<view class="gave_item l" v-if="item.info_selected">
							<text>包含精选</text>
							<text>{{item.info_selected}}次</text>
						</view>
						<view class="gave_item" v-if="item.info_top">
							<text>包含置顶</text>
							<text>{{item.info_top}}次</text>
						</view>
					</view>
				</view>
				<view class="spread-item empty"></view>
			</view>
		</scroll-view>
		<view class="log_tip" @click="toMealDetail">
			<text>查看套餐购买记录</text>
			<my-icon type="ic_into" color="#ff656b"></my-icon>
		</view>
		<view class="note-container">
			<view class="note">
				<view>注：</view>
				<view>
					<text>有效期内需使用完，到期将失效</text>
					<text>置顶1次可抵扣指定信息置顶{{top_time}}天</text>
					<text>精选1次可抵扣指定信息精选刷新{{refresh_num}}次</text>
				</view>
			</view>
		</view>
	</view>
	<view class="exchange-box" v-if="showTab==1 ||showTab==2">
			<view class="inp-box flex-row bottom-line" v-if="show_custom">
				<input type="number" @input="handelInput" placeholder="请输入充值个数" placeholder-style="font-size:26rpx">
				<view class="computed">
					<text>需花费</text>
					<text class="highlight" v-if="showTab === 1">{{amount}}</text>
					<text class="highlight" v-if="showTab === 2">{{amount*integral_price}}</text>
					<text>元</text>
				</view>
			</view>
			<view class="note" v-if="showTab==1">
				<view>注：</view>
				<view>当前账户金币{{jinbi}}个，金币可用于信息置顶、精选、兑换积分。</view>
				<view>金币/积分一经充值 不能退款，请按需充值。</view>
			</view>
			<view class="note" v-else>
				<view>注：</view>
				<view>当前账户积分{{score}}，积分可用于信息刷新。</view>
				<view>金币/积分一经充值 不能退款，请按需充值。</view>
			</view>
		<!-- 充值按钮 -->
		<view class="button-box">
			<view class="button" @click="subOrder()">支付</view>
		</view>
	</view>

	<view class="exchange-box" v-if ="showTab==3">
		<view class="pay_type" v-if="showTab==3">
			<view class="title">使用金币或余额</view>
					<checkbox-group @change="onCheckChange">
							<label class="pay_item flex-box" v-if="current_spread.pay_by_corn==1">
									<view class="pay_name flex-row">
											<my-icon type="jinbi" color="#f9cf5e" size="56rpx"></my-icon>
											<text class="text">使用金币</text>
											<text class="tip">(当前账户金币{{jinbi}})</text>
									</view>
									<checkbox value="use_corn" :checked="use_corn==1" color="#FB656A"></checkbox>
							</label>
							<label class="pay_item flex-box" v-if="shequ_money">
									<view class="pay_name flex-row">
											<my-icon type="yue" color="#ff656b" size="56rpx"></my-icon>
											<text class="text">使用余额</text>
											<text class="tip">(当前账户余额{{shequ_money}})</text>
									</view>
									<checkbox value="use_yue" :checked="use_yue==1" color="#FB656A"></checkbox>
							</label>
					</checkbox-group>
					<view class="pay_ment flex-row">
							<text>还需支付</text>
							<text>{{pay_amount}}元</text>
					</view>
		</view>
		<!-- 购买套餐按钮 -->
		<view class="button-box">
			<view class="button" @click="subOrder2()">支付</view>
		</view>
	</view>
	<view class="exchange-box" v-if ="showTab==4">
		<view class="taocan">
			<view class="top flex-row">
				<view class="top_item flex-box" :style="{backgroundImage:`url(${ossDomain}/user_center/taocan/taocanBg0.png)`}">
					<view class="top_item_con" >
						<view class="top_item_con_title">精选总量</view>
						<view class="top_item_con_num">{{taocanList.packageSelectedCount ||0}}条</view>
					</view>
				</view>
				<view class="top_item flex-box" :style="{backgroundImage:`url(${ossDomain}/user_center/taocan/taocanBg1.png)`}">
					<view class="top_item_con">
						<view class="top_item_con_title">置顶总量</view>
						<view class="top_item_con_num">{{taocanList.packageTopCount ||0}}条</view>
					</view>
				</view>
				<view class="top_item flex-box" :style="{backgroundImage:`url(${ossDomain}/user_center/taocan/taocanBg2.png)`}">
					<view class="top_item_con">
						<view class="top_item_con_title">刷新总量</view>
						<view class="top_item_con_num">{{taocanList.packageRefreshCount ||0}}条</view>
					</view>
				</view>
				
			</view>
			<view class="bottom flex-row">
					<view class="bottom_item flex-box" >
						<view class="botttom_img">
							<view class="botttom_img_con">

							</view>
						</view>
					<view class="bottom_item_con">
						<view class="bottom_item_con_title">精选余量</view>
						<view class="bottom_item_con_num">{{taocanList.packageSelectedSurplusCount ||0}}条</view>
					</view>
				</view>
				<view class="bottom_item_line"></view>
				<view class="bottom_item flex-box" >
						<view class="botttom_img">
							<view class="botttom_img_con botttom_img_con1 ">

							</view>
						</view>
					<view class="bottom_item_con">
						<view class="bottom_item_con_title">置顶余量</view>
						<view class="bottom_item_con_num">{{taocanList.packageTopSurplusCount ||0}}条</view>
					</view>
				</view>
				<view class="bottom_item_line"></view>
				<view class="bottom_item flex-box" >
						<view class="botttom_img">
							<view class="botttom_img_con botttom_img_con2">

							</view>
						</view>
					<view class="bottom_item_con">
						<view class="bottom_item_con_title">刷新余量</view>
						<view class="bottom_item_con_num">{{taocanList.packageRefreshSurplusCount ||0}}条</view>
					</view>
				</view>
			</view>
		</view>
	</view>
	<my-popup ref="pay_popup">
		<pay ref="pay" :pay_params="pay_params" h5WxPayApi="/wapi/upgrade/wxPayByWapPackage.html" h5AliPayApi="/wapi/upgrade/aliPayPackage.html" mpWxPayApi="upgrade/wxPayPackage.html" @pay_success="paySuccess"></pay>
	</my-popup>
</view>
</template>

<script>
	import myIcon from "../components/myIcon"
	import {isIos} from '../common/index'
	// import myPopup from "../components/myPopup"
	import {formatImg,config} from "../common/index"
	import {mapMutations} from 'vuex'
	export default {
		data() {
			return {
				jinbi:0,
				shequ_money:0,
				score:0,
				amount:0,
				showTab:1,
				set_meal_list:[],
				set_meal_id:null,
				show_custom:false,
				integral_price:1,
				spread_list: [],
				current_spread: {},
				default_header:"https://images.tengfangyun.com/images/set_meal/spread_1.png",
				use_corn: 0,
				use_yue: 0,
				top_time:"",
				refresh_num:"",
				taocanList:{
					packageRefreshSurplusCount:0,
					packageRefreshCount:0,
					packageTopSurplusCount:0,
					packageSelectedSurplusCount:0,
					packageTopCount:0,
					packageSelectedCount:0,
				},
			};
		},
		components:{
			myIcon,
			// myPopup
		},
		computed:{
			pay_amount(){
				if(!this.current_spread.money){
						return 0
				}
				let pay_amount = parseFloat(this.current_spread.money)
				if(this.use_yue){
					console.log(pay_amount)
					console.log(this.shequ_money)
						pay_amount-=this.shequ_money
				}
				if(this.use_corn){
						pay_amount-=this.jinbi
				}
				if(pay_amount>0){
						return pay_amount.toFixed(2)
				}else{
						return 0
				} 
			},
			ossDomain(){
				return config.imgDomain
			}
		},
		onLoad(options){
			if(options.type){
				this.showTab = parseInt(options.type)
			}
			if(this.showTab===3){
				this.getSpread()
			}else{
				this.getSetmeal()
			}
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			this.getWxConfig(
        ['chooseWXPay', 'hideOptionMenu'],
        wx => {
          console.log('执行回调')
          this.wx = wx
        },
        1
      )
		},
		onShow(){
			if(this.$store.state.user_info.shequ_money!=undefined&&this.$store.state.user_info.money_own!=undefined){
				this.jinbi = this.$store.state.user_info.money_own
				this.shequ_money = this.$store.state.user_info.shequ_money
				this.score = this.$store.state.user_info.score
			}else{
				this.getUser()
			}
		},
		methods:{
			...mapMutations(['getUserInfo']),
			handelInput(e){
				this.amount = parseFloat(e.detail.value)||0
			},
			switchTab(type){
				this.showTab = type
				if(type === 3){
					this.getSpread()
				}else if (type ==2 ||type==1){
					this.getSetmeal()
				}else if (type==4){
					this.getTaocan()
				}
			},
			// 获取套餐余量
			getTaocan(){
				this.$ajax.get('upgrade/packageSurplus',{},res=>{
					if(res.data.code == 1){
						this.taocanList = res.data.finance
					}
				})
			},
			// 获取套餐列表
			getSetmeal(){
				this.$ajax.get('Upgrade/integralAndCornList.html',{type:this.showTab},res=>{
					if(res.data.integral_price){
						this.integral_price = res.data.integral_price
					}
					if(res.data.code === 1){
						this.set_meal_list = res.data.list
					}
				})
			},
			// 获取推广套餐
			getSpread(){
				this.$ajax.get('upgrade/promotionPackage.html', {}, res=>{
					if(res.data.code === 1){
						this.top_time = res.data.top_count
						this.refresh_num = res.data.refresh_times
						this.spread_list = res.data.list.map(item=>{
							item.bg_pic = formatImg(item.bg_pic, 'w_8001')
							return item
						})
					}else{
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
					}
				})
			},
			selectSetMeal(item){
				this.set_meal_id === item.id?this.set_meal_id = 0:this.set_meal_id=item.id
				this.show_custom = false
				this.amount = ""
			},
			subOrder(){
				if(!this.amount&&!this.set_meal_id){
					uni.showToast({
						title:"请输入或选择充值数量",
						icon:"none"
					})
					return
				}
				if(this.amount<1&&!this.set_meal_id){
					uni.showToast({
						title:"一次至少充值一个",
						icon:"none"
					})
					return
				}
				if(this.amount%1){
					uni.showToast({
						title:"充值数量必须为整数",
						icon:"none"
					})
					return
				}
				let params = {num:this.amount,type:this.showTab,gid:this.set_meal_id}
				this.$ajax.get('Upgrade/buy.html',params,(payInfo)=>{
					if(payInfo.data.code == 1){
						this.handelPay(payInfo.data.data.timeStamp,payInfo.data.data.nonceStr,payInfo.data.data.package,payInfo.data.data.signType,payInfo.data.data.paySign)
					}else{
						uni.showToast({
							title:payInfo.data.msg,
							icon:"none",
							duration:5000
						})
					}
				})
			},
			handelPay(timeStamp, nonceStr, payPackage, signType, paySign){
				console.log("开始调起支付")
				this.wx.chooseWXPay({
					// provider: 'wxpay',
					timestamp:timeStamp,
					nonceStr:nonceStr,
					package:payPackage,
					signType:signType,
					paySign:paySign,
					success: (res)=> {
						uni.showToast({
							title:"支付成功"
						})
						setTimeout(()=>{
							this.$navigateBack()
						},1500)
					},
					fail: function (err) {
						console.log("支付失败：",err);
						uni.showToast({
							title:err.err_desc||err.errMsg,
							icon:"none",
							duration:5000
						})
					}
				});
			},
			/** 
			 * @date 2020-12-10 11:43:32 
			 * @desc 选择套餐 
			 */
			onSelectSparead(item){
				this.current_spread = item
				this.use_corn = 0
				this.use_yue = 0
			},
			toMealDetail(){
				this.$navigateTo(`/user/meal_detail`)
			},
			/** 
			 * @date 2020-12-10 11:43:32 
			 * @desc 选择使用金币或余额 
			 */
			onCheckChange(e){
				if(e.detail.value.includes("use_corn")){
							this.use_corn = 1
					}else{
							this.use_corn = ''
					}
					if(e.detail.value.includes('use_yue')){
							this.use_yue = 1
					}else{
							this.use_yue = ''
					}
			},
			subOrder2(){
				if(!this.current_spread.upgrade_id){
					uni.showToast({
						title:"请选择套餐",
						icon:'none'
					})
					return
				}
				this.$ajax.get('upgrade/buyPackage.html', {upgrade_id: this.current_spread.upgrade_id,is_balance: this.use_yue?1:0, is_corn: this.use_corn?1:0}, res=>{
					if(res.data.code!==1){
						uni.showToast({
							title: res.data.msg,
							icon:'none'
						})
						return
					}
					if(res.data.pay_status===1){
						// 使用金币或余额支付成功
						this.paySuccess(res.data.msg)
						return
					}else if(res.data.order_id){
						this.$ajax.get('upgrade/wxPayPackage.html', {order_id: res.data.order_id}, payInfo=>{
							if(payInfo.data.code == 1){
								this.handelPay(payInfo.data.data.timeStamp,payInfo.data.data.nonceStr,payInfo.data.data.package,payInfo.data.data.signType,payInfo.data.data.paySign)
							}else{
								uni.showToast({
									title:payInfo.data.msg,
									icon:"none",
									duration:5000
								})
							}
						})
					}
				})
			},
			paySuccess(msg){
				uni.showToast({
					title: msg||'支付成功'
				})
				setTimeout(() => {
					this.$navigateBack()
				}, 1500)
			},
			getUser(){
				this.$ajax.get('member/index.html', {}, (res) => {
					if (res.data.code == 1) {
						this.getUserInfo(res.data.user)
						this.jinbi = res.data.user.money_own
						this.shequ_money = res.data.user.shequ_money
						this.score = res.data.user.score
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: "none"
						})
					}
				})
			},
			withdrawFunds(){
				this.$navigateTo('/user/withdraw_funds')
			}
		}
	}
</script>

<style scoped lang="scss">
page{
	background-color: #fff;
}
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row{
  flex-direction: row;
}

.highlight{
	color: $uni-color-primary;
}

.header {
  margin: 24rpx 48rpx;
	align-items: center;
  border-radius: 30rpx;
  overflow: hidden;
	height: 278rpx;
	background-image: linear-gradient(90deg, #ff676c 0%, #fe8e8d 100%);
  .info {
    flex: 1;
		text-align: center;
		display: block;
		&.left{
			border-right: 1rpx solid #FB656A ;
		}
		&.right{
			border-left: 1rpx solid #FF989B ;
		}
    .title {
      line-height: 1;
      margin-bottom: 10rpx;
      font-size: 26rpx;
      color: #fff;
    }
    .integral{
      font-size: 64rpx;
      color: #fff;
    }
		.btn{
			margin-top: 24rpx;
			display: inline-block;
			height: 48rpx;
			line-height: 46rpx;
			padding: 0 24rpx;
			font-size: 22rpx;
			border-radius: 24rpx;
			border: 1rpx solid #fff;
			color: #fff;
		}
  }
}

.select_type{
	justify-content: center;
	.select_option{
		font-size: 36rpx;
		font-weight: bold;
		margin: 24rpx;
		padding: 24rpx;
		white-space: nowrap;
		position: relative;
		&.active{
			color: $uni-color-primary;
			&::after{
				content:'';
				position: absolute;
				height: 8rpx;
				border-radius: 4rpx;
				background-color: $uni-color-primary;
				left: 40%;
				right: 40%;
				bottom: 0;
			}
		}
	}
}

.exchange-box{
  padding: 24rpx 48rpx;
}

.inp-box{
  padding: 20rpx 0;
  justify-content: space-between;
  input{
    padding: 0 20rpx;
  }
  .computed{
    display: block;
  }
}
.tip{
  margin-top: 24rpx;
  font-size: 22rpx;
  color: $uni-color-primary;
}

.pay_type{
	margin-top: 50rpx;
	.title{
		font-size: 40rpx;
		font-weight: bold;
		margin-bottom: 24rpx;
	}
	.pay_item{
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 0;
		.pay_name{
			.text{
				margin-left: 20rpx;
			}
		}
	}
}

.button-box {
  margin-top: 48rpx;
  padding: 24rpx 0;
  padding-bottom: 32rpx;
  .button {
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    background: #fb656a;
    box-shadow: 0 8rpx 32rpx 0 rgba(251, 101, 106, 0.4);
    border-radius: 44rpx;
    border-radius: 44rpx;
  }
}


.options-box{
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 0 48rpx;
	.options{
		width: 31.5%;
		height: 132rpx;
		margin-bottom: 18rpx;
		border: 1rpx solid #d8d8d8;
		border-radius: 8rpx;
		padding: 24rpx;
		position: relative;
		color: #666;
		text-align: center;
		overflow: hidden;
		&.vacancy{
			height: 0;
			padding: 0;
			border: 0;
		}
		&.active{
			background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
			border-color: $uni-color-primary;
			.title{
				color: #333;
			}
			.tip{
				background-color: $uni-color-primary;
			}
		}
		&.selected{
			background-color: $uni-color-primary;
			border-color: $uni-color-primary;
			color: #fff;
			.more{
				color: #fff;
			}
		}
		.title{
			text-align: center;
			margin-bottom: 10rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #666;
		}
		.more{
			justify-content: center;
			align-items: center;
			margin-top: 20rpx;
			color: #666;
		}
		.tip{
			font-size: 22rpx;
			position: absolute;
			bottom: 0;
			width: 100%;
			left: 0;
			padding: 6rpx 0;
			background-color: #d8d8d8;
			color: #fff;
		}
	}
}


.spread-box{
	padding-left: 48rpx;
	.spread-list{
		align-items: flex-start;
		.spread-item{
			min-width: 82%;
			margin-right: 24rpx;
			border-radius: 18rpx;
			margin-bottom: 24rpx;
			box-shadow: 0 8rpx 20rpx 0 rgba(0,0,0,0.08);
			.spread-header{
				height: 280rpx;
				justify-content: space-between;
				padding: 32rpx 48rpx;
				background-size: 100% 100%;
				color: #fff;
				position: relative;
				.block{
					display: block;
				}
				.name{
					overflow: hidden;
					text-overflow: ellipsis;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					display: -webkit-box;
					margin-bottom: 16rpx;
					font-size: 36rpx;
				}
				.aligin-center{
					align-items: center;
				}
				.time{
					display: inline-block;
					padding: 0 16rpx;
					height: 32rpx;
					line-height: 32rpx;
					border-radius: 16rpx;
					font-size: 22rpx;
					background-color: rgba(0, 0, 0, 0.2);
				}
				.price_row{
					justify-content: space-between;
					align-items: flex-end;
					>view{
						line-height: 1;
						font-size: 22rpx;
					}
					.price-box{
						align-items: flex-end;
					}
					.price{
						position: relative;
						bottom: -6rpx;
						font-size: 64rpx;
					}
				}
				.check_box{
					position: absolute;
					top: 24rpx;
					right: 24rpx;
					width: 48rpx;
					height: 48rpx;
					border-radius: 50%;
					border: 4rpx solid #fff;
					align-items: center;
					justify-content: center;
				}
			}
			.info{
				padding: 24rpx 48rpx 48rpx 48rpx;
				border: 1rpx solid #eee;
				border-bottom-left-radius: 16rpx;
				border-bottom-right-radius: 16rpx;
				.descp{
					margin-bottom: 24rpx;
					font-size: 28rpx;
					line-height: 1.5;
					white-space: break-spaces;
				}
			}
			&.empty{
				padding: 0;
				height: 1rpx;
				min-width: 24rpx;
			}
		}
	}
}

// .use_corn{
// 	flex-direction: row;
// 	justify-content: flex-end;
// 	padding: 16rpx 0;
// 	color: #666;
// 	.check_icon{
// 		width: 36rpx;
// 		height: 36rpx;
// 		margin-left: 24rpx;
// 		margin-right: 12rpx;
// 		border-radius: 4rpx;
// 		border: 2rpx solid $uni-color-primary;
// 		align-items: center;
// 		justify-content: center;
// 		&.active{
// 			background-color: $uni-color-primary;
// 		}
// 	}
// }

.gave_list{
	.gave_item{
		flex-direction: row;
		justify-content: space-between;
		padding: 16rpx 24rpx;
		color: #666;
		&.l{
			background-color: #fff3ec;
		}
	}
}


.pay_type{
		margin-top: 50rpx;
		.title{
				font-size: 40rpx;
				font-weight: bold;
				margin-bottom: 24rpx;
				color: #333;
		}
		.pay_item{
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 0;
				.pay_name{
					align-items: center;
						.text{
							margin-left: 20rpx;
						}
						.tip{
							margin: 0;
							color: #999999;
						}
				}
		}
}
.pay_ment{
		justify-content: flex-end;
		color: #333;
}

.surplus{
	width: 600rpx;
	margin: auto;
	border-radius: 16rpx;
	overflow: hidden;
	background-color: #fff;
	.title{
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		font-size: 36rpx;
		color: #fff;
		background-image: linear-gradient(-45deg, #FFC072 0%, #FF8D3E 100%);;
	}
	.info{
		padding: 48rpx;
	}
	.time{
		margin-bottom: 32rpx;
		padding: 0;
		line-height: 1;
		padding-left: 10rpx;
		border-left: 4rpx solid #FF9344;
	}
}
.close{
		width: 60rpx;
		height: 60rpx;
		margin: 32rpx auto;
		align-items: center;
		justify-content: center;
	}
	.note-container{
		margin-right: 48rpx;
	}
	.note{
		padding: 24rpx;
		border-radius: 8rpx;
		overflow: hidden;
		line-height: 1.8;
		background-color: rgba($uni-color-primary, 0.15);
		border-left: 6rpx solid $uni-color-primary;
		color: #333;
		view{
			font-size: 26rpx;
		}
	}
	.log_tip{
		color: $uni-color-primary;
		flex-direction: row;
		font-size: 30rpx;
		padding: 16rpx 0;
		margin-bottom: 24rpx;
		align-items: center;
	}
	.taocan {
		// padding: 0 48rpx;
		.title{
			align-items: center;
			.title_img{
				width:44rpx;
				height: 44rpx;
				overflow: hidden;
				image{
					width: 100%;
				}
			}
			.title_text{
				color: #303030;
				font-weight: bold;
				font-size: 32rpx;
				margin-left: 16rpx;
			}
		}
		.top {
			margin-top: 40rpx;
			justify-content:space-between;
			align-items: center;
			.top_item {
				width:180rpx;
				height: 180rpx;
				align-items: center;
				justify-content: center;
				background-position: top;
				background-repeat: no-repeat;
				background-size: contain;
				.top_item_con {
					font-size: 24rpx;
					color: #7B7B7B;
					text-align: center;
					.top_item_con_num{
						text-align: center;
						color: #303030;
					}
				}
			}
		}
		.bottom {
			justify-content: space-between;
			.bottom_item {
				padding:0 24rpx;
				.botttom_img_con{
					width: 28rpx;
					height: 12rpx;
					border-radius:5px;
					background-color:#EE6E3D;
					&.botttom_img_con1{
						background-color:#3574F8;
					}
					&.botttom_img_con2{
						background-color:#F69F42;
					}
				}
				.bottom_item_con {
					margin-top: 12rpx;
					.bottom_item_con_title {
						color: #7B7B7B;
						font-size: 28rpx;
						margin-bottom: 20rpx;
					}
					.bottom_item_con_num {
						color: #303030;
						font-size: 28rpx;
					}
				}
				
			}
			.bottom_item_line{
					// height: 50rpx;
					align-self: flex-end;
					padding-bottom:10rpx;
					// width: 2rpx;
					
					// &::before{
					// 	content: "";
					// 	height:80rpx;
					// 	width: 2rpx;
					// 	background: #7B7B7B;
					// }
				}
		}
	}
</style>
