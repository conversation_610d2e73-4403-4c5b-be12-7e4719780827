<template>
  <view class="page">
    <tab-bar class="nav" :tabs="type_list" :nowIndex="current_type_index" @click ="switchType">
      </tab-bar>
    
    <view class="house_list">
      <template v-if="!param.categoryid">
        <view class="filter_list" v-if="params.categoryid==4">
      <tab-bar :tabs="filter_list" :fixedTop="false" :showLine="false" :nowIndex="current_filter_index">
        <view
          class="filter_item"
          :class="{ active: index === current_filter_index }"
          :id="'i' + index"
          v-for="(item, index) in filter_list"
          :key="index"
          @click="onClickFilter(item, index)"
          >{{ item.name }}</view
        >
      </tab-bar>
        </view>
      </template>
      <block v-for="item in listsData" :key="item.id">
        <list-item v-if="item.parentid == 1" :item-data="item" from="mendian" type="sale" @click="toDetail"></list-item>
        <list-item v-if="item.parentid == 2" :item-data="item" from="mendian" type="rent" @click="toDetail"></list-item>
        <list-item v-if="item.parentid == 3" :item-data="item" from="mendian" type="transfer" @click="toDetail"></list-item>
      </block>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
  </view>
</template>

<script>
import { uniLoadMore } from '@dcloudio/uni-ui'
import tabBar from '../../components/tabBar.vue'
import listItem from '../components/listItem.vue'
export default {
  components: {
    uniLoadMore,
    tabBar,
    listItem,
  },
  data() {
    return {
      get_status: 'loading',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了',
      },
      params: {
        parentid: 1,
        categoryid:1,
        page: 1,
        rows: 20,
      },
      param:{
        page: 1,
        rows: 20,
        info_type: 1,
        info_id:'',
        uid:'',
        categoryid:1
      },
      serve:'',
      nowTab: 0,
      listsData: [],
      current_filter_index: 0,
      current_type_index:0
    }
  },
  computed: {
    filter_list(){
      return [
        {
          name:'出售',
          type:1
        },
        {
          name:'出租',
          type:2
        },
        {
          name:'转让',
          type:3
        }
      ]
    },
    type_list(){
      return [
        {
          name:'商业出售',
          type:1
        },
        {
          name:'商业出租',
          type:2
        },
        {
          name:'生意转让',
          type:3
        },
        {
          name:'土地转让',
          type:4
        }
      ]
    }
  },
  onLoad(options) {
    this.current_filter_type =''
    if(options.uid){
      this.params.uid=options.uid
      this.param.uid = options.uid
    }
    this.param.info_type = options.info_type
		this.param.info_id = options.info_id
		this.serve = options.serve
    if(options.id){
      this.params.id=options.id
    }
    this.getData()
  },
  onShow() {
  },
  onUnload() {
  },
  methods: {
    getData() {
      this.get_status = 'loading'
      var url;
			var from;
      // if (this.param.categroyid==1) {
			// 		this.param.categroyid=''
			// }
			if (this.serve==1) {
      	url = 'infoServicer/historyInfoList'
      	from = this.param
      }else{
      	url = 'infoAudit/estateList'
      	from = this.params
      } 
      this.$ajax.get(url, from, (res) => {
        if (from.page == 1) {
          this.listsData = []
        }
        if (res.data.code == 1) {
          this.listsData = this.listsData.concat(res.data.list)
          if (res.data.list.length < this.params.rows) {
            this.get_status = 'noMore'
          } else {
            this.get_status = 'more'
          }
        } else {
          this.get_status = 'noMore'
        }
      
      }, (err)=> {
        console.log(err)
      })
      
    },
    onClickFilter(item, index) {
      console.log(item);
      this.current_filter_index = index
      this.params.parentid = item.type
      this.params.page = 1
      this.param.page = 1
      this.getData()
    },
    switchType(e){
      console.log(e);
      this.current_type_index =e.index
      this.params.categoryid = e.type
      this.param.categoryid = e.type
      this.getData()
    },
    toDetail(e) {
      if (!e.detail.id) {
        return
      }
      this.$store.state.tempData = e.detail
      if (e.detail.parentid == 1) {
        this.$navigateTo('/commercial/sale/detail?id=' + e.detail.id)
      }
      if (e.detail.parentid == 2) {
        this.$navigateTo('/commercial/rent/detail?id=' + e.detail.id)
      }
      if (e.detail.parentid == 3) {
        this.$navigateTo('/commercial/transfer/detail?id=' + e.detail.id)
      }
    },
  },
  onReachBottom() {
    if (this.get_status == 'more') {
      this.params.page = this.params.page + 1
      this.param.page = this.param.page + 1
      this.getData()
    }
  },
}
</script>

<style lang="scss" scoped>
.page {
  background: #fff;
	.nav{
			top:0;
	}
}
.house_list {
  padding: 90rpx 48rpx 0;
  background-color: #fff;
}

</style>
