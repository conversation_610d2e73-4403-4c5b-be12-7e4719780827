import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)
// 获取用户经纬度
const getPosition = function(wx,fun){
	if(this.position.lat&&this.position.lng){
		fun({ lat: this.position.lat, lng: this.position.lng })
		return
	}
	// 部分手机获取不到位置且不走失败回调，所以添加个计时器超时提示获取位置失败
	let timer = setTimeout(()=>{
		fun({})
		uni.showToast({
			title:"获取位置超时",
			icon:"none"
		})
	},5000)
	console.log("开始获取")
	wx.ready(()=>{
		wx.getLocation({
			type:'gcj02',
			success: (res) => {
				console.log("获取成功")
				clearTimeout(timer)
				this.position.lat = res.latitude
				this.position.lng = res.longitude
				fun({ lat: this.position.lat, lng: this.position.lng })
				console.log(JSON.stringify(res));
			},
			fail: err=>{
				clearTimeout(timer)
				fun({})
				console.log("获取失败",err)
			},
			cancel: (res) => {
				clearTimeout(timer)
				fun({})
				alert('用户拒绝授权获取地理位置');
			}
		});
	})
}
// 聊天初始化数据格式
let chatData = { socketOpen: false, chatIndex: 0, nowChat: {}, chatList: [], tempFriends:[], myChatInfo:{}}
if (uni.getStorageSync('chatData')){
	chatData.chatList = JSON.parse(uni.getStorageSync('chatData'))
}
const store = new Vuex.Store({
    state: {
		position:{
			lat:"",
			lng:""
		},
		toReg:false,
		getPosition,
		// 位置信息
		location: {
			latitude: '',
			longitude: ''
		},
		defaultAvatar:"/images/new_icon/default_avatar.png",
		shareStyles:{},
		user_info:{},
		buildInfo: {},
		autoSendMsg:'', //记录需要自动发送的消息的内容
		systemInfo:{},
		allowOpen:true,
		updatePageData:false, //记录返回到一个页面时是否允许获取页面数据
		tempData:{}, //缓存列表数据
		imgSize: 10240, //上传图片大小限制，后台设置的是kb
		statistics:"",
		codeLogin:'0',
		loginByBaiduUnion: 0, //百度联合登陆状态
		hasWechat:true, //是否安装微信  ios审核判断
		huxingInfo:{}, //转到户型图数据
		ter_isretail: 0, //是否开启分销报功能
		shareList:{}, //分享详情的置业顾问头像 名称
		styles:{},   //置业顾问背景样式
		topicSkin:'', //专题模板样式
		agentStyles: {}, //经纪人背景样式
		vrlink_ischeck:0, //是否已验证vr地址
		switch_community_expert: null,
		appIcon:'',  //iosapp三网登录图标
		use_middle_call_house: 0, //1：个人和中间信息都开启隐私号 2：中介信息开启隐私号 3：个人信息开启隐私号
		im: {
			socketTask:null,
			friendList:[],
			blackFriendList:[],
			myChatInfo:{},
			adviser: 1, //是否全局开启置业顾问 
			ischat: 1, //是否全局开启聊天
			istelcall: 1, //是否全局开启中间号
			isNav:0,
			nowChat:{
				chatList:[]
			}
		}, //聊天数据
		zhuaqu_js:"",
		toUser:{}, //直聊后台需要对方的昵称和头像,这里存储一下
		home_switchs:{ //首页板块显示开关
			open_news:false,
			open_info:false,
			open_newhouse:false,
			open_agent:false,
			open_advsiver:false,
			allow_advsier_register:false
		},
		tel400jing:false, //是否自动拼接400电话后面的#号
		siteName:"",
		siteTel:"",
		adv_show_label:1,//是否显示广告
		code_status:1,//关闭注册和找回密码 1 开放
		on_click:false, //是否已点击，防止多次点击记录
		has_setting:false,
		audit_mode:0, //是否开启资讯和楼盘详情的评论的审核模式
		is_subscribe:0, //是否已经订阅消息
		unread_templateid_xcx:[], //订阅的消息id
		online_inviter:{
			inviter_id: "",//在线选房推荐者id 
			online_id: "",//在线项目id 
		},
		myonehouse:{},//单条房源信息
		is_open_share_douyin:0,
		isShowTaoshu: '', //预售是否显示住宅 非住宅套数
		user_login_status:'', //用户登录状态 1: 没登录;2: 没绑定手机号;3: 已登录已绑定手机号
		sub_form_mode: 0, //提交报名模式吗，0:系统默认姓名和手机号，不需要引导登录；1:简约模式，仅需要手机号，不需要引导登录； 2:引导登录模式，姓名手机号，需要验证手机号
		temp_huxing_contrast_ids:[], //未登录时添加的户型对比的id
		temp_ershou_contrast_ids:[], //未登录时添加的二手房对比的id
		temp_renting_contrast_ids:[], //未登录时添加的出租房对比的id
    temp_commercial_contrast_ids:[], //未登录时添加的商业地产对比的id
		wxqunopen:1,//是否显示添加微信客服
    if_info_verification_code: 0, //房源统一核验是否开启
		requesting_list: [],
		in_mobile: true,
		weapp_appid: '',
		SiteCity:'',
		wx_service_link: '',
		huiFilter:""
    },
    mutations: {
			mutationPosition(state,payload ){
				state.position.lat = payload.latitude
				state.position.lng = payload.longitude
			},
			getUserInfo(state,provider){
				state.user_info = provider
			},
			setAllowOpen(state,provider){
				state.allowOpen = provider
			},
			login(state, provider) {
				state.login = true;
				state.token = provider.token;
				state.userName = provider.userName;
				state.avatarUrl = provider.avatarUrl;
			},
			logout(state) {
				state.login = false;
				state.token = '';
				state.userName = '';
				state.avatarUrl = '';
			},
			setOnlineInviter(state, provider){
				state.online_inviter = provider
			}
		},
		actions:{
			// 获取位置经纬度
			getLocation(ctx, options) {
				if (ctx.state.location.latitude && ctx.state.location.longitude) {
					if (options.success) {
						options.success(ctx.state.location)
					}
					return
				}
				// 部分手机获取不到位置且不走失败回调，所以添加个计时器超时提示获取位置失败
				const timer = setTimeout(() => {
					if (options.fail) {
						options.fail({ msg: '获取位置超时' })
					}
					// uni.showToast({
					//   title: '获取位置失败',
					//   icon: 'none'
					// })
				}, 3500)
				if (options.wx) {
					options.wx.getLocation({
						type: 'gcj02',
						success: res => {
							clearTimeout(timer)
							// ctx.commit('setLocation', res)
							if (options.success) {
								options.success(res)
							}
						},
						fail: err => {
							clearTimeout(timer)
							console.log('获取位置失败：', err)
							// uni.showToast({
							//   title: '获取位置失败',
							//   icon: 'none'
							// })
							if (options.fail) {
								options.fail(err)
							}
						}
					})
				} else {
					uni.getLocation({
						type: 'gcj02',
						success: res => {
							clearTimeout(timer)
							// ctx.commit('setLocation', res)
							if (options.success) {
								options.success(res)
							}
						},
						fail: err => {
							clearTimeout(timer)
							console.log('获取位置失败：', err)
							// uni.showToast({
							//   title: '获取位置失败',
							//   icon: 'none'
							// })
							if (options.fail) {
								options.fail(err)
							}
						}
					})
				}
			}
		}
})
export default store