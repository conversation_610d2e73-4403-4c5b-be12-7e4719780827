<template>
  <view class="page">
    <view class="sherch-box">
      <mySearch mode="small_round" placeholder="请输入楼盘名称" @input="getData"></mySearch>
    </view>
    <view class="build_list">
      <view class="build_item bottom-line" v-for="item in builds" :key="item.id" @click="$navigateTo('/contrast/replace_house_list?bid='+item.id)">
        {{item.title}}
      </view>
    </view>
  </view>
</template>

<script>
import mySearch from '../components/mySearch'
export default {
  components: {
    mySearch
  },
  data() {
    return {
      builds:[]
    }
  },
  methods: {
    getData(e) {
      this.$ajax.get(
        'index/search.html',
        { keywords: e, type: 1 },
        res => {
          if (res.data.code == 1) {
            this.builds = res.data.list
          } else {
            this.builds = []
          }
        },
        err => {
          console.log(err)
        }
      )
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  height: calc(100vh - 44px);
  background-color: #fff;
}
.sherch-box {
  padding: 0 48rpx;
}

.build_item{
  padding: 35rpx 48rpx;
}
</style>
