<template>
<view class="user_info">
    <my-input label="真实姓名: " name="uname" :value="user_info.uname" maxlength="4" placeholder="请输入真实姓名" @input="handleInput" ></my-input>
    <my-input label="手机号码:" name="tel" :value="user_info.tel" placeholder="请输入手机号" type="number" @input="handleInput"></my-input>
    <my-input label="身份证号:" name="id_card" :value="user_info.id_card" placeholder="请输入身份证号码" type="idcard" @input="handleInput"></my-input>
    <!-- <view class="flex-box flex-center bottom-line" style="background-color:#ffffff">
        <my-input label="手机号码:" type="number" disabled :value="user_info.tel"></my-input>
        <text style="font-size:24upx;padding:4upx 20upx;background-color:#f1f1f1;color:#999;border-radius:12upx" @click="toEditPhone">修改手机号</text>
    </view> -->
    <view class="botom-fixed btn-box">
        <button class="default" @click="subData()">提交修改</button>
    </view>
</view>
</template>

<script>
import myInput from "../components/form/myInput.vue"
export default {
    data() {
        return {
            user_info:{
                uname:"",
                tel:"",
                id_card:""
            }
        }
    },
    onLoad(){
        this.getData()
    },
    components: {
        myInput
    },
    methods:{
        getData(){
            this.$ajax.get('online_my/userOnlineInfo',{},res=>{
                if(res.data.code === 1&&res.data.info){
                    this.user_info = res.data.info
                }
            })
        },
        handleInput(e){
            this.user_info[e._name] = e.detail.value
        },
        subData(){
            if(!this.user_info.uname){
                uni.showToast({
                    title:"请输入真实姓名",
                    icon:'none'
                })
                return
            }
            if(!this.user_info.tel){
                uni.showToast({
                    title:"请输入手机号",
                    icon:'none'
                })
                return
            }
            if(this.user_info.tel.length<11||this.user_info.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return
            }
            this.$ajax.post('online_my/addUserOnlineInfo',this.user_info,res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title:res.data.msg
                    })
                    setTimeout(()=>{
                        uni.navigateBack()
                    },1500)
                }else{
                     uni.showToast({
                        title:res.data.msg
                    })
                }
            })
        }
    }
}
</script>

<style scoped lang="scss">

</style>
