<template>
<view>
    <my-popup ref="sub_form_popup" position="center" :height="sub_box_height+'px'" :touch_hide="false">
        <view class="sub_box" id="sub_box">
            <view class="sub_header">
                <view class="sub_title">{{sub_title||title}}</view>
                <view class="sub_tip" v-if="groupCount">已有<text>{{groupCount}}</text>人订阅该楼盘</view>
                <view class="icon">
                <image  mode="widthFix" :src="'/images/new_icon/hb.png' | imageFilter('m_320')"></image>
                </view>
            </view>
            <view class="form_box">
                <view class="sub_content">{{sub_content||content}}</view>
                <view class="sub_form">
                    <input class="sub_tel" v-if="sub_mode===0||sub_mode===2" maxlength="10" type="text" placeholder="称呼" @input="inputName" />
                    <input class="sub_tel" maxlength="11" type="number" placeholder="手机号" @input="inputPhone" />
                    <view class="flex-box sms_code_inp" v-if="show_sms_code">
                        <input class="flex-1" type="text" placeholder="请输入验证码" @input="inputCode" />
                        <view class="send-code" :class="sending?'disable':''" @click="getVerify()">{{time?time+'s':'获取验证码'}}</view>
                    </view>
                    <view class="btn-box">
                        <!-- #ifdef MP-WEIXIN -->
                        <!-- <button v-if="login_status==1" open-type="getUserInfo" @getuserinfo="onGetUserInfo" class="default">{{sub_submit}}</button>
                        <button v-else-if="login_status==2" open-type="getPhoneNumber" @getphonenumber="onGetPhonenumber" class="default">{{sub_submit}}</button> -->
                        <button class="default" @click="subData()">{{sub_submit}}</button>
                        <!-- #endif -->
                        <!-- #ifndef MP-WEIXIN -->
                        <button class="default" @click="subData()">{{sub_submit}}</button>
                        <!-- #endif -->
                        <view class="close_btn" @click="closeSub()">取消</view>
                    </view>
                </view>
            </view>
            <view class="verify_block" v-show="show_verify">
                <drag-verify ref="verify" :verify_img="verify_img" :verify_fail="verify_fail" :verify_success="verify_success" @ondragend="onDragEnd" @onrefreshend="onRefreshEnd" @onrefresh="onRefresh"></drag-verify>
            </view>
        </view>
    </my-popup>
</view>
</template>

<script>
import myPopup from './myPopup.vue'
import myIcon from './myIcon.vue'
import dragVerify from "./dragVerify.vue"
export default {
    data() {
        return {
            sending: false,
            sub_box_height: 'initial',
            time: 0,
            show_verify: false,
            code_token: '',
            verify_img: '',
            verify_fail: false,
            verify_success: false,
            show_sms_code:true,
            title:'',
            content:''
        }
    },
    props: {
        sub_title: {
            type: [String],
            default: '发送地址到手机'
        },
        sub_content: {
            type: [String],
            default: ''
        },
        groupCount: {
            type: [String,Number],
            default: ''
        },
        sub_mode:{ // 0:系统默认姓名和手机号，不需要引导登录；1:简约模式，仅需要手机号，不需要引导登录； 2:引导登录模式，姓名手机号，需要验证手机号, 3: 引导登录模式，只需要手机号
            type:[Number],
            default:0
        },
        sub_submit:{
            type: [String],
            default: '提交获取'
        },
        after_login_auto_handle: { //登录成功后是否自动处理一下逻辑
            type: Boolean,
            default: true
        }
    },
    computed:{
        login_status(){
            return this.$store.state.user_login_status
        }
    },
    components: {
        myIcon,
        myPopup,
        dragVerify
    },
    watch:{
        
    },
    methods: {
        showPopup() {
            // #ifdef MP-WEIXIN
            this.getLoginStatus()
            // #endif
            this.$nextTick(()=>{
                const query = uni.createSelectorQuery().in(this);
                setTimeout(() => {  //适配百度小程序加载高度不够的问题
                    query.select('#sub_box').boundingClientRect(data => {
                        this.sub_box_height = data.height+20
                        
                        this.$refs.sub_form_popup.show()
                    }).exec();
                }, 50);
                
            })
        },
        hide(){
            this.$refs.sub_form_popup.hide()
        },
        inputName(e){
            this.name = e.detail.value
        },
        inputPhone(e) {
            this.tel = e.detail.value
        },
        inputCode(e) {
            this.sms_code = e.detail.value
        },
        closeSub() {
            this.$refs.sub_form_popup.hide()
            this.show_verify = false
            this.sending = false
            this.show_sms_code = true
        },
        // 获取滑块验证码
        getVerify() {
            // #ifndef MP-WEIXIN
            if (this.sending) {
                return
            }
            if (!this.tel) {
                uni.showToast({
                    title: '请输入手机号',
                    icon: 'none'
                })
                return
            }
              // #ifdef H5
            var url = this.$route.fullPath
            // #endif
            // #ifndef H5
            var pages = getCurrentPages()
            var currentPage = pages[pages.length - 1] //获取当前页面的对象
            var url = currentPage.route //当前页面url
            var options = currentPage.options //当前页面url参数
            let i = 0
            url='/'+url;
            for(let key in options){
                if(i===0){
                    url+=`?${key}=${options[key]}`
                }else{
                    url+=`&${key}=${options[key]}`
                }
                i++
            }
            // #endif
            this.url = url
            this.$emit('signUp',{name:this.name,tel:this.tel,page_url:this.url,title:this.sub_title||this.title,desc:this.desc,showMesssge:1})
            this.$ajax.get('member/slideToken', {}, res => {
                if (res.data.code === 1) {
                    this.verify_img = res.data.url
                    this.code_token = res.data.imgCode
                    this.show_verify = true
                }
            }, err => {

            })
            // #endif
        },
        onRefresh(){
            this.getVerify()
        },
        // 滑块验证码重置完成的回调
        onRefreshEnd() {
            this.verify_success = false
            this.verify_fail = false
        },
        // 用户滑动验证码结束的回调
        onDragEnd(value){
            this.sendSmsCode(value)
        },
        sendSmsCode(verify_code) {
            if(!verify_code){
                uni.showToast({
                    title:"请先滑动验证",
                    icon:"none"
                })
                return
            }
            if(!this.tel){
                uni.showToast({
                    title:'请输入手机号',
                    icon:'none'
                })
                return
            }
            if(this.tel.length<11||this.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return
            }
            this.$ajax.get('member/checkSlideCode', {
                tel: this.tel,
                imgCode: this.code_token,
                code: verify_code
            }, res => {
                if (res.data.code === 1) {
                    this.verify_success = true
                    this.sms_token = res.data.code_token || ''
                    uni.showToast({
                        title: res.data.msg
                    })
                    // 显示填写短信验证码
                    this.show_sms_code = true
                    this.$nextTick(()=>{
                        const query = uni.createSelectorQuery().in(this);
                        setTimeout(() => {
                            query.select('#sub_box').boundingClientRect(data => {
                                this.sub_box_height = data.height+20
                            }).exec();
                            // 隐藏拖动验证码
                            this.show_verify = false
                        }, 50);  //适配百度小程序加载高度不够的问题                      
                    })
                    this.time = 60
                    this.sending = true
                    if (this.timer) {
                        clearInterval(this.timer)
                    }
                    this.timer = setInterval(() => {
                        if (this.time <= 0) {
                            clearInterval(this.timer)
                            this.sending = false
                            return
                        }
                        this.time--
                    }, 1000)
                } else {
                    this.verify_fail = true
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                    this.getVerify()
                }
            }, err => {
                this.verify_fail = true
                uni.showToast({
                    titley: '验证失败',
                    icon: 'none'
                })
                this.getVerify()
            })
        },
        // #ifdef MP-WEIXIN
        getLoginStatus(){
            // 检测登录状态
            this.$ajax.get('member/checkUserStatus',{},res=>{
                this.$store.state.user_login_status = res.data.status
            })
        },
        onGetPhonenumber(res){
            if(res.target.encryptedData&&res.target.iv){
                this.bindPhone(res.target.encryptedData, res.target.iv)
            }else{
                this.$navigateTo('/user/bind_phone/bind_phone')
            }
        },
        onGetUserInfo(res){
        if(res.target.userInfo&&res.target.userInfo.avatarUrl&&res.target.userInfo.nickName){
            uni.login({
            provider: 'weixin',
            success: (loginRes)=> {
                this.getToken(loginRes.code,res.target.userInfo.avatarUrl,res.target.userInfo.nickName)
            }
            })
        }else{
            uni.removeStorageSync('token')
            this.$navigateTo('/user/login/login')
        }
        },
        getToken(code,avatarUrl,nickName){
        this.$ajax.get("member/getOpenidByCode.html",{code,headimgurl:avatarUrl,nickname:nickName},(res)=>{
            if(res.data.code ==1){
                // this.getUserInfo(res.data.user)
                // 存储token
                uni.setStorageSync('token',res.data.token)
                uni.showToast({
                    title:'登录成功'
                })
                if(res.data.tel){
                    this.subData()
                }else{
                    this.$store.state.user_login_status = 2
                }
            }else{
                uni.showToast({
                    title:res.data.msg,
                    icon:"none"
                })
            }
        })
        },
        bindPhone(encryptedData, iv){
        this.$ajax.get('member/getWxPhoneNumber',{encryptedData,iv},(res)=>{
            if(res.data.code == 1){
                uni.showToast({
                    title:res.data.msg
                })
                if(res.data.token){ //绑定手机号成功后台返回一个新的token
                    uni.setStorageSync('token',res.data.token)
                }
                this.subData()
                this.$store.state.user_login_status = 3
            }
        })
        },
        // #endif
        subData(){
            if(!this.name&&(this.sub_mode===0||this.sub_mode===2)){
                uni.showToast({
                    title:'请输入用户名',
                    icon:'none'
                })
                return
            }
            if(!this.tel){
                uni.showToast({
                    title:'请输入手机号',
                    icon:'none'
                })
                return
            }
            if(this.tel.length<11||this.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return
            }
            // if(this.login_status==1){
            //     this.authRegister()
            // }else{
                uni.showLoading({
                    title:"正在提交",
                    mask:true
                })
                this.$emit('onsubmit',{name:this.name,tel:this.tel,code:this.sms_code,code_token:this.sms_token})
            // }
        },
        authRegister(){
            if(!this.sms_code){
                uni.showToast({
                title:'请输入短信验证码',
                icon:'none'
                })
                return
            }
            this.$ajax.get('member/authRegister',{tel:this.tel,code_token:this.sms_token,code:this.sms_code},res=>{
                if (res.data.token) {
                    uni.setStorageSync('token', res.data.token)
                }
                if(res.data.code === 1){
                    this.show_sms_code = true
                    this.$emit('onsubmit',{name:this.name,tel:this.tel,sms_code:this.sms_code,page_url:this.url,title:this.sub_title||this.title,selectIndex:this.currentSelect,desc:this.desc})
                    // if(this.after_login_auto_handle){
                    //     this.closeSub()
                    //     uni.showToast({
                    //         title:'提交成功',
                    //         icon:'none'
                    //     })
                    // }
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            })
        },
    }
}
</script>

<style scoped lang="scss">
.sub_box{
  background-color: #fff;
  margin: 0 40rpx;
  border-radius: 16rpx;
  position: relative;
//   overflow-y: hidden;
  margin-top: 32rpx;
  .sub_header{
      padding: 24rpx 48rpx;
      color: #fff;
      background-image: linear-gradient(-41deg, #F7918F 0%, #FB656A 100%);
      position: relative;
      border-top-left-radius: 16rpx;
      border-top-right-radius: 16rpx;
      .sub_title{
        margin-bottom: 16rpx;
        font-size: 40rpx;
        font-weight: bold;
    }
    .sub_tip{
        font-size: 24rpx;
    }
    .icon{
        width: 188rpx;
        height: 188rpx;
        position: absolute;
        top: -32rpx;
        right: 48rpx;
        image {
            width: 100%;
            height: 100%;
        }
    }
  }
  .form_box{
      padding: 30rpx 48rpx;
  }
  .sub_content{
    font-size: 32rpx;
    line-height: 1.5;
    color: #333;
  }
  .sub_form{
    margin-top: 25rpx;
    .sms_code_inp{
        align-items: center;
        margin-bottom: 20upx;
    }
    .sub_tel{
        margin-bottom: 20rpx;
    }
    .entrustSelect{
        height: 80upx;
        background: #f5f5f5;
        margin-bottom: 20upx;
        display: flex;
        padding: 0 20rpx;
        color: #888;
        align-items: center;
        justify-content: space-between;
    }
    input{
      padding: 20rpx;
      font-size: 28rpx;
      background-color: #f5f5f5;
    }
    .send-code{
      margin-left: 10rpx;
      color: $uni-color-primary;
      &.disable{
        color: #888;
      }
    }
    .btn-box{
      padding: 10px 0 0 0;
      button{
          font-size: 34rpx;
          font-weight: bold;
          height: 88rpx;    
          line-height: 88rpx;    
        background: #FB656A;
        box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
        border-radius: 44rpx;
      }
      .close_btn{
          padding: 24rpx;
          text-align: center;
          color: #999;
      }
    }
  }
  .verify_block{
    position: absolute;
    left: 0;
    right: 0;
    top: 150rpx;
    bottom: 40rpx;
    background-color: #fff;
    z-index: 2;
  }
}

</style>
