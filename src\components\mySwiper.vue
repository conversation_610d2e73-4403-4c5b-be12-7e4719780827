<template>
<view class="swiper-box">
    <swiper :style="{'height':height}" class="banner" :indicator-dots="indicatorDots&&!customDots" :circular="circular" :duration="duration" :indicator-active-color="indicatorActiveColor" :autoplay="autoplay" :interval="interval" @change="onChange">
        <swiper-item v-for="(img,index) in focus" :key="index" @click="toBanner(img)">
            <view class="swiper-item" :class="{rounded:rounded}">
                <image :src="img.image | imageFilter('w_6401')" mode="aspectFill"></image>
                <view v-if="is_adv" class="marker">广告</view>
                <view v-else-if="img.is_show_label==1" class="marker">广告</view>
            </view>
        </swiper-item>
    </swiper>
    <view class="dot-list flex-box" v-if="customDots">
        <view class="dot" :style="{'background-color':current===index?indicatorActiveColor:''}" v-for="(img,index) in focus" :key="index"></view>
    </view>
</view>
</template>

<script>
export default {
    props: {
        focus: Array,
        height: {
            type: String,
            default: '45vw'
        },
        indicatorDots: Boolean,
        customDots: Boolean,
        circular: Boolean,
        duration: {
            type: Number,
            default: 300
        },
        indicatorActiveColor: {
            type: String,
            default: '#666666'
        },
        autoplay: Boolean,
        rounded: Boolean,
        interval: {
            type: [String, Number],
            default: 5000
        },
        is_adv: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            current:0
        }
    },
    methods: {
        toBanner(url) {
            if(url.url){
                this.$navigateTo(url.url)
            }else if(url.link){
                this.$navigateTo(url.link)
            }
        },
        onChange(e){
            this.current = e.detail.current
        }
    }
}
</script>

<style lang="scss" scoped>
.swiper-item {
    height: 100%;
    position: relative;
    &.rounded{
        border-radius: 20rpx;
        overflow: hidden;
    }
    .marker{
        line-height: 1;
        padding: 4rpx 10rpx;
        position: absolute;
        right: 12rpx;
        bottom: 10rpx;
        font-size: 20rpx;
        border-radius: 4rpx;
        background-color: rgba($color: #000000, $alpha: 0.5);
        color: #fff;
    }
}

.swiper-item image {
    width: 100%;
    height: 100%;
}
.swiper-box{
    position: relative;
    .dot-list{
        padding: 10rpx 48rpx;
        position: absolute;
        left: 0;
        bottom: 0;
        .dot{
            width: 14rpx;
            height:14rpx;
            margin: 8rpx;
            border-radius: 50%;
            background-color: rgba($color: #ffffff, $alpha: 0.5);
        }
    }
}
</style>
