<template>
    <view>
        <view class="head">
            <view class="address">{{SiteCity}}房地产</view>
            <view class="tit">经纪机构备案信息查询系统</view>
            <view class="search flex-row">
                <view class="inp flex-1 flex-row">
                    <view class="icon" @click="handleSearch">
                        <myIcon type="ic_sousuo" color="#999999" size="38rpx"></myIcon>
                    </view>
                    <input class="flex-1" type="text" placeholder="请输入您要搜索的内容" v-model="params.keywords" @confirm="handleSearch()"/>
                </view>
                <view class="submit" @click="handleSearch">确定</view>
            </view>
        </view>
        <view class="pageNum flex-row">
            <view>为您找到<text>{{count||0}}</text>个经纪机构</view>
            <!-- <view>第 1/300 页</view> -->
        </view>
        <view class="list">
            <view class="info" v-for="(item,index) in list" :key="index">
                <view class="top">统一社会信用代码：{{item.social_credit_code||'--'}}</view>   
                <view class="title flex-row">
                    <view class="logo" :style="{background:item.color}">{{item.logo_text}}</view>
                    <view class="name flex-1">{{item.name}}</view>
                </view>
                <view class="sort flex-row">
                    <view>
                        <text class="blue">{{item.legal_person||'--'}}</text>
                        <view>机构法人代表</view>
                    </view>
                    <text class="border"></text>
                    <view>
                        <text>{{item.record_cert_no||'--'}}</text>
                        <view>证书备案编号</view>
                    </view>
                    <text class="border"></text>
                    <view>
                        <text>{{item.record_cert_period||'--'}}</text>
                        <view>备案证书有效期限</view>
                    </view>
                </view>
                <view class="des flex-row">机构全称：<text>{{item.full_name||'--'}}</text></view>
                <view class="des flex-row">经营地址：<text class="blue">{{item.address||'--'}}</text></view>
            </view>
        </view>
        <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
        <view class="remark" v-if="remark">
            <view class="pink">友情提示</view>
            <view class="value">{{remark}}</view>
        </view>
    </view>
</template>
<script>
// #ifdef MP-BAIDU
import { setSeo } from '../../common/mixin'
// #endif
import myIcon from "@/components/myIcon";
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
    data(){
        return{
			params: {
				page: 1,
				rows: 20,
                keywords:''
			},
            count:'',
            list:[],
            get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
            seo:{},
            remark:''
        }
    },
    // #ifdef MP-BAIDU
    mixins: [setSeo],
    // #endif
    components: {
        myIcon,
        uniLoadMore
    },
    computed:{
        SiteCity(){
            return this.$store.state.SiteCity
        }
    },
    onLoad() {
        this.getData()
        
    },
	methods: {
        getData(){
            this.$ajax.get("agentCompany/beianCompanyList",this.params,(res) => {
				if (res.data.seo){
					this.seo =res.data.seo
				}
                setTimeout(() => {
                   uni.setNavigationBarTitle({
                        title:`${this.SiteCity}经纪机构备案信息查询系统 `
                    }) 
                }, 200);                
				if (res.data.code == 1) {
                    this.remark = res.data.remark
					this.list = this.list.concat(res.data.list);
                    let colorArr =['#8dcdb5','#62b4cc','#97a2e2','#e79178','#9eb5de','#53abfc','#e788c0','#d4a84f','#91c769']
                    this.list.forEach(item => {
                        item.color = colorArr[Math.floor(Math.random() * colorArr.length)]
                    });
                    this.count = res.data.count
					if (res.data.list.length < this.params.rows) {
						this.get_status = "noMore";
					} else {
						this.get_status = "more";
					}
					if (res.data.share) {
						this.share = res.data.share;
					}
				} else {
					this.get_status = "noMore";
				}
			});
        },
        onReachBottom(){
            if(this.get_status === "noMore"){
                return
            }
            this.params.page++
            this.getData()
        },
        handleSearch(){
            this.list = []
            this.params.page = 1
            this.getData()  
        }
    },
    onShareAppMessage() {
        return {
            title: this.seo.title || "",
            content: this.seo.keywords || ""
        };
	},
}
</script>
<style lang="scss" scoped>
.remark{
    padding: 24rpx;
    background: #fff;
    margin-top: 24rpx;
    .value{
        color: #999;
        font-size: 22rpx;
    }
    .pink{
        position: relative;
        color: #FF5C6A;
        padding-bottom: 20rpx;
        margin-bottom: 20rpx;
    }
    .pink::after{
        content: '';
        position: absolute;
        width: 100%;
        height: 2rpx;
        border-bottom: 2rpx solid #e0e0e0;
        left: 0;
        bottom: 0;
        -webkit-transform: scaleY(0.33);
        transform: scaleY(0.33);
    }
}
.address{
    font-size: 36rpx;
    font-weight: 500;
    color: #fff;
    text-align: center;
    margin-bottom: 24rpx;
}
.list{
    .info{
        background: #fff;
        padding: 30rpx 24rpx;
        margin-bottom: 24rpx;
        .des{
            color: #999;
            font-size: 22rpx;
            margin-top: 24rpx;
            text{
                flex: 1;
            }
            .blue{
                color: #197cff;
            }
        }
        .sort{
            justify-content: space-around;
            text-align: center;
            align-items: center;
            .border{
                position: relative;
                display: block;
                height: 50rpx;
            }
            .border::after{
                content: '';
                position: absolute;
                width: 2rpx;
                height: 80rpx;
                border-left: 2rpx solid #e0e0e0;
                left: 0;
                top: 0;
                -webkit-transform: scaleY(0.3);
                transform: scaleY(0.3);
            }
            >view{
                color: #999;
                flex: 1;
                view{
                    font-size: 22rpx;
                    margin-top: 10rpx;
                }
                text{
                    display: block;
                    margin: 30rpx 0 0 0;
                    height: 64rpx;
                    color: #333;
                    padding: 0 52rpx;
                    font-size: 22rpx;
                    overflow: hidden;
                }
                .blue{
                    color: #197cff;
                    font-size: 32rpx;
                }
            }
        }
        .title::after{
            content: '';
            position: absolute;
            width: 100%;
            height: 2rpx;
            border-bottom: 2rpx solid #e0e0e0;
            left: 0;
            bottom: 0;
            -webkit-transform: scaleY(0.33);
            transform: scaleY(0.33);
        }
        .title{
            margin-top: 24rpx;
            align-items: center;
            padding-bottom: 30rpx;
            position: relative;
            .name{
                color: #333;
                font-size: 36rpx;
                font-weight: 500;
                margin-left: 24rpx;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .logo{
                width: 64rpx;
                height: 64rpx;
                border-radius: 8rpx;
                background: pink;
                color: #fff;
                font-size: 22rpx;
                text-align: center;
                line-height: 32rpx;
                padding: 0 8rpx;
                box-sizing: border-box;
            }
        }
        .top{
            font-size: 24rpx;
            color: #999;
        }
    }
}
page{
    background: #f8f8f8;
}
.flex-row{
    display: flex;
	flex-direction: row;
}
.flex-1{
    flex: 1;
}
.pageNum{
    justify-content: space-between;
    color: #999;
    font-size: 22rpx;
    padding: 30rpx 24rpx;
    text{
        color: #f52e2e;
        margin: 0 10rpx;
    }
}
.head{
    background-image: url('https://images.tengfangyun.com/yidongduan/beian/beian_top_bg.png?x-oss-process=style/w_400');
    background-size: 100% 100%;
    padding: 100rpx 0 40rpx 0;
    .tit{
        font-size: 48rpx;
        font-weight: 500;
        color: #fff;
        text-align: center;
    }
    .search {
	align-items: center;
	box-sizing: border-box;
	margin: 30rpx 48rpx;
    background: #f8f8f8;
    border-radius: 8rpx;
	.inp {
		position: relative;
		padding: 20rpx 0 20rpx 20rpx;
		font-size: 22rpx;
		input {
			font-size: 28rpx;
            margin-left: 10rpx;
		}
	}
	.submit {
        text-align: center;
        color: #197cff;
		font-size: 28rpx;
        padding: 0 24rpx;
	}
}
}

</style>
