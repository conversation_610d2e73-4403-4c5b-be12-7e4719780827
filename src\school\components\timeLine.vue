<template>
  <view class="time-line-box">
    <view class="top-points" v-if="line_data.length>0">
      <view class="point"></view>
      <view class="point"></view>
      <view class="point"></view>
    </view>
    <view class="bottom-points" v-if="line_data.length>0">
      <view class="point"></view>
      <view class="point"></view>
      <view class="point"></view>
    </view>
    <view
      class="stage"
      :class="{ first_state: index === 0 }"
      v-for="(item, index) in line_data"
      :key="index"
      @click="$emit('click', index)"
    >
      <view class="node">
        <view class="point"></view>
      </view>
      <view class="vertical_line"></view>
      <view class="across_line"></view>
      <view class="stage_content">
        <view class="time">{{ item.ctime }}</view>
        <view class="desc">{{ item.title }}</view>
      </view>
      <view class="btn-box">
        <view class="btn">查看详情</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  components: {},
  data() {
    return {}
  },
  props: {
    line_data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  methods: {}
}
</script>

<style scoped lang="scss">
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.time-line-box {
  padding: 84rpx 20rpx 50rpx 20rpx;
  position: relative;
  .top-points {
    position: absolute;
    left: 60rpx;
    top: 0;
    .point {
      width: 8rpx;
      height: 8rpx;
      border-radius: 50%;
      background-color: $uni-color-primary;
      margin-bottom: 10rpx;
    }
  }
  .bottom-points {
    position: absolute;
    left: 60rpx;
    bottom: 0;
    .point {
      width: 8rpx;
      height: 8rpx;
      border-radius: 50%;
      background-color: #ffa99d;
      margin-top: 10rpx;
    }
  }
}
.stage {
  margin: 0 20rpx;
  padding-bottom: 80rpx;
  position: relative;
  &.first_state {
    .node {
      width: 66rpx;
      height: 66rpx;
      left: -8rpx;
      background-color: $uni-color-primary;
      .point {
        width: 50rpx;
        height: 50rpx;
      }
    }
    .vertical_line {
      // background-color: $uni-color-primary;
      top: -30rpx;
      border-top-right-radius: 8rpx;
      border-top-left-radius: 8rpx;
      background: linear-gradient(
        to bottom,
        $uni-color-primary,
        $uni-color-primary,
        #ffa99d
      );
    }
    .across_line {
      top: 29rpx;
      background-color: $uni-color-primary;
    }
    .stage_content {
      padding-top: 16rpx;
      .time {
        color: $uni-color-primary;
      }
    }
  }
  .node {
    width: 50rpx;
    height: 50rpx;
    padding: 8rpx;
    border-radius: 50%;
    background-color: #ffa99d;
    display: inline-block;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    .point {
      width: 34rpx;
      height: 34rpx;
      border-radius: 50%;
      background-color: #ffffff;
    }
  }
  .vertical_line {
    position: absolute;
    left: 21rpx;
    // top: -30rpx;
    top: 0;
    bottom: 0;
    width: 8rpx;
    background-color: #ffa99d;
    z-index: 1;
  }
  .across_line {
    position: absolute;
    width: 30rpx;
    left: 50rpx;
    top: 21rpx;
    height: 8rpx;
    background-color: #ffa99d;
    z-index: 1;
    border-top-right-radius: 8rpx;
    border-bottom-right-radius: 8rpx;
  }
  .stage_content {
    padding-left: 100rpx;
    padding-top: 8rpx;
    margin-bottom: 20rpx;
    .time {
      color: #ffa99d;
      font-size: 32rpx;
      margin-bottom: 20rpx;
      font-weight: bold;
    }
    .desc {
      padding: 10rpx;
      line-height: 1.5;
      font-size: 30rpx;
    }
  }
  .btn-box {
    padding: 20rpx 0;
    flex-direction: row;
    justify-content: flex-end;
    .btn {
      padding: 0 32rpx;
      height: 54rpx;
      line-height: 54rpx;
      border-radius: 30rpx;
      font-size: 24rpx;
      background: #fef2ee;
      color: $uni-color-primary;
    }
  }
}
</style>
