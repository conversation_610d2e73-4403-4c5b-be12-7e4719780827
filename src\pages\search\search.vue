<template>
	<view class="content p-top-80 search-page">
		<view class="search-title flex-box">
			<view class="input-box flex-box flex-1">
				<view class="flex-box select right-line" @click="showAction">
					<text>{{name}}</text>
					<my-icon type="ic_open" size="32rpx" v-if="!from"></my-icon>
				</view>
				<view class="inp-box-def search-box flex-1">
					<input  type="text" confirm-type="search" @input="handelInput" @confirm="handelSearch" :value="keywords" focus :maxlength="20" placeholder-style="font-size:28rpx" placeholder="您想住哪里？" />
				</view>
			</view>
			<view class="c-right search-btn" @click="$navigateBack()">
				取消
			</view>
		</view>
		<!-- 搜索结果 -->
        <view class="search_con ">
                <view class="con_ flex-box bottom-line" v-if="keywords" @click="handelSearch" >
                    <view class="left">
                        <view class="tag green">{{type | type}}</view>
                    </view>
                    <view class="center flex-1">
                        <view class="title">搜索“{{keywords}}”</view>
                    </view>
                    <view class="right">
                        <my-icon type="ic_into" color="#999"></my-icon>
                    </view>
                </view>
            <template v-if ="builds.length">
				<view v-for="item in builds" :key="item.id" class="con_ flex-box bottom-line" @click ="to_list(item)" >
                    <view class="left">
                        <view class="tag green">新房</view>
                    </view>
                    <view class="center flex-1">
                        <view class="title">{{item.title}}</view>
                        <view class="address">
                            <text class="area">{{item.areaname}}</text>
                            <text>{{item.address}}</text>
                        </view>
                    </view>
                    <view class="right">
                        <text v-if="type === 1" class="avg_price">{{item.price_type}}{{item.build_price}}{{item.price_unit}}</text>
                        <!-- <text v-if="type === 2">{{item.count}}套在售</text>
                        <text v-if="type === 3">{{item.cz_count}}套在租</text> -->
                    </view>
            	</view>
			</template>
            <view v-for="item in search_list" :key="item.id" class="con_ flex-box bottom-line" @click ="toList(item)" >
                <view class="left">
                    <view class="tag">小区</view>
                </view>
                <view class="center flex-1">
                    <view class="title">{{item.title}}</view>
                    <view class="address">
                        <text class="area">{{item.areaname}}</text>
                        <text>{{item.address}}</text>
                    </view>
                </view>
                <view class="right">
                    <text v-if="(type === 1 ||type === 4)&&item.avg_price" class="avg_price">均价{{item.avg_price}}元/m²</text>
                    <text v-if="type === 2">{{item.count}}套在售</text>
                    <text v-if="type === 3">{{item.cz_count}}套在租</text>
                    <!-- <text v-if="type === 4">{{item.cz_count}}套在租</text> -->
                </view>
            </view>
        </view>
		<view class="history" v-if="this.searchKey[type]!='' && keywords===''">
			<view class="history_tit">
				<text>搜索记录</text>
				<my-icon @tap="clearKey" type="ic_delete_w" color="#666" size="30rpx"></my-icon>
			</view>
			<view class="history_list">
				<!-- <template v-if ="type==1"> -->
					<text v-for="(item) in searchKey[type]" :key='item.id' @click="historySearch(item,1)" >{{item.name}}</text>
				<!-- </template> -->
				<!-- <template v-if ="type==1">
					<text v-for="(item) in searchKey[1]" :key='item.id' @click="historySearch(item,1)" >{{item.name}}</text>
				</template>
				<template v-if ="type==2">
					<text v-for="(item) in searchKey[2]" :key='item.id' @click="historySearch(item,2)" >{{item.name}}</text>
				</template>
				<template v-if ="type==3">
					<text v-for="(item) in searchKey[3]" :key='item.id' @click="historySearch(item,3)" >{{item.name}}</text>
				</template>
				<template v-if ="type==4">
					<text v-for="(item) in searchKey[4]" :key='item.id' @click="historySearch(item,4)" >{{item.name}}</text>
				</template> -->
			</view>
		</view>
		<!-- 二手房排行榜 -->
		<view class="ershou_rank" v-if="name=='二手房' && keywords===''">
            <view v-for="(item,idx) in ershou_list" :key="idx" @click="valueList(idx)">
				<view class="ershou_tag">
					<image class="tag_icon" :src="require(`@/static/icon/${item.src}.png`)" />
						<text>{{item.name}}</text>
					</view>
            </view>
		</view>
		<view class="hot_search_default" v-if="keywords===''">
			<!-- 选择小区时显示的排行榜 -->
			<view class="ranklist" v-if="hotList.length>0&&name!='新房'">
				 <view class="hot-top-list">
                    <swiper class="adviser-swiper" :duration="260" display-multiple-items="2" next-margin="100rpx">
                        <swiper-item v-for="(item,idx) in hotList" :key="idx">
                            <view class="swiper-item" @click="goList_community(item)">
                                <view class="info" :style="{background:item.bg_color}">
                                    <view class="name" :style="{backgroundImage:`url(https://images.tengfangyun.com/community/ranking/rank${item.id}.png)`}">| {{item.title}} <my-icon type="ic_into" color="#fff" size="30rpx"></my-icon></view>
                                    <!-- <view class="name" :style="{backgroundImage:`url(${item.top_pic})`}">| {{item.title}} <my-icon type="ic_into" color="#fff" size="30rpx"></my-icon></view> -->
                                   	<view class="search_list">
										<view class="search_item flex-box" v-for="(item, index) in item.communities" :key="index" @click.prevent.stop="$navigateTo(`/pages/house_price/detail?id=${item.id}`)">
											<view class="title-box flex-box flex-1">
												<view class="index" :class="'index'+(index+1)">{{index+1}}</view>
												<view class="title flex-1">{{item.title}}</view>
											</view>
											<my-icon type="ic_into" color="#999" size="28rpx"></my-icon>
										</view>
										<view class="ranklist_to" @click.prevent.stop="goList_community(item)">查看全部<my-icon type="ic_into" color="#999" size="28rpx"></my-icon></view>
									</view>

                                </view>
                            </view>
                        </swiper-item>
                    </swiper>
                </view>
			</view>
            <view class="hot-top" v-if ="hotList.length>0&&name=='新房'">
                <view class="hot-top-title">排行榜</view>
                <view class="hot-top-list">
                    <swiper class="adviser-swiper" :duration="260" display-multiple-items="2" next-margin="100rpx">
                        <swiper-item v-for="(item,idx) in hotList" :key="idx">
                            <view class="swiper-item" @click="goList(item)">
                                <view class="info" :style="{background:item.bg_color}">
									<view class="circle"></view>
                                    <view class="name">{{item.title}}</view>
                                    <view class="hot-info">
                                    <view v-for="(info,index) in item.builds" :key="info.id" class="hot-item"><text class="zero">0</text> <text class="idx">{{index+1}}</text><text class="info-name">{{info.title}}</text></view>
                                    </view>
                                </view>
                            </view>
                        </swiper-item>
                    </swiper>
                </view>
            </view>
			<view class="hot_search">
				<view class="title flex-box">
					<image class="icon" mode="widthFix" :src="'/images/new_icon/hot.png' | imageFilter('m_320')"></image>
					<text>大家都在搜</text>
				</view>
				<view class="site_name flex-box">
					<view class="line"></view>
					<view class="text">{{site_name}}</view>
					<view class="line"></view>
				</view>
				<view class="search_list">
					<view class="search_item flex-box" v-for="(item, index) in hot_list" :key="index" @click="goMore(item)">
						<view class="title-box flex-box flex-1">
							<view class="index" :class="'index'+(index+1)">{{index+1}}</view>
							<view class="title flex-1">{{item.title}}</view>
						</view>
						<view class="price">{{item.price_type}}{{item.build_price}}{{item.price_unit}}</view>
						<my-icon type="ic_into" color="#999" size="28rpx"></my-icon>
					</view>
				</view>
			</view>
		</view>
		<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
		<chat-tip></chat-tip>
	</view>
</template>

<script>
	import myIcon from "../../components/myIcon"
	import {uniLoadMore} from '@dcloudio/uni-ui'
	export default {
		components:{
			myIcon,
			uniLoadMore
		},
		data() {
			return {
				// searchKey: [],
				searchClose: true,
				type_community:'',
				keywords:"",
				name:"新房",
				options:["新房","二手房","出租房","小区"],
				type:1,
				hot_list:[],
				site_name:'',
				flag:0,
				from:"",
				search_list: [],
				builds:[],
				get_status: "more",
				content_text: {
                    contentdown: "",
                    contentrefresh: "正在加载...",
                    contentnomore: ""
                },
                hotList:[{
                    name:"热搜榜",
					color:'#ff70ff',
					id:1,
                    items:[{
                        id:1,
                        name:"打通天下"
                    },{
                        id:2,
                        name:"大同天下"
                    },]
                },{
                    name:"排行榜",
					color:'#ff706b',
					id:2,
                    items:[{
                        id:1,
                        name:"打通天下"
                    },{
                        id:2,
                        name:"大同天下"
                    },]
                },
                {
					name:"人气榜",
					id:3,
					color:'#ff6ccb',
                    items:[{
                        id:1,
                        name:"打通天下"
                    },{
                        id:2,
                        name:"大同天下"
                    },]
                }],
				ershou_list:[
					{
						name:"3日飙升热门房源价值榜",
						src:"dy"
					},{
						name:"业主降价急卖好房",
						src:"school"
					},{
						name:"发现好房，高性价比房源",
						src:"re"
					}
				],
				storageLen: 10
			};
		},
		filters:{
            type(val){
                switch (val) {
                case 1:
                    return "新房"
                    break;
                case 2:
                    return "二手房"
                    break;
                case 3:
                    return "租房"
                                break;
                        case 4:
                    return "小区"
                    break;
                
                default:
                    return "新房"
                    break;
                }
            },
            
        },
		onLoad(options) {
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			if(options.text){
				this.name=options.text;
				this.from=options.text
				switch (this.name) {
					case "新房":
						this.type =1
						break;
					case "二手房":
						this.type =2
						break;
					case "出租房":
						this.type =3
						break;
					case "小区":
						this.type = 4
						break;
					default:
						this.type =1
						break;
				}
			}
			if(options.keywords||options.keyword){
				this.handelInput({detail:{value: options.keywords||options.keyword}})
			}
			this.getHot()
		},
		computed:{
			searchKey:{
				set(val){
					return val
				},
				get(){
					if (uni.getStorageSync('searchKey')) return JSON.parse(uni.getStorageSync('searchKey'))
				return [[],[],
					[],
					[],
					[]]
				}
				
			}
		},
		methods:{
			// 获取楼盘的热搜
			getHot(){
				if (this.name != '新房') {
					this.type_community = 'community'
				}else{
					this.type_community = ''
				}
				this.$ajax.get('index/searchList',{type:this.type_community},res=>{
					if(res.data.code == 1){
						this.hot_list = res.data.list
						this.hotList=res.data.tops
						this.hotList.map((item,index)=>{
							if (index==0){
								item.color="#ff70ff"
							}else if (index==1){
								item.color="#ff706b"
							}else if (index==2){
								item.color="#ff6bff"
							}
						})
						this.site_name = res.data.siteName
					}
					if (res.data.share&&res.data.share.title){
						this.share =res.data.share
					}else {
						this.share={
							title:'',
							content:'',
							pic:''
						}
					}
					this.getWxConfig()
				})
            },
			valueList(idx){
				var type = idx+1
      			this.$navigateTo('/statistics/value_list?type='+type)
			},
			handelInput(e){
				this.get_status ="loading"
				this.keywords = e.detail.value
				if (!this.keywords) {
					this.search_list =[];
					this.builds =[];
					this.get_status ="more"
					return ;
				}
				let params = {keywords: this.keywords, type:this.type}
				this.$ajax.get('index/searchCommunity.html',params, res=>{
					if(res.data.code === 1){
						console.log(res.data.list)
						this.search_list = res.data.list
						if(res.data.builds&&res.data.builds.length>0){
							this.builds = res.data.builds
						}
						this.get_status ="noMore"
					}else{
						this.search_list=[]
						this.builds=[]
						this.get_status = "noMore"
					}
				}, err=>{
					this.search_list=[]
					this.builds=[]
					this.get_status ="noMore"
				})
        // 	this.$ajax.get('index/search.html',{keywords:this.key,type:this.type},res=>{
				// 	if(res.data.code == 1){
				// 		 this.builds =res.data.list
				// 		 this.get_status ="noMore"
				// 	}else {
				// 		 this.get_status ="noMore"
				// 		 this.builds=[]
				// 	}
				// },
				// err =>{
				// 		this.get_status ="noMore"
				// 		this.builds=[]
				// })
			},
			toList(item){
				let type=4
				console.log(item);
				if (item.title) {
					this.keywords = item.title
					let index =this.searchKey[type].findIndex(item=>item.name ==this.keywords)
					if (index>=0){
						this.searchKey[type].splice(index,1)
						this.searchKey[type].map((item)=>{
							// if (item.id<this.storageLen-index && item.id<this.searchKey[type][index].id) {
								item.id++
							// }
						})
					}
					this.searchKey[type].unshift({id:this.storageLen-this.searchKey[type].length,name: this.keywords})
					console.log(this.searchKey[type],"-======");
					if (this.searchKey[type].length >= this.storageLen) {
						this.searchKey[type].length = this.storageLen
					}
					uni.setStorageSync('searchKey', JSON.stringify(this.searchKey))
					this.searchKey = JSON.parse(uni.getStorageSync("searchKey"))
					this.$forceUpdate()
				}		
				switch(type)
				{
					case 1:						
						this.$navigateTo(`/pages/house_price/detail?id=${item.id}`)
						uni.setStorageSync('no_watch_search_key','1')
						return
					case 2:	
						this.$navigateTo(`/pages/ershou/ershou?cid=${item.id}`)
						uni.setStorageSync('no_watch_search_key','1')
						return
					case 3:	
						this.$navigateTo(`/pages/renting/renting?cid=${item.id}`)
						uni.setStorageSync('no_watch_search_key','1')
						return
					case 4:	
						this.$navigateTo(`/pages/house_price/detail?id=${item.id}`)
						return
					default:	
						this.$navigateTo(`/pages/new_house/new_house`)
				}
			},
			goList(item){
				this.$navigateTo("/statistics/search_list?id="+item.id)
			},
			goList_community(item){
				this.$navigateTo("/statistics/search_list?type=1&id="+item.id)
			},
			goDetail(id){
				console.log(id);
				
		  	let url
				switch(this.type)
				{
					case 1:
					url = "/pages/new_house/detail"
					break;
					case 2:
					url = "/pages/ershou/detail"
					break;
					case 3:
					url = "/pages/renting/detail"
					break;
					case 4:
					url = "/pages/house_price/detail"
					break;
					default:
					url = "/pages/new_house/new_house"
				}
				this.$navigateTo(url+"?id="+id)
			},
			handelSearch(){
				if (this.keywords) {
					let index =this.searchKey[this.type].findIndex(item=>item.name ==this.keywords)
					if (index>=0){
						this.searchKey[this.type].splice(index,1)
						this.searchKey[this.type].map(item=>{
							// if (item.id<this.storageLen-index && item.id<this.searchKey[this.type][index].id) {
								item.id++
							// }
						})
					}
					this.searchKey[this.type].unshift({id:this.storageLen-this.searchKey[this.type].length,name: this.keywords})
					if (this.searchKey[this.type].length >= this.storageLen) {
						this.searchKey[this.type].length = this.storageLen
					}
					uni.setStorageSync('searchKey', JSON.stringify(this.searchKey))
					this.searchKey = JSON.parse(uni.getStorageSync("searchKey"))
					this.$forceUpdate()
				}
				// 如果来自出租房或二手房
				if(this.from){
					setTimeout(()=>{
						uni.$emit('handleSearch', this.keywords)
					},300)
					uni.navigateBack()
					
				}else{
					let url
					switch(this.type)
					{
						case 1:
							url = "/pages/new_house/new_house" 
						break;
						case 2:
							url = "/pages/ershou/ershou"
						break;
						case 3:
							url = "/pages/renting/renting"
						break;
						case 4:
							url = "/pages/house_price/house_price"
						break;
						default:
							url = "/pages/new_house/new_house"
					}
					this.$navigateTo(url+"?keyword="+this.keywords)
				}
			
			},
			showAction(){
				if(this.from){
					return
				}
				uni.showActionSheet({
					itemList: this.options,
					success: (res)=>{
						this.name = this.options[res.tapIndex]
						this.type = res.tapIndex+1
						console.log(this.type,this.name)
						 this.search_list=[];
						 this.builds=[];
						 this.keywords =""
						 this.get_status="more"
						 this.getHot()
					},
					fail: (res)=>{
						console.log(res.errMsg);
					}
				});
			},
			// 删除搜索历史
			clearKey() {
				this.searchKey[this.type]=[]
				// 存储
				uni.setStorageSync('searchKey', JSON.stringify(this.searchKey))
				this.searchKey = JSON.parse(uni.getStorageSync("searchKey"))
				this.$forceUpdate()
			},
			to_list(item){
				if (item.title) {
					this.keywords = item.title
					console.log(this.type);
					let index =this.searchKey[this.type].findIndex(item=>item.name == this.keywords)
					console.log(index);
					if (index>=0){
						this.searchKey[this.type].splice(index,1)
						this.searchKey[this.type].map(item=>{
							// if (item.id<this.storageLen-index && item.id<this.searchKey[type][index].id) {
								item.id++
							// }
						})
					}
					this.searchKey[1].unshift({id:this.storageLen-this.searchKey[this.type].length,name: this.keywords})
					if (this.searchKey[this.type].length >= this.storageLen) {
						this.searchKey[this.type].length = this.storageLen
					}
					uni.setStorageSync('searchKey', JSON.stringify(this.searchKey))
					this.searchKey = JSON.parse(uni.getStorageSync("searchKey"))
					this.$forceUpdate()
				}
				console.log(this.keywords);
				this.$navigateTo(`/pages/new_house/detail?id=${item.id}`)
			},
			historySearch(item,type){
				console.log(item,type);
				let url
					switch(this.type)
					{
						case 1:
							url = "/pages/new_house/new_house" 
						break;
						case 2:
							url = "/pages/ershou/ershou"
						break;
						case 3:
							url = "/pages/renting/renting"
						break;
						case 4:
							url = "/pages/house_price/house_price"
						break;
						default:
							url = "/pages/new_house/new_house"
					}
					this.keywords = item.name
					console.log(this.keywords);
					let index =this.searchKey[this.type].findIndex(item=>item.name ==this.keywords)
					console.log(index);
					if (index>=0){
						this.searchKey[this.type].splice(index,1)
						this.searchKey[this.type].map(item=>{
							// if (item.id<this.storageLen-index && item.id<this.searchKey[type][index].id) {
								item.id++
							// }
						})
					}
					this.searchKey[this.type].unshift({id:this.storageLen-this.searchKey[this.type].length,name: this.keywords})
					if (this.searchKey[this.type].length >= this.storageLen) {
						this.searchKey[this.type].length = this.storageLen
					}
					uni.setStorageSync('searchKey', JSON.stringify(this.searchKey))
					this.searchKey = JSON.parse(uni.getStorageSync("searchKey"))
					this.$forceUpdate()
					this.$navigateTo(url+"?keyword="+item.name)
			},
			// 大家都在看
			goMore(item){
				if (this.name!='新房') {
					this.$navigateTo(`/pages/house_price/detail?id=${item.id}`)
				}else{
					this.$navigateTo(`/pages/new_house/detail?id=${item.id}`)
				}
			}
			
		},
		// #ifdef APP_PLUS
		onBackPress(){
			// 收起软键盘
			plus.key.hideSoftKeybord();
		},
		// #endif
	}
</script>

<style lang="scss">
.ranklist_title{
	font-size: 40rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 32rpx;
}
.ranklist{
	margin-left: 50rpx;
	.adviser-swiper{
		width: 1360rpx;
		height: 630rpx;
		margin-top: 0;
		swiper-item{
			width: 480rpx!important;
		}
		.swiper-item{
			.info{
				height: 100%;
				padding: 0;
				box-shadow: 0 0 24rpx 0 rgba(0,0,0,0.1);
				border-radius: 40rpx;
				overflow: hidden;
				margin: 6rpx 50rpx 6rpx 6rpx;
				.name{
					font-size: 40rpx;
					padding: 30rpx 20rpx;
					background-size: cover;
					.iconfont{
						margin-left: 10rpx;
					}
				}
				.search_list{
					background: #fff;
					border-radius: 30rpx;
					padding: 10rpx 20rpx;
					// position: relative;
					// top: -20rpx;
					.ranklist_to{
						color: #999;
						text-align: center;
						margin: 20rpx 0;
					}
				}
			}

		}
	}
}
.ershou_rank{
	margin-bottom: 10rpx;
	display: flex;
	flex-wrap: wrap;
	padding: 0 50rpx;
	.adviser-swiper{
		height: 50rpx;
    	width: 760rpx;
		margin: 0;
	}
	.ershou_tag{
		background: #fff1f5;
		color: #fb656a;
		font-size: 22rpx;
		align-items: center;
		height: 48rpx;
		padding: 0 16rpx 0 8rpx;
		margin-bottom: 24rpx;
		margin-right: 24rpx;
		border-radius: 4rpx;
		font-weight: bold;
		text-align: center;
	}
	.tag_icon {
		position: relative;
		top: 8rpx;
	  	width: 32rpx;
	  	height: 32rpx;
	  	margin-right: 4rpx;
	}
}
.history{
	padding: 0 48rpx;
	color: #666666;
	.history_tit{
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 15rpx;
	}
	.history_list{
		display: flex;
		flex-wrap: wrap;
		margin: 0 0 30rpx 0;
		text{
			font-size: 22rpx;
			padding: 8rpx 16rpx;
			background: #F5F5F5;
			border-radius: 4rpx;
			margin-right: 16rpx;
			margin-top: 16rpx;
		}
	}
}
	.search-page{
		height: calc(100vh - 44px);
		box-sizing: border-box;
		background-color: #fff
	}
	.hot-top{
		margin-left: 48rpx;
	}
	.hot-top-title{
		font-size: 36rpx;
		font-weight: bold;
	}
    .adviser-swiper{
        height: 170rpx;
		margin: 40rpx 0;
		.info{
			color: #fff;
			height: 170rpx;
			padding:10rpx 20rpx;
			margin-right: 20rpx;
			border-radius: 8rpx;
			position: relative;
			.name{
				padding: 6rpx 0;
			}
			.hot-info{
				padding: 6rpx 0;
				.hot-item{
					font-size: 22rpx;
					white-space: nowrap;
					width: 100%;
					margin-bottom: 4rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					.zero{
						font-weight: blod;
						opacity: 0.5;
						margin-right: 2rpx;

					}
					.info-name{
						margin-left: 10rpx;
					}
					

				}
			}
			.circle{
				position: absolute;
				top: 0;
				bottom: 0;
				left: 60%;
				width: 100%;
				height: 100%;
				border-radius: 100%;
				opacity: 0.1;
				background: #fff;
			}
		}
    }
	/* #ifdef H5 */
	.search-title{
		width: 100%;
		padding: 15rpx 48rpx;
		box-sizing: border-box;
		position: fixed;
		top: 44px;
		z-index: 99;
		background-color: #fff;
	}
	/* #endif */
	/* #ifndef H5 */
	.search-title{
		width: 100%;
		padding: 15rpx 48rpx;
		box-sizing: border-box;
		position: fixed;
		top: var(--window-top);
		z-index: 99;
		background-color: #fff;
		border-bottom: 1upx solid $uni-border-color;
		box-shadow: 0 0 26upx #dedede;
	}
	/* #endif */
	.input-box{
		align-items: center;
		padding: 0 24rpx;
		background-color: #eee;
		border-radius: 8rpx;
	}
	.select{
		align-items: center;
		line-height: 32upx;
		margin: 16rpx 0;
		padding-right: 15upx;
	}
	.search-box{
		flex: 1;
	}
	.search-btn{
		line-height: 60upx;
		padding-left: 15upx;
		color: #666
	}
	.search-box input{
		height: 100%;
		padding: 6rpx 10rpx;
		box-sizing: border-box;
		font-size: $uni-font-size-base;
	}
  .search-page{
    .search_con{
      margin-top: 40upx;
      background: #fff;
      .con_{
        padding: 15upx 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height:48upx;
				.left{
					margin-right: 20rpx;
				}
				.tag {
					padding: 10upx 20upx;
					font-size: 24upx;
					border-radius: 4rpx;
					background: #fff4ee;
					color: #ea6e31;
					line-height:1;
					&.green{
						background: #e6fdf7;
						color: #43ccac;
					}
				}
        .center{
					overflow: hidden;
            .title{
              font-size: 32upx;
              color: #333;
							max-width: 550rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;

            }
            .address{
                font-size: 26upx;
                color: #999;
								max-width: 550rpx;
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
							.area{
								margin-right: 10rpx;
							}
            }
        }
				.right{
					margin-left: 10rpx;
					font-size: 30rpx;
					color: #333;
					.avg_price{
						font-size: 26rpx;
					}
				}
      }
    }
  }

.hot_search{
	padding: 0 48rpx;
	margin-top: 40rpx;
	background: #fff;
	>.title{
		justify-content: center;
		align-items: center;
		font-size: 40rpx;
		font-weight: bold;
		margin-bottom: 32rpx;
		.icon{
			margin-right: 16rpx;
			width: 48rpx;
			height: 0;
		}
	}
	.site_name{
		align-items: center;
		margin-bottom: 24rpx;
		.line{
			flex: 1;
			height: 1rpx;
			background-color: #d8d8d8;
		}
		>.text{
			color: #666;
			margin: 0 24rpx;
		}
	}


}	
	.search_item{
		padding: 20rpx 0;
		align-items: center;
		overflow: hidden;
		color: #333;
		.title-box{
			overflow: hidden;
			margin-right: 20rpx;
		}
		.index{
			width: 40rpx;
			height: 40rpx;
			margin-right: 24rpx;
			line-height: 40rpx;
			text-align: center;
			border-radius: 4rpx;
			background-color: #d8d8d8;
			color: #fff;
			&.index1{
				background-color: #FB656A;
			}
			&.index2{
				background-color: #FB8968;
			}
			&.index3{
				background-color: #FBC365;
			}
		}
		.title{
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.price{
			margin-right: 16rpx;
		}
	}
</style>
