<template>
  <view class="index-content" :style="{ filter: huiFilter }" :class="{ 'pt96': search.is_show == 1 }" v-show="showView">
    <view class="search-bar flex-row" v-if="search.is_show">
      <!-- <view class="search-left">
          <text class="site-name">{{siteName}}</text>
        </view> -->
      <view class="search-box flex-row" @click="toSearch()">
        <my-icon type="ic_sousuo" color="#999999" size="46rpx"></my-icon>
        <view class="inp">
          <text>您想住哪里？</text>
        </view>
      </view>
      <view class="search-right flex-row" @click="$navigateTo('/pages/map_find/map_find')">
        <my-icon type="weizhi" size="42rpx" color="#666"></my-icon>
        <text>地图</text>
      </view>
    </view>
    <!-- 轮播 -->

    <view class="swiper_container" v-if="focus.is_show && !focus.banner" :class="{ rounded: focus.style == 1 }">
      <view class="swiper">
        <my-swiper :is_adv="$store.state.adv_show_label == 1 ? true : false" :focus="focus.focus"
          :rounded="focus.style == 1" :autoplay="true" :interval="4000" :indicatorDots="true" :circular="true"
          indicatorActiveColor="#ffffff" :height="focus.setting * 2 + 'rpx'"></my-swiper>
      </view>
    </view>
    <view class="swiper_banner mb30" v-if="focus.banner">
      <image style="width:100vw" :src='focus.banner' mode="widthFix"></image>
    </view>
    <!-- 宫格 -->
    <template v-if="navs.is_show === 1 && navs.setting != 1">
      <swiper class="grid" :indicator-dots="gridDots" :current="gridCurrent" @change="changeGridSwiper" :duration="360"
        indicator-active-color="#f65354" :style="{ height: swiperHeight, paddingBottom: swipePaddingBottom }">
        <swiper-item v-for="(options, idx) in navs.nav" :key="idx">
          <my-grid @click="toNav" hoverClass='hoverClass' :options="options" :column-num="columnNum" :fontSize="14"
            :show-border="false"></my-grid>
        </swiper-item>
      </swiper>
    </template>
    <view v-if='(navs.is_show === 1 && navs.setting == 1 && navs.children.length) || subOptions.length' class='mb20'>
      <template v-if="navs.is_show === 1 && navs.setting == 1 && navs.children.length">
        <view class="grid sub_grid sub_top_grid">
          <my-grid hoverClass='hoverClass' @click="toNav1($event, 'navs_children')" :options="navs.children"
            :column-num="5" :fontSize="12" :show-border="false"></my-grid>
        </view>
      </template>
      <view class="grid sub_grid mt20" v-if='subOptions.children && subOptions.children.length'>
        <template v-if='subOptions.display_mode == 2'>
          <swiper class="grid" :indicator-dots="subGridDots" :current="subGridCurrent" @change="changeSubGridSwiper"
            :duration="360" indicator-active-color="#f65354"
            :style="{ height: subSwiperHeight, paddingBottom: subSwipePaddingBottom }">
            <swiper-item v-for="(options, idx) in subOptions.children" :key="idx">
              <my-grid hoverClass='hoverClass' @click="toNav1($event, 'subOptions')" :options="options"
                :column-num="columnSubNum" :fontSize="14" :show-border="false"></my-grid>
            </swiper-item>
          </swiper>
        </template>
        <template v-else>
          <my-grid @click="toNav1($event, 'subOptions')" hoverClass='hoverClass' :options="subOptions.children"
            :column-num="5" :fontSize="12" :show-border="false"></my-grid>
        </template>

        <!--  -->
      </view>
    </view>

    <!-- 房价数据 -->
    <view class="plate-box container" v-if="benyuefangjia.is_show === 1 || newsList.is_show === 1">
      <view class="plate-title flex-row flex-item-center" v-if="benyuefangjia.is_show === 1">
        <text class="active">{{ benyuefangjia.title }}</text>
        <view class="lsb flex-row" v-if="loushibao.is_show" @click="$navigateTo('/topic/jianbao')">
          <my-icon type="riqi" size="46rpx" color="#fff"></my-icon>
          <text class="text">{{ loushibao.title }}</text>
        </view>
      </view>
      <view class="card-box">
        <view class="data-box flex-row" v-if="benyuefangjia.is_show === 1" @click="goPropertyData">
          <view class="data-item flex-1">
            <view class="data-value flex-row">
              <text class="value">{{ benyuefangjia.left.value }}</text>
              <text class="unit">{{ data_units[benyuefangjia.left.type] }}</text>
            </view>
            <view class="trend flex-row">
              <text>{{ benyuefangjia.left.desc }}</text>
              <block v-if="benyuefangjia.left.status.status === 1">
                <text style="margin-left:10rpx">持平</text>
              </block>
              <block v-if="benyuefangjia.left.status.status === 2">
                <my-icon type="ic_up" color="#fb656a"></my-icon>
                <text style="color:#fb656a">{{ benyuefangjia.left.status.value }}%</text>
              </block>
              <block v-if="benyuefangjia.left.status.status === 3">
                <my-icon type="ic_down" color="#00CAA7"></my-icon>
                <text style="color:#00CAA7">{{ benyuefangjia.left.status.value }}%</text>
              </block>
            </view>
          </view>
          <view class="data-item flex-1">
            <view class="data-value flex-row">
              <text class="value">{{ benyuefangjia.right.value }}</text>
              <text class="unit">{{ data_units[benyuefangjia.right.type] }}</text>
            </view>
            <view class="trend flex-row">
              <text>{{ benyuefangjia.right.desc }}</text>
              <block v-if="benyuefangjia.right.status.status === 1">
                <text style="margin-left:10rpx">持平</text>
              </block>
              <block v-if="benyuefangjia.right.status.status === 2">
                <my-icon type="ic_up" color="#fb656a"></my-icon>
                <text style="color:#fb656a">{{ benyuefangjia.right.status.value }}%</text>
              </block>
              <block v-if="benyuefangjia.right.status.status === 3">
                <my-icon type="ic_down" color="#00CAA7"></my-icon>
                <text style="color:#00CAA7">{{ benyuefangjia.right.status.value }}%</text>
              </block>
            </view>
          </view>
        </view>
        <view class="line" v-if="benyuefangjia.is_show === 1 && newsList.is_show === 1"></view>
        <view class="news-box" v-if="newsList.is_show">
          <swiper class="flex-1" :duration="300" :circular="true" :autoplay="true" display-multiple-items="2"
            :vertical="true">
            <swiper-item v-for="news in newsList.news" :key="news.id">
              <view @click="$navigateTo('/pages/news/detail?id=' + news.id)" class="swiper-item"
                hover-class="navigator-hover">
                <text class="lebel" :class="'label' + news.level">{{ news.level === 3 ? '头条' : (news.level === 2 ? '关注'
                  :
                  '动态') }}</text>
                <text>{{ news.title }}</text>
              </view>
            </swiper-item>
          </swiper>
        </view>
      </view>
    </view>
    <!-- 小工具 -->
    <view class="tools plr48" v-if='toolsOptions.length'>
      <view class="tool_title">
        小工具
      </view>
      <view class="tools_content">
        <my-grid @click="toNav1($event, 'toolsOptions')" :options="toolsOptions" :column-num="4" :fontSize="14"
          :show-border="false"></my-grid>
      </view>
    </view>
    <!-- 新增模块 -->
    <view class="model flex-row"
      v-if="module_nav.is_show == 1 && module_nav.module_nav && module_nav.module_nav.length == 4">
      <template v-if="module_nav.style == 1">
        <view class="model_icon" v-for="(item, index) in module_nav.module_nav" :key="index"
          @click="$navigateTo(item.path)">
          <image :src="item.icon | imgUrl('w_240')" mode="aspectFit"> </image>
        </view>
      </template>
      <template v-if="module_nav.style == 2">
        <view class="model_style flex-row ">
          <view class="model_style_item" v-for="(item, index) in module_nav.module_nav" :key="index"
            @click="$navigateTo(item.path)" :style="{ background: item.bgcolor }">
            <view class="model_style_item_top flex-row">
              <view class="line" :style="{ backgroundColor: item.color }"></view>
              <view class="title">{{ item.title }}</view>
            </view>
            <view class="model_style_item_bottom">
              {{ item.descr }}
            </view>
            <view class="bg_img">
              <image :src="item.icon" mode="widthFix" alt=""></image>
            </view>

          </view>

        </view>

      </template>
    </view>


    <!-- 各个板块 -->
    <!-- <view class="banner-box container" v-if="adv.is_show&&adv.advA.length>0">
        <block v-for="(item, idx) in (adv.advA||[])" :key="idx">
        <view class="banner-item" @click="toLink(item.link)">
          <banner :image="item.image" mode="widthFix" height="auto"></banner>
        </view>
      </block>
    </view> -->
    <!-- <view class="banner-box container" v-if="adv.is_show&&adv.advA.length>0" >
      <view class="banner-item">
          <my-swiper  @click="toBanner"  :focus="adv.advA" :autoplay="true" :interval="5000" :circular="true" height="152rpx"></my-swiper>
      </view>
    </view> -->
    <view class="guanggao" v-if="adv.is_show && adv.advA && adv.advA.length > 0">
      <view class="banner-box container">
        <swiper class="banner" :autoplay="true" :interval="5000" :circular="true"
          :style="{ 'height': advaswiperHeight + 'px' }">
          <swiper-item v-for="(img, index) in adv.advA" :key="index" @click="toLink(img.link)">
            <view class="adv-item banner-item" :class="{ rounded: rounded }">
              <image class="swiperItmeHeight" :src="img.image | imageFilter('w_6401')" mode="widthFix"
                @load="swiperHeightFn"></image>
              <view v-if="img.is_show_label == 1" class="marker">广告</view>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>
    <view class="plate-box sub_build container"
      v-if="buildCount.is_show = 1 && buildCount.hot_builds && buildCount.hot_builds.length > 0">
      <swiper class="sub_swiper" :duration="260" display-multiple-items="3" next-margin="80rpx">

        <swiper-item v-for="(item, index) in buildCount.hot_builds" :key="index"
          @click="$navigateTo(`/pages/new_house/new_house?status=${item.build_status}`)">
          <view class="swiper-item" :class="{ 'first': index == 0 }">
            <view class="sub_top flex-row">
              <text v-if="index != 0" class="top_before" :style="{ background: item.color }"></text>
              <text class="build_title">{{ item.title }}</text>
            </view>
            <view class="info flex=row">
              <view class="num" v-if="index == 0">{{ item.count }}</view>
              <view class="num" v-else :style="{ color: item.color }">{{ item.count }}</view>
              <view class="icon">
                <image :src="item.icon" mode="widthFixed"></image>
              </view>
            </view>
          </view>
        </swiper-item>
        <swiper-item v-if="buildCount.hot_builds.length < 2"></swiper-item>
        <swiper-item v-if="buildCount.hot_builds.length < 3"></swiper-item>
      </swiper>

    </view>
    <template v-if="build_list.is_show == 1 && build_list.build_list.length > 0">
      <block v-for="(item, index) in build_list.build_list" :key="index">
        <view class="plate-box super_build container" v-if="item.list.length > 0">

          <view class="super_build_name">
            {{ item.title }}
          </view>
          <swiper class="sup_build_swiper" :duration="260" display-multiple-items="2" next-margin="80rpx">
            <swiper-item v-for="(build) in item.list" :key="build.id"
              @click="$navigateTo('/pages/new_house/detail?id=' + build.id)">
              <view class="swiper-item">

                <view class="build_img_box">
                  <image mode="widthFix" :src="build.img | imgUrl('w_240')"></image>
                  <!-- <view class="hui-row" v-if="build.discount">
                        <text class="text">{{build.discount}}</text>
                      </view>
                      <view class="tuan-row" v-else-if="build.group_title">
                        <text class="tuan">团</text>
                        <text class="text">{{build.group_title}}</text>
                      </view> -->
                </view>
                <view class="info">
                  <view class="name"><text>{{ build.title }}</text></view>

                  <view class="labels"><text v-for="(label, i) in build.build_type" :key="i" class="label">{{ label
                      }}</text></view>
                  <view class="build_name flex-row">
                    <text class="price_type">{{ build.price_type }}</text>
                    <text class="price">
                      {{ build.build_price }}
                    </text>
                    <text class="price_unit">
                      {{ build.price_unit }}
                    </text>
                  </view>


                </view>
                <view class="hui-row" v-if="build.discount">
                  <!-- <text class="hui">惠</text> -->
                  <text class="text">{{ build.discount }}</text>
                </view>
                <view class="tuan-row" v-else-if="build.group_title">

                  <!-- <text class="tuan">团</text> -->
                  <text class="text">{{ build.group_title }}</text>
                </view>
                <view class="sanjiao" v-if="build.discount || build.group_title"></view>
              </view>
            </swiper-item>
            <swiper-item v-if="item.list.length < 2"></swiper-item>
            <!-- <swiper-item v-if ="item.list.length<3"></swiper-item> -->
          </swiper>

        </view>
      </block>
    </template>
    <!-- 热门新房 -->
    <view class="plate-box container" v-if="hotBuilds.is_show === 1">
      <!-- 热门楼盘风格1 -->
      <template v-if="hotBuilds.style == 1">
        <view class="plate-title flex-row">
          <text class="active">{{ hotBuilds.title }}</text>
          <view class="c-right more" @click="More('xinfang')">更多</view>
        </view>
        <view class="new_house-list flex-row">
          <block v-for="(item, idx) in hotBuilds.hotBuild" :key="item.id">
            <view style="width:100%" v-if="idx === 0">
              <new-house-item :item-data="item" :mode='2' type="new_house" @click="toDetail"></new-house-item>
            </view>
            <view v-else class="new_house-square" :style="idx <= 3 ? 'margin-top:0' : ''"
              @click="toDetail({ type: 'new_house', detail: item })">
              <view class="img-box">
                <view class="level-box">
                  <!-- <text class="level level1" v-if="item.isvip">精选</text> -->
                  <!-- <text class="level level2" v-if="item.isgood">优惠</text> -->
                </view>
                <image class="img" :src="item.img | imageFilter('w_240')" lazy-load mode="aspectFill"></image>
                <image v-if="item.is_vr == 1 || item.quanjing" class="video-icon" src="/static/icon/vr.png"
                  mode="widthFix">
                </image>
                <image v-else-if="item.is_video == 1" class="video-icon" src="/static/icon/video.png" mode="widthFix">
                </image>
                <text class="area_name">{{ item.areaname }}</text>
              </view>
              <view class="build_name">{{ item.title }}</view>
              <view class="hui-row" v-if="item.discount">
                <!-- <text class="hui">惠</text> -->
                <text class="text">{{ item.discount }}</text>
              </view>
              <view class="tuan-row" v-else-if="item.group_title">
                <text class="tuan">团</text>
                <text class="text">{{ item.group_title }}</text>
              </view>
            </view>
          </block>
          <!-- 占位符号 -->
          <view class="new_house-square" style="height:0;padding:0;margin:0"></view>
        </view>
      </template>
      <!-- 热门楼盘风格2 -->
      <template v-if="hotBuilds.style == 2">
        <view class="plate-title flex-row">
          <text class="active">{{ hotBuilds.title }}</text>
          <view class="c-right more" @click="More('xinfang')">更多</view>
        </view>
        <block v-for="item in hotBuilds.hotBuild" :key="item.id">
          <view class="bottom-line">
            <new-house-item :item-data="item" :mode='2' type="new_house" @click="toDetail"></new-house-item>
          </view>
        </block>
        <!-- <view class="bottom-more" @click="More('xinfang')">查看更多</view> -->
      </template>
    </view>
    <!-- 视频模块 -->
    <template v-if="video_is_show.is_show == 1 && radiolist.length > 0">
      <view class="plate-box content-one super_build container">
        <view class="plate-title flex-row">
          <text class="active">{{ radiotitle }}</text>
          <view class="c-right more" @click="handleEvery" style="margin-right:50rpx">更多</view>
        </view>
        <swiper class="sup_build_swiper" :duration="260" display-multiple-items="2" next-margin="80rpx">
          <swiper-item v-for="(build, idx) in radiolist" :key="idx">
            <view class="swiper-item">
              <view class="build_img_box" @click="getTapRadio(build, idx)">
                <image :src="(build.cover || build.video_path) | imageFilter('w_8601')"
                  style="width: 100%;height: 100%;border-radius: 10rpx;z-index: 0;"></image>
                <view class="video"
                  :class="build.type == 1 ? 'videoType1' : build.type == 2 ? 'videoType2' : 'videoType3'">
                </view>
              </view>
              <view class="video-title" style="margin-top:10rpx">
                {{ build.title }}
              </view>

            </view>
          </swiper-item>
          <swiper-item v-if="radiolist.length < 2"></swiper-item>
          <swiper-item v-if="radiolist.length < 3"></swiper-item>
        </swiper>
      </view>
    </template>
    <!-- 置业顾问 经纪人 咨询师 -->
    <view class="plate-box swiper-plate container" v-if="advisers.is_show">
      <view class="plate-title flex-row">
        <view class="cate flex-row">
          <text v-for="(item, idx) in advisers.zhiyeguwen" :key="idx" :class="{ active: service_type === item.type }"
            @click="service_type = item.type">{{ item.title }}</text>
        </view>
        <view class="c-right more" @click="More(service_type)">更多</view>
      </view>
      <swiper v-for="(item, index) in advisers.zhiyeguwen" :key="index" v-show="service_type === item.type"
        class="adviser-swiper" :duration="260" display-multiple-items="2" next-margin="170rpx">
        <template v-if="item.type === 'adviser'">
          <!-- 置业顾问 -->
          <swiper-item v-for="(item, idx) in item.adviserMember" :key="idx">
            <view class="swiper-item" :class="{ first: idx === 0 }" @click="consuDetail(item.id)">
              <view class="avatar-box">
                <view class="img-box">
                  <!-- #ifdef H5 -->
                  <img class="header_img" :src="item.prelogo | imageFilter('w_240')" />
                  <!-- #endif -->
                  <!-- #ifndef H5 -->
                  <image class="header_img" :src="item.prelogo | imageFilter('w_240')" mode="widthFix"></image>
                  <!-- #endif -->
                </view>
                <image v-if="idx < 3" class="brand" :src="`/images/new_icon/No${idx + 1}.png` | imageFilter('m_80')">
                </image>
              </view>
              <view class="info">
                <view class="name">{{ item.cname }}</view>
                <view class="build_name">
                  <!-- <image class="level_icon" :src="getLevelIcon(item.levelid) | imageFilter('w_80')"></image> -->
                  <text>{{ item.build_names }}</text>
                </view>
              </view>
              <view class="btn_box flex-row" @click.prevent.stop="handleAsk(item.mid, item.id, 'adviser')">
                <my-icon type="ic_zixun1" size="32rpx" color="#fff"></my-icon>
                <text class="zx">咨询</text>
              </view>
            </view>
          </swiper-item>
          <swiper-item v-if="item.adviserMember.length < 2"></swiper-item>
        </template>
        <template v-else-if="item.type === 'agent'">
          <!-- 经纪人 -->
          <swiper-item v-for="(item, idx) in item.recommendMember" :key="idx">
            <view class="swiper-item" :class="{ first: idx === 0 }" @click="toAgentDetail(idx, item.id)">
              <view class="avatar-box">
                <view class="img-box">
                  <!-- #ifdef H5 -->
                  <img class="header_img" :src="item.img | imageFilter('w_240')" />
                  <!-- #endif -->
                  <!-- #ifndef H5 -->
                  <image class="header_img" :src="item.img | imageFilter('w_240')" mode="widthFix"></image>
                  <!-- #endif -->
                </view>
                <image v-if="idx < 3" class="brand" :src="`/images/new_icon/No${idx + 1}.png` | imageFilter('m_80')">
                </image>
              </view>
              <view class="info">
                <view class="name">{{ item.cname }}</view>
                <view class="build_name">
                  <text>{{ item.tname }}</text>
                </view>
              </view>
              <view class="btn_box flex-row" @click.prevent.stop="handleAsk(item.mid, item.id, 'agent')">
                <my-icon type="ic_zixun1" size="32rpx" color="#fff"></my-icon>
                <text class="zx">咨询</text>
              </view>
            </view>
          </swiper-item>
          <swiper-item v-if="item.recommendMember.length < 2"></swiper-item>
        </template>
        <template v-else>
          <!-- 购房咨询师 -->
          <swiper-item v-for="(item, idx) in item.adviserGuWen" :key="idx">
            <view class="swiper-item" :class="{ first: idx === 0 }">
              <view class="avatar-box">
                <view class="img-box">
                  <!-- #ifdef H5 -->
                  <img class="header_img" :src="item.prelogo | imageFilter('w_240')" />
                  <!-- #endif -->
                  <!-- #ifndef H5 -->
                  <image class="header_img" :src="item.prelogo | imageFilter('w_240')" mode="widthFix"></image>
                  <!-- #endif -->
                </view>
                <image v-if="idx < 3" class="brand" :src="`/images/new_icon/No${idx + 1}.png` | imageFilter('m_80')">
                </image>
              </view>
              <view class="info">
                <view class="name">{{ item.cname }}</view>
                <view class="build_name">
                  <!-- <image class="level_icon" :src="getLevelIcon(item.levelid) | imageFilter('w_80')"></image> -->
                  <text>{{ item.minfo }}</text>
                </view>
              </view>
              <view class="btn_box flex-row" @click.prevent.stop="handleAsk(item.mid, item.id, 'adviser')">
                <my-icon type="ic_zixun1" size="32rpx" color="#fff"></my-icon>
                <text class="zx">咨询</text>
              </view>
            </view>
          </swiper-item>
          <swiper-item v-if="item.adviserGuWen.length < 2"></swiper-item>
        </template>
      </swiper>
    </view>
    <!-- 楼盘问答 -->
    <question-hot :question="questionHot" v-if="questionHot.is_show && questionHot.list.length > 0"></question-hot>
    <!-- 猜你喜欢的二手房 -->
    <!-- <view class="plate-box container" v-if="style===0">
        <view class="banner-box" v-if="adv.is_show&&adv.advB.length>0">
          <block v-for="(item, idx) in adv.advB" :key="idx">
            <view class="banner-item" @click="toLink(item.link)">
              <banner :image="item.image" mode="widthFix" height="auto"></banner>
            </view>
          </block>
        </view>
        <view class="plate-title flex-row">
          <text class="active">猜你喜欢的二手房</text>
          <view class="c-right more" @click="More('ershoufang')">更多</view>
        </view>
        <block v-for="(item) in secondHouse" :key="item.id">
          <house-item :item-data="item" type="ershou" @click="toDetail"></house-item>
        </block>
        <view class="bottom-more" @click="More('ershoufang')">查看更多</view>
      </view> -->
    <!-- 信息流 -->
    <!-- <view class="banner-box container" v-if="adv.is_show&&adv.advB.length>0">
        <block v-for="(item, idx) in (adv.advB||[])" :key="idx">
        <view class="banner-item" @click="toLink(item.link)">
          <banner :image="item.image" mode="widthFix" height="auto"></banner>
        </view>
      </block> 
    </view> -->
    <!-- <view class="banner-box container" v-if="adv.is_show&&adv.advB.length>0" >
      <view class="banner-item">
          <my-swiper   @click="toBanner"  :focus="adv.advB" :autoplay="true" :interval="5000" height="152rpx" :circular="true"></my-swiper>
      </view>
    </view> -->
    <view class="guanggaob" v-if="adv.is_show && adv.advB && adv.advB.length > 0">
      <view class="banner-box container">
        <swiper class="banner" :autoplay="true" :interval="5000" :circular="true"
          :style="{ 'height': advbswiperHeight + 'px' }">
          <swiper-item v-for="(img, index) in adv.advB" :key="index" @click="toLink(img.link)">
            <view class="adv-item banner-item" :class="{ rounded: rounded }">
              <image class="swiperItmeHeightb" :src="img.image | imageFilter('w_6401')" mode="widthFix"
                @load="swiperHeightFnb"></image>
              <view v-if="img.is_show_label == 1" class="marker">广告</view>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>
    <view class="plate-box info_box" id="info_box" v-if="xinxiList.is_show === 1 && xinxiList.style == 1">
      <view class="plate-title flex-row" :class='{ filter: huiFilter || search.is_show == 0 }'>
        <view class="cate flex-row" style="line-height:64rpx;height:74rpx">
          <tab-bar :tabs="xinxiList.xinxilan" :fixedTop="false" lineHeight="88rpx" height="88rpx" :showLine="false"
            :nowIndex="info_list_current_index">
            <text v-for="(item, index) in xinxiList.xinxilan" :key="index" :id="'i' + index"
              :class="{ active: info_list_current_index === index }"
              @click="onClickInfoCate({ detail: { current: index } })">{{ item.title }}</text>
          </tab-bar>
        </view>
      </view>
      <view class="infos" style="width:100vw;overflow:hidden">
        <swiper @change="onInfoBoxChange" @animationfinish="onAnimationfinish" :current="info_list_current_index"
          :style="{ 'height': info_list_height }">
          <swiper-item v-for="(xinxi, index) in xinxiList.xinxilan" :key="index">
            <view class="info_list"
              :class="{ new: xinxilan_list_styleto == 2 && xinxi.type != 'zixun' && xinxi.type != 'xinfang' }"
              :id="'info_list' + index">
              <template v-if="xinxi.type !== 'xinfang' && xinxi.type !== 'zixun'">
                <template v-if="xinxilan_list_styleto == 1">
                  <block v-for="item in xinxi.list" :key="item.id">
                    <house-item v-if="xinxi.type === 'ershoufang'" :item-data="item" type="ershou"
                      @click="toDetail"></house-item>
                    <house-item v-if="xinxi.type === 'zufang'" :item-data="item" type="renting"
                      @click="toDetail"></house-item>
                    <block v-if="xinxi.type === 'estate'">

                      <listItemItem v-if="item.parentid == 1" :itemData="item" type="sale" @click="toDetail">
                      </listItemItem>
                      <listItemItem v-if="item.parentid == 2" :itemData="item" type="rent" @click="toDetail">
                      </listItemItem>
                      <listItemItem v-if="item.parentid == 3" :itemData="item" type="transfer" @click="toDetail">
                      </listItemItem>
                    </block>
                  </block>
                </template>
                <template v-if="xinxilan_list_styleto == 2">
                  <view class="info_item" v-for="item in left" :key="item.id">
                    <index-item :itemData="item" :type="xinxi.type" @click="toDetail"></index-item>
                  </view>
                  <view class="info_item" v-for="item in right" :key="item.id">
                    <index-item :itemData="item" :type="xinxi.type" @click="toDetail"></index-item>
                  </view>
                </template>

              </template>
              <template v-else>
                <template v-for="item in xinxi.list">
                  <new-house-item v-if="xinxi.type === 'xinfang'" :key="item.id" :item-data="item" type="new_house"
                    @click="toDetail"></new-house-item>
                  <news-item v-if="xinxi.type === 'zixun'" :key="item.id" :itemData="item"></news-item>

                </template>
              </template>
            </view>
          </swiper-item>
        </swiper>
        <!-- 瀑布流布局 -->

        <!-- <view class="load_status text-center" v-if="xinxiList.xinxilan[info_list_current_index].load_status === 'loading'">
    加载中...</view>
  <view class="load_status text-center" v-if="xinxiList.xinxilan[info_list_current_index].load_status === 'nomore'">
    没有更多数据了</view> -->
        <view class="more_bottom_btn mg" @click="More(recommend_type, info_list_current_index)">查看更多</view>
      </view>
    </view>

    <view v-if="xinxiList.is_show === 1 && xinxiList.style != 1">
      <view v-for="(xinxi, index) in xinxiList.xinxilan" :key="index">
        <view class="load_status text-center mgb_32" v-if="xinxi.load_status === 'loading'">加载中...</view>
        <view class="plate-box  container" v-if="xinxi.list.length > 0">
          <view class="xinxi_list">
            <view class="plate-title flex-row" :class='{ filter: huiFilter }'>
              <text class="active">{{ xinxi.title }}</text>
            </view>
            <block v-for="item in xinxi.list" :key="item.id">
              <!-- 新房 -->
              <new-house-item v-if="xinxi.type === 'xinfang'" :item-data="item" type="new_house"
                @click="toDetail"></new-house-item>
              <!-- 二手房 -->
              <house-item v-if="xinxi.type === 'ershoufang'" :item-data="item" type="ershou"
                @click="toDetail"></house-item>
              <!-- 出租房 -->
              <house-item v-if="xinxi.type === 'zufang'" :item-data="item" type="renting"
                @click="toDetail"></house-item>
              <!-- 资讯 -->
              <news-item v-if="xinxi.type === 'zixun'" :itemData="item"></news-item>
              <block v-if="xinxi.type === 'estate'">

                <listItemItem v-if="item.parentid == 1" :itemData="item" type="sale" @click="toDetail"></listItemItem>
                <listItemItem v-if="item.parentid == 2" :itemData="item" type="rent" @click="toDetail"></listItemItem>
                <listItemItem v-if="item.parentid == 3" :itemData="item" type="transfer" @click="toDetail">
                </listItemItem>
              </block>
            </block>
            <view class="more_bottom_btn" @click="More(xinxi.type, index)">查看更多</view>
          </view>
        </view>
      </view>
    </view>
    <!-- <view class="nomore">
        <view class="line"></view>
        <text>我也是有底线的</text>
        <view class="line"></view>
      </view> -->

    <view class="servicer">
      <view class="site_name">{{ $store.state.siteName }} 客服电话：{{ $store.state.siteTel }}</view>
    </view>

    <my-popup ref="img_popup" position="top" top_0 height="100vh">
      <view class="img_box">
        <view class="img">
          <view class="time_down" @click="closePopupImg">跳过{{ img_popup_time || '' }}</view>
          <view class="label" v-if='$store.state.adv_show_label == 1'>广告</view>
          <image @click="toPopupImgUrl(popup.url)" mode="widthFix" :src="popup.img_popup | imageFilter('w_8601')">
          </image>
        </view>
      </view>
    </my-popup>
    <view class="right_hb" :class="{ show: show_right_hb }">
      <view class="close" @click="show_right_hb = false">
        <my-icon type="qingchu" color="#555"></my-icon>
      </view>
      <image @click="toRightHbUrl(popup1.url)" mode="widthFix" :src="popup1.img_popup | imageFilter('w_320')"></image>
    </view>
    <!-- #ifdef APP-PLUS -->
    <my-model title="隐私政策提示" :is_show="show_model"
      :btns='[{ name: "不同意", color: "#666666" }, { name: "同意", color: "#f65354" }]' @clickBtn="clickModelBtn">
      <view>欢迎使用{{ siteName }}！根据相关法律法规要求，请您仔细阅读<text @click="toProtocol()" style="color:#f65354">《用户协议》</text>及<text
          @click="toPrivacy()" style="color:#f65354">《隐私政策》</text>相关条款。在您同意并接受后，将可以正常使用{{ siteName }}。</view>
    </my-model>
    <!-- #endif -->
    <!-- #ifndef MP-WEIXIN -->
    <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip"></login-popup>
    <!-- #endif -->
    <chat-tip></chat-tip>
    <!-- <image style="width: 0;height: 0; position: fixed; left: -1px" src="https://images.tengfangyun.com/attachment/config/20210528/4410b102a42e0ab9520ab1bb44d4d3179b3f186b.jpeg?x-oss-process=style/w_1200"></image> -->
  </view>

</template>

<script>
import { mapState } from 'vuex';
import { formatImg, config } from '../../common/index.js'
import myIcon from '../../components/myIcon'
import mySwiper from '../../components/mySwiper'
import myGrid from '../../components/myGrid.vue'
import banner from "../../components/banner.vue"
import myPopup from "../../components/myPopup.vue"
import tabBar from "../../components/tabBar.vue"
import houseItem from '../../components/houseItem.vue'
import newHouseItem from '../../components/newHouseItem.vue'
import indexItem from '../../components/indexItem.vue'
import newsItem from '../../components/newsItem.vue'
import getChatInfo from '../../common/get_chat_info'
import listItemItem from '../../components/listItemItem.vue'
// #ifndef MP-WEIXIN
import loginPopup from '../../components/loginPopup'
import questionHot from '../../components/questionHot'
// #endif
export default {
  data() {
    return {
      rounded: '', // 初始化为 false 或其他默认值
      advaswiperHeight: '',
      advbswiperHeight: '',
      benyuefangjia: {},
      // imageURL1:'../../static/icon/video.png',
      fload: 1,
      video_is_show: {},
      xinxilan_list_style: '',
      xinxilan_list_styleto: '',
      leixing: '',
      radiolist: [],
      equispaced: 1,
      puwon: '',
      data_units: {
        xinfangjunjia: "元/m²",
        xinfangshulliang: "套",
        ershoufangjunjia: "元/m²",
        ershoufangshuliang: "套",
        chuzufangjunjia: "元/月",
        chuzufangshuliang: "套",
        chengjiaoliang: "套",
        xiaoqushuliang: "个",
      },
      loushibao: {},
      focus: {},
      navs: {},
      gridDots: false,
      gridCurrent: 0,
      subGridDots: false,
      subGridCurrent: 0,
      newsList: {},
      adv: {},
      hotBuilds: {}, //热门楼盘
      advisers: {},
      pai_list: {
        1: '/images/new_icon/ic_jinbei.png',
        2: '/images/new_icon/ic_yinbei.png',
        3: '/images/new_icon/ic_tongbei.png'
      },
      xinxiList: {},
      popup: {},
      popup1: {},
      // #ifdef APP_PLUS
      show_model: false,
      // #endif
      login_tip: '',
      service_type: '',
      recommend_type: '',
      info_list_current_index: 0,
      info_list_height: 'auto',
      show_right_hb: false, //是否显示右侧红包广告
      img_popup_time: 5,
      showPropertyData: 0,
      questionHot: {},   //楼盘问答
      build_list: {},
      module_nav: {},
      buildCount: {
        is_show: 0,
        hot_builds: []
      },
      color: "",
      columnNum: 5,
      swiperHeight: '52vw',
      swipePaddingBottom: "0",
      xinxilan_list_style: '',
      xinxilan_list_styleto: '',
      search: {
        is_show: 0
      },
      subOptions: {},
      showView: false,
      toolsOptions: [],
      subSwiperHeight: 0,
      subSwipePaddingBottom: 0,
      columnSubNum: 5,
      bannerH: 0,
      left: [],
      right: [],
      radiotitle: '',
      leixing: '',
      videoType: 0,
    }
  },
  computed: {
    ...mapState(['home_switchs', 'siteName']),
    imchat() {
      return this.$store.state.im.ischat
    },
    help_hb() {
      return config.imgDomain + "/hongbao/fangyuan_list_hongbao.png"
    },
    huiFilter() {
      return this.$store.state.huiFilter
    }

  },
  components: {
    myGrid,
    mySwiper,
    banner,
    myPopup,
    tabBar,
    houseItem,
    indexItem,
    newsItem,
    myIcon,
    newHouseItem,
    questionHot,
    listItemItem,
    // #ifndef MP-WEIXIN
    loginPopup
    // #endif
  },
  onLoad() {
    // #ifdef APP-PLUS
    if (!uni.getStorageSync('read_privacy_olicy') && this.$store.state.systemInfo.platform === 'android') {
      this.show_model = true
      this.nvMask = new plus.nativeObj.View("nvMask", { //先创建遮罩层
        bottom: '0px',
        left: '0px',
        height: '50px',
        width: '100%',
        backgroundColor: 'rgba(0,0,0,0.3)'
      });
      this.nvMask.show()
    }
    // #endif
    uni.showLoading({
      title: "加载中..."
    })
    let optionsList = uni.getStorageSync('indexOptionsList')
    if (optionsList) {
      if (optionsList.length == 1 && optionsList[0].length <= 5) {
        this.swiperHeight = '150rpx'
        if (optionsList[0].length > 2) {
          this.swipePaddingBottom = "30rpx"
          this.columnNum = optionsList[0].length
        }
      } else {
        this.swiperHeight = '52vw'
      }
      this.navs = optionsList
      if (optionsList.length > 1) {
        this.gridDots = true
      }
    }
    this.initData()
    uni.$on("getDataAgain", () => {
      // this.xinxiList.xinxilan[this.info_list_current_index].list = []
      //  this.initData()
      this.xinxiList.xinxilan[this.info_list_current_index].page == 1
      this.getListData(this.info_list_current_index)
    })
    // this.getHandleRadio()
    // this.getData()
  },
  computed: {
    huiFilter() {
      return this.$store.state.huiFilter
    }
  },
  // #ifdef APP-PLUS
  onShow() {
    if (this.nvMask && this.show_model) {
      this.nvMask.show()
    }

  },
  onHide() {
    if (this.nvMask && this.show_model) {
      this.nvMask.hide()
    }
  },
  // #endif
  filters: {
    showFormat(items) {
      if (items.show_type === '1' && ((items.type === 'new_house' && this.home_switchs.open_newhouse) || (items.type === 'ershou' && this.home_switchs.open_info) || (items.type === 'renting' && this.home_switchs.open_info))) {
        return true
      }
      return false
    },
    imgUrl(val, param = "") {
      return formatImg(val, param)
    },
    formatBgColor(index) {
      switch (index) {
        case 0:
          return '#FFF5F0'
          break;

        default:
          break;
      }
    }
  },
  onUnload() {
    uni.$off("getDataAgain")
  },
  methods: {
    swiperHeightFn() {
      var self = this;
      let view = uni.createSelectorQuery().select(".swiperItmeHeight");
      view.boundingClientRect(data => {
        if (data) {
          self.advaswiperHeight = data.height
        }
      }).exec();

    },
    swiperHeightFnb() {
      var self = this;
      let view = uni.createSelectorQuery().select(".swiperItmeHeightb");
      view.boundingClientRect(data => {
        if (data) {
          self.advbswiperHeight = data.height
        }
      }).exec();

    },
    // 查看全部
    handleEvery() {
      this.$navigateTo("/radio/video")
    },
    // 视频跳转列表
    getTapRadio(val, index) {
      if (val.type == 3) {
        this.$navigateTo("/vr/detailto?id=" + this.radiolist[index].id)
        return
      }
      this.$navigateTo("/vr/prevideotwo?id=" + this.radiolist[index].id + "&index=" + index)
    },
    // 获取视频分类
    getHandleRadio() {
      this.$ajax.get('video/getIndexVideos', {}, res => {
        console.log(res, "我是视频")
        if (res.data.code == 1) {
          this.radiolist = res.data.list;
        } else {
          this.radiolist = []
        }
      })
    },
    getLevelIcon(levelid) {
      return this.pai_list[levelid]
    },
    // #ifdef APP-PLUS
    clickModelBtn(e) {
      if (e === 0) {
        plus.runtime.quit()
      }
      if (e === 1) {
        this.show_model = false
        this.nvMask.hide()
        uni.setStorageSync('read_privacy_olicy', 1)
      }
    },
    toProtocol() {
      this.$navigateTo('/user/my/protocol')
    },
    toPrivacy() {
      this.$navigateTo('/user/my/reg_infos')
    },
    // #endif
    isShow(items) {
      if (items.show_type === '1' && ((items.type === 'new_house' && this.home_switchs.open_newhouse) || (items.type === 'ershou' && this.home_switchs.open_info) || (items.type === 'renting' && this.home_switchs.open_info))) {
        return true
      }
      return false
    },
    More(e, info_list_current) {
      let url = ""
      let params = ""
      if (e !== 'estate') {
        if (info_list_current !== undefined && this.xinxiList.xinxilan[info_list_current].parameter) {
          let i = 0
          for (let key in this.xinxiList.xinxilan[info_list_current].parameter) {
            params += `${i === 0 ? '?' : '&'}${key}=${this.xinxiList.xinxilan[info_list_current].parameter[key]}`
            i++
          }
        }
        switch (e) {
          case 'xinfang':
            url = '/pages/new_house/new_house' + params
            break;
          case 'adviser':
            url = '/pages/consultant/consultant' + params
            break;
          case 'ershoufang':
            url = '/pages/ershou/ershou' + params
            break;
          case 'agent':
            url = '/pages/agent/agent' + params
            break;
          case 'zufang':
            url = '/pages/renting/renting' + params
            break;
          case 'zixun':
            url = '/pages/news/news' + params
            break;
        }
      } else {
        if (info_list_current !== undefined && this.xinxiList.xinxilan[info_list_current].parameter && this.xinxiList.xinxilan[info_list_current].parameter.parentid) {
          let type = Number(this.xinxiList.xinxilan[info_list_current].parameter.parentid)
          switch (type) {
            case 1:
              url = '/commercial/sale/sale'
              break;
            case 2:
              url = '/commercial/rent/rent'
              break;
            case 3:
              url = '/commercial/transfer/transfer'
              break;
            case 0:
              url = '/commercial/commercial?catid=' + (this.xinxiList.xinxilan[info_list_current].parameter.cid || '')
              break;

            default:
              url = '/commercial/commercial'
              break;
          }
        } else {
          url = '/commercial/commercial'
        }
      }
      if (url) {
        this.$navigateTo(url)
      }
    },
    handleAsk(mid, id, type) {
      if (type) {
        this.chat_tempinfo = { mid, id, type }
      }
      if (this.imchat == 0) {
        if (this.chat_tempinfo.type == "adviser") {
          this.consuDetail(this.chat_tempinfo.id)
          return
        } else {
          this.$navigateTo('/pages/agent/detail?id=' + this.chat_tempinfo.id)
          return
        }
      }
      // #ifdef MP-WEIXIN
      if (type == "adviser") {
        getChatInfo(this.chat_tempinfo.mid, 9)
      } else if (type == "agent") {
        getChatInfo(this.chat_tempinfo.id, 9)
      }
      // #endif
      // #ifndef MP-WEIXIN
      this.checkLogin('为方便您及时接收消息通知，请输入手机号码', () => {
        if (this.chat_tempinfo.type == "adviser") {
          getChatInfo(this.chat_tempinfo.mid, 9)
        } else if (this.chat_tempinfo.type == "agent") {
          getChatInfo(this.chat_tempinfo.id, 9)
        }
      })
      // #endif
    },
    checkLogin(tip, callback) {
      this.$ajax.get('member/checkUserStatus', {}, res => {
        if (res.data.code === 1) {
          callback && callback()
        } else if (res.data.status == 1) {
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
        } else if (res.data.status == 2) {
          this.$store.state.user_login_status = res.data.status
          this.login_tip = tip
          this.$refs.login_popup.showPopup()
        }
      })
    },
    handleCloseLogin() {
      if (this.$store.state.user_login_status === 1) {
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if (this.login_status === 2) {
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    loginSuccess(e) {
      this.handleAsk()
    },
    // numAnimation(val, name, add_num = 25) {
    //   if (val == null) {
    //     return ""
    //   }
    //   let start = val / add_num
    //   let num = 1
    //   if (val <= add_num) {
    //     this[name] = val
    //     return
    //   }
    //   setTimeout(() => {
    //     this[name] = ""
    //     let timer = setInterval(() => {
    //       num++
    //       this[name] = parseInt(start * num)
    //       if (num >= 25) {
    //         clearInterval(timer)
    //         this[name] = parseInt(val)
    //       }
    //     }, 120)
    //   }, 500)
    // },
    // toDetail(e) {
    //   let url = ""
    //   switch (e.type) {
    //     case 'new_house':
    //       url = '/pages/new_house/detail?id=' + e.detail.id
    //       break;
    //     case 'ershou':
    //       url = '/pages/ershou/detail?id=' + e.detail.id
    //       break;
    //     case 'agent':
    //       url = '/pages/agent/detail?id=' + id
    //       break;
    //     case 'renting':
    //       url = '/pages/renting/detail?id=' + e.detail.id
    //       break;
    //     case 'sale':
    //       url = '/commercial/sale/detail?id=' + e.detail.id
    //       break;
    //     case 'rent':
    //       url = '/commercial/rent/detail?id=' + e.detail.id
    //       break;
    //     case 'transfer':
    //       url = '/commercial/transfer/detail?id=' + e.detail.id
    //       break;
    //   }
    //   if (url) {
    //     this.$store.state.tempData = e.detail
    //     this.$navigateTo(url)
    //   }
    // },
    toDetail(e) {
      let url = ""
      switch (e.type) {
        case 'new_house':
        case 'xinfang':
          url = '/pages/new_house/detail?id=' + e.detail.id
          break;
        case 'ershou':
        case 'ershoufang':
          url = '/pages/ershou/detail?id=' + e.detail.id
          break;
        case 'agent':
          url = '/pages/agent/detail?id=' + id
          break;
        case 'renting':
        case 'zufang':
          url = '/pages/renting/detail?id=' + e.detail.id
          break;
        case 'sale':
          url = '/commercial/sale/detail?id=' + e.detail.id
          break;
        case 'rent':
          url = '/commercial/rent/detail?id=' + e.detail.id
          break;
        case 'transfer':
          url = '/commercial/transfer/detail?id=' + e.detail.id
          break;
        case 'zixun':
          url = '/pages/news/detail?id=' + e.detail.id
          break;
        case 'estate':
          if (e.detail.parentid == 1) {
            url = '/commercial/sale/detail?id=' + e.detail.id
          }
          if (e.detail.parentid == 2) {
            url = '/commercial/rent/detail?id=' + e.detail.id
          }
          if (e.detail.parentid == 3) {
            url = '/commercial/transfer/detail?id=' + e.detail.id
          }

          break;
      }
      if (url) {
        this.$store.state.tempData = e.detail
        this.$navigateTo(url)
      }
    },
    //申请入驻
    join() {
      this.$store.state.allowOpen = true
      if (this.$store.state.home_switchs.allow_advsier_register) {
        this.getUserData()
      } else {
        uni.showToast({
          title: "暂未开放 敬请期待",
          icon: "none"
        })
      }
    },
    getUserData() {
      // 获取会员信息
      this.$ajax.get('Adviser/adv_reg', {}, (res) => {
        if (res.data.code == 1) {
          this.cname = res.data.data.cname || ''
          this.tel = res.data.data.tel || ''
          this.wxkf = res.data.wxqunkefu || ''
          this.$navigateTo("/user/consultant/add?cname=" + this.cname + "&tel=" + this.tel + "&wxkf=" + this.wxkf)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
        }
      }, (err) => {
        console.log(err)
      }, false)
    },
    toAgentDetail(index, id) {
      // this.$store.state.tempData = this.recommendMember[index]
      this.$navigateTo("/pages/agent/detail?id=" + id)
    },
    consuDetail(id) {
      this.$navigateTo("/pages/consultant/detail?id=" + id)
    },
    initData() {
      var thisadv = this;
      this.$ajax.get('index/customIndexNew.html', {}, res => {
        uni.stopPullDownRefresh();
        // #ifdef H5 || MP-BAIDU
        if (res.data.seo) {
          let seo = res.data.seo
          if (res.data.share && res.data.share.pic) {
            seo.image = formatImg(res.data.share.pic, 'w_8001')
          }
          this.seo = seo
        }
        // #endif
        if (res.data.share) {
          this.share = res.data.share
        }
        this.getWxConfig()
        this.adv = res.data.adv
        if (res.data.code == 0) {
          return
        }
        if (res.data.code == 0) {
          return
        }
        // 焦点图
        this.focus = res.data.focus;
        this.float = res.data.xinxi.is_show
        this.video_is_show = res.data.video
        if (this.video_is_show.is_show == 1) {
          this.radiotitle = res.data.video.title
          this.getHandleRadio()
        }
        // 宫格导航
        let arr = []
        for (const key in res.data.navs.nav) {
          if (res.data.navs.nav.hasOwnProperty(key)) {
            const element = res.data.navs.nav[key];
            arr.push(element)
          }
        }
        let optionsList = arr.map((item, index) => {
          return {
            text: item.title,
            image: formatImg(item.ico, 'w_240'),
            url: item.url
          }
        })
        let len = optionsList.length
        let tempOptionsList = []
        for (let i = 0; i < len; i += 10) {
          tempOptionsList.push(optionsList.splice(0, 10))
        }
        res.data.navs.nav = tempOptionsList
        this.navs = res.data.navs
        this.search = res.data.search

        uni.setStorage({
          key: "indexOptionsList",
          data: this.navs
        })
        if (this.navs.nav.length > 1) {
          this.gridDots = true
        }
        if (this.navs.nav.length == 1 && this.navs.nav[0].length <= 5) {
          this.swiperHeight = '150rpx'
          if (this.navs.nav[0].length > 2) {
            this.columnNum = this.navs.nav[0].length
            this.swipePaddingBottom = "30rpx"
          }
        } else {
          this.swiperHeight = '52vw'
        }
        let subOption = res.data.subnav
        let subArr = []
        for (const key in subOption.children) {
          if (subOption.children.hasOwnProperty(key)) {
            const element = subOption.children[key];
            subArr.push(element)
          }
        }
        let subOptionsList = subArr.map((item, index) => {
          return {
            text: item.title,
            image: formatImg(item.ico, 'w_240'),
            url: item.url
          }
        })
        if (subOption.display_mode == 2) {
          // 宫格导航

          let subLen = subOptionsList.length
          let tempSubOptionsList = []
          for (let i = 0; i < subLen; i += 10) {
            tempSubOptionsList.push(subOptionsList.splice(0, 10))
          }
          subOption.children = tempSubOptionsList
          if (tempSubOptionsList.length == 1 && tempSubOptionsList[0].length <= 5) {
            this.subGridDots = false
            this.subSwiperHeight = '130rpx'
            if (tempSubOptionsList[0].length > 2) {
              this.columnSubNum = tempSubOptionsList[0].length
              // this.swipePaddingBottom ="30rpx"
            }
          } else {
            this.subGridDots = true
            this.subSwiperHeight = "35vw"
          }
        } else {
          subOption.children = subOptionsList
        }
        this.subOptions = subOption
        // if (subOption.is_show && subOption.children &&subOption.children.length) {
        //   this.subOptions=subOption.children.map((item, index) => {
        //     return {
        //       text: item.title,
        //       image: formatImg(item.ico, 'w_240'),
        //       url: item.url
        //     }
        //   })
        // }
        if (res.data.navs.setting == 1 && res.data.navs.children && res.data.navs.children.length) {
          res.data.navs.children.map(item => {
            return {
              text: item.title,
              icon: item.icon,
              url: item.url,
              count: item.count
            }
          })
        }
        let toolsOptions = res.data.tools
        if (toolsOptions.is_show && toolsOptions.children && toolsOptions.children.length) {
          this.toolsOptions = toolsOptions.children
        }

        this.showView = true
        uni.hideLoading()
        // 瀑布流样式
        this.xinxilan_list_styleto = res.data.xinxilan_list_style
        // 楼市报
        this.loushibao = res.data.loushibao
        // 房价数据
        this.benyuefangjia = res.data.benyuefangjia
        // 最新动态
        this.newsList = res.data.news

        // 热门楼盘
        this.hotBuilds = res.data.hotBuild
        //楼盘数据
        this.buildCount = res.data.hot_builds
        // 置业顾问
        this.advisers = res.data.adviser
        if (res.data.adviser.zhiyeguwen && res.data.adviser.zhiyeguwen.length > 0) {
          this.service_type = res.data.adviser.zhiyeguwen[0].type
        }

        // 楼盘问答
        this.questionHot = res.data.build_question;
        this.build_list = res.data.build_list
        this.module_nav = res.data.module_nav
        // 信息栏
        res.data.xinxi.xinxilan = res.data.xinxi.xinxilan.map(item => {
          item.load_status = ""
          item.list = []
          return item
        })
        this.xinxiList = res.data.xinxi
        this.recommend_type = this.xinxiList.xinxilan[this.info_list_current_index].type
        this.getListData(this.info_list_current_index)
        // 弹窗
        if (res.data.popup) {
          if (res.data.popup.duration || res.data.popup.duration == 0) {
            this.img_popup_time = res.data.popup.duration
          }
          this.popup = res.data.popup
          // 获取弹出时间
          let popup_time = Date.parse(new Date())
          // 和上次弹出时间做比较
          let home_popup_temp_store = uni.getStorageSync('home_popup')
          if (home_popup_temp_store) {
            let home_popup_temp = JSON.parse(home_popup_temp_store)
            if (popup_time - home_popup_temp.time > 1000 * 60 * 60 * 2 || this.popup.img_popup !== home_popup_temp.img) {
              let popup_temp = {
                time: popup_time,
                img: this.popup.img_popup
              }
              uni.setStorageSync('home_popup', JSON.stringify(popup_temp))
              this.popupImg()
            }
          } else {
            let popup_temp = {
              time: popup_time,
              img: this.popup.img_popup
            }
            uni.setStorageSync('home_popup', JSON.stringify(popup_temp))
            this.popupImg()
          }
        }
        this.showPropertyData = res.data.house_data

        if (res.data.popup1 && this.popup1.img_popup === undefined) {
          this.popup1 = res.data.popup1
          // 获取弹出时间
          let popup1_time = Date.parse(new Date())
          // 和上次弹出时间做比较
          let home_popup1_temp_store = uni.getStorageSync('home_popup1')
          if (home_popup1_temp_store) {
            let home_popup1_temp = JSON.parse(home_popup1_temp_store)
            if (popup1_time - home_popup1_temp.time > 1000 * 60 * 60 * 2 || this.popup1.img_popup !== home_popup1_temp.img) {
              let popup1_temp = {
                time: popup1_time,
                img: this.popup1.img_popup
              }
              uni.setStorageSync('home_popup1', JSON.stringify(popup1_temp))
              this.show_right_hb = true
            }
          } else {
            let popup1_temp = {
              time: popup1_time,
              img: this.popup1.img_popup
            }
            uni.setStorageSync('home_popup1', JSON.stringify(popup1_temp))
            this.show_right_hb = true
          }
        }

      }, err => {
        uni.hideLoading()
        uni.stopPullDownRefresh();
      })
    },
    popupImg() {
      if (this.img_popup_time == 0) {
        return
      }
      setTimeout(() => {
        this.$refs.img_popup.show()
        this.popup_img_timer = setInterval(() => {
          if (this.img_popup_time <= 0) {
            this.closePopupImg()
            return
          }
          this.img_popup_time--
        }, 1000)
      }, 300)
    },
    goPropertyData() {
      if (this.showPropertyData == 1) {
        this.$navigateTo("/propertyData/property_data")
      }
    },
    toPopupImgUrl(url) {
      this.$navigateTo(url)
      this.closePopupImg()
    },
    toRightHbUrl(url) {
      this.$navigateTo(url)
      this.show_right_hb = false
    },
    // 关闭弹窗广告
    closePopupImg() {
      if (this.popup_img_timer) {
        clearInterval(this.popup_img_timer)
      }
      this.$refs.img_popup.hide()
    },
    // getListData(index) {
    //   // if (this.xinxiList.xinxilan[index].list.length > 0) {
    //   //   return
    //   // }
    //   let params = Object.assign({}, this.xinxiList.xinxilan[index].parameter || {})
    //   params.type = this.xinxiList.xinxilan[index].type
    //   params.setting = this.xinxiList.setting
    //   this.xinxiList.xinxilan[index].load_status = 'loading'
    //   this.$ajax.get('index/infoData.html', params, res => {
    //     this.xinxiList.xinxilan[index].load_status = 'loadend'
    //     if (res.data.code === 1) {
    //       if (this.xinxiList.xinxilan[index].type == "xinfang") {
    //         res.data.list.map(item => {
    //           if (item.build_type && item.build_type.length > 2) {
    //             item.build_type.length = 2
    //           }
    //         })
    //       }
    //       this.xinxiList.xinxilan[index].list = res.data.list
    //     }
    //     if (this.xinxiList.xinxilan[index].list.length === 0) this.xinxiList.xinxilan[index].load_status = 'nomore'
    //     if (this.xinxiList.style == 1) {
    //       this.setSwiperHeight(index)
    //     }
    //   })
    // },
    getListData(index) {
      // if(this.xinxiList.xinxilan[index].list.length>0){
      //   return
      // }
      this.xinxiList.xinxilan[index].load_status = 'loading'
      let params = Object.assign({}, this.xinxiList.xinxilan[index].parameter || {})
      params.type = this.xinxiList.xinxilan[index].type
      params.setting = this.xinxiList.setting
      // params.setting = 10
      params.page = this.xinxiList.xinxilan[index].page || 1
      if (this.xinxiList.xinxilan[index].page == 1) {
        this.xinxiList.xinxilan[index].list = []
        this.left = []
        this.right = []
      }
      this.leixing = params.type

      this.$ajax.get('index/infoData.html', params, res => {
        this.xinxiList.xinxilan[index].load_status = 'loadend'
        if (res.data.code === 1) {
          if (this.xinxiList.xinxilan[index].type == "xinfang") {
            res.data.list.map(item => {
              if (item.build_type && item.build_type.length > 2) {
                item.build_type.length = 2
              }
            })
          }
          let arr = res.data.list
          const left = res.data.list.filter((item, index) => index % 2 === 0);
          const right = res.data.list.filter((item, index) => index % 2 !== 0);
          this.left = this.left.concat(left)
          this.right = this.right.concat(right)
          // arr = [...left,...right]
          this.xinxiList.xinxilan[index].list = this.xinxiList.xinxilan[index].list.concat(arr)
          if (arr.length < params.setting) {
            this.xinxiList.xinxilan[index].load_status = 'nomore'
          } else {
            this.xinxiList.xinxilan[index].load_status = 'more'
          }
          // if(this.xinxiList.style == 1){
          this.setSwiperHeight(index)
          // }
        } else {
          this.xinxiList.xinxilan[index].load_status = 'nomore'
        }

      })
    },

    setSwiperHeight(index) {
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        query.select('#info_list' + index).fields({ rect: true, scrollOffset: true, size: true }, data => {
          this.info_list_height = data.height + 'px'
        }).exec();
      })
    },
    changeGridSwiper(e) {
      this.gridCurrent = e.detail.current
    },
    changeSubGridSwiper(e) {
      this.subGridCurrent = e.detail.current
    },
    toNav(e) {
      this.$navigateTo(this.navs.nav[this.gridCurrent][e.index].url)
    },
    toNav1(e, type) {
      let url;
      if (type == 'navs_children') {
        url = this.navs.children[e.index].path
      } else if (type == 'subOptions') {
        if (this.subOptions.display_mode == 2) {
          url = this[type]['children'][this.subGridCurrent][e.index].url
        } else {
          url = this[type]['children'][e.index].url
        }
      } else {
        url = this[type][e.index].url
      }
      if (type == 'toolsOptions') {
        url = this[type][e.index].path
      }
      this.$navigateTo(url)
    },
    toSearch() {
      this.$navigateTo("/pages/search/search")
    },
    toLink(url) {
      this.$navigateTo(url)
    },
    onClickInfoCate(e) {
      this.info_list_current_index = e.detail.current
    },
    onInfoBoxChange(e) {
      this.info_list_current_index = e.detail.current
      this.recommend_type = this.xinxiList.xinxilan[e.detail.current].type

      this.xinxiList.xinxilan[this.info_list_current_index].page = 1
      this.getListData(this.info_list_current_index)

      if (this.xinxiList.xinxilan[this.info_list_current_index].list.length > 0) {
        this.setSwiperHeight(this.info_list_current_index)
      } else {
        // this.xinxiList.xinxilan[e.detail.current].load_status = 'loading'
      }
    },
    onAnimationfinish(e) {
      // let params = Object.assign({},this.xinxiList.xinxilan[e.detail.current].parameter||{});
      // params.type = this.xinxiList.xinxilan[e.detail.current].type
      // this.getListData(e.detail.current)
    }
  },
  onPullDownRefresh() {
    this.initData()
    if (this.xinxiList.style != 1) {
      this.info_list_current_index = 0
    }
  },
  onTabItemTap(e) {
    uni.$emit('onTabItemTap', e)
    // #ifdef H5
    const { aplus_queue } = window;
    if (aplus_queue) {
      aplus_queue.push({
        action: 'aplus.sendPV',
        arguments: [{ is_auto: false }, {}]
      });
    }
    //#endif
  },
  onReachBottom() {
    // if(this.fload==0){
    //   let curr =this.xinxiList.xinxilan[this.info_list_current_index] 
    // // if (this.xinxiList.xinxilan[this.info_list_current_index].load_status == 'nomore') return 
    // if (curr.load_status == 'more') {
    //   if (!curr.page){
    //       curr.page=1
    //     }
    //     curr.page++
    //     this.getListData(this.info_list_current_index)
    // }
    // if(this.xinxilan_list_style==2){
    //       if(this.leixing=='xinfang'&&this.xinxilan_list_styleto==2){
    //       this.xinxilan_list_styleto=1
    //       this.puwon = 'old'
    //     }else{
    //       this.xinxilan_list_styleto=2
    //       this.puwon = 'new'
    //     }
    // }
    // }else{
    if (this.xinxiList.style == 1 || this.info_list_current_index >= this.xinxiList.xinxilan.length - 1) {
      return
    }
    this.info_list_current_index++
    this.getListData(this.info_list_current_index)
    // }
  },
  onPageScroll(e) {
    uni.createSelectorQuery().select('#info_box').fields({ rect: true, scrollOffset: true, size: true }, data => {

      if (data && data.top == uni.upx2px(88)) {
        this.info_cate_fixed_top = true
      } else {
        this.info_cate_fixed_top = false
      }
    }).exec();
  },
  onShareAppMessage() {
    if (this.share) {
      return {
        title: this.share.title || "",
        content: this.share.content || "",
        imageUrl: this.share.pic ? formatImg(this.share.pic, 'w_6401') : ""
      }
    }
  }
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.flex-row {
  flex-direction: row;
}

.pt96 {
  padding-top: 96rpx;
}

.plr48 {
  padding: 0 48rpx;
}

.mb30 {
  margin-bottom: 30rpx;
}

.mt30 {
  margin-top: 30rpx;
}

.mt20 {
  margin-top: 20rpx;
}

.mb20 {
  margin-bottom: 20rpx;
}

.index-content {

  background-color: #ffffff;
}

// 顶部搜索栏
.search-bar {
  box-sizing: border-box;
  width: 100%;
  height: 96rpx;
  line-height: 64rpx;
  padding: 16rpx 48rpx;
  align-items: center;
  position: fixed;
  top: 0;
  z-index: 10;
  background-color: #ffffff;

  .search-box {
    padding: 10rpx 24rpx;
    align-items: center;
    height: 100%;
    flex: 1;
    background-color: #f5f5f5;
    color: #999;
    border-radius: 8rpx;

    .inp {
      margin-left: 20rpx;
    }
  }

  .search-left {
    margin-right: 20rpx;
  }

  .site-name {
    font-size: 36rpx;
    font-weight: bold;
  }

  .search-right {
    margin-left: 20rpx;
    align-items: center;

    >text {
      color: #666;
      margin-left: 6rpx;
    }
  }
}

.swiper_container {
  padding-bottom: 44rpx;

  &.rounded {
    padding: 0 48rpx;
    padding-bottom: 44rpx;

    .swiper {
      border-radius: 20rpx;
    }
  }

  margin-top: 28rpx;

  .swiper {
    overflow: hidden;
  }
}

.grid {
  padding: 0 30rpx;
  // height: 52vw;
  background-color: #ffffff;
}

.container {
  padding: 0 48rpx;

  &.swiper-plate {
    padding-right: 0;

    .plate-title {
      padding-right: 48rpx;
    }
  }
}

.tools {
  .tool_title {
    font-size: 26rpx;
    color: #4D4D64;
    padding: 16rpx 0;
    // font-weight: 600;

  }

  .tools_content ::v-deep .uni-grid-item-text {
    margin-top: 4rpx;
  }
}

.line {
  height: 1rpx;
  background-color: #eee
}

// 新闻滚动条
.news-box {
  flex-direction: row;
  align-items: center;
  padding: 20rpx 0;
  background-color: #ffffff;

  swiper {
    height: 110upx;
    background-color: #ffffff;

    .swiper-item {
      flex-direction: row;
      align-items: center;
    }

    .lebel {
      display: inline-block;
      margin-right: 10rpx;
      padding: 0 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      color: #fff;
      border-top-left-radius: 8rpx;
      border-bottom-right-radius: 8rpx;

      &.label3 {
        background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
      }

      &.label2 {
        background-image: linear-gradient(180deg, #8CD3FC 0%, #4CC7F6 100%);
      }

      &.label1 {
        background-image: linear-gradient(to right, #ff706b, #fdaa5e);
      }
    }

    view {
      width: 100%;
      display: inline-block;
      height: 50upx;
      line-height: 50upx;
      font-size: 28rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #333;
    }
  }
}

.active {
  span {
    font-size: 20rpx;
  }
}

.sub_grid {
  padding: 0 12rpx;
}

.sub_grid ::v-deep {
  .uni-grid-item-image {
    width: 50rpx;
    height: 50rpx;
  }

  .uni-grid-item-text {
    margin-top: 4rpx;
  }

  .uni-grid-item-image-count {
    margin-top: -4rpx;
  }

  .uni-grid-item:before {
    padding-bottom: 82%;
  }

  .uni-grid__flex~.uni-grid__flex {
    margin-top: 8rpx;
  }
}

.sub_top_grid ::v-deep {
  .uni-grid-item-image {
    width: 100rpx;
    height: 80rpx;
  }

  .uni-grid-item-text {
    font-size: 24rpx;
  }
}

.card-box {
  margin-top: 48rpx;
  padding: 0 24rpx;
  border-radius: 8rpx;
  border: 1rpx solid #eee;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.05);
}

.data-box {
  .data-item {
    margin: 24rpx 0;
    align-items: center;

    ~.data-item {
      border-left: 1rpx solid #eee;
    }

    .data-value {
      line-height: 1;
      align-items: flex-end;
      margin-bottom: 10rpx;
      color: #333;
    }

    .value {
      font-weight: bold;
      font-size: 44rpx;
    }

    .unit {
      position: relative;
      left: 10rpx;
      bottom: 8rpx;
      font-size: 24rpx;
    }

    .trend {
      font-size: 22rpx;
      color: #999;
    }
  }
}




.plate-box {
  &.info_box {

    // position: relative;
    // padding-top: 80rpx;
    .plate-title {
      height: 80rpx;
      padding: 28rpx 48rpx 72rpx;
      // padding-left: 48rpx;
      // padding-right: 48rpx;
      position: sticky;
      top: 0;
      // left: 0;
      // right: 0;
      background-color: #fff;
      z-index: 9;
      display: block;

      &.filter {
        top: 0;
      }
    }

    .info_list_zixun {
      padding: 0 48rpx;
    }

    .info_list {
      padding: 0 48rpx;

      &.new {
        column-count: 2;
        column-gap: 18rpx;
        display: block;
        padding-top: 10rpx;
      }


      .info_item {
        display: inline-block;
        // float: left;
        // display: block;
        width: 100%;
        // margin-right: 20rpx;
        margin-bottom: 24rpx;
        // &:nth-child(2n){
        //   margin-right: 0;
        // }
      }
    }
  }
}

.plate-box {
  margin-bottom: 30rpx;
  background-color: #ffffff;

  &.mgb0 {
    margin-bottom: 0;
  }

  .plate-title {
    height: 52rpx;
    line-height: 1;
    justify-content: space-between;
    align-items: flex-end;

    // margin-bottom: 20rpx;
    &.flex-item-center {
      align-items: center;
    }

    .cate {
      align-items: flex-end;
      max-width: 100%;
    }

    text {
      color: #333;
      transition: 0.2s;
      padding-right: 20rpx;

      &.active {
        font-weight: bold;
        font-size: 40rpx;
      }

      &~text {
        padding-left: 20rpx;
      }
    }
  }

  .bottom-more {
    padding-top: 20rpx;
    text-align: center;
    color: #999999;
  }
}


.list-info .info-title {
  line-height: 50upx;
}

.more {
  font-size: 26upx;
  line-height: 40upx;
  color: #666666;
}

.banner-box {
  margin-bottom: 20rpx;

  .banner-item {
    border-radius: 8rpx;
    overflow: hidden;

    ~.banner-item {
      margin-top: 10upx;
    }
  }
}

.adviser-swiper {
  margin-top: 48rpx;
  height: 294rpx;

  .swiper-item {
    align-items: center;
    height: 100%;
    padding: 16rpx 20rpx;
    border: 1px solid #eee;
    box-shadow: 0 0 16rpx 0 rgba(0, 0, 0, 0.05);
    border-radius: 8rpx;
    margin-right: 30rpx;

    &.first {
      background-image: url(https://images.tengfangyun.com/images/new_icon/bg_touxiang%403x.png);
      background-size: 136rpx;
      background-repeat: no-repeat;
      background-position: 48rpx 10rpx;
    }

    .avatar-box {
      margin-bottom: 30rpx;
      height: 90upx;
      // overflow: hidden;
      width: 90upx;
      display: block;
      position: relative;

      .img-box {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        overflow: hidden;
        background-color: #dedede;
      }

      .header_img {
        position: absolute;
        width: 100%;
        top: 0;
      }

      .brand {
        width: 120rpx;
        height: 48rpx;
        position: absolute;
        left: -16rpx;
        bottom: -24rpx;
      }
    }

    .info {
      flex: 1;
      line-height: 1;
      text-align: center;
      width: 100%;
      overflow: hidden;

      .name {
        display: block;
        font-size: 32rpx;
        margin-bottom: 15rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .build_name {
        flex-direction: row;
        align-items: center;
        font-size: 22rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #999999;

        .level_icon {
          width: 30rpx;
          height: 30rpx;
          margin-right: 10rpx;
        }

        >text {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .btn_box {
      justify-content: center;
      align-items: center;
      font-size: 22rpx;
      color: #fff;
      height: 46rpx;
      border-radius: 23rpx;
      padding: 0 18rpx;
      background: linear-gradient(90deg, #FB656A 30%, #FBAC65 100%);

      >.zx {
        margin-left: 10rpx;
      }
    }
  }
}

.new_house-list {
  flex-wrap: wrap;
  justify-content: space-between;
}

.new_house-square {
  width: 207rpx;
  line-height: 1;
  margin-top: 40rpx;
  display: block;

  .build_name {
    display: block;
    font-size: 30rpx;
    margin-top: 16rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.img-box {
  width: 204rpx;
  height: 172rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;

  .img {
    width: 100%;
    height: 100%;
  }

  .has_help_hb {
    height: 34rpx;
    width: 66rpx;
    position: absolute;
    top: 5rpx;
    left: 5rpx;
  }

  .level-box {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;

    .level {
      line-height: 1;
      display: block;
      margin-bottom: 8rpx;
      padding: 4rpx 10rpx;
      font-size: 22rpx;
      border-bottom-left-radius: 20rpx;
      color: #fff;

      &.level1 {
        background: linear-gradient(135deg, #69D4BB 0%, #00CAA7 100%);
      }

      &.level2 {
        background: linear-gradient(132deg, #F7918F 0%, #FB656A 100%);
      }
    }
  }

  .video-icon {
    width: 40rpx;
    height: 40rpx;
    position: absolute;
    right: 12rpx;
    bottom: 12rpx;
    margin: auto;
  }

  .area_name {
    position: absolute;
    left: 12rpx;
    bottom: 12rpx;
    font-size: 22rpx;
    color: #fff;
  }
}

.hui-row {
  box-sizing: border-box;
  display: inline-block;
  height: 32rpx;
  // padding: 0 6rpx;
  line-height: 30rpx;
  margin-top: 16rpx;
  font-size: 22rpx;
  border: 1rpx solid $uni-color-primary;
  color: $uni-color-primary;
  max-width: 100%;
  // background-image: linear-gradient(132deg, #FFA242 0%, #FE7430 100%);;
  border-radius: 4rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  .hui {
    height: 100%;
    padding: 0 6rpx;
    display: inline-block;
    background-color: $uni-color-primary;
    color: #fff;
  }

  .text {
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
    padding: 0 10rpx;
  }
}

.tuan-row {
  box-sizing: border-box;
  display: inline-block;
  height: 32rpx;
  // padding: 0 6rpx;
  line-height: 30rpx;
  margin-top: 16rpx;
  font-size: 22rpx;
  border: 1rpx solid #00caa9;
  color: #00caa9;
  max-width: 100%;
  border-radius: 4rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  .tuan {
    height: 100%;
    padding: 0 6rpx;
    display: inline-block;
    background-color: #00caa9;
    color: #fff;
  }

  .text {
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
    padding: 0 10rpx;
  }
}

.more_bottom_btn {
  padding: 24rpx;
  font-size: 32rpx;
  margin-top: 20rpx;
  background-color: rgba($uni-color-primary, 0.1);
  color: $uni-color-primary;
  text-align: center;

  &.mg {
    margin: 0 48rpx;
  }
}

.nomore {
  padding: 20rpx;
  flex-direction: row;
  align-items: center;
  justify-content: center;

  .line {
    height: 4rpx;
    width: 80rpx;
    margin: 0 20rpx;
    background-color: #d8d8d8;
  }

  text {
    color: #999999;
  }
}

.load_status {
  padding: 24rpx;
  font-size: 28rpx;
  color: #999;

  &.mgb_32 {
    margin-bottom: 32rpx;
  }
}

// 右侧红包
.right_hb {
  width: 160rpx;
  position: fixed;
  right: -180rpx;
  bottom: 200rpx;
  z-index: 9;
  transition: 0.26s;

  &.show {
    right: 0;
  }

  .close {
    padding: 6rpx;
    position: absolute;
    right: 0;
    z-index: 998;
  }

  image {
    width: 100%;
    height: 0;
  }
}

// 弹窗图片
.img_box {
  width: 80%;
  margin: auto;
  height: 90%;
  top: 5%;
  position: relative;

  .img {
    max-width: 100%;
    position: relative;
    margin: auto;

    .time_down {
      padding: 5rpx 10rpx;
      border: 1rpx solid #fff;
      border-radius: 4rpx;
      color: #fff;
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      z-index: 2;
      font-size: 26rpx;
    }

    .label {
      padding: 5rpx 16rpx;
      font-size: 24rpx;
      color: #fff;
      border-radius: 4rpx;
      background-color: rgba(0, 0, 0, 0.5);
      position: absolute;
      bottom: 10rpx;
      left: 10rpx;
      z-index: 2
    }
  }

  image {
    max-width: 100%;
    margin: auto;
  }
}

.servicer {
  color: #999;
  text-align: center;
  margin-bottom: 48rpx;

  .site_name {
    font-size: 24rpx;
    margin-bottom: 16rpx;
  }

  .service_tel {
    font-size: 28rpx;
  }
}

.plate-box {
  .lsb {
    align-items: center;
    height: 64rpx;
    padding: 32rpx;
    border-radius: 32rpx;
    background-image: linear-gradient(to right, #ff5700, #ff9e02);
    box-shadow: 0 8rpx 10px 0 rgba(255, 97, 0, 0.5);
    color: #fff;

    >text {
      padding-right: 16rpx;
    }

    .text {
      padding: 0;
      color: #fff;
    }
  }
}

.model {
  padding: 0 48rpx 24rpx;
  flex-wrap: wrap;

  .model_icon {
    width: 48%;
    height: 120rpx;
    background: #FFFFFF;
    // border: 2rpx solid #D8D8D8;
    // box-shadow: 0 0 4px 0 rgba(0,0,0,0.04);
    border-radius: 4px;
    overflow: hidden;
    margin-right: 4%;
    margin-bottom: 24rpx;

    &:nth-child(2n) {
      margin-right: 0;
    }

    image {
      width: 100%;
      height: 100%;

    }
  }

  .model_style {
    align-items: center;
    flex-wrap: wrap;

    width: 100%;

    &_item {
      width: 48%;
      padding: 20rpx 10rpx;
      // border: 2rpx solid #f3f3f3;
      border-radius: 10rpx;
      margin-right: 4%;
      margin-bottom: 24rpx;
      position: relative;
      background-repeat: no-repeat;
      background-position: right bottom;

      &:nth-child(2n) {
        margin-right: 0;
      }

      &_top {
        align-items: center;

        .line {
          height: 32rpx;
          width: 8rpx;
          margin-top: 4rpx;
          margin-right: 10rpx;

        }

        .title {
          font-size: 32rpx;
          // color: #333;
          // font-weight: 700;
          display: inline-block;
          max-width: 90%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      &_bottom {
        font-size: 24rpx;
        margin-top: 4rpx;
        color: #999;
        display: inline-block;
        max-width: 90%;
        z-index: 2;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .bg_img {
        width: 96rpx;
        position: absolute;
        ;
        // height: 50rpx;
        height: 96rpx;
        justify-content: flex-end;
        right: 0;
        bottom: 12rpx;

        image {
          width: 100%;
        }
      }

    }
  }

}

.super_build {
  .super_build_name {
    font-size: 40rpx;
    color: #333333;
    font-weight: 600;
  }
}

.sup_build_swiper {
  margin-top: 40rpx;
  height: 380rpx;

  .swiper-item {
    padding: 0;
    padding-left: 12rpx;
    position: relative;
    margin-right: 24rpx;

    .hui-row,
    .tuan-row {
      position: absolute;
      top: 145rpx;
      left: 0;
      z-index: 1;
      width: 290rpx;
      max-width: 290rpx;
      margin: 0;
      border: 0;
      border-radius: 0;
      padding: 12rpx 0;
      height: auto;
      background-image: linear-gradient(132deg, #FFA242 0%, #FE7430 100%);
      color: #fff;
    }

    .sanjiao {
      z-index: 0;
    }

    .sanjiao:after {
      position: absolute;
      width: 0;
      height: 0;
      left: -12rpx;
      top: 134rpx;
      content: " ";
      border-top: 12rpx solid transparent;
      border-right: 12rpx solid #F26016;
      border-left: 12rpx solid transparent;
      border-bottom: 12rpx solid transparent;
    }

    .build_img_box {
      width: 100%;
      height: 210rpx;
      border-radius: 12rpx;
      position: relative;
      overflow: hidden;

      image {
        width: 100%;
        height: 100%;
        min-height: 210rpx;
      }

    }

    .info {
      .name {
        margin-top: 10rpx;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 32rpx;

        // color: #333333;
        // font-weight: 600;
        text {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }

      .labels {
        margin: 10rpx 0;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;

        .label {
          display: inline-block;
          font-size: 22rpx;
          padding: 4rpx 6rpx;
          margin-right: 4rpx;
          color: #999;
          border: 1rpx solid #d8d8d8;
          border-radius: 4rpx;
        }
      }

      .build_name {

        align-items: center;
        font-size: 22rpx;
        color: #FB656A;

        .price {
          font-size: 32rpx;
          margin: 0 2rpx;
        }
      }

      .hui-row {
        margin-top: 8rpx;
      }

      .tuan-row {
        margin-top: 8rpx;
      }
    }
  }
}

.plate-box {
  &.sub_build {
    padding-right: 0;
    margin: 8rpx 0 24rpx;

    .sub_build_name {
      font-size: 40rpx;
      margin-bottom: 10rpx;
      font-weight: 600;
    }
  }

  &.super_build {
    padding-right: 0;
  }
}

.sub_swiper {
  height: 130rpx;

  // padding: 20rpx 0;
  .swiper-item {
    &.first {
      background: linear-gradient(90deg, #FBAC65 50%, #FB656A 100%);
      border: 0;

      .sub_top {
        color: #fff;
      }

      .info {
        .num {
          color: #fff;
        }
      }
    }

    background: #fff;
    padding: 16rpx 24rpx;
    margin-right: 24rpx;
    border:2rpx solid #f3f3f3;
    border-radius: 10rpx;
    height: 130rpx;

    .sub_top {
      font-size: 30rpx;
      font-weight: 600;
      justify-content: flex-start;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;

      .top_before {
        width: 10rpx;
        height: 100%;
        margin-right: 10rpx;
        background: #fb656a;
        min-width: 10rpx;
      }

      .top_name {
        font-size: 30rpx;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .build_title {
        width: 100%;
        overflow: hidden;
        font-size: 24rpx;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .info {
      margin-top: 18rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .num {
        font-size: 40rpx;
        font-weight: 700;
      }

      .icon {
        width: 48rpx;
        height: 48rpx;
        overflow: hidden;

        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

.page_top {
  // margin-top: 46rpx;
  display: flex;
  width: 700rpx;
  margin: 0 auto;
  justify-content: space-between;

  view {
    color: #141414;
    font-family: PingFang SC;
    font-weight: medium;
    font-size: 30rpx;
    line-height: normal;
  }
}

.uni-swiper-dot {
  display: none !important;
}

.page_center {
  display: flex;
}

.uni-video-cover {
  margin-right: 20rpx !important;
}

.page_center_one:nth-child(1) {
  margin-left: 30rpx;
}

.page_center_one {
  margin-right: 20rpx;
  width: 320rpx;
  margin-top: 20rpx;

  view:nth-child(1) {
    // width: 320rpx;
    height: 200rpx;
    // background-color: pink;
    border-radius: 10rpx;
  }

  view:nth-child(2) {
    margin-top: 20rpx;
    color: #141414;
    font-family: PingFang SC;
    font-weight: regular;
    font-size: 30rpx;
    line-height: normal;
    word-wrap: break-word;
    word-break: break-all;
  }

  view:nth-child(3) {
    margin-top: 14rpx;
    width: 192rpx;
    height: 40rpx;
    background-color: #fff1dd;
    display: flex;
    align-items: center;

    image {
      width: 24rpx;
      height: 28rpx;
      // margin-top: 6rpx;
      margin-left: 12rpx;
    }

    text {
      color: #FF8E09;
      color: #FF8E09;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 22rpx;
      line-height: normal;
      margin-left: 8rpx;
      // margin-top: 6rpx;
    }
  }
}

.content-one {
  height: 370rpx !important;
  padding-top: 30rpx;
}

.videoType1,
.videoType2,
.videoType3 {
  width: 60rpx;
  height: 60rpx;
  // background-color: red;
  z-index: 111;
  position: absolute;
  top: 50%;
  margin-top: -30rpx;
  left: 50%;
  margin-left: -40rpx;
  // background-image: url('../../static/icon/video.png');

}

.videoType1 {
  background-image: url('../../static/icon/video.png');
  background-size: 100% 100%;
}

.videoType2 {
  background-image: url('../../static/icon/hangpai.png');
  background-size: 100% 100%;
}

.videoType3 {
  background-image: url('../../static/icon/vr.png');
  background-size: 100% 100%;
}

.gengduo {
  font-size: 26rpx;
}

.video-title {
  margin-top: 10rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1 !important;
  /* 显示的行数 */
  -webkit-box-orient: vertical !important;
  text-overflow: ellipsis !important;
}

.guanggao {
  .adv-item {
    height: 100%;
    position: relative;

    &.rounded {
      border-radius: 20rpx;
      overflow: hidden;
    }

    .marker {
      line-height: 1;
      padding: 4rpx 10rpx;
      position: absolute;
      right: 12rpx;
      bottom: 10rpx;
      font-size: 20rpx;
      border-radius: 4rpx;
      background-color: rgba($color: #000000, $alpha: 0.5);
      color: #fff;
    }
  }

  .adv-item image {
    width: 100%;
    height: 100%;
  }

}

.guanggaob {
  .adv-item {
    height: 100%;
    position: relative;

    &.rounded {
      border-radius: 20rpx;
      overflow: hidden;
    }

    .marker {
      line-height: 1;
      padding: 4rpx 10rpx;
      position: absolute;
      right: 12rpx;
      bottom: 10rpx;
      font-size: 20rpx;
      border-radius: 4rpx;
      background-color: rgba($color: #000000, $alpha: 0.5);
      color: #fff;
    }
  }

  .adv-item image {
    width: 100%;
    height: 100%;
  }

}
</style>