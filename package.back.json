{"name": "tfy", "version": "0.1.0", "private": true, "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js"}, "dependencies": {"@dcloudio/uni-app-plus": "0.0.238", "@dcloudio/uni-h5": "^0.4.9", "@dcloudio/uni-mp-alipay": "0.0.810", "@dcloudio/uni-mp-baidu": "0.0.836", "@dcloudio/uni-mp-toutiao": "0.0.336", "@dcloudio/uni-mp-weixin": "0.0.958", "@dcloudio/uni-ui": "0.0.7", "countup.js": "^2.0.4", "flyio": "^0.6.14", "gaoyia-parse": "^1.0.9", "html2canvas": "^1.0.0-rc.3", "regenerator-runtime": "^0.12.1", "vconsole": "^3.3.2", "vue": "^2.6.10", "vue-countup-v2": "^4.0.0", "vuex": "^3.1.1", "weixin-js-sdk": "^1.4.0-test"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^0.2.994", "@dcloudio/uni-template-compiler": "^0.9.180", "@dcloudio/vue-cli-plugin-hbuilderx": "^0.9.531", "@dcloudio/vue-cli-plugin-uni": "^0.9.497", "@dcloudio/webpack-uni-mp-loader": "^0.3.639", "@dcloudio/webpack-uni-pages-loader": "^0.2.854", "@types/html5plus": "^1.0.0", "@types/uni-app": "^1.3.2", "@vue/cli-plugin-babel": "^3.9.2", "@vue/cli-service": "^3.9.3", "babel-plugin-import": "^1.12.0", "node-sass": "^4.12.0", "sass-loader": "^7.1.0", "ssh2": "^0.8.5", "ssh2-sftp-client": "^2.5.0", "vue-template-compiler": "^2.6.10", "weex-vue-loader": "^0.7.0"}, "overrideBrowserslist": ["last 3 versions", "Android >= 4.4", "ios >= 8"]}