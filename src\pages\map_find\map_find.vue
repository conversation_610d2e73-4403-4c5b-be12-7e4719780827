<template>

	<view class="find-map">
		<!-- #ifdef MP-WEIXIN || H5 -->
		<!-- <view class="nav-box">
			<view class="nav-item" :class="{'active':activeIndex==index}" v-for="(item,index) in tabs" :key="item.index" @click="switchCate(item,index)">{{item.name}}</view>
		</view> -->
		

		<template v-if="satisfyVersion>=0">
		<!-- 搜索 -->
		<cover-view class="search-title flex-box">
			<cover-view class="inp-box-def search-box flex-1 flex-box">
				<cover-view class="icon-box">
					<my-icon type="ic_sousuo" color="#999999" size="46rpx"></my-icon>
				</cover-view>
				<input type="text" v-model='params.keyword'  confirm-type="search" @confirm="getCovers"  placeholder="您想住哪里？" />
				<cover-view class='to_list flex-box ' @click="toList">
					<cover-view class ="list_img">
						<cover-image class ="list_img_con" :src="'/ditu/ditu_list.png' | imageFilter('m_80')"></cover-image>
					</cover-view>
					<text class = "list_name">列表</text>
				</cover-view>
			</cover-view>
		</cover-view>
		<view class="screen-tab flex-box">
			<view class="screen-tab-item flex-1 text-center" @click="switchTab(5)">
				<text>{{params.type | filterType}}</text>
				<uni-icons type="arrowdown" size="16"></uni-icons>
			</view>
			<view class="screen-tab-item flex-1 text-center" @click="switchTab(1)">
				<text>{{areaName}}</text>
				<uni-icons type="arrowdown" size="16"></uni-icons>
			</view>
			<view class="screen-tab-item flex-1 text-center" v-if="params.type===1" @click="switchTab(2)">
				<text>{{priceName}}</text>
				<uni-icons type="arrowdown" size="16"></uni-icons>
			</view>
			<view class="screen-tab-item flex-1 text-center" @click="switchTab(3)">
				<text>{{distanceName}}</text>
				<uni-icons type="arrowdown" size="16"></uni-icons>
			</view>
			<view class="screen-tab-item flex-1 text-center" v-if="params.type===1" @click="switchTab(4)">
				<text>更多</text>
				<uni-icons type="arrowdown" size="16"></uni-icons>
			</view>
		</view>
		<scroll-view scroll-y  class="screen-panel" :class="nowTab==1?'show':''" @touchmove.stop.prevent="stopMove">
				<view class="cascade_box flex-box">
					<view class="first cate">
						<view class="cascade_list">
							<view class="cascade_item" :class="{selected:selectted_area.length>0&&selectted_area[0]===item.areaid}" v-for="item in first_area" :key="item.areaid" @click="onSelectArea(1,item)">{{item.areaname}}</view>
						</view>
					</view>
					<view class="second cate">
						<view class="cascade_list">
							<view class="cascade_item" :class="{selected:selectted_area.length>1&&selectted_area[1]===item.areaid}" v-for="item in second_area" :key="item.areaid" @click="onSelectArea(2,item)">{{item.areaname}}</view>
						</view>
					</view>
					<view class="third cate">
						<view class="cascade_list">
							<view class="cascade_item" :class="{selected:selectted_area.length>2&&selectted_area[2]===item.areaid}" v-for="item in third_area" :key="item.areaid" @click="onSelectArea(3,item)">{{item.areaname}}</view>
						</view>
					</view>
				</view>
		</scroll-view>
		<scroll-view scroll-y  class="screen-panel" :class="nowTab==2?'show':''" @touchmove.stop.prevent="stopMove">
			<block v-for="(item,index) in price" :key="index">
				<uni-list-item :title="item.name" show-arrow="false" @click="selectPrice(item.value, item.name)"></uni-list-item>
			</block>
		</scroll-view>
		<scroll-view scroll-y  class="screen-panel" :class="nowTab==3?'show':''" @touchmove.stop.prevent="stopMove">
			<block v-for="(item,index) in distance" :key="index">
				<uni-list-item :title="item.name" show-arrow="false" @click="selectDistance(item.value, item.name)"></uni-list-item>
			</block>
		</scroll-view>
		<scroll-view scroll-y  class="screen-panel more-panel " :class="nowTab==4?'show':''" @touchmove.stop.prevent="stopMove">
			<view class="more-screen-item">
				<view class="title">类型</view>
				<view class="options flex-box">
					<view class="options-item" @click="selectOption({type_id:item.id},'type_id')" :class="params.type_id==item.id?'active':''" v-for="(item,index) in types" :key="index">{{item.typename}}</view>
				</view>
			</view>
			<view class="more-screen-item">
				<view class="title">销售状态</view>
				<view class="options flex-box">
					<view class="options-item" @click="selectOption({status:item.id},'status')" :class="params.status==item.id?'active':''" v-for="(item,index) in status" :key="index">{{item.leixing}}</view>
				</view>
			</view>
			<view class="more-screen-item">
				<view class="title">装修</view>
				<view class="options flex-box">
					<view class="options-item" @click="selectOption({renovation_id:item.id},'renovation_id')" :class="params.renovation_id==item.id?'active':''" v-for="(item,index) in renovation" :key="index">{{item.jiaofang}}</view>
				</view>
			</view>
			<view class="more-screen-item">
				<view class="title">状态</view>
				<view class="options flex-box">
					<view class="options-item" @click="selectOption({progress_id:item.id},'progress_id')" :class="params.progress_id==item.id?'active':''" v-for="(item,index) in progress" :key="index">{{item.jindu}}</view>
				</view>
			</view>
			<view class="flex-box padding-20">
				<button size="medium" type="default" @click="resetMore">重置</button>
				<button size="medium" type="primary" @click="selectMore()">确定</button>
			</view>
		</scroll-view>
		<scroll-view scroll-y  class="screen-panel" :class="nowTab==5?'show':''" @touchmove.stop.prevent="stopMove">
			<block v-for="(item,index) in tabs" :key="index">
				<uni-list-item :title="item.name" show-arrow="false" @click="selectType(item.type, item.name)"></uni-list-item>
			</block>
		</scroll-view>
		<view class="mask" :class="nowTab>0?'show':''" @click="nowTab=0" @touchmove.stop.prevent="stopMove"></view>
		</template>
		<!-- #endif -->


		<map id="map" :scale="mapData.scale" :latitude="params.lat" :longitude="params.lng" :markers="mapData.covers" @regionchange="regionChange" @markertap="handelMark($event)" @callouttap="handelMark($event)"></map>
			<cover-view class="list-item list-item_build" v-if="item.id">
				<cover-view class="loupan_info flex-box items-center"  @click="goTo()">
					<cover-view class="loupan_info_left flex-1">
						<cover-view class="loupan_title flex-box items-center">
							<cover-view class="loupan_title_con">
								{{item.title}}
							</cover-view>
							<cover-view class="loupan_title_type">
								{{params.type==1?"新房":'小区'}}
							</cover-view>
						</cover-view>
						<cover-view class="loupan_text flex-box items-center">
						<cover-view class="loupan_area" v-if='item.areaname'>
							{{item.areaname}}
						</cover-view>
						<cover-view class="loupan_area" v-if  ="params.type!=1">
						  {{params.type==2?'在售':'在租'}}	:{{item.count}}套
						</cover-view>
						<cover-view class="loupan_price flex-row" v-if ="params.type==1">
							<cover-view class="text price_label">{{item.price_type}}：</cover-view>
							<cover-view class="total text">{{item.build_price}}</cover-view>
							<cover-view class="acreage text">{{item.price_unit}}</cover-view>
						</cover-view>
						<cover-view class="loupan_price flex-row" v-else >
							<cover-view class="text price_label">均价：</cover-view>
							<cover-view class="total text">{{item.price || '暂无数据'}}</cover-view>
							<cover-view class="acreage text" v-if='item.price'>元/m²</cover-view>
						</cover-view>
						</cover-view>
					</cover-view>
					<cover-view class="loupan_info_right">
						<my-icon type="ic_into" color="#999999" size="46rpx"></my-icon>
					</cover-view>
				</cover-view>
				<cover-view class="loupan_other flex-box items-center">
					<cover-view class="loupan_other_item" v-for ="build in otherList" :key ="build.id"  @click ="toOther(build)">
						<cover-view class="loupan_other_item_img">
							<cover-image class ="loupan_other_item_img_icon" :src ="build.icon | imageFilter('m_320')"></cover-image>
						</cover-view>
						<cover-view class="loupan_other_item_name">
							{{build.text}}
						</cover-view>
					</cover-view>
				</cover-view>
			</cover-view>
			
			
		
	</view>
</template>

<script>
	import myIcon from '../../components/myIcon'
	import {uniIcons,uniListItem} from '@dcloudio/uni-ui'
	import {navigateTo,debounce,formatImg,isIos} from "../../common/index.js"
	import {wxShare} from '../../common/mixin'
	export default {
		data() {
			return {
				item:{},
				flag:false,
				tabs:[
					
				],
				activeIndex:0,
				nowTab:0,
				areaName:'区域',
				priceName:'价格',
				distanceName:'距离',
				typename:'类型',
				first_area:[],
				second_area:[],
				third_area:[],
				selectted_area:[''],
				area:[],
				price:[],
				distance:[],
				status:[],
				types:[],
				renovation:[],
				progress:[],
				params:{
					lat:"",
					lng:"",
					distance:3,
					type:'',
					keyword:''
				},
				mapData:{
					// #ifdef MP-BAIDU
					scale:15,
					// #endif
					// #ifndef MP-BAIDU
					scale:14,
					// #endif
					covers: []
				},
				prevTimeStamp:0,
				satisfyVersion:0,
				otherList:[
					{
          icon: '/ditu/fangchanfenxi.png',
          text: '房价分析',
					id:1,
					from:1,
          // path: '/propertyData/map/analysis?id={build_id}&type=1&lat={lat}&lng={lng}&from=1'
        },
        {
          icon: '/ditu/bankuaijiazhi.png',
          text: '版块价值',
					id:2,
					from:2,

        },
        {
          icon: '/ditu/cunliangfenxi.png',
          text: '存量分析',
					id:3,
					from:3,
         
        },
        {
          icon: '/ditu/jiaoyufenxi.png',
          text: '教育分析',
					id:4,
					from:4,
          
        },
        {

          icon: '/build/v_3/calculator.png',
          text: '配套分析',
					id:0,
					from:'',
          
        },
				]
			};
		},
		mixins:[wxShare],
		components:{
			uniIcons,
			uniListItem,
			myIcon
		},
		onLoad(options){
			if(options.type){
				this.params.type = parseInt(options.type)
			}
			this.map = uni.createMapContext("map",this);
			this.map.includePoints({
				padding:[20,20,20,20]
			})
			this.getLocation()
		},
		filters:{
			imgUrl(img){
				return formatImg(img,'w_320')
			},
			filterType(val){
				switch (Number(val)) {
					case 1:
						return '新房'
						
						break;
					case 2:
						return '二手房'
						
						break;
					case 3:
						return '租房'
						
						break;
				
					default:
						return  '类型'
						break;
				}
			}
		},
		methods:{
			getLocation(){
				this.$ajax.get('index/mappoint',{},res=>{
					if(res.data.code == 1){
						this.center_point = res.data.cfg_mappoint.split(",")
						this.params.lat = this.center_point[0]
						this.params.lng = this.center_point[1]
						if (res.data.housemaporder==2){
							this.tabs=[
								{type:2,name:"二手房"},
								{type:1,name:"新房"},
								{type:3,name:"出租房"}
							]
							
						}else {
							this.tabs=[
								{type:1,name:"新房"},
								{type:2,name:"二手房"},
								{type:3,name:"出租房"}
							]
						}
						if (!this.params.type){
							this.params.type=this.tabs[0].type
						}
						// this.activeIndex = this.tabs.findIndex(item=>item.type === this.params.type)
						this.getCovers()
						this.getScreen()
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:none
						})
					}
				})

				// this.$store.state.getPosition(()=>{
				// 	this.getCovers()
				// })
			},
			getScreen(){
				this.$ajax.get('build/buildCondition',{},(res)=>{
					res.data.area.push({areaid:'',parentid:0 ,mapx:'',mapy:'',areaname:"区域"})
					this.area_list =res.data.area
					this.creatArea(this.area_list)
					if(this.params.areaid){
						this.getParentId(this.area_list,parseInt(this.params.areaid),true)
					}
					this.price = res.data.price
					this.distance = res.data.distance
					this.status = res.data.status
					this.types = res.data.types
					this.renovation = res.data.renovation
					this.progress = res.data.progress
					this.showTab=true
				},(err)=>{
					console.log(err)
				})
			},
			creatArea(arr){
				this.first_area = arr.filter(item=>item.parentid===0)
			},
			/** 
			 * <AUTHOR> 
			 * @date 2020-06-22 10:11:34 
			 * @desc 选择区域事件处理 
			 */
			onSelectArea(i,e){
				let _this = this
				const getData = function(ev){
					if(ev.tx_mapx&&ev.tx_mapy){
						_this.params.lat = ev.tx_mapy
						_this.params.lng = ev.tx_mapx
					}else{
						_this.params.lat = _this.center_point[0]
						_this.params.lng = _this.center_point[1]
					}
					_this.params.areaid = ev.areaid
					_this.areaName = e.areaname
					_this.nowTab = 0
					_this.getCovers()
				}
				switch(i){
					case 1:
						this.selectted_area = [e.areaid]
						// 筛选出当前区域下的二级区域
						this.second_area = this.area_list.filter(item=>item.parentid===e.areaid)
						if(this.second_area.length===0){
							getData(e)
						}
						break;
					case 2:
						this.selectted_area[1] = e.areaid
						if(this.selectted_area.length>2){
							this.selectted_area.splice(2,1)
						}
						// 筛选出当前区域下的三级区域
						this.third_area = this.area_list.filter(item=>item.parentid===e.areaid)
						if(this.third_area.length===0){
							getData(e)
						}
						break;
					case 3:
						this.selectted_area[2] = e.areaid
						getData(e)
				}
			},
			getCovers(){
				// if(!this.params.lat&&!this.params.lng){
				// 	this.params.lat = this.$store.state.position.lat
				// 	this.params.lng = this.$store.state.position.lng
				// }
			
				// let center = [{
				// 		// #ifdef MP-BAIDU
				// 		markerId:0, // 百度小程序marker的id有bug，官方回应说可以先使用markerId过度
				// 		// #endif
				// 		// #ifndef MP-BAIDU
				// 		id: 0,
				// 		// #endif
				// 		latitude: this.params.lat,
				// 		longitude: this.params.lng,
				// 		width: 30,
				// 		height: 30,
				// 		// callout: {content:"中心位置",padding:10,borderRadius:4,bgColor:"#f65354",color:"#ffffff",display:'ALWAYS'},
				// 		iconPath: '/static/icon/center.png'
				// 	}]
				// this.mapData.covers = center
				let getCovers = ()=>{
					// this.map.getScale({
					// 	success:(res)=>{
					// 		this.mapData.scale = res.scale
					// 	}
					// })
					this.allow_region = false
					this.mapData.covers = []
					this.lists =[]
					this.item ={}
					this.$ajax.get("map/mapList.html",this.params,(res)=>{
						if(res.data.share){
							this.share = res.data.share
						}else{
							this.share = {}
						}
						if(!this.setShare){
							this.setShare = true
							this.getWxConfig()
						}
						uni.hideLoading()
						if(res.data.code == 0){
							uni.showToast({
								title:res.data.msg,
								icon:"none"
							})
							this.mapData.covers = []
							return
						}
						if(res.data.share){
							this.share = res.data.share
						}
						let icon = '/static/icon/none.png'
						if (res.data.list.length){
								this.lists = res.data.list
						}else {
							uni.showToast({
								title:'没有更多数据了',
								icon:'none'
							})
						}
						
						// res.data.list = res.data.list.slice(0,230)
						let covers = res.data.list.map((item,index)=>{
							let distance = parseInt(item._distance)
							let content,bgColor
							if(this.params.type===1){
								content = item.title+'\n'+item.price_type+':'+item.build_price+item.price_unit
							}else if(this.params.type===3){
								content = item.title+((item.price&&item.price!="0")?'\n'+item.price+'元/月':'')
							}else{
								content = item.title+((item.price&&item.price!="0")?'\n'+item.price+'元/m²':'')
							}
							switch (item.leixing){ //1: 待售；2: 在售；3:尾盘；4:售完
								case 1:
									bgColor = "#17bfff"
									break
								case 2:
									bgColor = "#70d298"
									break
								case 3:
									bgColor = "#ff7213"
									break
								case 4:
									bgColor = "#d3b03d"
									break
								default:
									bgColor = "#6478a6"
							}
							let ob = {
								id: index+1,
								width: 30,
								height: 30,
								leixing:item.leixing,
								iconPath: icon,
								latitude: item.lat,
								longitude: item.lng,
								// label: {content:content,padding:10,borderRadius:8,bgColor:bgColor,color:"#ffffff",display:'ALWAYS',textAlign:"center"},
								callout: {content:content,padding:10,borderRadius:8,bgColor:bgColor,color:"#ffffff",display:'ALWAYS',textAlign:"center"},
								distance: distance
							}
							return ob
						})
						this.prevTimeStamp = 0
						this.mapData.covers = covers
						if (this.params.keyword &&this.lists.length) {
							this.item= 	this.lists[0]
							// this.map.moveToLocation({
							// 	latitude: parseFloat(this.item.lat)-0.0015,
							// 	longitude: parseFloat(this.item.lng)
							// })
							// setTimeout(() => {
							// 	this.params.keyword=''
							// }, 300);
						}
						if(!this.prevTimeStamp){
							setTimeout(()=>{
								this.prevTimeStamp = 1
							},800)
						}
					},err=>{

					})
				}
				getCovers()
			},
			toList(){
				switch (Number(this.params.type)) {
					case 1:
						this.$navigateTo("/pages/new_house/new_house")
						break;
					case 2:
						this.$navigateTo("/pages/ershou/ershou")
						break;
					case 3:
						this.$navigateTo("/pages/renting/renting")
						break;
				
					default:
						this.$navigateTo("/pages/new_house/new_house")
						break;
				}
			},
			
			
			// switchCate(e,index){
			// 	if(this.activeIndex!==index){
			// 		this.resetMore()
			// 		this.priceName = "价格"
			// 		this.params.price = ""
			// 		this.nowTab = 0
			// 	}
			// 	this.activeIndex = index
			// 	this.item = {}
			// 	this.params.type = this.tabs[index].type
			// 	if(this.params.type===1){
			// 		this.params.distance = 3
			// 	}else{
			// 		this.params.distance = 2
			// 	}
			// 	this.prevTimeStamp = 0
			// 	this.getLocation()
			// },

			switchTab(index){
				if(this.nowTab == index){
					this.nowTab = 0
				}else{
					this.nowTab = index
				}
			},
			selectArea(id, name){
				this.params.areaid = id
				this.areaName = name
				this.nowTab = 0
				this.getCovers()
			},
			selectPrice(value, name){
				this.params.price = value
				this.priceName = name
				this.nowTab = 0
				this.getCovers()
			},
			selectDistance(id, name){
				this.params.distance = id/1000
				this.distanceName = name
				this.nowTab = 0
				this.getCovers()
			},
			selectType(id, name){
				if (this.params.type == id ) {
						this.nowTab = 0 
						return 
				}
				this.item ={}
				this.setShare=false
				this.params.type = id
				this.typename = name
				this.nowTab = 0
				setTimeout(() => {
					this.getCovers()
				}, 2);
				
			},
			selectOption(obj,type){
				switch(type)
				{
				case "type_id":
					if(this.params.type_id == obj.type_id){
						obj.type_id = ""
					}
				case "renovation_id":
					if(this.params.renovation_id == obj.renovation_id){
						obj.renovation_id = ""
					}
				case "status":
					if(this.params.status == obj.status){
						obj.status = ""
					}
				case "progress_id":
					if(this.params.progress_id == obj.progress_id){
						obj.progress_id = ""
					}
				}
				this.params = Object.assign({},this.params,obj)
			},
			selectMore(){
				this.nowTab = 0
				this.getCovers()
			},
			resetMore(){
				this.params.type_id = ""
				this.params.renovation_id = ""
				this.params.progress_id = ""
				this.params.status=""
			},
			
			regionChange(e){	
				console.log(e)
				// #ifdef MP-WEIXIN
				if(e.causedBy === "gesture"){
					console.log("非手动拖动")
					return
				}
				// #endif
// 				if(e.causedBy=="scale"){ //缩放导致的视野变化
// 					this.map.getScale({
// 						success:(res)=>{
// 							this.getDistance(res.scale)
// 						}
// 					})
// 				}
				if(e.type=="end"){
					uni.showLoading({
						title:"正在获取房源",
						mask:true
					})
					// 获取中心点经纬度
					this.map.getCenterLocation({
						success:(res)=>{
							this.params.lat = res.latitude
							this.params.lng = res.longitude
							debounce(this.getCovers,200)()
						}
					})
				}
			},
			getDistance(scale){ //废弃不用
// 				let distance
// 				switch(scale)
// 				{
// 				case 5:
// 					distance = 8000
// 					break
// 				case 6:
// 					distance = 4000
// 					break
// 				case 7:
// 					distance = 1500
// 					break
// 				case 8:
// 					distance = 700
// 					break
// 				case 9:
// 					distance = 160
// 					break
// 				case 10:
// 					distance = 120
// 					break
// 				case 11:
// 					distance = 60
// 					break
// 				case 12:
// 					distance = 25
// 					break
//				case 13:
// 					distance = 10
// 					break
//				case 14:
// 					distance = 5
// 					break
//				case 15:
// 					distance = 3
// 					break
// 				default:
// 					distance = 13
// 				}
				this.params.distance = distance
				if(scale!==this.mapData.scale){
					this.getLocation()
				}
			},
			handelMark(e){
				if(!e.detail.markerId){
					return
				}
				this.item = this.lists[e.detail.markerId-1]
			},
			goTo(){
				if(!this.item.id){
					return
				}
				if(this.params.type==1){
					navigateTo('/pages/new_house/detail?id='+this.item.id)
				}else{
					navigateTo('/pages/house_price/detail?id='+this.item.id)
				}
			},
			stopMove(){

			},
			toOther(build){
				// id={build_id}&type=1&lat={lat}&lng={lng}&from=1
				let path =''
					let type = this.params.type
					if (this.params.type!=1) {
						type =3
					}
					if (build.from) {
						path = '/propertyData/map/analysis'
						path+=`?id=${this.item.id}&type=${type}&lat=${this.item.lat}&lng=${this.item.lng}&from=${build.from}`
					}else {
						path = `/propertyData/map/map?id=${this.item.id}&type=${type}&lat=${this.item.lat}&lng=${this.item.lng}`
					}
					
				this.$navigateTo(path)
			}
		}
	}
</script>
<style lang="scss">

.cascade_box{
	align-items: flex-start;
	justify-content: space-between;
	.cate{
		flex: 1;
		overflow: hidden;
	}
	.cascade_item{
		padding: 26rpx 32rpx;
		text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		&.selected{
			color: $uni-color-primary;
		}
	}
}

.nav-box{
	width: 100%;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: #fff;
	// border-bottom: 1rpx solid $uni-border-color;
	// box-shadow: 0 4rpx 10rpx #e6e6e6;
	position: fixed;
	/* #ifdef H5 */
	top: 44px;
	/* #endif */
	/* #ifndef H5 */
	top: var(--window-top);
	/* #endif */
	z-index: 99;
	.nav-item{
		height: 100%;
		box-sizing: border-box;
		padding: 20rpx;
		width: 27.333%;
		text-align: center;
		position: relative;
		&.active:before{
			position: absolute;
			content: "";
			height: 4upx;
			max-width:18vw;
			left:0;
			right:0;
			margin:auto;
			bottom:0;
			background-color: $uni-color-primary
		}
	}
}

.e-text{
	display: inline;
}

.screen-tab{
	/* #ifdef H5 */
	top: 44px;
	/* #endif */
	/* #ifndef H5 */
	top: var(--window-top);
	/* #endif */
	margin-top: 80upx;
	z-index: 98;
}

.screen-panel {
	/* #ifdef H5 */
	top: 44px;
	/* #endif */
	/* #ifndef H5 */
	top: var(--window-top);
	/* #endif */
	margin-top: 160upx;
	background-color: #fff;
	position: fixed;
	width: 100%;
	max-height: 50vh;
	overflow-x: hidden;
	background-color: #fff;
	z-index: 97;
	-webkit-transform: translateY(-100%);
	transform: translateY(-100%);
	-webkit-transition: .2s;
	transition: .2s;
	&.show{
		transform: translateY(0);
	}
	.filter_item{
		padding: 26rpx 32rpx;
		border-bottom: 1rpx solid #f3f3f3;
	}
}

	.find-map map{
		position: absolute;	
		 width: 100%;
		
		top: 80rpx;
		bottom: 0;
		height:auto;
	}
	.find-map map.my-map{
		bottom: 240upx;
	}
	.find-map .grid-box{
		position: absolute;
		width: 100%;
		bottom: 0;
	}
	.find-map .list-item{
		position: fixed;
		bottom: 0;
		width: 100%;
		box-sizing: border-box;
	}
	.find-map .total{
		margin-right: 0;
		font-size: 22rpx;
		float: initial
	}
	.find-map .acreage{
		margin-left: 10upx;
		font-size: 22rpx;
		margin-right: 0;
	}
	.find-map .up{
		font-size: 22rpx;
		color: #f65354;
	}
	.find-map .down{
		font-size: 22upx;
		color: #179B16;
	}
	.find-map .text{
		display: inline;
	}
	.find-map .price_label{
		font-size: 22upx;
		// color: #666
	}
	.find-map .address-row{
		margin-top: 10upx;
		display: flex;
		justify-content: space-between;
		font-size: $uni-font-size-sm;
		color: $uni-text-color-grey
	}
	.find-map .address-row .address{
		margin-right: 20upx;
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-size: $uni-font-size-sm;
	}
	.find-map .address-row .distance{
		font-size: $uni-font-size-sm;
	}
	.up_down{
		width: 18rpx;
		height: 18rpx;
	}

.search-title {
  width: 100%;
  padding: 15upx 48rpx;
  box-sizing: border-box;
	position: fixed;
	/* #ifdef H5 */
	top: 44px;
	/* #endif */
	/* #ifndef H5 */
	top: var(--window-top);
	/* #endif */
	background: #fff;
  flex: 1;
	z-index: 99;
}

.search-box {
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.search-box .icon-box {
  width: 60upx;
  margin-left: 28upx;
  /* #ifndef H5 */
  margin-top: 7upx;
  /* #endif */
  position: absolute;
  z-index: 1;
}

.search-box input {
  height: 64upx;
  // padding: 6upx 10upx;
  padding-left: 90upx;
  box-sizing: border-box;
  font-size: $uni-font-size-sm;
  border-radius: 8rpx;
  // border: 1upx solid $uni-border-color;
  flex: 2;
  background: #f5f5f5;
}
.find-map .list-item.list-item_build {
	padding: 48rpx;
}
.to_list {
	margin-left: 30rpx;
	align-items: center;
	.list_img {
		width: 32rpx;
		height: 32rpx;
		.list_img_con{
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	.list_name {
		font-size: 28rpx;
		color: #333333;
		margin-left: 8rpx;
	}
}
.items-center {
	align-items: center;
}
.loupan_info {
	width: 100%;
	line-height: 1;
	.loupan_info_left{
		.loupan_title {
			font-size: 40rpx;
			font-weight: 600;
			font-family:PingFangSC-Medium;
			color: #333333;
			.loupan_title_type{
				margin-left: 30rpx;
				padding: 2rpx 10rpx;
				font-size: 22rpx;
				background: rgba(251,101,106,0.15);
				border-radius: 4rpx;
				font-weight: normal;
				color: #FB656A;
			}
		}
		.loupan_text {
			margin-top: 16rpx;
			font-size: 28rpx;
			color: #999999;
			.loupan_area{
				margin-right: 24rpx;
				font-size: 22rpx;
			}
			.loupan_price{
				.total{
					font-size: 22rpx;

				}
				.acreage{
					font-size: 22rpx;
					margin-left: 0;
					
				}
			}
		}
	}
}
.loupan_other {
	margin-top: 48rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.loupan_other_item {
		.loupan_other_item_img{
			width: 92rpx;
			height: 92rpx;
			overflow: hidden;
			.loupan_other_item_img_icon {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}
		.loupan_other_item_name {
			font-size: 22rpx;
			color: #000000;
		}
	}
}
</style>
