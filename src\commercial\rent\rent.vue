<template>
  <view class="page">
    <title-bar custom>
      <view class="search-box flex-box">
        <search @input="handelInput" @confirm="handelSearch" placeholder="请输入搜索内容"></search>
        <!-- <my-icon type="ic_sousuo" color="#999999" size="46rpx"></my-icon>
        <view class="inp">
          <text :class="{ has_key: params.keyword }">{{ params.keyword || '请在这里输入' }}</text>
        </view> -->
      </view>
      <view slot="right" class="seach_btn flex-box" @click.prevent.stop="$navigateTo('/commercial/add?parentid=2')">
        <my-icon type="ic_tianjia" color="#fb656a" size="46rpx"></my-icon>
        <text class="text">发布</text>
      </view>
    </title-bar>
    <!-- 广告位 -->
    <view class="swiper-container" v-if="adv.length > 0">
      <view class="swiper-con">
        <view class="swiper">
          <my-swiper
            :focus="adv"
            is_adv
            :autoplay="true"
            :interval="4000"
            :indicatorDots="adv.length > 1"
            :circular="true"
            indicatorActiveColor="#ffffff"
            height="140rpx"
          ></my-swiper>
        </view>
      </view>
    </view>
    <view class="screen-tab flex-box" id="tab_top">
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(1)">
        <text>{{ catesName }}</text>
        <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(2)">
        <text>{{ areaName }}</text>
        <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(3)">
        <text>{{ priceName }}</text>
        <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(4)">
        <text>{{ distanceName }}</text>
        <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(5)">
        <text>{{ typeName }}</text>
        <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <scroll-view scroll-y class="screen-panel" :class="nowTab == 1 ? 'show' : ''" @touchmove.stop.prevent="stopMove">
        <block v-for="(item, index) in cates" :key="index">
          <uni-list-item
            :title="item.title"
            show-arrow="false"
            @click="selectCates(item.id, item.title)"
          ></uni-list-item>
        </block>
      </scroll-view>
      <scroll-view
        scroll-y
        class="screen-panel"
        :class="nowTab == 2 ? 'show' : ''"
        @touchmove.stop.prevent="stopMove"
        v-if="showTab"
      >
        <addressd :addressd="area" ref="showArea" @changes="changeArea"></addressd>
      </scroll-view>
      <scroll-view scroll-y class="screen-panel" :class="nowTab == 3 ? 'show' : ''" @touchmove.stop.prevent="stopMove">
        <block v-for="(item, index) in price" :key="index">
          <uni-list-item
            :title="item.name"
            show-arrow="false"
            @click="selectPrice(item.value, item.name)"
          ></uni-list-item>
        </block>
      </scroll-view>
      <scroll-view scroll-y class="screen-panel" :class="nowTab == 4 ? 'show' : ''" @touchmove.stop.prevent="stopMove">
        <block v-for="(item, index) in distance" :key="index">
          <uni-list-item
            :title="item.name"
            show-arrow="false"
            @click="selectDistance(item.value, item.name)"
          ></uni-list-item>
        </block>
      </scroll-view>
      <scroll-view scroll-y class="screen-panel more-panel" :class="nowTab == 5 ? 'show' : ''">
        <view class="more-screen-item">
          <view class="title">面积(㎡)</view>
          <view class="options flex-box">
            <view
              class="options-item"
              @click="selectOption({ space: item.value }, 'space')"
              :class="params.space == item.value ? 'active' : ''"
              v-for="(item, index) in space"
              :key="index"
              >{{ item.name }}</view
            >
          </view>
        </view>
        <view class="more-screen-item">
          <view class="title">类型</view>
          <view class="options flex-box">
            <view
              class="options-item type-item"
              @click="selectOption({ type_id: item.id }, 'type_id')"
              :class="params.type_id == item.id ? 'active' : ''"
              v-for="(item, index) in types"
              :key="index"
              >{{ item.title }}</view
            >
          </view>
        </view>
        <view class="flex-box padding-20">
          <button size="medium" type="default" @click="resetMore">重置</button>
          <button size="medium" type="primary" @click="selectMore()">确定</button>
        </view>
      </scroll-view>
    </view>
    <view class="filter_list">
      <tab-bar :tabs="filter_list" :fixedTop="false" :showLine="false" :nowIndex="current_filter_index">
        <view
          class="filter_item"
          :class="{ active: index === current_filter_index }"
          :id="'i' + index"
          v-for="(item, index) in filter_list"
          :key="index"
          @click="onClickFilter(item, index)"
          >{{ item.name }}</view
        >
      </tab-bar>
    </view>
    <view class="house_list">
      <block v-for="item in listsData" :key="item.id">
        <list-item :item-data="item" type="rent" @click="toDetail"></list-item>
      </block>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
    <view
      class="mask"
      :class="nowTab > 0 ? 'show' : ''"
      @click="
        () => {
          nowTab = 0
          showPannel = false
        }
      "
      @touchmove.stop.prevent="stopMove"
    ></view>
  </view>
</template>

<script>
import titleBar from '../../components/titleBar.vue'
import myIcon from '../../components/myIcon'
import addressd from '../../components/jm-address/jm-address'
import { uniLoadMore, uniList, uniListItem } from '@dcloudio/uni-ui'
import tabBar from '../../components/tabBar.vue'
import listItem from '../components/listItem.vue'
import mySwiper from '@/components/mySwiper'
import search from "../../components/search.vue"
export default {
  components: {
    titleBar,
    myIcon,
    addressd,
    uniLoadMore,
    uniList,
    uniListItem,
    tabBar,
    listItem,
    mySwiper,
    search
  },
  data() {
    return {
      get_status: 'loading',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了',
      },
      params: {
        parentid: 2,
        catid: 1,
        price: '',
        distance: '',
        areaid: '',
        space: '',
        keyword: '',
        label: '',
        page: 1,
        rows: 20,
      },
      showTab: false,
      nowTab: 0,
      area: [],
      typeName: '更多',
      cates: [],
      adv: [],
      distance: [],
      space: [],
      types: [],
      filter_list: [],
      listsData: [],
      price: '',
      scrollTop: 0,
      scrollTopOffset: 0,
      showPannel: false,
      current_filter_index: 0,
    }
  },
  computed: {
    catesName() {
      let name = '商铺'
      if (this.params.catid && this.cates.length > 0) {
        let res = this.cates.find((item) => {
          return item.id == this.params.catid
        })
        if (res && res.title) {
          name = res.title
        }
      }
      return name
    },
    distanceName() {
      let name = '距离'
      if (this.params.distance && this.distance.length > 0) {
        let res = this.distance.find((item) => {
          return item.value == this.params.distance
        })
        if (res && res.name) {
          name = res.name
        }
      }
      return name
    },
    priceName() {
      let name = '价格'
      if (this.params.price && this.price.length > 0) {
        let res = this.price.find((item) => {
          return item.value == this.params.price
        })
        if (res && res.name) {
          name = res.name
        }
      }
      return name
    },
    areaName() {
      let name = '区域'
      if (this.params.areaid && this.area_list.length > 0) {
        let res = this.area_list.find((item) => {
          return item.areaid == this.params.areaid
        })
        if (res && res.areaname) {
          name = res.areaname
        }
      }
      return name
    },
    status_top() {
      return this.$store.state.systemInfo.statusBarHeight
    },
  },
  onLoad(options) {
    for (let key in options) {
      this.params[key] = options[key]
    }
    this.showTab = true
    this.getScreen()
    this.getData()
  },
  onShow() {
    setTimeout(() => {
      if (this.$store.state.updatePageData) {
        this.$store.state.updatePageData = false
        this.getLocation()
      }
    }, 150)
  },
  onUnload() {
    if (!uni.getStorageSync('no_watch_search_key')) {
      uni.$off('handleSearch')
    } else {
      uni.removeStorageSync('no_watch_search_key')
    }
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop
    const query = uni.createSelectorQuery().in(this)
    if (this.showPannel) {
      uni.pageScrollTo({
        scrollTop: this.scrollTopOffset,
        duration: 0,
      })
    }
  },
  methods: {
    getScreen() {
      this.$ajax.get(
        'estate/conditions',
        { catid: this.params.catid, parentid: this.params.parentid },
        (res) => {
          res.data.area.push({ areaid: '', parentid: 0, mapx: '', mapy: '', areaname: '全部' })
          let area = res.data.area
          this.area_list = res.data.area
          this.area = this.getJiedao(area, 'areaid', 'parentid', 'city')
          this.price = res.data.price
          this.cates = res.data.cates
          this.distance = res.data.distance
          this.space = res.data.space
          this.types = res.data.types
          let label = [
            {
              name: '全部',
              type: 'all',
              id: 0,
            },
            {
              name: '精选',
              type: 'info_level',
              id: 2,
            },
            {
              name: '经纪人',
              type: 'zhongjie',
              id: 2,
            },
          ]
          if (res.data.is_show_personal_tab==1){
						label=[
							{
								name:'全部',
								type:'all',
								id:0,
								optionId:0
							},
							{
								name:'精选',
								type:'info_level',
								id:2,
								optionId:3
							},
							{
								name:'个人',
								type:'zhongjie',
								id:1,
								optionId:1
							},
							{
								name:'经纪人',
								type:'zhongjie',
								id:2,
								optionId:2
							}
						]
					}
          let label2 = res.data.label.map((item) => {
            item.type = 'label'
            return item
          })
          this.filter_list = label.concat(label2)
          if (this.params.label) {
            this.$nextTick(() => {
              this.current_filter_index = this.filter_list.findIndex((item) => item.id == this.params.label)
            })
          }
          this.showTab = true
        },
        (err) => {
          console.log(err)
        }
      )
    },
    getData() {
      this.params.lat = this.$store.state.position.lat
      this.params.lng = this.$store.state.position.lng
      if (this.params.page == 1) {
        this.listsData = []
      }
      this.showPannel = false
      this.get_status = 'loading'
      this.$ajax.get('estate/index', this.params, (res) => {
        if (res.data.adv && res.data.adv.length > 0 && this.adv.length === 0) {
          this.adv = res.data.adv.map((item) => {
            item.url = item.wap_link
            return item
          })
        }
        if (res.data.code == 1) {
          this.listsData = this.listsData.concat(res.data.list)
          if (res.data.list.length < this.params.rows) {
            this.get_status = 'noMore'
          } else {
            this.get_status = 'more'
          }
          if (res.data.share) {
            this.share = res.data.share
          }
        } else {
          this.get_status = 'noMore'
        }
      })
    },
    selectCates(value) {
      this.current_filter_index = -1
      this.params.catid = value
      this.nowTab = 0
      this.params.page = 1
      this.params.type_id = ''
      this.getScreen()
      this.getData()
    },
    changeArea(e) {
      this.current_filter_index = -1
      this.params.areaid = e.district_id ? e.district_id : e.city_id ? e.city_id : e.province_id ? e.province_id : ''
      this.nowTab = 0
      this.params.page = 1
      this.getData()
    },
    selectPrice(value) {
      this.current_filter_index = -1
      this.params.price = value
      this.nowTab = 0
      this.params.page = 1
      this.getData()
    },
    selectDistance(value) {
      this.current_filter_index = -1
      this.params.distance = value
      this.nowTab = 0
      this.params.page = 1
      // #ifdef MP
      checkAuth('scope.userLocation', {
        authOk: () => {
          this.getLocation()
        },
        success: () => {
          this.getLocation()
        },
        fail: () => {
          this.show_dialog = true
        },
      })
      // #endif
      // #ifdef H5 || APP-PLUS
      this.getLocation()
      // #endif
    },
    getLocation() {
      this.$store.state.getPosition(() => {
        this.getData()
      })
    },
    getJiedao(a, idStr, pIdStr, chindrenStr) {
      var r = [],
        hash = {},
        id = idStr,
        pId = pIdStr,
        children = chindrenStr,
        i = 0,
        j = 0,
        len = a.length
      for (; i < len; i++) {
        a[i].label = a[i].name
        delete a[i].name
        hash[a[i][id]] = a[i]
      }
      for (; j < len; j++) {
        var aVal = a[j],
          hashVP = hash[aVal[pId]]

        if (hashVP) {
          !hashVP[children] && (hashVP[children] = [])

          hashVP[children].push(aVal)
        } else {
          r.push(aVal)
        }
      }
      return r
    },
    scroppTo(fun) {
      const query = uni.createSelectorQuery().in(this)
      query
        .select('#tab_top')
        .fields({ rect: true, scrollOffset: true }, (data) => {
          if (data) {
            if (data.top <= 44) {
              fun && fun()
              return
            }
            // #ifdef H5
            this.scrollTopOffset = (this.scrollTop || 0) + data.top - uni.upx2px(80)
            // #endif
            // #ifndef H5
            this.scrollTopOffset = (this.scrollTop || 0) + data.top - (44 + this.status_top) + uni.upx2px(100)
            // #endif
          }
          uni.pageScrollTo({
            duration: 120,
            // #ifdef H5
            scrollTop: this.scrollTopOffset,
            // #endif
            // #ifndef H5
            scrollTop: this.scrollTopOffset,

            // #endif
            success: () => {
              if (fun) {
                fun()
              }
            },
          })
        })
        .exec()
    },
    switchTab(index) {
      this.scroppTo(() => {
        // this.scrollTop =200
        let timeout = setTimeout(() => {
          if (this.nowTab == index) {
            this.nowTab = 0
            this.showPannel = false
            // this.$refs.ershou.allMove()
          } else {
            this.nowTab = index
            if (index == 2) {
              this.$refs.showArea.showAddress()
            }
            this.showPannel = true
            // this.$refs.ershou.stopMove()
          }
        }, 200)
      })
    },
    resetMore() {
      this.params.type_id = ''
      this.params.space = ''
    },
    selectMore() {
      this.current_filter_index = -1
      this.nowTab = 0
      this.params.page = 1
      this.getData()
    },
    selectOption(obj, type) {
      switch (type) {
        case 'type_id':
          if (this.params.type_id === obj.type_id) {
            obj.type_id = ''
          }
        case 'space':
          if (this.params.space === obj.space) {
            obj.space = ''
          }
      }
      this.current_filter_index = -1
      this.params = Object.assign({}, this.params, obj)
    },
    onClickFilter(item, index) {
      this.current_filter_index = index
      this.params.info_level = ''
      this.params.type = ''
      if (item.type === 'all') {
        this.typeName = '更多'
        this.resetMore()
        this.params.areaid = ''
        this.params.price = ''
        this.params.distance = ''
        this.params.label = ''
        this.params.lat = ''
        this.params.lng = ''
        this.params.page = 1
        this.getData()
        return
      }
      this.params.zhongjie = ''
      this.params.info_level = ''
      this.params.label = ''
      this.params[item.type] = item.id
      this.params.page = 1
      this.getData()
    },
    toDetail(e) {
      if (!e.detail.id) {
        return
      }
      this.$store.state.tempData = e.detail
      this.$navigateTo('/commercial/rent/detail?id=' + e.detail.id)
    },
    handelInput(e) {
      this.params.keyword = e.detail.value
    },
    handelSearch() {
      this.params.page = 1
      this.getData()
    },
  },
  onPullDownRefresh() {
    this.params.page = 1
    this.getData()
  },
  onReachBottom() {
    if (this.get_status == 'more') {
      this.params.page = this.params.page + 1
      this.getData()
    }
  },
}
</script>

<style lang="scss" scoped>
.page {
  background: #fff;
}
.search-box {
  // margin-left: 20rpx;
  align-items: center;
  // padding: 10rpx 20rpx;
  // background-color: #f5f5f5;
  // color: #999;
  // border-radius: 8rpx;
  .search-left {
    margin-right: 20rpx;
  }
  .inp {
    margin-left: 20rpx;
    .has_key {
      color: #333;
    }
  }
}
.seach_btn {
  align-items: center;
  padding: 0 24rpx;
  .text {
    margin-left: 16rpx;
  }
}
.swiper-container {
  background: #fff;
  .swiper-con {
    padding: 32rpx 48rpx 0;
  }
  .swiper {
    border-radius: 8rpx;
    overflow: hidden;
  }
}
/* #ifdef H5 */
.screen-tab {
  position: sticky;
  top: 44px;
  // margin-top: 44px;
  box-sizing: border-box;
  padding: 0 48rpx;
}
.screen-panel {
  top: 0;
  margin-top: 80rpx;
  display: none;
}
.screen-panel.show {
  top: 37px;
  left: 0;
  display: block;
}
/* #endif */
/* #ifndef H5 */
.screen-tab {
  position: sticky;
  top: var(--window-top);
  top: 43px;
  z-index: 99;
  box-sizing: border-box;
  padding: 0 48rpx;
}
.screen-panel {
  top: var(--window-top);
  left: 0;
  transform: translateY(-130%);
  margin-top: 162rpx;
  &.show{
    transform: translateY(0);
  }
}
/* #endif */
.filter_list {
  padding-left: 48rpx;
  padding-top: 24rpx;
  background-color: #fff;
  .filter_item {
    display: inline-block;
    padding: 10rpx 20rpx;
    border-radius: 4rpx;
    line-height: 1;
    box-sizing: border-box;
    margin-right: 24rpx;
    font-size: 24rpx;
    background-color: #f5f5f5;
    border: 1rpx solid #f5f5f5;
    color: #999;
    &.active {
      background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
      color: $uni-color-primary;
      border: 1rpx solid $uni-color-primary;
    }
  }
}
.house_list {
  min-height: 100vh;
  padding: 0 48rpx;
  background-color: #fff;
}
.more-screen-item .options .options-item {
  line-height: 1;
}
.more-screen-item .options .options-item.type-item {
  width: auto;
}
.search-title {
  padding: 7px 0;
   ::v-deep .uni-input-wrapper {
    text-align: left;
  }
}
</style>