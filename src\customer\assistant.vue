<template>
  <view>
    <view class="block">
      <view class="label">
        <text>生成专属海报</text>
        <text class="question" @click="showTip('海报')">?</text>
      </view>
      <view class="poster_list flex-box">
        <view class="poster_item" hover-class="navigator-hover" :hover-start-time="30" :hover-stay-time="100" v-for="(poster, index) in poster_list" :key="index" @click="toLink(poster)">
          <image :src="poster.icon | imageFilter('w_80')" mode="widthFix"></image>
          <view class="text">{{poster.name}}</view>
        </view>
      </view>
    </view>
    <view class="block">
      <view class="label">
        <text>专属获客链接</text>
        <text class="question" @click="showTip('链接')">?</text>
      </view>
      <view class="poster_list flex-box">
        <view class="poster_item" hover-class="navigator-hover" :hover-start-time="30" :hover-stay-time="100" v-for="(link, index) in link_list" :key="index" @click="toLink(link)">
          <image class="link_img" :src="link.icon | imageFilter('w_80')" mode="widthFix"></image>
          <view class="text">{{link.name}}</view>
        </view>
      </view>
    </view>
    <enturstBtn :to_user="user_info" @click="showTip('海报或链接')" />
    <view class="user_info flex-box top-line">
      <image class="avatar" :src="user_info.prelogo | imageFilter('w_80')" mode="widthFix"></image>
      <view class="user_name flex-1">
        <view class="uname">{{user_info.cname}}</view>
        <view class="type">{{user_info.adviser_id?'置业顾问':'经纪人'}}</view>
      </view>
      <view class="btn_group">
        <view class="btn" @click="showTip('海报或链接')">微聊</view>
        <view class="btn" @click="showTip('海报或链接')">电话咨询</view>
      </view>
    </view>
    <my-popup ref="tip_popup" position="center" height="396rpx">
      <view class="tip_container">
        <view class="title">生成专属{{title_tip}}</view>
        <view class="desc">生成专属二维码海报或分享链接，客户扫描后将访问带你的个人信息的专属获客页面，客户浏览追踪，委托找房，授权手机号，实时消息通知。</view>
        <view class="btn" @click="$refs.tip_popup.hide()">好的，知道了</view>
      </view>
    </my-popup>
    <my-popup ref="options_popup">
      <view class="options_box">
        <view class="label">请选择</view>
        <view class="search_box" v-if="current_option_id==3||current_option_id==11||current_option_id==12||current_option_id==17">
          <search @input="onInputKey" @confirm="onConfirmKey" :value="keywords" placeholder="请输入关键字检索"></search>
        </view>
        <scroll-view scroll-y class="options">
          <view class="options_item flex-box bottom-line" v-for="(item, index) in options_list" :key="index" @click="toShareLink(item)">
            <image :src="item.pic | imageFilter('w_240')" mode="aspectFill"></image>
            <view class="title">{{item.title}}</view>
          </view>
        </scroll-view>
      </view>
    </my-popup>
  </view>
</template>

<script>
import myPopup from '@/components/myPopup'
import enturstBtn from '@/components/enturstBtn'
import search from '@/components/search'
import copyText from '../common/utils/copy_text'
export default {
  components: {
    myPopup,
    enturstBtn,
    search
  },
  data () {
    return {
      poster_list: [],
      link_list: [],
      user_info: {},
      options_list: [],
      keywords: "",
      current_option_id: "",
      title_tip: "海报或链接"
    }
  },
  computed: {
  },
  onLoad(){
    this.getData()
  },
  methods: {
    showTip(text){
      this.title_tip = text
      this.$refs.tip_popup.show()
    },
    getData(){
      this.$ajax.get('share/shareMenu.html', {}, res=>{
        if(res.data.code === 1){
           res.data.data.poster.map(item=>{
            if (item.path.includes("wapi/poster")){
              item.path+='&header_from=2'
            }
           })
          this.poster_list = res.data.data.poster
          this.link_list = res.data.data.link
          if(res.data.data.adviser.id){
            this.user_info = res.data.data.adviser
            this.user_info.adviser_id = this.user_info.id
            this.user_info.share_type = 1
          }else{
            this.user_info = res.data.data.agent
            this.user_info.id = this.user_info.mid
            this.user_info.share_type = 2
          }
          this.getShortLink()
        }
      })
    },
    toLink(item){
      if(item.operation==1&&item.path){
        let path = item.path
        this.navigateTo(path)
      }
      if(item.operation==2){
        this.onClickOptions(item.id)
      }
      if(item.operation==3){
        let text = ''
        if(this.user_info.adviser_id){
          text = `我是${this.user_info.build_name}置业顾问-${this.user_info.cname} 欢迎向我咨询\n【链接】${this.link}`
        }else{
          text =  `我是${this.user_info.tname||''}${this.user_info.cname}，今天房源已经更新，马上点击查看，欢迎咨询！\n【链接】${this.link}`
        }
        copyText(text, ()=>{
          uni.showToast({
            title: "复制成功"
          })
        })
      }
    },
    getShortLink(){
      if(this.user_info.adviser_id){
        this.link = `${window.location.origin}/h5/pages/consultant/detail?id=${this.user_info.adviser_id}&shareId=${this.user_info.adviser_id}&shareType=1`
      }else{
        this.link = `${window.location.origin}/h5/pages/agent/detail?id=${this.user_info.mid}&shareId=${this.user_info.mid}&shareType=2`
      }
      this.$ajax.get('build/shortUrl.html', {page_url: this.link}, res=>{
        if(res.data.code === 1){
          this.link = res.data.short_url
        }
      })
    },
    onInputKey(e){
      this.keywords = e.detail.value
    },
    onConfirmKey(e){
      this.keywords = e.detail.value
      this.getOptionsList('search')
    },
    onClickOptions(id){
      this.current_option_id = id
      this.keywords = ''
      this.getOptionsList()
    },
    getOptionsList(type){
      uni.showLoading({
        title: '加载中...'
      })
      this.options_list = []
      this.$ajax.get('share/shareInfo.html', {id: this.current_option_id, keywords: this.keywords}, res=>{
        uni.hideLoading()
        if(res.data.code === 1){
          if(res.data.list.length===1&&!type){
            this.toShareLink(res.data.list[0])
            return
          }
          this.options_list = res.data.list
          this.$refs.options_popup.show()
        }else{
          uni.showToast({
            title: res.data.msg,
            icon:'none'
          })
        }
      })
    },
    toShareLink(item){
      this.navigateTo(item.url)
    },
    navigateTo(path){
      let httpArr = ['http','https']
      if(httpArr.includes(path.split('://')[0])){
        this.$navigateTo(path)
        return
      }
      if(path.includes('?')){
        path = `${path}&shareId=${this.user_info.id}&shareType=${this.user_info.share_type}&type=${this.user_info.share_type }`
      }else{
        path = `${path}?shareId=${this.user_info.id}&shareType=${this.user_info.share_type}&type=${this.user_info.share_type }`
      }
      this.$navigateTo(path)
    }
  }
}
</script>

<style scoped lang="scss">
page{
  padding-top: 48rpx;
  background-color: #fff;
}
.block{
  padding: 0 48rpx;
  .label{
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    .question{
      margin-left: 24rpx;
      font-size: 22rpx;
      display: inline-block;
      width: 32rpx;
      text-align: center;
      height: 32rpx;
      line-height: 32rpx;
      border-radius: 50%;
      font-weight: initial;
      background-color: #eee;
      color: #fff;
    }
  }
}
.poster_list{
  flex-wrap: wrap;
  .poster_item{
    box-sizing: border-box;
    padding: 8rpx;
    width: 19%;
    min-width: 20%;
    text-align: center;
    margin-bottom: 48rpx;
    border-radius: 4rpx;
    image{
      width: 100%;
      height: 0;
      &.link_img{
        width: 64rpx
      }
    }
    .text{
      font-size: 22rpx;
      color: #333;
    }
  }
}

.user_info{
  position: fixed;
  width: 100%;
  bottom: 0;
  padding: 24rpx 48rpx;
  box-sizing: border-box;
  align-items: center;
  background-color: #fff;
  .avatar{
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 16rpx;
  }
  .user_name{
    overflow: hidden;
    .uname{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 6rpx;
      font-size: 28rpx;
      color: #333;
    }
    .type{
      font-size: 24rpx;
      color: #999;
    }
  }
  .btn_group{
    display: flex;
    .btn{
      padding: 0 16rpx;
      margin-left: 24rpx;
      min-width: 128rpx;
      text-align: center;
      height: 64rpx;
      line-height: 64rpx;
      border: 1rpx solid $uni-color-primary;
      box-shadow: 0 4rpx 8rpx 0 rgba(251,101,106,0.10);
      border-radius: 8rpx;
      border-radius: 8rpx;
      color: $uni-color-primary;
    }
  }
}

.tip_container{
  width: 80%;
  height: 396rpx;
  box-sizing: border-box;
  padding: 24rpx 48rpx;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  border-radius: 16rpx;
  background-color: #fff;
  .title{
    text-align: center;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  .desc{
    font-size: 28rpx;
    color: #666;
  }
  .btn{
    padding: 24rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: $uni-color-primary;
  }
}
.options_box{
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  overflow: hidden;
  >.label{
    padding: 24rpx;
    text-align: center;
    font-size: 32rpx;
    background-color: #f8f8f8;
    color: #666;
  }
}
.options{
  padding: 0 48rpx;
  height: 50vh;
  overflow-x: hidden;
  box-sizing: border-box;
  .options_item{
    padding: 24rpx 0;
    image{
      width: 120rpx;
      height: 100rpx;
      margin-right: 16rpx;
      border-radius: 8rpx;
      background-color: #dedede;
    }
    .title{
      height: 92rpx;
      line-height: 1.5;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      font-size: 30rpx;
      color: #333;
    }
  }
}
</style>