<template>
	<view>
		<block v-for="(item, index) in listsData" :key="index">
			<list-item :item-data="item" type="info" @click="toDetail"></list-item>
		</block>
	</view>
</template>

<script>
	import listItem from '@/components/listItem.vue'
	import {navigateTo,config} from '@/common/index.js'
	export default {
		props:{
			listsData:Array,
			infoList:Array
		},
		components:{
			listItem
		},
		data() {
			return {
				
			}
		},
		methods:{
			toDetail(e){
				if (e.detail.url){
					// #ifdef H5
					location.href=e.detail.url
					// #endif
					// #ifdef MP
					let url =e.detail.url
                    let urlArr = e.detail.url.split("?")
                    let ids=[],url='';
                    if (urlArr[1]){
                        ids =urlArr[1].split("=")
                        url =urlArr[0]+"@@@"+ids[0]+"@@"+ids[1] 
                    }
                    uni.redirectTo({
                        url:'/pages/web_view/web_view?url='+encodeURIComponent(url)
                    })
					//#endif
					return 
				}
				if (!e.detail.id) return 
				navigateTo('/topic/detail?id='+e.detail.id)
				// this.$store.state.tempData = e.detail
				// if(e.detail.id){
				// 		if (e.detail.tp_url){
				// 			navigateTo('/pages/news/detail?id='+e.detail.tp_url)
				// 		}else {
				// 			// #ifdef H5
				// 			navigateTo('/topic/detail?id='+e.detail.id)
				// 			// #endif 
				// 			// #ifdef MP
				// 			let url =encodeURIComponent(config.apiDomain+'/m/topic/detail?id='+e.detail.id) 
				// 			navigateTo('/pages/web_view/web_view?url='+url)
				// 			// #endif
				// 		}					
				// 		return 
						

				// 	}else {  //动态 进列表	
				// 		// #ifdef H5
				// 		navigateTo(config.apiDomain+'/topic/infolist?id='+e.detail.id)
				// 		// #endif
				// 		// #ifdef MP
				// 		let url =encodeURIComponent(config.apiDomain+'/m/topic/infolist?id='+e.detail.id) 
				// 		navigateTo('/pages/web_view/web_view?url='+url)					
				// 		// #endif
				// 	}
				
				}
			}
		}
</script>

<style lang="scss">
	.bottom-line::after{
		left: $uni-spacing-row-base;
		right: $uni-spacing-row-base;
	}
	.ding{
		font-size:$uni-font-size-sm;
		border-radius: 6upx;
		margin-right: 10upx;
		padding: 1upx 8upx;
		color: #fff;
		background-color: #f40
	}

</style>
