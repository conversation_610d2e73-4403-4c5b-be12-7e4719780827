<template>
  <view class="group_container">
      <view class="header" :style="{backgroundImage: `url(${header_bg})`}">
        <text>购房交流群（{{detail.count}}）</text>
        <view class="icon_box" @click="$emit('close')">
          <my-icon type="ic_guanbi" color="#fff"></my-icon>
        </view>
      </view>
      <view class="form">
        <image class="header_img" v-for="(item, index) in group_headers" :key="index" mode="aspectFill" :src="item | imageFilter('w_80')"></image>
        <view class="scroll_box">
          <image class="notice" :src="'/build/v_3/notice.png' | imageFilter('m_320')" />
          <swiper class="swiper" vertical autoplay circular :interval="3000">
            <swiper-item class="swiper-item" v-for="(item, index) in scroll_list" :key="index">
              <text>{{item.descp}}</text>
            </swiper-item>
          </swiper>
        </view>
        <view class="group_info" v-if="apply_status">
          <view class="img_box">
            <image class="img" :src="detail.qrcode" />
            <!-- #ifdef H5 -->
            <view class="tip">长按识别进群</view>
            <!-- #endif -->
          </view>
          <view class="info">
            <view class="text">
              申请成功！复制客服微信号<text class="highlight">{{detail.number}}</text>，添加微信好友，立即拉你入群
            </view>
            <view class="btn_group">
              <!-- #ifndef H5 -->
              <view class="small_btn" @click="saveImg(detail.qrcode)">保存相册</view>
              <!-- #endif -->
              <view class="small_btn" @click="copyContent(detail.number)">复制微信号</view>
            </view>
          </view>
        </view>
        <view v-else>
          <input class="input" type="number" v-model="tel" maxlength="11" placeholder="请输入您的手机号码" />
           <view class="btn" @click="handleApply">立即申请</view>
        </view>
      </view>
    </view>
</template>

<script>
import { formatImg, config } from '../../../common/index.js'
import myIcon from '../../../components/myIcon.vue'
import copyText from '../../../common/utils/copy_text.js'
export default {
  name: 'GroupPop',
  components: {},
  data () {
    return {
      header_bg: "",
      apply_status: false,
      tel: ''
    }
  },
  components:{
    myIcon
  },
  props:{
    group_headers: Array,
    scroll_list: Array,
    detail: {
      type: Object,
      default: ()=>{}
    },
    bid: String,
  },
  created(){
    this.header_bg = formatImg('/build/v_3/group_pop_bg.png')
  },
  methods: {
    saveImg(src) {
      uni.getImageInfo({
        src: src,
        success: image => {
          uni.saveImageToPhotosAlbum({
            filePath: image.path,
            success: () => {
              uni.showToast({
                title: '保存成功'
              })
            }
          })
        }
      })
    },
    copyContent(content){
      copyText(content, ()=>{
        uni.showToast({
          title: '复制成功'
        })
      })
    },
    handleApply(){
      if(!this.tel){
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }
      var params = {
        from: '申请加群',
        bid: this.bid||'',
        tel: this.tel,
        sub_type: '',
        name: config.projectName+'网友'
      }
      // #ifdef H5
      var url = this.$route.fullPath
      // #endif
      // #ifndef H5
      var pages = getCurrentPages()
      var currentPage = pages[pages.length - 1] //获取当前页面的对象
      var url = currentPage.route //当前页面url
      var options = currentPage.options //当前页面url参数
      let i = 0
      url='/'+url;
      for(let key in options){
          if(i===0){
              url+=`?${key}=${options[key]}`
          }else{
              url+=`&${key}=${options[key]}`
          }
          i++
      }
      // #endif
      params.page_url = url
      this.$ajax.post('build/signUp.html', params, res => {
        if(res.data.code === 1){
          uni.showToast({
            title: '申请成功',
          })
          this.apply_status = true
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.group_container{
  width: 560rpx;
  height: 100%;
  margin: auto;
  border-radius: 24rpx;
  background-color: #fff;
  .header{
    flex-shrink: 0;
    box-sizing: border-box;
    padding: 24rpx;
    height: 260rpx;
    text-align: center;
    background-size: 100% 100%;
    position: relative;
    font-size: 28rpx;
    color: #fff;
    .icon_box{
      position: absolute;
      padding: 12rpx;
      top: 0;
      right: 0;
    }
  }
  .form{
    padding: 24rpx;
    .input{
      margin-top: 24rpx;
      height: 76rpx;
      padding: 0 24rpx;
      border-radius: 8rpx;
      border: 1rpx solid #cbcbcb;
      font-size: 26rpx;
    }
    .btn{
      margin-top: 24rpx;
      height: 76rpx;
      line-height: 76rpx;
      text-align: center;
      border-radius: 8rpx;
      color: #fff;
      background: linear-gradient(180deg, #FFA800 0%, #FF7A00 100%);
    }
  }
  .header_img{
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    ~.header_img{
      margin-left: 16rpx;
    }
  }
  .scroll_box{
    display: flex;
    align-items: center;
    margin-top: 24rpx;
    height: 60rpx;
    line-height: 60rpx;
    padding: 0 24rpx;
    border-radius: 8rpx;
    border: 1rpx solid #ffa101;
    background-color: #fff7e9;
    .notice{
      flex-shrink: 0;
      width: 36rpx;
      height: 36rpx;
      margin-right: 8rpx;
    }
  }
  .swiper{
    flex: 1;
    height: 100%;
    font-size: 24rpx;
    .swiper-item{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .group_info{
    margin-top: 24rpx;
    display: flex;
    .img_box{
      margin-right: 24rpx;
      .img{
        flex-shrink: 0;
        width: 176rpx;
        height: 176rpx;
        vertical-align: top;
      }
      .tip{
        text-align: center;
        font-size: 24rpx;
        color: #666;
      }
    }
    .info{
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      font-size: 26rpx;
      .text{
        min-height: 80rpx;
      }
      .highlight{
        color: $uni-color-primary;
      }
      .btn_group{
        display: flex;
        flex-direction: row;
        // justify-content: flex-end;
      }
      .small_btn{
        // display: inline-block;
        margin: 12rpx 0 12rpx 0;
        width: 124rpx;
        padding: 8rpx 16rpx;
        font-size: 24rpx;
        border-radius: 4rpx;
        color: #fff;
        background: linear-gradient(180deg, #FFA800 0%, #FF7A00 100%);
      }
    }
  }
}
</style>