<template>
<view class="build">
  <view class="title">
    <view class="line"></view>
    <view class="title_content">
      <image class="icon" :src="'/exhibition/icon/brand_active.png'| imageFilter('w_80')" />
      品牌馆</view>
    <view class="line"></view>
  </view>
  <view class="build_list" id="build_list">
    <view class="build_item" v-for="item in list" :key="item.id" @click="toDetail(item)">
      <view class="img_box" :class="{in_pc: !$store.state.in_mobile}">
        <image class="img" :src="item.pic | imageFilter('w_240')" mode="aspectFill" alt="">
      </view>
      <view v-if="item.title" class="title">{{item.title}}</view>
    </view>
    <view class="build_item empty"></view>
  </view>
</view>
</template>

<script>
export default {
  name: 'BrandList',
  data () {
    return {}
  },
  props: {
    list: {
      type: Array,
      default: ()=>[]
    },
  },
  methods: {
    toDetail(item){
      this.$navigateTo(item.url)
    }
  }
}
</script>

<style scoped lang="scss">
.build {
  >.title {
    display: flex;
    align-items: center;
    padding: 0 48rpx;
    margin-bottom: 32rpx;
    .line{
      flex: 1;
      height: 1rpx;
      background-color: #e1e3ee;
    }
    .title_content{
      display: flex;
      align-items: center;
      margin: 0 24rpx;
      font-size: 30rpx;
      color: #ff6e00;
      .icon{
        width: 48rpx;
        height: 48rpx;
      }
    }
    // margin-bottom: 24rpx;
    // font-size: 40rpx;
    // font-weight: bold;
    // color: #353535;
  }
}
.build_list{
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 48rpx;
  .build_item{
    flex: 1;
    min-width: 40%;
    margin-bottom: 24rpx;
    box-sizing: border-box;
    // padding: 24rpx;
    border-radius: 8rpx;
    border: 1rpx solid #d8d8d8;
    overflow: hidden;
    background-color: #fff;
    &:nth-child(even){
      margin-left: 24rpx;
    }
    &.empty{
      padding-top: 0;
      padding-bottom: 0;
      height: 0;
      border: none;
    }
    .img_box{
      width: 100%;
      height: 21vw;
      &.in_pc{
        max-height: 176rpx;
      }
      .img{
        width: 100%;
        height: 100%;
      }
    }
    .title{
      line-height: 2;
      padding: 0 12rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: bold;
      font-size: 28rpx;
      color: #353535;
    }
  }
}
</style>