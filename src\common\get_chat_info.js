import { ajax, navigateTo } from './index'
import store from '../store/index'
/**
 * 获取聊天前的好友信息
 * @param {Number || String} id - 会员id
 * @param {Number} type - 类型 1：经纪人详情;2：好友列表;3：楼盘详情;4：置业顾问列表;5：置业顾问详情;6：二手房;7：出租房;8：经纪人列表 9 首页置业顾问 10 楼盘活动页面 11：资讯详情 12：家装公司详情 13：案例列表 14：案例详情 15：线上售楼置业顾问列表 16：小区房价详情 17：求购列表 18：求租 19：预售列表 20：预售详情 21：预售榜单列表 22：楼市报页面  23：土拍列表 24：土拍详情 25：土拍榜单列表 26 搜索榜单 27：小区相册列表 28：VR详情 29：楼市圈  30：问答 31：荐房单 32：商业地产（出售） 33：商业地产（出租） 34：商业地产（转让）35  楼盘地图分析和地图配套  36  二手房 出租房地图分析和地图配套    37  小区地图分析和地图配套 38 门店地图分析和地图配套  39  商业地产地图分析和地图配套
 */
const getChatInfo = function (id, type, bid="", auto_jump=true, call_back) {
    // uni.showLoading({
    //     title: "正在建立连接...",
    //     mask: true
    // })
    store.state.allowOpen = true
    if (store.state.on_click) {
        console.log("点击中")
        setTimeout(() => {
            store.state.on_click = false
        }, 3000)
        return
    }
    store.state.on_click = true
    ajax.get('im/chatInfo.html', { agent_id: id, type, bid }, res => {
        if (res.data.code == 1) {
            // store.state.user_info.id = res.data.fromUser.id
            store.state.user_info.prelogo = res.data.fromUser.headimage
            store.state.user_info.cname = res.data.fromUser.nickname
            store.state.im.myChatInfo = res.data.fromUser
            store.state.im.imToken = res.data.imToken
            store.state.im.nowChat = {
                headimage: res.data.toUser.headimage,
                nickname: res.data.toUser.nickname,
                platform_id: res.data.toUser.platform_id,
                chat_id: res.data.toUser.chat_id,
                user_id: id,
                time: "",
                uncount: 1, //接口没有返回这个字段，为了进入直聊执行一次清楚未读，所以设置为大于0
                chat: {},
                chatList: []
            }
            // uni.hideLoading()
            store.state.on_click = false
            // 判断是否被加黑
            if (parseInt(store.state.im.nowChat.owner_id) == parseInt(store.state.im.nowChat.platformid)) {
                uni.showToast({
                    title: "对方已拒绝您的会话",
                    icon: 'none'
                })
                return
            }
            // 判断是否把对方加黑
            if (parseInt(store.state.im.nowChat.owner_id) == parseInt(store.state.user_info.id)) {
                uni.showToast({
                    title: "您已将对方加入黑名单",
                    icon: 'none'
                })
                return
            }
            // store.state.user_info = res.data.user
            // #ifdef MP-WEIXIN
            // subscriptions()
            // #endif
            if (auto_jump){
                navigateTo('/chatPage/chat/chat?title=' + store.state.im.nowChat.nickname + '&user_id=' + id)
            }
            if(call_back){
                call_back()
            }
            chatStatistics(id, type)
        } else {
            // uni.hideLoading()
            store.state.on_click = false
            uni.showToast({
                title: res.data.msg,
                icon: 'none'
            })
        }
    }, err => {
        store.state.on_click = false
        console.log(err)
        uni.showToast({
            title: "操作失败",
            icon: 'none'
        })
        // uni.hideLoading()
    })
}
const chatStatistics = function (id, type = '') {
    if (!type) {
        return
    }
    ajax.get('im/chatStatistics', { id, type }, () => {}, ()=>{}, { disableAutoHandle: true })
}

// #ifdef MP-WEIXIN
const subscriptions = function () {
    //if (!store.state.is_subscribe) { //如果没有订阅小程序订阅消息
    let tmplIds = store.state.unread_templateid_xcx
    uni.getSetting({
        withSubscriptions: true,
        success: e => {
            // 如果没有授权或者没有授权的模板id
            if (!e.subscriptionsSetting || e.subscriptionsSetting[tmplIds[0]] !== 'accept') {
                uni.requestSubscribeMessage({
                    tmplIds: tmplIds,
                    success: res => {
                        console.log(res)
                        store.state.is_subscribe = 1
                        pushIsSubscribe()
                    },
                    fail: err => {
                        console.log(err)
                    }
                })
            }
        },
        fail(err) {
            console.log(err)
        }
    })
    // }
}

const pushIsSubscribe = function () {
    ajax.post('im/subscribe', {}, res => {
        console.log(res.data)
    })
}
// #endif

module.exports = getChatInfo