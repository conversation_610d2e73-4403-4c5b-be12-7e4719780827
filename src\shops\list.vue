<template>
	<view class="content p-top-180">
		<view class="screen-tab flex-box">
			<!-- <view class="screen-tab-item flex-1 text-center" @click="switchTab(1)">
				<text>{{areaName}}</text>
				<my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
			</view>
			<view class="screen-tab-item flex-1 text-center" @click="switchTab(2)">
				<text>
                    附近
                </text>
				<my-icon :type="nowTab==0?'ic_down':'ic_up'" color="#d8d8d8" size="24rpx"></my-icon>
			</view> -->
			<view class="search-title flex-box">
				<view class="input-box flex-box flex-1">
					<view class="flex-box select right-line" @click="switchTab(1)">
						<text>{{areaName}}</text>
						<my-icon type="ic_down" size="32rpx"></my-icon>
					</view>
					<view class="inp-box-def search-box flex-1">
						<input  type="text" confirm-type="search" @input="handelInput" @confirm="handelSearch" :value="params.keywords" :maxlength="20" placeholder-style="font-size:28rpx" :placeholder="placeholder" />
					</view>
				</view>
				<view class="screen-tab-item text-center" @click="switchTab(2)" v-if="show_tab=='mendian'">
				<text>
                    附近
                </text>
				<my-icon :type="nowTab==0?'ic_down':'ic_up'" color="#d8d8d8" size="24rpx"></my-icon>
			</view>
			</view>
			
			
			
		</view>
		<view class="tab-list flex-row bottom-line" :style="{top:advTop}" >
			<view class="tab-item" :class="{active:show_tab==='mendian'}" @click="switchTabType('mendian')">门店</view>
			<view class="tab-item" :class="{active:show_tab==='jingjiren'}" @click="switchTabType('jingjiren')">经纪人</view>
			<view class="tab-item" :class="{active:show_tab==='gongsi'}" @click="switchTabType('gongsi')">公司</view>
		</view>
		<scroll-view scroll-y class="screen-panel" :class="nowTab==1?'show':''" @touchmove.stop.prevent="stopMove"  v-if ="showTab">
			<addressd :addressd = "area" ref="showArea" @changes="changeArea"></addressd>
		</scroll-view>
		<view class="shop-list">
			<template v-if="show_tab==='mendian'">
				<!-- :twice="item.twice||0"   暂时隐藏门店的出售房源--> 
				<shop-item v-for="item in listsData" :key='item.id' @click='handleGoDetail(item.id)' :image="item.image" :checked="item.checked" :name="item.name" :address="item.address" :agent="item.agent_num||0"  @tel="handleTel(item)"></shop-item>
			</template>
			<template v-if="show_tab==='jingjiren'">
				<shop-item v-for="item in listsData" :key='item.id' @click='handleGoAgentDetail(item.id)' :image="item.img" :checked="item.checked" :name="item.cname" :twice="item.house_count||0" :tname="item.tname" @tel="handleTel_agent(item)"></shop-item>
			</template>
			<template v-if="show_tab==='gongsi'">
				<shop-item v-for="item in listsData" :key='item.id' @click='handleGoCompanyDetail(item.id)' :image="item.logo" :checked="item.checked" :name="item.name" :address="item.address" :mendian="item.store_num||0" :agent="item.agent_num||0" @tel="handleTel(item)"></shop-item>
			</template>
        </view>
		
		<my-dialog ref="dialog" @cancelButton="getData" :show="show_dialog" @close="show_dialog = false" title="温馨提示" openType="openSetting">
			<view class="set-nickname-box">
				<view class="row">只有获取位置权限才能获取附近门店</view>
			</view>
		</my-dialog>
		<view class="mask" :class="nowTab==1?'show':''" @click="nowTab=0" @touchmove.stop.prevent="stopMove"></view>
		<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
		<chat-tip></chat-tip>
		<!-- #ifndef MP-WEIXIN -->
        <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip"></login-popup>
        <!-- #endif -->
    	<tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
	</view>
</template>

<script>
	import titleBar from "../components/titleBar.vue";
	import shopItem from "./components/shopItem.vue"
	import myIcon from "../components/myIcon"
	// #ifndef MP-WEIXIN
	import loginPopup from '../components/loginPopup'
	// #endif
	import {uniLoadMore,uniList,uniListItem} from '@dcloudio/uni-ui'
	import myDialog from "../components/dialog.vue"
	import wx from "weixin-js-sdk"
	import {checkAuth, formatImg,isIos,} from "../common/index.js"
	import addressd from '../components/jm-address/jm-address'
	import allTel from '../common/all_tel.js'
	export default{
		components:{
			titleBar,
			shopItem,
			myIcon,
			uniLoadMore,
			uniList,
			myDialog,
			addressd,
			uniListItem,
			// #ifndef MP-WEIXIN
			loginPopup,
			// #endif
		},
		data(){
			return{
				get_status:"loading",
				content_text:{
					contentdown:"",
					contentrefresh:"正在加载...",
					contentnomore:"没有更多数据了"
				},
				nowTab:0,
				areaName:'区域',
				area:[],
				params:{
					page:1,
					rows:20,
					sort:'',
					keywords:""
				},
				showTab:false,
				listsData:[],
				popup:{},
				show_dialog:false,
				login_tip:'',
				show_tab:"mendian",
      			show_tel_pop:false,
      			tel_res: {},
			}
		},
		computed:{
			placeholder(){
				if (this.show_tab=="mendian"){
					return "请输入要搜索的门店"
				}else if(this.show_tab=='jingjiren'){
					return "请输入要搜索的经纪人"
				}else if (this.show_tab=='gongsi'){
					return "请输入要搜索的公司"
				}
			},
			advTop(){
				// #ifdef MP 
				return this.$store.state.systemInfo.statusBarHeight
				// #endif 
				// #ifndef MP 
				return '44px'
				// #endif 
			}
		}, 
		onLoad(options){
			for (let key in options){
				this.params[key] = options[key]
			}
			this.getData()
			this.getScreen()
		},
		
        onShow(){
                //console.log("进入缓存");
        },
		methods:{
			getLocation(){
				let url;
				if(isIos()){
					url = this.$store.state.firstUrl
				}else{
					url = window.location.href
				}
				this.$ajax.get('/wechat/index/signature.html',{url:url},res=>{
					if(res.data.code == 1){
						res.data.config.jsApiList = ['getLocation','updateAppMessageShareData','updateTimelineShareData']
						wx.config(res.data.config)
						this.$store.state.getPosition(wx,()=>{
							this.getData()
						})
					}
				})
			},
			handelInput(e){
                this.params.keywords =e.detail.value
			},
			handelSearch(){
				this.params.page = 1
                this.getData()
			},
			getScreen(){
				this.$ajax.get('agentCompany/areaList',{no_limit:1},(res)=>{
					res.data.list.push({areaid:'',parentid:0,mapx:'',mapy:'',areaname:"全部"})
					let area = res.data.list
					this.area = this.getJiedao(area,"areaid","parentid","city")
					this.showTab=true
				},(err)=>{
					console.log(err)
				})
			},
			switchTabType(type){
				this.show_tab=type
				this.params.keywords=''
				if(type==='mendian'){
					this.params.page = 1
					this.getData()
				}
				if(type==='gongsi'){
					this.params.page = 1
					this.getData()
					// this.$navigateTo('/pages/community/news_list')
				}
				if(type==='jingjiren'){
					this.params.page = 1
					this.getData()
					// this.$navigateTo('/pages/news/news')
				}
			},
			getData(){
				this.params.lat = this.$store.state.position.lat
				this.params.lng = this.$store.state.position.lng
				if(this.params.page == 1){
					this.listsData=[]
				}
				let url =""
				if (this.show_tab=="mendian"){
					url='agentCompany/agentStoreList'
				}else if (this.show_tab=="gongsi"){
					url='agentCompany/companyList'
				}else if (this.show_tab=="jingjiren"){
					url='agent/agentMemberList'
				}
				this.get_status = "loading"
				this.$ajax.get(url,this.params,(res)=>{
					//console.log(JSON.stringify(this.params));
					if(res.data.share){
							this.share = res.data.share
						}else {
							if (this.show_tab=="mendian"){
								this.share={
									title:'门店列表',
									content:'门店列表',
									pic:""
								}
							}else if (this.show_tab=="jingjiren"){
								this.share={
									title:'经纪人列表',
									content:'经纪人列表',
									pic:""
								}
							}else if (this.show_tab=="gongsi"){
								this.share={
									title:'公司列表',
									content:'公司列表',
									pic:""
								}
							}
						}
						this.getWxConfig()
					if(res.data.code == 1){
						this.listsData = this.listsData.concat(res.data.list)
						if(res.data.list.length<this.params.rows){
							this.get_status = "noMore"
						}else{
							this.get_status = "more"
						}
						
					}else{
						this.get_status = "noMore"
					}
					uni.stopPullDownRefresh();
				},(err)=>{
					console.log(err)
					uni.stopPullDownRefresh();
				})
            },
            handleTel(e){
            // #ifdef MP-WEIXIN
            // 在模板处已经判断过是否登录和绑定手机号了
            uni.makePhoneCall({
							phoneNumber: e.tel
						})
            // #endif
            // #ifndef MP-WEIXIN
            this.$ajax.get('member/checkUserStatus', {}, res => {
							if (res.data.code === 1) {
								uni.makePhoneCall({
									phoneNumber: e.tel
								})
								// this.$ajax.get(
								// 	'im/callUpStatistics',
								// 	{
								// 		id: e.id,
								// 		tel: parseInt(e.tel),
								// 		type: 3
								// 	},
								// 	res => {}
								// )
                
                // encryptionTel(e.id, e.id, e.tel, 'agent', 8, 'list')
              }else if(res.data.status == 1){
								uni.removeStorageSync('token')
								this.$navigateTo('/user/login/login')
							}else if(res.data.status == 2){
								this.$store.state.user_login_status = res.data.status
								this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
								this.$refs.login_popup.showPopup()
							}
            })
            // #endif
            },
			// 拨打经纪人电话
			handleTel_agent(e) {
				this.tel_params = {
					type: 3,
					callee_id:e.uid,
					scene_type:3,
					scene_id:e.uid,
					source: 'agent_list',
					success: (res)=>{
					this.tel_res = res.data
					this.show_tel_pop = true
					}
				}
				// #ifdef MP-WEIXIN
				// 在模板处已经判断过是否登录和绑定手机号了
				allTel(this.tel_params)
				// #endif
				// #ifndef MP-WEIXIN
				this.tel_params.intercept_login = true
				this.tel_params.fail = (res)=>{
					if(res.data.code === -1){
					this.$store.state.user_login_status = 1
					this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
					this.$refs.login_popup.showPopup()
					}
					if(res.data.code === 2){
					this.$store.state.user_login_status = 2
					this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
					this.$refs.login_popup.showPopup()
					}
				}
				allTel(this.tel_params)
				// #endif
			},
			retrieveTel(){
				allTel(this.tel_params)
			},
			handleGoDetail(id){
				this.$navigateTo('/shops/detail?id='+id)
			},
			handleGoCompanyDetail(id){
				this.$navigateTo("/shops/myCompany?id="+id)
			},
			handleGoAgentDetail(id){
				this.$navigateTo("/pages/agent/detail?id="+id)
			},
			stopMove(){

			},
			getJiedao (a,idStr,pIdStr,chindrenStr) {
				var r = [], hash = {}, id = idStr, pId = pIdStr, children = chindrenStr, i = 0, j = 0, len = a.length;  
				for(; i < len; i++){  
				a[i].label= a[i].name;	
				delete a[i].name; 
					hash[a[i][id]] = a[i];
				}  
				for(; j < len; j++){  
					var aVal = a[j], hashVP = hash[aVal[pId]];  

					if(hashVP){  
						!hashVP[children] && (hashVP[children] = []);  
				
						hashVP[children].push(aVal);  
					}else{  
						r.push(aVal);  
					}  
				}  
				return r;
			},
			changeArea(e){
				this.areaName = e.district?e.district:(e.city?e.city:(e.province?e.province:""))
				this.params.area_id = e.district_id?e.district_id:(e.city_id?e.city_id:(e.province_id?e.province_id:""))
				this.nowTab = 0
				this.params.page = 1
				this.getData()
			},
			switchTab(index){
				if(this.nowTab == index){
					this.nowTab = 0
					// this.$refs.ershou.allMove()
				}else{
					this.nowTab = index
					if (index==1){
						this.params.sort =''
						this.$refs.showArea.showAddress()
					}else if(index==2){
						this.params.sort =1
						// this.getData()
                        this.getLocation()
                    }
					// this.$refs.ershou.stopMove()
				}
			},
			selectArea(id, name){
				this.params.areaid = id
				this.areaName = name
				this.nowTab = 0
				this.params.page = 1
				this.getData()
			},
			handleInput(e){
				this.keyword = e.detail.value
			},
			handleSearch(e){
				this.params.keyword = e.detail.value
				this.params.page = 1
				this.getData()
			},
			// 关闭登录弹窗时
			handleCloseLogin() {
				if (this.$store.state.user_login_status === 1) {
					uni.removeStorageSync('token')
					this.$navigateTo('/user/login/login')
				}
				if (this.$store.state.user_login_status === 2) {
					this.$navigateTo('/user/bind_phone/bind_phone')
				}
			},
			checkLogin(tip, callback) {
				this.$ajax.get('member/checkUserStatus', {}, res => {
					if (res.data.code === 1) {
						callback&&callback()
					}else if(res.data.status == 1){
						uni.removeStorageSync('token')
						this.$navigateTo('/user/login/login')
					}else if(res.data.status == 2){
						this.$store.state.user_login_status = res.data.status
						this.login_tip = tip
						this.$refs.login_popup.showPopup()
					}
				})
			},
			
			back() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.switchTab({
						url: '/pages/index/index'
					})
				}
			}
		},
		onPullDownRefresh(){
			this.params.page = 1
			this.getData()
		},
		onReachBottom(){
			this.params.page = this.params.page+1
			this.getData()
		},
		onShareAppMessage(){
			if(this.share){
				return {
					title: this.share.title||"",
					content:this.share.content||"",
					imageUrl: this.share.pic?formatImg(this.share.pic,'w_6401'):""
				}
			}
		},
		// #ifdef H5 || APP-PLUS
		onNavigationBarButtonTap(option){
			this.$navigateTo('/pages/add/detail?catid=1')
		},
		// #endif
	}
</script>
<style lang="scss" scoped>
	.p-top-180{
		// padding-top: 80upx;
	}
	/* #ifdef H5 */
	.screen-tab{
		top: 0;
		// margin-top: 44px;
		box-sizing: border-box;
		padding: 0 48rpx;
		border:none;
		position: relative;
	}
	.screen-panel {
		margin-top: 44px;
		// top: 44px;
		// transform: translateY(-130%);
		// margin-top: 80rpx;
	}
	.screen-panel.show{
		z-index: 102;
	}
	/* #endif */
	/* #ifndef H5 */
	.screen-tab{
		top: var(--window-top);
		// margin-top: 44px;
		box-sizing: border-box;
		padding: 0 48rpx;
		border:none;
	}
	.screen-panel {
		top: var(--window-top);
		// margin-top: 162rpx;
	}
	/* #endif */
	/* #ifdef H5 */
	.search-title{
		width: 100%;
		// padding: 15rpx 48rpx;
		box-sizing: border-box;
		// position: fixed;
		// top: 44px;
		z-index: 99;
		background-color: #fff;
		
	}
	/* #endif */
	/* #ifndef H5 */
	.search-title{
		width: 100%;
		padding: 15rpx 48rpx;
		box-sizing: border-box;
		// position: fixed;
		// top: var(--window-top);
		z-index: 99;
		background-color: #fff;
		border-bottom: 1upx solid $uni-border-color;
		box-shadow: 0 0 26upx #dedede;
	}
	/* #endif */
	.input-box{
		align-items: center;
		padding: 0 24rpx;
		background-color: #eee;
		border-radius: 8rpx;
		margin-right: 10upx;
	}
	.select{
		align-items: center;
		line-height: 32upx;
		margin: 16rpx 0;
		padding-right: 15upx;
	}
	.search-box{
		flex: 1;
	}
	.search-btn{
		line-height: 60upx;
		padding-left: 15upx;
		color: #666
	}
	.search-box input{
		height: 100%;
		padding: 6rpx 10rpx;
		box-sizing: border-box;
		font-size: $uni-font-size-base;
	}
	.flex-row{
		display: flex;
		flex-direction: row;
	}
	.tab-list{
		padding: 0 48rpx;
		justify-content: space-between;
		position: sticky;
		//   position: fixed;
		//   top: var(--window-top);
		// width: 100%;
		background-color: #fff;
		z-index: 100;
		.tab-item{
			flex: 1;
			padding: 24rpx;
			text-align: center;
			position: relative;
			&.active{
			color: $uni-color-primary;
			&::after{
				content: "";
				height: 8rpx;
				border-radius: 4rpx;
				background-color: $uni-color-primary;
				position: absolute;
				bottom: 0;
				width: 48rpx;
				left: 0;
				right: 0;
				margin: auto
			}
			}
		}
		}

    // .search-box {
    //     margin-left: 20rpx;
    //     align-items: center;
    //     padding: 10rpx 20rpx;
    //     background-color: #f5f5f5;
    //     color: #999;
    //     border-radius: 8rpx;
    //     .search-left {
    //         margin-right: 20rpx;
    //     }
    //     .inp{
    //         margin-left: 20rpx;
    //     }
    // }
    // .seach_btn{
    //     align-items: center;
    //     padding: 0 24rpx;
    //     .text{
    //         margin-left: 16rpx;
    //     }
    // }
    .shop-list{
        padding: 10upx 48upx;
        background: #fff;
    }
	

</style>