<template>
  <view>
    <!-- #ifdef MP-WEIXIN -->
    <button v-if="user_login_status==1" open-type="getUserInfo" @click.prevent.stop="stopEvent()" @getuserinfo="onGetUserInfo">
      <slot />
    </button>
    <button v-else-if="user_login_status==2" open-type="getPhoneNumber" @click.prevent.stop="stopEvent()" @getphonenumber="onGetPhonenumber">
      <slot />
    </button>
    <view v-else @click.prevent.stop="advAsk()">
      <slot />
    </view>
    <!-- #endif -->
    <!-- #ifndef MP-WEIXIN -->
    <view @click.prevent.stop="advAsk()">
      <slot />
    </view>
    <!-- #endif -->
  </view>
</template>

<script>
export default {
  components: {

  },
  props:{
    user_login_status:[Number,String], // 用户登录状态
    identity_id: [Number, String], // 用户身份的id例如置业顾问的id或店铺的id等
    user_id:[Number,String] // 用户id
  },
  data () {
    return {

    }
  },
  methods: {
    advAsk(){
      this.$emit('ok', { identity_id: this.identity_id, user_id: this.user_id,  type:this.from_type})
    },
    onGetPhonenumber(res) {
      if (res.target.encryptedData && res.target.iv) {
        this.bindPhone(res.target.encryptedData, res.target.iv)
      } else {
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    onGetUserInfo(res) {
      if (res.target.userInfo && res.target.userInfo.avatarUrl && res.target.userInfo.nickName) {
        uni.login({
          provider: 'weixin',
          success: loginRes => {
            this.getToken(loginRes.code, res.target.userInfo.avatarUrl, res.target.userInfo.nickName)
          }
        })
      } else {
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
    },
    getToken(code, avatarUrl, nickName) {
      this.$ajax.get('member/getOpenidByCode.html', { code, headimgurl: avatarUrl, nickname: nickName }, res => {
        if (res.data.code == 1) {
          // this.getUserInfo(res.data.user)
          // 存储token
          uni.setStorageSync('token', res.data.token)
          uni.showToast({
            title: '登录成功'
          })
          if (res.data.tel) {
            this.advAsk()
            this.$store.state.user_login_status = 3
          } else {
            this.$store.state.user_login_status = 2
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    bindPhone(encryptedData, iv) {
      this.$ajax.get('member/getWxPhoneNumber', { encryptedData, iv }, res => {
        if (res.data.code == 1) {
          uni.showToast({
            title: res.data.msg
          })
          if (res.data.token) {
            //绑定手机号成功后台返回一个新的token
            uni.setStorageSync('token', res.data.token)
          }
          this.advAsk()
          this.$store.state.user_login_status = 3
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    stopEvent(){
      return false
    }
  }
}
</script>

<style scoped lang="scss">
button{
  border: 0;
  padding: 0;
  margin: 0;
  line-height: 1;
  background: none;
}
</style>