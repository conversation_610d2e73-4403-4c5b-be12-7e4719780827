<template>
  <view
    class="house"
    :class="{ 'bottom-line': showLine }"
    @click="$emit('click', { type: type, detail: itemData })"
  >
    <view class="tip">
      <view>
        <text v-if="itemData.is_show == 1" class="el-tag el-success"
          >已上架</text
        >
        <text v-else class="el-tag el-danger">已下架</text>
        <text v-if="itemData.info_level == 0" class="el-tag el-warning"
          >待审</text
        >
        <text v-else-if="itemData.info_level == 1" class="el-tag el-success"
          >正常</text
        >
        <text v-else class="el-tag el-danger">精选</text>
        <text v-if="itemData.upgrade_type == 2" class="el-tag el-danger"
          >置顶</text
        >
        <text v-if="itemData.is_fee == 1" class="el-tag el-primary">付费</text>
        <text v-if="itemData.is_deleted == 1" class="el-tag el-danger"
          >已删除</text
        >
        <text
          v-if="itemData.release_type == 'check_release'"
          class="el-tag el-warning"
          >委托发布</text
        >
      </view>
      <view class="used el-tag el-danger" @click.prevent.stop="used"
        >历史{{ itemData.history_count }}条记录</view
      >
    </view>
    <view class="info_box flex-row">
      <view
        class="img-box"
        :class="{
          cut_price:
            itemData.is_cut_price == 1 &&
            itemData.cut_price &&
            itemData.cut_price > 0,
        }"
      >
        <view class="img_con">
          <view class="level-box">
            <text class="level level2" v-if="itemData.info_level === 2"
              >精选</text
            >
          </view>
          <image
            class="img"
            :src="itemData.img | imgUrl"
            lazy-load
            mode="aspectFill"
          ></image>
          <image
            v-if="itemData.is_vr || itemData.vr"
            class="video-icon"
            src="/static/icon/vr.png"
          ></image>
          <image
            v-else-if="itemData.is_video == 1"
            class="video-icon"
            src="/static/icon/video.png"
          ></image>
          <view
            v-if="itemData.is_cut_price == 1 && itemData.cut_price > 0"
            class="cut_price_info"
            >直降{{ itemData.cut_price
            }}{{ itemData.parentid == 1 ? "万" : "元/月" }}</view
          >
        </view>
        <!-- 当前会员认领的+委托发布的+下架或者待审的，加一个上架按钮,已删除的不显示上架 -->
        <!-- <text v-if="itemData.is_claim == 1&&itemData.release_type == 'check_release'&&itemData.is_deleted != 1&&(itemData.is_show != 1 || itemData.info_level == 0)" class="foot-status" @click.prevent.stop="update">上架</text> -->
      </view>
      <view class="info">
        <view class="title_box flex-row">
          <view class="title" :class="titleRow == 2 ? 'row2' : 'row1'">
            <text v-if="itemData.upgrade_type == 2" class="ding">顶</text>
            <view
              class="is_agent"
              v-if="itemData.levelid > 1 && from == 'find_house'"
            >
              经纪人
            </view>
            <view
              class="is_personal"
              v-if="itemData.levelid == 1 && from == 'find_house'"
            >
              个人
            </view>
            <text
              :class="{
                red: itemData.ifred,
                bold: itemData.ifbold,
              }"
              >{{ itemData.title }}</text
            >
          </view>
        </view>
        <!-- 二手房和出租房 -->
        <view class="center-info">
          <!-- <text :class="'attr' + itemData.zhongjie">{{ itemData.zhongjie == 2 ? "经纪人" : "个人" }}</text> -->
          <text class="huxing"
            >{{ itemData.shi || 0 }}室{{ itemData.ting || 0 }}厅{{
              itemData.wei || 0
            }}卫</text
          >
          <text class="jiange jiange-margin" v-if="itemData.mianji">|</text>
          <text class="mj" v-if="itemData.mianji">{{ itemData.mianji }}㎡</text>
          <text class="jiange" v-if="itemData.chaoxiang">|</text>
          <text class="cx" v-if="itemData.chaoxiang">{{
            itemData.chaoxiang
          }}</text>
          <!-- <text class="area">{{ itemData.areaname || "" }}</text> -->
          <text class="type">{{
            itemData.community_name || itemData.areaname
          }}</text>
        </view>
        <view class="labels">
          <!-- <text class ="cut_price_info" v-if="itemData.is_cut_price==1&&itemData.cut_price >0">
          直降{{itemData.cut_price}}{{itemData.parentid==1?"万":'元/月'}}
        </text> -->
          <template v-if="itemData.label && itemData.label.length > 0">
            <text
              class="label"
              :style="{ color: label.color, borderColor: label.color }"
              v-for="(label, index) in itemData.label"
              :key="index"
              >{{ label.name }}</text
            >
          </template>
        </view>
        <view class="bottom-info flex-box">
          <view class="bottom-left">
            <template v-if="type === 'ershou'">
              <text
                class="mianyi"
                v-if="
                  itemData.fangjia == '面议' ||
                    itemData.fangjia == '0' ||
                    !itemData.fangjia
                "
                >面议</text
              >
              <text class="price" v-else>{{ itemData.fangjia }}</text>
              <block
                v-if="
                  itemData.fangjia !== '面议' &&
                    itemData.fangjia != '0' &&
                    itemData.fangjia
                "
              >
                <text class="price-unit">万</text>
                <text class="average_price"
                  >{{
                    ((itemData.fangjia * 10000) / itemData.mianji).toFixed(2)
                  }}元/m²</text
                >
              </block>
              <!-- <text>{{ itemData.shi }}室{{ itemData.ting }}厅{{ itemData.wei }}卫</text>
            <text class="mj">{{ itemData.mianji }}㎡</text> -->
            </template>
            <template v-if="type === 'renting'">
              <text
                class="mianyi"
                v-if="
                  itemData.zujin == '面议' ||
                    itemData.zujin == '0' ||
                    !itemData.zujin
                "
                >面议</text
              >
              <text class="price" v-else>{{ itemData.zujin }}</text>
              <text
                class="price-unit"
                v-if="
                  itemData.zujin !== '面议' &&
                    itemData.zujin != '0' &&
                    itemData.zujin
                "
                >元/月</text
              >
              <!-- <text>{{ itemData.shi }}室{{ itemData.ting }}厅{{ itemData.wei }}卫</text>
            <text class="mj">{{ itemData.mianji }}㎡</text> -->
            </template>
          </view>
          <view class="bottom-right" v-if="showTime">
            <text class="u-time">{{ itemData.begintime }}</text>
          </view>
        </view>
        <view class="footer-info">
          <template v-if="itemData.loudong != '' && itemData.loudong != 0">
            <view class="loudong">
              <text>{{ itemData.loudong }}号楼</text>
              <template v-if="itemData.danyuan != '' && itemData.loudong != 0">
                <text> {{ itemData.danyuan }}单元</text>
                <text v-if="itemData.fanghao != '' && itemData.fanghao != 0">{{
                  itemData.fanghao
                }}</text>
              </template>
            </view>
          </template>
        </view>
        <template
          v-if="itemData.is_claim == 1 && itemData.claim_is_protected == 0"
        >
          <view class="footer-info">
            <view class="footer-right">
              <image
                v-if="itemData.prelogo != ''"
                class="ic_dianhua prelogo"
                :src="itemData.prelogo"
              ></image>
              <text class="contact-name" v-if="itemData.contact_who != ''">{{
                itemData.contact_who
              }}</text>
              <text v-if="itemData.tel != ''">{{ itemData.tel }}</text>
              <image
                v-if="itemData.tel != ''"
                class="ic_dianhua"
                @click.prevent.stop="handleTel"
                src="https://images.tengfangyun.com/images/icon/ic_dianhua.png"
              ></image>
            </view>
          </view>
        </template>
        <view class="foot-info">
          <text class="foot-status" v-if="itemData.is_claim == 1">已认领</text>
          <text
            class="foot-status"
            v-if="itemData.is_claim == 0"
            @click.prevent.stop="renling"
            >点击认领</text
          >
          <!-- 信息被当前会员认领 -->
          <template v-if="itemData.is_claim == 1">
            <text class="blue" @click.prevent.stop="browse">浏览</text>
            <text class="orange" @click.prevent.stop="follow">跟进</text>
            <text class="green" @click="showCopywriting()">复制</text>
          </template>
        </view>
        <view v-if="itemData.is_claim == 1" class="foot-info foot-text">
          {{ itemData.claim_cname }}已认领
        </view>
        <!-- <view class="agent_info flex-row" v-if="itemData.levelid > 1">
        <image
          class="header_img"
          :src="itemData.prelogo | imageFilter('w_80')"
        ></image>
        <text class="c_name">{{ itemData.cname }}</text>
        <text class="b_name flex-1">{{ itemData.tname }}</text>
      </view> -->
        <!-- <view class="hongbao" v-if="itemData.hb_is_open">
          <image src="/static/icon/hongbao.png" mode="aspectFit"></image>
          <text class="text">红包</text>
        </view>
        <view
          class="push flex-row"
          v-if="from == 'find_house' && itemData.is_match"
        >
          <view class="is_push pushed" v-if="itemData.is_push == 1"
            >已推送</view
          >
          <view class="is_push" v-else>
            猜你喜欢
          </view>
          <view class="tiaojian">{{ itemData.is_match }}</view>
        </view> -->
        <!-- <view class="tips flex-row" v-if='from =="decommend"'>
        以上房源展示面积均为建筑面积
      </view> -->

        <!-- 复制分享文案 -->
        <my-popup
          ref="text_popup"
          position="center"
          :height="text_popup_height"
        >
          <view class="copy-text-box" id="copy-text">
            <view class="title">{{ itemData.title }}</view>
            <view class="info-box">
              <view class="info-row flex-row" v-if="itemData.community_name">
                <text class="label">小区：</text>
                <text class="value">{{ itemData.community_name }}</text>
              </view>
              <view class="info-row flex-row">
                <text class="label">户型：</text>
                <text class="value">{{
                  `${itemData.shi}室${itemData.ting}厅${itemData.wei}卫${itemData.mianji}m²`
                }}</text>
              </view>
              <view class="info-row flex-row" v-if="type === 'ershou'">
                <text class="label">售价：</text>
                <text class="value">{{
                  itemData.fangjia ? itemData.fangjia + "万" : "面议"
                }}</text>
                <text class="value" v-if="itemData.danjia"
                  >单价{{ itemData.danjia }}</text
                >
              </view>
              <view class="info-row flex-row" v-if="type === 'renting'">
                <text class="label">租金：</text>
                <text class="value">{{
                  itemData.zujin ? itemData.zujin + "元/月" : "面议"
                }}</text>
              </view>
              <view class="info-row flex-row">
                <text class="label">类型：</text>
                <text class="value">{{ itemData.catname }}</text>
              </view>
              <view
                class="info-row flex-row"
                v-if="itemData.label && itemData.label.length > 0"
              >
                <text class="label">卖点：</text>
                <text class="value">{{
                  itemData.label.map((item) => item.name).join(" ")
                }}</text>
              </view>
              <view class="info-row flex-row">
                <text class="label">楼层：</text>
                <text class="value" v-if="itemData.floor"
                  >{{ itemData.floor || "" }}/共{{
                    itemData.louceng || ""
                  }}层</text
                >
                <text class="value" v-else
                  >共{{ itemData.louceng || "" }}层</text
                >
              </view>
              <view class="info-row flex-row" v-if="itemData.tel">
                <text class="label">电话：</text>
                <text class="value" v-if="itemData.tel">{{
                  itemData.tel ? itemData.tel : ""
                }}</text>
              </view>
              <view class="button disabled-btn flex-row" v-if="copy_success">
                <my-icon
                  type="check-circle"
                  size="30rpx"
                  color="#999"
                ></my-icon>
                <text class="text">文本已复制</text>
              </view>
              <view class="button" v-else @click="copywriting">复制文本</view>
            </view>
          </view>
        </my-popup>
      </view>
    </view>
  </view>
</template>
<style scoped lang="scss">
.el-tag {
  height: 40rpx;
  padding: 0 10rpx;
  line-height: 36rpx;
  display: inline-block;
  font-size: 24rpx;
  border-width: 2rpx;
  border-style: solid;
  border-radius: 4rpx;
  box-sizing: border-box;
  white-space: nowrap;
  margin-right: 10rpx;
}

.el-primary {
  color: #409eff;
  background-color: #ecf5ff;
  border-color: #d9ecff;
}

.el-warning {
  background-color: #fdf6ec;
  border-color: #faecd8;
  color: #e6a23c;
}

.el-success {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}

.el-danger {
  background-color: #fef0f0;
  border-color: #fde2e2;
  color: #f56c6c;
}
// 复制文案
.copy-text-box {
  padding: 24rpx 32rpx;
  background-color: #fff;
  width: 600rpx;
  // height: 100%;
  // margin-left: 75rpx;
  margin: 0 auto;
  border-radius: 16rpx;
  .title {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }
  .info-row {
    line-height: 1.6;
    color: #333;
    .label {
      color: #999;
    }
    .value {
      flex: 1;
      &.highlight {
        color: $uni-color-primary;
      }
    }
  }
  .button {
    align-items: center;
    justify-content: center;
    line-height: 64rpx;
    width: 236rpx;
    text-align: center;
    border-radius: 32rpx;
    margin: 32rpx auto;
    background: #fb656a;
    box-shadow: 0 2px 8px 0 rgba(251, 101, 106, 0.4);
    color: #fff;
  }
  .disabled-btn {
    background-color: #f5f5f5;
    box-shadow: none;
    color: #999;
    > .text {
      margin-left: 12rpx;
    }
  }
}
.footer-info {
  margin: 20rpx 0;
  color: #333;
  .loudong {
    text {
      margin-right: 10rpx;
    }
  }
  .footer-right {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 28rpx;
    .contact-name {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    text {
      margin-right: 10rpx;
      display: inline-block;
    }
  }
  .ic_dianhua {
    width: 50rpx;
    height: 50rpx;
  }
  .prelogo {
    border-radius: 50%;
    margin-right: 10rpx;
    object-fit: cover;
  }
}
.foot-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16rpx;
  .foot-status {
    background: #fb656a;
  }
  .blue {
    background: #498fe2;
  }
  .orange {
    background: #fbac65;
  }
  .green {
    background: #67c23a;
  }
  text {
    background: #fb656a;
    color: #fff;
    border-radius: 22rpx;
    border: none;
    padding: 6rpx 20rpx;
    font-size: 24rpx;
    // width: 90rpx;
    text-align: center;
  }
}
.foot-text {
  color: #333;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.house {
  padding: 40rpx 0;
  .tip {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    .used {
      background: #fff;
      border-color: #f56c6c;
    }
  }
  .info_box {
    display: flex;
    .img-box {
      width: 204rpx;
      height: 172rpx;
      border-radius: 8rpx;
      margin-right: 16rpx;
      position: relative;
      .img_con {
        position: relative;
        width: 204rpx;
        height: 172rpx;
        background: #fff;
        overflow: hidden;
        border-radius: 6rpx;
        z-index: 1;
      }
      .foot-status{
        background: #fb656a;
        color: #fff;
        display: block;
        text-align: center;
        font-size: 24rpx;
        padding: 4rpx 0;
        border-radius: 4rpx;
        margin-top: 14rpx;
      }
      &.cut_price {
        padding: 2rpx;

        .cut_price_info {
          position: absolute;
          left: 0;
          bottom: 0;
          z-index: 3;
          display: inline-block;
          padding: 8rpx 12rpx;
          font-size: 22rpx;
          line-height: 1;
          border-top-right-radius: 8rpx;
          background: #ff6069;
          background-size: 100% 100%;
          color: #fff;
        }
        &:after {
          content: "";
          position: absolute;
          top: -3px;
          bottom: -3px;
          left: -3px;
          right: -3px;
          background: linear-gradient(to bottom, #ff6069 0%, #ffa857 100%);
          border-radius: 10rpx;
          z-index: 0;
        }
      }
      .img {
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 1;
      }
      .level-box {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 2;
        .level {
          display: block;
          margin-bottom: 5rpx;
          padding: 2rpx 10rpx;
          font-size: 22rpx;
          border-bottom-left-radius: 8rpx;
          color: #fff;
          &.level1 {
            background: linear-gradient(132deg, #f7918f 0%, #fb656a 100%);
          }
          &.level2 {
            background: linear-gradient(135deg, #69d4bb 0%, #00caa7 100%);
          }
        }
      }
      .video-icon {
        width: 40rpx;
        height: 40rpx;
        position: absolute;
        left: 50%;
        bottom: 50%;
        transform: translate(-50%, 50%);
        z-index: 3;
      }
    }
    .info {
      flex: 1;
      overflow: hidden;
      .title {
        font-size: 32rpx;
        line-height: 1.5;
        flex: 1;
        margin-top: -6rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        .red {
          color: #fb656a;
        }
        .bold {
          font-weight: bold;
        }
        &.row1 {
          max-height: 90rpx;
          margin-bottom: 10rpx;
        }
        &.row2 {
          min-height: 90rpx;
        }
      }
      .title_box {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }
      .push {
        margin-top: 10rpx;
        align-items: center;
        .tiaojian {
          font-size: 22rpx;
          color: #999;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .is_push {
        // background:linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
        color: #fff;
        // padding: 6rpx 10rpx;
        // margin-right: 10rpx;
        // line-height: 1;
        // font-size: 22rpx;
        display: inline-block;
        min-width: 100rpx;
        margin-right: 10rpx;
        text-align: center;
        font-size: 22rpx;
        padding: 5rpx 10rpx;
        border-radius: 8rpx;
        // color: #FB656A;
        background: linear-gradient(90deg, #f7918f 0%, #fb656a 100%);
        &.pushed {
          background-image: linear-gradient(90deg, #8cd3fc 0%, #4cc7f6 100%);
        }
      }
      .is_agent {
        display: inline-block;
        padding: 6rpx 10rpx;
        margin-right: 10rpx;
        line-height: 1;
        font-size: 22rpx;
        border-radius: 4rpx;
        background: #44331a;
        color: #fcecdb;
        // color: linear-gradient(to right, #fcecdb 0%, #f8dda9 100%);
      }
      .is_personal {
        display: inline-block;
        padding: 6rpx 10rpx;
        margin-right: 10rpx;
        line-height: 1;
        font-size: 22rpx;
        border-radius: 4rpx;
        background: linear-gradient(90deg, #69d4bb 0, #00caa7);
        color: #fff;
      }
      .ding {
        display: inline-block;
        padding: 6rpx 10rpx;
        margin-right: 10rpx;
        line-height: 1;
        font-size: 22rpx;
        border-radius: 4rpx;
        background: linear-gradient(to right, #f7918f 0%, #fb656a 100%);
        color: #fff;
      }
      .center-info {
        display: flex;
        align-items: center;
        margin-top: 5rpx;
        font-size: 22rpx;

        .jiange {
          margin: 0 4rpx;
          color: #999;
          &.jiange-margin {
            margin: 0 12rpx;
          }
        }
        &.need {
          .price_box {
            margin-left: 48rpx;
          }
          .label {
            font-size: 22rpx;
            color: #999;
          }
          .area {
            font-size: 22rpx;
            color: #333;
          }
          .in_price {
            font-size: 22rpx;
            color: $uni-color-primary;
          }
        }
        .area {
          margin-left: 16rpx;
          color: #999;
        }
        .type {
          overflow: hidden;
          white-space: nowrap;
          flex: 1;
          text-align: right;
          text-overflow: ellipsis;
          color: #333;
        }
        .cx {
          margin-right: 4rpx;
        }
        .mj {
          margin-right: 4rpx;
        }
      }

      .labels {
        margin-top: 16rpx;
        line-height: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        .cut_price_info {
          display: inline-block;
          padding: 8rpx 12rpx;
          font-size: 22rpx;
          line-height: 1;
          border-radius: 4rpx;
          background: #ff6069;
          margin-right: 4rpx;
          color: #fff;
        }
        .label {
          display: inline-block;
          line-height: 1;
          font-size: 22rpx;
          padding: 4rpx 8rpx;
          border: 1rpx solid #d8d8d8;
          color: #999;
          border-radius: 4rpx;
          vertical-align: middle;
          ~ .label {
            margin-left: 16rpx;
          }
        }
      }
      .bottom-info {
        margin-top: 16rpx;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        flex-wrap: wrap;
        text {
          font-size: 22rpx;
        }
        .mianyi {
          font-size: 32rpx;
          font-weight: bold;
          margin-right: 10rpx;
          color: #fb656a;
        }
        .price {
          font-size: 34rpx;
          line-height: 1;
          font-weight: bold;
          color: #fb656a;
        }
        .price-unit {
          font-size: 26rpx;
          margin: 0 16rpx 0 8rpx;
        }
        .average_price {
          color: #999;
        }
        .bottom-right {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          text-align: right;
          margin-left: 16rpx;
        }
        .u-time {
          line-height: 1;
          position: relative;
          font-size: 22rpx;
          color: #999;
        }
      }
    }
    .agent_info {
      display: flex;
      margin-top: 16rpx;
      align-items: center;
      font-size: 22rpx;
      color: #999;
      .header_img {
        width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
        background-color: #f5f5f5;
      }
      .c_name,
      .b_name {
        margin-left: 16rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
.hongbao {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 20rpx;
  }
  .text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #ff5b5b;
  }
}
.tips {
  color: #fb656a;
  font-size: 24rpx;
  padding: 20rpx 0 0;
  justify-content: flex-end;
}
</style>
<script>
import { formatImg } from "@/common/index.js";
import myPopup from "@/components/myPopup.vue";
import myIcon from "../../components/myIcon.vue";
export default {
  components: {
    myIcon,
    myPopup,
  },
  data() {
    return {
      text_popup_height: "",
      copy_success: false,
    };
  },
  props: {
    itemData: Object,
    type: {
      type: String,
      default: "ershou",
    },
    titleRow: {
      type: [Number, String],
      default: 2,
    },
    from: {
      type: String,
      default: "",
    },
    showLine: {
      type: Boolean,
      default: true,
    },
    showTime: {
      type: Boolean,
      default: true,
    },
  },
  filters: {
    imgUrl(val) {
      return formatImg(val, "w_240");
    },
  },
  methods: {
    renling() {
      this.$emit("renling", this.itemData);
    },
    browse() {
      this.$emit("browse", this.itemData);
    },
    follow() {
      this.$emit("follow", this.itemData);
    },
    handleTel() {
      this.$emit("handleTel", this.itemData);
    },
    used() {
      this.$emit("used", this.itemData);
    },
    update(){
      this.$emit("update", this.itemData);
    },
    showCopywriting() {
      console.log(this.itemData);
      this.$ajax.get(
        "infoServicer/infoCopyCheck",
        {
          info_id: this.itemData.id,
          info_type: 1,
        },
        (res) => {
          if (res.data.code == 1) {
            const query = uni.createSelectorQuery().in(this);
            query
              .select("#copy-text")
              .fields(
                { rect: true, scrollOffset: true, size: true },
                (data) => {
                  this.text_popup_height = data.height + "px";
                }
              )
              .exec();
            this.copy_success = false;
            this.$refs.text_popup.show();
            // this.$refs.ershou.hide()
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        }
      );
    },
    copywriting() {
      this.$ajax.post(
        "infoServicer/infoCopy",
        {
          info_id: this.itemData.id,
          info_type: 1,
        },
        (res) => {
          if (this.type === "ershou") {
            const content = `${this.itemData.title}
${
  this.itemData.community_name
    ? "【小区】" + this.itemData.community_name + "\n"
    : ""
}【户型】${this.itemData.shi}室${this.itemData.ting}厅${this.itemData.wei}卫${
              this.itemData.mianji
            }m²
【售价】${this.itemData.fangjia ? this.itemData.fangjia + "万" : "面议"}${
              this.itemData.danjia
                ? " 单价" + this.itemData.danjia + "元/m²"
                : ""
            }
【类型】${this.itemData.catname}
【卖点】${
              this.itemData.label.length > 0
                ? this.itemData.label.map((item) => item.name).join(" ") + "\n"
                : ""
            }【楼层】${
              this.itemData.floor
                ? this.itemData.floor + "/共" + this.itemData.louceng + "层"
                : "共" + this.itemData.louceng + "层"
            }
${this.itemData.tel ? "【电话】" + this.itemData.tel : ""}`;
            this.copyContent(content, () => {
              this.copy_success = true;
            });
          }
          if (this.type === "renting") {
            const content = `${this.itemData.title}
${
  this.itemData.community_name
    ? "【小区】" + this.itemData.community_name + "\n"
    : ""
}【户型】${this.itemData.shi}室${this.itemData.ting}厅${this.itemData.wei}卫${
              this.itemData.mianji
            }m²
【租金】${this.itemData.zujin ? this.itemData.zujin + "元/月" : "面议"}
【类型】${this.itemData.catname}
【卖点】${
              this.itemData.label.length > 0
                ? this.itemData.label.map((item) => item.name).join(" ") + "\n"
                : ""
            }【楼层】${
              this.itemData.floor
                ? this.itemData.floor + "/共" + this.itemData.louceng + "层"
                : "共" + this.itemData.louceng + "层"
            }
${this.itemData.tel ? "【电话】" + this.itemData.tel : ""}`;
            this.copyContent(content, () => {
              this.copy_success = true;
            });
          }
        }
      );
    },
    // 复制内容
    copyContent(content, callback) {
      // #ifndef H5
      uni.setClipboardData({
        data: content,
        success: (res) => {
          if (callback) callback();
        },
      });
      // #endif
      // #ifdef H5
      let oInput = document.createElement("textarea");
      oInput.value = content;
      document.body.appendChild(oInput);
      oInput.style.opacity = 0;
      oInput.select(); // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand("Copy"); // 执行浏览器复制命令
      uni.showToast({
        title: "复制成功",
        icon: "none",
      });
      oInput.blur();
      oInput.remove();
      if (callback) callback();
      // #endif
    },
  },
};
</script>
