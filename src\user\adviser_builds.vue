<template>
  <view class="houe_list">
    <new-house-item v-for="item in list" :key="item.id" :itemData="item" @click="onClick"></new-house-item>
    <view class="btn-box">
      <view class="button" @click="$navigateTo('/user/bind_builds')">我已更换项目 立即更改</view>
    </view>
    <view class="tip">
      <view class="site_name">{{$store.state.siteName}}</view>
      <view class="tel-box flex-box" @click="makePhone">
        <my-icon type="ic_tel" color="#666"></my-icon>
        <text class="tel">{{service_tel}}</text>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
import newHouseItem from '../components/newHouseItem'
export default {
  components: {
    myIcon,
    newHouseItem
  },
  data () {
   return {
     list:[],
     service_tel: ''
    }
  },
  onLoad(){
    this.getData()
  },
  methods: {
    getData(){
      this.$ajax.get('adviser/adviserProject',{},res=>{
        this.getUserInfo()
        if(res.data.code === 1){
          this.list = res.data.list
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    },
    getUserInfo(){
      this.$ajax.get('member/index.html', {}, (res) => {
        if(res.data.code === 1){
          this.service_tel = res.data.tel
        }
      })
    },
    onClick(e){
      this.$navigateTo(`/pages/new_house/detail?id=${e.detail.id}`)
    },
    makePhone(){
      uni.makePhoneCall({
        phoneNumber: this.service_tel.replace(/\s*/g,'')
      })
    }
  }
}
</script>

<style scoped lang="scss">
.houe_list{
  background-color: #fff;
  padding: 0 48rpx;
}
.btn-box{
  padding: 24rpx 48rpx;
}
.button{
  line-height: 88rpx;
  border-radius: 44rpx;
  background-color: $uni-color-primary;
  box-shadow: 0 4px 10px 0 rgba(251,101,106,0.20);
  color: #fff;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
}
.tip{
  padding: 24rpx 48rpx;
  text-align: center;
  color: #666;
  .site_name{
    font-size: 32rpx;
    margin-bottom: 24rpx;
  }
  .tel-box{
    align-items: center;
    justify-content: center;
    .tel{
      margin-left: 16rpx;
    }
  }
}
</style>