<template>
  <view class="detail">
    <!-- <view class="swiper-box" v-if="is_share">
      <swiper vertical autoplay circular>
        <swiper-item>
          <view class="sharers_info flex-row">
            <image class="prelogo" mode="aspectFill" :src="sharers_info.prelogo"></image>
            <view class="info">
              <text class="cname">{{ sharers_info.cname }}</text>
              <text class="title">快来帮我看一下，哪一个户型更适合我？</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view> -->
    <view class="house-box flex-row" v-if="contrast_list.length === 2">
      <view class="item">
        <view class="label" v-if="is_share">A户型</view>
        <view class="house" @click="previewImage('a')">
          <image :src="contrast_list[0].path | imageFilter('w_240')" mode="aspectFill"></image>
          <view class="build_title">{{ contrast_list[0].title }}</view>
          <view class="desc">{{ contrast_list[0].desc }}</view>
          <view class="huxing">{{ `${contrast_list[0].house_type} ${contrast_list[0].mianji}m²` }}</view>
          <view class="btn flex-row" v-if="!is_share" @click.stop.prevent="$navigateBack()">
            <my-icon type="huanyihuan"></my-icon>
            <text>换一个</text>
          </view>
        </view>
      </view>
      <view class="pk-icon-box">
        <my-icon type="pk" color="#fff" size="28rpx"></my-icon>
      </view>
      <view class="item">
        <view class="label" v-if="is_share">B户型</view>
        <view class="house house2" @click="previewImage('b')">
          <image :src="contrast_list[1].path | imageFilter('w_240')" mode="aspectFill"></image>
          <view class="build_title">{{ contrast_list[1].title }}</view>
          <view class="desc">{{ contrast_list[1].desc }}</view>
          <view class="huxing">{{ `${contrast_list[1].house_type} ${contrast_list[1].mianji}m²` }}</view>
          <view class="btn flex-row" v-if="!is_share" @click.stop.prevent="$navigateTo('/contrast/search_house')">
            <my-icon type="huanyihuan"></my-icon>
            <text>换一个</text>
          </view>
        </view>
      </view>
    </view>
    <view class="recommend flex-row" v-if="contrast_list.length === 2 && is_share">
      <view class="left">
        <view class="prelogos" v-if="contrast_list[0].support_log.length>0||contrast_list[1].support_log.length>0">
          <image
            class="prelogo"
            v-for="(prelogo, index) in contrast_list[0].support_log"
            :key="index"
            mode="aspectFill"
            :src="prelogo | imageFilter('w_80')"
          ></image>
        </view>
        <view class="btn" @click="handleRecommend(contrast_list[0].img_id, 'a')">推荐A</view>
      </view>
      <view class="center flex-1">
        <view class="nums flex-row">
          <text class="num">{{contrast_list[0]&&contrast_list[0].support}}人</text>
          <text class="num">{{contrast_list[1]&&contrast_list[1].support}}人</text>
        </view>
        <view class="progress-box flex-row">
          <view class="progress progress1 flex-1" :style="contrast_list | getProportion(contrast_list[0].support,contrast_list[1].support, 'a')"></view>
          <view class="icon-box">
            <my-icon type="pk" color="#ff7c82" size="26rpx"></my-icon>
          </view>
          <view class="progress progress2 flex-1" :style="contrast_list | getProportion(contrast_list[0].support,contrast_list[1].support, 'b')"></view>
        </view>
        <view class="swiper-box" v-if="is_share">
          <swiper vertical autoplay circular>
            <swiper-item>
              <view class="sharers_info flex-row">
                <!-- <image class="prelogo" mode="aspectFill" :src="sharers_info.prelogo"></image> -->
                <view class="info">
                  <!-- <text class="cname">{{ sharers_info.cname }}</text> -->
                  <text class="title">哪个户型更适合我？</text>
                </view>
              </view>
            </swiper-item>
            <swiper-item>
              <view class="sharers_info flex-row">
                <!-- <image class="prelogo" mode="aspectFill" :src="sharers_info.prelogo"></image> -->
                <view class="info">
                  <!-- <text class="cname">{{ sharers_info.cname }}</text> -->
                  <text class="title">哪个户型更适合我？</text>
                </view>
              </view>
            </swiper-item>
          </swiper>
        </view>
      </view>
      <view class="right">
        <view class="prelogos" v-if="contrast_list[0].support_log.length>0||contrast_list[1].support_log.length>0">
          <image
            class="prelogo"
            v-for="(prelogo, index) in contrast_list[1].support_log"
            :key="index"
            mode=""
            :src="prelogo | imageFilter('w_80')"
          ></image>
        </view>
        <view class="btn btn_b" @click="handleRecommend(contrast_list[1].img_id, 'b')">推荐B</view>
      </view>
    </view>
    <view class="table-box flex-1">
      <scroll-view scroll-y @scroll="onTabScroll" @scrolltolower="onScrollBottom" :lower-threshold="20">
        <view class="table">
          <view class="tr">
            <view class="th text-center">户型信息</view>
          </view>
          <view class="tr flex-row">
            <view class="th">房型</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.house_type }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">面积</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.mianji }}m²</view>
          </view>
          <view class="tr flex-row">
            <view class="th">户型状态</view>
            <view class="td flex-row" v-for="(item, index) in contrast_list" :key="index">
              <text :class="'status' + item.status_id">{{ item.status === '暂未更新' ? '-' : item.status }}</text>
              <chatBtn v-if="is_open_im&&item.open_adviser" :login_status="login_status" @ok="getSendMsg($event, 1, item.bid)">
                <view class="btn_box flex-row">
                  <my-icon type="ic_zixun1" size="32rpx" color="#fff"></my-icon>
                  <text class="zx">咨询</text>
                </view>
              </chatBtn>
            </view>
          </view>
          <view class="tr flex-row">
            <view class="th">房型标签</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.label }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">总价</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.price }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">首付</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.sfje }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">贷款年限</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.dkm }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">月供</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.emTotal }}</view>
          </view>
          <view class="tr">
            <view class="th text-center">楼盘基础信息</view>
          </view>
          <view class="tr flex-row">
            <view class="th">楼盘名称</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.title }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">物业类型</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.other }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">区域板块</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.areaname }}</view>
          </view>
          <view class="tr flex-row" v-if="is_share">
            <view class="th">优惠信息</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">
              <view class="btn_box flex-row" @click="$navigateTo(`/pages/new_house/detail?id=${item.bid}`)">
                <text class="zx">领优惠</text>
              </view>
            </view>
          </view>
          <view class="tr flex-row">
            <view class="th">开盘时间</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.kptime }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">交房时间</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.rztime }}</view>
          </view>
          <view class="tr">
            <view class="th">建筑规划信息</view>
          </view>
          <view class="tr flex-row">
            <view class="th">建筑类型</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.build_type }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">产权年限</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.cqnx }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">容积率</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.dfl }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">绿化率</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.lhl }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">规划户数</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.total_hushu }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">车位数</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.tcw }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">装修情况</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.jiaofang }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">开发商</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.kfs }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">物业公司</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.wygs }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">物业费</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.wyf }}</view>
          </view>
        </view>
        <view class="tip" style="margin-bottom: 120rpx">免责声明：以上信息均为参考，实际情况以售楼处信息为准</view>
      </scroll-view>
      <!-- #ifdef H5 || APP-PLUS -->
      <view class="btn-box" v-if="huxing_contrast_id&&is_share" :class="{ show: show_share_btn }">
        <view class="btns flex-row">
          <view class="btn" @click="showShareTip">帮好友转发</view>
          <view class="btn active" @click="$navigateTo(`/pages/new_house/detail?id=${contrast_list[0].bid}&position=huxing`)">创建户型PK</view>
        </view>
      </view>
      <view class="share_btn" v-else-if="huxing_contrast_id" :class="{ show: show_share_btn }" @click="showShareTip"
        >分享好友帮我选</view
      >
      <view class="share_btn" v-else :class="{ show: show_share_btn }" @click="creatContrastId">生成分享链接</view>
      <!-- #endif -->
      <!-- #ifdef MP -->
      <view class="btn-box" v-if="huxing_contrast_id&&is_share">
        <view class="btns flex-row">
          <button open-type="share" class="btn">帮好友转发</button>
          <view class="btn active" @click="$navigateTo(`/pages/new_house/detail?id=${contrast_list[0].bid}&position=huxing`)">创建户型PK</view>
        </view>
      </view>
      <button class="share_btn" v-else-if="huxing_contrast_id" :class="{ show: show_share_btn }" open-type="share">
        分享好友帮我选
      </button>
      <view class="share_btn" v-else :class="{ show: show_share_btn }" @click="creatContrastId">生成分享链接</view>
      <!-- #endif -->
    </view>
    <shareTip :show="show_share" @hide="show_share = false"></shareTip>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
import shareTip from '../components/shareTip'
import chatBtn from '../components/open-button/chatBtn'
import { formatImg } from '../common/index.js'
import getChatInfo from '../common/get_chat_info'
export default {
  components: {
    myIcon,
    shareTip,
    chatBtn
  },
  data() {
    return {
      is_share: 0,
      contrast_list: [],
      show_share_btn: false,
      huxing_contrast_id: '',
      sharers_id: '',
      sharers_info: {},
      show_share: false
    }
  },
  computed: {
    login_status() {
      return this.$store.state.user_login_status
    },
    is_open_im() {
      //是否开通了聊天共功能
      return this.$store.state.im.ischat
    },
  },
  onLoad(options) {
    this.id1 = options.id1 || ''
    this.id2 = options.id2 || ''
    this.img_id1 = options.img_id1 || ''
    this.img_id2 = options.img_id2 || ''
    this.sharers_id = options.sharers_id || '' //分享者id
    this.huxing_contrast_id = options.huxing_contrast_id || '' //对比关系id
    this.is_share = options.is_share
    if (options.is_share && (!options.huxing_contrast_id||options.huxing_contrast_id==='undefined')) {
      console.log('没有创建户型对比关系就分享了,跳转到对比列表')
      uni.redirectTo({
        url: '/contrast/house_list'
      })
      return
    }
    // 如果传递户型id代表是未登录状态来的
    if(this.img_id1&&this.img_id2){
      this.getContrastDetail2(this.img_id1, this.img_id2)
      uni.$on('replaceHouse', data => {
        if(this.img_id1==data){
          setTimeout(()=>{
            uni.showToast({
              title:"此户型已在对比中",
              icon:'none'
            })
          }, 200)
          return
        }
        this.img_id2 = data
        this.getContrastDetail2(this.img_id1, this.img_id2)
      })
      return
    }
    this.getContrastDetail(this.id1, this.id2, this.huxing_contrast_id, this.sharers_id)
    uni.$on('replaceHouse', data => {
      if(this.id1==data){
        setTimeout(()=>{
          uni.showToast({
            title:"此户型已在对比中",
            icon:'none'
          })
        }, 200)
        return
      }
      this.id2 = data
      this.getContrastDetail(this.id1, this.id2)
    })
  },
  onUnload(){
    uni.$off('replaceHouse')
  },
  filters:{
    getProportion(contrast_list, support_a, support_b, type){
      if(contrast_list.length<2){
        if(type==='a'){
          return `min-width:50%;background-image:url(${formatImg('/images/new_icon/progress1.png', 'w_80')})`
        }
        if(type==='b'){
          return `min-width:50%;background-image:url(${formatImg('/images/new_icon/progress2.png', 'w_80')})`
        }
      }
      if(support_a==0&&support_b==0){
        if(type==='a'){
          return `min-width:50%;background-image:url(${formatImg('/images/new_icon/progress1.png', 'w_80')})`
        }
        if(type==='b'){
          return `min-width:50%;background-image:url(${formatImg('/images/new_icon/progress2.png', 'w_80')})`
        }
      }
      if(type==='a'){
        return `min-width:${support_a*100/(support_a+support_b)}%;background-image:url(${formatImg('/images/new_icon/progress1.png', 'w_80')})`
      }
      if(type==='b'){
        return `min-width:${support_b*100/(support_a+support_b)}%;background-image:url(${formatImg('/images/new_icon/progress2.png', 'w_80')})`
      }
    },
  },
  methods: {
    getContrastDetail(id1, id2, huxing_contrast_id, sharers_id) {
      let params = { contrast_id: id1 + ',' + id2 }
      if (huxing_contrast_id) params.huxing_contrast_id = huxing_contrast_id
      if (sharers_id) params.create_share_uid = sharers_id
      this.$ajax.get('build/contrast.html', params, res => {
        if (res.data.code !== 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
          return
        }
        if (res.data.share) this.share = res.data.share
        if (res.data.createShareUser) this.sharers_info = res.data.createShareUser
        this.contrast_list = res.data.list
        if(this.sharers_id&&this.huxing_contrast_id){
          this.setWxConfig()
        }
      })
    },
    getContrastDetail2(img_id1, img_id2){
      this.$ajax.get('build/contrastNoLogin.html',{img_ids:img_id1 + ',' + img_id2},res=>{
        if (res.data.code !== 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
          return
        }
        if (res.data.share) this.share = res.data.share
        this.contrast_list = res.data.list
      })
    },
    creatContrastId() {
      this.$store.state.allowOpen = true
      uni.showLoading({
        title: '正在生成',
        mask: true
      })
      let api = 'build/shareContrast.html'
      let params = {}
      if(this.id1&&this.id2){
        params = {contrast_id: this.id1 + ',' + this.id2}
      }
      if(this.img_id1&&this.img_id2){
        api = "build/addShareContrast.html"
        params = {img_ids: this.img_id1 + ',' + this.img_id2}
      }
      this.$ajax.get(api, params, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          this.huxing_contrast_id = res.data.huxing_contrast_id
          if (!this.sharers_id && res.data.create_share_uid) this.sharers_id = res.data.create_share_uid
          if(res.data.contrast_id){
            this.id1 = res.data.contrast_id.split(',')[0]
            this.id2 = res.data.contrast_id.split(',')[1]
          }
          this.setWxConfig()
          uni.showToast({
            title: '链接生成成功'
          })
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    setWxConfig(){
      const link = `${window.location.origin}/h5/contrast/house_detail?id1=${this.id1}&id2=${this.id2}&huxing_contrast_id=${this.huxing_contrast_id}&sharers_id=${this.sharers_id}&is_share=1`
      if(this.share){
        this.share.link = link
      }else{
        this.share = {
          link:link,
          pic: this.contrast_list[0].path,
          title:'快来帮我看一下，哪一个户型更适合我？',
          content:"我分享了一个户型对比投票，赶快来看看吧。"
        }
      }
      this.getWxConfig()
    },
    showShareTip() {
      this.show_share = true
      let path = `/contrast/house_detail?id1=${this.id1}&id2=${this.id2}&huxing_contrast_id=${this.huxing_contrast_id||''}&sharers_id=${this.sharers_id}&is_share=1`
      console.log(path)
    },
    getSendMsg(e, type, bid) {
      // #ifdef MP-WEIXIN
      this.$ajax.get('im/getUserReply.html', { page_from: type, bid: bid }, res => {
        if (res.data.mid) {
          this.$store.state.autoSendMsg = res.data.content || ''
          getChatInfo(res.data.mid, 3)
        }
      })
      // #endif
      // #ifndef MP-WEIXIN
      this.$ajax.get('member/checkUserStatus', {}, res => {
        if (res.data.code === 1) {
          this.$ajax.get('im/getUserReply.html', { page_from: type, bid: bid }, res => {
            if (res.data.mid) {
              this.$store.state.autoSendMsg = res.data.content || ''
              console.log(this.$store.state.autoSendMsg)
              getChatInfo(res.data.mid, 3)
            }
          })
        } else {
          this.$navigateTo('/user/login/login')
          // this.$store.state.user_login_status = res.data.status
          // this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
          // this.$refs.login_popup.showPopup()
        }
      })
      // #endif
    },
    handleRecommend(img_id, type) {
      this.$ajax.get('build/supportContrast.html', { huxing_contrast_id: this.huxing_contrast_id, img_id }, res => {
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg
          })
          if(type === 'a'){
            this.contrast_list[0].support++
            this.contrast_list[0].support_log.push(res.data.prelogo)
          }
          if(type === 'b'){
            this.contrast_list[1].support++
            this.contrast_list[1].support_log.push(res.data.prelogo)
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    previewImage(type){
      if(type === 'a'){
        uni.previewImage({
          urls:[formatImg(this.contrast_list[0].path, 'w_860')],
          current: 0
        })
      }
      if(type === 'b'){
        uni.previewImage({
          urls:[formatImg(this.contrast_list[1].path, 'w_860')],
          current: 0
        })
      }
    },
    onTabScroll(e) {
      if(!this.is_share){
        this.show_share_btn = true
      }
      if(e.detail.scrollTop<=60){
        this.show_share_btn = false
      }
    },
    onScrollBottom(e){
      this.show_share_btn = true
    }
  }
}
</script>

<style scoped lang="scss">
.detail {
  height: calc(100vh - 44px);
  padding: 24rpx 48rpx;
  background-color: #fff;
}
view {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.swiper-box{
  // position: fixed;
  // left: 24rpx;
  // right: 72rpx;
  // top: 44px;
  // z-index: 98;
  // border-top-right-radius:16rpx;  
  // border-bottom-right-radius:16rpx;  
  swiper{
    height: 70rpx;
    color: #666;
  }
}

.sharers_info {
  .prelogo {
    width: 64rpx;
    height: 64rpx;
    border-radius: 32rpx;
    margin-right: 16rpx;
  }
  .info {
    flex: 1;
    overflow: hidden;
    .cname {
      line-height: 1;
      margin-bottom: 16rpx;
      font-size: 26rpx;
      color: #333;
    }
    .title {
      text-align: center;
      font-size: 22rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 10rpx 10rpx;
      box-sizing: border-box;
      // border-radius: 8rpx;
      // background-color: rgba(#000, 0.5);
    }
  }
}

.recommend{
  margin-top: 48rpx;
  justify-content: space-between;
  align-items: center;
  .prelogos{
    display: block;
    margin-bottom: 24rpx;
    height: 48rpx;
    .prelogo{
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      float: left;
      ~.prelogo{
        margin-left: -24rpx;
      }
    }
    .prelogo2{
      float: right;
      ~.prelogo2{
        margin-right: -24rpx;
      }
    }
  }
  .center{
    margin: 0 24rpx;
    .nums{
      justify-content: space-between;
      font-size: 24rpx;
      color: #999;
    }
    .progress-box{
      align-items: center;
      width: 100%;
    }
    .progress{
      height: 24rpx;
      border-radius: 12rpx;
      background-size: auto 100%;
      background-repeat: repeat-x;
      &.progress1{
        margin-right: -20rpx;
      }
      &.progress2{
        margin-left: -20rpx;
      }
    }
    .icon-box{
      position: relative;
      z-index: 1;
      justify-content: center;
      align-items: center;
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      border: 1rpx solid #f5f5f5;
      background-color: #fff;
    }
  }
  .btn{
    line-height: 64rpx;
    border-radius: 32rpx;
    padding: 0 32rpx;
    background-color: #ff7c82;
    color: #fff;
    &.btn_b{
      background-color: #8795f5;
    }
  }
}

.house-box {
  align-items: center;
  justify-content: space-between;
  .label {
    text-align: center;
    margin-bottom: 10rpx;
    color: #333;
  }
  .house {
    padding: 24rpx;
    border-radius: 16rpx;
    line-height: 1;
    background-color: #ff7c81;
    color: #fff;
    box-shadow: 0 4px 8px 0 rgba(251, 101, 106, 0.2);
    > image {
      width: 232rpx;
      height: 128rpx;
      border-radius: 8rpx;
    }
    .build_title {
      margin-top: 24rpx;
      font-size: 32rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .desc {
      font-size: 24rpx;
      margin-top: 16rpx;
    }
    .huxing {
      font-size: 24rpx;
      margin-top: 16rpx;
    }
    .btn {
      margin-top: 24rpx;
      align-items: center;
      justify-content: center;
      line-height: 48rpx;
      padding: 0 24rpx;
      border-radius: 24rpx;
      text-align: center;
      font-size: 24rpx;
      color: #333;
      background-color: #fff;
    }
    &.house2 {
      background-color: #8997fa;
      box-shadow: 0 4px 10px 0 rgba(139, 153, 255, 0.2);
    }
  }
  .pk-icon-box {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    background: $uni-color-primary;
    box-shadow: 0 2px 8px 0 rgba($uni-color-primary, 0.4);
  }
}

.status1 {
  color: #4cc7f6;
}
.status2 {
  color: #00caa7;
}
.status3 {
  color: #ff7213;
}
.status4 {
  color: #666;
}

.table-box {
  margin-top: 72rpx;
  overflow: hidden;
  position: relative;
  > scroll-view {
    height: 100%;
  }
}

.table {
  line-height: 1.5;
  border: 1rpx solid #d8d8d8;
  border-bottom: 0;
  color: #333;
  .tr {
    .td {
      flex: 1;
    }
  }
  .th {
    justify-content: center;
    min-width: 160rpx;
    padding: 20rpx;
    text-align: center;
    font-size: 28rpx;
    background-color: #f5f5f5;
    border-bottom: 1rpx solid #d8d8d8;
    color: #666;
  }
  .td {
    justify-content: center;
    padding: 20rpx;
    text-align: center;
    font-size: 28rpx;
    border-bottom: 1rpx solid #d8d8d8;
    border-left: 1rpx solid #d8d8d8;
    .btn_box {
      margin-left: 10rpx;
      justify-content: center;
      align-items: center;
      font-size: 22rpx;
      color: #fff;
      height: 46rpx;
      border-radius: 23rpx;
      padding: 0 10rpx;
      background: linear-gradient(90deg, #fb656a 30%, #fbac65 100%);
      > .zx {
        margin-left: 10rpx;
      }
    }
  }
}

.share_btn {
  position: absolute;
  bottom: -88rpx;
  left: 0;
  right: 0;
  display: block;
  margin: 0;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  color: #fff;
  background: $uni-color-primary;
  box-shadow: 0 4px 12px 0 rgba($uni-color-primary, 0.4);
  transition: 0.36s;
  &.show {
    bottom: 24rpx;
  }
}

.tip {
  padding: 24rpx 0;
  color: #999;
  font-size: 24rpx;
}

.btn-box{
  padding: 0;
  position: absolute;
  bottom: -118rpx;
  left: 0;
  right: 0;
  display: block;
  transition: 0.36s;
  &.show {
    bottom: 24rpx;
  }
  .btns{
    line-height: 88rpx;
    border-radius: 44rpx;
    border: 1rpx solid $uni-color-primary;
    overflow: hidden;
    box-shadow: 0 4px 10px 0 rgba(251,101,106,0.20);
    .btn{
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      color: $uni-color-primary;
      background-color: #fff;
      &.active{
        background-color: $uni-color-primary;
        color: #fff;
      }
    }
  }
}
</style>
