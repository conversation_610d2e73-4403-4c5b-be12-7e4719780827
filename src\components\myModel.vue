<template>
<view class="model" @touchmove.stop.prevent="bindTouchmove">
<view class="model_box" v-show="is_show">
    <view class="model_title">{{title}}</view>
    <view class="model_content">
        <template v-if="content">
            <text>{{content}}</text>
        </template>
        <template v-else>
            <slot />
        </template>
    </view>
    <view class="model_btn_box top-line">
        <view v-for="(btn, index) in btns" :key="index" @click="clickBtn(index)" class="btn right-line" :style="{color:btn.color}">{{btn.name}}</view>
    </view>
</view>
<view class="mask" :class="{show:is_show}"></view>
</view>
</template>

<script>
export default {
    props:{
        is_show: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: '提示'
        },
        content: {
            type: String,
            default: ''
        },
        btns: {
            type: Array,
            default: [{name: "取消", color: "#666666"}, {name:"确定", color: "#f65354"}]
        }
    },
    data() {
        return {

        }
    },
    methods: {
        clickBtn(index){
            this.$emit('clickBtn',index)
        },
        bindTouchmove(){}
    }
}
</script>

<style lang="scss">
.model_box{
    position: fixed;
    z-index: 999;
    width: 80%;
    max-width: 300px;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    background-color: #fff;
    text-align: center;
    border-radius: 20rpx;
    overflow: hidden;
    .model_title{
        padding: 40upx 50upx 10upx 50upx;
        box-sizing: border-box;
        font-size: 36upx;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #555
    }
    .model_content{
      padding: 36upx 46upx;
      min-height: 40px;
      font-size: 15px;
      line-height: 1.4;
      color: #999;
      max-height: 400px;
      text-align: left;
      overflow-y: auto;
    }
    .model_btn_box{
      position: relative;
      line-height: 96upx;
      font-size: 36upx;
      display: flex;
      box-sizing: border-box;
      .btn{
        flex: 1;
        font-size: 32upx;
      }
    }
    .mask{
      transition: 0.3s;
      position: fixed;
      width: 100%;
      top: 0;
      bottom: 0;
      z-index: -1;
      &.show{
        background-color: rgba($color: #000000, $alpha: 0.6);
        z-index: 998;
      }
    }
}
</style>
