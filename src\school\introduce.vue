<template>
  <view>
    <view class="title">{{ title }}</view>
    <view class="content">
      <!-- #ifdef H5 -->
      <view class="article-content" v-html="content"></view>
      <!-- #endif -->
      <!-- #ifndef H5 -->
      <view class="article-content">
        <u-parse
          :html="content"
          @linkpress="navigate"
          :tag-style="tagStyle"
        ></u-parse>
      </view>
      <!-- #endif -->
    </view>
  </view>
</template>

<script>
import uParse from '../components/Parser/index'
import wxApi from '../common/mixin/wx_api';
import {formatImg} from '../common/index.js'
export default {
  components: { uParse },
  mixins:[wxApi],
  data() {
    return {
      title: "",
      content: "",
      tagStyle: {
          video: 'max-width:100%',
          p:'font-size:17px'
      }
    };
  },
  onLoad(options) {
    if (options.school_id) {
      this.school_id = options.school_id;
      this.getData();
    }
  },
  methods: {
    getData() {
      this.$ajax.get(
        "school/schoolInfo.html",
        { id: this.school_id },
        (res) => {
          uni.setNavigationBarTitle({
            title:res.data.data.name?res.data.data.name+'简介':"学校简介"
          })
          if (res.data.code === 1) {
            this.title = res.data.data.name + "简介";
            this.content = res.data.data.content;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
            });
          }
          if(res.data.share){
            this.share = res.data.share
            this.getWxConfig()
          }
        }
      );
    },
    navigate(href) {
      console.log(href);
      // navigateTo(href)
    }
  },
  onShareAppMessage() {
    if (this.share) {
      return {
        title: this.share.title||"",
        content:this.share.content||"",
        imageUrl: this.share.pic?formatImg(this.share.pic, 'w_6401'):""
      }
    }
  }
};
</script>

<style scoped lang="scss">
view{
  display: flex;
  flex-direction: column;
}
.title {
  display: block;
  padding: 24rpx;
  line-height: 1.5;
  margin-bottom: 30rpx;
  text-align: center;
  font-size: 40rpx;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.article-content {
  padding: 24rpx 40rpx;
  line-height: 1.6;
  font-size: 32rpx;
  font-family: -apple-system-font, BlinkMacSystemFont, 'Helvetica Neue',
    'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei',
    Arial, sans-serif;
  letter-spacing: 0.544px;
  white-space: normal;
  p {
    min-height: 1em;
    margin-bottom: 30rpx;
  }
  img {
    max-width: 100%;
  }
}
</style>
