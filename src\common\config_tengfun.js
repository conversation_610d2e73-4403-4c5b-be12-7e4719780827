const config = {
	projectName:"腾房云",
	// #ifdef H5
	apiDomain: "", //H5api接口
	// #endif
	// #ifndef H5
	apiDomain: "https://www.tengfun.com", //小程序api接口
	// #endif
	uploadApi: "house/uploadFile.html", //上传文件api
	apiBase:'/wapi/',
	imSocket: "wss://imcloud.tengfangyun.com:9002",
	imgDomain: "https://images.tengfun.com", //图片域名 //images.tengfangyun.com images.tengfun.com
	thumbParam:"?x-oss-process=style/", //oss图片缩放参数
	ossSnapshot:"?x-oss-process=video/snapshot,t_1000,f_jpg,m_fast", //视频截帧参数
	modalBtnColor:"#f65354", //弹窗确定按钮颜色
	anti_shake_api: { //防抖api
		list: ['member/getOpenidByCode', 'member/getWxPhoneNumber', 'member/loginByBaiduUnion'],
		delay: 500 //请求成功后多久可以再次请求
	}
}
if(process.env.NODE_ENV === 'development'){
	// #ifdef H5
	config.apiDomain = "";
	// #endif
}
module.exports = {config}