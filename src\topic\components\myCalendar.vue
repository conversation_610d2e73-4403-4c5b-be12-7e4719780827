<template>
  <view class="time_picker">
    <view class="header">
      <view class="month">
        <text>{{current_time_string.year}}年{{current_time_string.month}}月</text>
      </view>
      <view class="icon-box">
        <image mode="widthFix" :src="'/images/new_icon/zaobao/yuyue.png' | imageFilter('m_240')"></image>
      </view>
    </view>
    <view class="day_box">
      <view class="week_list">
        <text class="week" v-for="(week, index) in weeks" :key="index">{{week}}</text>
      </view>
      <view class="day_list">
        <div class="day" v-for="(day, index) in times" :key="index" @click="onClickDay(day)">
          <text :class="{invalid:day.invalid, active:(day.year == current_time_string.year&&day.month == current_time_string.month&&day.day == current_time_string.day)}">{{day.day}}</text>
        </div>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  components: {

  },
  data () {
   return {
     weeks:[
       '日',
       '一',
       '二',
       '三',
       '四',
       '五',
       '六',
     ],
     active_day: new Date()
    }
  },
  props:{
    days:{
      type: Array,
      default:()=>{return []}
    },
    // 当前选中的天
    current_time: {
      type: [Date, String, Number],
      default: ()=>{
        return new Date()
      }
    }
  },
  watch:{
    current_time(val){
      this.active_day = new Date(val)
    }
  },
  created(){
    this.active_day = new Date(this.current_time)
  },
  computed:{
    current_time_string(){
      let active_day = this.active_day
      return this.getTime(active_day)
    },
    times(){
      if(this.days.length === 0){
        return []
      }
      let days = this.days
      let first_day = days[0]
      let first_week = first_day.week
      if(first_week>0){
        while (first_week>0){
          let before_time = Date.parse(first_day.time) - (3600*1000*24*(first_day.week-first_week+1))
          const time = new Date(before_time)
          let before_day = this.getTime(time)
          days.unshift(before_day)
          first_week --
        }
      }
      let last_day = days[days.length-1]
      let last_week = last_day.week
      if(last_week<6){
        while (last_week<6){
          let after_time = Date.parse(last_day.time) + (3600*1000*24*(last_week - last_day.week+1))
          const time = new Date(after_time)
          let after_day = this.getTime(time)
          days.push(after_day)
          last_week ++
        }
      }
      return days
    }
  },
  methods: {
    getTime(time){
      let year = time.getFullYear()
      let original_month = time.getMonth()
      let month = original_month+1>=10?original_month+1:'0'+(original_month+1)
      let day = time.getDate()
      let week = time.getDay()
      return {
        invalid: true,
        time,
        year,
        month,
        day,
        week
      }
    },
    onClickDay(e){
      if(e.invalid) return
      this.active_day = new Date(e.time)
      this.$emit('select', e)
    }
  }
}
</script>

<style scoped lang="scss">
.time_picker{
  margin-top: 68rpx;
}
.header{
  height: 112rpx;
  padding: 32rpx 24rpx;
  box-sizing: border-box;
  background-image: linear-gradient(125deg, #FF5500 0%, #FFA402 100%);
  border-radius: 16rpx 16rpx 0 0;
  position: relative;
  .month{
    padding: 8rpx 16rpx;
    line-height: 1;
    border-left: 8rpx solid #fff;
    font-size: 36rpx;
    font-weight: bold;
    color: #fff;
  }
  .icon-box{
    position: absolute;
    right: 48rpx;
    top: -68rpx;
    image{
      width: 210rpx;
    }
  }
}

.day_box{
  background-color: #fff;
  padding: 32rpx 24rpx;
  border-radius: 0 0 16rpx 16rpx;
}
.week_list{
  display: flex;
  margin-bottom: 24rpx;
  .week{
    width: 14.28%;
    text-align: center;
    color: #999;
  }
}
.day_list{
  display: flex;
  flex-wrap: wrap;
  .day{
    width: 14.28%;
    padding: 12rpx;
    box-sizing: border-box;
    text-align: center;
    color: #666;
    >text{
      display: inline-block;
      width: 64rpx;
      height: 64rpx;
      line-height: 64rpx;
      border-radius: 50%;
      transition: 0.16s;
    }
    .invalid{
      color: #d8d8d8;
    }
    .active{
      color: #fff;
      background-image: linear-gradient(125deg, #FF5500 0%, #FFA402 100%);
      box-shadow: 0 4rpx 12rpx 0 rgba(255,145,1,0.50);
    }
  }
}
</style>