import {
    ajax,
    showModal,
    config
  } from './index'
  import store from '../store/index'
  
  var num = 0;
  let content = "", telnum = 0, type = ""
  /**
   * 中间号拨打
    * @param {string} type   被叫类型：0个人,1楼盘,2置业顾问,3经纪人,4住宅信息,5家装,6商业信息，(预留被叫类型：7小区)
    * @param {string} callee_id   对应的被叫id
    * @param {string} scene_type   
    *   场景类型：1楼盘,2置业顾问,3经纪人,4住宅信息,5家装,6商业信息,7小区
        大部分情况下，场景类型与被叫类型相同。个别情况有所不同，示例如下：
        1、在楼盘页拨打挂载的置业顾问电话，被叫类型为 2，场景类型为 1；
        2、在小区页拨打挂载的经纪人电话，被叫类型为 3，场景类型为 7；
        3、经纪人转发二手房信息，拨打的为经纪人电话，此时被叫类型为 3，场景类型为 4。
    * @param {string} scene_id   对应的场景id
    * @param {string} source  
    *   来源页标识：build_detail(楼盘详情页)，
        adviser_list(列表页)，adviser_detail(详情页)
        agent_list(列表页)， agent_detail(详情页)
    * @param {string||number} bid  经纪人转发楼盘时
    * @param {string||number} isstatis  是否统计 默认统计 不统计的暂时为400号码
   */
  const encryptionTel = function(options={}) {
    let {type,callee_id,scene_type,scene_id,source,bid,isstatis=1,} = options
    store.state.allowOpen = true
    type = type
    uni.showLoading({
      title: "请稍侯"
    })
    var params = {
      type: type, 
      callee_id: callee_id, 
      scene_type: scene_type,
      scene_id: scene_id, 
      source: source, 
      bid: bid,
    }
    ajax.get("call/getVirtualNumber", params, res => {
      if (res.data.code == 1) {
        uni.hideLoading()
        if (res.data.realNumber) { //如果是真实号码则直接拨打
          if ( params.type === 3 || params.type === 6) {
            showModal({
              title: '友情提示',
              content: "接通后，提及在" + (store.state.siteName || config.projectName) + "看到的信息，获得更好的服务！",
              confirm: () => {
                uni.makePhoneCall({
                  phoneNumber: res.data.realNumber
                });
                if (isstatis == 1) {
                  statistics(callee_id, scene_type, scene_id)
                }
              }
            })
          } else {
            uni.makePhoneCall({
              phoneNumber: res.data.realNumber
            });
            if (isstatis == 1) {
              statistics(callee_id, scene_type, scene_id)
            }
          }
          return
        }
        if(options.success){
          res.data.from = scene_type
          res.data.mid = callee_id
          res.data.info_id = scene_id
          res.data.isstatis = isstatis
          options.success(res)
          return
        }
        if (res.data.expireSeconds) {
          num = res.data.expireSeconds + "秒后未拨打请返回重新拨打"
        } else {
          num = ""
        }
  
        uni.hideLoading();
        telnum = res.data.virtualNumber;
        if (type == 3) {
          content = res.data.tip||("接通后，提及在" + (store.state.siteName || config.projectName) + "看到的信息，获得更好的服务！")
        } else {
          content = res.data.tip ||("拨通后请说明来自" + (store.state.siteName || config.projectName) + " 本次通话已进行加密 " + num)
        }
        showModal({
          title: '友情提示',
          content: content,
          confirm: () => {
            uni.makePhoneCall({
              phoneNumber: telnum
            });
            if (isstatis == 1) {
              statistics(callee_id, scene_type, scene_id)
            }
  
          }
        })
      } else if (res.data.code == 101) { //已经获取了且号码还有效
        uni.hideLoading();
        if (options.success) {
          res.data.from = scene_type
          res.data.mid = callee_id
          res.data.info_id = scene_id
          res.data.isstatis = isstatis
          options.success(res)
          return
        }
        telnum = res.data.virtualNumber;
        uni.makePhoneCall({
          phoneNumber: telnum
        });
        if (isstatis == 1) {
          statistics(callee_id, scene_type, scene_id)
        }
  
      } else if (res.data.code === -1 || res.data.code === 2||res.data.code === -5|| res.data.code === -10){
        uni.hideLoading();
        options.fail && options.fail(res)
      } else {
        uni.hideLoading();
        uni.showToast({
          title: res.data.msg,
          icon: 'none'
        })
      }
    }, err => { }, { disableAutoHandle: options.intercept_login||false})
    
  }
  const statistics = (callee_id, scene_type, scene_id) => {
    ajax.get("im/callUpStatistics", {
      id: callee_id,
      type: scene_type,
      scene_id
    }, res => {
      console.log(res.data);
    }, err => {
      console.log(err)
    }, { disableAutoHandle: true })
  }
  module.exports = encryptionTel