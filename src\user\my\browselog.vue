<template>
    <view class="page">
        <view class="tips" v-if="show">
            <text class="spantext">列表仅显示近<text class="tipstext">30</text>天，前<text class="tipstext">50</text>条动态记录</text>
        </view>
        <scroll-view scroll-y class="browselog flex-1" @touchmove.stop.prevent="stopMove">
            <view class="time_line">
                <view class="item" :class="{ current: item.is_finish !== 0 }" v-for="(item, index) in lineData"
                    :key="index">
                    <view class="time">{{ item.ctime }}</view>
                    <!-- <view class="title">业主姓名：{{ item.cname }}</view>
                    <view class="title">业主电话：{{ item.owner_tel_cc }}</view> -->
                    <view class="labels">
                        <text class="cx">{{ item.content }}</text>
                    </view>
                </view>
                <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
            </view>
        </scroll-view>

    </view>
</template>

<script>
import timeLine from '../../components/timeLine.vue'
import { uniLoadMore } from '@dcloudio/uni-ui'
import { mapState } from 'vuex'
const recorderManager = uni.getRecorderManager();
const innerAudioContext = uni.createInnerAudioContext();
export default {
    data() {
        return {
            show: false,
            lineData: [],
            nodata: false,
            tel_log_page: 1,
            play_voice_index: -1,
            get_status: "loading",
            content_text: {
                contentdown: "",
                contentrefresh: "正在加载...",
                contentnomore: "还没有访问记录~~"
            },
            info_id: '',
            cate_id: '',
            rows: 10,
            page: 1
        }
    },
    components: {
        timeLine,
        uniLoadMore,
    },
    computed: {
        ...mapState(['im'])
    },
    onLoad(options) {
        this.info_id = options.id
        this.cate_id = options.cate_id
        this.getData()
    },
    filters: {

    },
    methods: {
        getData() {
            this.get_status = 'loading'
            this.$ajax.get('member/houseBrowseLog', { cate_id: this.cate_id, info_id: this.info_id }, res => {
                if (res.data.code === 1) {
                    if (res.data.list.length < 10) {
                        this.nodata = true
                        this.get_status = "noMore"
                    } else {
                        this.nodata = false
                        this.get_status = "more"
                    }
                    this.lineData = res.data.list
                    if (this.lineData.length > 0) {
                        this.content_text.contentnomore = ""
                        this.show = true
                    }
                    else {
                        this.show = false
                    }
                } else {
                    this.nodata = true
                    this.get_status = "noMore"

                }

            })
        },
        stopMove() {

        },
    },
    onUnload() {
        innerAudioContext.stop()
    },

}
</script>

<style lang="scss" scoped>
.page {
    min-height: 100vh;
    background-color: #fff;
    padding: 0rpx 20rpx;

    .tip {
        margin-top: 30upx;
        padding: 24upx 20upx;
        font-size: 28upx;
        text-align: center;
        color: #999;
    }

    .browselog {
        max-height: 95vh;
        overflow-y: scroll;
        transition: 0.26s;
    }

    .time_line {
        padding: 30upx 30upx;

        .item {
            position: relative;
            padding: 0 20upx 36upx 32upx;
            border-left: 4upx solid #3399ff;

            .title {
                font-size: 28upx;
                margin-bottom: 15upx;
                line-height: 1.5;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                display: -webkit-box;
            }

            .time {
                font-size: 24upx;
                font-weight: bold;
                color: #999;
                margin-bottom: 10upx;
            }
        }

        .item::after {
            content: "";
            height: 36upx;
            width: 36upx;
            box-sizing: border-box;
            border-radius: 50%;
            position: absolute;
            border: 4rpx solid #3399ff;
            background-color: #fff;
            left: -20upx;
            top: -8rpx;
        }

        .current::before {
            content: "";
            height: 20upx;
            width: 20upx;
            border-radius: 50%;
            background-color: #3399ff;
            position: absolute;
            left: -12upx;
            top: 0;
            z-index: 2;
        }

        .iconws {
            color: rgb(240, 86, 86);
            border-color: rgb(240, 86, 86);
        }

        .iconys {
            color: #29de18;
            border-color: #29de18;
        }

        .labels {
            margin-top: 16rpx;
            line-height: 1.5;


            .label {
                display: inline-block;
                line-height: 1;
                font-size: 24rpx;
                padding: 4rpx 8rpx;
                border: 1rpx solid #d8d8d8;
                border-radius: 4rpx;
                vertical-align: middle;
                margin-right: 10rpx;

                ~.label {
                    margin-left: 16rpx;
                }
            }
        }
    }
}

.tips {
    display: inline-block;
    width: 100%;
    padding: 16rpx 0rpx;

    .spantext {
        display: block;
        border-radius: 8rpx;
        background-color: #FFF0E8;
        color: #FE7B2E;
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 40rpx;
        width: 70%;
        margin: 0 auto;
        text-align: center;
        padding: 10rpx 0rpx
    }

    .tipstext {
        color: #FE7B2E;
        font-size: 30rpx;
        font-weight: 500;
    }
}
</style>
