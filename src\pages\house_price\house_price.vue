<template>
  <view class="page">
    <view class="back  flex-row">
      <view class="icon-box flex-row"  @click="goBack">
          <my-icon type="ic_back" color="#fff" size="48rpx"></my-icon>
      </view>
      <!-- <text class ="title-con" >{{detail.cname}}</text> -->
    </view>
      <view class="adv">

        <view class="swiper_container">
          <view class="swiper">
            <my-swiper height="424rpx" :focus="adv" :rounded="false" :autoplay="true" :interval="4000" :indicatorDots="true" :circular="true" indicatorActiveColor="#ffffff"></my-swiper>
          </view>
        </view>
        <view class="search">
          <view class="search-left"  >
            <!-- <view> -->
              <my-icon type="ic_sousuo" color="#b9b9b9" size="36rpx"></my-icon>
              <input v-model="params.keyword" readonly   placeholder="请输入小区名称" placeholder-style="color: #b9b9b9" style="font-size: 28rpx"  @click="onSearch()" />
            <!-- </view> -->
          </view>
          <view class="search-right"  @click ="goPropertyData">
            <text class="add-text">查房价</text>
          </view>
        </view>

      </view>
      <view class="filter-box  flex-box" id="tab_top">
        <view
          class="filter-item"
          v-for="item in filter_list"
          :key="item.type"
          @click="click_c(item)"
        >
          <text>{{ item.name }}</text>
          <my-icon type="ic_down" size="24rpx" color="#d8d8d8"></my-icon>
        </view>
    <scroll-view
      scroll-y
      class="screen-panel"
      v-if ="showTab"
      :style="{paddingTop:status_top+'px'}"
      :class="show_filter_type == 1 ? 'show' : ''"
      @touchmove.stop.prevent="stopMove"
    >
    <addressd :addressd="area_list" ref="showArea" @changes="changeArea"></addressd>
    </scroll-view>
    <scroll-view
      scroll-y
      class="screen-panel"
      :style="{paddingTop:status_top+'px'}"
      :class="show_filter_type == 2 ? 'show' : ''"
      @touchmove.stop.prevent="stopMove"
    >
      <block v-for="item in price_list" :key="item.value">
        <uni-list-item
          :title="item.name"
          show-arrow="false"
          @click="switchTab(item.value, item.name)"
        ></uni-list-item>
      </block>
    </scroll-view>
     <scroll-view
      scroll-y
      class="screen-panel"
      :style="{paddingTop:status_top+'px'}"
      :class="show_filter_type == 3 ? 'show' : ''"
      @touchmove.stop.prevent="stopMove"
    >
      <block v-for="item in distance_list" :key="item.value">
        <uni-list-item
          :title="item.name"
          show-arrow="false"
          @click="switchTab(item.value, item.name)"
        ></uni-list-item>
      </block>
    </scroll-view>
     </view>
    <template v-if ="showList">
    <view class="list" :class ="{'house_little_list':likeData.length==0||!showLike}" :style='{paddingTop:padTop}' v-if ="list.length>0" >
      <house-price
        v-for="(item, index) in list"
        :key="index"
        :itemData="item"
        :showDistance="show_distance"
      ></house-price>
      <uni-load-more
        :status="get_status"
        :content-text="content_text"
      ></uni-load-more>
    </view>
    <view class="list"  :style='{paddingTop:padTop}' v-else>
      <view class="no_data flex-row">
        <view class="icon flex-row">
          <myIcon type="tishifu" fontSize ="30"></myIcon>
        </view>
        <view class="no_data_text">
          没有找到符合条件的小区 换个条件试试吧
        </view>
      </view>
		</view>
    <view class="list no_pad" v-if ="likeData.length>0&&showLike">
      <view class="xihuan flex-row">
        <view class="line flex-1">
        </view>
        <view class="xihuan_text">
          猜你喜欢
        </view>
        <view class="line flex-1">

        </view>
      </view>
      <house-price
          v-for="(item, index) in likeData"
          :key="index"
          :itemData="item"
          :showDistance="show_distance"
        ></house-price>
    </view>

    </template>

    <my-dialog
      ref="dialog"
      @cancelButton="getData"
      :show="show_dialog"
      @close="show_dialog = false"
      title="温馨提示"
      openType="openSetting"
    >
      <view class="set-nickname-box">
        <view class="row">只有获取位置权限才能获取附近的房源</view>
      </view>
    </my-dialog>
    <view
      class="mask"
      :class="show_filter_type>0 ? 'show' : ''"
      @click="()=>{show_filter_type = 0
          showPannel = false}"
      @touchmove.stop.prevent="stopMove"
    ></view>
    <chat-tip></chat-tip>
  </view>
</template>

<style scoped lang="scss">
.page {
  background-color: #fff;
}
view{
  box-sizing: border-box;
}
.flex-row{
  display: flex;
  flex-direction: row;
}
.search-box {
  margin-left: 20rpx;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #eeeeee;
  border-radius: 8rpx;
  input{
    text-align: left;
    font-size: 28rpx;
  }
  .search-left {
    margin-right: 20rpx;
  }
}
.map-icon{
  width: 40rpx;
  height: 40rpx;
}
// #ifdef MP
.search-box-right {
  width: 210rpx;
}
// #endif
.filter-box {
  justify-content: space-around;
  align-items: center;
  border-bottom: 1rpx solid #dedede;
  background-color: #fff;
  width: 100%;
  position: sticky;
  // #ifndef H5
  top: var(--window-top);
  // #endif
   // #ifdef H5
  top: 0;
  // #endif
  z-index: 99;
  .filter-item {
    padding: 20rpx;
    color: #333;
  }
}
 // #ifdef H5
.screen-panel {
  top: 0;
  margin-top: 70rpx;
  display: none;
}
.screen-panel.show {
  top: 0;
  left: 0;
  display: block;
}
// #endif
// #ifndef H5
.screen-panel {
  top: var(--window-top);
  margin-top: 90rpx;
  left: 0;
  // display: none;
  transform: translateY(-130%);
  &.show{
    transform: translateY(0);
    // display: block;
  }
}
// #endif
.list {
  // padding-top: 80rpx;
  &.house_little_list{
    min-height: 100vh;
  }
  &.no_pad{
    padding-top: 0;
  }
  .no_data{
			justify-content: center;
			align-items: center;
			padding: 40rpx 0;
			.no_data_text{
				font-size: 30rpx;
				color: #333;
				margin-left: 10rpx;

			}
      .icon{
        justify-content: center;
        align-items: center;
      }
		}
		.xihuan{
			justify-content: center;
			align-items: center;
			padding: 0 0 20rpx ;
			.xihuan_text{
				margin: 0 20rpx;
				color: #968E9F;
				font-size: 30rpx;

			}
			.line{
				height: 2rpx;
				background:#E1E3EE;
			}
		}
}
 .adv {
    position: relative;
    margin-bottom: 66rpx;
    .adv-img {
      height: 284rpx;
      image {
        width: 100%;
        height: 284rpx;
      }
    }
    .search {
      position: absolute;
      bottom: -50rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 654rpx;
      height: 96rpx;
      display: flex;
      .search-left {
        flex: 1;
        background: #fff;
        box-shadow: 0px 7px 7px -7px #ccc;
        padding: 28rpx 20rpx 28rpx 70rpx;
        display: flex;
        align-items: center;
        font-size: 28rpx;
        input {
          font-size: 28rpx;
          margin-left: 4rpx;
        }
      }
      .search-right {
        width: 162rpx;
        background-image: linear-gradient(to right, #ff5700, #ff9e02);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 28rpx;
        .add-text {
          margin-left: 8rpx;
        }
      }
    }
  }
  .back{
        position: fixed;
        width: 100%;
        height: 88rpx;
        padding: 2px 10rpx;
        align-items: center;
        justify-content: space-between;
        // background-image:  linear-gradient(125deg, #FF5500 0%, #FFA402 100%);
        z-index: 3;
        .title-con{
          flex: 1;
          text-align: center;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 32rpx;
          color: #fff;
        }
        .icon-box{
            // height: 44px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            justify-content: center;
            align-items: center;
            padding: 8px;
            background: rgba(0, 0, 0, 0.3);
            justify-content: center;
            align-items: center;
            &.icon-share{
              justify-self: end;
              margin-left: auto;
            }
        }
    }
</style>

<script>
import titleBar from "../../components/titleBar.vue";
import housePrice from "../../components/housePrice.vue";
import { formatImg } from "../../common/index.js";
import myIcon from "../../components/myIcon";
import { uniLoadMore, uniListItem } from "@dcloudio/uni-ui";
import myDialog from "../../components/dialog.vue";
import { mapActions } from "vuex";
import mySwiper from '@/components/mySwiper'
import addressd from '../../components/jm-address/jm-address'
export default {
  components: {
    titleBar,
    housePrice,
    myIcon,
    uniLoadMore,
    uniListItem,
    myDialog,
    mySwiper,
    addressd
  },
  data() {
    return {
      filter_list: [
        {
          name: "区域",
          type: 1,
        },
        {
          name: "均价",
          type: 2,
        },
        {
          name: "距离",
          type: 3,
        },

        // {
        //   name: "房龄",
        //   type: 3,
        // },
        // {
        //   name: "更多",
        //   type: 4,
        // },
      ],
      scrollTop: 0,
      scrollTopOffset: 0,
      likeData:[],
      show_filter_type: 0,
      show_dialog: false,
      params: {
        areaid: 0,
        distance:0,
        page: 1,
        rows:20,
      },
      get_status: "loading",
      area_list: [
        {name:"全部",areaid:0}
      ],
      price_list:[],
      distance_list:[],
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了",
      },
      show_distance: false,
      list: [],
      showList:false,
      showLike :false,
      adv:[],
      showTab:false
    };
  },
  onLoad(options) {
    if(options.keyword){
      this.params.keyword = decodeURIComponent(options.keyword)
    }
    uni.$on('handleSearch',(e)=>{
      this.params.keyword = e
      this.params.page = 1
      this.getData()
    })
    this.getData();
    this.getFilterList()
  },
  computed: {
    status_top(){
      return this.$store.state.systemInfo.statusBarHeight
    },
    padTop(){
      // #ifndef H5
      return this.status_top+44+'px'
      // #endif
      // #ifndef H5
      return '80upx'
      // #endif
    }
  },
  onUnload(){
    uni.$off('handleSearch')
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop
    const query = uni.createSelectorQuery().in(this)
    if (this.showPannel) {
      uni.pageScrollTo({
        scrollTop: this.scrollTopOffset,
        duration: 0,
      })
    }
  },
  methods: {
    ...mapActions(["getLocation"]),
    init() {
      this.getFilterList()
      this.get_status = "loading";
      this.getWxConfig(
        ['getLocation', 'updateAppMessageShareData', 'updateTimelineShareData'],
        wx => {
          this.getLocation({
            success: (res) => {
              if (res) {
                this.params.lat = res.latitude;
                this.params.lng = res.longitude;
              }
              this.getData();
            },
            fail: (err) => {
              console.log(err);
              this.getData();
            },
          });
        }
      )
    },
    scroppTo(fun) {
      console.log(123);
      const query = uni.createSelectorQuery().in(this)
      query
        .select('#tab_top')
        .fields({ rect: true, scrollOffset: true }, (data) => {
          console.log(data);
          if (data) {
            if (data.top <= 44) {
              fun && fun()
              return
            }
            console.log(data.top);
            // #ifdef H5
            this.scrollTopOffset = (this.scrollTop || 0) + data.top + 1
            console.log(this.scrollTopOffset );
            // #endif
            // #ifndef H5
            this.scrollTopOffset = (this.scrollTop || 0) + data.top - (44 + this.status_top) + uni.upx2px(100)
            // #endif
          }
          uni.pageScrollTo({
            duration: 120,
            // #ifdef H5
            scrollTop: this.scrollTopOffset,
            // #endif
            // #ifndef H5
            scrollTop: this.scrollTopOffset,

            // #endif
            success: () => {
              if (fun) {
                fun()
              }
            },
          })
        })
        .exec()
    },
    goBack(){
      if (getCurrentPages().length > 1) {
        uni.navigateBack()
      } else {
        // #ifdef H5
        var ua = window.navigator.userAgent.toLowerCase();
        //通过正则表达式匹配ua中是否含有MicroMessenger字符串
        if(ua.match(/MicroMessenger/i) == 'micromessenger'){
          uni.switchTab({
            url: '/pages/index/index'
          })
        }else{
          window.history.go(-1)
        }
        // #endif
        // #ifndef H5
          uni.switchTab({
            url: '/pages/index/index'
          })
        // #endif
      }
    },
    goPropertyData(){
      this.$navigateTo("/propertyData/property_data")
    },
    getData() {
      // if (this.params.page == 1) {
      //   this.list = [];
      // }
      this.get_status = "loading";
      this.showPannel = false
      this.$ajax.get(
        "house/priceTrend.html",
        this.params,
        (res) => {
          if (res.data.code == 1) {
            this.likeData = res.data.list2?res.data.list2:this.likeData
            if(this.params.page==1){
              this.list =res.data.list
            }else {
              this.list = this.list.concat(res.data.list);
            }
            this.showList =true

            if (res.data.list.length < this.params.rows ) {
              this.get_status = "noMore";
              this.showLike =true
            } else {
              this.get_status = "more";
            }
            this.adv =res.data.adv
            if (res.data.share) {
              this.share = res.data.share;
            }else{
              this.share = {}
            }
            this.getWxConfig()
          } else {
            this.list = []
            this.showLike =true
            this.get_status = "noMore";
          }
        },
        (err) => {
          this.list = []
          console.log(err);
        }
      );
    },
    changeArea(e){
      this.current_filter_index = -1
      this.areaName = e.district?e.district:(e.city?e.city:(e.province?e.province:""))
      this.params.areaid = e.district_id?e.district_id:(e.city_id?e.city_id:(e.province_id?e.province_id:""))
      this.filter_list.forEach((item,index)=>{
        if(item.type === this.show_filter_type){
          this.filter_list[index].name = this.areaName
        }
      })
      this.show_filter_type = 0
      this.params.page = 1
      this.getData()
    },
    click_c(item){
      this.scroppTo(()=>{
        setTimeout(() => {
          if (this.show_filter_type === item.type){
            this.show_filter_type = 0
             this.showPannel = false
          }else {
            this.show_filter_type =item.type
            if (this.show_filter_type==1){
              this.$refs.showArea.showAddress()
            }
             this.showPannel = true
          }
          // this.show_filter_type === item.type? (this.show_filter_type = 0): (this.show_filter_type =item.type)
        }, 200);
      })
      
    },
    getFilterList(){
      this.$ajax.get("build/buildCondition",{},res=>{
        res.data.area.push({areaid:'',parentid:0,mapx:'',mapy:'',areaname:"全部"})
        let area_list = res.data.area
        this.area_list = this.formatArea(area_list,'areaid', 'parentid', 'city');
        this.price_list = res.data.price
        this.distance_list = res.data.distance
        this.showTab=true
      })
    },
    formatArea(a, idStr, pIdStr, chindrenStr) {
      var r = [],
        hash = {},
        id = idStr,
        pId = pIdStr,
        children = chindrenStr,
        i = 0,
        j = 0,
        len = a.length
      for (; i < len; i++) {
        a[i].label = a[i].name
        delete a[i].name
        hash[a[i][id]] = a[i]
      }
      for (; j < len; j++) {
        var aVal = a[j],
          hashVP = hash[aVal[pId]]

        if (hashVP) {
          !hashVP[children] && (hashVP[children] = [])

          hashVP[children].unshift(aVal)
        } else {
          r.unshift(aVal)
        }
      }
      return r
    },
    switchTab(id,name){	
      this.params.page = 1
      switch (this.show_filter_type){
        case 1:
          this.params.areaid = id
          this.getData()
          break
        case 2:
          this.params.price = id
          this.getData()
          break
        case 3:
          this.params.distance =id
          // #ifdef MP
          checkAuth('scope.userLocation', {
            
            authOk: () => {
              this.init()
            },
            success: () => {
              this.init()
            },
            fail: () => {
              this.show_dialog = true
            }
          })
          // #endif
          // #ifdef H5 || APP-PLUS
          this.init()
          // #endif
      }
      this.filter_list.forEach((item,index)=>{
        if(item.type === this.show_filter_type){
          this.filter_list[index].name = name
        }
      })
      this.show_filter_type = 0;
      
    },
    onSearch(){
      this.$navigateTo(`/pages/search/search?text=小区`)
      // this.params.page = 1
      // this.getData()
    },
    navigateTo(url){
      this.$navigateTo(url)
    },
    stopMove(){

    }
  },
  onReachBottom() {
    if (this.get_status =="more"){
      this.params.page = this.params.page + 1;
      this.getData();
    }

  },
  onShareAppMessage() {
    if (this.share) {
      return {
        title: this.share.title || "",
        content: this.share.content || "",
        imageUrl: this.share.pic ? formatImg(this.share.pic, "w_6401") : "",
      };
    }
  },
};

</script>
