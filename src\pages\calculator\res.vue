<template>
    <view class="p-top-80">
        <tab-bar :tabs="navs" @click="switchTab" :nowIndex="type"></tab-bar>
        <view class="top-20 desc-box">
			<view class="desc-title">计算结果</view>
			<view class="qiun-charts">
				<!--#ifdef H5-->
				<canvas canvasId="canvasRing" class="charts" :style="{'width':cWidth*pixelRatio+'px','height':cHeight*pixelRatio+'px', 'transform': 'scale('+(1/pixelRatio)+')','margin-left':-cWidth*(pixelRatio-1)/2+'px','margin-top':-cHeight*(pixelRatio-1)/2+'px'}" @touchstart="touchRing"></canvas>
				<!--#endif-->
				<!--#ifndef H5-->
				<canvas canvasId="canvasRing" class="charts" @touchstart="touchRing"></canvas>
				<!--#endif-->
			</view>
		</view>
        <view class="row bottom-line flex-box">
            <view class="flex-1"><text class="label">贷款总额:</text>{{(loan.loanLimit+(loan.aloanLimit||0)) | float}}万元</view>
            <view class="flex-1"><text class="label">还款总额:</text>{{(loan.loanLimit+loan.loanInterest+(loan.aloanLimit||0)+loan.aloanInterest) | float}}万元</view>
        </view>
        <view class="row bottom-line flex-box">
            <view class="flex-1"><text class="label">支付利息:</text>{{(loan.loanInterest+loan.aloanInterest) | float}}万元</view>
            <view class="flex-1"><text class="label">首月月供:</text>{{((loan.yuegong+loan.ayuegong)*10000) | float}}元</view>
        </view>
    </view>
</template>

<script>
import tabBar from '../../components/tabBar.vue'
// #ifndef MP
import uCharts from '../../components/u-charts/u-charts.js';
// #endif
// #ifdef MP
import uCharts from '../../components/wx-charts/wxcharts.js';
// #endif
export default {
    data(){
        return{
            navs:[
                {
					name:"等额本息"
				},
				{
					name:"等额本金"
				}
            ],
            type:0,
            loan:{
                yuegong:0,
                ayuegong:0,
                loanInterest:0,
                aloanInterest:0
            },
            cWidth:'',
            cHeight:'',
            pixelRatio:1,
            ringData:[{name: '贷款总额',data:1},{name: '支付利息',data:1}]
        }
    },
    components:{
        tabBar
    },
    filters:{
        float(val){
            console.log(val)
            if(!val){
                return ""
            }
            return val.toFixed(2)
        }
    },
    onLoad(options){
		// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
		uni.hideShareMenu()
		// #endif
        if(!options.data){
            return
        }
        this.loan_type = options.loan_type
        Object.assign(this.loan,JSON.parse(options.data))
        console.log(this.loan)
        if(this.loan_type==0){
            this.handleComputed()
        }else if(this.loan_type==1){
            this.handleComputedS()
        }else{
             this.handleComputedAll()
        }
        //#ifdef H5
        uni.getSystemInfo({
            success: (res)=> {
                if(res.pixelRatio>1){
                    //正常这里给2就行，如果pixelRatio=3性能会降低一点
                    //_self.pixelRatio =res.pixelRatio;
                    this.pixelRatio =2;
                }
            }
        });
        //#endif
        this.cWidth=uni.upx2px(750);
        this.cHeight=uni.upx2px(500);
    },
    onReady() {
        this.showRing("canvasRing",this.ringData);
    },
    methods:{
        handleComputed(update=0){
            let yuegong,loanInterest
            if(this.type===0){
                yuegong = this.loan.loanLimit*(this.loan.loanRate/100/12)*Math.pow((1+this.loan.loanRate/100/12),this.loan.loanTime*12)/(Math.pow((1+this.loan.loanRate/100/12),this.loan.loanTime*12)-1)
                loanInterest = yuegong*this.loan.loanTime*12-this.loan.loanLimit
            }else{
                yuegong = (this.loan.loanLimit/(this.loan.loanTime*12))+(this.loan.loanLimit-0)*(this.loan.loanRate/100/12)
                loanInterest = ((this.loan.loanLimit/(this.loan.loanTime*12)+this.loan.loanLimit*this.loan.loanRate/100/12)+this.loan.loanLimit/(this.loan.loanTime*12)*(1+this.loan.loanRate/100/12))/2*this.loan.loanTime*12-this.loan.loanLimit
            }
            if(this.loan_type!=2){
                this.loan.yuegong = yuegong
                this.loan.loanInterest = loanInterest
                this.ringData = [{name: '贷款总额',data:this.loan.loanLimit},{name: '支付利息',data:this.loan.loanInterest}]
                if(update==1){
                    this.changeData()
                }
            }else{
                return {
                    yuegong:yuegong,
                    loanInterest:loanInterest
                }
            }
        },
        handleComputedS(update=0){
            let ayuegong,aloanInterest
            if(this.type===0){
                ayuegong = this.loan.aloanLimit*(this.loan.aloanRate/100/12)*Math.pow((1+this.loan.aloanRate/100/12),this.loan.aloanTime*12)/(Math.pow((1+this.loan.aloanRate/100/12),this.loan.aloanTime*12)-1)
                aloanInterest = ayuegong*this.loan.aloanTime*12-this.loan.aloanLimit
            }else{
                ayuegong = (this.loan.aloanLimit/(this.loan.aloanTime*12))+(this.loan.aloanLimit-0)*(this.loan.aloanRate/100/12)
                aloanInterest = ((this.loan.aloanLimit/(this.loan.aloanTime*12)+this.loan.aloanLimit*this.loan.aloanRate/100/12)+this.loan.aloanLimit/(this.loan.aloanTime*12)*(1+this.loan.aloanRate/100/12))/2*this.loan.aloanTime*12-this.loan.aloanLimit
            }
            if(this.loan_type!=2){
                this.loan.ayuegong = ayuegong
                this.loan.aloanInterest = aloanInterest
                this.ringData = [{name: '贷款总额',data:this.loan.aloanLimit},{name: '支付利息',data:this.loan.aloanInterest}]
                if(update==1){
                    this.changeData()
                }
            }else{
                return {
                    ayuegong:ayuegong,
                    aloanInterest:aloanInterest
                }
            }
        },
        handleComputedAll(update=0){
            this.loan.yuegong = this.handleComputed().yuegong
            this.loan.ayuegong = this.handleComputedS().ayuegong
            this.loan.loanInterest = this.handleComputed().loanInterest
            this.loan.aloanInterest = this.handleComputedS().aloanInterest
            this.ringData = [{name: '贷款总额',data:this.loan.loanLimit+this.loan.aloanLimit},{name: '支付利息',data:this.loan.loanInterest+this.loan.aloanInterest}]
            if(update==1){
                this.changeData()
            }
        },
        changeData(){
            this.canvaRing.updateData({
                series: this.ringData
            });
        },
        switchTab(e){
            this.type = e.index
            if(this.loan_type==0){
                this.handleComputed(1)
            }else if(this.loan_type==1){
                this.handleComputedS(1)
            }else{
                this.handleComputedAll(1)
            }
        },
        showRing(canvasId,chartData){
            let _self = this
            this.canvaRing=new uCharts({
                $this:_self,
                canvasId: canvasId,
                type: 'ring',
                fontSize:11,
                legend:true,
                background:'#FFFFFF',
                pixelRatio:_self.pixelRatio,
                animation: true,
                series: chartData,
                width: _self.cWidth*_self.pixelRatio,
                height: _self.cHeight*_self.pixelRatio,
                dataLabel: true,
                disablePieStroke: true,
                dataPointShape: true,
                extra: {
                    tooltip:{
                        showBox:false
                    },
                    pie: {
                        offsetAngle: -45,
                        ringWidth: 60,
                        labelWidth:15
                    }
                },
            });
        },
        touchRing(e){
            this.canvaRing.showToolTip(e, {
                format: function (item, category) {
                    return category + ' ' + item.name + ':' + item.data 
                }
            });
        }
    }
}
</script>

<style lang="scss">
    .desc-box{
		padding: $uni-spacing-row-base $uni-font-size-base;
		background-color: #fff;
	}
	.desc-box .desc-title{
		font-size: 32upx;
		padding: 0 20upx;
		margin-bottom: 20upx;
		border-left: 6upx solid #179B16
	}
    // .qiun-charts {
	// 	width:100%;
	// 	height:500upx;
	// 	background-color:#FFFFFF;
	// }
	.charts{
		width: 100%;
		height:500upx;
		background-color: #FFFFFF;
	}
    .row{
        padding: $uni-spacing-col-lg $uni-spacing-row-lg;
    }
    .row .label{
        color: #666
    }
</style>