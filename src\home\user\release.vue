<template>
	<view id="add">
		<my-input :value="params.title" @input="handelInput" label="标题" name="title"></my-input>
        <view class="inp-box">
            <my-input :value="catname" @input="handleSearch" placeholder="请输入小区名称并选择小区" label="小区" name="cid"></my-input>
            <view class="list-box" v-if="cateList.length>0">
                <uni-list-item v-for="(item,index) in cateList" :key="index" :title="item.name" @click="select(item)"></uni-list-item>
            </view>
        </view>
		<my-select v-if="params.typeid==2" :value="params.jindu" @change="pickerChange" label="施工进度" :range="jinduList" name="jindu"></my-select>
		<view class="textarea-row bottom-line top-20">
			<view class="block-title bottom-line">描述</view>
			<textarea :value="params.content" placeholder="请输入描述" @input="inputContent" />
		</view>
		<view>
			<view class="top-20 block-title bottom-line">上传案例封面</view>
			<view class="upload-box">
				<my-upload @uploadDon="uploadDon2" action="memberShop/uploadFileByWx" :imgs="pathList" :maxCount="1" :chooseType="1"></my-upload>
			</view>
		</view>
        <view>
			<view class="top-20 block-title bottom-line">上传案例图片(单次最多9张，请勿含有电话水印)</view>
			<view class="upload-box">
				<my-upload @uploadDon="uploadDon" action="memberShop/uploadFileByWx" :imgs="imgList" :maxCount="9" :chooseType="1"></my-upload>
			</view>
		</view>
		<view class="btn-box">
			<button class="default" @click="subData()">立即发布</button>
		</view>
	</view>
</template>

<script>
	import myInput from "../../components/form/myInput.vue"
	import mySelect from "../../components/form/mySelect.vue"
	import myUpload from "../../components/form/myUpload.vue"
    import {uniIcons} from "@dcloudio/uni-ui"
    import {uniListItem} from "@dcloudio/uni-ui"
    import {navigateTo,debounce} from "../../common/index.js"
	export default{
		components:{
			myInput,
			mySelect,
			myUpload,
            uniIcons,
            uniListItem
		},
		data(){
			return{
				jinduList:[],
				cateList:[],
                imgList:[],
                pathList:[],
                catname:"",
				params:{
                    typeid:"",
					title:"",
                    cid:"",
                    path:"",
					caseImgs:"",
                    content:"",
                    jindu:""
				}
			}
		},
		onLoad(options){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO || MP-BAIDU
			uni.hideShareMenu()
			// #endif
			if(options.typeid){
				this.params.typeid = options.typeid||1
            }
            console.log(this.params.typeid)
            this.getData()
		},
		methods:{
            getData(){
                this.$ajax.get('memberShop/releaseInfo',{},res=>{
                    if(res.data.code == 1){
                        this.jinduList = res.data.jindu.map(item=>{
                            return {
                                name:item.jindu,
                                value:item.id
                            }
                        })
                    }
                })
            },
			handelInput(e){
				this.params[e._name] = e.detail.value
			},
			pickerChange(e){
				this.params[e._name] = e.value
			},
			handleSearch(e){
                this.catname = e.detail.value
				debounce(this.getRes,500)(e.detail.value)
            },
			getRes(key){
				if(!key){
					return
				}
				this.$ajax.get('house/searchCommunity.html',{keyword:key},(res)=>{
					this.getStatus = 0
					if(res.data.code == 1){
						this.cateList = res.data.list
					}
					console.log(res.data)
				},(err)=>{
					
				},false)
			},
			select(item){
                this.catname = item.name
                this.params.cid = item.id
                this.cateList = []
			},
			inputContent(e){
				this.params.content = e.detail.value
			},
			uploadDon(e){
				if(e.type == 'image'){
					this.imgList = e.files
					this.params.caseImgs = e.files.join(',')
				}
			},
			uploadDon2(e){
				if(e.type == 'image'){
					this.pathList = e.files
					this.params.path = e.files.join(',')
				}
			},
			subData(){
				// 表单验证
				if(!this.params.title){
					uni.showToast({
						title:"请输入标题",
						icon:"none"
					})
					return
				}
				if(!this.params.cid&&this.params.typeid!=3){
					uni.showToast({
						title:"请选择小区",
						icon:"none"
					})
					return
				}
				if(!this.params.content){
					uni.showToast({
						title:"请输入描述",
						icon:"none"
					})
					return
                }
                uni.showLoading({
                    title:"正在发布",
                    mask:true
                })
                // console.log(this.params)
                // return
				this.$ajax.post('memberShop/releaseInfo',this.params,(res)=>{
					uni.hideLoading()
					if(res.data.code == 1){
						uni.showToast({
							title:res.data.msg,
							mask:true,
							duration:2000
						})
						setTimeout(()=>{
							uni.navigateBack()
						},1500)
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
				},err=>{
                    console.log(err)
                    uni.hideLoading()
                })
			}
		}
	}
</script>

<style lang="scss">
	.form-item-row{
		display: flex;
		padding: 16upx 24upx;
		line-height:62upx;
		background-color: #fff;
	}
	.form-item-row .value{
		flex: 1;
		height: 62upx;
		padding: 0 8upx;
		margin-right: 10upx;
	}
	.form-item-row .novalue{
		color: $uni-text-color-grey;
	}
	.form-item-row label{
		width: 130upx;
		font-size: $uni-font-size-lg;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-right: 38upx;
		text-align-last:justify;
	}
	.form-item-row uni-icon{
		line-height: 62upx;
		margin-left: 10upx;
	}
	#add{
        .block-title{
            padding: $uni-spacing-col-lg $uni-spacing-row-lg;
            background-color: #fff;
            font-size: $uni-font-size-lg;
        }
        .upload-box{
            padding: $uni-spacing-row-base 1.6vw;
            background-color: #fff;
        }
        .textarea-row{
            background-color: #fff
        }
        textarea{
            padding: 10upx $uni-spacing-row-lg;
        }
        .inp-box{
            position: relative;
        }
        .list-box{
            position: absolute;
            left: 20upx;
            right: 20upx;
            z-index: 2;
            border: 1upx solid #f3f3f3;
            box-shadow: 5upx 10upx 20upx 5upx #dedede;
            background-color: #fff;
        }
    }
</style>
