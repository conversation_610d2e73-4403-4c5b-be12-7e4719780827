<template>
    <view class="client-list">
        <title-bar :showback="false" custom>
            <template slot="left">
            <view class="title-left flex-row" @click="clearUnread">
                <my-icon type="ic_delete1x" size="46rpx" color="#666"></my-icon>
                <text class="text" v-if="login_status===3">清除未读</text>
            </view>
            </template>
            <view class="bar_title">消息</view>
            <template slot="right">
            <view class="title-right flex-row" @click="$navigateTo('/pages/map_find/map_find')">
                <text class="text"></text>
            </view>
            </template>
        </title-bar>
        <block v-if="is_open_im&&login_status===3">
        <view class="card-box" v-if="my_info.adviser>0||my_info.levelid>1">
            <view class="set_card" @click="toSet()" v-if="my_info.adviser>0">+名片设置</view>
            <view class="set_card" @click="toSet()" v-else-if="my_info.levelid>1">店铺设置</view>
            <agent-card :infoData="my_info">
                <template v-slot:options="{infoData}">
                    <view class="operation-box flex-box">
                        <view class="operation" @click="addInfo(infoData)">
                            <my-icon type="fabu1" size="42rpx" color="#ff656b"></my-icon>
                            <text class="text">{{infoData.adviser>0?'发布楼盘动态':'发布信息'}}</text>
                        </view>
                        <view class="operation" @click="myCard(infoData)">
                            <my-icon type="weixin" size="42rpx" color="#09de74"></my-icon>
                            <text class="text">我的名片</text>
                        </view>
                        <view class="operation" @click="toMore(infoData)">
                            <my-icon type="ic_fabu" size="42rpx" color="#333"></my-icon>
                            <text class="text">更多</text>
                        </view>
                    </view>
                </template>
            </agent-card>
        </view>
        <!-- <view class="list-label">客户列表</view> -->
        <view class="subscribe-box">
            <view class="subscribe_tip" v-if="!is_subscribe">
                <text>关注{{$store.state.siteName}}公众号可随时接收聊天消息</text>
                <text class="btn" @click="$refs.qrcode_popup.show()">关注</text>
            </view>
        </view>
        <view class="search-box">
            <view class="search-icon"><my-icon type="ic_sousuo" color="#999999"></my-icon></view>
            <input type="text" confirm-type="search" v-model="params.keywords" @confirm="handleSearch()" placeholder="请输入昵称或手机号" />
        </view>
        <view class="tab-box flex-box">
            <view class="tab-item" :class="{'active':activeIndex==index}" :id="'i'+index" v-for="(item,index) in tabs" :key="item.index" @click="clickTab(index)">
                <view class="tab-text">{{item.name}}</view>
            </view>
            <!-- <view class="tab-item clear" @click="clearUnread()">
                <view class="tab-text">
                    <my-icon type="shanchu" color="#666"></my-icon>
                    <text>清除未读</text>
                </view>
            </view> -->
        </view>
        <!-- 好友列表 -->
        <template v-if="params.is_black===0">
        <view class="friend-item flex-box" hover-class="navigator-hover" v-for="(item, index) in im.friendList" :key="index" @click="toDetail(item.platform_id, item.chat_id, item.user_id, index)">
            <view class="img-box">
                <image class="chat-header" :src="item.headimage | imgUrl('w_80')" mode="aspectFill"></image>
            </view>
            <view class="dot" v-if="item.uncount>0">{{item.uncount}}</view>
            <view class="info-box flex-box flex-1 bottom-line">
                <view class="flex-1">
                    <view class="title flex-box">
                        <text class="uname">{{item.nickname||'暂无昵称'}}</text>
                        <text v-if="item.alias_name" class="levelname" :class="{official:item.alias_name==='官方',agent:item.alias_name==='经纪人',adviser:item.alias_name==='顾问'}">{{item.alias_name}}</text>
                    </view>
                    <view class="desc">{{item | formatMsg}}</view>
                    <!-- <view class="other">{{item.title?'来源：'+item.title:''}}</view> -->
                </view>
                <view class="right-icon">
                    <view class="time">{{item.chat.time}}</view>
                    <!-- <view class="dot" v-if="item.uncount>0">{{item.uncount}}</view> -->
                </view>
            </view>
        </view>
        <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
        </template>
        <!-- 黑名单列表 -->
        <template v-else-if="params.is_black===1">
        <view class="friend-item flex-box" hover-class="navigator-hover" v-for="(item, index) in im.blackFriendList" :key="index" @click="toDetail(item.platform_id, item.chat_id, index)">
            <view class="img-box">
                <image class="chat-header" :src="item.headimage | imgUrl('w_80')" mode="aspectFill"></image>
                <view class="dot" v-if="item.uncount>0"></view>
            </view>
            <view class="info-box flex-box flex-1 bottom-line">
                <view class="flex-1">
                    <view class="title">
                        <text>{{item.nickname||'暂无昵称'}}</text>
                        <text v-if="item.alias_name" class="levelname" :class="{official:item.alias_name==='官方',agent:item.alias_name==='经纪人',adviser:item.alias_name==='顾问'}">{{item.alias_name}}</text>
                    </view>
                    <view class="desc">{{item | formatMsg}}</view>
                    <!-- <view class="other">{{item.title?'来源：'+item.title:''}}</view> -->
                </view>
            </view>
            <view class="right-icon">
                <view class="time">{{item.chat.time}}</view>
                <view class="dot" v-if="item.uncount>0">{{item.uncount}}</view>
                <view class="dot_place" v-else></view>
            </view>
        </view>
        <uni-load-more :status="get_status2" :content-text="content_text2"></uni-load-more>
        </template>
        <!--访客列表-->
        <template v-else>
        <view class="friend-item flex-box" hover-class="navigator-hover" v-for="(item, index) in visitorList" :key="index" @click="toVisitorDetail(item.user_id, index)">
            <view class="img-box">
                <image class="chat-header" :src="item.prelogo | imgUrl('w_80')" mode="aspectFill"></image>
                <view class="dot" v-if="item.uncount>0"></view>
            </view>
            <view class="info-box flex-box flex-1 bottom-line">
                <view class="flex-1">
                    <view class="title">{{item.cname||'暂无昵称'}}</view>
                    <view class="desc">{{item.last_desc}}</view>
                </view>
                <view class="right-icon">
                    <view class="time">{{item.time}}</view>
                    <view class="dot_place"></view>
                </view>
            </view>
        </view>
        <uni-load-more :status="get_status3" :content-text="content_text3"></uni-load-more>
        </template>
        <my-popup ref="socket_colse" :showMask="false" position="top">
            <view class="flex-box err-tip">
                <view class="tip-text">聊天连接已断开</view>
                <view class="tip-btn" @click="connectChatAgain()">点击重连</view>
            </view>
        </my-popup>
        </block>
        <block v-if="is_open_im&&login_status===1">
            <view class="login_tip">
                <image class="icon" mode="widthFix" :src="'/images/new_icon/login_tip_icon.png' | imageFilter('m_320')"></image>
                <text class="title">还没有登录</text>
                <text class="tip">登录后查看会话，聊天更顺畅</text>
                <view class="btn" @click="$navigateTo('/user/login/login')">立即登录</view>
            </view>
        </block>
        <block v-if="is_open_im&&login_status===2">
            <view class="login_tip">
                <image class="icon" mode="widthFix" :src="'/images/new_icon/login_tip_icon.png' | imageFilter('m_320')"></image>
                <text class="title">还没绑定手机号</text>
                <text class="tip">绑定手机号后查看会话，聊天更顺畅</text>
                <view class="btn" @click="$navigateTo('/user/bind_phone/bind_phone')">立即绑定</view>
            </view>
        </block>
        <block v-if="!is_open_im">
            <view class="login_tip">
                <image class="icon" mode="widthFix" :src="'/images/new_icon/login_tip_icon.png' | imageFilter('m_320')"></image>
                <text class="title">暂未开启</text>
                <!-- <text class="tip">登录后查看会话，聊天更顺畅</text> -->
            </view>
        </block>
        <my-popup ref="qrcode_popup" position="top">
			<view class="qrcode-box">
				<!-- #ifdef H5 -->
				<view class="qrimg-box">
					<image @longtap="saveQrcode" class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="title">长按保存图片</view>
						<view class="tip">相册选取，识别关注</view>
					</view>
				</view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view class="qrimg-box">
					<image class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="tip">长按识别二维码关注公众号</view>
					</view>
				</view>
				<!-- #endif -->
				<view class="icon-box" @click="$refs.qrcode_popup.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
    </view>
</template>

<script>
    import titleBar from "../../components/titleBar"
    import myIcon from "../../components/myIcon"
    import agentCard from '../../components/agentCard'
    import {formatImg} from "../../common/index"
    import chat from '../../common/chat_mixin'
    import {uniLoadMore} from '@dcloudio/uni-ui'
    import myPopup from '../../components/myPopup'
    import { mapState } from 'vuex'
    export default {
        components:{
            titleBar,
            myIcon,
            agentCard,
            uniLoadMore,
            myPopup
        },
        mixins:[chat],
        data(){
            return {
                login_status: '',
                params:{
                    page:1,
                    rows:20,
                    is_black:0,
                    keywords:""
                },
                activeIndex:0,
                tabs:[
                   
                ],
                my_info:{
                    adviser:0
                },
                visitorList:[],
                get_status:"loading",
                content_text:{
                    contentdown:"",
                    contentrefresh:"正在加载...",
                    contentnomore:""
                },
                get_status2:"loading",
                content_text2:{
                    contentdown:"",
                    contentrefresh:"正在加载...",
                    contentnomore:""
                },
                get_status3:"loading",
                content_text3:{
                    contentdown:"",
                    contentrefresh:"正在加载...",
                    contentnomore:""
                },
                qrcode: "",
                is_subscribe: 1 //是否已关注公众号
            }
        },
        computed: {
            ...mapState(['im', 'user_info']),
            is_open_im() {
                return this.$store.state.im.ischat
            }
        },
        onLoad(options){
            // #ifdef MP-WEIXIN
            uni.hideShareMenu()
            // #endif
            console.log(options);
            this.options = options
            // this.userToken = uni.getStorageSync('token')
            // this.getMyInfo()
            // // 判断是否是要显示访客列表
            // if(options.type==='visitor'){
            //     this.activeIndex = 2
            //     this.params.is_black=""
            //     this.getVisitorList()
            // }else{
            //     this.getFriendList()
            // }
        },
        onShow(){
            // uni.$on('getDataAgain',()=>{
            //     this.userToken = uni.getStorageSync('token')
            //     this.getMyInfo()
            // })
            if(!this.im.socketOpen){
                this.userToken = uni.getStorageSync('token')
                this.params.page = 1
                this.getMyInfo()
            }
            // if(!this.my_info.id&&!this.$store.state.allowOpen){
            //     this.getMyInfo()
            //     this.$store.state.allowOpen = true
            // }
            this.getUnReadCount()
        },
        onUnload(){
            uni.$off('getDataAgain')
            // 关闭socket，停止心跳维持
            this.closeSocket('active')
            if (this.timer){
                clearInterval(this.timer)
            }
        },
        filters:{
            imgUrl(val, param=""){
                if(!val){
                    return ""
                }
                return formatImg(val, param)
            },
            formatMsg(val){
                switch(val.chat.type){
                    case 'text':
                        return val.chat.content
                    case 'image':
                        return "[图片]"
                    case 'map':
                        return "[位置]"
                    case 'voice':
                        return "[语音]"
                    case 'build':
                        return "[楼盘]"
                    case 'ershou':
                        return "[二手房]"
                    case 'renting':
                        return "[出租房]"
                    case "commercial":
                        return "[商业出售]";
                    case "commercial_rent":
                        return "[商业出租]";
                    case "commercial_transfer":
                        return "[生意转让]";
                    case 'wechat':
                        return "[微信名片]"
                    case 'apply_wx':
                        return "[申请查看微信名片]"
                    case 'tel':
                        return "[手机号码]"
                    case 'apply_tel':
                        return "[申请查看手机号]"
                    case '':
                        return ""
                    default:
                        return "[不支持的消息类型]"
                }
            }
        },
        methods: {
            socketOnOpen(){
                uni.$once('onTabItemTap', ()=>{
                    uni.$off('getDataAgain')
                    // 关闭socket，停止心跳维持
                    this.closeSocket('active')
                    if (this.timer){
                        clearInterval(this.timer)
                    }
                })
            },
            clickTab(index){
               
                this.params.page = 1
                this.params.keywords = ""
                switch (index){
                    case 0:
                        this.activeIndex = index
                        this.params.is_black = 0
                        this.getFriendList()
                        break
                    case 1:
                        this.activeIndex = index
                        this.params.is_black = ""
                        this.getVisitorList()
                        break
                    case 2:
                        if (this.my_info.levelid>1||this.my_info.adviser){
                            this.$navigateTo('/customer/list')
                        }else {
                            this.activeIndex = index
                            this.params.is_black = 1
                            this.getBlackFriendList()
                        }
                }
            },
            /**
             * 获取好友列表
             */
            getFriendList(){
                this.get_status = "loading"
                this.content_text.contentnomore=""
                if(this.params.page == 1){
                    this.im.friendList = []
                }
                this.$ajax.get('im/chatFriends.html',this.params,res=>{
                    this.im.imToken = res.data.imToken||''
                    if(res.data.user){
                        this.im.myChatInfo = res.data.user
                        if(this.my_info.wechat_img){
                            this.im.myChatInfo.wechat_img = this.my_info.wechat_img
                        }
                        if(this.my_info.wechat){
                            this.im.myChatInfo.wechat = this.my_info.wechat
                        }
                        if(this.my_info.is_optimization){
                            this.im.myChatInfo.is_optimization = this.my_info.is_optimization
                        }
                        if(this.my_info.id){
                            this.im.myChatInfo.user_id = this.my_info.id
                        }
                        this.im.myChatInfo.adviser = this.my_info.adviser || 0
                        this.im.myChatInfo.levelid = this.my_info.levelid || 1
                    }
                    if(!this.im.socketOpen){
                        if(this.im.imToken){
                            console.log("执行聊天初始化")
                            this.initMsg = {flag: 'init', from_id: this.im.myChatInfo.platform_id}
                            this.connectChat()
                            // this.onMessage()
                            this.onClose()
                            this.onSocketError()
                        }
                    }else{
                        console.log("聊天已经是连接状态")
                    }
                    this.is_subscribe = res.data.is_subscribe || 0
                    this.qrcode = res.data.gzhewm
                    if(res.data.code == 1){
                        let friends = res.data.friends.map(item=>{
                            item.chatList = []
                            return item
                        })
                        this.im.friendList = this.im.friendList.concat(friends)
                        if(res.data.friends.length<this.params.rows){
                            this.get_status = "noMore"
                        }else{
                            this.get_status = "more"
                        }
                    }else{
                        this.params.page--
                        if (this.params.page<1) this.params.page = 1
                        this.get_status = "noMore"
                    }
                    if(this.im.friendList.length==0){
                        this.content_text.contentnomore="您还没有联系人哦~~"
                    }
                },err=>{
                    console.log(err)
                    this.params.page--
                    if (this.params.page<1) this.params.page = 1
                    this.get_status = "noMore"
                })
            },
            /**
             * 获取黑名单列表
             */
            getBlackFriendList(){
                this.get_status2 = "loading"
                this.content_text2.contentnomore=""
                if(this.params.page == 1){
                    this.im.blackFriendList = []
                }
                this.$ajax.get('im/chatFriends.html',this.params,res=>{
                    if(res.data.code == 1){
                        let friends = res.data.friends.map(item=>{
                            item.chatList = []
                            return item
                        })
                        this.im.blackFriendList = this.im.blackFriendList.concat(friends)
                        if(res.data.friends.length<this.params.rows){
                            this.get_status2 = "noMore"
                        }else{
                            this.get_status2 = "more"
                        }
                    }else{
                        this.params.page--
                        if (this.params.page<1) this.params.page = 1
                        this.get_status2 = "noMore"
                    }
                    if(this.im.blackFriendList.length==0){
                        this.content_text2.contentnomore='黑名单是空空如也~~'
                    }
                },err=>{
                    console.log(err)
                    this.params.page--
                    if (this.params.page<1) this.params.page = 1
                    this.get_status = "noMore"
                })
            },
            /**
             * 获取访客列表
             */
            getVisitorList(){
                this.get_status3 = "loading"
                this.content_text3.contentnomore=""
                if(this.params.page == 1){
                    this.visitorList = []
                }
                this.$ajax.get('im/visitorList', {page:this.params.page,keywords:this.params.keywords}, res=>{
                    if(res.data.code===1&&res.data.list.length>0){
                        this.visitorList = this.visitorList.concat(res.data.list)
                        this.get_status3 = "more"
                    }else{
                        if(this.visitorList.length==0){
                            this.content_text3.contentnomore='您还没有访客'
                        }else{
                            // uni.showToast({
                            //     title:"没有更多访客了",
                            //     icon:'none'
                            // })
                        }
                        this.params.page--
                        if (this.params.page<1) this.params.page = 1
                        this.get_status3 = "noMore"
                    }
                })
            },
            handleSearch(){
                if(this.params.is_black===''){
                    this.getVisitorList()
                }else{
                    this.getFriendList()
                }
            },
            /**
             * 去访客详情
             */
            toVisitorDetail(user_id, index){
                if(!user_id){
                    uni.showToast({
                        title:"无法查看游客访问详情",
                        icon:'none'
                    })
                    return
                }
                this.$navigateTo('/chatPage/chat/friend_info?user_id='+user_id)
            },
            /**
             * 获取好友列表上面自己的信息
             */
            getMyInfo(){
                this.$ajax.get('im/contactDetails.html',{},res=>{
                    if(res.data.code === -1){
                        this.login_status = 1
                        return
                    }
                    if(res.data.code === 2){
                        this.login_status = 2
                        return
                    }
                    // 判断是否是要显示访客列表
                    if(this.options.type==='visitor'){
                        this.activeIndex = 1
                        this.params.is_black=""
                        this.getVisitorList()
                    }else{
                        this.getFriendList()
                    }
                    if(res.data.code === 1){
                        this.login_status = 3
                        this.my_info = res.data.member
                        this.im.myChatInfo.user_id = this.my_info.id
                        this.im.myChatInfo.wechat_img = this.my_info.wechat_img
                        this.im.myChatInfo.wechat = this.my_info.wechat
                        this.im.myChatInfo.adviser = this.my_info.adviser
                        this.im.myChatInfo.levelid = this.my_info.levelid
                        this.im.myChatInfo.is_optimization = this.my_info.is_optimization||0
                        // if(this.my_info.adviser>0||this.my_info.levelid>1){
                        //     this.tabs.push({name:"访客"})
                        // }
                        if (this.my_info.levelid>1 || this.my_info.adviser){
                            this.tabs=[
                                {name:"聊天"},
                                {name:"访客"},
                                {name:"客户"}
                            ]
                        }else {
                            this.tabs = [
                                {name:"聊天"},
                                {name:"访客"},
                                {name:"黑名单"},
                            ]
                        }
                    }
                },err=>{
                    console.log(err)
                    // 判断是否是要显示访客列表
                    if(this.options.type==='visitor'){
                        this.activeIndex = 2
                        this.params.is_black=""
                        this.getVisitorList()
                    }else{
                        this.getFriendList()
                    }
                },{disableAutoHandle:true})
            },
            /**
             * 连接socket
             */
            connectChat(){
                this.connectPage = 'list'
                // this.onOpen()
                this.handleConnectSocket()
            },
            connectChatAgain(){
                this.$ajax.get('im/chatFriends.html',this.params,res=>{
                    this.im.imToken = res.data.imToken||''
                    if(!this.im.socketOpen){
                        this.connectChat()
                        return
                    }
                })
            },
            toSet(){
               if(this.my_info.adviser>0){
                    this.$navigateTo('/user/adviser_info')
                } else if(this.my_info.levelid>1){
                    this.$navigateTo('/user/agent_info')
                } 
            },
            addInfo(info){
                if(!info){
                    info = this.my_info
                }
                if(info.adviser>0){
                    this.$navigateTo('/user/consultant/addpost?buildid='+info.build_ids)
                } else if(info.levelid>1){
                    uni.switchTab({
                        url: '/pages/add/add'
                    });
                }
            },
            myCard(info){
                if(!info){
                    info = this.my_info
                }
                if(info.adviser>0){
                    this.$navigateTo('/pages/consultant/detail?id='+info.adviser)
                } else if(info.levelid>1){
                    this.$navigateTo('/pages/agent/detail?id='+info.id)
                }
            },
            toMore(info){
                if(!info){
                    info = this.my_info
                }
                this.$navigateTo('/chatPage/chat/more_info?adviser_id='+info.adviser)
            },
            /**
             * 去聊天
             * @param {String} to_id 聊天中的好友id
             * @param {String} chat_id 好友关系id
             * @param {Number} index 好友再好友列表中的索引
             */
            toDetail(to_id, chat_id, user_id, index){
                if(this.params.is_black){
                    uni.showActionSheet({
                        itemList:['加为好友', '删除好友'],
                        success: (res)=> {
                            if(res.tapIndex === 0){
                                console.log("恢复好友")
                                this.reverseBlack(to_id, chat_id, index)
                            }else if(res.tapIndex === 1){
                                console.log("删除好友")
                                this.removeFriend(chat_id, index)
                            }
                        },
                        fail: (res)=> {
                            console.log(res.errMsg);
                        }
                    })
                    return
                }
                let blackStatue = this.getBlack(this.im.friendList[index].owner_id, this.im.friendList[index].passive_id, this.im.friendList[index].platform_id, this.im.myChatInfo.platform_id)
                // console.log("黑名单状态",blackStatue)
                if (blackStatue){
                    return
                }
                this.chatStatistics(user_id, 2)
                this.im.chatIndex = index
                this.im.nowChat = this.im.friendList[index]
                if(!this.im.socketOpen){
                    this.im.socketOpen = true
                    this.connectChatAgain()
                }
                this.is_auto_replay = 0
                this.$navigateTo("/chatPage/chat/chat?title="+(this.im.friendList[index].nickname||'')+'&to_id='+to_id+'&user_id='+user_id)
            },
            chatStatistics(id, type){
                this.$ajax.get('im/chatStatistics',{id, type},()=>{})
            },
            /**
             * 将黑名单好友从黑名单中恢复
             */
            reverseBlack(to_id, chat_id, index){
                this.$ajax.get('im/removeBack.html',{chat_id},res=>{
                    if(res.data.code === 1){
                        uni.showToast({
                            title:"操作成功"
                        })
                        this.pullReverseBlack(to_id)
                        this.page = 1
                        this.getBlackFriendList()
                    }else{
                        uni.showToast({
                            title:res.data.msg,
                            icon:'none'
                        })
                    }
                })
            },
            // 删除好友
            removeFriend(chat_id, index){
                this.$ajax.get('im/removeFriend.html', {chat_id}, res=>{
                    if(res.data.code === 1){
                        this.im.blackFriendList.splice(index,1)
                    }else{
                        uni.showToast({
                            title:res.data.msg || '',
                            icon:'none'
                        })
                    }
                })
            },
            pullReverseBlack(to_id){
                let reverseMesage = {flag:'removeBlack',to_id:to_id,from_id:this.im.myChatInfo.platform_id}
                this.im.socketTask.send({
                    data: JSON.stringify(reverseMesage)
                });
            },
            // 获取未读数量
            getUnReadCount(){
                if(!uni.getStorageSync('token')){
                    return
                }
                this.$ajax.get('im/unRead',{},res=>{
                    if(res.data.uncount){
                        uni.setTabBarBadge({
                            index:3,
                            text:res.data.uncount>99?'99+':res.data.uncount+''
                        })
                    }else{
                        uni.removeTabBarBadge({
                            index: 3
                        })
                    }
                },err=>{
                    console.log(err)
                }, {disableAutoHandle:true})
			},
            // 清除未读消息
            clearUnread(){
                this.$ajax.get('im/clearAllUnRead',{},res=>{
                    if(res.data.code === 1){
                        this.im.friendList.map(item=>{
                            item.uncount = 0
                        })
                        uni.removeTabBarBadge({
                            index:3
                        })
                    }else{
                        uni.showToast({
                            title:res.data.msg||'操作失败',
                            icon:'none'
                        })
                    }
                })
            },
            // 保存二维码
            saveQrcode(){
                uni.request({
                    url:this.qrcode,
                    method:'GET',
                    responseType: 'arraybuffer',
                    success:(res)=>{
                        let base64 = uni.arrayBufferToBase64(res);
                        const userImageBase64 = 'data:image/jpg;base64,' + base64;
                        uni.saveImageToPhotosAlbum({
                            filePath: userImageBase64,
                            success: result => {
                                uni.showToast({
                                    title: '保存成功，在微信从相册中选取识别吧',
                                    icon: 'none',
                                    duration: 4000
                                })
                            },
                            fail: err => {
                                console.log(err)
                                uni.showToast({
                                    title: '保存失败，请重试',
                                    icon: 'none'
                                })
                            }
                        })
                    }
                }); 
			},
        },
        onReachBottom(){
            if(!this.is_open_im||this.login_status!==3){
                return
            }
            this.params.page ++
            if(this.params.is_black===1){
                this.getBlackFriendList()
            }else if(this.params.is_black===0){
                this.getFriendList()
            }else{
                this.getVisitorList()
            }
        },
        // #ifdef H5 || APP-PLUS
		onNavigationBarButtonTap(option){
            this.clearUnread()
		},
		// #endif
    }
</script>

<style lang="scss" scoped>

.title-left{
    display: flex;
    align-items: center;
    width: 160rpx;
    padding-left: 48rpx;
    color: #666;
}
.title-right{
    width: 160rpx;
    padding-right: 48rpx;
}
.bar_title{
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    font-size: 16px;
    font-weight: 700;
}
.login_tip{
    display:flex;
    flex-direction: column;
    align-items: center;
    justify-content:center;
    position: absolute;
    padding-bottom: 200rpx;
    top: 0;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    .icon{
        width: 300rpx;
        height: 150rpx;
    }
    .title{
        margin-top: 24rpx;
        margin-bottom: 16rpx;
        font-size: 32rpx;
        font-weight: bold;
    }
    .tip{
        font-size: 24rpx;
        color: #999;
    }
    .btn{
        margin-top: 88rpx;
        line-height: 88rpx;
        padding: 0 52rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: #fff;
        background: #FB656A;
        box-shadow: 0 4px 15px 0 rgba(251,101,106,0.40);
    }
}
.client-list{
    // .tab-box{
    //     width: 100%;
    //     padding: 0 20upx;
    //     box-sizing: border-box;
    //     background-color: #fff;
    //     .tab-item{
    //         padding: 20upx;
    //         margin: 20upx;
    //         text-align: center;
    //     }
    //     .tab-img{
    //         width: 80upx;
    //         height: 80upx;
    //         border-radius: 8upx;
    //     }
    //     .tab-text{
    //         font-size: 26upx;
    //         color: #666;
    //     }
    // }
    .list-label{
        padding: 10upx 20upx;
        font-size: 26upx;
        color: #888;
    }
    .tab-box{
        padding: 0 24rpx;
        display: flex;
        background-color: #fff;
        .tab-item{
            flex: 1;
            text-align: center;
            padding: 0 20upx;
            font-size: 0;
            .tab-text{
                display: inline-block;
                min-width: 126upx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding: 20upx 0;
            }
            &.active{
                .tab-text{
                    color: $uni-color-primary;
                    border-bottom: 4upx solid $uni-color-primary;
                }
            }
        }
        .clear{
            color: #666
        }
    }
    .row{
        // padding: 20upx 24upx;
        padding: 32upx 24upx;
        align-items: center;

    }
    .friend-item{
        padding-left: 48rpx;
        align-items: center;
        background-color: #fff;
        position: relative;
        .info-box{
            padding: 32rpx 0;
            padding-right: 32rpx;
            >view{
                overflow: hidden;
            }
        }
    }
    .img-box{
        width: 110upx;
        height: 110upx;
        margin-right: 36upx;
        position: relative;
        .chat-header{
            border-radius: 50%;
            position: absolute;
            width: 100%;
            height: 100%;
        }
    }
    .dot{
        min-width: 32upx;
        height: 32upx;
        padding: 0 10upx;
        box-sizing: border-box;
        line-height: 30upx;
        text-align: center;
        font-size: 22upx;
        border-radius: 16upx;
        background-color: #f44;
        color: #fff;
        position: absolute;
        top: 30rpx;
        left: 130rpx;
        z-index: 2;
    }
    .info-box{
        overflow: hidden;
        .title{
            align-items: center;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 32upx;
            font-weight: bold;
            color: #0f1d32;
            letter-spacing: 2upx;
            margin-bottom: 12upx;
            .uname{
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .levelname{
                line-height: 32rpx;
                margin: 0 16upx;
                padding: 0 16rpx;
                display: inline-block;
                // border-radius: 16upx;
                font-size: 22upx;
                // border: 1upx solid #4ebdf8;
                // color: #999;
                color: #fff;
                // background-color: #f2f2f2;
                background-image: linear-gradient(180deg,#69d4bb,#00caa7);
                font-weight: initial;
                border-top-left-radius: 4px;
                border-bottom-right-radius: 4px;
                &.official{
                    // background-color: #1296db;
                    color: #fff;
                    background-image: linear-gradient(180deg,#8cd3fc,#4cc7f6)
                    // border: 1upx solid #1296db;
                }
                &.agent{
                    // border: 1upx solid #f96063;
                    // background-color: #f96063;
                    background-image: linear-gradient(180deg,#ff9767,#fd7737);
                    color: #fff;
                }
                &.adviser{
                    // border: 1upx solid #f0bb2c;
                    // background-color: #f0bb2c;
                    color: #fff;
                    background-image: linear-gradient(180deg,#fcd88c,#f6ce4c);
                }
            }
        }
        .desc{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 26upx;
            height: 40upx;
            line-height: 40upx;
            color: #888
        }
        .other{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 24upx;
            color: #999
        }
    }
    .right-icon{
        position: relative;
        height: 90upx;
        // width: 120upx;
        // .icon-box{
        //     position: absolute;
        //     display:inline-block;
        //     height: 28px;
        //     width: 28px;
        //     left: 0;
        //     right: 0;
        //     top: 0;
        //     bottom: 0;
        //     margin: auto;
        // }
        .time{
            font-size: 24upx;
            color: #999
        }
        .dot{
            min-width: 32upx;
            height: 32upx;
            padding: 0 10upx;
            box-sizing: border-box;
            line-height: 30upx;
            text-align: center;
            font-size: 22upx;
            border-radius: 16upx;
            background-color: #f44;
            color: #fff;
            position: absolute;
            // display: inline-block;
            margin-top: 20upx;
            right: 0;
        }
        .dot_place{
            margin-top: 20upx;
            height: 32upx
        }
    }
}
.card-box{
    position: relative;
    background-color: #fff;
    .set_card{
        position: absolute;
        top: 46upx;
        right: 45upx;
        color: $uni-color-primary;
        z-index: 2;
        font-size: 26upx;
    }
    .operation-box{
        margin-top: 24rpx;
        justify-content: space-around;
        align-items: center;
        .operation{
            display: flex;
            align-items: center;
            font-size: 24upx;
            color: #666;
            .text{
                margin-left: 20upx;
            }
        }
    }
}
.err-tip{
    justify-content: space-between;
    padding: 10upx 24upx;
    background-color: #f44;
    color: #fff;
    .tip-btn{
        padding: 6upx 12upx;
        border: 1upx solid rgb(255, 103, 103);
        border-radius: 6upx;
        font-size: 26upx;
    }
}

.subscribe-box{
    padding-top: 10rpx 48rpx 0 48rpx;
    background-color: #fff;
    .subscribe_tip{
        position: relative;
        bottom: -12rpx;
        z-index: 3;
        padding: 10rpx 48rpx;
        background-color: rgba($color: $uni-color-primary, $alpha: 0.15);
        color: $uni-color-primary;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 24rpx;
        .btn{
            padding: 6rpx 18rpx;
            border: 1rpx solid $uni-color-primary;
            border-radius: 6rpx;
        }
    }
}

.search-box{
    position: relative;
    padding: 30upx 50upx;
    background-color: #fff;
    .search-icon{
        position: absolute;
        left: 65upx;
        top: 44upx
    }
    input{
        font-size: 28upx;
        background-color: #f5f5f5;
        padding: 12upx;
        padding-left: 60upx;
        border-radius: 6upx;
    }

}


.qrcode-box{
	position: relative;
	margin-top: 15vh;
	.qrimg-box{
		width: 584rpx;
		padding: 12rpx;
		margin: auto;
		background-color: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		.title{
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
			color: #333;
		}
		.tip{
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			color: #666;
		}
	}
	.qrcode{
		width: 560rpx;
		height: 560rpx;
	}
	.icon-box{
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
}
</style>