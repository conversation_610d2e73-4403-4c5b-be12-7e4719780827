<template>
  <view class="customer_detail">
    <view class="form">
      <view class="form_row">
        <view class="input_form">
          <view class="label">客户姓名</view>
          <input class="form_value" type="text" maxlength="10" v-model="customer_detail.cname" placeholder="请输入客户姓名" placeholder-style="font-size: 30rpx; text-align: right; color: #999">
        </view>
      </view>
      <view class="form_row">
        <view class="input_form">
          <view class="label">联系方式</view>
          <input class="form_value" type="number" maxlength="11" v-model="customer_detail.tel" placeholder="请输入联系方式" placeholder-style="font-size: 30rpx; text-align: right; color: #999">
        </view>
      </view>
    </view>
    <view class="form">
      <view class="form_row">
        <view class="input_form">
          <view class="label">意向区域</view>
          <view class="form_value" @click="$refs.area_popup.show()">
            <text v-if="customer_detail.area_id" class="value">{{area_name}}</text>
            <text v-else class="placeholder">请选择意向区域</text>
            <my-icon type="ic_into" color="#999" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
      <view class="form_row">
        <view class="input_form">
          <view class="label">意向小区</view>
          <input class="form_value" type="text"  v-model="customer_detail.community" placeholder="请输入意向小区" placeholder-style="font-size: 30rpx; text-align: right; color: #999">
        </view>
      </view>
    </view>
    <view class="form">
      <view class="form_row">
        <view class="input_form">
          <view class="label">购房进度</view>
          <view class="form_value" @click="$refs.progress.show()">
            <text v-if="customer_detail.speed" class="value">{{customer_detail.speed}}</text>
            <text v-else class="placeholder">请选择进度</text>
            <my-icon type="ic_into" color="#999" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <my-popup ref="area_popup">
      <addressPicker :data_list="area_list" @onselect="onAreaChange"></addressPicker>
    </my-popup>
    <my-popup ref="progress">
      <followUp @cancel="$refs.progress.hide()" :cname="customer_detail.cname" :show="true" :showCname="false" @confirm="onProgressChange"/>
    </my-popup>
    <view class="btn" @click="subAdd">提交</view>
  </view>
</template>

<script>
import mySelect from '@/components/form/mySelect'
import myIcon from '@/components/myIcon'
import myPopup from '@/components/myPopup'
import addressPicker from '@/components/addressPicker.vue'
import followUp from './components/followUp'
export default {
  name: 'CustomerDetail',
  components: {
    mySelect,
    myIcon,
    myPopup,
    addressPicker,
    followUp
  },
  data () {
    return {
      customer_detail:{
        cname: "",
        tel: "",
        community: "",
        area_id: 0,
        speed: "",
        remark: ""
      },
      area_name: "",
      area_list: [],
    }
  },
  onLoad(){
    this.getAreaList()
  },
  methods: {
    onAreaChange(e){
      this.area_name = e[e.length-1].name
      this.customer_detail.area_id = e[e.length-1].value
      this.$refs.area_popup.hide()
    },
    getAreaList() {
      this.$ajax.get('Release/getArea', {}, res => {
        if (res.data.code === 1) {
          this.area_list = res.data.data
        }
      })
    },
    onProgressChange(e){
      this.customer_detail.speed = e.status_text
      this.customer_detail.images = e.imgs
      this.customer_detail.remark = e.descp
      this.$refs.progress.hide()
    },
    subAdd(){
      // 接口要求如果有跟进则必须填写备注
      if(this.customer_detail.speed&&!this.customer_detail.remark){
        uni.showToast({
          title: "请填写跟进备注",
          icon: 'none'
        })
        return
      }
      uni.showLoading({
        title: "提交中...",
      })
      let params = Object.assign({}, this.customer_detail)
      if(!params.speed){
        delete params.speed
        delete params.remark
      }
      this.$ajax.post('im/addCustomer', params, res=>{
        uni.hideLoading()
        if(res.data.code === 1){
          uni.showToast({
            title: res.data.msg
          })
          setTimeout(()=>{
            uni.$emit('getListAgain',{type:3})
            this.$navigateBack()
          }, 1500)
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      }, err=>{
        uni.hideLoading()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.form{
  margin-top: 24rpx;
  padding: 0 48rpx;
  background-color: #fff;
  .form_row{
    padding: 24rpx 0;
    .input_form{
      display: flex;
      justify-content: space-between;
      align-items: center;
      .label{
        font-size: 32rpx;
        color: #666;
        margin-right: 16rpx;
      }
      .form_value{
        height: 72rpx;
        text-align: right;
        box-sizing: border-box;
        padding: 16rpx;
        flex: 1;
        font-size: 30rpx;
      }
      .placeholder{
        color: #999;
      }
    }
  }
}

.btn{
  position: fixed;
  bottom: 64rpx;
  left: 48rpx;
  right: 48rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 8rpx;
  text-align: center;
  font-size: 32rpx;
  background-color: $uni-color-primary;
  box-shadow: 0 8rpx 32rpx 0 rgba($color: $uni-color-primary, $alpha: 0.40);
  color: #fff;
}
</style>