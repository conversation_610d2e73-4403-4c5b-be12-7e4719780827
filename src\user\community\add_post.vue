<template>
	<view class="add-post">
		<!-- <my-input label="标题" :value="params.title" name="title" @input="handleInput"></my-input> -->
		<view class="block bottom-line top-20">
			<view class="block-title bottom-line">评论内容</view>
			<textarea :value="params.content" placeholder="请输入评论内容" maxlength="640" @input="inputContent" />
		</view>
		<view class="upload-box bottom-line" v-if="showUpload">
			<my-upload action="news/uploadFileByWx" @uploadDon="uploadDon" :imgs="imgList" :maxCount="9" :chooseType="2" :videos="videoList"></my-upload>
		</view>
		<block v-if="params.cid">
		<view class="list">
			<uni-list-item title="是否悬赏" :show-switch="true" :show-arrow="false" @switchChange="handleSwitch"></uni-list-item>
		</view>
		<view v-if="params.is_reward==1">
			<my-checkbox v-if="!showColumeMoney" label="悬赏金额" :range="moneyList" :onlayOne="true" @select="handleSelect" name="reward_money"></my-checkbox>
			<my-input v-if="showColumeMoney" label="悬赏金额" type="number" :value="params.reward_money" name="reward_money" @input="handleInput"></my-input>
			<my-checkbox v-if="!showColumeTime" label="有效天数" :range="timeList" :onlayOne="true" @select="handleSelect" name="reward_time"></my-checkbox>
			<my-input v-if="showColumeTime" label="有效天数" type="number" :value="params.reward_time" name="reward_time" @input="handleInput"></my-input>
		</view>
		</block>
		<view class="btn-box">
			<button class="default" @click="subData">发表帖子</button>
		</view>
	</view>
</template>

<script>
	import myInput from '../../components/form/myInput.vue'
	import myUpload from '../../components/form/myUpload.vue'
	import myCheckbox from '../../components/form/myCheckbox.vue'
	import {uniListItem} from "@dcloudio/uni-ui"
	export default{
		components:{
			myInput,
			myUpload,
			myCheckbox,
			uniListItem
		},
		data(){
			return{
				params:{
					content:"",
					imgs:"",
					videos:"",
					is_reward:0,
					reward_money:"",
					reward_time:""
				},
				showUpload:false,
				imgList:[],
				videoList:[],
				showColumeMoney:false,
				showColumeTime:false,
				moneyList:[{name:'1元',value:1},{name:'5元',value:5},{name:'10元',value:10},{name:'20元',value:20},{name:'自定义',value:""}],
				timeList:[{name:'1天',value:1},{name:'3天',value:3},{name:'7天',value:7},{name:'15天',value:15},{name:'自定义',value:""}],
			}
		},
		onLoad(options){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			if(options.id){
				this.params.id = options.id
				this.api = "build/comment.html"
			}else{
				this.showUpload = true
			}
			if(options.cid){
				this.params.cid = options.cid
				this.api = "news/release.html"
			}
		},
		methods:{
			handleInput(e){
				this.params[e._name] = e.detail.value
			},
			inputContent(e){
				this.params.content = e.detail.value
			},
			uploadDon(e){
				// console.log(e)
				if(e.type=="image"){
					this.imgList = e.files
					this.params.imgs = e.files.join(',')
				}
				if(e.type=="video"){
					this.videoList = e.files
					this.params.videos = e.files.join(',')
				}
			},
			handleSwitch(e){
				this.params.is_reward = e.value?1:0
				if(!this.params.is_reward){
					this.params.reward_money = ""
					this.params.reward_time = ""
				}
			},
			handleSelect(e){
				this.params[e._name] = e.value[0]
				if(e.value[0]==""&&e._name=='reward_money'){
					this.showColumeMoney = true
				}
				if(e.value[0]==""&&e._name=='reward_time'){
					this.showColumeTime = true
				}
			},
			subData(){
				// if(!this.params.title){
				// 	uni.showToast({
				// 		title:"请输入标题",
				// 		icon:"none"
				// 	})
				// 	return
				// }
				if(!this.params.content){
					uni.showToast({
						title:"请输入评论内容",
						icon:"none"
					})
					return
				}
				if(this.params.is_reward&&!this.params.reward_money){
					uni.showToast({
						title:"悬赏金额不能为空",
						icon:"none"
					})
					return
				}
				if(this.params.is_reward&&!this.params.reward_time){
					uni.showToast({
						title:"悬赏有效时间不能为空",
						icon:"none"
					})
					return
				}
				this.$ajax.post(this.api,this.params,res=>{
					if(res.data.code == 1){
						setTimeout(()=>{
							this.$navigateBack()
						},1500)
						this.$store.state.updatePageData = true
					 	this.$ajax.post("adviser/ajax",{act : 'notice',comment_id:res.data.comment_id})
				 }
					uni.showToast({
						title:res.data.msg,
						icon:'none',
						mask:true
					})
				})
			}
		}
	}
</script>

<style lang="scss">
	.block{
		padding: 0 48rpx;
		background-color: #fff;
	}	
	 .block-title{
		padding: $uni-spacing-col-lg 0;
		background-color: #fff;
		color: #666;
		font-size: 24rpx;
	}
	.textarea-row{
		background-color: #fff
	}
	textarea{
		padding: 10upx 0;
		width:100%;
		box-sizing:border-box;
	}
	.upload-box{
		padding: $uni-spacing-row-base 48rpx;
		background-color: #fff;
	}
	.list{
		background-color: #fff
	}
	.set-nickname-box input{
		padding: 10upx;
		border: 1upx solid #f3f3f3;
		border-radius: 5upx;
	}
</style>
