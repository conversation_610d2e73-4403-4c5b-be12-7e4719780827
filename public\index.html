<!DOCTYPE html>
<html lang="zh-CN">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Cache-Control" content="no-cache">
        <meta http-equiv="Cache" content="no-cache">
        <meta http-equiv="Expires" content="0">
        <title>
            <%= htmlWebpackPlugin.options.title %>
        </title>
        <meta name="keywords" content="">
        <meta name="description" content="">
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
            })
            var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
            document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />')
        </script>
        <!-- <script src="https://tfy.tengfun.com/h5/static/js/jweixin_1.4.0.js"></script> -->
        <link rel="stylesheet" href="<%= BASE_URL %>static/index.css" />
       
		<!-- <script src='//static.app1.magcloud.net/public/static/dest/js/libs/magjs-x.js'></script> -->
        <!-- <script src='<%= BASE_URL %>static/js/video-js.js'></script>  -->
        <!-- <script src='https://vjs.zencdn.net/8.0.4/video.min.js'></script> -->

    </head>

    <body>
        <noscript>
            <strong>Please enable JavaScript to continue.</strong>
        </noscript>
        <div id="app"></div>
        <!-- built files will be auto injected -->
    </body>

</html>