<template>
  <view>
    <view class="block">
      <view class="label">
        <text>{{ item_type }}</text>
      </view>
      <textarea :value="tgmask" rows="4" maxlength="300" placeholder="房源权益描述"
        placeholder-style="font-size:28rpx;color:#999" disabled></textarea>
    </view>

    <!-- <view class="btn-box">
      <view class="btn" @click="subData">确定</view>
    </view> -->
  </view>
</template>

<script>
import myUpload from '../components/form/myUpload'
export default {
  components: {
    myUpload
  },
  data() {
    return {
      tgmask: '',
      type: 0,
      item_type: '',
    }
  },
  onLoad() {
    uni.$once('gitetgcation', (data) => {
      this.tgmask = data.tgmask
      this.type = data.type
      if (this.type == 1) {
        this.item_type = '置顶房源权益描述'
      } else {
        this.item_type = '精选房源权益描述'
      }
    })
  },
  methods: {

    subData() {
      uni.$emit('settgcation', this.tgmask)
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.block {
  padding: 0 48rpx;
  padding-top: 24rpx;
  background-color: #fff;
  padding-bottom: 50rpx;

  .label {
    padding: 24rpx 0;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
  }

  textarea {
    width: 100%;
    height: 250rpx;
    padding: 20rpx;
    box-sizing: border-box;
    line-height: 1.5;
    font-size: 28rpx;
    background-color: #f3f3f3;

  }
}

.btn-box {
  padding: 48rpx;

  .btn {
    height: 88rpx;
    line-height: 88rpx;
    background: #FB656A;
    box-shadow: 0 8rpx 32rpx 0 rgba(251, 101, 106, 0.40);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    text-align: center;
  }
}
</style>