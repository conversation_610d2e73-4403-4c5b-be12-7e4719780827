import Vue from 'vue'
import App from './App'
import store from './store'
import formatImg from './common/mixin/image_filter'
import wxApi from './common/mixin/wx_api'
import {navigateTo, ajax, uploadFile} from './common/index'
import { config } from './common/config'
import chatTip from '@/components/chatTip'
import telPop from '@/components/telPop/telPop'
// #ifdef H5
// import Vconsole from 'vconsole'
// #endif
store.state.firstUrl = window.location.href
let navigateBack = function (num = 1, switch_url){
    if (getCurrentPages().length > 1) {
        uni.navigateBack({
            delta:num
        })
    } else {
        uni.switchTab({
            url: switch_url||'/pages/index/index'
        })
    }
}
Vue.prototype.$navigateTo = navigateTo
Vue.prototype.$navigateBack = navigateBack
Vue.prototype.$store = store
Vue.prototype.$ajax = ajax
Vue.prototype.$uploadFile = uploadFile
Vue.prototype.$imgDomain = config.imgDomain

Vue.config.productionTip = false
Vue.component('chatTip', chatTip)
Vue.component('telPop', telPop)
Vue.mixin(formatImg)
Vue.mixin(wxApi)
// #ifdef H5
// const vConsole = new Vconsole();
// Vue.use(vConsole)
// #endif

// #ifdef H5
// const cnzz = {
//     mounted(){
//         if(store.state.cnzz){
//             this.handleCnzz()
//         }else{
//             setTimeout(()=>{
//                 this.handleCnzz()
//             },800)
//         }
//     },
//     methods: {
//         handleCnzz(){
//             const script = document.createElement('script')
//             script.src = store.state.cnzz
//             script.language = 'JavaScript'
//             document.body.appendChild(script)
//         }
//     }
// }
// Vue.mixin(cnzz)
// #endif

App.mpType = 'app'

const app = new Vue({
    ...App
})
app.$mount()
