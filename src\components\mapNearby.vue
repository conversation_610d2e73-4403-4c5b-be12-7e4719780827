<template>
  <view class="map-box">
    <map
      style="width: 100%; height: 360rpx;"
      id="map"
      :scale="scale"
      :latitude="lat"
      :longitude="lng"
      :markers="markers"
      :circles="cirles"
      :enable-zoom="enableZoom"
      :enable-scroll="enableScroll"
      @regionchange="regionChange"
      @click="onClickMap"
    ></map>
    <block v-if="showCate">
      <view class="map-cate-list">
        <view class="cate-item" :class="{active: current === '交通'}" @click="getCovers('交通')">
          <!-- <image class="image" :src="img_domain+'/images/icon/jiaotong.png'"></image> -->
          <view class="text">交通</view>
        </view>
        <view class="cate-item" :class="{active: current === '商业'}" @click="getCovers('商业')">
          <!-- <image class="image" :src="img_domain+'/images/icon/shangye.png'"></image> -->
          <view class="text">商业</view>
        </view>
        <view class="cate-item" :class="{active: current === '教育'}" @click="getCovers('教育')">
          <!-- <image class="image" :src="img_domain+'/images/icon/jiaoyu.png'"></image> -->
          <view class="text">教育</view>
        </view>
        <view class="cate-item" :class="{active: current === '医疗'}" @click="getCovers('医疗')">
          <!-- <image class="image" :src="img_domain+'/images/icon/yiliao.png'"></image> -->
          <view class="text">医疗</view>
        </view>
      </view>
      <view class="res_list">
        <view class="item" v-for="(item, index) in markers" :key="index" v-show="item.title" @click="moveTo(item.latitude, item.longitude)">
          <text class="title">{{item.title}}</text>
          <text class="address">{{item.address}}</text>
          <text class="distance">{{item._distance | distanceFormat}}</text>
        </view>
      </view>
    </block>
  </view>
</template>

<script>
export default {
  components: {},
  props:{
    scale:{
      type:Number,
      default:14
    },
    showCate:{
      type:Boolean,
      default:true,
    },
    enableScroll:{
      type:Boolean,
      default:true
    },
    enableZoom:{
      type:Boolean,
      default:true,
    },
    lat:[Number,String],
    lng:[Number,String],
    markers:Array,
    cirles:Array
  },
  data() {
    return {
      current: '交通',
      currentScale:14
    }
  },
  created(){
    this.currentScale = this.scale
    this.$emit('clickCate', {type:this.current,scale:this.currentScale})
    
    this.map= uni.createMapContext("map",this)
  },
  filters:{
    distanceFormat(val){
      if(!val){
        return ''
      }
      if(val<1000){
        return Math.ceil(val)+'m'
      }else{
        return (val/1000).toFixed(1)+'km'
      }
    }
  },
  methods: {
    getCovers(type){
      this.current = type
      this.$emit('clickCate',{type,scale:this.currentScale})
    },
    regionChange(){
        this.map.getScale({
					success:(res)=>{
            if(this.currentScale == res.scale ) return 
            this.currentScale =res.scale
            this.$emit('clickCate',{type:this.current,scale:this.currentScale})
					}
				})
    },
    onClickMap(){
      // #ifdef H5
      if(!this.click_time||Date.now()-this.click_time>200){
        this.$emit('clickMap')
        this.click_time = Date.now()
      }
      // #endif
      // #ifndef H5
      this.$emit('clickMap')
      // #endif
      
    },
    moveTo(lat, lng){
      // var map = uni.createMapContext('map', this)
      // map.moveToLocation({
      //   latitude: parseFloat(lat),
      //   longitude: parseFloat(lng)
      // })
    }
  }
}
</script>

<style scoped lang="scss">
.map-box {
  width: 100%;
  position: relative;
  .map-cate-list {
    padding: 16rpx 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    .cate-item {
      // flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      padding: 8rpx;
      &.active{
        color: $uni-color-primary;
        &:after{
          content: '';
          position: absolute;
          bottom: 0;
          left: 12rpx;
          right: 12rpx;
          height: 4rpx;
          background-color: $uni-color-primary;
        }
      }
      .image {
        width: 48rpx;
        height: 48rpx;
        margin-right: 6rpx;
      }
      .text {
        font-weight: bold;
        font-size: 26rpx;
      }
    }
  }
}
.res_list{
  max-height: 400rpx;
  overflow-y: auto;
  .item{
    display: flex;
    flex-direction: row;
    width: 100%;
    overflow: hidden;
    font-size: 26rpx;
    ~.item{
      margin-top: 24rpx;
    }
    .title{
      flex-shrink: 0;
    }
    .address{
      margin: 0 12rpx;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #999;
    }
    .distance{
      flex-shrink: 0;
      color: #999;
    }
  }
}
</style>
