<template>
  <view class="page">
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  components: {},
  data() {
    return {
      code: ''
    }
  },
  computed: {
    ...mapState(['user_login_status'])
  },
  onLoad(options) {
    if (options.code) {
      this.code = options.code
    }
    uni.$on("getDataAgain",()=>{
      this.bindCompany()
    })
    this.getUserStatus()
  },
  methods: {
    getUserStatus() {
      this.$ajax.get('member/checkUserStatus', {}, res => {
        this.$store.state.user_login_status = res.data.status || ''
        if (res.data.status == 1) {
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
          return
        }
        if (res.data.status === 2) {
          this.$navigateTo('/user/bind_phone/bind_phone')
          return
        }
        this.bindCompany()
      })
    },
    bindCompany(){
        this.$ajax.get('Enterprise/bindEnterprise',{code:this.code },res=>{
          uni.showToast({
            title:res.data.msg ,
            icon:"none"
          })
          if (res.data.code == 1) {
            setTimeout(() => {
              this.$navigateBack()
            }, 1000);
            
          }
        })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
