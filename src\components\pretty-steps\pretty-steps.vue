<template>
	<view class="steps">
		<view class="steps_list">
			<view class="node" :class="{'active-node':item.activity == item.index}" ></view>
			<view class="tail" :class="{'active-tail':item.activity == item.index}" :hidden="item.index == 0"></view>
		</view>
		<view class="wrapper">
			<view class="date">
				{{item.ctime}}
			</view>
			<view class="content">
				<view class="content_l">
					<image :src="item.prelogo" mode="aspectFill"></image>
				</view>
				<view class="content_r">
					<view class="name">
						<text v-if="item.username">{{ item.username }}</text>
						<text v-if="item.owner_name">业主姓名：{{ item.owner_name }}</text>
						<text v-if="item.owner_tel">业主电话：{{ item.owner_tel }}</text>
					</view>
					<view v-if="item.is_valid==1" class="valid">有效房源</view>
					<view v-if="item.is_valid==2" class="invalid">无效房源</view>
					<view class="scan_time">
						{{ item.content }}
					</view>
				</view>

			</view>
		</view>
	</view>
</template>

<script>
	/*
	item：没条记录详情
	activity: 当前进度条状态
	wrapperStatus：流程状态对应字段
	wrapperTitle：详情对应字段
	index：进度条排列序号 index == 0代表最后一个没有竖立进度条
	date：时间
	 */
	export default {
		name: 'm-steps',
		data() {
			return {

			}
		},
		props: {
			item: {
				type: Object,
			},

		},
		methods: {

		}
	}
</script>

<style lang="scss" scoped>
	.steps {
		display: flex;
		// background-color: #eee;
		
		.steps_list {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 0 10px;
		}

		.date {
			color: #AAAAAA;
			text-align: left;
			padding-bottom: 10px;
		}
		.wrapper {
			width: 100%;
		}
		.tail {
			height: calc(100% - 15px);;
			width: 2px;
			background-color:  #eee;
		}
		.active-tail {
			width: 2px;
			// background-color:  #5b76ec !important;
			background-color:  #0074fd !important;
		}

		.active-node {
			background-color: #0074fd !important;
		}

		.node {
			width: 15px;
			height: 15px;
			background-color: #aaa;
			border-radius: 50%;
		}
		.content {
			width: 95%;
			background-color: #fff;
			border-radius: 5px;
			display: flex;
			padding: 10px 0;
			margin-bottom: 10px;
			align-items: flex-start;
		}

		.content_l {
			margin-right: 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 50px;
				height: 50px;
				border-radius: 50%;
				object-fit: cover;
			}
		}

		.content_r {
			width: 70%;
			.invalid{
				color: #858585;
				background-color: #f5f5f5;
				border:2rpx solid #e7e7e7;
				display: inline-block;
				padding: 0 10rpx 2rpx 10rpx;
				font-size: 22rpx;
				margin-top: 10rpx;
				border-radius: 4rpx;
			}
			.valid{
				color: #409EFF;
				background-color: #ecf5ff;
				border:2rpx solid #d9ecff;
				display: inline-block;
				padding: 0 10rpx 4rpx 10rpx;
				font-size: 22rpx;
				margin-top: 10rpx;
				border-radius: 4rpx;
			}
			.name {
				color: #000;
				text{
					display: block;
					margin-bottom: 2rpx;
				}
			}
			.scan_time {
				font-size: 12px;
				color: #333;
				margin: 8px 0 0;
			}
		}
	}
</style>
