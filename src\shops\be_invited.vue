<template>
<view class="page">
    <view class="desc">
        您的好友，<text class="friend_name">{{infoData.nickname}}</text>通过<text>{{projectName}}</text>向你发送邀请，邀请您加入<text class="friend_name">{{storeData.name}}</text>。
    </view>
    <!-- <agent-card :infoData="infoData"></agent-card> -->
    <!-- <view class="invited-box">
        <view class="invited-title">邀请码</view>
        <view class="invited">{{invited}}</view>
        <view class="invited-btn" @click="copyInvited(invited)">点击复制</view>
    </view> -->
    <view class="btn-box">
        <button class="btn" @click="toAdd()">接收邀请 立即加入</button>
    </view>
</view>
</template>

<script>
import agentCard from '../components/agentCard'
import {navigateTo, config} from "../common/index.js"
export default {
    data() {
        return {
            invited:"",
            from_user_id:"",
            infoData:{},
            storeData:{},
            projectName:config.projectName
        }
    },
    components: {
        agentCard
    },
    onLoad(options){
        if(options.invited){
            this.invited = options.invited
        }
        if(options.user_id){
            this.from_user_id = options.user_id
            this.store_id=options.store_id
            this.getUserInfo(this.from_user_id)
            this.getStoreInfo(this.store_id)
        }
    },
    methods: {
        getUserInfo(user_id){
            this.$ajax.get('im/contactDetails.html',{user_id},res=>{
                if(res.data.code === 1){
                    this.infoData = res.data.member
                }
            })
        },
        getStoreInfo(id){
            this.$ajax.get('agentCompany/getStoreDetail.html',{id},res=>{
                if(res.data.code === 1){
                    this.storeData = res.data.store
                }
            })
        },
        copyInvited(content){
            // #ifdef H5
            let oInput = document.createElement('input');
            oInput.value = content;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            document.execCommand("Copy"); // 执行浏览器复制命令
            uni.showToast({
                title:"复制成功",
                icon:"none"
            })
            oInput.remove()
            // #endif
            // #ifndef H5
            uni.setClipboardData({
                data: content,
                success: function () {
                    uni.showToast({
                        title:"复制成功"
                    })
                }
            });
            // #endif
        },
        toAdd(){
            this.$ajax.get('agentCompany/joinStore.html',{store_id:this.store_id},res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title:res.data.msg
                    })
                    setTimeout(() => {
                        uni.switchTab({url:'/'})
                    }, 1000);
                }else {
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            })
            // navigateTo('/user/consultant/add?invited='+this.invited+"&share=1")
        }
    }
}
</script>

<style lang="scss">
.desc{
    padding: 20upx 50upx;
    margin: 50upx 0;
    font-size: 28upx;
    line-height: 1.6;
    .build_name{
        color: #24a7d3
    }
    .friend_name{
        color: #ff6699;
    }
}
.invited-box{
    text-align: center;
    margin: 50upx 0;
    .invited-title{
        font-size: 30upx;
        margin-bottom: 10upx;
    }
    .invited{
        font-size: 106upx;
        font-weight: bold;
        color: #ff6699
    }
    .invited-btn{
        margin-top: 30upx;
        display: inline-block;
        padding: 13upx 35upx;
        border-radius: 20upx;
        font-size: 28upx;
        border: 1upx solid #ff6699
    }
}
.btn-box{
    .btn{
        width: 70%;
        background-color: #00cc33;
        font-size: 40upx;
        height: 82upx;
        line-height: 82upx;
        color: #fff;
    }
}
</style>
