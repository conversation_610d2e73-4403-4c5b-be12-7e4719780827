<template>
  <view class="adviser-container white-bg">
    <view class="back  flex-row">
      <view class="icon-box flex-row"  @click="goBack">
          <my-icon type="ic_back" color="#fff" size="48rpx"></my-icon>
      </view>
      <!-- <text class ="title-con" >{{detail.cname}}</text> -->
    </view>
    <view class="card-box">
      <view class="bg"></view>
      <view
        class="adviser-card"
        :class="{
          jin: adviser_info.levelid === 1,
          yin: adviser_info.levelid === 2,
          tong: adviser_info.levelid === 3
        }"
      >
        <image class="level-icon" :src="level_icon | imageFilter('w_240')"></image>
        <view class="adviser-info flex-row">
          <view class="header-img-box" @click.prevent.stop="prevImg(adviser_info.prelogo)">
            <image class="header-img" :src="adviser_info.prelogo | imageFilter('w_240')" mode="widthFix"></image>
          </view>
          <view class="info">
            <view class="name-row flex-row">
              <text class="name">{{ adviser_info.cname }}</text>
              <text class="level_name">{{ adviser_info.level_name }}</text>
            </view>
            <view class="builds">主营楼盘：{{ adviser_info.build_names }}</view>
            <!-- <view class="activity">活跃度：{{ adviser_info.active }}</view> -->
          </view>
        </view>
        <view class="adviser-data flex-row">
          <view class="data-item left">
            <text class="label">浏览量</text>
            <text class="value">{{adviser_info.browse}}</text>
          </view>
          <view class="data-item center">
            <text class="label">咨询量</text>
            <text class="value">{{adviser_info.traffic_volume||0}}</text>
          </view>
          <view class="data-item right">
            <text class="label">点赞数</text>
            <text class="value">{{adviser_info.star}}</text>
          </view>
        </view>
        <view class="autograph">个性签名：{{adviser_info.minfo||'暂无'}}</view>
        <view class="browse_user_list flex-row">
          <view class="browse_user flex-row" @click="$navigateTo('/user/consultant/seeme?id=' + id)">
            <image
              class="prelogo"
              v-for="item in browse_user_list"
              :key="item.id"
              :src="item.prelogo | imageFilter('w_80')"
            ></image>
          </view>
          <text class="browse_num">浏览量：{{adviser_info.browse}}</text>
        </view>
      </view>
    </view>
    <view class="options flex-row">
      <view v-for="(item,index) in toolBtns" :key="index">
        <!-- 1：转发 2：点赞 3：生成海报 4：加我好友-->
        <template v-if="item.event == 1">
          <button class="item" @click="showSharePopup()">
            <view class="icon_box share">
              <image class="jhy" :src="item.icon" mode="widthFix"></image>
            </view>
            <text>{{item.title}}</text>
          </button>
        </template>
        <template v-if="item.event == 2">
          <view class="item" @click="handleZan()">
            <view class="icon_box zan">
              <image class="jhy" :src="item.icon" mode="widthFix"></image>
            </view>
            <text>{{item.title}}</text>
          </view>
        </template>
        <template v-if="item.event == 3">
          <view class="item" @click="onClickCreatPoster()">
            <view class="icon_box post">
              <image class="jhy" :src="item.icon" mode="widthFix"></image>
            </view>
            <text>{{item.title}}</text>
          </view>
        </template>
        <template v-if="item.event == 4">
          <view class="item" @click="showWechat()">
            <view class="icon_box">
                <image class="jhy" :src="item.icon" mode="widthFix"></image>
            </view>
            <text>{{item.title}}</text>
          </view>
        </template>
      </view>
    </view>
    <!-- 入驻楼盘 -->
    <view class="builds-box">
      <view class="label">入驻楼盘</view>
      <scroll-view scroll-x style="width: 100%" class="build-list">
        <view
          class="build-item"
          v-for="item in service_builds"
          :key="item.id"
          @click="$navigateTo(`/pages/new_house/detail?id=${item.id}&shareId=${adviser_info.id}&shareType=1`)"
        >
          <image class="build_img" :src="item.pico | imageFilter('w_400')" mode="aspectFill"></image>
          <view class="build_name">{{ item.title }}</view>
        </view>
      </scroll-view>
    </view>

    <!-- 分享 -->
    <view class="share-box">
      <view class="label">TA的分享</view>
      <view class="share-list">
        <share-item v-for="(share, index) in share_list" :key="share.id" :share="share" @clickpraise="handlePraise($event, index)" @clickvoice="onClickVoice" @voiceEnded="voice_playing_index=-1" @voiceError="voice_playing_index=-1" :voice_playing="voice_playing_index==index" @click="toShareDetail"></share-item>
        <uni-load-more :status="get_status"></uni-load-more>
      </view>
    </view>

    <view class="add-post" @click="$navigateTo('/user/consultant/addpost?buildid='+ adviser_info.build_ids)" v-if="isMe == 1">
      <my-icon type="jiahao" size="72rpx" color="#fff"></my-icon>
    </view>
    <my-popup ref="wechat" position="center" height="95vw">
      <view class="qrcode-box">
        <image class="wechat-img" v-if="adviser_info.wechat_img" :src="adviser_info.wechat_img | imageFilter('w_6401')" mode="aspectFill"></image>
        <view class="no-wechat" v-else>暂未上传，请点击咨询或拨打电话</view>
        <view class="btn-box">
          <!-- #ifdef H5 -->
          <view class="btn btn-lg">长按识别二维码</view>
          <!-- #endif -->
          <!-- #ifndef H5 -->
          <!-- <view class="btn btn-lg" @click="downImg(adviser_info.wechat_img)">保存到相册</view> -->
          <!-- #endif -->
        </view>
      </view>
    </my-popup>
     <sharePop ref="share_popup" @handleCreat="onClickCreatPoster()" @copyLink="copyLink()" @showCopywriting="showCopywriting()"></sharePop>
    <!-- 底部操作菜单 -->
    <view class="bottom-bar flex-row top-line">
      <!-- <view class="bar-left flex-row flex-1">
        <button open-type="share" class="icon-btn">
          <my-icon type="ic_fenxiang" size="50rpx"></my-icon>
          <text>转发</text>
        </button>
        <view class="icon-btn" @click="onClickCreatPoster">
          <my-icon type="ic_erweima" size="50rpx"></my-icon>
          <text>生成海报</text>
        </view>
      </view> -->
      <view class="bar-right flex-row" :class ="{single:!switch_adviser_tel}">
        <chat-btn :user_login_status="login_status" @ok="handleChat()">
          <view class="bar-btn btn1">在线咨询</view>
        </chat-btn>
        <tel-btn v-if='switch_adviser_tel' :user_login_status="login_status" @ok="handleTel">
          <view class="bar-btn btn2">电话咨询</view>
        </tel-btn>
      </view>
    </view>
     <!-- 复制分享文案 -->
    <my-popup ref="text_popup" position="center" :height="text_popup_height">
      <view class="copy-text-box" id="copy-text">
        <!-- <view class="title">{{detail.title}}</view> -->
        <view class="info-box">
          <view class="info-row flex-row">
            <text class="label">顾问名称：</text>
            <text class="value">{{adviser_info.cname}}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">入驻楼盘：</text>
            <text class="value">{{builds}}</text>
          </view>
          <!-- <view class="info-row flex-row">
            <text class="label">地址：</text>
            <text class="value">{{(detail.address||'')}}</text>
          </view> -->
          <view class="button disabled-btn flex-row" v-if="copy_success">
            <my-icon type="check-circle" size="30rpx" color="#999"></my-icon>
            <text class="text">文本已复制</text>
          </view>
          <view class="button" v-else @click="copywriting">复制文本</view>
        </view>
      </view>
    </my-popup>
    <enturstBtn :to_user="adviser_info" @click="$refs.enturst_popup.show()" />
    <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
      <enturstBox @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="showLoginPopup('为方便您及时接收消息通知，请输入手机号码')" :to_user="adviser_info" isDetail=2 />
    </my-popup>
    <!-- #ifndef MP-WEIXIN -->
     <!-- 登录弹窗 -->
       <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
    <!-- #endif -->
    <chat-tip></chat-tip>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import {wxShare} from '../../common/mixin'

import myIcon from '../../components/myIcon.vue'
import shareItem from '../../components/shareItem'
import sharePop from '../../components/sharePop'
import myPopup from '../../components/myPopup.vue'
// #ifndef MP-WEIXIN
import loginPopup from '../../components/loginPopup'
// #endif
import chatBtn from '../../components/open-button/chatBtn'
import telBtn from '../../components/open-button/telBtn'
import getChatInfo from '../../common/get_chat_info'
import { uniLoadMore } from '@dcloudio/uni-ui'
import { formatImg, config, showModal, getSceneParams } from '../../common/index.js'
import shareTip from '../../components/shareTip.vue'
import allTel from '../../common/all_tel.js'
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
export default {
  components: {
    myIcon,
    shareItem,
    uniLoadMore,
    myPopup,
    // #ifndef MP-WEIXIN
    loginPopup,
    // #endif
    chatBtn,
    telBtn,
    shareTip,
    sharePop,
    enturstBtn,
    enturstBox
  },
  data() {
    return {
      // that: this,
      level_icons: [
        {
          color: '#fecc69',
          icon: '/images/new_icon/<EMAIL>'
        },
        {
          color: '#b6d1f8',
          icon: '/images/new_icon/<EMAIL>'
        },
        {
          color: '#e3af79',
          icon: '/images/new_icon/<EMAIL>'
        }
      ],
      adviser_info: {
        id: "",
        levelid: 0
      },
      isMe: 0,
      is_zan: 0, //是否点赞
      isfollow: 0, //是否关注
      browse_user_list: [], //浏览的用户的头像
      service_builds: [], //入驻的楼盘
      share_list: [],
      share_list_params: {
        page: 1,
        rows: 10
      },
      quest_list:[],
      quest_list_params:{
        page:1,
        rows:10
      },
      get_status: 'loading',
      get_quest_status: 'loading',
      show_quest_or_share:'share',
      id:'',
      login_tip: '',
      show_share_tip: false,
      text_popup_height:'',
      shareType:'',
      sid:'',
      currentUserInfo:{},
      builds:'',
      link:'',
      copy_success:false,
      toLogin:true,
      voice_playing_index: -1,
      tel_res: {},
      show_tel_pop: false,
      toolBtns:[]
    }
  },
  computed: {
    is_open_im() {
      //是否开通了聊天共功能
      return this.$store.state.im.ischat
    },
    is_oprn_middle_num() {
      //是否开通了中间号功能
      return this.$store.state.im.istelcall
    },
    login_status() {
      //当前用户的登录状态
      return this.$store.state.user_login_status
    },
    level_icon(){ //级别的图标
      if (!isNaN(this.adviser_info.levelid - 1) && this.adviser_info.levelid - 1 >= 0) {
        return this.level_icons[this.adviser_info.levelid - 1].icon
      } else {
        return ''
      }
    },
    level_color() { //级别的颜色
      if (this.is_zan && !isNaN(this.adviser_info.levelid - 1) && this.adviser_info.levelid - 1 >= 0) {
        return this.level_icons[this.adviser_info.levelid - 1].color
      } else {
        return '#dedede'
      }
    },
    poster_styles(){ //生成海报的颜色列表
      return this.$store.state.shareStyles
    },
    switch_adviser_tel(){
      return this.$store.state.switch_adviser_tel
    }
  },
  mixins: [wxShare],
  onLoad(options) {
    // #ifdef  MP
    if (options.scene) {
      const params = getSceneParams(decodeURIComponent(options.scene))
      if (params.id) {
        this.id = params.id
        this.getAdviserInfo()
      }
      return
    }
    // #endif
    if (options.shareId){
      this.sid=options.shareId
      this.shareType=options.shareType
      this.share_time =options.f_time||''
    }
    if (options.id) {
      this.id = options.id
      this.getAdviserInfo()
    }

    /**
     * 预留接收页面通讯，刷新页面数据
     */
    // *********
  },
  filters: {
    // 获取时间的月和日
    timeFilter(val, type) {
      if (!val) {
        return ''
      }
      let time = new Date(val)
      let month = time.getMonth()
      let day = time.getDate()
      if (type === 'month') {
        return month + 1 + '月'
      }
      return (month + 1 < 10 ? '0' + (month + 1) : month + 1) + '-' + (day < 10 ? '0' + day : day)
    }
  },
  methods: {
    prevImg(img){
        uni.previewImage({
            urls: [formatImg(img,'w_8001')],
            current: 0
        })
    },
    // 获取置业顾问信息
    getAdviserInfo() {
      this.$ajax.get('adviser/showAdvInfo', { id: this.id, sid:this.sid,sharetype:this.shareType, forward_time:this.share_time ||''}, res => {
        // #ifdef H5 || MP-BAIDU
        if (res.data.seo) {
          let seo = res.data.seo
          if (res.data.share && res.data.share.pic) {
            seo.image = formatImg(res.data.share.pic, 'w_8001')
          }
          this.seo = seo
        }
        // #endif
        this.$store.state.user_login_status = res.data.status
        if (res.data.code == 1) {
          this.adviser_info = res.data.data[0]
          this.adviser_info.adviser_id = this.adviser_info.id
          this.is_zan = res.data.isStar //是否点赞
          this.isfollow = res.data.isfollow //是否关注
          this.isMe = res.data.isMe
          this.toolBtns = res.data.toolBtns //置业顾问按钮列表
          // if (this.isMe){
          //     this.sid=this.adviser_info.id
          //     this.shareType=1
          // }
          this.currentUserInfo=res.data.shareUser
          if (this.currentUserInfo.adviser_id>0){
              this.currentUserInfo.shareType=1
              this.currentUserInfo.sid=this.currentUserInfo.adviser_id
          }else if (this.currentUserInfo.agent_id>0) {
            this.currentUserInfo.shareType=2
            this.currentUserInfo.sid=this.currentUserInfo.agent_id
          }else {
            this.currentUserInfo={
              shareType:this.shareType||'',
              sid:this.sid ||''
            }
          }
          let browse_user_list = [] //定义变量防止一直添加

          //当前为最后一个访问的在最后显示
          res.data.seelist.map(item => {
            if (item.prelogo) {
              let obj = {
                prelogo: item.prelogo
              }
              browse_user_list.unshift(obj) //当前为最后一个访问的在最后显示
            }
            this.browse_user_list = browse_user_list
          })
          if(this.sid&&this.shareType){ //分享链接没登陆情况下弹出授权登录
            this.$ajax.get('member/checkUserStatus', {}, res => {
                if (res.data.code === 1) {
                } else {
                  if (this.toLogin==false) return 
                  this.toLogin=false
                  this.$store.state.user_login_status = res.data.status
                  if(this.$store.state.user_login_status==1){
                        uni.setStorageSync('backUrl', window.location.href)
                        this.$navigateTo("/user/login/login")
                  }
                }
                })
             
          }

          this.getServiceBuilds(this.adviser_info.build_ids)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    /**
     * @desc 获取入驻楼盘
     * @param {String} ids - 入驻楼盘的id集合
     */
    getServiceBuilds(ids) {
      this.$ajax.get(
        'adviser/getBuildInfo',
        {
          buildid: ids
        },
        res => {
          if (res.data.code == 1) {
            this.service_builds = res.data.data
            let buildName =[]
            this.service_builds.map(item=>{
              buildName.push(item.title)
            })
            if (buildName.length>0){
              this.builds=buildName.join(",")
            }
            
            // 获取入驻楼盘对应的cid
            this.cids = this.service_builds.map(item => item.cid).join(',')
            let title=""
            if (this.service_builds.length>0){
              let buildNames =this.service_builds[0].title
                title = "我是"+buildNames+"置业顾问-"+this.adviser_info.cname+" 欢迎向我咨询"
            }else {
              title ="我是置业顾问-"+this.adviser_info.cname+" 欢迎向我咨询"
            }
            let time =parseInt(+new Date()/1000)
            this.share = {
              title:this.adviser_info.card_title||title,
              content:this.adviser_info.minfo||"",
              pic:this.adviser_info.prelogo,
              link: window.location.origin+'/h5/pages/consultant/detail?id='+this.id+'&shareId='+this.currentUserInfo.sid+'&shareType='+this.currentUserInfo.shareType+"&f_time="+time
            }
            this.getWxConfig()
            // this.getQuestList(cids)
            this.getShareList(this.cids)
          }else{
            this.get_status = 'noMore'
          }
        },err=>{
          this.get_status = 'noMore'
        }
      )
    },
    /**
     * @desc 获取分享列表
     * @param {String} build_ids - 入驻楼盘对应的cid集合
     */
    getShareList(build_ids) {
      // if(!build_ids){
      //   this.get_status = 'noMore'
      //   return
      // }
      // this.share_list_params.cid = build_ids
      this.share_list_params.uid = this.adviser_info.mid
      this.get_status = 'loading'
      if (this.share_list_params.page === 1) {
        this.share_list = []
      }
      this.$ajax.get('building_circle/listsOfMember', this.share_list_params, res => {
        if (res.data.code == 1) {
          if (res.data.lists.length===0) {
            this.get_status = 'noMore'
          } else {
            this.get_status = 'more'
          }
          this.share_list = this.share_list.concat(res.data.lists)
        } else {
          this.get_status = 'noMore'
        }
      })
    },
    goBack(){
      this.$navigateBack()
      // if (this.sid){
      //   uni.switchTab({
      //       url: '/pages/index/index'
      //   });
      // }else {
      //   history.back();
      // }
    },
    // 获取说说问答
    getQuestList(build_ids){
      this.quest_list_params.cid = build_ids
      this.quest_list_params.mid = this.adviser_info.mid
      this.get_quest_status = 'loading'
      if (this.quest_list_params.page === 1) {
        this.quest_list = []
      }
      this.$ajax.get('adviser/getQuestList.html', this.quest_list_params, res => {
        if (res.data.code == 1) {
          if (res.data.data.length < this.quest_list_params.rows) {
            this.get_quest_status = 'noMore'
          } else {
            this.get_quest_status = 'more'
          }
          this.quest_list = this.quest_list.concat(res.data.data)
        } else if (res.data.code == 0) {
          this.quest_list_params.page > 1 && this.quest_list_params.page--
          this.get_quest_status = 'noMore'
        }
        console.log(this.quest_list)
      })
    },
    // 处理点赞
    handleZan() {
      this.$ajax.get(
        'adviser/adv_addstar',
        {
          id: this.id
        },
        res => {
          if (res.data.code == 1) {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            if (this.is_zan) {
              this.is_zan = 0
            } else {
              this.is_zan = 1
            }
            // 更新点赞数量
            this.adviser_info.star = res.data.NowStar
            this.$store.state.updatePageData = true
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
          }
        }
      )
    },
    checkLogin(tip, callback) {
      this.$ajax.get('member/checkUserStatus', {}, res => {
        if (res.data.code === 1) {
          callback&&callback()
        }else if(res.data.status == 1){
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
        }else if(res.data.status == 2){
          this.$store.state.user_login_status = res.data.status
          this.login_tip = tip
          this.$refs.login_popup.showPopup()
        }
      })
    },
    showLoginPopup(tip){
        this.login_tip = tip
        this.$refs.login_popup.showPopup()
    },
    handleCloseLogin() {
        if (this.$store.state.user_login_status === 1) {
            uni.removeStorageSync('token')
            this.$navigateTo('/user/login/login')
        }
        if(this.$store.state.user_login_status===2){
            this.$navigateTo('/user/bind_phone/bind_phone')
        }
    },
    onLoginSuccess(res){
        this.$store.state.user_login_status = 3
        if(this.weituo_is_show){
            console.log("登录成功后继续执行委托接口")
            this.$refs.enturst_box.handleEnturst()
        }
    },
    showSharePopup(){
      this.$refs.share_popup.show()
      this.getShortLink()
    },
    getShortLink(){
      let time =parseInt(+new Date()/1000)
      this.link = window.location.origin+'/h5/pages/consultant/detail?id='+this.id+'&shareId='+this.currentUserInfo.sid+'&shareType='+this.currentUserInfo.shareType+"&f_time="+time
      this.$ajax.get('build/shortUrl.html', {page_url: this.link}, res=>{
        if(res.data.code === 1){
          this.link = res.data.short_url
        }
      })
    },
     // #ifdef H5
    copyLink(){
      this.show_share_tip = true
    },
    // #endif
    showCopywriting(){
      const query = uni.createSelectorQuery().in(this)
      query.select('#copy-text').fields({rect:true,scrollOffset:true,size:true},data => {
        this.text_popup_height = data.height+'px'
      }).exec();
      this.copy_success = false
      this.$refs.text_popup.show()
      this.$refs.share_popup.hide()
    },
    copywriting(){
      const content = `【顾问名称】${this.adviser_info.cname}
【入驻楼盘】${this.builds}
【链接】${this.link}`
      this.copyContent(content,()=>{
        this.copy_success = true
      })
    },
    // 复制内容
    copyContent(content, callback){
      // #ifndef H5
      uni.setClipboardData({
        data: content,
        success: res => {
          if(callback) callback()
        }
      })
      // #endif
      // #ifdef H5
      let oInput = document.createElement('textarea')
      oInput.value = content
      document.body.appendChild(oInput)
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      oInput.blur()
      oInput.remove()
      if(callback) callback()
      // #endif
    },
    // 发起聊天
    handleChat() {
      // 没开启聊天功能显示微信二维码
      if (!this.is_open_im) {
        this.$refs.wechat.show()
        return
      }
      // #ifdef MP-WEIXIN
      getChatInfo(this.adviser_info.mid, 5)
      //  #endif
      // #ifndef MP-WEIXIN
      this.checkLogin('为方便您及时接收消息通知，请输入手机号码', ()=>{
        getChatInfo(this.adviser_info.mid, 5)
      })
      //  #endif
    },
    // 拨打电话
    handleTel() {
      if (this.imistel ==0){
        uni.makePhoneCall({
          phoneNumber: this.adviser_info.tel
        });
        this.$ajax.get("im/callUpStatistics", {
          id: this.adviser_info.mid,
          tel: this.adviser_info.tel,
          type: 2,
        }, res => {
          console.log(res);
        })
        return 
      }
      this.tel_params = {
        type: 2,
        callee_id:this.adviser_info.id,
        scene_type:2,
        scene_id:this.adviser_info.id,
        source: 'adviser_detail',
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      // #ifdef MP-WEIXIN
      allTel(this.tel_params)
      // #endif
      // #ifndef MP-WEIXIN
      this.tel_params.intercept_login = true
      this.tel_params.fail = (res)=>{
        if(res.data.code === -1){
          this.$store.state.user_login_status = 1
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
        }
        if(res.data.code === 2){
          this.$store.state.user_login_status = 2
          this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
          this.$refs.login_popup.showPopup()
        }
      }
      allTel(this.tel_params)
      // #endif
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    // 点击生成海报
    onClickCreatPoster(){
      this.$navigateTo(`${location.origin}/wapi/poster/branch?type=6&id=${this.id}&header_from=2`)
    },
    toShareDetail(id){
      this.$navigateTo('/pages/community/detail?id=' + id + '&adv=' + this.adviser_info.id)
    },
    downImg(img){
      uni.getImageInfo({
        src: img,
        success: image => {
          uni.saveImageToPhotosAlbum({
            filePath: image.path,
            success: () => {
              uni.showToast({
                title: '保存成功'
              })
            }
          })
        }
      })

    },
    handlePraise(e, index){
      this.$ajax.post('building_circle/praise',{id: e.id, value: e.ispraise?0:1},res=>{
        if(res.data.code == 1){
          if (e.ispraise === 0) {
            this.share_list[index].ispraise = 1
            this.share_list[index].praise_count+=1
          } else {
            this.share_list[index].ispraise = 0
            this.share_list[index].praise_count-=1
          }
        }
        uni.showToast({
          title:res.data.msg,
          icon:'none'
        })
      })
    },
    onClickVoice(src){
      // 判断点击的哪个语音
      var voice_playing_index = this.share_list.findIndex(item=>item.attached&&item.attached.length>0&&item.attached[0].path == src)
      if(this.voice_playing_index === voice_playing_index){
        this.voice_playing_index = -1
      }else{
        this.voice_playing_index = voice_playing_index
      }
    },
    // 加我为好友
    showWechat(){
      this.$refs.wechat.show()
    },
  },
  onReachBottom() {
    if(this.show_quest_or_share==='share'){
      if (this.get_status === 'noMore') {
        return
      }
      this.share_list_params.page++
      this.getShareList(this.cids)
    }
    if(this.show_quest_or_share==='quest'){
      if (this.get_quest_status === 'noMore') {
        return
      }
      this.quest_list_params.page++
      this.getQuestList()
    }
  }
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.adviser-container {
  padding-bottom: 130rpx;
}


.card-box{
  width: 100%;
  // height: 346rpx;
  padding: 88rpx 48rpx 0 48rpx;
  position: relative;
  z-index: 1;
  // margin-bottom: 160rpx;
  .bg{
    height: 346rpx;
    background-color: #1E1F25;
    position: absolute;
    top: 0;
    width: 100%;
    left: 0;
    z-index: -1;
  }
}
// 头部的卡片
.adviser-card {
  // position: absolute;
  // left: 48rpx;
  // right: 48rpx;
  background-color: #f3f3f3;
  border-radius: 28rpx;
  margin-bottom: 24rpx;
  &.jin {
    color: #A36418;
    background-image: linear-gradient(135deg, #E5BA72 0%, #FFF4E2 58%, #EAC790 100%);
  }
  &.yin {
    color: #4F5F76;
    background-image: linear-gradient(-46deg, #B1C6E3 0%, #F6FAFF 39%, #B3C3DA 100%);
  }
  &.tong {
    color: #835125;
    background-image: linear-gradient(135deg, #F0B683 0%, #FFEEDE 64%, #F0B683 100%);
  }
  .level-icon {
    width: 108rpx;
    height: 108rpx;
    position: absolute;
    top: 88rpx;
    right: 72rpx;
  }
  .adviser-info {
    padding: 24rpx 48rpx;
    align-items: center;
    position: relative;
    z-index: 3;
    &::after{
      content:'';
      position: absolute;
      bottom: 0;
      left: 48rpx;
      right: 48rpx;
      height: 1rpx;
      background-color: rgba($color: #000000, $alpha: 0.05);
    }
    .header-img-box {
      width: 128rpx;
      height: 128rpx;
      margin-right: 16rpx;
      border-radius: 50%;
      overflow: hidden;
      position: relative;
      background-color: #f3f3f3;
      .header-img {
        width: 100%;
        position: absolute;
      }
    }
    .info {
      flex: 1;
      overflow: hidden;
      .name-row {
        align-items: center;
        margin-bottom: 10rpx;
      }
      .name {
        font-size: 32rpx;
        margin-right: 16rpx;
      }
      .level_name {
        height: 32rpx;
        line-height: 32rpx;
        padding: 0 15rpx;
        border-radius: 16rpx;
        font-size: 22rpx;
        background-color: rgba($color: #000000, $alpha: 0.2);
        color: #fff;
      }
      .builds {
        display: block;
        margin-bottom: 10rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 22rpx;
      }
      .activity {
        display: block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 22rpx;
      }
    }
  }

  .adviser-data {
    padding: 24rpx 48rpx;
    justify-content: space-between;
    .data-item{
      flex: 1;
      line-height: 1;
      &.center{
        align-items: center;
        position: relative;
        &::after{
          content:'';
          position: absolute;
          top: 20rpx;
          bottom: 20rpx;
          right: 0;
          width: 1rpx;
          background-color: rgba($color: #000000, $alpha: 0.05);
        }
        &::before{
          content:'';
          position: absolute;
          top: 20rpx;
          bottom: 20rpx;
          left: 0;
          width: 1rpx;
          background-color: rgba($color: #000000, $alpha: 0.05);
        }
      }
      &.right{
        align-items: flex-end;
      }
      .label{
        font-size: 22rpx;
        margin-bottom: 16rpx;
      }
      .value{
        font-size: 28rpx;
        font-weight: bold;
      }
    }
  }
  .autograph{
    line-height: 1.5;
    padding: 0 48rpx;
    font-size: 22rpx;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .browse_user_list{
    align-items: center;
    padding: 24rpx 48rpx;
    .browse_user{
      .prelogo{
        width: 28rpx;
        height: 28rpx;
        margin-left: -14rpx;
        border-radius: 50%;
        background-color: #f5f5f5;
      }
    }
    .browse_num{
      margin-left: 16rpx;
      font-size: 22rpx;
    }
  }
}


.options{
  line-height: 1;
  align-items: center;
  justify-content: space-between;
  padding: 0 48rpx;
  button{
    margin: 0;
    padding: 0;
    border-radius: 0;
    background: none;
    font-size: 28rpx;
    line-height: 1;
  }
  .item{
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 205rpx;
    padding: 24rpx;
    border-radius: 8rpx;
    background-color: #f5f5f5;
    .icon_box{
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      align-items: center;
      justify-content: center;
      margin-bottom: 16rpx;
      .jhy{
        width:64rpx;
        position: relative;
        top: -4rpx;
      }
      &.share{
        background-color: #00CAA7;
        box-shadow: 0 2px 8px 0 rgba(0,202,167,0.40);
      }
      &.zan{
        background-color: #FB656A ;
        box-shadow: 0 2px 8px 0 rgba(251,101,106,0.40);
      }
      &.post{
        background-color: #4CC7F6;
        box-shadow: 0 2px 8px 0 rgba(76,199,246,0.40);
      }
    }
  }
}
// 入驻楼盘
.builds-box {
  margin-top: 48rpx;
  // padding: 0 48rpx;
  padding-left: 48rpx;
  .label {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
    color: #333;
  }
  .build-list {
    white-space: nowrap;
  }
  .build-item {
    display: inline-block;
    font-size: 0;
    margin-right: 24rpx;
    width: 400rpx;
    .build_img {
      width: 100%;
      height: 220rpx;
    }
    .build_name {
      line-height: 1;
      margin-top: 24rpx;
      font-size: 28rpx;
      color: #333;
    }
  }
}

// 分享模块
.share-box {
  margin-top: 48rpx;
  padding: 0 48rpx;
  .label {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
    color: #333;
  }
}

.add-post{
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 180rpx;
  right: 48rpx;
  background-color: $uni-color-primary;
  box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
}

// 底部菜单
.bottom-bar {
  background-color: #fff;
  height: 110rpx;
  padding: 15rpx 48rpx;
  z-index: 10;
  .icon-btn {
    // width: 100rpx;
    align-items: center;
    padding: 0;
    margin: 0;
    background-color: #fff;
    line-height: initial;
    display: flex;
    flex-direction: column;
    & ~ .icon-btn {
      margin-left: 48rpx;
    }
    text {
      line-height: 1.5;
      font-size: 22rpx;
      color: #333;
    }
  }
  .bar-right{
    width: 100%;
    justify-content: center;
    &.single{
      .bar-btn{
        &.btn1{
          border-top-right-radius: 20px;
          border-bottom-right-radius: 20px;
        }
      }
    }
  }
  .bar-btn {
    width: 240rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    padding: 0;
    margin: 0;
    color: #fff;
    &.btn1{
      background: #FBAC65;
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.05);
      border-top-left-radius: 20px;
      border-bottom-left-radius: 20px;
    }
    &.btn2 {
      background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
      box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.3);
      border-top-right-radius: 20px;
      border-bottom-right-radius: 20px;
    }
  }
}

.card-img {
  width: 80%;
  margin: 0 10%;
  padding: 40upx 0;
  &.long-card{
    margin: 0 17%;
    width: 66%
  }
}

.share-button-box{
  padding: 20upx 0;
  // margin-bottom: 90upx;
  background-color: #fff;
  .tip{
    padding: 10px;
    width: 100%;
    font-weight: 700;
    box-sizing: border-box;
    text-align: center;
  }
  .button-box{
    flex-direction: row;
  }
  button{
    line-height: initial;
    padding: 10upx 20px;
    background-color: #fff;
  }
  .wechat-img{
    width: 60vw;
    height: 60vw;
    margin-left: 20vw
  }
  .item{
    text-align: center;
    padding: 10upx 20px;
    line-height: inherit;
  }
}

/* #ifdef H5 */
// h5海报
#card {
  padding-bottom: 15px;
  width: 100%;
  position: fixed;
  left: -110vw;
}
.cardOuter {
  height: 100%;
  position: relative;
  padding-top: 30px;
}
.cardBg {
  height: 80vw;
  position: absolute;
  top: 0;
  left: 0;
  right: 0px;
}

.cardBgBot {
  /* height: 50px; */
  overflow: hidden;
  position: absolute;
  height: 22px;
  width: 100%;
  top: 79.9vw;
  left: 0;
  font-size: 0;
  /* color: #fff; */
}
.cardBotLeft {
  position: absolute;
  left: -14px;
  top: -40px;
  width: 0;
  height: 0;
  border-width: 50px;
  border-style: solid;
  border-color: transparent #212741 transparent transparent;
  transform: rotate(-30deg);
}
.cardBotRight {
  position: absolute;
  right: -14px;
  top: -40px;
  width: 0;
  height: 0;
  border-width: 50px;
  border-style: solid;
  border-color: transparent transparent transparent #212741;
  transform: rotate(30deg);
}
.logoimg {
  border-top-left-radius: 15upx;
  border-top-right-radius: 15upx;
  margin-top: 50px;
  min-height: 90vw;
  max-height: 125vw;
  box-sizing: border-box;
}
.card_img-box {
  /* width: 100%;
    height: 68vw; */
  margin: 0 40upx;
  /* padding-top: 40upx; */
  overflow: hidden;
}
.logoimg image {
  width: 100%;
  min-height: 90vw;
}

.card_img-box image {
  /* width: 100%; */
  float: left;
  /* height: 100%; */
}
.card-footer.logoinfo {
  margin: 50upx 40upx;
  flex-direction: row;
}
.card-footer.logoinfo .text {
  padding: 0 20upx;
}
.logoinfo .title {
  font-size: 34upx;
  font-weight: bold;
  margin-bottom: 20upx;
}

.logoinfo .text-left {
  font-size: 34upx;
  margin-bottom: 20upx;
}

.card_info-box {
  margin: 40upx;
  padding: 20upx 30upx;
  font-size: 30upx;
  color: #555;
  background-color: #f3f3f3;
}

.text-right {
  text-align: right;
}

.card_info-box .title {
  font-size: 40upx;
  height: 100upx;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 40upx;
  color: #000;
  -webkit-line-clamp: 2;
  display: -webkit-box;
}

.card_info-box .price {
  font-weight: bold;
  color: #f65354;
}

.card-footer {
  margin: 40upx;
  font-size: 34px;
  line-height: 50upx;
  color: #333;

  .text {
    padding: 20upx;
  }

  .tip {
    font-size: 26upx;
    color: #666;
  }

  .qrcode {
    width: 30vw;
    height: 30vw;
  }
}

/* #endif */
/* 名片 */
canvas.hide {
  position: fixed;
  left: -100vw
}
.canvas {
  width: 320px;
}


.qrcode-box{
  padding: 5vw;
  width: 80vw;
  margin-left: 10vw;
  border-radius: 16rpx;
  // margin-bottom: 90upx;
  background-color: #fff;
  .wechat-img{
    width: 70vw;
    height: 70vw;
    margin: 0 auto;
  }
  .no-wechat{
    text-align: center;
    margin-top: 24rpx;
    padding: 180rpx 24rpx;
    font-size: 30rpx;
    color: #999;
  }
  // .btn{
  //   text-align: center;
  //   font-size: 30rpx;
  // }
  button{
    line-height: initial;
    padding: 10upx 20px;
    background-color: #fff;
  }
}
.btn-box{
  padding: 0;
}
.btn-box .btn.btn-lg{
  width: 100%;
  padding: 10upx;
  border-radius: 10upx;
  height: 80upx;
  text-align: center;
  line-height: 60upx;
  box-sizing: border-box;
  font-size: $uni-font-size-lg;
  color: #fff;
  background-color: $uni-color-primary;
}
// 复制文案
.copy-text-box{
  padding: 24rpx 32rpx;
  background-color: #fff;
  width: 600rpx;
  // height: 100%;
  margin-left: 75rpx;
  border-radius: 16rpx;
  .title{
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }
  .info-row{
    line-height: 1.6;
    color: #333;
    .label{
      color: #999;
    }
    .value{
      flex: 1;
      &.highlight{
        color: $uni-color-primary;
      }
    }
  }
  .button{
    align-items: center;
    justify-content: center;
    line-height: 64rpx;
    width: 236rpx;
    text-align: center;
    border-radius: 32rpx;
    margin: 32rpx auto;
    background: #FB656A;
    box-shadow: 0 2px 8px 0 rgba(251,101,106,0.40);
    color: #fff;
  }
  .disabled-btn{
    background-color: #f5f5f5;
    box-shadow: none;
    color: #999;
    >.text{
      margin-left: 12rpx;
    }
  }
}
.back{
        position: fixed;
        width: 100%;
        height: 88rpx;
        padding: 2px 10rpx;
        align-items: center;
        justify-content: space-between;
        // background-image:  linear-gradient(125deg, #FF5500 0%, #FFA402 100%);
        z-index: 3;
        .title-con{
          flex: 1;
          text-align: center;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 32rpx;
          color: #fff;
        }
        .icon-box{
            // height: 44px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            justify-content: center;
            align-items: center;
            padding: 8px;
            background: rgba(0, 0, 0, 0.6);
            justify-content: center;
            align-items: center;
            &.icon-share{
              justify-self: end;
              margin-left: auto;
            }
        }
    }
</style>
