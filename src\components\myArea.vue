<template>
  <view class="cascade_box flex-box">
    <view class="first cate">
      <view class="cascade_list">
        <view class="cascade_item" :class="{selected:selectted_area.length>0&&selectted_area[0]===item.areaid}" v-for="item in first_area" :key="item.areaid" @click="onSelectArea(1,item)">{{item.areaname}}</view>
      </view>
    </view>
    <view class="second cate" v-if="second_area.length>0">
      <view class="cascade_list">
        <view class="cascade_item" :class="{selected:selectted_area.length>1&&selectted_area[1]===item.areaid}" v-for="item in second_area" :key="item.areaid" @click="onSelectArea(2,item)">{{item.areaname}}</view>
      </view>
    </view>
    <view class="third cate" v-if="third_area.length>0">
      <view class="cascade_list">
        <view class="cascade_item" :class="{selected:selectted_area.length>2&&selectted_area[2]===item.areaid}" v-for="item in third_area" :key="item.areaid" @click="onSelectArea(3,item)">{{item.areaname}}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  components: {

  },
  props:{
    area_list:{
      type:Array,
      default:[]
    }
  },
  data () {
    return {
      first_area:[],
      second_area:[],
      third_area:[],
      selectted_area:[],
    }
  },
  created(){
    this.creatArea(this.area_list)
  },
  methods: {
    creatArea(arr){
      this.first_area = arr.filter(item=>item.parentid===0)
    },
    /** 
     * <AUTHOR> 
     * @date 2020-06-22 10:11:34 
     * @desc 选择区域事件处理 
     */
    onSelectArea(i,e){
      let _this = this
      const getData = function(ev){
        // if(ev.tx_mapx&&ev.tx_mapy){
        // 	_this.params.lat = ev.tx_mapy
        // 	_this.params.lng = ev.tx_mapx
        // }else{
        // 	_this.params.lat = _this.center_point[0]
        // 	_this.params.lng = _this.center_point[1]
        // }
        let params = {
          id: ev.areaid,
          name: e.areaname
        }
        _this.$emit('onselect', params)
      }
      switch(i){
        case 1:
          this.selectted_area = [e.areaid]
          // 筛选出当前区域下的二级区域
          this.second_area = this.area_list.filter(item=>item.parentid===e.areaid)
          if(this.second_area.length===0){
            getData(e)
          }
          break;
        case 2:
          this.selectted_area[1] = e.areaid
          if(this.selectted_area.length>2){
            this.selectted_area.splice(2,1)
          }
          // 筛选出当前区域下的三级区域
          this.third_area = this.area_list.filter(item=>item.parentid===e.areaid)
          if(this.third_area.length===0){
            getData(e)
          }
          break;
        case 3:
          this.selectted_area[2] = e.areaid
          getData(e)
      }
    },
  }
}
</script>

<style scoped lang="scss">
.cascade_box{
	align-items: flex-start;
	justify-content: space-between;
	.cate{
		flex: 1;
		overflow: hidden;
	}
	.cascade_item{
		padding: 26rpx 32rpx;
		// text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		border-bottom: 1rpx solid #dedede;
		&.selected{
			color: $uni-color-primary;
		}
	}
}
</style>