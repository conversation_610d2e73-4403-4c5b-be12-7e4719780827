const list = [
  {
    from: "/pages/vr/vr",
    to: "/vr/detail"
  },
  {
    from: "/pages/price_trend/price_trend",
    to: "/propertyData/price_trend/price_trend"
  },
  {
    from: "/pages/sub_form/sub_form ",
    to: "/user/sub_form/sub_form "
  },
  {
    from: "/pages/community/add_post",
    to: "/user/community/add_post"
  },
  {
    from: "/pages/sub_form/sub_form",
    to: "/user/sub_form/sub_form"
  },
]
module.exports = function(options){
  for(let i=0; i<list.length; i++){
    if(list[i].from === options.path){
      let url = list[i].to
      for(let key in options.query){
        if (options.query.hasOwnProperty(key)){
          if (url.search("/\?/")){
            url += `&${key}=${options.query[key]}`
          }else{
            url += `?${key}=${options.query[key]}`
          }
        }
      }
      uni.redirectTo({
        url: url
      })
      return true
    }
  }
}