<template>
  <view class="house_default">
    <view class="top_img">
      <image :src ="`/yidongduan/toolIndex/top_bg.png` | imageFilter"  mode="widthFix">

      </image>
      <view class="title">
        房产应用中心
      </view>
      <view class="back flex-row" >
      <view class="icon-box flex-row" @click="back">
          <my-icon type="ic_back" color="#ffffff" size="48rpx"></my-icon>
      </view>
    </view>
    </view>
    <view class="tools_con">
      <view class="tools_item" v-for ='(item,index) in navs' :key ="item.id" :class="{border_r:index==0}">
        <view class="tools_title">
          {{item.catname}}
        </view>
         <view class ="grid" :class ='"grid_" + index' v-if ="item.children&&item.children.length">
          <my-grid @click="toNav($event,item)" :options="item.children" :column-num="5" :fontSize="12" :show-border="false"></my-grid>
        </view>
      </view>
    </view>
  </view>

</template>

<script>
import myGrid from '@/components/myGrid.vue'
import myIcon from '@/components/myIcon.vue'
export default {
  data(){
    return  {
      navs:[]
    }
  },
  components:{
    myGrid ,
    myIcon 
  },
  onLoad(){
    uni.showLoading({
      title:"加载中。。。"
    })
    this.getData()
  },
  methods:{
    getData(){
      this.$ajax.get("index/allSubNavs",{},res=>{
        if(res.data.code ==1){
          let navs = res.data.navs
          //  res.data.navs[1] =res.data.navs[0]
          //   res.data.navs[1].id=2
          navs.map(item=>{
            if(item.children&&item.children.length){
              item.children.map(child=>{
                child.icon = child.ico
                return child
              })
            }
            return item
          })
          this.navs =navs
        }else {
          uni.showToast({
            title:res.data.message || '请求失败',
            icon:'none'
          })
        }
        uni.hideLoading()
      },()=>{
        uni.hideLoading()
      })
    },
    toNav(e,type){
      this.$navigateTo(type["children"][e.index].url)
    },
    back() {
      if (getCurrentPages().length > 1) {
        uni.navigateBack()
      } else {
        // #ifdef H5
        var ua = window.navigator.userAgent.toLowerCase();
        //通过正则表达式匹配ua中是否含有MicroMessenger字符串
        if(ua.match(/MicroMessenger/i) == 'micromessenger'){
          uni.switchTab({
            url: '/pages/index/index'
          })
        }else{
          window.history.go(-1)
        }
        // #endif
        // #ifndef H5
          uni.switchTab({
            url: '/pages/index/index'
          })
        // #endif
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .top_img{
    width: 100%;
    position: relative;
    image {
      width: 100%;
    }
    .title {
      position: absolute;
      font-size: 60rpx;
      font-weight: 600;
      letter-spacing: 3px;
      white-space: nowrap;
      color: #fff;
      top: 50%;
      left: 48rpx;
      transform: translate(0,calc(-50% - 40rpx));
    }
  }
  .back{
  position: absolute;
  width: 100%;
  height: 44px;
  top: 10rpx;
  left: 10rpx;
  padding: 2px 10rpx;
  // justify-content: space-between;
  z-index: 999;
  .title-con{
    flex: 1;
    text-align: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 32rpx;
    max-width: 62vw;
  }
  .icon-box{
    // height: 44px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    padding: 8px;
    justify-content: center;
    align-items: center;
  }
}
  .grid ::v-deep{
    .uni-grid-item-image {
      width: 50rpx;
      height: 50rpx;
    }
    .uni-grid-item-text {
      margin-top: 4rpx;
    }
    .uni-grid-item:before{
      padding-bottom: 75%;
    }
    .uni-grid__flex ~ .uni-grid__flex {
      margin-top: 8rpx;
    }
  }
  .tools_con {
    margin-top: -40rpx;
    position: relative;
    .tools_item {
      padding: 48rpx;
      margin-bottom: 24rpx;
      background: #fff;
      &.border_r{
        border-top-left-radius: 40rpx;
        border-top-right-radius: 40rpx;
      }
      .tools_title {
        font-size: 36rpx;
        padding-bottom: 24rpx;
        color: #333;
        font-weight: 600;
      }
      // .grid ::v-deep .uni-grid-item-text {
      //   margin-top: 4rpx;
      // }
    }
  }
</style>