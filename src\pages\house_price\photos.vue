<template>
  <view class="photos">
    <view v-if="!params.cid" class="tab-list flex-row bottom-line" :style="{top:advTop}">
      <!-- <view class="tab-item" @click="switchTab('share_list')">楼市圈</view>
      <view class="tab-item" @click="switchTab('news_list')">楼盘动态</view>
      <view class="tab-item active" @click="switchTab('community_photos')">小区专家</view> -->
            <view  class="tab-item" v-for="(item,index) in navs" :class="{'active':float==index} "  :key="index+1">  
                <navigator :url="item.path"> {{ item.label }}</navigator>
            </view>
    </view>
    <view class="tips" v-if="tips&&params.cid" @click="toAdd">
      <my-icon type="tishifu" color="#fb656a" size="36rpx"></my-icon>
      <text class="text">{{tips}}</text>
    </view>
    <!-- 经纪人实拍 -->
    <view class="right mid">
      <!-- fixed -->
      <view v-if="isadviser" class="add" @click="toAdd">+</view>
      <view
        class="user_comments_list"
        v-for="(item, index) in user_list"
        :key="index"
      >
      <commutityAgent :from="1" @toAgentDetail="toAgentDetail" @handleChat= "handleChat" @handleTel ="handleTel" :item="item">
        <!-- 小区信息 -->
            <view v-if="!params.cid" class="house_type flex-row" @click="$navigateTo('/pages/house_price/detail?id='+item.cid)">
              <image :src="item.community_img | imageFilter('w_240')" mode="aspectFill"></image>
              <view class="house_info flex-1">
                <view class="title">{{item.community_name}}</view>
                <view class="desc flex-row">
                  <text style="margin-right: 12rpx">售 {{item.sale_count}}套</text>
                  <text>租 {{item.rent_count}}套</text>
                </view>
                <view class="price flex-row">
                  <text class="label">参考均价</text>
                  <text class="value">{{item.avg_price | avrPrice}}</text>
                  <text class="unit">元/m²</text>
                </view>
              </view>
            </view>
            <view class="infor_box row">
              <view class="left row">
                <text class="community_name" v-if="params.cid">{{ item.community_name }}</text>
                <text class="shop_name">{{ item.tname }}</text>
                <text class="up_time">{{ item.ctime }}</text>
              </view>
              <view class="right row">
                <view @click="onLick(item)">
                  <myIcon
                    v-if="item.ispraise"
                    type="ic_zan"
                    color="#FB656A"
                    size="36rpx"
                  ></myIcon>
                  <myIcon
                    v-else
                    type="ic_zan"
                    color="#999999"
                    size="36rpx"
                  ></myIcon>
                </view>
                <view class="zan_num">{{item.praise}}</view>
              </view>
            </view>
      </commutityAgent>
      </view>
    </view>

    <!-- 官方采集 -->
    <view class="right mid" v-if="video_list.length>0||img_list.length>0">
      <!-- 视频 -->
      <view
        v-if="video_list&&video_list.length>0"
        class="user_comments_list"
      >
        <view class="top row">
          <view class="user_box row">
            <image :src="video_list[0].prelogo | imgUrl('w_120')" mode="aspectFill"></image>
            <view class="user_name flex-row">
              <text>{{ siteName }}</text>
              <text class="label gf">官方</text>
            </view>
          </view>
          <!-- <view class="up_time">{{ item.ctime||'' }}</view> -->
        </view>
        <view class="user_right">
          <view class="txt">{{ video_list[0].content || ''}}</view>
          <view class="content row">
            <view class="video" v-for="(item, idx) in video_list" :key="idx" @click="viewVideo(item.pic)">
              <image :src="item.pic | imgUrl('w_240')" mode="aspectFill"> </image>
              <view class="bofang">
                <myIcon
                  type="ic_video"
                  size="66rpx"
                  color="#fff"
                ></myIcon>
              </view>
            </view>
            <view class="video perch"></view>
            <view class="video perch"></view>
          </view>
        </view>
      </view>
      <!-- 图片 -->
      <view
        v-if="img_list&&img_list.length>0"
        class="user_comments_list"
      >
        <view class="top row">
          <view class="user_box row">
            <image :src="img_list[0].prelogo | imgUrl('w_120')" mode="aspectFill"></image>
            <view class="user_name flex-row">
              <text>{{ siteName }}</text>
              <text class="label gf">官方</text>
            </view>
          </view>
          <!-- <view class="up_time">{{ item.ctime||'' }}</view> -->
        </view>
        <view class="user_right">
          <view class="txt">{{ img_list[0].content || ''}}</view>
          <view class="content row">
            <view class="video" v-for="(item, idx) in img_list" :key="idx">
              <image :src="item.pic | imgUrl('w_240')" mode="aspectFill" @click="previewOfficialImage(item.pic,img_list)"> </image>
            </view>
            <view class="video perch"></view>
            <view class="video perch"></view>
          </view>
        </view>
      </view>
    </view>
    <uni-load-more
        :status="get_status"
        :content-text="content_text"
      ></uni-load-more>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import myIcon from '../../components/myIcon'
import { uniLoadMore } from "@dcloudio/uni-ui";
import noData from '../../components/noData'
import {formatImg} from '../../common/index'
import wxApi from '../../common/mixin/wx_api'
import { mapState } from 'vuex';
import getChatInfo from "../../common/get_chat_info"
import encryptionTel from "../../common/encryption_tel"
export default {
  components: {
    myIcon,
    uniLoadMore,
    noData
  },
  mixins:[wxApi],
  data() {
    return {
      clicking:false,
      video_list: [],
      navs:[],
      img_list:[],
      user_list: [],
      get_status: "",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了",
      },
      isadviser:0,
      params:{
        cid:'',
        rows:20,
        page:1
      },
      tips: "",
      tel_res: {},
      float:2,
      show_tel_pop: false
    }
  },
  computed:{
    ...mapState(['siteName', ]),
    is_open_im(){
      return this.$store.state.im.ischat
    },
    is_open_middle_num(){
      return this.$store.state.im.istelcall
    },
    advTop() {
      // #ifndef H5
      return 0
      // #endif
      // #ifdef H5
      return '44px'
      // #endif
    }
  },
  filters: {
    imgUrl(val) {
      if (!val) {
        return ""
      }
      return formatImg(val, 'w_240')
    },
    avrPrice(val) {
      let newVal = parseInt(val);
      if (!newVal||isNaN(newVal)) {
        return val || "不详";
      }
      return newVal;
    }
  },
  onLoad(options) {
    if(options.cid){
      this.params.cid = options.cid
    }
    this.getData()
    this.handleBudiling()
  },
  methods: {
    handleBudiling(){
          console.log("222244444444")
          // 获取楼市去信息
          this.$ajax.get('building_circle/navs', {},res=>{
              console.log(res)
              if(res.data.code==1){
                this.navs = res.data.navs
              }
          })
    },
    getData(){
      if(this.params.cid){
        this.getOfficialPhotos()
        this.getAgentPhotos()
      }else{
        this.getAllPhotos()
      }
    },
    getOfficialPhotos(){
      uni.showLoading({
        title:'加载中...'
      })
      this.$ajax.get('house/communityMedia.html',{cid:this.params.cid},res=>{
        uni.hideLoading()
        if(res.data.code ===1 ){
          this.img_list = res.data.images
          this.video_list = res.data.videos
        }
        if(res.data.share&&!this.share){
          this.share = res.data.share
          this.getWxConfig()
        }
      },err=>{
        uni.hideLoading()
      })
    },
    getAgentPhotos(){
      this.get_status = "loading";
      this.$ajax.get('member/pubList.html',this.params,res=>{
        this.isadviser = res.data.isadviser
        this.tips = res.data.notice
        if(res.data.code === 1){
          this.get_status = 'more'
          if (this.params.page==1){
            this.user_list=res.data.list
          }else {
            this.user_list = this.user_list.concat(res.data.list) 
          }
          if (res.data.list.length < this.params.rows) {
            this.get_status = "nomore";
          }
        }else{
          this.get_status = "nomore";
        }
        if(res.data.share&&!this.share){
          this.share = res.data.share
          this.getWxConfig()
        }
      })
    },
    getAllPhotos(){
      this.get_status = "loading";
      this.$ajax.get('Community/agentPubLists', this.params, res=>{
        this.isadviser = res.data.isAgent
        this.tips = res.data.notice
        if(res.data.code === 1){
          this.get_status = 'more'
          if (this.params.page==1){
            this.user_list=res.data.lists
          }else {
            this.user_list = this.user_list.concat(res.data.lists) 
          }
          if (res.data.lists.length < this.params.rows) {
            this.get_status = "nomore";
          }
        }else{
          this.get_status = "nomore";
        }
        if(res.data.share&&!this.share){
          this.share = res.data.share
          this.getWxConfig()
        }
      })
    },
    // 点赞
    onLick(e,index) {
      if(this.clicking) return 
      this.clicking=true
      let type;
      if (e.ispraise==0){  //后台要求传参 1点赞 0 取消点赞 处理下参数
        type =1  
      }else {
        type =0
      }
      // type = e.ispraise
      this.$ajax.get("member/pubPraise", {id:e.id, state:type}, res=>{
        if(res.data.code ===1){
          if(e.ispraise == 1){
            e.ispraise = 0
            e.praise--
          }else{
            e.ispraise = 1
            e.praise++
          }
          uni.showToast({
            title: res.data.msg,
            mask: true
          })
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
            mask: true
          })
        }
        this.clicking=false;
      },(err)=>{
          this.clicking=false;
      })
    },
    viewVideo(url){
      this.$navigateTo(`/vr/preview_video?url=${url}`)
    },
    // 查看预览图
    previewImage(img,img_list) {
      img_list = img_list.map(item=>formatImg(item,'w_800'))
      uni.previewImage({
        urls: img_list,
        current: formatImg(img,'w_800')
      })
    },
    previewOfficialImage(img,data_list){
      let img_list = data_list.map(item=>formatImg(item.pic,'w_800'))
      uni.previewImage({
        urls: img_list,
        current: formatImg(img,'w_800')
      })
    },
    handleChat(id){
      if(this.is_open_im){
        getChatInfo(id, 27)
      }else{
        this.$navigateTo(`/pages/agent/detail?id=${id}`)
      }
    },
    toAgentDetail(e){
      if(e.uid&&e.agent_isvalid){
        this.$navigateTo('/pages/agent/detail?id='+e.uid)
      }
    },
    handleTel(agent){
      this.tel_params = {
        id: agent.uid,
        mid: agent.uid,
        tel: agent.tel,
        type: 'agent',
        from: 26,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      encryptionTel(this.tel_params)
    },
    retrieveTel(){
      encryptionTel(this.tel_params)
    },
    switchTab(type) {
      if (type === 'share_list') {
        this.$navigateTo('/pages/community/community')
      }
      if (type === 'news_list') {
        this.$navigateTo('/pages/community/news_list')
      }
      if (type === 'news_loushi') {
        this.$navigateTo('/pages/news/news')
      }
      if (type === 'community_photos' && this.user_list.length==0) {
        this.params.page = 1
        this.getData()
      }
    },
    toAdd(){
      if(!this.isadviser){
        uni.showToast({
          title: '目前仅对经纪人开放',
          icon: 'none'
        })
        return
      }
      this.$navigateTo(`/user/house_price/publication?cid=${this.params.cid}`)
    }
  },
  onReachBottom(){
    if(this.get_status !== "nomore"){
      this.params.page ++
      if(this.params.cid){
        this.getAgentPhotos()
      }else{
        this.getAllPhotos()
      }
    }
  },
  onShareAppMessage(){
    if(this.share){
      return {
        title: this.share.title||"",
        content:this.share.content||"",
        imageUrl: this.share.pic?formatImg(this.share.pic,'w_6401'):""
      }
    }
  }
}
</script>

<style lang="scss">
page{
  background-color: #fff;
}
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  &.row{
    flex-direction: row;
    padding: 0;
  }
  &.content{
    padding: 0;
  }
}
.flex-row{
  flex-direction: row;
}

.tab-list {
  padding: 0 48rpx;
  justify-content: space-between;
  position: sticky;
  background-color: #fff;
  z-index: 2;
  .tab-item {
    flex: 1;
    padding: 24rpx;
    text-align: center;
    position: relative;
    color: #666;
    &.active {
      color: $uni-color-primary;
      &::after {
        content: '';
        height: 8rpx;
        border-radius: 4rpx;
        background-color: $uni-color-primary;
        position: absolute;
        bottom: 0;
        width: 48rpx;
        left: 0;
        right: 0;
        margin: auto;
      }
    }
  }
}
// 导航
.nav {
  position: fixed;
  border-bottom: 2rpx solid #eee;
  width: 100%;
  background: #fff;
  z-index: 10;
  flex-direction: row;
  padding: 30rpx;
  justify-content: space-between;
  .nav-item {
    flex: 1;
    margin: 20rpx 5rpx;
    text-align: center;
    color: #666;
    &.active {
      font-weight: bold;
      color: #fb656a;
    }
  }
  .nav-item:first-child {
    border-right: 2rpx solid #eee;
  }
}

// 内容

.mid {
  padding: 0 48rpx;
  .type{
    margin: 24rpx 0;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  .title {
    font-size: 32rpx;
    color: #333333;
    margin-bottom: 24rpx;
  }
  .content {
    justify-content: space-between;
    flex-wrap: wrap;
    .video {
      position: relative;
      width: 178rpx;
      height: 178rpx;
      margin-top: 24rpx;
      &.perch{
        height: 0;
      }
      image {
        width: 100%;
        height: 100%;
      }
      .bofang {
        position: absolute;
        border-radius: 50%;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }
}
.right {
  .zan_num{
    margin-left: 8rpx;
  }
  .user_comments_list {
    margin-bottom: 28rpx;
    .top {
      margin-top: 24rpx;
      margin-bottom: 10rpx;
      justify-content: space-between;
      align-items: center;
      .user_box {
        flex: 1;
        align-items: center;
        width: 100%;
        image {
          margin-right: 16rpx;
          width: 64rpx;
          height: 64rpx;
          border-radius: 50%;
          background-color: #dedede;
        }
        .user_name {
          font-size: 30rpx;
          // font-weight: bold;
          align-items: center;
          overflow: hidden;
          .cname{
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .label{
            margin-left: 12rpx;
            font-size: 24rpx;
            font-weight: initial;
            line-height: 1;
            padding: 6rpx 0;
            min-width: 90rpx;
            text-align: center;
            border-radius: 4rpx;
            background: linear-gradient(to right, #f7918f, #fb656a);
            color: #fff;
            &.gf{
              min-width: 72rpx;
              background: linear-gradient(to right, #7bbcf8, #419ef5);
            }
          }
        }
        .btns{
          justify-content: flex-end;
          .btn{
            font-size: 26rpx;
            padding: 10rpx 24rpx;
            border-radius: 6rpx;
            color: #fb656a;
            border: 1rpx solid #fb656a;
            ~.btn{
              margin-left: 20rpx;
            }
          }
        }
      }
      .up_time {
        color: #999;
        font-size: 22rpx;
      }
    }
    .user_right {
      margin-left: 74rpx;
      .txt {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
        // 文本溢出隐藏
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      .media_box {
        flex-wrap: wrap;
        .imgs_box {
          width: 100%;
          flex-wrap: wrap;
          justify-content: space-between;
          .img_item_alone{
            margin-top: 24rpx;
            width: 78%;
            max-height: 600upx;
            position: relative;
            overflow: hidden;
            image{
              width: 100%;
              height: 100%;
              // margin-bottom: 24rpx;
              overflow: unset;
            }
          }
          .img_item{
            width: 178rpx;
            height: 178rpx;
            margin-top: 24rpx;
            &.perch{
              height: 0;
              margin: 0;
            }
          }
          image {
            width: 100%;
            height: 100%;
          }
        }
        .video_box {
          position: relative;
          width: 480rpx;
          height: 320rpx;
          image {
            margin-top: 24rpx;
            width: 100%;
            height: 100%;
          }
          .bofang {
            position: absolute;
            border-radius: 50%;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.5);
          }
        }
      }
      .infor_box {
        justify-content: space-between;
        flex: 1;
        font-size: 22rpx;
        margin-top: 16rpx;
        margin-bottom: 24rpx;
        .left {
          align-items: center;
          &.row{
            font-size: 22rpx;
          }
          .community_name {
            color: #fb656a;
            margin-right: 16rpx;
          }
          .shop_name {
            color: #999;
          }
          .up_time {
            margin-left: 16rpx;
            color: #999;
            font-size: 22rpx;
          }
        }
        .right {
          align-items: center;
          color: #d8d8d8;
        }
      }
      .house_type{
        margin-top: 24rpx;
        padding: 24rpx;
        border-radius: 8rpx;
        background-color: #f8f8f8;
        image{
          width: 128rpx;
          height: 128rpx;
          margin-right: 24rpx;
          border-radius: 8rpx;
        }
        .house_info{
          .title{
            margin-bottom: 10rpx;
            font-size: 32rpx;
            color: #333;
          }
          .desc{
            margin-bottom: 8rpx;
            font-size: 22rpx;
            color: #666;
            .huxing_desc{
              margin-right: 16rpx;
            }
          }
          .price{
            align-items: center;
            .label{
              margin-right: 12rpx;
              font-size: 24rpx;
              color: #666;
            }
            .value{
              font-weight: bold;
              font-size: 32rpx;
              color: $uni-color-primary;
            }
            .unit{
              margin-left: 8rpx;
              font-size: 24rpx;
              color: #666;
            }
          }
        }
      }
    }
  }
}
.add {
  z-index: 1;
  right: 6%;
  bottom: 20%;
  position: fixed;
  font-size: 80rpx;
  width: 96rpx;
  height: 96rpx;
  line-height: 86rpx;
  background-image: linear-gradient(135deg, #f7918f 0%, #fb656a 100%);
  box-shadow: 0 3px 6px 0 rgba(250, 112, 115, 0.7);
  border-radius: 50%;
  text-align: center;
  color: #fff;
}

.tips{
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 18rpx 32rpx;
  color: $uni-color-primary;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
  >.text{
    margin-top: -6rpx;
    margin-left: 12rpx;
  }
}
</style>
