<template>
  <view class="content">
		<view class="block">
			<view class="label">可提现余额（元）</view>
			<input class="inp" disabled :value="shequ_money" type="text">
		</view>
		<view class="block">
			<view class="label">提现余额（元）</view>
			<view class="inp-row flex-row">
				<text class="prefix">￥</text>
				<input class="inp" @input="inputChange" v-model="params.withdraw_money" type="number">
				<my-icon type="qingchu" color="#b5b5b5" @click="params.withdraw_money=''"></my-icon>
			</view>
			<!-- <view v-if ="!showTips" class="tip">提现手续费{{payfee}}%，预计到账{{(1-payfee/100)*params.withdraw_money}}元</view> -->
			<view  class="tip">{{tips_content}}</view>
		</view>
		<view class="block"  v-if ="params.withdraw_method!=='wxpay'">
			<view class="input_row mgb20">
				<new-input label="支付宝账号" label_width="150rpx" v-model="params.withdraw_account"></new-input>
			</view>
			<view class="input_row">
				<new-input label="真实姓名" label_width="150rpx" v-model="params.account_name" placeholder="支付宝真实姓名"></new-input>
			</view>
		</view>
		<view class="btn-box">
			<view class="button" @click="subApply()">{{params.withdraw_method=="wxpay"?'提现到微信零钱':'提交申请'}}</view>
		</view>
  </view>
</template>

<script>
	import newInput from "../components/form/newInput"
	import myIcon from "../components/myIcon"
  import {uniListItem} from "@dcloudio/uni-ui"
	import {mapMutations} from 'vuex'
	export default {
		data() {
			return {
				showTab:0,
				payType:"",
				tabs:[
				    {name:"提现",type:1},
				    {name:"兑换金币",type:2}
				],
				shequ_money:0,
				exchange_money:"",
				params:{
					withdraw_money:"",
					withdraw_account:"",
					account_name:"",
					withdraw_method:'',
				},
				min_money:1,
				payfee:0,
				tips_content:""
			};
		},
		components:{
			newInput,
			myIcon,
      uniListItem
		},
		onLoad(options){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			if(this.$store.state.user_info.shequ_money!=undefined&&this.$store.state.user_info.money_own!=undefined){
				this.shequ_money = this.$store.state.user_info.shequ_money
			}else{
				this.getUser()
			}
			this.getData()
		},
		methods:{
			...mapMutations(['getUserInfo']),
			handelInput(e){
                // console.log(e)
                this.params[e._name] = e.detail.value
			},
			handelInputExchange(e){
				this.exchange_money = e.detail.value
			},
			inputChange(e){
				if (Number(e.target.value)  <Number(this.min_money) ){
					this.tips_content = "提现金额不能小于"+this.min_money +'元'
				}else if (Number(e.target.value) >Number(this.shequ_money)){
					this.tips_content = "最多可提取" +this.shequ_money+"元"
				}else {
					this.tips_content = `提现手续费${this.payfee}%，预计到账${(1-this.payfee/100)*this.params.withdraw_money}元`
				}
			},
			switchTab(e){
				this.showTab = e.index
			},
			getData(){
				this.$ajax.get('member/withdraw.html',{},res=>{
					if(res.data.code == 1){
						this.payfee = res.data.data.payfee-0
						this.min_money = res.data.data.min_money
						this.params.withdraw_method =  res.data.data.withdraw_method? res.data.data.withdraw_method:""
						this.tips_content = `提现手续费${this.payfee}%，预计到账${(1-this.payfee/100)*this.params.withdraw_money}元`
					}
				})
			},
			subApply(){
				if(!this.params.withdraw_money){
					uni.showToast({
						title:"请输入提现金额",
						icon:"none"
					})
					return
				}
				if(this.params.withdraw_money<this.min_money){
					uni.showToast({
						title:"一次至少提现"+this.min_money+"元",
						icon:"none"
					})
					return
				}
				if(this.params.withdraw_money>this.shequ_money){
					uni.showToast({
						title:"提现金额不能大于余额",
						icon:"none"
					})
					return
				}
				if(!this.params.withdraw_account &&this.params.withdraw_method !='wxpay'){
					uni.showToast({
						title:"请输入支付宝账号",
						icon:"none"
					})
					return
				}
				if(!this.params.account_name&&this.params.withdraw_method !='wxpay'){
					uni.showToast({
						title:"请输入真实姓名",
						icon:"none"
					})
					return
				}
				// let params = {total_fee:this.params.amount}
				this.$ajax.post('member/withdraw.html',this.params,(res)=>{
					if(res.data.code == 1){
						uni.showToast({
							title:'提现申请已提交',
							icon:'none'
						})
						this.getUser()
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:'none'
						})
					}
				})
			},
			subApplyExchange(){
				if(!this.exchange_money){
					uni.showToast({
						title:"请输入兑换数量",
						icon:"none"
					})
					return
				}
				if(this.exchange_money>parseFloat(this.shequ_money)){
					uni.showToast({
						title:"兑换数量不能大于余额",
						icon:"none"
					})
					return
				}
				if(this.exchange_money%1){
					uni.showToast({
						title:"兑换数量必须为整数",
						icon:"none"
					})
					return
				}
				this.$ajax.post('member/coinExchange',{exchange_money:this.exchange_money},(res)=>{
					if(res.data.code == 1){
						uni.showToast({
							title:res.data.msg,
							mask:true
						})
						setTimeout(()=>{
							this.exchange_money = ""
						},1200)
						this.getUser('back')
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:'none'
						})
					}
				})
			},
			getUser(back){
				this.$ajax.get('member/index.html', {}, (res) => {
					if (res.data.code == 1) {
						this.getUserInfo(res.data.user)
						this.shequ_money = res.data.user.shequ_money
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: "none"
						})
					}
					if(getCurrentPages().length>1&&back==='back'){
						setTimeout(()=>{
							uni.navigateBack()
						},1500)
					}
				})
			}
		}
	}
</script>

<style lang="scss">
view{
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}
.flex-row{
	flex-direction: row;
}
.block{
	padding: 24rpx 48rpx;
	margin-bottom: 20rpx;
	background-color: #fff;
	.label{
		color: #999;
	}
	.inp-row{
		align-items: center;
	}
	.prefix{
		font-size: 60rpx;
	}
	.inp{
		height: 64rpx;
		font-size: 64rpx;
	}
	.tip{
		color: $uni-color-primary;
	}
	.input_row{
		padding: 10rpx 0;
	}
	.mgb20{
		margin-bottom: 20rpx;
	}
}

.btn-box{
	padding: 48rpx;
	.button{
		padding: 24rpx;
		border-radius: 10rpx;
		background-color: $uni-color-primary;
		color: #fff;
		font-size: 40rpx;
		text-align: center;
	}
}
</style>
