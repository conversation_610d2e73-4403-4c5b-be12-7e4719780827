<template>
<view class="bullet_list" :class="{mode_text:mode_text}">
    <view v-for="(item, index) in bullet_store" :key="index" :class="{show:item.is_show}" class="bullet_item flex-box">
        <image v-if="item.prelogo" class="header_img" mode="aspectFill" :src="item.prelogo | imgUrl('w_80')"></image>
        <view class="info-box flex-1">
            <view v-if="item.name" class="nickname" :style="{color:name_color}">{{item.name}}</view>
            <view class="info">{{item.content}}</view>
        </view>
    </view>
</view>
</template>

<script>
import {
    formatImg,
} from '../../common/index.js'
export default {
    data() {
        return {
            bullet_store: JSON.parse(JSON.stringify(this.bullet_list)),
        }
    },
    props: {
        bullet_list: {
            type: [Array],
            default: []
        },
        delay: {
            type:[Number],
            default:1000
        },
        name_color:{
            type:[String],
            default:"#ffffff"
        },
        mode_text:{
            type:[Boolean],
            default:false
        }
    },
    created() {
        setTimeout(() => {
            this.showBullet(0)
        }, this.delay)
    },
    destroyed() {
        this.un_load = true
        clearTimeout(this.timer)
    },
    filters:{
        imgUrl(val,param){
            return formatImg(val,param)
        },
    },
    methods: {
        showBullet(i) {
            let offset = 0
            if(this.mode_text){
                offset = 180
            }
            if(this.un_load||!this.bullet_store[i]){
                return
            }
            this.bullet_store[i].is_show = 1
            // console.log(`第${i}个开始显示`)
            this.timer = setTimeout(() => {
                let next = i + 1
                if (next >= this.bullet_store.length) {
                    next = 0
                }
                this.showBullet(next)
                setTimeout(() => {
                    this.bullet_store[i].is_show = 0
                }, 8000-0.4166*7000)
            }, 0.4166*8000+offset)
        }
    },
}
</script>

<style lang="scss" scoped>
@keyframes fadeIn {
    0% {left: -100%; top: 170rpx; opacity: 1;}
    5.66%,
    41.66% {left: 10rpx; top: 170rpx; opacity: 1;}
    47.32%,
    83.32% {left: 10rpx; top: 20rpx; opacity: 1;}
    90%,
    95% {left: 10rpx; top: -70px; opacity: 0.3;}
    100% {left: 10rpx; top: -120px; opacity: 0;}
}

@keyframes fadeIn2 {
    0% {left: -100%; top: 140rpx; opacity: 1;}
    5.66%,
    41.66% {left: 10rpx; top: 140rpx; opacity: 1;}
    47.32%,
    83.32% {left: 10rpx; top: 50rpx; opacity: 1;}
    90%,
    95% {left: 10rpx; top: -40px; opacity: 0.3;}
    100% {left: 10rpx; top: -80px; opacity: 0;}
}


// 弹幕
.bullet_list {
    position: relative;
    width: 100%;
    overflow: hidden;
    height: 290rpx;
    &.mode_text{
        height: 200rpx;
        .bullet_item{
            top: 140rpx;
            height: 90rpx;
            &.show {
                // animation: fadeIn 7s linear .2s;
                animation: fadeIn2 8s linear;
            }
        }
    }

    .bullet_item {
        position: absolute;
        align-items: flex-start;
        top: 170rpx;
        left: -100%;
        max-width: 98%;
        height: 120rpx;
        box-sizing: border-box;

        // transition: 0.26s;
        &.show {
            // animation: fadeIn 7s linear .2s;
            animation: fadeIn 8s linear;
        }

        .header_img {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            margin-right: 15rpx;
        }

        .info-box {
            color: #fff;
            text-align: left;
            overflow: hidden;

            .nickname {
                margin-bottom: 15rpx;
            }

            .info {
                padding: 0 20rpx;
                height: 60rpx;
                line-height: 60rpx;
                background-color: rgba($color: #000000, $alpha: 0.5);
                color: #fff;
                border-radius: 8rpx;
                font-size: 26rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
</style>
