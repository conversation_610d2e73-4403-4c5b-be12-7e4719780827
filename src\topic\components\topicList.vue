<template>
<view class = "topicList">
    <view class = "toutiao"  v-if= "TopNew.length>0" >
            <view class="header">{{title1}}</view>
            <view class="toutiaoInfo" v-for = "item in TopNew" :key = item.id  @click="goTo(item)">
                <view class="img">
                    <image  :src="item.img | imgUrl('w_240')" mode = "widthFix"></image>
                    <view class="title">{{item.title}}</view>
                </view>
                <view class="con">
                    <view class="con_">{{item.connect}}</view>
                </view>

            </view>
        
    </view>
    <view class="import" v-if = "MainNewTop.length>0" @click= "goToNews" >
            <view class="header">{{title2}}</view>
            <view class="newsInfo" v-for = "item in MainNewTop" :key= "item.id">
                <view class="first" >
                    <view class="fir flex-box bottom-line" @click.prevent.stop="goTo(item)">
                        <view class="left">
                            <view class="title">{{item.title}}</view>
                            <view class="sub_title">{{item.sub_title}}</view>
                        </view>
                        <view class="img" >
                            <image :src="item.img|imgUrl('w_240')" mode = "widthFix"></image>
                        </view>
                    </view>
                    <view class="other" v-if = "MainNewList.length>0">   <!--条目 -->
                        <block v-for="items  in MainNewList" :key ='items.id'>
                                <view class="left info"   @click.prevent.stop="goDetail(items.url)" >
                                    <view class="title"><span class="bef"></span>{{items.title}}</view>

                                </view>
                        </block>
                    </view>
                </view>
                </view>
                
    </view>
    <view class="import" v-if = "ShopNewTop.length>0" @click= "goToNews" >
             <view class="header">{{title3}}</view>
            <view class="newsInfo" v-for = "item in ShopNewTop" :key= "item.id">
                <view class="first" >
                    <view class="fir flex-box bottom-line" @click.prevent.stop="goTo(item)">
                        <view class="left">
                            <view class="title">{{item.title}}</view>
                            <view class="sub_title">{{item.sub_title}}</view>
                        </view>
                        <view class="img" >
                            <image :src="item.img|imgUrl('w_240')" mode = "widthFix"></image>
                        </view>
                    </view>
                    <view class="other" v-if ="ShopNewList.length>0" >   <!--条目 -->
                        <block v-for="items  in ShopNewList" :key ='items.id'>
                                <view class="left info"   @click.prevent.stop="goDetail(items.url)" >
                                    <view class="title"><span class="bef"></span>{{items.title}}</view>

                                </view>
                        </block>
                    </view>
                    <!-- <view class="more" v-if = "item.info.length>2"  @click="goTo(item)">
                            查看更多
                        </view> -->
                </view>
                </view>
                
    </view>

    </view>  

    

</template>

<script>
import {
    formatImg,
    navigateTo,
    config
} from "@/common/index.js"
export default {
    props:{
        TopNew:{
            type:Array,
            default:{}
        },
        MainNewTop:{
              type:Array,
            default:{}
        },
        MainNewList:{
            type:Array,
            default:{}
        },
        ShopNewTop:{
            type:Array,
            default:{}
        },
        ShopNewList:{
            type:Array,
            default:{}
        },
        title1:{
            type:String,
            default:""
        },
        title2:{
            type:String,
            default:""
        },
         title3:{
            type:String,
            default:""
        }

    },

    data(){
        return {
            
        }
    },
    filters:{
        imgUrl(val, param = "") {
            return formatImg(val, param)
        }
    },
    computed: {
    //    toutiaoFirst(){
    //        console.log(this.listsData);
           
    //         return this.listsData.TopNew
    //     },
        // toutiaoOther(){
        //     return  this.listsData
            
        // },
        
   
    },
    methods: { 
        goTo(option){ 
            navigateTo(option.url)
            return;
            console.log(112);
            
            if (option.tp_mod==2)   { //动态 跳到信息列表     
            
               
                // #ifdef H5
                navigateTo(option.url)
                // navigateTo(config.apiDomain+'/topics/index?id=18')
                // +option.id)
                // #endif
                
                let url =encodeURIComponent(option.url).replace("?","@@@").replace("=","@@")
                // +option.id) 
                // #ifdef MP
                // let url =encodeURIComponent(config.apiDomain+'/topic/infolist?id='+option.id) 
                navigateTo('/pages/web_view/webview?path='+url)
                // #endif


            }else {  //静态  跳到详情

                if (option.tp_url&&option.tp_url!=0){
					navigateTo('/pages/news/detail?id='+option.tp_url)
				}else {
                    // #ifdef H5
                    // navigateTo('/topic/detail?id='+option.id)
                    navigateTo(option.url)
                    // #endif 
                    // #ifdef MP
                    let url =encodeURIComponent(option.url).replace("?","@@@").replace("=","@@")
                    navigateTo('/pages/web_view/webview?url='+url)
                    // #endif

                }
            }
        },
        goToNews(){
            navigateTo('/pages/news/news')
        },
        goDetail(url){
            navigateTo(url)
            return;
            if (type&&type!=0){  //资讯
                navigateTo(url)
            }else {   //不是资讯
                // #ifdef H5
                // navigateTo('/topic/detail?id='+option.id)
                navigateTo(url)
                // #endif 
                // #ifdef MP
                let urls =encodeURIComponent(url).replace("?","@@@").replace("=","@@")
                navigateTo('/pages/web_view/webview?path='+urls)
                // #endif
                // navigateTo("/topic/info_detail?id="+id)
            }
            
        }
    },
}
</script>

<style lang="scss">
.topicList .header{
        padding: 20upx 70upx;
        font-size: 34upx;
        font-weight: bolder;
        background: rgba(205,205,205,0.06)
}
.toutiao{
    padding: 20upx 10upx;
    .toutiaoInfo{
    padding: 20upx 50upx;
    border: 1px solid #f0f0f0;
    box-shadow: 0upx 10upx 10upx 10upx #f0f0f0;
    border-radius: 10upx;
    margin-top: 20upx;
    .img{
        padding: 0px 20upx;
        margin-bottom: 10upx;
        image{
            width: 100%;
            max-height: 300upx;
            border-radius: 10upx;
            transform: translateY(-40upx);
        }
        .title{
            font-size: 34upx;
            font-weight: bolder;
            white-space: nowrap;
            overflow: hidden;
            width: 100%;
            text-overflow: ellipsis;
            // letter-spacing: 4upx;
        }
    }
    .con{
        .con_{
            color: #666;
            font-size: 28upx;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp:2;
            overflow: hidden;
            text-overflow: hidden;
            background: #fff;
            // letter-spacing: 4upx;

        }
    } 
    }
}
.import{
    padding: 10upx;
    .uni-list-item{
        padding: 0 40upx;
    }
    .newsInfo{
        padding: 20upx 50upx;
        border: 1px solid #f0f0f0;
        box-shadow: 0upx 10upx 10upx 10upx #f0f0f0;
        border-radius: 10upx;
        .fir{
            padding: 20upx 0upx;
            align-items: center;
            justify-content: space-between;
           
            .left{
                max-width: calc(100% - 240upx);
                line-height: 1.8;
                &.info{
                    width: 100%;
                    .title{
                    font-size: 26upx;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp:1;
                    overflow: hidden;
                    text-overflow: hidden;  
                    letter-spacing: 2upx;
                }
                .sub_title{
                    font-size: 26upx;
                }
                }
                .title{
                    font-size: 34upx;
                    font-weight: bolder;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp:1;
                    overflow: hidden;
                    text-overflow: hidden;  
                
                    letter-spacing: 2upx;
                }
                .sub_title{
                    font-size: 26upx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;  
                    
                }
            }
        
            .img{
                width: 200upx;
                max-height: 200upx;
                overflow: hidden;
                image{
                    border-radius: 10upx;
                    width: 100%;
                }
            }
        }
         
        .other{
            padding: 20upx 0upx;
            // max-height: 100upx;
            line-height: 42upx;
            overflow: hidden;
            box-sizing: border-box;
            .title{
                position: relative;
                padding-left: 30upx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                letter-spacing: 2upx;

                .bef{
                    display: inline-block;
                    width: 10upx;
                    height: 10upx;
                    line-height: 10upx;
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    left: 0upx;
                    background:#357CF5; 
                    border-radius: 50%;
                }   
            }
        }
        .more{
            text-align: center;
            padding: 20upx 10upx 10upx;

        }
        
    }
}

</style>