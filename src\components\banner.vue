<template>
<view class="banner">
    <image class="img" :style="{'height':height}" :src="image | imgUrl" :mode="mode" @click="toLink"></image>
    <view v-if="is_adv" class="marker">广告</view>
</view>
</template>

<script>
import {navigateTo,formatImg} from "../common/index"
export default {
    props:{
        height:{
            type:[String],
            default:"28vw"
        },
        mode:{
            type:String,
            default:"aspectFill"
        },
        is_adv: {
            type: Boolean,
            default: false
        },
        image:String,
        link:String
    },
    data() {
        return {

        }
    },
    filters: {
        imgUrl(val){
            return formatImg(val,'w_8601')
        }
    },
    methods: {
        toLink(){
            navigateTo(this.link)
        }
    }
}
</script>

<style lang="scss">
.banner{
    position: relative;
    font-size: 0;
    .img{
        max-height: 100%;
        width: 100%;
    }
    .marker{
        line-height: 1;
        padding: 4rpx 10rpx;
        position: absolute;
        right: 12rpx;
        bottom: 10rpx;
        font-size: 20rpx;
        border-radius: 4rpx;
        background-color: rgba($color: #000000, $alpha: 0.5);
        color: #fff;
    }
}
</style>
