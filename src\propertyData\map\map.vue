<template>
	<view class="page">
		<cover-view class ="back flex-box" @click="goBack">
        <cover-image class ="back_icon" :src="'/ditu/ditu_back.png' | imageFilter('m_120')"></cover-image>
		</cover-view>
		<map id="map" :scale="mapData.scale" @regionchange ="regionchange"  :circles="cirles" :latitude="lat" :longitude="lng" layer-style=2  :markers="mapData.covers" @callouttap ="callouttap"  @markertap ="markertap">
		</map>
		<!-- <cover-view class="map-cate-list">
			<cover-view class="cate-item" @click="getCovers('商业')">
				<cover-image class="image" src="https://images.tengfangyun.com/images/icon/shangye.png"></cover-image>
				<cover-view class="text">商业</cover-view>
			</cover-view>
			<cover-view class="cate-item" @click="getCovers('教育')">
				<cover-image class="image" src="https://images.tengfangyun.com/images/icon/jiaoyu.png"></cover-image>
				<cover-view class="text">教育</cover-view>
			</cover-view>
			<cover-view class="cate-item" @click="getCovers('医疗')">
				<cover-image class="image" src="https://images.tengfangyun.com/images/icon/yiliao.png"></cover-image>
				<cover-view class="text">医疗</cover-view>
			</cover-view>
			<cover-view class="cate-item" @click="getCovers('交通')">
				<cover-image class="image" src="https://images.tengfangyun.com/images/icon/jiaotong.png"></cover-image>
				<cover-view class="text">交通</cover-view>
			</cover-view>
		</cover-view>
	</view> -->
		<cover-view class="right_menu">
			<cover-image class="item" @click="opemLocation()" :src="'/build/v_3/map_property/nav.png' | imageFilter('m_320')">导航</cover-image>
			<cover-image v-if="type==1" class="item" @click="sendAddressToPhone()" :src="'/build/v_3/map_property/mobile.png' | imageFilter('m_320')">发送</cover-image>
			<cover-image class="item" @click="show_share_tip=true" :src="'/build/v_3/map_property/share.png' | imageFilter('m_320')">分享</cover-image>
		</cover-view>
		<cover-view class="cover_container" @touchend ="onTouchEnd" @touchmove ="onTouchMove" @touchstart ="onTouchStart">
			<cover-view class="options_btn">
					<cover-image class="icon" :class="{icon_reversal: icon_reversal}" :src="'/build/v_3/map_property/open.png' | imageFilter('m_320')"></cover-image>
				</cover-view>
				<cover-view class="shareInfo bottom-line flex-row" v-if ="shareUserInfo&&shareUserInfo.mid">
					<cover-image class="share_prelogo" :src="shareUserInfo.prelogo | imageFilter('w_320')"></cover-image>
					<cover-view class="share_name flex-1" >{{shareUserInfo.cname}}</cover-view>
					<cover-view class="ask_button flex-row"  @click='getChatInfo'>
					<cover-image class ="ask_btn_image" :src="'/ditu/ask_icon.png' | imageFilter('m_120')"></cover-image>
					<cover-view class ="ask_btn_name">咨询</cover-view>
				</cover-view>
				</cover-view>
			<!-- <cover-view class="map-cate-list">
					<cover-view v-for="(cate, index) in type_list" :key="index" class="cate-item" :class="{active: currentType === cate.name}" @click="chooseType(cate.name)">
						<cover-view class="text">{{cate.name}}</cover-view>
					</cover-view>
				</cover-view>-->
			<cover-view> 
				
				<cover-view class="map-cate-list" v-if='cate_list.length'>
					<cover-view v-for="(cate, index) in cate_list"  :key="index" class="cate-item" :class="{active: currentName === cate.name}" @click="changeCate(cate.name,cate.title, cate.children)">
						<cover-view class="text">{{cate.title}}</cover-view>
					</cover-view>
				</cover-view>
				<cover-view class="chil_cate"  v-if = 'current_chil_cate&&current_chil_cate.length'>
					<cover-view v-for="(item, idx) in current_chil_cate" :key="idx" class="chil_cate-item" :class="{active: chil_cate===item}" @click="getCovers(current, current_chil_cate, item)">{{item}}</cover-view>
				</cover-view>
			</cover-view>
			<cover-view class="res_list">
				<cover-view v-if="mapData.covers.length<=1" class="nodata">暂无数据</cover-view>
				<cover-view class="all_list" v-else>
					<cover-view v-if ="currentName =='all' &&(type==1 ||type==3)">
						<cover-view  class="poi_total flex-row" >
							<cover-view  class="poi_name ">
							POI热度
							</cover-view>
								<cover-view  class="poi_tip flex-row " @click="showPoiRule">
									POI热度说明
									<cover-view class="wenhao">
										?
									</cover-view>
									
							</cover-view>
							<cover-view  class="poi_num">
									POI={{totalNum}}个
							</cover-view>
						
						</cover-view>
						<cover-view  class="poi_item flex-row" v-for ="(item,index) in poiList" :key ="index">
							
							<cover-view class="poi_left flex-1">
								<cover-view  class="poi_zhanbi">
									
									<cover-view class="precent" :style="{width:item.bgWidth}">
									<cover-view  class="poi_name">
									{{item.title}}
									</cover-view>
									</cover-view>
								</cover-view>
							</cover-view>
							<cover-view class="poi_right">
									{{item.count}}个
							</cover-view>
							
							
							
						</cover-view>
					</cover-view>
					<template v-if='list.length'>
						<cover-view class="list_" :key="idx" v-for="(item, idx) in list " >
								<cover-view class="list_title flex-row" v-if =" currentName=='all' &&item.name!=='all'">
									<cover-image class= "list_title_img" :src="'/ditu/'+ item.name +'_icon.png' | imageFilter('m_80')"></cover-image>
									<cover-view>{{item.title}}</cover-view>
								</cover-view>
							<cover-view  class="item" v-for="(item, index) in item.list "  :key="index" v-show="item.title" @click="moveTo(item.latitude, item.longitude,index)">
					<cover-view class="left flex-1">
						<cover-view class="title">{{item.title}}</cover-view>
						<cover-view class="address">{{item.address}}</cover-view>
					</cover-view>
					<cover-view class="distance">{{item._distance | distanceFormat}}</cover-view>
				</cover-view>
						</cover-view>
						
					</template>

				</cover-view>
				
			</cover-view>
			
			</cover-view>
		<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
		<send-address-to-phone
      ref="sub_send_form"
      :sub_mode="sub_mode"
      @onsubmit="handleSubFormPhone"
      @signUp ="handleSubFormTel"
      :login_status="login_status"
    ></send-address-to-phone>
		<poiRules
      ref="poi_rules"
      @onsubmit="closePoiRules"
    ></poiRules>

	</view>
</template>

<script>
	import {wxShare} from '../../common/mixin'
	import SendAddressToPhone from '../../components/sendAddressToPhone.vue'
	import shareTip from '../../components/shareTip.vue'
	import poiRules from './components/poiRules.vue'
	import copyText from '../../common/utils/copy_text'
	import getChatInfo from '../../common/get_chat_info'
	import checkLogin from '../../common/utils/check_login'
	export default {
		components:{
			SendAddressToPhone,
			shareTip,
			poiRules
		},
		data() {
			return {
				id:"",
				type:1,
				lat:"",
				lng:"",
				current:'全部',
				currentName: 'all',
				chil_cate: '',
				current_chil_cate: [],
				currentType:'配套',
				type_list:[
					{
						name:'配套'
					},
					{
						name:'POI热度'
					}
				],
				cate_list: [
					// {
					// 	title: '全部',
					// 	name:'all',
					// 	children: []
					// },
					// {
					// 	title: '交通',
					// 	name:'traffic',
					// 	children: [
					// 		'公交',
					// 		'地铁',
					// 		'其他'
					// 	]
					// },
					// {
					// 	title: '教育',
					// 	name:'edu',
					// 	children: [
					// 		'幼儿园',
					// 		'小学',
					// 		'中学',
					// 		'高中'
					// 	]
					// },
					// {
					// 	title: '医疗',
					// 	name:'medical',
					// 	children: [
					// 		'综合',
					// 		'药店',
					// 		'其他'
					// 	]
					// },
					// {
					// 	title: '商业',
					// 	name:'business',
					// 	children: [
					// 		'购物',
					// 		'金融',
					// 		'机构'
					// 	]
					// }
				],
				mapData:{
					scale:13,
					covers: [],
				},
				cover_container_translate: uni.upx2px(300),
				animationData: {},
				icon_reversal: true,
				show_share_tip: false,
				cirles:[],
				map:null,
				moteNum:0,
				moteNum1:0,
				matches:[],
				list:[],
				totalNum:0,
				matchesAll:[],
				shareUserInfo:{},
				currentScale:13,
				toLogin:true,
				name:''
				// typeList
			};
		},
		mixins:[wxShare],
		computed: {
			login_status() {
				return this.$store.state.user_login_status
			},
			sub_mode() {
				return this.$store.state.sub_form_mode 
			},
			oneKm(){
				return this.getLonAndLat(this.lng,this.lat,0,1000)
			},
			twoKm(){
				return this.getLonAndLat(this.lng,this.lat,0,2000)
			},
			threeKm(){
				return this.getLonAndLat(this.lng,this.lat,0,3000)
			}
		},
		onReady(){
			
		},
		onLoad(options){
			this.lat = options.lat
			this.lng = options.lng
			this.title = options.title||''
			let ar = [options.lat,options.lng]
			this.mapData.cover = ar
			this.type = options.type
			this.currentScale =this.mapData.scale 
			this.map = uni.createMapContext("map",this);
			this.map.includePoints({
				padding:[20,20,20,20],
				points:[]
			})
			if(options.id){
				this.id = options.id
				uni.showLoading({
					title: '加载中...'
				})
				if (options.shareId) {
					this.shareId = options.shareId
					this.shareType = options.shareType
				}
				// this.getPois()
				// this.getCovers(this.current, [])
				this.loginState()
			}
			uni.$on("getDataAgain",()=>{
				if(this.type==1 ||this.type==3){
					this.getPois()
				}else {
					this.getCovers(this.current,[])
				}
				
			})
		},
		onUnload(){
			uni.$off("getDataAgain")
		},
		filters:{
			distanceFormat(val){
				if(!val){
					return ''
				}
				if(val<1000){
					return Math.ceil(val)+'m'
				}else{
					return (val/1000).toFixed(1)+'km'
				}
			}
		},
		onUnload(){
			uni.$off("getDataAgain")
		},
		methods:{
			loginState() {
				checkLogin({
					success: (res) => {
						if(this.type==1 ||this.type==3){
							this.getPois()
						}else {
							this.getCovers(this.current,[])
						}
					},
					fail: (res) => {
						if (this.toLogin == false) return
						this.toLogin = false
						if (this.$store.state.user_login_status == 1) {
							uni.setStorageSync('backUrl', window.location.href)
							this.$navigateTo('/user/login/login')
						}else if (this.$store.state.user_login_status == 2){
							this.$navigateTo('/user/bind_phone/bind_phone')
						}else {
							if(this.type==1){
								this.getPois()
							}else {
								this.getCovers(this.current,[])
							}
						}
					},
					complete: (res) => {
						this.$store.state.user_login_status = res.status
					},
				})
			},
			changeCate(name,title,child){
				this.currentName = name
				this.getNewCovers(title,child)
			},
			getChatInfo(){
				getChatInfo(this.shareUserInfo.mid, Number(this.type)+34, this.id)
			},
			getPois(){
				this.$ajax.get('map/mapNearbyPois.html',{id:this.id ,type:this.type},(res)=>{
					if (!res.data.done&&this.moteNum1 < 5 ){
						this.moteNum1++
						this.getPois()
						// return 
					}else {
						this.getCovers(this.current, [])
					}
					this.matchesAll = res.data.matches
					// this.totalNum = res.data.poi
					this.setPOI(res.data.matches)
				})
			},
			getCovers(keywords=this.current, chil_cate, chil_cate_name=''){
				let api
				// #ifndef MP-BAIDU
				api = "build/peripheryByMap.html"
				// #endif
				// #ifdef MP-BAIDU
				api = "build/peripheryByMapApp.html"
				// #endif
				this.current = keywords
				if(this.current == "全部") this.current =''
				if(!this.map){
						this.map = uni.createMapContext("map",this);
						this.map.includePoints({
							padding:[20,20,20,20]
						})
				}
				this.current_chil_cate = chil_cate
				this.chil_cate = chil_cate_name
				uni.showLoading({
					title: '加载中...'
				})
				this.$ajax.get(api,{id:this.id, keywords: this.current,type:this.type, filter: this.chil_cate,sid:this.shareId || '',sharetype:this.shareType ||''},(res)=>{
					uni.hideLoading()
					if(res.data.share){
						this.share = res.data.share
						this.getWxConfig()
					}
					this.title = res.data.title
					uni.setNavigationBarTitle({
						title:`${res.data.title||''}地图周边`
					})
					if (this.title){
						let name = this.title.split(""),numCount=0,ncount = 0
						name.map((item,index)=>{
							if(!isNaN(parseInt(item))){
								numCount ++
								if (numCount==1){
									numCount = 0 
									ncount ++
								}
							}
							if(index>2&&(index-ncount)%10== 1 ) {
								ncount=0
								numCount = 0
								name[index] = item+'\n'
							}
							return name 
						})
						this.name = name.join("")
					}
					if(res.data.code != 1){
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						return
					}
					if(res.data.shareUser) {
						this.currentUserInfo=res.data.shareUser
						if (this.currentUserInfo.adviser_id>0){
								this.currentUserInfo.shareType=1
								this.currentUserInfo.sid=this.currentUserInfo.adviser_id
						}else if (this.currentUserInfo.agent_id>0) {
							this.currentUserInfo.shareType=2
							this.currentUserInfo.sid=this.currentUserInfo.agent_id
						}else {
							this.currentUserInfo={
								sid:'',
								shareType:''
							}
						}
					}
					if (res.data.share_user) {
							this.shareUserInfo=res.data.share_user
					}
					if (!res.data.done && this.moteNum <5 ){
						this.moteNum++
						this.getNewCovers(this.current, chil_cate, chil_cate_name='')
						// return 
					}else {
						// if (this.currentName =='all' ) {
						// 		this.matchesAll = res.data.matches
						// }
						// this.getPois()
						this.isgetFinish =true
					}
					uni.hideLoading()
					this.matches = res.data.matches
					if (this.currentType =='配套') {
						this.setCovers(res.data.matches)
					}
					
					this.isGet = true
					if (!this.isGet) {
						this.map.moveToLocation({
							latitude: parseFloat(this.lat)-0.0015,
							longitude: parseFloat(this.lng)
						})
					}
					this.$nextTick(()=>{
							uni.pageScrollTo({
								scrollTop: uni.upx2px(200),
								duration: 200
							})
						})
					

						this.cirles = [
						{
							longitude:this.lng,
							latitude:this.lat,
							color:"#ff0000",
							radius:1000,
							strokeWidth:1,
						},
						{
							longitude:this.lng,
							latitude:this.lat,
							color:"#ff9c00",
							radius:2000,
							strokeWidth:1
						},
						{
							longitude:this.lng,
							latitude:this.lat,
							color:"#fee500",
							fillColor:"#00000026",
							radius:3000,
							strokeWidth:1
						}
					]
				})
			},
			getNewCovers(keywords=this.current, chil_cate, chil_cate_name=''){
				let api= 'map/mapNearbyMatches.html'
				
				this.current = keywords
				if(this.current =="全部") this.current =''
				if(!this.map){
						this.map = uni.createMapContext("map",this);
						this.map.includePoints({
							padding:[20,20,20,20]
						})
				}
				this.current_chil_cate = chil_cate
				this.chil_cate = chil_cate_name
				
				
				this.$ajax.get(api,{id:this.id, keywords:this.current ,type:this.type, filter: this.chil_cate,sid:this.shareId || '',sharetype:this.shareType ||''},(res)=>{
					
					// this.title = res.data.title
					uni.setNavigationBarTitle({
						title:`${res.data.title||''}地图周边`
					})
					// #ifdef H5 || MP-BAIDU
					if (res.data.seo) {
						this.seo = res.data.seo
					}
					// #endif
					if(res.data.code != 1){
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						return
					}
					if (res.data.shareUser) {
						this.currentUserInfo=res.data.shareUser
						if (this.currentUserInfo.adviser_id>0){
								this.currentUserInfo.shareType=1
								this.currentUserInfo.sid=this.currentUserInfo.adviser_id
						}else if (this.currentUserInfo.agent_id>0) {
							this.currentUserInfo.shareType=2
							this.currentUserInfo.sid=this.currentUserInfo.agent_id
						}else {
							this.currentUserInfo={
								sid:'',
								shareType:''
							}
						}
					}
					if (res.data.share_user) {
						this.shareUserInfo=res.data.share_user
					}
					if (!res.data.done && this.moteNum <5 ){
						this.moteNum++
						this.getNewCovers()
						// return 
					}else {
						// if (this.currentName =='all' ) {
						// 		this.matchesAll = res.data.matches
						// }
						// this.getPois()
						this.isgetFinish =true
					}
					uni.hideLoading()
					this.matches = res.data.matches
					if (this.currentType =='配套') {
						this.setCovers(res.data.matches)
					}
					
					this.isGet = true
					if (!this.isGet) {
						this.map.moveToLocation({
							latitude: parseFloat(this.lat)-0.0015,
							longitude: parseFloat(this.lng)
						})
					}
					this.$nextTick(()=>{
							uni.pageScrollTo({
								scrollTop: uni.upx2px(200),
								duration: 200
							})
						})
					

						this.cirles = [
						{
							longitude:this.lng,
							latitude:this.lat,
							color:"#ff0000",
							radius:1000,
							strokeWidth:1,
						},
						{
							longitude:this.lng,
							latitude:this.lat,
							color:"#ff9c00",
							radius:2000,
							strokeWidth:1
						},
						{
							longitude:this.lng,
							latitude:this.lat,
							color:"#fee500",
							fillColor:"#00000026",
							radius:3000,
							strokeWidth:1
						}
					]
				})
			},
			setCovers(data = 	this.matches  ){
					// setTimeout(() => {
					// 	this.mapData.covers = []
					// }, 100);
					 let array = [],list=[],covers =[],children
					data.map((cover,idx)=>{
						let icon,color,bgColor,title
						if (cover.data.length){
							cover.data.map(item=>{
								item.latitude = item.location.lat
								item.longitude = item.location.lng
							})
						}
						switch (cover.name) {
								case 'business':
									children=[
												'购物',
												'金融',
												'机构'
									]
									break;
								case 'edu':
										children=[
											'幼儿园',
											'小学',
											'中学',
											'高中'
										]
									break;
								case 'medical':
									children=[
												'综合',
												'药店',
												'其他'
											]
									break;
								case 'traffic':
									children=[
										'公交',
										'地铁',
										'其他'
									]
									break;
							
								default:
									break;
							}
						if (array.includes(cover.name)) {
							let i = list.findIndex (item=>item.name ==cover.name )

							list[i].list = list[i].list.concat(cover.data)
						}else {
								let obj = {
											name:cover.name,
											title:cover.keyword,
											list:cover.data,
											children:children
									}
								list.push(obj)
						}
						array.push(cover.name)
						switch(cover.name)
								{
								case 'business':
									icon = '/static/icon/foot.png'
									bgColor=  "#ffbabc"
									title="商"
									color="#fff"
									break
								case 'edu':
									icon = '/static/icon/edu.png'
									title="教"
									bgColor="#34dec1"
									color="#fff"
									break
								case 'medical':
									icon = '/static/icon/yiliao.png'
									title="医"
									bgColor="#feb9bb"
									color="#fff"
									break
								case 'traffic':
									icon = '/static/icon/jiaotong.png'
									bgColor="#66d1fa"
									title ="交"
									color="#fff"
									break
								default:
									icon = '/static/icon/center.png'
						}
						if (cover.data&&cover.data.length) {
							
								cover.data.map(item=>{
									let name_a =item.title.split("")
									name_a.map((name,index)=>{
										if (index >2 &&index %10 == 1 ) {
											name_a[index] = name +' \n '
										}
										return name
									})
									let name_s = name_a.join("")
									item.name = name_s
									// item.name = item.title.
									let distance = parseInt(item._distance)
									let ob = {
										width: 30,
										height: 30,
										iconPath: icon,
										// name:title,
										latitude: item.location.lat,
										longitude: item.location.lng,
										title: item.title,
										name:item.name,
										// showtitle:this.currentScale<13?false:true,
										id:item.id+'' +cover.keyword +cover.filter,
										address: item.address,
										_distance: item._distance,
										callout: {
											content: this.currentScale<=14?title:item.name,
											padding: 5,
											fontSize:10,
											boxShadow:'none',
											bgColor,
											color,
											width:100,
											borderRadius: 4,
											borderColor:bgColor,
											display:'ALWAYS'
										},
										distance: distance
									}
									covers.push(ob)
									return item
								})
						}
						return cover
					})
					setTimeout(() => {
						covers.push({
							latitude: this.lat,
							longitude: this.lng,
							id:this.id,
							width: 0,
							height: 0,
							callout: {content:this.name,padding:5,borderRadius:4,bgColor:"#f65354",color:"#ffffff",display:'ALWAYS',zIndex:11},
							iconPath: '/static/icon/center.png'
						})
					}, 100);
					
					covers.push({
						latitude: this.oneKm.lat,
						id:"a"+1,
						longitude: this.oneKm.lon,
						width: -1,
						height:-1,
						label: {
							content:'1公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff0000",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5,
							borderColor:'#ffffff'
						},
						iconPath: '/static/icon/none.png'
					})
					covers.push({
						latitude: this.twoKm.lat,
						longitude: this.twoKm.lon,
						width: -1,
						height: -1,
						id:"a"+2,
						label: {
							content:'2公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff9c00",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
						},
						iconPath: '/static/icon/none.png'
					})
					covers.push({
						latitude: this.threeKm.lat,
						longitude: this.threeKm.lon,
						width: -1,
						height: -1,
						id:"a"+3,
						label: {
							content:'3公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#fee500",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
							},
						iconPath: '/static/icon/none.png'
					})
					// this.mapData.covers.length = 0
					// this.$set(this.mapData,'covers', [])
					this.$nextTick(()=>{
						this.mapData.covers = covers
					})
					
					
					list.unshift({
						name:"all",
						title:'全部',
						children:[]
					})
					if (this.isgetFinish&&this.cate_list.length) {
					}else {
						this.cate_list = list
					}
					this.list = list
			},
			setPOI(data = this.matchesAll,setMap=1 ){
					let covers =[]
					 
					data.map((cover)=>{
						let icon,color,bgColor,title
						switch(cover.name)
						{
						case 'food':
							icon = '/static/icon/foot.png'
							break
						case 'shopping':
							icon = '/static/icon/jiaotong.png'
							break
						case 'medical':
							icon = '/static/icon/yiliao.png'
						
							break
						case 'school':
							icon = '/static/icon/edu.png'
							break
						case 'culture':
							icon = '/static/icon/edu.png'
							break
						case 'life':
							icon = '/static/icon/jiaotong.png'
							break
						default:
							icon = '/static/icon/center.png'
						}
						
						if (cover.data&&cover.data.length) {
							
								cover.data.map(item=>{
									let ob = {
										width: 10,
										height: 10,
										iconPath: icon,
										// name:title,
										latitude: item.location.lat,
										longitude: item.location.lng,
										id:item.id+'' +cover.keyword +cover.filter,
									}
									covers.push(ob)
									return item
								})
						}
						return cover
					})
					covers.push({
						latitude: this.lat,
						longitude: this.lng,
						id:this.id,
						width: 0,
						height: 0,
						callout: {content:this.title,padding:5,borderRadius:4,bgColor:"#f65354",color:"#ffffff",display:'ALWAYS',zIndex:11},
						iconPath: '/static/icon/center.png'
					})

					covers.push({
						latitude: this.oneKm.lat,
						id:"a"+1,
						longitude: this.oneKm.lon,
						width: -1,
						height:-1,
						label: {
							content:'1公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff0000",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5,
							borderColor:'#ffffff'
						},
						iconPath: '/static/icon/center.png'
					})
					covers.push({
						latitude: this.twoKm.lat,
						longitude: this.twoKm.lon,
						width: -1,
						height: -1,
						id:"a"+2,
						label: {
							content:'2公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff9c00",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
						},
						iconPath: '/static/icon/center.png'
					})
					covers.push({
						latitude: this.threeKm.lat,
						longitude: this.threeKm.lon,
						width: -1,
						height: -1,
						id:"a"+3,
						label: {
							content:'3公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#fee500",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
							},
						iconPath: '/static/icon/center.png'
					})
					if (setMap) {
						this.$nextTick(res=>{
							this.mapData.covers = covers
						})
					}
				let arr  = data,maxNum =0
				this.totalNum =0
					data.map (item=>{
						this.totalNum += item.count
						if (item.count >maxNum ) {
							maxNum = item.count
						}
						return  item
					})
					arr.map ((item) =>{
						let pecent = (item.count)/maxNum
						// if( pecent<0.5) {
							item.bgWidth="calc(" + (pecent*100) +'% + 100rpx) '
						// }else {
						// 	item.bgWidth=(pecent*100) +'%'
						// }
							
					})
				this.poiList = arr
					
					
			},
			showPoiRule(){
				this.$refs.poi_rules.showPopup()
			},

			/**
				02
				* 根据一个经纬度及距离角度，算出另外一个经纬度
				03
				* @param {*} lon 经度 113.3960698
				04
				* @param {*} lat 纬度 22.941386
				05
				* @param {*} brng 方位角 45 ---- 正北方：000°或360° 正东方：090° 正南方：180° 正西方：270°
				06
				* @param {*} dist 90000距离(米)
				07
			*/
			getLonAndLat (lon,lat,brng,dist){
						var a=6378137;

					var b=6356752.3142;

					var f=1/298.257223563;

					var lon1 = lon*1;

					var lat1 = lat*1;

					var s = dist;

					// var alpha1 = mapNumberUtil.rad(brng);
			// * (Math.PI/180)
					var alpha1 = brng *(Math.PI/180)
					var sinAlpha1 = Math.sin(alpha1);

					var cosAlpha1 = Math.cos(alpha1);

					// var tanU1 = (1-f) * Math.tan(mapNumberUtil.rad(lat1));
					var tanU1 = (1-f) * Math.tan(lat1*(Math.PI/180));
					var cosU1 = 1 / Math.sqrt((1 + tanU1*tanU1)), sinU1 = tanU1*cosU1;

					var sigma1 = Math.atan2(tanU1, cosAlpha1);

					var sinAlpha = cosU1 * sinAlpha1;

					var cosSqAlpha = 1 - sinAlpha*sinAlpha;

					var uSq = cosSqAlpha * (a*a - b*b) / (b*b);

					var A = 1 + uSq/16384*(4096+uSq*(-768+uSq*(320-175*uSq)));

					var B = uSq/1024 * (256+uSq*(-128+uSq*(74-47*uSq)));

					var sigma = s / (b*A), sigmaP = 2*Math.PI;

					while (Math.abs(sigma-sigmaP) > 1e-12) {

							var cos2SigmaM = Math.cos(2*sigma1 + sigma);

							var sinSigma = Math.sin(sigma);

							var cosSigma = Math.cos(sigma);

							var deltaSigma = B*sinSigma*(cos2SigmaM+B/4*(cosSigma*(-1+2*cos2SigmaM*cos2SigmaM)-

							B/6*cos2SigmaM*(-3+4*sinSigma*sinSigma)*(-3+4*cos2SigmaM*cos2SigmaM)));

							sigmaP = sigma;

							sigma = s / (b*A) + deltaSigma;

					}

					var tmp = sinU1*sinSigma - cosU1*cosSigma*cosAlpha1;

					var lat2 = Math.atan2(sinU1*cosSigma + cosU1*sinSigma*cosAlpha1,(1-f)*Math.sqrt(sinAlpha*sinAlpha + tmp*tmp));

					var lambda = Math.atan2(sinSigma*sinAlpha1, cosU1*cosSigma - sinU1*sinSigma*cosAlpha1);

					var C = f/16*cosSqAlpha*(4+f*(4-3*cosSqAlpha));

					var L = lambda - (1-C) * f * sinAlpha * (sigma + C*sinSigma*(cos2SigmaM+C*cosSigma*(-1+2*cos2SigmaM*cos2SigmaM)));

					var revAz = Math.atan2(sinAlpha, -tmp); // final bearing
			// * (180/Math.PI)
					// var lonLatObj = {lon:lon1+mapNumberUtil.deg(L),lat:mapNumberUtil.deg(lat2)}
			var lonLatObj = {lon:lon1+L * (180/Math.PI),lat:lat2 * (180/Math.PI)}
					return lonLatObj
					

			},

			onTouchStart(e){
				this.touch_start = e.touches[0].clientY
				this.current_translate = this.cover_container_translate
			},
			onTouchMove(e){
				let difference = this.current_translate+(e.touches[0].clientY - this.touch_start)
				if(difference<=0){
					difference = 0
				}
				if(difference>=uni.upx2px(500)){
					difference = uni.upx2px(500)
				}
				this.cover_container_translate = difference
			},
			onTouchEnd(e){
				if(this.cover_container_translate<130){
					this.handleAnimation(this.cover_container_translate, 0)
				}else{
					this.handleAnimation(this.cover_container_translate, uni.upx2px(500))
				}
			},
			regionchange(){
				this.map.getScale({
					success:(res)=>{
						if (res.scale == this.currentScale )  return 
						
						this.currentScale = res.scale
						if (this.currentType=='配套') {
							this.setCovers()
						}
							
					}
				})

			},
			callouttap(e){
					if (e.detail.markerId == this.id ) return 
				let covers = []
				this.mapData.covers.map(item=>{
					covers.push(item)
				})
				let item = 	this.mapData.covers.find(item=>item.id == e.detail.markerId )	
				let i = 	this.mapData.covers.findIndex(item=>item.id == e.detail.markerId )	
				item.callout.content = item.title 
				this.mapData.covers.splice(i,1)
				this.$nextTick(res=>{
					this.mapData.covers.splice(i,0,item)
				})
				this.$forceUpdate()
				
				
			
			
			},
			chooseType(name){
				this.currentType = name
				if (this.currentType=='配套'){
					this.setCovers()
				}else{
						this.$nextTick(()=>{
							uni.pageScrollTo({
								scrollTop: uni.upx2px(500),
								duration: 200
							})
						})
						// if (!this.poiList.length) {
						// 	this.getPois()
						// }else {
							this.setPOI(this.matchesAll,1)
						// }
					
				}
			},
			markertap (e){
				this.markerId = e.detail.markerId
				if (this.markerId == this.id ) return 
				let item = this.mapData.covers.find(item=>item.id == e.detail.markerId )
				let i = this.mapData.covers.findIndex(item=>item.id == e.detail.markerId )
				item.callout.content  = item.title;
				this.mapData.covers.splice(i,1)
				this.$nextTick(res=>{
					this.mapData.covers.splice(i,0,item)
				})
				
			},
			handleAnimation(value, last_value){
				var timer = setInterval(()=>{
					if(last_value===0){
						this.cover_container_translate-=3
						if(this.cover_container_translate<=0){
							this.cover_container_translate = 0
							clearInterval(timer)
						}
					}
					if(last_value>value){
						this.cover_container_translate+=3
						if(this.cover_container_translate>=last_value){
							this.cover_container_translate = last_value
							clearInterval(timer)
						}
					}
				}, 6)
			},
			opemLocation(){
				uni.openLocation({
					latitude: parseFloat(this.lat),
					longitude: parseFloat(this.lng),
					name: this.title,
					address: this.address||''
				})
			},
			sendAddressToPhone(){
				this.$refs.sub_send_form.showPopup()
			},
			handleSubFormPhone(e){
				//提交报名
				e.from ='发送地址到手机'
				// e.name=""
				// e.bid = this.id
				
				e.build_id =this.id
				this.$ajax.get('member/authRegister', e, res => {
					uni.hideLoading()
					if (res.data.code === 1) {
							//提示报名成功 微信小程序需要单独处理
							// #ifndef MP-WEIXIN
								if (res.data.token) {
										uni.setStorageSync('token', res.data.token)
								}
								this.$store.state.user_login_status =3
								//#endif
								uni.showToast({
									title: res.data.msg,
									icon: 'none'
								})
							this.$refs.sub_send_form.closeSub()
							// if(this.login_status==1&&res.data.token ){
							//   let token =res.data.token 
							//   uni.setStorageSync("token",token)
							//   this.$store.state.user_login_status=3
							// }
						// } else {
						//   this.$refs.sub_form.getVerify()
						// }
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
					}
				})
			},
			handleSubFormTel(e) {
				//提交报名
				e.from ='地图周边页面'
				e.bid = this.id
				e.type = this.sub_type || ''
				this.$ajax.post('build/signUp.html', e, res => {
				})
			},
			handleShare(){
				let href =window.location.origin+ window.location.pathname+'?id='+this.id+'&lat='+this.lat+'&lng='+this.lng+"&type="+this.type
				if (this.currentUserInfo.sid){
					href += "&shareId="+this.currentUserInfo.sid + "&shareType="+ this.currentUserInfo.shareType
				}

				let content = `${this.title}地图周边:${href}`
				copyText(content, ()=>{
					uni.showToast({
						title: '复制成功，去发送给好友吧',
						icon: 'none'
					})
				})
			},
			moveTo(lat, lng,index){
				let item = this.mapData.covers.find(item=>item.latitude == lat )
				let i = this.mapData.covers.findIndex(item=>item.latitude == lat )
				item.callout.content  = item.name;
				this.map.moveToLocation({
					latitude: parseFloat(lat)-0.0015,
					longitude: parseFloat(lng)
				})
				this.mapData.covers.splice(i,1)
				this.$nextTick(res=>{
					this.mapData.covers.splice(i,0,item)
				})
			},
			goBack (){
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					// #ifdef H5
					var ua = window.navigator.userAgent.toLowerCase();
					//通过正则表达式匹配ua中是否含有MicroMessenger字符串
					if(ua.match(/MicroMessenger/i) == 'micromessenger'){
						uni.switchTab({
							url: '/pages/index/index'
						})
					}else{
						window.history.go(-1)
					}
					// #endif
					// #ifndef H5
						uni.switchTab({
							url: '/pages/index/index'
						})
					// #endif
				}
			},
		},
		onPageScroll(e){
			if(e.scrollTop === 0){
				this.icon_reversal = true
			}else{
				this.icon_reversal = false
			}
		},
		onShareAppMessage(){
			return {
				title :`${this.title}地图周边`,
				path:"/propertyData/map/map?id=" + this.id + '&type=1&lat=' + this.lat + '&lng=' + this.lng
			}
		}
	}
</script>

<style lang="scss">
.page{
	height: calc(100vh - 44px);
	overflow: hidden;
}
	map{
		position: fixed;
		width: 100%;
		top: 0;
		bottom: 0;
		height: auto;
	}
	.grid-box{
		position: absolute;
		width: 100%;
		bottom: 0;
	}
	.cate-icon{
		width: 48rpx;
		height: 48rpx;
	}
	.flex-row {
		display: flex;
	}

	.cover_container{
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #fff;
		transform: translateY(500rpx);
		.options_btn{
			display: flex;
			justify-content: center;
			padding-top: 24rpx;
			.icon{
				width: 48rpx;
				&.icon_reversal{
					transform: rotateX(180deg);
				}
			}
		}
	}
	.map-cate-list {
    padding: 16rpx 0;
    display: flex;
    align-items: center;
		justify-content: space-around;
    background-color: #fff;
    // box-shadow: 0 0 12rpx 0 rgba(0, 0, 0, 0.15);
    .cate-item {
      // flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
			padding: 8rpx;
      color: #999;
			position: relative;
      &.active{
        color: $uni-color-primary;
        &:after{
          content: '';
          position: absolute;
          bottom: 0;
          left: 12rpx;
          right: 12rpx;
          height: 4rpx;
          background-color: $uni-color-primary;
        }
      }
      .image {
        width: 48rpx;
        height: 48rpx;
        margin-right: 6rpx;
      }
      .text {
        font-size: 24rpx;
      }
    }
  }
	
	.chil_cate{
		padding: 24rpx 48rpx;
		display: flex;
		align-items: center;
		.chil_cate-item{
			height: 48rpx;
			line-height: 48rpx;
			padding: 0 24rpx;
			border-radius: 24rpx;
			background-color: #F8F8F8;
			~.chil_cate-item{
				margin-left: 24rpx;
			}
			&.active{
				background-color: #FFE3E3;
				color: #FF3939;
			}
		}
	}

	.res_list{
		padding: 0 48rpx;
		box-sizing: border-box;
		height: 500rpx;
		overflow-y: auto;
		width: 100%;
		.nodata{
			text-align: center;
			padding: 24rpx;
			color: #999;
		}
		.item{
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			align-items: flex-end;
			width: 100%;
			overflow: hidden;
			margin-bottom: 24rpx;
			font-size: 24rpx;
			.title{
				margin-bottom: 8rpx;
				flex-shrink: 0;
				font-size: 26rpx;
			}
			.address{
				// margin: 0 12rpx;
				flex: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				color: #999;
			}
			.distance{
				margin-left: 12rpx;
				flex-shrink: 0;
				color: #999;
			}
		}
	}
	
	.right_menu{
		position: absolute;
		right: 24rpx;
		bottom: 300rpx;
		.item{
			width: 120rpx;
			height: 120rpx;
		}
	}


.poi_total {
	// justify-content: space-between;
	align-items: center;
	padding:10rpx 0;
	color: #000;
	.poi_name {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
	.poi_tip{
		font-size: 22rpx;
		color: #999999;
		margin-left: 24rpx;
		align-items: center;
		.wenhao {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 12px;
			height: 12px;
			font-size: 12px;
			transform: scale(0.8);
			margin-left: 5rpx;
			border: 1px solid #999;
			color: #999;
			border-radius: 50%;
		}
	}
	.poi_num {
		font-size:28rpx;
		color: #FB656A;
		margin-left: auto;
	}	
	// font-weight: 600;


}
.poi_item{
	// width: 100%;
	padding:15rpx 0;
	align-items: center;
	position: relative;

	.poi_right {
			min-width: 120rpx;
			font-family: PingFangSC-Regular;
			font-size: 28rpx;
			color: #333333;
			text-align: right;
	}
	.poi_name {
		// position: absolute;
		// top:25rpx;
		// left: 48rpx;
		padding-left: 24rpx;
		font-size: 28rpx;
		color: #fff;

	}
	.poi_zhanbi {
		width: 100%;
		// padding: 15rpx  0;
		background: #F8F8F8;;
		border-top-right-radius: 30rpx;
		border-bottom-right-radius:30rpx;
		
		// background: #fff;
		// margin-bottom: 10rpx;
		.precent{
			padding: 10rpx  0;
			background-image: linear-gradient(36deg, #FFA533 0%, #FE6C17 100%);
			border-top-right-radius: 30rpx;
			border-bottom-right-radius:30rpx;
			// text-align: right;
		}
	}

} 

.list_title{
	font-family: PingFangSC-Medium;
	font-size: 28rpx;
	padding: 8rpx 0;
	font-weight: 600;
	color: #333333;
	align-items: center;

	.list_title_img {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
		object-fit: cover;
	}
}
.shareInfo {
	// margin-bottom: 24rpx;
	align-items: center;
	padding: 15rpx 48rpx;
	.share_prelogo{
		width: 50rpx;
		height: 50rpx;
		border-radius: 100%;
		object-fit: cover;
	}
	.share_name{
		margin-left: 16rpx;
		font-size: 28rpx;
	}
	.ask_button {
		padding: 8rpx 15rpx;
		font-size: 24rpx;
		align-items: center;
		background: #FE6C17;
		color: #fff;
		border-radius: 40rpx;
		.ask_btn_image{
			width: 30rpx;
			height: 30rpx;
			object-fit: cover;
			margin-right: 8rpx;
		}
		.ask_btn_name {
			font-size: 24rpx;
		}
	}
}
.back{
	position: fixed;
	top: 10rpx;
	left: 10rpx;
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(0,0,0,0.2);
	overflow: hidden;
	align-items: center;
	justify-content: center;
	z-index: 10;
	.back_icon {
		width: 18rpx;
		height: 36rpx;
		object-fit: cover;
	}
}
// #map {
// 		::v-deep div{
// 			text-align: center!important;
// 			line-height: 1.2!important;
// 			max-width: 50vw;
// 			white-space: normal;
// 		}
// 	}
</style>
