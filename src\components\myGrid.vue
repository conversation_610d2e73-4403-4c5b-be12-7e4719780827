<template>
	<view class="uni-grid" :class="{'uni-grid-no-border':!showBorder,'uni-grid-no-out-border':showBorder && !showOutBorder}">
		<view class="uni-grid__flex" :style="{marginTop: i>0&&rowTop?rowTop:''}" v-for="(items,i) in gridGroup" :key="i">
			<view class="uni-grid-item" :hover-class="hoverClass" :hover-start-time="20" :hover-stay-time="70" v-for="(item,index) in items" :key="index" :style="'max-width:'+(100/columnNum).toFixed(6)+'%'" :class="[index == columnNum ? 'uni-grid-item-last' : '','uni-grid-item-' + type]" @click="onClick(i,index)">
				<view class="uni-grid-item__content">
                    <my-icon v-if="iconType=='font'" :size="item.icon_size||24" :type="item.icon_name" :color="item.icon_color"></my-icon>
										<view class="uni-grid-item-image"  :class="className" v-else-if="iconType=='image'">
												<image   :src="item.image || item.icon"></image>
												<view class="uni-grid-item-image-count" v-if ='item.count>=0'>
													<view>{{item.count}}</view>
												</view>
										</view>
				
					<!-- #ifdef APP-PlUS -->
					<text class="uni-grid-item-text" :style="'font-size: '+fontSize*2+'upx'">{{item.text || item.title}}</text>
					 <!-- #endif -->
					 <!-- #ifndef APP-PlUS -->
					<text class="uni-grid-item-text" :style="'font-size: '+fontSize*2+'rpx'">{{item.text || item.title}}</text>
					 <!-- #endif -->
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import myIcon from './icon'
	export default {
		name: "uni-grid",
		props: {
            options: Array,
            iconType:{
                type:String,
                default:"image"
            },
			type: { //布局格式，长方形oblong，正方形square
				type: String,
				default: 'square'
			},
			columnNum: { //每一行有多少个
				type: [Number, String],
				default: 3
			},
			showOutBorder: { //显示外边框
				type: [Boolean, String],
				default: true
			},
			showBorder: { //是否显示border，如果为false，showOutBorder无效
				type: [Boolean, String],
				default: true
			},
			rowTop: String,
			fontSize:{
				type:Number,
				default:12
			},
			className:{
				type:String,
				default:""
			},
			hoverClass:{
				type:String,
				default:"uni-grid-item-hover"
			}
		},
		data() {
			return {}
        },
        components:{
            myIcon
        },
		computed: {
			gridGroup() {
				let group = []
				let groupItem = []
				this.options && this.options.forEach((item, index) => {
					groupItem.push(item)
					if (index % this.columnNum === this.columnNum - 1) {
						group.push(groupItem)
						groupItem = []
					}
				})
				if (groupItem.length > 0) {
					group.push(groupItem)
				}
				groupItem = null
				return group
			},
			
		},
		methods: {
			onClick(index, num) {
				this.$emit('click', {
					index: index * this.columnNum + num
				})
			}
		}
	}
</script>

<style lang="scss">
	.uni-grid {
		position: relative;
		display: flex;
		flex-direction: column;

		&__flex {
			display: flex;
			flex-direction: row;
			~.uni-grid__flex{
				margin-top: 42rpx;
			}
		}

		&-item {
			display: flex;
			position: relative;
			flex-direction: column;
			flex:1;

			&:before {
				display: block;
				content: " ";
				padding-bottom: 107%;
			}

			&:after {
				content: '';
				position: absolute;
				z-index: 1;
				transform-origin: center;
				box-sizing: border-box;
				top: -50%;
				left: -50%;
				right: -50%;
				bottom: -50%;
				border-color: $uni-border-color;
				border-style: solid;
				border-width: 1px;
				-webkit-transform: scale(.5);
				transform: scale(.5);
				border-top-width: 0;
				border-left-width: 0;
			}

			&__content {
				position: absolute;
				left: 0;
				top: 0;
				bottom: 0;
				width: 100%;
				// height: 105%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
			}

			&-text {
				font-size: $uni-font-size-sm;
				color: $uni-text-color;
				margin-top: 20upx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			&-hover {
				background-color: $uni-bg-color-hover;
			}

			&-image {
				width: $uni-img-size-lg;
				height: $uni-img-size-lg;
				position: relative;
				image {
					width: 100%;
					height: 100%;
				}
				&-count {
				color: #fff;
				font-weight: 600;
				font-size: 24rpx;
				position: absolute;
				top: 0;
				right: 0;
				left: 0;
				bottom: 0;

				display: flex;
				justify-content: center;
				align-items: center;
      	
			}
			}
			
            &-image.round{
                border-radius: 30upx;
            }
			&-image.sm {
				width:60upx;
				height: 60upx;
			}
		}
	}

	.uni-grid .uni-grid__flex:first-child .uni-grid-item:after {
		border-top-width: 1px;
	}

	.uni-grid .uni-grid__flex .uni-grid-item:first-child:after {
		border-left-width: 1px;
	}

	/* 无外边框 */
	.uni-grid.uni-grid-no-out-border .uni-grid__flex {
		&:first-child .uni-grid-item:after {
			border-top-width: 0;
		}

		&:last-child .uni-grid-item:after {
			border-bottom-width: 0;
		}

		.uni-grid-item:first-child:after {
			border-left-width: 0;
		}

		.uni-grid-item:last-child:after {
			border-right-width: 0;
		}
	}

	/* 无边框 */
	.uni-grid.uni-grid-no-border {
		.uni-grid-item:after {
			border-width: 0;
		}

		.uni-grid__flex:first-child .uni-grid-item:after {
			border-top-width: 0px;
		}

		.uni-grid__flex .uni-grid-item:first-child:after {
			border-left-width: 0px;
		}
	}

	.uni-grid-item-oblong {
		&.uni-grid-item:before {
			padding-bottom: 60%;
		}

		.uni-grid-item {
			&__content {
				flex-direction: row;
			}

			&-image {
				width: $uni-img-size-base;
				height: $uni-img-size-base;
			}

			&-text {
				margin-top: 0;
				margin-left: 12upx;
			}
		}
	}
</style>
