<template>
  <view class="page">
    <view class="title">您的身份是？</view>
    <view class="flex-box btn-group">
      <view class="btn" @click="getRegInfo">置业顾问</view>
      <view class="btn" @click="showApply">业内人士</view>
    </view>
    <my-popup position="bottom" ref="apply">
      <view class="apply_form">
        <view class="header_title">
          <text>个人信息</text>
          <view class="cancel" @click="$refs.apply.hide()">取消</view>
        </view>
        <scroll-view scroll-y style="max-height: 70vh" class="form">
          <view class="form-item">
            <input type="text" v-model="apply_form.cname" placeholder="真实姓名">
          </view>
          <view class="form-item">
            <input type="number" maxlength="11" v-model="apply_form.tel" placeholder="手机号">
          </view>
          <view class="form-item">
            <view class="select-row">
               <picker @change="onTypeChange" :range="types" range-key="name">
                <view :class="type_name?'':'novalue'">{{type_name||'请选择认证类型'}}</view>
              </picker>
              <my-icon type="ic_into" color="#999" size="32rpx"></my-icon>
            </view>
          </view>
          <view class="form-item">
            <input type="text" v-model="apply_form.verified_name" placeholder="认证信息（如： xx楼盘销售总监）">
          </view>
          <!-- <view class="form-item">
            <input type="text" v-model="apply_form.verified_desc" placeholder="描述（如：我是xx楼盘销售总监）">
          </view> -->
          <view class="form-item">
            <view class="label">
              <text>微信</text>
              <text class="tip">(微信二维码)</text>
            </view>
            <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadWechatImg">
              <image class="img" mode="aspectFill" v-if="apply_form.wechat_img" :src="apply_form.wechat_img | imageFilter('w_240')"></image>
              <view v-else class="upload-btn">
                <my-icon type="ic_jia" size="96rpx" color="#d8d8d8"></my-icon>
              </view>
            </my-upload>
          </view>
          <view class="form-item" v-if="apply_form.verified_cate==2">
            <view class="label">
              <text>营业执照</text>
            </view>
            <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadBusiness">
              <image class="img" mode="aspectFill" v-if="apply_form.business_license" :src="apply_form.business_license | imageFilter('w_240')"></image>
              <view v-else class="upload-btn">
                <my-icon type="ic_jia" size="96rpx" color="#d8d8d8"></my-icon>
              </view>
            </my-upload>
          </view>
          <view class="form-item" v-if="apply_form.verified_cate==1">
            <view class="label">
              <text>身份证</text>
              <text class="tip">(正面)</text>
            </view>
            <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadSfzFront">
              <image class="img" mode="aspectFill" v-if="apply_form.sfz_front" :src="apply_form.sfz_front | imageFilter('w_240')"></image>
              <view v-else class="upload-btn">
                <my-icon type="ic_jia" size="96rpx" color="#d8d8d8"></my-icon>
              </view>
            </my-upload>
          </view>
          <view class="form-item" v-if="apply_form.verified_cate==1">
            <view class="label">
              <text>身份证</text>
              <text class="tip">(反面)</text>
            </view>
            <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadSfzBack">
              <image class="img" mode="aspectFill" v-if="apply_form.sfz_back" :src="apply_form.sfz_back | imageFilter('w_240')"></image>
              <view v-else class="upload-btn">
                <my-icon type="ic_jia" size="96rpx" color="#d8d8d8"></my-icon>
              </view>
            </my-upload>
          </view>
          <view class="form-item">
            <view class="button_box">
              <view class="button" @click="onSubmit">提交审核</view>
            </view>
          </view>
        </scroll-view>
      </view>
    </my-popup>
  </view>
</template>

<script>
import myPopup from '@/components/myPopup'
import myUpload from '@/components/form/myUpload'
import myIcon from "@/components/myIcon"
export default {
  name: '',
  components: {
    myPopup,
    myUpload,
    myIcon
  },
  data () {
    return {
      types: [
        {
          name: "个人",
          id: 1
        },
        {
          name: "机构",
          id: 2
        }
      ],
      type_name: "",
      apply_form:{
        verified_cate: "",
        cname: "",
        tel: "",
        verified_name: "",
        wechat_img: "",
        business_license: "",
        sfz_front: "",
        sfz_back : ""
      }
    }
  },
  methods: {
    getRegInfo(){
      // 获取会员信息
      this.$ajax.get('Adviser/adv_reg', {}, (res) => {
        if (res.data.code == 1) {
          this.cname = res.data.data.cname||''
          this.tel = res.data.data.tel||''
          this.wxkf = res.data.wxqunkefu||''
          this.$navigateTo("/user/consultant/add?cname=" + this.cname + "&tel=" + this.tel+"&wxkf="+this.wxkf)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
        }
      }, (err) => {
        console.log(err)
      }, false)
    },
    showApply(){
      this.$refs.apply.show()
    },
    uploadWechatImg(e){
      this.apply_form.wechat_img = e.files.join(',')
    },
    uploadBusiness(e){
      this.apply_form.business_license = e.files.join(',')
    },
    uploadSfzFront(e){
      this.apply_form.sfz_front = e.files.join(',')
    },
    uploadSfzBack(e){
      this.apply_form.sfz_back = e.files.join(',')
    },
    onTypeChange(e){
      this.apply_form.verified_cate = this.types[e.detail.value].id
      this.type_name = this.types[e.detail.value].name
    },
    onSubmit(){
      this.$ajax.post('building_circle/apply', this.apply_form, res=>{
        if(res.data.code === 1){
          uni.showToast({
            title: res.data.msg
          })
          this.$refs.apply.hide()
          uni.$emit('getDataAgain', {})
          setTimeout(()=>{
            this.$navigateBack()
          }, 2000)
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.page{
  // #ifdef H5
  height: calc(100vh - 44px);
  // #endif
  // #ifndef H5
  height: 100vh;
  // #endif
  background-color: #fff;
}
.title{
  padding: 0 48rpx;
  font-size: 48rpx;
  font-weight: bold;
  .cancel{
    font-weight: 400;
    padding: 24rpx;
    color: #999;
    top: 0;
    right: 48rpx;
  }
}
.btn-group{
  align-content: center;
  padding: 36rpx;
  .btn{
    margin: 12rpx;
    width: 316rpx;
    height: 112rpx;
    text-align: center;
    line-height: 112rpx;
    border: 1rpx solid #e2e2e2;
    box-shadow: 0 9rpx 20rpx 0 rgba(0,0,0,0.05);
    border-radius: 8rpx;
    border-radius: 8rpx;
    font-size: 36rpx;
    color: #333;
  }
}
.apply_form{
  padding: 48rpx;
  background-color: #fff;
  border-top-left-radius: 48rpx;
  border-top-right-radius: 48rpx;
  .header_title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 48rpx;
    font-size: 40rpx;
    .cancel{
      font-size: 26rpx;
      padding: 24rpx 0;
      color: #999;
    }
  }
}
.form{
  .form-item{
    margin-bottom: 24rpx;
    input{
      padding: 24rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      background-color: #f8f8f8;
    }
    .label{
      font-size: 36rpx;
      margin-bottom: 24rpx;
      .tip{
        margin-left: 40rpx;
        font-size: 28rpx;
        color: #999;
      }
    }
    .upload-btn{
      width: 25vw;
      height:25vw;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;
      background-color: #f5f5f5;
    }
    .img{
      width: 25vw;
      height: 25vw;
      border-radius: 8rpx;
    }
    .button_box{
      margin-top: 192rpx;
      padding: 0 48rpx;
      .button{
        height: 88rpx;
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 32rpx;
        background: #FB656A;
        color: #fff;
        box-shadow: 0 8rpx 32rpx 0 rgba(251,101,106,0.40);
        border-radius: 8rpx;
      }
    }
    .select-row{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      background-color: #f8f8f8;
      picker{
        flex: 1;
        // height: 62upx;
        margin-right: 10rpx;
      }
      .novalue{
        color: grey;
      }
    }
  }
}
</style>