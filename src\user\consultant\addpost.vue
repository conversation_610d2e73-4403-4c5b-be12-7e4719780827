<template>
<view class="add-post">
  <!-- <my-input label="标题" :value="params.title" name="title" @input="handleInput"></my-input> -->
  <view class="block">
    <view class="build_info" v-if="all_build.length == 1">
      <text class="label">所属楼盘</text>
      <text class="value">{{buildName}}</text>
    </view>
    <view class="select_box" v-if="all_build.length > 1">
      <view class="label">所属楼盘</view>
      <view class="elsect_row" @click="showBuildList">
        <text class="value" :class="{noval: !params.buildid}">{{buildName||'请选择'}}</text>
        <my-icon type="ic_into" color="#999" size="32rpx"></my-icon>
      </view>
    </view>
     <!-- <my-select v-if="build_list.length > 1" :value="params.buildid" @change="pickerChange" label="所属楼盘" :range="all_build" name="buildid"></my-select> -->
    <view class="build_info" @click="toSelectHouseType">
      <text class="label">选择户型</text>
      <text class="value" :class="{noval: !params.houseid}">{{params.houseid?houseTypeName:'请选择户型'}}</text>
    </view>
  </view>
  <view class="tab-list flex-row bottom-line">
    <view class="tab-item" :class="{active: type==1}" @click="switchTab(1)">图文</view>
    <view class="tab-item" :class="{active: type==2}" @click="switchTab(2)">视频</view>
    <view class="tab-item" :class="{active: type==3}" @click="switchTab(3)">语音</view>
    <view class="tab-item" :class="{active: type==4}" @click="switchTab(4)" v-if="is_adviser == 1">房源</view>
  </view>
  <view class="block top-20" v-if="type!=3 && type!=4">
    <view class="block-title">分享内容</view>
    <textarea :value="params.content" placeholder="请输入分享内容" maxlength="640" @input="inputContent" />
    <view class="tip flex-box" style="padding-bottom:48rpx">
      <my-icon type="tishifu" color="#999"></my-icon>
      <text class="text">禁止同一条内容多次发布，严重者将封禁处理</text>
    </view>
  </view>
  <view class="block top-20" v-if="type!=3 && type!=4">
    <my-select :value="params.cateid" @change="pickerChange" label="选择标签" :range="cate_list" name="cateid"></my-select>
  </view>
    <!-- #ifndef MP-BAIDU -->
  <view class="block top-20" v-show="type!=3 && type!=4">
    <view class="block-title">{{type===1?'上传图片':'上传视频'}}</view>
    <view class="upload-box" v-if="showUpload">
      <my-upload
        :action="type===2?'building_circle/uploadFile':'house/uploadFileByWx'"
        @uploadDon="uploadDon"
        :imgs="imgList"
        :maxCount="type===1?3:1"
        :chooseType="type===1?1:4"
        :videos="videoList"
      ></my-upload>
      <view class="tip flex-box">
        <my-icon type="tishifu" color="#999"></my-icon>
        <text class="text">请勿上传含有第三方二维码、网址、水印等违规内容</text>
      </view>
    </view>
    <!-- #endif -->
    <!-- #ifdef MP-BAIDU -->
    <view class="upload-box" v-if="showUpload" v-show="showUp">
      <view class="block-title">{{type===1?'上传图片':'上传视频'}}</view>
      <my-upload
        action="building_circle/uploadFile"
        @uploadDon="uploadDon"
        :imgs="imgList"
        :maxCount="tyepe===1?3:1"
        :chooseType="type===1?1:4"
        :videos="videoList"
      ></my-upload>
    </view>
    <!-- #endif -->
  </view>
  <view class="voice-box top-20" v-show="type==3">
    <view class="voice_row" v-if="voiceUrl">
      <view class="voice felx-1" @click="playVoice(voiceUrl)">
        <image class="play_vioce_icon" :src="voice_playing?'/static/icon/voice/play_voice.gif':'/static/icon/voice/voice_icon.png'"></image>
        <text>{{parseInt(voice_duration/1000)}}''</text>
      </view>
      <view class="close-box" @click="handleVoice">
        <my-icon type="ic_guanbi" color="#333" size="50rpx"></my-icon>
      </view>
    </view>
    <view v-else class="label">‘长按讲话’</view>
    <view class="btn-box">
      <view class="btn-box2">
        <recorder ref="recorder" @recorded="uploadVoice" @recordering="is_recordering=true" @recordered="is_recordering=false" :recorder_max_time="30000" :use_wx_voice="use_wx_voice">
          <view class="btn" :class="{animation: is_recordering}">
            <my-icon type="luyin" color="#fff" size="68rpx"></my-icon>
          </view>
        </recorder>
      </view>
    </view>
  </view>
  <template v-if="type == 4">
    <view class="block top-20">
      <my-select v-if="showBuildSelect" :value="building_params.building_id" @change="buildingChange" label="所属楼栋" :range="building_list" name="building_id" :show_icon="false" unit="号楼"></my-select>
      <my-input ref="buildInput" v-else label="所属楼栋" placeholder="请输入楼栋" v-model="building_params.building" unit="号楼"></my-input>
    </view>
    <view class="block">
      <my-input label="所属单元" placeholder="请输入单元" v-model="building_params.building_unit" unit="单元"></my-input>
    </view>
    <view class="block">
      <my-input label="所在楼层" placeholder="请输入所在楼层" type="number" v-model="building_params.building_floor" unit="层"></my-input>
    </view>
    <view class="block">
      <my-input label="总楼层" placeholder="请输入总楼层" type="number" v-model="building_params.building_total_floor" unit="层"></my-input>
    </view>
    <view class="block">
      <my-input label="房号" placeholder="请输入房号" type="number" v-model="building_params.house_name" unit="室"></my-input>
    </view>
    <view class="block">
      <my-input label="售价" placeholder="请输入售价" type="number" v-model="building_params.house_price" unit="元/m²"></my-input>
    </view>
    <view class="block">
      <my-select :value="building_params.house_sale_status" @change="buildingChange" label="状态" :range="sale_status_list" name="house_sale_status" :show_icon="false"></my-select>
    </view>
  </template>
  <view class="btn-box">
    <button class="default" @click="subData">发表分享</button>
  </view>
  <my-popup ref="options_popup">
    <view class="options_box">
      <view class="label">请选择</view>
      <view class="search_box">
        <search @input="onInputKey" placeholder="请输入关键字检索"></search>
      </view>
      <scroll-view scroll-y class="options">
        <view class="options_item bottom-line" v-for="(item, index) in build_list" :key="index" @click="onSelectBuild(item)">
          <text class="text">{{item.title}}</text>
          <my-icon v-if="params.buildid === item.id" type="wancheng" color="#ff656b"></my-icon>
        </view>
      </scroll-view>
    </view>
  </my-popup>
</view>
</template>

<script>
import myInput from '../../components/form/newInput.vue'
import mySelect from '../../components/form/mySelect.vue'
import myUpload from '../../components/form/myUpload.vue'
import myCheckbox from '../../components/form/myCheckbox.vue'
import myIcon from '../../components/myIcon.vue'
import myPopup from '@/components/myPopup'
import search from '@/components/search'
import recorder from '../../components/recorder.vue'
const innerAudioContext = uni.createInnerAudioContext();
export default {
  components: {
    myInput,
    myUpload,
    myCheckbox,
    mySelect,
    myIcon,
    myPopup,
    search,
    recorder
  },
  data() {
    return {
      params: {
        content: "",
        img: "",
        video: "",
        buildid: "",
        houseid: "",
        duration: ""
      },
      type: 1,
      buildName: "",
      buildid: "",
      houseTypeName: "",
      showUpload: false,
      showUp: false,
      imgList: [],
      videoList: [],
      voice_playing: false,
      voiceUrl: "",
      voice_duration: 0,
      all_build: [],
      build_list: [],
      houseTypeList: [],
      cate_list: [],
      is_recordering: false,
      use_wx_voice: true, //是否使用微信jsapi上传
      is_adviser: 0,
      showBuildSelect: true,
      building_list: [],
      building_params: {
        building: '',
        building_id: '',
        building_unit: '',
        building_floor: '',
        building_total_floor: '',
        house_name: '',
        house_price: '',
        house_sale_status: ''
      },
      sale_status_list: [
        {name: '待售', value: 1},
        {name: '在售', value: 2},
      ]
    }
  },
  onLoad(options) {
    if (options.buildid) {
      this.buildid = options.buildid
    }  
    console.log(this.buildid)
    this.getData()
   
    // this.api = "adviser/release.html"
    this.api = "building_circle/release"
    this.showUpload = true
    // #ifdef MP-BAIDU
    // 百度小程序如果刚开始没有图片上传第一张不会显示,所以默认添加一张空的图片，然后在上传组件在删除掉这个空的图片
    this.imgList = ['']
    this.videoList = ['']
    // #endif
    // 监听语音播放
    innerAudioContext.onPlay(()=>{
        this.voice_playing = true
    })
    // 监听语音播放停止
    innerAudioContext.onStop(()=>{
        this.voice_playing = false
    })
    // 监听语音自然播放结束事件
    innerAudioContext.onEnded(()=>{
        this.voice_playing = false
    })
    // 监听语音播放失败事件
    innerAudioContext.onError(()=>{
      if(this.wx&&this.voiceLocalId){
        this.localIdPlaying = true
        this.wx.playVoice({
          localId: this.voiceLocalId // 需要播放的音频的本地ID，由stopRecord接口获得
        });
        this.voice_playing = true
        this.wx.onVoicePlayEnd({
          success: (res)=> {
            this.localIdPlaying = false
            this.voice_playing = false
          }
        });
      }else{
        uni.showToast({
            title:'播放失败，请重试',
            icon:'none'
        })
        this.voice_playing = false
      }
    })
  },
  methods: {
    handleInput(e) {
      this.params[e._name] = e.detail.value
    },
    inputContent(e) {
      this.params.content = e.detail.value
    },
    getData() {
      this.$ajax.get("building_circle/release", {
        buildid: this.buildid
      }, (res) => {
        if (res.data.code == 1) {
          this.all_build = res.data.builds
          this.build_list = Array.from(this.all_build)
          this.is_adviser = res.data.is_adviser
        }
        if (this.all_build.length > 0) {
          if(this.buildid){
            this.buildName = this.all_build[0].title
            this.params.buildid = this.all_build[0].id
            this.getSandBuilding()
          }
        }
        this.cate_list = res.data.cates.map(item=>{
          return {
            name: item.title,
            value: item.id
          }
        })
      })
    },
    showBuildList(){
      this.$refs.options_popup.show()
    },
    onInputKey(e){
      this.build_list = this.all_build.filter(item=>item.title.includes(e.detail.value))
    },
    onSelectBuild(e){
      console.log(this.params.buildid, e.id)
      if (this.params.buildid != e.id) {
        this.params.houseid = ''
      }
      this.buildName = e.title
      this.params.buildid = e.id
      this.$refs.options_popup.hide()
      this.getSandBuilding()
    },
    toSelectHouseType(){
      if(!this.params.buildid){
        uni.showToast({
          title: "请先选择楼盘",
          icon: "none"
        })
        return
      }
      uni.$once('select_house_type', (e)=>{
        this.houseTypeName = e.desc
        this.params.houseid = e.hid
      })
      this.$navigateTo(`/pages/new_house/house_type_list?bid=${this.params.buildid}&list_type=select`)
    },
    switchTab(type){
      // #ifdef H5
      if(type === 3 && !this.recorderPermission){
        this.$refs.recorder.checkRecorderPermission(()=>{
          this.recorderPermission = true
          this.type = type
        })
        return
      }
      // #endif
      if(type === 2){
        this.imgList = []
        this.params.img = ""
      }
      if(type === 1){
        this.videoList = []
        this.params.video = ""
      }
      this.type = type
    },
    uploadDon(e) {
      if (e.type == "image") {
        this.imgList = e.files
        this.params.img = e.files.join(',')
      }
      if (e.type == "video") {
        this.videoList = e.files
        this.params.video = e.files.join(',')
      }
      // #ifdef MP-BAIDU
      this.showUp = true
      // #endif
    },
    pickerChange(e) {
      if(e._name === 'buildid'){
        // this.getHouseType(e.value)
        this.params.buildid = e.value
      }
      this.params[e._name] = e.value
    },
    subData() {
      if (this.type!=3&&this.type!=4&&!this.params.content) {
        uni.showToast({
          title: "请输入分享内容",
          icon: "none"
        })
        return
      }
      //限制上传图片数量
      if(this.type==1&&this.imgList.length>3) {
          uni.showToast({
            title: "最多只能上传3张图片",
            icon: "none"
          })
          return
      }
      if(this.type==3&&this.params.voice=='') {
          uni.showToast({
            title: "请上传语音",
            icon: "none"
          })
          return
      }
      //限制上传视频数量
      if(this.type==2&&this.videoList.length>1) {
          uni.showToast({
            title: "最多只能上传1个视频文件",
            icon: "none"
          })
          return
      }
      uni.showLoading({
        title: "发布中",
        mask: true
      })
      let params
      if (this.type == 4) {
        this.building_params.type = 5
        this.building_params.buildid = this.params.buildid
        this.building_params.houseid = this.params.houseid
        params = this.building_params
      } else {
        params = this.params
      }
      if (this.type!=3) {
        delete params.voice
        delete params.duration
      }
      this.$ajax.post(this.api, params, res => {
        uni.hideLoading()
        if (res.data.code == 1) {
          setTimeout(() => {
            uni.$emit('getDataAgain')
            this.$navigateBack()
          }, 1500)
        }
        uni.showToast({
          title: res.data.msg,
          icon: (res.data.code == 1 && res.data.msg.length<=5)? 'succsee' : 'none',
          mask: true
        })
      })
    },
    // 上传录音文件并发送
    uploadVoice(e){
        if(e.duration < 1000){
            uni.showToast({
                title: "录制时间太短",
                icon: 'none'
            })
            // recorder.destroy()
            return
        }
        if(this.use_wx_voice){
          this.wx = e.wx
          this.voiceLocalId = e.localId
          this.wx.uploadVoice({
            localId: e.localId, // 需要上传的音频的本地ID，由stopRecord接口获得
            isShowProgressTips: 1, // 默认为1，显示进度提示
            success: (res)=> {
              var serverId = res.serverId; // 返回音频的服务器端ID
              console.log(serverId)
              uploadServerId(serverId, e.duration)
              // this.sendMessage({voice: serverId, duration}, 'voice')
            }
          });
          // 将serverId传递给后台，后台进行语音的下载和转码
          var uploadServerId = (serverId, duration)=>{
            uni.showLoading({
                title:"正在上传"
            })
            this.$ajax.get('UploadQiniu/downLoadVoice.html',{media_id:serverId, duration:Math.ceil(duration/1000)},res=>{
              uni.hideLoading()
              if(res.data.code === 1){
                console.log(res.data.url)
                this.voiceUrl = res.data.url
                this.voice_duration = duration
                this.params.voice = res.data.url
                this.params.duration = duration
              }else{
                uni.showToast({
                    title: res.data.msg,
                    icon: 'none'
                })
              }
            },err=>{
              uni.hideLoading()
            })
          }
          return
        }
        // 获取上传后的录音文件路径，然后执行发送消息
        uni.showLoading({
            title: "正在上传"
        })
        this.$uploadFile('UploadQiniu/uploadVoice.html', e.file, {duration:Math.ceil(e.duration/1000)}, (res)=>{
            uni.hideLoading()
            let result
            // #ifdef MP-BAIDU
            result = res.data
            // #endif
            // #ifndef MP-BAIDU
            result = JSON.parse(res.data)
            // #endif
            if(!result.url){
                uni.showToast({
                    title:result.msg||"上传失败",
                    icon:"none"
                })
                return
            }
            this.voiceUrl = result.url
            this.voice_duration = e.duration
            this.params.voice = result.url
            this.params.duration = e.duration
        },err=>{
            uni.hideLoading()
            // recorder.destroy()
        })
    },
    // 播放语音
    playVoice(source){
      console.log("播放", source)
      if(this.wx&&this.voiceLocalId&&this.localIdPlaying){
        this.localIdPlaying = false
        this.voice_playing = false
        this.wx.stopVoice({
          localId: voiceLocalId // 需要停止的音频的本地ID，由stopRecord接口获得
        });
        return
      }
      if(this.voice_playing){
        innerAudioContext.stop()
        return
      }
      this.voice_playing = true
      innerAudioContext.src = source
      innerAudioContext.play()
    },
    handleVoice(){
      this.voice_playing = false
      this.voiceUrl = ""
      this.localIdPlaying = false
      this.voiceLocalId = null
      this.params.voice =''
      this.params.duration = ''
    },
    getSandBuilding(){
      this.$ajax.get("building_circle/getSandBuilding", {
        buildid: this.params.buildid
      }, (res) => {
        if (res.data.code == 1) {
          this.building_params.building_id = ''
          this.building_params.building = ''
          this.showBuildSelect = true
          this.building_list = res.data.list.map(item=>{
            return {
              name: item.number,
              value: item.id
            }
          })
          this.building_list.push({
            name: '没找到？填写楼栋',
            value: 0
          })
        }
      })
    },
    buildingChange(e){
      console.log(e)
      if(e._name == 'building_id' && e.value == 0) {
        this.showBuildSelect = false
      } else {
        this.building_params[e._name] = e.value
      }
    }
  }
}
</script>

<style lang="scss">
.block{
  padding: 0 48rpx;
  background-color: #fff;
}	
.block-title {
  padding: $uni-spacing-col-lg 0;
  background-color: #fff;
  color: #666;
  font-size: 22rpx;
}
.build_info{
  padding: 24rpx 0;
  .label{
    margin-right: 48rpx;
    color: #666;
  }
  .noval{
    color: #999;
  }
}

.tab-list {
  padding: 0 48rpx;
  display: flex;
  justify-content: space-between;
  position: sticky;
  background-color: #fff;
  z-index: 2;
  .tab-item {
    flex: 1;
    padding: 24rpx;
    text-align: center;
    position: relative;
    color: #666;
    &.active {
      color: $uni-color-primary;
      &::after {
        content: '';
        height: 8rpx;
        border-radius: 4rpx;
        background-color: $uni-color-primary;
        position: absolute;
        bottom: 0;
        width: 48rpx;
        left: 0;
        right: 0;
        margin: auto;
      }
    }
  }
}

.textarea-row {
  background-color: #fff;
}

textarea {
  padding: 10upx 0;
  width: 100%;
  box-sizing: border-box;
  padding: 16rpx;
  border-radius: 8rpx;
  background-color: #f8f8f8;
}

.upload-box {
  background-color: #fff;
}

.list {
  background-color: #fff;
}

.set-nickname-box input {
  padding: 10upx;
  border: 1upx solid #f3f3f3;
  border-radius: 5upx;
}
.btn-box{
  padding: 24rpx 48rpx;
}
.tip{
  align-items: center;
  padding: 24rpx 0;
  font-size: 24rpx;
  color: #999;
  .text{
    margin-left: 16rpx;
  }
}
@keyframes rotate {
  0% {
    box-shadow: 0 0 1rpx 32rpx rgba($color: $uni-color-primary, $alpha: 0.06);
  }
  50% {
    box-shadow: 0 0 1rpx 84rpx rgba($color: $uni-color-primary, $alpha: 0.15);
  }
  100% {
    box-shadow: 0 0 1rpx 136rpx rgba($color: $uni-color-primary, $alpha: 0.3);
  }
}
.voice-box{
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 48rpx 120rpx 48rpx;
  background-color: #fff;
  position: relative;
  .label{
    padding: 24rpx 24rpx 80rpx 24rpx;
    font-size: 36rpx;
    color: #666;
  }
  .btn-box{
    padding: 64rpx;
    border-radius: 50%;
    border: 1rpx solid #eee;
  }
  .btn-box2{
    padding: 64rpx;
    border-radius: 50%;
    border: 1rpx solid #dedede;
  }
  .btn{
    width: 128rpx;
    height: 128rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: $uni-color-primary;
    box-shadow: 0 8rpx 32rpx 0 rgba($color: $uni-color-primary, $alpha: 0.4);
    &.animation{
      animation: rotate 1.2s linear infinite;
    }
  }
}

// 录音中
.voiced{
  width: 240rpx;
  padding: 24rpx;
  text-align: center;
  box-sizing: border-box;
  height: 240rpx;
  position: absolute;
  z-index: -1;
  left: 0;
  right:0;
  top: 80rpx;
  margin: auto;
  border-radius: 20rpx;
  background-color: rgba($color: #000000, $alpha: 0.5);
  opacity: 0;
  transition: 0.26s;
  &.show{
    opacity: 1;
    z-index: 10;
  }
  .tip{
    height: 30rpx;
    line-height: 30rpx;
    text-align: center;
    font-size: 22rpx;
    color: #fff;
  }
  .voice_img{
    width: 140rpx;
  }
}
.voice_row{
  width: 100%;
  padding-bottom: 72rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.voice{
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  border-radius: 12rpx;
  width: 100%;
  margin-right: 64rpx;
  box-sizing: border-box;
  background-color: $uni-color-primary;
  color: #fff;
  .play_vioce_icon{
    width: 40rpx;
    height: 40rpx;
  }
}

.select_box{
  padding: 24rpx 0;
  .elsect_row{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .label{
    font-size: 22rpx;
    margin-bottom: 24rpx;
    color: #666;
  }
  .value{
    font-size: 36rpx;
    &.noval{
      color: #999;
    }
  }
}

.options_box{
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  overflow: hidden;
  >.label{
    padding: 24rpx;
    text-align: center;
    font-size: 32rpx;
    background-color: #f8f8f8;
    color: #666;
  }
}

.options{
  padding: 0 48rpx;
  height: 50vh;
  overflow-x: hidden;
  box-sizing: border-box;
  .options_item{
    padding: 24rpx 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .text{
      margin-right: 24rpx;
      line-height: 1.5;
      display: block;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 30rpx;
      color: #333;
    }
  }
}
</style>
