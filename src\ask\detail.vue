<template>
  <view class="page">
    <view class="top">
      <view class="top-question padding-lr-48 bg-fff">
        <view class="title-icon question-icon">问</view>
        <view class="question-span">
          <view class="question-content">{{ detailInfo.question }}</view>
          <view class="question-info">
            <view>{{ detailInfo.cname }}</view>
            <view>{{ detailInfo.update_time }}</view>
          </view>
        </view>
      </view>
      <!-- 广告位 -->
      <view class="swiper-container" v-if="adv.length > 0">
        <view class="swiper-con">
          <view class="swiper">
            <my-swiper
              :focus="adv"
              :autoplay="true"
              :interval="4000"
              :indicatorDots="adv.length > 1"
              :circular="true"
              indicatorActiveColor="#ffffff"
              height="140rpx"
            ></my-swiper>
          </view>
        </view>
      </view>
      <view class="answer-number" v-if="detailInfo.answer_list && detailInfo.answer_list.length > 0"
        >共有{{ detailInfo.answer_list.length }}个回答</view
      >
      <view v-if="detailInfo.answer_list && detailInfo.answer_list.length === 0" class="no_qa padding-lr-48">
        <view class="title-icon answer-icon">答</view>
        <image class="no_qa-img" :src="no_qaSrc |imageFilter('m_320')" mode="widthFix"></image>
        <view class="no_qa-content">暂无回复答案</view>
        <view class="no_qa-btn" @click="$navigateTo(`/ask/answer?id=${id}&question=${detailInfo.question}`)">
          <my-icon type="ic_pinglun" color="#fff" size="26"></my-icon>
          <text>我来回答</text>
        </view>
        <view class="line"></view>
      </view>
      <view class="top-answer padding-lr-48 bg-fff" v-for="(item, index) in detailInfo.answer_list" :key="index">
        <view class="answer-info">
          <view class="answer-author">
            <image :src="item.prelogo | imageFilter('w_240')" class="author-icon"></image>
            <view class="author-info">
              <view class="answer-name">{{ item.answer_cname }}</view>
              <view v-if="item.position === 1">购房咨询师</view>
              <view v-if="item.position === 2">置业顾问</view>
              <view v-if="item.position === 3">经纪人</view>
            </view>
          </view>
          <view class="answer-contact" v-if="item.position">
            <view class="wei" @click="handleChat(item)">微聊</view>
            <view class="phone" @click="handleTel(item)">电话咨询</view>
          </view>
        </view>
        <view class="answer-content">
          <view class="answer-text">
            <text>{{ item.answer_content }}</text>
          </view>
          <view class="answer-bottom">
            <view class="answer-date">{{ item.utime }}</view>
            <image
              v-if="item.set_best"
              class="best-icon"
              :src="bestIconSrc | imageFilter('m_240')"
              mode="widthFix"
            ></image>
            <view class="answer-num" @click.stop="praise(item)">
              <my-icon type="ic_zan" :color="!item.is_praise ? '#cccccc' : '#F65354'" size="28rpx"></my-icon>
              <text>有用({{ item.praise_count }})</text>
            </view>
          </view>
        </view>
        <view class="line"></view>
      </view>
    </view>
    <view class="center padding-lr-48 bg-fff">
      <view class="center-house" v-if="detailInfo.bid">
        <view class="title-text">此问题相关楼盘</view>
        <view class="house-info" @click="$navigateTo('/pages/new_house/detail?id=' + detailInfo.bid)">
          <view class="house-image"><image class="house-image" :src="detailInfo.pico | imageFilter('w_240')" mode="aspectFill"></image></view>
          <view class="house-right">
            <view class="house-title">
              <view class="house-name">{{ detailInfo.title }}</view>
              <view class="house-state" :class="'state' + detailInfo.bs_id">{{ detailInfo.leixing }}</view>
            </view>
            <view class="house-label">
              <view v-for="(label, index) in detailInfo.build_type" :key="index">{{ label }}</view>
            </view>

            <view class="house-address"></view>
            <view class="house-price"
              >{{ detailInfo.build_price }}<text>{{detailInfo.price_unit}}</text></view
            >
          </view>
        </view>
        <view class="btn_list-box">
          <view class="btn-item" @click="toSubForme(1)">
            <my-icon type="tongzhi" color="#ff656b" size="42rpx"></my-icon>
            <text>开盘通知我</text>
          </view>
          <view class="btn-item flex-row" @click="toSubForme(2)">
            <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon>
            <text>降价通知我</text>
          </view>
        </view>
      </view>
      <view class="center-more" v-if="other_question_list.length">
        <view class="title-text">你可能还想了解</view>
        <view
          class="more-box"
          v-for="(item, index) in other_question_list"
          :key="index"
          @click="$navigateTo('/ask/detail?id=' + item.id)"
        >
          <view class="title-icon">问</view>
          <view class="more-right">
            <view class="more-question">{{ item.question }}</view>
            <view class="more-answer" v-if="item.other_answer_list[0]">{{
              item.other_answer_list[0].answer_content
            }}</view>
            <view class="more-answer" v-else>暂无回答</view>
            <view class="line"></view>
          </view>
        </view>
      </view>
    </view>
    <view class="other">
      <view class="line"></view>
      <view class="bottom padding-lr-48 bg-fff">
        <view class="share" @click="showSharePop">
          <my-icon type="ic_fenxiang" color="#222222" size="50rpx"></my-icon>
          <view>分享</view>
        </view>
        <view class="bottom-left" @click="showConsult()">我要提问</view>
        <view class="bottom-right" @click="$navigateTo(`/ask/answer?id=${id}&question=${detailInfo.question}`)"
          >写回答</view
        >
      </view>
    </view>
    <my-popup ref="consult" position="center" height="auto" :touch_hide="true" @click="stopMove">
      <question-popup :bid="id" :isLogin="isLogin" @closePopup="closeConsult"></question-popup>
    </my-popup>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <share-pop
      ref="show_share_pop"
      @copyLink="copyLink"
      @appShare="appShare"
      :showHaibao="false"
      @showCopywriting="showCopywriting"
    ></share-pop>
    <!-- 报名弹窗 -->
    <sub-form
      :groupCount="detailInfo.groupCount"
      :sub_type="sub_type"
      :sub_mode="sub_mode"
      ref="sub_form"
      @onsubmit="handleSubForm"
      :login_status="login_status"
    ></sub-form>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import myIcon from '../components/myIcon.vue'
import { getSceneParams } from '../common/index.js'
import { config } from '@/common/config.js'
import allTel from '../common/all_tel.js'
import getChatInfo from '../common/get_chat_info'
import sharePop from '../components/sharePop.vue'
import myPopup from '../components/myPopup.vue'
import checkLogin from '../common/utils/check_login'
import copyText from '../common/utils/copy_text'
import shareTip from '../components/shareTip'
import mySwiper from '../components/mySwiper.vue'
import questionPopup from '../components/questionPopup'
import subForm from '../components/subForm'
export default {
  components: {
    myIcon,
    getSceneParams,
    sharePop,
    shareTip,
    myPopup,
    mySwiper,
    questionPopup,
    subForm,
  },
  data() {
    return {
      id: '',
      detailInfo: {},
      other_question_list: [],
      popupTopSrc: config.imgDomain + '/images/background/ask_popup.png',
      bestIconSrc: config.imgDomain + '/images/new_icon/bast_answer.png',
      no_qaSrc: config.imgDomain + '/images/new_icon/no_qa.png',
      isLogin: false,
      addQuestion: '',
      addPhoto: '',
      show_share_tip: false,
      cname: '',
      adv: [],
      sub_type: 0,
      shareUserInfo: {},
      tel_res: {},
      show_tel_pop:false
    }
  },
  computed: {
    is_open_im() {
      return this.$store.state.im.ischat
    },
    sub_mode() {
      return this.$store.state.sub_form_mode
    },
    login_status() {
      return this.$store.state.user_login_status
    },
  },
  onLoad(options) {
    this.id = options.id
    this.$ajax.get('buildQuestion/questionDetail', { id: options.id }, (res) => {
      if (res.data.code) {
        this.detailInfo = res.data.data
        this.adv = res.data.adv
        this.detailInfo.answer_list.forEach((item, index) => {
          if (item.set_best === 1) {
            this.detailInfo.answer_list.unshift(this.detailInfo.answer_list.splice(index, 1)[0])
          }
        })
        this.other_question_list = res.data.other_question_list
        this.share = {
          title: res.data.share.title || `${this.detailInfo.question}`,
          content: res.data.share.content ? res.data.share.content : '',
          pic: res.data.share.pic ? res.data.share.pic : this.popupTopSrc,
          link: this.getShareLink(),
        }
        this.getWxConfig();
        if (res.data.share_user) {
          this.shareUserInfo = res.data.share_user
        }
        document.title = this.detailInfo.question
      }
    })
    this.loginState()
    uni.$on('getDataAgain', () => {
      this.loginState()
    })
  },
  onUnload() {
    uni.$off('getDataAgain', () => {})
  },
  methods: {
    loginState() {
      checkLogin({
        success: (res) => {
          this.isLogin = true
        },
        fail: (res) => {
          this.isLogin = false
        },
        complete: (res) => {
          this.$store.state.user_login_status = res.status
        },
      })
    },
    showSharePop() {
      this.$refs.show_share_pop.show()
    },
    showCopywriting() {
      let link = ''
      // #ifdef H5
      link = window.location.href
      // #endif
      // #ifndef H5
      link = config.apiDomain + (this.id ? '/ask/detail?id=' + this.id : '/ask/detail')
      // #endif
      const text = `【我正在看】${this.detailInfo.question}
【阅读原文】${link}`
      copyText(text, () => {
        uni.showToast({
          title: '复制成功,去发送给好友吧',
          icon: 'none',
        })
      })
    },
    showConsult() {
      this.$ajax.get('buildQuestion/isTouristMode', {}, (res) => {
        // 1游客模式
        if (res.data.data !== '1' && this.login_status ==1) {
          this.$navigateTo('/user/login/login')
        } else {
          this.$refs.consult.show()
        }
      })
    },
    closeConsult() {
      this.$refs.consult.hide()
    },
    handleChat(user) {
      // 判断站点是否开启聊天功能
      if (this.is_open_im == 0) {
        // 购房咨询师 跳转 置业顾问详情(购房咨询师按照置业顾问处理)
        // 如果是置业顾问
        if (user.position < 3) {
          this.$navigateTo('/pages/consultant/detail?id=' + user.adviser)
        } else {
          // 如果是经纪人
          this.$navigateTo('/pages/agent/detail?id=' + user.answer_uid)
        }
        return
      }
      getChatInfo(user.answer_uid, 30, this.detailInfo.bid || '')
    },
    handleTel(user) {
      this.tel_params = {
        type: user.position < 3 ? 2 : 3,
        callee_id: user.position < 3 ? user.adviser_id : user.answer_uid,
        scene_type:1,
        scene_id: this.detailInfo.bid,
        success:(res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      allTel(this.tel_params)
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    praise(answer) {
      this.$store.state.allowOpen = true
      this.$ajax.post('buildQuestion/answerPraise', { answer_id: answer.answer_id }, (res) => {
        if (res.data.msg === '点赞成功') {
          answer.praise_count += 1
          answer.is_praise = 1
        } else if (res.data.msg === '取消点赞成功') {
          answer.praise_count -= 1
          answer.is_praise = 0
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    copyLink() {
      this.show_share_tip = true;
      // this.showCopywriting()
    },
    // 获取分享链接
    getShareLink() {
      let link = ""
      // #ifdef H5
      link = window.location.href
      // #endif
      // #ifndef H5
      link = config.apiDomain + '/ask/detail?id=' + this.id
      // #endif
      return link
    },
    // 楼盘通知
    toSubForme(type, operation) {
      this.sub_operation = operation || ''
      this.sub_type = type
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e) {
      //提交报名
      e.from = '问答详情'
      e.bid = this.detailInfo.bid
      e.type = this.sub_type || ''
      if (this.sub_operation) e.operation = this.sub_operation
      if (this.shareUserInfo && this.shareUserInfo.adviser_id) {
        // 如果是置业顾问分享的
        e.share_uid = this.shareUserInfo.adviser_id
        e.is_adviser = 1
      } else if (this.shareUserInfo && this.shareUserInfo.agent_id) {
        // 如果是经纪人分享的
        e.share_uid = this.shareUserInfo.agent_id
        e.is_adviser = 2
      }
      this.$ajax.post('build/signUp.html', e, (res) => {
        uni.hideLoading()
        if (res.data.code === 1) {
          // 没开启引导登录模式或已经绑定手机号了
          if (this.sub_mode !== 2 || res.data.status === 3) {
            //提示报名成功
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
            this.$refs.sub_form.closeSub()
          } else {
            this.$refs.sub_form.getVerify()
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.padding-lr-48 {
  padding: 0 48rpx;
}
.bg-fff {
  background: #ffffff;
}
.title-text {
  font-size: 40rpx;
  color: #333333;
  font-weight: bold;
}
.title-icon {
  width: 48rpx;
  height: 48rpx;
  line-height: 48rpx;
  text-align: center;
  border-radius: 16rpx 0 16rpx 0;
  color: #ffffff;
  font-size: 22rpx;
}
.line {
  height: 1rpx;
  transform: scaleY(0.5);
  background: #d8d8d8;
}
.top {
  // padding: 40rpx 0 64rpx 0;
  .top-question {
    display: flex;
    padding-top: 40rpx;
    padding-bottom: 40rpx;
    .question-icon {
      background-image: linear-gradient(90deg, #ffa857 0%, #ff6069 100%);
    }
    .question-span {
      flex: 1;
      font-family: PingFangSC-Medium;
      padding-left: 20rpx;
      .question-content {
        font-size: 32rpx;
        color: #333333;
        letter-spacing: 0;
        text-align: justify;
        line-height: 48rpx;
        font-weight: bold;
      }
    }
    .question-info {
      display: flex;
      justify-content: space-between;
      padding-top: 20rpx;
      font-size: 22rpx;
      color: #999999;
      view {
        font-size: 22rpx;
      }
    }
  }
  .swiper-container {
    background: #f8f8f8;
    .swiper-con {
      padding: 32rpx 48rpx 0 48rpx;
    }
    .swiper {
      border-radius: 16rpx;
      overflow: hidden;
    }
  }
  .answer-number {
    padding-left: 48rpx;
    background: #f8f8f8;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 28rpx;
    color: #666666;
  }
  .top-answer {
    // display: flex;
    padding-top: 49rpx;
    .answer-info {
      width: 100%;
      display: flex;
      justify-content: space-between;
      color: #999999;
      padding-bottom: 24rpx;
      .answer-author {
        display: flex;
        font-size: 22rpx;
        .author-icon {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
        }
        .author-info {
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding-left: 20rpx;
          color: #999999;
          view {
            font-size: 22rpx;
          }
          .answer-name {
            color: #333333;
            padding-bottom: 8rpx;
          }
        }
      }
      .answer-contact {
        display: flex;
        align-items: center;
        color: #fb656a;
        view {
          border: 0.5rpx solid #fa8a8e;
          box-shadow: 0 4rpx 8rpx 0 rgba(251, 101, 106, 0.1);
          border-radius: 8rpx;
          font-size: 28rpx;
          height: 64rpx;
          line-height: 64rpx;
          text-align: center;
        }
        .wei {
          width: 128rpx;
        }
        .phone {
          width: 144rpx;
          margin-left: 24rpx;
        }
      }
    }
    .answer-icon {
      background: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
    }
    .answer-content {
      flex: 1;
      .best-icon {
        width: 96rpx;
      }
      .answer-text {
        color: #666666;
        line-height: 42rpx;
        font-size: 28rpx;
      }
      .answer-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 0;
        // border-bottom: 1rpx solid #d8d8d8;
      }
      .answer-date {
        font-size: 22rpx;
        color: #999999;
      }
      .answer-num {
        text-align: right;
        display: flex;
        font-size: 22rpx;
        align-items: center;
        justify-content: flex-end;
        text {
          color: #999999;
          padding-left: 10rpx;
        }
      }
    }
  }
}
.center {
  padding-top: 49rpx;
  padding-bottom: 41rpx;
  margin-bottom: 112rpx;
}
.center-house {
  padding-bottom: 64rpx;
  .house-info {
    display: flex;
    padding-top: 40rpx;
    .house-image {
      width: 240rpx;
      height: 176rpx;
    }
    .house-right {
      width: 374rpx;
      padding-left: 42rpx;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      view {
        font-size: 22rpx;
        color: #999999;
      }
      .house-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;
        .house-name {
          font-size: 32rpx;
          color: #333333;
        }
        .house-state {
          padding: 0 6rpx;
          height: 32rpx;
          text-align: center;
          line-height: 32rpx;
          border-radius: 4rpx;
          color: #ffffff;
          font-size: 22rpx;
          &.state1 {
            color: #fff;
            background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
          }
          &.state2 {
            color: #fff;
            background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
          }
          &.state3 {
            color: #fff;
            background: linear-gradient(to right, #ff8533 0%, #ff7213 100%);
          }
          &.state4 {
            color: #fff;
            background: linear-gradient(to right, #ccc 0%, #ccc 100%);
          }
        }
      }
      .house-address {
        margin-top: 12rpx;
        margin-bottom: 12rpx;
      }
      .house-label {
        width: 100%;
        overflow: hidden;
        display: flex;
        box-sizing: border-box;
        view {
          height: 32rpx;
          line-height: 32rpx;
          text-align: center;
          padding: 0 8rpx;
          border: 1rpx solid #d8d8d8;
          border-radius: 2px;
          margin-right: 16rpx;
          flex-shrink: 0;
        }
      }
    }
  }
}
.center-more {
  .more-box {
    display: flex;
    padding-top: 40rpx;
    .title-icon {
      background: linear-gradient(90deg, #ffa857 0%, #ff6069 100%);
    }
    .more-right {
      flex: 1;
      margin-left: 20rpx;
      view {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }
      .more-question {
        font-size: 32rpx;
        color: #333333;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        font-weight: bold;
      }
      .more-answer {
        padding-top: 20rpx;
        margin-bottom: 40rpx;
        // border-bottom: 1rpx solid #d8d8d8;
        line-height: 42rpx;
        font-size: 28rpx;
        color: #666666;
        -webkit-line-clamp: 2;
      }
    }
    // &:last-child .more-right {
    //   border-bottom: none;
    // }
  }
}
.other {
  width: 100%;
  height: 112rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  .line {
    margin-left: 0;
  }
}
.bottom {
  box-sizing: border-box;
  width: 100%;
  background: rgba(255, 255, 255, 0.98);
  display: flex;
  justify-content: space-between;
  padding: 0 48rpx;
  height: 112rpx;
  align-items: center;
  .share {
    display: flex;
    align-items: center;
    view {
      margin-left: 20rpx;
    }
  }
  .bottom-left {
    width: 280rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 32rpx;
    color: #ffffff;
    border-radius: 40rpx;
    background: linear-gradient(90deg, #ffa857 0%, #ff6069 100%);
    box-shadow: 0 6rpx 12rpx 0 rgba(255, 109, 102, 0.3);
  }
  .bottom-right {
    width: 180rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 40rpx;
    font-size: 32rpx;
    color: #ffffff;
    background: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
    box-shadow: 0 6rpx 12rpx 0 rgba(38, 113, 255, 0.3);
  }
}
.no_qa {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  box-sizing: border-box;
  padding: 40rpx 0;
  margin-top: 40rpx;
  margin-bottom: 40rpx;
  .no_qa-img {
    width: 350rpx;
  }
  .no_qa-content {
    font-size: 26rpx;
    color: #999;
  }
  .no_qa-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 200rpx;
    height: 50rpx;
    margin-top: 20rpx;
    color: #fff;
    font-size: 26rpx;
    background: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
    border-radius: 40rpx;
    text-align: center;
    text {
      margin-left: 5rpx;
    }
  }
  .answer-icon {
    background: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
    position: absolute;
    top: 40rpx;
    left: 48rpx;
  }
}
// 报名按钮
.btn_list-box {
  display: flex;
  margin-top: 32rpx;
  .btn-item {
    padding: 20rpx 5rpx;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.05);
    color: $uni-color-primary;
    ~ .btn-item {
      margin-left: 14rpx;
    }
    text {
      font-size: 32rpx;
      margin-left: 16rpx;
    }
  }
}
</style>