<template>
<view class="order_detail">
    <view class="card">
        <view class="title qr_code_title bottom-line">{{order_info.title}}</view>
        <view class="card_content text-center">
            <image class="img" :src="order_info.write_off_path | imgUrl('w_640')" mode="aspectFit"></image>
             <view class="tip" v-if="order_info.write_off==1">已核销</view>
            <view class="tip" v-else>将此二维码展示给商家核销</view>
            <view v-if="order_info.pay_status==0" class="btn highlight" @click="toPay()">去支付</view>
            <view v-if="order_info.pay_status==1&&order_info.write_off==0" class="btn" @click="handleRefund()">申请退款</view>
        </view>
    </view>
    <view class="card">
         <view class="title bottom-line">
             <text>核销码</text>
             <text class="sub_title">点击核销码可复制</text>
         </view>
         <view class="card_content" @click="handleCopy()">
             <text class="verification_code">{{order_info.write_off_number}}</text>
         </view>
    </view>
    <view class="activity flex-box" v-if="coupon&&coupon.id">
        <view class="act_left">
            <view class="title"><text class="label">￥</text>{{coupon.money}}<text class="isuse" v-if="coupon.is_use">已使用</text></view>
            <view class="sub_title">{{coupon.name}}</view>
            <view class="time-box">
                <text>{{coupon.start_time | timestampToTime}}</text>-<text>{{coupon.end_time | timestampToTime}}</text>
            </view>
        </view>
        <!-- <view class="act_right">
           <text class="canyu">立即参与</text>
        </view> -->
    </view>
    <view class="card">
        <view class="title bottom-line">
             <text>订单总价</text>
             <text class="price">￥{{order_info.total_money}}</text>
         </view>
         <view class="card_content">
             <view class="list_item">姓名：{{order_info.uname}}</view>
             <view class="list_item">支付时间：{{order_info.pay_time}}</view>
             <view class="list_item">订单号：{{order_info.order_sn}}</view>
             <!-- 后台说没有有效期功能 -->
             <!-- <view class="list_item">有效期：{{order_info.uname}}</view> -->
         </view>
    </view>
    <view class="card">
         <view class="title bottom-line">
             <text>项目信息：</text>
         </view>
         <view class="card_content">
            <view class="business_name">{{business.business_name}}</view>
            <view class="business_info flex-box">
                 <view class="info_list flex-1">
                    <!-- <view class="list_item">项目名称：{{business.business_name}}</view> -->
                    <view class="list_item">接待时间：{{business.business_time}}</view>
                    <view class="list_item">咨询电话：{{business.business_tel}}</view>
                    <view class="list_item">接待中心：{{business.business_address}}</view>
                 </view>
                 <view>
                     <view class="handler">
                         <text>　</text>
                     </view>
                     <view class="handler" @click="toTel()" v-if='business.business_tel'>
                         <text class="c-btn">拨号</text>
                     </view>
                     <view class="handler" v-if='business.business_tx_location' @click="toAddress()">
                         <text class="c-btn">导航</text>
                     </view>
                 </view>
            </view>
         </view>
    </view>
    <view class="card">
        <view class="title bottom-line">
             <text>注意事项：</text>
             <view class="card_content">
                 <view class="article-content" v-html="business.attention"></view>
             </view>
         </view>
    </view>
    <my-dialog ref="dialog" :show="show_dialog" @confirmButton="applyRefund" @close="show_dialog = false" title="请填写退款原因">
        <view class="text-box">
            <!-- #ifdef MP -->
            <textarea v-if="show_dialog" fixed maxlength="120" placeholder="请输入退款原因" v-model="refund_desc"></textarea>
            <!-- #endif -->
            <!-- #ifndef MP -->
            <textarea fixed maxlength="120" placeholder="请输入退款原因" v-model="refund_desc"></textarea>
            <!-- #endif -->
        </view>
    </my-dialog>
</view>
</template>

<script>
import myDialog from "../components/dialog.vue"
import {
    navigateTo,
    formatImg,
    isIos,
} from '../common/index.js'
import wx from "weixin-js-sdk"
export default {
    data() {
        return {
            order_info:{
                total_money:'',
                uname:'',
                pay_time:'',
                order_sn:''
            },
            business:{
                business_time:'',
                business_tel:'',
                business_address:''
            },
            coupon:{},
            // coupon:{
            //     id:1,
            //     money:"10000",
            //     name:"满90万减10000",
            //     start_time:1583716909000,
            //     end_time:1583716909000,
            //     is_use:1,
            // },
            show_dialog:false,
            refund_desc:"",//退款原因
        }
    },
    onLoad(options){
        if(options.id){
            this.order_id = options.id
            this.getData()
        }
    },
    components: {
        myDialog
    },
    filters:{
        imgUrl(val,param){
            return formatImg(val,param)
        },
        timestampToTime(timestamp) {
            var date = new Date(timestamp*1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
            var Y = date.getFullYear() + '.';
            var M = (date.getMonth()+1 < 10 ? date.getMonth()+1 : date.getMonth()+1) + '.';
            var D = date.getDate() + ' ';
            var h = date.getHours() + ':';
            var m = date.getMinutes() + ':';
            var s = date.getSeconds();
            return Y+M+D+h+m+s;
        },
    },
    methods: {
        getData(){
            this.$ajax.get('online/orderDetail',{order_id:this.order_id},res=>{
                if(res.data.code == 1){
                    this.order_info = res.data.order_info
                    this.business = res.data.business
                    if(res.data.coupon){
                        this.coupon = res.data.coupon
                    }
                }else{
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                    if(getCurrentPages().length>1){
                        setTimeout(()=>{
                            uni.navigateBack()
                        },1500)
                    }
                }
                this.wxInit()
            })
        },
        wxInit(){
            let url;
            if(isIos()){
                url = this.$store.state.firstUrl
            }else{
                url = window.location.href
            }
            this.$ajax.get('/wechat/index/signature.html',{url:url},res=>{
                if(res.data.code == 1){
                    res.data.config.jsApiList = ['openLocation']
                    wx.config(res.data.config)
                }
            })
        },
        handleCopy(){
            // #ifndef H5
            uni.setClipboardData({
                data: this.order_info.write_off_number+'',
                success: res => {
                // uni.showToast({
                //   title: "复制成功",
                //   icon: "none"
                // })
                }
                
            })
            // #endif
            // #ifdef H5
            let oInput = document.createElement('input');
            oInput.value = this.order_info.write_off_number;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            document.execCommand("Copy"); // 执行浏览器复制命令
            uni.showToast({
                title: "复制成功"
            })
            oInput.remove()
            // #endif
        },
        toTel(){
            if(!this.business.business_tel){
                uni.showToast({
                    title:'该项目未绑定电话',
                    icon:'none'
                })
                return
            }
            uni.makePhoneCall({
                phoneNumber:this.business.business_tel
            })
        },
        toAddress(){
            if (!this.business.business_tx_location) return 
            let point = this.business.business_tx_location.split(',')
            wx.ready(()=>{
                wx.openLocation({
                    latitude:  parseFloat(point[0]), // 纬度，浮点数，范围为90 ~ -90
                    longitude: parseFloat(point[1]), // 经度，浮点数，范围为180 ~ -180。
                    name: this.business.business_name, // 位置名
                    address: this.business.business_address, // 地址详情说明
                    scale: 16, // 地图缩放级别,整形值,范围从1~28。默认为最大
                });
            });
            // uni.openLocation({
            //     name:this.business.business_name,
            //     address:this.business.business_address,
            //     latitude: parseFloat(point[0]),
            //     longitude: parseFloat(point[1]),
            //     success: function () {
            //         console.log('success');
            //     }
            // });
        },
        /** 
         * <AUTHOR> 
         * @date 2020-02-28 14:30:28 
         * @desc 点击申请退款 
         */
        handleRefund(){
            this.show_dialog = true
        },
        applyRefund(){
            this.$ajax.get('online/refundOrder',{order_id:this.order_id,refund_desc:this.refund_desc},res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title:res.data.msg
                    })
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            })
        },
        toPay(){
            navigateTo(`/online/pay?order_id=${this.order_id}`)
        },
    },
}
</script>

<style scoped lang="scss">
.card{
    margin: 24rpx;
    padding: 26rpx;
    border-radius: 10rpx;
    background-color: #fff;
    box-shadow: 0 0 10px #dedede;
    .title{
        padding: 26rpx 0;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        &.qr_code_title{
            font-weight: initial;
            font-size: 32rpx;
            padding-bottom: 40rpx;
        }
        .sub_title{
            margin-left: 25rpx;
            font-size: 25rpx;
            font-weight: initial;
            color: #666;
        }
        .price{
            float: right;
            font-weight: bold;
            color: #f00;
        }
    }
    .card_content{
        margin-top: 20rpx;
        .article-content{
            padding: 0;
        }
        .img{
            margin-top: 40rpx;
            width: 350rpx;
            height: 350rpx;
        }
        .tip{
            color: #555;
        }
        .btn{
            margin-top: 20rpx;
            display: inline-block;
            width: 150rpx;
            height: 50rpx;
            line-height: 50rpx;
            border: 1rpx solid #dedede;
            border-radius: 25rpx;
            font-size: 26rpx;
            color: #555;
            &.highlight{
                color: $uni-color-primary;
                border-color: $uni-color-primary;
            }
        }
        .verification_code{
            display: inline-block;
            padding-bottom: 20rpx;
            font-size: 36rpx;
            font-weight: bold;
        }
        .list_item{
            padding: 5rpx;
            line-height: 50rpx;
            // height: 50rpx;
            // line-height: 50rpx;
        }
        .handler{
            padding: 10rpx;
            text{
                display: inline-block;
                // height: 40rpx;
                // line-height: 40rpx;
                padding: 5rpx 15rpx;
                border-radius: 5rpx;
                // background-color: #f8a306;
                color: #fff;
            }
            .c-btn{
                background-color: #f8a306;
            }
        }
    }
}

.business_name{
    margin-bottom: 12rpx;
}

.text-box{
    padding: 20upx;
    textarea{
        text-align: left;
        width: 100%;
        height: 150upx;
        border: 1upx solid #f3f3f3;
        padding: 10upx;
        box-sizing: border-box;
    }
}

.activity{
    padding: 20upx 40rpx;
    margin:10upx 24upx;
    border-radius: 10upx;
    background: linear-gradient(to right,#ee1140, #fd7128);
    display: flex;
    justify-content: space-between;
    align-items: center;
    .act_left{
        flex: 1;
    }
    .title{
        font-size: 42rpx;
        font-weight: 500;
        color:#fff;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        .label{
            font-size: 28rpx;
            margin-right: 5rpx;
        }
        .isuse{
            font-size: 28rpx;
            margin-left: 10rpx;
        }
    }
    .sub_title{
        font-size: 28upx;
        color: #fff;
        padding: 10upx 0upx;
        width: 98%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .time-box{
        font-size: 26rpx;
        color: #fff;
    }
    .act_right {
        flex :1;
        text-align: center;
    }
    .canyu {
        padding: 10upx 30upx;
        border-radius: 40upx;
        background: #fff;
        color:#f4283a;
    }

}
</style>
