<template>
  <view>
    <view class="consult top">
      <view class="top-image">
        <image :src="popupTopSrc | imageFilter('m_240')" mode="widthFix"></image>
        <view class="top-font">
          <view class="top-title">买房，<br />从问问开始</view>
        </view>
      </view>
      <view class="consult-center">
        <textarea
          class="consult-textarea"
          placeholder="您想咨询的问题（必填）"
          placeholder-style="color:#999;letter-spacing:2rpx"
          v-model="addQuestion"
        ></textarea>
        <input
          class="consult-input"
          placeholder="您的手机号码（必填）"
          placeholder-style="color:#999"
          v-model="addTel"
        />
        <view class="consult-btn" @click="submitQuestion">我要提问</view>
      </view>
      <view class="consult-close" @click="closePopup">
        <my-icon type="ic_guanbi" color="#ffffff"></my-icon>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from '../components/myIcon.vue'
import { config } from '@/common/config.js'
export default {
  components: {
    myIcon,
  },
  data() {
    return {
      popupTopSrc: config.imgDomain + '/images/background/ask_popup.png',
      addQuestion: '',
      addTel: '',
    }
  },
  props: {
    bid: {
      type: String,
      default: '',
    },
    isLogin: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    submitQuestion() {
      if (!this.addQuestion) {
        uni.showToast({
          title: '请填写提问内容',
          icon: 'none',
        })
        return false
      }
      if (this.addTel.length !== 11 || this.addTel[0] != 1) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none',
        })
        return false
      }
      uni.showLoading({
        title: '提交中',
        mask: true
      });
      this.$ajax.post(
        'buildQuestion/ask',
        { content: this.addQuestion, tel: this.addTel, bid: this.bid },
        (res) => {
          uni.hideLoading();
          if (res.data.code === 1) {
            uni.showToast({
              title: '提交成功',
              icon: 'none',
            })
            this.closePopup();
            this.addQuestion = '';
            this.addTel = '';
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
          }
        }
      )
    },
    closePopup() {
      this.$emit('closePopup');
    }
  },
}
</script>

<style lang="scss" scoped>
.top {
  position: relative;
  image {
    width: 100%;
  }
  .top-font {
    position: absolute;
    top: 64rpx;
    left: 80rpx;
    .top-title {
      font-size: 48rpx;
      color: #ffffff;
      letter-spacing: 3rpx;
      line-height: 64rpx;
    }
    .top-describe {
      padding-top: 24rpx;
      font-size: 22rpx;
      color: #eeeeee;
      letter-spacing: 0;
    }
  }
}
.consult {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80vw;
  background: #ffffff;
  margin: 0 auto;
  border-radius: 20rpx;
  .top-image {
    border-radius: 20rpx 20rpx 0 0;
    overflow: hidden;
  }
  .top-font {
    top: 36rpx;
    left: 78rpx;
  }
  .top-title {
    font-size: 40rpx !important;
  }
  .consult-center {
    background: #ffffff;
    padding: 48rpx;
    border-radius: 0 0 20rpx 20rpx;
    .consult-textarea {
      box-sizing: border-box;
      background: #f2f2f2;
      border-radius: 10rpx;
      height: 200rpx;
      padding: 28rpx 24rpx;
      width: auto;
      font-size: 30rpx;
    }
    .consult-input {
      margin-top: 30rpx;
      background: #f2f2f2;
      border-radius: 10rpx;
      padding: 28rpx 24rpx;
      width: auto;
      font-size: 30rpx;
    }
    .consult-btn {
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      color: #ffffff;
      border-radius: 10rpx;
      margin-top: 48rpx;
      background: linear-gradient(90deg, #ffa857 0%, #ff6069 100%);
      font-size: 30rpx;
    }
  }
}
.consult-close {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
}
</style>