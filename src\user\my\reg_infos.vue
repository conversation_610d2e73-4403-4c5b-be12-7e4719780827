<template>
  <view class="regInfos" v-html="content" >
      
  </view>
</template>

<script>
export default {
  data(){
    return {
      content:""
    }
  },
  onLoad(){
    this.getInfo()
  },
  methods: {
    getInfo(){
      this.$ajax.get('member/faq', {}, (res) => {
        if (res.data.code ==1){
          this.content = res.data.data.content
        }
      })
    }
  },
}
</script>

<style>
  .regInfos{
    padding: 5upx 20upx;
    background: #fff;
  }
</style>