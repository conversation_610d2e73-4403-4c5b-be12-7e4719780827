<template>
	<view class="list">
		<view class="lists">
			<block v-for="(item,i) in lists" :key="i">
				<view class="list-item bottom-line" @click="goTo(i,item)">
					<view class="img-box list-img">
						<image :src="item.img | imgURL" lazy-load mode="widthFix"></image>
						<view class="status" :class='{"ended":item.status==2}'>{{item.status==2?'已结束':'正在报名'}}</view>
					</view>
					<view class="info-title">{{item.title}}</view>
					<!-- <view class="preferential">
						{{item.discount}}
					</view> -->
					<view class="top-10">
						<text class="time">活动时间:{{item.during}}</text>
						<text class="author c-right">已报名:{{item.count}}人</text>
					</view>
				</view>
			</block>
			<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
		</view>
		<chat-tip></chat-tip>
	</view>
</template>

<script>
	import {uniLoadMore} from '@dcloudio/uni-ui'
	import {formatImg,navigateTo,isIos} from "../../common/index.js"
	import {wxShare} from '../../common/mixin'
	export default {
		data() {
			return {
				params:{
					page:1,
					rows:20
				},
				lists:[],
				get_status:"loading",
				content_text:{
					contentdown:"",
					contentrefresh:"正在加载...",
					contentnomore:"没有更多数据了"
				},
				bullet_list:[],
				swiperCurrent:0,
			};
		},
		mixins:[wxShare],
		components:{
			// banner,
			uniLoadMore
		},
		onLoad(){
			uni.showLoading({
				title:"加载中..."
			})
			this.getData()
		},
		filters:{
			imgURL(img){
				return formatImg(img)
			}
		},
		methods:{
			getData(){
				this.get_status = "loading"
				this.$ajax.get("build/groupList.html",this.params,(res)=>{
					uni.setNavigationBarTitle({
						title: res.data.navTitle || '看房团'
					})
					if(res.data.code==1){
						this.lists = this.lists.concat(res.data.list)
						this.bullet_list=res.data.groups
						if(res.data.list.length<this.params.rows){
							this.get_status = "noMore"
						}else{
							this.get_status = "more"
						}
					}else{
						this.get_status = "noMore"
					}
					if(res.data.share){
						this.share = res.data.share
					}else{
						this.share = {}
					}
					this.getWxConfig()
					setTimeout(()=>{
						uni.hideLoading()
					},260)
				})
			},
			goTo(index,item){
				this.$store.state.tempData = this.lists[index]
        if (item.is_discount) {
          this.$navigateTo('/pages/groups/detail?id='+item.id)
        } else {
          this.$navigateTo('/pages/groups/detail?id='+item.id)
        }
			}
		},
		onReachBottom(){
			this.params.page = this.params.page+1
			this.getData()
		}
	}
</script>

<style lang="scss" scoped>
	.list{
		box-sizing: border-box;
		background: #fff;
		// padding: 0 48upx;
	}
	.list-item{
		margin-bottom: 20upx;
		padding: 30upx 0;
		background: #fff;
	}
	.lists{
		
		padding: 0 48upx;

	}
	.list-img{
		width: 100%;
		height: auto;
		padding: 10upx 0;
		box-sizing: border-box;
		overflow: hidden;
		position: relative;
		max-height: 368px;
	}
	.list-img image{
		border-radius: 6upx;
		width: 100%;
		height: 100%
	}
	.status{
		position: absolute;
		top: 28upx;
		left: 28upx;
		padding: 10upx 30upx;
		border-radius: 30upx;
		background-image: linear-gradient(to right, rgba(251, 172, 101, 1),rgba(251, 101, 106, 1));
		// border-top-left-radius: 10upx;
		// background-color: rgba($color: #000000, $alpha: 0.5);
		color: #fff
	}
	.status.ended{
		background-image: linear-gradient(to right, rgba(160, 171, 218, 1),rgba(122, 152, 231, 1));
	}
	.info-title{
		font-size: $uni-font-size-blg;
		max-height: 72upx;
		line-height: 38upx;
		font-weight: bold;
		overflow: hidden;
		text-overflow: ellipsis;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		display: -webkit-box;
		margin: 10upx 0;
		color: #333;
	}
	.preferential{
		font-size: $uni-font-size-base;
		font-weight: bold;
		color: #f65354
	}
	.author,.time{
		margin-right: 10upx;
		font-size: $uni-font-size-sm;
		color: #666;
	}
	.top-10{
		margin-top: 10upx
	}
	.bullet-box {
		margin:10px 5px;
	}
	.bullet-box .banners{
		height:40upx;
	}
	.bullet-box .banners .con{
		padding:0 40upx;
	}
</style>
