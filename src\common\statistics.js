import store from '../store/index'
import {ajax} from '../common/index'
var _hmt = _hmt || [];

const statistics = function() {
    let path = window.location
    let isershoupath = false
    if (path.pathname == "/m/pages/ershou/detail" || path.pathname == "/h5/pages/ershou/detail" || path.pathname == "/m/pages/ershou/ershou" || path.pathname == "/h5/pages/ershou/ershou") {
        isershoupath = true
    } else {
        isershoupath = false
    }
    // if(store.state.statistics){
    //     handle(store.state.statistics)
    // } else {
    //     ajax.get('index/setting', {}, res => {
    //         if (store.state.statistics) {
    //             handle(store.state.statistics)
    //             store.state.statistics = res.data.statistics
    //         }
    //     })
    // }
    if (store.state.codekefu || store.state.ershou_statistics || store.state.statistics) {
      if (store.state.statistics) {
        handle(store.state.statistics)
      }
      if (isershoupath && store.state.ershou_statistics) {
        handle(store.state.ershou_statistics, "53kefu")
        return
      }
      if (store.state.codekefu) {
        handle(store.state.codekefu,"53kefu")
      }
      
    } else {
      ajax.get('index/setting', {}, res => {
        if (res.data['code53kf'] || res.data.code53kf_esf || res.data.statistics) {
          if (res.data.statistics) {
            handle(res.data.statistics)
            store.state.statistics = res.data.statistics
          }

          if (isershoupath && res.data.code53kf_esf) {
            handle(res.data.code53kf_esf, "53kefu_esf")
            store.state.ershou_statistics = res.data.code53kf_esf
            return
          }
          if (res.data['code53kf']) {
            handle(res.data['code53kf'], "53kefu")
            store.state.codekefu = res.data['code53kf']
          }
          
        }
      })
    }
    
}
const handle = function (statistics, id = "baidu_tj") {
    console.log(statistics);
    // console.log("执行统计")
    // return
    //每次执行前，先移除上次插入的代码
    if (window.id_creared_53app) {
      delete(window.id_creared_53app)
    }
     
    document.getElementById(id) && document.getElementById(id).remove();
    var hm = document.createElement("script");
    hm.src = statistics;
    hm.id = id
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(hm, s);
}
module.exports={
    statistics
}