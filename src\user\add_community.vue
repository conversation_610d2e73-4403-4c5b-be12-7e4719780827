<template>
  <view>
    <!-- 图片 -->
    <view class="block">
      <view class="title flex-row">
        <text>小区封面</text>
      </view>
      <view class="upload-box">
        <my-upload @uploadDon="uploadDon1" :chooseType="1" :maxCount="1" :imgs="params.cover_image"></my-upload>
      </view>
    </view>
    <view class="block">
      <view class="title flex-row">
        <text>小区相册</text>
        <text class="tip">最多上传{{ image_max }}张{{ params.images.length }}/{{ image_max }}</text>
      </view>
      <view class="upload-box">
        <my-upload @uploadDon="uploadDon" :chooseType="1" :maxCount="image_max" :imgs="params.images"></my-upload>
      </view>
    </view>
    <view class="block">
      <my-input label="小区名称" v-model="params.name" placeholder="请输入"></my-input>
      <view class="select_btn flex-row" @click="showAreaPopup">
        <view class="left">
          <text class="label">区域</text>
          <text class="value" :class="{ has_value: area_name }">{{ area_name || '请选择' }}</text>
        </view>
        <view>
          <my-icon type="ic_into" size="36rpx" color="#999"></my-icon>
        </view>
      </view>
      <my-popup ref="area_popup">
        <addressPicker :data_list="area_list" @onselect="onAreaChange"></addressPicker>
      </my-popup>
    </view>
    <view class="block">
      <view @click="chooseLocation">
        <my-input label="小区地址" disabled v-model="params.address" placeholder="请选择小区地址"></my-input>
      </view>
    </view>
    <view class="btn-box">
      <view class="btn" @click="handleAdd">确认添加</view>
    </view>
  </view>
</template>

<script>
import myUpload from '../components/form/myUpload'
import myInput from '../components/form/newInput.vue'
import myIcon from '../components/myIcon.vue'
import myPopup from '../components/myPopup.vue'
import addressPicker from '../components/addressPicker.vue'
export default {
  components: {
    myUpload,
    myInput,
    myIcon,
    myPopup,
    addressPicker
  },
  data() {
    return {
      image_max: 6,
      params: {
        name: '',
        cover_image: [],
        images: [],
        address: '',
        lat: '',
        lng: ''
      },
      area_name: '',
      area_list: []
    }
  },
  onLoad(){
    this.getAreaList()
  },
  methods: {
    uploadDon1(e) {
      this.params.cover_image = e.files
    },
    uploadDon(e) {
      this.params.images = e.files
    },
    showAreaPopup() {
      this.$refs.area_popup.show()
    },
    getAreaList() {
      this.$ajax.get('Release/getArea', {}, res => {
        if (res.data.code === 1) {
          this.area_list = res.data.data
        }
      })
    },
    onAreaChange(e) {
      this.area_name = e[e.length-1].name
      this.params.areaid = e[e.length-1].value
      this.$refs.area_popup.hide()
    },
    chooseLocation() {
      // #ifdef APP-PLUS
      uni.chooseLocation({
        keyword: '',
        success: res => {
          this.params.address = res.address
          this.params.lat = parseFloat(res.latitude)
          this.params.lng = parseFloat(res.longitude)
        }
      })
      // #endif
      // #ifndef APP-PLUS
      uni.chooseLocation({
        keyword: '',
        success: res => {
          this.params.address = res.address
          this.params.lat = res.latitude
          this.params.lng = res.longitude
        }
      })
      // #endif
    },
    handleAdd(){
      if(!this.params.areaid){
        uni.showToast({
          title:"请选择区域",
          icon:'none'
        })
        return
      }
      if(!this.params.lng||!this.params.lat||!this.params.address){
        uni.showToast({
          title:"请选择小区地址",
          icon:'none'
        })
        return
      }
      uni.showLoading({
        title: '添加中...',
        icon: 'none'
      })
      let params = Object.assign({}, this.params)
      params.images = params.images.join(',')
      params.cover_image = params.cover_image.join(',')
      this.$ajax.post('Release/addCommunity', params, res=>{
        uni.hideLoading()
        if(res.data.code !== 1){
          uni.showToast({
            title: res.data.msg,
            icon:'none'
          })
          return
        }
        uni.showToast({
          title: res.data.msg,
          mask: true
        })
        setTimeout(()=>{
          uni.$emit('newCommunity', {name: this.params.name, id:res.data.community_id})
          this.$navigateBack(2)
        }, 1500)
      }, err=>{
        uni.hideLoading()
      })
    }
  }
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.block {
  margin-bottom: 20rpx;
  padding: 0 48rpx;
  background-color: #fff;
  input {
    color: #333;
  }
  .title {
    justify-content: space-between;
    align-items: center;
    font-size: 36rpx;
    margin-bottom: 24rpx;
  }
  .tip {
    font-size: 22rpx;
    color: #999;
  }
}

.select_btn {
  padding: 24rpx 0;
  justify-content: space-between;
  align-items: center;
  .label {
    margin-bottom: 24rpx;
    font-size: 24rpx;
    color: #666;
  }
  .value {
    font-size: 36rpx;
    color: #999;
    &.has_value {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
}


.btn-box {
  padding: 48rpx;
  margin-top: 120rpx;
  .btn {
    height: 88rpx;
    line-height: 88rpx;
    background: #fb656a;
    box-shadow: 0 8rpx 32rpx 0 rgba(251, 101, 106, 0.4);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    text-align: center;
  }
}
</style>
