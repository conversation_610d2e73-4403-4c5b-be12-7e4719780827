<template>
  <view class="page" :class="{pdb_120: (sharers_info.adviser_id&&is_open_adviser)||sharers_info.agent_id}">
    <view class="header" :style="{paddingTop:systemInfo.statusBarHeight+44+'px',backgroundImage:`url(${header_bg})`}">
      <!-- #ifdef MP -->
      <view class="title_bar" :style="{top:systemInfo.statusBarHeight+'px'}">
        <view class="left_box" @click="back()">
          <my-icon type="ic_back" size="50rpx"></my-icon>
        </view>
      </view>
      <!-- #endif -->
      <!-- <view class="header_name">热点资讯</view>
      <view class="sub_title">房产热点 每日更新 最新消息 快人一步</view> -->
    </view>
    <view class="dybtn_box flex-box">
      <view class=" btn flex-box" @click="showSubscribe()">
        <image class="btn_icon" :src="'/images/new_icon/zaobao/dingyue.png' | imageFilter('m_120')"></image>
        <view class="btn_name">订阅</view>
      </view>
      <view class="btn flex-box" @click="$refs.show_share_pop.show()">
        <image class="btn_icon" :src="'/images/new_icon/zaobao/fenxaing.png' | imageFilter('m_120')"></image>
        <view class="btn_name">分享</view>
      </view>
    </view>
    <view class="descp_box" v-if="descp" @click="onCopy()">
      <view class="descp">
        <text>{{descp}}</text>
      </view>
      <view class="copy_line flex-box">
        <view class="copy_btn flex-box">
          <my-icon type="copy" color="#fff" size="24rpx"></my-icon>
          <text>复制</text>
        </view>
      </view>
    </view>
    <!-- 资讯 -->
    <view class="news_box">
      <view class="label_row flex-box">
        <text class="label">{{title}}</text>
        <view class="label_right flex-box" @click="$refs.time_picker.show()">
          <my-icon type="yuyue" size="32rpx" color="#333"></my-icon>
          <text class="text">{{format_ctime}}</text>
        </view>
      </view>
      <view class="news_list" v-if="!no_news">
        <view class="news_item" v-for="(news, index) in news_list" :key="news.id" @click="$navigateTo('/pages/news/detail?id=' + news.id)">
          <view class="point" :class="'point'+(index+1)" v-if="index<3">{{index+1}}</view>
          <view class="point" v-else></view>
          <view class="info">
            <view class="title">{{news.title}}</view>
            <view class="time">{{news.ctime}}</view>
          </view>
          <image v-if="news.img" class="img" mode="aspectFill" :src="news.img | imageFilter('w_240')"></image>
        </view>
      </view>
      <view class="nodata" v-else>
        <image mode="widthFix" :src="'/images/new_icon/zaobao/nodata.png' | imageFilter('m_240')"></image>
        <view class="tip">当日热点暂未发布，稍后在来吧～</view>
      </view>
    </view>
    <!-- 楼盘 -->
    <view class="news_box">
      <view class="label_row flex-box">
        <text class="label">热门楼盘</text>
      </view>
      <view class="build_list" v-if="!no_build">
        <block v-for="item in build_list" :key="item.id">
          <newHouseItem :item-data="item" @click="$navigateTo(`/pages/new_house/detail?id=${item.id}`)"></newHouseItem>
        </block>
      </view>
      <view class="nodata" v-else>
        <image mode="widthFix" :src="'/images/new_icon/zaobao/nodata.png' | imageFilter('m_240')"></image>
        <view class="tip">当日还没有热门楼盘，稍后在来吧～</view>
      </view>
      <view class="more_build" @click="$navigateTo('/pages/new_house/new_house')">
        <text>更多楼盘</text>
      </view>
    </view>
    <template v-if="show_house_nav">
      <!-- 二手房价值榜 -->
      <view class="ershou_rank">
        <view v-for="(item,idx) in ershou_list" :key="idx" @click="valueList(idx)">
          <view class="ershou_tag">
            <image class="tag_icon" :src="require(`@/static/icon/${item.src}.png`)" />
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>
      <view class="service_list flex-row">
        <view class="service_item flex-row red" @click="$navigateTo(item.path)" v-for="(item, index) in house_nav_list" :key="index">
          <view class="info">
            <text class="title">{{item.title}}</text>
            <text class="sub_title">{{item.desc}}</text>
          </view>
          <image class="service_icon" :src="item.icon"></image>
        </view>
      </view>
    </template>

    <!-- 预售 -->
    <view class="news_box" v-if="show_yx&&yushou_data.count" @click="toYushou()">
      <view class="label_row flex-box">
        <text class="label">{{siteCity}}商品房预售证</text>
        <view class="label_right flex-box">
          <my-icon type="ic_into" size="32rpx" color="#333"></my-icon>
        </view>
      </view>
      <view class="data_box flex-box">
        <view class="flex-1 text-center right-line">
          <view class="label">本月发证</view>
          <view>
            <text class="value">{{yushou_data.count}}</text>
            <text class="unit">个</text>
          </view>
        </view>
        <view class="mianji text-center right-line">
          <view class="label">总面积</view>
          <view>
            <text class="value_after">约</text>
            <text class="value">{{yushou_data.area}}</text>
            <text class="unit">m²</text>
          </view>
        </view>
        <view class="flex-1 text-center">
          <view class="label">总套数</view>
          <view>
            <text class="value">{{yushou_data.zzts}}</text>
            <text class="unit">套</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 土地 -->
    <view class="news_box" v-if="show_tudi&&tudi_data.count" @click="toTudi()">
      <view class="label_row flex-box">
        <text class="label">{{siteCity}}土地市场成交数据</text>
        <view class="label_right flex-box">
          <my-icon type="ic_into" size="32rpx" color="#333"></my-icon>
        </view>
      </view>
      <view class="data_box flex-box">
        <view class="flex-1 text-center right-line">
          <view class="label">本年度成交</view>
          <view>
            <text class="value">{{tudi_data.count}}</text>
            <text class="unit">个</text>
          </view>
        </view>
        <view class="flex-1 text-center right-line">
          <view class="label">成交额</view>
          <view>
            <text class="value_after">约</text>
            <text class="value">{{tudi_data.money}}</text>
            <text class="unit">亿</text>
          </view>
        </view>
        <view class="flex-1 text-center">
          <view class="label">总面积</view>
          <view>
            <text class="value_after">约</text>
            <text class="value">{{tudi_data.area}}</text>
            <text class="unit">亩</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 客服电话 -->
    <view class="site_tel" @click="mkPhone">{{siteName}} 客服电话：{{siteTel}}</view>
    <view class="sharers_info flex-box" v-if="(sharers_info.adviser_id&&is_open_adviser)||sharers_info.agent_id">
      <view class="img" @click="toUserIndex()">
        <image :src="sharers_info.prelogo | imageFilter('w_240')" mode="widthFix"></image>
      </view>
      <view class="info flex-1" @click="toUserIndex()">
        <view class="name">{{sharers_info.cname}}</view>
        <view class="identity">{{sharers_info.identity===1?'置业顾问':'经纪人'}}</view>
      </view>
      <view class="btn_box flex-box">
        <view class="btn" @click="handleChat()">微聊</view>
        <view class="btn" @click="handleTel()">电话咨询</view>
      </view>
    </view>
    <myPopup position='center' ref="time_picker" height="840rpx">
      <view style="width: 90%;margin-left: 5%">
        <myCalendar :days="days" :current_time="format_ctime" @select="onSelect"></myCalendar>
      </view>
    </myPopup>
    <!-- 公众号二维码 -->
    <my-popup ref="qrcode_popup" position="top">
      <view class="qrcode-box">
        <!-- #ifdef H5 -->
        <view class="img-box">
          <view class="title titles">每日{{title}}将通过服务号发送</view>
          <view class="tip red">请关注{{siteName}}公众号</view>
          <image @longtap="saveQrcode" class="qrcode" :src="qrcode" mode="aspectFill"></image>
          <view>
            <view class="title">长按保存图片</view>
            <view class="tip">相册选取，识别关注</view>
          </view>
        </view>
        <!-- #endif -->
        <!-- #ifndef H5 -->
        <view class="img-box">
          <view class="title titles">每日{{title}}将通过服务号发送</view>
          <view class="tip red">请关注{{siteName}}公众号</view>
          <image class="qrcode" :src="qrcode" mode="aspectFill"></image>
          <view>
            <view class="tip">长按识别二维码关注公众号</view>
          </view>
        </view>
        <!-- #endif -->
        <view class="icon-box" @click="$refs.qrcode_popup.hide()">
          <my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
        </view>
      </view>
    </my-popup>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <dingyue ref="dingyue" title="订阅更新通知" hangqing_title="每日楼市动态" hangqing_sub_title="新房行情 开盘情报" @dingyue="subscribe" @login="toLogin" 	@bindPhone="toBind" :type="dy_type"></dingyue>
    <!-- #ifdef H5 -->
    <sharePop ref="show_share_pop" @copyLink="show_share_tip = true" @handleCreat="handleCreat" @showCopywriting='showCopywriting'></sharePop>
    <!-- #endif -->
    <!-- #ifndef H5 -->
    <sharePop ref="show_share_pop" @copyLink="copyLink" @handleCreat="handleCreat" @showCopywriting='showCopywriting'></sharePop>
    <!-- #endif -->
    <enturstBtn v-if="sharers_info.agent_id||sharers_info.adviser_id" :to_user="sharers_info" @click="$refs.enturst_popup.show()" />
    <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
      <enturstBox @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="$navigateTo('/user/login/login')" :to_user="sharers_info" />
    </my-popup>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
import myPopup from '../components/myPopup'
import newHouseItem from '../components/newHouseItem'
import myCalendar from './components/myCalendar'
import sharePop from '../components/sharePop'
import shareTip from "../components/shareTip";
import dingyue from "../components/dingyue.vue"
import {formatImg, config} from '../common/index'
import copyText from '../common/utils/copy_text'
import {mapState} from 'vuex'
import getChatInfo from '../common/get_chat_info'
import allTel from '../common/all_tel.js'
import checkLogin from '../common/utils/check_login'
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
var default_top_pic = '/images/new_icon/zaobao/header_bg.png'
export default {
  components: {
    myIcon,
    myPopup,
    sharePop,
    shareTip,
    newHouseItem,
    myCalendar,
    dingyue,
    enturstBtn,
    enturstBox
  },
  data () {
    return {
      title:"楼市报",
      top_pic:'',
      descp: '',
      format_ctime: '',
      news_list: [],
      build_list: [],
      no_news: false,
      no_build: false,
      days: [],
      qrcode: '',
      show_share_tip: false,
      siteCity:'',
      show_tudi: 0, //是否显示土地数据
      show_yx: 0, //是否显示预售数据
      tudi_data:{},
      yushou_data:{},
      sharers_info: {}, // 分享者信息
      current_user_info: {}, // 当前用户的信息
      dy_type: 'dingyue', //订阅组件的type dingyue :订阅 denglu：登录  bangshouji  去绑定手机号
      tel_res: {},
      show_tel_pop: false,
      show_house_nav:0, //是否显示二手房导航
      house_nav_list:[],  //二手房导航列表
      ershou_list:[  //二手房价值榜
					{
						name:"3日飙升热门房源价值榜",
						src:"dy"
					},{
						name:"业主降价急卖好房",
						src:"school"
					},{
						name:"发现好房，高性价比房源",
						src:"re"
					}
				],
    }
  },
  computed:{
    ...mapState(['systemInfo', 'siteName', 'siteTel']),
    header_bg(){
      return `${formatImg(this.top_pic)}`
    },
    is_open_adviser() { //是否开启置业顾问功能
      return this.$store.state.im.adviser
    },
    is_open_im() { // 是否开启聊天功能
      return this.$store.state.im.ischat
    }
  },
  onLoad(options) {
    // 如果是分享链接进来的
      if (options.shareId && (options.type||options.shareType)) {
        this.shareId = options.shareId
        this.shareType = options.type||options.shareType
        this.share_time =options.f_time||''
        checkLogin({
          fail:(res)=>{
            if (res.status==1){
                this.dy_type = 'denglu'
                uni.setStorageSync('backUrl', window.location.href)
            }else if (res.status==2){
								this.dy_type='bangshouji'
						}
            this.$nextTick(()=>{
              this.$refs.dingyue.showPopup()
            })
          },
          complete: (res)=>{
            this.$store.state.user_login_status = res.status
          }
        })
      }
    this.id = options.id||''
    this.getData()
  },
  onUnLoad(){
    uni.$off('getDataAgain')
  },
  methods: {
    getData(time=''){
      uni.showLoading({
        title: "加载中..."
      })
      let params = {}
      if(!time&&this.id){
        params.id = this.id
      }else{
        params.month = time
      }
      if(this.shareId&&this.shareType){
        params.sid = this.shareId
        params.sharetype = this.shareType
      }
      params.forward_time=this.share_time ||''
      this.$ajax.get('news/buildPaper',params, res=>{
        uni.hideLoading()
        this.id = (res.data.data||{}).id
        this.siteCity = res.data.siteCity
        this.top_pic = (res.data.data||{}).top_pic||default_top_pic
        this.show_house_nav = res.data.data.show_house_nav||0
        this.descp = (res.data.data||{}).descp
        this.show_tudi = (res.data.data||{}).show_tudi
        this.show_yx = (res.data.data||{}).show_yx
        this.tudi_data = res.data.tudi
        this.yushou_data = res.data.booking
        if (res.data.nav) {
          this.house_nav_list = res.data.nav
        }
        if(res.data.months){
          this.days = this.formatDay(res.data.months)
        }
        if(res.data.title){
          this.title = res.data.title
          uni.setNavigationBarTitle({
            title: res.data.title
          })
        }
        if (res.data.shareUser) { //当前用户信息
            this.current_user_info = res.data.shareUser
            if(res.data.shareUser.adviser_id){
                this.current_user_info.identity = 1
                this.current_user_info.identity_id = res.data.shareUser.adviser_id
            }else if(res.data.shareUser.agent_id){
                this.current_user_info.identity = 2
                this.current_user_info.identity_id = res.data.shareUser.agent_id
            }
        }
        if (res.data.share_user) { //分享者信息
            this.sharers_info = res.data.share_user
            if(res.data.share_user.adviser_id){
                this.sharers_info.identity = 1
            }else if(res.data.share_user.agent_id){
                this.sharers_info.identity = 2
            }
        }
        this.share = {
          title: (res.data.data||{}).share_title||`${this.siteName}简报`,
          content: (res.data.data||{}).share_content||'',
          pic: (res.data.data||{}).share_pic||`${config.imgDomain}${default_top_pic}`,
          link: this.getShareLink()
        }
        if(!(res.data.data||{}).share_title&&!this.siteName){
          setTimeout(()=>{
            this.share.title = `${this.siteName}简报`
            this.getWxConfig()
          }, 300)
        }else{
          this.getWxConfig()
        }
        this.getShortLink()
        if(res.data.code == 1){
          this.no_news = false
          this.no_build = false
          this.format_ctime = res.data.data.format_ctime
          this.news_list = res.data.data.news
          if(this.news_list.length === 0){
            this.no_news = true
          }
          this.build_list = res.data.data.builds
          if(this.build_list.length === 0){
            this.no_build = true
          }
        }else{
          if(!this.format_ctime){
            let time = new Date()
            let year = time.getFullYear()
            let original_month = time.getMonth()
            let month = original_month+1>=10?original_month+1:'0'+(original_month+1)
            let day = time.getDate()
            this.format_ctime = `${year}-${month}-${day}`
          }
          this.no_news = true
          this.no_build = true
        }
      },err=>{
        uni.hideLoading()
      })
    },
    valueList(idx){
			var type = idx+1
      this.$navigateTo('/statistics/value_list?type='+type)
		},
    // 格式化下接口给的天数据
    formatDay(datas){
      return datas.map(item=>{
        const time = new Date(item.day)
        let year = time.getFullYear()
        let original_month = time.getMonth()
        let month = original_month+1>=10?original_month+1:'0'+(original_month+1)
        let day = time.getDate()
        let week = time.getDay()
        return {
          time: item.day,
          has_data: item.has_data || 0,
          year,
          month,
          day,
          week
        }
      })
    },
    onSelect(e){
      this.getData(e.time)
      this.format_ctime = e.time
      this.$refs.time_picker.hide()
    },
    toYushou(){
      this.$navigateTo(`/pages/yushou/index`)
    },
    toTudi(){
      this.$navigateTo(`/pages/tudi/index`)
    },
    mkPhone(){
      uni.makePhoneCall({
        phoneNumber: this.siteTel
      })
    },
    toLogin(){
      uni.$on('getDataAgain', ()=>{
        checkLogin({
          success:()=>{
            this.$refs.dingyue.hide()
          },fail:(res)=>{
            if (res.status==1){
                this.dy_type = 'denglu'
            }else if (res.status==2){
								this.dy_type='bangshouji'
						}
            this.$refs.dingyue.showPopup()
          },
          complete: (res)=>{
            this.$store.state.user_login_status = res.status
          }
        })
      })
      this.$navigateTo("/user/login/login")
    },
    toBind(){
      uni.$on('getDataAgain', ()=>{
        checkLogin({
          success:()=>{
            this.$refs.dingyue.hide()
          },fail:(res)=>{
            if (res.status==1){
                this.dy_type = 'denglu'
            }else if (res.status==2){
								this.dy_type='bangshouji'
						}
            this.$refs.dingyue.showPopup()
          },
          complete: (res)=>{
            this.$store.state.user_login_status = res.status
          }
        })
      })
      // this.$refs.dingyue.hide()
      this.$navigateTo("/user/bind_phone/bind_phone")
    },
    // 订阅
    showSubscribe(){
      checkLogin({
        success:()=>{
          this.dy_type = 'dingyue'
          this.$refs.dingyue.showPopup()
        },
        fail:()=>{
          if (this.$store.state.user_login_status==1){
              this.dy_type = 'denglu'
              uni.setStorageSync('backUrl', window.location.href)
          }else if (this.$store.state.user_login_status==2){
              this.dy_type='bangshouji'
          }
          this.$refs.dingyue.showPopup()
        },
        complete: (res)=>{
          this.$store.state.user_login_status = res.status
        }
      })
    },
    subscribe(){
      this.$ajax.get('build/subscribeBooking', {type: 3}, res=>{
          this.$refs.dingyue.hide()
          if(res.data.code ==1){
              uni.showToast({
                  title:res.data.msg,
                  icon:"success"
              })
          }else if (res.data.code ==0){  //订阅失败
              uni.showToast({
                  title:res.data.msg,
                  icon:"none"
              })
              if (res.data.gzhewm){
                  this.qrcode=res.data.gzhewm
                  setTimeout(() => {
                      this.$refs.qrcode_popup.show()
                  }, 300);
              }
          }
      })
    },
    // 获取分享链接
    getShareLink(){
        let link = window.location.origin+window.location.pathname
        let time =parseInt(+new Date()/1000)
        if(this.id){
          link = `${link}?id=${this.id}`
        }
        if (this.current_user_info&&this.current_user_info.identity) { //当前用户是 置业顾问或者经纪人
            link = `${link}${this.id?'&':'?'}shareId=${this.current_user_info.identity_id}&type=${this.current_user_info.identity}&f_time=${time}`
        }
        return link
    },
    // 获取短链接
    getShortLink(callback){
      this.link=this.getShareLink()
      this.$ajax.get('build/shortUrl.html', {page_url:this.link }, res=>{
        if(res.data.code === 1){
          this.link = res.data.short_url
        }
        callback&&callback(this.link)
      }, err=>{
        callback&&callback(this.link)
      })
    },
    onCopy(){
      const content = `${this.descp||''}\n【链接】${this.link}`
      copyText(content, ()=>{
          uni.showToast({
          title: '复制成功,去发送给好友吧',
          icon: 'none'
          })
      })
    },
    // 复制分享内容
    showCopywriting(){
        console.log("复制内容")
        const weeks = [
          '日',
          '一',
          '二',
          '三',
          '四',
          '五',
          '六',
        ]
        let y_m_d = this.format_ctime.split('-')
        let week = weeks[new Date(this.format_ctime).getDay()]
        let content = `#${this.siteName}|${this.title||''}#\n【${y_m_d[1]}月${y_m_d[2]}日 星期${week}】\n`
        this.news_list.slice(0, 5).forEach((item, index)=>{
          content += `${index+1}.${item.title}\n`
        })
        content += `更多资讯${this.link}`
        copyText(content, ()=>{
            uni.showToast({
            title: '复制成功,去发送给好友吧',
            icon: 'none'
            })
        })
    },
    handleCreat(){
      // #ifdef H5
      window.open(`/wapi/poster/morningPaper?id=${this.id}&header_from=2`,'_self')
      // #endif
    },
    toUserIndex(){
      if (this.sharers_info.identity == 1) { //置业顾问
          this.$navigateTo('/pages/consultant/detail?id=' + this.sharers_info.adviser_id)
      } else if (this.sharers_info.identity == 2) {
          this.$navigateTo('/pages/agent/detail?id=' + this.sharers_info.agent_id)
      }
    },
    // 发起聊天
    handleChat(){
        if(!this.is_open_im){
            this.toUserIndex()
            return
        }
        getChatInfo(this.sharers_info.mid, 22)
    },
    // 拨打电话
    handleTel(){
      this.tel_params = {
        type: this.sharers_info.identity == 1?'2':'3',
				callee_id: this.sharers_info.mid,
				scene_type:this.sharers_info.identity == 1?'2':'3',
				scene_id:this.sharers_info.mid,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
        allTel(this.tel_params)
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
  }
}
</script>

<style scoped lang="scss">
.page{
  background-color: #fff;
}
.pdb_120{
    padding-bottom: 120rpx;
}
.header{
  height: 400rpx;
  padding: 0 48rpx;
  box-sizing: border-box;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  .header_name{
    margin-top: 100rpx;
    font-size: 80rpx;
    color: #fff;
  }
  .sub_title{
    margin-top: 16rpx;
    font-size: 28rpx;
    color: #fff;
  }
}
.title_bar{
  height: 44px;
  width: 100%;
  line-height: 44px;
  padding: 7px 12px;
  box-sizing: border-box;
  display: flex;
  position: fixed;
  left: 0;
  .left_box,
  .right_box {
    min-width: 27px;
  }
}

.dybtn_box{
  display: flex;
  width: 100%;
  box-sizing: border-box;
  justify-content: space-between;
  margin-top: 24rpx;
  padding: 24rpx 48rpx;
  background-color: #fff;
  .btn{
    width: 42%;
    justify-content: center;
    align-items: center;
    padding: 16rpx;
    border-radius: 16rpx;
    color: #333;
    background-color: #f8f8f8;
    .btn_icon{
      width: 72rpx;
      height: 72rpx;
      margin-right: 16rpx;
    }
  }
}

.descp_box{
  padding: 24rpx 48rpx;
  .descp{
    padding: 24rpx;
    line-height: 1.5;
    background: rgba(251,101,106,0.08);
    border-radius: 16rpx;
    border-radius: 16rpx;
    color: #2a2a2a;
    .label{
      font-weight: bold;
      color: #333;
    }
  }
  .copy_line{
    justify-content: flex-end;
  }
  .copy_btn{
    margin-top: 12rpx;
    width: 84rpx;
    padding: 4rpx;
    align-items: center;
    justify-content: center;
    background-image: linear-gradient(125deg, #FF5500 0%, #FFA402 100%);
    box-shadow: 0 4rpx 12rpx 0 rgba(255,145,1,0.50);
    border-radius: 4rpx;
    border-radius: 4rpx;
    color: #fff;
    font-size: 22rpx;
  }
}
.news_box{
  padding: 24rpx 48rpx;
  margin-top: 24rpx;
}
.label_row{
  justify-content: space-between;
  align-items: center;
  color: #333;
  .label{
    font-size: 40rpx;
    font-weight: bold;
  }
  .label_right{
    align-items: center;
    .text{
      font-size: 26rpx;
      color: #666;
      margin-left: 16rpx;
    }
  }
}
.news_list{
  margin-top: 48rpx;
  .news_item{
    display: flex;
    padding-left: 24rpx;
    padding-bottom: 24rpx;
    position: relative;
    &::before{
      content:'';
      position: absolute;
      width: 2rpx;
      top: 0;
      bottom: 0;
      left: -0.5px;
      background-color: #f5f5f5;
    }
    .point{
      position: absolute;
      width: 18rpx;
      height: 18rpx;
      border-radius: 50%;
      background: #f8666a;
      color: #fff;
      top: 10rpx;
      left: -9rpx;
    }
    .point1{
      width: 28rpx;
      height: 28rpx;
      line-height: 28rpx;
      text-align: center;
      font-size: 20rpx;
      background: #f8b500;
      color: #fff;
      left: -13rpx;
    }
    .point2{
      width: 28rpx;
      height: 28rpx;
      line-height: 28rpx;
      text-align: center;
      font-size: 20rpx;
      background: #c6cdd6;
      color: #fff;
      left: -13rpx;
    }
    .point3{
      width: 28rpx;
      height: 28rpx;
      line-height: 28rpx;
      text-align: center;
      font-size: 20rpx;
      background: #e59077;
      color: #fff;
      left: -13rpx;
    }
    .info{
      flex: 1;
    }
    .img{
      margin-left: 24rpx;
      width: 204rpx;
      height: 152rpx;
      border-radius: 8rpx;
    }
    .title{
      // height: 90rpx;
      margin-bottom: 24rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      font-size: 32rpx;
    }
    .time{
      font-size: 24rpx;
      color: #999;
      margin-bottom: 24rpx;
    }
  }
}

.nodata{
  padding: 48rpx;
  text-align: center;
  image{
    width: 260rpx;
  }
  .tip{
    margin-top: 24rpx;
    font-size: 28rpx;
    color: #999;
  }
}

.more_build{
  padding: 24rpx;
  font-size: 32rpx;
  margin-top: 20rpx;
  background-color: rgba($uni-color-primary, 0.1);
  color: $uni-color-primary;
  text-align: center;
}


.data_box{
  margin-top: 48rpx;
  padding: 32rpx 0;
  border: 1rpx solid #eee;
  box-shadow: 0 0 8rpx 0 rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  >view{
    padding: 0 24rpx;
    &.mianji{
      min-width: 33.33%;
      max-width: 50%;
    }
  }
  .label{
    margin-bottom: 24rpx;
  }
  .value{
    font-size: 36rpx;
    font-weight: bold;
    color: $uni-color-primary;
  }
  .value_after{
    margin-right: 4rpx;
    font-size: 24rpx;
    color: #666;
  }
  .unit{
    margin-left: 4rpx;
    font-size: 24rpx;
    color: #666;
  }
}

.site_tel{
  margin: 48rpx;
  border-radius: 8rpx;
  text-align: center;
  padding: 32rpx;
  font-size: 32rpx;
  color: #fff;
  background-image: linear-gradient(to right, #ff5700, #fd9f01);
  box-shadow: 0 0 12rpx 0 rgba(255, 97, 0, 0.5);
}

// 分享者信息
.sharers_info{
  position: fixed;
  width: 100%;
  height: 120rpx;
  bottom: 0;
  padding: 0 48rpx;
  box-sizing: border-box;
  align-items: center;
  background-color: #fff;
  z-index: 3;
  .img{
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 16rpx;
  }
  image{
    width: 100%;
    height: 100%;
  }
  .info{
    overflow: hidden;
    .name{
      margin-bottom: 16rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .identity{
      font-size: 24rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: #999;
    }
  }
  .btn_box{
    .btn{
      margin-left: 20rpx;
      padding: 10rpx 34rpx;
      font-size: 26rpx;
      color: $uni-color-primary;
      border: 1px solid $uni-color-primary;
      border-radius: 3px;
      box-shadow: 0 2px 4px 0 rgba(251,101,106,.1);
    }
  }
}

//公众号二维码弹框
.qrcode-box{
  position: relative;
  margin-top: 15vh;
  .img-box{
    width: 584rpx;
    padding: 12rpx;
    box-sizing: border-box;
    margin: auto;
    background-color: #fff;
    border-radius: 8rpx;
    overflow: hidden;
    .title{
      font-size: 36rpx;
      font-weight: bold;
      text-align: center;
      color: #333;
      &.titles{
        margin-top: 36rpx;
      }
    }
    .tip{
      padding: 24rpx;
      padding-bottom: 48rpx;
      text-align: center;
      color: #666;
      &.red{
        padding-bottom: 8rpx;
        color: #f00;
      }
    }
  }
  .qrcode{
    width: 560rpx;
    height: 560rpx;
  }
  .icon-box{
    position: absolute;
    bottom: -80rpx;
    width: 52rpx;
    height: 52rpx;
    left: 0;
    right: 0;
    margin: auto;
  }
}

.service_list{
  margin-top: 24rpx;
  padding: 0 48rpx 24rpx 48rpx;
  // margin-bottom: 48rpx;
  flex-wrap: wrap;
  display:flex;
  flex-direction: row;
  justify-content: space-between;
  .service_item{
    width: 278rpx;
    // flex: 1;
    height: 130rpx;
    padding: 18rpx;
    margin-bottom: 24rpx;
    border-radius: 8rpx;
    align-items: center;
    justify-content: space-between;
    border: 1rpx solid #d8d8d8;
    display:flex;
    flex-direction: row;
    .info{
      display: flex;
      flex-direction: column;
    }
    .title{
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
      color: #333;
    }
    .sub_title{
      font-size: 22rpx;
      color: #999;
    }
    .service_icon{
      width: 64rpx;
      height: 64rpx;
    }
  }
}

.ershou_rank{
    padding: 24rpx 48rpx 0 48rpx;
    margin-bottom: 10rpx;
    display: flex;
    flex-wrap: wrap;
    .adviser-swiper{
      height: 50rpx;
        width: 760rpx;
      margin: 0;
    }
    .ershou_tag{
      background: #fff1f5;
      color: #fb656a;
      font-size: 22rpx;
      align-items: center;
      height: 48rpx;
      padding: 0 16rpx 0 8rpx;
      margin-bottom: 24rpx;
      margin-right: 24rpx;
      border-radius: 4rpx;
      font-weight: bold;
      text-align: center;
    }
    .tag_icon {
      position: relative;
      top: 8rpx;
        width: 32rpx;
        height: 32rpx;
        margin-right: 4rpx;
    }
}


</style>