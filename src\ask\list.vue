<template>
  <view class="page">
    <view class="top" v-if="!this.bid">
      <view class="top-img">
        <image :src="topSrc | imageFilter('w_8601')" mode="widthFix"></image>
        <view class="top-font">
          <view class="top-title">买房<br />来这里，问一下</view>
          <view class="top-describe">所有问答均来自客户提问和专业解答</view>
        </view>
      </view>
      <view class="top-search">
        <view @click="handleSearch()" class="search-icon"><my-icon size="48rpx" type="ic_sousuo" color="#999999"></my-icon></view>
        <input
          type="text"
          confirm-type="search"
          v-model="keyword"
          @confirm="handleSearch()"
          placeholder="搜你想知道的问题"
        />
      </view>
    </view>
    <view class="center">
      <view class="center-menu" v-if="!this.bid">
        <tab-bar :tabs="menus" :fixedTop="false" :showLine="false" :nowIndex="menu_index">
          <view
            v-for="(item, index) in menus"
            class="menu-box"
            :class="{ active: item.type_id == currentMenuId }"
            :id="'i' + index"
            :key="index"
            @click="menuChange(item)"
            >{{ item.name }}</view
          >
        </tab-bar>
      </view>
      <view class="btn_list-box" v-if="this.bid">
        <view class="btn-item" @click="toSubForme(1)">
          <my-icon type="tongzhi" color="#ff656b" size="42rpx"></my-icon>
          <text>开盘通知我</text>
        </view>
        <view class="btn-item flex-row" @click="toSubForme(2)">
          <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon>
          <text>降价通知我</text>
        </view>
      </view>
      <view class="center-content">
        <view class="content-box" v-for="(item, index) in questionList" :key="index" @click="toDetail(item.id)">
          <view class="question">
            <view class="title-icon question-icon">问</view>
            <view class="question-span">
              <view class="question-content">{{ item.question }}</view>
              <view class="question-info">
                <view>{{ item.cname }}</view>
                <view>{{ item.update_time }}</view>
              </view>
            </view>
          </view>
          <view class="answer" v-for="(answerItem, answerIndex) in item.answer_list" :key="answerIndex">
            <view class="title-icon answer-icon">答</view>
            <view class="answer-content">
              <view class="answer-text" :class="{ 'is-best': answerItem.set_best }">
                <view
                  class="text"
                  :class="{ 'show-all': answerItem.moreState == 2 }"
                  :id="'view' + index + answerIndex"
                >
                  <text :id="'text' + index + answerIndex">{{ answerItem.answer_content }}</text>
                </view>
                <view class="more" v-if="answerItem.moreState > 0">
                  <text v-if="answerItem.moreState === 1" @click.stop="showAll(answerItem, 'show')">查看全文</text>
                  <text v-if="answerItem.moreState === 2" @click.stop="showAll(answerItem, 'hide')">收起</text>
                </view>
              </view>
              <view class="answer-info">
                <view class="answer-author">
                  <image :src="answerItem.prelogo | imageFilter('w_120')" class="author-icon"></image>
                  <text class="answer-name">{{ answerItem.answer_cname }}</text>
                  <text v-if="answerItem.position === 1">购房咨询师</text>
                  <text v-if="answerItem.position === 2">置业顾问</text>
                  <text v-if="answerItem.position === 3">经纪人</text>
                </view>
                <view class="answer-num" @click.stop="praise(answerItem)">
                  <view
                    ><my-icon
                      type="ic_zan"
                      :color="!answerItem.is_praise ? '#cccccc' : '#F65354'"
                      size="28rpx"
                    ></my-icon
                  ></view>
                  <text>有用({{ answerItem.praise_count }})</text>
                </view>
              </view>
              <view class="answer-number"> 查看{{ item.count }}个回答 </view>
            </view>
          </view>
          <view class="line"></view>
        </view>
        <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
      </view>
    </view>
    <view class="other">
      <view class="line"></view>
      <view class="bottom">
        <view class="share" @click="showSharePop">
          <my-icon type="ic_fenxiang" color="#222222" size="50rpx"></my-icon>
          <view>分享</view>
        </view>
        <view class="bottom-left" @click="showConsult()" v-if="!this.bid">我要提问</view>
        <view class="bottom-left build-bottom" @click="toSubForme(3, 5)" v-if="this.bid">预约优惠</view>
        <view class="bottom-left build-bottom" @click="showConsult()" v-if="this.bid">我要提问</view>
      </view>
    </view>
    <my-popup ref="consult" position="center" height="auto" :touch_hide="true" @click="stopMove">
      <question-popup :bid="bid" :isLogin="isLogin"  @closePopup="closeConsult"></question-popup>
    </my-popup>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <share-pop
      ref="show_share_pop"
      @copyLink="copyLink"
      @appShare="appShare"
      :showHaibao="false"
      @showCopywriting="showCopywriting"
    ></share-pop>
    <!-- 报名弹窗 -->
    <sub-form
      :groupCount="build.groupCount"
      :sub_type="sub_type"
      :sub_mode="sub_mode"
      ref="sub_form"
      @onsubmit="handleSubForm"
      :login_status="login_status"
    ></sub-form>
  </view>
</template>

<script>
import { uniLoadMore } from '@dcloudio/uni-ui'
import myIcon from '../components/myIcon.vue'
import tabBar from '../components/tabBar'
import { config } from '@/common/config.js'
import myPopup from '../components/myPopup.vue'
import checkLogin from '../common/utils/check_login'
import sharePop from '../components/sharePop.vue'
import { mapState } from 'vuex'
import copyText from '../common/utils/copy_text'
import shareTip from '../components/shareTip'
import questionPopup from '../components/questionPopup.vue'
import subForm from '../components/subForm'
export default {
  components: {
    myIcon,
    myPopup,
    tabBar,
    uniLoadMore,
    sharePop,
    shareTip,
    questionPopup,
    subForm,
  },
  data() {
    return {
      // 1购房咨询师 2置业顾问 3经纪人
      get_status: 'loading',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了',
      },
      ischange: true,
      topSrc: config.imgDomain + '/images/background/ask_banner.png',
      popupTopSrc: config.imgDomain + '/images/background/ask_popup.png',
      bestIconSrc: config.imgDomain + '/images/new_icon/bast_answer.png',
      keyword: '',
      menus: [{ type_id: 0, name: '全部' }],
      menu_index: 0,
      currentMenuId: 0,
      showList: [],
      questionList: [],
      rows: 5,
      page: 1,
      addQuestion: '',
      addPhoto: '',
      cname: '',
      isLogin: false,
      bid: '',
      show_share_tip: false,
      sub_type: 0,
      shareUserInfo: {},
      build: {}
    }
  },
  onLoad(options) {
    if (options.id) {
      this.bid = options.id
    }
    if (options.cate_id) {
      this.currentMenuId = options.cate_id
    }
    this.getData()
    this.loginState()
    uni.$on('getDataAgain', () => {
      this.loginState()
    })
  },
  onUnload() {
    uni.$off('getDataAgain')
  },
  onReachBottom() {
    if (this.get_status === 'noMore') {
      return
    }
    this.getList()
  },
  onPullDownRefresh() {
    this.getList()
  },
  computed: {
    ...mapState(['systemInfo', 'siteName', 'siteTel']),
    sub_mode() {
      return this.$store.state.sub_form_mode
    },
    login_status() {
      return this.$store.state.user_login_status
    },
  },
  methods: {
    getData() {
      this.$ajax.get('buildQuestion/questionType', {}, (res) => {
        if (res.statusCode === 200) {
          this.menus.push(...res.data.data)
          this.$nextTick(()=>{
            this.menu_index = this.menus.findIndex(item=>item.type_id === parseInt(this.currentMenuId))
          })
        }
      })
      this.getList()
    },
    loginState() {
      checkLogin({
        success: (res) => {
          this.isLogin = true
        },
        fail: (res) => {
          this.isLogin = false
        },
        complete: (res) => {
          this.$store.state.user_login_status = res.status
        },
      })
    },
    getList() {
      this.get_status = 'loading'
      let params = {
        rows: this.rows,
        page: this.page,
        bid: this.bid,
      }
      if (this.currentMenuId) {
        params.type_id = this.currentMenuId
      }
      if (this.keyword) {
        params.keyword = this.keyword
      }
      this.$ajax.get('buildQuestion/askList', params, (res) => {
        if (res.statusCode === 200) {
          // 最佳答案排序
          res.data.data.forEach((e) => {
            e.answer_list.forEach((item, index) => {
              item.moreState = 0
              if (item.set_best === 1) {
                e.answer_list.unshift(e.answer_list.splice(index, 1)[0])
              }
            })
          })
          this.questionList.push(...res.data.data)
          this.page++
          this.$nextTick(() => {
            const query = uni.createSelectorQuery().in(this)
            this.questionList.forEach((e, i) => {
              e.answer_list.forEach((item, index) => {
                item.moreState = 0
                let viewHeight, textHeight
                query
                  .select('#view' + i + index)
                  .boundingClientRect((data) => {
                    viewHeight = data.height
                  })
                  .exec()
                query
                  .select('#text' + i + index)
                  .boundingClientRect((data) => {
                    textHeight = data.height
                    this.questionList[i].answer_list[index].moreState = textHeight - viewHeight > 5 ? 1 : 0
                  })
                  .exec()
                // this.$set(item, 'moreState', textHeight - viewHeight > 5 ? 1 : 0)
              })
            })
            this.$forceUpdate()
          })
          if (this.questionList.length === 0 || res.data.data.length < this.rows) {
            this.get_status = 'noMore'
          } else {
            this.get_status = 'more'
          }
          
          this.build = res.data.build
          if (this.build && this.build.title) {
            document.title = (this.build.title || '') + '问答'
            // uni.setNavigationBarTitle({
            //   title: this.build.title + '问答'
            // })
          }else{
            document.title = '问答'
          }
          this.share = {
            title: res.data.share.title || `${(this.build?this.build.title:'')||this.$store.state.siteName}问答`,
            content: res.data.share.content || '',
            pic: res.data.share.pic || this.popupTopSrc,
            link: this.getShareLink(),
          }
          this.getWxConfig()
          if (res.data.share_user) {
            this.shareUserInfo = res.data.share_user
          }
        }
      })
      uni.stopPullDownRefresh()
    },
    handleSearch() {
      this.questionList = []
      this.page = 1
      this.getList()
    },
    menuChange(item) {
      this.questionList = []
      this.page = 1
      this.currentMenuId = item.type_id
      this.menu_index = this.menus.findIndex(f=>f.type_id === parseInt(this.currentMenuId))
      this.getList()
    },
    showConsult() {
      this.$ajax.get('buildQuestion/isTouristMode', {}, (res) => {
        // 1游客模式
        if (res.data.data !== '1' && this.login_status==1) {
          this.$navigateTo('/user/login/login')
        } else {
          this.$refs.consult.show()
        }
      })
    },
    closeConsult() {
      this.$refs.consult.hide()
    },
    toDetail(id) {
      this.$navigateTo(`/ask/detail?id=` + id)
    },
    showAll(item, type) {
      item.moreState = type === 'show' ? 2 : 1
      this.$forceUpdate()
    },
    praise(answer) {
      this.$store.state.allowOpen = true
      this.$ajax.post('buildQuestion/answerPraise', { answer_id: answer.answer_id }, (res) => {
        if (res.data.msg === '点赞成功') {
          answer.praise_count += 1
          answer.is_praise = 1
        } else if (res.data.msg === '取消点赞成功') {
          answer.praise_count -= 1
          answer.is_praise = 0
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    showSharePop() {
      this.$refs.show_share_pop.show()
    },
    showCopywriting() {
      let link = this.getShareLink()
      const weeks = ['日', '一', '二', '三', '四', '五', '六']
      let time = new Date()
      let year = time.getFullYear()
      let original_month = time.getMonth()
      let month = original_month + 1 >= 10 ? original_month + 1 : '0' + (original_month + 1)
      let day = time.getDate()
      let format_ctime = `${year}-${month}-${day}`
      let y_m_d = format_ctime.split('-')
      let week = weeks[new Date(format_ctime).getDay()]
      let content = `#${this.siteName}|${this.share.title || ''}#\n【${y_m_d[1]}月${y_m_d[2]}日 星期${week}】\n`
      this.questionList.slice(0, 8).forEach((item, index) => {
        content += `${index + 1}.${item.question}\n`
      })
      content += `更多资讯${link}`
      copyText(content, () => {
        uni.showToast({
          title: '复制成功,去发送给好友吧',
          icon: 'none',
        })
      })
    },
    copyLink() {
      this.show_share_tip = true;
      // this.showCopywriting()
    },
    // 获取分享链接
    getShareLink() {
      let link = ""
      // #ifdef H5
      link = window.location.href
      // #endif
      // #ifndef H5
      link = `${config.apiDomain}/ask/list?id=${this.bid}`
      // #endif
      return link
    },
    // 楼盘通知
    toSubForme(type, operation) {
      this.sub_operation = operation || ''
      this.sub_type = type
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e) {
      //提交报名
      e.from = '问答列表'
      e.bid = this.bid
      e.type = this.sub_type || ''
      if (this.sub_operation) e.operation = this.sub_operation
      if (this.shareUserInfo && this.shareUserInfo.adviser_id) {
        // 如果是置业顾问分享的
        e.share_uid = this.shareUserInfo.adviser_id
        e.is_adviser = 1
      } else if (this.shareUserInfo && this.shareUserInfo.agent_id) {
        // 如果是经纪人分享的
        e.share_uid = this.shareUserInfo.agent_id
        e.is_adviser = 2
      }
      this.$ajax.post('build/signUp.html', e, (res) => {
        uni.hideLoading()
        if (res.data.code === 1) {
          // 没开启引导登录模式或已经绑定手机号了
          if (this.sub_mode !== 2 || res.data.status === 3) {
            //提示报名成功
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
            this.$refs.sub_form.closeSub()
          } else {
            this.$refs.sub_form.getVerify()
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.page {
  position: relative;
  min-height: 100%;
  overflow: hidden;
}
.line {
  height: 1rpx;
  margin-left: 68rpx;
  transform: scaleY(0.5);
  background: #d8d8d8;
}
.top {
  position: relative;
  image {
    width: 100%;
  }
  .top-font {
    position: absolute;
    top: 64rpx;
    left: 80rpx;
    .top-title {
      font-size: 48rpx;
      color: #ffffff;
      letter-spacing: 3rpx;
      line-height: 64rpx;
    }
    .top-describe {
      padding-top: 24rpx;
      font-size: 22rpx;
      color: #eeeeee;
      letter-spacing: 0;
    }
  }
  .top-search {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -44rpx;
    width: 654rpx;
    padding: 24rpx 0 24rpx 120rpx;
    background: #ffffff;
    border: 0 solid #d8d8d8;
    box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.05);
    border-radius: 56rpx;
    box-sizing: border-box;
    input {
      font-size: 28rpx;
    }
    .search-icon {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 51rpx;
    }
  }
}
.center {
  .center-menu {
    background: #ffffff;
    padding-top: 84rpx;
    padding-left: 48rpx;
    overflow: hidden;
    white-space: nowrap;
    .menu-box {
      display: inline-block;
      width: 176rpx;
      height: 64rpx;
      background: #f8f8f8;
      border-radius: 8rpx;
      text-align: center;
      line-height: 64rpx;
      font-size: 28rpx;
      color: #999999;
      margin-right: 40rpx;
      &.active {
        background: #fb656a;
        box-shadow: 0 0 10px 0 rgba(251, 101, 106, 0.2);
        color: #ffffff;
      }
    }
  }
  .center-content {
    padding-bottom: 120rpx;
    .content-box {
      padding: 40rpx 48rpx 0 48rpx;
      background: #ffffff;
    }
    .title-icon {
      width: 48rpx;
      height: 48rpx;
      line-height: 48rpx;
      text-align: center;
      border-radius: 16rpx 0 16rpx 0;
      color: #ffffff;
      font-size: 22rpx;
    }
    .question {
      display: flex;
      padding-bottom: 40rpx;
      .question-icon {
        background: linear-gradient(90deg, #ffa857 0%, #ff6069 100%);
      }
      .question-span {
        flex: 1;
        font-family: PingFangSC-Medium;
        padding-left: 20rpx;
        .question-content {
          font-size: 32rpx;
          color: #333333;
          letter-spacing: 0;
          text-align: justify;
          line-height: 48rpx;
          font-weight: bold;
        }
      }
      .question-info {
        display: flex;
        justify-content: space-between;
        padding-top: 20rpx;
        font-size: 22rpx;
        color: #999999;
        view {
          font-size: 22rpx;
        }
      }
    }
    .answer {
      display: flex;
      .answer-icon {
        background: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
      }
      .answer-content {
        position: relative;
        margin-left: 20rpx;
        flex: 1;
        padding-bottom: 40rpx;
        .answer-text {
          position: relative;
          &.is-best {
            min-height: 74rpx;
          }
          .text {
            color: #666666;
            overflow: hidden;
            word-break: break-all;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 4;
            line-height: 42rpx;
            font-size: 28rpx;
            &.show-all {
              max-height: fit-content;
              -webkit-line-clamp: inherit;
            }
          }
          .more {
            text-align: right;
            font-size: 22rpx;
            color: #fb656a;
          }
        }
        .answer-info {
          display: flex;
          justify-content: space-between;
          color: #999999;
          padding-top: 20rpx;
          .answer-author {
            display: flex;
            line-height: 64rpx;
            font-size: 22rpx;
            .author-icon {
              width: 64rpx;
              height: 64rpx;
              border-radius: 50%;
            }
            .answer-name {
              padding-left: 20rpx;
              padding-right: 40rpx;
            }
          }
          .answer-num {
            display: flex;
            line-height: 64rpx;
            font-size: 22rpx;
            align-items: center;
            justify-content: center;
            text {
              padding-left: 10rpx;
            }
          }
        }
        .answer-number {
          background: #f8f8f8;
          border-radius: 8rpx;
          height: 64rpx;
          line-height: 64rpx;
          text-align: center;
          font-size: 22rpx;
          color: #999999;
          margin-top: 24rpx;
        }
      }
    }
  }
}
.other {
  width: 100%;
  height: 112rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  .line {
    margin-left: 0;
  }
}
.bottom {
  box-sizing: border-box;
  width: 100%;
  background: rgba(255, 255, 255, 0.98);
  display: flex;
  justify-content: space-between;
  padding: 0 48rpx;
  height: 111rpx;
  align-items: center;
  .share {
    display: flex;
    align-items: center;
    view {
      margin-left: 20rpx;
    }
  }
  .bottom-left {
    width: 480rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 32rpx;
    color: #ffffff;
    border-radius: 40rpx;
    background: linear-gradient(90deg, #ffa857 0%, #ff6069 100%);
    box-shadow: 0 6rpx 12rpx 0 rgba(255, 109, 102, 0.3);
    &.build-bottom {
      width: 250rpx;
    }
  }
}
// 报名按钮
.btn_list-box {
  display: flex;
  padding: 32rpx 48rpx 0 48rpx;
  background: #fff;
  .btn-item {
    padding: 20rpx 5rpx;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.05);
    color: $uni-color-primary;
    ~ .btn-item {
      margin-left: 14rpx;
    }
    text {
      font-size: 32rpx;
      margin-left: 16rpx;
    }
  }
}
</style>