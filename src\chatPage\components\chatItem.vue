<template>
<view>
    <view class="flex-box chat-item" :class="{'my-chat':item.is_my===1}">
        <block v-if="item.is_my===1">
            <view class="info-box flex-1">
                <view v-if="item.type==='text'" class="chat-info" v-html="fomatText(item.content)"></view>
                <view v-else-if="item.type==='image'" class="img-box">
                    <image :style="{width:(item.content.width>max_img_width?max_img_width:item.content.width)+'px',height:(item.content.width>max_img_width?max_img_width*item.content.height/item.content.width:item.content.height)+'px'}" :src="item.content.img | imgUrl('w_400')" mode="widthFix" @click="viewImg(item.content.img)" @error="imgLoadErr(e)"></image>
                </view>
                <view class="voice chat-info" v-else-if="item.type==='voice'" @click="$emit('playvoice', item)">
                    <text>{{parseInt(item.content.duration/1000)}}''</text>
                    <image class="play_vioce_icon" :src="paly_voice?'/static/icon/voice/play_voice.gif':'/static/icon/voice/voice_icon.png'"></image>
                </view>
                <view v-else-if="['build', 'ershou', 'renting', 'commercial', 'commercial_rent', 'commercial_transfer'].includes(item.type)" class="build-card" @click="toDetail(item.content, item.type)">
                    <view class="build-img-box">
                        <image class="build-img" mode="aspectFill" :src="item.content.image | imgUrl('w_400')"></image>
                    </view>
                    <view class="detail">
                        <view class="build-title">{{item.content.title}}</view>
                        <view class="desc" v-if="item.content.desc">{{item.content.desc}}</view>
                        <view class="price" v-if="item.content.price">{{item.content.price}}</view>
                    </view>
                    <view class="tel top-line" v-if="item.content.tel" @click="toTel(item.content.tel)">拨打电话</view>
                </view>
                <view v-else-if="item.type==='map'" @click="viewLocation(item)" class="chat-info">{{item.content.address}}<text class="view-map">[查看位置]</text></view>
                <!-- 微信名片消息 -->
                <view v-else-if="item.type==='wechat'" class="apply-wechat">
                    <text class="tip">您已将微信号发送给对方</text>
                </view>
                <view v-else-if="item.type==='apply_wx'" class="apply-wechat">
                    <view class="tip">您申请查看对方微信，等待对方同意</view>
                </view>
                <view v-else-if="item.type==='tel'" class="apply-tel">
                    <text class="tip">您已将手机号发送给对方</text>
                </view>
                <view v-else-if="item.type==='apply_tel'" class="apply-tel">
                    <text class="tip">您申请查看对方手机号，等待对方同意</text>
                </view>
                <view v-else class="apply-tel">
                    <text class="tip">[不支持的消息类型]</text>
                </view>
                <text v-if="item.type==='text'||item.type==='voice'" class="point_right_to"></text>
            </view>
            <view class="header-img-box">
                <image class="chat-header" :src="myHeader | imgUrl('w_80')" mode="aspectFill"></image>
            </view>
        </block>
        <block v-else-if="item.is_my===0">
            <view class="header-img-box">
                <image class="chat-header" :src="friendHeader | imgUrl('w_80')" mode="aspectFill"></image>
            </view>
            <view class="info-box flex-1">
                <view v-if="item.type==='text'" class="chat-info" v-html="fomatText(item.content)"></view>
                <view v-else-if="item.type==='image'" class="img-box">
                    <image :style="{width:(item.content.width>max_img_width?max_img_width:item.content.width)+'px',height:(item.content.width>max_img_width?max_img_width*item.content.height/item.content.width:item.content.height)+'px'}" :src="item.content.img | imgUrl('w_400')" mode="widthFix" @click="viewImg(item.content.img)" @error="imgLoadErr(e)"></image>
                </view>
                <view class="voice chat-info" v-else-if="item.type==='voice'" @click="$emit('playvoice', item)">
                    <image class="play_vioce_icon" :src="paly_voice?'/static/icon/voice/play_voice_black.gif':'/static/icon/voice/voice_icon_black.png'"></image>
                    <text>{{parseInt(item.content.duration/1000)}}''</text>
                </view>
                <view v-else-if="['build', 'ershou', 'renting', 'commercial', 'commercial_rent', 'commercial_transfer'].includes(item.type)" class="build-card" @click="toDetail(item.content, item.type)">
                    <view class="build-img-box">
                        <image class="build-img" mode="aspectFill" :src="item.content.image | imgUrl('w_400')"></image>
                    </view>
                    <view class="detail">
                        <view class="build-title">{{item.content.title}}</view>
                        <view class="desc" v-if="item.content.desc">{{item.content.desc}}</view>
                        <view class="price" v-if="item.content.price">{{item.content.price}}</view>
                    </view>
                    <view class="tel top-line" v-if="item.content.tel" @click="toTel(item.content.tel)">拨打电话</view>
                </view>
                <view v-else-if="item.type==='map'" @click="viewLocation(item)" class="chat-info">{{item.content.address}}<text class="view-map">[查看位置]</text></view>
                <!-- 微信名片消息 -->
                <view v-else-if="item.type==='wechat'" class="wechat-card">
                    <view class="flex-box wechat-info-box">
                        <view class="wechat-icon">
                            <my-icon type="weixin3" size="30" color="#fff"></my-icon>
                        </view>
                        <view class="wechat-info">
                            <view class="name">{{item.content.name}}的微信号</view>
                            <view class="wechat">{{item.content.wechat||''}}</view>
                        </view>
                    </view>
                    <view class="options">
                        <view class="left-btn" v-if="item.content.wechat" @click="copyWechat(item.content.wechat)">复制微信号</view>
                        <view class="right-btn" @click="viewQrcode(item.content.qrcode)">查看二维码</view>
                    </view>
                </view>
                <!-- 对方申请查看微信号 -->
                <view v-else-if="item.type==='apply_wx'" class="apply-wechat">
                    <text class="tip">对方申请查看您的微信,是否同意?</text>
                    <text class="agree" @click="agree('wechat')">同意</text>
                </view>
                <!-- 手机号消息 -->
                <view v-else-if="item.type==='tel'" class="wechat-card">
                    <view class="flex-box wechat-info-box" @click="toTel(item.content.tel)">
                        <view class="wechat-icon">
                            <my-icon type="dianhua1" size="30" color="#fff"></my-icon>
                        </view>
                        <view class="wechat-info">
                            <view class="name">{{item.content.name}}的手机号</view>
                            <view class="wechat">{{item.content.tel}}</view>
                        </view>
                    </view>
                </view>
                <!-- 对方申请查看手机号 -->
                <view v-else-if="item.type==='apply_tel'" class="apply-tel">
                    <text class="tip">对方申请查看您的手机号,是否同意?</text>
                    <text class="agree" @click="agree('tel')">同意</text>
                </view>
                <view v-else class="apply-tel">
                    <text class="tip">[不支持的消息类型]</text>
                </view>
                <view v-if="item.type==='text'||item.type==='voice'" class="point_left_to"></view>
            </view>
        </block>
        <!-- 系统消息 -->
        <block v-else>
            <view class="system-info">
                <view v-if="item.type==='wechat'" class="wechat-card">
                    <view class="flex-box wechat-info-box">
                        <view class="wechat-icon">
                            <my-icon type="weixin3" size="30" color="#fff"></my-icon>
                        </view>
                        <view class="wechat-info">
                            <view class="name">{{item.content.name}}的微信号</view>
                            <view class="wechat">{{item.content.wechat}}</view>
                        </view>
                    </view>
                    <view class="options">
                        <view class="left-btn" v-if="item.content.wechat" @click="copyWechat(item.content.wechat)">复制微信号</view>
                        <view class="right-btn" @click="viewQrcode(item.content.qrcode)">查看二维码</view>
                    </view>
                </view>
                <view v-if="item.type==='applyTel'">
                    <view class="apply-tel">
                        <text class="tip">对方申请查看您的手机号，是否同意？</text>
                        <!-- <text class="no">拒绝</text> -->
                        <text class="agree" @click="agree('tel')">同意</text>
                    </view>
                </view>
                <view v-if="item.type==='giveTel'" class="wechat-card">
                    <view class="flex-box wechat-info-box" @click="toTel(item.content.tel)">
                        <view class="wechat-icon">
                            <my-icon type="dianhua1" size="30" color="#fff"></my-icon>
                        </view>
                        <view class="wechat-info">
                            <view class="name">{{item.content.name}}的手机号</view>
                            <view class="wechat">{{item.content.tel}}</view>
                        </view>
                    </view>
                </view>
                <!-- <view v-if="item.type==='giveTel'">
                    <view class="apply-tel" @click="toTel(item.content.tel)">
                        <text class="tel">对方手机号码：{{item.content.tel}}</text>
                    </view>
                </view> -->
            </view>
        </block>
    </view>
</view>
</template>

<script>
import myIcon from '../../components/icon'
import {navigateTo, formatImg} from '../../common/index'
export default {
    props: {
        item: {
            type: Object,
            default: {}
        },
        index:Number,
        paly_voice:Boolean,
        myHeader:String,
        friendHeader:String
    },
    data() {
        return {
            max_img_width: this.$store.state.systemInfo.windowWidth/2,
            face_list: ['微笑','大笑','笑哭','开心','呲牙','坏笑','欣慰','鄙视','白眼','飞吻','鬼脸','酷','爱财','调皮','惊讶','无表情','思考','亲亲','喜欢','低沉','怒','生气','超爱','大哭','小声','惊恐','爱心','心碎','偷看','OK','耶','大拇指','握拳','强壮']
        }
    },
    filters: {
        imgUrl(val, param=""){
            if(!val){
                return ""
            }
            return formatImg(val, param)
        },
    },
    methods: {
        fomatText(val){
            if(typeof(val)!=='string'){
                return val
            }
            if(!val){
                return "　"
            }
            const regex = new RegExp(/\[(.+?)\]/, 'gi');
            let html = val.replace(regex,(item, face_name, index)=>{
                let face_index= this.face_list.indexOf(face_name)
                if(face_index>-1){
                    return `<img width="20" style="position:relative;top:5px;margin-left:3px" src="https://images.tengfangyun.com/images/face/${face_index}.png" />`
                }else{
                    return item
                }
            })
            return html
        },
        // 预览图片
        viewImg(img){
            this.$emit('viewimg',img)
        },
        // 查看位置
        viewLocation(item){
            uni.openLocation({
                name:item.content.name,
                address:item.content.address,
                latitude: parseFloat(item.content.lat),
                longitude: parseFloat(item.content.lng),
                success: res=> {
                    console.log('success');
                }
            });
        },
        
        copyWechat(cont){
            if(!cont){
                uni.showToast({
                    title:"此用户没有填写微信号",
                    icon:"none"
                })
                return
            }
            // #ifndef H5
            uni.setClipboardData({
                data:cont,
                success:res=>{
                    uni.showToast({
                        title:"复制成功",
                        icon:"none"
                    })
                }
            })
            // #endif
            // #ifdef H5
            let oInput = document.createElement('input');
            oInput.value = cont;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            document.execCommand("Copy"); // 执行浏览器复制命令
            uni.showToast({
                title:"复制成功",
                icon:"none"
            })
            oInput.remove()
            // #endif
        },
        viewQrcode(img){
            uni.previewImage({
                current:formatImg(img, 'w_280'),
                urls:[formatImg(img, 'w_280')]
            })
        },
        agree(type){
            let now_time = Date.parse(new Date())
            if(this.agree_time&&now_time-this.agree_time<10000){
                uni.showToast({
                    title:"您的操作过于频繁",
                    icon:'none'
                })
                return
            }
            this.agree_time = now_time
            this.$emit('agree', type)
            // let msg = {flag:'applyTel',tel:15866226825,to_id:this.$store.state.im.nowChat.platform_id,from_id:this.$store.state.im.myChatInfo.platform_id}
            //     uni.sendSocketMessage({
            //         data: JSON.stringify(msg),
            //         success:(res)=>{
            //             uni.showToast({
            //                 title:"已将您的手机发送给对方",
            //                 icon:'none'
            //             })
            //         },
            //         fail:err=>{
            //             console.log(err)
            //             uni.showToast({
            //                 title:err||'操作失败',
            //                 icon:'none',
            //                 mask:true
            //             })
            //         }
            //     });
        },
        toTel(tel){
            uni.makePhoneCall({
                phoneNumber:tel.toString()
            })
        },
        toDetail(info, type){
            let page;
            switch (type){
                case 'build':
                    page = "/pages/new_house/detail?id="+info.id
                    break
                case 'ershou':
                    page = "/pages/ershou/detail?id="+info.id
                    break
                case 'renting':
                    page = "/pages/renting/detail?id="+info.id
                    break
                case 'commercial':
                    page = "/commercial/sale/detail?id="+info.id
                    break
                case 'commercial_rent':
                    page = "/commercial/rent/detail?id="+info.id
                    break
                case 'commercial_transfer':
                    page = "/commercial/transfer/detail?id="+info.id
                    break
                default:
                    break
            }
            if(page){
                navigateTo(page)
            }
        },
        imgLoadErr(){
            this.$emit('onImgLoadErr',this.index)
        }
    },
    components: {
        myIcon
    }
}
</script>

<style lang="scss">
.chat-item.my-chat {
    .header-img-box {
        margin-left: 30upx;
        margin-right: 0;
    }

    .info-box {
        margin-left: 120upx;
        margin-right: 0;

        .img-box {
            float: right;
        }
        .build-card{
            float: right;
        }

        .chat-info {
            float: right;
            background-color: $uni-color-primary;
            color: #fff;
        }
        .point_right_to{
            height: 0;
            width: 0;
            border: 18upx solid;
            border-color: $uni-color-primary transparent transparent $uni-color-primary;
            position: absolute;
            top: 24upx;
            right: -15upx;
        }
    }
}

.chat-item {
    padding: 20upx 48rpx;

    .header-img-box {
        width: 80upx;
        height: 80upx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 30upx;
        position: relative;

        .chat-header {
            position: absolute;
            width: 100%;
            height: 100%;
        }
    }

    .info-box {
        margin-right: 120upx;
        position: relative;
        .point_left_to{
            height: 0;
            width: 0;
            border: 18upx solid;
            border-color: #ffffff #ffffff transparent transparent;
            position: absolute;
            top: 24upx;
            left: -15upx;
        }

        .img-box {
            // padding: 20upx;
            max-width: 50vw;

            image {
                // height: auto;
                max-width: 100%;
                border-radius: 20upx;
            }
        }

        .build-card{
            width: 55vw;
            background-color: #fff;
            border-radius: 8upx;
            float: left;

            .build-img-box{
                width: 100%;
                height: 35vw;
                overflow: hidden;

                .build-img{
                    width: 100%;
                    height: 100%;
                }
            }
            .detail{
                padding: 24upx;
                border-bottom-left-radius: 8rpx;
                border-bottom-right-radius: 8rpx;
                .build-title{
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    display: -webkit-box;
                }
                .desc{
                    line-height: 1;
                    font-size: 22rpx;
                    color: #999;
                    margin-top: 16rpx;
                    margin-bottom: 16rpx;
                }
                .price{
                    line-height: 1;
                    font-size: 22rpx;
                    color: $uni-color-primary;
                }
            }
            .tel{
                padding: 24rpx;
                line-height: 1;
                text-align: center;
                font-size: 32rpx;
                color: $uni-color-primary;
            }
        }

        .chat-info {
            display: inline-block;
            float: left;
            font-size: 32upx;
            min-height: 48upx;
            white-space: pre-wrap;
            word-break: normal;
            line-height: 1.5;
            padding: 16upx 30upx;
            border-radius: 8upx;
            background-color: #ffffff;
            &.voice{
                display: flex;
                align-items: center;
            }
        }
    }
    .play_vioce_icon{
        width: 40rpx;
        height: 40rpx;
    }
    .view-map {
        margin-left: 10upx;
        color: #3071bb;
    }
}
.system-info{
    padding: 10upx;
    width: 72%;
    margin: 0 auto;
}
.wechat-card{
    border-radius: 20upx;
    background-color: #fff;
    border: 1upx solid #f3f3f3;
    .wechat-info-box{
        align-items: center;
        padding: 24upx;
    }
    .wechat-icon{
        width: 100upx;
        min-width: 100upx;
        height: 100upx;
        text-align: center;
        line-height: 120rpx;
        border-radius: 10upx;
        background-color: #09de74;
        margin-right: 20upx;
    }
    .wechat-info{
        .name{
            margin-bottom: 10upx;
            color: #666;
        }
        .wechat{
            font-weight: bold;
        }
    }
    .options{
        border-top: 1upx solid #f3f3f3;
        display: flex;
        view{
            flex: 1;
            text-align: center;
            padding: 30upx 20upx;
        }
        .right-btn{
            border-left: 1upx solid #f3f3f3;
            color: #ff706f
        }
    }
}
.apply-tel,.apply-wechat{
    text-align: center;
    font-size: 24upx;
    margin-top: 20upx;
    padding: 6upx 10upx;
    background-color: #f3f3f3;
    border-radius: 5upx;
    .tip{
        color: #666;
        margin-right: 10upx;
    }
    .no{
        color: #666;
        margin-right: 10upx;
    }
    .agree{
        color: #1296db;
    }
    .tel{
        color: #1296db;
    }
}
</style>
