<template>
<view class="appointed_order">
    <view class="card" v-for="order_info in order_list" :key="order_info.id" @click="toOrderDetail(order_info.id)">
        <view class="title flex-box bottom-line">
            <text class="label">楼盘名称</text>
            <text>{{order_info.build_name}}</text>
        </view>
        <view class="card_content text-center">
            <view class="info_item flex-box">
                <view class="label">订单编号</view>
                <view class="value">{{order_info.order_sn}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">意向房源</view>
                <view class="value">{{order_info.house_name}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">款项金额</view>
                <view class="value">{{order_info.money}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">支付状态</view>
                <view class="value">{{order_info.pay_status_text}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">订单生成时间</view>
                <view class="value">{{order_info.ctime}}</view>
            </view>
        </view>
        <view class="order-footer flex-box top-line">
            <!-- 如果已经失效 -->
            <view class="surplus_time" v-if="order_info.shixiao">已失效</view>
            <!-- 如果失效且没确认 -->
            <view class="surplus_time" v-else-if="order_info.is_confirm===0">待确认</view>
            <!-- 如果没失效且已确认但是没付款显示支付的有效时间 -->
            <view class="surplus_time" v-else-if="order_info.pay_status==0">{{order_info.limit_time | timeFormat}}</view>
            <!-- 如果没失效已确认且已付款且已经核销显示已核销 -->
            <view class="surplus_time" v-else-if="order_info.write_off">已核销</view>
            <!-- 如果未核销且已付款 -->
            <view class="surplus_time" v-else-if="order_info.pay_status==1">待核销</view>
            <!-- 如果未不是已付款状态（退款或已退款等） -->
            <view class="surplus_time" v-else>{{order_info.pay_status_text}}</view>
            <view class="flex-box">
                <view class="btn" @click.stop.prevent="toOrderStatus(order_info.id)">订单状态</view>
                <!-- <view class="btn" @click.stop.prevent="toOrderDetail(order_info.id)">查看订单</view> -->
                <view class="btn" v-if="can_del_order" @click.stop.prevent="deleteOrder(order_info.id)">删除订单</view>
            </view>
            <!-- <view v-if="order_info.pay_status===0" class="btn" @click.stop.prevent="toPay(order.id)">去支付</view> -->
        </view>
    </view>
    <view class="nomore" v-if="order_list.length===0&&no_data">{{nodata_tip}}</view>
    <uni-load-more v-else :status="get_status" :content-text="content_text"></uni-load-more>
    <view v-if="can_add_order" class="create_order" @click="createOrder()">
        <my-icon type="zengjia" size="36" color="#fff"></my-icon>
    </view>
</view>
</template>

<script>
import myIcon from "../components/icon"
import {navigateTo,showModal} from '../common/index.js'
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
    data() {
        return {
            order_list:[],
            no_data:false,
            can_del_order:false,
            can_add_order:false,
            nodata_tip:"",
            get_status: "loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
            params:{
                page:1,
                rows:20
            }
        }
    },
    components: {
        myIcon,
        uniLoadMore
    },
    onLoad(options){
        this.online_id = options.online_id || ''
        this.getData()
    },
    filters:{
        timeFormat(val){
            if(!val){
                return ""
            }
            let now_hour = new Date().getHours()
            let hour = new Date(val*1000).getHours()
            let minute = new Date(val*1000).getMinutes()
            let second = new Date(val*1000).getSeconds()
            return `在${hour<10?'0'+hour:hour}:${minute<10?'0'+minute:minute}:${second<10?'0'+second:second}前支付有效`
        },
    },
    methods: {
        getData(){
            if(this.params.page === 1){
                this.order_list = []
            }
            this.get_status = "loading"
            this.params.online_id = this.online_id
            this.$ajax.get('online_my/orderListByAdviser',this.params,res=>{
                if(res.data.code === 1){
                    this.order_list = this.order_list.concat(res.data.list)
                    this.can_add_order = res.data.addorder
                    this.can_del_order = res.data.delorder
                    if(res.data.list.length<this.params.rows){
                        this.get_status = "noMore"
                    }else{
                        this.get_status = "more"
                    }
                }else{
                    this.no_data = true
                    this.nodata_tip = res.data.msg
                    this.get_status = "noMore"
                }
            })
        },
        toOrderDetail(order_id){
            navigateTo(`/adviser/order_detail?id=${order_id}`)
        },
        toOrderStatus(order_id){
            navigateTo(`/adviser/order_status?id=${order_id}`)
        },
        deleteOrder(order_id){
            showModal({
                content:'确定要删除此订单吗？',
                confirm: (res)=>{
                    this.$ajax.get('OnlineMy/delOrderByManager.html',{order_id,online_id:this.online_id}, res=>{
                        if(res.data.code === 1){
                            uni.showToast({
                                title: res.data.msg
                            })
                            this.getData()
                        }else{
                            uni.showToast({
                                title: res.data.msg,
                                icon:'none'
                            })
                        }
                    })
                }
            })
        },
        createOrder(){
            navigateTo(`/adviser/create_order?online_id=${this.online_id}`)
        }
    },
    onReachBottom(){
        if(this.get_status === "noMore"){
            return
        }
        this.params.page++
        this.getData()
    },
}
</script>

<style scoped lang="scss">
.card{
    margin: 0 24rpx 24rpx 24rpx;
    padding: 26rpx;
    border-radius: 10rpx;
    background-color: #fff;
    box-shadow: 0 0 10px #dedede;
    .title{
        justify-content: space-between;
        padding: 26rpx 0;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        .label{
            color: #666;
        }
    }
    .card_content{
        margin-top: 20rpx;
    }
}

.order-footer{
    justify-content: space-between;
    align-items: center;
    padding-top: 30rpx;
    margin-top: 20rpx;
    .surplus_time{
        color: $uni-color-primary;
    }
    .btn{
        height: 50rpx;
        line-height: 50rpx;
        font-size: 26rpx;
        padding: 0 25rpx;
        border-radius: 30rpx;
        background-color: $uni-color-primary;
        color: #fff;
        ~.btn{
            margin-left: 20rpx;
        }
    }
}

.info_item{
    padding: 15rpx 0;
    width: 100%;
    box-sizing: border-box;
    justify-content: space-between;
    .label{
        display: inline-block;
        text-align: left;
        min-width: 140rpx;
        color: #666;
    }
}

.create_order{
    position: fixed;
    right: 36upx;
    bottom: 180upx;
    height: 90upx;
    width: 90upx;
    border-radius: 50%;
    display:flex;
    align-items:center;
    justify-content:center;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.9);
    z-index: 96;
    box-shadow: 1upx 1upx 20upx 3upx rgba($color: $uni-color-primary, $alpha: 1);
}
.nomore{
    margin-top: 50rpx;
    padding: 20rpx;
    text-align: center;
    color: #666;
}
</style>
