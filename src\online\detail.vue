<template>
<view class="online_detail">
    <template v-if="theme==1">
    <view class="img-box">
		<!-- #ifndef APP-PLUS -->
		 <view class="online-data flex-box">
		<!-- #endif -->
		<!-- #ifdef APP-PLUS -->
		 <view class="online-data flex-box" :style="{top:top+'px'}">
		<!-- #endif -->
       
            <view class="data-item">
                <view><my-icon type="jiudian" color="#fff"></my-icon><text class="text">房源数</text><text>{{online_info.total_count}}</text></view>
            </view>
            <view class="data-item">
                <view><my-icon type="remen" color="#fff"></my-icon><text class="text">热度</text><text>{{online_info.hit}}</text></view>
                <!-- <view class="num">{{online_info.flow_number}}</view> -->
            </view>
        </view>
        <!-- #ifdef APP-PLUS -->
        <view class="share" :style="{top:top+'px'}" @click="showAppShare()"><my-icon line_height="inherit" type="fenxiang1" color="#ffffff" size="23"></my-icon></view>
        <!-- #endif -->
        <!-- #ifdef MP -->
        <button open-type="share" class="share"><my-icon line_height="inherit" type="fenxiang1" color="#ffffff" size="23"></my-icon></button>
        <!-- #endif -->
        <view class="audio-box" :class="{rotate:playing}" @click="switchAudio()" v-if="audio">
            <my-icon type="yinyue" size="20" color="#fff"></my-icon>
        </view>
        <image class="main-img" :src="online_info.pic | imgUrl('w_8601')" mode="widthFix"></image>
    </view>
    <view class="build_name">
        <uni-list-item v-if="online_info.build_id" className="build_title" :title="online_info.title" @click="toBuild()"></uni-list-item>
        <view class="coupon_info flex-box">
            <text class="label label_red">{{online_info.limit_buy?'限购'+online_info.limit_buy+'套':'不限购'}}</text>
            <text class="label label_green" v-if="online_info.support_refund">订金可退</text>
            <view class="diyongquan flex-2" v-if="user_coupon.money">￥{{user_coupon.money}}<text>抵用券</text></view>
            <view class="diyongquan flex-2" v-else></view>
            <view class="shengyu">只剩{{online_info.total}}</view>
        </view>
    </view>
    <view class="row">
        <view class="activity flex-box">
            <view class="act_left flex-1">
                <view class="title">{{online_info.discount||online_info.name}}</view>
            </view>
            <view class="act_right" @click="navigate('/online/choose?online_id='+id)">
            <view class="xuanfang">选房大厅</view>
            <view class="countdown flex-box">
                    <view class="label">
                        <text>{{online_info.is_start==0?"距离开始":(online_info.is_start==1?"距离结束":"已结束")}}</text>    
                    </view>
                    <view class="time-box" v-if="countdown.day||countdown.hours">
                        <text class="time">{{countdown.day}}</text>
                        <text>天：</text>
                    </view>
                    <view class="time-box">
                        <text class="time">{{countdown.hours}}</text>
                        <text>时</text>
                    </view>
                    <view class="time-box" v-if="!countdown.day&&!countdown.hours">
                        <text class="time">{{countdown.minutes}}</text>
                        <text>分</text>
                    </view>
                    <!-- <view class="colon">:</view>
                    <view class="time-box">
                        <text class="time">{{countdown.minutes}}</text>
                        <text>分</text>
                    </view>
                    <view class="colon">:</view>
                    <view class="time-box">
                        <text class="time">{{countdown.seconds}}</text>
                        <text>秒</text>
                    </view> -->
            </view>
            </view>
        </view>
    </view>
    <view class="card">
        <view class="content">
            <u-parse :html="online_info.content" @linkpress="navigate" :tag-style="tagStyle"></u-parse>
        </view>
    </view>
    <view class="bottom-menu flex-box">
        <view class="bar-item active">
            <my-icon type="home" size="22" color="#f65354"></my-icon>
            <view class="text">大厅</view>
        </view>
        <view class="bar-item" @click="toChoose()">
            <my-icon type="jiudian" size="22"></my-icon>
            <view class="text">选房</view>
        </view>
        <view class="bar-item" @click="navigate('/online/adviser?online_id='+id)">
            <my-icon type="xiaoxi" size="22"></my-icon>
            <view class="text">咨询</view>
        </view>
        <view class="bar-item" @click="navigate('/online/my?online_id='+id)">
            <my-icon type="shikebiao" size="22"></my-icon>
            <view class="text">订单</view>
        </view>
    </view>
    </template>
    <template v-if="theme==2">
        <view class="bg" :style="{backgroundImage:'url('+bg_img+'?x-oss-process=style/w_1200)'}">
            <view class="audio-box" :class="{rotate:playing}" @click="switchAudio()" v-if="audio">
                <my-icon type="yinyue" size="20" color="#fff"></my-icon>
            </view>
			<!-- #ifdef APP-PLUS -->
			  <view class="header flex-box" :style="{marginTop:top+'px'}">
			<!-- #endif -->
			<!-- #ifndef APP-PLUS -->
			  <view class="header flex-box" >
			<!-- #endif -->
                <view v-if="online_info.build_id" class="build-info flex-box"   @click="toBuild()">
                    <image  v-if ="build_info.img" class="build_img" mode="aspectFill" :src="build_info.img | imgUrl('w_80')"></image>
                    <view class="info flex-1" >
                        <view class="title">{{build_info.title}}</view>
                        <view class="flow_num">热度：{{online_info.hit}}</view>
                    </view>
                    <view class="btn" @click.prevent.stop="toChoose()">选房</view>
                </view>
                <view class="browsers">
                    <view class="browser" v-for="(item, index) in browse" :key="index">
                        <image :src="item | imgUrl('w_80')" mode="aspectFill"></image>
                    </view>
                </view>
            </view>
            <!-- 实时动态 -->
            <view class="notice-box flex-box">
                <view class="icon">
                    <my-icon type="yinliang" color="#c57403"></my-icon>
                </view>
                <view class="swiper-box flex-1">
                    <swiper class="swiper" :indicator-dots="false" :circular="true" :autoplay="true" :interval="4000" :duration="380">
                        <swiper-item @click="toChoose()">
                            <view class="swiper-item">实时动态：共{{online_info.total_count}}套 还剩{{online_info.total}}套</view>
                        </swiper-item>
                        <swiper-item @click="navigate('/online/notice?online_id='+id)">
                            <view class="swiper-item uni-bg-green">请查看购房大厅选房须知</view>
                        </swiper-item>
                        <swiper-item @click="toChoose()" v-if="online_info.discount">
                            <view class="swiper-item uni-bg-blue">{{online_info.discount}}</view>
                        </swiper-item>
                    </swiper>
                </view>
            </view>
            <!-- 优惠券 -->
            <view class="toast coupon_toast" :class="{show:show_toast}">
                <view class="close" @click="show_toast=false">
                    <my-icon type="zengjia" size="26" color="#fff"></my-icon>
                </view>
                <view class="toast_content flex-box">
                    <image class="icon" src="https://images.tengfangyun.com/images/icon/coupon.png" mode="aspectFill"></image>
                    <view class="flex-1">
                        <view class="title">恭喜您，已领取</view>
                        <view class="coupon_money">￥{{coupon.money}}</view>
                        <view class="coupon_name">{{coupon.name}}</view>
                    </view>
                    <view class="btn" @click="toChoose()">立即选房</view>
                </view>
            </view>
            <view class="bullet-box">
                <bullet v-if="bullet_store.length>0&&show_bullet" :delay="bullet_delay" :bullet_list="bullet_store"></bullet>
            </view>
            <view class="countdown-box flex-box">
                <view class="label">
                    <my-icon type="naozhong" size="18" color="#feac00"></my-icon>
                    <text style="margin-left:10rpx;">{{online_info.is_start==0?"距离开始时间：":(online_info.is_start==1?"距离结束时间：":"已结束")}}</text>    
                </view>
                <view class="time-box">
                    <text class="time">{{countdown.hours}}</text>
                </view>
                <view class="colon">时</view>
                <view class="time-box">
                    <text class="time">{{countdown.minutes}}</text>
                </view>
                <view class="colon">分</view>
                <view class="time-box">
                    <text class="time">{{countdown.seconds}}</text>
                </view>
                <view class="colon">秒</view>
            </view>
            <view class="options">
                <!-- #ifdef APP-PLUS -->
                <view class="option options1" @click="showAppShare()"><my-icon line_height="inherit" type="fenxiang1" color="#ffffff" size="26"></my-icon></view>
                <!-- #endif -->
                <!-- #ifdef MP -->
                <button open-type="share" class="option options1"><my-icon line_height="inherit" type="fenxiang1" color="#ffffff" size="26"></my-icon></button>
                <!-- #endif -->
                <view class="option baoming" @click="navigate('/online/sign_up?online_id='+id+'&inviter_id='+inviter_id)">荐</view>
                <view class="option options2" @click="navigate('/online/notice?online_id='+id)"><my-icon line_height="inherit" type="report" color="#ffffff" size="26"></my-icon></view>
                <view v-if="online_info.live_qrcode" class="option options6" @click="showZhibio()"><my-icon line_height="inherit" type="zhibo" color="#ffffff" size="26"></my-icon></view>
                <view class="option options3 relative-box" @click="toAdviser()"><my-icon line_height="inherit" type="zixun" color="#ffffff" size="26"></my-icon></view>
                <view class="option options4" v-if="online_info.build_id" @click="toBuild()"><my-icon line_height="inherit" type="jiudian" color="#333333" size="26"></my-icon></view>
                <view class="option options5" @click="navigate('/online/my?online_id='+id)"><my-icon line_height="inherit" type="my" color="#ffffff" size="26"></my-icon></view>
            </view>
            <view class="xuanfang" @click="toChoose()">选</view>
            <!-- 直播二维码 -->
            <view class="toast zhibo" :class="{show:show_zhibo}">
                <view class="close" @click="show_zhibo=false">
                    <my-icon type="zengjia" size="26" color="#333"></my-icon>
                </view>
                <view class="toast_content">
                    <image class="zhibo_img" :src="online_info.live_qrcode" mode="widthFix"></image>
                    <view class="zhibo_text">{{online_info.live_desc}}</view>
                </view>
            </view>
           
        </view>
    </template>
    <view class="mask" @click="onClickMask()" :class="{show:show_zhibo}"></view>
    <view class="mask mask2" :class="{show:show_tip}"></view>
	<!-- #ifndef APP-PLUS -->
	 <image class="tip1"  :class="{show:show_tip}" mode="widthFix" src="https://images.tengfangyun.com/images/icon/tip_1.png"></image>
	<!-- #endif -->
	<!-- #ifdef APP-PLUS -->
	 <image class="tip1" :style="{top:top+45+'px'}" :class="{show:show_tip}" mode="widthFix" src="https://images.tengfangyun.com/images/icon/tip_1.png"></image>
	<!-- #endif -->
   
    <image class="tip2" :class="{show:show_tip}" mode="widthFix" src="https://images.tengfangyun.com/images/icon/tip_2.png"></image>
    <image class="tip3" @click="show_tip=false" :class="{show:show_tip}" mode="widthFix" src="https://images.tengfangyun.com/images/icon/inow.png"></image>
	<!-- #ifdef APP-PLUS -->
	<my-popup ref="popup" position="bottom" @handleHide="showPopup=false">
	    <view class="share-box flex-box">
	    <button open-type="share" class="flex-1 item" @click="appShare('WXSceneSession')">
	        <image style="height:64upx;width:64upx" src="https://images.tengfangyun.com/images/icon/wechat.png"></image>
	        <view class="text">分享给好友</view>
	    </button>
	    <view class="flex-1 item" @click="appShare('WXSenceTimeline')">
	        <image style="height:64upx;width:64upx" src="https://images.tengfangyun.com/images/icon/time_line.png"></image>
	        <view class="text">分享到朋友圈</view>
	    </view>
	    </view>
	</my-popup>
	<!-- #endif -->
</view>
</template>

<script>
import {
  navigateTo,
  formatImg,
  isIos,
} from '../common/index.js'
import wx from "weixin-js-sdk"
import myIcon from "../components/icon.vue"
import bullet from "./components/bullet.vue"
import uParse from '../components/Parser/index'
import {uniListItem} from '@dcloudio/uni-ui'
import getChatInfo from '../common/get_chat_info'
import myPopup from '../components/myPopup.vue'
// import {wxShare} from '../common/mixin'
import {mapState, mapMutations} from 'vuex'
export default {
    data() {
        return {
            theme:0,
            online_info:{ //楼盘信息
                
            },
            build_info:{}, //楼盘信息
            bullet_store:[], //弹幕仓库
            show_bullet:true,
            bullet_delay:1500,
            browse:[], //右上角浏览记录
            audio:"", //音乐
            playing:false,
            coupon:{
                name:"",
                money:""
            },
            user_coupon:{
                name:"",
                money:""
            },
            show_toast:false,
            countdown:{
                hours:0,
                minutes:0,
                seconds:0
            },
            id:"",
            tagStyle:{
                video:'max-width:100%'
            },
            bg_img:"", //背景图片
            show_zhibo:false,
            adviser_choose:1, //1:自主选房 2:置业顾问选房
            inviter_id:"",
            show_tip:false, //是否显示提示
			// #ifdef APP-PLUS
			top:0,
			// #endif
        }
    },
    computed: {
        ...mapState(['online_inviter'])
    },
    // mixins:[wxShare],
    components: {
        myIcon,
        bullet,
        uParse,
        uniListItem,
        myPopup
    },
    onLoad(options){
        if(options.id){
            this.id = options.id
            if(options.inviter_id){
                this.inviter_id = options.inviter_id
                this.setOnlineInviter({
                    inviter_id: options.inviter_id,
                    online_id: this.id
                })
            }
            uni.showLoading({
                title:"加载中..."
            })
            let url;
            if(isIos()){
                url = this.$store.state.firstUrl
            }else{
                url = window.location.href
            }
            this.$ajax.get('/wechat/index/signature.html',{url:url},res=>{
                uni.hideLoading()
                if(res.data.code == 1){
                    res.data.config.jsApiList = ['updateAppMessageShareData','updateTimelineShareData']
                    wx.config(res.data.config)
                }
                this.getData()
            })
        }
        uni.$on("getDataAgain",this.getData)
		// #ifdef APP-PLUS
		 this.top=this.$store.state.systemInfo.safeArea.top
		// #endif
    },
    onShow(){
        if(this.playing){
            this.innerAudioContext.play()
        }
        // #ifdef H5
        if(!this.show_bullet){
            this.bullet_delay = 100
            this.show_bullet = true
        }
        // #endif
    },
    onHide(){
        this.innerAudioContext&&this.innerAudioContext.pause()
        // #ifdef H5
        this.show_bullet = false
        // #endif
    },
    filters: {
        imgUrl(val, param = "") {
            return formatImg(val, param)
        }
    },
    methods:{
        ...mapMutations(['setOnlineInviter']),
        getData(){
            uni.showLoading({
                title:"加载中..."
            })
            this.share = {}
            this.$ajax.get('online/index.html',{id:this.id},res=>{
                uni.hideLoading()
                if(res.data.code === 1){
                    res.data.onlineInfo.title = res.data.buildInfo.title
                    uni.setNavigationBarTitle({
                        title:`${res.data.onlineInfo.title||res.data.onlineInfo.name||''}线上售楼`
                    })
                    this.my_id = res.data.uid || 0 //记录自己的id，分享的时候需要携带此参数
                    if(res.data.onlineInfo.style==2){
                        if(!uni.getStorageSync('show_tip')){
                            this.show_tip = true
                            uni.setStorageSync('show_tip','1')
                        }
                        this.theme = 2
                        this.bg_img = res.data.onlineInfo.bg_pic
                        this.build_info = res.data.buildInfo
                        
                        if(res.data.trajectory.length==1){
                            res.data.trajectory = res.data.trajectory.concat(JSON.parse(JSON.stringify(res.data.trajectory)))
                            res.data.trajectory = res.data.trajectory.concat(JSON.parse(JSON.stringify(res.data.trajectory)))
                        }
                        if(res.data.trajectory.length==2){
                            res.data.trajectory = res.data.trajectory.concat(JSON.parse(JSON.stringify(res.data.trajectory)))
                        }
                        this.bullet_store = res.data.trajectory.map(item=>{
                            item.is_show = 0
                            return item
                        })
                        this.browse = res.data.browse
                    }else{
                        this.theme = 1
                    }
                    // 如果开启了置业顾问选房模式 1:自主选房 2:置业顾问选房
                    this.adviser_choose = res.data.onlineInfo.model
                    // 如果有优惠券
                    if(res.data.coupon&&res.data.coupon.name){
                        this.coupon = res.data.coupon
                        setTimeout(()=>{
                            this.show_toast = true
                        },1000)
                    }
                    if(res.data.user_coupon){
                        this.user_coupon = res.data.user_coupon
                    }
                    wx.ready(() => {   //需在用户可能点击分享按钮前就先调用
                        if(res.data.share){
                            this.share = res.data.share
                            this.initShare()
                        }
                        // 如果有音乐
                        if(res.data.onlineInfo.audio_path){
                            this.audio = res.data.onlineInfo.audio_path
                            this.playAudio(this.audio)
                        }
                    })
                    this.online_info = res.data.onlineInfo
                    let nowTime = new Date().getTime()
                    let surplusTime = res.data.onlineInfo.live_stime*1000-nowTime
                    if(surplusTime>=0){
                        setTimeout(()=>{
                            this.show_zhibo = true
                        },surplusTime)
                    }
                    if(res.data.onlineInfo.is_start==0){ //未开始
                        if(this.theme == 2){
                            this.countDown2(res.data.onlineInfo.start_time-Date.parse(new Date())/1000)
                        }else{
                            this.countDown(res.data.onlineInfo.start_time-Date.parse(new Date())/1000)
                        }
                    }else if(res.data.onlineInfo.is_start==1){ //进行中
                        if(this.theme == 2){
                            this.countDown2(res.data.onlineInfo.end_time-Date.parse(new Date())/1000)
                        }else{
                            this.countDown(res.data.onlineInfo.end_time-Date.parse(new Date())/1000)
                        }
                    }else{ // 已结束
                        
                    }
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            })
        },
        initShare(){
            wx.updateAppMessageShareData({ 
                title: this.share.title||'', // 分享标题
                desc: this.share.content, // 分享描述
                link: `${window.location.origin}/h5/online/detail?id=${this.id}&inviter_id=${this.my_id}`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                imgUrl: this.share.pic+'?x-oss-process=style/w_220', // 分享图标
                success: function () {
                    // 设置成功	
                }
            })
            wx.updateTimelineShareData({ 
                title: this.share.title, // 分享标题
                link: window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                imgUrl:this.share.pic+'?x-oss-process=style/w_220', // 分享图标
                success: function () {
                // 设置成功
                }
            },err=>{
                uni.showToast({
                    title:'获取数据失败',
                    icon:'none'
                })
                setTimeout(()=>{
                    uni.navigateBack()
                },1500)
            })
        },
        playAudio(src){
            this.innerAudioContext = uni.createInnerAudioContext();
            // #ifdef MP
            this.innerAudioContext.obeyMuteSwitch = false; //为false时即使用户打开了静音开关，也能继续发出声音
            // #endif
            this.innerAudioContext.autoplay = true;
            this.innerAudioContext.loop = true;
            this.innerAudioContext.src = src;
            this.innerAudioContext.onPlay(() => {
                console.log('开始播放');
                this.playing = true
            });
            this.innerAudioContext.onError((res) => {
                console.log("播放失败",res)
            });
        },
        switchAudio(){
            if(this.playing){
                this.innerAudioContext.pause()
            }else{
                this.innerAudioContext.play()
            }
            this.playing = !this.playing
        },
        countDown(remaining_time){
            if(remaining_time<=0){
                return
            }
            this.countdown.day = parseInt(remaining_time/86400)
            let hours_str = remaining_time%86400
            this.countdown.hours = parseInt(hours_str/3600)
            let minutes_str = hours_str%3600
            this.countdown.minutes = parseInt(minutes_str/60)<10?"0"+parseInt(minutes_str/60):parseInt(minutes_str/60)
            this.countdown.seconds = minutes_str%60<10?"0"+minutes_str%60:minutes_str%60
            this.timer = setTimeout(()=>{
                remaining_time--
                this.countDown(remaining_time)
            },1000)
        },
        countDown2(remaining_time){
            if(remaining_time<=0){
                return
            }
            // this.countdown.day = parseInt(remaining_time/86400)
            this.countdown.hours = parseInt(remaining_time/3600)
            let minutes_str = remaining_time%3600
            this.countdown.minutes = parseInt(minutes_str/60)<10?"0"+parseInt(minutes_str/60):parseInt(minutes_str/60)
            this.countdown.seconds = minutes_str%60<10?"0"+minutes_str%60:minutes_str%60
            this.timer = setTimeout(()=>{
                remaining_time--
                this.countDown2(remaining_time)
            },1000)
        },
        toBuild(){
            let to_page = 'pages/new_house/detail'
            let prev_pages = getCurrentPages().map(item=>item.route)
            if(prev_pages.indexOf(to_page)==1){
                uni.navigateBack()
            }else{
                navigateTo(`/${to_page}?id=${this.online_info.build_id}`)
            }
        },
        toChoose(){
            // if(this.adviser_choose === 2){
            //     this.show_choose_adviser = true
            //     return
            // }
            // if(this.adviser_choose === 3){
            //     navigateTo(`/online/sign_up?online_id=${this.id}`)
            //     return
            // }
            navigateTo(`/online/choose?online_id=${this.id}`)
        },
        showZhibio(){
            // #ifdef MP-WEIXIN
            if(this.online_info.live_room){
                navigateTo(`plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${this.online_info.live_room}`)
            }else{
                this.show_zhibo = true
            }
            // #endif
            // #ifndef MP-WEIXIN
            this.show_zhibo = true
            // #endif
        },
        toAdviser(){
            navigateTo(`/online/adviser?online_id=${this.id}`)
        },
        navigate(url){
            // console.log(url)
            navigateTo(url)
        },
        onClickMask(){
            this.show_zhibo=false
        }
    },
    onUnload(){
        if(this.innerAudioContext&&this.innerAudioContext.destroy){
            this.innerAudioContext.destroy()
        }
        if(this.timer){
            clearTimeout(this.timer)
        }
        uni.$off("getDataAgain")
        this.$store.state.allowOpen = true
    },
    onShareAppMessage(){
        // #ifdef MP-BAIDU
        return {
            title:this.share.title,
            content:this.share.content,
            imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):"",
            path:`/online/detail?id=${this.id}&inviter_id=${this.my_id}`
        }
        // #endif
        // #ifdef MP-WEIXIN
        return {
            title:this.share.title,
            imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):"",
            path:`/online/detail?id=${this.id}&inviter_id=${this.my_id}`
        }
        // #endif
    }
}
</script>

<style scoped lang="scss">
// 模式2
@keyframes rotate{
    0%{-webkit-transform:rotate(0deg);}
    25%{-webkit-transform:rotate(90deg);}
    50%{-webkit-transform:rotate(180deg);}
    75%{-webkit-transform:rotate(270deg);}
    100%{-webkit-transform:rotate(360deg);}
}
.bg{
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    // 音乐
    .audio-box{
        position: fixed;
        right: 20upx;
        margin-top: var(--window-top);
        top: 160upx;
        width: 70upx;
        height: 70upx;
        border-radius: 50%;
        background-color: rgba($color: #333, $alpha: 0.6);
        z-index: 1;
        color: #fff;
        text-align: center;
        line-height: 70upx;
        &.rotate{
            animation:rotate 3s linear infinite
        }
        .icon-yinyue{
            line-height: 70upx;
        }
    }
    .header{
        align-items: center;
        box-sizing: border-box;
        padding: 30rpx 20rpx;
    }
    // 楼盘信息
    .build-info{
        width: 60%;
        margin-right: 20rpx;
        color: #fff;
        .build_img{
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            margin-right: 15rpx;
        }
        // .info{
        //     max-width:calc(100% - 200upx);
        // }
        .title{
            margin-bottom: 5rpx;
            max-width: 100%;
            display:-webkit-box; //作为弹性伸缩盒子模型显示。
            -webkit-box-orient:vertical; //设置伸缩盒子的子元素排列方式--从上到下垂直排列
            -webkit-line-clamp:2; //显示的行
            // white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .flow_num{
            font-size: 24rpx;
        }
        .btn{
            height: 50rpx;
            width: 100rpx;
            text-align: center;
            line-height: 50rpx;
            border-radius: 25rpx;
            margin-top: 10rpx;
            background-color: #ff3952;
        }
    }
    // 访客
    .browsers{
        flex: 1;
        text-align: right;
        height: 60rpx;
        overflow: hidden;
        .browser{
            display: inline-block;
            ~.browser{
                margin-left: -20rpx;
            }
        }
        image{
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
        }
    }
    // 实时动态
    .notice-box{
        align-items: center;
        margin-left: 20rpx;
        width: 60%;
        height: 40rpx;
        line-height: 40rpx;
        padding: 10rpx 0 10rpx 20rpx;
        background-color: rgba($color: #000000, $alpha: 0.6);
        border-radius: 15rpx;
        color: #fff;
        overflow: hidden;
        .icon{
            margin-top: 5rpx;
        }
        .swiper-box{
            height: 100%;
            .swiper-item{
                font-size: 26rpx;
                padding-left: 15rpx;
                padding-right: 20rpx;
                box-sizing: border-box;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
    // 优惠券弹窗内容
    .coupon_toast{
        width: 75vw;
        top: 30vh;
        padding: 60rpx 30rpx;
        .toast_content{
            align-items: center;
            .icon{
                width: 130rpx;
                height: 130rpx;
            }
            .title{
                color: #fff;
            }
            .coupon_money{
                font-size: 68rpx;
                color: #ff755b;
                line-height: 1;
            }
            .coupon_name{
                font-size: 26rpx;
                color: #dfae33
            }
            .btn{
                padding: 8rpx 16rpx;
                border-radius: 12rpx;
                font-size: 26rpx;
                background-color: #34cc67;
                color: #fff;
            }
        }
    }

    // 直播弹窗内容
    .zhibo{
        top: 20vh;
        width: 600rpx;
        background-color: #fff;
        border-radius: 20rpx;
        .toast_content{
            text-align: center;
            .zhibo_img{
                width: 520rpx;
                height: 520rpx;
            }
            .zhibo_text{
                margin-top: 20rpx;
                font-size: 32rpx;
            }
        }
    }

    // 倒计时
    .countdown-box{
        position: absolute;
        align-items: center;
        left: 20rpx;
        bottom: 56rpx;
        padding: 15rpx 5rpx;
        box-sizing: border-box;
        // height: 82rpx;
        border-radius: 15rpx;
        background-color: rgba($color: #000000, $alpha: 0.6);
        .label{
            font-size: 26rpx;
            color: #fff;
            padding: 0 5rpx;
        }
        .colon{
            font-size: 26rpx;
            color: #fff;
            padding: 0 5rpx;
        }
        .time-box{
            height: 36rpx;
            padding: 0 15rpx;
            line-height: 36rpx;
            border-radius: 8rpx;
            background-color: #feac00;
            .time{
                font-size: 26rpx;
                color: #fff;
            }
        }
    }
    // 右侧菜单
    .options{
        position: fixed;
        right: 20rpx;
        bottom: 50rpx;
        z-index: 10;
        .option{
            width: 80rpx;
            height: 80rpx;
            padding: 0;
            text-align: center;
            line-height: 80rpx;
            border-radius: 50%;
            margin-top: 15rpx;
            background-color: #dedede;
            &.options1{
                background-color: rgba($color: #000000, $alpha: 0.6)
            }
            &.options2{
                background-color: #c90207
            }
            &.options3{
                background-color: #00cccb
            }
            &.options4{
                background-color: #dedede
            }
            &.options5{
                background-color: #00cccb
            }
            &.options6{
                background-color: #09bb07
            }
        }
        .baoming{
            width: 80rpx;
            height: 80rpx;
            text-align: center;
            line-height: 80rpx;
            border-radius: 50%;
            font-size: 38rpx;
            background-color: #ff4c6c;
            color: #fff;
        }
    }

    .xuanfang{
        width: 80rpx;
        height: 80rpx;
        text-align: center;
        line-height: 80rpx;
        border-radius: 50%;
        position: fixed;
        right: 115rpx;
        bottom: 50rpx;
        font-size: 38rpx;
        background-color: #ff4c6c;
        color: #fff;
    }
}



// 模式1
.online_detail{
    background-color: #f3f3f3;
    padding-bottom: 100rpx;
    .img-box{
        position: relative;
        font-size: 0;
        .online-data{
            padding: 15rpx 20rpx;
            position: absolute;
            top: 0;
            left: 0;
            background-color: rgba($color: #000000, $alpha: 0.6);
            color: #fff;
            z-index: 2;
            border-radius: 8rpx;
            .data-item{
                padding: 5rpx 10rpx;
                text-align: center;
                position: relative;
                ~.data-item:before{
                    content: "";
                    position: absolute;
                    top: 10rpx;
                    bottom: 10rpx;
                    left: 0;
                    width: 1rpx;
                    background-color: #fff;
                }
                .text{
                    margin-right: 8rpx;
                    display: inline-block;
                    // margin-bottom: 10rpx;
                }
            }
        }
        .main-img{
            width: 100%;
        }
        .share{
            width: 70rpx;
            height: 70rpx;
            padding: 0;
            text-align: center;
            line-height: 70rpx;
            border-radius: 50%;
            margin-top: 15rpx;
            background-color: rgba($color: #000000, $alpha: 0.5);
            position: absolute;
            top: 20rpx;
            right: 20rpx;
            z-index: 2;
        }
        .audio-box{
            position: absolute;
            right: 20upx;
            // #ifdef H5
            top: 20upx;
            // #endif
            // #ifndef H5
            top: 150upx;
            // #endif
            width: 70upx;
            height: 70upx;
            border-radius: 50%;
            background-color: rgba($color: #000000, $alpha: 0.5);
            z-index: 1;
            color: #fff;
            text-align: center;
            line-height: 70upx;
            &.rotate{
                animation:rotate 3s linear infinite
            }
            .icon-yinyue{
                line-height: 70upx;
            }
        }
    }
    
    .build_name{
        background-color: #fff;
    }

    .coupon_info{
        padding: 20rpx;
        align-items: center;
        .label{
            padding: 10rpx 20rpx;
            line-height: 1;
            font-size: 24rpx;
            ~.label{
                margin-left: 20rpx;
            }
        }
        .label_red{
            background-color: #fdeded;
            color: #ff1f3d
        }
        .label_green{
            background-color: #ccffe0;
            color: #25ac29
        }
        .diyongquan{
            font-size: 36rpx;
            font-weight: bold;
            color: #ff2525;
            text{
                display: inline-block;
                font-size: 22rpx;
                transform:scale(0.8,0.8);
            }
        }
        .shengyu{
            font-size: 22rpx;
            color: #666;
            transform:scale(0.8,0.8);
        }
    }


    .title-row{
        padding: 20rpx;
        font-size: 32rpx;
        background: #fff;
    }
    
    .activity{
        padding: 24rpx 20rpx;
        margin:10rpx 0;
        border-radius: 10rpx;
        background: linear-gradient(to right,#ee1140, #fd7128);
        color: #fff;
        display: flex;
        justify-content: space-between;
        .act_left{
            margin-right: 10rpx;
        }
        .title{
            margin-right: 10rpx;
            font-size: 30rpx;
            line-height: 1.6;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            display: -webkit-box;
        }
        .act_right{
            text-align: right;
        }
        .xuanfang{
            padding: 8upx 20upx;
            border-radius: 24upx;
            margin-bottom: 15rpx;
            background-color: #fff;
            color: $uni-color-primary;
            display: inline-block
        }
        .countdown{
            .label{
                padding: 0;
                margin: 0;
                color: #fff;
                font-size: 26rpx;
            }
            .time-box{
                font-size: 26rpx;
                .time{
                    margin: 0 10rpx;
                    padding: 0 10rpx;
                    border-radius: 5rpx;
                    background: #ffff33;
                    color: $uni-color-primary;
                }
            }
        }
    }
        
    .content{
        background-color: #fff;
        padding: 20rpx;
    }    
    
    .bottom-menu{
        position: fixed;
        width: 100%;
        padding: 8rpx 0;
        box-sizing: border-box;
        bottom: 0;
        background-color: #fff;
        border-top: 1rpx solid #dedede;
        .bar-item{
            line-height: 1;
            flex: 1;
            text-align: center;
            color: #333;
            .text{
                margin-top: 8rpx;
            }
            &.active{
                color:  $uni-color-primary;
            }
        }
    }
}


.mask{
    position: fixed;
    top: 0;
    bottom: 0;
    width: 100%;
    z-index: -1;
    background-color: rgba($color: #000000, $alpha: 0);
    transition: 0.26s;
    &.show{
        background-color: rgba($color: #000000, $alpha: 0.5);
        z-index: 90;
        &.mask2{
            z-index: 100;
        }
    }
}


.share-box {
  padding: 20upx 0;
  background-color: #fff;

  .tip {
    padding: 10px;
    width: 100%;
    font-weight: 700;
    box-sizing: border-box;
    text-align: center;
  }

  button {
    line-height: initial;
    padding: 10upx 20upx;
    background-color: #fff;
  }

  .item {
    text-align: center;
    padding: 10upx 20upx;
    line-height: inherit;
  }
}
.bullet-box{
    position: fixed;
    width: 70vw;
    bottom: 200rpx;
    z-index: 9;
}

// 弹窗
.toast{
    position: fixed;
    left: 0;
    right: 0;
    padding: 40rpx;
    margin: auto;
    background-color: rgba($color: #000000, $alpha: 0.6);
    border-radius: 20rpx;
    opacity: 0;
    z-index: -1;
    transition: 0.26s;
    transform:scale(0.3,0.3);
    &.show{
        opacity: 1;
        transform:scale(1,1);
        z-index: 99;
    }
    .close{
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        transform:rotate(45deg);
        z-index: 2;
    }
}

.tip1{
    width: 200rpx;
    height: 200rpx;
    position: fixed;
    z-index: -1;
    left: calc(60% - 170upx);
    top: 90rpx;
    opacity: 0;
    transition: 0.26s;
    &.show{
        opacity: 1;
        z-index: 101;
    }
}
.tip2{
    width: 200rpx;
    height: 200rpx;
    position: fixed;
    z-index: -1;
    right: 90rpx;
    bottom: 210rpx;
    opacity: 0;
    transition: 0.26s;
        &.show{
        opacity: 1;
        z-index: 101;
    }
}

.tip3{
    position: fixed;
    bottom: 100rpx;
    width: 200rpx;
    height: 200rpx;
    left: 0;
    right: 0;
    margin: auto;
    z-index: -1;
    opacity: 0;
    transition: 0.26s;
        &.show{
        opacity: 1;
        z-index: 101;
    }
}

</style>
