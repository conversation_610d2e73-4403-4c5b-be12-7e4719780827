<template>
	<view class="page">
		<view class="header" :style="{backgroundImage:'url('+bgcolor+')'}">
			<view class="head-info">
				<view class="title">数据报表</view>
				<view class="update-tip">{{siteName}} {{currentMonth}}</view>
			</view>
			<view class="share_icon" @click="showSharePop">
				<my-icon type="ic_fenxiang" size="32rpx" color="#fff"></my-icon>
			</view>
		</view>

		<view class="yushou-data-info">
			<view class="middle-bar">
                <view class="yongtu flex-box">
                    <view class="yongtu-con">规划用途</view>
                    <view class="yongtu-tabs flex-1">
                        <tab-bar :tabs="auction" :nowIndex="currentIndexAuction" :fixedTop="false" lineHeight="1" height="auto" :showLine="false">
                            <text v-for="(item, index) in auction" :key="index" :id="'i' + index" :class="{active:currentIndexAuction===index}" @click="clickTab({detail:{current:index,value:item.id}},1)">{{item.auc_name}}</text>
                        </tab-bar>
                    </view>

                </view>
                <view class="yongtu flex-box">
                    <view class="yongtu-con">区域</view>
                    <view class="yongtu-tabs flex-1">
                       <tab-bar :tabs="areas" :nowIndex="currentIndex" :fixedTop="false" lineHeight="1" height="auto" :showLine="false">
						   <block v-for="(item, index) in areas" :key="index">
                            <picker v-if="item.children&&item.children.length>0" :range="item.children"  :id="'i' + index" :class="{active:currentIndex===index}" @change="changeTab($event,item.children)" range-key="areaname"  @click="clickTab({detail:{current:index,value:item.areaid}},3,item)">{{item.areaname}}</picker>
							 <text v-else  :id="'i' + index" :class="{active:currentIndex===index}" @click="clickTab({detail:{current:index,value:item.areaid}},2,item)">{{item.areaname}}</text>
							</block>
                        </tab-bar>
                    </view>

                </view>
				
			</view>

				<view class="qiun-columns">
					<view class="qiun-charts" >
						<canvas canvas-id="canvasLineA" id="canvasLineA" class="charts" @touchstart="touchLineA"></canvas>
					</view>
				</view>
                <view class='desc_table' v-if = 'tableDate.length>0'>
                    <view class="desc-title">成交详情</view>
                    <table  cellspacing="1" cellpadding="0">
                        <thead>
                            <th class="month">月份</th>
                            <th class="price">成交宗数（块）</th>
                            <th class="desc">成交面积（m²）</th>
                            <!-- <tr v-for="(item,index) in state" :key="index">
                            <th>{{item}}</th>
                            </tr> -->
                        </thead>
                        <tbody>
                            <tr v-for="(item,index) in (tableDate)" :key="index">
                                <td>{{item.date}}</td>
                                <td>{{item.zongshu}}</td>
                                <td>{{item.area}}</td>
                                <!-- <td v-for="(item,index) in (lineData.categories)" :key="index">{{item}}--{{index}}</td> -->
                            </tr>
                        </tbody>
                    </table>
                </view>
				<view class="friend-tips">
					{{tips}}
				</view>
		</view>
		<share-pop ref="show_share_pop" @copyLink="copyLink" :showHaibao="false" @showCopywriting='showCopywriting'></share-pop>
		<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
		<dingyue ref="dingyue" @dingyue="dingyue" :type="type" @login="toLogin" @bindPhone="toBind" ></dingyue>
        <my-popup ref="qrcode_popup" position="top">
			<view class="qrcode-box">
				<!-- #ifdef H5 -->
				<view class="img-box">
					<view class="title titles">数据报告将通过服务号发送</view>
					<view class="tip red">请关注{{siteName}}公众号</view>
					<image @longtap="saveQrcode" class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="title">长按保存图片</view>
						<view class="tip">相册选取，识别关注</view>
					</view>
				</view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view class="img-box">
					<view class="title titles">数据报告将通过服务号发送</view>
					<view class="tip red">请关注{{siteName}}公众号</view>
					<image class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="tip">长按识别二维码关注公众号</view>
					</view>
				</view>
				<!-- #endif -->
				<view class="icon-box" @click="$refs.qrcode_popup.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
		<chat-tip></chat-tip>
	</view>
</template>

<script>
import { config } from "../common/config";
import myIcon from "../components/myIcon";
import sharePop from "../components/sharePop";
import shareTip from "../components/shareTip";
import uCharts from '@/components/u-charts/u-charts.js';
import myPopup from "../components/myPopup.vue"
import dingyue from "../components/dingyue.vue"
import tabBar from "../components/tabBar.vue"
	var _self;
	var canvasLineA=null;
export default {
	data() {
		return {
			cWidth:'650rpx',
			cHeight:'500rpx',
			pixelRatio:1,
            siteCity:'',
            params:{
                areaid:0,
                cate_id:0
            },
			siteUrl:'',
			tips:'',
			currentMonth:'',
			qrcode:"",
            currentIndex:0,
            currentIndexAuction:0,
            type:"dingyue",
            areas:[],
            auction:[],
            tableDate:[],
            categories:[],
			series:[{},{}],
			show_share_tip:false,
			share:{},
			tips:''
		};
	},
	components: {
		myIcon,
		sharePop,
        myPopup,
        tabBar,
		dingyue,
		shareTip
        
	},
	onLoad(options) {
		// this.bgcolor=config.imgDomain+'/images/new_icon/record/<EMAIL>'
		
		// this.getStatisData();
		_self = this;
		this.cWidth=uni.upx2px(650);
		this.cHeight=uni.upx2px(500);
		// this.getServerData();
		this.getData();
		uni.$on('getDataAgain',()=>{
			this.getData()
		})
	},
	onUnload(){
		uni.$off('getDataAgain')
	},
	onShow(){
		if(this.$store.state.updatePageData){
			this.getData()
			this.$store.state.updatePageData = false
		}
	},
	computed: {
		siteName() {
			return this.$store.state.siteName;
        },
        bgcolor(){
            return config.imgDomain+'/images/new_icon/record/<EMAIL>' 
		},
	},
	methods: {
		getData() {
			this.$ajax.get("build/tudiForm",this.params, (res) => {
                console.log(res.data);
				this.tableDate=[]
				this.type='dingyue'
                if (res.data.area.length>0){
					res.data.area.map(areas=>{
						if(areas.children.length>0){
							areas.hasChildren=true
						}else {
							areas.hasChildren=false
						}
					})
					this.areas=[{areaid:0,areaname:"全部",children: []}].concat(res.data.area)
                }
                if (res.data.auction){
                    this.auction=[{id:0,auc_name:"全部"}].concat(res.data.auction)
                }
                if (res.data.code==1){
                    res.data.data.month.map(item=>{
                    let obj ={
								date:item
							}
							this.tableDate.push(obj)
                    })
                    res.data.data.areas.map((item,index)=>{
                        item=item+'m²'
                        this.tableDate[index].area=parseInt(item) 
                    })
                    res.data.data.zongshu.map((item,index)=>{
                        item=item+'块'
                        this.tableDate[index].zongshu=parseInt(item)
                    })
                    this.categories=res.data.data.month
                    this.series[0].name="成交宗数"
                    this.series[0].color="#0f0"
                    this.series[0].data= res.data.data.zongshu
                    this.series[1].name="成交面积"
                    this.series[1].data= res.data.data.areas
                    _self.showLine("canvasLineA", this.series);
                }
				if (res.data.disclaimer){
					this.tips=res.data.disclaimer
				}
				if(res.data.siteCity){
					this.siteCity = res.data.siteCity;
				}
				if (res.data.share){
					this.share=res.data.share
				}else {
					this.share={
						title:'',
						content:''
					}
				}
				this.share.title=`${this.siteCity}土地拍卖数据统计【最新报表】`
				this.getWxConfig()
				// 获取登录状态
					this.$ajax.get('member/checkUserStatus', {}, res => {
						if (res.data.code !== 1) {
							this.$store.state.user_login_status = res.data.status
							if (this.$store.state.user_login_status==1){
								this.type="denglu"
								this.$store.state.updatePageData=true
								uni.setStorageSync('backUrl', window.location.href)
								this.showDingyuePop()
							}else if (this.$store.state.user_login_status==2){
								this.type='bangshouji'
								this.$store.state.updatePageData=true
								this.showDingyuePop()
							}
						}
					})
			});
		},
			showLine(canvasId,chartData,lineName){
				canvasLineA=new uCharts({
					$this:_self,
					canvasId: canvasId,
					type: 'area',
					padding:[15,15,4,0],
					fontSize:10,
					legend:{show:true},
					dataLabel:false,
					dataPointShape:true,
					background:'#FFFFFF',
					pixelRatio:_self.pixelRatio,
					categories: this.categories,
					series: this.series,
					animation: true,
					xAxis: {
						type:'grid',
						gridColor:'#CCCCCC',
						gridType:'dash',
						dashLength:1,
						fontSize:10,
						// boundaryGap:"justify"
					},
					yAxis: {
                        disabled:true,
						gridType:'dash',
						gridColor:'#CCCCCC',
						dashLength:8,
						splitNumber:5,
						// min:10,
						// max:180,
							
					},
					width: _self.cWidth*_self.pixelRatio,
					height: _self.cHeight*_self.pixelRatio,
					extra: {
						area:{
							type: 'curve',
						},
					}
				});
				if (lineName=="canvaLineA"){
					canvaLineA=lineNames
				}else if (lineName=="canvaLineB"){
					canvaLineB=lineNames
				}else if(lineName=="canvaLineC"){
					canvaLineC=lineNames
				}else if (lineName=="canvasLineJianzhuA"){
					canvasLineJianzhuA=lineNames
				}else if (lineName=="canvasLineJianzhuB"){
					canvasLineJianzhuB=lineNames
				}else if(lineName=="canvasLineJianzhuC"){
					canvasLineJianzhuC=lineNames
				}
				
			},
			touchLineA(e) {
				this.$nextTick(()=>{
					canvasLineA.showToolTip(e, {
					format: function (item, category) {
                        console.log(item);
						return category + ' ' + item.name + ':' + item.data
					}
				});
				})
				
			},
			showSharePop(){
				this.getShortLink()
				this.$refs.show_share_pop.show()
			},
			getShortLink(){
				this.link=window.location.origin+window.location.pathname
				this.$ajax.get('build/shortUrl.html', {page_url:this.link }, res=>{
					if(res.data.code === 1){
					this.link = res.data.short_url
					}
				})
			},
			
			// 复制分享链接
		copyLink(){
			this.show_share_tip=true
		},
		// 复制分享内容
		showCopywriting(){
			let link=''
			// #ifdef H5
			link=window.location.origin+window.location.pathname
			// #endif
			// #ifndef H5
			link=config.apiDomain+"/h5/statistics/tudi_statistics"
			// #endif
			const content = `【我正在看】${this.siteName}土地统计报表\n【链   接】${this.link}`
			this.copyText(content, ()=>{
					uni.showToast({
					title: '复制成功,去发送给好友吧',
					icon: 'none'
					})
			})
		},
		// 复制内容
		copyText(cont, callback) {
			let oInput = document.createElement('textarea')
			oInput.value = cont
			document.body.appendChild(oInput)
			oInput.select() // 选择对象;
			oInput.setSelectionRange(0, oInput.value.length);
			document.execCommand('Copy') // 执行浏览器复制命令
			oInput.blur()
			oInput.remove()
			if(callback) callback()
		},
		toLogin(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/login/login")
		},
		toBind(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/bind_phone/bind_phone")
		},
		// 订阅
		showDingyuePop(){
			this.$refs.dingyue.showPopup()
		},
        dingyue(){
            this.$ajax.get("build/subscribeBooking",{type:2},res=>{
                if (res.data.code ==-1){
					uni.setStorageSync('backUrl', window.location.href)
					this.$refs.dingyue.hide();
					this.$navigateTo("/user/login/login")
                }else if (res.data.code ==2){
					this.type='bangshouji'
					this.$refs.dingyue.hide();
					this.showDingyuePop()
                }else if(res.data.code ==1){
                    uni.showToast({
                        title:res.data.msg,
                        icon:"success"
                    })
                    setTimeout(() => {
                        this.$refs.dingyue.hide()
                    },500)
                }else if (res.data.code ==0){  //订阅失败
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                    if (res.data.gzhewm){
                        this.qrcode=res.data.gzhewm
                        setTimeout(() => {
                            this.$refs.qrcode_popup.show()
                        }, 500);
					}
					this.$refs.dingyue.hide()

                }
            },err=>{},{disableAutoHandle:true})
		},
		 // 保存二维码
        saveQrcode(){
            uni.request({
                url:this.qrcode,
                method:'GET',
                responseType: 'arraybuffer',
                success:(res)=>{
                    let base64 = uni.arrayBufferToBase64(res);
                    const userImageBase64 = 'data:image/jpg;base64,' + base64;
                    uni.saveImageToPhotosAlbum({
                        filePath: userImageBase64,
                        success: result => {
                            uni.showToast({
                                title: '保存成功，在微信从相册中选取识别吧',
                                icon: 'none',
                                duration: 4000
                            })
                        },
                        fail: err => {
                            console.log(err)
                            uni.showToast({
                                title: '保存失败，请重试',
                                icon: 'none'
                            })
                        }
                    })
                }
            }); 
        },
		clickTab(e,type,arr){
            // console.log(e);
            if (type==1){
                this.currentIndexAuction=e.detail.current
				this.params.cate_id=e.detail.value
				this.getData()
            }else if (type==2){
				this.currentIndex=e.detail.current
				this.params.areaid=e.detail.value
				this.getData()
            }else if (type==3){
				this.currentIndex=e.detail.current
            }
			
			
		},
		changeTab(e,arr){
			console.log(e);
			this.params.areaid=arr[e.detail.value].areaid
			this.getData()
		}
	},
	onShareAppMessage() {
		if (this.share) {
			return {
				title: this.share.title || "",
				content: this.share.content || "",
				imageUrl: this.share.pic ? formatImg(this.share.pic, "w_6401") : "",
			};
		}
	},
	onNavigationBarSearchInputConfirmed(e) {
		this.handleSearch(e.text);
	},
};
</script>

<style scoped lang="scss">
.page {
	background: #fff;
}
.header {
	width: 100%;
	height: 400rpx;
	background-image: linear-gradient(0deg, #f7918f 0%, #fb656a 100%);
	display: flex;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	padding: 48rpx 48rpx 352rpx;;
	box-sizing: border-box;
	position: relative;
	.share_icon{
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 24rpx;
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		background-color: rgba(0,0,0, 0.5);
		position: absolute;
		top: 48rpx;
		right: 48rpx;
}
	.head-info {
		width: 100%;
	}
	.title {
		font-size: 80rpx;
		color: #FFFFFF;
		margin:0 0 32rpx;
	}
	.update-tip {
		margin-top: 16upx;
		color: #fff;
	}
}
.yushou-data-info{
	position: relative;
	top: -20px;
	background: #fff;
	border-radius: 48rpx 48rpx 0 0;
	padding: 20px 48rpx;
	.qiun-title-dot-light{
		font-size: 40rpx;
		color: #333;
		font-weight: bold;
	}
	.mianji{
		margin-top: 48rpx;
	}
	.tips{
		justify-content: space-between;
		align-items: center;
		margin-top: 48rpx;
		margin-bottom: 24rpx;
		.qiun-title{
			font-size: 28rpx;
			color: #333333;
		}
		.qiun-title-tips{
			font-size: 22rpx;
			color: #999999;
		}
	}
}
.qiun-charts {
		width: 650upx;
		height: 500upx;
		background-color: #FFFFFF;
		// background-image: url("https://images.tengfangyun.com/images/new_icon/record/<EMAIL>");/
	}
	
	.charts {
		width: 650upx;
		height: 500upx;
		background-color: #FFFFFF;
		// background-image: url("https://images.tengfangyun.com/images/new_icon/record/<EMAIL>");
		// background-size: 50% 50%;
		// background-position: 50% 30%;
		// background-repeat: no-repeat;
	}

.friend-tips{
	margin-top: 60rpx;
	color: #999;
	line-height: 1.5;
}
.dingyue{
	margin-left: auto;
	align-items: center;
	border: 2rpx solid #999;
	border-radius: 8rpx;
	padding: 4rpx 8rpx;;

	.text{
		margin-left: 10rpx;
	}
}
//公众号二维码弹框
.qrcode-box{
	position: relative;
	margin-top: 15vh;
	.img-box{
		width: 584rpx;
		padding: 12rpx;
		margin: auto;
		background-color: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		.title{
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
			color: #333;
			&.titles{
				margin-top: 36rpx;
			}
		}
		.tip{
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			color: #666;
			&.red{
				padding-bottom: 8rpx;
				color: #f00;
			}
		}
	}
	.qrcode{
		width: 560rpx;
		height: 560rpx;
	}
	.icon-box{
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
}
.middle-bar {
    padding: 24rpx 48rpx ;
    .yongtu{
        margin-bottom: 24rpx;
        align-items: center;
        .yongtu-con{
            width: 116rpx;
            margin-right: 24rpx;
        }
        .yongtu-tabs{
            white-space: nowrap;
            overflow: auto;
            .cate{
                align-items: flex-end;
                max-width: 100%;
            }
            picker {
                color: #333;
				display: inline-block;
                transition: 0.2s;
                padding-right: 20rpx;
                &.active{
                    color: #FB656A;
                }
                &~picker{
                    padding-left: 20rpx;
                }
            }
			text {
                color: #333;
                transition: 0.2s;
                padding-right: 20rpx;
                &.active{
                    color: #FB656A;
                }
                &~text{
                    padding-left: 20rpx;
                }
            }
        }

    }
	.middle-bar-info{
		background: #f8f8f8;
		border-radius: 44rpx;

	}
	.mid-bar {
		flex: 1;
		justify-content: center;
		align-items: center;
		padding: 20rpx 0;
		&.active{
			background-image: linear-gradient(57deg, #FF5500 27%, #FFA402 80%);
			box-shadow: 0 2px 6px 0 rgba(255,145,1,0.50);
			border-radius: 22px;
			.mid-bar-info{
				color: #fff;
			}
		}
	}
}
.tab-bars{
    padding: 0 48rpx;
    .cate{
        align-items: flex-end;
        max-width: 100%;
    }
    text {
        color: #333;
        transition: 0.2s;
        padding-right: 20rpx;
        &.active{
            font-weight: bold;
            font-size: 40rpx;
        }
        &~text{
            padding-left: 20rpx;
        }
    }
}
.desc_table{
		.desc-title{
			margin-top: 20upx;
		}
		table{
			text-align: center;
			border-collapse: collapse;
			color: #787878;
			padding: 20upx 40upx;
			// #ifdef H5
			width: 100%;
			margin:20rpx auto;
			//  #endif
			thead{
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-collapse: collapse;
				// #ifndef H5
				border-top: 2upx solid #e5e5e5;
					//#endif
				border-left: 2upx solid #e5e5e5;
				border-right: 2upx solid #e5e5e5;
				width: 100%;
					// #ifndef H5
				box-sizing: border-box;
				//#endif
				background: #eee;
				tr{
					flex: 1;
					width: 100%;
					border-collapse: collapse;
					th{
						border-left: 2upx solid #e5e5e5;
						border-top: 2upx solid #e5e5e5;
					}
				}
                .month{
                    width: 25%;
                    padding: 10upx;
                    border-right: 2upx solid #e5e5e5;
                }
                .price{
                    width: 45%;
                    border-right: 2upx solid #e5e5e5;
                        padding: 10upx;
                }
                .desc{
                    width: 45%;
                    padding: 10upx;
                }
			}
			tbody{
                border-collapse: collapse;
                border-top: 2upx solid #e5e5e5;
				border-left: 2upx solid #e5e5e5;
				border-right: 2upx solid #e5e5e5;
				tr{
					border-collapse: collapse;
					display: flex;
					// flex-direction: column;
					// justify-content: space-between;
					align-items: center;
				   border-bottom: 2upx solid #e5e5e5;
					width: 100%;
					td{
						 border-right: 2upx solid #e5e5e5;
						 padding: 10upx;
						 border-right: 2upx solid #e5e5e5;
					//  border-bottom: 2upx solid #666;
					}
					td:first-child{
						width: 25%;
					}
					td:nth-child(2){
						width: 45%;
					}
					td:nth-child(3){
						width: 45%;
						border-right: 0upx ;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
				
		
			}

        }
    }
</style>
