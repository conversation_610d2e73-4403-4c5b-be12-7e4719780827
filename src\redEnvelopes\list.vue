<template>
  <view @touchstart="firstPlayingAudio" :style="{ minHeight: '100vh', backgroundColor: bgColor }">
    <view class="audio-box" :class="{rotate:audio_playing}" @click="switchAudio()" v-if="music">
      <icons type="yinyue" size="20" color="#fff"></icons>
    </view>
    <view class="top">
      <image :src="top_img | imgUrl('w_8601')" mode="widthFix"></image>
      <view class="share" @click="openShare()">
        <image :src="share_img" mode="aspectFit"></image>
      </view>
    </view>
    <view class="top-list">
        <view class="list-box">
          <view class="box-title">红包大厅</view>
          <view v-for="(item, index) in infoList" :key="index" class="info-content">
            <view class="info-content-left">
              <image :src="item.info_pic" mode="aspectFill"></image>
            </view>
            <view class="info-content-right">
              <view class="info-name">{{item.info_title}}</view>
              <view class="info-hb" @click="openHb(item)">
                <image class="info-hb-bg" :src="item.hb_time_status == 1 ? hb_bg1 : hb_bg2" mode="aspectFill"></image>
                <view class="hb-content">
                  <image class="hb-icon" :src="item.hb_time_status == 1 ? hb_close : hb_open" mode="aspectFill"></image>
                  <view>
                    <view class="hb-title">{{item.info_desc}}</view>
                    <view class="count" v-if="item.limit_hb_count">{{item.limit_hb_count}}份/拼手气</view>
                  </view>
                </view>
                <view class="hb-line"></view>
                <view class="hb-time">
                  <view class="time-left">{{item.hb_time}}</view>
                  <view class="time-right" v-if="item.hb_time_status == 0">未开始</view>
                  <view class="time-right" v-if="item.hb_time_status == 1">进行中</view>
                  <view class="time-right" v-if="item.hb_time_status == 2">已结束</view>
                </view>
                <!-- <view class="hb-time" v-if="item.hb_time_status == 0">未开始</view>
                <view class="hb-time" v-if="item.hb_time_status == 1">
                  <text v-if="item.start_time">{{item.start_time}}-</text>
                  <text v-if="item.end_time">{{item.end_time}}</text>
                </view>
                <view class="hb-time" v-if="item.hb_time_status == 2">已结束</view> -->
              </view>
            </view>
          </view>
          <view v-if="get_status == 'more'" class="load-more" @click="getData">点击加载更多</view>
          <uni-load-more v-show="get_status != 'more'" :status="get_status" :content-text="content_text"></uni-load-more>
        </view>
        <view class="article-content" v-html="introduce"></view>
      </view>
      <my-popup ref="linghb_popup" position="top">
      <view class="ling-popup">
        <view class="ling-popup-top">
          <view class="ling-top-info">
            <image v-if="current_data.info_pic" class="ling-info-icon" :src="current_data.info_pic" mode="aspectFill"></image>
            <text class="ling-info-text">{{current_data.info_title}}</text>
          </view>
          <!-- <view class="ling-top-text">扫码领红包</view> -->
          <view class="ling-top-text1">塞了{{current_data.limit_hb_count}}份红包</view> 
          <image class="hb-code" :src="hbCode" mode="aspectFill"></image>
          <view class="ling-top-tips">长按识别二维码</view>
          <!-- <view class="ling-top-tips1">拼手气领红包</view> -->
        </view>
        <view class="ling-popup-btm">
          <view class="ling-btn" v-if ="current_data.virtual_money" >参与瓜分<text class = "orange">￥{{current_data.virtual_money}}</text>元现金红包</view>
          <!-- 没有现金红包 -->
          <view v-else  class="ling-btn" ><text class = "orange">拼手气领红包</text></view>
        </view> 
        <!-- <view class="ling-popup-bottom">
          <view class="ling-btn" @click="lingHb">開</view>
        </view> -->
        <view class="ling-popup-close" @click="$refs.linghb_popup.hide()">
          <my-icon type="guanbi" color="#fff" size="56rpx"></my-icon>
        </view>
      </view>      
    </my-popup>
    <share-pop ref="share_popup" @copyLink="show_share_tip=true" @appShare="appShare" @handleCreat='handleCreat' @showCopywriting="copyLink" :showHaibao="false"></share-pop>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
  </view>
</template>

<script>
import { config, formatImg } from '../common/index'
import myPopup from '../components/myPopup.vue'
import myIcon from '../components/myIcon.vue'
import { uniLoadMore } from '@dcloudio/uni-ui'
import sharePop from '../components/sharePop'
import shareTip from '../components/shareTip.vue'
import icons from '../components/icon.vue'
export default {
  components: {myPopup, myIcon, uniLoadMore, sharePop, shareTip, icons},
  filters: {
      imgUrl(val, param = "") {
          return formatImg(val, param)
      }
  },
  data() {
    return {
      image_domain: config.imgDomain,
      top_img: '',
      share_img: config.imgDomain + '/hongbao/share_btn.png?x-oss-process=style/m_240',
      hb_bg1: config.imgDomain + '/hongbao/hb_bg1.png?x-oss-process=style/m_240',
      hb_bg2: config.imgDomain + '/hongbao/hb_bg2.png?x-oss-process=style/m_240',
      hb_close: config.imgDomain + '/hongbao/hb_close.png?x-oss-process=style/m_240',
      hb_open: config.imgDomain + '/hongbao/hb_open.png?x-oss-process=style/m_240',
      infoList: [],
      hbCode: '',
      params: {
        page: 1,
      },
      get_status: 'loading',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了',
      },
      show_share_tip: false,
      current_data: {
        info_pic: '',
        info_title: '',
      },
      audio_playing: false,
      music: '',
      bgColor: '',
      introduce: '',
    }
  },
  onLoad() {
    this.getData()
  },
  onHide(){
      this.innerAudioContext&&this.innerAudioContext.pause()
  },
  onShow(){
      if(this.audio_playing){
          this.innerAudioContext.play()
      }
  },
  onUnload(){
      if (this.innerAudioContext){
          this.innerAudioContext.destroy()
      }
  },
  methods: {
    getData() {
      this.get_status = 'loading'
      this.$ajax.get('wx_money/pingHbInfoList', this.params, (res) => {
        if (res.data.code == 1) {
          if (res.data.page == 1) {
            this.infoList = res.data.list
          } else {
            this.infoList = this.infoList.concat(res.data.list)
          }
          if (res.data.list.length == 0) {
            this.get_status = 'noMore'
          } else {
            this.get_status = 'more'
          }
          if(res.data.music){
            this.music = res.data.music
            this.playAudio(this.music)
          }
          if (res.data.bgcolor) {
            this.bgColor = res.data.bgcolor || '#fe4b3f'
          }
          this.params.page = res.data.page + 1
          this.top_img = res.data.subject_pic
          this.share = {
            title: res.data.share.title || '',
            content: res.data.share.content || '',
            pic: res.data.share.pic || ''
          }
          this.share.link = window.location.href
          this.getWxConfig()
          this.introduce = res.data.introduce
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    openHb(info) {
      let params = {
        id: info.info_id,
        type: info.info_type || 19,
        hb_info_id:info.id
      }
      if (params.type == 3) {
        params.type = 15 + info.parentid
      }
      if (params.type == 2 && info.parentid == 2) {
        params.type = 3
      }
      if (info.hb_time_status == 1) {
        this.$ajax.get('wx_money/pingHbQrcode',params, (res) => {
          if (res.data.code == 1) {
            // this.current_data.info_pic = info.info_pic
            // this.current_data.info_title = info.info_title
            this.current_data =  res.data.hb_info
            this.hbCode = res.data.url
            this.$refs.linghb_popup.show()
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
          }
        })
      } else if (info.hb_time_status == 0) {
        uni.showToast({
          title: '该红包未开始',
          icon: 'none'
        })
      } else if (info.hb_time_status == 2) {
        uni.showToast({
          title: '该红包已结束',
          icon: 'none'
        })
      }
    },
    lingHb() {
      uni.showToast({
        title: '扫码领红包',
        icon: 'none'
      })
    },
    openShare() {
      this.$refs.share_popup.show()
    },
    // #ifdef H5
    copyLink(){
      let link = window.location.href
      this.copyWechatNum(link, ()=>{
        uni.showToast({
          title: '复制成功,去发送给好友吧',
          icon: 'none'
        })
      })
    },
    // #endif
    // #ifndef H5
    copyWechatNum(cont) {
      uni.setClipboardData({
        data: cont,
        success: res => {
          // uni.showToast({
          //   title: "复制成功",
          //   icon: "none"
          // })
        }
      })
    },
    // #endif
    // #ifdef H5
    copyWechatNum(cont, callback) {
      let oInput = document.createElement('textarea')
      oInput.value = cont
      document.body.appendChild(oInput)
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      oInput.blur()
      oInput.remove()
      if(callback) callback()
    },
    // #endif
    async playAudio(src){
      this.innerAudioContext = uni.createInnerAudioContext();
      // this.innerAudioContext.autoplay = true;
      this.innerAudioContext.loop = true;
      this.innerAudioContext.src = src;
      this.innerAudioContext.onPlay(() => {
        console.log('开始播放');
        this.audio_playing = true
      });
      this.innerAudioContext.onError((res) => {
        console.log("播放失败")
        console.log(res.errMsg);
        console.log(res.errCode);
      });
      this.innerAudioContext.play()
    },
    firstPlayingAudio() {
      if (this.isClicked) return 
      if (!this.audio_playing){
          this.innerAudioContext.play()
          this.isClicked =true
      }else{
          this.isClicked =true
      }
    },
    switchAudio(){
      if(this.audio_playing){
        this.innerAudioContext.pause()
      }else{
        this.innerAudioContext.play()
        if(this.videoContext){
          this.videoContext.pause()
        }
      }
      this.audio_playing = !this.audio_playing
    },
  },
  // onReachBottom() {
  //   if (this.get_status === 'noMore') {
  //     return
  //   } else {
  //     this.getData()
  //   }
  // },
}
</script>
<style lang="scss" scoped>
.top {
  position: relative;
  &>image {
    width: 100%;
    display: block;
  }
  .guize {
    position: absolute;
    top: 248rpx;
    right: 0;
    width: 144rpx;
    height: 52rpx;
    border-radius: 26rpx 0px 0px 26rpx;
    background: linear-gradient(100.56deg, #EBD097 0%, #DFB56A 100%);
    box-shadow: 0px 8rpx 8rpx 0px #0000003F;
    display: flex;
    justify-content: center;
    align-items: center;
    text {
      color: #7C3800;
      font-size: 28rpx;
    }
    image {
      width: 26rpx;
      height: 26rpx;
      margin-left: 6rpx;
    }
  }
  .share {
    position: fixed;
    top: 652rpx;
    right: 38rpx;
    z-index: 3;
    image {
      width: 90rpx;
      height: 90rpx;
    }
  }
}
.top-list {
    position: relative;
    margin-bottom: 50rpx;
    .list-box {
      width: 654rpx;
      border-radius: 10rpx;
      background: linear-gradient(-0.54deg, #FFDEBE 0%, #FFFFFF 100%);
      box-shadow: 0px 4rpx 30rpx 0px #FF856E33;
      z-index: 2;
      padding-top: 42rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 auto;
      padding-bottom: 30rpx;
    }
    .box-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .num {
        font-size: 36rpx;
        color: #FF1717;
        font-weight: bold;
      }
      .desc {
        margin-top: 18rpx;
        font-size: 28rpx;
        color: #7C3800;
        font-weight: bold;
      }
    }
    .box-line {
      width: 1rpx;
      height: 66rpx;
      background: #d2d2d2;
    }
    .box-title {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 0 0 20rpx 20rpx;
      background: linear-gradient(173.97deg, #FFE9BC 0%, #FFCF7A 100%);
      width: 224rpx;
      height: 56rpx;
      line-height: 56rpx;
      text-align: center;
      color: #472204;
      font-size: 24rpx;
      font-weight: bold;
    }
    .info-content {
      display: flex;
      margin-top: 48rpx;
      .info-content-left>image {
        width: 72rpx;
        height: 72rpx;
        border-radius: 10rpx;
      }
      .info-content-right {
        margin-left: 12rpx;
        .info-name {
          color: #472204;
          font-size: 22rpx;
          font-weight: bold;
          width: 460rpx;
          overflow: hidden;
          text-overflow: ellipsis;    
          white-space: nowrap;
          padding-left: 16rpx;
          padding-bottom: 4rpx;
        }
        .info-area {
          font-size: 22rpx;
          color: #472204;
          margin-top: 8rpx;
        }
        .info-price {
          font-size: 22rpx;
          color: #E50000;
          margin-top: 16rpx;
          &>text {
            font-size: 26rpx;
            font-weight: bold;
            margin-left: 6rpx;
          }
        }
        .info-hb {
          position: relative;
          .info-hb-bg {
            width: 476rpx;
            height: 168rpx;
          }
          .hb-content {
            position: absolute;
            left: 48rpx;
            top: 20rpx;
            font-size: 28rpx;
            color: #fff;
            display: flex;
            align-items: center;
            .hb-icon {
              width: 60rpx;
              height: 72rpx;
              margin-right: 18rpx;
              flex-shrink: 0;
            }
            .hb-title {
              width: 334rpx;
              overflow: hidden;    
              text-overflow:ellipsis;    
              white-space: nowrap;
            }
            .count {
              font-size: 22rpx;
              margin-top: 10rpx;
            }
          }
          .hb-line {
            position: absolute;
            left: 40rpx;
            bottom: 60rpx;
            width: 420rpx;
            height: 1rpx;
            background: #fff;
            opacity: 0.5;
          }
          .hb-time {
            position: absolute;
            bottom: 10rpx;
            height: 44rpx;
            line-height: 44rpx;
            padding-left: 40rpx;
            font-size: 20rpx;
            color: #fff;
            display: flex;
            justify-content: space-between;
            width: 420rpx;
            view {
              font-size: 20rpx;
            }
          }
        }
      }
    }
  }
.ling-popup {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 584rpx;
  height: 964rpx;
  border-radius: 20rpx;
  .ling-popup-top {
    position: relative;
    width: 100%;
    height: 782rpx;
    overflow: hidden;
    z-index: 2;
    &::after {
      position: absolute;
      width: 140%;
      height: 782rpx;
      left: -20%;
      top: 0;
      z-index: -1;
      content: '';
      border-radius: 0 0 50% 50%;
      background: #E16754;
      box-shadow: 0px 4rpx 8rpx 0px #00000026;
    }
    .ling-top-info {
      position: absolute;
      top: 50rpx;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      .ling-info-icon {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
      }
      .ling-info-text {
        font-size: 30rpx;
        font-weight: bold;
        color: #E5CD9F;
      }
      .ling-info-tips {
        font-size: 30rpx;
        color: #E5CD9F;
      }
    }
    .ling-top-title {
      position: absolute;
      top: 80rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 30rpx;
      color: #E5CD9F;
      width: 90%;
      text-align: center;
      overflow: hidden;    
      text-overflow:ellipsis;    
      white-space: nowrap;
    }
    .ling-top-text {
      position: absolute;
      top: 220rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 44rpx;
      color: #E5CD9F;
    }
    .ling-top-text1 {
      position: absolute;
      top: 220rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 30rpx;
      color: #E5CD9F;
    }
    .hb-code {
      position: absolute;
      top: 312rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 320rpx;
      height: 320rpx;
    }
    .ling-top-tips {
      position: absolute;
      top: 700rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 30rpx;
      color: #E5CD9F;
      width: 100%;
      text-align: center;
    }
    .ling-top-tips1 {
      position: absolute;
      top: 700rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 30rpx;
      color: #E5CD9F;
      width: 100%;
      text-align: center;
    }
  }
  .ling-popup-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 300rpx;
    background: #E0604C;
    .ling-btn {
      position: absolute;
      bottom: 100rpx;
      left: 50%;
      transform: translate(-50%, 0) rotateY(0deg);
      width: 176rpx;
      height: 176rpx;
      border-radius: 50%;
      line-height: 176rpx;
      text-align: center;
      background: #E6CD9F;
      box-shadow: 0px 4rpx 8rpx 0px #0000003F;
      color: #454545;
      font-size: 52rpx;
      z-index: 9;
    }
  }
  .ling-popup-btm{
    margin-top: 40rpx;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 300rpx;
    background: #E0604C;
    .ling-btn {
      position: absolute;
      bottom: 174rpx;
      left: 50%;
      transform: translate(-50%, 0) rotateY(0deg);
      width: 100%;
      text-align: center;
      font-size: 24rpx;
      color:  #fff;
      z-index: 9;
      .orange{
        color: #E5CD9F;
        font-size: 32rpx;
        font-weight: 600;
      }
    }
  }
  .ling-popup-close {
    position: absolute;
    bottom: -104rpx;
    left: 50%;
    transform: translateX(-50%);
  }
}
.audio-box{
  position: fixed;
  right: 24rpx;
  top: 60rpx;
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background-color: rgba($color: #333, $alpha: 0.6);
  z-index: 5;
  color: #fff;
  text-align: center;
  line-height: 70rpx;
  // right: calc(50vw - 196px);
  &.rotate{
      animation:rotate 3s linear infinite
  }
  .icon-yinyue{
      line-height: 70upx;
  }
}
.load-more {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #777777;
}
.article-content {
  padding: 48rpx;
  img {
    max-width: 100%;
  }
}
@keyframes rotate{
  0%{-webkit-transform:rotate(0deg);}
  25%{-webkit-transform:rotate(90deg);}
  50%{-webkit-transform:rotate(180deg);}
  75%{-webkit-transform:rotate(270deg);}
  100%{-webkit-transform:rotate(360deg);}
}
@-webkit-keyframes rotate{
  0%{-webkit-transform:rotate(0deg);}
  25%{-webkit-transform:rotate(90deg);}
  50%{-webkit-transform:rotate(180deg);}
  75%{-webkit-transform:rotate(270deg);}
  100%{-webkit-transform:rotate(360deg);}
}
</style>