<template>
  <view class="exhibition" :class="{ in_pc: !$store.state.in_mobile }">
    <view class="audio-box" :class="{rotate:audio_playing}" @click="switchAudio()" v-if="meeting.audio_path">
      <icons type="yinyue" size="20" color="#fff"></icons>
    </view>
    <view style="min-height: 100vh" :style="{ backgroundColor: meeting.body_bg || '#fff' }">
      <view class="top" :style="{height: !meeting.top_bg&&meeting.body_bg?'560rpx': ''}">
        <image :src="meeting.top_bg" mode="widthFix"></image>
        <view class="header_content" v-if="!meeting.top_bg">
          <view class="date">
            <view class="date-year">{{meeting.year}}</view>
            <view class="date-range">
              <view class="date-start">{{meeting.month}}/{{meeting.day}}</view>
              <view class="date-end">{{meeting.month2}}/{{meeting.day2}}</view>
            </view>
          </view>
          <view class="title">{{ meeting.title }}</view>
          <view class="company" v-if="meeting.organizer">主办单位：{{ meeting.organizer }}</view>
        </view>
        <view class="data" :class="{no_border: meeting.body_bg !==''}">
          <view class="data-share">
            <view class="num">{{ meeting.share_num }}</view>
            <view class="name">分享</view>
          </view>
          <view class="line"></view>
          <view class="data-visit">
            <view class="num">{{ meeting.hit }}</view>
            <view class="name">访问量</view>
          </view>
          <view class="line"></view>
          <view class="data-house">
            <view class="num">{{ meeting.buildCount }}</view>
            <view class="name">楼盘</view>
          </view>
        </view>
      </view>
      <view class="search" :class="{no_border: meeting.body_bg !==''}">
        <view class="search-icon"><my-icon size="48rpx" type="ic_sousuo" color="#999"></my-icon></view>
        <input type="text" confirm-type="search" v-model="keyword" placeholder-style="color: #999" :disabled="search_disabled" @confirm="handleSearch()" placeholder="请输入楼盘名称" />
      </view>
      <view class="recommend" :class="{ has_bg_color: meeting.body_bg }" @click="toIntroduce">
        <view class="content_text">{{ meeting.desc }}</view>
        <view class="more">查看详情 <my-icon type="ic_into" size="26rpx" :color="meeting.body_bg?'#fff':'#353535'"></my-icon></view>
      </view>
      <view v-if="meeting.sys_video_path||meeting.out_video_path" class="video_container" :class="{no_border: meeting.body_bg !==''}">
        <view v-show="show_video">
          <video v-if ='meeting.sys_video_path' :src="meeting.sys_video_path" @play ="playVideo" @error="videoErrorCallback" :poster="meeting.sys_video_path | imageFilter" controls x5-playsinline></video>
          <iframe v-else-if ="meeting.out_video_path" width='100%' :src="meeting.out_video_path" frameborder="0"></iframe>
        </view>
      </view>
      <template v-if="meeting.if_show_index_news">
        <view class="news_content" :class="{has_bg: meeting.body_bg !==''}" v-if="meeting.news_content">
          <view class="news_title">最新动态</view>
          <view class="article-content" v-html="meeting.news_content"></view>
        </view>
        <News v-else-if="news_list.length>0" :has_bg_color="meeting.body_bg !== ''" :list="news_list" />
      </template>
      <view id="build_container">
        <Build :bg_color="meeting.body_bg" :empty_height="build_list_height" :cates="build_cates" :list="build_list"
        :buildCon="currentBuildCon"
         :ex_id="id" @switchCate="onBuildCateChange" @sign="onBuildSign" />
      </view>
      <uni-load-more v-if ="currentBuildCon.display_type==0" :status="get_status" :color="meeting.body_bg?'#fff':'#999'" :content-text="content_text"></uni-load-more>
      <view class="empty" :style="{height: build_list_height}"></view>
    </view>
    <sub-form
      groupCount=""
      sub_title="在线报名，获取优惠"
      sub_content="为方便通知到您最新的信息，请输入您的手机号码"
      :sub_type="3"
      :sub_mode="0"
      ref="sub_form"
      @onsubmit="handleSubForm"
      @close="show_video = true"
      :login_status="login_status"
    ></sub-form>
    <view class="sign_btn" @click="showSubForm()" v-if="meeting.index_booking">
      <image :src="'/exhibition/icon/sign_btn.png' | imageFilter('m_120')" mode="widthFix"></image>
    </view>
    <BottomBar pagePath="/exhibition/index" :list="meeting.nav" :query="query" :id="id"/>
  </view>
</template>

<script>
import myIcon from "../components/myIcon.vue";
import icons from '../components/icon.vue'
import News from "./components/News.vue";
import Build from "./components/Build.vue";
import BottomBar from "./components/BottomBar.vue";
import {uniLoadMore} from '@dcloudio/uni-ui'
import subForm from '../components/subForm'
export default {
  data() {
    return {
      id: "",
      query: "",
      is_share: '',
      meeting: {
        year: "",
        body_bg: "",
        top_bg: "",
        title: "",
        organizer: "",
        show_time_begin: "",
        show_time_end: "",
        share_num: "",
        hit: "",
        buildCount: "",
        desc: "",
        index_booking: 0
      },
      keyword: "",
      page: 1,
      rows: 20,
      news_list: [],
      build_cates: [],
      build_list: [],
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了",
      },
      audio_playing: false,
      build_list_height: '0',
      show_video: true,
      // search_focus: false,
      search_disabled: false,
      build_container_top: 0,
      scroll_top: 0,
      currentBuildCon:{}
    };
  },
  components: {
    myIcon,
    icons,
    News,
    Build,
    BottomBar,
    uniLoadMore,
    subForm,
  },
  computed: {
    login_status() {
      return this.$store.state.user_login_status
    },
    sub_mode() {
      return this.$store.state.sub_form_mode
    }
  },
  onLoad(options) {
    if(options.is_share){
      this.is_share = 1
    }
    if (options.id) {
      this.id = options.id;
      this.query = "?id=" + this.id;
      this.getData(options.id);
    }
  },
  onShow(){
    if(this.audio_playing&&this.innerAudioContext){
      this.innerAudioContext.play()
    }
  },
  methods: {
    getData(id) {
      this.$ajax.get("buildShow/meetings", { id, is_share:this.is_share }, (res) => {
        if (res.data.code === 1) {
          this.share = {
            title: res.data.meetings.share_title||res.data.meetings.title||'',
            content: res.data.meetings.share_content||res.data.desc||'',
            pic: res.data.meetings.share_pic||'',
            link: window.location.href+'&is_share=1'
          }
          this.getWxConfig()
          var body_color = res.data.meetings.body_bg.toLowerCase()
          if(body_color==='#fff'||body_color==='#ffffff'){
            res.data.meetings.body_bg = ''
          }
          this.meeting = res.data.meetings;
          if(this.meeting.title){
            uni.setNavigationBarTitle({
              title: this.meeting.title
            })
          }
          if(this.meeting.audio_path){
            this.playAudio(this.meeting.audio_path)
          }
          this.meeting.year = res.data.meetings.show_time_begin.split('-')[0]
          this.meeting.month = res.data.meetings.show_time_begin.split('-')[1]
          this.meeting.day = res.data.meetings.show_time_begin.split('-')[2]
          this.meeting.month2 = res.data.meetings.show_time_end.split('-')[1]
          this.meeting.day2 = res.data.meetings.show_time_end.split('-')[2]
          this.news_list = res.data.news||[];
          this.build_cates = res.data.cates;
          if (res.data.cates.length){
            this.onBuildCateChange(0,res.data.cates[0])
          }
          if (!this.build_cateid&&this.build_cates.length) {
            this.build_cateid = this.build_cates[0].id;
          }
          // this.getBuildList(this.build_cateid);
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      });
    },
    handleSearch(){
      // this.search_focus = false
      this.search_disabled = true
      setTimeout(()=>{
        this.search_disabled = false
      }, 300)
      this.setEmptyHeight()
      this.page = 1
      this.getBuildList(this.build_cateid, ()=>{
        var query = uni.createSelectorQuery().in(this)
        query.select('#build_container').boundingClientRect(data => {
          this.build_container_top = data.top
        }).exec();
        uni.pageScrollTo({
          scrollTop: this.build_container_top+this.scroll_top,
          duration: 200
        })
      })
    },
    getBuildList(cate_id, call_back) {
      this.get_status = "loading"
      if(this.page === 1){
        this.build_list = []
      }
      this.$ajax.get("buildShow/getBrandBuild", {id: this.id, cate_id, page: this.page, rows: this.rows, keywords: this.keyword }, (res) => {
        this.build_list_height = '0'
        if (res.data.code === 1) {
          this.build_list = this.build_list.concat(res.data.data);
          this.$nextTick(()=>{
            call_back&&call_back()
          })
          // this.get_status = "more"
          // 接口目前不支持分页，先设置noMore
          this.get_status = "noMore"
        }else{
          this.get_status = "noMore"
          call_back&&call_back()
        }
      });
    },
    onBuildCateChange(index,item) {
      this.build_cateid = this.build_cates[index].id;
      this.currentBuildCon = item
      if(this.currentBuildCon.display_type==1){
        this.$navigateTo(this.currentBuildCon.jump_link)
        return 
      }
      
      if (this.currentBuildCon.display_type==0) {
        this.page = 1
        this.getBuildList(this.build_cateid);
      }
      this.$nextTick(()=>{
        this.setEmptyHeight()
      })
    },
    setEmptyHeight(){
      var query = uni.createSelectorQuery().in(this)
      query.select('#build_list').boundingClientRect(data => {
        // console.log(data.height);
        this.build_list_height = data.height+'px'
      }).exec();
    },
    toIntroduce(){
      this.$navigateTo('/exhibition/introduce'+this.query)
    },
    showSubForm(){
      this.current_build_id = ''
      this.show_video = false
      this.$refs.sub_form.showPopup()
    },
    onBuildSign(e){
      this.current_build_id = e.id
      this.show_video = false
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e){
      var params = {
        meeting_id: parseInt(this.id),
        info_id: this.current_build_id||'',
        cname: e.name,
        tel: e.tel
      }
      this.$ajax.post('buildShow/signUp',params, res=>{
        uni.hideLoading()
        if(res.data.code === 1){
          uni.showToast({
            title: res.data.msg||'报名成功',
            mask: true
          })
          this.$refs.sub_form.hide()
        }else{
          uni.showToast({
            title: res.data.msg||'报名失败',
            icon: 'none',
            mask: true
          })
        }
      })
    },
    playAudio(src){
      this.innerAudioContext = uni.createInnerAudioContext();
      // #ifdef MP
      this.innerAudioContext.obeyMuteSwitch = false; //为false时即使用户打开了静音开关，也能继续发出声音
      // #endif
      this.innerAudioContext.autoplay = true;
      this.innerAudioContext.loop = true;
      this.innerAudioContext.src = src;
      this.innerAudioContext.onPlay(() => {
        console.log('开始播放');
        this.audio_playing = true
      });
      this.innerAudioContext.onError((res) => {
        console.log("播放失败")
        console.log(res.errMsg);
        console.log(res.errCode);
      });
    },
    switchAudio(){
      if(this.audio_playing){
        this.innerAudioContext.pause()
      }else{
        this.innerAudioContext.play()
        if(this.videoContext){
          this.videoContext.pause()
        }
      }
      this.audio_playing = !this.audio_playing
    },
    playVideo(e){
      this.innerAudioContext&&this.innerAudioContext.pause()
      this.audio_playing=false
    },
    videoErrorCallback(){
      console.log('视频播放失败')
    },
  },
  onPageScroll(e){
    this.scroll_top = e.scrollTop
  },
  onReachBottom(){
    if(this.get_status==='more'){
      this.page++
      this.getBuildList(this.build_cateid)
    }
  },
  onHide(){
    this.innerAudioContext&&this.innerAudioContext.pause()
  },
  onUnload(){
    this.innerAudioContext&&this.innerAudioContext.stop()
    this.innerAudioContext&&this.innerAudioContext.destroy()
  },
};
</script>

<style lang="scss" scoped>
.exhibition {
  min-height: 100vh;
  position: relative;
  // padding-bottom: 50px;
  background-color: #fff;
  &.in_pc {
    max-width: 414px;
    margin: auto;
    ::v-deep .popup-page .popup-box.center{
      max-width: 400px;
    }
    .sign_btn{
      right: calc(50vw - 196px);
    }
    .audio-box{
      right: calc(50vw - 196px);
    }
    .video_container{
      height: 212px;
      video,iframe{
        height: 212px;
      }
    }
  }
}
.top {
  position: relative;
  // height: 560rpx;
  image {
    width: 100%;
    // height: 100%;
  }
  .header_content{
    position: absolute;
    left: 48rpx;
    bottom: 88rpx;
  }
  .date {
    margin-bottom: 32rpx;
    display: flex;
    align-items: center;
    height: 96rpx;
    .date-year {
      font-family: Helvetica;
      font-size: 90rpx;
      font-weight: bold;
      color: #ffffff;
    }
    .date-range {
      height: 96rpx;
      border: 4rpx solid #ffffff;
      margin-left: 30rpx;
      .date-start,
      .date-end {
        font-size: 28rpx;
        height: 48rpx;
        line-height: 48rpx;
        padding: 0 8rpx;
        color: #fff;
      }
      .date-start {
        border-bottom: 1px solid #fff;
      }
    }
  }
  .title {
    margin-bottom: 24rpx;
    font-size: 72rpx;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    color: #ffffff;
  }
  .company {
    font-size: 28rpx;
    color: #fff;
  }
  .data {
    position: absolute;
    left: 48rpx;
    right: 48rpx;
    bottom: -100rpx;
    height: 164rpx;
    border: 1rpx solid #d8d8d8;
    border-radius: 20rpx;
    background: #fff;
    display: flex;
    align-items: center;
    &.no_border{
      border: none;
    }
    .data-share,
    .data-house {
      flex: 1;
    }
    .data-visit {
      flex: 1;
    }
    .line {
      width: 2rpx;
      height: 48rpx;
      background: #d8d8d8;
    }
    .num {
      font-size: 40rpx;
      color: #ff5600;
      text-align: center;
    }
    .name {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #999999;
      text-align: center;
    }
  }
}
.search {
  position: relative;
  margin: 148rpx 48rpx 0 48rpx;
  background: #fff;
  border: 1rpx solid #d8d8d8;
  box-shadow: 0 0 16rpx -8rpx rgba(0, 0, 0, 0.08);
  border-radius: 40rpx;
  padding: 20rpx 0 20rpx 92rpx;
  &.no_border{
    border: none;
  }
  .search-icon {
    position: absolute;
    left: 24rpx;
    top: 50%;
    transform: translateY(-50%);
  }
  input {
    font-size: 28rpx;
  }
}
.recommend {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin: 48rpx 48rpx 0 48rpx;
  color: #353535;
  &.has_bg_color {
    color: #fff;
  }
  .content_text{
    width: 100%;
    font-size: 28rpx;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  .more{
    padding-top: 8rpx;
  }
}
.video_container{
  margin: 24rpx 48rpx 0 48rpx;
  padding: 24rpx;
  height: 52vw;
  border-radius: 16rpx;
  border: 1rpx solid #d8d8d8;
  background-color: #fff;
  &.no_border{
    border: none;
  }
  video,iframe{
    width: 100%;
    height: 52vw;
  }
}
.sign_btn{
  position: fixed;
  right: 24rpx;
  bottom: 320rpx;
  z-index: 3;
  image{
    width: 128rpx;
    // height: 128rpx;
  }
}

@keyframes rotate{
  0%{-webkit-transform:rotate(0deg);}
  25%{-webkit-transform:rotate(90deg);}
  50%{-webkit-transform:rotate(180deg);}
  75%{-webkit-transform:rotate(270deg);}
  100%{-webkit-transform:rotate(360deg);}
}
@-webkit-keyframes rotate{
  0%{-webkit-transform:rotate(0deg);}
  25%{-webkit-transform:rotate(90deg);}
  50%{-webkit-transform:rotate(180deg);}
  75%{-webkit-transform:rotate(270deg);}
  100%{-webkit-transform:rotate(360deg);}
}
@keyframes fade {
  from {
    opacity: 1.0;
  }
  50% {
    opacity: 0.1;
  }
  to {
    opacity: 1.0;
  }
}

@-webkit-keyframes fade {
  from {
    opacity: 1.0;
  }
  50% {
    opacity: 0.1;
  }
  to {
    opacity: 1.0;
  }
}

.audio-box{
  position: fixed;
  right: 24rpx;
  top: 60rpx;
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background-color: rgba($color: #333, $alpha: 0.6);
  z-index: 5;
  color: #fff;
  text-align: center;
  line-height: 70rpx;
  &.rotate{
      animation:rotate 3s linear infinite
  }
  .icon-yinyue{
      line-height: 70upx;
  }
}
.news_content{
  padding: 24rpx 48rpx;
  &.has_bg{
    .news_title{
      color: #fff;
    }
    .article-content{
      border: none;
    }
  }
  .news_title{
    margin-bottom: 24rpx;
    font-size: 40rpx;
    font-weight: bold;
    color: #353535;
  }
  .article-content{
    padding: 24rpx;
    border: 1rpx solid #D8D8D8;
    border-radius: 16rpx;
    background-color: #fff;
    ::v-deep p{
      margin-bottom: 20rpx;
    }
    ::v-deep img{
      margin-bottom: 0;
    }
  }
}
</style>
