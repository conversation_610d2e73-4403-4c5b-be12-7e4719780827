<template>
<view class="confirm_order">
    <view class="card">
        <my-input small disabled label_width="160rpx" :value="params.uname" label="客户姓名" placeholder="请输入您的姓名" name="uname" @input="handleInput"></my-input>
        <my-input small label_width="160rpx" :value="params.tel" disabled label="客户手机号" placeholder="请输入您的手机号" type="number" name="tel" @input="handleInput"></my-input>
        <my-input small label_width="160rpx" label="验证码" placeholder="请输入验证码" type="number" name="code" @input="handleInput">
            <view class="send-code" :class="sending?'disable':''" @click="sendCode">{{time?time+'s':'获取验证码'}}</view>
        </my-input>
        <my-input small disabled label_width="160rpx" :value="params.id_card" label="身份证号码" placeholder="请输入您的身份证号码" type="id_card" name="id_card" @input="handleInput"></my-input>
        <!-- <my-input small disabled label_width="160rpx" label="地址" placeholder="请输入您的地址" type="text" name="address" @input="handleInput"></my-input> -->
    </view>
    <view class="card">
        <view class="title flex-box bottom-line">
            <text class="label">楼盘名称</text>
            <text>{{order_info.build_name}}</text>
        </view>
        <view class="card_content text-center">
            <view class="info_item flex-box">
                <view class="label">订单编号</view>
                <view class="value">{{order_info.order_sn}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">意向房源</view>
                <view class="value">{{order_info.house_name}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">建筑面积</view>
                <view class="value">{{order_info.jzmj}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">款项名称</view>
                <view class="value">{{order_info.money_type}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">款项金额</view>
                <view class="value">{{order_info.money}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">订单状态</view>
                <view class="value">{{order_info.pay_status_text}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">专属职业顾问</view>
                <view class="value">{{order_info.adviser_name}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">订单生成时间</view>
                <view class="value">{{order_info.ctime}}</view>
            </view>
        </view>
        <!-- <view class="order-footer flex-box top-line">
            <view class="btn" @click.stop.prevent="toOrderDetail(order_info.id)">查看订单</view>
            <view v-if="order_info.pay_status===0" class="btn" @click.stop.prevent="toPay(order_info.id)">去支付</view>
        </view> -->
    </view>
    <view class="btn-box">
        <button class="default" @click="subData()">确认订单</button>
    </view>
</view>
</template>

<script>
import {
    navigateTo,
} from '../common/index.js'
import myInput from "../components/form/myInput.vue"
import {config,showModal} from "../common/index.js"
export default {
    data() {
        return {
            params:{
                tel:"",
                uname:"",
                id_card:"",
                // address:""
            },
            sending:false,
            time:0,
            order_info:{}
        }
    },
    onLoad(options){
        this.params.order_id = options.order_id || ''
    },
    onShow(){
        if(this.params.order_id&&!this.get_once)
        this.getData()
    },
    components: {
        myInput
    },
    filters:{
        orderStatus(val){
            let status
            switch (val){
                case 0:
                    status = "待付款"
                    break;
                case 1:
                    status = "已付款"
                    break;
                case 2:
                    status = "处理中"
                    break;
                case 3:
                    status = "已退款"
                    break;
                case 5:
                    status = "待使用"
                    break;
                case 6:
                    status = "已失效"
                    break;
                default:
                    status = ""
            }
            return status
        }
    },
    methods: {
        /** 
         * <AUTHOR> 
         * @date 2020-03-03 13:36:52 
         * @desc 获取订单详情 
         */
        getData(){
            this.$ajax.get('online/orderDetailByUserConfirm.html',{order_id:this.params.order_id},res=>{
                if(res.data.code == 1){
                    this.get_once = true
                    this.order_info = res.data.order
                    this.params.uname = res.data.order.uname||''
                    this.params.tel = res.data.order.tel||''
                    this.params.id_card = res.data.order.id_card||''
                }
            })
        },
        handleInput(e){
            // console.log(e)
            this.params[e._name] = e.detail.value
        },
        inputImgCode(e){
            this.imgcode = e.detail.value
        },
        checkPhone(tel){ //检测手机号格式
            if(!tel){
                uni.showToast({
                    title:"请输入手机号",
                    icon:"none"
                })
                return false
            }
            if(tel.length!==11||tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return false
            }
            return true
        },
        sendCode() { //发送验证码
            if(this.sending){
                return
            }
            if(!this.checkPhone(this.params.tel)){
                return
            }
            this.$ajax.get('online/confirmCode.html',{tel:this.params.tel,order_id:this.params.order_id},(res)=>{
                if(res.data.code == 1){
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                    this.params.code_token = res.data.code_token
                    this.time = 60
                    this.timer()
                    this.sending = true
                }
                if(res.data.code == 0){
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            })
        },
        timer(){ //倒计时
            if(timer){
                clearInterval(timer)
            }
            let timer = setInterval(()=>{
                if(this.time<=0){
                    clearInterval(timer)
                    this.sending = false
                    return
                }
                this.time--
            },1000)
        },
        /** 
         * <AUTHOR> 
         * @date 2020-03-03 13:58:07 
         * @desc 确认订单 
         */
        subData(){
            // if(this.sending){
            //     return
            // }
            // if(!this.params.name){
            //    uni.showToast({
            //         title: "请输入姓名",
            //         icon: "none"
            //     })
            //     return 
            // }
            if(!this.checkPhone(this.params.tel)){
                return
            }
            if(!this.params.code){
                uni.showToast({
                    title: "请输入验证码",
                    icon: "none"
                })
                return
            }
            // if(!this.params.id_card){
            //    uni.showToast({
            //         title: "请输入身份证号码",
            //         icon: "none"
            //     })
            //     return 
            // }
            // if(!this.params.address){
            //    uni.showToast({
            //         title: "请输入地址",
            //         icon: "none"
            //     })
            //     return 
            // }
            this.$ajax.get('online/confirmOrder.html',this.params,res=>{
                if(res.data.code !== 1){
                    uni.showToast({
                        title:res.data.msg || '操作失败',
                        icon: 'none'
                    })
                    return
                }
                showModal({
                    content: '确认成功，是否去支付？',
                    confirmText: "去支付",
                    cancelText: "否",
                    confirm:()=>{
                        navigateTo(`/online/pay?order_id=${this.params.order_id}`)
                    }
                })
                uni.showToast({
                    title: res.data.msg
                })

            })
        },
    },
}
</script>

<style scoped lang="scss">
.confirm_order{
    padding-top: 24rpx;
}
.card{
    margin: 0 15rpx 24rpx 15rpx;
    padding: 26rpx;
    border-radius: 10rpx;
    background-color: #fff;
    box-shadow: 0 0 10px #dedede;
    .title{
        justify-content: space-between;
        padding: 26rpx 0;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        .label{
            color: #666;
        }
    }
    .card_content{
        margin-top: 20rpx;
    }
}

.order-footer{
    flex-direction: row-reverse; 
    padding: 20rpx;
    margin-top: 20rpx;
    .btn{
        height: 50rpx;
        line-height: 50rpx;
        font-size: 26rpx;
        padding: 0 25rpx;
        border-radius: 30rpx;
        background-color: $uni-color-primary;
        color: #fff;
        ~.btn{
            margin-right: 20rpx;
        }
    }
}

.info_item{
    padding: 15rpx 0;
    width: 100%;
    box-sizing: border-box;
    justify-content: space-between;
    .label{
        display: inline-block;
        text-align: left;
        min-width: 140rpx;
        color: #666;
    }
}
.send-code{
    padding: 0 15upx;
    border-radius: 8upx;
    background-color: $uni-color-primary;
    color: #fff;
    &.disable{
        background-color: #f1f1f1;
        color: #666
    }
}
</style>
