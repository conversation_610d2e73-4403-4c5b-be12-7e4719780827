<template>
  <view class="page">
    <!-- <view class="title">{{detail.title}}</view> -->
    <view class="content" v-html="detail.content"></view>
  </view>
</template>

<script>
export default {
  components: {

  },
  data () {
   return {
     detail:{

     }
    }
  },
  onLoad(options){
    this.id = options.id || ''
    this.getData()
  },
  methods: {
    getData(){
      this.$ajax.get('article/faqDetail',{id: this.id}, res=>{
        console.log(res.data)
        if(res.data.code === 1){
          uni.setNavigationBarTitle({
            title: res.data.detail.title
          })
          this.detail = res.data.detail
        }else{
          uni.showToast({
            title: res.data.msg,
            icon:'none'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.page{
  .title{
    font-size: 32rpx;
    font-weight: bold;
    padding: 24rpx 48rpx;
  }
  .content{
    margin-top: 24rpx;
    padding: 24rpx 48rpx;
    font-size: 30rpx;
    background-color: #fff;
  }
}
</style>