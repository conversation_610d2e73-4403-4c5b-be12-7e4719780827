<template>
<view>
  <view :style="{height:wHeight,width:wWidth}">
  <!-- #ifndef H5 -->
  <map :height="wHeight" :width="wWidth" :latitude="lat" :longitude="lng" :circles="circles" refs="mapId" id ="mapId"  @tap="getLocation"></map>
  <!-- #endif -->
  <!-- #ifdef H5 -->
  <qqMap class ="map" :scale="scale" :circles="circles" :latitude="lat" :longitude="lng" :polygons ="[]" :markers='markers'  :mapkey="mapKey" singleCircle singleMarker   @click="getLocation" ></qqMap>
  <!-- #endif -->
  <cover-view>
    <button class="cofirm" @click ="confirm">确定</button>
  </cover-view>
</view>
  </view>
</template>

<script>
import qqMap from "@/components/qqMap";
import {config} from '../common/index.js'
export default {
  components:{
    qqMap
  },
  data(){
    const key = __uniConfig.qqMapKey
    return {
        id:"",
        lat:'',
        lng:'',
        circles:[],
        wHeight:'',
        wWidth:'',
        scale:13,
        radius:2000,
        maps:null,
        mapKey:key,
        markers:[],
        iconPath:"/images/new_icon/find_map.png"
    }
  },
  onLoad(options){
    if (options.id){
      this.id =options.id
    }
    if (options.lat){
      this.lat =options.lat
    }
    if (options.lng ){
      this.lng =options.lng
    }
    if (options.areaname ){
      this.areaname =options.areaname
    }
    this.circles =[
      {latitude:this.lat,
      longitude:this.lng,
      fillColor:'#ff000022',
      color:"rgba(255,255,0,0.5)",
      strokeWidth:1,
      radius:this.radius}
    ]

    this.markers =[
      {latitude:this.lat,
      id: this.id,
      longitude:this.lng,
      iconPath:'none',
      // iconPath:config.imgDomain+this.iconPath,
      width: 10,
			height: 10,
      callout:{
        content:'已选位置',
        padding:10
      }
      }
    ]
    let wInfo  = uni.getSystemInfoSync()
    console.log(wInfo.windowHeight);
    this.wHeight =wInfo.windowHeight+'px'
    this.wWidth =wInfo.windowWidth+'px'
  },
  
  methods: {
    getLocation(e) {
				var that = this;
        console.log(e);
        this.$set(this.circles[0],"latitude",e.lat)
        this.$set(this.circles[0],"longitude",e.lng)
        this.$set(this.markers[0],"latitude",e.lat)
        this.$set(this.markers[0],"longitude",e.lng)
        this.lat=e.lat
        this.lng =e.lng

        console.log(this.circles);
			},
      confirm(){
        this.$navigateBack(2)
        // this.$navigateTo("/findHouse/ai_house_search")
        setTimeout(() => {
          uni.$emit("getAreaInfo",{
            lat:this.lat,
            lng:this.lng,
            id:this.id,
            areaname:this.areaname
          })
        }, 200);
        
      },
      regionchange(e){
        console.log(e.type);
        console.log(e.target.scale);
        // this.radius =e.target.scale*50
        uni.showToast({
          title: this.radius,
          duration: 2000
        });
        this.$forceUpdate()
        // if(e.type == "end"){
        //   console.log(123);
        //   uni.showToast({
        //     title: e.target.scale,
        //     duration: 2000
        //   });
        // }
        // uni.showToast({
        //   title: JSON.stringify(e),
        //   duration: 50000
        // });
      }
  },
}
</script>

<style scoped lang ="scss">
.map{
  width: 100%;
  height: 100%;
}
.cofirm {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  background: #fff;
}
</style>

