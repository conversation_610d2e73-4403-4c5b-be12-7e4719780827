<template>
	<view id="search" class="p-top-80">
		<view class="top-fixed">
			<search focus placeholder="请输入小区名称" @input="handelInput"></search>
		</view>
		<view class="list-box">
			<uni-list-item v-for="(item,index) in resList" :key="index" :title="item.name" @click="select(item)"></uni-list-item>
			<view v-if="search_end" class="add_btn" @click="toAddCommunity">
				<text class="text">没有找到？点击添加</text>
				<my-icon type="ic_fabud" size="46rpx" color="#ff656b"></my-icon>
			</view>
		</view>
	</view>
</template>

<script>
	import search from "../components/search.vue"
	import myIcon from "../components/myIcon.vue"
	import {uniListItem} from "@dcloudio/uni-ui"
	import {debounce} from "../common/index.js"
	export default {
		data() {
			return {
				getStatus:0,
				resList:[],
				keyword:"",
				search_end: false
			};
		},
		onLoad() {
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
		},
		components:{
			search,
			myIcon,
			uniListItem
		},
		methods:{
			handelInput(e){
				this.keyword = e.detail.value
				debounce(this.getRes,300)(e.detail.value)
			},
			getRes(key){
				if(!key){
					return
				}
				this.search_end = false
				this.$ajax.get('house/searchCommunity.html',{keyword:key},(res)=>{
					this.search_end = true
					this.getStatus = 0
					if(res.data.code == 1){
						this.resList = res.data.list
					}
					console.log(res.data)
				},(err)=>{
					this.search_end = true
				},false)
			},
			toAddCommunity(){
				this.$navigateTo('/user/add_community')
			},
			select(item){
				uni.setStorageSync('smallArea',JSON.stringify(item))
				uni.navigateBack()
			}
		}
	}
</script>

<style>
/* #ifdef H5 */
.top-fixed{
	position: fixed;
	width: 100%;
	top: 44px;
	background-color: #fff;
	z-index: 999;
}
/* #endif */
/* #ifndef H5 */
.top-fixed{
	position: fixed;
	width: 100%;
	top: var(--window-top);
	background-color: #fff;
	z-index: 999;
}
/* #endif */
#search .list-box{
	background-color: #fff;
}

.add_btn{
	padding: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.add_btn .text{
		margin-right: 12rpx;
	}
</style>
