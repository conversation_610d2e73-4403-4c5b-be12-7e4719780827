<template>
	<view class="lists">
		<!-- 小图 -->
		<view v-if="itemData.img_type==1 || isDefault=='true'" class="list-item flex-box" @click="goTo(itemData.id, itemData.title)">
			<view class="info_detail flex-1">
				<view class="info-title">{{itemData.title}}</view>
				<view class="flex-box align_center">
					<text class="author" v-if="itemData.name">{{itemData.name}}</text>
					<block v-if ="itemData.label && itemData.label.length>0">
						<text class="labels" v-for="(item,i) in itemData.label" :key="i">{{item}}</text>
					</block>
					<text class="time">{{itemData.ctime}}</text>
					<!-- <view class="click_count c-right" v-if="isOther">
						<my-icon type="yanjing" color="#999" size="30rpx"></my-icon>
						<text class="num">{{itemData.click_count}}</text>
					</view> -->
				</view>
			</view>
			<view class="img-box list-img" v-if="itemData.img">
				<image :src="itemData.img | imgUrl('w_240')" lazy-load mode="aspectFill"></image>
			</view>
		</view>
		<!-- 大图（海报图） -->
		<view v-else-if="itemData.img_type==2" class="list-item" @click="goTo(itemData.id, itemData.title)">
			<view class="info-title">{{itemData.title}}</view>
			<!-- <view class="img-box list-img2">
				<image :src="itemData.imghb_path | imgUrl('w_6401')" lazy-load mode="widthFix"></image>
			</view> -->
			<view class="flex-box align_center">
				<text class="author">{{itemData.name}}</text>
				<block v-if ="itemData.label && itemData.label.length>0">
					<text class="labels" v-for ="(item,i) in itemData.label" :key ="i">{{item}}</text>
				</block>
				<text class="time">{{itemData.ctime}}</text>
				<!-- <view class="click_count c-right" v-if="isOther">
					<my-icon type="yanjing" color="#999" size="30rpx"></my-icon>
					<text class="num">{{itemData.click_count}}</text>
				</view> -->
			</view>
		</view>
		<!-- 三张图 -->
		<view v-else-if="itemData.img_type==3" class="list-item" @click="goTo(itemData.id, itemData.title)">
			<view class="info-title">{{itemData.title}}</view>
			<!-- <view class="img-list flex-box">
				<view class="img-box list-img3 flex-1" v-for="(img,idx) in itemData.third_img" :key="idx">
					<image :src="img | imgUrl('w_240')" lazy-load mode="aspectFill"></image>
				</view>
			</view> -->
			<view class="flex-box align_center">
				<text class="author">{{itemData.name}}</text>
				<block v-if ="itemData.label && itemData.label.length>0">
					<text class="labels" v-for ="(item,i) in itemData.label" :key ="i">{{item}}</text>
				</block>
				<text class="time">{{itemData.ctime}}</text>
				<!-- <view class="click_count c-right" v-if="isOther">
					<my-icon type="yanjing" color="#999" size="30rpx"></my-icon>
					<text class="num">{{itemData.click_count}}</text>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script>
	import {formatImg,navigateTo} from "../../common/index.js"
	import myIcon from '../../components/myIcon'
	export default {
		components:{myIcon},
		props:{
			itemData:Object,
			isDefault:{
				type:String,
				default:"false"
			},
			isOther:{
				type:Boolean,
				default:false
			}
		},
		data() {
			return {
				
			};
		},
		filters:{
			imgUrl(val, param=""){
				if(!val){
					return ""
				}
				return formatImg(val, param)
			}
		},
		methods:{
			goTo(id, title){
				navigateTo('/pages/news/detail?id='+id)
			}
		}
	}
</script>

<style lang="scss" scoped>
	// view{
	// 	font-size: 32upx;
	// 	line-height: 52upx;
	// }
	// view{
	// 	display: flex;
	// 	flex-direction: column;
	// 	box-sizing: border-box;
	// }
	// .flex-row{
	// 	flex-direction: row;
	// }
	.list-img{
    width: 176rpx;
    height: 128rpx;
		border-radius: 10rpx;
		margin-left: 24upx;
		margin-right: 0
	}
	.list-img image{
		border-radius: 12rpx;
	}
	.list-img2{
		width: 100%;
		padding: 10upx 0;
		box-sizing: border-box;
		overflow: hidden
	}
	.list-img2 image{
		border-radius: 12rpx;
		width: 100%;
		height: 0
	}
	.list-img3{
		width: 30%;
		height: 20vw;
		margin: 1.5%
	}
	.list-img3 image{
		border-radius: 12rpx;
		width: 100%;
		height: 100%
	}
	.list-item{
		padding: 48rpx 0;
    background: none;
		.info_detail{
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}
	}
	.list-item .info-title{
		font-size: 28rpx;
		max-height: 120upx;
		min-height: 60upx;
		line-height: 50upx;
		font-weight: bold;
		overflow: hidden;
		text-overflow: ellipsis;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		display: -webkit-box;
		color: #333;
	}
	.author,.time{
		margin-right: 16upx;
		font-size: 22rpx;
		color: #999;
	}
	.click_count{
		font-size: 22rpx;
		color: #999;
		margin-left: auto;
		.num{
			margin-left: 10rpx;
		}
	}
	.align_center{
		align-items:center;
	}
	.labels{
		margin-right: 16upx;
		line-height: 1;
		font-size: 22rpx;
		padding:4rpx 6upx;
		border-radius: 4upx;
		color: #999;
    border: 0.5px solid #d8d8d8;
	}
  .hb-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 20rpx;
  }
  .hb-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #FF5B5B;
  }
</style>
