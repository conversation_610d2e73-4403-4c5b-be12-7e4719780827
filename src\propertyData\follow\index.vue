<template>
  <view>
    <view class="list">
      <view class="info">
        <view>
          <view class="name">
            <text>姓名 12345678910</text>
            <text class="receive">未领取（点击领取）</text>
          </view>
          <view class="detail">
            <text>报备楼盘</text>
            <text>xx</text>
          </view>
          <view class="detail">
            <text>客户性别</text>
            <text>xx</text>
          </view>
          <view class="detail">
            <text>报备会员</text>
            <text>xx</text>
            <my-icon class="iconfont" type="dianhua1" size="20" color="#fb6c70"></my-icon>
            <my-icon class="iconfont" type="zixun" size="20" color="#fb6c70"></my-icon>
            <!-- <iamge src="/static/icon/tel.png"></iamge>
            <iamge src="/static/icon/mes.png"></iamge> -->
          </view>
          <view class="detail">
            <text>公司名称</text>
            <text>xx</text>
          </view>
          <view class="detail">
            <text>公司区域</text>
            <text>xx</text>
          </view>
          <view class="detail">
            <text>报备时间</text>
            <text>xx</text>
          </view>
          <view class="detail">
            <text>最后更新</text>
            <text>xx</text>
          </view>
          <view class="detail">
            <text>身份证号</text>
            <text>xx</text>
          </view>
          <view class="detail">
            <text>客户意向</text>
            <text>xx</text>
          </view>
          <view class="detail">
            <text>报备状态</text>
            <text>xx</text>
            <text class="valid">报备有效</text>
          </view>
        </view>
        <view class="btn">
          <text class="grey">设为无效</text>
          <text class="pink">补全号码</text>
          <text class="darkblue">复制客户</text>
          <text class="green">设为到访</text>
          <text @click="$navigateTo('info')" class="blue">跟进</text>
          <text @click="$navigateTo('status')" class="red">状态</text>
        </view>
      </view>
    </view>
    <!-- 阴影层 -->
    <!-- <view class="mask show"></view> -->
    <!-- 复制客户 -->
    <!-- <view class="template" style="height: 310rpx;">
      <view class="close">
        <my-icon type="close" size="20"></my-icon>
      </view>
      <view>
        <select name="" id="" class="select">
          <option value="0">---请选择---</option>
          <option value="2">2</option>
        </select>
        <select name="" id="" class="select">
          <option value="0">---请选择---</option>
          <option value="2">2</option>
        </select>
        <text class="default">默认模板</text>
      </view>
    </view> -->
    <!-- 补全号码 -->
    <!-- <view class="template" style="height: 180rpx;">
      <view class="close">
        <my-icon type="close" size="20"></my-icon>
      </view>
      <view class="input">
        <input type="number" name="" id="" placeholder="请输入****内容" />
        <text>确认</text>
      </view>
    </view> -->
    <!-- 设为无效 -->
    <!-- <view class="off" style="height:200rpx;">
      <view class="close">
        <my-icon type="close" size="16"></my-icon>
      </view>
      <view class="text-box">
        <textarea placeholder="请填写无效内容（非必填）" name="" id="" cols="30" rows="10"></textarea>
        <view class="button">
          <text class="add">提交</text>
          <text class="add_mes">提交并短信提醒</text>
        </view>
      </view>
    </view> -->
  </view>
</template>
<script>
import myIcon from "@/components/icon.vue";
export default {
  components: {
    myIcon,
  },
  data() {
    return {};
  },
  computed: {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.off {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
  margin: auto 60rpx;
  .text-box {
    background: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    .button{
      display: flex;
      justify-content: space-between;
      margin-top: 30rpx;
      text{
        color: #fff;
        width: 46%;
        text-align: center;
        padding: 16rpx 0;
        border-radius: 10rpx;
        &.add{
          background: #708efc;
        }
        &.add_mes{
          background: #2fbcf3;
        }
      }
    }
    textarea{
      background: #f3f2f7;
      width: 100%;
      border-radius: 4rpx;
      padding: 20rpx;
      box-sizing: border-box;
    }
  }
  .close {
    position: absolute;
    right: -16rpx;
    top: -30rpx;
    border-radius: 50%;
    background: #fff;
    width: 50rpx;
    height: 50rpx;
    line-height: 50rpx;
    text-align: center;
  }
}
.mask {
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  z-index: -1;
  background-color: rgba($color: #000000, $alpha: 0);
  transition: 0.26s;
  &.show {
    background-color: rgba($color: #000000, $alpha: 0.5);
    z-index: 9;
  }
}
.template {
  z-index: 99;
  margin: auto 30rpx;
  padding: 28rpx 32rpx;
  background: #fff;
  border-radius: 10rpx;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  .input {
    display: flex;
    border-radius: 12rpx;
    overflow: hidden;
    input {
      flex: 1;
      background: #eeeeee;
      height: 100%;
      padding-left: 20rpx;
    }
    text {
      color: #fff;
      background: #6485f0;
      width: 140rpx;
      text-align: center;
      line-height: 80rpx;
    }
  }
  .close {
    float: right;
    margin-bottom: 20rpx;
  }
  .default {
    color: #fff;
    background: #0074fb;
    border-radius: 8px;
    padding: 6rpx 20rpx;
  }
  .select {
    background: #6485f0;
    width: 100%;
    border: 0;
    color: #fff;
    height: 30px;
    border-radius: 8px;
    outline: none;
    padding-left: 10rpx;
    margin-bottom: 34rpx;
  }
}
.list {
  background: #f5f5f5;
  .info {
    background: #fff;
    margin: 30rpx;
    padding: 30rpx;
    > view {
      margin-bottom: 10rpx;
    }
    .name {
      font-size: 30rpx;
      margin-bottom: 26rpx;
      .receive {
        color: #d11418;
        margin-left: 16rpx;
      }
    }
    .detail {
      color: #999999;
      > text:first-child {
        margin-right: 16rpx;
      }
      .valid {
        color: #6d93ee;
      }
      .iconfont{
        position:relative;
        top: 6rpx;
        margin-left: 24rpx;
      }
    }
  }
  .btn {
    display: flex;
    flex-wrap: wrap;
    text {
      color: #fff;
      padding: 10rpx 28rpx;
      border-radius: 40rpx;
      margin-right: 20rpx;
      margin-top: 20rpx;
    }
    .grey {
      background: #889ab2;
    }
    .pink {
      background: #f76e71;
    }
    .darkblue {
      background: #0277fb;
    }
    .green {
      background: #61d4ba;
    }
    .blue {
      background: #2cbff8;
    }
    .red {
      background: #f35076;
    }
  }
}
</style>
