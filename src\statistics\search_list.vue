<template>
    <view class="search" :style="{background:infos.bg_color}" :class="{pdb_120: (shareUserInfo.adviser_id&&is_open_adviser)||shareUserInfo.agent_id}"> 
        <!-- 头部开始 -->
        <view class="header" :style="{backgroundImage:'url('+infos.top_pic+')'}">
            <block v-if ="infos.top_words">
                <view class="title">{{infos.title||"热搜榜"}}</view>
                <view class="sitename">{{siteName}}</view>
                <view class="info flex-row">
                    <view class="info-date">{{infos.info||infoDate}}</view>
                </view>
                <!-- 涨跌榜切换 -->
                <template v-if="type==1">
                    <view v-if="tabId == 4&&rise == 1" @click="riseSwitch(2)" class="rise">查看跌榜 ↓</view>
                    <view v-if="tabId == 4&&rise == 2" @click="riseSwitch(1)" class="rise_down">查看涨榜 ↑</view>
                </template>
            </block>
        </view>
        <!-- 头部结束 -->
        <!-- tab切换开始 -->
        <view class="filter_list">
            <tab-bar :tabs="tab" :bgColor="'none'" :fixedTop="false" :showLine="false" :nowIndex="tabId" :lineHeight="'50rpx'" :height="'auto'">
            <view
            class="nav_item"
            :class="{ tab_on: item.id  == tabId}"
            v-for="(item, index) in tab"
            :key="index"
            @click="gotab(item)"
            >{{ item.title }}
            <text></text></view
            >
            </tab-bar>
        </view>
        <!-- tab切换结束 -->
        <!-- 列表开始 -->
        <view class="list" >
            <view class="builds">
                <view class="build flex-box" v-for="(build,index) in ranklist" :key="index" @click="goDetail(build.id)">
                    <view class="jiangbei" v-if="index==0">
                        <image :src="imgSrc0" mode="widthFix"></image>
                    </view>
                    <view class="jiangbei" v-if="index==1">
                        <image :src="imgSrc1" mode="widthFix"></image>
                    </view>
                    <view class="jiangbei" v-if="index==2">
                        <image :src="imgSrc2" mode="widthFix"></image>
                    </view>
                    <view class="paiwei" v-if="index>2" :style ="'backgroundImage:url('+biaoqianSrc+')'">
                        {{index+1}}
                    </view>
                    <view class="build-con flex-row">
                        <view class="img">
                            <image :src="build.img| imgFormat" lazy-load mode="aspectFill"></image>
                        </view>
                        <view class="infos flex-box flex-1">
                            <view class="title">{{build.title}}</view>
                            <view class="price" v-if="type==1 && build.avg_price">均价<text class="red">{{build.avg_price}}</text>元/m²</view>
                            <view class="price" v-else-if="type==1 && !build.avg_price">均价待更新<text class="red"></text></view>
                            <view class="price" v-else>{{build.price_type}}<text class="red">{{build.build_price}}</text>{{build.price_unit}}</view>
                            <view class="area flex-row"><text class="areaname">{{build.areaname}} </text><text class="fenge" v-if ="build.mj"> | </text> <text class="jianmian" v-if ="build.mj">{{build.mj}}m²</text>
                                <view class="price_z" v-if="type == 1 && tabId == 4">
                                    <view v-if="build.house_value > 0">
                                        <text v-if="build.house_status == 1" class="info_up">{{build.house_value}}% ↑</text>
                                        <text v-if="build.house_status == 2" class="info_down">{{build.house_value}}% ↓</text>
                                    </view>
                                    <view v-else>--</view>
                                </view>
                            </view>
                            <view class="others flex-row" v-if="type!=1">
                                <view class="status" :class="'status'+build.leixing">{{build.status_name}}</view>
                                <!-- <view class="labels"><text class="label">洋房</text></view> -->
                                <view class = "labels" v-if="build.build_type&&build.build_type.length>0">
                                    <text class="label" v-for="(item,idx) in build.build_type" :key = "idx" >{{item}}</text>
                                </view>
                            </view>
                            <view class="mingci">
                                <view v-if="type==1" class="first mingci-con flex-row" :class="'mingci'+(index>=2?2:index)" ><view class="icon" :style="{backgroundImage:'url('+mingciSrc+')'}"></view> <view class="neirong">{{siteCity}}{{month}}月小区{{infos.title||"热搜榜"}}{{chineseMingci[index]}}</view></view>
                                <view v-else class="first mingci-con flex-row" :class="'mingci'+(index>=2?2:index)" ><view class="icon" :style="{backgroundImage:'url('+mingciSrc+')'}"></view> <view class="neirong">{{siteCity}}{{month}}月楼盘{{infos.title||"热搜榜"}}{{chineseMingci[index]}}</view></view>
                            </view>
                            
                        </view>
                    </view>
                    

                </view>
            </view>
        </view>

        <!-- 列表结束 -->
        <!-- 声明开始 -->
        <view class="shengming" v-if="infos.disclaimer">
            {{infos.disclaimer}}
        </view>
        <!-- 声明结束 -->
        <!-- 底部分享者信息 -->
        <view class="sharers_info flex-row" v-if="(shareUserInfo.adviser_id&&is_open_adviser)||shareUserInfo.agent_id">
            <view class="img"> <image :src="shareUserInfo.prelogo | imageFilter('w_240')" mode="widthFix"></image></view>
            <view class="info flex-1">
                <view class="name">{{shareUserInfo.cname}}</view>
                <view class="identity">{{shareUserInfo.identity===1?'置业顾问':'经纪人'}}</view>
            </view>
            <view class="btn_box flex-row">
                <view class="btn" @click="handleChat()">微聊</view>
                <view class="btn" v-if ="(shareUserInfo.adviser_id&&switch_adviser_tel) ||shareUserInfo.agent_id" @click="handleTel()">电话咨询</view>
            </view>
        </view>
        <!-- 分享弹框 -->
        <share-pop ref="showSharePop"  @copyLink="copyLink"  @showCopywriting="showCopywriting" :showHaibao="false"></share-pop>
        <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
        <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
    </view>
</template>

<script>
import tabBar from '@/components/tabBar.vue'
import myIcon from '@/components/myIcon'
import sharePop from '@/components/sharePop'
import {formatImg} from '@/common/index.js'
import {config} from '@/common/config.js'
import shareTip from '../components/shareTip.vue'
// #ifdef H5 || MP-BAIDU
import {setSeo} from '../common/mixin'
// #endif
import getChatInfo from '../common/get_chat_info'
import allTel from '../common/all_tel.js'
export default {
    components: {
        myIcon,
        sharePop,
        shareTip,
        tabBar
    },
    data(){
        return {
            id:'',   //暂时没有入口  指定一个默认值
            infos:{build:[]},
            siteName:'',
            siteCity:"",
            infoDate:"",
            share:{},
            link:'',
            toLogin:true,
            shareId:'',  //分享者id（置业顾问的为置业顾问Id 经纪人的为用户id
            shareType:'', //分享者身份  置业顾问的为1 经纪人的为2
            currentUserInfo:{},  //当前用户信息
            shareUserInfo:{},
            show_share_tip:false,
            chineseMingci:['第一名','第二名','第三名','第四名','第五名','第六名','第七名','第八名','第九名','第十名'],
            tel_res: {},
            show_tel_pop: false,
            type:'', //type==1 为小区 type=='' 为新房
            ranklist:[],
            tab:[],
            tabId:'',
            rise:1 // 涨跌榜 1：涨 2：跌
        }
    },
    filters:{
        imgFormat(img, param = "w_240"){
            if (!img) {
                return ""
            }
            return formatImg(img, param)
        },
    },
    computed: {
		biaoqianSrc(){
			return config.imgDomain+'/images/new_icon/record/biaoqian.png'
        },
        imgSrc0(){
            return config.imgDomain+'/images/new_icon/record/<EMAIL>'
        },
        imgSrc1(){
            return config.imgDomain+'/images/new_icon/record/<EMAIL>'
        },
        imgSrc2(){
            return config.imgDomain+'/images/new_icon/record/<EMAIL>'
        },
        mingciSrc(){
            return config.imgDomain+'/images/new_icon/record/<EMAIL>'
        },
        is_open_im() {
            return this.$store.state.im.ischat 
        },
        is_open_adviser() {
			//是否开启置业顾问功能
			return this.$store.state.im.adviser;
		},
        is_open_middle_num() {
            return this.$store.state.im.istelcall 
        },
        login_status() {
            return this.$store.state.user_login_status
        },
        switch_adviser_tel(){
             return this.$store.state.switch_adviser_tel
        }
	},
    onLoad(options){
        if (options.type){
            this.type=options.type
        }
        if (options.id){
            this.id=options.id
            this.tabId=options.id
        }
        if (options.shareId&&options.shareType){
            this.shareId=options.shareId
            this.shareType =options.shareType
            this.share_time =options.f_time||''
        }
        this.infoDate=this.formatDate(new Date())+"更新"
        this.formatDate()
        this.getData()
    },
    onShow(){
        if (this.$store.state.updatePageData&&this.login_status!=3){
            this.getData()
            this.$store.state.updatePageData=false 
        }
    },
    onUnload(){
        this.$store.state.updatePageData=false 
    },
    methods: {
        getData(){
            var url
            if (this.type == 1) {   //小区排行榜
                url = 'community/communityRankingDetail'
            }else{
                url = 'build/buildRankingDetail'
            }
            // 榜单列表
            this.$ajax.get(url,{id:this.id,sid:this.shareId,sharetype:this.shareType,forward_time:this.share_time || ''},res=>{
                this.siteName=res.data.siteName
                this.share={
                    title:'榜单列表',
                    content:'榜单列表',
                    pic:''
                }
                this.siteCity=res.data.siteCity
                if(res.data.code ==1){
                    this.infos=res.data.data
					this.tab = res.data.rankings    //榜单tab
                    if (this.type == 1) {   //小区榜单
                        if (this.rise == 1) {   //涨跌榜
                            this.ranklist = res.data.data.communities  
                        }else{
                            this.ranklist = res.data.data.communities2
                        }
                    }else{
                        this.ranklist = res.data.data.builds
                    }
                    // this.ranklist = res.data.data.communities
                    // this.ranklist = res.data.data.builds
                    this.share={
                        title:this.infos.share_title||'',
                        content:this.infos.share_content||'',
                        pic:this.infos.share_pic||''
                    }
                    //当前用户信息
                    this.currentUserInfo=res.data.shareUser  
                    if (this.currentUserInfo.adviser_id>0){
                        this.currentUserInfo.shareType=1
                        this.currentUserInfo.shareId=this.currentUserInfo.adviser_id
                    }else if (this.currentUserInfo.agent_id>0) {
                        this.currentUserInfo.shareType=2
                        this.currentUserInfo.shareId=this.currentUserInfo.agent_id
                    }else {
                        this.currentUserInfo={
                            shareId:this.shareId,
                            shareType:this.shareType
                        }
                    }
                    if (res.data.share_user) { //分享者信息
                        this.shareUserInfo = res.data.share_user
                        if(res.data.share_user.adviser_id){
                            this.shareUserInfo.identity = 1
                        }else if(res.data.share_user.agent_id){
                            this.shareUserInfo.identity = 2
                        }
                    }
                    if (this.shareId){
                        // 获取登陆状态
                        this.$ajax.get('member/checkUserStatus', {}, res => {
                            if (res.data.code === 1) {
                            } else { 
                                if (this.toLogin==false) return 
                                this.toLogin=false
                                // this.$store.state.updatePageData=true
                                this.$store.state.user_login_status = res.data.status
                                if(this.login_status==1){
                                    uni.setStorageSync('backUrl', window.location.href)
                                    this.$navigateTo("/user/login/login") 
                                }
                                
                            }
                        })
                    }
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
                this.share.link=this.getShareLink()
                this.getWxConfig()
            })
        },
        // 切换tab
        gotab(item){
            this.id = item.id
            this.tabId = item.id
            this.rise = 1
            this.getData()
        },
        // 涨跌榜切换
        riseSwitch(index){
            this.rise = index
            this.getData()
        },
        showShare(){
            this.$refs.showSharePop.show()
        },
        formatDate(timestamp){
            if (!timestamp){
                timestamp=new Date()
            }
            let time= Date.parse(timestamp)
            let date = new Date(time);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
            let Y = date.getFullYear() + '-';
            let month=date.getMonth() + 1
            let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
            let D =date.getDate() <10? ('0' + date.getDate() + ' '):(date.getDate() + ' ');
            let h = date.getHours()<10? ('0' + date.getHours()+ ':'): (date.getHours()+":");
            let m = date.getMinutes() < 10 ? ('0' + date.getMinutes() + ':') : (date.getMinutes() + " :");
            let s = date.getSeconds() < 10 ? ('0' + date.getSeconds() ) : (date.getSeconds())
            this.month=month
            return Y + M + D;
        },
        goDetail(id){
             if (this.type ==1 ){//  小区
                this.$navigateTo("/pages/house_price/detail?id="+id)
            }else{  
                this.$navigateTo("/pages/new_house/detail?id="+id)
            }
        },
        // 获取分享链接
        getShareLink(){
            let link = window.location.href
            let time =parseInt(+new Date()/1000)
            if (this.currentUserInfo.shareId) { //当前用户是 置业顾问或者经纪人  
                link = `${window.location.origin}${window.location.pathname}?id=${this.id}&shareId=${this.currentUserInfo.shareId}&shareType=${this.currentUserInfo.shareType}&f_time=${time}`
            }
            return link
        },
        copyLink(){
            this.show_share_tip=true
        },
        getShortLink(){
            this.link=this.getShareLink()
            this.$ajax.get("build/shortUrl.html",{page_url:this.link},(res)=>{
                if(res.data.code ==1){
                    this.link=res.data.short_url
                }
            })
        },
        // 复制分享内容
        showCopywriting(){
            console.log("复制内容")
            const content = `${this.siteName}${this.infos.title}榜单\n【链接】${this.link}`
            this.copyText(content, ()=>{
                uni.showToast({
                title: '复制成功,去发送给好友吧',
                icon: 'none'
                })
            })
        },
        // 复制内容
        copyText(cont, callback) {
            let oInput = document.createElement('textarea')
            oInput.value = cont
            document.body.appendChild(oInput)
            oInput.select() // 选择对象;
            oInput.setSelectionRange(0, oInput.value.length);
            document.execCommand('Copy') // 执行浏览器复制命令
            oInput.blur()
            oInput.remove()
            if(callback) callback()
        },
        // 发起聊天
        handleChat(){
            if(!this.is_open_im){
                if (this.shareUserInfo.identity == 1) { //置业顾问
                    this.$navigateTo('/pages/consultant/detail?id=' + this.sharers_info.adviser_id)
                } else if (this.shareUserInfo.identity == 2) {
                    this.$navigateTo('/pages/agent/detail?id=' + this.sharers_info.agent_id)
                }
                return
            }
            // #ifndef MP-WEIXIN
                getChatInfo(this.shareUserInfo.mid, 26)
            // #endif
        },
        // 拨打电话
        handleTel(){
            this.tel_params = {
                type: this.shareUserInfo.identity == 1?'2':'3',
				callee_id: this.shareUserInfo.mid,
				scene_type:this.shareUserInfo.identity == 1?'2':'3',
				scene_id: this.shareUserInfo.mid,
                success: (res)=>{
                    this.tel_res = res.data
                    this.show_tel_pop = true
                }
            }
            allTel(this.tel_params)
        },
        retrieveTel(){
            allTel(this.tel_params)
        },
        // 检测登录状态
        checkLogin(callback) {
            this.$ajax.get('member/checkUserStatus', {}, res => {
                if (res.data.code === 1) {
                    callback&&callback()
                } else {
                    this.$store.state.user_login_status = res.data.status
                    if (this.$store.state.user_login_status==1){
                        uni.setStorageSync('backUrl', window.location.href)
						this.$navigateTo("/user/login/login")
					}else if (this.$store.state.user_login_status==2){
						this.$navigateTo("/user/bind_phone/bind_phone")
					}
                }
            })
        },
    },
     // #ifdef H5
    onNavigationBarButtonTap(option){
        if(option.index==0){
        // this.handleCreat()
            this.getShortLink()
            this.$refs.showSharePop.show()
        }
        // if(option.index==1){
        // this.handleCollect()
        // }
    },
		// #endif
    onShareAppMessage() {
        return {
            title: this.share.title || "",
            content: this.share.content || "",
            imageUrl: this.share.pic ? formatImg(this.share.pic, "w_6401") : "",
        };
	},

    


}
</script>

<style lang="scss">
    .filter_list {
        padding: 20rpx 50rpx;
        .nav_item {
            width: 23%;
            display: inline-block;
            box-sizing: border-box;
            text-align: center;
            color: #fff;
            text{
                display: block;
                width: 50rpx;
                height: 8rpx;
                border-radius: 20rpx;
                margin: 0 auto;
                margin-top: 12rpx;
            }
            &.tab_on{
                font-size: 32rpx;
                font-weight: 500;
                text{
                    background: #fff;
                }
            }
        }
    }
    .flex-box{
        display: flex;
        flex-direction: column;
    }
    .flex-row{
        display: flex;
        flex-direction: row;
    }
    .pdb_120{
        padding-bottom: 120rpx;
    }
    // 头部样式开始
    .header{
        height: 400rpx;
        width: 100%;
        padding: 0 48rpx;
        position: relative;
        box-sizing:border-box ;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-image: url('https://images.tengfangyun.com/images/new_icon/record/<EMAIL>');
        .title{
            font-size: 80rpx;
            color: #FFFFFF;
            padding-top: 80rpx;
            font-weight: bold;
        }
        .sitename{
            margin: 16rpx 0;
            font-size: 28rpx;
            color: #FFFFFF;
        }
        .card-btn {
                position: absolute;
                z-index: 96;
                bottom: 100rpx;
                right: 48upx;
                width: 70upx;
                height: 70upx;
                border-radius: 50%;
                justify-content: center;
                align-items: center;
                // padding: 15upx;
                background: rgba($color: #000000, $alpha: 0.6);
            }
        .info{
            font-size: 28rpx;
            color: #FFFFFF;
            align-items: center;            
            .card-btn-right{
                margin-left: auto;
                width: 50rpx;
                height: 50rpx;
                border-radius: 50%;
                background: rgba($color: #000000, $alpha: 0.6);
                justify-content: center;
                align-items: center;
            }
            
        }
        .rise,.rise_down{
            display: inline-block;
            color: #fff;
            border-radius: 40rpx;
            padding: 8rpx 24rpx;
            border: 2rpx solid #fff;
            margin-top: 26rpx;
            font-size: 24rpx;
        }
    }
    // 头部样式结束
    // 列表样式开始
    .list{
        padding: 0 48rpx;
    }
    .build{
        position: relative;
        padding: 24rpx;
        margin-bottom: 24rpx;
        background: #ffffff;
        border-radius: 8rpx;
        .paiwei{
            position: absolute;
            text-align: center;
            top:0;
            right:24rpx;
            height: 50rpx;
            width: 50rpx;
            padding-top: 4rpx;
            box-sizing: border-box;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            color: #fff;
                
        }
        .jiangbei{
            position: absolute;
            right: 24rpx;
            top: 0;
            height: 50rpx;
            width: 50rpx;
            image{
                width: 100%;
                height: 100%;
            }
        }
        .img{
            width: 200rpx;
            min-width: 200rpx;
            height: 170rpx;
            overflow: hidden;
            margin-right: 24rpx;
            image{
                width: 100%;
                height: 100%;
            }
        }
        
        .infos{
            overflow: hidden;
            .title{
                font-size: 32rpx;
                color: #333333;
                font-weight: bolder;
                max-width:calc(100% - 50rpx);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            .price{
                font-size: 28rpx;
                color: #333333;
                margin-top: 10rpx;
                .red{
                    font-size: 32rpx;
                    color: #FB656A;
                    font-weight: bold;
                }
            }
            .area{
                margin-top: 5rpx;
                color: #999;
                font-size: 22rpx;
                    .price_z{
                        font-size: 22rpx;
                        position: absolute;
                        right: 30rpx;
                        color: #858585;
                        .info_up{
                            color: #FF5B6A;
                        }
                        .info_down{
                            color: #4bba42;
                        }
                    }
            }
            .others{
                margin-top: 6rpx;
                max-width: 100%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                .status {
                    line-height: 1;
                    font-size: 22rpx;
                    padding: 6rpx 10rpx;
                    border-radius: 4rpx;
                    color: #fff;
                    background-color: #53d2ab;
                    font-weight: initial;
                    &.status1 {
                        color: #fff;
                        background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
                    }
                    &.status2 {
                        color: #fff;
                        background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
                    }
                    &.status3 {
                        color: #fff;
                        background: linear-gradient(to right, #ff8533 0%, #ff7213 100%);
                    }
                    &.status4 {
                        color: #fff;
                        background: linear-gradient(to right, #ccc 0%, #ccc 100%);
                    }
                }
                .labels{
                    display: flex;
                    align-items: center;
                    .label{
                        border: 1upx solid #d8d8d8;
                        padding: 2upx 6upx;
                        border-radius: 4upx;
                        margin-left: 10upx;
                        font-size: 22rpx;
                        color: #999999;
                    }
                }
            }
            .mingci{
                margin-top: 20rpx;
                display: flex;
                .mingci-con{
                    font-size: 22rpx;
                    padding: 0 16rpx;
                    border-radius: 8px;
                    overflow: hidden;
                    align-items: center;
                    white-space: nowrap;
                    &.mingci0{
                        background: rgba(255,58,58,0.15);
                        color: #FB656A;
                    }
                    &.mingci1{
                        color: #FB8968;
                        background: rgba(255,120,26,0.21);
                    }
                    &.mingci2{
                        color: #FBC365;
                        background: rgba(251,195,101,0.20);
                    }
                    .neirong{
                        font-size: 22rpx;
                    }
                    .icon{
                        width: 32rpx;
                        height: 32rpx;
                        min-width: 32rpx;
                        margin-right: 10rpx;
                        background-repeat: no-repeat;
                        background-size: 100% 100%;
                    }
                    
                }
            }
        }

    }
    .infos{
        .area{
            align-items: center;
            .fenge{
                margin: 0 6rpx;
            }
        }
        
    }
    // 列表样式结束
    //声明
    .shengming{
        border: 1rpx solid #fff;
        border-radius: 8rpx;
        padding: 10rpx;
        color: #ffff;
        margin: 0 48rpx  24rpx 48rpx;
    }
    // 分享者信息
    .sharers_info{
        position: fixed;
        width: 100%;
        height: 120rpx;
        bottom: 0;
        padding: 0 48rpx;
        box-sizing: border-box;
        align-items: center;
        background-color: #fff;
        z-index: 90;
        .img{
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            margin-right: 16rpx;
            overflow: hidden;
            image{
                width: 100%;
                height: 100%;
            }
        }
        .info{
            overflow: hidden;
            .name{
                margin-bottom: 16rpx;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            .identity{
                font-size: 24rpx;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                color: #999;
            }
        }
        .btn_box{
            .btn{
                margin-left: 20rpx;
                padding: 10rpx 34rpx;
                font-size: 26rpx;
                color: $uni-color-primary;
                border: 1px solid $uni-color-primary;
                border-radius: 3px;
                box-shadow: 0 2px 4px 0 rgba(251,101,106,.1);
            }
        }
    }
</style>