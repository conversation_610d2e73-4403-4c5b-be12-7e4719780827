<template>
	<view id="search" class="p-top-80">
		<view class="top-fixed">
			<search focus placeholder="请输入门店名称" @input="handelInput"></search>
		</view>
		<view class="list-box">
			<uni-list-item v-for="(item,index) in resList" :key="index" :title="item.name" @click="select(item)"></uni-list-item>
		</view>
	</view>
</template>

<script>
	import search from "../components/search.vue"
	import {uniListItem} from "@dcloudio/uni-ui"
	import {debounce} from "../common/index.js"
	export default {
		data() {
			return {
				getStatus:0,
				resList:[]
			};
		},
		onLoad() {
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
		},
		components:{
			search,
			uniListItem
		},
		methods:{
			handelInput(e){
				debounce(this.getRes,500)(e.detail.value)
			},
			getRes(key){
				if(!key){
					return
				}
				this.$ajax.get('agentCompany/agentStoreList',{keywords:key},(res)=>{
					this.getStatus = 0
					if(res.data.code == 1){
						this.resList = res.data.list
					}
					console.log(res.data)
				},(err)=>{
					
				},false)
			},
			select(item){
				uni.setStorageSync('smallStore',JSON.stringify(item))
				uni.navigateBack()
			}
		}
	}
</script>

<style>
/* #ifdef H5 */
.top-fixed{
	position: fixed;
	width: 100%;
	top: 44px;
	background-color: #fff;
	z-index: 999;
}
/* #endif */
/* #ifndef H5 */
.top-fixed{
	position: fixed;
	width: 100%;
	top: var(--window-top);
	background-color: #fff;
	z-index: 999;
}
/* #endif */
#search .list-box{
	background-color: #fff;
}
</style>
