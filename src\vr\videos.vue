<template>
<view class="page">
    <view class="video_list">
        <template v-if ="!videos.length">
           <view class ="no_data">没有更多数据了</view> 
        </template>
        <template v-else>
            <view class="video_item" v-for="(video, index) in videos" :key="index">
            <view class="video_box" @click ="toVr(video)">
                <!-- <image class="video_img" mode="aspectFill" :src="(video.pic||video.url) | imgUrl('w_6401')"></image>
                <image class="video-icon" src="/static/icon/video.png" mode="widthFix"></image> -->
                <video :src="video.url" :poster="(video.pic||video.url) | imgUrl('w_6401')" @error="videoErrorCallback" controls x5-playsinline></video>
            </view>
            <view class="label row">{{video.desc}}</view>
        </view>
        </template>
    </view>
</view>
</template>

<script>
import {
  formatImg
} from '../common/index.js'
export default {
    data() {
        return {
            videos:[],
            id:''
        }
    },
    components: {

    },
    onLoad(options){
        if(options.id){
            this.id =options.id
            this.getData(options.id)
        }
    },
    filters: {
        imgUrl(val, param = "") {
            return formatImg(val, param)
        }
    },
    methods:{
        getData(id){
            this.$ajax.get('build/videoList.html',{id},res=>{
                if(res.data.code === 1){
                    this.videos = res.data.list
                }
            },err=>{

            })
        },
        videoErrorCallback(e){
            console.log(e)
        },
        toVr(video){
            this.$navigateTo('/vr/prevideo?id='+this.id+"&video_id="+video.id)
        }
    }
}
</script>

<style scoped lang="scss">
.label{
    font-size: 32upx;
    padding: 20upx;
}
.no_data {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    font-size: 24rpx;
}
.video_list{
    padding: 24upx;
    .video_item{
        width: 100%;
        margin-bottom: 30upx;
        background-color: #fff;
        border-radius: 8upx;
        overflow: hidden;
        box-shadow: 0 0 18upx #dedede;
        .video_box{
            width: 100%;
            height: 56vw;
            // border-radius: 8upx;
            // overflow: hidden;
            position: relative;
            .video_img{
                width: 100%;
                height: 100%;
            }
            .video-icon{
                position: absolute;
                width: 120upx;
                height: 120upx;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                margin: auto;
                z-index: 2;
            }
            video{
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>
