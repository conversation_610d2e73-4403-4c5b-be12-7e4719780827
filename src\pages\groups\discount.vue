<template>
  <view class="page" v-if="this.id">
    <view class="top">
      <image v-if="detail.img" class="top-bg" :src="detail.img | imageFilter('w_8601')" mode="widthFix"></image>
      <image
        v-if="!detail.img && startDraw"
        class="top-bg"
        :src="topSrc | imageFilter('w_8601')"
        mode="widthFix"
      ></image>
      <view class="top-card" v-if="!detail.img && startDraw">
        <view class="ellipsis card-title">{{ detail.title }}</view>
        <view class="ellipsis card-content">{{ detail.discount }}</view>
        <view class="card-date">● {{ detail.during }} ●</view>
      </view>
    </view>
    <view class="content">
      <view class="house" v-if="build.length > 0">
        <view
          class="house-left"
          v-for="(item, index) in build"
          :key="index"
          @click="$navigateTo(`/pages/new_house/detail?id=${item.id}`)"
        >
          <image class="house-image" :src="item.img" mode="aspectFill"></image>
          <view class="house-info">
            <view class="house-title">
              <view class="house-name">{{ item.title }}</view>
              <view class="house-right">
                <view class="house-detail">楼盘详情</view>
                <my-icon type="ic_into" color="#999999" size="24rpx"></my-icon>
              </view>
            </view>
            <view class="house-labels">
              <view class="labels-box" v-for="(label, index) in item.label" :key="index">{{ label }}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="detail">
        <view class="detail-icon">
          <image :src="title_bg" mode="widthFix"></image>
          <view class="icon-text">优惠详情</view>
        </view>
        <view class="detail-title" v-if="detail.img">{{ detail.title }}，{{ detail.discount }}</view>
        <view class="detail-title" v-else>优惠信息</view>
        <view class="detail-date">活动时间：{{ detail.during }}</view>
        <view class="detail-content">
          <!-- #ifdef H5 -->
          <view class="group-content" v-html="detail.content"></view>
          <!-- #endif -->
          <!-- #ifndef H5 -->
          <u-parse :html="detail.content"></u-parse>
          <!-- #endif -->
        </view>
      </view>
    </view>
    <view class="bottom">
      <view class="line"></view>
      <view class="bottom-content">
        <view class="bottom-left" @click="showSharePop">
          <my-icon type="ic_fenxiang" color="#222222" size="48rpx"></my-icon>
          <view class="fenxiang">分享</view>
        </view>
        <view class="bottom-right">
          <view class="bottom-num">已有{{ detail.count }}人领取</view>
          <view class="to-baoming flex-1 text-center submit-baoming bottom-btn" @click="toSubForme()">{{
            detail.is_free == 1 ? detail.price + '元报名' : '立即报名'
          }}</view>
        </view>
      </view>
    </view>
    <sub-form :groupCount="detail.count" :sub_type="sub_type" :sub_mode="sub_mode" :sub_title="sub_title"  :sub_content="sub_content" :sub_submit="sub_submit" ref='sub_form' :after_login_auto_handle="false" @onsubmit="handleSubForm" @after_login="handleSubForm"></sub-form>
		<share-pop ref="show_share_pop" @copyLink="copyLink" @appShare="appShare" @handleCreat='handleCreat' @showCopywriting='showCopywriting'></share-pop>
		<!-- #ifdef H5 || APP-PLUS -->
		<my-popup ref="pay_box">
				<pay ref="pay" :pay_params="pay_params" h5WxPayApi="/wapi/buildGroupPay/wxPayByWap.html" h5AliPayApi="/wapi/buildGroupPay/aliPay.html" show_cancel @pay_success="paySuccess" @cancel="$refs.pay_box.hide()"></pay>
		</my-popup>
		<!-- #endif -->
		<chat-tip></chat-tip>
		<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
  </view>
</template>

<script>
import { formatImg, getSceneParams, config } from '../../common/index.js'
import myPopup from '../../components/myPopup.vue'
import myIcon from '../../components/myIcon.vue'
import subForm from '../../components/subForm'
import sharePop from '../../components/sharePop'
import { showModal } from '../../common/index.js'
import shareTip from '../../components/shareTip'
import pay from '@/components/pay'
export default {
  components: { myIcon, sharePop },
  data() {
    return {
      topSrc: config.imgDomain + '/group/discount.png',
      title_bg: config.imgDomain + '/group/title_bg.png',
      id: '',
      detail: {},
      build: {},
      seo: {},
			sub_submit:"",
			bulletList:[],
			sub_type:6,
			sub_title:'',
			sub_content:'',
			// group_id:0,  //用户提交报名以后支付需要用
			// pay_type:"", //支付方式切换
			pay_params:{},
      startDraw: false,
      show_share_tip:false,
			link:'',
    }
  },
  components: {
    myIcon,
		myPopup,
		subForm,
		sharePop,
		shareTip,
    pay
  },
  onLoad(options) {
    // #ifdef MP
    // 如果是扫码进来的
    if (options.scene) {
      const params = getSceneParams(decodeURIComponent(options.scene))
      if (params.id) {
        this.id = params.id
        this.getData()
      }
      return
    }
    // #endif

    if (options.id) {
      this.id = options.id
      this.getData()
    }
    if (this.$store.state.tempData) {
      this.detail = this.$store.state.tempData
      if (this.$store.state.tempData.title) {
        uni.setNavigationBarTitle({
          title: this.detail.title,
        })
      }
    } else if (options.title) {
      this.detail.title = decodeURIComponent(options.title)
      uni.setNavigationBarTitle({
        title: this.detail.title,
      })
    }
  },
  onUnload() {
    this.$store.state.tempData = {}
  },
  computed: {
    sub_mode() {
      return this.$store.state.sub_form_mode
    },
  },
  methods: {
    getData() {
      this.$ajax.get('build/groupDetail.html', { id: this.id }, (res) => {
        if (res.data.code == 1) {
          //const regex = new RegExp('<img', 'gi');
          // 正则匹配处理富文本图片过大显示问题
          //res.data.detail.content = res.data.detail.content.replace(regex, `<img style="max-width: 100%;"`);
          this.detail = res.data.detail
          this.bulletList = res.data.detail.sign_list
          this.build = res.data.build
          uni.setNavigationBarTitle({
            title: this.detail.title,
          })
          this.startDraw = true
        }
        if (res.data.share) {
          this.share = res.data.share
        } else {
          this.share = {
            title: this.detail.title,
            content: this.detail.discount,
            pic: this.detail.img,
          }
        }
        this.getWxConfig(['chooseWXPay','updateAppMessageShareData','updateTimelineShareData'],
							wx => {
							console.log('执行回调')
							this.wx = wx
						})
      })
    },
    showSharePop() {
      this.getShortLink()
      this.$refs.show_share_pop.show()
    },
    getShortLink(){
				// #ifdef H5
				this.link = window.location.href
				// #endif
				// #ifndef H5
				this.link = config.apiDomain +'/h5/pages/groups/detail?id=' + this.id 
				// #endif
				this.$ajax.get("build/shortUrl.html",{page_url:this.link},(res)=>{
					if(res.data.code ==1){
						this.link=res.data.short_url
					}
				})
			},
    showCopywriting(){
					const address=this.detail.address?`【活动地点】${this.detail.address}`:''
					const text = `【看房团名称】${this.detail.title}
【活动时间】${this.detail.during}
${address}
【报名人数】已有${this.detail.count}人报名
【访问链接】${this.link}`
					this.copyWechatNum(text, ()=>{
						this.copy_success = true
					})
			},
    // #ifdef H5
    copyLink() {
      this.show_share_tip=true
			return;
      let link = window.location.href
      this.copyWechatNum(link, () => {
        uni.showToast({
          title: '复制成功,去发送给好友吧',
          icon: 'none',
        })
      })
    },
    // #endif
    // #ifndef H5
    copyWechatNum(cont) {
      uni.setClipboardData({
        data: cont,
        success: (res) => {
          // uni.showToast({
          //   title: "复制成功",
          //   icon: "none"
          // })
        },
      })
    },
    // #endif
    // #ifdef H5
    copyWechatNum(cont, callback) {
      let oInput = document.createElement('textarea')
      oInput.value = cont
      document.body.appendChild(oInput)
      oInput.style.opacity = 0
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length)
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      })
      oInput.blur()
      oInput.remove()
      if (callback) callback()
    },
    // #endif
    toHome() {
      uni.switchTab({
        url: '/pages/index/index',
      })
    },
    handelTel() {
      uni.makePhoneCall({
        phoneNumber: this.detail.tel,
      })
    },
    toMap() {
      if (this.build.lat > 0 && this.build.lng > 0) {
        uni.openLocation({
          latitude: parseFloat(this.build.lat),
          longitude: parseFloat(this.build.lng),
          success: function () {
            console.log('success')
          },
        })
      }
    },
    toSubForme() {
      if (this.detail.status == 2) {
        uni.showToast({
          title: '此活动报名已结束',
          icon: 'none',
        })
        return
      }
      if(this.detail.is_free==1 && this.$store.state.user_login_status == 1){
					uni.removeStorageSync('token')
					this.$navigateTo('/user/login/login')
					return
			}
      this.sub_title = '预约报名'
      this.sub_content = '为方便通知到您最新的信息，请输入您的手机号码'
      this.sub_submit = this.detail.is_free == 1 ? this.detail.price + '元预约' : '提交预约'
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e) {
      //提交报名
      e.group_id = this.id
      e.from = '看房团页'
      e.type = this.sub_type
      if (this.detail.is_free == 1) {
        this.$ajax.get('buildGroupPay/checkBuildGroupPay.html', { group_id: this.detail.id }, (res) => {
          uni.hideLoading()
          if(res.data.status === 1){
							uni.removeStorageSync('token')
							this.$navigateTo('/user/login/login')
							return
						}
						if(res.data.status === 2){
							this.$refs.sub_form.getVerify()
							return
						}
          if (res.data.code !== 1) {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
            return
          }
          showModal({
            title: '提示',
            content: '需要支付' + this.detail.price + '元报名' || '',
            cancelText: '暂不支付',
            confirmText: '立即支付',
            confirm: () => {
              this.group_id = this.detail.id
              this.pay_params = {
                cname: e.name,
                tel: e.tel,
                group_id: this.detail.id,
              }
              this.handlePay()
            },
            fail: () => {},
            complete: () => {},
          })
        })
        return
      }
      this.$ajax.post('build/signUp.html',e,res=>{
					uni.hideLoading()
					if(res.data.code === 1){
						if(this.sub_mode!==2||res.data.status === 3){ //提示报名成功
							uni.showToast({
								title:res.data.msg,
								icon:"none"
							})
							this.$refs.sub_form.closeSub()
						}else if(res.data.status === 1){
							uni.removeStorageSync('token')
							this.$navigateTo('/user/login/login')
						}else if(res.data.status === 2){
							this.$refs.sub_form.getVerify()
						}
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
				})
    },
    handlePay(){
			uni.showLoading({
				title:"正在处理...",
				mask: true
			})
			this.mpWxPay()
		},
    mpWxPay(){
				this.$ajax.get('buildGroupPay/wxPay.html',this.pay_params,res=>{
					uni.hideLoading()
					if(res.data.code === 1){
							let pay_info = res.data.data
							this.wx.chooseWXPay({
									// provider: 'wxpay',
									timestamp:pay_info.timeStamp,
									nonceStr:pay_info.nonceStr,
									package:pay_info.package,
									signType:pay_info.signType,
									paySign:pay_info.paySign,
									success: (res)=> {
											uni.showToast({
													title:"支付成功"
											})
											this.$refs.sub_form.closeSub()
											setTimeout(()=>{
												this.$navigateTo('/user/order_list')
											}, 1500)
											// this.$refs.upgrade_success.show()
									},
									fail: function (err) {
											console.log(err);
											uni.showToast({
													title:err.err_desc||(err.errMsg=='requestPayment:fail cancel'?'已取消支付':err.errMsg),
													icon:"none",
													duration:5000
											})
									}
							})
					}else {
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
				})
      },
    handleCreat(){
			this.$navigateTo(`${location.origin}/wapi/poster/branch?type=7&id=${this.id}&header_from=2`)
		},
    lookAll() {
      console.log('查看全部')
    },
  },
  onShareAppMessage() {
    return {
      title: this.detail.title || '',
      content: this.detail.discount || '',
      imageUrl: this.detail.img ? formatImg(this.detail.img, 'w_6401') : '',
    }
  },
}
</script>

<style lang="scss" scoped>
.top-img {
  height: fit-content;
  .top-image {
    width: 750rpx;
  }
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.page {
  flex-direction: column;
  background: #ffffff;
  min-height: calc(100vh - 44px);
}
.top {
  height: fit-content;
  text-align: center;
  .top-bg {
    width: 100vw;
  }
  .top-card {
    position: absolute;
    top: 64rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 654rpx;
    height: 357rpx;
    box-sizing: border-box;
    padding-top: 48rpx;
    padding: 48rpx 20rpx 0 20rpx;
    text-align: center;
    .card-title {
      font-size: 36rpx;
      color: #b96100;
      letter-spacing: 2.24rpx;
    }
    .card-content {
      padding-top: 24rpx;
      padding-bottom: 24rpx;
      font-size: 64rpx;
      color: #b96100;
      letter-spacing: 4rpx;
    }
    .card-date {
      width: 360rpx;
      height: 52rpx;
      line-height: 52rpx;
      margin: 0 auto;
      background: #ffffff;
      border-radius: 26rpx;
      font-size: 28rpx;
      color: #b96100;
    }
  }
}
.content {
  position: relative;
  width: 100vw;
  padding-top: 48rpx;
  background: #fff;
  border-radius: 30rpx 30rpx 0 0;
  margin-top: -32rpx;
}
.house {
  margin: 0 auto;
  box-sizing: border-box;
  width: 654rpx;
  height: 176rpx;
  border: 0 solid #f3f3f3;
  box-shadow: 0 0 24rpx 0 rgba(0, 0, 0, 0.06);
  border-radius: 16rpx;
  // align-items: center;
  padding: 40rpx 24rpx 40rpx 48rpx;
  .house-left {
    display: flex;
    .house-image {
      width: 96rpx;
      height: 96rpx;
      border-radius: 10rpx;
      background: #ff5c00;
    }
    .house-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding-left: 24rpx;
      width: 462rpx;
      .house-title {
        display: flex;
        justify-content: space-between;
      }
      .house-name {
        flex: 1;
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .house-labels {
        display: flex;
        overflow: hidden;
        .labels-box {
          flex-shrink: 0;
          height: 32rpx;
          line-height: 32rpx;
          padding: 0 8rpx;
          border: 1rpx solid #d8d8d8;
          border-radius: 4rpx;
          margin-right: 16rpx;
          font-size: 22rpx;
          color: #999999;
        }
      }
    }
  }
  .house-right {
    display: flex;
    align-items: center;
    align-self: flex-start;
    .house-detail {
      color: #999999;
      font-size: 22rpx;
      padding-right: 31rpx;
    }
  }
}
.detail {
  width: 654rpx;
  position: relative;
  border: 0 solid #f3f3f3;
  box-shadow: 0 0 24rpx 0 rgba(0, 0, 0, 0.06);
  border-radius: 16rpx;
  padding: 120rpx 48rpx 48rpx 48rpx;
  box-sizing: border-box;
  margin: 0 auto;
  margin-top: 66rpx;
  margin-bottom: 160rpx;
  .detail-icon {
    position: absolute;
    left: 50%;
    top: -15rpx;
    transform: translateX(-50%);
    text-align: center;
    image {
      width: 384rpx;
    }
    .icon-text {
      position: absolute;
      top: 16rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 36rpx;
      color: #ffffff;
    }
  }
  .detail-title {
    font-size: 32rpx;
    color: #333333;
    font-weight: bold;
  }
  .detail-date {
    padding: 24rpx 0;
    font-size: 22rpx;
    color: #666666;
  }
}
.bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 111rpx;
  .line {
    height: 1rpx;
    transform: scaleY(0.5);
    background: #d8d8d8;
  }
}
.bottom-content {
  height: 110rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding-left: 48rpx;
  padding-right: 48rpx;
  background: #ffffff;
  .bottom-left {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 80rpx;
    .fenxiang {
      font-size: 20rpx;
      color: #333333;
    }
  }
  .bottom-right {
    display: flex;
    align-items: center;
    height: 80rpx;
    .bottom-num {
      font-size: 28rpx;
      color: #ff6b01;
      padding-right: 24rpx;
    }
    .bottom-btn {
      width: 316rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background-image: linear-gradient(125deg, #ff5500 0%, #ffa402 100%);
      box-shadow: 0 8rpx 20rpx 0 rgba(255, 145, 1, 0.4);
      border-radius: 40rpx;
      font-size: 32rpx;
      color: #ffffff;
    }
  }
}
</style>