<template>
    <view class="hb">
        <my-popup ref="popup" position="top" top_0>
			<view class="hb-box">
				<view class="img-box">
                    <template v-if="popupData.type==1">
                        <!-- #ifndef MP-BAIDU  -->
                        <image class="hb" :src="popupData.img_popup | imgUrl" mode="widthFix"></image>
                        <!-- #endif -->
                        <!-- #ifdef MP-BAIDU  -->
                        <image class="hb" :src="popupData.img_popup+'?x-oss-process=style/w_6401'" mode="widthFix"></image>
                        <!-- #endif -->
                        <view class="desc">{{popupData.desc}}</view>
                        <view class="button" @click.stop.prevent="handleHbBtn()">立即领取</view>
                    </template>
                    <template v-else>
                        <!-- #ifndef MP-BAIDU  -->
                        <image class="hb" @click.stop.prevent="handleHbBtn()" :src="popupData.img_popup | imgUrl" mode="widthFix"></image>
                        <!-- #endif -->
                         <!-- #ifdef MP-BAIDU  -->
                        <image class="hb" @click.stop.prevent="handleHbBtn()" :src="popupData.img_popup+'?x-oss-process=style/w_6401'" mode="widthFix"></image>
                        <!-- #endif -->
                    </template>
					<view class="close" @click="hideHb()">
						<uni-icons type="close" color="#fff" size="36"></uni-icons>
					</view>
				</view>
			</view>
		</my-popup>
    </view>
</template>

<script>
    import myPopup from './myPopup.vue'
    import {uniIcons} from '@dcloudio/uni-ui'
    import {formatImg} from '../common/index.js'
    export default {
        props:{
            popupData:Object
        },
        components:{
            myPopup,
            uniIcons
        },
        data(){
            return {
                
            }
        },
        filters:{
            imgUrl(val){
                return formatImg(val,'w_6401')
            }
        },
        methods: {
            handleHbBtn(){
				//如果是红包则跳转报名
				if(this.popupData.type==1){
					this.$navigateTo('/user/sub_form/sub_form?bid='+this.popupData.bid+'&title='+this.popupData.title+'&from=1&type=1&popup_desc='+this.popupData.desc)
					setTimeout(()=>{
						this.hideHb()
					},300)
					return
				}
				//如果不是红包是广告
				if(this.popupData.event==1){ //如果是小程序内跳转
					this.$navigateTo(this.popupData.url)
				}else{ //如果是超链接
					this.$navigateTo('/pages/web_view/web_view?url='+this.popupData.url)
				}
				
				setTimeout(()=>{
					this.hideHb()
				},300)
            },
            showHb(){
                this.$refs.popup.show()
            },
            hideHb(){
				this.$refs.popup.hide()
			}
        }
    }
</script>

<style lang="scss">
.hb-box{
    height: 100vh;
    width: 100%;
    position: relative;
    .img-box{
        width: 80%;
        position: absolute;
        top: 15vh;
        left: 0;
        right: 0;
        margin: auto
    }
    .hb{
        width: 100%
    }
    .desc{
        position: absolute;
        text-align: center;
        bottom:50vw;
        left: 0;
        right: 0;
        color: #fdf453;
        font-size: 70upx;
    }
    .button{
        position: absolute;
        text-align: center;
        bottom:28vw;
        width: 40vw;
        border-radius: 6upx;
        padding: 18upx;
        left: 0;
        right: 0;
        margin: auto;
        background-color: #fdf453;
        color: #ff2d4b;
        font-size: $uni-font-size-blg;
    }
    .close{
        position: absolute;
        bottom:0;
        width: 20vw;
        text-align: center;
        padding: 18upx;
        left: 0;
        right: 0;
        margin: auto;
    }
}
</style>