<template>
  <view>
    <view class="block">
      <view class="info-row avatar-row">
        <view class="label">上传头像</view>
        <view class="right flex-row">
          <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadHeaderImg">
            <image class="img" mode="aspectFill" v-if="(params.agent&&params.agent.uncheck_prelogo)||params.prelogo" :src="((params.agent&&params.agent.uncheck_prelogo)||params.prelogo )| imageFilter('w_120')"></image>
            <view v-else class="upload-btn">
              <my-icon type="ic_jia" size="46rpx" color="#d8d8d8"></my-icon>
            </view>
          </my-upload>
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="block">
      <view class="info-row">
        <view class="label">联系人</view>
        <view class="right flex-row">
          <input type="text" v-model="params.cname" placeholder="请输入" placeholder-style="text-align:right;font-size:32rpx;color:#999">
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="block">
      <view class="info-row">
        <view class="label">手机号码</view>
        <view class="right flex-row">
          <input
            class="flex-1"
            disabled
            type="number"
            maxlength="11"
            v-model="params.tel"
            placeholder="请输入"
            placeholder-style="text-align:right;font-size:32rpx;color:#999"
          />
          <view class="send-code" @click="toEditPhone">修改手机号</view>
        </view>
      </view>
    </view>
    <view class="block">
      <view class="info-row">
        <view class="label">店铺名</view>
        <view class="right flex-row"  @click="$navigateTo('/shops/search')">
          <input :value="store_name" disabled="disabled" placeholder="请选择" placeholder-style="text-align:right;font-size:32rpx;color:#999">
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
      <!-- <view class="info-row">
        <view class="label">店铺名</view>
        <view class="right flex-row">
            <picker  @change="pickerChange" :value="index" :range="shopsList" range-key="name">
                {{index>=0?shopsList[index].name:'请选择店铺'}}
			      </picker>
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view> -->
    </view>
    <view class="block">
      <view class="info-column">
        <view class="label">描述</view>
        <textarea type="text" v-model="params.introduce" placeholder="请输入" placeholder-style="font-size:32rpx;color:#999"></textarea>
      </view>
    </view>
    <view class="block">
      <view class="upload-row">
        <view class="title flex-row">
          <text>微信二维码</text>
        </view>
        <view class="upload-box">
          <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadQrCode">
            <image class="img" mode="aspectFill" v-if="params.wechat_img" :src="params.wechat_img | imageFilter('w_240')"></image>
            <view v-else class="upload-btn">
              <my-icon type="ic_jia" size="96rpx" color="#d8d8d8"></my-icon>
            </view>
          </my-upload>
        </view>
      </view>
    </view>
    <view class="block" v-if='isChecking'>
      <view class="info-row">
        <view class="label">审核状态</view>
        <view class="right flex-row red">
          正在审核中
        </view>
      </view>
    </view>
    <view class="btn" @click="onSubmit">保存</view>
  </view>
</template>

<script>
import myIcon from "../components/myIcon"
import myUpload from '../components/form/myUpload'
export default {
  components: {
    myIcon,
    myUpload
  },
  data () {
    return {
      params:{
        prelogo:"",
        introduce:"",
        cname:"",
        tel:"",
        wechat_img:"",
        store_id:0 
      },
    shopsList:[{id:0,name:"请选择"}],
    index:0,
    isChecking:false,
    store_name:''
    }
  },
  onLoad(){
    // #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
    uni.hideShareMenu()
    // #endif
    // this.getShopsList()
    this.getData()
    
  },
  onShow(){
    if (uni.getStorageSync('smallStore')) {
      let smallStore = JSON.parse(uni.getStorageSync('smallStore'))
      this.params.store_id = smallStore.id
      this.store_name =smallStore.name
    }
    uni.removeStorageSync('smallStore')
  },
  methods: {
    getData(){
      this.$ajax.get('agentCompany/editAgentCard.html',{},res=>{
        if(res.data.code == 1){
            // res.data.info.prelogo = res.data.user.prelogo[0]||''
            // res.data.info.wechat_img = res.data.user.wechat_img[0]||''
            if (this.$store.state.user_info.agent&&this.$store.state.user_info.agent.uncheck==1){
              this.isChecking =true
              if (this.$store.state.user_info.agent.uncheck_cname){
                res.data.info.cname  =this.$store.state.user_info.agent.uncheck_cname
              }
              if (this.$store.state.user_info.agent.uncheck_prelogo){
                res.data.info.prelogo  =this.$store.state.user_info.agent.uncheck_prelogo
              }
            }
            // if (res.data.info.uncheck_cname){
            //   this.isChecking=true
            //   res.data.info.cname =res.data.info.uncheck_cname
            // }
            // if (res.data.info.uncheck_prelogo){
            //   res.data.info.prelogo =res.data.info.uncheck_prelogo
            //   this.isChecking=true
            // }
            this.params = res.data.info
            
            this.store_name=this.params.store_name
            // this.getShopsList(this.params.id)
            
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:"none"
          })
        }
      })
    },
    pickerChange(e){
        this.index=e.detail.value
        this.params.store_id=this.shopsList[this.index].id
    },
    getShopsList(id){
        this.$ajax.get('agentCompany/agentStoreList.html',{},res=>{
            if(res.data.code == 1){
                this.shopsList=this.shopsList.concat(res.data.list)
                    this.shopsList.map((item,index)=>{
                      if (item.store_id ==id){
                        this.index=index
                      }
                    })
                
            }else{
                uni.showToast({
                    title:res.data.msg,
                    icon:"none"
                })
            }
        })
    },
    toEditPhone() {
      uni.$once('getDataAgain', ()=>{
        this.getData()
      })
      this.$navigateTo('/user/bind_phone/bind_phone?type=edit')
    },
    uploadQrCode(e){
        this.params.wechat_img = e.files[0]
    },
    uploadHeaderImg(e){
      if (this.params.agent&&this.params.agent.uncheck_prelogo){
        this.params.agent.uncheck_prelogo =''
      }
      this.params.prelogo = e.files[0]
    },
    onSubmit(){
        uni.showLoading({
          title:"提交中",
          mask: true
        })
        this.$ajax.post('agentCompany/editAgentCard.html',this.params,res=>{
            uni.hideLoading()
            if(res.data.code == 1){
            uni.showToast({
                title:res.data.msg,
                icon: "none"
            })
            setTimeout(()=>{
                uni.$emit('onChangeWechat',this.params)
                this.$navigateBack()
            },1500)
            }else{
                uni.showToast({
                    title:res.data.msg,
                    icon:"none"
                })
            }
        })
    }
  }
}
</script>

<style scoped lang="scss">
view{
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.flex-row{
  flex-direction: row;
}
.block{
  padding: 0 48rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  .info-row{
    width: 100%;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 48rpx 0;
    &.avatar-row{
      padding: 24rpx 0;
      .upload-btn{
        width: 96rpx;
        height: 96rpx;
      }
      .img{
        width: 96rpx;
        height: 96rpx;
      }
    }
    input{
      text-align: right;
    }
  }
  .info-column{
    padding: 48rpx 0;
    .label{
      margin-bottom: 20rpx;
    }
    textarea{
      width: 100%;
      height: 200rpx;
    }
  }
  .label{
    width: 160rpx;
    font-size: 32rpx;
    color: #666;
  }
  .right{
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    &.red{
      color: #f00;
    }
    .icon-box{
      margin-left: 16rpx;
    }
    .send-code {
      padding: 0 20rpx;
      margin-left: 24rpx;
      line-height: 50rpx;
      border-radius: 25rpx;
      border: 1rpx solid $uni-color-primary;
      color: $uni-color-primary;
      &.disable {
        color: #999;
        background-color: #f5f5f5;
        border-color: #999;
      }
    }
  }
  .title{
    justify-content: space-between;
    align-items: center;
    font-size: 32rpx;
    margin-bottom: 24rpx;
    color: #666;
  }
  .upload-row{
    padding: 48rpx 0;
  }
  .upload-btn{
    width: 25vw;
    height:25vw;
    align-items: center;
    justify-content: center;
    border-radius: 8rpx;
    background-color: #f5f5f5;
  }
  .img{
    width: 25vw;
    height: 25vw;
    border-radius: 8rpx;
  }
}

.btn{
  margin: 24rpx 48rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  background-color: $uni-color-primary;
  box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
  &.ordinary{
    background-color: #fff;
    color: $uni-color-primary;
    border: 1rpx solid $uni-color-primary;
  }
}
</style>