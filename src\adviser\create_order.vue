<template>
<view class="create_order">
    <view class="card">
        <view class="title bottom-line">客户信息：</view>
        <view class="card_content">
            <my-input small label_width="160rpx" label="客户姓名" placeholder="请输入您的姓名" name="uname" @input="handleInputOrderInfo"></my-input>
            <my-input small label_width="160rpx" label="客户手机号" placeholder="请输入您的手机号" type="number" name="tel" @input="handleInputOrderInfo"></my-input>
            <my-input small label_width="160rpx" label="身份证号码" placeholder="请输入您的身份证号码" type="idcard" name="id_card" @input="handleInputOrderInfo"></my-input>
        </view>
    </view>
    <view class="btn-box">
        <tab-bar :tabs="navs" :nowIndex="-1" :theme="2" :fixedTop="false" small @click="switchTab"></tab-bar>
    </view>
    <!-- <view class="add-btn-box">
        <view class="add-btn" @click="chooseHouse()">
            <view><my-icon type="zengjia" size="36"></my-icon></view>
            <view>选择房源</view>
        </view>
    </view> -->
    <!-- <view class="card" v-show="house_info.house_id">
        <view class="title bottom-line">房源信息：</view>
        <view class="card_content">
            <my-select small label="所属楼栋" :value="house_info.number_id" @change="pickerChange" :range="loudong_list" name="number_id"></my-select>
            <my-select small disabled label="所属单元" :value="house_info.unit_id" @change="pickerChange" :range="unit_list" name="unit_id"></my-select>
            <my-input small disabled label="所在楼层" :value="house_info.floor" type="number" placeholder="请输入所在楼层" name="floor" @input="handleInput"></my-input>
            <my-input small disabled label="房号" :value="house_info.name" type="number" placeholder="请输入房号" name="name" @input="handleInput"></my-input>
            <my-input small disabled label="室" :value="house_info.shi" placeholder="请输入室" type="number" name="shi" @input="handleInput"></my-input>
            <my-input small disabled label="厅" :value="house_info.ting" placeholder="请输入厅" type="number" name="ting" @input="handleInput"></my-input>
            <my-input small disabled label="卫" :value="house_info.wei" placeholder="请输入卫" type="number" name="wei" @input="handleInput"></my-input>
            <my-input small disabled label="建筑面积" :value="house_info.jzmj" unit="m²" placeholder="请输入建筑面积" type="number" name="jzmj" @input="handleInput"></my-input>
            <my-input small disabled label="折扣总价" :value="house_info.discount_price" placeholder="请输入房源折扣后价格" type="text" name="discount_price" @input="handleInput"></my-input>
            <my-input small disabled label="定金" :value="house_info.earnest_money" placeholder="请输入定金" type="number" name="earnest_money" @input="handleInput"></my-input>
        </view>
    </view> -->
    <view class="card bottom-line" v-show="house_info.house_id">
        <view class="title bottom-line">户型信息</view>
        <view class="card_content info_list">
            <view class="w-100 bottom-line">
                <text class="label">所属楼栋</text>
                <text class="text ">{{house_info.number_name}}</text>
            </view>
            <view class="w-100 bottom-line">
                <text class="label">所属单元</text>
                <text class="text ">{{house_info.unit_name}}</text>
            </view>
            <view class="w-100 bottom-line">
                <text class="label">所在楼层</text>
                <text class="text ">{{house_info.floor}}</text>
            </view>
            <view class="w-100 bottom-line">
                <text class="label">房号</text>
                <text class="text ">{{house_info.name}}</text>
            </view>
            <view class="w-100 bottom-line">
                <text class="label">室</text>
                <text class="text ">{{house_info.shi}}</text>
            </view>
            <view class="w-100 bottom-line">
                <text class="label">厅</text>
                <text class="text ">{{house_info.ting}}</text>
            </view>
            <view class="w-100 bottom-line">
                <text class="label">卫</text>
                <text class="text ">{{house_info.wei}}</text>
            </view>
            <view class="w-100 bottom-line">
                <text class="label">建筑面积</text>
                <text class="text ">{{house_info.jzmj}}</text>
            </view>
            <view class="w-100 bottom-line">
                <text class="label">折扣后总价</text>
                <text class="text ">{{house_info.discount_price}}</text>
            </view>
            <view class="w-100 bottom-line">
                <text class="label">定金</text>
                <text class="text ">{{house_info.earnest_money}}</text>
            </view>
        </view>
    </view>
    <view class="btn-box">
        <button class="default" @click="createOrder()">生成订单</button>
    </view>
</view>
</template>

<script>
import myInput from "../components/form/myInput.vue"
import mySelect from "../components/form/mySelect.vue"
import tabBar from "../components/tabBar.vue"
// import myIcon from "../components/icon"
import {navigateTo} from "../common/index.js"
export default {
    data() {
        return {
            order_info:{
                tel: "", //客户手机号
                uname: "", // 客户姓名
                id_card: "", // 客户身份证号
            },
            house_info:{
                online_id: "",//项目id
                number_id: "", //楼栋id
                earnest_money: "", //定金
                floor: "", // 楼层
                jzmj: "", // 建筑面积
                discount_price: "", // 折扣后总价
                name: "", // 房号
                unit_id: "", //  单元id
                shi: "", //室
                ting: "", // 厅
                wei: "" // 卫
            },
            loudong_list:[],
            unit_list:[],
            show_add:false,
            navs:[
                {
                    type:1,
                    name:"选择房源",
                },
                {
                    type:2,
                    name:"添加房源"
                }
            ],
        }
    },
    components: {
        myInput,
        mySelect,
        tabBar,
        // myIcon,
    },
    onLoad(options){
        this.house_info.online_id = options.online_id || ''
        this.online_id = options.online_id || ''
        // this.getLoudong()
        uni.$on('onChooseHouse', this.getHouse)
    },
    onUnload(){
        uni.$off('onChooseHouse')
    },
    methods: {
        handleInputOrderInfo(e){
            this.order_info[e._name] = e.detail.value
        },
        handleInput(e){
            this.house_info[e._name] = e.detail.value
        },
        switchTab(e){
            if(e.type == 1){
                this.chooseHouse()
            }
            if(e.type == 2){
                this.toAddHouse()
            }
        },
        chooseHouse(){
            navigateTo(`/adviser/house_list?online_id=${this.online_id}&type=choose`)
        },
        toAddHouse(){
            navigateTo(`/adviser/manage_house?online_id=${this.online_id}`)
        },
        getLoudong(){
            this.$ajax.get('online/buildingNumber',{online_id:this.online_id},res=>{
                if(res.data.code === 1){
                    this.loudong_list = res.data.number.map(item=>{
                        return {value:item.id,name:item.number}
                    })
                }
            })
        },
        getUnit(number_id){
            this.$ajax.get('online/buildingUnit',{online_id:this.online_id,number_id},res=>{
                if(res.data.code === 1){
                    this.unit_list = res.data.lists.map(item=>{
                        return {value:item.id,name:item.unit}
                    })
                }
            })
        },
        pickerChange(e){
            this.house_info[e._name] = e.value
            if(e._name==='number_id'){
                this.getUnit(e.value)
            }
        },
        /** 
         * <AUTHOR> 
         * @date 2020-03-03 17:06:58 
         * @desc 获取房源信息 
         */
        getHouse(params){
            params.online_id = this.online_id
            this.$ajax.get('online/houseDetailByAdviser.html',params,res=>{
                if(res.data.code === 1){
                    // this.getUnit(res.data.house.number_id)
                    this.house_info = res.data.house
                }
            })
        },
        handleCreate(){
            if(this.show_add){
                // 添加房源
                this.addHouse()
            }else{
                // 选择后修改房源
                this.editHouse()
            }
        },
        /** 
         * <AUTHOR> 
         * @date 2020-03-04 09:58:28 
         * @desc 添加房源 
         */
        addHouse(){
            this.$ajax.post('online/addHouseByAdviser.html',this.house_info,res=>{
                if(res.data.code === 1){
                    if(res.data.house_id){
                        this.createOrder(res.data.house_id)
                    }
                    return
                }
                if(res.data.code === -12){
                    // 房源已经存在
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                    return
                }
                uni.showToast({
                    title:res.data.msg,
                    icon:'none'
                })
            })
        },
        /** 
         * <AUTHOR> 
         * @date 2020-03-04 09:58:35 
         * @desc 编辑房源 
         */
        editHouse(){
            this.$ajax.post('online/editHouseByAdviser.html',this.house_info,res=>{
                if(res.data.code === 1){
                    this.createOrder(res.data.house_id)
                }
            })
        },
        /** 
         * <AUTHOR> 
         * @date 2020-03-04 09:58:40 
         * @desc 创建订单 
         */
        createOrder(house_id){
            if(!this.order_info.uname){
                uni.showToast({
                    title:"请输入客户名称",
                    icon:"none"
                })
                return false
            }
            if(!this.order_info.tel){
                uni.showToast({
                    title:"请输入手机号",
                    icon:"none"
                })
                return false
            }
            if(this.order_info.tel.length!==11||this.order_info.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return false
            }
            let id_card_reg = /(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/
            if(this.order_info.id_card&&!id_card_reg.test(this.order_info.id_card)){
                uni.showToast({
                    title:"身份证号格式不正确",
                    icon:"none"
                })
                return false
            }
            this.order_info.house_id = house_id||this.house_info.house_id
            this.order_info.online_id = this.online_id
            if(!this.order_info.house_id){
                uni.showToast({
                    title:"请选择或添加房源",
                    icon:"none"
                })
                return false
            }
            this.$ajax.post('online/orderDoneByAdviser',this.order_info,res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title: res.data.msg
                    })
                    setTimeout(()=>{
                        navigateTo(`/adviser/order_detail?id=${res.data.order_id}`)
                    },1500)
                }else{
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
    },
}
</script>

<style scoped lang="scss">
.create_order{
    padding-top: 24rpx;
    .card{
        margin: 0 24rpx 24rpx 24rpx;
        padding: 26rpx;
        border-radius: 10rpx;
        background-color: #fff;
        box-shadow: 0 0 10px #dedede;
        .title{
            padding: 26rpx 0;
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
        }
        .card_content{
            margin-top: 20rpx;
        }
    }
    .btn-box{
        padding: 10rpx 20rpx;
        justify-content: space-between;
        background-color: #fff;
        .btn{
            padding: 5rpx 15rpx;
        }
    }

    .info_list {
        flex-wrap: wrap;
        margin-bottom: 20rpx;
        background-color: #fff;
        .w-100 {
            width: 100%;
            padding-top: 20rpx;
            padding-bottom: 20rpx;
            box-sizing: border-box;
            // display: flex;
            // justify-content: space-between;
            .label{
                margin-right: 30rpx;
                display: inline-block;
                color: #666;
                min-width: 140rpx;
            }
        }
    }
    .add-btn-box{
        padding: 20rpx;
        .add-btn{
            padding: 50rpx;
            text-align: center;
        }
    }
}
</style>
