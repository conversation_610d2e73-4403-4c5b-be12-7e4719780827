<template>
<view>
  <view class="text-box">
    <view class="label">
      <text>房源详情</text>
      <text class="link" v-if="levelid>1&& !cont.disabled" @click="showTemplate('content', 1)">使用模板</text>
    </view>
    <textarea v-model="contents.content" :disabled="cont.disabled && cont.content?true:false" rows="3" maxlength="300" placeholder="从房屋本身实际情况出发进行描述，如：户型结构、采光通风、产权状况、税费明细、附送优惠等。" placeholder-style="font-size:28rpx;color:#999"></textarea>
    <view class="footer flex-box">
      <text class="select_btn"></text>
      <view>
        <!-- <text class="clear" @click="onClear">清空</text> -->
        <text>{{contents.content.length}}/300</text>
      </view>
    </view>
  </view>
  <view class="text-box" v-if="(parentid==1||parentid==2)&&levelid>1">
    <view class="label">
      <text>业主心态</text>
      <text class="link" v-if ="!cont.disabled" @click="showTemplate('owner_think', 2)">使用模板</text>
    </view>
    <textarea v-model="contents.owner_think" :disabled='cont.disabled&& cont.owner_think?true:false'  rows="3" maxlength="300" :placeholder="parentid==1?'从房东卖房原因、是否急售等方面进行描述。':'从房东出租原因、是否着急出租等方面进行描述。'" placeholder-style="font-size:28rpx;color:#999"></textarea>
    <view class="footer flex-box">
      <text class="select_btn"></text>
      <view>
        <!-- <text class="clear" @click="onClear">清空</text> -->
        <text>{{contents.owner_think.length}}/300</text>
      </view>
    </view>
  </view>
  <view class="text-box" v-if="(parentid==1||parentid==2)&&levelid>1">
    <view class="label">
      <text>服务介绍</text>
      <text class="link" v-if="!cont.disabled" @click="showTemplate('service_introduce', 3)">使用模板</text>
    </view>
    <textarea v-model="contents.service_introduce" :disabled='cont.disabled&& cont.service_introduce?true:false'  rows="3" maxlength="300" placeholder="可以多角度描述您的各类专业服务，例如：佣金比例及额度，税费申报、产权过户、定金协议、房屋交割、买卖合同、行业经验、服务态度等。" placeholder-style="font-size:28rpx;color:#999"></textarea>
    <view class="footer flex-box">
      <text class="select_btn"></text>
      <view>
        <!-- <text class="clear" @click="onClear">清空</text> -->
        <text>{{contents.service_introduce.length}}/300</text>
      </view>
    </view>
  </view>
  <view class="tip">请勿包含手机号、网址、微信等风险隐私信息，违规信息将被下架，严重将封号处理。</view>
  <view class="btn-box">
    <view class="btn" @click="subData">确定</view>
  </view>
  <myPopup ref="template_popup">
    <view class="template_box">
      <view class="header flex-box">
        <text class="cancel" @click="templateCancel">取消</text>
        <text class="ok" @click="templateOk">确定</text>
      </view>
      <scroll-view class="template_list" scroll-y>
        <view class="template_item bottom-line" v-for="(item, index) in template_list" :key="index" @click="selectTemplate(item, index)">
          <view class="name">
            <text>{{item.name}}</text>
            <my-icon v-if="temp_template===item" type="ic_xuanze" color="#ff656b" size="38rpx"></my-icon>
            <text v-else class="check"></text>
          </view>
          <view class="desc">{{item.content}}</view>
        </view>
      </scroll-view>
    </view>
  </myPopup>
</view>
</template>

<script>
import myPopup from '../../components/myPopup.vue'
import myIcon from '../../components/myIcon'
import { showModal } from '../../common/index.js'
export default {
  components: {
    myPopup,
    myIcon
  },
  data () {
    return {
      contents: {
        content:'',
        owner_think: '',
        service_introduce: '',
      },
      current_type: '',
      temp_template: '',
      template_list: [],
      levelid: 0,
      parentid: 0,
      cont:{}
    }
  },
  onLoad(options){
    this.levelid = +options.levelid||0
    this.parentid = +options.parentid||0
    uni.$once('giveDesc',(data)=>{
      this.contents = data
      this.cont = Object.assign({},data)
    })
  },
  methods: {
    // 显示模板列表
    showTemplate(type, cate_id){
      this.current_type = type
      this.temp_template = ""
      uni.showLoading({
        title: "正在获取模板"
      })
      this.getTemplateList(cate_id)
    },
    getTemplateList(cate_id){
      this.template_list = []
      this.$ajax.get('release/getReleaseTemplate', {cate_id}, res=>{
        uni.hideLoading()
        if (res.data.code === 1) {
          this.template_list = res.data.list;
          this.$refs.template_popup.show()
        }else{
          showModal({
            content: '您还没有此模板',
            confirmText: '去添加',
            confirm: () => {
              this.$navigateTo(`/user/add/add_template?cate_id=${cate_id}`)
            },
            cancel: () => {
              
            }
          })
          // uni.showToast({
          //   title: res.data.msg,
          //   icon: 'none'
          // })
          this.template_list = []
        }
      }, err=>{
        uni.hideLoading()
      })
    },
    onClear(){
      this.content = ''
    },
    // 选中模板
    selectTemplate(item){
      this.temp_template = item
      // this[this.current_type] = item
    },
    // 取消选择模板
    templateCancel(){
      this.$refs.template_popup.hide()
    },
    // 确定选择模板
    templateOk(){
      this.contents[this.current_type] = this.temp_template.content
      this.$refs.template_popup.hide()
    },
    subData(){
      if(!this.contents.content){
        uni.showToast({
          title:"请输入房源详情",
          icon:'none'
        })
        return
      }
      uni.$emit('transmitDesc', this.contents)
      uni.navigateBack()
    }
  }
}
</script>

<style scoped lang="scss">
.text-box{
  padding: 0 48rpx;
  padding-top: 12rpx;
  background-color: #fff;
  .label{
    padding: 24rpx 0;
    // margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
    .link{
      font-size: 28rpx;
      font-weight: initial;
      color: $uni-color-primary;
    }
  }
  textarea{
    width: 100%;
    height: 200rpx;
    padding: 20rpx;
    box-sizing: border-box;
    line-height: 1.5;
    font-size: 28rpx;
    background-color: #f8f8f8;
  }
  .footer{
    justify-content: space-between;
    padding: 20rpx 0;
    .select_btn{
      color: $uni-color-primary;
    }
    .clear{
      color: #999;
      padding: 0 48rpx;
    }
  }
}
.tip{
  padding: 24rpx 48rpx;
  font-size: 22rpx;
  color: #999;
}

.btn-box{
  margin: 0;
  padding: 24rpx 48rpx;
  .btn{
    height: 88rpx;
    line-height: 88rpx;
    background: #FB656A;
    box-shadow: 0 8rpx 32rpx 0 rgba(251,101,106,0.40);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    text-align: center;
  }
}


.template_box{
  padding: 24rpx 0;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  background-color: #fff;
  .header{
    padding: 24rpx 48rpx;
    justify-content: space-between;
    font-size: 32rpx;
    .ok{
      color: $uni-color-primary;
    }
  }
  .template_list{
    max-height: 70vh;
    min-height: 25vh;
    box-sizing: border-box;
    padding: 24rpx 0;
    .template_item{
      display: block;
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      // white-space: nowrap;
      padding: 20rpx 48rpx;
      &.active{
        color: $uni-color-primary;
      }
      .name{
        display: flex;
        justify-content: space-between;
        margin-bottom: 24rpx;
        font-size: 22rpx;
        color: #666;
        .check{
          width: 30rpx;
          height: 30rpx;
          border-radius: 50%;
          border: 4rpx solid #dedede;
        }
      }
      .desc{
        white-space: pre-line;
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}
</style>