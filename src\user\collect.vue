<template>
	<view class="p-top-80">
		<tab-bar :tabs="tabs" :nowIndex="currentIndex" uniform col="2" @click="switchTab"></tab-bar>
		<view class="house_list" v-for="(item, index) in listsData" :key="index">
				<uni-swipe-action v-if="params.cate_id===0 && !params.parentid">
					<uni-swipe-action-item :parentData="item">
						<view class="house_item">
							<new-house-item :item-data="item" titleRow="2" type="ershou" @click="toDetail"></new-house-item>
						</view>
						<!-- <template v-slot:option="{parentData}">
							<view>取消收藏{{parentData.id}}</view>
						</template> -->
						<template slot="option" slot-scope="{parentData}">
							<view class="cancel_collect" @click="delFollow(parentData.id)">
									<my-icon type="ic_shoucang_red" color="#ff656b" size="52rpx"></my-icon>
									<view>取消关注</view>
							</view>
						</template>
					</uni-swipe-action-item>
				</uni-swipe-action>
				<uni-swipe-action v-if="params.cate_id===1">
					<uni-swipe-action-item :parentData="item">
						<view class="house_item">
							<house-item :item-data="item" titleRow="2" type="ershou" @click="toDetail"></house-item>
						</view>
						<!-- <template v-slot:option="{parentData}">
							<view>取消收藏{{parentData.id}}</view>
						</template> -->
						<template slot="option" slot-scope="{parentData}">
							<view class="cancel_collect" @click="delCollect(parentData.id)">
									<my-icon type="ic_shoucang_red" color="#ff656b" size="52rpx"></my-icon>
									<view>取消收藏</view>
							</view>
						</template>
					</uni-swipe-action-item>
				</uni-swipe-action>
				<uni-swipe-action v-else-if="params.cate_id===2">
					<uni-swipe-action-item :parentData="item">
						<view class="house_item">
							<house-item :item-data="item" titleRow="2" type="renting" @click="toDetail">
							</house-item>
						</view>
						<view class="cancel_collect" slot="option" slot-scope="{parentData}" @click="delCollect(parentData.id)">
							<my-icon type="ic_shoucang_red" color="#ff656b" size="52rpx"></my-icon>
							<view>取消收藏</view>
						</view>
					</uni-swipe-action-item>
				</uni-swipe-action>
				<uni-swipe-action v-else-if="params.cate_id===4">
					<uni-swipe-action-item :parentData="item">
						<view class="house_item" style="margin-bottom:20rpx">
							<demand-item :item-data="item" type="buy_house" @click="toDetail"></demand-item>
							<!-- <house-item type="buy_house" :titleRow="1" :item-data="item" @click="toDetail"></house-item> -->
						</view>
						<view class="cancel_collect" slot="option" slot-scope="{parentData}" @click="delCollect(parentData.id)">
							<my-icon type="ic_shoucang_red" color="#ff656b" size="52rpx"></my-icon>
							<view>取消收藏</view>
						</view>
					</uni-swipe-action-item>
				</uni-swipe-action>
				<uni-swipe-action v-else-if="params.cate_id===3">
					<uni-swipe-action-item :parentData="item">
						<view class="house_item">
							<demand-item :item-data="item" type="rest_house" @click="toDetail"></demand-item>
						<!-- <house-item type="rest_house" :titleRow="1" :item-data="item" @click="toDetail"></house-item> -->
						</view>
						<view class="cancel_collect" slot="option" slot-scope="{parentData}" @click="delCollect(parentData.id)">
							<my-icon type="ic_shoucang_red" color="#ff656b" size="52rpx"></my-icon>
							<view>取消收藏</view>
						</view>
					</uni-swipe-action-item>
				</uni-swipe-action>
        <uni-swipe-action v-else-if="params.parentid">
					<uni-swipe-action-item :parentData="item">
						<view class="house_item">
              <list-item v-if="item.parentid == 1" :item-data="item" type="sale" @click="toEstateDetail"></list-item>
              <list-item v-if="item.parentid == 2" :item-data="item" type="rent" @click="toEstateDetail"></list-item>
              <list-item v-if="item.parentid == 3" :item-data="item" type="transfer" @click="toEstateDetail"></list-item>
						</view>
						<view class="cancel_collect" slot="option" slot-scope="{parentData}" @click="delEstate(parentData.id)">
							<my-icon type="ic_shoucang_red" color="#ff656b" size="52rpx"></my-icon>
							<view>取消收藏</view>
						</view>
					</uni-swipe-action-item>
				</uni-swipe-action>
		</view>
		<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
		<chat-tip></chat-tip>
	</view>
</template>

<script>
	import tabBar from "../components/tabBar.vue";
	import houseItem from "../components/houseItem.vue";
	import newHouseItem from "../components/newHouseItem.vue";
  import listItem from "../commercial/components/listItem.vue"
	import demandItem from "../components/demandItem.vue"
	import myIcon from "../components/myIcon"
	import {showModal} from '../common/index.js'
	import {
		uniLoadMore,
		uniSwipeAction,
		uniSwipeActionItem
	} from "@dcloudio/uni-ui";
	export default {
		components: {
			tabBar,
			houseItem,
			newHouseItem,
			demandItem,
      listItem,
			myIcon,
			uniLoadMore,
			uniSwipeAction,
			uniSwipeActionItem
		},
		data() {
			return {
				options:[
        {
            text: '取消',
            style: {
                backgroundColor: '#007aff'
            }
        }, {
            text: '确认',
            style: {
                backgroundColor: '#dd524d'
            }
        }
      ],
				get_status: "loading",
				content_text: {
					contentdown: "",
					contentrefresh: "正在加载...",
					contentnomore: "没有更多数据了"
				},
				tabs: [{
						name: "新房",
						type: 0
					},
					{
						name: "二手房",
						type: 1
					},
					{
						name: "出租房",
						type: 2
					},
					{
						name: "求购",
						type: 4
					},
					{
						name: "求租",
						type: 3
					},
          {
            name: "商业出售",
            parentid: 1,
            type: 0
          },
          {
            name: "商业出租",
            parentid: 2,
            type: 0
          },
          {
            name: "生意转让",
            parentid: 3,
            type: 0
          }
				],
				currentIndex:0,
				params: {
					page: 1,
					cate_id: 0,
					rows: 20
				},
				listsData: []
			};
		},
		onLoad() {
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			this.getNewHouse();
		},
		methods: {
			getNewHouse(){
				if (this.params.page == 1) {
					this.listsData = [];
				}
				this.get_status = "loading";
				this.$ajax.get('member/myBuildCollect.html',this.params,res=>{
					if (res.data.code == 1) {
						this.listsData = this.listsData.concat(res.data.list);
						this.get_status = "more";
					} else {
						this.get_status = "noMore";
					}
				})
			},
			getData() {
				if (this.params.page == 1) {
					this.listsData = [];
				}
				this.get_status = "loading";
				this.$ajax.get(
					"member/myCollect.html",
					this.params,
					res => {
						if (res.data.code == 1) {
							this.listsData = this.listsData.concat(res.data.list);
							this.get_status = "more";
						} else {
							this.get_status = "noMore";
						}
					},
					err => {
						console.log(err);
					}
				);
			},
      getEstate() {
        if (this.params.page == 1) {
					this.listsData = [];
				}
				this.get_status = "loading";
				this.$ajax.get(
					"estate/myCollectList",
					this.params,
					res => {
						if (res.data.code == 1) {
							this.listsData = this.listsData.concat(res.data.list);
							if(res.data.list.length<this.prams.rows){
								this.get_status = "nomore";
							}else {
								this.get_status = "more";
							}
						} else {
							this.get_status = "noMore";
						}
					},
					err => {
						console.log(err);
					}
				);
      },
			switchTab(e) {
				this.params.page = 1;
				this.currentIndex = e.index
        if (e.parentid) {
          this.params.parentid = e.parentid
        } else {
          delete this.params.parentid
        }
				this.params.cate_id = e.type;
				if(e.index===0){
					this.getNewHouse()
				}else if (this.params.parentid){
					this.getEstate()
				}else{
          this.getData();
        }
			},
			toDetail(e){
				if(!e.detail.id){
					return
				}
				this.$store.state.tempData = e.detail
				if(e.type === 'new_house'){
					this.$navigateTo('/pages/new_house/detail?id='+e.detail.id)
					return
				}
				if(this.params.cate_id==1){
					this.$navigateTo('/pages/ershou/detail?id='+e.detail.id)
				}else if(this.params.cate_id==2){
					this.$navigateTo('/pages/renting/detail?id='+e.detail.id)
				}else if(this.params.cate_id==4){
					this.$navigateTo('/needPage/buy_house/detail?id='+e.detail.id)
				}else if(this.params.cate_id==3){
					this.$navigateTo('/needPage/rest_house/detail?id='+e.detail.id)
				}
			},
      toEstateDetail(e) {
        if(!e.detail.id){
					return
				}
				this.$store.state.tempData = e.detail
        if (this.params.parentid == 1) {
          this.$navigateTo('/commercial/sale/detail?id=' + e.detail.id)
        }
        if (this.params.parentid == 2) {
          this.$navigateTo('/commercial/rent/detail?id=' + e.detail.id)
        }
        if (this.params.parentid == 3) {
          this.$navigateTo('/commercial/transfer/detail?id=' + e.detail.id)
        }
      },
			delCollect(id){
				let handleDel = ()=>{
					this.$ajax.get('house/cancelCollect.html',{id},res=>{
						if(res.data.code == 1){
							this.params.page = 1;
							this.getData();
						}
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					})
				}
				showModal({
					title: '提示',
					content: '确定取消收藏此信息？',
					cancelText: '否',
					confirmText: '确定',
					confirm: () => {
						handleDel()
					}
				});
			},
			delFollow(id){
				let handleDel = ()=>{
					this.$ajax.get('build/cancelFollowBuild.html',{bid:id},res=>{
						if(res.data.code == 1){
							this.params.page = 1;
							this.getNewHouse();
						}
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					})
				}
				showModal({
					title: '提示',
					content: '确定取消关注此楼盘？',
					cancelText: '否',
					confirmText: '确定',
					confirm: () => {
						handleDel()
					}
				});
			},
      delEstate(id){
				let handleDel = ()=>{
					this.$ajax.get('estate/cancelCollect',{id},res=>{
						if(res.data.code == 1){
							this.params.page = 1;
							this.getEstate();
						}
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					})
				}
				showModal({
					title: '提示',
					content: '确定取消收藏此信息？',
					cancelText: '否',
					confirmText: '确定',
					confirm: () => {
						handleDel()
					}
				});
			},
		},
		onReachBottom() {
			this.params.page++;
			this.getData();
		}
	};
</script>
<style lang="scss">
.house_list{
	/* padding: 0 48rpx; */
	// background-color: #fff;
	.house_item{
		padding: 0 48rpx;
		width: 100%;
		box-sizing: border-box;
		background-color: #fff;
	}
}
.cancel_collect{
	display: flex;
	height: 100%;
	flex-direction: column;
	justify-content: center;
	padding-left: 20rpx;
	padding-right: 48rpx;
	background-color: #f5f5f5;
	text-align: center;
	>view{
		font-size: 24rpx;
		color: #999;
	}
}
.info-price .total{
	float:inherit;
	margin-right:0;
	margin-left:10upx;
}
</style>
