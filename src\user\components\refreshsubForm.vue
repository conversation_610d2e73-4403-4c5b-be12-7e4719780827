<template>
    <view v-show="show_verify">
        <my-popup ref="sub_form_popup" position="center" :height="sub_box_height + 'px'" :touch_hide="false">
            <view class="sub_box" id="sub_box" :style="{ height: (sub_box_height - 20) + 'px' }">
                <view class="sub_header">
                    <view class="sub_title">请先滑动验证</view>
                    <!-- <view class="sub_title">取消</view> -->
                    <!-- <my-icon type="close" size="20"></my-icon> -->
                    <view class="sub_title" @click="refreshClose()" v-show="false">
                        <my-icon type="ic_guanbi" color="#fff" size="50rpx"></my-icon>
                    </view>
                </view>
                <view class="verify_block">
                    <drag-verify ref="verify" :verify_img="verify_img" :verify_fail="verify_fail"
                        :verify_success="verify_success" @ondragend="onDragEnd" @onrefreshend="onRefreshEnd"
                        @onrefresh="onRefresh"></drag-verify>
                </view>
            </view>
        </my-popup>
    </view>
</template>

<script>
import myPopup from '@/components/myPopup.vue'
import myIcon from '@/components/myIcon.vue'
import dragVerify from "@/components/dragVerify.vue"
export default {
    data() {
        return {
            uuid: '',
            sending: false,
            sub_box_height: 'initial',
            time: 0,
            show_verify: false,
            code_token: '',
            verify_img: '',
            verify_fail: false,
            verify_success: false,
            show_sms_code: false,
            title: '',
            content: '',
            count: 0,
            verify_code: '',
        }
    },
    props: {
        refretype: {
            type: [String],
            default: ''
        },
    },
    computed: {
        login_status() {
            return this.$store.state.user_login_status
        }
    },
    components: {
        myIcon,
        myPopup,
        dragVerify
    },
    watch: {

    },

    methods: {
        refreshClose() {
            this.$refs.sub_form_popup.hide()
            this.show_verify = false;
        },
        showPopup() {
            // #ifdef MP-WEIXIN
            this.getLoginStatus()
            // #endif
            this.$nextTick(() => {
                const query = uni.createSelectorQuery().in(this);
                setTimeout(() => {  //适配百度小程序加载高度不够的问题
                    query.select('#sub_box').boundingClientRect(data => {
                        if (this.sub_box_height == 'initial') {
                            this.sub_box_height = data.height + 300
                        }
                        this.$refs.sub_form_popup.show()
                    }).exec();
                }, 50);

            })
        },
        hide() {
            this.$refs.sub_form_popup.hide()
        },
        inputName(e) {
            this.name = e.detail.value
        },
        inputPhone(e) {
            this.tel = e.detail.value
        },
        inputCode(e) {
            this.sms_code = e.detail.value
        },
        closeSub() {
            this.$refs.sub_form_popup.hide()
            this.show_verify = false
            this.sending = false
            // this.show_sms_code = true
        },
        // 获取滑块验证码
        getVerify() {
            this.$refs.sub_form_popup.hide()
            this.show_verify = false
            this.verify_img = ''
            this.count++
            // #ifndef MP-WEIXIN

            // #ifdef H5
            var url = this.$route.fullPath
            // #endif
            // #ifndef H5
            var pages = getCurrentPages()
            var currentPage = pages[pages.length - 1] //获取当前页面的对象
            var url = currentPage.route //当前页面url
            var options = currentPage.options //当前页面url参数
            let i = 0
            url = '/' + url;
            for (let key in options) {
                if (i === 0) {
                    url += `?${key}=${options[key]}`
                } else {
                    url += `&${key}=${options[key]}`
                }
                i++
            }
            // #endif
            this.url = url
            // this.$emit('signUp',{name:this.name,tel:this.tel,page_url:this.url,title:this.sub_title||this.title,desc:this.desc,showMesssge:1})
            this.$ajax.get('info_refresh/doNeedToSlide', {}, res => {
                this.next_click_time = new Date().getTime()
                if (res.data.code === 1) {
                    if (res.data && res.data.need_to_slide !== undefined) {
                        if (res.data.need_to_slide == 1 && res.data.url) {
                            //进行滑块验证
                            this.verify_img = res.data.url
                            this.uuid = res.data.uuid
                            this.show_verify = true
                            this.$refs.sub_form_popup.show()
                        }
                        else if (res.data.need_to_slide == 0) {
                            this.$refs.sub_form_popup.hide()
                            this.show_verify = false
                            this.uuid = res.data.uuid
                            //直接返回调用刷新接口
                            if (this.refretype === 'multi' && this.uuid) {
                                this.$emit('referbatch', { uuid: this.uuid, verify_code: this.verify_code })
                            }
                            else if (this.refretype === 'one' && this.uuid) {
                                this.$emit('onereferbatch', { uuid: this.uuid, verify_code: this.verify_code })
                            }

                        }
                    } else {
                        this.show_verify = false
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none'
                        })
                        return
                    }

                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            }, err => {

            })
            // #endif
        },
        oncegetVerify() {
            this.verify_img = ''
            this.count++
            // #ifndef MP-WEIXIN

            // #ifdef H5
            var url = this.$route.fullPath
            // #endif
            // #ifndef H5
            var pages = getCurrentPages()
            var currentPage = pages[pages.length - 1] //获取当前页面的对象
            var url = currentPage.route //当前页面url
            var options = currentPage.options //当前页面url参数
            let i = 0
            url = '/' + url;
            for (let key in options) {
                if (i === 0) {
                    url += `?${key}=${options[key]}`
                } else {
                    url += `&${key}=${options[key]}`
                }
                i++
            }
            // #endif
            this.url = url
            // this.$emit('signUp',{name:this.name,tel:this.tel,page_url:this.url,title:this.sub_title||this.title,desc:this.desc,showMesssge:1})
            this.$ajax.get('info_refresh/doNeedToSlide', { repeat_loading: 1 }, res => {
                this.next_click_time = new Date().getTime()
                if (res.data.code === 1) {
                    if (res.data && res.data.need_to_slide !== undefined) {
                        if (res.data.need_to_slide == 1 && res.data.url) {
                            //进行滑块验证
                            this.verify_img = res.data.url
                            this.uuid = res.data.uuid
                            this.show_verify = true
                            this.$refs.sub_form_popup.show()
                        }
                        else if (res.data.need_to_slide == 0) {
                            this.$refs.sub_form_popup.hide()
                            this.show_verify = false
                            this.uuid = res.data.uuid
                            //直接返回调用刷新接口 
                            if (this.refretype === 'multi' && this.uuid) {
                                this.$emit('referbatch', { uuid: this.uuid, verify_code: this.verify_code })
                            }
                            else if (this.refretype === 'one' && this.uuid) {
                                this.$emit('onereferbatch', { uuid: this.uuid, verify_code: this.verify_code })
                            }
                            // this.$emit('referbatch', {
                            //     uuid: this.uuid,
                            //     refretype: this.refretype
                            // })
                        }
                    } else {
                        this.show_verify = false
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none'
                        })
                        return
                    }

                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            }, err => {

            })
            // #endif
        },
        onRefresh() {
            let now_time = new Date().getTime()
            if (this.next_click_time && now_time - this.next_click_time < 1000 * 10 && this.count > 1) {
                uni.showToast({
                    title: "您的操作太频繁，请稍后再试！",
                    icon: 'none'
                })
                this.refreshing = false
                return
            }
            //重新加载需要
            setTimeout(() => {
                this.oncegetVerify()
            }, 1000)

        },
        // 滑块验证码重置完成的回调
        onRefreshEnd() {
            this.verify_success = false
            this.verify_fail = false
        },
        // 用户滑动验证码结束的回调
        onDragEnd(value) {
            this.sendSmsCode(value)
        },
        sendSmsCode(verify_code) {
            if (!verify_code) {
                uni.showToast({
                    title: "请先滑动验证",
                    icon: "none"
                })
                return
            }
            this.verify_code = verify_code

            this.onRefreshEnd()
            this.hide()
            this.$ajax.get('info_refresh/checkSlideCode', {
                uuid: this.uuid,
                code: verify_code
            }, res => {
                if (res.data.code === 1) {
                    //清空避免再次点击展示验证码
                    this.verify_success = true
                    uni.showToast({
                        title: res.data.msg,
                        duration: 900
                    })
                    if (this.refretype === 'multi' && this.uuid) {
                        this.$emit('referbatch', { uuid: this.uuid, verify_code: this.verify_code })
                    }
                    else if (this.refretype === 'one' && this.uuid) {
                        this.$emit('onereferbatch', { uuid: this.uuid, verify_code: this.verify_code })
                    }

                } else {
                    this.show_verify = false
                    this.verify_fail = true
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 1000
                    })
                    //重新加载
                    setTimeout(() => {
                        this.oncegetVerify()
                    }, 900)
                }
            }, err => {
                this.verify_fail = true
                uni.showToast({
                    titley: '滑动验证失败',
                    icon: 'none',
                    duration: 800
                })
                setTimeout(() => {
                    this.oncegetVerify()
                }, 900)
            })
        },
        // #ifdef MP-WEIXIN
        getLoginStatus() {
            // 检测登录状态
            this.$ajax.get('member/checkUserStatus', {}, res => {
                this.$store.state.user_login_status = res.data.status
            })
        },
        onGetPhonenumber(res) {
            if (res.target.encryptedData && res.target.iv) {
                this.bindPhone(res.target.encryptedData, res.target.iv)
            } else {
                this.$navigateTo('/user/bind_phone/bind_phone')
            }
        },
        onGetUserInfo(res) {
            if (res.target.userInfo && res.target.userInfo.avatarUrl && res.target.userInfo.nickName) {
                uni.login({
                    provider: 'weixin',
                    success: (loginRes) => {
                        this.getToken(loginRes.code, res.target.userInfo.avatarUrl, res.target.userInfo.nickName)
                    }
                })
            } else {
                uni.removeStorageSync('token')
                this.$navigateTo('/user/login/login')
            }
        },
        getToken(code, avatarUrl, nickName) {
            this.$ajax.get("member/getOpenidByCode.html", { code, headimgurl: avatarUrl, nickname: nickName }, (res) => {
                if (res.data.code == 1) {
                    // this.getUserInfo(res.data.user)
                    // 存储token
                    uni.setStorageSync('token', res.data.token)
                    uni.showToast({
                        title: '登录成功'
                    })
                    if (res.data.tel) {
                        this.subData()
                    } else {
                        this.$store.state.user_login_status = 2
                    }
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            })
        },
        bindPhone(encryptedData, iv) {
            this.$ajax.get('member/getWxPhoneNumber', { encryptedData, iv }, (res) => {
                if (res.data.code == 1) {
                    uni.showToast({
                        title: res.data.msg
                    })
                    if (res.data.token) { //绑定手机号成功后台返回一个新的token
                        uni.setStorageSync('token', res.data.token)
                    }
                    this.subData()
                    this.$store.state.user_login_status = 3
                }
            })
        },
        // #endif
        subData() {
            if (!this.name && (this.sub_mode === 0 || this.sub_mode === 2)) {
                uni.showToast({
                    title: '请输入用户名',
                    icon: 'none'
                })
                return
            }
            if (!this.tel) {
                uni.showToast({
                    title: '请输入手机号',
                    icon: 'none'
                })
                return
            }
            if (this.tel.length < 11 || this.tel[0] != 1) {
                uni.showToast({
                    title: "手机号格式不正确",
                    icon: "none"
                })
                return
            }
            // if(this.login_status==1){
            //     this.authRegister()
            // }else{
            uni.showLoading({
                title: "正在提交",
                mask: true
            })

            // #ifdef H5
            var url = this.$route.fullPath
            // #endif
            // #ifndef H5
            var pages = getCurrentPages()
            var currentPage = pages[pages.length - 1] //获取当前页面的对象
            var url = currentPage.route //当前页面url
            var options = currentPage.options //当前页面url参数
            let i = 0
            url = '/' + url;
            for (let key in options) {
                if (i === 0) {
                    url += `?${key}=${options[key]}`
                } else {
                    url += `&${key}=${options[key]}`
                }
                i++
            }
            // #endif
            this.url = url
            this.$emit('signUp', { name: this.name, tel: this.tel, page_url: this.url, title: this.sub_title || this.title, desc: this.desc })
            // this.$emit('signUp',{name:this.name,tel:this.tel})
            // }
        },
        authRegister() {
            if (!this.sms_code) {
                uni.showToast({
                    title: '请输入短信验证码',
                    icon: 'none'
                })
                return
            }
            this.$ajax.get('member/authRegister', { tel: this.tel, code_token: this.sms_token, code: this.sms_code }, res => {
                if (res.data.token) {
                    uni.setStorageSync('token', res.data.token)
                }
                if (res.data.code === 1) {
                    this.show_sms_code = true
                    this.$emit('onsubmit', { name: this.name, tel: this.tel, sms_code: this.sms_code, page_url: this.url, title: this.sub_title || this.title, selectIndex: this.currentSelect, desc: this.desc })
                    // if(this.after_login_auto_handle){
                    //     this.closeSub()
                    //     uni.showToast({
                    //         title:'提交成功',
                    //         icon:'none'
                    //     })
                    // }
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
    }
}
</script>

<style scoped lang="scss">
.sub_box {
    background-color: #fff;
    margin: 0 40rpx;
    border-radius: 16rpx;
    position: relative;
    //   overflow-y: hidden;
    margin-top: 32rpx;

    .sub_header {
        padding: 18rpx 16rpx;
        color: #fff;
        background-image: linear-gradient(-41deg, #F7918F 0%, #FB656A 100%);
        position: relative;
        border-top-left-radius: 16rpx;
        border-top-right-radius: 16rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .sub_title {
            line-height: 1;
            font-size: 32rpx;
            font-weight: bold;
            letter-spacing: 2rpx;
        }

        .sub_tip {
            font-size: 24rpx;
        }

        .icon {
            width: 188rpx;
            height: 188rpx;
            position: absolute;
            top: -32rpx;
            right: 48rpx;

            image {
                width: 100%;
                height: 100%;
            }
        }
    }

    .form_box {
        padding: 30rpx 48rpx;
    }

    .sub_content {
        font-size: 32rpx;
        line-height: 1.5;
        color: #333;
    }

    .sub_form {
        margin-top: 25rpx;

        .sms_code_inp {
            align-items: center;
            margin-bottom: 20upx;
        }

        .sub_tel {
            margin-bottom: 20rpx;
        }

        .entrustSelect {
            height: 80upx;
            background: #f5f5f5;
            margin-bottom: 20upx;
            display: flex;
            padding: 0 20rpx;
            color: #888;
            align-items: center;
            justify-content: space-between;
        }

        input {
            padding: 20rpx;
            font-size: 28rpx;
            background-color: #f5f5f5;
        }

        .send-code {
            margin-left: 10rpx;
            color: $uni-color-primary;

            &.disable {
                color: #888;
            }
        }

        .btn-box {
            padding: 10px 0 0 0;

            button {
                font-size: 34rpx;
                font-weight: bold;
                height: 88rpx;
                line-height: 88rpx;
                background: #FB656A;
                box-shadow: 0 4px 16px 0 rgba(251, 101, 106, 0.40);
                border-radius: 44rpx;
            }

            .close_btn {
                padding: 24rpx;
                text-align: center;
                color: #999;
            }
        }
    }

    .verify_block {
        position: absolute;
        left: 0;
        right: 0;
        top: 90rpx;
        bottom: 40rpx;
        background-color: #fff;
        z-index: 2;
    }
}
</style>