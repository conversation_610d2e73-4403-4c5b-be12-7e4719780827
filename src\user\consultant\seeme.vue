<template>
<view class="seeme">
  <view class="all">
    <image :src="item.prelogo | imgUrl('w_240')" v-for="(item,index) in list" :key="index" mode ="aspectFill"> </image>
  </view>

</view>
</template>

<script>
import {
  formatImg,
} from '../../common/index.js'
export default {
  data() {
    return {
      list: [],
      id: ""
    }
  },
   filters: {
    imgUrl(val, param = "") {
      if (val) {
        return formatImg(val, param)
      }
    },
   },
  onLoad(options) {
    if (options.id) {
      this.id = options.id
      this.getData()
    }

  },
  methods: {
    getData() {
      this.$ajax.get('adviser/seeMeList', {
        id: this.id
      }, res => {
        if (res.data.code == 1) {
          let list = res.data.data;
          list.map(item => {
            if (item.prelogo) {
              let obj = {
                prelogo: item.prelogo
              }
              this.list.unshift(obj)
            }
          })
        }

      })
    }
  },
}
</script>

<style lang="scss">
.seeme {
  .all {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    padding: 20upx;
    width: 85%;
    margin: 0 auto;


    image {
      height: 115upx;
      width: 115upx;
      border-radius: 50%;
      margin-right: 10upx;
      margin-bottom: 10upx;

    }
  }
}
</style>
