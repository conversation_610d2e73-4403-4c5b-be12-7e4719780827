<template>
  <view class="customer" @click="toDetail">
    <view class="header">
      <image  class="avatar" :src="customer.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
      <view class="user_info">
        <view class="user_name flex-row"><text class ="name_con">{{customer.cname||"暂无姓名"}}</text><text class="levelname" :class="{official:customer.role=='1'||customer.role=='6',agent:customer.role=='3',adviser:customer.role=='2',member:customer.role=='4',zijian:customer.role=='5' }">{{customer.alias_name}}</text></view>
        <view class="user_tel">{{customer.tel}}</view>
      </view>
      <view class="status">

        <view class="flex-row">
        <!-- <text class="label">跟进：</text> -->
        <text class="value">{{customer.speed||"未跟进"}}</text>
        </view>
        <view class="copy_line flex-box">
          <view class="copy_btn flex-box" @click.stop.prevent="onCopy()">
            <my-icon type="copy" color="#fff" size="24rpx"></my-icon>
            <text>复制</text>
          </view>
        </view>
      </view>
    </view>
    <view class="remark">{{customer.remark}}</view>
    <view class="footer">
      <view class="btn_group">
        <view class="button plain" :class="{disabled: customer.status===0}" v-if="customer.user_id" @click.stop.prevent="handleChat">微聊</view>
        <view class="button plain" :class="{disabled: customer.status===0}" @click.stop.prevent="handleTel">电话</view>
        <view class="button primary " :class="{disabled: customer.status===0}" @click.stop.prevent="toDetail">客户跟进</view>
      </view>
      <view class="tip">{{customer.time}}{{customer.last_desc}}</view>
    </view>
  </view>
</template>

<script>
import getChatInfo from "../../common/get_chat_info"
import myIcon from '../../components/myIcon'
import copyText from '../../common/utils/copy_text'
export default {
  name: '',
  components: {myIcon},
  data () {
    return {}
  },
  props: {
    customer: {
      type: Object,
      default: ()=>{
        return {}
      }
    }
  },
  // filters:{
  //   memberFormat(val){
  //     switch (val) {
  //       case 1:
  //         return "官方"
  //         break;
  //       case 2:
  //         return "顾问"
  //         break;
  //       case 3:
  //         return "经纪人"
  //         break;
  //       case 4:
  //         return "会员"
  //         break;
  //       case 5:
  //         return "自建"
  //         break;
  //       default:
  //         break;
  //     }
  //   }
  // },
  methods: {
    handleChat(){
      if(this.customer.status === 0) return
      getChatInfo(this.customer.user_id)
    },
    handleTel(){
      if(this.customer.status === 0) return
      uni.makePhoneCall({
        phoneNumber: this.customer.tel
      })
    },
    onCopy(){
      const content = `客户姓名：${this.customer.cname||'暂无姓名'}\n手机号码：${this.customer.tel}\n意向小区：${this.customer.community||"未跟进"}\n跟进进度：${this.customer.speed||"未跟进"}\n客户来源：${this.customer.page_from}\n跟进时间：${this.customer.customer_time}`
        copyText(content, ()=>{
            uni.showToast({
              title: '客户信息复制成功',
              icon: 'none'
            })
        })
    },
    toDetail(){
      if(this.customer.status === 0) return
      this.$navigateTo(`/customer/detail?id=${this.customer.id}`)
    }
  }
}
</script>

<style scoped lang="scss">
.flex-row{
  display: flex;
  flex-direction: row;

}
.customer{
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  border-bottom: 1px solid #f2f2f2;
  .header{
    display: flex;
    align-items: flex-start;
    .avatar{
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 16rpx;
    }
    .user_info{
      flex: 1;
      color: #333;
      .user_name{
        margin-bottom: 12rpx;
        font-weight: bold;
        align-items: center;
        .name_con{
          max-width: 71%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .status{
      font-size: 22rpx;
      .label{
        color: #999;
      }
      .value{
        color: $uni-color-primary;
      }
    }
  }
  .remark{
    padding: 24rpx 0;
    margin-bottom: 12rpx;
    font-weight: bold;
    color: #333;
  }
  .footer{
    padding: 14rpx 0;
    .btn_group{
      display: flex;
      justify-content: space-between;
      .button{
        height: 64rpx;
        line-height: 64rpx;
        padding: 0 32rpx;
        border-radius: 8rpx;
        text-align: center;
        font-size: 32rpx;
        box-sizing: border-box;
        ~.button{
          margin-left: 24rpx;
        }
        &.plain{
          border: 1rpx solid $uni-color-primary;
          color: $uni-color-primary;
        }
        &.primary{
          background-color: $uni-color-primary;
          color: #fff;
        }
        &.disabled{
          background-color: #f2f2f2;
          border-color: #f2f2f2;
          color: #d8d8d8;
        }
      }
    }
    .tip{
      margin-top: 24rpx;
      font-size: 22rpx;
      color: #999;
    }
  }
}

.copy_line{
    justify-content: flex-end;
  }
  .copy_btn{
    margin-top: 12rpx;
    width: 84rpx;
    padding: 4rpx;
    align-items: center;
    justify-content: center;
    background-image: linear-gradient(125deg, #FF5500 0%, #FFA402 100%);
    box-shadow: 0 4rpx 12rpx 0 rgba(255,145,1,0.50);
    border-radius: 4rpx;
    border-radius: 4rpx;
    color: #fff;
    font-size: 22rpx;
  }
  .levelname{
                line-height: 32rpx;
                margin: 0 16upx;
                padding: 0 16rpx;
                display: inline-block;
                // border-radius: 16upx;
                font-size: 22upx;
                // border: 1upx solid #4ebdf8;
                // color: #999;
                color: #fff;
                // background-color: #f2f2f2;
                background-image: linear-gradient(180deg,#69d4bb,#00caa7);
                font-weight: initial;
                border-top-left-radius: 4px;
                border-bottom-right-radius: 4px;
                &.official{
                    // background-color: #1296db;
                    color: #fff;
                    background-image: linear-gradient(180deg,#8cd3fc,#4cc7f6)
                    // border: 1upx solid #1296db;
                }
                &.agent{
                    // border: 1upx solid #f96063;
                    // background-color: #f96063;
                    background-image: linear-gradient(180deg,#ff9767,#fd7737);
                    color: #fff;
                }
                &.adviser{
                    // border: 1upx solid #f0bb2c;
                    // background-color: #f0bb2c;
                    color: #fff;
                    background-image: linear-gradient(180deg,#fcd88c,#f6ce4c);
                }
                &.zijian{
                    // border: 1upx solid #f0bb2c;
                    // background-color: #f0bb2c;
                    color: #fff;
                    background-image: linear-gradient(180deg,#4c86b3, #838afb);
                }
            }
</style>