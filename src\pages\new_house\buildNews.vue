<template>
  <view class="content build_news">
    <view class="tab-list flex-row bottom-line">
      <view class="tab-item" :class="{ active: show_tab === 'news_list' }" @click="switchTab('news_list')">项目动态</view>
      <view class="tab-item" :class="{ active: show_tab === 'sales_news' }" @click="switchTab('sales_news')">销售动态</view>
      <view class="tab-item" :class="{ active: show_tab === 'share_list' }" @click="switchTab('share_list')">置业顾问动态</view>
    </view>
    <!-- 楼盘资讯动态 -->
    <view class="new_list" v-show="show_tab === 'news_list'">
      <view class="top flex-row">
        <text>楼盘动态</text>
        <text class="tips" @click="showTips">免责声明</text>
      </view>
      <view class="news_list">
        <view class="news_item" :class="'level' + news.level" v-for="news in news_list" :key="news.id"
          @click="$navigateTo('/pages/news/detail?id=' + news.id)">
          <view class="time flex-row">
            <text class="attr">{{ news.level === 3 ? '头条' : (news.level === 2 ? '关注' : '动态') }}</text>
            <text>{{ news.ctime }}</text>
          </view>
          <view class="title">{{ news.title }}</view>
          <view class="sub_title">{{ news.sub_title }}</view>
        </view>
      </view>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
    <!-- 销售动态 -->
    <view class="new_list" v-show="show_tab === 'sales_news'">
      <view class="top flex-row">
        <text>销售动态</text>
        <text class="tips" @click="showTips">免责声明</text>
      </view>
      <view class="news_list">
        <view class="news_item level" v-for="news in sales_news" :key="news.id">
          <view class="time flex-row">
            <text class="attr">{{ news.label }}</text>
            <text>{{ news.ktime }}</text>
          </view>
          <!-- <view class="title">{{news.title}}</view> -->
          <view class="sub_title">{{ news.descp }}</view>
        </view>
      </view>
      <uni-load-more :status="get_status3" :content-text="content_text"></uni-load-more>
    </view>
    <!-- 置业顾问动态 -->
    <view class="share-list" v-show="show_tab === 'share_list'">
      <view v-for="(share, index) in share_list" :key="share.id">
        <view class="adviser-box flex-row">
          <image class="prelogo" :src="share.prelogo | imageFilter('w_80')" @click="toAdviserDetail(share.adviser_id)">
          </image>
          <view class="adviser-info" @click="toAdviserDetail(share.adviser_id)">
            <view class="name flex-row">
              <text class="text">{{ share.cname }}</text>
              <image class="level_icon" :src="share.adviser_levelid | levelIcon"></image>
            </view>
            <view class="level">{{ share.adviser_level_name }}</view>
          </view>
          <view class="btn-list flex-row">
            <view class="btn chat" @click="handleAsk(share.uid, share.adviser_id)">微聊</view>
            <view class="btn tel" @click="handleTel(share)">电话咨询</view>
          </view>
        </view>
        <share-item :share="share" @click="goShareDetail" @clickvoice="onClickVoice"
          @voiceEnded="voice_playing_index = -1" @voiceError="voice_playing_index = -1"
          @clickpraise="handlePraise($event, index)" :voice_playing="voice_playing_index == index"></share-item>
      </view>
      <uni-load-more :status="get_status2" :content-text="content_text"></uni-load-more>
    </view>
    <!-- 底部操作菜单 -->
    <view class="bottom-bar flex-row">
      <view class="bar-left flex-row flex-1">
        <!-- <view class="icon-btn" @click="handleFollow">
          <my-icon :type="is_follow?'ic_guanzhu_red':'ic_guanzhu'" :color="is_follow?'#ff656b':'#666'" size="50rpx"></my-icon>
          <text>关注</text>
        </view> -->
        <view class="icon-btn" v-if="navs[0].is_show && (navs[0].operation === 1 || navs[0].operation === 2)"
          @click="toYuyue(3, navs[0])">
          <my-icon type="yuyue" color="#666" size="50rpx"></my-icon>
          <text>{{ navs[0].name }}</text>
        </view>
        <view class="icon-btn" v-if="navs[0].is_show && (navs[0].operation === 3 || navs[0].operation === 4)"
          @click="cusList(navs[0].operation)">
          <my-icon type="ic_zixun" color="#666" size="50rpx"></my-icon>
          <text>{{ navs[0].name }}</text>
        </view>
        <view class="icon-btn" v-if="navs[1].is_show === 1" @click="toContrast()">
          <text class="badge" v-if="login_status > 1 && contrastCount > 0">{{ contrastCount > 99 ? '99+' : contrastCount }}</text>
          <text class="badge"
            v-if="login_status <= 1 && $store.state.temp_huxing_contrast_ids.length > 0">{{ $store.state.temp_huxing_contrast_ids.length > 99 ? '99+' : $store.state.temp_huxing_contrast_ids.length }}</text>
          <my-icon type="pk" color="#666" size="50rpx"></my-icon>
          <text>{{ navs[1].name }}</text>
        </view>
      </view>
      <!-- 置业顾问按钮 -->
      <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show === 1 && (navs[2].operation === 1 || navs[2].operation === 2)"
        @click="toYuyue(3, navs[2])">{{ navs[2].name }}</view>
      <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show === 1 && (navs[2].operation === 3 || navs[2].operation === 4)"
        @click="cusList(navs[2].operation)">{{ navs[2].name }}</view>
      <!-- 咨询售楼处按钮 -->
      <view class="flex-1" :class="{ alone: navs[2].is_show === 0 }" v-if="navs[3].is_show === 1" @click="handleTel()">
        <view class="bar-btn btn2">{{ navs[3].name }}</view>
      </view>
    </view>
    <sub-form :sub_type="sub_type" :sub_mode="sub_mode" ref='sub_form' @onsubmit="handleSubForm"></sub-form>
    <chat-tip></chat-tip>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import shareItem from '../../components/shareItem'
import myIcon from '../../components/myIcon'
import subForm from '../../components/subForm'
import { uniLoadMore } from '@dcloudio/uni-ui'
import {
  formatImg,
  showModal,
} from '../../common/index.js'
import { wxShare } from '../../common/mixin'
import allTel from '../../common/all_tel.js'
import getChatInfo from '../../common/get_chat_info'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了"
      },
      get_status2: "loading",
      get_status3: "loading",
      show_tab: 'news_list',
      voice_playing_index: -1,
      news_list: [],
      share_list: [],
      sales_news: [],
      newsBid: "",
      seo: {},
      share: {},
      news_params: {
        page: 1,
        rows: 20,
      },
      share_params: {
        page: 1,
        rows: 20,
      },
      sales_params: {
        page: 1,
        rows: 20,
      },
      build: {},
      sub_type: 0,
      title: "",
      contrastCount: 0,
      navs: [
        {},
        {},
        {},
        {}
      ],
      tel_res: {},
      show_tel_pop: false
    }
  },
  mixins: [wxShare],
  computed: {
    ...mapState(['tel400jing']),
    glabol_middle_tel() {
      return this.$store.state.im.istelcall
    },
    is_open_im() {
      return this.$store.state.im.ischat
    },
    sub_mode() {
      return this.$store.state.sub_form_mode
    },
    login_status() {
      return this.$store.state.user_login_status
    }
  },
  components: { uniLoadMore, subForm, shareItem, myIcon },
  onLoad(options) {
    if (options.bid) {
      this.newsBid = options.bid
    }
    if (options.title) {
      this.title = decodeURIComponent(options.title)
      // this.title =encodeURIComponent(options.title) 

    }
    if (options.type) {
      this.show_tab = options.type

    }
    this.getNav()
    if (this.show_tab == 'news_list') {
      this.getData()

    } else if (this.show_tab == 'share_list') {
      this.getShareList()
      uni.setNavigationBarTitle({
        title: this.title + '动态'
      });
    } else if (this.show_tab == 'sales_news') {
      this.getSalesList()
      uni.setNavigationBarTitle({
        title: this.title + '动态'
      });
    }


    //  uni.setNavigationBarTitle({
    //    title:this.title?`${this.title}动态`:"动态"
    //  })
  },
  filters: {
    levelIcon(val) {
      if (!val) {
        return ""
      }
      let icon = ''
      switch (val) {
        case 1:
          icon = formatImg('/images/new_icon/<EMAIL>', 'w_80')
          break;
        case 2:
          icon = formatImg('/images/new_icon/<EMAIL>', 'w_80')
          break;
        case 3:
          icon = formatImg('/images/new_icon/<EMAIL>', 'w_80')
          break;
      }
      return icon
    },
    imgUrl(img, param = "") {
      return formatImg(img, param)
    },
  },
  methods: {
    getNav() {
      this.$ajax.get('build/buildNav.html', { bid: this.newsBid }, res => {
        if (res.data.code === 1) {
          this.navs = res.data.navs
          if (res.data.build) this.build = res.data.build
        }
      })
    },
    getData() {
      this.get_status = "loading"
      if (this.news_params.page == 1) {
        this.news_list = []
      }
      if (this.newsBid) {
        this.news_params.bid = this.newsBid
      }
      this.$ajax.get("news/index.html", this.news_params, (res) => {

        // #ifdef H5 || MP-BAIDU
        if (res.data.seo) {
          let seo = res.data.seo
          if (res.data.share.pic) {
            seo.image = formatImg(res.data.share.pic, 'w_8001')
          }
          this.seo = seo
        }
        // #endif 
        if (res.data.build) this.build = res.data.build
        this.contrastCount = res.data.contrastCount || 0
        if (res.data.title) {
          uni.setNavigationBarTitle({
            title: `${res.data.title}动态`
          })
        } else {
          uni.setNavigationBarTitle({
            title: `楼盘动态`
          })
        }
        this.get_status = "more"
        if (res.data.code == 1) {
          this.news_list = this.news_list.concat(res.data.news)
          if (res.data.news.length < this.news_params.rows) {
            this.get_status = "noMore"
          }
        } else {
          this.get_status = "noMore"
        }
        if (res.data.share) {
          this.share = res.data.share
          this.getWxConfig()
        }
        uni.stopPullDownRefresh();
      }, (err) => {
        uni.stopPullDownRefresh();
      })
    },
    goShareDetail(e) {
      console.log(e);
      this.$navigateTo(`/pages/community/detail?id=${e}`)
    },
    onClickVoice(src) {
      // 判断点击的哪个语音
      var voice_playing_index = this.share_list.findIndex(item => item.attached && item.attached.length > 0 && item.attached[0].path == src)
      if (this.voice_playing_index === voice_playing_index) {
        this.voice_playing_index = -1
      } else {
        this.voice_playing_index = voice_playing_index
      }
    },
    getShareList() {
      this.get_status2 = "loading"
      if (this.share_params.page == 1) {
        this.share_list = []
      }
      if (this.newsBid) {
        this.share_params.bid = this.newsBid
      }
      this.$ajax.get('building_circle/listsOfBuild', this.share_params, res => {
        this.get_status2 = "more"
        if (res.data.code == 1 && res.data.lists.length > 0) {
          this.share_list = this.share_list.concat(res.data.lists)
          if (res.data.lists.length < this.share_params.rows) {
            this.get_status = "noMore"
          }
        } else {
          this.get_status2 = "noMore"
        }
      })
    },
    getSalesList() {
      this.get_status3 = 'loading'
      if (this.sales_params.page == 1) {
        this.sales_news = []
      }
      if (this.newsBid) {
        this.sales_params.bid = this.newsBid
      }
      this.$ajax.get('build/calendar', this.sales_params, res => {
        this.get_status3 = 'more'
        if (res.data.code === 1 && res.data.list.length > 0) {
          this.sales_news = this.sales_news.concat(res.data.list)
          if (res.data.list.length < this.sales_params.rows) {
            this.get_status3 = "noMore"
          }
        } else {
          this.get_status3 = 'noMore'
        }
      })
    },
    switchTab(type) {
      this.show_tab = type
      if (this.show_tab === 'share_list' && this.share_list.length === 0) {
        this.share_params.page = 1
        this.getShareList()
      }
      if (this.show_tab === 'sales_news' && this.share_list.length === 0) {
        this.sales_params.page = 1
        this.getSalesList()
      }
      if (this.show_tab === 'news_list' && this.news_list.length === 0) {
        this.news_params.page = 1
        this.getData()
      }
    },
    toAdviserDetail(adviser_id) {
      this.$navigateTo(`/pages/consultant/detail?id=${adviser_id}`)
    },
    handleAsk(mid, adviser_id) {
      if (this.is_open_im == 1) {
        getChatInfo(mid, 3)
      } else {
        this.toAdviserDetail(adviser_id)
      }
    },
    toYuyue(type, nav) {
      if (nav.operation === 2 && nav.group_id) {
        // 跳转团购报名
        this.$navigateTo(`/pages/groups/detail?id=${nav.group_id}`)
      } else {
        this.toSubForme(type)
      }
    },
    toSubForme(type) {
      this.sub_type = type
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e) {
      //提交报名
      e.from = '楼盘页'
      e.bid = this.newsBid
      e.type = this.sub_type || ''
      this.$ajax.post('build/signUp.html', e, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          // 没开启引导登录模式或已经绑定手机号了
          if (this.sub_mode !== 2 || res.data.status === 3) {
            //提示报名成功
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            this.$refs.sub_form.closeSub()
          } else {
            this.$refs.sub_form.getVerify()
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    cusList(operation) {
      // if(operation===4){
      //   console.log("和置业顾问发起聊天")
      //   return
      // } 
      if (!uni.getStorageSync('token')) {
        this.$navigateTo('/user/login/login')
        return
      }
      this.$navigateTo('/pages/consultant/consuList?id=' + this.newsBid)
    },
    handleTel(adviser_info) {
      console.log(adviser_info);
      this.tel_params = {
        type: 1,
        callee_id: this.newsBid,
        scene_type: 1,
        scene_id: this.newsBid,
        success: (res) => {
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      if (adviser_info) {
        this.tel_params.type = 2
        this.tel_params.callee_id = adviser_info.adviser_id
        this.tel_params.scene_type = 2
        this.tel_params.scene_id = adviser_info.adviser_id
        allTel(this.tel_params)
      } else {
        let phoneNumber = ""
        // 如果没有开启虚拟号功能
        if (this.build.use_middle_call == 0 || this.glabol_middle_tel == 0) {
          if (this.build.phone && this.build.sellmobile_part) {
            phoneNumber = this.build.phone + ',' + this.build.sellmobile_part.trim()
            if (this.tel400jing) {
              phoneNumber += "#"
            }
            showModal({
              title: "温馨提示",
              content: "请拨打" + this.build.phone + "后转拨分机号" + this.build.sellmobile_part,
              confirm: (res) => {
                uni.makePhoneCall({
                  phoneNumber: phoneNumber
                });
              }
            })
          } else if (this.build.tel) {
            uni.makePhoneCall({
              phoneNumber: this.build.tel,
              success: () => {
                // this.statistics()
              }
            });
          } else {
            uni.showToast({
              title: "此楼盘没有绑定联系电话",
              icon: 'none'
            })
          }
        } else {
          allTel(this.tel_params)
        }
      }
    },
    retrieveTel() {
      allTel(this.tel_params)
    },
    toContrast() {
      if (this.login_status > 1) {
        this.$navigateTo('/contrast/house_list')
      } else {
        this.$navigateTo(`/contrast/house_list?no_login=1`)
      }
    },
    showTips() {
      uni.showModal({
        title: "免责声明",
        content: "  本页面内容，旨在满足广大用户的信息需求而采集提供，如有异议请及时联系我们，本页面内容不代表本网站观点或意见，仅供用户参考以借鉴",
        showCancel: false,
        confirmText: "我知道了"
      })
    }
  },
  onReachBottom() {
    if (this.show_tab === 'news_list') {
      if (this.get_status !== 'more') {
        return
      }
      this.news_params.page++
      this.getData()
    }
    if (this.show_tab === 'sales_news') {
      if (this.get_status3 !== 'more') {
        return
      }
      this.sales_params.page++
      this.getSalesList()
    }
    if (this.show_tab === 'share_list') {
      if (this.get_status2 !== 'more') {
        return
      }
      this.share_params.page++
      this.getShareList()
    }
  },
  onPullDownRefresh() {
    this.news_params.page = 1
    this.share_params.page = 1
    this.getData()
    this.getShareList()
  }
}
</script>

<style lang="scss" scoped>
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.flex-row {
  flex-direction: row;
}

.content {
  padding-top: 90rpx;
  padding-bottom: 120rpx;

  .top {
    padding: 50upx 32upx;

    text {
      font-size: 28upx;

    }

    .tips {
      margin-left: 15upx;
      color: cornflowerblue;
    }
  }

  .tab-list {
    padding: 0 48rpx;
    justify-content: space-between;
    position: fixed;
    top: var(--window-top);
    width: 100%;
    background-color: #fff;
    z-index: 2;

    .tab-item {
      flex: 1;
      padding: 24rpx;
      text-align: center;
      position: relative;

      &.active {
        color: $uni-color-primary;

        &::after {
          content: "";
          height: 8rpx;
          border-radius: 4rpx;
          background-color: $uni-color-primary;
          position: absolute;
          bottom: 0;
          width: 48rpx;
          left: 0;
          right: 0;
          margin: auto
        }
      }
    }
  }

  .news_list {
    padding: 0 48rpx;

    .news_item {
      padding-left: 20rpx;
      padding-bottom: 24rpx;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        width: 2rpx;
        top: -10rpx;
        bottom: 0;
        left: -0.5px;
        background-color: #f5f5f5;
      }

      &::after {
        content: '';
        position: absolute;
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
        background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
        top: 10rpx;
        left: -10rpx;
      }

      .attr {
        line-height: 1;
      }

      &.level1 {
        &::after {
          background-image: linear-gradient(to right, #ff706b, #fdaa5e);
        }

        .time {
          .attr {
            background-image: linear-gradient(to right, #ff706b, #fdaa5e);
          }
        }
      }

      &.level2 {
        &::after {
          background-image: linear-gradient(180deg, #8CD3FC 0%, #4CC7F6 100%);
        }

        .time {
          .attr {
            background-image: linear-gradient(180deg, #8CD3FC 0%, #4CC7F6 100%);
          }
        }
      }

      &.level3 {
        &::after {
          background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
        }

        .time {
          .attr {
            background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
          }
        }
      }

      .time {
        align-items: center;
        padding-left: 20rpx;
        margin-bottom: 24rpx;
        font-size: 22rpx;
        color: #999;

        .attr {
          line-height: 1;
          padding: 4rpx 10rpx;
          margin-right: 10rpx;
          font-size: 24rpx;
          color: #fff;
          background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
          border-top-left-radius: 8rpx;
          border-bottom-right-radius: 8rpx;
        }
      }

      .title {
        font-weight: bold;
        font-size: 32rpx;
        padding: 0 20rpx;
        margin-bottom: 24rpx;
      }

      .sub_title {
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 24rpx;
      }
    }
  }

  .share-list {
    padding: 24rpx 48rpx;

    .adviser-box {
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      margin-bottom: 24rpx;

      .prelogo {
        margin-right: 24rpx;
        width: 54rpx;
        height: 54rpx;
        border-radius: 27rpx;
        background-color: #f5f5f5;
      }

      .adviser-info {
        flex: 1;
        overflow: hidden;
        line-height: 1;

        .name {
          align-items: center;
          font-size: 26rpx;
          margin-bottom: 16rpx;

          .text {
            margin-right: 16rpx;
            max-width: 180rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }

        .level_icon {
          width: 28rpx;
          height: 28rpx;
        }

        .level {
          font-size: 24rpx;
          color: #999;
        }
      }

      .btn-list {
        line-height: 1;

        .btn {
          margin-left: 24rpx;
          padding: 16rpx 16rpx;
          min-width: 144rpx;
          text-align: center;
          color: $uni-color-primary;
          border: 1rpx solid $uni-color-primary;
          border-radius: 8rpx;
        }
      }
    }
  }
}

.build_news {
  background: #fff;

}

// 底部菜单
.bottom-bar {
  background-color: #fff;
  height: 110rpx;
  padding: 15rpx 48rpx;
  left: 0;
  z-index: 10;

  .bar-left {
    padding-right: 10rpx;
    justify-content: flex-start;
  }

  .icon-btn {
    // width: 100rpx;
    align-items: center;
    padding: 0;
    margin: 0;
    background-color: #fff;
    line-height: initial;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // flex: 1;
    padding-right: 32rpx;
    overflow: hidden;
    position: relative;

    // & ~ .icon-btn {
    //   margin-left: 24rpx;
    // }
    .header_img {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
    }

    text {
      line-height: 1;
      font-size: 22rpx;
      color: #999;
      display: inline-block;
      width: 100%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .badge {
      display: inline-block;
      box-sizing: border-box;
      width: auto;
      position: absolute;
      top: 0;
      left: 32rpx;
      // right: 38rpx;
      height: 28rpx;
      padding: 0 8rpx;
      min-width: 28rpx;
      border-radius: 14rpx;
      font-size: 22rpx;
      background-color: $uni-color-primary;
      color: #fff;
    }
  }

  .bar-btn {
    // width: 220rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    padding: 0;
    margin: 0;
    border-radius: 0;
    color: #fff;

    &.btn1 {
      background: #FBAC65;
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.05);
      border-top-left-radius: 40rpx;
      border-bottom-left-radius: 40rpx;
    }

    &.btn2 {
      background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
      box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.30);
      border-top-right-radius: 40rpx;
      border-bottom-right-radius: 40rpx;
    }
  }
}
</style>