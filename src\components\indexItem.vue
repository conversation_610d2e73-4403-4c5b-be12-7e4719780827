<template>
  <view class="house" @click="toDetail">
    <view class="img-box">
      <!-- <view class="img" :style='{backgroundImage:`url(${itemData.img ||itemData.imghb_path })`}'>

      </view> -->
      <image class="img" :src="(itemData.img|| itemData.imghb_path) | imageFilter('w_240')"  mode="aspectFill"></image>
      <view class="level-box">
          <text class="level level3" v-if="itemData.is_zhenxuan == 1">甄选</text>
          <text class="level level2" v-else-if="itemData.info_level===2">精选</text>
      </view>
      <view class="is_top level-box">
          <text class="level level1" v-if="itemData.upgrade_type == 2">顶</text>
      </view>
      <view v-if="itemData.is_cut_price==1&&itemData.cut_price >0" class="cut_price_info">直降{{itemData.cut_price}}{{itemData.parentid==1?"万":'元/月'}}</view>
      <!-- <view class="cut_price_info">直降8万</view> -->
      <!-- <view class="area_name" v-if ="itemData.areaname">
        {{itemData.areaname}}
      </view> -->
    </view>
    <view class="info">
      <template v-if ='type=="ershoufang" || type=="zufang"'>
        <view class="info_top flex-row items-center">
          <text class="jushi">
            {{itemData.shi?itemData.shi+'室':""}}{{itemData.ting?itemData.ting+'厅':""}}
          </text>
          <view class="line"></view>
          <text class="mianji">
            {{itemData.mianji}}m²
          </text>
          <!-- <view class="朝向">
            {{itemData.jm}}
          </view> -->
        </view>
      </template>
      <view class="title-box">
        <view class="title">
          <text>{{ itemData.title }}</text>
        </view>
      </view>
      <view class="center-info"  v-if ='(itemData.build_type&& itemData.build_type.length) || (itemData.label && itemData.label.length)'>
        <view class="label-row">
          <template v-if ="type=='xinfang'">
            <text class="label" v-for = "(label,idx) in itemData.build_type" :key= "idx" >{{label}}</text>
          </template>
          <template v-if ="type!=='xinfang'">
            <text class="label" v-for = "(label,idx) in itemData.label" :key= "idx" >{{label.name}}</text>
          </template>
        </view>
        <!-- <text :class="'status'+itemData.leixing">{{itemData.status_name}}</text> -->
      </view>
      <view class="bottom-info flex-box" >
        <view class="bottom-left">
           <template v-if ="type=='xinfang'">
              <text class="price-unit price-type">{{itemData.build_price!='一房一价'?itemData.price_type:''}}</text>
              <text class="price small">{{itemData.build_price}}</text>
              <text class="price-unit">{{itemData.price_unit}}</text>
           </template>
           <template v-if ="type=='ershoufang'">
              
              <text class="price small">{{itemData.fangjia | numFormat}}万</text>
              <text class="price-unit">{{itemData.danjia |numFormat(1)}}元/m²</text>
           </template>
           <template v-if ="type=='zufang'">
              
              <text class="price small">{{itemData.zujin}}元/月</text>
              <!-- <text class="price-unit">{{itemData.danjia}}元/m²</text> -->
           </template>
           <template v-if ="type=='estate'">
              <text class="price-unit">{{itemData.price}}{{itemData.price_unit}}</text>
           </template>
        </view>

      </view>

    </view>
  </view>
</template>
<style scoped lang="scss">
view{
  line-height: 1;
}
.flex-row{
  display: flex;
  flex-direction: row;
}
.house {
  display: flex;
  flex: 1;
  width: 100%;
  flex-direction: column;
  border-radius: 20rpx;
  padding-bottom: 24rpx;
  background: #fff;
  overflow: hidden;
  // padding: 41rpx 0;
  .img-box {
    width: 100%;
    height: 290rpx;
    // width: 204rpx;
    // height: 172rpx;
    // margin-right: 16rpx;
    position: relative;
    // border-radius: 8rpx;
    overflow: hidden;
    .img {
      width: 100%;
      height: 100%;
      // background-repeat: no-repeat;
      // background-size: 100%;
    }
    .level-box{
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;
      &.is_top {
        right: unset;
        left: 0;
        .level{
          border-bottom-left-radius: 0;
          border-bottom-right-radius: 20rpx;
        }
      }
      .level{
        display: block;
        margin-bottom: 8rpx;
        padding: 4rpx 10rpx;
        font-size: 22rpx;
        border-bottom-left-radius: 20rpx;
        color: #fff;
        &.level1{
          background: linear-gradient(132deg, #F7918F 0%, #FB656A 100%);
        }
        &.level2{
          background: linear-gradient(135deg, #69D4BB 0%, #00CAA7 100%);
        }
        &.level3{
          background-image: linear-gradient(to bottom right, #ffe9bc, #f0cc8b);
          color: #512c19;
        }
      }
    }
    .cut_price_info{
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 3;
        // display: inline-block;
        padding: 8rpx 12rpx;
        font-size: 22rpx;
        line-height: 1;
        // border-top-right-radius: 8rpx;
        background:#FF6069;
        background-size: 100% 100%;
        color: #fff;
      }
    .video-icon{
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      left: 20rpx;
      bottom: 20rpx;
      &.center{
        left: initial;
        right: 12rpx;
        bottom: 12rpx;
        top: initial;
        margin: auto;
      }
    }
    .area_name{
      position: absolute;
      left: 12rpx;
      bottom: 12rpx;
      font-size: 22rpx;
      color: #fff;
    }
  }
  .info {
    flex: 1;
    padding: 0 20rpx;
    overflow: hidden;
    .title-box{
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }
    .title {
      flex: 1;
      font-size: 32rpx;
      line-height: 1.3;
      color: #333;
      font-weight: 600;
      font-size: 28rpx;
      // min-height: 72rpx;
      margin-top: 4rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      
    }
    .info_top{
      color: #444547;
      font-size:22rpx;
      padding: 10rpx 0;
      .line {
        display: inline-block;
        width: 1px;
        height: 22rpx;
        margin: 0 10rpx;
        background: #444547;
      }
    }

    .center-info {
      display: flex;
      align-items: center;
      margin-top: 16rpx;
      font-size: 22rpx;
      color: #999;
      &.colum{
        align-items: flex-start;
        flex-direction: column;
        .area{
          margin-bottom: 16rpx;
          .area_name{
            margin-right: 16rpx;
          }
        }
      }
      .area {
        font-size: 22rpx;
        margin-right: 10rpx;
      }
      .status1{
        color: #4cc7f6;
      }
      .status2{
        color: #00caa7;
      }
      .status3{
        color: #666;
      }
      .status4{
        color: #ff7213;
      }
    }

    .label-row{
      display: block;
      // min-height: 30rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }
    .label{
      display: inline-block;
      font-size: 22rpx;
      padding: 4rpx 10rpx;
      background: #F3F7FE;
      color: #5D6187;
      border-radius: 4rpx;
      ~.label{
        margin-left: 16rpx;
      }
    }

    .bottom-info {
      margin-top: 16rpx;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      flex-wrap: wrap;
      text {
        font-size: 22rpx;
      }
      .price {
        font-size: 32rpx;
        font-weight: bold;
        color: #fb656a;
        &.small{
          font-size: 32rpx;
        }
      }
      
      .price-unit {
        font-size: 22rpx;
        color: #fb656a;
        margin: 0 10rpx 0 10rpx;
        &.price-type{
          margin-left: 0;
          color: #999;
        }
      }
      .mj {
        margin-left: 10rpx;
      }
      .bottom-right{
        text-align: right;
        // flex-shrink:0;
        flex: 1;
        overflow: hidden;
        font-size: 0;
      }
      .u-time {
        display: inline-block;
        max-width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        position: relative;
        font-size: 22rpx;
        color: #999;
      }
    }
  }
}
.cache{
  width: 0;
  height: 0;
}
.rz_strip{
  display: flex;
  align-items: center;
  line-height: 1;
  padding: 10rpx 20rpx;
  background-image: linear-gradient(90deg, #F0D4B0 0%, #FBEFDB 100%);
  border-radius: 4rpx;
  border-radius: 4rpx;
  color: #373131;
  
  .rz_icon{
    width: 15px;
    height: 15px;
    margin-right: 6rpx;
  }
  .text_block{
    flex: 1;
    display: block;
    overflow : hidden;
    text-overflow: ellipsis;
    .text{
      line-height: 1;
      padding: 0 10rpx;
      font-size: 22rpx;
      position: relative;
      // &.label{
      //   &:after{
      //     content: "";
      //     position: absolute;
      //     left: 0;
      //     top: 8rpx;
      //     bottom: 6rpx;
      //     width: 4rpx;
      //     -webkit-transform: scaleX(.5);
      //     transform: scaleX(.5);
      //     background-color: #373131;
      //   }
      // }
    }
  }
}
.hongbao {
  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 20rpx;
  }
  .text {
    color: #FF5B5B;
  }
}
</style>
<script>
import myIcon from '../components/myIcon'
export default {
  components: {myIcon},
  data() {
    return {
      other_info_onclose:true
    };
  },
  props: {
    itemData: Object,
    type: {
      type: String,
      default: "ershou",
    },
    titleRow: {
      type: [Number, String],
      default: 1,
    },
    from:{
      type: [String],
      default: '',
    }
  },
  computed:{
    
  },
  filters:{
    numFormat(val,type=0){
      let name =0
      if (+val%1==0 || type==1) {
        name =(+val).toFixed(0)
      }else {
        name =(+val).toFixed(1)
      }
      return name
    }
  },
  methods: {
    toDetail(){
      this.$emit("click",{type:this.type,detail:this.itemData})
    }
  },
};
</script>
