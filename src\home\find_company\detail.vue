<template>
<view class="company-detail">
    <view class="head">
        <image class="company-logo" :src="shopInfo.shoplogo | imgUrl" mode="aspectFill"></image>
        <view class="company-name">{{shopInfo.shopname}}</view>
        <!-- #ifdef MP -->
        <button open-type="share" class="share_btn">
            <my-icon type="fenxiang1" color="#fff" size="20"></my-icon>
        </button>
        <!-- #endif -->
    </view>
    <tab-bar :tabs="tabs" :nowIndex="nowTabIndex" ref="tab_bar" :fixedTop="false" @click="switchTab"></tab-bar>
    <view class="data-list" v-show="showType==1">
        <!-- <home-list :dataList="dataList" :hideShop="true" :hideTel="true"></home-list> -->
        <!-- #ifndef MP-TOUTIAO||MP-BAIDU -->
        <home-list-item v-for="(item) in dataList" :key="item.id" :item="item" inPage='shop' v-slot:default="{slotItem,show_bar}">
            <view class="handle-bar" :class="{'show':show_bar}">
                <view v-if="slotItem.shoptel" class="bar-item right-line" @click.stop.prevent="handleTel('case', slotItem)">
                    <my-icon type="dianhua" color="#ffffff"></my-icon>
                    <text>电话</text>
                </view>
                <view class="bar-item right-line" @click.stop.prevent="handleYuyue()">
                    <my-icon type="shikebiao" color="#ffffff"></my-icon>
                    <text>预约</text>
                </view>
            </view>
        </home-list-item>
        <!-- #endif-->
        <!-- #ifdef MP-TOUTIAO ||MP-BAIDU -->
        <home-list-item v-for="(item) in dataList" :key="item.id" :item="item" inPage='shop' :hideShop="true" @handleYuyue="handleYuyue" @handleTel="getChildData">
        </home-list-item>
        <!-- #endif -->
    </view>
    <view class="introduction" v-show="showType==2">
        {{shopInfo.shopmemo}}
    </view>
    <view class="activity-box" v-show="showType==3">
        <view class="activity" v-html="activitys.content"></view>
    </view>
    <view class="row-lg" v-if="showNoData&&showType==1">还没有数据~~</view>
    <my-popup ref="popup">
        <view class="popup-content">
            <view class="head">
                <image class="company-logo" :src="shopInfo.shoplogo | imgUrl"></image>
                <view class="company-name">{{shopInfo.shopname}}</view>
            </view>
            <view class="row bottom-line">在线预约【免费设计/获取报价】</view>
            <my-input label="手机号码" maxlength="11" type="number" placeholder="设计师怎么联系您呢？" name="tel" @input="handleInput"></my-input>
            <my-input label="您的称呼" maxlength="10" type="text" placeholder="Hi-您怎么称呼？" name="name" @input="handleInput"></my-input>
            <view class="btn-box">
                <button class="default" hover-class="btn-hover" @click="subData">立即预约</button>
            </view>
            <view class="btn-box bottom">
                <button class="" hover-class="btn-hover" @click="closePopup">取消</button>
            </view>
        </view>
    </my-popup>
    <view class="flex-box bottom-bar">
        <view class="flex-1 flex-box text-center to-buy top-line right-line" @click="handleTel('shop', shopInfo)">
            <my-icon type="dianhua" color="#333333" size="22"></my-icon>
            <view>电话</view>
        </view>
        <view class="flex-1 flex-box to-common top-line" v-if="ischat" @click="showWechat()">
            <my-icon type="zixun" size="22" color="#28bdfb"></my-icon>
			<view class="text" style="color:#28bdfb">咨询</view>
        </view>
        <view class="flex-2 flex-box text-center to-tel" @click="handleYuyue()">
            <my-icon type="fabu1" color="#ffffff" size="22"></my-icon>
            <view>在线预约</view>
        </view>
    </view>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
</view>
</template>

<script>
import tabBar from '../../components/tabBar'
import homeListItem from '../../components/homeListItem'
import myIcon from '../../components/icon'
import myPopup from '../../components/myPopup'
import myInput from '../../components/form/myInput'
import {formatImg} from '../../common/index'
import {wxShare} from '../../common/mixin'
import allTel from '../../common/all_tel.js'
import getChatInfo from '../../common/get_chat_info'
export default {
    data() {
        return {
            tabs: [{
                    name: "首页",
                    type: "",
                    show_type: 1
                },
                {
                    name: "简介",
                    type: 4,
                    show_type: 2
                },
                {
                    name: "案例",
                    type: 1,
                    show_type: 1
                },
                {
                    name: "工地",
                    type: 2,
                    show_type: 1
                },
                {
                    name: "动态",
                    type: 3,
                    show_type: 3
                }
            ],
            nowTabIndex: 0,
            showType: 1,
            shopInfo: {},
            dataList: [],
            activitys: [],
            showNoData: false,
            params: { //预约报名提交的参数
                type: 2,
                from: 6
            },
            tel_res: {},
            show_tel_pop: false
        }
    },
    mixins:[wxShare],
    computed: {
        ischat() {
            return this.$store.state.im.ischat 
        }
    },
    components: {
        tabBar,
        homeListItem,
        myIcon,
        myPopup,
        myInput
    },
    onLoad(options) {
        if(options.type){
            if(options.type==3){
                this.showType = 3
            }
            this.type = options.type //1:案例 2：工地 3：活动
        }else{
            this.type = "" //1:案例 2：工地 3：活动
        }
        if (options.id) {
            this.id = options.id
        }
        this.getData()
    },
    onReady(){
        let nowTabIndex = 0;
        this.tabs.forEach((item, index) => {
            if (item.type == this.type) {
                nowTabIndex = index
            }
        })
        this.nowTabIndex = nowTabIndex
    },
    filters: {
        imgUrl(val){
            return formatImg(val, 'w_120')
        }
    },
    methods: {
        getData() {
            this.showNoData = false
            this.$ajax.get('memberShop/shopDetail.html', {
                shopid: this.id,
                type: this.type
            }, res => {
                if (res.data.code != 1) {
                    this.showNoData = true
                    return
                }
                if(res.data.share&&res.data.share.title){
                    this.share = res.data.share
                }else{
                    this.share = {
                        title:res.data.shop.shopname,
                        content:res.data.shop.shopmemo,
                        pic:res.data.shop.shoplogo||''
                    }
                }
                this.getWxConfig()
                if (res.data.list.length == 0) {
                    this.showNoData = true
                }
                if (this.showType == 1) {
                    this.dataList = res.data.list
                } else if (this.showType == 3) {
                    const regex = new RegExp('<img', 'gi');
                    // 正则匹配处理富文本图片过大显示问题
                    if (res.data.list.content){
                        res.data.list.content = res.data.list.content.replace(regex, `<img style="max-width: 100%;"`);
                    }
                    this.activitys = res.data.list
                }
                this.shopInfo = res.data.shop
            })
        },
        switchTab(e) {
            this.nowTabIndex = e.index
            this.type = e.type
            this.showType = e.show_type
            if (this.showType == 2) { //如果切换的是简介则不需要请求数据
                return
            }
            this.getData()
        },
        handleYuyue() {
            this.$refs.popup.show()
        },
        closePopup() {
            this.$refs.popup.hide()
        },
        handleInput(e) {
            this.params[e._name] = e.detail.value
        },
        subData() {
            this.params.mid = this.shopInfo.id
            this.$ajax.get('memberShop/signUp', this.params, res => {
                if (res.data.code == 1) {
                    uni.showToast({
                        title: res.data.msg
                    })
                    this.sendMessage(this.params.mid)
                    setTimeout(() => {
                        this.closePopup()
                    }, 2000)
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none",
                        duration: 3000
                    })
                }
            })
        },
        sendMessage(mid){
            this.$ajax.get('memberShop/sendAdminNotice.html',{mid},res=>{})
        },
        getChildData(data){
            let info =data;
            this.handleTel("case",info)
        },
        handleTel(type, shop_data) {
            let shopid
            if(type === 'case'){
                shopid = shop_data.shopid
            }
            if(type === 'shop'){
                shopid = shop_data.id
            }
            this.tel_params = {
                type: 5,
                callee_id: shopid,
                scene_type: 5,
                scene_id: this.id,
                success: (res)=>{
                    this.tel_res = res.data
                    this.show_tel_pop = true
                }
            }
            allTel(this.tel_params)
        },
        retrieveTel(){
            allTel(this.tel_params)
        },
        showWechat(){
            getChatInfo(this.shopInfo.uid, 12)
        }
    }
}
</script>

<style lang="scss">
.company-detail {
    padding-bottom: 90upx;
    .share_btn{
        position: absolute;
        top: 30upx;
        right: 20upx;
        padding: 15upx;
        background: rgba($color: #000000, $alpha: 0.6);
        width: 40upx;
        height: 40upx;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        box-sizing: initial;
    }
    .head {
        width: 100%;
        height: 50vw;
        padding: 28upx;
        box-sizing: border-box;
        background-color: #26ad37;
        text-align: center;

        .company-logo {
            width: 120upx;
            height: 120upx;
            margin-top: 8vw;
            border-radius: 50%;
        }

        .company-name {
            margin-top: 20upx;
            font-size: 46upx;
            font-weight: 500;
            color: #fff;
        }
    }

    .introduction {
        padding: 20upx 28upx;
        line-height: 1.7;
        background-color: #fff;
    }
    .activity {
        padding: 20upx 28upx;
        line-height: 1.7;
        font-size: 30upx;
        background-color: #fff;
    }

    .bottom-bar {
        my-icon {
            line-height: 1;
            margin-right: 10upx;
        }

        .to-tel {
            align-items: center;
            justify-content: center;
            color: #fff;
            background-color: $uni-color-primary;
        }

        .to-buy {
            align-items: center;
            justify-content: center;
            color: #666666;
            background-color: #fff;
        }

        .to-common {
            align-items: center;
            justify-content: center;
            color: #666666;
            background-color: #fff;
        }
    }

    .popup-content {
        height: 100vh;
        box-sizing: border-box;
        padding-bottom: 90upx;
        background-color: #fff;

        .row {
            color: #999;
        }

        .bottom {
            width: 100%;
            box-sizing: border-box;
            position: absolute;
            bottom: 90upx;
        }
    }

    .handle-bar {
        padding: 10upx 0;
        line-height: 50upx;
        position: absolute;
        top: 0;
        display: flex;
        right: 44upx;
        width: 0;
        transition: 0.3s;
        border-radius: 8upx;
        background-color: #4d5154;

        .bar-item {
            flex: 1;
            min-width: 33.333%;
            overflow: hidden;
            // text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center;
            color: #fff;
            transform: 0.3s;
        }
    }

    .handle-bar.show {
        width: 480upx;
    }

    .row-lg {
        text-align: center;
        padding: 30upx;
        color: #999;
    }
}
</style>
