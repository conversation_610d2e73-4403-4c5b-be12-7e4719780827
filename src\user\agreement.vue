<template>
  <view class="agreement_content" v-html="content">

  </view>
</template>

<script>
export default {
  data() {
    return {
      content: "",
      agreement_list: [
        {
          type: 'user_agreement',
          title: '用户协议',
          api: 'member/agreement',
        },
        {
          type: 'policy',
          title: '隐私政策',
          api: 'member/faq'
        },
        {
          type: 'push_info',
          title: '信息发布协议',
          api: 'release/agreement.html'
        }
        ,
        {
          type: 'payGuide',
          title: '推广服务协议',
          api: 'payGuide/promotionService'
        }]
    }
  },
  onLoad(options) {
    if (options.type) {
      this.agreement = this.agreement_list.find(item => item.type === options.type)
      if (this.agreement) {
        this.getInfo()
      }
    }
  },
  methods: {
    getInfo() {
      this.$ajax.get(this.agreement.api, {}, (res) => {
        if (res.data.code == 1) {
          this.content = res.data.data.content
          if (res.data.data.title) {
            uni.setNavigationBarTitle({
              title: res.data.data.title
            })
          }
        }
      })
    }
  },
}
</script>

<style>
.agreement_content {
  box-sizing: border-box;
  min-height: calc(100vh - 44px);
  padding: 24upx 48upx;
  line-height: 1.8;
  font-size: 32rpx;
  background: #fff;
}
</style>