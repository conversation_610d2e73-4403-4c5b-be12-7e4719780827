<template>
  <view class="page">
    <view class="question padding-lr-48">{{ question }}</view>
    <view class="answer padding-lr-48">
      <textarea class="answer-content" placeholder="有想法？赶紧回答Ta！" placeholder-style="color: #979797" maxlength="-1" v-model="content"/>
    </view>
    <view class="bottom">
      <view class="line"></view>
      <view class="submit-btn" @click="submitForm">确定</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      question: '',
      content: ''
    }
  },
  onLoad(options) {
    this.id = options.id
    this.question = options.question
  },
  methods: {
    submitForm() {
      uni.showLoading({
        title: '提交中',
        mask: true
      })
      this.$ajax.post('buildQuestion/answer', {id: this.id, answer_content: this.content}, (res)=> {
        uni.hideLoading()
        uni.showToast({
          title: res.data.msg,
          icon: 'none',
        })
        if (res.data.msg === '提交成功') {
          setTimeout(()=> {
            this.$navigateBack()
          }, 500)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 44px);
}
.padding-lr-48 {
  padding: 0 48rpx;
}
.question {
  padding-top: 40rpx;
  padding-bottom: 40rpx;
  background: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
}
.answer {
  flex: 1;
  margin-top: 30rpx;
  padding-top: 40rpx;
  padding-bottom: 40rpx;
  background: #ffffff;
  .answer-content {
    box-sizing: border-box;
    width: 100%;
    background: #f8f8f8;
    border-radius: 15rpx;
    padding: 30rpx;
    font-size: 28rpx;
  }
}
.bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: #fff;
  .line {
    height: 1rpx;
    transform: scaleY(0.5);
    background: #d8d8d8;
  }
  .submit-btn {
    box-sizing: border-box;
    margin: 16rpx 48rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 32rpx;
    color: #ffffff;
    border-radius: 40rpx;
    background: linear-gradient(90deg, #ffa857 0%, #ff6069 100%);
    box-shadow: 0 6rpx 12rpx 0 rgba(255,109,102,0.30);
  }
}
</style>