<template>
  <view class="ershou_detail_content" v-if="loading">
    <!-- 焦点图 -->
    <view class="focus-box">
      <swiper
        class="banner"
        :indicator-dots="false"
        :circular="true"
        :duration="300"
        indicator-active-color="#f65354"
        @change="swiperChange"
        :current="swiperCurrent"
      >
        <swiper-item v-for="(item, index) in focus" :key="index">
          <view v-if="item.type === 'vr'" class="swiper-item" @click="toVr(item)">
            <image :src="(item.cover || imgs[0]) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/vr_b.png" mode="widthFix"></image>
          </view>
          <view v-if="item.type === 'video_list'" class="swiper-item" @click="preVideo(item.url)">
            <image :src="(item.cover || item.url) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/video.png" mode="widthFix"></image>
          </view>
          <view v-if="item.type === 'img'" class="swiper-item" @click="preImg(index)">
            <image :src="item.url | imageFilter('w_6401')" mode="aspectFill"></image>
          </view>
        </swiper-item>
      </swiper>
      <view class="number">编号：{{ detail.id }}</view>
      <view class="img-total">共{{ focusLen }}张</view>
      <view class="cate-box">
        <view class="cate-list flex-row">
          <view v-if="detail.vr" class="cate" @click="switchFocus('vr')" :class="cateActive == 'vr' ? 'active' : ''"
            >VR</view
          >
          <view
            v-if="detail.videos.length > 0"
            class="cate"
            @click="switchFocus('video_list')"
            :class="cateActive == 'video_list' ? 'active' : ''"
            >视频</view
          >
          <view
            v-if="detail.img.length > 0"
            class="cate"
            @click="switchFocus('img')"
            :class="cateActive == 'img' ? 'active' : ''"
            >图片</view
          >
        </view>
      </view>
      <view v-if="hb_info.is_open" class="hongbao"   @click="toHbHelp()">
        <view class="hb_content" :animation="num == 0 ? showpic : hidepic" >
          <image class ="hb_img"   src ='/static/icon/1.png'></image>
          
        </view>
        <view class="hb_content hb_content1" :animation="num == 1 ?showpic : hidepic ">
          <image class ="hb_img"   src ='/static/icon/2.png'></image>
        </view>
      </view>
    </view>
    <!-- 内容部分 -->
    <view class="container">
      <!-- 标题 -->
      <view class="house_title">
        <!-- <text class="attr" :class="'attr' + detail.zhongjie">{{ detail.zhongjie==1?'个人':'经纪人' }}</text> -->
        {{ detail.title }}</view
      >
      <!-- 主要信息 -->
      <view class="main_info-box flex-row">
        <view class="info">
          <view class="infomation flex-row">
            <view class="price flex-row">
              <text class="value">{{ detail.price == '面议' || detail.price == '0' ? '面议' : detail.price }}</text>
              <text v-if="detail.price !== '面议' && detail.price !== '0'" class="unit">{{ detail.price_unit }}</text>
            </view>
            <view class="mianji flex-row">
              <text class="value">{{ detail.mianji }}</text>
              <text class="unit">{{detail.mianji_unit}}</text>
            </view>
          </view>
          <!-- 房源标签 -->
          <view class="label-list flex-row" v-if="detail.label && detail.label.length > 0">
            <!-- <text class="attr" :class="'attr' + detail.zhongjie">{{ detail.zhongjie==1?'个人':'经纪人' }}</text>
            <text class="label">{{ detail.catname||'' }}</text>
            <text class="label">{{ detail.areaname||'' }}</text> -->
            <text
              class="label"
              :style="{ color: item.color, borderColor: item.color }"
              v-for="(item, index) in detail.label"
              :key="index"
              >{{ item.name }}</text
            >
          </view>
        </view>
        <!-- <view class="btn" @click="addContrast(id)">+对比</view> -->
        <view>
            <button style="padding: 0 40rpx;" class="get_phone" v-if ='login_status==1&&loginByToutiaoUnion' open-type = "getPhoneNumber"   @getphonenumber="getToutiaoPhoneNumber"><view class="btn">{{is_collect?'已收藏':'+收藏'}}</view></button>
            <view class="btn" v-else  @click="handleCollect()" style="padding: 0 40rpx;">{{is_collect?'已收藏':'+收藏'}}</view>
        </view>
      </view>
      <!-- 基础信息 -->
      <view class="block">
        <view class="label">基础信息</view>
        <view class="info-list flex-row">
          <view class="info-list-item mg-b48">
            <view class="label">区域</view>
            <view class="data">{{ detail.areaname || '不详' }}</view>
          </view>
          <view class="info-list-item mg-b48" v-if="detail.catid < 3">
            <view class="label">装修</view>
            <view class="data">{{ detail.zhuangxiu || '不详' }}</view>
          </view>
          <view class="info-list-item" v-if="detail.catid < 3">
            <view class="label">楼层</view>
            <text class="data" v-if="detail.szlc"
              >{{ detail.floor_title }}</text
            >
            <!-- <text class="data" v-else>{{ detail.floor_title }}共{{ detail.louceng || '' }}层</text> -->
          </view>
          <view class="info-list-item" v-if="detail.catid == 5">
            <view class="label">流转类型</view>
            <view class="data">出租</view>
          </view>
          <view class="info-list-item">
            <view class="label">{{detail.catid == 5 ? '土地用途' : '类型'}}</view>
            <view class="data">{{ detail.type_title || '不详' }}</view>
          </view>
          <template v-if="detail.catid == 1">
            <view class="info-list-item">
              <view class="label">经营状态</view>
              <view class="data">{{detail.business_status == 1 ? '经营中' : '空置中'}}</view>
            </view>
            <view class="info-list-item" v-if="detail.trade_title">
              <view class="label">经营行业</view>
              <view class="data">{{detail.trade_ptitle}}-{{detail.trade_title}}</view>
            </view>
            <view class="info-list-item" v-if="!detail.trade_title || show_more_info">
              <view class="label">规格</view>
              <view class="data">
                <text v-if="detail.sizes_width">面宽{{detail.sizes_width}}m、层高{{detail.sizes_height}}m、进深{{detail.sizes_depth}}m</text>
                <text v-else>不详</text>
              </view>
            </view>
            <view class="info-list-item" v-if="detail.consumer_ids && show_more_info">
              <view class="label">客流人群</view>
              <view class="data">{{detail.consumer_ids}}</view>
            </view>
          </template>
          <template v-if="detail.catid == 2">
            <view class="info-list-item">
              <view class="label">可注册</view>
              <view class="data">{{detail.can_register == 1 ? '是' : '否'}}</view>
            </view>
            <view class="info-list-item">
              <view class="label">可拆分</view>
              <view class="data">{{detail.can_divisible == 1 ? '是' : '否'}}</view>
            </view>
          </template>
          <template v-if="detail.catid == 5">
            <view class="info-list-item">
              <view class="label">土地来源</view>
              <view class="data">{{detail.land_source_title}}</view>
            </view>
            <view class="info-list-item">
              <view class="label">流转年限</view>
              <view class="data">{{detail.useful_years}}年</view>
            </view>
            <view class="info-list-item">
              <view class="label">土地使用证</view>
              <view class="data">{{detail.land_certificate == 1 ? '有' : '无'}}</view>
            </view>
            <view class="info-list-item" v-if="show_more_info">
              <view class="label">所有权证</view>
              <view class="data">{{detail.owner_certificate == 1 ? '有' : '无'}}</view>
            </view>
          </template>
          <view class="info-list-item" v-if="detail.catid == 3 || detail.catid == 4 || show_more_info">
            <view class="label">更新</view>
            <view class="data">{{ detail.begintime || '' }}</view>
          </view>
          <view class="info-list-item" v-if="housePrice.name && (detail.catid == 3 || detail.catid == 4 || show_more_info)">
            <view class="label">小区</view>
            <view class="data">{{ housePrice.name || '不详' }}</view>
          </view>
          <view class="info-list-item" v-if="detail.catid == 3 || detail.catid == 4 || show_more_info">
            <view class="label">来源</view>
            <view class="data">{{ detail.zhongjie == 1 ? '个人' : '经纪人' }}</view>
          </view>
          <view class="info-list-item" v-if="detail.catid == 3 || detail.catid == 4 || show_more_info">
            <view class="label">浏览</view>
            <view class="data">{{ detail.hit || 0 }}</view>
          </view>
          <!-- 占位 -->
          <!-- 租商铺--小区、经营行业、客流人群其三者有二则补 -->
          <view class="info-list-item" v-if="show_more_info && detail.catid == 1 && 
            ((housePrice.name && detail.trade_title && !detail.consumer_ids) || 
            (housePrice.name && !detail.trade_title && detail.consumer_ids) || 
            (!housePrice.name && detail.trade_title && detail.consumer_ids))">
            <view class="label"></view>
            <view class="data"></view>
          </view>
          <!-- 租厂房、租车位--无小区则补 -->
          <view class="info-list-item" v-if="(detail.catid == 3 || detail.catid == 4) && !housePrice.name">
            <view class="label"></view>
            <view class="data"></view>
          </view>
          <view class="xiajia_icon" v-if="detail.is_show === 0">
            <my-icon type='yixiajia' size="180rpx" color='#e94e50'></my-icon>
          </view>
        </view>
        <template v-if="detail.catid == 1 || detail.catid == 2 || detail.catid == 5">
          <template v-if='agent&&agent.levelid>1'>
            <view class="more_btn has_agent" @click="show_more_info=!show_more_info">{{show_more_info?'收起':'查看更多'}}</view>
          </template>
          <template v-else>
            <view class="more_btn" @click="show_more_info=!show_more_info">{{show_more_info?'收起':'查看更多'}}</view>
          </template>
        </template>
      </view>
      <!-- 聊天咨询按钮 -->
      <template v-if="agent && agent.levelid > 1">
        <view class="btn_list-box flex-row" v-if="is_open_im">
          <view class="btn-item">
            <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 3)">
              <view class="flex-row">
                <view class="img">
                  <image
                    class="img_c"
                    mode="widthFixed"
                    :src="'/images/newhouse_detail/<EMAIL>' | iconformat"
                  ></image>
                </view>
                <!-- <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon> -->
                <text>咨询楼层</text>
              </view>
            </chatBtn>
          </view>
          <view class="btn-item">
            <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 4)">
              <view class="flex-row">
                <view class="img">
                  <image
                    class="img_c"
                    mode="widthFixed"
                    :src="'/images/newhouse_detail/<EMAIL>' | iconformat"
                  ></image>
                </view>
                <text>咨询详细设施</text>
              </view>
            </chatBtn>
          </view>
        </view>
      </template>
      <!-- 房源简介 -->
      <view class="block house_desc">
        <view class="label">房源特色</view>
        <!-- <view class="wnsb-list flex-row" v-if="detail.wnsb && detail.wnsb.length > 0">
          <text class="label" v-for="(item, index) in detail.wnsb" :key="index">{{ item }}</text>
        </view> -->
        <view class="wnsb-list flex-row" v-if="detail.wnsbs&&detail.wnsbs.length>0">
          <view class="wnsb-box" v-for="(item, index) in detail.wnsbs" :key="index">
            <view class="peitao" :style="{backgroundImage:`url(${ossDomain}/fabu/peitao/peitao${item.id}.png)`}"  :class="'peitao'+item.id"></view>
            <view>{{item.title}}</view>
          </view>
        </view>
        <view class="content_info">
          <view class="desc">
            <text class="cate">核心卖点</text>
            <text>{{ detail.content }}</text>
          </view>
          <view class="desc" v-if="detail.owner_think">
            <text class="cate">业主心态</text>
            <text>{{ detail.owner_think }}</text>
          </view>
          <view class="desc" v-if="detail.service_introduce">
            <text class="cate">服务介绍</text>
            <text>{{ detail.service_introduce }}</text>
          </view>
          <view class="tip">联系我时，请说在{{ site_name }}看到的信息，谢谢</view>
        </view>
        <!-- <view class="agent-box flex-row" v-if="agent.levelid>1">
          <image class="header_img" :src="agent.prelogo | imageFilter('w_120')"></image>
          <view class="agent_info">
            <text class="name">{{agent.cname}}</text>
            <text class="shop_name">{{agent.tname||'经纪人'}}</text>
          </view>
          <view class="into-btn" @click="$navigateTo('/pages/agent/detail?id='+agent.id)">进入主页</view>
        </view> -->
        <!-- <view class="selling_point" v-if="detail.content">
          <view class="label">核心卖点</view>
          <view class="content_info">{{detail.content}}</view>
        </view> -->
        <!-- <view class="selling_point" v-if="detail.content">
          <view class="label">户型介绍</view>
          <view class="content_info">{{detail.content}}</view>
          <view class="view_more">展开</view>
        </view> -->
      </view>
      <!-- 广告位 -->
      <swiper v-if="advs && advs.length > 0" class="ext_swiper" autoplay :interval="3000">
        <swiper-item v-for="(item, index) in advs" :key="index" @click="$navigateTo(item.wap_link)">
          <image :src="item.image | imageFilter('w_8601')" mode="aspectFill"></image>
          <view class="marker">广告</view>
        </swiper-item>
      </swiper>
      <!-- 房源动态 -->
      <template v-if="agent && agent.levelid > 1">
        <view class="block house_news">
          <view class="label flex-row"> 房源动态 </view>
          <view class="house_news_info flex-row">
            <view class="house_news_info_item">
              <view class="house_news_info_item_num">
                {{ detail.hit }}
              </view>
              <view class="house_news_info_item_title"> 浏览人数 </view>
            </view>
            <view class="house_news_info_item">
              <view class="house_news_info_item_num">
                {{ detail.tel_volume }}
              </view>
              <view class="house_news_info_item_title"> 近30天咨询 </view>
            </view>
          </view>
          <view class="house_info_timeline">
            <view class="time_line">
              <view class="item" v-for="(item, index) in detail.price_changes" :key="index">
                <view class="line-item">
                  <view class="content_c flex-row">
                    <view class="content_con flex-row"
                      ><text>{{ item.title }}</text> <text class="blod"> {{ item.price }}</text
                      ><text>{{ item.price_uint }}</text>
                      <my-icon v-if="item.type == 2 && item.diff < 0" type="ic_down" color="rgb(0, 202, 167)"></my-icon>
                      <my-icon v-if="item.type == 2 && item.diff > 0" type="ic_up" color="rgb(251, 101, 106)"></my-icon>
                    </view>
                    <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, item.type + 7)">
                      <view class="content_sub">
                        {{ item.type | formatType }}
                      </view>
                    </chatBtn>
                  </view>
                  <view class="line-header flex-box">
                    <view class="time">{{ item.time }}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>

      <!-- 所属小区 -->
      <view class="block community-box" v-if="housePrice.cid">
        <view class="label flex-row">
          <text>{{ housePrice.name }}</text>
          <view class="into flex-row" @click="$navigateTo('/pages/house_price/detail?id=' + housePrice.cid)">
            <text>小区详情</text>
            <my-icon type="ic_into" color="#999" size="28rpx"></my-icon>
          </view>
        </view>
        <view class="community flex-row" @click="$navigateTo('/pages/house_price/detail?id=' + housePrice.cid)">
          <image class="img" mode="aspectFill" :src="housePrice.img | imageFilter('w_240')"></image>
          <view class="info">
            <view class="price_row flex-row">
              <view class="price flex-row">
                <text class="label">租金均价</text>
                <text class="number">{{ housePrice.rent_price }}</text>
                <text class="unit">元/月</text>
              </view>
            </view>
            <view class="increase flex-row">
              <text class="label">比上月</text>
              <my-icon type="ic_up" v-if="housePrice.rent_statue === 1" color="#fb656a"></my-icon>
              <my-icon type="ic_down" v-else-if="housePrice.rent_statue === 2" color="#179B16"></my-icon>
              <text v-else class="ping">持平</text>
              <view class="increase-text">
                <text v-if="housePrice.rent_statue !== 0" class="value" :class="{ down: housePrice.rent_statue === 2 }">{{
                  housePrice.rent_value
                }}</text>
                <text v-if="housePrice.rent_statue !== 0" class="unit" :class="{ down: housePrice.rent_statue === 2 }"
                  >%</text
                >
              </view>
            </view>
            <view class="address flex-row">
              <text class="label">小区地址</text>
              <text class="value">{{ housePrice.address || '暂未更新' }}</text>
            </view>
          </view>
        </view>
        <view
          class="more_btn no_bg"
          v-if="housePrice.rent_count > 0"
          @click="$navigateTo(`/commercial/commercial?cid=${housePrice.cid}&catid=${detail.catid}&parentid=${detail.parentid}`)"
          >查看同小区{{ housePrice.rent_count }}套在租房源></view
        >
      </view>
      <!-- 聊天咨询按钮 -->
      <!-- <view class="btn_list-box flex-row" v-if="is_open_im">
        <view class="btn-item">
          <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 1)">
            <view class="flex-row">
              <view class="img">
                <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
              </view>
              <text>咨询房贷首付</text>
            </view>
          </chatBtn>
        </view>
        <view class="btn-item">
          <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 2)">
            <view class="flex-row">
              <view class="img">
                <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
              </view>
              <text>咨询详细税费</text>
            </view>
          </chatBtn>
        </view>
      </view> -->
      <!-- TA的其他房源 -->
      <view class="block" v-if="agent && agent.levelid > 1 && other_house.length > 0">
        <view class="label">
          <text>TA的其他房源</text>
          <text class="more" @click="$navigateTo(`/pages/agent/detail?id=${agent.id}&parentid=2`)">更多</text>
        </view>
        <swiper class="other_house-list" :duration="300" :display-multiple-items="2" next-margin="88rpx">
          <swiper-item v-for="(item, index) in other_house" :key="index">
            <view class="swiper-item" @click="toOtherDetail(item)">
              <view class="img-box">
                <image :src="item.img | imageFilter('w_320')" mode="aspectFill"></image>
              </view>
              <view class="house_type">{{ item.title }}</view>
              <view class="aligin-end flex-row">
                <text class="mianji">{{ item.mianji }}{{item.mianji_unit}}</text>
              </view>
              <view class="price-box flex-row">
                <view v-if="item.parentid == 1" class="flex-row">
                  <text class="price">{{ item.price }}</text><text class="unit">{{ item.price_unit }}</text>
                </view>
                <view v-if="item.parentid == 2 || item.parentid == 3" class="flex-row">
                  <text class="price">{{ item.price }}</text><text class="unit">{{ item.price_unit }}</text>
                </view>
                <view v-if="item.parentid == detail.parentid && item.catid == detail.catid" class="db_btn" @click.prevent.stop="addContrast(item.id)">+对比</view>
              </view>
            </view>
          </swiper-item>
          <swiper-item v-if="other_house.length < 2"></swiper-item>
        </swiper>
      </view>
      <!-- 位置及周边 -->
      <view class="block" v-if="detail.lat && detail.lng">
        <view class="label">位置及周边</view>
        <mapNearby
          :scale="mapData.scale"
          :cirles ="cirles"
          :enableZoom="false"
          :enableScroll ='false'
          :lat="detail.lat"
          :lng="detail.lng"
          :markers="mapData.covers"
          @clickMap="viewMap()"
          @clickCate="getCovers"
        ></mapNearby>
      </view>
      <!-- 推荐房源 -->
      <view class="block" v-if="recommend_list.length > 0">
        <view class="label">推荐房源</view>
        <block v-for="item in recommend_list" :key="item.id">
          <list-item
            :item-data="item"
            type="rent"
            @click="$navigateTo(`/commercial/rent/detail?id=${item.id}`)"
          ></list-item>
        </block>
      </view>
      <view class="entrant_button" v-if="agent && agent.levelid > 1" @click="showWeituo">
        房源不合适?委托TA帮我找房
      </view>
      <!-- 免责声明 -->
      <view class="shengming">
        <view class="shengming_title flex-row">
          <text>免责声明</text>
          <text class="label" @click="$navigateTo(`/user/inform/inform?type=5&id=${id}`)">举报</text>
        </view>
        <view class="shengming_content" v-html="disclaimer"></view>
      </view>

      <!-- 底部操作菜单 -->
      <view class="bottom-bar flex-row" v-if="show == true && detail.is_show === 1">
        <view class="bar-left flex-row">
          <view
            class="icon-btn"
            v-if="agent && agent.levelid > 1"
            @click="$navigateTo('/pages/agent/detail?id=' + agent.id)"
          >
            <image :src="agent.prelogo | imageFilter('w_120')" class="header_img"></image>
            <text>{{ agent.cname || '经纪人' }}</text>
          </view>
          <!-- <view class="icon-btn" v-else @click="toHome()">
            <my-icon type="ic_shouyed" size="50rpx"></my-icon>
            <text>首页</text>
          </view> -->
          <!-- <view class="icon-btn" @click="handleCollect()">
            <my-icon v-if="is_collect" type="ic_shoucang_red" size="50rpx" color="#fb656a"></my-icon>
            <my-icon v-else type="ic_shoucang" size="50rpx"></my-icon>
            <text>收藏</text>
          </view> -->
          <view class="icon-btn" @click="handleSharp()" style="padding-top:5rpx">  
            <my-icon type="ic_fenxiang" color="#666" size="40rpx"></my-icon>
            <text>分享</text>
          </view>
          <view class="icon-btn last" @click="toContrast">
            <text class="badge" v-if="login_status > 1 && contrastCount > 0">{{
              contrastCount > 99 ? '99+' : contrastCount
            }}</text>
            <text class="badge" v-if="login_status <= 1 && $store.state.temp_renting_contrast_ids.length > 0">{{
              $store.state.temp_renting_contrast_ids.length > 99 ? '99+' : $store.state.temp_renting_contrast_ids.length
            }}</text>
            <my-icon type="pk" color="#666" size="50rpx"></my-icon>
            <text>对比</text>
          </view>
        </view>
        <view class="bar-right flex-row flex-1">
          <view class="bar-btn btn1 flex-1" v-if="is_open_im" @click="showWechat()">在线咨询</view>
          <view class="bar-btn btn2 flex-1" :class="{ alone: !is_open_im }" @click="handleTel()">电话咨询</view>
        </view>
      </view>
    </view>
    <!-- 分享选项 -->
    <share-pop ref="share_popup" @copyLink="show_share_tip=true" @appShare="appShare" @handleCreat='handleCreat' @showCopywriting='showCopywriting'></share-pop>
    <!-- 复制分享文案 -->
    <my-popup ref="text_popup" position="center" :height="text_popup_height">
      <view class="copy-text-box" id="copy-text">
        <view class="title">{{ detail.title }}</view>
        <view class="info-box">
          <view class="info-row flex-row" v-if="housePrice.name">
            <text class="label">小区：</text>
            <text class="value">{{ housePrice.name }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">面积：</text>
            <text class="value">{{ `${detail.mianji}${detail.mianji_unit}` }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">租金：</text>
            <text class="value">{{ detail.price ? detail.price + detail.price_unit : '面议' }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">{{detail.catid == 5 ? '土地用途：' : '类型：'}}</text>
            <text class="value">{{ detail.type_title }}</text>
          </view>
          <view class="info-row flex-row" v-if="detail.catid < 3">
              <text class="label">楼层：</text>
              <text class="value" v-if="detail.szlc">{{detail.floor_title}}{{detail.szlc}}层{{detail.szlc2 !== 0 ? `至${detail.szlc2}层` : ''}}/共{{detail.louceng||''}}层</text>
              <text class="value" v-else>{{detail.floor_title}}共{{detail.louceng||''}}层</text>
            </view>
          <template v-if="detail.catid == 1">
            <view class="info-row flex-row">
              <text class="label">经营状态：</text>
              <text class="value">{{detail.business_status == 1 ? '经营中' : '空置中'}}</text>
            </view>
            <view class="info-row flex-row" v-if="detail.trade_title">
              <text class="label">经营行业：</text>
              <text class="value">{{detail.trade_ptitle}}-{{detail.trade_title}}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">规格：</text>
              <text class="value">面宽{{detail.sizes_width}}m、层高{{detail.sizes_height}}m、进深{{detail.sizes_depth}}m</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">客流人群：</text>
              <text class="value">{{detail.consumer_ids}}</text>
            </view>
          </template>
          <template v-if="detail.catid == 2">
            <view class="info-row flex-row">
              <text class="label">可注册：</text>
              <text class="value">{{detail.can_register == 1 ? '是' : '否'}}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">可拆分：</text>
              <text class="value">{{detail.can_divisible == 1 ? '是' : '否'}}</text>
            </view>
          </template>
          <template v-if="detail.catid == 5">
            <view class="info-row flex-row">
              <text class="label">土地来源：</text>
              <text class="value">{{detail.land_source_title}}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">流转年限：</text>
              <text class="value">{{detail.useful_years}}年</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">土地使用证：</text>
              <text class="value">{{detail.land_certificate == 1 ? '有' : '无'}}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">所有权证：</text>
              <text class="value">{{detail.owner_certificate == 1 ? '有' : '无'}}</text>
            </view>
          </template>
          <view class="info-row flex-row" v-if="detail.label&&detail.label.length>0">
            <text class="label">卖点：</text>
            <text class="value">{{detail.label.map(item=>item.name).join(' ')}}</text>
          </view>
          <view
            class="info-row flex-row"
            v-if="(agent && !agent.shareId && agent.levelid > 1) || (agent && agent.shareId)"
          >
            <text class="label">电话：</text>
            <text class="value" v-if="agent && agent.shareId">{{ agent.tel ? agent.tel : '' }}</text>
            <text class="value" v-if="agent && !agent.shareId && agent.levelid > 1">{{
              detail.tel ? detail.tel : ''
            }}</text>
          </view>
          <view class="button disabled-btn flex-row" v-if="copy_success">
            <my-icon type="check-circle" size="30rpx" color="#999"></my-icon>
            <text class="text">文本已复制</text>
          </view>
          <view class="button" v-else @click="copywriting">复制文本</view>
        </view>
      </view>
    </my-popup>
    <enturstBtn v-if="agent && (agent.agent_id || agent.adviser_id)" :to_user="agent" @click="showWeituo" />
    <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
      <enturstBox
        @success="$refs.enturst_popup.hide()"
        :isDetail="isDetail"
        @close="$refs.enturst_popup.hide()"
        @popup_login="$navigateTo('/user/login/login')"
        :to_user="agent"
      />
    </my-popup>
    <!-- 登录弹窗 -->
    <!-- #ifndef MP-WEIXIN -->
    <login-popup
      ref="login_popup"
      @onclose="handleCloseLogin"
      :sub_content="login_tip"
      @success="onLoginSuccess"
    ></login-popup>
    <!-- #endif -->
    <hongbao v-if="hb_result" ref="hongbao" :money="hb_info.hb_money" :expire_seconds="hb_result.expire_seconds" @openHb="openHb"></hongbao>
    <chat-tip></chat-tip>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import { uniList, uniListItem } from '@dcloudio/uni-ui'
import { formatImg, getSceneParams, showModal, config } from '../../common/index.js'
import myIcon from '../../components/myIcon.vue'
import mapNearby from '../../components/mapNearby.vue'
import listItem from '../components/listItem.vue'
import myPopup from '../../components/myPopup.vue'
import shareTip from '../../components/shareTip.vue'
import allTel from '../../common/all_tel.js'
import getChatInfo from '../../common/get_chat_info'
import checkLogin from '../../common/utils/check_login'
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
import chatBtn from '@/components/open-button/chatBtn'
import hongbao from '@/components/hongbao'
import getLocation from '../../common/get_location'
// #ifndef MP-WEIXIN
import loginPopup from '../../components/loginPopup'
// #endif
import sharePop from '../../components/sharePop'
import { getLonAndLat } from '@/common/utils/getLonAndLat'
export default {
  components: {
    uniList,
    uniListItem,
    myIcon,
    mapNearby,
    listItem,
    myPopup,
    shareTip,
    enturstBtn,
    enturstBox,
    chatBtn,
    loginPopup,
    hongbao,
    sharePop,
  },
  data() {
    return {
      id: '',
      loading:false,
      detail: {
        price: '',
        img: [],
        videos: [],
      },
      agent: {},
      is_collect: 0,
      housePrice: {},
      loan_type: 0,
      other_house: [], //附近房源
      recommend_list: [], //推荐的房源
      focus: [],
      focusLen: '',
      swiperCurrent: 0,
      cateActive: '',
      mapList: [],
      mapData: {
        scale: 12,
        covers: [],
      },
      opacity:0,
      showBackBtn:1,
      iconColor:"#fff",
      show: false,
      show_share_tip: false,
      jingjiren: 0, //当前用户是不是经纪人
      show_more_info: false,
      show_tel_pop: false,
      contrastCount: 0,
      disclaimer: '',
      text_popup_height: '',
      copy_success: false,
      currentUserInfo: {},
      sid: '',
      shareType: '',
      toLogin: true,
      login_tip: '',
      advs: [],
      sub_type: 0,
      tel_res: {},
      isDetail: 0,
      hb_info: {},
      hb_result: {},
      hb: '',
      task_id: '',
      hongbao_gif: config.imgDomain+'/hongbao/linghongbao.png',
      saihongbao_gif: config.imgDomain+'/hongbao/saihongbao.gif',
      isLogin: false,
      map_key: '',
      hb_share_query: '',
      num:0,
      showpic:{},
      hidepic:{},
      moteNum:0,
      tuiguang_mp3:"/static/icon/voice/tuiguang_bg.mp3"
    }
  },
  onLoad(options) {
    // app端没有安装微信 处理
    // #ifdef APP-PLUS
    if (!this.hasWechat) {
      let webView = this.$mp.page.$getAppWebview()
      webView.setTitleNViewButtonStyle(0, {
        type: 'none',
        width: 0,
      })
    }
    // #endif

    // #ifdef MP
    if (options.scene) {
      const params = getSceneParams(decodeURIComponent(options.scene))
      if (params.id) {
        this.id = params.id
        this.getData(this.id)
      }
      return
    }
    // #endif
    if (JSON.stringify(this.$store.state.tempData) != '{}') {
      var tempDate = JSON.parse(JSON.stringify(this.$store.state.tempData))
      tempDate.img = ''
      Object.assign(this.detail, tempDate)
      uni.setNavigationBarTitle({
        title: this.detail.title,
      })
      this.$store.state.tempData = {}
    } else if (options.title) {
      this.detail.title = decodeURIComponent(options.title)
      uni.setNavigationBarTitle({
        title: this.detail.title,
      })
    }
    if (options.shareId) {
      this.share_time =options.f_time||''
      this.sid = options.shareId
      this.shareType = options.shareType
    }
    if (options.id) {
      this.id = options.id
      this.getData(options.id)
    }
    this.gettap()
  },
  onShow() {
    if (this.reload) {
      this.reload = false
      this.$store.state.allowOpen = true
      this.getData(this.id)
    }
  },
  onPageScroll(e){
    this.opacity =e.scrollTop/180
    if (this.opacity>1){
      this.opacity=1
    }
    if (this.opacity<0){
      this.opacity=0
    }
    
    if (this.opacity>0.3){
      this.iconColor="#000"
    }else {
      this.iconColor="#fff"
    }
  },
  onUnload() {
    if (this.innerAudioContext){
        this.innerAudioContext.destroy()
    }
  this.showpic={}
  this.hidepic={}
  if (this.setInterval1){
      clearInterval(this.setInterval1)
    }
    this.$store.state.buildInfo = {}
    this.$store.state.tempData = {}
  },
  filters: {
    iconformat(val) {
      return config.imgDomain + val
    },
    formatType(val) {
      if (val == '1') return '咨询优惠'
      if (val == '2') return '咨询底价'
      if (val == '3') return '咨询成交价'
    },
  },
  computed: {
    is_open_im() {
      return this.$store.state.im.ischat
    },
    is_open_middle_num() {
      return this.$store.state.im.istelcall
    },
    hasWechat() {
      return this.$store.state.hasWechat
    },
    login_status() {
      return this.$store.state.user_login_status
    },
    site_name() {
      return this.$store.state.siteName
    },
    status_top(){
      return 0
    },
    ossDomain() {
      return config.imgDomain
    },
    oneKm(){
      return getLonAndLat(this.detail.lng,this.detail.lat,0,1000)
    },
    twoKm(){
      return getLonAndLat(this.detail.lng,this.detail.lat,0,2000)
    },
    threeKm(){
      return getLonAndLat(this.detail.lng,this.detail.lat,0,3000)
    },
    cirles(){
      if(this.detail &&this.detail.lat) {
        return [
          {
							longitude:this.detail.lng,
							latitude:this.detail.lat,
							color:"#ff0000",
							radius:1000,
							strokeWidth:1,
						},
						{
							longitude:this.detail.lng,
							latitude:this.detail.lat,
							color:"#ff9c00",
							radius:2000,
							strokeWidth:1
						},
						{
							longitude:this.detail.lng,
							latitude:this.detail.lat,
							color:"#fee500",
							fillColor:"#00000026",
							radius:3000,
							strokeWidth:1
						}
        ]
      }
    }
  },
  methods: {
    getData(id) {
      let params = {
        lat: this.$store.state.position.lat,
        lng: this.$store.state.position.lng,
        id: id,
        sid: this.sid,
        sharetype: this.shareType,
        forward_time:this.share_time ||''
      }
      this.$ajax.get('estate/detail', params, (res) => {
        if (res.data.code == 0) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
          this.show = true
          uni.hideLoading();
          return
        }
        this.parameter = res.data.parameter
        this.contrastCount = res.data.contrastCount
        this.disclaimer = res.data.disclaimer
        // res.data.info.wnsb = res.data.info.wnsb ? res.data.info.wnsb.split(',') : []
        this.detail = res.data.info
        this.showBackBtn=res.data.switch_wap_info_title
        if (res.data.adv && res.data.adv.length > 0) {
          this.advs = res.data.adv
        }
        this.agent = res.data.agent
        if (this.agent && this.agent.id) {
          this.agent.infoDetailId = this.agent.id
        }
        if (res.data.share_user && res.data.share_user.agent_id) {
          this.agent = res.data.share_user
          this.agent.levelid = res.data.share_user.levelid ? res.data.share_user.levelid : 2
          this.agent.id = this.agent.agent_id
          this.agent.shareId = res.data.share_user.agent_id
        }
        if (res.data.community) {
          this.housePrice = res.data.community
        }
        // 获取用户信息
        this.currentUserInfo = res.data.shareUser
        if (this.currentUserInfo.adviser_id > 0) {
          this.currentUserInfo.shareType = 1
          this.currentUserInfo.sid = this.currentUserInfo.adviser_id
        } else if (this.currentUserInfo.agent_id > 0) {
          this.currentUserInfo.shareType = 2
          this.currentUserInfo.sid = this.currentUserInfo.agent_id
        } else {
          this.currentUserInfo = {
            sid: this.sid,
            shareType: this.shareType,
          }
        }
        if (this.sid) {
          // 获取登陆状态
          checkLogin({
            success: (res) => {
              this.loginState()
            },
            fail: (res) => {
              if(this.$store.state.user_login_status==1){
                  if (this.toLogin==false) return 
                  this.toLogin=false
                  uni.setStorageSync('backUrl', window.location.href)
                  this.$navigateTo("/user/login/login")
              }else {
                this.loginState()
              }
            },
            complete: (res) => {
              this.$store.state.user_login_status = res.status
            },
          })
        }else {
          this.loginState()
        }

        // 设置地图中心点
        if (this.detail.lat && this.detail.lng) {
          // this.getCovers('商业')
          this.mapData.covers = [
            {
              latitude: this.detail.lat,
              longitude: this.detail.lng,
              width: 30,
              height: 30,
              iconPath: '/static/icon/center.png',
            },
          ]
        }
        this.other_house = res.data.others
        this.recommend_list = res.data.recommendList
        this.is_collect = res.data.is_collect
        uni.setNavigationBarTitle({
          title: res.data.info.title,
        })
        this.video_num = res.data.info.video_list.length
        this.img_num = res.data.info.images.length
        // 合并图片视频和全景图
        this.imgs = res.data.info.images
        let imgs = res.data.info.images.map((item) => {
          return {
            type: 'img',
            url: item,
          }
        })
        let videos = res.data.info.video_list.map((item) => {
          return {
            type: 'video_list',
            url: item.cover,
          }
        })
        this.focus = videos.concat(imgs)
        this.focus = [...videos, ...imgs]
        if (res.data.info.vr) {
          this.vr_num = 1
          this.focus.unshift({
            type: 'vr',
            url: res.data.info.vr,
          })
        } else {
          this.vr_num = 0
        }
        this.focusLen = this.focus.length
        this.cateActive = this.focus[0].type
        let link=''
        let time =parseInt(+new Date()/1000)
        if (this.currentUserInfo.sid){
          link="https://"+window.location.host+"/h5/commercial/rent/detail?id="+this.id +"&isShare=1&shareType="+this.currentUserInfo.shareType+"&shareId="+this.currentUserInfo.sid+"&f_time="+time
        }else {
          link = window.location.href.split('&hb=')[0]
        }
        // if (this.hb_share_query) {
        //   link += `&${this.hb_share_query}`
        // }
        this.share = {
          title:res.data.info.title,
          content:res.data.info.mianji+res.data.info.mianji_unit+'/'+res.data.info.price+res.data.info.price_unit,
          pic:res.data.info.images[0]||'',
          link:link
        }
        this.getWxConfig()
        this.show = true
        uni.hideLoading();
        this.loading = true
      })
    },
    gettap() {
            uni.showLoading({
                title: '加载中'
            });
    },
    toAgent(id) {
      if (!id) {
        return
      }
      this.$navigateTo('/pages/agent/detail?id=' + id)
    },
    checkLogin(tip, callback) {
      this.$ajax.get('member/checkUserStatus', {}, (res) => {
        if (res.data.code === 1) {
          callback && callback()
        } else {
          this.$store.state.user_login_status = res.data.status
          this.login_tip = tip
          this.$refs.login_popup.showPopup()
        }
      })
    },
    handleCloseLogin() {
      if (this.$store.state.user_login_status === 1) {
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if (this.$store.state.user_login_status === 2) {
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    onLoginSuccess(res) {
      this.$store.state.user_login_status = 3
      if (this.weituo_is_show) {
        console.log('登录成功后继续执行委托接口')
        this.$refs.enturst_box.handleEnturst()
      }
    },
    getSendMsg(e, type) {
      // #ifdef MP-WEIXIN
      if (this.sid){
        this.showWechat(2)
        return 
      }
      this.$ajax.get('im/getUserReplyOfAgentEstate.html', { page_from: type, info_id: this.id }, (res) => {
        if (res.data.mid) {
          this.$store.state.autoSendMsg = res.data.content || ''
          console.log(this.$store.state.autoSendMsg)
          getChatInfo(res.data.mid, 6, this.id)
        }
      })
      // #endif
      // #ifndef MP-WEIXIN
      if (this.sid){
        this.showWechat(2)
        return 
      }
      this.checkLogin('当前操作需要绑定手机号，请输入您的手机号', () => {
        this.$ajax.get('im/getUserReplyOfAgentEstate.html', { page_from: type, info_id: this.id }, (res) => {
          console.log(this.agent)
          if (res.data.mid) {
            this.$store.state.autoSendMsg = res.data.content || ''
            setTimeout(() => {
              getChatInfo(res.data.mid, 6, this.id)
            }, 500)
          }
        })
      })
      // #endif
    },
    showWeituo(type) {
      // if (type =="share"){
      //   this.isDetail=0
      // }else if (type =="bottom"){
      //   this.isDetail=1
      // }
      if (this.agent.shareId) {
        this.isDetail = 0
      } else {
        this.isDetail = 1
      }
      this.$refs.enturst_popup.show()
    },
    swiperChange(e) {
      this.swiperCurrent = e.detail.current
      this.cateActive = this.focus[this.swiperCurrent].type
    },
    switchFocus(type) {
      switch (type) {
        case 'vr':
          this.swiperCurrent = 0
          break
        case 'video':
          this.swiperCurrent = this.vr_num
          break
        case 'img':
          this.swiperCurrent = this.vr_num + this.video_num
          break
        default:
          this.swiperCurrent = 0
      }
    },
    toHome() {
      uni.switchTab({
        url: '/pages/index/index',
      })
    },
    showWechat(type=1) {
      if (this.detail.shixiao === 1) {
        uni.showToast({
          title: '此信息已失效',
          icon: 'none',
        })
        return false
      }
      if (!uni.getStorageSync('token')) {
        this.$navigateTo('/user/login/login')
        this.reload = true
        return
      }
      let thumb
      if (this.imgs.length > 0) {
        thumb = this.imgs[0]
      } else if (this.focus.length > 0) {
        thumb = this.focus[0].url
      }
      this.$store.state.buildInfo = {
        id: this.id,
        title: this.detail.title,
        type: 'commercial_rent',
        catid: this.detail.catid,
        image: thumb,
        desc: ``,
        price: `${this.detail.price ? this.detail.price + this.detail.price_unit : '面议'}`,
      }
      if (type=1) {
        getChatInfo(this.agent && this.agent.agent_id ? this.agent.agent_id : this.agent.id, 33)
      }else {
        getChatInfo(this.sid, 33)
      }
      
    },
    handleCollect() {
      this.$store.state.allowOpen = true
      if (!this.is_collect) {
        this.collect()
      } else {
        this.noCollect()
      }
    },
    collect() {
      this.$ajax.get(
        'estate/infoCollect',
        {
          id: this.id,
        },
        (res) => {
          if (res.data.code == 1) {
            this.is_collect = 1
            uni.showToast({
              title: res.data.msg,
              duration: 2000,
            })
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000,
            })
          }
        }
      )
    },
    noCollect() {
      this.$ajax.get(
        'estate/cancelCollect',
        {
          id: this.id,
        },
        (res) => {
          if (res.data.code == 1) {
            this.is_collect = 0
            uni.showToast({
              title: res.data.msg,
              duration: 2000,
            })
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000,
            })
          }
        }
      )
    },
    // 获取地图附近周边
    getCovers(e, type = 5) {
      let params = {
        id: this.id,
        keywords:e?e.type:'',
        type: type,
      }
      let api ='map/mapNearbyMatches.html'
      
      this.$ajax.get(
        api,
        params,
        (res) => {
          if (res.data.code != 1) {
            return
          }
					if (!res.data.done&&this.moteNum <5 &&!e){
						this.moteNum ++
						this.getCovers(e, type)
						return 
					}
          let covers=[]
          res.data.matches.map(cover => {
            let icon,color,bgColor,title
            switch(cover.keyword)
						{
						case '商业':
							icon = '/static/icon/foot.png'
							bgColor= "#ffbabc"
							title="商"
							color="#fff"
							break
						case '教育':
							icon = '/static/icon/edu.png'
							title="教"
							bgColor="#34dec1"
							color="#fff"
							break
						case '医疗':
							icon = '/static/icon/yiliao.png'
							title="医"
							bgColor="#feb9bb"
							color="#fff"
							break
						case '交通':
							icon = '/static/icon/jiaotong.png'
							bgColor="#66d1fa"
							title ="交"
							color="#fff"
							break
						default:
							icon = '/static/icon/center.png'
						}
						if (cover.data&&cover.data.length) {
								cover.data.map(item=>{
                 let ob = {
                    width: 30,
                    height: 30,
                    iconPath: icon,
                    latitude: item.location.lat,
                    longitude: item.location.lng,
                    title: item.title,
                    address: item.address,
                    _distance: item._distance,
                    callout: {
                      content:  ((e && e.scale<=14) || !e)?title:item.title,
                      padding: 5,
											fontSize:10,
											boxShadow:'none',
											bgColor,
											color,
											borderRadius: 4,
											borderColor:bgColor,
											display:'ALWAYS'
                    },
                    distance: parseInt(item._distance)
                  }
                 	covers.push(ob)
									return item
                })
            }
            
						return cover
          })
          covers.push({
            latitude: this.detail.lat,
            longitude: this.detail.lng,
            width: 30,
            height: 30,
            iconPath: '/static/icon/center.png',
          })
          covers.push({
						latitude: this.oneKm.lat,
						id:"a"+1,
						longitude: this.oneKm.lon,
						width: -1,
						height:-1,
						label: {
							content:'1公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff0000",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5,
							borderColor:'#ffffff'
						},
						iconPath: '/static/icon/center.png'
					})
					covers.push({
						latitude: this.twoKm.lat,
						longitude: this.twoKm.lon,
						width: -1,
						height: -1,
						id:"a"+2,
						label: {
							content:'2公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff9c00",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
						},
						iconPath: '/static/icon/center.png'
					})
					covers.push({
						latitude: this.threeKm.lat,
						longitude: this.threeKm.lon,
						width: -1,
						height: -1,
						id:"a"+3,
						label: {
							content:'3公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#fee500",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
							},
						iconPath: '/static/icon/center.png'
					})
          this.mapData.covers = covers
        },
        (err) => {}
      )
    },
    
    // #ifdef APP-PLUS
    appShare(type = 'WXSceneSession') {
      let href = '',time=parseInt(+new Date()/1000)
      if (this.currentUserInfo.sid) {
        href =
          config.apiDomain +
          '/h5/commercial/rent/detail?id=' +
          this.detail.id +
          '&isShare=1&shareType=' +
          this.currentUserInfo.shareType +
          '&shareId=' +
          this.currentUserInfo.sid+"&f_time="+time
      } else {
        href = config.apiDomain + '/h5/commercial/rent/detail?id=' + this.detail.id
      }
      uni.share({
        provider: 'weixin',
        type: 0,
        title: this.detail.title || '',
        scene: type,
        imageUrl: formatImg(this.detail.images[0], 'w_220') || '',
        summary: this.detail.mianji + this.detail.mianji_unit + this.detail.price + this.detail.price_unit,
        href: href,
        success: function (res) {
          console.log('success:' + JSON.stringify(res))
          uni.showToast({
            title: '分享成功',
            icon: 'none',
          })
        },
        fail: function (err) {
          uni.showToast({
            title: '分享失败:' + JSON.stringify(err),
            icon: 'none',
          })
          console.log('fail:' + JSON.stringify(err))
        },
      })
    },
    // #endif
    handleCreat() {
      this.$navigateTo(`${location.origin}/wapi/poster/branch?type=17&id=${this.id}`)
    },

    showCopywriting() {
      const query = uni.createSelectorQuery().in(this)
      query
        .select('#copy-text')
        .fields({ rect: true, scrollOffset: true, size: true }, (data) => {
          this.text_popup_height = data.height + 'px'
        })
        .exec()
      this.copy_success = false
      this.$refs.text_popup.show()
      this.$refs.share_popup.hide()
    },
    copywriting() {
      let link = '',time=parseInt(+new Date()/1000)
      // #ifdef H5
      if (this.currentUserInfo.sid) {
        link =
          'https://' +
          window.location.host +
          '/h5/commercial/rent/detail?id=' +
          this.detail.id +
          '&isShare=1&shareType=' +
          this.currentUserInfo.shareType +
          '&shareId=' +
          this.currentUserInfo.sid+"&f_time="+time
      } else {
        link = window.location.href
      }

      // #endif
      // #ifndef H5
      if (this.currentUserInfo.sid) {
        link =
          config.apiDomain +
          '/h5/commercial/rent/detail?id=' +
          this.detail.id +
          '&isShare=1&shareType=' +
          this.currentUserInfo.shareType +
          '&shareId=' +
          this.currentUserInfo.sid+"&f_time="+time
      } else {
        link = config.apiDomain + '/h5/commercial/rent/detail?id=' + this.detail.id
      }
      // #endif
      let tel = ''
      if (this.agent && this.agent.shareId) {
        //分享过来的
        tel = '【电话】' + this.agent.tel + '\n'
      } else if (this.agent && !this.agent.shareId && this.agent.levelid > 1) {
        //经纪人
        tel = '【电话】' + this.detail.tel + '\n'
      }
      let louceng = ''
      if (this.detail.catid < 3) {
        louceng = `【楼层】${this.detail.floor_title}${this.detail.szlc?this.detail.szlc+'层'+(this.detail.szlc2 !== 0?'至'+this.detail.szlc2+'层':''):''}${'共' + this.detail.louceng + '层'}\n`
      }
      let info = ''
      if (this.detail.catid == 1) {
        info = `【经营状态】${this.detail.business_status == 1 ? '经营中' : '空置中'}
${this.detail.trade_title ? '【经营行业】'+this.detail.trade_ptitle+'-'+this.detail.trade_title+'\n' : ''}【规格】面宽${this.detail.sizes_width}m、层高${this.detail.sizes_height}m、进深${this.detail.sizes_depth}m\n`
      }
      if (this.detail.catid == 2) {
        info = `【可注册】${this.detail.can_register == 1 ? '是' : '否'}\n【可拆分】${this.detail.can_divisible == 1 ? '是' : '否'}\n`
      }
      if (this.detail.catid == 5) {
        info = `【土地来源】${this.detail.land_source_title}\n【流转年限】${this.detail.useful_years}年
【土地使用证】${this.detail.land_certificate == 1 ? '有' : '无'}\n【所有权证】${this.detail.owner_certificate == 1 ? '有' : '无'}\n`
      }
      const text = `${this.detail.title}
${this.housePrice && this.housePrice.name ? '【小区】' + this.housePrice.name + '\n' : ''}【面积】${this.detail.mianji}${this.detail.mianji_unit}
【租金】${this.detail.price ? this.detail.price + this.detail.price_unit : '面议'}
${this.detail.catid == 5 ? '【土地用途】' : '【类型】'}${this.detail.type_title}
${louceng}${info}${this.detail.label.length > 0 ? '【卖点】'+this.detail.label.map((item) => item.name).join(' ') + '\n' : ''}${tel}【查看】${link}`
      this.copyContent(text, () => {
        this.copy_success = true
      })
    },
    // #ifdef H5
    copyLink() {
      let link = '',time=parseInt(+new Date()/1000)
      if (this.currentUserInfo.sid) {
        link =
          'https://' +
          window.location.host +
          '/h5/commercial/rent/detail?id=' +
          this.detail.id +
          '&isShare=1&shareType=' +
          this.currentUserInfo.shareType +
          '&shareId=' +
          this.currentUserInfo.sid+"&f_time="+time
      } else {
        link = window.location.href
      }
      // 红包分享加参
      // if (this.hb_share_query) {
      //   link += `&${this.hb_share_query}`
      // }
      // let link = window.location.href
      this.copyContent(link, () => {
        uni.showToast({
          title: '复制成功,去发送给好友吧',
          icon: 'none',
        })
      })
    },
    // #endif
    // #ifndef H5
    copyContent(cont) {
      uni.setClipboardData({
        data: cont,
        success: (res) => {
          // uni.showToast({
          //   title: "复制成功",
          //   icon: "none"
          // })
        },
      })
    },
    // #endif
    // #ifdef H5
    copyContent(cont, callback) {
      let oInput = document.createElement('textarea')
      oInput.value = cont
      document.body.appendChild(oInput)
      oInput.style.opacity = 0
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length)
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      })
      oInput.blur()
      oInput.remove()
      if (callback) callback()
    },
    // #endif
    handleTel() {
      if (this.detail.shixiao === 1) {
        uni.showToast({
          title: "此信息已失效",
          icon: 'none'
        })
        return false
      }
      this.tel_params = {}
      if(this.agent&&this.agent.shareId){
        this.tel_params={
          type: 3,
          scene_type: 6,
          scene_id: this.detail.id,
          callee_id: this.agent.id,
        }
      }else{
        this.tel_params={
          type: 6,
          scene_type: 6,
          scene_id: this.detail.id,
          callee_id: this.detail.id,
        }
      }
      if (this.sid) {
        this.tel_params.type = 3
      }
      this.tel_params.intercept_login = true
      this.tel_params.success = (res)=>{
        // console.log(res)
        this.tel_res = res.data
        this.show_tel_pop = true
      }
      this.tel_params.fail = (res)=>{
        switch(res.data.code){
          case -1:
            this.reload = true
            this.$navigateTo('/user/login/login')
            break
          case 2:
            this.reload = true
            // #ifdef H5 || APP-PLUS || MP-BAIDU
            this.$navigateTo('/user/login/login')
            // #endif
            // #ifdef MP-WEIXIN
            this.$navigateTo('/user/bind_phone/bind_phone')
            // #endif
            break
          case -5:
            showModal({
              title: "安全验证，防恶意骚扰已开启",
              content: "验证后可免费发布查看信息。",
              confirm: () => {
                if (res.data.is_agent){
                  this.$navigateTo('/user/member_upgrade')
                }else{
                  this.$navigateTo('/user/member_upgrade?is_personal=1')
                }
              }
            })
            break
          case -10:
            console.log("账号被封禁")
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            break
          default:
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
        }
      }
      allTel(this.tel_params)
    },
    getShortLink(){
      let time =parseInt(+new Date()/1000)
      if (this.currentUserInfo.sid){
        this.link="https://"+window.location.host+"/h5/commercial/transfer/detail?id="+this.id +"&isShare=1&shareType="+this.currentUserInfo.shareType+"&shareId="+this.currentUserInfo.sid+"&f_time="+time
      }else {
        this.link = window.location.href
      }
			this.$ajax.get("build/shortUrl.html",{page_url:this.link},(res)=>{
				if(res.data.code ==1){
					this.link=res.data.short_url
				}
			})
    },
    preImg(index) {
      let img_index = index - this.detail.videos.length
      let img_urls = this.detail.images.map((item) => {
        return formatImg(item, 'w_860')
      })
      uni.previewImage({
        current: img_urls[img_index],
        indicator: 'number',
        urls: img_urls,
      })
    },
    toVr() {
      this.$navigateTo('/vr/detail?estateid=' + this.id)
    },
    preVideo(url) {
      this.$navigateTo('/vr/prevideo?type=3&id=' + this.id)
      // this.$navigateTo('/vr/preview_video?url=' + url)
    },
      // 转发
    handleSharp(){
      this.$refs.share_popup.show()
    },
    toOtherDetail(item) {
      if (item.parentid == 1) {
        this.$navigateTo('/commercial/sale/detail?id=' + item.id)
      }
      if (item.parentid == 2) {
        this.$navigateTo('/commercial/rent/detail?id=' + item.id)
      }
      if (item.parentid == 3) {
        this.$navigateTo('/commercial/transfer/detail?id=' + item.id)
      }
    },
    toPosition() {
      if (this.detail.lat > 0 && this.detail.lng > 0) {
        uni.openLocation({
          latitude: parseFloat(this.detail.lat),
          longitude: parseFloat(this.detail.lng),
          name: this.detail.cmname,
          address: this.detail.address,
          success: function () {
            console.log('success')
          },
        })
      }
    },
    viewMap(e) {
      if (this.detail.lat > 0 && this.detail.lng > 0) {
        this.$navigateTo(
          '/propertyData/map/map?id=' + this.detail.id + '&type=5&lat=' + this.detail.lat + '&lng=' + this.detail.lng
        )
      } else {
        uni.showToast({
          title: '未标记地图位置',
          icon: 'none',
        })
      }
    },
    toJubao() {
      this.$navigateTo('/user/inform/inform?id=' + this.id + '&type=1')
    },
    // #ifdef MP-BAIDU
    baiduShareImg() {
      swan.shareFile({
        filePath: this.cardImg,
        success: (res) => {
          // uni.showToast({
          // 	title:"分享成功"
          // })
        },
        fail: (err) => {
          uni.showToast({
            title: '分享失败',
            icon: 'none',
          })
        },
      })
    },
    // #endif
    // 将房源加入对比
    addContrast(info_id) {
      this.$ajax.get(
        'estate/addContrast',
        { info_id },
        (res) => {
          if (res.data.code === -1) {
            this.$store.state.user_login_status = 1
            // 检测是否已添加
            if (this.$store.state.temp_renting_contrast_ids.includes(info_id)) {
              uni.showToast({
                title: '该房源已经添加',
                icon: 'none',
              })
              return
            }
            this.$store.state.temp_renting_contrast_ids.push(info_id)
            return
          }
          if (res.data.code === 1) {
            uni.showToast({
              title: res.data.msg,
            })
            this.contrastCount++
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
          }
        },
        (err) => {},
        { disableAutoHandle: true }
      )
    },
    toContrast() {
      if (this.login_status > 1) {
        this.$navigateTo(`/contrast/commercial_list?type=2&cate_id=${this.detail.catid}`)
      } else {
        this.$navigateTo(`/contrast/commercial_list?type=2&no_login=1`)
      }
    },
    showSharePop(){
      this.getShortLink()
      this.$refs.share_popup.show()
    },
    goBack(){
      if (this.sid){
        uni.switchTab({
            url: '/pages/index/index'
        });
      }else {
        uni.navigateBack()
      }
    },
    doNot() {},
    checkHb(){
      this.$ajax.get('WxMoney/checkHb', { info_id: this.id, info_type: 3, hb: this.hb}, res => {
        if (res.data.code == -1) {
          uni.setStorageSync('backUrl', window.location.href)
          this.$navigateTo("/user/login/login")
        }
        if (res.data.help_fail_desc) {
          uni.showToast({
            title: res.data.help_fail_desc,
            icon: 'none'
          })
          return 
        }
        if (res.data.code == 1) {
          this.hb_info = res.data.hb_info
           this.is_help_link = res.data.is_help_link   // 是否是助力链接进来的 如果是 （值为1）弹出红包弹框
          this.$nextTick(()=> {
            if (this.is_help_link ==1){
              // this.timeDownStart()
              this.$refs.hongbao.showPopup()    //页面不主动弹出领取弹框改为入口打开显示弹框 或者通过助力链接打开时主动弹出
            }
            
          })
          // this.hb_share_query = res.data.hb_share_query
          this.map_key = res.data.txmapwapkey
          if (this.hb_info.is_open) {
            if (res.data.help_task && res.data.help_task.id) {
              this.task_id = res.data.help_task.id
            } else if (this.isLogin) {
              // this.createHb()
            }
            // if (this.hb_share_query) {
            //   this.share.link += `&${this.hb_share_query}`
            // }
            if (this.hb_info.limit_area) {
              this.getWxConfig(['getLocation','updateAppMessageShareData','updateTimelineShareData'], (wx)=>{
                this.wx = wx
              })
            } else {
              this.getWxConfig()
            }
            // if (this.hb_info.limit_area) {
            //   this.getCity()
            // }
          }
        }
      }, err => {console.log(err)}, {disableAutoHandle: true})
    },
    createHb(){
      let form = {
        info_id: this.id,
        info_type: 3,
        hb:this.hb
      }
      this.$ajax.post('WxMoney/createhb', form, res => {
        if (res.data.code == 1) {
          this.hb_result = res.data.hb_result
          this.$nextTick(()=> {
            if (this.is_help_link ==1){
              this.timeDownStart()
              this.$refs.hongbao.showPopup()    //页面不主动弹出领取弹框改为入口打开显示弹框 或者通过助力链接打开时主动弹出
            }
          })
        } else if (res.data.help_fail_desc){
          uni.showToast({
            title: res.data.help_fail_desc,
            icon: 'none'
          })
        }
      })
    },
    getCity(options={}) {
      this.$store.state.getPosition(this.wx, (res)=>{
        this.lat = res.lat
        this.lng = res.lng
        getLocation({
          latitude: res.lat,
          longitude: res.lng,
          map_key: this.map_key||'',
          success: cityRes=>{
            this.current_city = cityRes.city
            options.success && options.success(res)
          },
          fail: err=>{
            console.log(err)
            options.fail && options.fail(err)
          }
        })
      })
    },
    playAudio(){
        this.innerAudioContext = uni.createInnerAudioContext();
        // this.innerAudioContext.autoplay = true;
        this.innerAudioContext.loop = false;
        this.innerAudioContext.src = this.tuiguang_mp3;
        // this.innerAudioContext.pause()
        this.innerAudioContext.onPlay(() => {
            console.log('开始播放');
            this.playing = true
        });
        this.innerAudioContext.onEnded(() => {
            console.log('播放结束');
            this.playing = false
        });
        this.innerAudioContext.onError((res) => {
            this.playing = false
            console.log("播放失败")
            console.log(res.errMsg);
            console.log(res.errCode);
        });
        this.innerAudioContext.play()
    },
    openHb() {
      if (!this.playing) this.playAudio()
      if (this.hb_info.limit_area && !this.current_city) {
        uni.showLoading({
            title: '获取位置信息中，请稍等'
        });
        this.getCity({
          success: () => {
            uni.hideLoading()
            this.getHb()
          }, fail: (err) => {
            console.log(err)
            uni.hideLoading()
            this.getHb()
          }
        })
      } else {
        this.getHb()
      }
    },
    getHb() {
      let form = {
        info_id: this.id,
        info_type: 3,
        hb:this.hb,
        area: this.current_city,
      }
      this.$ajax.post('WxMoney/help', form, (res) => {
         uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        if (res.data.code == 1) {
          this.$refs.hongbao.hidenPopup()
          //  let  link = this.share.link.split("&hb=")[0]
          //       this.share.link = link +`&${res.data.hb_share_query}`
          //       this.getWxConfig()
          //       setTimeout(() => {
                    // this.$navigateTo(`/redEnvelopes/index?info_id=${this.id}&info_type=4&task_id=${this.task_id}`)
                // }, 500);

          
        } else {
          if ( res.data.state==1){
            this.$refs.hongbao.hidenPopup()
          }else if (res.data.state==2) {
            this.$refs.hongbao.hidenPopup()
            setTimeout(() => {
              showModal({
                content:res.data.msg +',您也可以参与领取红包',
                confirm: res => {
                  this.$navigateTo(`/redEnvelopes/index?info_id=${this.id}&info_type=3`)
                }
              })
            }, 1000);
            
          }
          // uni.showToast({
          //   title: res.data.msg,
          //   icon: 'none'
          // })
        }
      })
    },
    // 倒计时
    timeDownStart() {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        if (this.hb_result.expire_seconds > 0) {
          this.hb_result.expire_seconds--
        } else {
          clearInterval(this.timer)
        }
      }, 1000)
    },
    toHbHelp() {
      this.$navigateTo(`/redEnvelopes/index?info_id=${this.id}&info_type=3&task_id=${this.task_id}`)
    },
    // 判断登录
    loginState() {
      checkLogin({
        success: (res) => {
          this.isLogin = true
          this.checkHb()
        },
        fail: (res) => {
          this.isLogin = false
          this.checkHb()
        },
        complete: (res) => {
          this.$store.state.user_login_status = res.status
        },
      })
    },
  },
  onShareAppMessage(res) {
    let link = '',time=parseInt(+new Date()/1000)
    if (this.currentUserInfo.sid) {
      link =
        '/commercial/rent/detail?id=' +
        this.detail.id +
        '&isShare=1&shareType=' +
        this.currentUserInfo.shareType +
        '&shareId=' +
        this.currentUserInfo.sid+"&f_time="+time
    } else {
      link = '/commercial/rent/detail?id=' + this.detail.id
    }
    return {
      title: this.detail.title || '',
      content:
        this.detail.mianji +
        this.detail.mianji_unit +
        (this.detail.price == '面议' || this.detail.price == '0' ? '面议' : this.detail.price + this.detail.price_unit),
      imageUrl: this.detail.images[0] ? formatImg(this.detail.images[0], 'w_6401') : '',
      path: link,
    }
  },
  // #ifdef H5
  onNavigationBarButtonTap(option) {
    if (option.index == 0) {
      // this.handleCreat()
      this.getShortLink()
      this.$refs.share_popup.show()
    }
    if (option.index == 1) {
      this.handleCollect()
    }
  },
  // #endif
  // #ifdef APP-PLUS
  onNavigationBarButtonTap(option) {
    if (option.index == 0) {
      this.showPopup = true
      // this.$refs.popup.show()
      this.$refs.share_popup.show()
    }
    if (option.index == 1) {
      this.handleCollect()
    }
  },
  onBackPress() {
    if (this.showPopup) {
      this.showPopup = false
      // this.$refs.popup.hide()
      this.$refs.share_popup.hide()
      return true
    }
  },
  // #endif
}
</script>

<style scoped lang="scss">
// @import url("../../static/css/commercial_peitao.scss");
.ershou_detail_content {
  padding-bottom: 160rpx;
  color: #333;
  background-color: #fff;
}
.wnsb-box {
  width: 20%;
  margin-bottom: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.peitao {
  width: 60rpx;
  height: 60rpx;
  background-size: 100%;
}
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.back{
  position: fixed;
  width: 100%;
  height: 88rpx;
  padding: 2px 10rpx;
  align-items: center;
  justify-content: space-between;
  z-index: 1;
  .title-con{
    flex: 1;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 32rpx;
    // width: calc(100% - 64px);
    // over
  }
  .icon-box{
    // height: 44px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    padding: 8px;
    background: rgba(0, 0, 0, 0.6);
    justify-content: center;
    align-items: center;
    &.icon-share{
      justify-self: end;
      margin-left: auto;
    }
  }
}

// 顶部焦点图
.focus-box {
  position: relative;
  swiper.banner {
    height: 75vw;
  }
  .swiper-item {
    height: 100%;
  }
  .swiper-item image {
    width: 100%;
    height: 100%;
  }
  .swiper-item image.video-icon {
    width: 16vw;
    height: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0);
  }
  .number {
    position: absolute;
    padding: 4rpx 20rpx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    border-top-right-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    left: 0;
    bottom: 20rpx;
    color: #fff;
  }
  .img-total {
    position: absolute;
    padding: 4rpx 20rpx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    border-radius: 20rpx;
    right: 20rpx;
    bottom: 20rpx;
    color: #fff;
  }
  .cate-box {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 24rpx;
    display: block;
    text-align: center;
    font-size: 0;
    .cate-list {
      display: inline-block;
      border-radius: 6rpx;
      overflow: hidden;
    }
  }
  .cate {
    display: inline-block;
    padding: 8upx 20upx;
    font-size: 22rpx;
    background-color: #fff;
    &.active {
      background: linear-gradient(45deg, #fd9ea3, #fb656a);
      color: #fff;
    }
  }
}

.container {
  padding: 0 48rpx;
}

.block {
  margin-top: 24rpx;
  > .label {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    line-height: 1;
    padding: 24rpx 0;
    margin-bottom: 24rpx;
    font-size: 40rpx;
    font-weight: bold;
    &.mgb0 {
      margin-bottom: 0;
    }
    .more {
      padding: 8rpx;
      font-size: 22rpx;
      font-weight: initial;
      color: #999;
      &.pd-r-48 {
        padding-right: 48rpx;
      }
    }
  }
}

// 房源标题
.house_title {
  .attr1 {
    padding: 2rpx 10rpx;
    margin-right: 10rpx;
    line-height: 1;
    font-size: 22rpx;
    background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
    color: #fff;
  }
  .attr2 {
    padding: 2rpx 10rpx;
    margin-right: 10rpx;
    line-height: 1;
    font-size: 22rpx;
    background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
    color: #fff;
  }
  font-size: 40rpx;
  line-height: 1.5;
  margin: 20rpx 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 主要信息
.main_info-box {
  align-items: center;
  justify-content: space-between;
  line-height: 1;
  .infomation {
    align-items: flex-end;
    line-height: 1;
    .value {
      font-size: 36rpx;
      font-weight: bold;
      color: $uni-color-primary;
    }
    .unit {
      font-size: 26rpx;
      margin-bottom: 2rpx;
      color: $uni-color-primary;
    }
  }
  .price {
    align-items: flex-end;
    margin-right: 24rpx;
  }
  .stw {
    align-items: flex-end;
    margin-right: 24rpx;
  }
  .mianji {
    align-items: flex-end;
    font-size: 22rpx;
    color: $uni-color-primary;
  }
  .btn {
    line-height: 64rpx;
    padding: 0 30rpx;
    color: #fff;
    background: $uni-color-primary;
    box-shadow: 0 2px 8px 0 rgba($uni-color-primary, 0.4);
    border-radius: 32rpx;
  }
}

// 房源标签
.label-list {
  margin-top: 24rpx;
  .attr1 {
    padding: 4rpx 10rpx;
    margin-right: 10rpx;
    line-height: 1;
    font-size: 22rpx;
    background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
    color: #fff;
  }
  .attr2 {
    padding: 6rpx 10rpx;
    margin-right: 10rpx;
    line-height: 1;
    font-size: 22rpx;
    background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
    color: #fff;
  }
  .label {
    line-height: 1;
    padding: 4rpx 10rpx;
    font-size: 22rpx;
    color: #999;
    border: 0.5rpx solid #d8d8d8;
    // background: #f2f2f2;
    margin-left: 8rpx;
  }
}

// 其他信息
.info-list {
  flex-wrap: wrap;
  position: relative;
  .xiajia_icon {
    position: absolute;
    width: 180rpx;
    height: 180rpx;
    left: 0;
    right: 0;
    top: -32rpx;
    margin: auto;
  }
  .info-list-item {
    line-height: 1;
    margin-bottom: 48rpx;
    // padding: 10rpx 0;
    min-width: 30%;
    flex: 1;
    &.mgb0 {
      margin-bottom: 0;
    }
    .label {
      font-size: 24rpx;
      margin-bottom: 24rpx;
      color: #999;
    }
    .data {
      font-size: 32rpx;
    }
  }
}

// 房源介绍
.house_desc {
  margin-top: 24rpx;
  .content_info {
    line-height: 1.8;
    // min-height: 160rpx;
    .desc {
      margin-bottom: 36rpx;
      color: #666;
    }
    .cate {
      margin-bottom: 20rpx;
      line-height: 1;
      padding-left: 10rpx;
      border-left: 6rpx solid $uni-color-primary;
      font-weight: bold;
      color: #333;
    }
  }
  // 经纪人信息
  .agent-box {
    padding: 20rpx;
    justify-content: space-between;
    align-items: center;
    background-color: #f2f2f2;
    .header_img {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    .agent_info {
      flex: 1;
      overflow: hidden;
      margin-right: 20rpx;
      .name {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 32rpx;
        margin-bottom: 10rpx;
      }
      .shop_name {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 22rpx;
        color: #999;
      }
    }
    .into-btn {
      height: 60rpx;
      line-height: 60rpx;
      padding: 0 24rpx;
      border-radius: 30rpx;
      font-size: 24rpx;
      color: #fff;
      background-color: $uni-color-primary;
    }
  }

  .selling_point {
    margin-top: 48rpx;
    .label {
      margin-bottom: 10rpx;
      color: #999;
    }
    .content_info {
      line-height: 1.5;
    }
    .view_more {
      margin: 10rpx 48rpx;
      padding: 20rpx;
      text-align: center;
      font-size: 30rpx;
      background-color: #fff4f4;
      color: $uni-color-primary;
    }
  }
}

// 房源标签
.wnsb-list {
  margin-bottom: 24rpx;
  flex-wrap: wrap;
  .label {
    line-height: 1;
    padding: 4rpx 10rpx;
    font-size: 22rpx;
    color: #999;
    border: 0.5rpx solid #d8d8d8;
    // background: #f2f2f2;
    margin-left: 8rpx;
    margin-bottom: 8rpx;
  }
}

// 轮播广告图
.ext_swiper {
  margin-top: 48rpx;
  height: 140rpx;
  swiper-item {
    height: 100%;
    background-color: #f5f5f5;
    border-radius: 16rpx;
    overflow: hidden;
    position: relative;
    > image {
      height: 100%;
      width: 100%;
    }
    .marker {
      line-height: 1;
      padding: 4rpx 10rpx;
      position: absolute;
      right: 12rpx;
      bottom: 10rpx;
      font-size: 20rpx;
      border-radius: 4rpx;
      background-color: rgba($color: #000000, $alpha: 0.5);
      color: #fff;
    }
  }
}

// 小区
.community-box {
  justify-content: space-between;
  > .label {
    justify-content: space-between;
    .into {
      font-weight: initial;
      color: #999;
    }
  }
}
.community {
  margin-bottom: 24rpx;
  .img {
    width: 140rpx;
    height: 140rpx;
    margin-right: 20rpx;
    border-radius: 8rpx;
  }
  > .info {
    flex: 1;
    justify-content: space-between;
    line-height: 1;
    overflow: hidden;
    .price_row {
      justify-content: space-between;
      .into {
        line-height: 1;
        font-size: 22rpx;
        align-items: center;
        color: #999;
      }
    }
    .label {
      display: inline-block;
      text-align-last: justify;
      min-width: 120rpx;
      margin-right: 15rpx;
      font-size: 28rpx;
      font-weight: initial;
      color: #999;
    }
    .value {
      flex: 1;
      font-size: 28rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .price {
      line-height: 1;
      align-items: center;
      font-size: 28rpx;
      font-weight: bold;
      color: $uni-color-primary;
      .unit {
        margin-left: 8rpx;
        font-size: 22rpx;
        font-weight: initial;
        color: #333;
      }
    }
    .increase {
      align-items: center;
      .increase-text {
        align-items: center;
        flex-direction: row;
      }
      .ping {
        font-size: 28rpx;
      }
      .value {
        font-size: 28rpx;
        font-weight: bold;
        color: $uni-color-primary;
        &.down {
          color: #179b16;
        }
      }
      .unit {
        position: relative;
        // top: 5rpx;
        left: 5rpx;
        color: $uni-color-primary;
        &.down {
          color: #179b16;
        }
      }
    }
    .yaers {
      align-items: center;
    }
  }
}

// TA的其他房源

.other_house-list {
  height: 50vw;
  .swiper-item {
    margin-right: 24rpx;
    .img-box {
      border-radius: 8rpx;
      overflow: hidden;
      height: 200rpx;
    }
    image {
      width: 100%;
      height: 100%;
    }
    .house_type {
      display: block;
      margin-top: 10rpx;
      font-size: 32rpx;
      font-weight: bold;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .aligin-end {
      align-items: flex-end;
    }
    .stw {
      align-items: center;
      margin-top: 6rpx;
      margin-right: 10rpx;
      font-size: 24rpx;
      color: #999;
    }
    .price-box {
      line-height: 1;
      align-items: flex-center;
      justify-content: space-between;
      margin-top: 10rpx;
      .db_btn {
        line-height: 30rpx;
        padding: 0 8rpx;
        border-radius: 15rpx;
        font-size: 22rpx;
        border: 1rpx solid $uni-color-primary;
        color: $uni-color-primary;
      }
    }
    .mianji {
      font-size: 22rpx;
      color: #999;
    }
    .price {
      font-size: 32rpx;
      font-weight: bold;
      color: $uni-color-primary;
    }
    .unit {
      margin-left: 8rpx;
    }
  }
}

.more_btn {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin: 0 48rpx;
  padding: 0 24rpx;
  height: 80rpx;
  border-radius: 8rpx;
  color: $uni-color-primary;
  background-color: rgba($uni-color-primary, 0.1);
  &.no_bg {
    background-color: rgba($uni-color-primary, 0);
  }
  &.has_agent {
    background-color: rgba($uni-color-primary, 0);
    color: #333;
  }
}

// 底部操作菜单
.bottom-bar {
  background-color: #fff;
  height: 110rpx;
  padding: 15rpx 48rpx;
  left: 0;
  z-index: 10;
  .bar-left {
    padding-right: 28rpx;
    justify-content: space-between;
  }
  .icon-btn {
    // width: 100rpx;
    align-items: center;
    padding: 0;
    margin: 0;
    background-color: #fff;
    line-height: initial;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // flex: 1;
    max-width: 130rpx;
    padding-right: 32rpx;
    overflow: hidden;
    position: relative;
    &.last {
      padding-right: 48rpx;
    }
    // & ~ .icon-btn {
    //   margin-left: 24rpx;
    // }
    .header_img {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
    }
    text {
      line-height: 1;
      font-size: 22rpx;
      color: #999;
      display: inline-block;
      width: 100%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .badge {
      display: inline-block;
      box-sizing: border-box;
      width: auto;
      position: absolute;
      top: 0;
      left: 32rpx;
      // right: 38rpx;
      height: 28rpx;
      padding: 0 8rpx;
      min-width: 28rpx;
      border-radius: 14rpx;
      font-size: 22rpx;
      background-color: $uni-color-primary;
      color: #fff;
    }
  }
  .bar-btn {
    // width: 220rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    padding: 0;
    margin: 0;
    border-radius: 0;
    color: #fff;
    &.alone {
      border-radius: 40rpx;
    }
    &.btn1 {
      background: #fbac65;
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.05);
      border-top-left-radius: 40rpx;
      border-bottom-left-radius: 40rpx;
    }
    &.btn2 {
      background: linear-gradient(90deg, #fb656a 0%, #fbac65 100%);
      box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.3);
      border-top-right-radius: 40rpx;
      border-bottom-right-radius: 40rpx;
    }
  }
}

.card-img {
  width: 80%;
  margin: 0 10%;
  padding: 40upx 0;
}

// 生成海报后的弹窗
.share-box {
  padding: 20upx 0;
  background-color: #fff;

  .tip {
    padding: 10px;
    width: 100%;
    font-weight: 700;
    box-sizing: border-box;
    text-align: center;
  }

  button {
    line-height: initial;
    padding: 10upx 20upx;
    background-color: #fff;
  }

  .wechat-img {
    width: 60vw;
    height: 60vw;
  }

  .item {
    text-align: center;
    padding: 10upx 20upx;
    line-height: inherit;
  }
}

/* #ifdef H5 */
// H5海报
#card {
  padding-bottom: 15px;
  width: 100%;
  position: fixed;
  left: -110vw;
  .card_img-box {
    width: 100%;
    height: 68vw;
    overflow: hidden;
  }

  .card_img-box image {
    width: 100%;
    height: 100%;
  }

  .card_info-box {
    margin: 40upx;
    padding: 20upx 30upx;
    font-size: 30upx;
    color: #555;
    background-color: #f3f3f3;
  }

  .text-right {
    text-align: right;
  }

  .card_info-box .title {
    font-size: 40upx;
    height: 100upx;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 40upx;
    color: #000;
    // -webkit-line-clamp: 2;
    // display: -webkit-box;
  }

  .card_info-box .price {
    font-weight: bold;
    color: #f65354;
  }

  .card-footer {
    margin: 40upx;
    font-size: 34px;
    line-height: 50upx;
    color: #333;

    .text {
      padding: 20upx;
    }

    .tip {
      font-size: 26upx;
      color: #666;
    }

    .qrcode {
      width: 30vw;
      height: 30vw;
    }
  }
}
/* #endif */

canvas.hide {
  position: fixed;
  left: -100vw;
}

// 免责声明
.shengming {
  margin-top: 32rpx;
  color: #999;
  .shengming_title {
    font-size: 30rpx;
    margin-bottom: 16rpx;
    justify-content: space-between;
    align-items: center;
    .label {
      line-height: 1;
      padding: 4rpx 8rpx;
      font-size: 22rpx;
      border: 1rpx solid #d8d8d8;
    }
  }
  .shengming_content {
    font-size: 26rpx;
    line-height: 1.8;
  }
}

// 复制文案
.copy-text-box {
  padding: 24rpx 32rpx;
  background-color: #fff;
  width: 600rpx;
  // height: 100%;
  margin-left: 75rpx;
  border-radius: 16rpx;
  .title {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }
  .info-row {
    line-height: 1.6;
    color: #333;
    .label {
      color: #999;
    }
    .value {
      flex: 1;
      &.highlight {
        color: $uni-color-primary;
      }
    }
  }
  .button {
    align-items: center;
    justify-content: center;
    line-height: 64rpx;
    width: 236rpx;
    text-align: center;
    border-radius: 32rpx;
    margin: 32rpx auto;
    background: #fb656a;
    box-shadow: 0 2px 8px 0 rgba(251, 101, 106, 0.4);
    color: #fff;
  }
  .disabled-btn {
    background-color: #f5f5f5;
    box-shadow: none;
    color: #999;
    > .text {
      margin-left: 12rpx;
    }
  }
}

// 报名按钮
.btn_list-box {
  margin-top: 32rpx;
  .btn-item {
    padding: 20rpx 5rpx;
    flex: 1;
    align-items: center;
    justify-content: center;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.05);
    color: $uni-color-primary;
    ~ .btn-item {
      margin-left: 14rpx;
    }
    text {
      font-size: 32rpx;
      margin-left: 16rpx;
    }
    .img {
      width: 40rpx;
      height: 40rpx;
      overflow: hidden;
      .img_c {
        width: 100%;
        height: 100%;
      }
    }
  }
}

// 房源动态
.house_news {
  .house_news_info {
    padding: 20rpx 0;
    margin-left: 20rpx;
    margin-right: 20rpx;
    &_item {
      flex: 1;
      align-items: center;
      &_num {
        margin-bottom: 20rpx;
      }
    }
  }
}

.time_line {
  padding: 20upx 30upx;
  margin-top: 40rpx;
  .item {
    position: relative;
    padding: 0 20upx 36upx 32upx;
    border-left: 2rpx solid #f3f3f3;
    &:last-child {
      border-left: 0;
    }
    .line-item {
      margin-top: -20rpx;
      .content_c {
        justify-content: space-between;
        align-items: center;
        .content_con{
          flex: 1;
          flex-wrap: wrap;
        }
        .content_sub {
          color: #fb656a;
        }
        .blod {
          color: #ff656b;
          font-weight: 600;
        }
      }
      .line-header {
        margin-top: 15rpx;
      }
    }
    .title {
      font-size: 28upx;
      margin-top: -10rpx;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
    }
    .time {
      font-size: 24upx;
      font-weight: bold;
      color: #999;
    }
  }
  .item::after {
    content: '';
    height: 12upx;
    width: 12upx;
    box-sizing: border-box;
    border-radius: 50%;
    position: absolute;
    // border: 4rpx solid #f3f3f3;
    background-color: #f3f3f3;
    left: -6rpx;
    top: -6rpx;
  }
  .item.current::before {
    content: '';
    height: 20upx;
    width: 20upx;
    border-radius: 50%;
    background-color: #3399ff;
    position: absolute;
    left: -12upx;
    top: 0;
    z-index: 2;
  }
}
.entrant_button {
  padding: 20rpx 5rpx;
  width: 80%;
  margin: 20rpx auto 0;
  background-color: rgba(255, 101, 107, 0.05);
  color: #ff656b;
  text-align: center;
  font-size: 30rpx;
}
.hongbao {
  position: absolute;
  right: 20rpx;
  width:120rpx;
  height:120rpx;
  white-space: nowrap;
  overflow: hidden;
  bottom: 72rpx;
  .hb_content{
    width:120rpx;
    height:120rpx;
    display: block;
    position: absolute;
    left:120rpx;
    top:0;
  }
  image {
    position:absolute;
    left: 0;
    top:0;
    width: 120rpx;
    height: 120rpx;
  }
}
</style>