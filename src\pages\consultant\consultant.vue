<template>
  <view :class="{padb:user_is_adviser==0&&is_show_apply_agent==1}" >
    <view class="header-box" :style="{ backgroundColor: theme_color }">
      <image v-if="theme_img" @click="toActivity" class="bg_img" mode="widthFix" :src="theme_img | imageFilter('w_8601')"></image>
      <!-- 搜索栏 -->
      <view class="search-box flex-row">
        <my-icon type="ic_sousuo" color="#fff" size="40rpx"></my-icon>
        <input
          class="inp"
          type="search"
          v-model="params.searchtxt"
          @confirm="handleSearch"
          placeholder="可按楼盘或顾问名称搜索"
          placeholder-style="font-size:26rpx;color:#fff"
        />
      </view>
      <!-- 前三名 -->
      <view class="adviser-card flex-row">
        <view class="adviser-item" @click="consuDetail(top_three[1].id)">
          <view class="header-img">
            <view class="img-box">
              <image mode="widthFix" class="avatar" :src="top_three[1].prelogo | imageFilter('w_120')"></image>
            </view>
            <image
              mode="widthFix"
              class="brand"
              src="https://images.tengfangyun.com/images/new_icon/No2.png"
            ></image>
          </view>
          <text class="cname">{{ top_three[1].cname }}</text>
          <text class="activity" v-if="params.type === 2">咨询量：{{ top_three[1].traffic_volume }}</text>
          <text class="activity" v-else>{{ top_three[1].build_names }}</text>
        </view>
        <view class="adviser-item first" @click="consuDetail(top_three[0].id)">
          <view class="header-img">
            <view class="img-box">
              <image mode="widthFix" class="avatar" :src="top_three[0].prelogo | imageFilter('w_120')"></image>
            </view>
            <image
              mode="widthFix"
              class="brand"
              src="https://images.tengfangyun.com/images/new_icon/No1.png"
            ></image>
          </view>
          <text class="cname">{{ top_three[0].cname }}</text>
          <text class="activity" v-if="params.type === 2">咨询量：{{ top_three[0].traffic_volume }}</text>
          <text class="activity" v-else>{{ top_three[0].build_names }}</text>
        </view>
        <view class="adviser-item" @click="consuDetail(top_three[2].id)">
          <view class="header-img">
            <view class="img-box">
              <image mode="widthFix" class="avatar" :src="top_three[2].prelogo | imageFilter('w_120')"></image>
            </view>
            <image
              mode="widthFix"
              class="brand"
              src="https://images.tengfangyun.com/images/new_icon/No3.png"
            ></image>
          </view>
          <text class="cname">{{ top_three[2].cname }}</text>
          <text class="activity" v-if="params.type === 2">咨询量：{{ top_three[2].traffic_volume }}</text>
          <text class="activity" v-else>{{ top_three[2].build_names }}</text>
        </view>
      </view>
    </view>
    <!-- 列表的分类 -->
    <view class="cate-box">
      <view class="cate-list flex-row">
        <view
          class="cate-item"
          :class="{ active: params.type === cate.type }"
          :style="{ backgroundColor: params.type === cate.type ? theme_color : '' }"
          v-for="cate in cate_list"
          :key="cate.type"
          @click="switchCate(cate.type)"
          >{{ cate.name }}</view
        >
      </view>
    </view>
    <!-- 置业顾问列表 -->
    <view class="advier-list">
      <view
        class="adviser-item flex-row"
        v-for="(item, index) in adviser_list"
        :key="item.id"
        @click="consuDetail(item.id)"
      >
        <view class="adviser_index">{{ index | indexFormat }}</view>
        <view class="header_img">
          <image mode="widthFix" :src="item.prelogo | imageFilter('w_120')"></image>
        </view>
        <view class="info">
          <view class="name flex-row">
            <text class="text">{{ item.cname || item.typename }}</text>
          </view>
          <view class="data flex-row">
            <text class="mgr-20" v-if="params.type===2">咨询量 {{ item.traffic_volume }}</text>
            <text>浏览量 {{ item.browse }}</text>
          </view>
          <view class="data">
            <text>{{ item.build_names }}</text>
          </view>
        </view>
        <view class="adviser-right">
          <view class="btn-list flex-row">
            <view class="btn">
              <chat-btn
                :user_login_status="login_status"
                :user_id="item.mid"
                :identity_id="item.id"
                :from_type="4"
                @ok="advAsk"
              >
                <view class="icon-box" :style="{ background: filterColor(theme_color, 0.12) }">
                  <my-icon type="ic_zixun1" size="45rpx" :color="theme_color || '#ff656c'"></my-icon>
                </view>
                <!-- <image class="icon" src="https://images.tengfangyun.com/images/icon/ic_zixun.png"></image> -->
              </chat-btn>
            </view>
            <view class="btn" v-if ="switch_adviser_tel">
              <tel-btn :user_id="item.mid" :identity_id="item.id" :tel="item.tel" @ok="handleTel">
                <view class="icon-box" :style="{ background: filterColor(theme_color, 0.12) }">
                  <my-icon type="ic_dianhua1" size="45rpx" :color="theme_color || '#ff656c'"></my-icon>
                </view>
                <!-- <image class="icon" src="https://images.tengfangyun.com/images/icon/ic_dianhua.png"></image> -->
              </tel-btn>
            </view>
          </view>
          <view class="zan flex-row" @click.prevent.stop="handleZan(item.id, index)">
            <my-icon
              type="ic_zan"
              :color="item.isstar ? theme_color : '#d8d8d8'"
              size="28rpx"
            ></my-icon>
            <text class="zan_num" :style="{ color: item.isstar ? theme_color : '#d8d8d8' }">{{
              item.star
            }}</text>
          </view>
        </view>
      </view>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
    <view class='join'  v-if="user_is_adviser === 0 &&is_show_apply_agent == 1">
      <view
        class="join-btn"
        :style="{
          background: theme_color,
          boxShadow: '0 8rpx 32rpx 0 ' + filterColor(theme_color, 0.4) + ''
        }"
       
        @click="joinAdviser"
        >立即入驻</view
      >
    </view>
    
    <!-- #ifndef MP-WEIXIN -->
    <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip"></login-popup>
    <!-- #endif -->
    <chat-tip></chat-tip>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import myIcon from '../../components/myIcon'
import { uniLoadMore } from '@dcloudio/uni-ui'
// #ifndef MP-WEIXIN
import loginPopup from '../../components/loginPopup'
// #endif
import { formatImg, showModal } from '../../common/index.js'
import chatBtn from '../../components/open-button/chatBtn'
import telBtn from '../../components/open-button/telBtn'
import allTel from '../../common/all_tel.js'
import getChatInfo from '../../common/get_chat_info.js'
import {wxShare} from '../../common/mixin'
const top_three = [
  {
    id: null,
    cname: '',
    prelogo: '',
    active: ''
  },
  {
    id: null,
    cname: '',
    prelogo: '',
    active: ''
  },
  {
    id: null,
    cname: '',
    prelogo: '',
    active: ''
  }
]
export default {
  components: {
    myIcon,
    uniLoadMore,
    // #ifndef MP-WEIXIN
    loginPopup,
    // #endif
    chatBtn,
    telBtn
  },
  mixins: [wxShare],
  data() {
    return {
      themes: [
        {
          img: 'https://images.tengfangyun.com/images/new_icon/adviser_list_bg1.png',
          color: 'rgba(251,101,106,1)'
        },
        {
          img: 'https://images.tengfangyun.com/images/new_icon/adviser_list_bg2.png',
          color: 'rgba(0,202,167,1)'
        },
        {
          img: 'https://images.tengfangyun.com/images/new_icon/adviser_list_bg3.png',
          color: 'rgba(76,199,246,1)'
        }
      ],
      cate_list: [
        {
          name: '优选置业顾问',
          type: 1
        },
        {
          name: '咨询榜',
          type: 2
        },
        {
          name: '新人榜',
          type: 3
        }
      ],
      params: {
        page: 0,
        rows: 10,
        type: 1,
        searchtxt: ''
      },
      get_status: '',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      },
      top_three: top_three, //前三名置业顾问
      adviser_list: [], //置业顾问列表
      login_tip: '',
      user_is_adviser: null,
      tel_res: {},
      show_tel_pop: false,
      is_show_apply_agent:1
    }
  },
  computed: {
    adviser_list_style() {
      if (this.$store.state.styles && this.$store.state.styles.toptitle) {
        uni.setNavigationBarTitle({
          title: this.$store.state.styles.toptitle
        })
      } else {
        uni.setNavigationBarTitle({
          title: '置业顾问排行榜'
        })
      }
      return this.$store.state.styles
    },
    theme_color(){
      // if(this.$store.state.styles&&this.$store.state.styles.topcolor){
      //   return this.$store.state.styles.topcolor
      // }else{
        return this.themes[this.params.type-1].color
      // }
    },
    theme_img(){
      // if(this.$store.state.styles&&this.$store.state.styles.topimage){
      //   return this.$store.state.styles.topimage
      // }else{
        return this.themes[this.params.type-1].img
      // }
    },
    is_open_im() {
      return this.$store.state.im.ischat
    },
    is_open_middle_num() {
      return this.$store.state.im.istelcall
    },
    login_status() {
      return this.$store.state.user_login_status
    },
    switch_adviser_tel(){
      return this.$store.state.switch_adviser_tel
    }
  },
  onLoad() {
    uni.showLoading({
      title: '加载中...',
      mask: true
    })
    this.getData()
  },
  filters: {
    indexFormat(index) {
      return index + 4 < 10 ? '0' + (index + 4) : index + 4
    },
    formatActive(data, cate_type) {
      if (!data) {
        return 0
      }
      if (cate_type === 1 && data.rank && parseInt(data.rank)) {
        return parseInt(data.active) + parseInt(data.rank)
      }
      return data.active
    }
  },
  methods: {
    filterColor(color, opacity) {
      if (!color) {
        return 'rgba(255,101,107,' + opacity + ')'
      }
      return color.replace(/1\)/, opacity + ')')
    },
    // 获取置业顾问列表数据
    getData() {
      this.get_status = 'loading'
      if (this.params.page === 0) {
        this.top_three = top_three
        this.adviser_list = []
      }
      this.$ajax.get('adviser/index.html', this.params, res => {
        uni.hideLoading()
        this.$store.state.user_login_status = res.data.status
        if (res.data.share) {
          let toptitle = "", topimage = ""
          if(this.$store.state.styles){
            // console.log("store:",this.$store.state.styles)
            toptitle = this.$store.state.styles.toptitle
            topimage = this.$store.state.styles.topimage
          }
          this.share = {
            title:res.data.share.title||toptitle||'',
            pic:res.data.share.pic||topimage||'',
            content:res.data.share.content||''
          }
          this.getWxConfig()
        }
        this.user_is_adviser = res.data.reg
        if (res.data.code === 1) {
          this.is_show_apply_agent = res.data.customSetting?res.data.customSetting.is_show_apply_adviser:1          
          if (res.data.data.length < this.params.rows) {
            this.get_status = 'noMore'
          } else {
            this.get_status = 'more'
          }

          if (this.params.page === 0 && res.data.data.length > 0) {
            this.top_three = res.data.data.splice(0, 3)
            if(this.top_three.length===0) this.top_three = top_three
            if(this.top_three.length===1) this.top_three.push(top_three[1])&&this.top_three.push(top_three[2])
            if(this.top_three.length===2) this.top_three.push(top_three[2])
          }
          this.adviser_list = this.adviser_list.concat(res.data.data)
        } else {
          this.get_status = 'noMore'
          this.params.page >= 1 && this.params.page-- //默认第一页为 0
        }
        uni.stopPullDownRefresh()
      })
    },
    handleSearch(){
      this.params.page = 0
      this.getData()
    },
    // 和置业顾问发起聊天
    advAsk(e) {
      if (this.is_open_im == 0) {
        this.$navigateTo('/pages/consultant/detail?id=' + id)
        return
      }
      // #ifdef MP-WEIXIN
      getChatInfo(e.user_id, 4)
      //  #endif
      // #ifndef MP-WEIXIN
      this.$ajax.get('member/checkUserStatus',{},res=>{
        if(res.data.code === 1){
          getChatInfo(e.user_id, 4)
        }else if(res.data.status == 1){
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
        }else if(res.data.status == 2){
          this.$store.state.user_login_status = res.data.status
          this.login_tip = '为方便您及时接收消息通知，请输入手机号码'
          this.$refs.login_popup.showPopup()
        }
      })
      //  #endif
    },
    // 切换置业顾问排名分类
    switchCate(cate_type) {
      this.params.type = cate_type
      this.params.page = 0
      this.getData()
    },
    // 点赞
    handleZan(id, index) {
      this.$ajax.get(
        'adviser/adv_addstar',
        {
          id: id
        },
        res => {
          if (res.data.code == 1) {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            if (this.adviser_list[index].isstar != 0) {
              this.adviser_list[index].isstar = 0
              this.adviser_list[index].star = res.data.NowStar
            } else {
              this.adviser_list[index].isstar = 9999
              this.adviser_list[index].star = res.data.NowStar
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
          }
        }
      )
    },
    // 拨打置业顾问电话
    handleTel(e) {
      if (this.is_open_middle_num == 0) {
        uni.makePhoneCall({
          phoneNumber: e.tel
        })
        this.$ajax.get(
          'im/callUpStatistics',
          {
            id: e.user_id,
            tel: parseInt(e.tel),
            type: 3
          },
          res => {}
        )
        return
      }
      this.tel_params = {
        type: 2,
        callee_id:e.identity_id,
        scene_type:2,
        scene_id:e.identity_id,
        source: 'adviser_list',
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      // #ifdef MP-WEIXIN
      allTel(this.tel_params)
      // #endif
      // #ifndef MP-WEIXIN
      this.tel_params.intercept_login = true
      this.tel_params.fail = (res)=>{
        if(res.data.code === -1){
          this.$store.state.user_login_status = 1
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
        }
        if(res.data.code === 2){
          this.$store.state.user_login_status = 2
          this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
          this.$refs.login_popup.showPopup()
        }
      }
      allTel(this.tel_params)
      // #endif
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    // 关闭登录弹窗时
    handleCloseLogin() {
      if (this.$store.state.user_login_status === 1) {
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if (this.$store.state.user_login_status === 2) {
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    consuDetail(id) {
      if (!id) return
      this.$navigateTo('/pages/consultant/detail?id=' + id)
    },
    //申请入驻
    joinAdviser() {
      this.$store.state.allowOpen = true
      // 判断是否允许置业顾问入驻
      if (this.$store.state.home_switchs.allow_advsier_register) {
        this.getUserData()
      } else {
        uni.showToast({
          title: '暂未开放 敬请期待',
          icon: 'none'
        })
      }
    },
    getUserData() {
      // 获取会员信息
      this.$ajax.get(
        'Adviser/adv_reg',
        {},
        res => {
          if (res.data.code == 1) {
            const { cname, tel, wxqunkefu } = res.data.data
            this.$navigateTo('/user/consultant/add?cname=' + cname + '&tel=' + tel + '&wxkf=' + wxqunkefu)
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
          }
        },
        err => {
          console.log(err)
        },
        false
      )
    },
    //跳转到活动页面
    toActivity() {
      if (this.adviser_list_style && this.adviser_list_style.tourl_app) {
        //#ifdef MP
        this.$navigateTo(this.adviser_list_style.tourl_app)
        //#endif
      } else if (this.adviser_list_style && this.adviser_list_style.tourl) {
        //#ifdef H5
        this.$navigateTo(this.adviser_list_style.tourl)
        // location.href = obj.adviser_list_style.tourl
        //#endif
      }
    }
  },
  // 触底加载
  onReachBottom() {
    this.params.page++
    this.getData()
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.params.page = 0
    this.params.searchtxt = ''
    this.getData()
  },
  onShareAppMessage() {
    if (this.adviser_list_style && this.adviser_list_style.toptitle) {
      return {
        title: this.adviser_list_style.toptitle,
        content: this.share.content || '',
        imageUrl: this.share.pic ? formatImg(this.share.pic, 'w_6401') : ''
      }
    } else if (this.share) {
      return {
        title: this.share.title || '',
        content: this.share.content || '',
        imageUrl: this.share.pic ? formatImg(this.share.pic, 'w_6401') : ''
      }
    }
  },
  // #ifdef APP_PLUS
  onBackPress() {
    // 收起软键盘
    plus.key.hideSoftKeybord()
  }
  // #endif
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.padb{
  padding-bottom: 130rpx;
}
.flex-row {
  flex-direction: row;
}
// 排行榜头部
.header-box {
  width: 100%;
  min-height: 560rpx;
  // background-size: 100%;
  // background-repeat: no-repeat;
  position: relative;

  .bg_img {
    width: 100%;
  }
  // 搜索栏
  .search-box {
    align-items: center;
    position: absolute;
    left: 48rpx;
    right: 48rpx;
    // #ifdef H5
    // margin-top: 44px;
    // #endif
    top: 12rpx;
    padding: 6rpx 20rpx;
    border-radius: 8rpx;
    background-color: rgba($color: #ffffff, $alpha: 0.6);
    .inp {
      margin-left: 20rpx;
    }
  }

  // 前三名置业顾问
  .adviser-card {
    position: absolute;
    bottom: 0;
    left: 48rpx;
    right: 48rpx;
    justify-content: space-between;
    align-items: flex-end;
    .adviser-item {
      width: 200rpx;
      height: 272rpx;
      padding: 24rpx 6rpx 30rpx 6rpx;
      background-color: #fff;
      border-top-left-radius: 24rpx;
      border-top-right-radius: 24rpx;
      align-items: center;
      overflow: hidden;
      &.first {
        height: 328rpx;
        width: 254rpx;
        margin: 0 2rpx;
        padding-top: 45rpx;
        background-image: url(https://images.tengfangyun.com/images/new_icon/bg_touxiang%403x.png);
        background-size: 196rpx;
        background-repeat: no-repeat;
        background-position: 30rpx 34rpx;
        .header-img {
          position: relative;
          width: 128rpx;
          height: 128rpx;
          margin-bottom: 45rpx;
          .brand {
            left: -36rpx;
          }
        }
        .img-box {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          background-color: #f3f3f3;
          overflow: hidden;
          .avatar {
            width: 100%;
          }
        }
      }
      .header-img {
        position: relative;
        width: 96rpx;
        height: 96rpx;
        margin-bottom: 45rpx;
        .brand {
          width: 200rpx;
          position: absolute;
          left: -52rpx;
          bottom: -50rpx;
        }
      }
      .img-box {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: #f3f3f3;
        overflow: hidden;
        .avatar {
          width: 100%;
        }
      }
      .cname {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 30rpx;
        margin-bottom: 10rpx;
        max-width: 100%;
      }
      .activity {
        width: 100%;
        text-align: center;
        padding: 0 16rpx;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 22rpx;
        color: #999;
      }
    }
  }
}

// 排行分类
.cate-box {
  padding: 20rpx 72rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  .cate-list {
    justify-content: space-between;
    line-height: 64rpx;
    border-radius: 32rpx;
    background-color: #f3f3f3;
    .cate-item {
      padding: 0 36rpx;
      border-radius: 32rpx;
      position: relative;
      &.active {
        background-color: $uni-color-primary;
        color: #fff;
      }
      &.active::after {
        content: ' ';
        width: 0;
        height: 0;
        position: absolute;
        left: 0;
        right: 0;
        margin: auto;
        bottom: -48rpx;
        border: 20rpx solid;
        border-color: transparent transparent #ffffff transparent;
      }
    }
  }
}

// 置业顾问列表
.advier-list {
  padding: 20rpx 48rpx;
  background-color: #fff;
  .adviser-item {
    justify-content: space-between;
    align-items: flex-start;
    padding: 30rpx 0;
    .adviser_index {
      margin-top: 20rpx;
      margin-right: 20rpx;
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
    }
    .header_img {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 15rpx;
      overflow: hidden;
      background-color: #f3f3f3;
    }
    image {
      width: 100%;
    }
    .info {
      flex: 1;
      overflow: hidden;
      .name {
        display: flex;
        align-items: center;
        margin-bottom: 6rpx;
        .text {
          // flex: 1;
          font-size: 32rpx;
        }
      }
      .mgr-20 {
        margin-right: 20rpx;
      }
      .data {
        display: inline-block;
        margin-bottom: 6rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 22rpx;
        color: #999;
      }
    }

    .adviser-right {
      align-items: flex-end;
    }

    .btn-list {
      align-items: center;
      text {
        color: #999;
      }
      .btn {
        width: 64rpx;
        height: 64rpx;
        ~ .btn {
          margin-left: 30rpx;
        }
        .icon-box {
          width: 64rpx;
          height: 64rpx;
          justify-content: center;
          text-align: center;
          border-radius: 50%;
        }
        .icon {
          width: 64rpx;
          height: 64rpx;
        }
      }
    }
    .zan {
      align-items: center;
      margin-top: 20rpx;
      .zan_num {
        margin-left: 6rpx;
        font-size: 22rpx;
        color: #d8d8d8;
      }
    }
  }
}
.join {
  position: fixed;
  width: 100vw;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 40rpx 0;
  background: #fff;
}
.join-btn {
  // position: fixed;
  // bottom: 40rpx;
  width: 360rpx;
  height: 88rpx;
  // left: 0;
  // right: 0;
  margin: auto;
  text-align: center;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  border-radius: 44rpx;
  font-weight: bold;
  color: #fff;
}
</style>
