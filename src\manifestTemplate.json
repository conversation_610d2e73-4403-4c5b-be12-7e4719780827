{
	"name": "{siteName}",
	"appid": "{appId}",
	"description" : "",
	"versionName" : "3.4.0",
	"versionCode" : 389,
	"transformPx" : false,
	"uniStatistics" : 
	{
		"enable" : false //全局关闭统计
		
	},
	"app-plus" : 
	{
		"nvueCompiler" : "uni-app",
		/* 5+App特有相关 */
		"usingComponents" : true, //是否启用`自定义组件模式`，为true表示新的`自定义组件模式` ，否则为`template模板模式`   
		
		"splashscreen" : 
		{
			"alwaysShowBeforeRender" : true,
			"waiting" : false,
			"autoclose" : true,
			"delay" : 0
		},
		"modules" : 
		{
			"Share" : {},
			"VideoPlayer" : {},
			"Maps" : {},
			"Payment" : {},
			"Statistic" : {},
			"OAuth" : {}
		},
		/* 模块配置 */
		"distribute" : 
		{
			/* 应用发布信息 */
			"android" : 
			{
				/* android打包配置 */
				"permissions" : 
				[
					"<uses-feature android:name=\"android.hardware.camera\"/>",
					"<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
					"<uses-permission android:name=\"android.permission.CAMERA\"/>",
					"<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
					"<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
					"<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
					"<uses-permission android:name=\"android.permission.INTERNET\"/>",
					"<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
					"<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
					"<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
					"<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
					"<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
					"<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>",
					"<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
					"<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>",
					"<uses-permission android:name=\"android.permission.VIBRATE\"/>",
					"<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
					"<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
					"<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
					"<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
					"<uses-permission android:name=\"android.permission.RECEIVE_USER_PRESENT\"/>"
				]
			},
			"ios" : 
			{
				"privacyDescription" : 
				{
					"NSLocationAlwaysAndWhenInUseUsageDescription" : "获取周边的房源",
					"NSPhotoLibraryAddUsageDescription" : "保存生成的图片海报",
					"NSPhotoLibraryUsageDescription" : "发布商品上传图片",
					"NSCameraUsageDescription" : "拍摄照片 上传照片",
					"NSLocationWhenInUseUsageDescription" : "获取周边房源"
				},
				"idfa" : false
			},
			/* ios打包配置 */
			"sdkConfigs" : 
			{
				"maps" : 
				{
					"amap" : 
					{
						"appkey_ios": "{amapIos}",
						"appkey_android": "{amapAndroid}"
					}
				},
				"payment" : 
				{
					"weixin" : 
					{
						"appid": "{weixinApppid}",
						"UniversalLinks" : ""
					},
					"alipay" : {}
				},
				"push" : {},
				"share" : 
				{
					"weixin" : 
					{
						"appid": "{weixinApppid}",
						"UniversalLinks" : ""
					}
				},
				"statics" : 
				{
					"umeng" : 
					{
						"appkey_ios": "{umengAppkey}",
						"channelid_ios": "{umengChannelid}",
						"appkey_android": "{umengAppkey}",
						"channelid_android": "{umengChannelid}"
					}
				},
				"ad" : null
			},
			"icons" : 
			{
				"android" : 
				{
					"hdpi" : "unpackage/res/icons/72x72.png",
					"xhdpi" : "unpackage/res/icons/96x96.png",
					"xxhdpi" : "unpackage/res/icons/144x144.png",
					"xxxhdpi" : "unpackage/res/icons/192x192.png"
				},
				"ios" : 
				{
					"appstore" : "unpackage/res/icons/1024x1024.png",
					"ipad" : 
					{
						"app" : "unpackage/res/icons/76x76.png",
						"app@2x" : "unpackage/res/icons/152x152.png",
						"notification" : "unpackage/res/icons/20x20.png",
						"notification@2x" : "unpackage/res/icons/40x40.png",
						"proapp@2x" : "unpackage/res/icons/167x167.png",
						"settings" : "unpackage/res/icons/29x29.png",
						"settings@2x" : "unpackage/res/icons/58x58.png",
						"spotlight" : "unpackage/res/icons/40x40.png",
						"spotlight@2x" : "unpackage/res/icons/80x80.png"
					},
					"iphone" : 
					{
						"app@2x" : "unpackage/res/icons/120x120.png",
						"app@3x" : "unpackage/res/icons/180x180.png",
						"notification@2x" : "unpackage/res/icons/40x40.png",
						"notification@3x" : "unpackage/res/icons/60x60.png",
						"settings@2x" : "unpackage/res/icons/58x58.png",
						"settings@3x" : "unpackage/res/icons/87x87.png",
						"spotlight@2x" : "unpackage/res/icons/80x80.png",
						"spotlight@3x" : "unpackage/res/icons/120x120.png"
					}
				}
			},
			"splashscreen" : 
			{
				"android" : 
				{
					"xxhdpi" : "D:/splashscreen/__UNI__A33430A/1080x1882.png",
					"hdpi" : "D:/splashscreen/__UNI__A33430A/480x762.png",
					"xhdpi" : "D:/splashscreen/__UNI__A33430A/720x1242.png"
				},
				"ios" : 
				{
					"iphone" : 
					{
						"portrait-896h@3x" : "D:/splashscreen/__UNI__A33430A/1242x2688.png",
						"portrait-896h@2x" : "D:/splashscreen/__UNI__A33430A/828x1792.png",
						"iphonex" : "D:/splashscreen/__UNI__A33430A/1125x2436.png",
						"retina55" : "D:/splashscreen/__UNI__A33430A/1242x2208.png",
						"retina47" : "D:/splashscreen/__UNI__A33430A/750x1334.png",
						"retina40" : "D:/splashscreen/__UNI__A33430A/640x1136.png",
						"retina35" : "D:/splashscreen/__UNI__A33430A/640x960.png"
					}
				}
			}
		}
	},
	/* SDK配置 */
	"quickapp" : {},
	/* 快应用特有相关 */
	"mp-weixin" : 
	{
		/* 小程序特有相关 */
		"usingComponents" : true, //是否启用`自定义组件模式`，为true表示新的`自定义组件模式`，否则启用老的`template模板模式`
		
		"appid": "{mpAppid}",
		"setting" : 
		{
			"urlCheck" : true
		},
		"permission" : 
		{
			"scope.userLocation" : 
			{
				"desc" : "获取附近的房源"
			}
		}
	},
	"mp-baidu" : 
	{
		"appid" : "17593772",
		"usingComponents" : true,
		"setting" : 
		{
			"urlCheck" : true
		}
	},
	"h5" : 
	{
		"router" : 
		{
			"base" : "/m/",
			"mode" : "history"
		},
		"devServer" : 
		{
			"host" : "m.tengfun.com",
			"https" : false,
			"disableHostCheck" : true,
			"port" : 81,
			"proxy" : 
			{
				"/wap" : 
				{
					"target" : "https://www.tengfun.com",
					"changeOrigin" : true
				},
				"/wechat" : 
				{
					"target" : "https://www.tengfun.com",
					"changeOrigin" : true
				}
			}
		},
		"sdkConfigs" : 
		{
			"maps" : 
			{
				"qqmap" : 
				{
					"key": "{mapKey}" //腾讯地图秘钥（key）
					
				}
			}
		},
		"optimization" : 
		{
			"treeShaking" : 
			{
				"enable" : false //是否开启摇树优化
				
			}
		},
		"async" : 
		{
			"timeout" : 15000 //js加载超时时间
			
		},
		"title": "{siteName}"
	}
}
