<template>
  <view class="progress">
    <view class="header flex-box">
      <text class="btn cancel" @click="$emit('cancel')">取消</text>
      <text class="btn confirm" @click="confirm">确认</text>
    </view>
    <view class="block" v-if ="showCname">
      <view class="label">客户姓名</view>
      <input v-model="detail.cname" placeholder="请输入客户姓名">
    </view>
    <view class="block" v-if="from != 'follow'">
      <view class="label">购房进度<text class="necessary">*</text></view>
      <view class="progress_list flex-box">
        <view class="progress_item" :class="{active: detail.status == item.id}" @click="onSelect(item)" v-for="item in progress_list" :key="item.id">{{item.name}}</view>
        <view class="custom flex-box">
          <text>自定义：</text>
          <view class="progress_item">
            <input type="text" @input="onInputStatus" :value="custom_statdus_text" placeholder="请输入" placeholder-style="font-size: 28rpx">
          </view>
        </view>
      </view>
    </view>
    <view class="block" v-if="from != 'follow'">
      <view class="label">上传图片</view>
      <my-upload :chooseType="1" :maxCount="3" @uploadDon="uploadDon"></my-upload>
    </view>
    <view class="block">
      <view class="label">备注<text class="necessary">*</text></view>
      <textarea v-if="show" v-model="detail.descp" placeholder="描述情况内容"></textarea>
    </view>
  </view>
</template>

<script>
import myUpload from '@/components/form/myUpload'
export default {
  components: {
    myUpload
  },
  data () {
    return {
      progress_list: [
        {
          id: 1,
          name: "未联系"
        },
        {
          id: 2,
          name: "未接通"
        },
        {
          id: 3,
          name: "失效"
        },
        {
          id: 4,
          name: "带看到访"
        },
        {
          id: 5,
          name: "意向高"
        },
        {
          id: 6,
          name: "成交"
        },
      ],
      detail: {
        status: "",
        imgs: "",
        descp: "",
        cname:""
      },
      custom_statdus_text: "",
      show_tab: ""
    }
  },
  props:{
    cname:{
      type:String,
      default:''
    },
    showCname:{
      type:Boolean,
      default:true
    },
    from:{
      type: String,
      default: "",
    },
    show:{
      type:Boolean,
      default:false
    }
  },
  watch:{
    cname(val, oldVal){//普通的watch监听
      this.detail.cname =val
      immediate: true
    },
  },
  methods: {
    onSelect(e){
      // if(this.detail.status == e.id){
      //   this.detail.status = ""
      //   this.detail.status_text = ""
      //   return
      // }
      this.custom_statdus_text = ""
      this.detail.status = e.id
      this.detail.status_text = e.name
    },
    onInputStatus(e){
      this.detail.status = ""
      this.custom_statdus_text = e.detail.value
      this.detail.status_text = e.detail.value
    },
    uploadDon(e){
      this.detail.imgs = e.files.join(',')
    },
    confirm(){
      if (this.from != 'follow') {
        if(this.detail.status_text===''){
          uni.showToast({
            title: '请选择购房进度',
            icon: 'none'
          })
          return
        }
      }
      if(this.detail.descp===''){
        uni.showToast({
          title: '请填写备注',
          icon: 'none'
        })
        return
      }
      this.$emit('confirm', this.detail)
    }
  }
}
</script>

<style scoped lang="scss">
.progress{
  padding: 24rpx 48rpx;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  background-color: #fff;
  max-height: 73vh;
  overflow-y: auto;
  .header{
    justify-content: space-between;
    margin-bottom: 32rpx;
    .btn{
      display: inline-block;
      padding: 20rpx;
      font-size: 32rpx;
      &.cancel{
        color: #999;
      }
      &.confirm{
        color: $uni-color-primary;
      }
    }
  }
  .block{
    margin-bottom: 32rpx;
    .label{
      margin-bottom: 32rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      .necessary{
        position: relative;
        left: 4rpx;
        top: 4rpx;
        color: $uni-color-primary;
      }
    }
    .progress_list{
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .progress_item{
      min-width: 202rpx;
      max-width: 202rpx;
      height: 80rpx;
      line-height: 80rpx;
      box-sizing: border-box;
      margin-bottom: 24rpx;
      text-align: center;
      border-radius: 8rpx;
      background-color: #f8f8f8;
      color: #666;
      &.active{
        border: 1rpx solid $uni-color-primary;
        color: $uni-color-primary;
        background-color: #fff;
      }
    }

    .custom{
      align-items: center;
      color: #666;
      .progress_item{
        margin-bottom: 0;
      }
      input{
        height: 40rpx;
        text-align: left;
        padding: 20rpx 10rpx;
        margin-left: 10rpx;
        color: #666;
      }
    }

    textarea{
      height: 200rpx;
      padding: 24rpx;
      font-size: 28rpx;
      background-color: #f8f8f8;
    }
    input{
      height: 60rpx;
      padding: 0 24rpx;
      font-size: 28rpx;
      background-color: #f8f8f8;
    }
  }
}
</style>