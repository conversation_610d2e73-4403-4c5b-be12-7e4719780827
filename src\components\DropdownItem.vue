<template>
  <view class="dropdown-item" :class="{ disabled: disabled }" @click.prevent.stop="onClick">
    <text class="text">
      <slot></slot>
    </text>
  </view>
</template>

<script>
export default {
  name: 'DropdownItem',
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    getParent(component_name){
  var _self = this
  var getParent = function (parent) {
    if (
      parent.$options.name === component_name ||
      parent.$options._componentTag === component_name
    ) {
      return parent
    } else if (parent.$parent) {
      return getParent(parent.$parent)
    } else {
      return null
    }
  }
  return getParent(_self.$parent)
    },
    onClick() {
      if (this.disabled) {
        return
      }
      let parent = this.getParent.call(this, 'Dropdown')
      parent.show = false
      this.$emit('click')
    },
  },
}
</script>

<style scoped lang="scss">
.dropdown-item {
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #2e3c4e;
  &.disabled {
    color: rgba($color: #2e3c4e, $alpha: 0.3);
  }
}
</style>
