<template>
  <view class="content">
    <view class="top_nav flex-row bottom-line" :style="{opacity:opacity}">
      <view class="nav_item" v-for="(item, index) in top_navs" :key="index" :class="{active:current_nav_index===index}" @click="scroppTo(item.id, index)">{{item.name}}</view>
    </view>
    <view class="focus-box">
      <swiper class="banner" :indicator-dots="false" :circular="true" :duration="300" indicator-active-color="#f65354" @change="swiperChange" :current="swiperCurrent" >
        <swiper-item v-for="item in images" :key="item.id" @click="navigateTo(`/pages/house_price/photos?cid=${id}`)">
          <view class="swiper-item">
            <image :src="item.pic | imageFilter('w_6401')" mode="aspectFill"></image>
          </view>  
        </swiper-item>
        <swiper-item v-for="item in videos" :key="item.id" @click="navigateTo(`/pages/house_price/photos?cid=${id}`)">
          <view class="swiper-item">
            <image :src="(item.cover||item.pic) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/video.png" mode="widthFix"></image>
          </view>
        </swiper-item>
        <swiper-item v-for="item in vrs" :key="item.id">
          <view class="swiper-item" @click="toVr(item)">
            <image :src="(item.cover || item.path)| imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/vr.png" mode="widthFix"></image>
          </view>
        </swiper-item>
      </swiper>
      <view class="img-total">共{{imageCount}}张</view>
      <view class="cate-box">
        <view class="cate-list">
          <view v-if="images.length>0" class="cate" @click="switchFocus('img')" :class="cateActive=='img'?'active':''">图片</view>
          <view v-if="videos.length>0" class="cate" @click="switchFocus('video')" :class="cateActive=='video'?'active':''">视频</view>
          <view v-if="vrs.length>0" class="cate" @click="switchFocus('vr')" :class="cateActive=='vr'?'active':''">全景</view>
        </view>
      </view>
    </view>
    <view class="info ">
      <view id='house_price'>
        <view class="info_box flex-box flex-row pad24">
          <view class="title_info">
            <view class="title">
              {{ detail.name }}
            </view>
            <view class="labels">
              <text class="label" v-for ="(item,index) in detail.labels" :key ="index">
                {{item}}
              </text>
            </view>
          </view>
          <view class="praise" @click ="followHouse">
            <view class="praise_icon">
              <myIcon type="ic_guanzhu" v-if ="detail.is_follow==0" color="#999"></myIcon>
              <myIcon type="ic_guanzhu_red" color="#fb656a" v-if ="detail.is_follow==1"></myIcon>
            </view>
            <view class="praise_name" :class ="{'has_praise':detail.is_follow==1}">
              {{detail.is_follow==1?"已订阅":'订阅'}}
            </view>
          </view>
        </view>
        
        <view class="info_card flex-box flex-row pad24">
            <view class="guapai info_card_con">
              <view class="info_card_con_top">
                <template v-if ="detail.avg_price">
                  <text class="num price">{{detail.avg_price}}</text>
                  <text class="unit">元/m²</text>
                </template>
                <template v-else>
                  <text class="num price">不详</text>
                </template>
              </view>
              <view class="info_card_con_bottom">
                二手房挂牌均价
              </view>
              
            </view>
            <view class="sale info_card_con" @click ="toErshou">
              <view class="info_card_con_top">
                  <text class="num">{{detail.count ||0}}</text>
                  <text class="unit">套</text>
              </view>
              <view class="info_card_con_bottom">
                <text>在售房源</text>
                <my-icon type="ic_into" size="22rpx" color="#999"></my-icon>
              </view>
              
            </view>
            <view class="rent info_card_con" @click ="toRenting">
              <view class="info_card_con_top">
                  <text class="num">{{detail.cz_count||0}}</text>
                  <text class="unit">套</text>
              </view>
              <view class="info_card_con_bottom">
                <text>在租房源</text>
                <my-icon type="ic_into" size="22rpx" color="#999"></my-icon>
              </view>
              
            </view>
        </view>
        <view class="mini_chart_box  pad24">
        <view class="mini_chart flex-box flex-row">
            <view class="mi-charts" :style="{width:cWidth1+'px'}" >
              <!--#ifdef H5-->
              <canvas
                canvasId="canvasLineTop"
                class="charts_top"
                :style="{
                  position: 'relative',
                  width: cWidth1 * pixelRatio + 'px',
                  height: cHeight1 * pixelRatio + 'px',
                  transform: 'scale(' + 1 / pixelRatio + ')',
                  'margin-left': (-cWidth1 * (pixelRatio - 1)) / 2 + 'px',
                  top: (-cHeight1 * (pixelRatio - 1)) / 2 + 'px',
                }"
                @touchstart="false"
              ></canvas>
              <!--#endif-->
              <!--#ifndef H5-->
              <canvas canvasId="canvasLineTop" class="charts_top" @touchstart="false"></canvas>
              <!--#endif-->
            </view>
            <view class="price_press">
              <view class="press_name">
                <text class ="press_direction">{{zoushi}}</text>
                <myIcon type="ic_up"  v-if ="zoushi=='上升'" color=" #FB656A"></myIcon>
                <myIcon type="ic_down" v-if ="zoushi=='下降'" color=" #00CAA7"></myIcon>
              </view>
              <view class="press_con">
                价格趋势
              </view>
            </view>
        </view>
        </view>
        <view class="options1 flex-row">
          <view class="item" v-for="(item, index) in options_list" :key="index" @click="toOptionPath(item)">
            <image class="icon" :src="item.icon | imageFilter('m_320')"></image>
            <text class="text">{{item.text}}</text>
          </view>
        </view>
      
        <view class="action-btn-box flex-box">
          <view class="action-btn flex-box flex-1" @click="showSubForm(2, 1)">
            <my-icon type="ic_jiangjia1" color="#ff656b" size="48rpx"></my-icon>
            <text class ="action_name">调价通知我</text>
          </view>
          <view class="action-btn flex-box flex-1" @click="showSubForm(2, 2)">
            <my-icon type="zuixinfangyuan" color="#ff656b" size="48rpx"></my-icon>
            <text class ="action_name">有房通知我</text>
          </view>
        </view>
      
        <view class="container house_infos">
          <view class="label-title flex-row"><text class="text flex-1">小区概况</text>
              <view class="flex-row j-box">
                <myIcon type="buchongbianji" color="#999"></myIcon>
                <text class="tip tip_correct" @click ="toedit(0)">补充信息</text>
              </view>
              <view class="flex-row j-box" style="margin-left:30rpx">            
                <myIcon type="jiucuojiangli" color="#999"></myIcon>
                <text class="tip tip_correct" @click ="toedit(1)">纠错奖励</text>
              </view>
            </view>
          <view class="house_info">
            <view class="house_info_c flex-box flex-row">
              <view class="house_info_name">
                楼栋总数
              </view>
              <view class="house_info_value showline1">
                {{detail.loudong||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row">
              <view class="house_info_name">
                房屋总数
              </view>
              <view class="house_info_value showline1">
                {{detail.house ||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row">
              <view class="house_info_name">
                容积率
              </view>
              <view class="house_info_value showline1">
                {{detail.volume ||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row">
              <view class="house_info_name">
                绿化率
              </view>
              <view class="house_info_value showline1">
                {{detail.green_rate ||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row">
              <view class="house_info_name">
                停车位
              </view>
              <view class="house_info_value showline1">
                {{detail.stall ||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row" v-if ="showMoreInfo">
              <view class="house_info_name">
                建筑面积
              </view>
              <view class="house_info_value showline1">
                {{detail.total_area ||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row"  v-if ="showMoreInfo">
              <view class="house_info_name">
                开发商
              </view>
              <view class="house_info_value showline1">
                {{detail.company ||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row"  v-if ="showMoreInfo">
              <view class="house_info_name">
                建成年代
              </view>
              <view class="house_info_value showline1">
                {{detail.completed_time ||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row"  v-if ="showMoreInfo">
              <view class="house_info_name">
                物业公司
              </view>
              <view class="house_info_value showline1">
                {{detail.management_company ||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row"  v-if ="showMoreInfo">
              <view class="house_info_name">
                物业费
              </view>
              <view class="house_info_value showline1">
                {{detail.property_price ||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row"  v-if ="showMoreInfo">
              <view class="house_info_name">
                建筑类型
              </view>
              <view class="house_info_value showline1">
                {{detail.architecture_type ||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row"  v-if ="showMoreInfo">
              <view class="house_info_name">
                小区地址
              </view>
              <view class="house_info_value showline1">
                {{detail.address ||"暂未更新"}}
              </view>
            </view>
            <view class="house_info_c flex-box flex-row"  v-if ="showMoreInfo&&detail.desc">
              <view class="house_info_name">
                小区介绍
              </view>
              <view class="house_info_value">
                {{detail.desc}}
              </view>
            </view>

          </view>
          <template v-if ="correct_user.is_adviser>0||correct_user.is_agent>0">
            <view class="user-box flex-row" v-if="correct_user.id">
              <view class="a-img flex-row">
                <image :src="correct_user.prelogo"></image>
                <view class="name">{{correct_user.cname}}</view>
              </view>
              
              <view class="right-c flex-row">
                <view class="r-l" @click="handleChat(correct_user)">在线咨询</view>
                <!-- <view class="l-l">查看全部</view> -->
              </view>
            </view>
          </template>
          <view class="tips-house flex-row" v-if="correct_user.id">
            <myIcon type="tishifu" color="#999"></myIcon>
            <text class="c">以上信息由 {{correct_user.cname}} 补充更新</text>
          </view>
          <view class="action-btn-box flex-box">
            <view class="action-btn flex-box flex-1" @click="showMoreInfo =!showMoreInfo">
              <!-- <my-icon type="zuixinfangyuan" color="#ff656b" size="48rpx"></my-icon> -->
              <text class ="action_name">查看更多</text>
            </view>
          </view>
        </view>
        <view class="container house_infos" v-if="sheshiList.length">
          <view class="label-title">小区设施</view>
          <view class="house_sheshi">
            <my-grid :options="sheshiList" :column-num="5" :show-border="false" className="sm"  ></my-grid>
          </view>
        </view>
      </view>
      <view id="agent">
      <!-- 相关房源信息 -->
        <view class="container">
          <view class="label-tab">
            <view class="tab-item" :class="{ active: house_type === 1 }" @click="house_type = 1">二手房</view>
            <view class="tab-item" :class="{ active: house_type === 2 }" @click="house_type = 2">租房</view>
            <view class="tab-item" :class="{ active: house_type === 3 }" @click="house_type = 3">商业租赁</view>
          </view>
          <view class="house-box">
            <block v-if="house_type == 1">
              <block v-for="(item, index) in ershou_list" :key="index">
                <house-item :item-data="item" type="ershou" @click="navigateTo(`/pages/ershou/detail?id=${item.id}`)"></house-item>
              </block>
            </block>
            <block v-if="house_type == 2">
              <block v-for="(item, index) in renting_list" :key="index">
                <house-item :item-data="item" type="renting" @click="navigateTo(`/pages/renting/detail?id=${item.id}`)"></house-item>
              </block>
            </block>
            <block v-if="house_type == 3">
               <block v-for="item in estate_list" :key="item.id">
                <listItemItem v-if="item.parentid == 1" :item-data="item" type="sale" @click="toDetail"></listItemItem>
                <listItemItem v-if="item.parentid == 2" :item-data="item" type="rent" @click="toDetail"></listItemItem>
                <listItemItem v-if="item.parentid == 3" :item-data="item" type="transfer" @click="toDetail"></listItemItem>
              </block>
            </block>
            <view class="look-more" v-if="house_type === 1" @click="toErshou">查看更多</view>
            <view class="look-more" v-if="house_type === 2" @click="toRenting">查看更多</view>
            <view class="look-more" v-if="house_type === 3" @click="toEstate">查看更多</view>
          </view>
        </view>
      
        <view class="container" v-if="agent_list&&agent_list.length">
          <view class="label-title title-row ">
            <text class="text">{{mountTitle}}</text>
          </view>
          <view class="agent-box">
            <view
              class="agent flex-box"
              v-for="agent in agent_list"
              :key="agent.id"
              @click="toAgentDetail(agent)"
            >
              <image :src="agent.prelogo" mode="aspectFill"></image>
              <view class="agent-info flex-1">
                <view class="agent-name flex-box">
                  <text class="text">{{ agent.cname }}</text>
                  <text class="agent-type" v-if="agent.tname">{{ agent.tname}}</text>
                </view>
                <view class="agent-address">
                  <text>{{ agent.traffic_volume }}人咨询过他</text>
                  <!-- {{ agent.introduce||'更多小区房源，点我咨询' }} -->
                </view>
              </view>
              <view class="right flex-box">
                <image
                  class="icon"
                  @click.prevent.stop="handleChat(agent)"
                  src="https://images.tengfangyun.com/images/icon/ic_zixun.png"
                ></image>
                <image
                  class="icon"
                  @click.prevent.stop="handleTel(agent)"
                  src="https://images.tengfangyun.com/images/icon/ic_dianhua.png"
                ></image>
              </view>
            </view>
            <view class="add_entrance" @click="toAdd">发布小区实勘动态 成为小区专家</view>
          </view>
        </view>

        <!-- 专家解读 -->
        <view class="container" v-if ="publists&&publists.length">
          <view class="label-title title-row">
            <text class="text">小区专家</text>
            <text class="tip" @click ="toAgentPublic">更多</text>
          </view>
          <view
            class="user_comments_list"
            v-for="(item, index) in publists"
            :key="index"
          >
          <commutityAgent :from="2" @toAgentDetail="toAgentDetail" @handleChat= "handleChat" @handleTel ="handleTel" :item="item"></commutityAgent>
            
          </view>
        </view>
      </view>
      <view id="zhoubian">
        <view class="container" v-if="!hideMap">
          <view class="label-title">地图及周边</view>
          <mapNearby :scale="mapData.scale" :cirles ="cirles"  :enableZoom="false" :enableScroll ='false' :lat="detail.lat" :lng="detail.lng" :markers="mapData.covers" @clickMap="viewMap()" @clickCate="getCovers"></mapNearby>
        </view>
        <view class="container" v-if="school_list.length>0">
          <view class="label-title">附近学校</view>
          <view class="school-box">
            <view
              class="school flex-box"
              v-for="school in school_list"
              :key="school.id"
              @click="navigateTo(`/school/detail?id=${school.id}`)"
            >
              <image :src="school.pic" mode="aspectFill"></image>
              <view class="school-info">
                <view class="school-name flex-box">
                  <text class="text">{{ school.name }}</text>
                  <text class="school-type">{{ school.type_name }}</text>
                </view>
                <view class="school-address">{{ school.address }}</view>
              </view>
              <view class="right flex-box">
                <text>详情</text>
                <my-icon type="ic_into" size="32rpx" color="#999"></my-icon>
              </view>
            </view>
          </view>
          <view class="look-more" @click="toSchool">查看更多</view>
        </view>
      </view>
      <view id="price">
        
        <view class="container" v-if="detail.avg_price">
          <view class="label-title">
            <text>购房预算</text>
            <text class="tip">（以均价{{ detail.avg_price }}元/平 购买120㎡房源为例）</text>
          </view>
          <view class="card">
            <view class="tab-box flex-box" :class="{right:loan_type === 1}">
              <view class="tab" :class="{ active: loan_type === 0 }" @click="loan_type = 0">等额本息</view>
            <view class="tab" :class="{ active: loan_type === 1 }" @click="loan_type = 1">等额本金</view>
            </view>
            <loan-details
              :tab="loan_type"
              :lilv="(borrow.shangdaililv||4.9)/100/12"
              :shoufubili="borrow.jiceng||3"
              :total="(detail.avg_price * 120) / 10000"
              :downPayments="(((detail.avg_price * 120) / 10000) *(borrow.jiceng||3)/10).toFixed(0)"
            ></loan-details>
          </view>
        </view>
        <view class="container">
          <view class="label-title">价格走势</view>
          <view class="qiun-charts">
            <!--#ifdef H5-->
            <canvas
              canvasId="canvasLine"
              class="charts"
              :style="{
                position: 'relative',
                width: cWidth * pixelRatio + 'px',
                height: cHeight * pixelRatio + 'px',
                transform: 'scale(' + 1 / pixelRatio + ')',
                'margin-left': (-cWidth * (pixelRatio - 1)) / 2 + 'px',
                top: (-cHeight * (pixelRatio - 1)) / 2 + 'px',
              }"
              @touchstart="touchLine"
            ></canvas>
            <!--#endif-->
            <!--#ifndef H5-->
            <canvas canvasId="canvasLine" class="charts" @touchstart="touchLine"></canvas>
            <!--#endif-->
          </view>
        </view>
      </view>
    </view>
    <view id="concat_info">
      <view class="container" v-if="house_price_list.length > 0">
        <view class="label-title pad_48">看过本小区的人还看过</view>
        <view class="house-price">
          <house-price
            v-for="(item, index) in house_price_list"
            :key="index"
            :itemData="item"
            :showBottomLine="false"
          ></house-price>
        </view>
      </view>
    
    </view>
    <sub-form :groupCount="detail.groupCount" :sub_type="sub_type" :sub_mode="sub_mode" :sub_title="sub_title" :sub_content="sub_content" ref='sub_form' @onsubmit="handleSubForm"></sub-form>
    <!-- #ifndef MP-WEIXIN -->
    <login-popup ref="login_popup" @onclose="handleCloseLogin" sub_content="当前操作需要绑定手机号，请输入您的手机号"></login-popup>
    <!-- #endif -->
    <chat-tip></chat-tip>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import { getSceneParams } from "../../common/index.js";
import myIcon from "../../components/myIcon.vue";
import loanDetails from "../../components/loanDetail.vue";
import uCharts from "../../components/u-charts/u-charts.js";
import houseItem from "../../components/houseItem.vue";
import listItemItem from "../../components/listItemItem.vue";
import housePrice from "../../components/housePrice.vue";
import mapNearby from "../../components/mapNearby.vue";
import myGrid from "../../components/myGrid.vue";
import allTel from '../../common/all_tel.js'
import getChatInfo from "../../common/get_chat_info.js";
import wxApi from '../../common/mixin/wx_api'
import subForm from '../../components/subForm'
// #ifndef MP-WEIXIN
import loginPopup from '../../components/loginPopup'
// #endif
import { getLonAndLat } from '@/common/utils/getLonAndLat'
export default {
  components: {
    myIcon,
    loanDetails,
    houseItem,
    housePrice,
    mapNearby,
    subForm,
    myGrid,
    // #ifndef MP-WEIXIN
    loginPopup,
    // #endif
    listItemItem
  },
  mixins: [wxApi],
  data() {
    return {
      id: "",
      detail: {
        lat:'',
        lng:''
      },
      imageCount:'',
      images:[],
      videos:[],
      vrs:[],
      swiperCurrent:0,
      cateActive:'',
      news_list: [],
      current_news_index:0,
      hideMap: true,
      mapList: [],
      mapData: {
        scale: 12,
        covers: [],
      },
      school_list: [],
      house_type: 1,
      mountTitl: '',
      agent_list: [],
      ershou_list: [],
      renting_list: [],
      estate_list:[],
      house_price_list: [],
      loan_type: 0,
      // #ifdef H5
      cWidth: "",
      cHeight: "",
      cWidth1: "",
      cHeight1: "",
      // #endif
      pixelRatio: 1,
      sub_type: 0,
      sub_title:"调价通知我",
      sub_content:"此小区房价有变化，我们会及时通知您",
      is_agent:0,
      shangdaililv: 4.9,
      borrow:{},
      tel_res: {},
      show_tel_pop: false,
      wxqunopen:1,
      group_headers:[],
      group_list:[],
      wxq:{},
      voice_playing_index: -1,
      agent_jiedu:[],
      opt:{
        canvaLine:null,
        canvaLineTop:null,
      },
      sheshiList:[],
      opacity:0,
      top_navs:[
        {
          name: '小区介绍',
          id: 'house_price'
        },
        
        {
          name: '小区专家',
          id: 'agent'
        },
        {
          name: '周边',
          id: 'zhoubian'
        },
        {
          name: '价格走势',
          id: 'price'
        },
        {
          name: '相关房源',
          id: 'concat_info'
        }
      ],
      current_nav_index:0,
      showMoreInfo:false,
      zoushi:'持平',
      publists:[],
      moteNum:0,
      correct_user:{},
      options_list: [
        {
          icon: '/ditu/fangchanfenxi.png',
          text: '房价分析',
          path: '/propertyData/map/analysis?id={build_id}&type=3&lat={lat}&lng={lng}&from=1'
        },
        {
          icon: '/ditu/bankuaijiazhi.png',
          text: '版块价值',
          path: '/propertyData/map/analysis?id={build_id}&type=3&lat={lat}&lng={lng}&from=2'
        },
        {
          icon: '/ditu/cunliangfenxi.png',
          text: '存量分析',
          path: '/propertyData/map/analysis?id={build_id}&type=3&lat={lat}&lng={lng}&from=3',
         
        },
        {
          icon: '/ditu/jiaoyufenxi.png',
          text: '教育分析',
          // path: '/online/loudong?build_id={build_id}&sand_id={sand_id}',
          path: '/propertyData/map/analysis?id={build_id}&type=3&lat={lat}&lng={lng}&from=4'
        },
        {
          icon: '/ditu/jiaoyupeitao.png',
          text: '配套分析',
          path: '/propertyData/map/map?id={build_id}&type=3&lat={lat}&lng={lng}'
        },
      ],
    };
  },
  computed:{
    sub_mode() {
      return this.$store.state.sub_form_mode 
    },
    is_open_im() {
      return this.$store.state.im.ischat
    },
    oneKm(){
      return getLonAndLat(this.detail.lng,this.detail.lat,0,1000)
    },
    twoKm(){
      return getLonAndLat(this.detail.lng,this.detail.lat,0,2000)
    },
    threeKm(){
      return getLonAndLat(this.detail.lng,this.detail.lat,0,3000)
    },
    cirles(){
      if(this.detail &&this.detail.lat) {
        return [
          {
							longitude:this.detail.lng,
							latitude:this.detail.lat,
							color:"#ff0000",
							radius:1000,
							strokeWidth:1,
						},
						{
							longitude:this.detail.lng,
							latitude:this.detail.lat,
							color:"#ff9c00",
							radius:2000,
							strokeWidth:1
						},
						{
							longitude:this.detail.lng,
							latitude:this.detail.lat,
							color:"#fee500",
							fillColor:"#00000026",
							radius:3000,
							strokeWidth:1
						}
        ]
      }
    }
  },
  onLoad(options) {
    //#ifdef H5
    uni.getSystemInfo({
      success: (res) => {
        if (res.pixelRatio > 1) {
          //正常这里给2就行，如果pixelRatio=3性能会降低一点
          //_self.pixelRatio =res.pixelRatio;
          this.pixelRatio = 2;
        }
      },
    });
    //#endif
    this.cWidth = uni.upx2px(650);
    this.cHeight = uni.upx2px(500);
    this.cWidth1 = uni.upx2px(500);
    this.cHeight1 = uni.upx2px(200);
    // if(options.title){
    // 	this.detail.title = decodeURIComponent(options.title)
    // 	uni.setNavigationBarTitle({
    // 		title: this.detail.title
    // 	})
    // }
    uni.$on("getDataAgain",()=>{
       this.getData(this.id)
    })
    if (options.scene) {
      const params = getSceneParams(decodeURIComponent(options.scene));
      if (params.id) {
        this.id = params.id;
        this.getData(this.id);
      }
    }
    if (options.id) {
      this.id = options.id;
      this.getData(options.id);
      this.getLineData();
    }
  },
  onUnload(){
    uni.$off("getDataAgain")
  },
  onReady() {
    
  },
  onPageScroll(e){
    let top = 44
    // #ifdef H5
    top = 88
    // #endif
    this.scrollTop = e.scrollTop
    let opacity = 0
    if(this.scrollTop>top){
      opacity = (this.scrollTop-top)/44/4
    }
    if(opacity>1)opacity=1
    this.opacity = opacity
    if(this.active_tab){
      return
    }
    if(this.time&&new Date().getTime()-this.time<220){
      return
    }else{
      this.time = new Date().getTime()
    }
    const query = uni.createSelectorQuery().in(this);
    query.select('#house_price').fields({rect:true,scrollOffset:true,size:true},data => {
      if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
        this.current_nav_index = 0
      }
    }).exec();
    
    query.select('#zhoubian').fields({rect:true,scrollOffset:true,size:true},data => {
      
      if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
        let _index = this.top_navs.findIndex(item=>item.id === 'zhoubian')
        if(_index>-1){
          this.current_nav_index = _index
        }
      }
    }).exec();
    
    query.select('#agent').fields({rect:true,scrollOffset:true,size:true},data => {
      if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
        let _index = this.top_navs.findIndex(item=>item.id === 'agent')
        if(_index>-1){
          this.current_nav_index = _index
        }
        // if(this.house_list.length>0){
        //   this.current_nav_index = 3
        // }else{
        //   this.current_nav_index = 2
        // }
      }
    }).exec();
    query.select('#price').fields({rect:true,scrollOffset:true,size:true},data => {
      if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
        let _index = this.top_navs.findIndex(item=>item.id === 'price')
        if(_index>-1){
          this.current_nav_index = _index
        }
        //  if(this.houseTypePic.length>0){
        //   this.current_nav_index = 3
        // }else{
        //   this.current_nav_index = 2
        // }
      }
    }).exec();
    query.select('#concat_info').fields({rect:true,scrollOffset:true,size:true},data => {
      if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
        let _index = this.top_navs.findIndex(item=>item.id === 'concat_info')
        if(_index>-1){
          this.current_nav_index = _index
        }
      }
    }).exec();
  },
  methods: {
    navigateTo(url) {
      this.$navigateTo(url);
    },
    scroppTo(id, index){
      this.current_nav_index = index
      this.active_tab = true
      setTimeout(()=>{
        this.active_tab = false
      },280)
      const query = uni.createSelectorQuery().in(this);
      query.select('#'+id).fields({rect:true,scrollOffset:true},data => {
        uni.pageScrollTo({
          duration:120,
          // #ifdef H5
          scrollTop:(this.scrollTop||0)+data.top -88,
          // #endif
          // #ifndef H5
          scrollTop:(this.scrollTop||0)+data.top - 44
          // #endif
        })
      }).exec();
    },
    getData(id) {
      this.$ajax.get(
        "house/communityDetailNew.html",
        { id },
        (res) => {
          if (res.data.code == 0) {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
            });
            return;
          }
          document.title = res.data.community.name
          // uni.setNavigationBarTitle({
          //   title: res.data.community.name,
          // });
          if(res.data.shangdaililv){
            this.shangdaililv = res.data.shangdaililv/100/12
          }
          this.detail = res.data.community
          this.correct_user = res.data.additionalMember
          this.imageCount = res.data.imageCount
          this.images = res.data.images.filter(item=>item.is_video===0)
          this.videos = res.data.images.filter(item=>item.is_video===1)
          let images =[],videos=[],vrs=[]
          if (this.images.length){
            images =this.images.map(image =>{
              return {
                type:"img",
                pic:image.pic,
              }
            })
          }
          if (this.videos.length){
            videos =this.videos.map(video =>{
              return {
                type:"video",
                pic:video.pic,
                cover:video.cover
              }
            })
          }

          if (res.data.community.vr){
            this.vrs = [res.data.community.vr]||[]
            vrs  =this.vrs.map(vr=>{
              return {
                type:"vr",
                path:vr.path,
                cover:vr.cover
              }
            })
          }
          this.focus = [...images,...videos,...vrs]
          if(this.images.length>0){
            this.cateActive = 'img'
          }else if(this.videos.length>0){
            this.cateActive = 'video'
          }else if(this.vrs.length>0){
            this.cateActive = 'vr'
          }
          this.sheshiList = res.data.facilities.map(item=>{
            item.text =item.title
            item.image = item.img
            return item
          })
          // this.sheshiList =[
          //   {
					// 	text:'地下停车场',
					// 	image:formatImg('/xiaoqu/<EMAIL>'),
					// 	// url:'/user/task_center'
					// },
          // {
					// 	text:'活动场地',
					// 	image:formatImg('/xiaoqu/<EMAIL>'),
					// 	// url:'/user/task_center'
					// },
          // {
					// 	text:'健身房',
					// 	image:formatImg('/xiaoqu/<EMAIL>'),
					// 	// url:'/user/task_center'
					// },
          // {
					// 	text:'书吧',
					// 	image:formatImg('/xiaoqu/<EMAIL>'),
					// 	// url:'/user/task_center'
					// },
          // {
					// 	text:'便利店',
					// 	image:formatImg('/xiaoqu/<EMAIL>'),
					// 	// url:'/user/task_center'
					// }
          // ]
          this.is_agent = res.data.is_agent
          this.news_list = res.data.news
          this.school_list = res.data.school
          // this.agent_list = res.data.member
          this.mountTitle = res.data.mountTitle
          this.agent_list = res.data.mountMembers
          this.ershou_list = res.data.saleList
          this.renting_list = res.data.rentingList
          this.estate_list = res.data.estateList
          this.house_price_list = res.data.communityList
          this.publists =res.data.publists.map(item=>{
            item.isAgent=item.agent_isvalid
            item.uid = item.mid
            if (item.medias.length){
              item.medias=item.medias.map(medias=>medias.path)
            }
            return item
          })
          console.log(this.publists);
          if (res.data.borrow){
            this.borrow=res.data.borrow
          }
          if ((!this.agent_list||(this.agent_list&&this.agent_list.length==0))&&!this.ershou_list||(this.ershou_list&&this.ershou_list.length==0)&&!this.renting_list||(this.renting_list&&this.renting_list.length==0)){
            this.top_navs=this.top_navs.filter(item=>item.id!="agent")
          }
          // 设置地图中心点
          if (res.data.community.lat > 0 && res.data.community.lng > 0) {
            this.hideMap = false;
            // this.getCovers("商业");
          }
          if (this.hideMap){
            this.top_navs=this.top_navs.filter(item=>item.id!="zhoubian")
          }
          // const map = uni.createMapContext("map", this);
          // map.includePoints({
          //   padding: [20, 20, 20, 20],
          // });
          // 设置地图中心点结束

          let center_cover = {
            latitude: res.data.community.lat,
            longitude: res.data.community.lng,
            width: 30,
            height: 30,
            iconPath: "/static/icon/center.png",
          };
          this.$set(this.mapData.covers, 0, center_cover);
          if(res.data.share){
						this.share = res.data.share
					}else{
            let pic = ""
            if(this.images.length>0){
              pic = this.images[0].pic
            }
						this.share = {
							title:this.detail.name+'最新房价',
							content:this.detail.address,
							pic: pic
						}
					}
					this.getWxConfig([
            'openLocation',
            'updateAppMessageShareData',
            'updateTimelineShareData'
          ],wx=>{
            this.wx = wx
          })
        },
        (err) => {
          console.log(err);
        }
      );
    },
    swiperChange(e) {
      this.swiperCurrent = e.detail.current
      this.cateActive = this.focus[this.swiperCurrent].type
      // if(this.swiperCurrent<this.images.length){
      //   this.cateActive = 'img'
      //   return
      // }
      // if(this.swiperCurrent>=this.images.length&&this.images.length&&this.videos.length>0){
      //   this.cateActive = 'video'
      // }
      // this.cateActive = this.focus[this.swiperCurrent].type
    },
    switchFocus(type) {
      this.cateActive = type
      switch (type) {
        case 'img':
          this.swiperCurrent = 0
          break;
        case 'video':
          this.swiperCurrent = this.images.length
          break;
        case 'vr':
           this.swiperCurrent =this.images.length + this.videos.length
          break;
        default:
          this.swiperCurrent = 0
      }
    },
    viewMap() {
      this.$navigateTo("/propertyData/map/map?id=" + this.id + "&type=" + 3 + "&lat=" + this.detail.lat + "&lng=" + this.detail.lng);
    },
    getCovers(e) {
      let params = {
        id: this.id,
        keywords: e?e.type:'',
        type: 3,
      };
      let api ='map/mapNearbyMatches.html';
      this.$ajax.get(
        api,
        params,
        (res) => {
          if (res.data.code != 1) {
            return;
          }
          if (!res.data.done&&this.moteNum <5 &&!e){
						this.moteNum ++
						this.getCovers(e)
						return 
					}
          let covers=[]
          res.data.matches.map(cover => {
            let icon,color,bgColor,title
            switch(cover.keyword)
						{
						case '商业':
							icon = '/static/icon/foot.png'
							bgColor= "#ffbabc"
							title="商"
							color="#fff"
							break
						case '教育':
							icon = '/static/icon/edu.png'
							title="教"
							bgColor="#34dec1"
							color="#fff"
							break
						case '医疗':
							icon = '/static/icon/yiliao.png'
							title="医"
							bgColor="#feb9bb"
							color="#fff"
							break
						case '交通':
							icon = '/static/icon/jiaotong.png'
							bgColor="#66d1fa"
							title ="交"
							color="#fff"
							break
						default:
							icon = '/static/icon/center.png'
						}
						if (cover.data&&cover.data.length) {
								cover.data.map(item=>{
                 let ob = {
                    width: 30,
                    height: 30,
                    iconPath: icon,
                    latitude: item.location.lat,
                    longitude: item.location.lng,
                    title: item.title,
                    address: item.address,
                    _distance: item._distance,
                    callout: {
                      content:  ((e && e.scale<=14) || !e)?title:item.title,
                      padding: 5,
											fontSize:10,
											boxShadow:'none',
											bgColor,
											color,
											borderRadius: 4,
											borderColor:bgColor,
											display:'ALWAYS'
                    },
                    distance: parseInt(item._distance)
                  }
                 	covers.push(ob)
									return item
                })
            }
            
						return cover
          })
          covers.push({
            latitude: this.detail.lat,
            longitude: this.detail.lng,
            width: 30,
            height: 30,
            iconPath: "/static/icon/center.png",
          });
          covers.push({
						latitude: this.oneKm.lat,
						id:"a"+1,
						longitude: this.oneKm.lon,
						width: -1,
						height:-1,
						label: {
							content:'1公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff0000",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5,
							borderColor:'#ffffff'
						},
						iconPath: '/static/icon/center.png'
					})
					covers.push({
						latitude: this.twoKm.lat,
						longitude: this.twoKm.lon,
						width: -1,
						height: -1,
						id:"a"+2,
						label: {
							content:'2公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff9c00",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
						},
						iconPath: '/static/icon/center.png'
					})
					covers.push({
						latitude: this.threeKm.lat,
						longitude: this.threeKm.lon,
						width: -1,
						height: -1,
						id:"a"+3,
						label: {
							content:'3公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#fee500",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
							},
						iconPath: '/static/icon/center.png'
					})
          this.mapData.covers = covers;
        },
        (err) => {}
      );
    },
    getLineData() {
      this.$ajax.get("build/communityPriceTrend.html", { id: this.id }, (res) => {
        if (res.data.code == 1) {
          let lineData = {};
          lineData.categories = res.data.months;
          lineData.series = [res.data.self]
          this.max_line = Math.max.apply(null,lineData.series[0].data)
          var min_line = Math.min.apply(null,lineData.series[0].data)
          let  length= res.data.self.data.length
          let lastData=0   // 最后一月数据
          let lastNextData=0 // 倒数第二月数据
          if (length>1){
            lastData =  res.data.self.data[res.data.self.data.length-1]
            lastNextData= res.data.self.data[res.data.self.data.length-2]
          }
          if (!isNaN(lastData)&&!isNaN(lastNextData)){
            let zoushi  =  lastData -lastNextData 
            this.zoushi =( zoushi>0?"上升":(zoushi<0?"下降":'持平'))
          }else {
            this.zoushi ='暂无数据'
          }
          this.showLine("canvasLine", lineData);
          this.showLine("canvasLineTop", lineData,{
            dataPointShapeType:"hollow",
            type:"line",
            axisLine:false,
            disableGridY:true,
            disabledY:true,
            legend:false,
            width:this.cWidth1,
            height:this.cHeight1,
            showArrow:false,
            showBox:false,
            splitLine:false
          });
          console.log(lineData);
        }
      });
    },
    showLine(canvasId, chartData,options) {
      let _self = this;
      var canvas = new uCharts({
        $this:_self,
        canvasId: canvasId,
        type: options&&options.type?options.type:"area",
        fontSize: 11,
        dataPointShapeType:options&&options.dataPointShapeType||"solid",
        background: "#FFFFFF",
        pixelRatio: _self.pixelRatio,
        categories: chartData.categories,
        animation: true,
        series: chartData.series,
        colors:["#ff6568"], //2版本以后改为color
        legend:{
          show:(options&&options.legend==false)?false:true,
        },
        extra: {
          area: {
              type: "curve",
              addLine: false
          },
        },
        tooltip:{
          // showArrow:true
          showBox:options&&options.showBox==false?false:true,
          showArrow:options&&options.showArrow==false?false:true,
          splitLine:options&&options.splitLine==false?false:true,
        },
        xAxis: {
          disableGrid: true,
          axisLine:options&&options.axisLine==false?false:true,
          fontColor:"#999999"
        },
        yAxis: {
          
          // format: function(val) {
          //   return val + "元";
          // },
          showTitle:true,
          data:[
            {
              "type": "value",
              "title": "元/m²",
              "max":(_self.max_line||12000)+2000,
              "min":2000,
              "unit": "",
              "format":''
            }
          ],
          disabled:options&&options.disabledY?true:false,
          disableGrid:options&&options.disableGridY?true:false
        },
        width:options&&options.width?options.width* _self.pixelRatio: _self.cWidth * _self.pixelRatio,
        height:options&&options.height?options.height* _self.pixelRatio: _self.cHeight * _self.pixelRatio,
        dataLabel: false,
        enableScroll: false,
        dataPointShape: true,
      });
      this.$set(this.opt,canvasId,canvas)
    },
    touchLine(e) {
      this.opt.canvasLine.showToolTip(e, {
        format: function(item, category) {
          return category + " " + item.name + ":" + item.data +"元/m";
        },
      });
    },
    showLocation(){
      if (this.wx) {
        this.wx.openLocation({
          latitude:parseFloat(this.detail.lat),
          longitude:parseFloat(this.detail.lng),
          name:this.detail.name,
          address:this.detail.address,
          scale: 18,
          success: function() {
            console.log('success')
          }
        })
      } else {
        uni.openLocation({
          latitude: this.baseInfo.lat,
          longitude: this.baseInfo.lng,
          name: this.baseInfo.name,
          address: this.baseInfo.address,
          success: function() {
            console.log('success')
          }
        })
      }
    },
    toAgentDetail(item){
      if(item.isAgent&&item.uid)
      this.$navigateTo(`/pages/agent/detail?id=${item.uid}`)
    },
    // 拨打电话
    handleTel(agent) {
      if (!uni.getStorageSync("token")) {
        this.$navigateTo("/user/login/login");
        // this.reload = true
        return;
      }
      if (this.istel == 0) {
        uni.makePhoneCall({
          phoneNumber: agent.tel,
        });
        this.$ajax.get(
          "im/callUpStatistics",
          {
            id: agent.mid||agent.uid||agent.id,
            tel: parseInt(agent.tel),
            type: 3,
          },
          (res) => {}
        );
        return;
      }
      var tel_type = "",call_scene_id=0,call_scene_type=0
      if(agent.isAgent){
        tel_type = '3'
      }else if(agent.isAdviser){
        tel_type = '2'
      }else  {
        call_scene_id = this.id
        call_scene_type =2
      }
      this.tel_params = {
        type: tel_type,
        callee_id:agent.adviser_id||agent.uid||agent.id,
        scene_type:7,
        scene_id:this.id,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      allTel(this.tel_params);
    },
    retrieveTel(){
      allTel(this.tel_params);
    },
    handleChat(agent) {
      if (!uni.getStorageSync("token")) {
        this.$navigateTo("/user/login/login");
        return;
      }
      if (this.is_open_im == 0) {
        // 判断是不是经纪人
        if(agent.isAgent || agent.is_agent){
          this.$navigateTo("/pages/agent/detail?id=" + (agent.mid||agent.uid||agent.id));
        }else{
          console.log("没开启聊天且不是经纪人,不跳转详情")
        }
        return;
      }
      getChatInfo(agent.mid||agent.uid||agent.id, 16);
    },
    toErshou(){
      this.$navigateTo(`/pages/ershou/ershou?cid=${this.id}`)
    },
    toRenting(){
      this.$navigateTo(`/pages/renting/renting?cid=${this.id}`)
    },
    toEstate(){
      this.$navigateTo(`/commercial/commercial`)
    },
    toSchool(){
      this.$navigateTo(`/school/list?cid=${this.id}`)
    },
    showSubForm(type, type2){
      if(type2 === 1){
        this.sub_title = '调价通知我'
        this.sub_content = '此小区房价有变化，我们会及时通知您'
      }
      if(type2 === 2){
        this.sub_title = '有房通知我'
        this.sub_content = '此小区有新房源时，我们会及时通知您'
      }
      this.sub_type = type
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e){  //提交报名
      e.from = "小区详情页"
      e.cid = this.id
      e.type = this.sub_type||''
      this.$ajax.post('build/signUp.html',e,res=>{
        uni.hideLoading()
					if(res.data.code === 1){
						if(this.sub_mode!==2||res.data.status === 3){ //提示报名成功
							uni.showToast({
								title:res.data.msg,
								icon:"none"
							})
							this.$refs.sub_form.closeSub()
						}else if(res.data.status === 1){
              uni.removeStorageSync('token')
							navigateTo('/user/login/login')
            }else{
              this.$refs.sub_form.getVerify()
						}
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
				})
    },
    handleCloseLogin(){
      if(this.$store.state.user_login_status===1){
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if(this.$store.state.user_login_status===2){
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    followHouse(){
      let url ='house/follow.html'
      if (this.detail.is_follow==1){
        url="house/cancelFollow.html"
      }
      this.$ajax.get(url,{cid:this.detail.id},res=>{
					if(res.data.code === 1){
						if (this.detail.is_follow==1){
              this.detail.is_follow = 0
            }else {
              this.detail.is_follow = 1
            }
					}
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
				})
    },
    onNewsChange(e){
      this.current_news_index = e.detail.current
    },
    toAdd(){
      if(!this.is_agent){
        uni.showToast({
          title: '目前仅开放经纪人可申请',
          icon: 'none'
        })
        return
      }
      this.$navigateTo(`/user/house_price/publication?cid=${this.id}`)
    },
    onClickVoice(src){
      // 判断点击的哪个语音
      var voice_playing_index = this.posts.findIndex(item=>item.attached&&item.attached.length>0&&item.attached[0].path == src)
      if(this.voice_playing_index === voice_playing_index){
        this.voice_playing_index = -1
      }else{
        this.voice_playing_index = voice_playing_index
      }
    },
    toAgentPublic(){
      this.$navigateTo("/pages/house_price/photos?cid="+this.detail.id)
    },
    toedit(type){
      this.$navigateTo("/propertyData/correct?id="+this.detail.id+"&type="+type)
    },
    toVr(item) {
      if(item.is_cover){
        this.$navigateTo('/vr/detail?cid=' + item.id)
      }else{
        this.$navigateTo(`/vr/list?cid=${this.id}`)
      }
    },
    toDetail(e) {
      if (!e.detail.id) {
        return
      }
      this.$store.state.tempData = e.detail
      if (e.detail.parentid == 1) {
        this.$navigateTo('/commercial/sale/detail?id=' + e.detail.id)
      }
      if (e.detail.parentid == 2) {
        this.$navigateTo('/commercial/rent/detail?id=' + e.detail.id)
      }
      if (e.detail.parentid == 3) {
        this.$navigateTo('/commercial/transfer/detail?id=' + e.detail.id)
      }
    },
    toOptionPath(e){
      if(!e.path&&e.event){
        this[e.event]()
        return
      }
      let real_path = e.path.replace('{build_id}', this.detail.id).replace('{lat}', this.detail.lat).replace('{lng}', this.detail.lng)
      this.$navigateTo(real_path)
    },
  },
  // #ifdef H5
  onNavigationBarButtonTap(option){
    if(option.index==0){
      uni.switchTab({
        url:'/'
      })
    }
  },
  // #endif
  onShareAppMessage(res) {
    if (res.from === "button") {
      // 来自页面内分享按钮
      console.log(res.target);
    }
    return {
      title: this.detail.name || "",
    };
  },
};
</script>

<style lang="scss" scoped>
.content {
  color: #333;
  background-color: #fff;
}
.pad24 {
  padding-top: 48rpx;
}
.flex-row {
  display: flex;
  flex-direction: row;
}

.top_nav{
  position: fixed;
  // #ifdef H5
  top: 42px;
  // #endif
  // #ifndef H5
  top: 0;
  // #endif
  z-index: 98;
  width: 100%;
  height: 90rpx;
  background-color: #fff;
  align-items: center;
  justify-content: space-between;
  .nav_item{
    flex: 1;
    height: 84rpx;
    // margin:0 20rpx;
    line-height: 84rpx;
    border-bottom: 4rpx solid #fff;
    text-align: center;
    transition: 0.26s;
    white-space: nowrap;
    &.active{
      color: $uni-color-primary;
      border-color: $uni-color-primary;
    }
  }
}

.vr-btn {
  position: absolute;
  right: 30upx;
  top: 20upx;
  padding: 2upx 20upx;
  border-radius: 8upx;
  background-color: #f65354;
  color: #fff;
  z-index: 99;
}

.focus-box {
  position: relative;
  swiper.banner {
    height: 75vw;
  }
  .swiper-item {
    height: 100%;
  }
  .swiper-item image {
    width: 100%;
    height: 100%;
  }
  .swiper-item image.video-icon {
    width: 16vw;
    height: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 50%;
    background-color: rgba(0, 0, 0,0);
  }
  .img-total {
    position: absolute;
    padding: 4upx 20upx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    border-radius: 20upx;
    right: 20upx;
    bottom: 20upx;
    color: #fff
  }
  .cate-box {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 24upx;
    display: flex;
    justify-content: center;
    .cate-list{
      border-radius: 6rpx;
      overflow: hidden;
      display: inline-block;
    }
  }
  .cate {
    display: inline-block;
    padding: 10upx 20upx;
    font-size: 22rpx;
    background-color: #fff;
    &.active {
      background: linear-gradient(45deg,#FD9EA3,#FB656A );
      color: #fff
    }
  }
}

.info {
  background-color: #fff;
  padding: 24rpx 48rpx 0 48rpx;
  .community-address{
    justify-content: space-between;
    align-items: center;
    color: #999;
  }
  .info_box{
    display: flex;
    justify-content: flex-start;
    justify-content: space-between;
    .title_info{
      flex: 1;
      .title{
        font-size: 40rpx;
        color: $uni-text-color;
      }
      .labels {
        margin-top: 10rpx;
        .label{
          background: #FFFFFF;
          border: 2rpx solid #D8D8D8;
          border-radius: 4rpx;
        }
      }
    }
    .praise {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      padding-top: 10rpx;
      .praise_name {
        color: #999999;
        font-size: 22rpx;
        margin-top: 15rpx;
        &.has_praise{
          color:#fb656a;
        }
      }
    }
  }
  // 小区简介
  .info_con {
    
    align-items: center;
    .info_content{
      background: #F8F8F8;
      border-radius: 8rpx;
      padding: 24rpx;
      flex:1;
      font-size: 28rpx;
      color: #333333;
      display: flex;
      align-items: center;
      .info_con{
        flex: 1;
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    .info_more{
      min-width: 80rpx;
      text-align: right;
      font-size: 14px;
      color: #FB656A;

    }
  }
  .info_card  {
    align-items: center;
    justify-content: space-between;
    .info_card_con{
      flex: 1;
      text-align: center;
      .info_card_con_top{
        .num{
          font-size: 34rpx;
          // font-weight: bold;
          color: #333;
          &.price{
            color: #fb656a;
          }
        }
        .unit{
          font-size: 24rpx;
          color: #333333;
        }
      }
      .info_card_con_bottom{
        margin-top: 20rpx;
        font-size: 22rpx;
        color: #999999;
      }
    }
  }
  .mini_chart{
    height: 200rpx;
    width: 100%;
    position: relative;
    border: 2rpx solid #D8D8D8;
    border-radius: 8rpx;
    align-items: center;
    .mi-charts{
      height: 200rpx;
      width: 100%;
    }
    .price_press{
      flex: 1;
      padding: 20rpx;
      display: flex;
      flex-direction: column;
      border-left: 2rpx solid #F8F8F8;
      align-items: center;
      justify-content: center;
      .press_name{
        .press_direction{
          font-size: 32rpx;
          color: #333333;
          font-weight: bold;
        }
      }
      .press_con {
        margin-top: 16rpx;
        font-size: 22rpx;
        color: #999999;
      }
    }
  }
  .house_infos{
      .label-title {
        align-items: center;
        justify-content: space-between;
        .tip_line {
          margin: 0 10rpx;
        }
      }
      .house_info{
        margin-bottom: 40rpx;
        .house_info_c{
          margin-bottom: 24rpx;
          .house_info_name{
            font-size: 28rpx;
            color: #999999;
            width:160rpx;
            margin-right: 60rpx;
          }
          .house_info_value{
            font-size: 28rpx;
            color: #333333;
            flex: 1;
            &.showline1{
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
}
.title-row {
  margin-bottom: 16rpx;
}
.title-row .title {
  font-size: 40upx;
  color: $uni-text-color;
  margin-right: $uni-spacing-row-base;
  line-height: 1;
}
// .tag-list {
//   align-items: center;
//   .tag {
//     margin-right: 10rpx;
//   }
// }

.card {
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx 0 rgba(0, 0, 0, 0.15);
  margin: 28rpx 0;
  box-sizing: border-box;
  overflow: hidden;
  .tab-box {
    justify-content: space-between;
    margin-bottom: 10rpx;
    background-image: url('https://images.tengfangyun.com/images/new_icon/fd_tab.png');
    background-size: 100%;
    background-position-y: -24rpx;
    &.right{
      background-image: url('https://images.tengfangyun.com/images/new_icon/fd_tab2.png');
    }
    .tab {
      flex: 1;
      text-align: center;
      padding: 20rpx;
      color: #999;
      &.active {
        // background-color: #fff3f3;
        color: #333;
      }
    }
  }
  .card-content {
    background-color: #fff;
    .top-data {
      display: flex;
      padding: 20rpx;
      border-bottom: 1rpx solid #f3f3f3;
      .top-left {
        flex: 1;
        text-align: center;
        position: relative;
        .label {
          margin-bottom: 15rpx;
          font-size: 22rpx;
          color: #999;
        }
        .price {
          font-size: 48rpx;
          font-weight: bold;
          color: #fb656a;
        }
        .unit {
          margin-left: 10rpx;
        }
      }
      .top-left::after {
        content: "";
        position: absolute;
        width: 1rpx;
        background-color: #dedede;
        top: 30rpx;
        bottom: 30rpx;
        right: 0;
      }
      .top-right {
        flex: 1;
        text-align: center;
        .label {
          margin-bottom: 15rpx;
          font-size: 22rpx;
          color: #999;
        }
        .price {
          font-size: 48rpx;
          font-weight: bold;
          color: #4cc7f6;
        }
        .unit {
          margin-left: 10rpx;
        }
      }
    }
    .bottom-data {
      display: flex;
      padding: 20rpx;
      .data-item {
        flex: 1;
        text-align: center;
        .data-num {
          color: #333;
          .num {
            font-weight: bold;
            line-height: 66rpx;
            font-size: 48rpx;
          }
          .up {
            margin-left: 10rpx;
            font-weight: bold;
            line-height: 66rpx;
            font-size: 48rpx;
            color: #f65354;
          }
          .down {
            margin-left: 10rpx;
            font-weight: bold;
            line-height: 66rpx;
            font-size: 48rpx;
            color: #179b16;
          }
          .flat {
            font-weight: bold;
            line-height: 66rpx;
            font-size: 42rpx;
          }
        }
        .label {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 22rpx;
          color: #999;
        }
      }
    }
  }
}

.news-box {
  background-color: #ffffff;
  border-left: 4rpx solid #fb656a;
  margin: 48rpx 0;
  .label {
    width: 60rpx;
    line-height: 1.2;
    margin-left: 10upx;
    font-weight: bold;
  }
  .swiper-box {
    position: relative;
    height: 60rpx;

    .swiper-mask {
      position: absolute;
      height: 100%;
      top: 0;
      left: 0;
      right: 0;
      z-index: 9;
    }
  }
  swiper {
    height: 100upx;
    background-color: #ffffff;
    view {
      padding: 10upx;
      height: 40upx;
      line-height: 40upx;
      font-size: $uni-font-size-blg;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 22rpx;
    }
  }
}

.action-btn-box {
  margin-top: 48rpx;
  .action-btn {
    padding: 20rpx;
    background-color: rgba(255, 101, 107, 0.05);
    color: #ff656b;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    text {
      margin-left: 8rpx;

    }
    .action_name {
      margin-left: 12rpx;
    }
    ~ .action-btn {
      margin-left: 16rpx;
    }
  }
}

.container {
  margin-top: 48rpx;
  background-color: #fff;
}
// .house-box{
//   padding: 0 48rpx;
// }
.label-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  &.title-row {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }
  .tip {
    font-size: 22rpx;
    color: #999;
  }
}

.school {
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 0;
  image {
    width: 88rpx;
    height: 88rpx;
    margin-right: 16rpx;
  }
  .school-info {
    flex: 1;
    overflow: hidden;
    .school-name {
      display: flex;
      align-items: center;
      margin-bottom: 15rpx;
      .text {
        flex: 1;
        font-size: 32rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .school-type {
        padding: 2rpx 14rpx;
        margin-left: 16rpx;
        font-size: 22rpx;
        color: #fff;
        background: linear-gradient(to right, #00caa7 0%, #69d4bb 100%);
        &minban {
          background: linear-gradient(to right, #fb656a 0%, #f7918f 100%);
        }
      }
    }
    .school-address {
      font-size: 22rpx;
      color: #999;
    }
  }
  .right {
    align-items: center;
    margin-left: 20rpx;
    text {
      color: #999;
    }
  }
}

// 经纪人排行
.agent {
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  .agent-info{
    flex: 1;
    overflow: hidden;
    .agent-address {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 22rpx;
    color: #999;
}
  }
  image {
    width: 88rpx;
    height: 88rpx;
    border-radius: 50%;
    margin-right: 10rpx;
  }
  .agent-name {
      display: flex;
      align-items: center;
      margin-bottom: 15rpx;
      text{
        display: inline-block;
        max-width: 200rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .text {
        // flex: 1;
        font-size: 32rpx;
        
      }
      .agent-type {
        padding: 4rpx 14rpx;
        line-height: 1;
        border-radius: 16rpx;
        margin-left: 16rpx;
        font-size: 22rpx;
        color: #fff;
         max-width: 116rpx;
        background: #d8d8d8;
      }
    }
  .right {
    align-items: center;
    text {
      color: #999;
    }
    .icon {
      width: 64rpx;
      height: 64rpx;
      ~ .icon {
        margin-left: 30rpx;
      }
    }
  }
}

.pad_48 {
  padding: 0 48rpx;
}

.label-tab {
  display: flex;
  align-items: flex-end;
  // padding: 0 48rpx;
  height:72rpx;
  .tab-item {
    font-size: 32rpx;
    margin-right: 40rpx;
    padding: 10rpx 0;
    &.active {
      font-size: 40rpx;
      color: #fb656a;
      position: relative;
      font-weight: bold;
    }
    &.active::after {
      content: "";
      position: absolute;
      width: 50rpx;
      height: 4rpx;
      background-color: #fb656a;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
    }
  }
}

.look-more {
  padding: 20rpx;
  text-align: center;
  // color: #999;
  background:rgba(255, 101, 107, 0.05);
  font-size: 32rpx;
  color: #FB656A;
  border-radius: 4px;
}

.right-line:after {
  top: $uni-spacing-row-sm;
  bottom: $uni-spacing-row-sm;
}
.info-item {
  text-align: center;
  padding: 18upx 0;
}
.info-item .tabel {
  color: #666;
}
.info-item .value {
  font-size: $uni-font-size-blg;
  font-weight: bold;
}
.info-item .value.liang {
  color: $uni-color-primary;
}
.info-item .value .unit {
  font-size: $uni-font-size-sm;
  font-weight: initial;
  color: #666;
}
.tip-title {
  font-size: 30upx;
}
.wring-color {
  color: $uni-color-warning;
}
.grey-color {
  color: $uni-text-color-grey;
}
.color-blur {
  color: #4db0fd;
}
.pan {
  line-height: 50upx;
  padding: $uni-spacing-col-base 28upx;
  background-color: #fff;
}
.desc-box {
  margin: $uni-spacing-row-base 0;
  padding: $uni-spacing-row-base $uni-font-size-base;
  background-color: #fff;
}
.desc-box .desc-title {
  font-size: 32upx;
  margin-bottom: 20upx;
}
.price-trend {
  margin: $uni-spacing-row-base 0;
  padding: $uni-spacing-row-base 0;
  background-color: #ffffff;
}
.price-trend .desc-title {
  font-size: 32upx;
  padding: 0 $uni-font-size-base;
  margin-bottom: 20upx;
}
.qiun-charts {
  width: 100%;
  height: 450rpx;
  background-color: #ffffff;
}
.charts {
  width: 100%;
  height: 500upx;
  background-color: #ffffff;
}



.qr-code {
  padding: 30upx;
  padding-bottom: 140upx;
  background-color: #fff;

  .title {
    padding: 20upx 0;
    text-align: center;
    font-size: 36upx;
    margin-bottom: 20upx;
  }

  .info {
    display: flex;
    flex-direction: column;
    padding: 10upx 0;

    .save-btn {
      text-align: center;
      padding: 12upx 20upx;
      border: 1upx solid #61c351;
      border-radius: 10upx;
    }

    .tip {
      flex: 1;
      padding: 20upx 0;
    }

    .color-red {
      color: #f44;
    }

    .copy-btn {
      text-align: center;
      padding: 12upx 20upx;
      background-color: #f3f3f3;
      border-radius: 10upx;
    }
  }

  .wechat-img {
    width: 50%;
    margin-right: 10upx;

    image {
      width: 100%;
      height: 100%
    }
  }
}

.add_entrance{
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  padding: 0 24rpx;
  border-radius: 8rpx;
  color: $uni-color-primary;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
}
// 小区纠错
.j-box{
  align-items: center;
  font-weight: normal;
  .tip_correct{
    margin-left: 14rpx;
  }
}
.user-box{
  padding: 24rpx;
  border: 2rpx solid rgba(216,216,216,1);
  border-radius: 8rpx;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  .a-img{
    align-items: center;
    image{
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
    }
    .name{
      font-size: 32rpx;
      margin-left: 16rpx;
      width: 200rpx;
    }
  }
  .right-c{
    align-items: center;
    .r-l{
      padding: 8rpx 11rpx;
      font-size: 22rpx;
      border-radius: 8rpx;
      background: rgba(251,101,106,0.05);
      color: #FB656A;
      margin-right: 48rpx;
    }
    .l-l{
      font-size: 22rpx;
      color: #999;
      border-left: 1rpx solid #999;
      padding-left: 24rpx;
    }
  }
}
.tips-house{
  align-items: center;
  margin-top: 24rpx;
  .c{
    margin-left: 10rpx;
    font-size:22rpx;
    color: #999999;
  }
}
.options1{
  padding: 24rpx 0 0;
  justify-content: space-between;
  >.item{
    // flex: 1;
    align-items: center;
    display: flex;
    flex-direction: column;
    .icon{
      width: 92rpx;
      height: 92rpx;
    }
    .text{
      margin-top: -10rpx;
      font-size: 24rpx;
      color: #141414;
    }
  }
}

</style>
