<template>
	<view class="page" :class="{pdb_120: (sharers_info.adviser_id&&is_open_adviser)||sharers_info.agent_id}">
		<view class="header" :style="{backgroundImage:'url('+bgcolor+')'}">
			<view class="head-info">
				<view class="title">预售榜</view>
				<view class="update-tip flex-box">
				<picker  :range="months" @change='getMonth'>{{siteName}} {{params.month||''}}</picker>
				<my-icon type="ic_down" size="14px" color="#fff"></my-icon>
				</view>
				<view class="update-tip" >
					<view class="dingyue flex-box" @click="showDingyuePop">
						<my-icon type="ic_jia" color="#fff" size="24rpx"></my-icon><text class="text">订阅</text>
					</view>
				</view>
			</view>
			<view class="share_icon" @click="showSharePop">
				<my-icon type="ic_fenxiang" size="32rpx" color="#fff"></my-icon>
			</view>
		</view>

		<view class="yushou-data-info">
			<view class="middle-bar">
				<view class="middle-bar-info flex-box">
					<view class="mid-bar flex-box flex-row" :class="{'active':params.sort==1}" @click="clickTab(1)">
						<view class="mid-bar-info" >总面积</view>
					</view>
					<view class="mid-bar flex-box flex-row" :class="{'active':params.sort==2}"  @click="clickTab(2)" v-if="isShowTaoshu">
						<!-- <image></image> -->
						<view class="mid-bar-info">住宅</view>
					</view>
					<view class=" mid-bar flex-box flex-row" :class="{'active':params.sort==3}"  @click="clickTab(3)" v-if="isShowTaoshu">
						<!-- <image></image> -->
						<view class="mid-bar-info" >非住宅</view>
					</view>
				</view>
			</view>
			<view class="top-20">
				<view class="lists">
					<view class="copy-text flex-box" @click="copyContent"  v-if ="textContent">
					<view class="text-content">
						{{textContent}}
					</view>
					<view class="copy flex-box">
						<my-icon type="copy" color="#fff" size="24rpx"></my-icon>
						<view class="copy-btn">复制</view>

					</view>
					</view>
					<view
						class="time-line"
						v-for="(item,index) in listData"
						:key="item.id"
						@click="toDetail(item.id)"
					>
						<view class="time">
							<view class="line-title">
								{{ item.buildname }}
							</view>
							<view class="data-card">
								<data-card :item="item" :idx='index' :red="params.sort"></data-card>
								
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="friend-tips top-20">
				{{tips}}
			</view>

		</view>
		
		<view class="sharers_info flex-box" v-if="(sharers_info.adviser_id&&is_open_adviser)||sharers_info.agent_id">
			<view class="img"><image :src="sharers_info.prelogo | imageFilter('w_240')" mode="widthFix"></image></view>
			<view class="info flex-1">
				<view class="name">{{sharers_info.cname}}</view>
				<view class="identity">{{sharers_info.identity===1?'置业顾问':'经纪人'}}</view>
			</view>
			<view class="btn_box flex-box">
				<view class="btn" @click="handleChat()">微聊</view>
				<view class="btn" v-if ="(sharers_info.adviser_id&&switch_adviser_tel) ||sharers_info.agent_id" @click="handleTel()">电话咨询</view>
			</view>
		</view>
		<share-pop ref="show_share_pop" @copyLink="copyLink" :showHaibao="false" @showCopywriting='showCopywriting'></share-pop>
		
		<dingyue ref="dingyue" @dingyue="dingyue" :type="type" @login="toLogin" @bindPhone="toBind"></dingyue>
        <my-popup ref="qrcode_popup" position="top">
			<view class="qrcode-box">
				<!-- #ifdef H5 -->
				<view class="img-box">
					<view class="title titles">数据报告将通过服务号发送</view>
					<view class="tip red">请关注{{siteName}}公众号</view>
					<image @longtap="saveQrcode" class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="title">长按保存图片</view>
						<view class="tip">相册选取，识别关注</view>
					</view>
				</view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view class="img-box">
					<view class="title titles">数据报告将通过服务号发送</view>
					<view class="tip red">请关注{{siteName}}公众号</view>
					<image class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="tip">长按识别二维码关注公众号</view>
					</view>
				</view>
				<!-- #endif -->
				<view class="icon-box" @click="$refs.qrcode_popup.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
		<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
		<enturstBtn v-if="sharers_info.agent_id||sharers_info.adviser_id" :to_user="sharers_info" @click="$refs.enturst_popup.show()" />
		<my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
			<enturstBox @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="showLoginPopup('为方便您及时接收消息通知，请输入手机号码')" :to_user="sharers_info" />
		</my-popup>
		<!-- 登录弹窗 -->
        <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
		<chat-tip></chat-tip>
		<tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
	</view>
</template>

<script>
import dataCard from "../components/dataCard";
import myIcon from "../components/myIcon";
import { config } from "../common/config";
// import { uniLoadMore } from "@dcloudio/uni-ui";
// #ifndef MP-WEIXIN
import loginPopup from '../components/loginPopup'
// #endif
import sharePop from '../components/sharePop'
import getChatInfo from '../common/get_chat_info'
import allTel from '../common/all_tel.js'
import myPopup from "../components/myPopup.vue"
import dingyue from "../components/dingyue.vue"
import shareTip from "../components/shareTip.vue"
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
// import timeLine from '@/components/timeLine'
export default {
	data() {
		return {
			listData: [],
			tip: "正在加载...",
			params: {
				sort: 1, // 1: 面积 2: 住宅 3: 非住宅
				month:''
			},
			currentIndex: 0,
			bgcolor:"",
			// 分享者信息
			sharers_info: {},
			// 当前用户的信息
			current_user_info: {},
			login_tip: '',
			months:[],
			defaultMonth:'',
			tips:'',
			show_share_tip:false,
			qrcode:'',
			link:'',
			siteCity:'',
			shareId:'',
			shareType:'',
			textContent:'',
			type:'dingyue',
			tel_res: {},
			show_tel_pop: false,
		};
	},
	components: {
		// uniLoadMore,
		dataCard,
		sharePop,
		myIcon,
		// #ifndef MP-WEIXIN
		loginPopup,
		// #endif
		myPopup,
		dingyue,
		shareTip,
		enturstBtn,
		enturstBox
		// timeLine
	},
	onLoad(options) {
		// 如果是分享链接进来的
		if (options.shareId && (options.type||options.shareType)) {
				this.shareId = options.shareId
				this.shareType = options.type||options.shareType
				this.share_time =options.f_time||''
		}
		if(options.sort){
			this.params.sort = parseInt(options.sort)
		}
		if(options.month){
			this.params.month = options.month
		}
		this.bgcolor=config.imgDomain+'/images/new_icon/record/<EMAIL>'
		
		this.getStatisData();
		this.getData();
		uni.$on('getDataAgain',()=>{
			this.type='dingyue'
			this.getStatisData()
		})
	},
	onShow(){
		if(this.$store.state.updatePageData){
				this.getStatisData()
				this.$store.state.updatePageData = false
			}
	},	
	onUnload(){
		uni.$off('getDataAgain')
	},
	computed: {
		siteName() {
			return this.$store.state.siteName;
		},
		is_open_adviser() { //是否开启置业顾问功能
			return this.$store.state.im.adviser
		},
		is_open_im() { // 是否开启聊天功能
			return this.$store.state.im.ischat
		},
		isShowTaoshu() { // 是否显示预售住宅非住宅套数
			return this.$store.state.isShowTaoshu
		},
		switch_adviser_tel(){
			return this.$store.state.switch_adviser_tel
		}


	},
	methods: {
		getData() {
			this.$ajax.get("build/booking.html", this.params, (res) => {
				
				this.listData =[]
				this.months=res.data.months
				if (!this.params.month){
					this.params.month=this.months[0]
				}
				this.textContent=''
				this.listData=[]
				if (res.data.code == 1) {
					this.listData = res.data.list;
					this.tips=res.data.declare	
					this.getText()
				}else {
					uni.showToast({
						title:"暂无数据",
						icon:"none",
					})
				}
			this.getShare() 
				
			});
		},
		// #ifdef H5 || MP-BAIDU
		setSeo(){
			let siteCity=this.siteCity||''
			this.seo.title=`${this.params.month}月${siteCity}商品房预售许可证数据【预售榜单】`
		},
		//#endif
		getStatisData() {
			let params = {};
			if(this.shareId&&this.shareType){
				params = {
					sid: this.shareId,
					sharetype: this.shareType
				}
			}
      this.type="dingyue"
			params.forward_time=this.share_time ||''
			this.$ajax.get("build/bookingStatistics", params, (res) => {
				if (res.data.siteCity){
					this.siteCity=res.data.siteCity
				}
				if (res.data.shareUser) { //当前用户信息
						this.current_user_info = res.data.shareUser
						if(res.data.shareUser.adviser_id){
								this.current_user_info.identity = 1
								this.current_user_info.identity_id = res.data.shareUser.adviser_id
						}else if(res.data.shareUser.agent_id){
								this.current_user_info.identity = 2
								this.current_user_info.identity_id = res.data.shareUser.agent_id
						}
				}
				if (res.data.share_user) { //分享者信息
						this.sharers_info = res.data.share_user
						if(res.data.share_user.adviser_id){
								this.sharers_info.identity = 1
						}else if(res.data.share_user.agent_id){
								this.sharers_info.identity = 2
						}
				}
					// 获取登录状态
					this.$ajax.get('member/checkUserStatus', {}, res => {
						if (res.data.code !== 1) {
							this.$store.state.user_login_status = res.data.status
							if (this.$store.state.user_login_status==1){
								this.type="denglu"
								uni.setStorageSync('backUrl', window.location.href)
								this.$store.state.updatePageData=true
								this.showDingyuePop()
							}else if (this.$store.state.user_login_status==2){
								this.type='bangshouji'
								this.$store.state.updatePageData=true
								this.showDingyuePop()
							}
						}
					})
				if (res.data.siteCity){
					this.siteCity=res.data.siteCity
				}
				if (res.data.share) {
					this.share = res.data.share;
					this.getShare()	
				}
				
			});
			
		},
		toLogin(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/login/login")
		},
		toBind(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/bind_phone/bind_phone")
		},

		getText(){
			this.getShortLink()
			this.$ajax.get("build/briefing", {month:this.params.month}, (res) => {
				console.log(res);
				if (res.data.code ==1){
					this.textContent=res.data.content
				}
				
			})
		},
		getShare(){
			let type=''
			if (this.params.sort==1){
				type="面积"
			}else if (this.params.sort==2){
				type="住宅"
			}else if (this.params.sort==3){
				type="非住宅"
			}
			if (!this.share){
				this.share={
					title:''
				}
			}
			
			this.share.title=`${this.params.month}月${this.siteCity}商品房预售许可证数据【预售榜单】`,
			this.share.link=this.getShareLink()
			this.getWxConfig()
		},
		getMonth(e){
			this.params.month=this.months[e.detail.value]
			this.getData()
			this.getShare()
		},
		clickTab(sort) {
			this.params.sort = sort;
			this.getData();
			this.getShare()
		},
		toDetail(id) {
			this.$navigateTo(`/pages/yushou/detail?id=${id}`);
		},
		showSharePop(){
			this.getShortLink()
			this.$refs.show_share_pop.show()
		},
		getShortLink(){
            this.link=this.getShareLink()
            this.$ajax.get('build/shortUrl.html', {page_url:this.link }, res=>{
                if(res.data.code === 1){
					this.link = res.data.short_url
                }
            })
        },
		// 获取分享链接
		getShareLink(){
				let link = ''
				let time =parseInt(+new Date()/1000)
				link=`${window.location.origin}${window.location.pathname}?sort=${this.params.sort}&month=${this.params.month}&shareId=${this.shareId}&type=${this.shareType}&f_time=${time}`

				if (this.current_user_info.identity) { //当前用户是 置业顾问或者经纪人  
						link = `${window.location.origin}${window.location.pathname}?shareId=${this.current_user_info.identity_id}&type=${this.current_user_info.identity}&sort=${this.params.sort}&month=${this.params.month}&f_time=${time}`
				}
				return link
		},
		// 复制分享链接
		copyLink(){
			this.show_share_tip=true
		},
		// 复制分享内容
		showCopywriting(){
			console.log("复制内容")
			const content = `【我正在看】${this.siteName}预售证榜单\n【链接】${this.link}`
			this.copyText(content, ()=>{
					uni.showToast({
					title: '复制成功,去发送给好友吧',
					icon: 'none'
					})
			})
		},
		copyContent(){
			const content = `${this.textContent}\n【链接】${this.link}`
			this.copyText(content, ()=>{
					uni.showToast({
						title: '复制成功,去发送给好友吧',
						icon: 'none'
					})
			})
		},
		// 复制内容
		copyText(cont, callback) {
			let oInput = document.createElement('textarea')
			oInput.value = cont
			document.body.appendChild(oInput)
			oInput.select() // 选择对象;
			oInput.setSelectionRange(0, oInput.value.length);
			document.execCommand('Copy') // 执行浏览器复制命令
			oInput.blur()
			oInput.remove()
			if(callback) callback()
		},
		// 发起聊天
		handleChat(){
			if(!this.is_open_im){
				if (this.sharers_info.identity == 1) { //置业顾问
					this.$navigateTo('/pages/consultant/detail?id=' + this.sharers_info.adviser_id)
				} else if (this.sharers_info.identity == 2) {
					this.$navigateTo('/pages/agent/detail?id=' + this.sharers_info.agent_id)
				}
				return
			}
			// #ifndef MP-WEIXIN
			this.checkLogin('当前操作需要绑定手机号，请输入您的手机号', ()=>{
				getChatInfo(this.sharers_info.mid, 21)
			})
			// #endif
		},
		// 拨打电话
		handleTel(){
			this.tel_params = {
				type: this.sharers_info.identity == 1?'2':'3',
				callee_id: this.sharers_info.mid,
				scene_type:this.sharers_info.identity == 1?'2':'3',
				scene_id:this.sharers_info.mid,
				success: (res)=>{
					this.tel_res = res.data
					this.show_tel_pop = true
				}
			}
			// #ifdef MP-WEIXIN
			allTel(this.tel_params)
			// #endif
			// #ifndef MP-WEIXIN
			this.tel_params.intercept_login = true
			this.tel_params.fail = (res)=>{
				if(res.data.code === -1){
						this.$store.state.user_login_status = 1
						uni.removeStorageSync('token')
						this.$navigateTo('/user/login/login')
					}
					if(res.data.code === 2){
						this.$store.state.user_login_status = 2
						this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
						this.$refs.login_popup.showPopup()
					}
			}
			allTel(this.tel_params)
			// #endif
		},
		retrieveTel(){
				allTel(this.tel_params)
		},
		// 检测登录状态
		checkLogin(tip, callback) {
			this.$ajax.get('member/checkUserStatus', {}, res => {
				if (res.data.code === 1) {
					callback&&callback()
				} else {
					this.$store.state.user_login_status = res.data.status
					this.login_tip = tip
					this.$refs.login_popup.showPopup()
				}
			})
		},
		showLoginPopup(tip){
			this.login_tip = tip
			this.$refs.login_popup.showPopup()
		},
		handleCloseLogin() {
            if (this.$store.state.user_login_status === 1) {
                uni.removeStorageSync('token')
                this.$navigateTo('/user/login/login')
            }
            if(this.$store.state.user_login_status===2){
                this.$navigateTo('/user/bind_phone/bind_phone')
            }
        },
        onLoginSuccess(res){
            this.$store.state.user_login_status = 3
            if(this.weituo_is_show){
                console.log("登录成功后继续执行委托接口")
                this.$refs.enturst_box.handleEnturst()
            }
        },
		// 订阅
		showDingyuePop(){
			this.$refs.dingyue.showPopup()
		},
		 // 保存二维码
        saveQrcode(){
            uni.request({
                url:this.qrcode,
                method:'GET',
                responseType: 'arraybuffer',
                success:(res)=>{
                    let base64 = uni.arrayBufferToBase64(res);
                    const userImageBase64 = 'data:image/jpg;base64,' + base64;
                    uni.saveImageToPhotosAlbum({
                        filePath: userImageBase64,
                        success: result => {
                            uni.showToast({
                                title: '保存成功，在微信从相册中选取识别吧',
                                icon: 'none',
                                duration: 4000
                            })
                        },
                        fail: err => {
                            console.log(err)
                            uni.showToast({
                                title: '保存失败，请重试',
                                icon: 'none'
                            })
                        }
                    })
                }
            }); 
        },
        dingyue(){
            this.$ajax.get("build/subscribeBooking",{type:1},res=>{
                if (res.data.code ==-1){
					uni.setStorageSync('backUrl', window.location.href)
					this.$store.state.updatePageData=true
					this.$refs.dingyue.hide();
					this.$navigateTo("/user/login/login")
                }else if (res.data.code ==2){
					this.type='bangshouji'
					this.$store.state.updatePageData=true
					this.showDingyuePop()
                }else if(res.data.code ==1){
                    uni.showToast({
                        title:res.data.msg,
                        icon:"success"
                    })
                    setTimeout(() => {
                        this.$refs.dingyue.hide()
                    },500)
                }else if (res.data.code ==0){  //订阅失败
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                    if (res.data.gzhewm){
                        this.qrcode=res.data.gzhewm
                        setTimeout(() => {
                            this.$refs.qrcode_popup.show()
                        }, 500);
                    }
                    this.$refs.dingyue.hide()
                }
            },err=>{},{disableAutoHandle:true})

        },
	},
	onShareAppMessage() {
		if (this.share) {
			return {
				title: this.share.title || "",
				content: this.share.content || "",
				imageUrl: this.share.pic ? formatImg(this.share.pic, "w_6401") : "",
			};
		}
	}
};
</script>

<style scoped lang="scss">
.page {
	background: #fff;
}
.pdb_120{
  padding-bottom: 120rpx;
}
.header {
	width: 100%;
	height:400rpx;
	background-image: linear-gradient(0deg, #f7918f 0%, #fb656a 100%);
	display: flex;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	padding: 48rpx 48rpx 0;
	box-sizing: border-box;
	position: relative;
	.head-info {
		width: 100%;
		.update-tip{
        // margin-top: 32upx;
        color: #fff;
        text-align: center;
        align-items: center;
        flex: 1;
        .data-infos{
            align-items: center;
        }
        .line{
            width: 80rpx;
            height: 1rpx;
            background-color: #fff;
        }
        .site-name{
            margin: 0 10rpx;
        }
    }
	}
	.title {
		font-size: 80rpx;
		color: #FFFFFF;
	}
	.update-tip {
		margin-top: 16upx;
		color: #fff;
	}
}
.yushou-data-info{
	position: relative;
	top: -20px;
	background: #fff;
	border-radius: 48rpx 48rpx 0 0;
}
.fengexian {
	position: relative;
}
.fengexian:after {
	content: "";
	position: absolute;
	top: 20%;
	bottom: 20%;
	right: 0;
	width: 1px;
	-webkit-transform: scaleX(0.5);
	transform: scaleX(0.5);
	background-color: $uni-border-color;
}
.data-box {
	position: absolute;
	background: #ffffff;
	padding: 24rpx 0;
	bottom: -60rpx;
	left: 48rpx;
	right: 48rpx;
	border: 2rpx solid #d8d8d8;
	box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.08);
	border-radius: 8px;
	// transform: translateY(-50%);
	.data-title {
		margin-bottom: 16upx;
		font-size: 22rpx;
		color: #999;
	}
	.data-data {
		font-size: 22rpx;
		// font-weight: bold;
		color: #999;
	}
	.data-datas {
		font-size: 32rpx;
		// font-weight: bold;
		color: #333;
	}
	.red {
		color: $uni-color-primary;
	}
}
.middle-bar {
	padding: 24rpx 48rpx ;
	.middle-bar-info{
		background: #f8f8f8;
		border-radius: 44rpx;

	}
	.mid-bar {
		flex: 1;
		justify-content: center;
		align-items: center;
		padding: 20rpx 0;
		&.active{
			background-image: linear-gradient(57deg, #FF5500 27%, #FFA402 80%);
			box-shadow: 0 2px 6px 0 rgba(255,145,1,0.50);
			border-radius: 22px;
			.mid-bar-info{
				color: #fff;
			}
		}
	}
}
.tab-bars {
	padding: 0 48rpx;
	.cate {
		align-items: flex-end;
		max-width: 100%;
	}
	text {
		color: #333;
		transition: 0.2s;
		padding-right: 20rpx;
		&.active {
			font-weight: bold;
			font-size: 40rpx;
		}
		& ~ text {
			padding-left: 20rpx;
		}
	}
}

.lists {
	padding: 0 48rpx;
	overflow: hidden;
	.copy-text{
		flex-direction: column;
		.text-content{
		background: rgba(251,101,106,0.08);
		border-radius: 8rpx;
		font-size: 28rpx;
		padding: 24rpx;
		color: #333333;

	}
	.copy{
		background-image: linear-gradient(125deg, #FF5500 0%, #FFA402 100%);
		box-shadow: 0 2px 6px 0 rgba(255,145,1,0.50);
		border-radius: 2px;
		padding: 4rpx 8rpx;
		align-items: center;
		margin-left: auto;
		margin-top: 8rpx;
		margin-bottom: 50rpx;
		.copy-btn{
			margin-left: 4rpx;
			font-size: 22rpx;
			color: #FFFFFF;
		}
	}
	}
	
	.time-line {
		// padding:0 24rpx ;
		padding-left: 24rpx;
		.time {
			position: relative;
			&:before {
				content: "";
				position: absolute;
				width: 2rpx;
				height: 100%;
				background: #d8d8d8;
				top: 10rpx;
				left: -20rpx;
			}
			.line-title {
				position: relative;
				color: #333;
				font-size: 22rpx;
				margin-bottom: 16rpx;
				&:before {
					content: "";
					position: absolute;
					background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
					width: 20rpx;
					height: 20rpx;
					top: 50%;
					transform: translateY(-50%);
					left: -30rpx;
					border-radius: 50%;
				}
			}
		}
		.data-card {
			padding-bottom: 24rpx;
			position: relative;
			.jiangbei{
				position: absolute;
				top: 10rpx;
				right: 40rpx;
				height: 50rpx;
				width: 50rpx;
				image{
					width: 100%;
				}
			}
		}
	}
}

.share_icon{
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 24rpx;
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		background-color: rgba(0,0,0, 0.5);
		position: absolute;
		right: 48rpx;
}

// 分享者信息
.sharers_info{
    position: fixed;
    width: 100%;
    height: 120rpx;
    bottom: 0;
    padding: 0 48rpx;
    box-sizing: border-box;
    align-items: center;
    background-color: #fff;
	z-index:90;
    .img{
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 16rpx;
		overflow: hidden;
		image{
			width: 100%;
			height: 100%;
		}
    }
    .info{
        overflow: hidden;
        .name{
            margin-bottom: 16rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .identity{
            font-size: 24rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: #999;
        }
    }
    .btn_box{
        .btn{
            margin-left: 20rpx;
            padding: 10rpx 34rpx;
            font-size: 26rpx;
            color: $uni-color-primary;
            border: 1px solid $uni-color-primary;
            border-radius: 3px;
            box-shadow: 0 2px 4px 0 rgba(251,101,106,.1);
        }
    }
}
.friend-tips{
	color: #999;
	line-height: 1.5;
	padding: 0 48rpx;
}
.dingyue{
	align-items: center;
	justify-content: flex-start;
	border: 2rpx solid #fff;
	border-radius: 8rpx;
	padding: 4rpx 8rpx;
	width: 100rpx;

	.text{
		margin-left: 10rpx;
	}
}
//公众号二维码弹框
.qrcode-box{
	position: relative;
	margin-top: 15vh;
	.img-box{
		width: 584rpx;
		padding: 12rpx;
		margin: auto;
		background-color: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		.title{
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
			color: #333;
			&.titles{
				margin-top: 36rpx;
			}
		}
		.tip{
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			color: #666;
			&.red{
				padding-bottom: 8rpx;
				color: #f00;
			}
		}
	}
	.qrcode{
		width: 560rpx;
		height: 560rpx;
	}
	.icon-box{
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
}
</style>
