<template>
	<view id="add">
    <view class="tips">提示：请正确选择分类，分类错误会导致信息下架。</view>
		<view class="block">
			<view class="list_item" v-for="(item,index) in addList" :key="index" @click="toDetail(item)">
				<view class="info">
					<view class="title">
            <text>{{item.title}}</text>
            <image v-if="item.badge" :src="item.badge" mode="aspectFit" class="title-icon"></image>
          </view>
					<view class="sub_title">{{item.note}}</view>
				</view>
        <image class="thumb" :src="item.thumb | imageFilter('m_220')"></image>
				<!-- <my-icon type="ic_into" size="30rpx" color="#bbb"></my-icon> -->
			</view>
		</view>
		<block v-if="login_status===1">
			<view class="login_tip">
				<image class="icon" mode="widthFix" :src="'/images/new_icon/login_tip_icon.png' | imageFilter('m_320')"></image>
				<text class="title">还没有登录</text>
				<text class="tip">登录后查看发布入口</text>
				<view class="btn" @click="$navigateTo('/user/login/login')">立即登录</view>
			</view>
		</block>
    <my-popup ref="cascader_popup" @hide="clearCascaderData" :bottom="cascader_bottom">
      <my-cascader ref="cascader" :data_list="children_cates" @onselect="onCascaderChange"></my-cascader>
    </my-popup>
	</view>
</template>

<script>
	import myIcon from "../../components/myIcon"
  import myPopup from '@/components/myPopup'
  import myCascader from '@/components/myCascader'
	import {showModal} from "../../common/index.js"
	import {mapMutations} from 'vuex'
	export default {
		components:{
			myIcon,
      myPopup,
      myCascader
		},
		data() {
			return {
				addList:[],
        children_cates: [],
				cascader_bottom: 0
			};
		},
		computed:{
			login_status(){
				return this.$store.state.user_login_status
			}
		},
		onLoad(){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			let addList = uni.getStorageSync('addList')
			if(addList){
				this.addList = addList
			}
			this.getList()
			this.getUser()
			// #ifdef H5
			this.cascader_bottom = '45px'
			// #endif
			uni.$on('getDataAgain', ()=>{
				this.getList()
				this.getUser()
			})
		},
		methods:{
			...mapMutations(['getUserInfo']),
			getList(){
				this.$ajax.get('house/houseCategoryNew.html',{},(res)=>{
					if(res.data.code == 1){
            // this.estate_cates = res.data.estate_cates
						this.addList = res.data.list
						uni.setStorage({
							key: "addList",
							data: this.addList
						})
					}
				},(err)=>{
					
				})
			},
			getUser(){
				this.$ajax.get('member/index.html',{}, res=>{
					if(res.data.user){
						this.getUserInfo(res.data.user)
					}
				}, err=>{
					console.log(err)
				}, {disableAutoHandle: true})
			},
			toDetail(item){
				if(this.$store.state.user_info.levelid>1){
					uni.showLoading({
						title: "加载中..."
					})
					this.$ajax.get('member/shopSetting.html',{}, res=>{
						uni.hideLoading()
						if(!res.data.verfiy_info_complete){
							this.beforeToAdd(item)
							return
						}
						let {cname, tname, tel, introduce, address, prelogo, wechat_img} = res.data.user
						if(cname&&tname&&tel&&introduce&&address&&prelogo&&prelogo.length>0&&wechat_img&&wechat_img.length>0){
							this.beforeToAdd(item)
						}else{
							showModal({
								content:'发布信息需要先完善店铺资料',
								confirmText:"去完善",
								confirm:()=>{
									this.$navigateTo('/user/agent_info')
								}
							})
						}
					},err=>{
						uni.hideLoading()
					})
				}else{
					this.beforeToAdd(item)
				}
			},
			beforeToAdd(item){
				var replace_key = function(arr){
					arr.forEach(e => {
						e.name = e.title
						e.value = e.id
						if(e.children&&e.children.length>0){
							replace_key(e.children)
						}
					});
				}
				if (item.children&&item.children.length>0) {
					replace_key(item.children)
					this.children_cates = item.children
					this.$refs.cascader_popup.show()
				} else {
					this.$navigateTo(item.url)
				}
			},
				/**
			 * 执行升级个人永久vip
			 */
			upgrade(){
				this.$ajax.post('member/upGradeVip',{},res=>{
					if(res.data.code === 1){
						uni.showToast({
							title:'升级成功'
						})
						setTimeout(()=>{
							this.$navigateTo(this.add_url)
						},2000)
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:'none'
						})
					}
				})
			},
      onCascaderChange(item) {
				this.$navigateTo(item[item.length-1]._current.url)
				this.$refs.cascader_popup.hide()
			},
			clearCascaderData(){
				this.$refs.cascader.clearData()
			}
		},
		onTabItemTap(e){
			uni.$emit('onTabItemTap', e)
		}
	}
</script>

<style>
page {
  background: #fff;
}
</style>
<style lang="scss">
	#add {
		background-color: #fff;
	}
	#add .uni-list-item__icon-img{
		width: 90upx;
		height: 90upx;
	}
	#add .uni-list-item__icon {
		margin-right: 24upx;
	}

  .tips {
    font-size: 28rpx;
    padding: 28rpx 48rpx;
    background-color: #ffeeef;
	  color: $uni-color-primary;
  }

	.block{
    margin-top: 32rpx;
		margin-bottom: 16rpx;
		padding: 0 48rpx;
		background-color: #ffff;
		view{
			line-height: 1;
		}
	}
	.list_item{
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 28rpx 48rpx;
    background: rgb(249,249,249);
    margin-bottom: 24rpx;
    border-radius: 10rpx;
		.thumb{
			width: 96rpx;
			height: 96rpx;
		}
		.info{
			flex: 1;
      margin-right: 16rpx;
			.title{
				font-size: 32rpx;
				margin-bottom: 16rpx;
        color: #262626;
        position: relative;
        width: fit-content;
        .title-icon {
          position: absolute;
          right: -44rpx;
          top: -8rpx;
          width: 36rpx;
          height: 28rpx;
        }
			}
			.sub_title{
				font-size: 24rpx;
				color: #9f9f9f;
			}
		}
	}
   ::v-deep .popup-box.show {
    z-index: 999;
  }

	.login_tip{
    display:flex;
    flex-direction: column;
    align-items: center;
    justify-content:center;
    position: fixed;
    padding-bottom: 200rpx;
    top: 0;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    .icon{
        width: 300rpx;
        height: 150rpx;
    }
    .title{
        margin-top: 24rpx;
        margin-bottom: 16rpx;
        font-size: 32rpx;
        font-weight: bold;
    }
    .tip{
        font-size: 24rpx;
        color: #999;
    }
    .btn{
        margin-top: 88rpx;
        line-height: 88rpx;
        padding: 0 52rpx;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: #fff;
        background: #FB656A;
        box-shadow: 0 4px 15px 0 rgba(251,101,106,0.40);
    }
	}
</style>
