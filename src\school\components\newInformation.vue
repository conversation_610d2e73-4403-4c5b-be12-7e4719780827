<template>
  <view
    class="content"
    hover-class="on-hover"
    :hover-start-time="60"
    :hover-stay-time="120"
    @click="toDetail()"
  >
    <view class="bottom-line">
      <view v-if="showVideo" class="ctn_box ctn_video">
        <text class="title">{{ item.title }}</text>
        <view class="ctn_content">
          {{ item.sub_title }}
        </view>
        <view class="video_box">
          <image :src="item.video_path | imgUrl('w_8601')" mode="aspectFill"></image>
          <view class="icon_box">
            <myIcon type="bofang" color="#fff" size="84rpx"></myIcon>
          </view>
        </view>
        <view class="watchAndtime">
          <view class="watch" v-if="item.click_count">
            <myIcon type="yanjing" color="#999"></myIcon>
            <text class="nowWatch">{{ item.click_count }}在看</text>
          </view>
          <view class="time">{{ item.utime }}</view>
        </view>
      </view>
      <view v-else-if="showImage" class="ctn_box ctn_img">
        <view class="ctn_img_l">
          <text class="title">{{ item.title }}</text>
          <view class="watchAndtime">
            <view class="watch" v-if="item.click_count">
              <myIcon type="yanjing" color="#999"></myIcon>
              <text class="nowWatch">{{ item.click_count }}在看</text>
            </view>
            <view class="time">{{ item.utime }}</view>
          </view>
        </view>
        <view class="img_box">
          <image
            :src="item.pic | imgUrl('w_240')"
            mode="aspectFill"
          ></image>
        </view>
      </view>
      <view v-else class="ctn_box">
        <text class="title">{{ item.title }}</text>
        <view class="ctn_content">
          {{ item.sub_title }}
        </view>
        <view class="watchAndtime">
          <view class="watch" v-if="item.click_count">
            <myIcon type="yanjing" color="#999"></myIcon>
            <text class="nowWatch">{{ item.click_count }}在看</text>
          </view>
          <view class="time">{{ item.utime }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from '@/components/icon'
import {
  navigateTo,
  formatImg
} from '../../common/index.js'
export default {
  props: {
    item: Object,
    index: Number,
    showImage: Boolean,
    showVideo: Boolean
  },
  components: {
    myIcon
  },
  filters: {
    imgUrl(val,param) {
      if (!val) {
        return ""
      }
      return formatImg(val, param)
    }
  },
  methods:{
    toDetail(){
      navigateTo(this.item.link || `/pages/news/detail?id=${this.item.id}`)
    }
  }
}
</script>

<style lang="scss">
view{
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  &.row {
    flex-direction: row;
  }
}
.content_txt {
  margin-bottom: 40rpx;
}
.ctn_box {
  width: 650rpx;
  margin: 40rpx auto;
}
.ctn_img {
  height: 148rpx;
  flex-direction: row;
  justify-content: space-between;
}
.ctn_img_l {
  flex: 1;
  justify-content: space-between;
  margin-right: 20rpx;
}
.img_box {
  width: 180rpx;
  height: 148rpx;
}
.img_box > image {
  width: 100%;
  height: 100%;
}
.video_box {
  width: 650rpx;
  height: 280rpx;
  margin-top: 20rpx;
  position: relative;
}
.video_box > image {
  width: 100%;
  height: 100%;
}
.icon_box {
  position: absolute;
  top: 35%;
  left: 45%;
}
.title {
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  font-size: 30rpx;
  color: #222;
}
.ctn_content {
  margin-top: 20rpx;
  text-indent: 2em;
  font-size: 24rpx;
  color: #999;
  overflow: hidden; /*超出隐藏*/
  display: -webkit-box; /*设置为弹性盒子*/
  -webkit-line-clamp: 2; /*最多显示x行*/
  text-overflow: ellipsis; /*超出显示为省略号*/
  -webkit-box-orient: vertical;
  line-height: 40rpx;
}
.watch {
  flex-direction: row;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}
.time {
  font-size: 24rpx;
  color: #999;
}
.nowWatch {
  margin-left: 8rpx;
}

.watchAndtime {
  margin-top: 20rpx;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
</style>
