<template>
  <view class="customer_detail">
    <view class="customer_info">
      <image v-if="customer_detail.prelogo" class="avatar" :src="customer_detail.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
      <view class="user_info">
        <view class="user_name flex-row"><text class="name-con">{{customer_detail.cname||'暂无姓名'}}</text> <text v-if="customer_detail.alias_name" class="levelname"  :class="{official:customer_detail.role=='1'||customer_detail.role=='6',agent:customer_detail.role=='3',adviser:customer_detail.role=='2',member:customer_detail.role=='4',zijian:customer_detail.role=='5' }">{{customer_detail.alias_name}}</text></view>
        <view class="user_tel">{{customer_detail.tel}}</view>
      </view>
      <view class="btn_group">
        <view v-if="customer_detail.vid" class="button plain" @click="handleChat">微聊</view>
        <view class="button plain" @click="handleTel">电话</view>
      </view>
    </view>
    <view class="oper flex-row">
      
      <view class="alert btns  flex-row" @click="$refs.remind.show()"> <my-icon type="tongzhi" color="#666" size="36rpx"></my-icon><text class ="btns-label">提醒</text></view>
      <view class="copy btns flex-row" @click ="copy">
        <my-icon type="copy" color="#fff" size="40rpx"></my-icon>
        <text class ="btns-label">复制</text>
      </view>
      <view class="del btns flex-row" @click="delCustomer"> <my-icon type="ic_delete1x" color="#666" size="42rpx"></my-icon><text class ="btns-label">删除</text></view>
    </view>
    <view class="form">
      <view class="form_row">
        <view class="input_form">
          <view class="label">意向区域</view>
          <view class="form_value" @click="$refs.area_popup.show()">
            <text v-if="customer_detail.area_id" class="value">{{customer_detail.area_name}}</text>
            <text v-else class="placeholder">请选择意向区域</text>
            <my-icon type="ic_into" color="#999" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
      <view class="form_row">
        <view class="input_form">
          <view class="label">意向小区</view>
          <input class="form_value" type="text" v-model="customer_detail.community" placeholder="请输入意向小区" placeholder-style="font-size: 30rpx; text-align: right; color: #999">
        </view>
      </view>
    </view>
    <view class="form">
      <view class="form_row">
        <view class="input_form">
          <view class="label">购房进度</view>
          <view class="form_value" @click="$refs.progress.show()">
            <text v-if="current_speed" class="value">{{current_speed}}</text>
            <text v-else class="placeholder">请选择进度</text>
            <my-icon type="ic_into" color="#999" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="logs_container">
      <view class="cate_list flex-box">
        <view class="cate_item" :class="{active: type === 'visit'}" @click="getLogs('visit')">客访轨迹</view>
        <view class="cate_item" :class="{active: type === 'follow_up'}" @click="getLogs('follow_up')">跟进记录</view>
        <view class="cate_item" v-if="is_open_middle_num&&is_optimization&&customer_detail.authorize_type!=3" :class="{active: type === 'tel_log'}" @click="getLogs('tel_log')">通话记录</view>
      </view>
      <time-line :lineData="log_list" v-if="type==='tel_log'">
        <template v-slot:content="{slotData, slotIndex}">
          <view class="tel-item flex-box">
            <view class="tel-info flex-1">
              <view class="title_row">{{slotData.username}}拨打了您的电话, 通话时长{{slotData.call_duration | durationFilter}}</view>
              <view class="time">{{slotData.callon_time}} ~ {{slotData.callend_time}}</view>
            </view>
            <view class="voice flex-box" v-if="slotData.record_ready&&slotData.record_file" @click="playVoice(slotData.record_file, slotIndex)">
              <image class="play_vioce_icon" :src="play_voice_index === slotIndex?'/static/icon/voice/play_voice_black.gif':'/static/icon/voice/voice_icon_black.png'"></image>
              <text>{{slotData.call_duration}}</text>
            </view>
          </view>
        </template>
      </time-line>
      <time-line :lineData="log_list" v-else>
        <template #content="{slotData}">
          <view class="follow_up_remark flex-box" v-if="type==='follow_up'">
            <view class="label">备注</view>
            <view class="remark flex-1">
              <view>{{slotData.content}}</view>
              <view class="img_list">
                <image class="img" v-for="(img, index) in slotData.images" :key="index" :src="img | imageFilter('w_240')" mode="aspectFill" @click="previewImg(index, slotData.images)"></image>
                <view class="img vacancy"></view>
              </view>
            </view>
          </view>
        </template>
      </time-line>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
    <my-popup ref="area_popup">
      <addressPicker :data_list="area_list" @onselect="onAreaChange"></addressPicker>
    </my-popup>
    <my-popup ref="progress">
      <followUp @cancel="$refs.progress.hide()" :cname="customer_detail.cname" :show="true" @confirm="onProgressChange"/>
    </my-popup>
    <my-popup ref="remind">
       <remind @cancel="$refs.remind.hide()" :detail="remind_time" @confirm="onRemindChange"/>
    </my-popup>
    <view class="btn" @click="subFollowUp">跟进</view>
  </view>
</template>

<script>
import mySelect from '@/components/form/mySelect'
import myIcon from '@/components/myIcon'
import myPopup from '@/components/myPopup'
import addressPicker from '@/components/addressPicker.vue'
import timeLine from './components/timeLine'
import followUp from './components/followUp'
import remind from './components/remind'
import {uniLoadMore} from '@dcloudio/uni-ui'
import getChatInfo from "../common/get_chat_info"
import {formatImg,showModal} from '../common/index.js'
import copyText from '../common/utils/copy_text'
const innerAudioContext = uni.createInnerAudioContext();
export default {
  name: 'CustomerDetail',
  components: {
    mySelect,
    myIcon,
    myPopup,
    addressPicker,
    timeLine,
    followUp,
    uniLoadMore,
    remind
  },
  data () {
    return {
      customer_detail:{},
      current_speed: "",
      area_list: [],
      type: "visit",
      get_status:"loading",
      content_text:{
        contentdown:"",
        contentrefresh:"正在加载...",
        contentnomore:"没有更多数据了"
      },
      log_page: 1,
      log_list: [],
      remind_time:'',
      is_optimization: 0,
      play_voice_index: -1,
      // remindObj:{
      //   date:new Date().toLocaleDateString(),
      //   time:new Date().toLocaleTimeString('chinese',{hour12:false}),
      // }
    }
  },
  computed:{
    is_open_middle_num() {
      return this.$store.state.im.istelcall
    }
  },
  onLoad(options){
    if(options.id){
      this.id = options.id
      this.getDetail(this.id)
    }
    this.getAreaList()
    innerAudioContext.onPlay(()=>{
      this.play_voice_index = this.current_voice_index
    })
    innerAudioContext.onStop(()=>{
      this.play_voice_index = -1
    })
    innerAudioContext.onEnded(()=>{
      this.play_voice_index = -1
    })
  },
  filters:{
    durationFilter(value){
      if(!value){
        return '0秒'
      }
      let time = parseInt(value)
      let minute = Math.trunc(time/60)
      let second = time%60
      if(minute){
        return `${minute}分${second}秒`
      }else{
        return `${second}秒`
      }
    }
  },
  methods: {
    onAreaChange(e){
      this.customer_detail.area_name = e[e.length-1].name
      this.customer_detail.area_id = e[e.length-1].value
      this.$refs.area_popup.hide()
    },
    // getAreaName(){
    //   if(this.area_list.length>0&&this.customer_detail.area_id){
    //     this.customer_detail.area_name = this.area_list.find(item=>item.areaid===this.customer_detail.area_id).areaname
    //   }
    // },
    copy(){
        const content = `客户姓名：${this.customer_detail.cname||'暂无姓名'}\n手机号码：${this.customer_detail.tel}\n意向小区：${this.customer_detail.community||"未跟进"}\n跟进进度：${this.current_speed||"未跟进"}\n客户来源：${this.customer_detail.page_from||""}\n跟进时间：${this.customer_detail.customer_time||""}`
        copyText(content, ()=>{
            uni.showToast({
              title: '客户信息复制成功',
              icon: 'none'
            })
        })
    },
    getDetail(id){
      this.$ajax.get('im/customerDetail.html', {id}, res=>{
        if(res.data.code === 1){
          this.current_speed = res.data.customer.speed
          this.customer_detail = res.data.customer
          this.customer_detail.speed = ""
          this.is_optimization = res.data.is_optimization
          this.getLogs('visit')
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    getAreaList() {
      this.$ajax.get('Release/getArea', {}, res => {
        if (res.data.code === 1) {
          this.area_list = res.data.data
        }
      })
    },
    getLogs(type){
      this.type = type
      if(this.log_page === 1){
        this.log_list = []
      }
      this.get_status = "loading"
      if(this.type === 'follow_up'){
        this.$ajax.get('im/followedList.html', {id: this.id, page: this.log_page, rows: 20}, res=>{
          if(res.data.code === 1){
            res.data.list = res.data.list.map(item=>{
              return {
                time: item.ctime,
                content: item.remark,
                speed: item.speed,
                images: item.images
              }
            })
            this.log_list = this.log_list.concat(res.data.list)
            this.get_status = "more"
            if(res.data.list.length<20){
              this.get_status = "noMore"
            }
          }else{
            this.get_status = "noMore"
          }
        })
        return
      }
      if(type === 'tel_log'){
        this.getTelLog()
        return
      }
      this.$ajax.get('im/visitorDetail.html',{user_id: this.customer_detail.vid, page: this.log_page, rows: 20}, res=>{
        if(res.data.code === 1){
          this.log_list = this.log_list.concat(res.data.list)
          this.get_status = "more"
          if(res.data.list.length<20){
            this.get_status = "noMore"
          }
        }else{
          this.get_status = "noMore"
        }
      })
    },
    getTelLog(){
      this.$ajax.get('im/callRecord.html',{user_id: this.customer_detail.vid, page:this.log_page, rows:20}, res=>{
        if(res.data.code === 1){
          this.log_list = this.log_list.concat(res.data.list)
          if(res.data.list.length<20){
            this.get_status = "noMore"
          }else{
            this.get_status = "more"
          }
        }else{
          this.get_status = "noMore"
        }
      })
    },
    playVoice(vioce ,index){
      console.log(index)
      if(!vioce){
        uni.showToast({
          title: '暂无录音文件',
          icon: 'none'
        })
        return
      }
      this.current_voice_index = index
      innerAudioContext.src = vioce
      innerAudioContext.play()
    },
    previewImg(index, img_list){
      let img_urls = img_list.map((item) => {
        return formatImg(item, 'w_860')
      })
      uni.previewImage({
        current: img_urls[index],
        indicator: "number",
        urls: img_urls
      })
    },
    onProgressChange(e){
      this.current_speed = e.status_text
      this.customer_detail.speed = e.status_text
      this.customer_detail.images = e.imgs
      this.customer_detail.remark = e.descp
      this.customer_detail.cname = e.cname
      this.$refs.progress.hide()
    },
    onRemindChange(e){
      // 提交事件
      let params ={}
      e.date=e.date+" "
      params.remind_time=e.date+e.time
      params.tid=this.id
      var currTime=new Date().getTime(),
          selectTime=new Date(params.remind_time).getTime();
          if (selectTime-currTime<9*60*1000){
            uni.showToast({
              title:'时间间隔太短 请选择10分钟以后的时间',
              icon:'none'
            })
            return
          }
      this.$ajax.get('Im/remindVisitor.html',params, res=>{
          if(res.data.code === 1){
              uni.showToast({
                  title: res.data.msg
              })
              setTimeout(() => {
                this.$refs.remind.hide()
              }, 500);
          }else{
              uni.showToast({
                  title: res.data.msg,
                  icon:'none'
              })
          }
      })
    },
    // 删除客户
    delCustomer(){
      if (this.customer_detail.role!=5){
        uni.showToast({
            title: "只可以删除自建客户",
            icon:"none"
        })
        return
      }
      showModal({
          content:'确定要删除客户吗？',
          confirm: (res)=>{
              this.$ajax.get('im/delCustomer.html',{id:this.customer_detail.cid}, res=>{
                  if(res.data.code === 1){
                      uni.showToast({
                          title: res.data.msg
                      })
                      setTimeout(() => {
                        uni.navigateBack()
                        uni.$emit("getListAgain",{type:0})
                      }, 1000);
                      
                  }else{
                      uni.showToast({
                          title: res.data.msg,
                          icon:'none'
                      })
                  }
              })
          }
      })
    },

    subFollowUp(){
      let {area_id, community, speed, images, remark,cname} = this.customer_detail
      // 接口要求跟进必须填写备注
      if(!speed){
        uni.showToast({
          title: "请选择跟进进度",
          icon: 'none'
        })
        return
      }
      if(!remark){
        uni.showToast({
          title: "请填写跟进备注",
          icon: 'none'
        })
        return
      }
      let params = {
        id: this.id,
        area_id: area_id,
        community: community,
        speed: speed,
        images: images,
        remark: remark,
        cname:cname
      }
      this.$ajax.post('im/editCustomer.html',params, res=>{
        if(res.data.code === 1){
          uni.showToast({
            title: res.data.msg
          })
          this.getLogs('follow_up')
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    handleChat(){
      getChatInfo(this.customer_detail.vid)
    },
    handleTel(){
      uni.makePhoneCall({
        phoneNumber: this.customer_detail.tel
      })
    },
  },
  onReachBottom(){
    if(this.get_status!=='more') return
    this.log_page++
    this.getLogs()
  },
}
</script>

<style scoped lang="scss">
.customer_detail{
  padding-bottom: 200rpx;
  .customer_info{
    padding: 24rpx 48rpx;
    display: flex;
    // justify-content: space-between;
    align-items: center;
    background-color: #fff;
    .avatar{
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 16rpx;
    }
    .user_info{
      flex: 1;
      color: #333;
      max-width:50%;
      .user_name{
        font-weight: bold;
        margin-bottom: 12rpx;
        align-items: center;
        display: flex;
        flex:1;
        .name-con{
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 60%;
          white-space: nowrap;
        }
        .levelname{
                line-height: 32rpx;
                margin: 0 16upx;
                padding: 0 16rpx;
                display: inline-block;
                // border-radius: 16upx;
                font-size: 22upx;
                // border: 1upx solid #4ebdf8;
                // color: #999;
                color: #fff;
                // background-color: #f2f2f2;
                background-image: linear-gradient(180deg,#69d4bb,#00caa7);
                font-weight: initial;
                border-top-left-radius: 4px;
                border-bottom-right-radius: 4px;
                &.official{
                    // background-color: #1296db;
                    color: #fff;
                    background-image: linear-gradient(180deg,#8cd3fc,#4cc7f6)
                    // border: 1upx solid #1296db;
                }
                &.agent{
                    // border: 1upx solid #f96063;
                    // background-color: #f96063;
                    background-image: linear-gradient(180deg,#ff9767,#fd7737);
                    color: #fff;
                }
                &.adviser{
                    // border: 1upx solid #f0bb2c;
                    // background-color: #f0bb2c;
                    color: #fff;
                    background-image: linear-gradient(180deg,#fcd88c,#f6ce4c);
                }
                &.zijian{
                    // border: 1upx solid #f0bb2c;
                    // background-color: #f0bb2c;
                    color: #fff;
                    background-image: linear-gradient(180deg,#4c86b3, #838afb);
                }
        }
      }
    }
    .btn_group{
      display: flex;
      justify-content: space-between;
      margin-left: auto;
      .button{
        height: 64rpx;
        line-height: 64rpx;
        padding: 0 32rpx;
        border-radius: 8rpx;
        text-align: center;
        font-size: 28rpx;
        box-sizing: border-box;
        ~.button{
          margin-left: 24rpx;
        }
        &.plain{
          border: 1rpx solid $uni-color-primary;
          color: $uni-color-primary;
        }
        &.disabled{
          background-color: #f2f2f2;
          border-color: #f2f2f2;
          color: #d8d8d8;
        }
      }
    }
  }
}
.oper{
  padding: 24rpx 48rpx;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
  .btns{
    padding: 10rpx 40rpx;
    text-align: center;
    border-radius: 10rpx;
    flex: 1;
    color: #666;
    display: flex;
    align-items: center;
    .btns-label{
      margin-left: 10rpx;
    }
    &.copy{
      // background: #999;
      background-image: linear-gradient(125deg, #FF5500 0%, #FFA402 100%);
      box-shadow: 0 1px 5px 0 rgba(255, 145, 1, 0.5);
      border-radius: 1px;
      border-radius: 1px;
      color: #fff;
      display: flex;
      justify-content: center;
    }
    &.alert{
     padding-left: 0;
      // background: #FB656A ;
      // color: #fff;
      // background-image: linear-gradient(180deg, #69d4bb, #00caa7);
      // box-shadow: 0 1px 5px 0 rgba(78, 192, 165, 0.5);
      // border-radius: 1px;
      // border-radius: 1px;
      // color: #fff;
    }
    &.del{
      padding-right: 0;
      display: flex;
      justify-content: flex-end;
      // background: #AB642C ;
      // color: #fff;
    }
  }
}

.form{
  margin-top: 24rpx;
  padding: 0 48rpx;
  background-color: #fff;
  .form_row{
    padding: 24rpx 0;
    .input_form{
      display: flex;
      justify-content: space-between;
      align-items: center;
      .label{
        font-size: 32rpx;
        color: #666;
        margin-right: 16rpx;
      }
      .form_value{
        height: 72rpx;
        text-align: right;
        box-sizing: border-box;
        padding: 16rpx;
        flex: 1;
        font-size: 30rpx;
      }
      .placeholder{
        color: #999;
      }
    }
  }
}

.logs_container{
  margin-top: 24rpx;
  background-color: #fff;
}

.cate_list{
  background-color: #fff;
  justify-content: center;
  align-items: center;
  margin-bottom: 24rpx;
  .cate_item{
    padding: 24rpx;
    margin: 0 30rpx;
    position: relative;
    color: #999;
    transition: 0.26s;
    &.active{
      color: #333;
    }
    &.active::after{
      content: "";
      position: absolute;
      width: 32%;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      height: 8rpx;
      border-radius: 4rpx;
      background-color: $uni-color-primary;
    }
  }
}

.follow_up_remark{
  .label{
    margin-right: 24rpx;
    color: #666;
  }
  .remark{
    color: #999;
  }
  .img_list{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
    .img{
      width: 168rpx;
      height: 168rpx;
      &.vacancy{
        height: 0;
      }
    }
  }
}


.tel-item{
  align-items: center;
  .tel-info{
    overflow: hidden;
    .title_row{
      font-size: 28rpx;
      line-height: 1.5;
      margin-bottom: 15rpx;
    }
    .time{
      font-size: 24rpx;
      font-weight: bold;
      color: #999;
    }
  }
  .voice{
    min-width: 120rpx;
    .play_vioce_icon{
      width: 36rpx;
      height: 36rpx;
    }
  }
}

.btn{
  position: fixed;
  left: 48rpx;
  right: 48rpx;
  bottom: 64rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 8rpx;
  text-align: center;
  font-size: 32rpx;
  background-color: $uni-color-primary;
  box-shadow: 0 8rpx 32rpx 0 rgba($color: $uni-color-primary, $alpha: 0.40);
  color: #fff;
}
</style>