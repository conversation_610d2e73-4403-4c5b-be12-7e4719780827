<template>
  <!-- 底部导航条 -->
  <view class="footernav row">
    <view @click="toHome()" class="footbox row">
      <myIcon size="20" type="home"></myIcon>
      <text class="home foottxt">首页</text>
    </view>
    <!-- #ifndef MP -->
    <view @click="handleShare()" class="footbox row">
      <myIcon size="20" type="fenxiang3"></myIcon>
      <text class="home foottxt">分享</text>
    </view>
    <!-- #endif -->
    <!-- #ifdef MP -->
    <button open-type="share" class="footbox row">
      <myIcon size="20" type="fenxiang3"></myIcon>
      <text class="home foottxt">分享</text>
    </button>
    <!-- #endif -->
    <view v-if="tel" @click="onMakePhoneCall()" class="footbox row">
      <myIcon size="20" type="zixun1"></myIcon>
      <text class="home foottxt">咨询</text>
    </view>
    <view v-else @click="onClickSign()" class="footbox row">
      <myIcon size="20" type="sousuo"></myIcon>
      <text class="home foottxt">找房</text>
    </view>
    <shareTip :show="show_tip" @hide="show_tip = false" />
  </view>
</template>
<script>
import myIcon from '@/components/icon'
import shareTip from '@/components/shareTip'
export default {
  props: {
    openType: {
      type: String,
      default: ''
    },
    tel: Boolean
  },

  data() {
    return {
      show_tip: false
    }
  },
  components: {
    myIcon,
    shareTip
  },
  methods: {
    toHome() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    },
    handleShare() {
      this.show_tip = true
    },
    onMakePhoneCall() {
      this.$emit('makePhoneCall')
    },
    onClickSign() {
      this.$emit('sign')
    }
  }
}
</script>
<style lang="scss" scoped>
view{
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  &.row {
    flex-direction: row;
  }
}
button::after {
  border: none;
}

button {
  background-color: #fff;
}
.row{
    padding: 0;
  }
.footernav {
  width: 100%;
  height: 104rpx;
  /* border: 1px solid #999; */
  border-radius: 50rpx;
  background-color: #fff;

  box-shadow: 0px 0 20rpx rgba(0, 0, 0, 0.2);
  justify-content: space-around;
}
.footbox {
  display: flex;
  margin: 0;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}
.foottxt {
  line-height: 104rpx;
  margin-left: 5rpx;
}
</style>
