<template>
	<view class="desc-content flex-box">
		<view class="flex-1">
			<view class="item-title">等额本息</view>
			<view class="row">
				<text class="label">月供</text>
				<text class="value">￥{{loan.yuegong}}(30年)</text>
			</view>
			<view class="row">
				<text class="label">总价</text>
				<text class="value">{{total}}万</text>
			</view>
			<view class="row">
				<text class="spot wring-color"></text>
				<text class="label">首付</text>
				<text class="value">{{downPayments}}万(3成)</text>
			</view>
			<view class="row">
				<text class="spot primary-color"></text>
				<text class="label">贷款</text>
				<text class="value">{{loan.loanLimit}}万</text>
			</view>
			<view class="row">
				<text class="spot success-color"></text>
				<text class="label">利息</text>
				<text class="value">{{loan.lixi}}万</text>
			</view>
		</view>
		<view class="flex-1">
			<view class="item-title">等额本金</view>
			<view class="row">
				<text class="label">首月</text>
				<text class="value">￥{{loan2.firstMonth}}(30年 递减{{loan2.dijian}})</text>
			</view>
			<view class="row">
				<text class="label">总价</text>
				<text class="value">{{total}}万</text>
			</view>
			<view class="row">
				<text class="spot wring-color"></text>
				<text class="label">首付</text>
				<text class="value">{{downPayments}}万(3成)</text>
			</view>
			<view class="row">
				<text class="spot primary-color"></text>
				<text class="label">贷款</text>
				<text class="value">{{loan2.loanLimit}}万</text>
			</view>
			<view class="row">
				<text class="spot success-color"></text>
				<text class="label">利息</text>
				<text class="value">{{loan2.lixi}}万</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			total:{
				type: [Number,String],
				default:0
			},
			downPayments:{
				type: [Number,String],
				default:0
			},
			lilv:{
				type:Number,
				default:4.9/100/12
			},
			time:{
				type:Number,
				default:30*12
			}
		},
		data() {
			return {
				
			};
		},
		computed:{
			loan(){
				let loanLimit = parseFloat(this.total)-this.downPayments
				let yuegong = loanLimit*this.lilv*Math.pow((1+this.lilv),this.time)/(Math.pow((1+this.lilv),this.time)-1)
				let lixi = yuegong*this.time-loanLimit
				return {
					loanLimit:loanLimit.toFixed(2),
					yuegong:(yuegong*10000).toFixed(2),
					lixi:lixi.toFixed(2)
				}
			},
			loan2(){
				let loanLimit = parseFloat(this.total)-this.downPayments
				let firstMonth = (loanLimit/this.time)+(loanLimit-0)*this.lilv
				let dijian = loanLimit/this.time*this.lilv
				let lixi = ((loanLimit/this.time+loanLimit*this.lilv)+loanLimit/this.time*(1+this.lilv))/2*this.time-loanLimit
				return {
					loanLimit:loanLimit.toFixed(2),
					firstMonth:(firstMonth*10000).toFixed(2),
					dijian:(dijian*10000).toFixed(2),
					lixi:lixi.toFixed(2)
				}
			}
		}
	}
</script>

<style lang="scss">
	.desc-content .row{
		font-size: 24upx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		padding: $uni-spacing-col-sm 0;
	}
	.desc-content .row .label{
		padding:0;
		margin-right: 20upx
	}
	.spot{
		display: inline-block;
		margin-right: 10upx;
		width: 0;
		height: 0;
		padding: 8upx;
		border-radius: 50%;
		background-color: #179B16
	}
	.spot.wring-color{
		background-color: $uni-color-warning
	}
	.spot.primary-color{
		background-color: $uni-color-primary
	}
	.spot.success-color{
		background-color: $uni-color-success
	}
</style>
