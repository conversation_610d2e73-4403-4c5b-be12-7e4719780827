<template>
  <view class="custom_build" :class="{ in_pc: !$store.state.in_mobile }">
    <view style="min-height: 100vh">
      <view class="img_box">
        <image :src="detail.pic | imageFilter('w_8001')" mode="aspectFill"></image>
      </view>
      <view class="build_title flex-row">
        <view class="title_row">
          <text class="title">{{ detail.build_name }}</text>
          <text class="status" v-if="detail.sale_status">{{ detail.sale_status }}</text>
        </view>
        <view class="label_list">
          <text class="label" v-for="(item, index) in detail.label" :key="index">{{ item }}</text>
        </view>
      </view>
      <view class="row_container">
        <view class="info_row">
          <text class="label">参考价格</text>
          <text class="value">{{ detail.price }}</text>
        </view>
        <view class="info_row" v-if ="detail.jian_mj&&detail.jian_mj.length">
          <text class="label">建面范围</text>
          <text class="value">{{ detail.jian_mj[0] }} -{{ detail.jian_mj[1] }}</text>
        </view>
        <view class="info_row">
          <text class="label">区域</text>
          <text>{{ detail.area_name }}</text>
        </view>
      </view>
      <view class="desc_container">
        <view class="label">项目简介</view>
        <view class="article-content" v-html="detail.content"></view>
      </view>
    </view>
    <sub-form
      groupCount=""
      sub_title="在线报名，获取优惠"
      sub_content="为方便通知到您最新的信息，请输入您的手机号码"
      :sub_type="3"
      :sub_mode="0"
      ref="sub_form"
      @onsubmit="handleSubForm"
      @close="show_video = true"
      :login_status="login_status"
    ></sub-form>
    <!-- <view class="btn_group">
      <view class="btn left" @click="makePhone">一键咨询</view>
      <view class="btn right" @click="onSign">立即报名</view>
    </view> -->
    
  </view>
</template>

<script>
import subForm from '../components/subForm'
export default {
  name: "customBuild",
  components: {
    subForm
  },
  data() {
    return {
      detail: {},
      read_time:10,
      current:0,
      count:0,
      show_step:false
    };
  },
  computed: {
    login_status() {
      return this.$store.state.user_login_status
    },
    sub_mode() {
      return this.$store.state.sub_form_mode
    },


  },
  onLoad(options) {
    this.ex_id = options.active_id;
    this.id = options.id;
    uni.$on("getDataAgain",()=>{
       this.getData(this.id, this.ex_id);
    })
    // this.is_task = options.is_task||''
    // this.count = options.count||0
    this.getData(this.id, this.ex_id);
  },
  beforeDestroy(){

  },
  onUnload(){
  },
  methods: {
    getData(id,activity_id) {
      this.$ajax.get("WordCollecting/buildDetail?activity_id=1&id=5", { id, activity_id}, (res) => {
        if (res.data.code === 1) {
          this.detail = res.data.build;
          uni.setNavigationBarTitle({
            title: res.data.build.title
          })
          // this.detail.build_type = res.data.detail.build_type.split(/[\s,]/g);
          this.$nextTick(() => {
            let imgs = document.querySelectorAll(".article-content img");
            let imgArr = [];
            let _this = this;
            for (let i = 0; i < imgs.length; i++) {
              imgArr.push(imgs[i].src);
              imgs[i].addEventListener("click", function () {
                _this.preImg(this.src, imgArr);
              });
            }
          });
        }
      });
    },

    preImg(nowImg, imgArr) {
      uni.previewImage({
        current: nowImg,
        indicator: "number",
        urls: imgArr,
      });
    },
    makePhone(){
      if(this.detail.phone){
        uni.makePhoneCall({
          phoneNumber: this.detail.phone
        })
      }
    },
    onSign(e){
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e){
      var params = {
        flop_id: parseInt(this.ex_id),
        info_id: this.id||'',
        cname: e.name,
        tel: e.tel
      }
      this.$ajax.post('buildShow/signUp', params, res=>{
        uni.hideLoading()
        if(res.data.code === 1){
          uni.showToast({
            title: res.data.msg||'报名成功',
            mask: true
          })
          this.$refs.sub_form.hide()
        }else{
          uni.showToast({
            title: res.data.msg||'报名失败',
            icon: 'none',
            mask: true
          })
        }
      })
    }
  },
};
</script>

<style scoped lang="scss">
.custom_build {
  min-height: 100vh;
  background-color: #fff;
  &.in_pc {
    max-width: 414px;
    margin: auto;
    ::v-deep .popup-page .popup-box.center {
      max-width: 400px;
    }
    .sign_btn {
      right: calc(50vw - 192px);
    }
    .video_container {
      height: 212px;
      video,
      iframe {
        height: 212px;
      }
    }
  }
}
.img_box {
  width: 100%;
  height: 562rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
.build_title {
  margin: 48rpx;
  .title_row {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
  }
  .title {
    margin-right: 8px;
    font-size: 40rpx;
    font-weight: 700;
  }
  .status {
    line-height: 1;
    font-size: 28rpx;
    padding: 6rpx 10rpx;
    border-radius: 4rpx;
    color: #fff;
    background-color: #53d2ab;
    font-weight: 400;
  }
  .label_list {
    .label {
      display: inline-block;
      vertical-align: top;
      line-height: 1;
      padding: 4rpx 10rpx;
      font-size: 22rpx;
      color: #999;
      border: 1rpx solid #d8d8d8;
      border-radius: 4rpx;
      ~ .label {
        margin-left: 12rpx;
      }
    }
  }
}
.row_container {
  margin: 48rpx;
  .info_row {
    margin-bottom: 36rpx;
    color: #999;
    .label {
      display: inline-block;
      width: 160rpx;
    }
    .value {
      color: $uni-color-primary;
    }
  }
}
.desc_container {
  margin: 48rpx;
  > .label {
    margin-bottom: 24rpx;
    font-size: 40rpx;
    font-weight: bold;
  }
  .article-content {
    // min-height: 100vh;
    padding: 48rpx;
    font-size: 28rpx;
  }
  .article-content  ::v-deep p {
    margin-bottom: 30upx !important;
  }
  .article-content  ::v-deep video {
    max-width: 100%;
    margin-bottom: 20upx;
  }
}
.btn_group{
  display: flex;
  padding: 24rpx 48rpx;
  position: sticky;
  bottom: 0;
  z-index: 3;
  .btn{
    width: 100%;
    height: 86rpx;
    line-height: 86rpx;
    text-align: center;
    border-radius: 12rpx;
    font-size: 32rpx;
    color: #fff;
    &.left{
      background-image: linear-gradient(90deg, #45A2FF 0%, #02A2FF 100%);
    }
    &.right{
      margin-left: 24rpx;
      background-image: linear-gradient(90deg, #FFA857 0%, #FF6069 100%);
      // box-shadow: 0 2px 7px -1px rgba(255,102,104,0.50);
    }
  }
}
.jindu {
  position: fixed;
  bottom:200rpx;
  border-radius: 8rpx;
  margin-left: 50%;
  transform: translateX(-50%);
  padding: 10rpx 20rpx;
  border: 2rxp solid #45A2FF;
  background: #45A2FF;
  color: #fff;
}
</style>
