<template>
  <view class="ershou_detail_content" v-if="loading">
    <!-- 焦点图 -->
    <view class="focus-box">
      <swiper class="banner" :indicator-dots="false" :circular="true" :duration="300" indicator-active-color="#f65354"
        @change="swiperChange" :current="swiperCurrent">
        <swiper-item v-for="(item, index) in focus" :key="item.index">
          <view v-if="item.type === 'vr'" class="swiper-item" @click="toVr(item)">
            <image :src="(item.cover || imgs[0]) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/vr_b.png" mode="widthFix"></image>
          </view>
          <view v-if="item.type === 'video_list'" class="swiper-item" @click="preVideo(item.url)">
            <image :src="(item.cover || item.url) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/video.png" mode="widthFix"></image>
          </view>
          <view v-if="item.type === 'main_imgs'" class="swiper-item" @click="preImg(index)">
            <image :src="item.url | imageFilter('w_6401')" mode="aspectFill"></image>
          </view>
          <view v-if="item.type === 'huxing_imgs'" class="swiper-item" @click="preHuxing(index)">
            <image :src="item.url | imageFilter('w_6401')" mode="aspectFill"></image>
          </view>
        </swiper-item>
      </swiper>
      <view class="number">编号：{{ detail.id }}</view>
      <view class="img-total">共{{ focusLen }}张</view>
      <view class="cate-box">
        <view class="cate-list flex-row">
          <view v-if="detail.vr" class="cate" @click="switchFocus('vr')" :class="cateActive == 'vr' ? 'active' : ''">VR
          </view>
          <view v-if="detail.videos.length > 0" class="cate" @click="switchFocus('video_list')"
            :class="cateActive == 'video_list' ? 'active' : ''">视频</view>
          <view v-if="detail.main_imgs.length > 0" class="cate" @click="switchFocus('main_imgs')"
            :class="cateActive == 'main_imgs' ? 'active' : ''">图片</view>
          <view v-if="detail.huxing_imgs.length > 0" class="cate" @click="switchFocus('huxing_imgs')"
            :class="cateActive == 'huxing_imgs' ? 'active' : ''">户型</view>
        </view>

      </view>
      <view v-if="hb_info.is_open" class="hongbao" @click="toHbHelp()">
        <view class="hb_content" :animation="num == 0 ? showpic : hidepic">
          <image class="hb_img" src='/static/icon/1.png'></image>

        </view>
        <view class="hb_content hb_content1" :animation="num == 1 ? showpic : hidepic">
          <image class="hb_img" src='/static/icon/2.png'></image>
        </view>
      </view>
    </view>
    <!-- 内容部分 -->
    <view class="container">
      <!-- 标题 -->
      <view class="house_title">
        <!-- <text class="attr" :class="'attr' + detail.zhongjie">{{ detail.zhongjie==1?'个人':'经纪人' }}</text> -->
        {{ detail.title }}
      </view>
      <!-- 主要信息 -->
      <view class="main_info-box flex-row">
        <view class="info house_info">
          <view class="huxing flex-row">
            <view class="price flex-row">
              <text class="value">{{ detail.zujin == '面议' || detail.zujin == '0' ? '面议' : detail.zujin }}</text>
              <text v-if="detail.zujin !== '面议' && detail.zujin != '0' && detail.zujin != ''" class="unit">元/月</text>
            </view>
            <view class="stw flex-row">
              <text class="value">{{ detail.shi }}</text>
              <text class="unit">室</text>
              <text class="value">{{ detail.ting }}</text>
              <text class="unit">厅</text>
              <text class="value">{{ detail.wei }}</text>
              <text class="unit">卫</text>
            </view>
            <view class="mianji flex-row">
              <text class="value">{{ detail.mianji }}</text>
              <text class="unit">m²</text>
            </view>
          </view>
          <!-- 房源标签 -->
          <view class="label-list flex-row" v-if="detail.label && detail.label.length > 0">
            <!-- <text class="attr" :class="'attr' + detail.zhongjie">{{ detail.zhongjie==1?'个人':'经纪人' }}</text>
            <text class="label">{{ detail.catname||'' }}</text>
            <text class="label">{{ detail.areaname||'' }}</text> -->
            <text class="label" :style="{ color: item.color, borderColor: item.color }"
              v-for="(item, index) in detail.label" :key="index">{{ item.name }}</text>
          </view>
        </view>
        <!-- <view class="btn" @click="addContrast(id)" >+对比</view> -->
        <view>
          <button style="padding: 0 40rpx;" class="get_phone" v-if='login_status == 1 && loginByToutiaoUnion'
            open-type="getPhoneNumber" @getphonenumber="getToutiaoPhoneNumber">
            <view class="btn">{{ is_collect ? '已收藏' : '+收藏' }}</view>
          </button>
          <view class="btn" v-else @click="handleCollect()"
            style="padding: 0 28rpx;text-align: center;flex-wrap: nowrap;">{{ is_collect ? '已收藏' : '+收藏' }}</view>
        </view>
      </view>
      <!-- 甄选标签 -->
      <view v-if="detail.is_zhenxuan == 1" class="selection flex-row">
        <text class="s_tag">甄选</text>
        <image :src="'/images/zhenxuan.png' | imageFilter('w_120')" alt=""></image>
        <text v-if="detail.parentid == 1">真实在售</text>
        <text v-else>真实房源</text>
        <image :src="'/images/zhenxuan.png' | imageFilter('w_120')" alt=""></image>
        <text>楼盘字典核验</text>
      </view>
      <!-- 智能房源评测标签 -->
      <view class="tag_box flex-row" v-if="tagList.length > 0">
        <view class="tag flex-row" v-for="(item, index) in tagList" :key="index">
          <image class="tag_icon" v-if="item.name == 'change_price_count'" src="@/static/icon/jiang.png" />
          <image class="tag_icon" v-else-if="item.name == 'lower_community_price'" src="@/static/icon/dy.png" />
          <image class="tag_icon" v-else-if="item.name == 'lower_size_price'" src="@/static/icon/hx.png" />
          <image class="tag_icon" v-else-if="item.name == 'is_hot_hit'" src="@/static/icon/sheng.png" />
          <image class="tag_icon" v-else-if="item.name == 'school_count'" src="@/static/icon/school.png" />
          <image class="tag_icon" v-else-if="item.name == 'is_ranking'" src="@/static/icon/re.png" />
          <text>{{ item.title }}</text>
        </view>
      </view>
      <!-- 基础信息 -->
      <view class="block">
        <!-- <view class="label">基础信息</view> -->
        <view class="info-list flex-row">
          <view class="info-list-item mg-b48">
            <view class="label">区域</view>
            <view class="data">{{ detail.areaname || '' }}</view>
          </view>
          <view class="info-list-item mg-b48">
            <view class="label">朝向</view>
            <view class="data">{{ detail.chaoxiang || '南' }}</view>
          </view>
          <view class="info-list-item mg-b48">
            <view class="label">装修</view>
            <view class="data">{{ detail.zhuangxiu || '不详' }}</view>
          </view>
          <view class="info-list-item">
            <view class="label">楼层</view>
            <view class="data" v-if="detail.floor">{{ detail.floor || '' }}/共{{ detail.louceng || '' }}层</view>
            <view class="data" v-else>共{{ detail.louceng || '' }}层</view>
          </view>
          <view class="info-list-item">
            <view class="label">类型</view>
            <view class="data">{{ detail.catname }}</view>
          </view>
          <!-- <view class="info-list-item">
            <view class="label">编号</view>
            <view class="data">{{detail.id||''}}</view>
          </view> -->
          <view class="info-list-item">
            <view class="label">更新</view>
            <view class="data">{{ detail.begintime || '' }}</view>
          </view>
          <!-- <view class="info-list-item" v-if="detail.loudong">
            <view class="label">栋/幢/弄/胡同</view>
            <view class="data">{{detail.loudong}}号</view>
          </view>
          <view class="info-list-item" v-if="detail.danyuan">
            <view class="label">单元</view>
            <view class="data">{{detail.danyuan}}单元</view>
          </view>
          <view class="info-list-item" v-if="detail.fanghao">
            <view class="label">门牌号</view>
            <view class="data">{{detail.fanghao}}室</view>
          </view> -->
          <view class="info-list-item">
            <view class="label">小区</view>
            <view class="data">{{ housePrice.title || '不详' }}</view>
          </view>
          <view class="info-list-item">
            <view class="label">来源</view>
            <view class="data">{{ detail.zhongjie == 1 ? '个人' : '经纪人' }}</view>
          </view>
          <view class="info-list-item">
            <view class="label">浏览</view>
            <view class="data">{{ detail.hit || 0 }}</view>
          </view>
          <view class="info-line" v-if="detail.verification_code" @click="showVerification">
            <view class="label">核验编码</view>
            <view class="data">{{ detail.verification_code }}</view>
            <view class="line-right">
              <my-icon type='erweima1x' size="48rpx" color='#999'></my-icon>
              <my-icon type="ic_into" size="36rpx" color="#999"></my-icon>
            </view>
          </view>
          <view class="xiajia_icon" v-if="detail.is_show === 0">
            <my-icon type='yixiajia' size="180rpx" color='#e94e50'></my-icon>
          </view>
        </view>
        <!-- <template v-if='agent&&agent.levelid>1'>
          <view class="more_btn has_agent" @click="show_more_info=!show_more_info">{{show_more_info?'收起':'查看更多'}}</view>
        </template>
<template v-else>
            <view class="more_btn" @click="show_more_info=!show_more_info">{{show_more_info?'收起':'查看更多'}}</view>
          </template> -->
      </view>
      <!-- 聊天咨询按钮 -->
      <template v-if="agent && agent.levelid > 1">
        <view class="btn_list-box flex-row" v-if="is_open_im">
          <view class="btn-item">
            <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 3)">
              <view class="flex-row">
                <view class="img">
                  <image class="img_c" mode="widthFixed" :src="'/images/newhouse_detail/<EMAIL>' | iconformat">
                  </image>
                </view>
                <!-- <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon> -->
                <text>咨询楼层</text>
              </view>
            </chatBtn>
          </view>
          <view class="btn-item">
            <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 4)">
              <view class="flex-row">
                <view class="img">
                  <image class="img_c" mode="widthFixed"
                    :src="'/images/newhouse_detail/<EMAIL>' | iconformat">
                  </image>
                </view>
                <!-- <my-icon type="huxing" color="#ff656b" size="42rpx"></my-icon> -->
                <text>咨询详细设施</text>
              </view>
            </chatBtn>
          </view>
        </view>
      </template>
      <!-- 房源简介 -->
      <view class="block house_desc">
        <view class="label">房源特色</view>
        <view class="wnsb-list flex-row"
          v-if="(peitaoList && peitaoList.length == 0 && detail.chepeng && detail.chepeng !== '无' && detail.chepeng != 0) || (detail.wnsb && detail.wnsb.length > 0)">
          <!-- <text class="label" v-for="(item, index) in detail.wnsb" :key="index">{{ item }}</text> -->
          <text class="label" v-if="peitaoList && peitaoList.length == 0 && detail.chepeng && detail.chepeng !== '无'">{{
            detail.chepeng
          }}</text>
        </view>
        <view class="wnsb-list flex-row" v-if="fangyuanList && fangyuanList.length > 0">
          <view class="wnsb-box" v-for="(item, index) in fangyuanList" :key="index">
            <view class="peitao" :style="{ backgroundImage: `url(${ossDomain}/fabu/tese/svg/${item.icon}.svg)` }">
            </view>
            <view class="peitao_name">{{ item.name }}</view>
          </view>
        </view>
        <view class="content_info " v-if="peitaoList && peitaoList.length > 0">
          <view class="desc peitao_title">
            <text class="cate"> 配套设施</text>
          </view>
        </view>
        <view class="wnsb-list flex-row" v-if="peitaoList && peitaoList.length > 0">
          <view class="wnsb-box" v-for="(item, index) in peitaoList" :key="index">
            <view class="peitao"
              :style="{ backgroundImage: `url(${ossDomain}/fabu/peitao_fangyuan/peitao${item.id}.png)` }"
              :class="'peitao' + item.id"></view>
            <view class="peitao_name">{{ item.title }}</view>
          </view>
        </view>
        <view class="content_info" v-if="basicData && basicData.length > 0">
          <view class="desc peitao_title">
            <text class="cate"> 基本信息</text>
          </view>
        </view>
        <view class="wnsb-list flex-row" v-if="basicData && basicData.length > 0">
          <view class="list-texto" v-for="(item, index) in basicData" :key="index + 1">
            <text>{{ item.label }}</text>
            <text>{{ item.value }}</text>
          </view>
        </view>
        <view class="content_info" v-if="tradeData && tradeData.length > 0" style="margin-top:30rpx;">
          <view class="desc peitao_title">
            <text class="cate"> 交易属性</text>
          </view>
        </view>
        <view class="wnsb-list flex-row" v-if="tradeData && tradeData.length > 0">
          <view class="list-texto" v-for="(item, index) in tradeData" :key="index + 2">
            <text>{{ item.label }}</text>
            <text>{{ item.value }}</text>
          </view>
        </view>
        <view class="content_info">
          <view class="desc" style="margin-top:20rpx;" v-if="house_detail_selling_point == 1">
            <text class="cate">核心卖点</text>
            <text>{{ detail.content }}</text>
          </view>
          <view class="desc" v-if="detail.owner_think">
            <text class="cate">业主心态</text>
            <text>{{ detail.owner_think }}</text>
          </view>
          <view class="desc" v-if="detail.service_introduce">
            <text class="cate">服务介绍</text>
            <text>{{ detail.service_introduce }}</text>
          </view>
          <view class="tip">联系我时，请说在{{ site_name }}看到的信息，谢谢</view>
        </view>
        <!-- <view class="agent-box flex-row" v-if="agent.levelid>1">
          <image class="header_img" :src="agent.prelogo | imageFilter('w_120')"></image>
          <view class="agent_info">
            <text class="name">{{agent.cname}}</text>
            <text class="shop_name">{{agent.tname||'经纪人'}}</text>
          </view>
          <view class="into-btn" @click="$navigateTo('/pages/agent/detail?id='+agent.id)">进入主页</view>
        </view> -->
        <!-- <view class="selling_point" v-if="detail.content">
          <view class="label">核心卖点</view>
          <view class="content_info">{{detail.content}}</view>
        </view> -->
        <!-- <view class="selling_point" v-if="detail.content">
          <view class="label">户型介绍</view>
          <view class="content_info">{{detail.content}}</view>
          <view class="view_more">展开</view>
        </view> -->
      </view>
      <!-- 广告位 -->
      <swiper v-if="advs && advs.length > 0" class="ext_swiper" autoplay :interval="3000">
        <swiper-item v-for="(item, index) in advs" :key="index" @click="$navigateTo(item.wap_link)">
          <image :src="item.image | imageFilter('w_8601')" mode="aspectFill"></image>
          <view class="marker">广告</view>
        </swiper-item>
      </swiper>
      <!-- 房源动态 -->
      <template v-if="house_detail_trends == 1 && agent && agent.levelid > 1">
        <view class="block house_news">
          <view class="label flex-row">
            房源动态
          </view>
          <view class="house_news_info flex-row">
            <view class="house_news_info_item">
              <view class="house_news_info_item_num">
                {{ detail.hit }}
              </view>
              <view class="house_news_info_item_title">
                浏览人数
              </view>
            </view>
            <view class="house_news_info_item">
              <view class="house_news_info_item_num">
                {{ detail.tel_volume }}
              </view>
              <view class="house_news_info_item_title">
                近30天咨询
              </view>
            </view>
          </view>
          <view class="house_info_timeline">
            <view class="time_line">
              <view class="item" v-for="(item, index) in detail.price_changes" :key="index">
                <view class="line-item">
                  <view class="content_c flex-row">
                    <view class="content_con flex-row"><text>{{ item.title }}</text> <text class="blod">
                        {{ item.price }}</text><text>{{ item.price_uint }}</text> <my-icon
                        v-if="item.type == 2 && item.diff < 0" type="ic_down" color="rgb(0, 202, 167)"></my-icon>
                      <my-icon v-if="item.type == 2 && item.diff > 0" type="ic_up" color="rgb(251, 101, 106)"></my-icon>
                    </view>
                    <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, (item.type + 7))">
                      <view class="content_sub">
                        {{ item.type | formatType }}
                      </view>
                    </chatBtn>
                  </view>
                  <view class="line-header flex-box">
                    <view class="time">{{ item.time }}</view>
                  </view>

                </view>
              </view>
            </view>
          </view>
        </view>
      </template>
      <!-- 带看点评 -->
      <template v-if="false">
        <view class="block house_news">
          <view class="label flex-row">
            带看点评
          </view>
          <view>
            <view>
              <view class="list-texto" v-if="lookData.ctime != ''">
                <text>时间</text>
                <text>{{ lookData.ctime }}</text>
              </view>
              <view class="list-texto" v-if="lookData.selling_point != '' && house_detail_selling_point == 1">
                <text>核心卖点</text>
                <text>{{ lookData.selling_point }}</text>
              </view>
              <view class="list-texto" v-if="lookData.community_introduction != ''">
                <text>小区介绍</text>
                <text>{{ lookData.community_introduction }}</text>
              </view>
              <view class="list-texto" v-if="lookData.structure_introduction != ''">
                <text>户型介绍</text>
                <text>{{ lookData.structure_introduction }}</text>
              </view>
              <view class="list-texto" v-if="lookData.transportation != ''">
                <text>交通出行</text>
                <text>{{ lookData.transportation }}</text>
              </view>
            </view>
          </view>
        </view>
      </template>
      <!-- 所属小区 -->
      <view class="block community-box" v-if="housePrice.id">
        <view class="label flex-row">
          <text>{{ housePrice.title }}</text>
          <view class="into flex-row" v-if="!agent.shareId" @click="toComminuty">
            <text>小区详情</text>
            <my-icon type="ic_into" color="#999" size="28rpx"></my-icon>
          </view>
        </view>
        <view class="community flex-row" @click="toComminuty">
          <image class="img" mode="aspectFill" :src="housePrice.img | imageFilter('w_240')"></image>
          <view class="info">
            <view class="price_row flex-row">
              <view class="price flex-row">
                <text class="label">租金均价</text>
                <text class="number">{{ housePrice.cz_avg_price }}</text>
                <text class="unit">元/月</text>
              </view>
            </view>
            <view class="increase flex-row">
              <text class="label">比上月</text>
              <my-icon type="ic_up" v-if="housePrice.cz_status === 1" color="#fb656a"></my-icon>
              <my-icon type="ic_down" v-else-if="housePrice.cz_status === 2" color="#179B16"></my-icon>
              <text v-else class="ping">持平</text>
              <view class="increase-text">
                <text v-if="housePrice.cz_status !== 0" class="value" :class="{ down: housePrice.cz_status === 2 }">{{
                  housePrice.cz_value }}</text>
                <text v-if="housePrice.cz_status !== 0" class="unit"
                  :class="{ down: housePrice.cz_status === 2 }">%</text>
              </view>
            </view>
            <view class="address flex-row">
              <text class="label">小区地址</text>
              <text class="value">{{ housePrice.address || '暂未更新' }}</text>
            </view>
          </view>
        </view>
        <!-- <view class="more_btn no_bg"  v-if="housePrice.count>0 && !agent.shareId" @click="$navigateTo(`/pages/renting/renting?cid=${housePrice.id}`)">查看同小区{{housePrice.cz_count}}套在租房源></view> -->
      </view>
      <view class="btn_list-box flex-row" v-if="housePrice.id">
        <view class="btn-item">
          <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 11)">
            <view class="flex-row">
              <!-- <view class="img">
                <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
              </view> -->
              <!-- <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon> -->
              <!-- <text v-if="housePrice.count">{{housePrice.count}}套在售房源</text>-->
              <text>查看同小区房源</text>
            </view>
          </chatBtn>
        </view>
        <view class="btn-item">
          <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 12)">
            <view class="flex-row">
              <!-- <view class="img">
                <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
              </view> -->
              <!-- <my-icon type="huxing" color="#ff656b" size="42rpx"></my-icon> -->
              <text>咨询学校入学政策</text>
            </view>
          </chatBtn>
        </view>
      </view>
      <!-- 聊天咨询按钮 -->
      <!-- <view class="btn_list-box flex-row" v-if="is_open_im">
        <view class="btn-item">
          <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 1)">
            <view class="flex-row">
              <view class="img">
                <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
              </view>
              <text>咨询房贷首付</text>
            </view>
          </chatBtn>
        </view>
        <view class="btn-item">
          <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 2)">
            <view class="flex-row">
              <view class="img">
                <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
              </view>
              <text>咨询详细税费</text>
            </view>
          </chatBtn>
        </view>
      </view> -->
      <!-- TA的其他房源 -->
      <view class="block" v-if="agent && agent.levelid > 1 && other_house.length > 0">
        <view class="label">
          <text>TA的其他房源</text>
          <text class="more" @click="$navigateTo(`/pages/agent/detail?id=${agent.id}&cate_id=2`)">更多</text>
        </view>
        <swiper class="other_house-list" :duration="300" :display-multiple-items="2" next-margin="88rpx">
          <swiper-item v-for="(item, index) in other_house" :key="index">
            <view class="swiper-item" @click="$navigateTo('/pages/renting/detail?id=' + item.id)">
              <view class="img-box">
                <image :src="item.img | imageFilter('w_320')" mode="aspectFill"></image>
              </view>
              <view class="house_type">{{ item.title }}</view>
              <view class="aligin-end flex-row">
                <text class="stw">{{ item.shi }}室{{ item.ting }}厅{{ item.wei }}卫</text>
                <text class="mianji">{{
                  item.mianji
                }}m²</text>
              </view>
              <view class="price-box flex-row">
                <view class="flex-row">
                  <text class="price">{{ item.zujin }}</text><text class="unit">元/月</text>
                </view>
                <view class="db_btn" @click.prevent.stop="addContrast(item.id)">+对比</view>
              </view>
            </view>
          </swiper-item>
          <swiper-item v-if="other_house.length < 2"></swiper-item>
        </swiper>
      </view>
      <!-- 位置及周边 -->
      <view class="block" v-if="detail.lat && detail.lng">
        <view class="label">位置及周边</view>
        <mapNearby :scale="mapData.scale" :cirles="cirles" :enableZoom="false" :enableScroll='false' :lat="detail.lat"
          :lng="detail.lng" :markers="mapData.covers" @clickMap="viewMap()" @clickCate="getCovers"></mapNearby>
      </view>
      <!-- 推荐房源 -->
      <view class="block" v-if="recommend_list.length > 0">
        <view class="label">推荐房源</view>
        <block v-for="(item) in recommend_list" :key="item.id">
          <house-item :item-data="item" type="renting"
            @click="$navigateTo(`/pages/renting/detail?id=${item.id}`)"></house-item>
        </block>
      </view>
      <view class="entrant_button" v-if="agent && agent.levelid > 1" @click="showWeituo">
        房源不合适?委托TA帮我找房
      </view>
      <!-- 免责声明 -->
      <view class="shengming">
        <view class="shengming_title flex-row">
          <text>免责声明</text>
          <text class="label" @click="$navigateTo(`/user/inform/inform?id=${id}`)">举报</text>
        </view>
        <view class="shengming_content" v-html="disclaimer"></view>
      </view>

      <!-- 底部操作菜单 -->
      <view class="bottom-bar flex-row" v-if="show == true && detail.is_show === 1">
        <view class="bar-left flex-row">
          <view class="icon-btn" v-if="agent && agent.levelid > 1"
            @click="$navigateTo('/pages/agent/detail?id=' + agent.id)">
            <image :src="agent.prelogo | imageFilter('w_120')" class="header_img"></image>
            <text>{{ agent.cname || '经纪人' }}</text>
          </view>
          <!-- <view class="icon-btn" v-else @click="toHome()">
            <my-icon type="ic_shouyed" size="50rpx"></my-icon>
            <text>首页</text>
          </view> -->
          <!-- <view class="icon-btn" @click="handleShare()" style="padding-top:5rpx">  
            <my-icon type="ic_fenxiang" color="#666" size="40rpx"></my-icon>
            <text>分享</text>
          </view> -->
          <!-- <view class="icon-btn" @click="handleCollect()">
            <my-icon v-if="is_collect" type="ic_shoucang_red" size="50rpx" color="#fb656a"></my-icon>
            <my-icon v-else type="ic_shoucang" size="50rpx"></my-icon>
            <text>收藏</text>
          </view> -->
          <view class="icon-btn" @click="handleShare()" style="padding-top:5rpx">
            <my-icon type="ic_fenxiang" color="#666" size="40rpx"></my-icon>
            <text>分享</text>
          </view>
          <view class="icon-btn last" @click="toContrast">
            <text class="badge" v-if="login_status > 1 && contrastCount > 0">{{ contrastCount > 99 ? '99+' :
              contrastCount
              }}</text>
            <text class="badge" v-if="login_status <= 1 && $store.state.temp_renting_contrast_ids.length > 0">{{
              $store.state.temp_renting_contrast_ids.length > 99 ? '99+' : $store.state.temp_renting_contrast_ids.length
              }}</text>
            <my-icon type="pk" color="#666" size="50rpx"></my-icon>
            <text>对比</text>
          </view>
        </view>
        <view class="bar-right flex-row flex-1">
          <view class="bar-btn btn1 flex-1" v-if="is_open_im" @click="showWechat()">在线咨询</view>
          <view class="bar-btn btn2 flex-1" :class="{ alone: !is_open_im }" @click="handleTel()">电话咨询</view>
        </view>
      </view>
    </view>
    <!-- 分享选项 -->
    <share-pop ref="share_popup" @copyLink="show_share_tip = true" @appShare="appShare" @handleCreat='handleCreat'
      @showCopywriting='showCopywriting'></share-pop>
    <!-- 复制分享文案 -->
    <my-popup ref="text_popup" position="center" :height="text_popup_height">
      <view class="copy-text-box" id="copy-text">
        <view class="title">{{ detail.title }}</view>
        <view class="info-box">
          <view class="info-row flex-row" v-if="housePrice.title">
            <text class="label">小区：</text>
            <text class="value">{{ housePrice.title }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">户型：</text>
            <text class="value">{{ `${detail.shi}室${detail.ting}厅${detail.wei}卫${detail.mianji}m²` }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">租金：</text>
            <text class="value">{{ detail.zujin ? detail.zujin + '元/月' : '面议' }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">类型：</text>
            <text class="value">{{ detail.catname }}</text>
          </view>
          <view class="info-row flex-row" v-if="detail.label && detail.label.length > 0">
            <text class="label">卖点：</text>
            <text class="value">{{ detail.label.map(item => item.name).join(' ') }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">楼层：</text>
            <text class="value" v-if="detail.floor">{{ detail.floor || '' }}/共{{ detail.louceng || '' }}层</text>
            <text class="value" v-else>共{{ detail.louceng || '' }}层</text>
          </view>
          <!-- <view class="info-row flex-row" v-if="(agent&&!agent.shareId&&agent.levelid>1)||(agent&&agent.shareId)">
              <text class="label">电话：</text>
              <text class="value" v-if ="agent&&agent.shareId" >{{agent.tel?agent.tel:''}}</text> 
              <text class="value" v-if ="agent&&!agent.shareId&&agent.levelid>1">{{detail.tel?detail.tel:''}}</text>   
            </view> -->
          <view class="button disabled-btn flex-row" v-if="copy_success">
            <my-icon type="check-circle" size="30rpx" color="#999"></my-icon>
            <text class="text">文本已复制</text>
          </view>
          <view class="button" v-else @click="copywriting">复制文本</view>
        </view>
      </view>
    </my-popup>
    <enturstBtn v-if="agent.agent_id || agent.adviser_id" :to_user="agent" @click="showWeituo" />
    <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
      <enturstBox @success="$refs.enturst_popup.hide()" :isDetail="isDetail" @close="$refs.enturst_popup.hide()"
        @popup_login="showLoginPopup('为方便您及时接收消息通知，请输入手机号码')" :to_user="agent" />
    </my-popup>
    <!-- 核验编码弹窗 -->
    <show-verification ref="show_verification" :verification_code="detail.verification_code"
      :verification_qrcode="detail.verification_qrcode" :verification_url="detail.verification_url"></show-verification>
    <!-- 登录弹窗 -->
    <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip"
      @success="onLoginSuccess"></login-popup>
    <chat-tip></chat-tip>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
    <hongbao v-if="hb_result" ref="hongbao" :money="hb_info.hb_money" :expire_seconds="hb_result.expire_seconds"
      @openHb="openHb"></hongbao>
  </view>
</template>

<script>
import {
  uniList,
  uniListItem
} from '@dcloudio/uni-ui'
import {
  formatImg,
  showModal,
  config
} from '../../common/index.js'
import myIcon from '../../components/myIcon.vue'
import mapNearby from "../../components/mapNearby.vue";
import houseItem from '../../components/houseItem.vue'
import myPopup from '../../components/myPopup.vue'
import shareTip from '../../components/shareTip.vue'
import allTel from '../../common/all_tel.js'
import getChatInfo from '../../common/get_chat_info'
import checkLogin from '../../common/utils/check_login'
import loginPopup from '../../components/loginPopup'
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
import chatBtn from '@/components/open-button/chatBtn'
import sharePop from '../../components/sharePop'
import hongbao from '@/components/hongbao'
import getLocation from '../../common/get_location'
import { getLonAndLat } from '@/common/utils/getLonAndLat'
import showVerification from '@/components/showVerification'
export default {
  components: {
    uniList,
    uniListItem,
    myIcon,
    mapNearby,
    houseItem,
    myPopup,
    shareTip,
    loginPopup,
    enturstBtn,
    enturstBox,
    chatBtn,
    loginPopup,
    hongbao,
    sharePop,
    showVerification
  },
  data() {
    return {
      id: "",
      loading: false,
      detail: {
        zujin: "",
        img: [],
        videos: [],
        huxing: [],
      },
      agent: {},
      is_collect: 0,
      loginByToutiaoUnion: '',
      housePrice: {},
      fangyuanList: [],
      loan_type: 0,
      other_house: [], //附近房源
      recommend_list: [], //推荐的房源
      focus: [],
      basicData: [],
      tradeData: [],
      lookData: '',
      focusLen: "",
      swiperCurrent: 0,
      cateActive: "",
      mapList: [],
      mapData: {
        scale: 12,
        covers: [],
      },
      show: false,
      opacity: 0,
      jingjiren: 0, //当前用户是不是经纪人
      show_more_info: false,
      contrastCount: 0,
      disclaimer: '',
      text_popup_height: '',
      copy_success: false,
      show_share_tip: false,
      link: '',
      currentUserInfo: {},
      sid: '',
      shareType: '',
      toLogin: true,
      login_tip: '',
      advs: [],
      showBackBtn: true,
      iconColor: "#fff",
      sub_type: 0,
      isDetail: 0,
      tel_res: {},
      show_tel_pop: false,
      peitaoList: [],
      hb_info: {},
      hb_result: {},
      hb: '',
      task_id: '',
      hongbao_gif: config.imgDomain + '/hongbao/linghongbao.png',
      saihongbao_gif: config.imgDomain + '/hongbao/saihongbao.gif',
      isLogin: false,
      map_key: '',
      hb_share_query: '',
      num: 0,
      showpic: {},
      hidepic: {},
      tuiguang_mp3: "/static/icon/voice/tuiguang_bg.mp3",
      hongbao_gif: config.imgDomain + '/hongbao/hongbao_gif.gif',
      peitaoList: [],
      tagList: [],
      moteNum: 0,
      house_detail_selling_point: 0, //是否显示核心卖点
    }
  },
  onLoad(options) {
    // app端没有安装微信 处理
    // #ifdef APP-PLUS
    if (!this.hasWechat) {
      let webView = this.$mp.page.$getAppWebview()
      webView.setTitleNViewButtonStyle(0, {
        type: "none",
        width: 0,
      })
    }
    // #endif
    uni.$on("getDataAgain", () => {
      this.getData(this.id)
    })
    if (JSON.stringify(this.$store.state.tempData) != "{}") {
      var tempDate = JSON.parse(JSON.stringify(this.$store.state.tempData))
      tempDate.img = ""
      Object.assign(this.detail, tempDate)
      document.title = this.detail.title
      uni.setNavigationBarTitle({
        title: this.detail.title
      })
      this.$store.state.tempData = {}
    } else if (options.title) {
      this.detail.title = decodeURIComponent(options.title)
      document.title = this.detail.title
      uni.setNavigationBarTitle({
        title: this.detail.title
      })
    }
    if (options.shareId) {
      this.sid = options.shareId,
        this.shareType = options.shareType
      this.share_time = options.f_time || ''
    }
    if (options.id) {
      this.id = options.id
      this.getData(options.id)
    }
    this.gettap()
  },
  onShow() {
    if (this.reload) {
      this.reload = false
      this.$store.state.allowOpen = true
      this.getData(this.id)
    }
  },
  onPageScroll(e) {
    this.opacity = e.scrollTop / 180
    if (this.opacity > 1) {
      this.opacity = 1
    }
    if (this.opacity < 0) {
      this.opacity = 0
    }

    if (this.opacity > 0.3) {
      this.iconColor = "#000"
    } else {
      this.iconColor = "#fff"
    }
  },
  onUnload() {
    if (this.innerAudioContext) {
      this.innerAudioContext.destroy()
    }
    if (this.setInterval1) {
      clearInterval(this.setInterval1)
    }
    this.$store.state.buildInfo = {}
    this.$store.state.tempData = {}
    uni.$off("getDataAgain")
  },
  filters: {
    iconformat(val) {
      return config.imgDomain + val
    },
    formatType(val) {
      if (val == "1") return "咨询优惠"
      if (val == "2") return "咨询底价"
      if (val == "3") return "咨询成交价"
    }
  },
  computed: {
    is_open_im() {
      return this.$store.state.im.ischat
    },
    is_open_middle_num() {
      return this.$store.state.im.istelcall
    },
    hasWechat() {
      return this.$store.state.hasWechat
    },
    login_status() {
      return this.$store.state.user_login_status
    },
    status_top() {
      return 0
    },
    site_name() {
      return this.$store.state.siteName
    },
    house_detail_trends() {
      return this.$store.state.house_detail_trends
    },
    ossDomain() {
      return config.imgDomain
    },
    oneKm() {
      return getLonAndLat(this.detail.lng, this.detail.lat, 0, 1000)
    },
    twoKm() {
      return getLonAndLat(this.detail.lng, this.detail.lat, 0, 2000)
    },
    threeKm() {
      return getLonAndLat(this.detail.lng, this.detail.lat, 0, 3000)
    },
    cirles() {
      if (this.detail && this.detail.lat) {
        return [
          {
            longitude: this.detail.lng,
            latitude: this.detail.lat,
            color: "#ff0000",
            radius: 1000,
            strokeWidth: 1,
          },
          {
            longitude: this.detail.lng,
            latitude: this.detail.lat,
            color: "#ff9c00",
            radius: 2000,
            strokeWidth: 1
          },
          {
            longitude: this.detail.lng,
            latitude: this.detail.lat,
            color: "#fee500",
            fillColor: "#00000026",
            radius: 3000,
            strokeWidth: 1
          }
        ]
      }
    }
  },
  methods: {
    showVerification() {
      this.$refs.show_verification.show()
    },
    getData(id) {
      let params = {
        lat: this.$store.state.position.lat,
        lng: this.$store.state.position.lng,
        id: id,
        sid: this.sid,
        sharetype: this.shareType,
        forward_time: this.share_time || ''
      }
      this.$ajax.get('house/houseDetail.html', params, (res) => {
        if (res.data.code == 0) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
          this.show = true;
          uni.hideLoading();
          return
        }
        //是否显示核心卖点
        this.house_detail_selling_point = res.data.house_detail_selling_point;

        // #ifdef H5 || MP-BAIDU
        if (res.data.seo) {
          let seo = res.data.seo
          if (res.data.house.img.length > 0) {
            seo.image = formatImg(res.data.house.img[0], 'w_8001')
          }
          this.seo = seo
        }
        // #endif
        this.parameter = res.data.parameter
        this.contrastCount = res.data.contrastCount
        this.disclaimer = res.data.disclaimer
        res.data.house.wnsb = res.data.house.wnsb ? res.data.house.wnsb.split(',') : []
        if (res.data.house.chepeng) {
          res.data.house.chepeng = res.data.house.chepeng.trim()
        }
        this.detail = res.data.house
        this.showBackBtn = res.data.switch_wap_info_title
        // 后台数据结构不做更改 需要前台处理
        var checkBoxList = [
          { id: 1, title: "储藏室" },
          { id: 2, title: "独立车位" },
          { id: 3, title: "独立车库" },
          { id: 4, title: "公用停车位" },
          { id: 5, title: "幼儿园" },
          { id: 6, title: "游泳池" },
          { id: 7, title: "会所" },
          { id: 8, title: "公园广场" }
        ]
        var peitaoList = []
        if (this.detail.peitao) {
          peitaoList = this.detail.peitao.split(",")
          console.log(peitaoList);
          this.peitaoList = []
          peitaoList.map(item => {
            checkBoxList.map(check => {
              if (check.title == (item.replace('\r', ''))) {
                this.peitaoList.push(check)
              }
            })
          })
        }
        for (var key in res.data.house.indoor) {
          this.fangyuanList.push(res.data.house.indoor[key])
        }
        if (res.data.adv && res.data.adv.length > 0) {
          this.advs = res.data.adv
        }
        this.agent = res.data.agent
        if (this.agent.id) {
          this.agent.infoDetailId = this.agent.id
        }
        if (res.data.share_user && res.data.share_user.agent_id) {
          this.agent = res.data.share_user
          this.agent.levelid = res.data.share_user.levelid ? res.data.share_user.levelid : 2
          this.agent.id = this.agent.agent_id
          this.agent.shareId = res.data.share_user.agent_id
        }
        if (res.data.community.length > 0) {
          this.housePrice = res.data.community[0]
        }
        // 获取用户信息

        this.currentUserInfo = res.data.shareUser
        if (this.currentUserInfo.adviser_id > 0) {
          this.currentUserInfo.shareType = 1
          this.currentUserInfo.sid = this.currentUserInfo.adviser_id
        } else if (this.currentUserInfo.agent_id > 0) {
          this.currentUserInfo.shareType = 2
          this.currentUserInfo.sid = this.currentUserInfo.agent_id
        } else {
          this.currentUserInfo = {
            sid: '',
            shareType: ''
          }
        }
        if (this.sid) {
          // 获取登陆状态
          checkLogin({
            fail: (res) => {

              if (res.status == 1) {
                if (this.toLogin == false) return
                this.toLogin = false
                uni.setStorageSync('backUrl', window.location.href)
                this.$navigateTo("/user/login/login")
              } else {
                this.loginState()
              }
            },
            success: (res) => {
              this.loginState()
            },
            complete: (res) => {
              this.$store.state.user_login_status = res.status
            }
          })
        } else {
          this.loginState()
        }
        for (let val in res.data.basicData) {
          this.basicData.push(res.data.basicData[val])
        }
        for (let val in res.data.tradeData) {
          this.tradeData.push(res.data.tradeData[val])
          console.log(this.tradeData)
        }
        this.lookData = res.data.lookData
        // 设置地图中心点
        if (this.detail.lat && this.detail.lng) {
          // this.getCovers('商业')
          this.mapData.covers = [
            {
              latitude: this.detail.lat,
              longitude: this.detail.lng,
              width: 30,
              height: 30,
              iconPath: '/static/icon/center.png'
            }
          ]
        }
        this.other_house = res.data.others
        this.recommend_list = res.data.recommendHouse
        if (res.data.worthLabels) {
          this.tagList = res.data.worthLabels
        }
        this.is_collect = res.data.is_collect
        document.title = this.detail.title
        uni.setNavigationBarTitle({
          title: res.data.house.title
        })
        this.video_num = res.data.house.video_list.length
        this.img_num = res.data.house.main_imgs.length
        // 合并图片视频和全景图
        this.imgs = res.data.house.main_imgs
        let imgs = res.data.house.main_imgs.map((item) => {
          return {
            type: 'main_imgs',
            url: item
          }
        })
        let videos = res.data.house.video_list.map((item) => {
          return {
            type: 'video_list',
            url: item.cover
          }
        })
        this.focus = videos.concat(imgs)
        this.focus = [...videos, ...imgs]
        if (res.data.house.vr) {
          this.vr_num = 1
          this.focus.unshift({
            type: 'vr',
            url: res.data.house.vr
          })
        } else {
          this.vr_num = 0
        }
        if (res.data.house.huxing_imgs && res.data.house.huxing_imgs.length > 0) {
          this.huxing_num = res.data.house.huxing_imgs.length
          let huxing = res.data.house.huxing_imgs.map((item) => {
            return {
              type: 'huxing_imgs',
              url: item
            }
          })
          this.focus = [...this.focus, ...huxing]
        } else {
          res.data.house.huxing_imgs = []
        }
        this.focusLen = this.focus.length
        this.cateActive = this.focus[0].type
        let link = '';
        let time = parseInt(+new Date() / 1000)
        if (this.currentUserInfo.sid) {
          link = "https://" + window.location.host + "/h5/pages/renting/detail?id=" + this.detail.id + "&isShare=1&shareType=" + this.currentUserInfo.shareType + "&shareId=" + this.currentUserInfo.sid + "&f_time=" + time
        } else {
          link = window.location.href.split('&hb=')[0]
        }
        // if (this.hb_share_query) {
        //   link += `&${this.hb_share_query}`
        // }
        this.share = {
          title: res.data.house.title,
          content: res.data.house.shi + '室' + res.data.house.ting + '厅' + res.data.house.wei + '卫/' + res.data.house.mianji + 'm²/' + res.data.house.zujin + '元/月',
          pic: res.data.house.img[0] || '',
          link: link
        }
        this.getWxConfig()
        this.show = true
        uni.hideLoading();
        this.loading = true
      })
    },
    // 转发
    handleShare() {
      this.$refs.share_popup.show()
    },
    gettap() {
      uni.showLoading({
        title: '加载中'
      });
    },
    toAgent(id) {
      if (!id) {
        return
      }
      this.$navigateTo('/pages/agent/detail?id=' + id)
    },
    checkLogin(tip, callback) {
      this.$ajax.get('member/checkUserStatus', {}, res => {
        if (res.data.code === 1) {
          callback && callback()
        } else {
          this.$store.state.user_login_status = res.data.status
          this.login_tip = tip
          this.$refs.login_popup.showPopup()
        }
      })
    },
    handleCloseLogin() {
      if (this.$store.state.user_login_status === 1) {
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if (this.$store.state.user_login_status === 2) {
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    onLoginSuccess(res) {
      this.$store.state.user_login_status = 3
      if (this.weituo_is_show) {
        console.log("登录成功后继续执行委托接口")
        this.$refs.enturst_box.handleEnturst()
      }
    },
    getSendMsg(e, type) {
      // #ifdef MP-WEIXIN
      this.$ajax.get('im/getUserReplyOfAgent.html', { page_from: type, info_id: this.id }, res => {
        if (res.data.mid) {
          this.$store.state.autoSendMsg = res.data.content || ''
          console.log(this.$store.state.autoSendMsg)
          getChatInfo(res.data.mid, 6, this.id)
        }
      })
      // #endif
      // #ifndef MP-WEIXIN
      this.checkLogin('当前操作需要绑定手机号，请输入您的手机号', () => {
        this.$ajax.get('im/getUserReplyOfAgent.html', { page_from: type, info_id: this.id }, res => {
          console.log(this.agent);
          if (res.data.mid) {
            this.$store.state.autoSendMsg = res.data.content || ''
            setTimeout(() => {
              getChatInfo(res.data.mid, 6, this.id)
            }, 500);

          }
        })
      })
      // #endif
    },
    showWeituo(type) {
      // if (type =="share"){
      //   this.isDetail=0
      // }else if (type =="bottom"){
      //   this.isDetail=1
      // }
      if (this.agent.shareId) {
        this.isDetail = 0
      } else {
        this.isDetail = 1
      }
      this.$refs.enturst_popup.show()
    },
    swiperChange(e) {
      this.swiperCurrent = e.detail.current
      this.cateActive = this.focus[this.swiperCurrent].type
    },
    switchFocus(type) {
      switch (type) {
        case 'vr':
          this.swiperCurrent = 0
          break;
        case 'video':
          this.swiperCurrent = this.vr_num
          break;
        case 'main_imgs':
          this.swiperCurrent = this.vr_num + this.video_num
          break;
        case 'huxing_imgs':
          this.swiperCurrent = this.vr_num + this.img_num + this.video_num
          break;
        default:
          this.swiperCurrent = 0
      }
    },
    toHome() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    },
    showWechat() {
      if (this.detail.shixiao === 1) {
        uni.showToast({
          title: "此信息已失效",
          icon: 'none'
        })
        return false
      }
      if (!uni.getStorageSync('token')) {
        this.$navigateTo('/user/login/login')
        this.reload = true
        return
      }
      let thumb
      if (this.imgs.length > 0) {
        thumb = this.imgs[0]
      } else if (this.focus.length > 0) {
        thumb = this.focus[0].url
      }
      this.$store.state.buildInfo = {
        id: this.id,
        title: this.detail.title,
        type: 'renting',
        image: thumb,
        desc: `${this.detail.shi}室${this.detail.ting}厅${this.detail.wei}卫`,
        price: `${this.detail.zujin ? this.detail.zujin + '元/月' : '面议'}`
      }
      getChatInfo(this.agent && this.agent.agent_id ? this.agent.agent_id : this.agent.id, 7)
    },
    handleCollect() {
      this.$store.state.allowOpen = true
      if (!this.is_collect) {
        this.collect()
      } else {
        this.noCollect()
      }
    },
    collect() {
      this.$ajax.get('house/infoCollect.html', {
        id: this.id
      }, res => {
        if (res.data.code == 1) {
          this.is_collect = 1
          uni.showToast({
            title: res.data.msg,
            duration: 2000
          })
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none",
            duration: 2000
          })
        }
      })
    },
    noCollect() {
      this.$ajax.get('house/cancelCollect.html', {
        id: this.id
      }, res => {
        if (res.data.code == 1) {
          this.is_collect = 0
          uni.showToast({
            title: res.data.msg,
            duration: 2000
          })
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none",
            duration: 2000
          })
        }
      })
    },
    // 获取地图附近周边
    getCovers(e, type = 2) {
      let params = {
        id: this.id,
        keywords: e ? e.type : '',
        type: type
      }
      let api = 'map/mapNearbyMatches.html'

      this.$ajax.get(
        api,
        params,
        res => {
          if (res.data.code != 1) {
            return
          }

          if (!res.data.done && this.moteNum < 5 && !e) {
            this.moteNum++
            this.getCovers()
            return
          }
          let covers = []
          res.data.matches.map(cover => {
            let icon, color, bgColor, title
            switch (cover.keyword) {
              case '商业':
                icon = '/static/icon/foot.png'
                bgColor = "#ffbabc"
                title = "商"
                color = "#fff"
                break
              case '教育':
                icon = '/static/icon/edu.png'
                title = "教"
                bgColor = "#34dec1"
                color = "#fff"
                break
              case '医疗':
                icon = '/static/icon/yiliao.png'
                title = "医"
                bgColor = "#feb9bb"
                color = "#fff"
                break
              case '交通':
                icon = '/static/icon/jiaotong.png'
                bgColor = "#66d1fa"
                title = "交"
                color = "#fff"
                break
              default:
                icon = '/static/icon/center.png'
            }
            if (cover.data && cover.data.length) {
              cover.data.map(item => {
                let ob = {
                  width: 30,
                  height: 30,
                  iconPath: icon,
                  latitude: item.location.lat,
                  longitude: item.location.lng,
                  title: item.title,
                  address: item.address,
                  _distance: item._distance,
                  callout: {
                    content: ((e && e.scale <= 14) || !e) ? title : item.title,
                    padding: 5,
                    fontSize: 10,
                    boxShadow: 'none',
                    bgColor,
                    color,
                    borderRadius: 4,
                    borderColor: bgColor,
                    display: 'ALWAYS'
                  },
                  distance: parseInt(item._distance)
                }
                covers.push(ob)
                return item
              })
            }

            return cover
          })
          covers.push({
            latitude: this.detail.lat,
            longitude: this.detail.lng,
            width: 30,
            height: 30,
            iconPath: '/static/icon/center.png'
          })
          covers.push({
            latitude: this.oneKm.lat,
            id: "a" + 1,
            longitude: this.oneKm.lon,
            width: -1,
            height: -1,
            label: {
              content: '1公里',
              padding: 2,
              borderRadius: 2,
              bgColor: "inherit",
              color: "#ff0000",
              display: 'ALWAYS',
              fontSize: 10,
              borderWidth: 0,
              x: -15,
              y: 5,
              anchorX: -15,
              anchorY: 5,
              borderColor: '#ffffff'
            },
            iconPath: '/static/icon/center.png'
          })
          covers.push({
            latitude: this.twoKm.lat,
            longitude: this.twoKm.lon,
            width: -1,
            height: -1,
            id: "a" + 2,
            label: {
              content: '2公里',
              padding: 2,
              borderRadius: 2,
              bgColor: "inherit",
              color: "#ff9c00",
              display: 'ALWAYS',
              fontSize: 10,
              borderWidth: 0,
              x: -15,
              y: 5,
              anchorX: -15,
              anchorY: 5
            },
            iconPath: '/static/icon/center.png'
          })
          covers.push({
            latitude: this.threeKm.lat,
            longitude: this.threeKm.lon,
            width: -1,
            height: -1,
            id: "a" + 3,
            label: {
              content: '3公里',
              padding: 2,
              borderRadius: 2,
              bgColor: "inherit",
              color: "#fee500",
              display: 'ALWAYS',
              fontSize: 10,
              borderWidth: 0,
              x: -15,
              y: 5,
              anchorX: -15,
              anchorY: 5
            },
            iconPath: '/static/icon/center.png'
          })
          this.mapData.covers = covers
        },
        err => { }
      )
    },
    handleCloseLogin() {
      if (this.$store.state.user_login_status === 1) {
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if (this.$store.state.user_login_status === 2) {
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    onLoginSuccess(res) {
      this.$store.state.user_login_status = 3
      if (this.weituo_is_show) {
        console.log("登录成功后继续执行委托接口")
        this.$refs.enturst_box.handleEnturst()
      }
    },
    showLoginPopup(tip) {
      this.login_tip = tip
      this.$refs.login_popup.showPopup()
    },
    handleCreat() {
      this.$navigateTo(`${location.origin}/wapi/poster/branch?type=3&id=${this.id}&header_from=2`)
    },
    showCopywriting() {
      const query = uni.createSelectorQuery().in(this)
      query.select('#copy-text').fields({ rect: true, scrollOffset: true, size: true }, data => {
        this.text_popup_height = data.height + 'px'
      }).exec();
      this.copy_success = false
      this.$refs.text_popup.show()
      this.$refs.share_popup.hide()
    },

    getShortLink() {
      let time = parseInt(+new Date() / 1000)
      if (this.currentUserInfo.sid) {
        this.link = "https://" + window.location.host + "/h5/pages/renting/detail?id=" + this.detail.id + "&isShare=1&shareType=" + this.currentUserInfo.shareType + "&shareId=" + this.currentUserInfo.sid + "&f_time=" + time
      } else {
        this.link = window.location.href
      }
      this.$ajax.get('build/shortUrl.html', { page_url: this.link }, res => {
        if (res.data.code === 1) {
          this.link = res.data.short_url
        }
      })
    },
    copywriting() {
      // let tel=""
      // if (this.agent&&this.agent.shareId){   //分享过来的
      //   tel='【电话】'+this.agent.tel+'\n'
      // }else if(this.agent&&!this.agent.shareId&&this.agent.levelid>1){  //经纪人 
      //   tel='【电话】'+this.detail.tel+'\n'
      // }
      const text = `${this.detail.title}
${this.housePrice && this.housePrice.title ? '【小区】' + this.housePrice.title + '\n' : ''}【户型】${this.detail.shi}室${this.detail.ting}厅${this.detail.wei}卫${this.detail.mianji}m²
【租金】${this.detail.zujin ? this.detail.zujin + '元/月' : '面议'}
【类型】${this.detail.catname}
【卖点】${this.detail.label.length > 0 ? this.detail.label.map(item => item.name).join(' ') + '\n' : ''}【楼层】${this.detail.floor ? this.detail.floor + '/共' + this.detail.louceng + '层' : '共' + this.detail.louceng + '层'}
【查看】${this.link}`
      this.copyContent(text, () => {
        this.copy_success = true
      })
    },
    // #ifndef H5
    copyContent(cont) {
      uni.setClipboardData({
        data: cont,
        success: res => {
          // uni.showToast({
          //   title: "复制成功",
          //   icon: "none"
          // })
        }
      })
    },
    // #endif
    // #ifdef H5
    copyContent(cont, callback) {
      let oInput = document.createElement('textarea')
      oInput.value = cont
      document.body.appendChild(oInput)
      oInput.style.opacity = 0
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      oInput.blur()
      oInput.remove()
      if (callback) callback()
    },
    // #endif
    handleTel() {

      if (this.detail.shixiao === 1) {
        uni.showToast({
          title: "此信息已失效",
          icon: 'none'
        })
        return false
      }

      this.tel_params = {}
      if (this.agent && this.agent.shareId) {
        this.tel_params = {
          type: 3,
          callee_id: this.agent.id,
          scene_type: 4,
          scene_id: this.id,
        }
      } else {
        this.tel_params = {
          type: 4,
          callee_id: this.id,
          scene_type: 4,
          scene_id: this.id,
        }
      }
      this.tel_params.intercept_login = true
      this.tel_params.success = (res) => {
        // console.log(res)
        this.tel_res = res.data
        this.show_tel_pop = true
      }
      this.tel_params.fail = (res) => {
        switch (res.data.code) {
          case -1:
            uni.removeStorageSync('token')
            this.reload = true
            this.$navigateTo('/user/login/login')
            break
          case 2:
            this.reload = true
            this.$navigateTo('/user/bind_phone/bind_phone')
            break
          case -5:
            showModal({
              title: "安全验证，防恶意骚扰已开启",
              content: "验证后可免费发布查看信息。",
              confirm: () => {
                if (res.data.is_agent) {
                  this.$navigateTo('/user/member_upgrade')
                } else {
                  this.$navigateTo('/user/member_upgrade?is_personal=1')
                }
              }
            })
            break
          case -10:
            console.log("账号被封禁")
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            break
          default:
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
        }
      }
      allTel(this.tel_params)
    },
    retrieveTel() {
      allTel(this.tel_params)
    },
    preImg(index) {
      let img_index = index - this.detail.videos.length
      let img_urls = this.detail.main_imgs.map((item) => {
        return formatImg(item, 'w_860')
      })
      uni.previewImage({
        current: img_urls[img_index],
        indicator: "number",
        urls: img_urls
      })
    },
    preHuxing(index) {
      let img_index = index - (this.detail.videos.length + this.detail.main_imgs.length)
      let img_urls = this.detail.huxing_imgs.map((item) => {
        return formatImg(item, 'w_860')
      })
      uni.previewImage({
        current: img_urls[img_index],
        indicator: "number",
        urls: img_urls
      })
    },
    toVr() {
      this.$navigateTo('/vr/detail?infoid=' + this.id)
    },
    preVideo(url) {
      this.$navigateTo('/vr/prevideo?type=2&id=' + this.id)

      // this.$navigateTo('/vr/preview_video?url=' + url)
    },
    toPosition() {
      if (this.detail.lat > 0 && this.detail.lng > 0) {
        uni.openLocation({
          latitude: parseFloat(this.detail.lat),
          longitude: parseFloat(this.detail.lng),
          name: this.detail.cmname,
          address: this.detail.address,
          success: function () {
            console.log('success');
          }
        });
      }
    },
    viewMap(e) {
      if (this.detail.lat > 0 && this.detail.lng > 0) {
        this.$navigateTo("/propertyData/map/map?id=" + this.detail.id + '&type=2&lat=' + this.detail.lat + '&lng=' + this.detail.lng)
      } else {
        uni.showToast({
          title: "未标记地图位置",
          icon: "none"
        })
      }
    },
    toJubao() {
      this.$navigateTo('/user/inform/inform?id=' + this.id + '&type=1')
    },
    // #ifdef MP-BAIDU
    baiduShareImg() {
      swan.shareFile({
        filePath: this.cardImg,
        success: res => {
          // uni.showToast({
          // 	title:"分享成功"
          // })
        },
        fail: err => {
          uni.showToast({
            title: "分享失败",
            icon: "none"
          })
        }
      })
    },
    // #endif
    // 将房源加入对比
    addContrast(info_id) {
      this.$ajax.get('house/addContrast.html', { info_id: this.id }, res => {
        if (res.data.code === -1) {
          this.$store.state.user_login_status = 1
          // 检测是否已添加
          if (this.$store.state.temp_renting_contrast_ids.includes(info_id)) {
            // uni.showToast({
            //   title:"该房源已经添加",
            //   icon:'none'
            // })
            if (this.login_status > 1) {
              this.$navigateTo('/contrast/info_list?type=2')
            } else {
              this.$navigateTo(`/contrast/info_list?type=2&no_login=1`)
            }
            return
          }
          this.$store.state.temp_renting_contrast_ids.push(info_id)
          return
        }
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg
          })
          this.contrastCount++
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      }, err => {

      }, { disableAutoHandle: true })
    },
    toContrast() {
      this.addContrast()
    },
    showSharePop() {
      this.getShortLink()
      this.$refs.share_popup.show()
    },
    goBack() {
      if (this.sid) {
        uni.switchTab({
          url: '/pages/index/index'
        });
      } else {
        this.$navigateBack()
      }
    },
    doNot() {

    },
    checkHb() {
      this.$ajax.get('WxMoney/checkHb', { info_id: this.id, info_type: 2, hb: this.hb }, res => {

        if (res.data.code == -1) {
          uni.setStorageSync('backUrl', window.location.href)
          this.$navigateTo("/user/login/login")
        }
        if (res.data.help_fail_desc) {
          uni.showToast({
            title: res.data.help_fail_desc,
            icon: 'none'
          })
          return
        }
        if (res.data.code == 1) {
          this.hb_info = res.data.hb_info
          this.is_help_link = res.data.is_help_link   // 是否是助力链接进来的 如果是 （值为1）弹出红包弹框
          this.$nextTick(() => {
            if (this.is_help_link == 1) {
              // this.timeDownStart()
              this.$refs.hongbao.showPopup()    //页面不主动弹出领取弹框改为入口打开显示弹框 或者通过助力链接打开时主动弹出
            }

          })
          // this.hb_share_query = res.data.hb_share_query
          this.map_key = res.data.txmapwapkey
          if (this.hb_info.is_open) {
            if (res.data.help_task && res.data.help_task.id) {
              this.task_id = res.data.help_task.id
            } else if (this.isLogin) {
              // this.createHb()
            }
            // if (this.hb_share_query) {
            //   this.share.link += `&${this.hb_share_query}`
            // }
            if (this.hb_info.limit_area) {
              this.getWxConfig(['getLocation', 'updateAppMessageShareData', 'updateTimelineShareData'], (wx) => {
                this.wx = wx
              })
            } else {
              this.getWxConfig()
            }
            // if (this.hb_info.limit_area) {
            //   this.getCity()
            // }
          }
        }
      }, err => { console.log(err) }, { disableAutoHandle: true })
    },
    createHb() {
      let form = {
        info_id: this.id,
        info_type: 2,
        hb: this.hb
      }
      this.$ajax.post('WxMoney/createhb', form, res => {
        if (res.data.code == 1) {
          this.hb_result = res.data.hb_result
          // this.$nextTick(()=> {
          //   this.timeDownStart()
          //   this.$refs.hongbao.showPopup()
          // })
        } else if (res.data.help_fail_desc) {
          uni.showToast({
            title: res.data.help_fail_desc,
            icon: 'none'
          })
        }
      })
    },
    getCity(options = {}) {
      this.$store.state.getPosition(this.wx, (res) => {
        this.lat = res.lat
        this.lng = res.lng
        getLocation({
          latitude: res.lat,
          longitude: res.lng,
          map_key: this.map_key || '',
          success: cityRes => {
            this.current_city = cityRes.city
            options.success && options.success(res)
          },
          fail: err => {
            console.log(err)
            options.fail && options.fail(err)
          }
        })
      })
    },
    playAudio() {
      this.innerAudioContext = uni.createInnerAudioContext();
      // this.innerAudioContext.autoplay = true;
      this.innerAudioContext.loop = false;
      this.innerAudioContext.src = this.tuiguang_mp3;
      // this.innerAudioContext.pause()
      this.innerAudioContext.onPlay(() => {
        console.log('开始播放');
        this.playing = true
      });
      this.innerAudioContext.onEnded(() => {
        console.log('播放结束');
        this.playing = false
      });
      this.innerAudioContext.onError((res) => {
        this.playing = false
        console.log("播放失败")
        console.log(res.errMsg);
        console.log(res.errCode);
      });
      this.innerAudioContext.play()
    },
    openHb() {
      if (!this.playing) this.playAudio()
      if (this.hb_info.limit_area && !this.current_city) {
        uni.showLoading({
          title: '获取位置信息中，请稍等'
        });
        this.getCity({
          success: () => {
            uni.hideLoading()
            this.getHb()
          }, fail: (err) => {
            console.log(err)
            uni.hideLoading()
            this.getHb()
          }
        })
      } else {
        this.getHb()
      }
    },
    getHb() {
      let form = {
        info_id: this.id,
        info_type: 2,
        hb: this.hb,
        area: this.current_city,
      }
      this.$ajax.post('WxMoney/help', form, (res) => {
        uni.showToast({
          title: res.data.msg,
          icon: 'none'
        })
        if (res.data.code == 1) {
          this.$refs.hongbao.hidenPopup()
          //  let  link = this.share.link.split("&hb=")[0]
          //       this.share.link = link +`&${res.data.hb_share_query}`
          //       this.getWxConfig()
          //       setTimeout(() => {
          // this.$navigateTo(`/redEnvelopes/index?info_id=${this.id}&info_type=4&task_id=${this.task_id}`)
          // }, 500);


        } else {
          if (res.data.state == 1) {
            this.$refs.hongbao.hidenPopup()
          } else if (res.data.state == 2) {
            this.$refs.hongbao.hidenPopup()
            setTimeout(() => {
              showModal({
                content: res.data.msg + ',您也可以参与领取红包',
                confirm: res => {
                  this.$navigateTo(`/redEnvelopes/index?info_id=${this.id}&info_type=2`)
                }
              })
            }, 1000);

          }
          // uni.showToast({
          //   title: res.data.msg,
          //   icon: 'none'
          // })
        }
      })
    },
    // 倒计时
    timeDownStart() {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        if (this.hb_result.expire_seconds > 0) {
          this.hb_result.expire_seconds--
        } else {
          clearInterval(this.timer)
        }
      }, 1000)
    },
    toHbHelp() {
      this.$navigateTo(`/redEnvelopes/index?info_id=${this.id}&info_type=2&shareType=${this.currentUserInfo.shareType}&task_id=${this.task_id}`)
    },
    // 判断登录
    loginState() {
      checkLogin({
        success: (res) => {
          this.isLogin = true
          this.checkHb()
        },
        fail: (res) => {
          this.isLogin = false
          this.checkHb()
        },
        complete: (res) => {
          this.$store.state.user_login_status = res.status
        },
      })
    },
    toComminuty() {
      if (this.agent.shareId) return
      this.$navigateTo('/pages/house_price/detail?id=' + this.housePrice.id)
    }
  },
  onShareAppMessage(res) {
    let link = ''
    let time = parseInt(+new Date() / 1000)
    if (this.currentUserInfo.sid) {
      link = "/pages/renting/detail?id=" + this.detail.id + "&isShare=1&shareType=" + this.currentUserInfo.shareType + "&shareId=" + this.currentUserInfo.sid + "&f_time=" + time
    } else {
      link = "/pages/renting/detail?id=" + this.detail.id
    }
    return {
      // {{detail.shi||''}}室{{detail.ting||''}}厅{{detail.wei||''}}卫
      title: this.detail.title || "",
      content: this.detail.shi + '室' + this.detail.ting + '厅' + this.detail.wei + '卫' + '/' + this.detail.mianji + 'm²/' + ((this.detail.fangjia == '面议' || this.detail.fangjia == '0') ? '面议' : this.detail.fangjia + '万'),
      imageUrl: this.detail.img[0] ? formatImg(this.detail.img[0], 'w_6401') : "",
      path: link
    }
  },
  // #ifdef H5
  onNavigationBarButtonTap(option) {
    if (option.index == 0) {
      // this.handleCreat()
      this.$refs.share_popup.show()
      this.getShortLink()
    }
    if (option.index == 1) {
      this.handleCollect()
    }
  },
  // #endif
  // #ifdef APP-PLUS
  onNavigationBarButtonTap(option) {
    if (option.index == 0) {
      this.showPopup = true
      // this.$refs.popup.show()
      this.$refs.share_popup.show()
    }
    if (option.index == 1) {
      this.handleCollect()
    }
  },
  onBackPress() {
    if (this.showPopup) {
      this.showPopup = false;
      // this.$refs.popup.hide()
      this.$refs.share_popup.hide()
      return true;
    }
  }
  // #endif
}
</script>

<style scoped lang="scss">
.ershou_detail_content {
  padding-bottom: 160rpx;
  color: #333;
  background-color: #fff;
}

.wnsb-box {
  width: 20%;
  margin-bottom: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .peitao_name {
    font-size: 24rpx;
    color: #000;
    white-space: nowrap;
  }
}

.peitao {
  width: 60rpx;
  height: 60rpx;
  background-size: 100%;
}

view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.flex-row {
  flex-direction: row;
}

.back {
  position: fixed;
  width: 100%;
  height: 88rpx;
  padding: 2px 10rpx;
  align-items: center;
  justify-content: space-between;
  z-index: 1;

  .title-con {
    flex: 1;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 32rpx;
    // width: calc(100% - 64px);
    // over
  }

  .icon-box {
    // height: 44px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    padding: 8px;
    background: rgba(0, 0, 0, 0.6);
    justify-content: center;
    align-items: center;

    &.icon-share {
      justify-self: end;
      margin-left: auto;
    }
  }
}

.selection {
  margin-top: 32rpx;

  .s_tag {
    width: 96rpx;
    height: 52rpx;
    text-align: center;
    color: #512c19;
    border-radius: 8rpx;
    background-image: linear-gradient(to bottom right, #ffe9bc, #f0cc8b);
  }

  text {
    font-size: 28rpx;
    line-height: 52rpx;
    color: #512c19;
  }

  image {
    width: 36rpx;
    height: 36rpx;
    margin-left: 24rpx;
    margin-right: 12rpx;
    position: relative;
    top: 10rpx;
  }
}

.info-line {
  line-height: 1;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;

  .label {
    font-size: 32rpx;
    color: #999;
  }

  .data {
    flex: 1;
    padding-left: 20rpx;
    font-size: 32rpx;
  }

  .line-right {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}

//  智能房源评测标签 
.tag_box {
  flex-wrap: wrap;
  margin: 20rpx 0;

  .tag {
    background: #fff1f5;
    color: #fb656a;
    font-size: 22rpx;
    align-items: center;
    height: 48rpx;
    padding: 0 16rpx 0 8rpx;
    margin-top: 20rpx;
    margin-right: 20rpx;
    border-radius: 4rpx;
    font-weight: bold;

    .tag_icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 4rpx;
    }
  }
}

// 顶部焦点图
.focus-box {
  position: relative;

  swiper.banner {
    height: 75vw;
  }

  .swiper-item {
    height: 100%;
  }

  .swiper-item image {
    width: 100%;
    height: 100%;
  }

  .swiper-item image.video-icon {
    width: 16vw;
    height: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0);
  }

  .number {
    position: absolute;
    padding: 4rpx 20rpx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    border-top-right-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    left: 0;
    bottom: 20rpx;
    color: #fff;
  }

  .img-total {
    position: absolute;
    padding: 4rpx 20rpx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    border-radius: 20rpx;
    right: 20rpx;
    bottom: 20rpx;
    color: #fff;
  }

  .cate-box {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 24rpx;
    display: block;
    text-align: center;
    font-size: 0;

    .cate-list {
      display: inline-block;
      border-radius: 6rpx;
      overflow: hidden;
    }
  }

  .cate {
    display: inline-block;
    padding: 8upx 20upx;
    font-size: 22rpx;
    background-color: #fff;

    &.active {
      background: linear-gradient(45deg, #fd9ea3, #fb656a);
      color: #fff;
    }
  }
}

.container {
  padding: 0 48rpx;
}

.block {
  margin-top: 24rpx;

  >.label {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    line-height: 1;
    padding: 24rpx 0;
    margin-bottom: 24rpx;
    font-size: 40rpx;
    font-weight: bold;

    &.mgb0 {
      margin-bottom: 0;
    }

    .more {
      padding: 8rpx;
      font-size: 22rpx;
      font-weight: initial;
      color: #999;

      &.pd-r-48 {
        padding-right: 48rpx;
      }
    }
  }
}

// 房源标题
.house_title {
  .attr1 {
    padding: 2rpx 10rpx;
    margin-right: 10rpx;
    line-height: 1;
    font-size: 22rpx;
    background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
    color: #fff;
  }

  .attr2 {
    padding: 2rpx 10rpx;
    margin-right: 10rpx;
    line-height: 1;
    font-size: 22rpx;
    background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
    color: #fff;
  }

  font-size: 40rpx;
  line-height: 1.5;
  margin: 20rpx 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 主要信息
.house_info {
  overflow: hidden;
}

.main_info-box {
  align-items: center;
  justify-content: space-between;
  line-height: 1;

  .huxing {
    align-items: flex-end;
    line-height: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .value {
      font-size: 36rpx;
      font-weight: bold;
      color: $uni-color-primary;
    }

    .unit {
      font-size: 26rpx;
      margin-bottom: 2rpx;
      color: $uni-color-primary;
    }
  }

  .price {
    align-items: flex-end;
    margin-right: 24rpx;
  }

  .stw {
    align-items: flex-end;
    margin-right: 24rpx;
  }

  .mianji {
    align-items: flex-end;
    font-size: 22rpx;
    color: $uni-color-primary;
  }

  .btn {
    line-height: 64rpx;
    padding: 0 30rpx;
    color: #fff;
    background: $uni-color-primary;
    box-shadow: 0 2px 8px 0 rgba($uni-color-primary, 0.40);
    border-radius: 32rpx;
    min-width: 140rpx;
  }
}

// 房源标签
.label-list {
  display: block;
  margin-top: 24rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  .attr1 {
    padding: 4rpx 10rpx;
    margin-right: 10rpx;
    line-height: 1;
    font-size: 22rpx;
    background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
    color: #fff;
  }

  .attr2 {
    padding: 6rpx 10rpx;
    margin-right: 10rpx;
    line-height: 1;
    font-size: 22rpx;
    background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
    color: #fff;
  }

  .label {
    line-height: 1;
    padding: 4rpx 10rpx;
    font-size: 22rpx;
    color: #999;
    white-space: nowrap;
    border: 0.5rpx solid #d8d8d8;
    // background: #f2f2f2;
    margin-left: 8rpx;
    display: inline-block;
  }
}

// 其他信息
.info-list {
  flex-wrap: wrap;
  position: relative;

  .xiajia_icon {
    position: absolute;
    width: 180rpx;
    height: 180rpx;
    left: 0;
    right: 0;
    top: -32rpx;
    margin: auto;
  }

  .info-list-item {
    line-height: 1;
    margin-bottom: 48rpx;
    // padding: 10rpx 0;
    min-width: 30%;
    flex: 1;

    &.mgb0 {
      margin-bottom: 0;
    }

    .label {
      font-size: 24rpx;
      margin-bottom: 24rpx;
      color: #999;
    }

    .data {
      font-size: 32rpx;
    }
  }
}

// 房源介绍
.house_desc {
  margin-top: 24rpx;

  .content_info {
    line-height: 1.8;

    // min-height: 160rpx;
    .desc {
      margin-bottom: 36rpx;
      color: #666;

      &.peitao_title {
        margin-bottom: 0;
      }
    }

    .cate {
      margin-bottom: 20rpx;
      line-height: 1;
      padding-left: 10rpx;
      border-left: 6rpx solid $uni-color-primary;
      font-weight: bold;
      color: #333;
    }
  }

  // 经纪人信息
  .agent-box {
    padding: 20rpx;
    justify-content: space-between;
    align-items: center;
    background-color: #f2f2f2;

    .header_img {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }

    .agent_info {
      flex: 1;
      overflow: hidden;
      margin-right: 20rpx;

      .name {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 32rpx;
        margin-bottom: 10rpx;
      }

      .shop_name {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 22rpx;
        color: #999;
      }
    }

    .into-btn {
      height: 60rpx;
      line-height: 60rpx;
      padding: 0 24rpx;
      border-radius: 30rpx;
      font-size: 24rpx;
      color: #fff;
      background-color: $uni-color-primary;
    }
  }

  .selling_point {
    margin-top: 48rpx;

    .label {
      margin-bottom: 10rpx;
      color: #999;
    }

    .content_info {
      line-height: 1.5;
    }

    .view_more {
      margin: 10rpx 48rpx;
      padding: 20rpx;
      text-align: center;
      font-size: 30rpx;
      background-color: #fff4f4;
      color: $uni-color-primary;
    }
  }
}

// 房源标签
.wnsb-list {
  margin-bottom: 24rpx;
  flex-wrap: wrap;

  .label {
    line-height: 1;
    padding: 4rpx 10rpx;
    font-size: 22rpx;
    color: #999;
    border: 0.5rpx solid #d8d8d8;
    // background: #f2f2f2;
    margin-left: 8rpx;
    margin-bottom: 8rpx;
  }
}

// 轮播广告图
.ext_swiper {
  margin-top: 48rpx;
  height: 140rpx;

  swiper-item {
    height: 100%;
    background-color: #f5f5f5;
    border-radius: 16rpx;
    overflow: hidden;
    position: relative;

    >image {
      height: 100%;
      width: 100%;
    }

    .marker {
      line-height: 1;
      padding: 4rpx 10rpx;
      position: absolute;
      right: 12rpx;
      bottom: 10rpx;
      font-size: 20rpx;
      border-radius: 4rpx;
      background-color: rgba($color: #000000, $alpha: 0.5);
      color: #fff;
    }
  }
}

// 小区
.community-box {
  justify-content: space-between;

  >.label {
    justify-content: space-between;

    .into {
      font-weight: initial;
      color: #999;
    }
  }
}

.community {
  margin-bottom: 24rpx;

  .img {
    width: 140rpx;
    height: 140rpx;
    margin-right: 20rpx;
    border-radius: 8rpx;
  }

  >.info {
    flex: 1;
    justify-content: space-between;
    line-height: 1;
    overflow: hidden;

    .price_row {
      justify-content: space-between;

      .into {
        line-height: 1;
        font-size: 22rpx;
        align-items: center;
        color: #999;
      }
    }

    .label {
      display: inline-block;
      text-align-last: justify;
      min-width: 120rpx;
      margin-right: 15rpx;
      font-size: 28rpx;
      font-weight: initial;
      color: #999;
    }

    .value {
      flex: 1;
      font-size: 28rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .price {
      line-height: 1;
      align-items: center;
      font-size: 28rpx;
      font-weight: bold;
      color: $uni-color-primary;

      .unit {
        margin-left: 8rpx;
        font-size: 22rpx;
        font-weight: initial;
        color: #333;
      }
    }

    .increase {
      align-items: center;

      .increase-text {
        align-items: center;
        flex-direction: row
      }

      .ping {
        font-size: 28rpx;
      }

      .value {
        font-size: 28rpx;
        font-weight: bold;
        color: $uni-color-primary;

        &.down {
          color: #179B16;
        }
      }

      .unit {
        position: relative;
        // top: 5rpx;
        left: 5rpx;
        color: $uni-color-primary;

        &.down {
          color: #179B16;
        }
      }
    }

    .yaers {
      align-items: center;
    }
  }
}

// TA的其他房源

.other_house-list {
  height: 50vw;

  .swiper-item {
    margin-right: 24rpx;

    .img-box {
      border-radius: 8rpx;
      overflow: hidden;
      height: 200rpx;
    }

    image {
      width: 100%;
      height: 100%;
    }

    .house_type {
      display: block;
      margin-top: 10rpx;
      font-size: 32rpx;
      font-weight: bold;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .aligin-end {
      align-items: flex-end;
    }

    .stw {
      align-items: center;
      margin-top: 6rpx;
      margin-right: 10rpx;
      font-size: 24rpx;
      color: #999;
    }

    .price-box {
      line-height: 1;
      align-items: flex-center;
      justify-content: space-between;
      margin-top: 10rpx;

      .db_btn {
        line-height: 30rpx;
        padding: 0 8rpx;
        border-radius: 15rpx;
        font-size: 22rpx;
        border: 1rpx solid $uni-color-primary;
        color: $uni-color-primary;
      }
    }

    .mianji {
      font-size: 22rpx;
      color: #999;
    }

    .price {
      font-size: 32rpx;
      font-weight: bold;
      color: $uni-color-primary;
    }

    .unit {
      margin-left: 8rpx;
    }
  }
}

.more_btn {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin: 0 48rpx;
  padding: 0 24rpx;
  height: 80rpx;
  border-radius: 8rpx;
  color: $uni-color-primary;
  background-color: rgba($uni-color-primary, 0.1);

  &.no_bg {
    background-color: rgba($uni-color-primary, 0);
  }

  &.has_agent {
    background-color: rgba($uni-color-primary, 0);
    color: #333;
  }
}

// 底部操作菜单
.bottom-bar {
  background-color: #fff;
  height: 110rpx;
  padding: 15rpx 48rpx;
  left: 0;
  z-index: 10;

  .bar-left {
    padding-right: 28rpx;
    justify-content: space-between;
  }

  .icon-btn {
    // width: 100rpx;
    align-items: center;
    padding: 0;
    margin: 0;
    background-color: #fff;
    line-height: initial;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // flex: 1;
    max-width: 130rpx;
    padding-right: 32rpx;
    overflow: hidden;
    position: relative;

    &.last {
      padding-right: 48rpx;
    }

    // & ~ .icon-btn {
    //   margin-left: 24rpx;
    // }
    .header_img {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
    }

    text {
      line-height: 1;
      font-size: 22rpx;
      color: #999;
      display: inline-block;
      width: 100%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .badge {
      display: inline-block;
      box-sizing: border-box;
      width: auto;
      position: absolute;
      top: 0;
      left: 32rpx;
      // right: 38rpx;
      height: 28rpx;
      padding: 0 8rpx;
      min-width: 28rpx;
      border-radius: 14rpx;
      font-size: 22rpx;
      background-color: $uni-color-primary;
      color: #fff;
    }
  }

  .bar-btn {
    // width: 220rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    padding: 0;
    margin: 0;
    border-radius: 0;
    color: #fff;

    &.alone {
      border-radius: 40rpx;
    }

    &.btn1 {
      background: #FBAC65;
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.05);
      border-top-left-radius: 40rpx;
      border-bottom-left-radius: 40rpx;
    }

    &.btn2 {
      background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
      box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.30);
      border-top-right-radius: 40rpx;
      border-bottom-right-radius: 40rpx;
    }
  }
}

.card-img {
  width: 80%;
  margin: 0 10%;
  padding: 40upx 0;
}


// 生成海报后的弹窗
.share-box {
  padding: 20upx 0;
  background-color: #fff;

  .tip {
    padding: 10px;
    width: 100%;
    font-weight: 700;
    box-sizing: border-box;
    text-align: center;
  }

  button {
    line-height: initial;
    padding: 10upx 20upx;
    background-color: #fff;
  }

  .wechat-img {
    width: 60vw;
    height: 60vw;
  }

  .item {
    text-align: center;
    padding: 10upx 20upx;
    line-height: inherit;
  }
}


/* #ifdef H5 */
// H5海报
#card {
  padding-bottom: 15px;
  width: 100%;
  position: fixed;
  left: -110vw;

  .card_img-box {
    width: 100%;
    height: 68vw;
    overflow: hidden;
  }

  .card_img-box image {
    width: 100%;
    height: 100%;
  }

  .card_info-box {
    margin: 40upx;
    padding: 20upx 30upx;
    font-size: 30upx;
    color: #555;
    background-color: #f3f3f3
  }

  .text-right {
    text-align: right
  }

  .card_info-box .title {
    font-size: 40upx;
    height: 100upx;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 40upx;
    color: #000;
    // -webkit-line-clamp: 2;
    // display: -webkit-box;
  }

  .card_info-box .price {
    font-weight: bold;
    color: #f65354;
  }

  .card-footer {
    margin: 40upx;
    font-size: 34px;
    line-height: 50upx;
    color: #333;

    .text {
      padding: 20upx;
    }

    .tip {
      font-size: 26upx;
      color: #666
    }

    .qrcode {
      width: 30vw;
      height: 30vw
    }
  }
}

/* #endif */

canvas.hide {
  position: fixed;
  left: -100vw;
}




// 免责声明
.shengming {
  margin-top: 32rpx;
  color: #999;

  .shengming_title {
    font-size: 30rpx;
    margin-bottom: 16rpx;
    justify-content: space-between;
    align-items: center;

    .label {
      line-height: 1;
      padding: 4rpx 8rpx;
      font-size: 22rpx;
      border: 1rpx solid #d8d8d8;
    }
  }

  .shengming_content {
    font-size: 26rpx;
    line-height: 1.8;
  }
}

// 复制文案
.copy-text-box {
  padding: 24rpx 32rpx;
  background-color: #fff;
  width: 600rpx;
  // height: 100%;
  margin-left: 75rpx;
  border-radius: 16rpx;

  .title {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }

  .info-row {
    line-height: 1.6;
    color: #333;

    .label {
      color: #999;
    }

    .value {
      flex: 1;

      &.highlight {
        color: $uni-color-primary;
      }
    }
  }

  .button {
    align-items: center;
    justify-content: center;
    line-height: 64rpx;
    width: 236rpx;
    text-align: center;
    border-radius: 32rpx;
    margin: 32rpx auto;
    background: #FB656A;
    box-shadow: 0 2px 8px 0 rgba(251, 101, 106, 0.40);
    color: #fff;
  }

  .disabled-btn {
    background-color: #f5f5f5;
    box-shadow: none;
    color: #999;

    >.text {
      margin-left: 12rpx;
    }
  }
}

// 报名按钮
.btn_list-box {
  margin-top: 32rpx;

  .btn-item {
    padding: 20rpx 5rpx;
    flex: 1;
    align-items: center;
    justify-content: center;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.05);
    color: $uni-color-primary;

    ~.btn-item {
      margin-left: 14rpx;
    }

    text {
      font-size: 32rpx;
      margin-left: 16rpx;
    }

    .img {
      width: 40rpx;
      height: 40rpx;
      overflow: hidden;

      .img_c {
        width: 100%;
        height: 100%;
      }
    }
  }
}

// 房源动态
.house_news {

  .house_news_info {
    padding: 20rpx 0;
    margin-left: 20rpx;
    margin-right: 20rpx;

    &_item {
      flex: 1;
      align-items: center;

      &_num {
        margin-bottom: 20rpx;
      }
    }
  }
}

.time_line {
  padding: 20upx 30upx;
  margin-top: 40rpx;

  .item {
    position: relative;
    padding: 0 20upx 36upx 32upx;
    border-left: 2rpx solid #f3f3f3;

    &:last-child {
      border-left: 0;
    }

    .line-item {
      margin-top: -20rpx;

      .content_c {
        justify-content: space-between;
        align-items: center;

        .content_sub {
          color: #fb656a;
        }

        .blod {
          color: #ff656b;
          font-weight: 600;
        }
      }

      .line-header {
        margin-top: 15rpx;
      }
    }

    .title {
      font-size: 28upx;
      margin-top: -10rpx;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
    }

    .time {
      font-size: 24upx;
      font-weight: bold;
      color: #999;
    }


  }

  .item::after {
    content: "";
    height: 12upx;
    width: 12upx;
    box-sizing: border-box;
    border-radius: 50%;
    position: absolute;
    // border: 4rpx solid #f3f3f3;
    background-color: #f3f3f3;
    left: -6rpx;
    top: -6rpx;
  }

  .item.current::before {
    content: "";
    height: 20upx;
    width: 20upx;
    border-radius: 50%;
    background-color: #3399ff;
    position: absolute;
    left: -12upx;
    top: 0;
    z-index: 2;
  }
}

.entrant_button {
  padding: 20rpx 5rpx;
  width: 80%;
  margin: 20rpx auto 0;
  background-color: rgba(255, 101, 107, 0.05);
  color: #ff656b;
  text-align: center;
  font-size: 30rpx;
}

.hongbao {
  position: absolute;
  right: 20rpx;
  width: 120rpx;
  height: 120rpx;
  white-space: nowrap;
  overflow: hidden;
  bottom: 72rpx;

  .hb_content {
    width: 120rpx;
    height: 120rpx;
    display: block;
    position: absolute;
    left: 120rpx;
    top: 0;
  }

  image {
    position: absolute;
    left: 0;
    top: 0;
    width: 120rpx;
    height: 120rpx;
  }
}

.list-texto {
  justify-items: center;
  display: flex;
  justify-content: flex-start;
  flex-direction: row;
  margin-top: 20rpx;
  width: 100%;
}

.list-texto:nth-child(1) {
  margin-top: 0rpx;
}

.list-texto text:nth-child(1) {
  color: #999999;
  margin-right: 20rpx;
}

.list-texto text:nth-child(2) {
  color: #333333;
}
</style>