<template>
    <view class="client-list">
        <view class="card-box" v-if="my_info.adviser>0||my_info.levelid>1">
            <view class="set_card" @click="toSet()">+名片设置</view>
            <agent-card :infoData="my_info">
                <template v-slot:options="{infoData}">
                    <view class="operation-box flex-box">
                        <view class="operation" @click="addInfo(infoData)">
                            <my-icon type="fabu1" size="24" color="#1296db"></my-icon>
                            <text class="text">{{infoData.adviser>0?'发布楼盘动态':'发布信息'}}</text>
                        </view>
                        <view class="operation" @click="myCard(infoData)">
                            <my-icon type="weixin2" size="24" color="#09de74"></my-icon>
                            <text class="text">我的名片</text>
                        </view>
                        <view class="operation" @click="toMore(infoData)">
                            <my-icon type="gengduo" size="24" color="#ff5e66"></my-icon>
                            <text class="text">更多</text>
                        </view>
                    </view>
                </template>
            </agent-card>
        </view>
        <!-- <view class="list-label">客户列表</view> -->
        <view class="search-box">
            <view class="search-icon"><my-icon type="sousuo" color="#ff5e66"></my-icon></view>
            <input type="text" confirm-type="search" v-model="params.keywords" @confirm="handleSearch()" placeholder="请输入好友昵称或手机号" />
        </view>
        <view class="tab-box flex-box">
            <view class="tab-item" :class="{'active':activeIndex==index}" :id="'i'+index" v-for="(item,index) in tabs" :key="item.index" @click="clickTab(index)">
                <view class="tab-text">{{item.name}}</view>
            </view>
            <view class="tab-item clear" @click="clearUnread()">
                <view class="tab-text">
                    <my-icon type="shanchu" color="#666"></my-icon>
                    <text>清除未读</text>
                </view>
            </view>
        </view>
        <!-- 好友列表 -->
        <template v-if="params.is_black===0">
        <view class="friend-item flex-box" hover-class="navigator-hover" v-for="(item, index) in im.friendList" :key="index" @click="toDetail(item.platform_id, item.chat_id, item.user_id, index)">
            <view class="img-box">
                <view class="dot" v-if="item.uncount>0"></view>
                <image class="chat-header" :src="item.headimage | imgUrl('w_80')" mode="aspectFill"></image>
            </view>
            <view class="info-box flex-box flex-1 bottom-line">
                <view class="flex-1">
                    <view class="title">
                        <text>{{item.nickname||'暂无昵称'}}</text>
                        <text v-if="item.alias_name" class="levelname" :class="{official:item.alias_name==='官方',agent:item.alias_name==='经纪人',adviser:item.alias_name==='置业顾问'}">{{item.alias_name}}</text>
                    </view>
                    <view class="desc">{{item | formatMsg}}</view>
                    <!-- <view class="other">{{item.title?'来源：'+item.title:''}}</view> -->
                </view>
                <view class="right-icon">
                    <view class="time">{{item.chat.time}}</view>
                    <view class="dot" v-if="item.uncount>0">{{item.uncount}}</view>
                </view>
            </view>
        </view>
        <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
        </template>
        <!-- 黑名单列表 -->
        <template v-else-if="params.is_black===1">
        <view class="row flex-box bottom-line" hover-class="navigator-hover" v-for="(item, index) in im.blackFriendList" :key="index" @click="toDetail(item.platform_id, item.chat_id, index)">
            <view class="img-box">
                <image class="chat-header" :src="item.headimage | imgUrl('w_80')" mode="aspectFill"></image>
                <view class="dot" v-if="item.uncount>0"></view>
            </view>
            <view class="info-box flex-1">
                <view class="title">
                    <text>{{item.nickname||'暂无昵称'}}</text>
                    <text v-if="item.alias_name" class="levelname" :class="{official:item.alias_name==='官方',agent:item.alias_name==='经纪人',adviser:item.alias_name==='置业顾问'}">{{item.alias_name}}</text>
                </view>
                <view class="desc">{{item | formatMsg}}</view>
                <!-- <view class="other">{{item.title?'来源：'+item.title:''}}</view> -->
            </view>
            <view class="right-icon">
                <view class="time">{{item.chat.time}}</view>
                <view class="dot" v-if="item.uncount>0">{{item.uncount}}</view>
                <view class="dot_place" v-else></view>
            </view>
        </view>
        <uni-load-more :status="get_status2" :content-text="content_text2"></uni-load-more>
        </template>
        <!--访客列表-->
        <template v-else>
        <view class="row flex-box bottom-line" hover-class="navigator-hover" v-for="(item, index) in visitorList" :key="index" @click="toVisitorDetail(item.user_id, index)">
            <view class="img-box">
                <image class="chat-header" :src="item.prelogo | imgUrl('w_80')" mode="aspectFill"></image>
                <view class="dot" v-if="item.uncount>0"></view>
            </view>
            <view class="info-box flex-1">
                <view class="title">{{item.cname||'暂无昵称'}}</view>
                <view class="desc">{{item.last_desc}}</view>
            </view>
            <view class="right-icon">
                <view class="time">{{item.time}}</view>
                <view class="dot_place"></view>
            </view>
        </view>
        <uni-load-more :status="get_status3" :content-text="content_text3"></uni-load-more>
        </template>
        <my-popup ref="socket_colse" :showMask="false" position="top">
            <view class="flex-box err-tip">
                <view class="tip-text">聊天连接已断开</view>
                <view class="tip-btn" @click="connectChatAgain()">点击重连</view>
            </view>
        </my-popup>
    </view>
</template>

<script>
    import myIcon from "../../components/icon"
    import agentCard from '../../components/agentCard'
    import {formatImg} from "../../common/index"
    import chat from '../../common/chat_mixin'
    import {uniLoadMore} from '@dcloudio/uni-ui'
    import myPopup from '../../components/myPopup'
    import { mapState } from 'vuex'
    export default {
        components:{
            myIcon,
            agentCard,
            uniLoadMore,
            myPopup
        },
        mixins:[chat],
        data(){
            return {
                params:{
                    page:1,
                    rows:20,
                    is_black:0,
                    keywords:""
                },
                activeIndex:0,
                tabs:[
                    {name:"聊天"},
                    {name:"黑名单"},
                    {name:"访客"}
                ],
                my_info:{
                    adviser:0
                },
                visitorList:[],
                get_status:"loading",
                content_text:{
                    contentdown:"",
                    contentrefresh:"正在加载...",
                    contentnomore:""
                },
                get_status2:"loading",
                content_text2:{
                    contentdown:"",
                    contentrefresh:"正在加载...",
                    contentnomore:""
                },
                get_status3:"loading",
                content_text3:{
                    contentdown:"",
                    contentrefresh:"正在加载...",
                    contentnomore:""
                }
            }
        },
        computed: {
            ...mapState(['im', 'user_info'])
        },
        onLoad(options){
            // #ifdef MP-WEIXIN
            uni.hideShareMenu()
            // #endif
            this.options = options
            this.userToken = uni.getStorageSync('token')
            this.getMyInfo()
            // // 判断是否是要显示访客列表
            // if(options.type==='visitor'){
            //     this.activeIndex = 2
            //     this.params.is_black=""
            //     this.getVisitorList()
            // }else{
            //     this.getFriendList()
            // }
        },
        onShow(){
            if(!this.my_info.id&&!this.$store.state.allowOpen){
                this.getMyInfo()
                this.$store.state.allowOpen = true
            }
        },
        onUnload(){
            // 关闭socket，停止心跳维持
            this.closeSocket('active')
            if (this.timer){
                clearInterval(this.timer)
            }
        },
        filters:{
            imgUrl(val, param=""){
                if(!val){
                    return ""
                }
                return formatImg(val, param)
            },
            formatMsg(val){
                    if(val.chat.type==='image'){
                        return "[图片]"
                    }
                    if(val.chat.type==='map'){
                        return "[位置]"
                    }
                    if(val.chat.type==='build'){
                        return "[楼盘]"
                    }
                    if(val.chat.type==='ershou'){
                        return "[二手房]"
                    }
                    if(val.chat.type==='renting'){
                        return "[出租房]"
                    }
                    if(val.chat.type==='wechat'){
                        return "[微信名片]"
                    }
                    if(val.chat.type==='apply_wx'){
                        return "[申请查看微信名片]"
                    }
                    if(val.chat.type==='tel'){
                        return "[手机号码]"
                    }
                    if(val.chat.type==='apply_tel'){
                        return "[申请查看手机号]"
                    }
                return val.chat.content
            }
        },
        methods: {
            clickTab(index){
                this.activeIndex = index
                this.params.page = 1
                this.params.keywords = ""
                if(index===1){
                    this.params.is_black = 1
                    this.getBlackFriendList()
                }else if(index===0){
                    this.params.is_black = 0
                    this.getFriendList()
                }else{
                    this.params.is_black = ""
                    this.getVisitorList()
                }
            },
            /**
             * 获取好友列表
             */
            getFriendList(){
                this.get_status = "loading"
                this.content_text.contentnomore=""
                if(this.params.page == 1){
                    this.im.friendList = []
                }
                this.$ajax.get('im/chatFriends.html',this.params,res=>{
                    this.im.imToken = res.data.imToken||''
                    if(res.data.user){
                        this.im.myChatInfo = res.data.user
                        if(this.my_info.wechat_img){
                            this.im.myChatInfo.wechat_img = this.my_info.wechat_img
                        }
                        if(this.my_info.wechat){
                            this.im.myChatInfo.wechat = this.my_info.wechat
                        }
                    }
                    if(!this.im.socketOpen){
                        if(this.im.imToken){
                            console.log("执行聊天初始化")
                            this.initMsg = {flag: 'init', from_id: this.im.myChatInfo.platform_id}
                            this.connectChat()
                            this.onMessage()
                            this.onClose()
                            this.onSocketError()
                        }
                    }else{
                        console.log("聊天已经是连接状态")
                    }
                    if(res.data.code == 1){
                        let friends = res.data.friends.map(item=>{
                            item.chatList = []
                            return item
                        })
                        this.im.friendList = this.im.friendList.concat(friends)
                        if(res.data.friends.length<this.params.rows){
                            this.get_status = "noMore"
                        }else{
                            this.get_status = "more"
                        }
                    }else{
                        this.params.page--
                        if (this.params.page<1) this.params.page = 1
                        this.get_status = "noMore"
                    }
                    if(this.im.friendList.length==0){
                        this.content_text.contentnomore="您还没有联系人哦~~"
                    }
                },err=>{
                    console.log(err)
                    this.params.page--
                    if (this.params.page<1) this.params.page = 1
                    this.get_status = "noMore"
                })
            },
            /**
             * 获取黑名单列表
             */
            getBlackFriendList(){
                this.get_status2 = "loading"
                this.content_text2.contentnomore=""
                if(this.params.page == 1){
                    this.im.blackFriendList = []
                }
                this.$ajax.get('im/chatFriends.html',this.params,res=>{
                    if(res.data.code == 1){
                        let friends = res.data.friends.map(item=>{
                            item.chatList = []
                            return item
                        })
                        this.im.blackFriendList = this.im.blackFriendList.concat(friends)
                        if(res.data.friends.length<this.params.rows){
                            this.get_status2 = "noMore"
                        }else{
                            this.get_status2 = "more"
                        }
                    }else{
                        this.params.page--
                        if (this.params.page<1) this.params.page = 1
                        this.get_status2 = "noMore"
                    }
                    if(this.im.blackFriendList.length==0){
                        this.content_text2.contentnomore='黑名单是空空如也~~'
                    }
                },err=>{
                    console.log(err)
                    this.params.page--
                    if (this.params.page<1) this.params.page = 1
                    this.get_status = "noMore"
                })
            },
            /**
             * 获取访客列表
             */
            getVisitorList(){
                this.get_status3 = "loading"
                this.content_text3.contentnomore=""
                if(this.params.page == 1){
                    this.visitorList = []
                }
                this.$ajax.get('im/visitorList', {page:this.params.page,keywords:this.params.keywords}, res=>{
                    if(res.data.code===1&&res.data.list.length>0){
                        this.visitorList = this.visitorList.concat(res.data.list)
                        this.get_status3 = "more"
                    }else{
                        if(this.visitorList.length==0){
                            this.content_text3.contentnomore='您还没有访客'
                        }else{
                            // uni.showToast({
                            //     title:"没有更多访客了",
                            //     icon:'none'
                            // })
                        }
                        this.params.page--
                        if (this.params.page<1) this.params.page = 1
                        this.get_status3 = "noMore"
                    }
                })
            },
            handleSearch(){
                if(this.params.is_black===''){
                    this.getVisitorList()
                }else{
                    this.getFriendList()
                }
            },
            /**
             * 去访客详情
             */
            toVisitorDetail(user_id, index){
                if(!user_id){
                    uni.showToast({
                        title:"无法查看游客访问详情",
                        icon:'none'
                    })
                    return
                }
                this.$navigateTo('/chatPage/chat/friend_info?user_id='+user_id)
            },
            /**
             * 获取好友列表上面自己的信息
             */
            getMyInfo(){
                this.$ajax.get('im/contactDetails.html',{},res=>{
                    // 判断是否是要显示访客列表
                    if(this.options.type==='visitor'){
                        this.activeIndex = 2
                        this.params.is_black=""
                        this.getVisitorList()
                    }else{
                        this.getFriendList()
                    }
                    if(res.data.code === 1){
                        if(res.data.member.is_optimization===1){
                            this.tabs.push({name:'通话记录'})
                        }
                        this.my_info = res.data.member
                        this.im.myChatInfo.wechat_img = this.my_info.wechat_img
                        this.im.myChatInfo.wechat = this.my_info.wechat
                        // if(this.my_info.adviser>0||this.my_info.levelid>1){
                        //     this.tabs.push({name:"访客"})
                        // }
                    }
                },err=>{
                    console.log(err)
                    // 判断是否是要显示访客列表
                    if(this.options.type==='visitor'){
                        this.activeIndex = 2
                        this.params.is_black=""
                        this.getVisitorList()
                    }else{
                        this.getFriendList()
                    }
                })
            },
            /**
             * 连接socket
             */
            connectChat(){
                this.connectPage = 'list'
                // this.onOpen()
                this.handleConnectSocket()
            },
            connectChatAgain(){
                this.$ajax.get('im/chatFriends.html',this.params,res=>{
                    this.im.imToken = res.data.imToken||''
                    if(!this.im.socketOpen){
                        this.connectChat()
                        return
                    }
                })
            },
            toSet(){
               if(this.my_info.adviser>0){
                    this.$navigateTo('/user/adviser_info')
                } else if(this.my_info.levelid>1){
                    this.$navigateTo('/user/agent_info')
                } 
            },
            addInfo(info){
                if(!info){
                    info = this.my_info
                }
                if(info.adviser>0){
                    this.$navigateTo('/user/consultant/addpost?buildid='+info.build_ids)
                } else if(info.levelid>1){
                    uni.switchTab({
                        url: '/pages/add/add'
                    });
                    // this.$navigateTo('/pages/add/add')
                }
            },
            myCard(info){
                if(!info){
                    info = this.my_info
                }
                if(info.adviser>0){
                    this.$navigateTo('/pages/consultant/detail?id='+info.adviser)
                } else if(info.levelid>1){
                    this.$navigateTo('/pages/agent/detail?id='+info.id)
                }
            },
            toMore(info){
                if(!info){
                    info = this.my_info
                }
                this.$navigateTo('/chatPage/chat/more_info?adviser_id='+info.adviser)
            },
            /**
             * 去聊天
             * @param {String} to_id 聊天中的好友id
             * @param {String} chat_id 好友关系id
             * @param {Number} index 好友再好友列表中的索引
             */
            toDetail(to_id, chat_id, user_id, index){
                if(this.params.is_black){
                    uni.showActionSheet({
                        itemList:['加为好友', '删除好友'],
                        success: (res)=> {
                            if(res.tapIndex === 0){
                                console.log("恢复好友")
                                this.reverseBlack(to_id, chat_id, index)
                            }else if(res.tapIndex === 1){
                                console.log("删除好友")
                                this.removeFriend(chat_id, index)
                            }
                        },
                        fail: (res)=> {
                            console.log(res.errMsg);
                        }
                    })
                    return
                }
                let blackStatue = this.getBlack(this.im.friendList[index].owner_id, this.im.friendList[index].passive_id, this.im.friendList[index].platform_id, this.im.myChatInfo.platform_id)
                // console.log("黑名单状态",blackStatue)
                if (blackStatue){
                    return
                }
                this.chatStatistics(user_id, 2)
                this.im.chatIndex = index
                this.im.nowChat = this.im.friendList[index]
                if(!this.im.socketOpen){
                    this.im.socketOpen = true
                    this.connectChatAgain()
                }
                this.$navigateTo("/chatPage/chat/chat?title="+this.im.friendList[index].nickname+'&to_id='+to_id+'&user_id='+user_id)
            },
            chatStatistics(id, type){
                this.$ajax.get('im/chatStatistics',{id, type},()=>{})
            },
            /**
             * 将黑名单好友从黑名单中恢复
             */
            reverseBlack(to_id, chat_id, index){
                this.$ajax.get('im/removeBack.html',{chat_id},res=>{
                    if(res.data.code === 1){
                        uni.showToast({
                            title:"操作成功"
                        })
                        this.pullReverseBlack(to_id)
                        this.page = 1
                        this.getBlackFriendList()
                    }else{
                        uni.showToast({
                            title:res.data.msg,
                            icon:'none'
                        })
                    }
                })
            },
            // 删除好友
            removeFriend(chat_id, index){
                this.$ajax.get('im/removeFriend.html', {chat_id}, res=>{
                    if(res.data.code === 1){
                        this.im.blackFriendList.splice(index,1)
                    }else{
                        uni.showToast({
                            title:res.data.msg || '',
                            icon:'none'
                        })
                    }
                })
            },
            pullReverseBlack(to_id){
                let reverseMesage = {flag:'removeBlack',to_id:to_id,from_id:this.im.myChatInfo.platform_id}
                this.im.socketTask.send({
                    data: JSON.stringify(reverseMesage)
                });
            },
            // 清除未读消息
            clearUnread(){
                this.$ajax.get('im/clearAllUnRead',{},res=>{
                    if(res.data.code === 1){
                        this.im.friendList.map(item=>{
                            item.uncount = 0
                        })
                    }else{
                        uni.showToast({
                            title:res.data.msg||'操作失败',
                            icon:'none'
                        })
                    }
                })
            }
        },
        onReachBottom(){
            this.params.page ++
            if(this.params.is_black===1){
                this.getBlackFriendList()
            }else if(this.params.is_black===0){
                this.getFriendList()
            }else{
                this.getVisitorList()
            }
        }
    }
</script>

<style lang="scss" scoped>
.client-list{
    // .tab-box{
    //     width: 100%;
    //     padding: 0 20upx;
    //     box-sizing: border-box;
    //     background-color: #fff;
    //     .tab-item{
    //         padding: 20upx;
    //         margin: 20upx;
    //         text-align: center;
    //     }
    //     .tab-img{
    //         width: 80upx;
    //         height: 80upx;
    //         border-radius: 8upx;
    //     }
    //     .tab-text{
    //         font-size: 26upx;
    //         color: #666;
    //     }
    // }
    .list-label{
        padding: 10upx 20upx;
        font-size: 26upx;
        color: #888;
    }
    .tab-box{
        display: flex;
        background-color: #fff;
        .tab-item{
            flex: 1;
            text-align: center;
            padding: 0 20upx;
            font-size: 0;
            .tab-text{
                display: inline-block;
                min-width: 120upx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding: 20upx 0;
            }
            &.active{
                .tab-text{
                    border-bottom: 4upx solid #f65354;
                }
            }
        }
        .clear{
            color: #666
        }
    }
    .row{
        // padding: 20upx 24upx;
        padding: 32upx 24upx;
        align-items: center;

    }
    .friend-item{
        padding-left: 48rpx;
        align-items: center;
        background-color: #fff;
        .info-box{
            padding: 32rpx 0;
            padding-right: 32rpx;
            >view{
                overflow: hidden;
            }
        }
    }
    .img-box{
        width: 110upx;
        height: 110upx;
        margin-right: 36upx;
        position: relative;
        .chat-header{
            border-radius: 50%;
            position: absolute;
            width: 100%;
            height: 100%;
        }
        .dot{
            width: 20upx;
            height: 20upx;
            border-radius: 50%;
            background-color: #f44;
            position: absolute;
            top: -5upx;
            right: -5upx;
        }
    }
    .info-box{
        overflow: hidden;
        .title{
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 30upx;
            font-weight: bold;
            color: #0f1d32;
            letter-spacing: 2upx;
            margin-bottom: 12upx;

            .levelname{
                margin-left: 10upx;
                padding: 2upx 8upx 3upx 8upx;
                display: inline-block;
                border-radius: 6upx;
                font-size: 22upx;
                border: 1upx solid #4ebdf8;
                color: #4ebdf8;
                &.official{
                    background-color: #1296db;
                    color: #fff;
                    border: 1upx solid #1296db;
                    // color: #1296db;
                }
                &.agent{
                    border: 1upx solid #f96063;
                    color: #f96063;
                }
                &.adviser{
                    border: 1upx solid #f0bb2c;
                    color: #f0bb2c;
                }
            }
        }
        .desc{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 26upx;
            height: 40upx;
            line-height: 40upx;
            color: #888
        }
        .other{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 24upx;
            color: #999
        }
    }
    .right-icon{
        position: relative;
        height: 90upx;
        // width: 120upx;
        // .icon-box{
        //     position: absolute;
        //     display:inline-block;
        //     height: 28px;
        //     width: 28px;
        //     left: 0;
        //     right: 0;
        //     top: 0;
        //     bottom: 0;
        //     margin: auto;
        // }
        .time{
            font-size: 24upx;
            color: #999
        }
        .dot{
            min-width: 32upx;
            height: 32upx;
            padding: 0 10upx;
            box-sizing: border-box;
            line-height: 30upx;
            text-align: center;
            font-size: 22upx;
            border-radius: 16upx;
            background-color: #f44;
            color: #fff;
            position: absolute;
            // display: inline-block;
            margin-top: 20upx;
            right: 0;
        }
        .dot_place{
            margin-top: 20upx;
            height: 32upx
        }
    }
}
.card-box{
    position: relative;
    background-color: #fff;
    .set_card{
        position: absolute;
        top: 10upx;
        right: 45upx;
        color:#1296db;
        z-index: 2;
        font-size: 26upx;
    }
    .operation-box{
        justify-content: space-between;
        align-items: center;
        .operation{
            display: flex;
            align-items: center;
            font-size: 24upx;
            color: #666;
            .text{
                margin-left: 10upx;
            }
        }
    }
}
.err-tip{
    justify-content: space-between;
    padding: 10upx 24upx;
    background-color: #f44;
    color: #fff;
    .tip-btn{
        padding: 6upx 12upx;
        border: 1upx solid rgb(255, 103, 103);
        border-radius: 6upx;
        font-size: 26upx;
    }
}

.search-box{
    position: relative;
    padding: 30upx 50upx;
    background-color: #fff;
    .search-icon{
        position: absolute;
        left: 65upx;
        top: 44upx
    }
    input{
        font-size: 28upx;
        background-color: #f5f5f5;
        padding: 10upx;
        padding-left: 60upx;
        border-radius: 6upx;
    }

}

</style>