<template>
  <view>
    <view class="logs">
      <view class="log-item" :class="{mgt20:index>0&&parseInt(item.month)==parseInt(list[index-1].month)}" v-for="(item, index) in list" :key="item.id">
        <text class="month" v-if="index===0||parseInt(item.month)<parseInt(list[index-1].month)">{{item.month}}月</text>
        <view class="detail">
          <view class="service flex-row">
            <text class="name">{{item.paybz}}</text>
            <text>-{{item.money}}</text>
          </view>
          <view class="time">{{item.posttime}}</view>
        </view>
      </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
  </view>
</template>

<script>
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
  components: {
    uniLoadMore
  },
  data() {
    return {
      get_status:"loading",
      content_text:{
        contentdown:"",
        contentrefresh:"正在加载...",
        contentnomore:"没有更多数据了"
      },
      list: [],
      params: {
        page: 1,
        rows: 20
      }
    }
  },
  onLoad(){
    this.getData()
  },
  methods: {
    getData() {
      if(this.params.page == 1){
        this.list=[]
      }
      this.get_status = "loading"
      this.$ajax.get('adviser/adviserBuyLog.html', this.params, res => {
        if(res.data.code === 1){
          if(res.data.list.length<this.params.rows){
            this.get_status = "noMore"
          }else{
            this.get_status = "more"
          }
          this.list = this.list.concat(res.data.list)
        }else{
          this.get_status = "noMore"
        }
      })
    }
  },
  onReachBottom(){
    if(this.get_status !== "more"){
      return
    }
    this.params.page ++
    this.getData()
  }
}
</script>

<style scoped lang="scss">
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row{
  flex-direction: row;
}

.log-item{
  margin-bottom: 0;
  &.mgt20{
    margin-top: 20rpx;
  }
  .month{
    padding: 24rpx 48rpx;
  }
  .detail{
    padding: 24rpx 48rpx;
    background-color: #fff;
    ~.detail{
      margin-top: 20rpx;
    }
    .service{
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 16rpx;
      overflow: hidden;
      .name{
        margin-right: 24rpx;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .time{
      font-size: 24rpx;
      color: #999;
    }
  }
}
</style>
