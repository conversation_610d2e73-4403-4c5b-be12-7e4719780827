<template>
  <view class="qrcode-box">
        
			
				<view class="img-box">
					<image  class="qrcode" :src="detail.cover | imageFilter" mode="aspectFill"></image>
					<view>
						<view class="title">{{detail.prize_title ||''}}</view>
						<view class="tip">凭核验码联系现场工作人员兑奖</view>
					</view>
          <view class="line">

          </view>
          <view class="title title_info">
            兑奖券详情
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              兑奖期限
            </view>
             <view class="duihuan_info_value">
              {{detail.cash_stime ||''}}- {{detail.cash_etime||''}}
            </view>
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              获奖用户
            </view>
             <view class="duihuan_info_value">
             {{detail.winner_name ||''}}
            </view>
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              获奖时间
            </view>
             <view class="duihuan_info_value">
             {{detail.ctime ||''}}
            </view>
          </view>
          <view class="title title_info">
            活动详情
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
             活动名称
            </view>
             <view class="duihuan_info_value">
              {{info.title ||''}}
            </view>
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              活动开始时间
            </view>
             <view class="duihuan_info_value">
             {{info.start_time ||''}}
            </view>
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              活动结束时间
            </view>
             <view class="duihuan_info_value">
               {{info.end_time ||''}}
            </view>
          </view>
				</view>
			
			<view class="btns flex-row items-center">
        <view class="btn" @click ="confirm">确认核销</view>
      </view>
			</view>
</template>

<script>
export default {
 data(){
   return  {
     detail:{},
     info:{}

   }
 },
 onLoad(options){
   if(options.id){
     this.id = options.id
   }
   this.getDetail()
 }, 
 methods:{
    getDetail(){
      this.$ajax.get("blind_box/getWriteOffPrize",{id:this.id},res=>{
        if (res.data.code ==1){
          this.detail =res.data.winner ||{}
          this.info = res.data.info 
        }else {
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    },
    confirm(){
      uni.showModal({
        title: '确认核销吗',
        // content: '这是一个模态弹窗',
        success: (res) =>{
          if (res.confirm) {
            this.$ajax.post("blind_box/writeOffPrize",{id:this.id},res=>{
                uni.showToast({
                    title:res.data.msg,
                    icon:"none"
                  })
                if (res.data.code ==1){
                  console.log(res);
                  this.$navigateTo("/topic/prize_detail?type=list")
                }

              })
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });
      
    }

 }
}
</script>

<style lang="scss" scoped>
.flex-row{
  display: flex;
  flex-direction: row;
}
.items-center {
  align-items: center;
}
.j-center{
  justify-content: center;
}
.flex-1 {
  flex: 1;
}
.qrcode-box{
	position: relative;
  
	.img-box{
		padding: 12rpx;
    padding-top: 48rpx;
		margin: auto;
    position: relative;
		background-color: #fff;
		border-radius: 15rpx;
		overflow: hidden;
		.title{
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
      color: #7F502C;
      &.title_info{
        text-align: left;
        margin-top: 48rpx;
        padding: 0 48rpx;
      }
		}
    .duihuan_info{
      margin: 24rpx 0;
      padding: 0 48rpx;
      font-size: 22rpx;
      color: #7F502C;
      .duihuan_info_name{
        white-space: nowrap;
        margin-right: 20rpx;
      }
      
    }
    .empty{
      height: 300rpx;
      width: 100vw;
     
    }
    
		.tip{
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			font-size: 28rpx;
      color: #7F502C;
		}
    .line {
      width: calc(100vw - 48rpx);
      height:2rpx;
      margin: 0 auto;
      background: #D8D8D8;
    }
	}
	.qrcode{
		width: 320rpx;
    margin: 0 auto;
		height: 320rpx;
    display: block;
	}
	.icon-box{
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
  
  
  
}
.btns {
  justify-content: center;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 10rpx;
  .btn {
    padding: 20rpx 60rpx;
    background: #2fa1e2;
    color: #fff;
    font-size: 28rpx;
    border-radius: 40rpx;
    text-align: center;
  }
}
</style>