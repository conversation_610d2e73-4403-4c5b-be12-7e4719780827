<template>
  <view class="user-info">
    <view class="block">
      <view class="info-row avatar-row">
        <view class="label">头像</view>
        <view class="right flex-row">
          <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadHeaderImg">
            <image
              class="img"
              mode="aspectFill"
              v-if="(params.agent&&params.agent.uncheck_prelogo)||params.prelogo"
              :src="((params.agent&&params.agent.uncheck_prelogo)||params.prelogo) | imageFilter('w_120')"
            ></image>
            <view v-else class="upload-btn">
              <my-icon type="ic_jia" size="46rpx" color="#d8d8d8"></my-icon>
            </view>
          </my-upload>
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="block">
      <view class="info-row">
        <view class="label">联系人</view>
        <view class="right flex-row">
          <input
            type="text"
            v-model="params.cname"
            placeholder="请输入"
            placeholder-style="text-align:right;font-size:32rpx;color:#999"
          />
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="block">
      <view class="info-row">
        <view class="label">社区昵称</view>
        <view class="right flex-row">
          <input
            type="text"
            v-model="params.nickname"
            placeholder="请输入"
            placeholder-style="text-align:right;font-size:32rpx;color:#999"
          />
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="block">
      <view class="info-row">
        <view class="label">登录密码</view>
        <view class="right flex-row">
          <input
            type="password"
            v-model="params.userpwd"
            placeholder="请输入(不修改请留空)"
            placeholder-style="text-align:right;font-size:32rpx;color:#999"
          />
          <view class="icon-box">
            <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="block">
      <view class="info-row">
        <view class="label">手机号码</view>
        <view class="right flex-row">
          <input
            class="flex-1"
            disabled
            type="number"
            maxlength="11"
            v-model="user_info.tel"
            placeholder="请输入"
            placeholder-style="text-align:right;font-size:32rpx;color:#999"
          />
          <view class="send-code" @click="toEditPhone">修改手机号</view>
        </view>
      </view>
    </view>
    <view class="block" v-if ="params.agent&&params.agent.uncheck">
      <view class="info-row">
        <view class="label">审核状态</view>
        <view class="right flex-row red">
          <view class="isChecked">{{(params.agent && params.agent.uncheck)| formatUncheck}}</view>
          
        </view>
      </view>
    </view>
    <view class="btn" @click="subData()">立即提交</view>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
import myUpload from '../components/form/myUpload.vue'
import { mapState, mapMutations } from 'vuex'
export default {
  components: {
    myIcon,
    myUpload
  },
  data() {
    return {
      params: {
        cname: '',
        nickname: '',
        userpwd: '',
        prelogo: '',
        agent:{
          uncheck:0,
          uncheck_name:'',
          uncheck_prelogo:""
        },
      }
    }
  },
  computed: {
    ...mapState(['user_info'])
  },
  filters:{
    formatUncheck(val){
      if (val==1){
        return "正在审核中"
      }else {
        return ""
      }
    }
  },
  onLoad() { 
    // #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
    uni.hideShareMenu()
    // #endif
  },
  onShow() {
    this.params.prelogo = this.user_info.prelogo
    this.params.nickname = this.user_info.nickname
    this.params.cname = this.user_info.cname
    
   
    if (this.user_info.agent){
      this.params.agent=this.user_info.agent
    }
    if (this.params.agent&&this.params.agent.uncheck_cname){
      this.params.cname=this.params.agent.uncheck_cname
    }
  },
  methods: {
    toEditPhone() {
      this.$navigateTo('/user/bind_phone/bind_phone?type=edit')
    },
    uploadHeaderImg(e) {
      if (e) {
        if (this.params.agent&&this.params.agent.uncheck_prelogo){
          this.params.agent.uncheck_prelogo =''
        }
        this.user_info.prelogo = e.files.join(',')
        this.params.prelogo = e.files.join(',')
      } else {
        this.params.prelogo = []
      }
    },
    subData() {
      if (this.params.userpwd != '' && this.params.userpwd.length < 8) {
        uni.showToast({
          title: '密码长度不能小于8',
          icon: 'none'
        })
        return
      }
      if (this.params.userpwd){
        var regNum=/\d/;
        var regString =/[a-zA-Z]/;
        var regSpe = /[^a-zA-Z\d\s]/
        var reg =/\s/
        if (!regNum.test(this.params.userpwd) ||!regString.test(this.params.userpwd) ||!regSpe.test(this.params.userpwd)){
          uni.showToast({
          title:"密码须同时包含数字、字母以及特殊符号(!@#$%^&*()等非空格)",
          icon:"none"
        })
            return
        }
        if (reg.test(this.params.userpwd)){
            uni.showToast({
              title:"密码不能包含空格)",
              icon:"none"
            })
            return 
        }
      }
      delete this.params.agent
      uni.showLoading({
        title:"提交中",
        mask: true
      })
      this.$ajax.get('member/updatePwd.html', this.params, res => {
        uni.hideLoading()
        uni.showToast({
          title: res.data.msg,
          icon: 'none'
        })
        if (res.data.code == 1) {
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      })
    }
  }
}
</script>

<style lang="scss">
view {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.block {
  padding: 0 48rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  .buildname {
    font-size: 32rpx;
    text-align: right;
    margin-right: 10rpx;
  }
  .info-row {
    width: 100%;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 48rpx 0;
    &.avatar-row {
      padding: 24rpx 0;
      .upload-btn {
        width: 96rpx;
        height: 96rpx;
      }
      .img {
        width: 96rpx;
        height: 96rpx;
      }
    }
    input {
      text-align: right;
    }
  }
  .info-column {
    padding: 48rpx 0;
    .label {
      margin-bottom: 20rpx;
    }
    textarea {
      width: 100%;
      height: 200rpx;
    }
  }
  .label {
    width: 160rpx;
    font-size: 32rpx;
    color: #666;
  }
  .right {
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    &.red{
      color: #f00;
    }
    .icon-box {
      margin-left: 16rpx;
    }
    .send-code {
      padding: 0 20rpx;
      margin-left: 24rpx;
      line-height: 50rpx;
      border-radius: 25rpx;
      border: 1rpx solid $uni-color-primary;
      color: $uni-color-primary;
      &.disable {
        color: #999;
        background-color: #f5f5f5;
        border-color: #999;
      }
    }
  }
  .title {
    justify-content: space-between;
    align-items: center;
    font-size: 32rpx;
    margin-bottom: 24rpx;
    color: #666;
  }
  .upload-row {
    padding: 48rpx 0;
  }
  .upload-btn {
    width: 25vw;
    height: 25vw;
    align-items: center;
    justify-content: center;
    border-radius: 8rpx;
    background-color: #f5f5f5;
  }
  .img {
    width: 25vw;
    height: 25vw;
    border-radius: 8rpx;
  }
}

.btn {
  margin: 24rpx 48rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  background-color: $uni-color-primary;
  box-shadow: 0 4px 16px 0 rgba(251, 101, 106, 0.4);
}
</style>
