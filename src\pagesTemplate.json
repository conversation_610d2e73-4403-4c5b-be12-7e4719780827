{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
			
		  {
			"path": "pages/index/index",
			"style": {
				// "navigationBarTitleText": "{siteName}",
				"enablePullDownRefresh": true,
				"h5":{
					"titleNView":false
				}
			}
		}, {
			"path": "pages/news/news",
			"style": {
				"navigationBarTitleText": "资讯频道",
				"enablePullDownRefresh": true,
				"navigationStyle": "default",
				"app-plus": {
					"animationType": "pop-in",
					"animationDuration": 260
				}
			}
		},
		{
			"path": "pages/news/detail",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/web_view/webview",
			"style": {
				"navigationStyle": "custom",
				"h5": {
					"titleNView": false
				}

			}
		},
		{
			"path": "pages/new_house/new_house",
			"style": {
				"navigationBarTitleText": "新楼盘",
				"enablePullDownRefresh": true,
				"navigationStyle": "default"
			}
		},
		{
				"path": "pages/new_house/photo",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "default",
					"app-plus": {
						"titleNView": {
							"backgroundColor" :"#f7f7f7",
							"type" :"transparent",
							"buttons" :[
								{
									"type": "share"
								}
							]
						}
					},
					"h5": {
						"titleNView": {
							"backgroundColor": "#f7f7f7",
							"type": "transparent"
						}
					}
				}
		},
		{
			"path": "pages/new_house/detail",
			"style": {
				"navigationBarTitleText": "",
				"backgroundColor": "#f7f7f7",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
							{
								"type": "share"
							}
						]
					}
				},
				"app-plus":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons":[
							{
								"type": "share"
							}
						]
					}
				}
			}
		},
		{
			"path": "pages/new_house/info",
			"style": {
				"navigationBarTitleText": "",
				"backgroundColor": "#f7f7f7"
			}
		},
			{
				"path": "pages/new_house/buildNews",
				"style": {
					"navigationBarTitleText": "",
					"backgroundColor": "#f7f7f7",
					"enablePullDownRefresh": true
				}
			},
		{
			"path": "pages/new_house/comment",
			"style": {
				"navigationBarTitleText": "楼盘点评",
				"backgroundColor": "#f7f7f7"
			}
		},
		{
			"path": "pages/new_house/photos",
			"style": {
				"navigationBarTitleText": "楼盘相册",
				"backgroundColor": "#f7f7f7"
			}
		},
		{
			"path": "pages/new_house/videos",
			"style": {
				"navigationBarTitleText": "楼盘视频",
				"backgroundColor": "#f7f7f7"
			}
		},
		{
			"path": "pages/new_house/activity",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "default",
				"h5": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/index/find_house",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/add/add",
			"style": {
				"navigationBarTitleText": "发布",
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/add/house_type",
			"style": {
				"navigationBarTitleText": "请选择房产类型"
			}
		},
		{
			"path": "pages/add/detail",
			"style": {
				"navigationBarTitleText": "发布信息"
			}
		},
		// {
		// 	"path": "pages/add/push",
		// 	"style": {
		// 		"navigationBarTitleText": "发布信息"
		// 	}
		// },
		{
			"path": "pages/add/upload",
			"style": {
				"navigationBarTitleText": "添加照片"
			}
		},
		{
			"path": "pages/add/add_desc",
			"style": {
				"navigationBarTitleText": "核心卖点"
			}
		},
		{
			"path": "pages/ershou/ershou",
			"style": {
				"navigationBarTitleText": "二手房",
				"enablePullDownRefresh": true,
				"navigationStyle": "default"
				// "h5": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"text": "发布",
				// 			"fontSize": "15px",
				// 			"width": "40px"
				// 		}]
				// 	}
				// },
				// "app-plus": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"text": "发布",
				// 			"fontSize": "15px",
				// 			"width": "40px"
				// 		}]
				// 	}
				// }
			}
		},
		{
			"path": "pages/ershou/detail",
			"style": {
				"navigationBarTitleText": "",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
							{
								"type": "share"
							}
						]
					}
				},
				"app-plus":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
							{
								"type": "share"
							}
						]
					}
				}
			}
		},
		
		{
			"path": "pages/index/chat_list",
			"style": {
				"navigationBarTitleText": "消息",
				"enablePullDownRefresh": false,
				"navigationStyle": "default",
				"h5": {
					"titleNView": {
						"buttons": [{
							"float": "left",
							"fontSize": "15px",
							"width": "100px",
							"text": "全部已读"
						}]
					}
				},
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"float": "left",
							"fontSize": "15px",
							"width": "100px",
							"text": "全部已读"
						}]
					}
				}
			}
		},
		{
			"path": "pages/my/protocol",
			"style": {
				"navigationBarTitleText": "用户协议"
			}
		},
		{
			"path": "pages/my/reg_infos",
			"style": {
				"navigationBarTitleText": "隐私政策"
			}
		},
		{
			"path": "pages/my/my",
			"style": {
				"navigationBarTitleText": "个人中心",
				"h5":{
					"titleNView":false
				}
			}
		}, {
			"path": "pages/renting/renting",
			"style": {
				"navigationBarTitleText": "出租房",
				"enablePullDownRefresh": true,
				"navigationStyle": "default"
				// "h5": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"text": "发布",
				// 			"fontSize": "15px",
				// 			"width": "40px"
				// 		}]
				// 	}
				// },
				// "app-plus": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"text": "发布",
				// 			"fontSize": "15px",
				// 			"width": "40px"
				// 		}]
				// 	}
				// }
			}
		},
		{
			"path": "pages/renting/detail",
			"style": {
				"navigationBarTitleText": "",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
							{
								"type": "share"
							}
						]
					}
				},
				"app-plus":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
							{
								"type": "share"
							}
						]
					}
				}
			}
		},
		 {
			"path": "pages/map/map",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "default",
					"app-plus": {
						"bounce": "none"
					}
			}
		},	
		 {
			"path": "pages/house_price/house_price",
			"style": {
				"navigationBarTitleText": "小区房价",
				"navigationStyle": "custom"
			}
		},
		
		{
			"path": "pages/house_price/detail",
			"style": {
				"navigationBarTitleText": "",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
								{
									"type": "home"
								}
							]
					}
				},
				"app-plus":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
								{
									"type": "home"
								}
							]
					},
					 "bounce": "none"
				}
			}
		},				
		
		{
			"path": "pages/house_price/photos",
			"style": {
				"navigationBarTitleText": "相册"
			}
		},				
		{
			"path": "pages/house_price/publication",
			"style": {
				"navigationBarTitleText": "发布"
			}
		},				
		{
			"path": "pages/search/search",
			"style": {
				"navigationBarTitleText": "搜索",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/groups/groups",
			"style": {
				"navigationBarTitleText": "看房团",
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/groups/detail",
			"style": {
				"navigationBarTitleText": "",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
		}, {
			"path": "pages/community/community1",
			"style": {
				"navigationBarTitleText": "邻里社区",
				"enablePullDownRefresh": true,
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/community/community",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/community/detail",
			"style": {
				"navigationBarTitleText": "详情"
			}
		}, {
			"path": "pages/community/add_post",
			"style": {
				"navigationBarTitleText": "发布",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/agent/agent",
			"style": {
				"navigationBarTitleText": "经纪人",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/agent/detail",
			"style": {
				"navigationBarTitleText": "经纪人详情",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
		},
		 
		 {
			"path": "pages/map_find/map_find",
			"style": {
				"navigationBarTitleText": "地图找房",
				"navigationStyle": "default",
				"app-plus": {
					"bounce": "none"
				}
				
			}	
		}, 	
		{
			"path" : "pages/consultant/consultant",
			"style" : {
				"navigationBarTitleText": "顾问排行榜",
				"navigationStyle": "default"
			}
		},
		
			{
				"path": "pages/consultant/consuList",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "default"
				}
			},
			
		{
			"path" : "pages/consultant/detail",
			"style" : {
				"navigationBarTitleText": "顾问详情",
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/consultant/add",
			"style": {
				"navigationBarTitleText": "申请为置业顾问",
				"navigationStyle": "default"
			}
		},
			
		{
			"path": "pages/consultant/seeme",
			"style": {
				"navigationBarTitleText": "谁看过我",
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/consultant/addpost",
			"style": {
				"navigationBarTitleText": "发表分享",
				"navigationStyle": "default"
			}
		},
			{
				"path": "pages/consultant/shareDetail",
				"style": {
					"navigationBarTitleText": "分享详情",
					"navigationStyle": "default"
				}
			},
		
		
		{
			"path": "pages/tudi/index",
			"style": {
				"navigationBarTitleText": "土地市场",
				"h5": {
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"searchInput":{
							"align":"left",
							"borderRadius":"2px",
							"placeholder":"地块位置（某某路）",
							"placeholderColor":"#666666"
						}
					}
				}
			}
		}, {
			"path": "pages/tudi/detail",
			"style": {
				"navigationBarTitleText": "地块详情",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				},
				"app-plus":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
		}, {
			"path": "pages/yushou/index",
			"style": {
				"navigationBarTitleText": "预售证查询系统",
				"h5" :{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"searchInput":{
							"align":"left",
							"borderRadius":"2px",
							"placeholder":"项目名称（支持模糊查询）",
							"placeholderColor":"#666666"
						}
					}
				}
			}
		}, {
			"path": "pages/yushou/detail",
			"style": {
				"navigationBarTitleText": "预售详情",
				"h5" :{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
		}, 
		{
			"path": "pages/search_areas/search_areas",
			"style": {
				"navigationBarTitleText": "查找小区",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "会员登录",
				"navigationStyle": "default",
				"backgroundColor": "#ffffff"
			}
		}, {
			"path": "pages/bind_phone/bind_phone",
			"style": {
				"navigationBarTitleText": "绑定手机号",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/my/announcement",
			"style": {
				"navigationBarTitleText": "公告详情",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/my/notice",
			"style": {
				"navigationBarTitleText": "站内信",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/my/notice_detail",
			"style": {
				"navigationBarTitleText": ""
			}
		}, {
			"path": "pages/my/edit_info",
			"style": {
				"navigationBarTitleText": "信息修改",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/my/pay_success",
			"style": {
				"navigationBarTitleText": "支付成功",
				"navigationStyle": "default"
			}
		},{
			"path": "pages/my/user_info",
			"style": {
				"navigationBarTitleText": "会员资料",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/my/setting_shop",
			"style": {
				"navigationBarTitleText": "店铺设置",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/my/invited",
			"style": {
				"navigationBarTitleText": "邀请码",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/preview_video/preview_video",
			"style": {
				"navigationBarTitleText": "视频预览",
				"h5": {
					"titleNView": {
						"backgroundColor": "#f7f7f7",
						"type": "transparent"
					}
				},
				"app-plus": {
					"titleNView": {
						"backgroundColor": "#f7f7f7",
						"type": "transparent"
					}
				}
			}
		}
	    ,{
            "path" : "pages/calculator/calculator",
            "style" : {
				"navigationBarTitleText": "房价计算",
				"navigationStyle": "default"
			}
		}
		,{
            "path" : "pages/calculator/res",
            "style" : {
				"navigationBarTitleText": "计算结果",
				"navigationStyle": "default"
			}
        }
        ,{
            "path" : "pages/web_view/web_view",
            "style" : {
				"h5":{
					"titleNView":false
				}
			}
		}
		,{
            "path" : "pages/vr/vr",
            "style" : {
				"h5":{
					"titleNView":false
				}
			}
		}
        ,{
            "path" : "pages/sub_form/sub_form",
			"style": {
				"navigationBarTitleText": "团购报名",
				"backgroundColor": "#f7f7f7",
				"navigationStyle": "default"
			}
        }
        ,{
            "path" : "pages/price_trend/price_trend",
            "style" : {
				"navigationBarTitleText": "",
				"navigationStyle": "default"
            }
        }
        ,{
            "path" : "pages/comment_list/comment_list",
            "style" : {
				"navigationBarTitleText": "评论列表",
				"navigationStyle": "default"
			}
        }
        ,{
            "path" : "pages/inform/inform",
            "style" : {
				"navigationBarTitleText": "举报中心",
				"navigationStyle": "default"
			}
		}
	
    ],
	"subPackages": [{ // 求购求租
        "root": "needPage",
        "pages": [{
            "path": "buy_house/buy_house",
            "style": {
				"navigationBarTitleText": "房屋求购",
				"enablePullDownRefresh": true,
				"navigationStyle": "default",
				"h5": {
					"titleNView": {
						"buttons": [{
							"text": "发布",
							"fontSize": "15px",
							"width": "40px"
						}]
					}
				},
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"text": "发布",
							"fontSize": "15px",
							"width": "40px"
						}]
					}
				}
			}
        },
		{
		    "path": "buy_house/detail",
		    "style": {
				"navigationBarTitleText": "求购详情"
			}
		},
		{
		    "path": "rest_house/rest_house",
		    "style": {
				"navigationBarTitleText": "房屋求租",
				"enablePullDownRefresh": true,
				"navigationStyle": "default",
				"h5": {
					"titleNView": {
						"buttons": [{
							"text": "发布",
							"fontSize": "15px",
							"width": "40px"
						}]
					}
				},
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"text": "发布",
							"fontSize": "15px",
							"width": "40px"
						}]
					}
				}
			}
		},{
            "path": "rest_house/detail",
            "style": {
				"navigationBarTitleText": "求租详情"
			}
        }]
    },{ // 聊天
		"root": "chatPage",
        "pages": [{
            "path": "chat/list",
            "style": {
				"navigationBarTitleText": "消息",
				"navigationStyle": "default"
			}
        },
		{
		    "path": "chat/chat",
		    "style": {
				"navigationBarTitleText": "",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/add_statement",
			"style": {
				"navigationBarTitleText": "添加常用语",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/edit_statement",
			"style": {
				"navigationBarTitleText": "编辑常用语",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/statement_list",
			"style": {
				"navigationBarTitleText": "编辑快捷回复",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/friend_info",
			"style": {
				"navigationBarTitleText": "更多",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/more_info",
			"style": {
				"navigationBarTitleText": "更多",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/invited",
			"style": {
				"navigationBarTitleText": "邀请码",
				"navigationStyle": "default"
			}
		}]
	},
	{ //线上售楼处
		"root": "online",
		"pages": [{
			"path": "list",
			"style": {
				"navigationBarTitleText": "线上选房"
			}
		},{
			"path": "detail",
			"style": {
				"navigationBarTitleText": "",
				"h5": {
					"titleNView": false
				},
				"app-plus": {
					"titleNView": false
				}
				// "usingComponents": {
				// 	"subscribe": "plugin-private://wx2b03c6e691cd7370/components/subscribe/subscribe"
				// }
			}
		},{
			"path": "house_detail",
			"style": {
				"navigationBarTitleText":"房源详情"
			}
		},
		{
			"path": "loudong",
			"style": {
				"navigationBarTitleText": "楼栋详情"
			}
		},
		{
			"path": "house_status",
			"style": {
				"navigationBarTitleText": "楼栋房源信息"
			}
		},
		{
			"path": "house_info",
			"style": {
				"navigationBarTitleText": "楼栋房源详情"
			}
		},
		{
			"path": "adviser",
			"style": {
				"navigationBarTitleText": "",
				"h5": {
					"titleNView": false
				},
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "choose_adviser",
			"style": {
				"navigationBarTitleText": "选择置业顾问"
			}
		},
		{
			"path": "confirm_order",
			"style": {
				"navigationBarTitleText": "确认订单"
			}
		},
		{
			"path": "pay",
			"style": {
				"navigationBarTitleText": "定金支付"
			}
		},
		{
			"path": "choose",
			"style": {
				"navigationBarTitleText": "选房大厅",
				"h5": {
					"titleNView": {
						"buttons": [{
							"fontSrc": "/static/font/iconfont.ttf",
							"text": "\ue86f",
							"fontWeight": "bold",
							"fontSize": "18px",
							"width": "30px"
						}]
					}
				},
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"fontSrc": "/static/font/iconfont.ttf",
							"text": "\ue86f",
							"fontWeight": "bold",
							"fontSize": "18px",
							"width": "30px"
						}]
					}
				}
			}
		},
		{
			"path": "notice",
			"style": {
				"navigationBarTitleText": "购房须知"
			}
		},
		{
			"path": "loupan_info",
			"style": {
				"navigationBarTitleText": "楼盘简介"
			}
		},{
			"path": "sign_up",
			"style": {
				"navigationBarTitleText": "在线预约"
			}
		},{
			"path": "my",
			"style": {
				"navigationBarTitleText": "个人中心",
				"h5": {
					"titleNView": {
						"backgroundColor": "#f7f7f7",
						"type": "transparent"
					}
				},
				"app-plus": {
					"titleNView": {
						"backgroundColor": "#f7f7f7",
						"type": "transparent"
					}
				}
			}
		},{
			"path": "order_list",
			"style": {
				"navigationBarTitleText": "我的订单"
			}
		},{
			"path": "order_detail",
			"style": {
				"navigationBarTitleText": "订单详情"
			}
		},{
			"path": "order_status",
			"style": {
				"navigationBarTitleText": "订单状态"
			}
		},{
			"path": "user_info",
			"style": {
				"navigationBarTitleText": "购房信息"
			}
		},{
			"path": "concern",
			"style": {
				"navigationBarTitleText": "我的关注"
			}
		},{
			"path": "liveList",
			"style": {
				"navigationBarTitleText": "直播列表"
			}
		},
		{
			"path": "videos",
			"style": {
				"navigationBarTitleText": "回放列表"
			}
		},{
			"path": "next",
			"style": {
				"navigationBarTitleText": "",
				"usingComponents": {
					// #ifdef MP-WEIXIN
					"subscribe": "plugin-private://wx2b03c6e691cd7370/components/subscribe/subscribe"
					// #endif
				}
			}
		}],
		"plugins": {
			"live-player-plugin": {
				"version": "1.1.0",
				"provider": "wx2b03c6e691cd7370"
			}
		}
	},
	{ //置业顾问售房
		"root": "adviser",
		"pages": [{
			"path": "house_list",
			"style": {
				"navigationBarTitleText": "选择房源"
			}
		},{
			"path": "manage_house",
			"style": {
				"navigationBarTitleText": "添加房源"
			}
		},{
			"path": "create_order",
			"style": {
				"navigationBarTitleText": "创建订单"
			}
		},{
			"path": "order_list",
			"style": {
				"navigationBarTitleText": "客户订单"
			}
		},{
			"path": "client_list",
			"style": {
				"navigationBarTitleText": "客户列表"
			}
		},{
			"path": "client_detail",
			"style": {
				"navigationBarTitleText": "客户详情"
			}
		},{
			"path": "friend_list",
			"style": {
				"navigationBarTitleText": "我的同事"
			}
		},{
			"path": "order_detail",
			"style": {
				"navigationBarTitleText": "订单详情",
				"h5": {
					"titleNView":{
						"backgroundColor": "#f7f7f7",
						"type": "transparent"
					}
				}
			}
		},{
			"path": "order_status",
			"style": {
				"navigationBarTitleText": "订单状态"
			}
		},{
			"path": "verification_order",
			"style": {
				"navigationBarTitleText": "订单核销",
				"h5": {
					"titleNView":{
						"backgroundColor": "#f7f7f7",
						"type": "transparent"
					}
				}
			}
		}]
	},

	{ //家装
		"root": "home",
        "pages": [{
            "path": "index/index",
            "style": {
				"navigationBarTitleText": "家装平台",
				"h5" :{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"searchInput":{
							"align":"left",
							"borderRadius":"2px",
							"placeholder":"找装修公司",
							"placeholderColor":"#666666"
						}
					}
				}
			}
        },{
            "path": "find_company/list",
            "style": {
				"navigationBarTitleText": "找公司",
				"h5" :{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"searchInput":{
							"align":"left",
							"borderRadius":"2px",
							"placeholder":"找装修公司",
							"placeholderColor":"#666666"
						}
					}
				},
				"enablePullDownRefresh": true
			}
        },{
            "path": "find_company/detail",
            "style": {
				"navigationBarTitleText": "店铺详情",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "case/list",
            "style": {
				"navigationBarTitleText": "案例列表",
				"navigationStyle": "default",
				"enablePullDownRefresh": true
			}
        },{
            "path": "case/detail",
            "style": {
				"navigationBarTitleText": "案例详情",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "yuyue/index",
            "style": {
				"navigationBarTitleText": "预约设计",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "quote/index",
            "style": {
				"navigationBarTitleText": "算报价",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "area/list",
            "style": {
				"navigationBarTitleText": "找我家",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"searchInput":{
							"align":"left",
							"borderRadius":"2px",
							"placeholder":"你家在哪里？",
							"placeholderColor":"#666666"
						}
					}
				}
			}
        },{
            "path": "area/detail",
            "style": {
				"navigationBarTitleText": "",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "user/center",
            "style": {
				"navigationBarTitleText": "用户中心",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "user/setting_shop",
            "style": {
				"navigationBarTitleText": "店铺设置",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
			"path": "user/manage_case",
			"style": {
				"navigationBarTitleText": "案例管理"
			}
		},{
            "path": "user/upgrade",
            "style": {
				"navigationBarTitleText": "会员升级"
			}
        },{
            "path": "user/client_list",
            "style": {
				"navigationBarTitleText": "客户列表"
			}
		},{
            "path": "user/release",
            "style": {
				"navigationBarTitleText": "发布信息"
			}
        }]
	},
	{
		"root":"user",
		"pages":[
			{
				"path":"collect",
				"style": {
					"navigationBarTitleText": "信息收藏",
					"navigationStyle": "default"
				}
			},
			{
				"path": "task_center",
				"style": {
					"navigationBarTitleText": "任务中心",
					"navigationStyle": "default"
				}
			},
			{
				"path": "manage_info",
				"style": {
					"navigationBarTitleText": "信息管理",
					"navigationStyle": "default"
				}
			},
			 {
				"path" :"member_upgrade",
				"style": {
					"navigationBarTitleText": "会员升级",
					"navigationStyle": "default"
				}
			},{
				"path" :"my_vip",
				"style": {
					"navigationBarTitleText": "我的会员",
					"navigationStyle": "default"
				}
			},{
				"path": "exchange",
				"style": {
					"navigationBarTitleText": "积分兑换",
					"navigationStyle": "default"
				}
			},
			{
				"path": "recharge",
				"style": {
					"navigationBarTitleText": "账户充值",
					"navigationStyle": "default",
					"backgroundColor": "#ffffff"
				}
			},
			{
				"path": "recharge_detail",
				"style": {
					"navigationBarTitleText": "充值明细",
					"navigationStyle": "default"
				}
			},
			 {
				"path": "withdraw_funds",
				"style": {
					"navigationBarTitleText": "申请提现",
					"navigationStyle": "default"
				}
			},
			 {
				"path": "history",
				"style": {
					"navigationBarTitleText": "历史浏览",
					"navigationStyle": "default"
				}
			},
			{
				"path": "agent_info",
				"style": {
					"navigationBarTitleText": "经纪人资料",
					"navigationStyle": "default"
				}
			},
			{
				"path": "adviser_info",
				"style": {
					"navigationBarTitleText": "名片设置",
					"navigationStyle": "default"
				}
			},
			{
				"path": "adviser_rights",
				"style": {
					"navigationBarTitleText": "会员特权",
					"navigationStyle": "default"
				}
			},
			{
				"path": "all_rights",
				"style": {
					"navigationBarTitleText": "所有特权",
					"navigationStyle": "default"
				}
			},
			{
				"path": "adviser_activity",
				"style": {
					"navigationBarTitleText": "活跃度",
					"navigationStyle": "default"
				}
			},
			{
				"path": "adviser_builds",
				"style": {
					"navigationBarTitleText": "我的项目",
					"navigationStyle": "default"
				}
			},
			{
				"path": "adviser_upgrade",
				"style": {
					"navigationBarTitleText": "兑换特权",
					"navigationStyle": "default"
				}
			}
		]
	},
	{
		"root": "topic",
		"pages": [
			{
				"path": "detail",
				"style": {
					"navigationBarTitleText": "专题详情",
					"navigationStyle": "default"
				}	
			},
			{
				"path": "infolist",
				"style": {
					"navigationBarTitleText": "条目列表",
					"navigationStyle": "default"
				}
			},
			{
				"path": "topicList",
				"style": {
					"navigationBarTitleText": "专题列表",
					"navigationStyle": "default"
				}
			},
			{
				"path": "info_detail",
				"style": {
					"navigationBarTitleText": "条目详情",
					"navigationStyle": "default"
				}
			}
		]
	},
	{
		"root": "school",
		"pages":[
			{
				"path": "list",
				"style": {
					"navigationBarTitleText": "查学校",
					"navigationStyle": "default",
					"backgroundColor":"#ffffff",
					"h5": {
						"titleNView": false
					}
				}	
			},
			{
				"path": "map",
				"style": {
					"navigationBarTitleText": "地图找学校",
					"navigationStyle": "custom"
				}	
			},
			{
				"path": "detail",
				"style": {
					"navigationBarTitleText": "学校详情",
					"h5":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent",
							"buttons": [
								{
									"type": "home"
								}
							]
						}
					},
					"app-plus":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent",
							"buttons": [
								{
									"type": "home"
								}
							]
						},
						"bounce": "none"
					}
				}	
			},
			{
				"path": "teach_range",
				"style": {
					"navigationBarTitleText": "施教范围",
					"h5":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent"
						}
					},
					"app-plus":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent"
						},
						"bounce": "none"
					}
				}	
			},
			{
				"path": "introduce",
				"style": {
					"navigationBarTitleText": "学校简介",
					"navigationStyle": "default",
					"h5": {
						"titleNView": false
					}
				}	
			},
			{
				"path": "regulations_detail",
				"style": {
					"navigationBarTitleText": "招生简章",
					"navigationStyle": "default",
					"h5": {
						"titleNView": false
					}
				}	
			},
			{
				"path": "regulations",
				"style": {
					"navigationBarTitleText": "招生简章列表",
					"navigationStyle": "default",
					"h5": {
						"titleNView": false
					}
				}	
			}
		    
            ]
	}
],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "{siteName}",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"onReachBottomDistance": 100
	},
	"tabBar": {
		"color": "#666",
		"selectedColor": "#f65354",
		"borderStyle": "white",
		"backgroundColor": "#FFFFFF",
		"list": [{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "static/tab_icon/home.png",
				"selectedIconPath": "static/tab_icon/home_active.png"
			},
			{
				"pagePath": "pages/index/find_house",
				"text": "找房",
				"iconPath": "static/tab_icon/findhouse.png",
				"selectedIconPath": "static/tab_icon/findhouse_active.png"
			},
			{
				"pagePath": "pages/add/add",
				"text": "发布",
				"iconPath": "static/tab_icon/add.png",
				"selectedIconPath": "static/tab_icon/add_active.png"
			},
			{
				"pagePath": "pages/index/chat_list",
				"text": "消息",
				"iconPath": "static/tab_icon/message.png",
				"selectedIconPath": "static/tab_icon/message_active.png"
			},
			{
				"pagePath": "pages/my/my",
				"text": "我的",
				"iconPath": "static/tab_icon/my.png",
				"selectedIconPath": "static/tab_icon/my_active.png"
			}
		]
	},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式（list 的索引项）
		"list": [
			{
				"name": "首页", //模式名称
				"path": "pages/community/community", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到。
			},
			{
				"name": "会员升级", //模式名称
				"path": "pages/my/member_upgrade", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到。
			},
			{
				"name": "now", //模式名称
				"path": "pages/calculator/calculator", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到。
			}
		]
	}
}