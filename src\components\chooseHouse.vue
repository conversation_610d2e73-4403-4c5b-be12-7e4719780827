<template>
<view class="louceng_item flex-box" :class="{custom:theme==='custom',houseInfo:house_list.length<=2}">
    <view class="louceng_num right-line" v-if="ceng">
        <text>{{ceng}}层</text>
    </view>
    <view class="louceng_block" v-if="theme=='default'">
        <view class="house"  v-for="house in house_list" :key="house.id" @click="handleClick(house)">
            <view class="house_num">{{house.house_title||house.title||house.name}}</view>
            <view class="house_info flex-box">
                <text>{{house.jzmj}}</text>
                <text>{{house.shi}}室{{house.ting}}厅</text>
                <slot :slotItem="house"></slot>
                <text class="del" v-if="show_del" @click.stop.prevent="handelDel(house)">删除</text>
            </view>
            <view class="status_icon" v-if="house.sale_status==1">
                <my-icon type="yishou" size="30" color="#eb1010"></my-icon>
            </view>
            <view class="status_icon" v-else-if="house.discount_is_show==1">
                <my-icon type="youhui" size="30" color="#eb1010"></my-icon>
            </view>
        </view>
        <view class="house supplement"></view>
    </view>
    <view class="louceng_block_custom" v-else>
        <view class="house" :class="{hui:house.sale_status==1}" v-for="house in house_list" :key="house.id" @click="handleClick(house)">
            <!-- <image mode="widthFix" src="https://images.tengfangyun.com/images/icon/house_bg.png"></image> -->
            <view class="house_info">
                <view class="name">{{house.house_title||house.title||house.name}}</view>
                <view class="house_spe">
                    <text class="text">{{house.shi}}室{{house.ting}}厅{{house.wei}}卫</text>
                    <text class="text">{{house.jzmj}}</text>
                </view>
            </view>
            <view class="discount">{{house.price_desc||'　'}}</view>
            <view class="price-box flex-box">
                <view class="price_line" :class="{overflow_hide:house.overflow_hide}">
                    <text class="price">￥{{house.discount_price||''}}</text>
                    <text v-if="house.price" class="del">￥{{house.price||''}}</text>
                </view>
                <view class="price_line_btn">
                    <text class="btn">订房</text>
                </view>
            </view>
            <view class="status_icon" v-if="house.sale_status==1">
                已售
            </view>
            <!-- <view class="status_icon" v-else-if="house.discount_is_show==1">
                <my-icon type="youhui" size="26" color="#eb1010"></my-icon>
            </view> -->
        </view>
        <view class="house supplement"></view>
        <view class="house" v-if="house_list.length==1"></view>
    </view>
</view>
</template>

<script>
import myIcon from "./icon.vue"
export default {
    data() {
        return {

        }
    },
    props:{
        house_list:[Array],
        ceng:[String,Number],
        show_del:[Boolean],
        theme:{
            type: [String],
            default: 'default'
        }
    },
    components:{myIcon},
    methods:{
        handleClick(e){
            this.$emit('onClick',e)
        },
        handelDel(e){
            this.$emit('onDel',e)
        }
    }
}
</script>

<style lang="scss" scoped>
.louceng_item {
    align-items: center;
    border-radius: 10rpx;
    padding: 8rpx 6rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
    &.custom{
        padding: 0;
    }
    &.houseInfo{
        align-items: normal;
    }

    .louceng_num {
        padding: 6rpx;
        margin-right: 5rpx;

        text {
            display: inline-block;
            width: 70rpx;
            height: 70rpx;
            line-height: 70rpx;
            border-radius: 50%;
            text-align: center;
            color: $uni-color-primary;
            border: 4rpx solid $uni-color-primary;
        }
    }

    .louceng_block {
        flex: 1;
        display: flex;
        flex-wrap: wrap;

        .house {
            flex: 1;
            max-width: 50%;
            min-width: 40%;
            padding: 12rpx 10rpx;
            margin: 6rpx;
            border-radius: 6rpx;
            background-color: #f5f3f3;
            position: relative;
           
            &.supplement {
                height: 0;
                padding: 0;
                background-color: inherit;
            }

            .house_num {
                font-size: 30rpx;
                color: #333;
                margin-bottom: 10rpx;
            }

            .house_info {
                justify-content: space-between;
                align-items: center;
                font-size: 26rpx;
                color: #888;
            }
            .del{
                padding: 5rpx 20rpx;
                background-color: #f44;
                border-radius: 8rpx;
                color: #fff;
            }
            .status_icon{
                position: absolute;
                top: 0;
                right: 0;
            }
        }
    }
    .louceng_block_custom{
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        .house {
            flex: 1;
            max-width: 50%;
            min-width: 40%;
            height: 32vw;
            margin-bottom: 24rpx;
            margin-right: 10rpx;
            position: relative;
            background-image: url('https://images.tengfangyun.com//images/new_icon/hongbao_red.png');
            background-position: 0% 0%;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            &.houseInfo{
                flex: none;
                max-width: 100%;
            }
            &.hui{
                background-image: url('https://images.tengfangyun.com//images/new_icon/hongbao.png');
                .discount{
                    color: #999;
                }
                .price_line{
                    .price{
                        color: #999;
                    }
                    .btn{
                        background-color: #eaeaea;
                        color: #d8d8d8;
                    }
                }  
                .price-box{
                    .price_line_btn{
                        .btn{
                            color: #999;
                        }
                    }  
                } 
            }
            &.supplement{
                height: 0;
            }
            .house_info{
                padding: 1.3vw 2.5vw;
                text-align: center;
                // padding-top: 1.3vw;
            }
            .name{
                overflow: hidden;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                font-size: 28rpx;
                color: #333;
            }
            .house_spe{
                .text{
                    margin-top: 7rpx;
                    font-size: 22rpx;
                    line-height: 0.8;
                    color: #999;
                }
            }
            .status_icon{
                position: absolute;
                line-height: 1;
                top: 1rpx;
                right: 18rpx;
                padding: 4rpx 8rpx;
                background-color: #d8d8d8;
                color: #666;
                font-size: 22rpx;
                border-top-right-radius: 4rpx;
            }
            .discount{
                line-height: 1;
                text-align: center;
                overflow: hidden;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding: 5rpx 16rpx;
                font-size: 32rpx;
                font-weight: bold;
                color: $uni-color-primary;
            }
            .price-box{
                position: absolute;
                bottom: 20rpx;
                justify-content: space-between;
                width: 100%;
                align-items: flex-end;
                .price_line_btn{
                    .btn{
                        display: inline-block;
                        height: 36rpx;
                        line-height: 36rpx;
                        padding: 0 20rpx;
                        font-size: 22rpx;
                        transform: scale(0.9,0.9);
                        background-color: #fff;
                        color: #f44;
                        border-radius: 18rpx;
                        margin-right: 12rpx;
                    }
                }
            }
            .price_line{
                flex: 1;
                padding: 0 0 0 12rpx;
                font-size: 26rpx;
                color: #fff;
                box-sizing: border-box;
                // width: 100%;
                &.overflow_hide{
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                .price{
                    font-size: 22rpx;
                    font-weight: initial;
                    color: #F0FF00 ;
                    display: block;
                }
                .del{
                    font-size: 22rpx;
                    text-decoration:line-through;
                }
            }
        }
    }
}
</style>
