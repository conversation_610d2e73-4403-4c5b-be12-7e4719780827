<template>
  <view>
    <image v-if="header_pic" class="header_pic" :src="header_pic | imageFilter('w_1200')" mode="widthFix" />
    <view class="agent_info flex-box" @click="toAgentDetail()" v-if="agent_detail && agent_detail.id >=1">
      <view class="adv-img"><image mode="aspectFill" :src="agent_detail.prelogo | imageFilter('w_240')" alt=""></image></view>
      <view class="adv-infos flex-box">
        <view class="adv-name">{{ agent_detail.cname }}</view>
        <view class="adv-level">经纪人</view>
      </view>
      <view class="adv-oper  flex-box">
        <view class="adv-ask" @click.stop="handleChat">微聊</view>
        <view class="adv-tel" @click.stop="handleTel">电话咨询</view>
      </view>
    </view>
    <view class="house_list">
      <house :listsData="list" v-if ="params.cate_id==1 || params.cate_id == 2"  :to_detail="false" from ="decommend" @click="toDetail" :type="params.cate_id"></house>
      <block v-for="item in list" v-else :key="item.id">
          <listItemItem v-if="item.parentid == 1" :item-data="item" type="sale" @click="toEstate"></listItemItem>
          <listItemItem v-if="item.parentid == 2" :item-data="item" type="rent" @click="toEstate"></listItemItem>
          <listItemItem v-if="item.parentid == 3" :item-data="item" type="transfer" @click="toEstate"></listItemItem>
      </block>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <view v-if ="list.length>0" class ="decommend_tips flex-box">以上展示房源面积均为建筑面积</view>
    <enturstBtn v-if="agent_detail.agent_id" :to_user="agent_detail" @click="showEnturst" />
    <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false" @hide="weituo_is_show = false">
      <enturstBox
        ref="enturst_box"
        @success="$refs.enturst_popup.hide()"
        @close="$refs.enturst_popup.hide()"
        isDetail="1"
        @popup_login="showLoginPopup()"
        :to_user="agent_detail"
      />
    </my-popup>
    <!-- <login-popup
      ref="login_popup"
      @onclose="handleCloseLogin"
      :login_success_tip="false"
      :sub_content="login_tip"
      @success="onLoginSuccess"
    ></login-popup> -->
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import house from '@/components/ershou.vue'
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
import myPopup from '@/components/myPopup.vue'
// import loginPopup from '@/components/loginPopup'
import getChatInfo from '@/common/get_chat_info'
import encryptionTel from '@/common/encryption_tel.js'
// import checkLogin from '@/common/utils/check_login.js'
import {uniLoadMore} from '@dcloudio/uni-ui'
import listItemItem from "@/components/listItemItem.vue";

export default {
  name: 'DecomendList',
  components: {
    house,
    enturstBtn,
    enturstBox,
    myPopup,
    uniLoadMore,
    listItemItem
  },
  data() {
    return {
      estate_list:[],
      get_status:"loading",
      content_text:{
        contentdown:"",
        contentrefresh:"正在加载...",
        contentnomore:"没有更多数据了"
      },
      params: {
        id: '',
        // page: 1,
        // cate_id: 1
      },
      agent_detail: {},
      header_pic: '',
      list: [],
      weituo_is_show: false,
      login_tip: '为方便您及时接收消息通知，请输入手机号码',
      tel_res: {},
      show_tel_pop: false,
    }
  },
  computed: {
    isopen_chat() {
      //是否全局开启聊天
      return this.$store.state.im.ischat
    }
  },
  onLoad(options) {
    this.params.id = options.id||''
    this.params.cate_id = options.cate_id||''
    if(options.sid){
      this.params.sid = options.sid
      this.share_time =options.f_time||''
    }
    if(options.is_share){
      this.params.is_forward = 1
    }else{
      this.params.is_forward = 0
    }
    // checkLogin({
    //   success: (res)=>{
    //     console.log(res)
    //   },
    //   fail:(res)=>{
    //     this.showLoginPopup('为方便您及时接收消息通知，请输入手机号码')
    //   },
    //   complete:(res)=>{
    //     this.$store.state.user_login_status = res.status
    //   }
    // })
    this.getData()
  },
  methods: {
    getData() {
      this.get_status = "loading"
      //  forward_time:parseInt(+new Date()/1000)
      this.params.forward_time =this.share_time ||''
      this.$ajax.get('agent/agentPaper', this.params, res => {
        if (res.data.code === 1) {
          uni.setNavigationBarTitle({
            title: res.data.share.forward_title||''
          })
          if(res.data.header_pic){
            this.header_pic = res.data.header_pic
          }
          this.list = res.data.list
          // this.estate_list = res.data.list
          this.current_userid = res.data.sid||''
          this.get_status = "noMore"
          if(res.data.agent&&res.data.agent.uid){
            res.data.agent.agent_id = res.data.agent.uid
            res.data.agent.id = res.data.agent.uid
            this.agent_detail = res.data.agent
          }
          if(res.data.share){
            let time =parseInt(+new Date()/1000)
            this.share = {
              title: res.data.share.forward_title,
              pic: res.data.share.forward_pic,
              content: res.data.share.forward_desc,
              link: `${window.location.origin}/h5/decommend/list?id=${this.params.id}&cate_id=${this.params.cate_id}&is_share=1&sid=${this.current_userid||''}&f_time=${time}`
            }
          }else{
            this.share = {}
          }
          this.getWxConfig()
        } else {
          this.get_status = "noMore"
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    showEnturst() {
      this.$refs.enturst_popup.show()
      this.weituo_is_show = true
    },
    showLoginPopup() {
      if (this.$store.state.user_login_status === 1) {
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if (this.$store.state.user_login_status === 2) {
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
      // this.$refs.login_popup.showPopup()
    },
    // handleCloseLogin() {
    //   if (this.$store.state.user_login_status === 1) {
    //     uni.removeStorageSync('token')
    //     this.$navigateTo('/user/login/login')
    //   }
    //   if (this.$store.state.user_login_status === 2) {
    //     this.$navigateTo('/user/bind_phone/bind_phone')
    //   }
    // },
    onLoginSuccess() {
      this.$store.state.user_login_status = 3
      if (this.weituo_is_show) {
        console.log('登录成功后继续执行委托接口')
        this.$refs.enturst_box.handleEnturst()
      }
    },
    handleChat() {
      if (!uni.getStorageSync('token')) {
        this.showLoginPopup()
        return
      }
      if (!this.isopen_chat) {
        this.toAgentDetail()
        return
      }
      getChatInfo(this.agent_detail.id, 31)
    },
    handleTel() {
      this.tel_params = {
        id: this.agent_detail.id,
        mid: this.agent_detail.id,
        tel: this.agent_detail.tel,
        type: 'agent',
        from : 30,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      encryptionTel(this.tel_params)
    },
    retrieveTel(){
      encryptionTel(this.tel_params)
    },
    toDetail(detail){
      if(this.params.cate_id==1){
        this.$navigateTo(`/pages/ershou/detail?id=${detail.id}&shareId=${this.agent_detail.id}&shareType=2`)
      }else if(this.params.cate_id==2){
        this.$navigateTo(`/pages/renting/detail?id=${detail.id}&shareId=${this.agent_detail.id}&shareType=2`)
      }
    },
    toAgentDetail() {
      this.$navigateTo(`/pages/agent/detail?id=${this.agent_detail.id}`)
    },
    toEstate(e) {
      if (!e.detail.id) {
        return
      }
      this.$store.state.tempData = e.detail
      if (e.detail.parentid == 1) {
        this.$navigateTo('/commercial/sale/detail?id=' + e.detail.id)
      }
      if (e.detail.parentid == 2) {
        this.$navigateTo('/commercial/rent/detail?id=' + e.detail.id)
      }
      if (e.detail.parentid == 3) {
        this.$navigateTo('/commercial/transfer/detail?id=' + e.detail.id)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.header_pic{
  width: 100%;
}
.agent_info {
  align-items: center;
  position: sticky;
  background: #fff;
  z-index: 3;
  padding: 28rpx 48rpx;
  top: 0;
  // width: 100%;
  height: 80rpx;
  .adv-img {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 16rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .adv-infos {
    flex-direction: column;
    flex: 1;
    height: 100%;
    .adv-name {
      color: #333;
      font-size: 22rpx;
    }
    .adv-level {
      color: #999;
      font-size: 22rpx;
      margin-top: auto;
    }
  }
  .adv-oper {
    color: #fb656a;
    .adv-ask {
      font-size: 28rpx;
      padding: 12rpx 36rpx;
      border: 2rpx solid #fb656a;
      border-radius: 8rpx;
      box-shadow: 0 2px 4px 0 rgba(251, 101, 106, 0.1);
    }
    .adv-tel {
      font-size: 28rpx;
      padding: 12rpx 16rpx;
      border: 2rpx solid #fb656a;
      border-radius: 8rpx;
      margin-left: 24rpx;
      box-shadow: 0 2px 4px 0 rgba(251, 101, 106, 0.1);
    }
  }
}
.house_list{
  padding: 0 48rpx;
  margin-top: 24rpx;
  background-color: #fff;
}
.decommend_tips {
  padding: 20rpx 48rpx;
  justify-content:center;
  color: #999;
  font-size: 24rpx;
}
</style>
