// 海报绘图
const drawCard = {
  zoomImg: {
    width: 320,
    height: 220
  },
  getImageInfo(img) {
    return new Promise((resolve, reject) => {
      uni.getImageInfo({
        src: img,
        success: (res) => {
          resolve(res)
        },
        fail: err => {
          uni.showToast({
            title: "海报生成失败",
            icon: "none",
            duration: 2000
          })
          reject(err)
        }
      })
    })
  },
  canvasImgCons(img) { // 处理图片尺寸并返回处理后的图片以及图片需要的放置位置
    if (!isHttp(img)) {
      img = config.imgDomain + img
    }
    let defImg = {
      width: 270,
      height: 310
    }
    let defLongImg = {
      width: 270,
      height: 380
    }
    // 计算绘制主图的数据信息
    return new Promise((resolve, reject) => {
      this.getImageInfo(img).then(res => {
        // 获取图片宽高比
        let imgBili = res.width / res.height
        let isLongImg
        let zoomImg = {}
        if (imgBili <= defLongImg.width / defLongImg.height) {
          isLongImg = true
          zoomImg.height = defLongImg.height
          zoomImg.width = defLongImg.width
        } else {
          zoomImg.height = defImg.height
          zoomImg.width = defImg.width
        }
        let height, width, sx, sy;
        // 如果宽度过长
        if (res.width / res.height > zoomImg.width / zoomImg.height) {
          height = res.height
          width = zoomImg.width * height / zoomImg.height
          sy = 0
          sx = (res.width - width) / 3
        } else { // 如果高度过长
          width = res.width
          height = zoomImg.height * width / zoomImg.width
          sx = 0
          sy = 0
          // sy = (res.height - height) / 2
        }
        let imgInfo = {
          sx: sx,
          sy: sy,
          sw: width,
          sh: height,
          dx: 25,
          dy: 30,
          dw: zoomImg.width,
          dh: zoomImg.height,
          url: res.path,
          isLongImg
        }
        resolve(imgInfo)
      }).catch(err => {
        reject(err)
      })
    })
  },
  canvasImg(img) { // 处理图片尺寸并返回处理后的图片以及图片需要的放置位置
    if (!isHttp(img)) {
      img = config.imgDomain + img
    }
    // 计算绘制主图的数据信息
    return new Promise((resolve, reject) => {
      this.getImageInfo(img).then(res => {
        let height, width, sx, sy;
        if (res.width / res.height > this.zoomImg.width / this.zoomImg.height) {
          height = res.height
          width = this.zoomImg.width * height / this.zoomImg.height
          sy = 0
          sx = (res.width - width) / 2
        } else {
          width = res.width
          height = this.zoomImg.height * width / this.zoomImg.width
          sx = 0
          sy = (res.height - height) / 2
        }
        let imgInfo = {
          sx: sx,
          sy: sy,
          sw: width,
          sh: height,
          dx: 0,
          dy: 0,
          dw: this.zoomImg.width,
          dh: this.zoomImg.height,
          url: res.path
        }
        resolve(imgInfo)
      }).catch(err => {
        reject(err)
      })
    })
  },
  drawBackground(obj, draw_true = true) { // 绘制背景
    return new Promise(resolve => {
      obj.el.setFillStyle(obj.color)
      obj.el.fillRect(obj.left, obj.top, obj.width, obj.height)
      obj.el.draw(draw_true, () => {
        resolve(obj)
      })
    })
  },
  drawBackgroundTrapezoid(obj) { // 绘制背景梯形
    return new Promise(resolve => {
      obj.el.moveTo(obj.x, obj.y);   //原点   x x坐标   y y坐标
      obj.el.lineTo(obj.xr, obj.yr); // 移动的坐标 向右终点 
      obj.el.lineTo(obj.xd, obj.yd);  //向下终点 
      obj.el.lineTo(obj.xld, obj.yld);  //向左下终点 
      obj.el.lineTo(obj.xl, obj.yl);  //向左
      obj.el.lineTo(obj.xlu, obj.ylu); //向左上终点 
      obj.el.lineTo(obj.x, obj.y); //向上终点 原点
      obj.el.fillStyle = obj.color;
      obj.el.fill() //填充颜色
      obj.el.draw(true, () => {
        resolve(obj)
      })
    })
  },
	/** 
	 * <AUTHOR> 
	 * @date 2020-02-11 17:23:22 
	 * @desc 绘制矩形
	 * @param {Object} obj.x - 绘制的起始x轴坐标 obj.y - 绘制的起始y轴坐标 obj.width - 绘制的宽度 obj.width - 绘制的高度 obj.radius - 矩形的圆角 obj.color - 背景色 obj.clip - 是否进行clip
	 */
  drawRectangle(obj) { // 绘制矩形
    if (!obj.radius) {
      obj.radius = 0
    }
    return new Promise(resolve => {
      obj.el.save()
      obj.el.moveTo(obj.x + obj.radius, obj.y); //原点   x x坐标   y y坐标
      obj.el.lineTo(obj.width + obj.x - obj.radius, obj.y);  //上横
      obj.el.arcTo(obj.width + obj.x, obj.y, obj.width + obj.x, obj.y + obj.radius, obj.radius)
      obj.el.lineTo(obj.width + obj.x, obj.height + obj.y); //右竖
      obj.el.lineTo(obj.x, obj.height + obj.y);  // 下横
      obj.el.lineTo(obj.x, obj.y + obj.radius); //左竖 
      obj.el.arcTo(obj.x, obj.y, obj.x + obj.radius, obj.y, obj.radius)
      obj.el.closePath();
      if (obj.color) {
        obj.el.fillStyle = obj.color;
        obj.el.lineWidth = 0;
        obj.el.fill() //填充颜色
      }
      if (obj.clip) {
        obj.el.clip()
      }
      obj.el.draw(true, () => {
        resolve(obj)
      })
    })
  },
  onLineImg(obj) { // 绘制远程图片
    return new Promise(resolve => {
      this.getImageInfo(obj.url).then((res) => {
        if (!obj.height) { // 如果没有指定高度则根据宽度等比例缩放
          obj.height = obj.width / res.width * res.height
        }
        obj.el.drawImage(res.path, obj.left, obj.top, obj.width, obj.height)
        obj.el.draw(true, () => {
          resolve()
        })
      }).catch(err => {
        console.log(err)
        // uni.showToast({
        // 	title:"绘制失败",
        // 	icon:"none"
        // })
      })
    })
  },
  drawImg(imgInfo, obj, callback) { //绘制本地主图
    return new Promise(resolve => {
      // 百度小程序绘制图片在手机上参数是正好相反的
      // #ifdef MP-BAIDU
      if (imgInfo.sw && imgInfo.sh) {
        obj.el.drawImage(imgInfo.url, imgInfo.dx, imgInfo.dy, imgInfo.dw, imgInfo.dh, imgInfo.sx, imgInfo.sy, imgInfo.sw, imgInfo.sh)
      } else {
        obj.el.drawImage(imgInfo.url, imgInfo.dx, imgInfo.dy, imgInfo.dw, imgInfo.dh)
      }
      // #endif
      // #ifndef MP-BAIDU
      if (imgInfo.sw && imgInfo.sh) {
        obj.el.drawImage(imgInfo.url, imgInfo.sx, imgInfo.sy, imgInfo.sw, imgInfo.sh, imgInfo.dx, imgInfo.dy, imgInfo.dw, imgInfo.dh)
      } else {
        obj.el.drawImage(imgInfo.url, imgInfo.dx, imgInfo.dy, imgInfo.dw, imgInfo.dh)
      }
      // #endif
      if (callback) {
        callback()
      }
      obj.el.draw(true, () => {
        resolve()
      })
    })
  },
  drawText(obj, fun) { // 绘制文字
    return new Promise(resolve => {
      obj.el.setFontSize(obj.size)
      obj.el.setFillStyle(obj.color)
      obj.el.setTextAlign(obj.align)
      obj.el.fillText(obj.text, obj.left, obj.top)
      obj.el.draw(true, () => {
        if (fun) {
          setTimeout(() => { //不延迟多文本有时候会样式错乱
            fun()
            resolve(obj)
          }, 300)
        } else {
          resolve(obj)
        }
      })
    })
  },
}
 module.exports ={
   drawCard
 }