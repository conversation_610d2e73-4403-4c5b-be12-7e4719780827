<template>
  <view class="dropdown">
    <view class="btn" @click.prevent.stop="onClickBtn">
      <slot></slot>
    </view>
    <view
      class="dropdown_box"
      :class="{ show }"
      :style="{
        bottom: show ? dropdown_list_bottom + 'px' : dropdown_list_bottom - 2 + 'px',
        height: show ? dropdown_list_height + 'px' : 0,
        right:show ? dropdown_list_right + 'px' : dropdown_list_right - 2 + 'px',
      }"
    >
      <view class="dropdown_list">
        <slot name="dropdown_list"></slot>
      </view>
      <view class="arrow"></view>
    </view>
    <view class="mask" v-if="show" @click ="show =false"></view>
  </view>
</template>

<script>
export default {
  name: 'Dropdown',
  components: {},
  data() {
    return {
      show: false,
      dropdown_list_bottom: '',
      dropdown_list_right: '',
      dropdown_list_height: '',
    }
  },
  props: {
    disabled: {
      type: <PERSON><PERSON>an,
      default: false,
    },
    options: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    options: {
      handler(val) {
        this.tabs = val
      },
      deep: true,
    },
    tabs(val) {
      console.log(val)
    },
  },
  created() {
  },
  mounted() {
    this.query = uni.createSelectorQuery().in(this)
    this.query
      .select('.btn')
      .boundingClientRect((data) => {
        // console.log(data.bottom)
        this.dropdown_list_bottom = this.$store.state.systemInfo.windowHeight - data.top
        this.dropdown_list_right = this.$store.state.systemInfo.windowWidth - data.right
      })
      .exec()
  },
  methods: {
    onClickBtn() {
      if (this.disabled) {
        return
      }
      if (this.show) {
        this.show = false
        return
      }
      this.query
        .select('.dropdown_list')
        .boundingClientRect((data) => {
          this.dropdown_list_height = data.height + uni.upx2px(48) + uni.upx2px(24)
          this.show = true
        })
        .exec()
    },
  },
}
</script>

<style scoped lang="scss">
.dropdown {
  background-color: #fff;
  height: 100%;
  // position: relative;
  .btn {
    height: 100%;
    position: relative;
    z-index: 3;
  }
  .dropdown_box {
    box-sizing: content-box;
    border-radius: 8rpx;
    overflow: hidden;
    right: 32rpx;
    position: absolute;
    transition: 0.26s;
    z-index: 1;
    .dropdown_list {
      padding-left: 24rpx;
      padding-right: 24rpx;
      border: 1rpx solid #fff;
      border-radius: 18rpx;
      background-color: #fff;
      box-shadow: 0 2rpx 12rpx 0 rgba(#dde1e9, 0.8);
      ::v-deep .dropdown-item {
        ~ .dropdown-item {
          border-top: 1rpx solid #dde1e9;
        }
      }
    }
    &.show {
      padding: 8rpx;
      z-index: 1000;
      // overflow: initial;
      .dropdown_list {
        padding-top: 24rpx;
        padding-bottom: 24rpx;
        border-color: #dde1e9;
      }
    }
  }
  // .arrow {
  //   position: absolute;
  //   left: calc(50% - 12rpx);
  //   width: 24rpx;
  //   height: 24rpx;
  //   bottom: -16rpx;
  //   transform: rotate(-135deg);
  //   border-radius: 4rpx;
  //   border-top: 1rpx solid $border-color;
  //   border-left: 1rpx solid $border-color;
  //   background-color: #fff;
  //   box-shadow: 0 1px 4px 0 rgba($border-color, 0.4);
  // }
  .arrow {
    position: relative;
    bottom: 2rpx;
    left: calc(50% - 12rpx);
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 20rpx;
    // filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
    box-shadow: 0 0 8rpx 0 rgba(#dde1e9, 0.4);
    border-top-color: #ebeef5;
    border-bottom-width: 0;
    border-radius: 2rpx;
    z-index: 2;
    &::after {
      content: '';
      position: absolute;
      display: block;
      width: 0;
      height: 0;
      border-width: 16rpx;
      bottom: -10rpx;
      left: -15rpx;
      border-color: transparent;
      border-top-color: #fff;
      border-style: solid;
    }
  }
}
.mask{
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  // background: #000;
  background: rgba(255,255,255,0);
  z-index: 999;
}
</style>
