<template>
  <view class="customer_detail">
    <view class="customer_info">
      <image
        v-if="customer_detail.prelogo"
        class="avatar"
        :src="customer_detail.prelogo | imageFilter('w_80')"
        mode="aspectFill"
      ></image>
      <view class="user_info">
        <view class="user_name flex-row"
          ><text class="name-con">{{
            customer_detail.cname || "暂无姓名"
          }}</text>
        </view>
        <view class="user_tel">{{ customer_detail.tel }}</view>
      </view>
      <view class="btn_group">
        <view
          v-if="customer_detail.uid"
          class="button plain"
          @click="handleChat"
          >微聊</view
        >
        <view class="button plain" @click="handleTel">电话</view>
      </view>
    </view>
    <view class="form">
      <view class="form_level">
        <view class="label">客户等级</view>
        <view class="level">
          <text class="level1" :class="{level_on : level == 3}" @click="checkLevel(3)">A 级</text>
          <text class="level2" :class="{level_on : level == 2}" @click="checkLevel(2)">B 级</text>
          <text class="level3" :class="{level_on : level == 1}" @click="checkLevel(1)">C 级</text>
        </view>
      </view>
      <view class="form_row">
        <view class="input_form">
          <view class="label">意向区域</view>
          <view class="form_value" @click="$refs.area_popup.show()">
            <text v-if="customer_detail.area_id">{{
              customer_detail.area_name
            }}</text>
            <text v-else class="placeholder">请选择意向区域</text>
            <my-icon type="ic_into" color="#999" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
      <view class="form_row">
        <view class="input_form">
          <view class="label">意向小区</view>
          <input
            class="form_value"
            type="text"
            v-model="customer_detail.community"
            placeholder="请输入意向小区"
            placeholder-style="font-size: 30rpx; text-align: right; color: #999"
          />
        </view>
      </view>
    </view>
    <view class="form">
      <view class="form_row">
        <view class="input_form">
          <view class="label">客户信息</view>
          <view class="form_value" @click="showCustomer">
            <text v-if="c_name" class="value">{{ c_name }}</text>
            <text v-else class="placeholder">请选择进度</text>
            <my-icon type="ic_into" color="#999" size="32rpx"></my-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="logs_container">
      <view class="cate_list flex-box">
        <view
          class="cate_item"
          :class="{ active: type === 'visit' }"
          @click="getLogs('visit')"
          >客访轨迹</view
        >
        <view
          class="cate_item"
          :class="{ active: type === 'follow_up' }"
          @click="getLogs('follow_up')"
          >跟进记录</view
        >
      </view>
      <time-line :lineData="log_list">
        <template #content="{slotData}">
          <view class="follow_up_remark flex-box" v-if="type === 'follow_up'">
            <view class="label">备注</view>
            <view class="remark flex-1">
              <view>{{ slotData.content }}</view>
              <view class="img_list">
                <image
                  class="img"
                  v-for="(img, index) in slotData.images"
                  :key="index"
                  :src="img | imageFilter('w_240')"
                  mode="aspectFill"
                  @click="previewImg(index, slotData.images)"
                ></image>
                <view class="img vacancy"></view>
              </view>
            </view>
          </view>
        </template>
      </time-line>
      <uni-load-more
        :status="get_status"
        :content-text="content_text"
      ></uni-load-more>
    </view>
    <my-popup ref="area_popup">
      <addressPicker
        :data_list="area_list"
        @onselect="onAreaChange"
      ></addressPicker>
    </my-popup>
    <my-popup ref="progress" :touch_hide='false'>
      <followUp
        @cancel="$refs.progress.hide(),this.followShow=false"
        :cname="customer_detail.cname"
        @confirm="onProgressChange" from="follow" :show="followShow"
      />
    </my-popup>
    <view class="btn" @click="subFollowUp">提交跟进</view>
  </view>
</template>

<script>
import mySelect from "@/components/form/mySelect";
import myIcon from "@/components/myIcon";
import myPopup from "@/components/myPopup";
import addressPicker from "@/components/addressPicker.vue";
import timeLine from "../components/timeLine";
import followUp from "../../customer/components/followUp";
import { uniLoadMore } from "@dcloudio/uni-ui";
import getChatInfo from "@/common/get_chat_info";
import { formatImg } from "@/common/index.js";
import copyText from "@/common/utils/copy_text";
const innerAudioContext = uni.createInnerAudioContext();
export default {
  name: "CustomerDetail",
  components: {
    mySelect,
    myIcon,
    myPopup,
    addressPicker,
    timeLine,
    followUp,
    uniLoadMore,
  },
  data() {
    return {
      customer_detail: {},
      // current_speed: "",
      c_name:"",
      area_list: [],
      type: "visit",
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了",
      },
      log_page: 1,
      log_list: [],
      level: 3,
      is_visitor:1,
      followShow:false
    };
  },
  props:{
    from:{
      type:String,
      default:""
    }
  },
  computed: {
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.getDetail(this.id);
    }
  },
  filters: {
  },
  methods: {
    onAreaChange(e) {
      this.customer_detail.area_name = e[e.length - 1].name;
      this.customer_detail.area_id = e[e.length - 1].value;
      this.$refs.area_popup.hide();
    },
    showCustomer(){
      this.$refs.progress.show()
      this.followShow = true
    },
    getDetail(id) {
      this.$ajax.get(
        "customerServicer/infoFollowForm",
        { info_id: id },
        (res) => {
          if (res.data.code === 1) {
            // this.current_speed = res.data.speed;
            this.c_name = res.data.cname;
            this.customer_detail = res.data;
            this.customer_detail.speed = "";
            // this.is_optimization = res.data.is_optimization;
            this.area_list = res.data.areaList;
            if (res.data.level) {
              this.level = res.data.level              
            }
            this.getLogs("visit");
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
            });
          }
        }
      );
    },
    checkLevel(index){
      this.level = index
    },
    getLogs(type) {
      if (this.type != type) {
        this.log_page = 1
      }
      this.type = type;
      if (this.log_page === 1) {
        this.log_list = [];
      }
      this.get_status = "loading";
      if (this.type === "follow_up") {
        this.$ajax.get(
          "customerServicer/infoFollowList",
          { info_id: this.id, page: this.log_page, rows: 10 },
          (res) => {
            if (res.data.code === 1) {
              res.data.list = res.data.list.map((item) => {
                return {
                  time: item.ctime,
                  content: item.content,
                  speed: item.speed,
                  images: item.images,
                };
              });
              this.log_list = this.log_list.concat(res.data.list);
              this.get_status = "more";
              if (res.data.list.length < 10) {
                this.get_status = "noMore";
              }
            } else {
              this.get_status = "noMore";
            }
          }
        );
        return;
      }else{
      this.$ajax.get(
        "customerServicer/visitorDetail",
        { user_id: this.customer_detail.uid, page: this.log_page, rows: 20, uid: this.customer_detail.uid, is_visitor: this.is_visitor,tel: this.customer_detail.tel},
        (res) => {
          if (res.data.code === 1) {
            this.log_list = this.log_list.concat(res.data.list);
            this.get_status = "more";
            if (res.data.list.length < 20) {
              this.get_status = "noMore";
            }
            this.is_visitor =res.data.is_visitor
          } else {
            this.get_status = "noMore";
          }
        }
      );       
      }
    },
    previewImg(index, img_list) {
      let img_urls = img_list.map((item) => {
        return formatImg(item, "w_860");
      });
      uni.previewImage({
        current: img_urls[index],
        indicator: "number",
        urls: img_urls,
      });
    },
    onProgressChange(e) {
      // this.current_speed = e.status_text;
      this.c_name = e.cname
      this.customer_detail.speed = e.status_text;
      this.customer_detail.images = e.imgs;
      this.customer_detail.remark = e.descp;
      this.customer_detail.cname = e.cname;
      this.$refs.progress.hide();
      this.followShow = false
    },
    subFollowUp() {
      let {
        area_id,
        community,
        speed,
        images,
        remark,
        cname,
      } = this.customer_detail;
      // 接口要求跟进必须填写备注
      // if (!speed) {
      //   uni.showToast({
      //     title: "请选择跟进进度",
      //     icon: "none",
      //   });
      //   return;
      // }
      if (!remark) {
        uni.showToast({
          title: "请填写并确认跟进备注",
          icon: "none",
        });
        this.$refs.progress.show();
        this.followShow = true
        return;
      }
      let params = {
        info_id: this.id,
        area_id: area_id,
        community: community,
        speed: speed,
        images: images,
        content: remark,
        cname: cname,
        level: this.level
      };
      this.$ajax.post("customerServicer/addInfoFollow", params, (res) => {
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg,
          });
          this.customer_detail.remark = ''
          this.getLogs("follow_up");
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none",
          });
        }
      });
    },
    handleChat() {
      getChatInfo(this.customer_detail.uid);
    },
    handleTel() {
      this.$ajax.post(
        "customerServicer/infoCall",
        {
          info_id: this.id,
        },
        (res) => {
          if (res.data.code == 1) {
            uni.makePhoneCall({
              phoneNumber: res.data.info.tel,
            });
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        }
      );
    },
  },
  onReachBottom() {
    if (this.get_status !== "more") return;
    this.log_page++;
    this.getLogs(this.type);
  },
};
</script>

<style scoped lang="scss">
.customer_detail {
  padding-bottom: 200rpx;
  background: #fff;
  min-height: 100vh;
  .customer_info {
    padding: 24rpx 48rpx;
    display: flex;
    // justify-content: space-between;
    align-items: center;
    background-color: #fff;
    .avatar {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 16rpx;
    }
    .user_info {
      flex: 1;
      color: #333;
      max-width: 50%;
      .user_name {
        font-weight: 500;
        margin-bottom: 12rpx;
        align-items: center;
        display: flex;
        flex: 1;
        font-size: 32rpx;
        .name-con {
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 60%;
          white-space: nowrap;
        }
        .levelname {
          line-height: 32rpx;
          margin: 0 16upx;
          padding: 0 16rpx;
          display: inline-block;
          // border-radius: 16upx;
          font-size: 22upx;
          // border: 1upx solid #4ebdf8;
          // color: #999;
          color: #fff;
          // background-color: #f2f2f2;
          background-image: linear-gradient(180deg, #69d4bb, #00caa7);
          font-weight: initial;
          border-top-left-radius: 4px;
          border-bottom-right-radius: 4px;
          &.official {
            // background-color: #1296db;
            color: #fff;
            background-image: linear-gradient(180deg, #8cd3fc, #4cc7f6);
            // border: 1upx solid #1296db;
          }
          &.agent {
            // border: 1upx solid #f96063;
            // background-color: #f96063;
            background-image: linear-gradient(180deg, #ff9767, #fd7737);
            color: #fff;
          }
          &.adviser {
            // border: 1upx solid #f0bb2c;
            // background-color: #f0bb2c;
            color: #fff;
            background-image: linear-gradient(180deg, #fcd88c, #f6ce4c);
          }
          &.zijian {
            // border: 1upx solid #f0bb2c;
            // background-color: #f0bb2c;
            color: #fff;
            background-image: linear-gradient(180deg, #4c86b3, #838afb);
          }
        }
      }
    }
    .btn_group {
      display: flex;
      justify-content: space-between;
      margin-left: auto;
      .button {
        height: 64rpx;
        line-height: 64rpx;
        padding: 0 32rpx;
        border-radius: 8rpx;
        text-align: center;
        font-size: 28rpx;
        box-sizing: border-box;
        margin-top: 1rpx;
        ~ .button {
          margin-left: 24rpx;
        }
        &.plain {
          color: #ff5500;
          border: 1rpx solid #ffa402;
          // border: 1rpx solid $uni-color-primary;
          // color: $uni-color-primary;
        }
        &.disabled {
          background-color: #f2f2f2;
          border-color: #f2f2f2;
          color: #d8d8d8;
        }
      }
    }
  }
}
.oper {
  padding: 24rpx 48rpx;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 24rpx solid #f8f8f8;
  .btns {
    padding: 10rpx 40rpx;
    text-align: center;
    border-radius: 10rpx;
    flex: 1;
    color: #666;
    display: flex;
    .btns-label {
      margin-left: 10rpx;
    }
    &.copy {
      // background: #999;
      background-image: linear-gradient(125deg, #ff5500 0%, #ffa402 100%);
      box-shadow: 0 1px 5px 0 rgba(255, 145, 1, 0.5);
      border-radius: 1px;
      border-radius: 1px;
      color: #fff;
      display: flex;
      justify-content: center;
    }
    &.alert {
      padding-left: 0;
      // background: #FB656A ;
      // color: #fff;
      // background-image: linear-gradient(180deg, #69d4bb, #00caa7);
      // box-shadow: 0 1px 5px 0 rgba(78, 192, 165, 0.5);
      // border-radius: 1px;
      // border-radius: 1px;
      // color: #fff;
    }
    &.del {
      padding-right: 0;
      display: flex;
      justify-content: flex-end;
      // background: #AB642C ;
      // color: #fff;
    }
  }
}

.form {
  border-top: 24rpx solid #f8f8f8;
  padding: 0 48rpx;
  background-color: #fff;
  .form_level{
    height: 72rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 0;
    .level{
      flex: 1;
      text-align: right;
      text{
        padding: 2rpx 40rpx;
        margin-right: 20rpx;
        color: #fff;
        background: #fff;
        border: 2rpx solid;
          &.level_on{
            color: #fff;
          }
      }
      .level1{
          border-color: #ff5500;
          color: #ff5500;
          &.level_on{
            background: #ff5500;
          }
      }
      .level2{
          border-color: #fbc365;
          color: #fbc365;
          &.level_on{
            background: #fbc365;
          }
      }
      .level3{
          border-color: #d8d8d8;
          color: #d8d8d8;
          &.level_on{
            background: #d8d8d8;
          }
      }
    }
    .label {
      font-size: 28rpx;
      color: #666;
      margin-right: 16rpx;
    }
  }
  .form_row {
    padding: 10rpx 0;
    .input_form {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .label {
        font-size: 28rpx;
        color: #666;
        margin-right: 16rpx;
      }
      .form_value {
        height: 72rpx;
        text-align: right;
        box-sizing: border-box;
        padding: 16rpx;
        flex: 1;
        color: #333;
        font-size: 28rpx;
        .value {
          color: #ff5500;
          font-weight: bold;
        }
      }
      .placeholder {
        color: #999;
      }
    }
  }
}

.logs_container {
  border-top: 24rpx solid #f8f8f8;
  background-color: #fff;
}

.cate_list {
  background-color: #fff;
  justify-content: center;
  align-items: center;
  margin-bottom: 24rpx;
  .cate_item {
    padding: 24rpx;
    margin: 0 30rpx;
    position: relative;
    color: #999;
    transition: 0.26s;
    &.active {
      color: #333;
    }
    &.active::after {
      content: "";
      position: absolute;
      width: 32%;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      height: 8rpx;
      border-radius: 4rpx;
      background-color: $uni-color-primary;
    }
  }
}

.follow_up_remark {
  .label {
    margin-right: 24rpx;
    color: #666;
  }
  .remark {
    color: #999;
  }
  .img_list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
    .img {
      width: 168rpx;
      height: 168rpx;
      &.vacancy {
        height: 0;
      }
    }
  }
}

.tel-item {
  align-items: center;
  .tel-info {
    overflow: hidden;
    .title_row {
      font-size: 28rpx;
      line-height: 1.5;
      margin-bottom: 15rpx;
    }
    .time {
      font-size: 24rpx;
      font-weight: bold;
      color: #999;
    }
  }
  .voice {
    min-width: 120rpx;
    .play_vioce_icon {
      width: 36rpx;
      height: 36rpx;
    }
  }
}

.btn {
  position: fixed;
  left: 48rpx;
  right: 48rpx;
  bottom: 64rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 8rpx;
  text-align: center;
  font-size: 32rpx;
  background-image: linear-gradient(125deg, #ff5500 0%, #ffa402 100%);
  box-shadow: 0 4rpx 12rpx 0 rgba(255, 145, 1, 0.5);
  // background-color: $uni-color-primary;
  // box-shadow: 0 8rpx 32rpx 0 rgba($color: $uni-color-primary, $alpha: 0.40);
  color: #fff;
}
</style>
