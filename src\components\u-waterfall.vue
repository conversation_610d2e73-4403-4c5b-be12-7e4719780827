<template>
    <view class="waterfall">
        <view class="left">
            <block v-for="(item, index) in leftList" :key="index">
                <view class="waterfall-item">
                   <indexItem @onImageLoad = "onImageLoad($event,index) "  :itemData="item" :type="type" :id='"item" +index' @click="toDetail(item)"></indexItem>
                </view>
            </block>
        </view>
        <view class="right">
            <block v-for="(item, index) in rightList" :key="index">
                <view class="waterfall-item"  >
                   <indexItem :id='"item" +index'  :itemData="item" @onImageLoad = "onImageLoad($event,index)" :type="type" @click="toDetail(item)"></indexItem>
                </view>
            </block>
        </view>
    </view>
</template>

<script>
import indexItem from '@/components/indexItem'
export default {
    name: 'water-fall',
		components:{indexItem},
    props: {
        list: {
            type: Array,
            default: []
        },
				type:{
					type:String,
					default:""
				}
    },
    watch: {
        list(n, o) {
            let that = this;
         console.log('=====watch  list=====', n, o);
            let ol = o.length;
            let nl = n.length;
            if (nl > ol) {
                if (this.leftHeight > this.rightHeight) {
                    that.rightList.push(that.list[ol]);
                } else {
                    that.leftList.push(that.list[ol]);
                }
                this.onImageLoad();
            }
        }
    },
    data() {
        return {
            leftList: [],
            rightList: [],
            itemIndex: 0,
            leftHeight: 0,
            rightHeight: 0
        };
    },
    created() {
        this.leftList = [this.list[0]]; //第一张图片入栈
    },
    destroyed() {
        console.log('destroy');
    },
    methods: {
        onImageLoad(e,index) {
            if (!e) {
                console.log('无图片！！！！');
                return;
            }
						console.log(e,index,1232);
						
            let imgH = (e.detail.height / e.detail.width) * 345 + 100 + 20; //图片显示高度加上下面的文字的高度100rpx,加上margin-bottom20rpx
            let that = this;
            if (that.itemIndex === 0) {
                that.leftHeight += imgH; //第一张图片高度加到左边
                that.itemIndex++;
                that.rightList.push(that.list[that.itemIndex]); //第二张图片先入栈
            } else {
                that.itemIndex++;
                //再加高度
                if (that.leftHeight > that.rightHeight) {
                    that.rightHeight += imgH;
                } else {
                    that.leftHeight += imgH;
                }
                if (that.itemIndex < that.list.length) {
                    //下一张图片入栈
                    if (that.leftHeight > that.rightHeight) {
                        that.rightList.push(that.list[that.itemIndex]);
                    } else {
                        that.leftList.push(that.list[that.itemIndex]);
                    }
                }
            }
        },
				toDetail(e){
					this.$emit(toDetail,{item:e,type})
				}
    }
};
</script>

<style lang="scss">
.waterfall {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20rpx;
    box-sizing: border-box;
    .left,
    .right {
        .waterfall-item {
            width: 345rpx;
            margin-bottom: 20rpx;
            background-color: pink;
            box-sizing: border-box;
            image {
                width: 345rpx;
                display: block;
            }
            .title {
                width: 345rpx;
                height: 100rpx;
                overflow: hidden;
            }
        }
    }
}
</style>