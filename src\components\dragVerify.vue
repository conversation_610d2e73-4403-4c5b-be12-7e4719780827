<template>
<view class="verity_box">
    <view style="width:320px;margin:auto;background-color:#fff">
        <view class="img_box">
            <view class="reset" @click="refresh">
                <my-icon type="huanyihuan" color="#ffffff" size="40rpx" line_height="60rpx"></my-icon>
            </view>
            <view class="bg" :style="{backgroundImage:'url('+verify_img+')'}"></view>
            <view :style="{left:btn_left+'px',backgroundImage:'url('+verify_img+')'}" class="block">
				<view class="block_img" :style="{left:btn_left+'px',backgroundImage:'url('+verify_img+')'}">
				</view>
            </view>
        </view>
        <view class="drag_bar flex-box">
            <view class="btn" :class="{error:verify_fail,active:active}" :style="{left:btn_left+'px'}" @touchstart="handleTouchstart" @touchmove.stop.prevent="handleTouchmove" @touchend="handleEnd">
                <my-icon :color="verify_fail||active?'#ffffff':'#666666'" :type="verify_fail?'ic_guanbi':(verify_success?'wancheng':'xiangyou')"></my-icon>
            </view>
            <view class="jindu_l" :class="{error:verify_fail,active:active}" :style="{width:btn_left+20+'px',height:'100%'}"></view>
            <view class="jindu_r" :style="{width:320-20-btn_left+'px',height:'100%'}">
                <text class="tip" :class="{hide:btn_left>5}">向右拖动滑块填充拼图</text>
            </view>
        </view>
    </view>
</view>
</template>

<script>
import myIcon from '../components/myIcon'
export default {
    data() {
        return {
            start_left:0,
            btn_left:0,
            active:false,
        }
    },
    props:{
       verify_img:{
           type:String
       },
       verify_fail: {
           type:Boolean,
           default:false
       },
       verify_success: {
           type:Boolean,
           default:false
       },
    },
    watch:{
        verify_img(val){
            if(val){
                this.btn_left = 0
                this.active = false
                this.refreshEnd()
            }
        }
    },
    components: {
        myIcon
    },
    methods:{
        refresh(){
            this.$emit('onrefresh')
        },
        refreshEnd(){
            this.start_left = 0
            this.$emit('onrefreshend')
        },
        handleTouchstart(e){
            if(this.verify_fail||this.verify_success){
                return
            }
            this.active = true
            this.start_left = e.changedTouches[0].pageX
            console.log("初始值：",this.start_left)
        },
        handleTouchmove(e){
            if(this.verify_fail||this.verify_success||!this.active){
                return
            }
            const moving_left = e.changedTouches[0].pageX
            if(moving_left-this.start_left<0){
                this.btn_left = 0
                return
            }
            if(moving_left-this.start_left>270){
                this.btn_left = 270
                return
            }
            this.btn_left = moving_left-this.start_left
        },
        handleEnd(){
            if(this.btn_left===0){
                this.active = false
                return
            }
            if(this.verify_fail||this.verify_success){
                return
            }
            this.$emit('ondragend',this.btn_left)
        },
    }
}
</script>

<style scoped lang="scss">
.verity_box{
    padding: 5px;
    box-sizing: border-box;
    width: 100%;
    .reset{
        position: absolute;
        right: 10rpx;
        top: 10rpx;
        width: 60rpx;
        height: 60rpx;
        text-align: center;
        line-height: 60rpx;
        border-radius: 50%;
        display: inline-block;
        background-color: rgba($color: #000000, $alpha: 0.3);
        z-index: 2;
    }
    .img_box{
        width: 320px;
        height: 160px;
        position: relative;
        .bg{
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            background-size: 100%;
            background-repeat: no-repeat;
        }
        .block{
            width: 50px;
            height: 160px;
            position: absolute;
            left: 0;
            background-repeat: no-repeat;
            background-position: 0 -160px;
			.block_img{
				width: 100%;
				height: 100%;
				background-repeat: no-repeat;
				background-position: 0 -160px;
			}
        }
    }
    .drag_bar{
        margin-top: 15rpx;
        width: 100%;
        height: 40px;
        line-height: 40px;
        text-align: center;
        position: relative;
        background-color: #fff;
        font-size: 0;
        border-radius: 6rpx;
        overflow: hidden;
        .jindu_l{
            box-sizing: border-box;
            display: inline-block;
            border-radius: 6rpx;
            &.active{
                background-color: #a3d3fd;
                border: 1px solid #1991fa;
                border-right: 0;
            }
            &.error{
                background-color: #ff9999;
                border: 1px solid #f00;
                border-right: 0;
            }
        }
        .jindu_r{
            box-sizing: border-box;
            display: inline-block;
            border-radius: 6rpx;
            border: 1px solid #dedede;
        }
        .btn{
            height: 40px;
            width: 50px;
            background-color: #f3f3f3;
            position: absolute;
            left: 0;
            top: 0;
            border: 1px solid #dedede;
            box-sizing: border-box;
            // border-right: 0;
            border-radius: 6rpx;
            &.active{
                border: 1px solid #1991fa;
                background-color: #5eb2fb;
            }
            &.error{
                background-color: #ff4c4c;
                border: 1px solid #f00;
            }
        }
        .tip{
            transition: 0.26s;
            color: #666;
            font-size: 26rpx;
            &.hide{
                opacity: 0;
            }
        }
    }
}
</style>
