<template>
	<view class="">
		<view class="jubao-box">
			<view class="close" @click="close({msg:'手动关闭'})">❌</view>
			<view class="row bottom-line">选择举报类型</view>
			<view>
				<radio-group @change="radioChange">
					<label v-for="(item,index) in options" :key="index" class="list-item bottom-line" hover-class="navigator-hover">
						<radio color="#f65354" :value="item.type" :checked="false" />
						<view class="list-info flex-1">
							<view class="list-title">{{item.title}}</view>
						</view>
					</label>
				</radio-group>
			</view>
			<view class="btn-box">
				<button class="default" @click="subJubao()">确定提交</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			options:Array,
			api:String,
			params:Object
		},
		data() {
			return {
			};
		},
		methods:{
			showJubao(){
				this.$refs.jubao.show()
			},
			radioChange(e){
				let index = parseInt(e.detail.value)
				this.jubao_type = this.options[index].title
			},
			subJubao(){
				if(!this.jubao_type){
					uni.showToast({
						title:'请选择举报类型',
						icon:"none"
					})
					return
				}
				this.$ajax.get(this.api,Object.assign(this.params,{content:this.jubao_type}),res=>{
					uni.showToast({
						title:res.data.msg,
						icon:"none"
					})
					if(res.data.code == 1){
						setTimeout(()=>{
							this.close(res.data)
						},1000)
					}
				})
			},
			close(res){
				console.log(res)
				this.$emit('success',{res})
			}
		}
	}
</script>

<style lang="scss">
	.jubao-box{
		height:100vh;
		width:100%;
		background-color:#fff;
		.row{
			padding:24upx 30upx;
		}
		.close{
			display:inline-block;
			padding:24upx;
			position:absolute;
			top:0;
			right:0;
			z-index:2
		}
	}
	.list-item{
	    display: flex;
	    align-items: center;
	    padding: $uni-spacing-col-lg $uni-font-size-lg;
	    radio{
	        padding: 20upx 30upx;
	        margin-left: -30upx;
	    }
	    .list-title{
	        font-size: $uni-font-size-lg;
	        text-overflow:ellipsis;
	        white-space:nowrap;
	        line-height:1.5;
	        overflow:hidden;
	    }
	}
</style>
