<template>
    <view class="page">
        <view class="title_top">
            <view class="search-box">
                <view class="search-icon"><my-icon type="ic_sousuo" color="#999999"></my-icon></view>
                <input type="text" v-model="val" confirm-type="search" @input="handleSearch()" placeholder="请输入小区名称" />
            </view>
        </view>
        <view class="title_nav only" @click="handletap(item.id,item.title)" v-for="(item, index) in list" :key="index">
            <view>{{ item.title }}</view>
            <view>{{ item.areaname }}</view>
        </view>
    </view>
</template>

<script>
import myIcon from "@/components/myIcon";
export default {
    components: {
        myIcon,
    },
    data() {
        return {
            loading: false,
            list: [],
            val: "",
        }
    },
    onLoad() {
       
    },
    watch:{
        'val':{
            handler: function (val,oldval){
                if(val==""){
                    this.list=[]
                    console.log("清空")
                    uni.hideLoading()
                }
            },
            deep:true,
            immediate:true
        }
    }   , // index/searchCommunity.html
    methods: {
        handletap(id,name) {
            this.$navigateTo("/room/assess/apprarser?query="+id+'&cate='+name)
        },
        handleSearch() {
            uni.showLoading({
                title: '加载中'
            });
            this.loading = true
            if (this.loading) {
                this.$ajax.get('index/searchCommunity.html', { type: 4, keywords: this.val }, res => {
                    console.log(res)
                    if (res.data.code == 1) {
                        this.list = res.data.list
                        this.loading = false
                        uni.hideLoading()
                    }else{
                        this.list = []
                        uni.hideLoading()
                    }
                })
            }
        },
    }
}
</script>

<style scoped lang="scss">
.page {
    background: white;
    min-height: calc(100vh - 88rpx);
}

.search-box {
    width: 670rpx;
    background-color: #f1f1f1;
    border-radius: 40rpx;
    height: 80rpx;
    position: relative;
    top: 0rpx;
    left: 40rpx;

    .search-icon {
        width: 36rpx !important;
        height: 36rpx !important;
        position: absolute;
        top: 22rpx;
        left: 40rpx;
    }

    input {
        position: absolute;
        top: 22rpx;
        left: 84rpx;
        font-size: 26rpx;
    }
}

.only {
    margin-top: 18rpx !important;
}

.title_nav {
    width: 654rpx;
    height: 116rpx;
    // background: red;
    margin: 0 auto;
    border-bottom: 0.5px #f0f0f0 solid;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    view:nth-child(1) {
        color: #232323;
        font-size: 32rpx;
        line-height: 116rpx;
        color: #232323;
        font-family: PingFang SC;
        font-weight: medium;
    }

    view:nth-child(2) {
        color: #979797;
        font-size: 28rpx;
        line-height: 116rpx;
    }
}
</style>