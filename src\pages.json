{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
			
		  {
			"path": "pages/index/index",
			"style": {
				// "navigationBarTitleText": "腾房云",
				"enablePullDownRefresh": true,
				"h5":{
					"titleNView":false
				}
			}
		}, {
			"path": "pages/news/news",
			"style": {
				"navigationBarTitleText": "资讯频道",
				"enablePullDownRefresh": true,
				"navigationStyle": "default",
				"app-plus": {
					"animationType": "pop-in",
					"animationDuration": 260
				}
			}
		},
		{
			"path": "pages/news/detail",
			"style": {
			}
		},
		{
			"path": "pages/web_view/webview",
			"style": {
				"navigationStyle": "custom",
				"h5": {
					"titleNView": false
				}

			}
		},
		{
			"path": "pages/new_house/new_house",
			"style": {
				"navigationBarTitleText": "新楼盘",
				"enablePullDownRefresh": true,
				"navigationStyle": "default"
			}
		},
		{
				"path": "pages/new_house/photo",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "default",
					"app-plus": {
						"titleNView": {
							"backgroundColor" :"#f7f7f7",
							"type" :"transparent",
							"buttons" :[
								{
									"type": "share"
								}
							]
						}
					},
					"h5": {
						"titleNView": {
							"backgroundColor": "#f7f7f7",
							"type": "transparent"
						}
					}
				}
		},
		{
				"path": "pages/new_house/house_type_list",
				"style": {
					"navigationBarTitleText": "全部户型",
					"navigationStyle": "default"
				}
		},
		{
			"path": "pages/new_house/detail",
			"style": {
				// "navigationBarTitleText": "",
				"backgroundColor": "#f7f7f7",
				// "transparentTitle":"always",
				// "titlePenetrate":"YES",
				"h5":{
					"titleNView":false
				},
				"app-plus":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons":[
							{
								"type": "share"
							}
						]
					}
				}
			}
		},
		{
			"path": "pages/new_house/info",
			"style": {
				"navigationBarTitleText": "",
				"backgroundColor": "#f7f7f7"
			}
		},
			{
				"path": "pages/new_house/buildNews",
				"style": {
					"navigationBarTitleText": "",
					"backgroundColor": "#f7f7f7",
					"enablePullDownRefresh": true
				}
			},
		{
			"path": "pages/new_house/comment",
			"style": {
				"navigationBarTitleText": "楼盘点评",
				"backgroundColor": "#f7f7f7"
			}
		},
		{
			"path": "pages/new_house/photos",
			"style": {
				"navigationBarTitleText": "楼盘相册",
				"backgroundColor": "#f7f7f7"
			}
		},
		{
			"path": "pages/new_house/activity",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "default",
				"h5": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/index/find_house",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/add/add",
			"style": {
				"navigationBarTitleText": "发布",
				"navigationStyle": "default",
        "h5": {
          "titleNView":false
        }
			}
		},
		{
			"path": "pages/add/detail",
			"style": {
				"navigationBarTitleText": "发布信息"
			}
		},
		// {
		// 	"path": "pages/add/push",
		// 	"style": {
		// 		"navigationBarTitleText": "发布信息"
		// 	}
		// },
		{
			"path": "pages/ershou/ershou",
			"style": {
				"navigationBarTitleText": "二手房",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
				// "h5": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"text": "发布",
				// 			"fontSize": "15px",
				// 			"width": "40px"
				// 		}]
				// 	}
				// },
				// "app-plus": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"text": "发布",
				// 			"fontSize": "15px",
				// 			"width": "40px"
				// 		}]
				// 	}
				// }
			}
		},
		{
			"path": "pages/ershou/detail",
			"style": {
				// "navigationBarTitleText": "",
				"h5":{
					// "titleNView":"default",
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
							{
								"type": "share"
							}
						]
					}
				},
				"app-plus":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
							{
								"type": "share"
							}
						]
					}
				}
			}
		},
		
		{
			"path": "pages/index/chat_list",
			"style": {
				"navigationBarTitleText": "消息",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
				// "h5": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"float": "left",
				// 			"fontSize": "15px",
				// 			"width": "100px",
				// 			"text": "全部已读"
				// 		}]
				// 	}
				// },
				// "app-plus": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"float": "left",
				// 			"fontSize": "15px",
				// 			"width": "100px",
				// 			"text": "全部已读"
				// 		}]
				// 	}
				// }
			}
		},
		{
			"path": "pages/my/my",
			"style": {
				"navigationBarTitleText": "个人中心",
				"h5":{
					"titleNView":false
				}
			}
		}, {
			"path": "pages/renting/renting",
			"style": {
				"navigationBarTitleText": "出租房",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
				// "h5": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"text": "发布",
				// 			"fontSize": "15px",
				// 			"width": "40px"
				// 		}]
				// 	}
				// },
				// "app-plus": {
				// 	"titleNView": {
				// 		"buttons": [{
				// 			"text": "发布",
				// 			"fontSize": "15px",
				// 			"width": "40px"
				// 		}]
				// 	}
				// }
			}
		},
		{
			"path": "pages/renting/detail",
			"style": {
				// "navigationBarTitleText": "",
				"h5":{
					// "titleNView":false
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
							{
								"type": "share"
							}
						]
					}
				},
				"app-plus":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
							{
								"type": "share"
							}
						]
					}
				}
			}
		},
		 {
			"path": "pages/house_price/house_price",
			"style": {
				"navigationBarTitleText": "小区房价",
				"navigationStyle": "custom"
			}
		},
		
		{
			"path": "pages/house_price/detail",
			"style": {
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
								{
									"type": "home"
								}
							]
					}
				},
				"app-plus":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"buttons": [
								{
									"type": "home"
								}
							]
					},
					 "bounce": "none"
				}
			}
		},				
		
		{
			"path": "pages/house_price/photos",
			"style": {
				"navigationBarTitleText": "小区专家"
			}
		},
		{
			"path": "pages/search/search",
			"style": {
				"navigationBarTitleText": "搜索",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/groups/groups",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "default"
			}
		},
    {
      "path": "pages/groups/discount",
      "style": {
				"navigationBarTitleText": "",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				},
				"app-plus":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
    },
		{
			"path": "pages/groups/detail",
			"style": {
				"navigationBarTitleText": "",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
		},
		{
			"path": "pages/community/community",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": true,
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/community/detail",
			"style": {
				"navigationBarTitleText": "详情"
			}
		},
		{
			"path": "pages/community/news_list",
			"style": {
				"navigationBarTitleText": "楼盘动态",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/agent/agent",
			"style": {
				"navigationBarTitleText": "经纪人",
				"navigationStyle": "default"
			}
		}, {
			"path": "pages/agent/detail",
			"style": {
				"navigationBarTitleText": "经纪人",
				"navigationStyle": "default",
				"h5": {
					"titleNView": false
				}
				// "h5":{
				// 	"titleNView":{
				// 		"backgroundColor":"#f7f7f7",
				// 		"type":"transparent"
				// 	}
				// },
				// "app-plus":{
				// 	"titleNView":{
				// 		"backgroundColor":"#f7f7f7",
				// 		"type":"transparent"
				// 	}
				// }
			}
		},
		 
		 {
			"path": "pages/map_find/map_find",
			"style": {
				"navigationBarTitleText": "地图找房",
				"navigationStyle": "default",
				"app-plus": {
					"bounce": "none"
				}
				
			}	
		}, 	
		{
			"path" : "pages/consultant/consultant",
			"style" : {
				"navigationBarTitleText": "顾问排行榜",
				"navigationStyle": "default"
			}
		},
		
			{
				"path": "pages/consultant/consuList",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "default"
				}
			},
			
		{
			"path" : "pages/consultant/detail",
			"style" : {
				"navigationBarTitleText": "置业顾问",
				"navigationStyle": "default",
				"h5":{
					"titleNView":"false"
				}
			}
		},
		
			// {
			// 	"path": "pages/consultant/shareDetail",
			// 	"style": {
			// 		"navigationBarTitleText": "分享详情",
			// 		"navigationStyle": "default"
			// 	}
			// },
		
		
		{
			"path": "pages/tudi/index",
			"style": {
				"navigationBarTitleText": "土地市场",
				"navigationStyle": "default"
				// "h5":{
				// 	"titleNView":{
				// 		"backgroundColor":"#f7f7f7",
				// 		"type":"transparent",
				// 		"searchInput":{
				// 			"align":"left",
				// 			"borderRadius":"2px",
				// 			"placeholder":"地块位置（某某路）",
				// 			"placeholderColor":"#666666"
				// 		}
				// 	}
				// },
				// "app-plus":{
				// 	"titleNView":{
				// 		"backgroundColor":"#f7f7f7",
				// 		"type":"transparent",
				// 		"searchInput":{
				// 			"align":"left",
				// 			"borderRadius":"2px",
				// 			"placeholder":"地块位置（某某路）",
				// 			"placeholderColor":"#666666"
				// 		}
				// 	}
				// }
			}
		}, {
			"path": "pages/tudi/detail",
			"style": {
				"navigationBarTitleText": "地块详情",
				"navigationStyle": "default"
				// "h5":{
				// 	"titleNView":{
				// 		"backgroundColor":"#f7f7f7",
				// 		"type":"transparent"
				// 	}
				// },
				// "app-plus":{
				// 	"titleNView":{
				// 		"backgroundColor":"#f7f7f7",
				// 		"type":"transparent"
				// 	}
				// }
			}
		}, {
			"path": "pages/yushou/index",
			"style": {
				"navigationBarTitleText": "预售证查询系统",
				"navigationStyle": "default"
				// "h5":{
				// 	"titleNView":{
				// 		"backgroundColor":"#f7f7f7",
				// 		"type":"transparent",
				// 		"searchInput":{
				// 			"align":"left",
				// 			"borderRadius":"2px",
				// 			"placeholder":"项目名称（支持模糊查询）",
				// 			"placeholderColor":"#666666"
				// 		}
				// 	}
				// },
				// "app-plus":{
				// 	"titleNView":{
				// 		"backgroundColor":"#f7f7f7",
				// 		"type":"transparent",
				// 		"searchInput":{
				// 			"align":"left",
				// 			"borderRadius":"2px",
				// 			"placeholder":"项目名称（支持模糊查询）",
				// 			"placeholderColor":"#666666"
				// 		}
				// 	}
				// }
			}
		}, {
			"path": "pages/yushou/detail",
			"style": {
				"navigationBarTitleText": "预售详情",
				"navigationStyle": "default"
				// "h5":{
				// 	"titleNView":{
				// 		"backgroundColor":"#f7f7f7",
				// 		"type":"transparent"
				// 	}
				// },
				// "app-plus":{
				// 	"titleNView":{
				// 		"backgroundColor":"#f7f7f7",
				// 		"type":"transparent"
				// 	}
				// }
			}
		}, 
		{
            "path" : "pages/calculator/calculator",
            "style" : {
				"navigationBarTitleText": "房价计算",
				"navigationStyle": "default"
			}
		}
		,{
            "path" : "pages/calculator/res",
            "style" : {
				"navigationBarTitleText": "计算结果",
				"navigationStyle": "default"
			}
        }
        ,{
            "path" : "pages/web_view/web_view",
            "style" : {
				"h5":{
					"titleNView":false
				}
			}
		}
        ,{
            "path" : "pages/comment_list/comment_list",
            "style" : {
				"navigationBarTitleText": "评论列表",
				"navigationStyle": "default"
			}
        }
        ,{
            "path" : "pages/comment_list/comment_detail",
            "style" : {
				"navigationBarTitleText": "评论详情",
				"navigationStyle": "default"
			}
        },
		{
            "path" : "pages/aerial/aerial",
            "style" : {
				"navigationBarTitleText": "评论详情",
				"navigationStyle": "default"
			}
        }
    ],
	"subPackages": [
		{ //数据看报
			"root":"newspaper",
			"pages":[
			{
			"path":"newspaper",
			"style":{
			"navigationBarTitleText":"",
			"navigationBarTextStyle":"white",
			"navigationBarBackgroundColor":"#313a4c"
			}
			}
			]
			},
			{//每周余量看报
			"root":"newspaper",
			"pages":[
			{
			"path":"report",
			"style":{
			"navigationBarTitleText":"",
			"navigationBarTextStyle":"white",
			"navigationBarBackgroundColor":"#313a4c"
			}
			}
			]
			},
		{	 //航拍视频
		"root":"hangpai",
		"pages":[
			{
				"path":"hangpai",
				"style":{
					"navigationBarTitleText":"",
					"navigationStyle":"custom"
				}
			}
		]
		},
		{ // 房价评估
		"root":"room",
		"pages":[
			{
				"path":"assess/assess",
				"style":{
					"navigationBarTitleText":"房价评估",
					"navigationStyle":"default"
				}
			},
			{
				"path": "assess/sellsersh",
				"style": {
					"navigationBarTitleText": "小区搜索",
					"navigationStyle": "default"
				}
			},
			{
				"path":"assess/apprarser",
				"style":{
					"navigationBarTitleText":"房价评估",
					"navigationStyle": "default"
				}
			},{
				"path":"assess/relestate",
				"style":{
					"navigationBarTitleText":"房价评估",
					"navigationStyle":"default"
				}
			},{
				"path":"assess/expenses",
				"style":{
					"navigationBarTitleText":"税费计算",
					"navigationStyle":"default"
				}
			},
			{
				"path":"assess/service",
				"style":{
					"navigationBarTitleText":"交易服务",
					"navigationStyle":"default"
				}
			}	
		]
	},
	// 直播视频
	{
		"root":"radio",
		"pages":[
			{
				"path":"video",
				"style":{
					"navigationBarTitleText":"",
					"navigationStyle": "custom",
					"enablePullDownRefresh":true
				}
			}
		]
	},
		{ // 求购求租
        "root": "needPage",
        "pages": [{
            "path": "buy_house/buy_house",
            "style": {
				"navigationBarTitleText": "房屋求购",
				"enablePullDownRefresh": true,
				"navigationStyle": "default",
				"h5": {
					"titleNView": {
						"buttons": [{
							"text": "发布",
							"fontSize": "15px",
							"width": "40px"
						}]
					}
				},
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"text": "发布",
							"fontSize": "15px",
							"width": "40px"
						}]
					}
				}
			}
        },
		{
		    "path": "buy_house/detail",
		    "style": {
				"navigationBarTitleText": "求购详情"
			}
		},
		{
		    "path": "rest_house/rest_house",
		    "style": {
				"navigationBarTitleText": "房屋求租",
				"enablePullDownRefresh": true,
				"navigationStyle": "default",
				"h5": {
					"titleNView": {
						"buttons": [{
							"text": "发布",
							"fontSize": "15px",
							"width": "40px"
						}]
					}
				},
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"text": "发布",
							"fontSize": "15px",
							"width": "40px"
						}]
					}
				}
			}
		},{
            "path": "rest_house/detail",
            "style": {
				"navigationBarTitleText": "求租详情"
			}
        }]
    },
	{ // 聊天
		"root": "chatPage",
        "pages": [
		{
		    "path": "chat/chat",
		    "style": {
				"navigationBarTitleText": "",
				"navigationStyle": "default",
				"app-plus": {
          "bounce": "none", //关闭窗口回弹效果
          "titleNView": {
              "buttons": [ //原生标题栏按钮配置,
                  {
											"type":"none",
											"text":"举报",
											"float":"right",
											"fontSize": "24rpx"
                  }
              ]
          }
				},
				"h5": {
          "titleNView": {
              "buttons": [ //原生标题栏按钮配置,
                  {
											"type":"none",
											"text":"举报",
											"float":"right",
											"fontSize": "24rpx"
                  }
              ]
          }
      }
			}
		},
		{
			"path": "chat/add_statement",
			"style": {
				"navigationBarTitleText": "添加常用语",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/edit_statement",
			"style": {
				"navigationBarTitleText": "编辑常用语",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/statement_list",
			"style": {
				"navigationBarTitleText": "编辑快捷回复",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/friend_info",
			"style": {
				"navigationBarTitleText": "更多",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/more_info",
			"style": {
				"navigationBarTitleText": "更多",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/auto_reply",
			"style": {
				"navigationBarTitleText": "设置自定回复",
				"navigationStyle": "default"
			}
		},
		{
			"path": "chat/invited",
			"style": {
				"navigationBarTitleText": "邀请码",
				"navigationStyle": "default"
			}
		}]
	},
	{ //线上售楼处
		"root": "online",
		"pages": [{
			"path": "list",
			"style": {
				"navigationBarTitleText": "线上选房"
			}
		},{
			"path": "detail",
			"style": {
				"navigationBarTitleText": "",
				"h5": {
					"titleNView": false
				},
				"app-plus": {
					"titleNView": false
				}
				// "usingComponents": {
				// 	"subscribe": "plugin-private://wx2b03c6e691cd7370/components/subscribe/subscribe"
				// }
			}
		},{
			"path": "house_detail",
			"style": {
				"navigationBarTitleText":"房源详情"
			}
		},
		{
			"path": "loudong",
			"style": {
				"navigationBarTitleText": "楼栋详情"
			}
		},
		{
			"path": "house_status",
			"style": {
				"navigationBarTitleText": "楼栋房源信息"
			}
		},
		{
			"path": "house_info",
			"style": {
				"navigationBarTitleText": "楼栋房源详情"
			}
		},
		{
			"path": "adviser",
			"style": {
				"navigationBarTitleText": "",
				"h5": {
					"titleNView": false
				},
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "choose_adviser",
			"style": {
				"navigationBarTitleText": "选择置业顾问"
			}
		},
		{
			"path": "confirm_order",
			"style": {
				"navigationBarTitleText": "确认订单"
			}
		},
		{
			"path": "pay",
			"style": {
				"navigationBarTitleText": "定金支付"
			}
		},
		{
			"path": "choose",
			"style": {
				"navigationBarTitleText": "选房大厅",
				"h5": {
					"titleNView": {
						"buttons": [{
							"fontSrc": "/static/font/iconfont.ttf",
							"text": "\ue86f",
							"fontWeight": "bold",
							"fontSize": "18px",
							"width": "30px"
						}]
					}
				},
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"fontSrc": "/static/font/iconfont.ttf",
							"text": "\ue86f",
							"fontWeight": "bold",
							"fontSize": "18px",
							"width": "30px"
						}]
					}
				}
			}
		},
		{
			"path": "notice",
			"style": {
				"navigationBarTitleText": "购房须知"
			}
		},
		{
			"path": "loupan_info",
			"style": {
				"navigationBarTitleText": "楼盘简介"
			}
		},{
			"path": "sign_up",
			"style": {
				"navigationBarTitleText": "在线预约"
			}
		},{
			"path": "my",
			"style": {
				"navigationBarTitleText": "个人中心",
				"h5": {
					"titleNView": {
						"backgroundColor": "#f7f7f7",
						"type": "transparent"
					}
				},
				"app-plus": {
					"titleNView": {
						"backgroundColor": "#f7f7f7",
						"type": "transparent"
					}
				}
			}
		},{
			"path": "order_list",
			"style": {
				"navigationBarTitleText": "我的订单"
			}
		},{
			"path": "order_detail",
			"style": {
				"navigationBarTitleText": "订单详情"
			}
		},{
			"path": "order_status",
			"style": {
				"navigationBarTitleText": "订单状态"
			}
		},{
			"path": "user_info",
			"style": {
				"navigationBarTitleText": "购房信息"
			}
		},{
			"path": "concern",
			"style": {
				"navigationBarTitleText": "我的关注"
			}
		},{
			"path": "liveList",
			"style": {
				"navigationBarTitleText": "直播列表"
			}
		},
		{
			"path": "videos",
			"style": {
				"navigationBarTitleText": "回放列表"
			}
		},{
			"path": "next",
			"style": {
				"navigationBarTitleText": "",
				"usingComponents": {
					// #ifdef MP-WEIXIN
					"subscribe": "plugin-private://wx2b03c6e691cd7370/components/subscribe/subscribe"
					// #endif
				}
			}
		},
		{
			"path": "sale_list",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "sale_detail",
			"style": {
				"navigationBarTitleText": ""
			}
		}
		],
		"plugins": {
			"live-player-plugin": {
				"version": "1.1.3",
				"provider": "wx2b03c6e691cd7370"
			}
		}
	},
	{ //置业顾问售房
		"root": "adviser",
		"pages": [{
			"path": "house_list",
			"style": {
				"navigationBarTitleText": "选择房源"
			}
		},{
			"path": "manage_house",
			"style": {
				"navigationBarTitleText": "添加房源"
			}
		},{
			"path": "create_order",
			"style": {
				"navigationBarTitleText": "创建订单"
			}
		},{
			"path": "order_list",
			"style": {
				"navigationBarTitleText": "客户订单"
			}
		},{
			"path": "client_list",
			"style": {
				"navigationBarTitleText": "客户列表"
			}
		},{
			"path": "client_detail",
			"style": {
				"navigationBarTitleText": "客户详情"
			}
		},{
			"path": "friend_list",
			"style": {
				"navigationBarTitleText": "我的同事"
			}
		},{
			"path": "order_detail",
			"style": {
				"navigationBarTitleText": "订单详情",
				"h5": {
					"titleNView":{
						"backgroundColor": "#f7f7f7",
						"type": "transparent"
					}
				}
			}
		},{
			"path": "order_status",
			"style": {
				"navigationBarTitleText": "订单状态"
			}
		},{
			"path": "verification_order",
			"style": {
				"navigationBarTitleText": "订单核销",
				"h5": {
					"titleNView":{
						"backgroundColor": "#f7f7f7",
						"type": "transparent"
					}
				}
			}
		}]
	},
	{ //门店
		"root": "shops",
		"pages": [{
				"path": "list",
				"style": {
					"navigationBarTitleText": "门店列表"
				}
			},
			{
				"path": "detail",
				"style": {
					"navigationBarTitleText": "门店详情"
				}
			}, {
				"path": "addShop",
				"style": {
					"navigationBarTitleText": "门店申请"
				}
			}, {
				"path": "editShop",
				"style": {
					"navigationBarTitleText": "门店编辑"
				}
			},
			{
				"path": "agent_list",
				"style": {
					"navigationBarTitleText": "经纪人列表"
				}
			},
			{
				"path": "addCompany",
				"style": {
					"navigationBarTitleText": "公司申请"
				}
			},
			{
				"path": "editCompany",
				"style": {
					"navigationBarTitleText": "公司修改"
				}
			},
			{
				"path": "myCompany",
				"style": {
					"navigationBarTitleText": "公司详情"
				}
			}, {
				"path": "agent_info",
				"style": {
					"navigationBarTitleText": "名片设置"
				}
			}, {
				"path": "be_invited",
				"style": {
					"navigationBarTitleText": "邀请好友"
				}
			}, {
				"path": "search",
				"style": {
					"navigationBarTitleText": "搜索"
				}
			}

		]
	},
	{
		"root": "vr",
		"pages": [
			{
				"path": "list",
				"style": {
					"navigationBarTitleText": "VR列表"
				}
			},
			{
				"path": "prevideo",
				
				"style": {
					"navigationBarTitleText": "视频",
					"background":"#000000",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "prevideotwo",
				
				"style": {
					"navigationBarTitleText": "",
					"background":"#000000",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "pre_comm_video",
				
				"style": {
					"navigationBarTitleText": "视频",
					"background":"#000000",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "detail",
				"style": {
					"navigationBarTitleText": "",
					"h5":{
						"titleNView":false
					}
				}
			},
			{
				"path": "detailto",
				"style": {
					"navigationBarTitleText": "",
					"h5":{
						"titleNView":false
					}
				}
			},
			{
				"path": "videos",
				"style": {
					"navigationBarTitleText": "楼盘视频",
					"backgroundColor": "#f7f7f7"
				}
			},
			{
			"path": "preview_video",
				"style": {
					"navigationBarTitleText": "视频预览",
					"h5": {
						"titleNView": {
							"backgroundColor": "#f7f7f7",
							"type": "transparent"
						}
					},
					"app-plus": {
						"titleNView": {
							"backgroundColor": "#f7f7f7",
							"type": "transparent"
						}
					}
				}
				
			}
		]
	},
	{
		"root": "lucky",
		"pages": [
			{
				"path": "wheel",
				"style": {
					"navigationBarTitleText": "大转盘抽奖",
					"h5": {
						"titleNView": false
					}
				}
			},
			{
				"path": "grid",
				"style": {
					"navigationBarTitleText": "九宫格抽奖",
					"h5": {
						"titleNView": false
					}
				}
			},
			{
				"path": "my_prizes",
				"style": {
					"navigationBarTitleText": "我的抽奖",
					"h5": {
						"titleNView": false
					}
				}
			},
			{
				"path": "prize_detail",
				"style": {
					"navigationBarTitleText": "兑奖管理"
				}
			},
			{
				"path": "grant",
				"style": {
					"navigationBarTitleText": "管理员授权"
				}
			}
		]
	},
	{ //家装
		"root": "home",
        "pages": [{
            "path": "index/index",
            "style": {
				"navigationBarTitleText": "家装平台",
				"h5" :{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"searchInput":{
							"align":"left",
							"borderRadius":"2px",
							"placeholder":"找装修公司",
							"placeholderColor":"#666666"
						}
					}
				}
			}
        },{
            "path": "find_company/list",
            "style": {
				"navigationBarTitleText": "找公司",
				"h5" :{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"searchInput":{
							"align":"left",
							"borderRadius":"2px",
							"placeholder":"找装修公司",
							"placeholderColor":"#666666"
						}
					}
				},
				"enablePullDownRefresh": true
			}
        },{
            "path": "find_company/detail",
            "style": {
				"navigationBarTitleText": "店铺详情",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "case/list",
            "style": {
				"navigationBarTitleText": "案例列表",
				"navigationStyle": "default",
				"enablePullDownRefresh": true
			}
        },{
            "path": "case/detail",
            "style": {
				"navigationBarTitleText": "案例详情",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "yuyue/index",
            "style": {
				"navigationBarTitleText": "预约设计",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "quote/index",
            "style": {
				"navigationBarTitleText": "算报价",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "area/list",
            "style": {
				"navigationBarTitleText": "找我家",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent",
						"searchInput":{
							"align":"left",
							"borderRadius":"2px",
							"placeholder":"你家在哪里？",
							"placeholderColor":"#666666"
						}
					}
				}
			}
        },{
            "path": "area/detail",
            "style": {
				"navigationBarTitleText": "",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "user/center",
            "style": {
				"navigationBarTitleText": "用户中心",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
            "path": "user/setting_shop",
            "style": {
				"navigationBarTitleText": "店铺设置",
				"h5":{
					"titleNView":{
						"backgroundColor":"#f7f7f7",
						"type":"transparent"
					}
				}
			}
        },{
			"path": "user/manage_case",
			"style": {
				"navigationBarTitleText": "案例管理"
			}
		},{
            "path": "user/upgrade",
            "style": {
				"navigationBarTitleText": "会员升级"
			}
        },{
            "path": "user/client_list",
            "style": {
				"navigationBarTitleText": "客户列表"
			}
		},{
            "path": "user/release",
            "style": {
				"navigationBarTitleText": "发布信息"
			}
        }]
	},
	{
		"root":"user",
		"pages":[
			{
				"path":"collect",
				"style": {
					"navigationBarTitleText": "信息收藏",
					"navigationStyle": "default"
				}
			},
			// #ifdef H5
			{
				"path":"sendToDouyin",
				"style": {
					"navigationBarTitleText": "发布到抖音",
					"navigationStyle": "default"
				}
			},
			// #endif
			{
				"path": "task_center",
				"style": {
					"navigationBarTitleText": "任务中心",
					"navigationStyle": "default"
				}
			},
			{
				"path": "manage_info",
				"style": {
					"navigationBarTitleText": "信息管理",
						"enablePullDownRefresh": true,
				"navigationStyle": "custom"
				}
			},
			{
				"path": "audit/audit",
				"style": {
					"navigationBarTitleText": "快速审核",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "audit/history",
				"style": {
					"navigationBarTitleText": "发布历史",
					"navigationStyle": "custom"
				}
			},
			{
				"path" :"member_upgrade",
				"style": {
					"navigationBarTitleText": "会员升级",
					"navigationStyle": "default"
				}
			},{
				"path" :"my_vip",
				"style": {
					"navigationBarTitleText": "我的会员",
					"navigationStyle": "default"
				}
			},{
				"path": "exchange",
				"style": {
					"navigationBarTitleText": "积分兑换",
					"navigationStyle": "default"
				}
			},
			{
				"path": "recharge",
				"style": {
					"navigationBarTitleText": "充值中心",
					"navigationStyle": "default",
					"backgroundColor": "#ffffff"
				}
			},
			{
				"path": "meal_detail",
				"style": {
					"navigationBarTitleText": "购买记录",
					"navigationStyle": "default",
					"backgroundColor": "#ffffff"
				}
			},
			{
				"path": "add/add_desc",
				"style": {
					"navigationBarTitleText": "核心卖点"
				}
			},
			{
				"path": "add/template_list",
				"style": {
					"navigationBarTitleText": "房源模板"
				}
			},
			{
				"path": "add/add_template",
				"style": {
					"navigationBarTitleText": "添加房源模板"
				}
			},
			{
				"path": "add/edit_template",
				"style": {
					"navigationBarTitleText": "编辑房源模板"
				}
			},
			{
				"path": "my/protocol",
				"style": {
					"navigationBarTitleText": "用户协议",
					"h5":{
						"titleNView":false
					}
				}
			},
			{
				"path": "my/reg_infos",
				"style": {
					"navigationBarTitleText": "隐私政策",
					"h5":{
						"titleNView":false
					}
				}
			},
			{
				"path": "my/about",
				"style": {
					"navigationBarTitleText": "关于我们"
				}
			},
			{
				"path": "my/announcement",
				"style": {
					"navigationBarTitleText": "公告详情",
					"navigationStyle": "default"
				}
			},
			{
				"path": "my/notice",
				"style": {
					"navigationBarTitleText": "站内信",
					"navigationStyle": "default"
				}
			},
			{
				"path": "my/notice_detail",
				"style": {
					"navigationBarTitleText": ""
				}
			},
			{
				"path": "my/edit_info",
				"style": {
					"navigationBarTitleText": "信息修改",
					"navigationStyle": "default"
				}
			},
			{
				"path": "my/pay_success",
				"style": {
					"navigationBarTitleText": "支付成功",
					"navigationStyle": "default"
				}
			},
			{
				"path": "my/invited",
				"style": {
					"navigationBarTitleText": "邀请码",
					"navigationStyle": "default"
				}
			},
			{
				"path": "my/genjin",
				"style": {
					"navigationBarTitleText": "跟进详情",
					"navigationStyle": "default"
				}
			},
			{
				"path": "my/browselog",
				"style": {
					"navigationBarTitleText": "访问详情",
					"navigationStyle": "default"
				}
			},{
				"path": "my/operation",
				"style": {
					"navigationBarTitleText": "房源操作详情",
					"navigationStyle": "default"
				}
			},
			{
				"path": "my/tuiguang",
				"style": {
					"navigationBarTitleText": "",
					"backgroundColor": "#ffffff",
					"navigationStyle": "default"
				}
			},
			{
				"path": "my/tuiguang_detail",
				"style": {
					"navigationBarTitleText": "",
					"backgroundColor": "#ffffff",
					"navigationStyle": "default"
				}
			},
			{
				"path": "my/tg_success",
				"style": {
					"navigationBarTitleText": "",
					"h5":{
						"titleNView":{
							"backgroundColor":"#F7F8FA"
						}
					},
					"app-plus":{
						"titleNView":{
							"backgroundColor":"#F7F8FA"
						}
					}
				}
			},
			 {
				"path": "community/add_post",
				"style": {
					"navigationBarTitleText": "发布",
					"navigationStyle": "default"
				}
			},
			{
				"path": "consultant/add",
				"style": {
					"navigationBarTitleText": "申请为置业顾问",
					"navigationStyle": "default"
				}
			},
			{
				"path": "consultant/seeme",
				"style": {
					"navigationBarTitleText": "谁看过我",
					"navigationStyle": "default"
				}
			},
			{
				"path": "consultant/addpost",
				"style": {
					"navigationBarTitleText": "发表分享",
					"navigationStyle": "default"
				}
			},{
				"path" : "sub_form/sub_form",
				"style": {
					"navigationBarTitleText": "团购报名",
					"backgroundColor": "#f7f7f7",
					"navigationStyle": "default"
				}
			},
			{
				"path": "house_price/publication",
				"style": {
					"navigationBarTitleText": "发布"
				}
			},
			{
				"path": "recharge_detail",
				"style": {
					"navigationBarTitleText": "充值明细",
					"navigationStyle": "default"
				}
			},
			{
				"path": "bind_phone/bind_phone",
				"style": {
					"navigationBarTitleText": "绑定手机号",
					"navigationStyle": "default"
				}
			},
			{
				"path": "inform/inform",
				"style": {
					"navigationBarTitleText": "举报中心",
					"navigationStyle": "default"
				}
			},
			{
				"path": "reg/reg",
				"style": {
					"navigationBarTitleText": "注册用户",
					"navigationStyle": "default"
				}
			},
			{
				"path": "login/login",
				"style": {
					"navigationBarTitleText": "会员登录",
					"navigationStyle": "default",
					"backgroundColor": "#ffffff"
				}
			},
			{
				"path": "login/reset_pwd",
				"style": {
					"navigationBarTitleText": "重置密码",
					"navigationStyle": "default"
				}
			},
			{
				"path": "order_list",
				"style": {
					"navigationBarTitleText": "订单卡券",
					"navigationStyle": "default"
				}
			},
			 {
				"path": "withdraw_funds",
				"style": {
					"navigationBarTitleText": "申请提现",
					"navigationStyle": "default"
				}
			},
			 {
				"path": "history",
				"style": {
					"navigationBarTitleText": "历史浏览",
					"navigationStyle": "default"
				}
			},
			{
				"path": "user_info",
				"style": {
					"navigationBarTitleText": "会员资料",
					"navigationStyle": "default"
				}
			},
			{
				"path": "agent_info",
				"style": {
					"navigationBarTitleText": "经纪人资料",
					"navigationStyle": "default"
				}
			},
			{
				"path": "agent_activity",
				"style": {
					"navigationBarTitleText": "活跃度",
					"navigationStyle": "default"
				}
			},
			{
				"path": "manage_photos",
				"style": {
					"navigationBarTitleText": "小区专家",
					"navigationStyle": "default"
				}
			},
			{
				"path": "adviser_info",
				"style": {
					"navigationBarTitleText": "名片设置",
					"navigationStyle": "default"
				}
			},
			{
				"path": "bind_builds",
				"style": {
					"navigationBarTitleText": "更换项目",
					"navigationStyle": "default"
				}
			},
			{
				"path": "adviser_rights",
				"style": {
					"navigationBarTitleText": "会员特权",
					"navigationStyle": "default"
				}
			},
			{
				"path": "adviser_activity",
				"style": {
					"navigationBarTitleText": "活跃度",
					"navigationStyle": "default"
				}
			},
			{
				"path": "adviser_builds",
				"style": {
					"navigationBarTitleText": "我的项目",
					"navigationStyle": "default"
				}
			},
			{
				"path": "adviser_upgrade",
				"style": {
					"navigationBarTitleText": "开通特权",
					"navigationStyle": "default"
				}
			},
			{
				"path": "adviser_pay_logs",
				"style": {
					"navigationBarTitleText": "购买记录",
					"navigationStyle": "default"
				}
			},
			{
				"path": "add_community",
				"style": {
					"navigationBarTitleText": "添加小区",
					"navigationStyle": "default"
				}
			},
			{
				"path": "search_areas",
				"style": {
					"navigationBarTitleText": "查找小区",
					"navigationStyle": "default"
				}
			},
			{
				"path": "upload_info_img",
				"style": {
					"navigationBarTitleText": "添加照片"
				}
			},
			{
				"path": "report",
				"style": {
					"navigationBarTitleText": "报备客户",
					"navigationStyle": "default"
				}
			},
			{
				"path": "agreement",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "default"
				}
			},
			{
				"path": "feedback",
				"style": {
					"navigationBarTitleText": "问题反馈",
					"navigationStyle": "default"
				}
			},
			{
				"path": "help_list",
				"style": {
					"navigationBarTitleText": "帮助中心",
					"navigationStyle": "default"
				}
			},
			{
				"path": "help_detail",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "default"
				}
			},
			{
				"path": "check_login",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "default",
					"h5":{
						"titleNView": false
					}
				}
			},
			{
				"path": "community_auth",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "default"
				}
			},
      {
        "path": "add_verification",
        "style": {
          "navigationBarTitleText": "房源统一核验"
        }
      },
	  {
        "path": "add_tuiguang",
        "style": {
          "navigationBarTitleText": "房源权益描述"
        }
      },
      {
        "path": "saihongbao",
        "style": {
          "navigationStyle": "custom"
        }
      }
		]
	},
	{
		"root": "contrast",
		"pages": [
			{
				"path": "house_list",
				"style": {
					"navigationBarTitleText":"户型对比",
					"navigationStyle": "default"
				}
			},
			{
				"path": "house_detail",
				"style": {
					"navigationBarTitleText":"对比详情",
					"navigationStyle": "default"
				}
			},
			{
				"path": "search_house",
				"style": {
					"navigationBarTitleText":"所有楼盘",
					"navigationStyle": "default"
				}
			},
			{
				"path": "replace_house_list",
				"style": {
					"navigationBarTitleText":"所有楼盘",
					"navigationStyle": "default"
				}
			},
			{
				"path": "info_list",
				"style": {
					"navigationBarTitleText":"房源对比",
					"navigationStyle": "default"
				}
			},
			{
				"path": "info_detail",
				"style": {
					"navigationBarTitleText":"对比详情",
					"navigationStyle": "default"
				}
			},
			{
				"path": "replace_info_list",
				"style": {
					"navigationBarTitleText":"房源收藏",
					"navigationStyle": "default"
				}
			},
      {
        "path": "commercial_list",
        "style": {
					"navigationBarTitleText":"房源对比",
					"navigationStyle": "default"
				}
      },
      {
				"path": "commercial_detail",
				"style": {
					"navigationBarTitleText":"对比详情",
					"navigationStyle": "default"
				}
			}
		]
	},
	{
		"root": "gongye",
		"pages": [
			{
				"path": "index",
				"style": {
					"navigationBarTitleText": "产业园区",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "detail",
				"style": {
					"backgroundColor": "#f7f7f7",
					"h5":{
						"titleNView":false
					},
					"app-plus":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent",
							"buttons":[
								{
									"type": "share"
								}
							]
						}
					}
				}
			}
		]
	},
	{
		"root": "statistics",
		"pages": [
			{
				"path": "tudi_data",
				"style": {
					"navigationBarTitleText": "土地数据",
					"navigationStyle": "default"
				}
			},
			{
				"path": "yushou_data",
				"style": {
					"navigationBarTitleText": "预售数据",
					"navigationStyle": "default"
				}
			},
			{
				"path": "map",
				"style": {
					"navigationBarTitleText": "地图",
					"navigationStyle": "default"
				}
			},
			{
				"path": "tudi_statistics",
				"style": {
					"navigationBarTitleText": "土地统计",
					"navigationStyle": "default"
				}
			},
			{
				"path": "value_list",
				"style": {
					"navigationBarTitleText": "价值榜",
					"h5":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent"
						}
					}
				}
			},
			{
				"path": "search_list",
				"style": {
					"navigationBarTitleText": "榜单",
					"navigationStyle": "default",
					"h5": {
						"titleNView": {
							"backgroundColor": "#f7f7f7",
							"type": "transparent",
							"buttons": [
								{
									"type": "share"
								}
							]
						}
					},
					"app-plus": {
						"titleNView": {
							"backgroundColor": "#f7f7f7",
							"type": "transparent",
							"buttons": [
								{
									"type": "share"
								}
							]
						}
					}
				}
			},
			{
				"path": "yushou_statistics",
				"style": {
					"navigationBarTitleText": "预售统计",
					"navigationStyle": "default"
				}
			}
		]
	},
		{
			"root": "findHouse",
			"pages": [
				{
					"path": "ai_house_search",
					"style": {
						"navigationBarTitleText": "智能找房",
						"navigationStyle": "default"
					}
				},
				{
					"path": "map",
					"style": {
						"navigationBarTitleText": "选取经纬度",
						"navigationStyle": "default"
					}
				},
				{
					"path": "find_house",
					"style": {
						"navigationBarTitleText": "委托找房",
						"navigationStyle": "default"
					}
				}, {
					"path": "search_area",
					"style": {
						"navigationBarTitleText": "订阅区域",
						"navigationStyle": "default"
					}
				}, {
					"path": "search_community",
					"style": {
						"navigationBarTitleText": "搜索小区",
						"navigationStyle": "default"
					}
				}
				, {
					"path": "community",
					"style": {
						"navigationBarTitleText": "订阅小区",
						"navigationStyle": "default"
					}
				}
				
			
			]
		},
	{
		"root": "propertyData",
		"pages": [
			{
				"path": "analyse",
				"style": {
					"navigationBarTitleText": "楼盘价值分析报告",
					"navigationStyle": "default",
					"h5":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent"
						}
					}
				}
			},
			{
				"path": "property_data",
				"style": {
				// 	"navigationBarTitleText": "房产数据",
				// 	"navigationStyle": "default",
				// 	"backgroundColor": "#ffffff"
				}
			},{
				"path" : "price_trend/price_trend",
				"style" : {
					"navigationBarTitleText": "",
					"navigationStyle": "default"
				}
			},{
				"path": "map/map",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "custom",
						"app-plus": {
							"bounce": "none"
						}
				}
			},{
				"path": "map/analysis",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "custom",
						"app-plus": {
							"bounce": "none"
						}
				}
			},{
				"path" : "correct",
				"style" : {
					"navigationBarTitleText": "",
					"navigationStyle": "default"
				}
			},{
				"path" : "customer/index",
				"style" : {
					"navigationBarTitleText": "客户认领",
					"navigationStyle": "custom"
				}
			}
			,{
				"path" : "customer/followup",
				"style" : {
					"navigationBarTitleText": "跟进",
					"navigationStyle": "custom"
				}
			}
			// #ifndef MP 
			,
			{
				"path" : "follow/index",
				"style" : {
					"navigationBarTitleText": "全部客户",
					"navigationStyle": "default"
				}
			},{
				"path" : "follow/status",
				"style" : {
					"navigationBarTitleText": "状态跟进",
					"navigationStyle": "default"
				}
			},{
				"path" : "follow/info",
				"style" : {
					"navigationBarTitleText": "跟进记录",
					"navigationStyle": "custom"
				}
			},
			{
				"path" : "follow/list",
				"style" : {
					"navigationBarTitleText": "门店服务",
					"navigationStyle": "custom"
				}
			}
			// #endif
			,{
				"path" : "beian/list",
				"style": {
					"navigationBarTitleText": "经纪机构备案信息查询系统",
					"navigationStyle": "default",
					"h5":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent"
						}
					}
				}
			}
		]
	},
	{
		"root": "ask",
		"pages": [
			{
				"path": "list",
				"style": {
          "enablePullDownRefresh": true
				}
			},
			{
				"path": "detail",
				"style": {
				}
			},
      {
				"path": "answer",
				"style": {
					"navigationBarTitleText": "回答问题",
					"navigationStyle": "default",
          "h5": {
            "titleNView": false
          }
				}
			}
		]
	},
	{
		"root": "topic",
		"pages": [
			{
				"path": "detail",
				"style": {
					"navigationBarTitleText": "专题详情",
					"navigationStyle": "default"
				}	
			},
			{
				"path": "blindBox",
				"style": {
					"navigationBarTitleText": "盲盒",
					"navigationStyle": "custom"
				}	
			},
			{
				"path": "my_prize",
				"style": {
					"navigationBarTitleText": "我的奖品",
					"navigationStyle": "custom"
				}	
			},
			{
				"path": "buildList",
				"style": {
					"navigationBarTitleText": "热门楼盘",
					"navigationStyle": "custom"
				}	
			},
			{
				"path": "build_detail",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "custom"
				}	
			},
			
			{
				"path": "preview_video",
				"style": {
					"navigationBarTitleText": "视频预览",
					"navigationStyle": "custom"
				}	
			},
			{
				"path": "grant",
				"style": {
					"navigationBarTitleText": "管理员授权",
					"navigationStyle": "custom"
				}	
			},
			
			{
				"path": "pinpai_info",
				"style": {
					"navigationBarTitleText": "品牌介绍",
					"navigationStyle": "custom"
				}	
			},
			{
				"path": "prize_detail",
				"style": {
					"navigationBarTitleText": "兑奖管理",
					"navigationStyle": "custom"
				}	
			},
			{
				"path": "write_off_award",
				"style": {
					"navigationBarTitleText": "核销奖品",
					"navigationStyle": "custom"
				}	
			},
			{
				"path": "icon",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "default"
				}
			},
			{
				"path": "infolist",
				"style": {
					"navigationBarTitleText": "条目列表",
					"navigationStyle": "default"
				}
			},
			{
				"path": "to_login",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "topicList",
				"style": {
					"navigationBarTitleText": "专题列表",
					"navigationStyle": "default"
				}
			},
			{
				"path": "info_detail",
				"style": {
					"navigationBarTitleText": "条目详情",
					"navigationStyle": "default"
				}
			},
			{
				"path": "find_house",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "jianbao",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "default",
					"backgroundColor":"#ffffff",
					"h5":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"default"
						}
					},
					"app-plus":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"default"
						}
					}
				}
			},
			{
				"path": "tools",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "custom"
				}
			}
		]
	},
	{
		"root": "flop",
		"pages": [
			{
				"path": "index",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "custom"
				}	
			},
			{
				"path": "my_prize",
				"style": {
					"navigationBarTitleText": "我的奖品",
					"navigationStyle": "custom"
				}	
			},
			// {
			// 	"path": "grant",
			// 	"style": {
			// 		"navigationBarTitleText": "管理员授权",
			// 		"navigationStyle": "default"
			// 	}	
			// },
			{
				"path": "build_detail",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "custom"
				}	
			},
			// apply_write_off
			{
				"path": "apply_write_off",
				"style": {
					"navigationBarTitleText": "管理员授权",
					"navigationStyle": "custom"
				}	
			},
			{
				"path": "write_off_award",
				"style": {
					"navigationBarTitleText": "核销奖品",
					"navigationStyle": "custom"
				}	
			}
		]
	},
	{
		"root": "customer",
		"pages": [
			{
				"path": "list",
				"style":{
					"navigationBarTitleText": "我的客户",
					"navigationStyle": "default",
					"backgroundColor":"#ffffff"
				}
			},
			{
				"path": "detail",
				"style":{
					"navigationBarTitleText": "客户详情",
					"navigationStyle": "default",
					"backgroundColor":"#ffffff"
				}
			},
			{
				"path": "add",
				"style":{
					"navigationBarTitleText": "新增客户",
					"navigationStyle": "default",
					"backgroundColor":"#ffffff"
				}
			},
			{
				"path": "assistant",
				"style":{
					"navigationBarTitleText": "获客助手",
					"navigationStyle": "default",
					"backgroundColor":"#ffffff"
				}
			}
		]
	},
	{
		"root": "decommend",
		"pages": [
			{
				"path": "list",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "default",
					"backgroundColor":"#ffffff",
					"h5": {
						"titleNView": false
					}
				}
			}
		]
	},
	{
		"root": "school",
		"pages":[
			{
				"path": "list",
				"style": {
					"navigationBarTitleText": "查学校",
					"navigationStyle": "default",
					"backgroundColor":"#ffffff",
					"h5": {
						"titleNView": false
					}
				}	
			},
			{
				"path": "map",
				"style": {
					"navigationBarTitleText": "地图找学校",
					"navigationStyle": "custom"
				}	
			},
			{
				"path": "detail",
				"style": {
					// "navigationBarTitleText": "学校详情",
					"h5":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent",
							"buttons": [
								{
									"type": "home"
								}
							]
						}
					},
					"app-plus":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent",
							"buttons": [
								{
									"type": "home"
								}
							]
						},
						"bounce": "none"
					}
				}	
			},
			{
				"path": "teach_range",
				"style": {
					"navigationBarTitleText": "施教范围",
					"h5":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent"
						}
					},
					"app-plus":{
						"titleNView":{
							"backgroundColor":"#f7f7f7",
							"type":"transparent"
						},
						"bounce": "none"
					}
				}	
			},
			{
				"path": "introduce",
				"style": {
					"navigationBarTitleText": "学校简介",
					"navigationStyle": "default",
					"h5": {
						"titleNView": false
					}
				}	
			},
			{
				"path": "regulations_detail",
				"style": {
					"navigationBarTitleText": "招生简章",
					"navigationStyle": "default",
					"h5": {
						"titleNView": false
					}
				}	
			},
			{
				"path": "regulations",
				"style": {
					"navigationBarTitleText": "招生简章列表",
					"navigationStyle": "default",
					"h5": {
						"titleNView": false
					}
				}	
			}
		    
            ]
	},
  {  //商业地产
    "root": "commercial",
    "pages": [
      {
        "path": "sale/sale",
        "style": {
          "navigationBarTitleText": "商业地产出售",
				  "enablePullDownRefresh": true,
				  "navigationStyle": "custom"
				  // "h5": {
				  // 	"titleNView": {
				  // 		"buttons": [{
				  // 			"text": "发布",
				  // 			"fontSize": "15px",
				  // 			"width": "40px"
				  // 		}]
				  // 	}
				  // },
				  // "app-plus": {
				  // 	"titleNView": {
				  // 		"buttons": [{
				  // 			"text": "发布",
				  // 			"fontSize": "15px",
				  // 			"width": "40px"
				  // 		}]
				  // 	}
				  // }
        }
      },
      {
        "path": "sale/detail",
        "style": {
          "navigationBarTitleText": "",
          "h5":{
            "titleNView":false
          },
          "app-plus":{
            "titleNView":{
              "backgroundColor":"#f7f7f7",
              "type":"transparent",
              "buttons": [
                {
                  "type": "share"
                }
              ]
            }
          }
        }
      },
      {
        "path": "rent/rent",
        "style": {
          "navigationBarTitleText": "商业地产出租",
				  "enablePullDownRefresh": true,
				  "navigationStyle": "custom"
				  // "h5": {
				  // 	"titleNView": {
				  // 		"buttons": [{
				  // 			"text": "发布",
				  // 			"fontSize": "15px",
				  // 			"width": "40px"
				  // 		}]
				  // 	}
				  // },
				  // "app-plus": {
				  // 	"titleNView": {
				  // 		"buttons": [{
				  // 			"text": "发布",
				  // 			"fontSize": "15px",
				  // 			"width": "40px"
				  // 		}]
				  // 	}
				  // }
        }
      },
      {
        "path": "rent/detail",
        "style": {
          "navigationBarTitleText": "",
          "h5":{
            "titleNView":false
          },
          "app-plus":{
            "titleNView":{
              "backgroundColor":"#f7f7f7",
              "type":"transparent",
              "buttons": [
                {
                  "type": "share"
                }
              ]
            }
          }
        }
      },
      {
        "path": "transfer/transfer",
        "style": {
          "navigationBarTitleText": "生意转让",
				  "enablePullDownRefresh": true,
				  "navigationStyle": "custom"
				  // "h5": {
				  // 	"titleNView": {
				  // 		"buttons": [{
				  // 			"text": "发布",
				  // 			"fontSize": "15px",
				  // 			"width": "40px"
				  // 		}]
				  // 	}
				  // },
				  // "app-plus": {
				  // 	"titleNView": {
				  // 		"buttons": [{
				  // 			"text": "发布",
				  // 			"fontSize": "15px",
				  // 			"width": "40px"
				  // 		}]
				  // 	}
				  // }
        }
      },
      {
        "path": "transfer/detail",
        "style": {
          "navigationBarTitleText": "",
          "h5":{
            "titleNView":false
          },
          "app-plus":{
            "titleNView":{
              "backgroundColor":"#f7f7f7",
              "type":"transparent",
              "buttons": [
                {
                  "type": "share"
                }
              ]
            }
          }
        }
      },
      {
        "path": "commercial",
        "style": {
          "navigationBarTitleText": "商业地产",
          "backgroundColor": "#f7f7f7",
          "enablePullDownRefresh": true,
          "h5":{
            "titleNView": "false"
          }
        }
      },
      {
        "path": "add",
        "style": {
          "navigationBarTitleText": "发布信息"
        }
      },
      {
        "path": "upload",
        "style": {
          "navigationBarTitleText": "添加照片"
        }
      },
      {
				"path": "manage_info",
				"style": {
					"navigationBarTitleText": "信息管理",
						"enablePullDownRefresh": true,
				"navigationStyle": "custom"
				}
			},
			{
				"path": "audit/audit",
				"style": {
					"navigationBarTitleText": "快速审核",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "audit/history",
				"style": {
					"navigationBarTitleText": "发布历史",
					"navigationStyle": "custom"
				}
			},
      {
				"path": "edit_info",
				"style": {
					"navigationBarTitleText": "信息修改",
					"navigationStyle": "default"
				}
			},
      {
        "path": "audit/audit",
        "style": {
					"navigationBarTitleText": "快速审核",
					"navigationStyle": "custom"
				}
      }
    ]
  }
  ,{
    //房展会
    "root": "exhibition",
    "pages": [
      {
        "path": "index",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": ""
        }
      },
      {
        "path": "introduce",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": ""
        }
      },
      {
        "path": "dynamic",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": ""
        }
      },
      {
        "path": "brand",
        "style": {
          "navigationStyle": "custom",
          "navigationBarTitleText": ""
        }
      },
      {
        "path": "detail",
        "style": {
          "navigationStyle": "custom",
          "h5":{
              "titleNView":{
                "backgroundColor":"#f7f7f7",
                "type":"transparent"
            }
          },
          "navigationBarTitleText": "楼盘详情"
        }
      }
    ]
  }
  ,{
    //红包记录
    "root": "redEnvelopes",
    "pages": [
      {
        "path": "index",
        "style": {
          "navigationBarTitleText": "红包",
				  "backgroundColor": "#f7f7f7",
				  "h5":{
            "titleNView": "false"
          },
          "app-plus":{
            "titleNView":{
              "backgroundColor":"#f7f7f7",
              "type":"transparent"
            }
          }
        }
      },
      {
        "path": "add",
        "style": {
          "navigationBarTitleText": "塞红包",
          "navigationBarBackgroundColor": "#f7f7f7",
				  "navigationStyle": "default",
          "h5":{
					  "titleNView": "false"
          }
        }
      },
      {
        "path": "money",
        "style": {
          "navigationBarTitleText": "拼手气红包",
          "backgroundColor": "#f7f7f7",
				  "h5":{
					  "titleNView": "false"
          },
          "app-plus":{
            "titleNView":{
              "backgroundColor":"#f7f7f7",
              "type":"transparent"
            }
          }
        }
      },
      {
        "path": "follow",
        "style": {
          "navigationBarTitleText": "关注红包",
          "navigationBarBackgroundColor": "#f7f7f7",
				  "navigationStyle": "custom"
        }
      },
      {
        "path": "list",
        "style": {
          "navigationBarTitleText": "红包大厅",
          "navigationBarBackgroundColor": "#f7f7f7",
				  "navigationStyle": "custom"
        }
      },
      {
        "path": "qrcode",
        "style": {
          "navigationBarTitleText": "红包",
          "navigationBarBackgroundColor": "#f7f7f7",
				  "navigationStyle": "custom"
        }
      }
    ]
  },
	{ // 绑定开发商 邀请绑定开发商  只有微信公众号有
		"root": "enterprise",
		"pages": [
					{
							"path": "invite",
							"style": {
							"navigationBarTitleText": "",
							"h5":{
								"titleNView": "false"
							}
						}
					}
		]
	}
],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "腾房云",
		"navigationBarBackgroundColor": "#ffffff",
		"backgroundColor": "#F8F8F8",
		"onReachBottomDistance": 100
	},
	"tabBar": {
		"color": "#666",
		"selectedColor": "#FB656A",
		"borderStyle": "white",
		"backgroundColor": "#FFFFFF",
		"list": [{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "static/tab_icon/home9.png",
				"selectedIconPath": "static/tab_icon/home_active9.png"
			},
			{
				"pagePath": "pages/index/find_house",
				"text": "找房",
				"iconPath": "static/tab_icon/findhouse9.png",
				"selectedIconPath": "static/tab_icon/findhouse_active9.png"
			},
			{
				"pagePath": "pages/add/add",
				"text": "发布",
				"iconPath": "static/tab_icon/add9.png",
				"selectedIconPath": "static/tab_icon/add_active9.png"
			},
			{
				"pagePath": "pages/index/chat_list",
				"text": "消息",
				"iconPath": "static/tab_icon/message9.png",
				"selectedIconPath": "static/tab_icon/message_active9.png"
			},
			{
				"pagePath": "pages/my/my",
				"text": "我的",
				"iconPath": "static/tab_icon/my9.png",
				"selectedIconPath": "static/tab_icon/my_active9.png"
			}
		]
	},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式（list 的索引项）
		"list": []
	}
}