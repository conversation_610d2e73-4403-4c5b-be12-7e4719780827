<template>
<view class="adviser">
	<!-- #ifndef APP-PLUS -->
	<view class="title bottom-line">
	    <text>在线联系置业顾问</text>
	</view>
	<!-- #endif -->
	<!-- #ifdef APP-PLUS -->
	<view class="title bottom-line" :style="{marginTop:top+'px'}">
	    <text>在线联系置业顾问</text>
	</view>
	<!-- #endif -->

<view class="adviser-list">
    <view class="adviser-item flex-box" v-for="adviser in adviser_list" :key="adviser.id">
        <view class="flex-box">
            <image class="head-img" :src="adviser.prelogo | imgUrl('w_120')" mode="aspectFill"></image>
            <view>
                <view class="name">{{adviser.cname}}</view>
                <view class="tel">{{adviser.tel}}</view>
            </view>
        </view>
        <view class="flex-box">
            <view @click="handleTel(adviser)" v-if ="switch_adviser_tel" class="icon-box"><my-icon type="dianhua1" color="#f65354" size="26"></my-icon></view>
            <view v-if="has_chat" @click="toChat(adviser.mid)" class="icon-box"><my-icon type="zixun" color="#f65354" size="27"></my-icon></view>
        </view>
    </view>
</view>
<view class="bottom-menu flex-box">
    <view class="bar-item" @click="navigate('/online/detail?id='+online_id)">
        <my-icon type="home" size="22"></my-icon>
        <view class="text">大厅</view>
    </view>
    <view class="bar-item" @click="navigate('/online/choose?online_id='+online_id)">
        <my-icon type="jiudian" size="22"></my-icon>
        <view class="text">选房</view>
    </view>
    <view class="bar-item active">
        <my-icon type="xiaoxi" size="22" color="#f65354"></my-icon>
        <view class="text">咨询</view>
    </view>
    <view class="bar-item" @click="navigate('/online/my?online_id='+online_id)">
        <my-icon type="shikebiao" size="22"></my-icon>
        <view class="text">订单</view>
    </view>
</view>
</view>
</template>

<script>
import {
  formatImg,
  navigateTo,
} from '../common/index.js'
import {wxShare} from '../common/mixin'
import myIcon from '../components/icon.vue'
import getChatInfo from '../common/get_chat_info'
export default {
    data() {
        return {
            adviser_list:[],
            online_id:'',
			// #ifdef APP-PLUS
			top:0,
			// #endif
        }
    },
    mixins: [wxShare],
    computed: {
        has_chat(){
            return this.$store.state.im.ischat
        },
        switch_adviser_tel(){
            return this.$store.state.switch_adviser_tel
        }
    },
    onLoad(options){
        if(options.online_id){
            this.online_id = options.online_id
            this.getData()
        }
        uni.$on("getDataAgain",this.getData)
		// #ifdef APP-PLUS
		this.top=this.$store.state.systemInfo.safeArea.top
		// #endif
    },
    onUnload(){
        uni.$off("getDataAgain")
        this.$store.state.allowOpen = true
    },
    components: {
        myIcon
    },
     filters: {
        imgUrl(val, param = "") {
            return formatImg(val, param)
        }
    },
    methods: {
        getData(){
            this.$ajax.get('online/buildAdviser',{online_id:this.online_id},res=>{
                if(res.data.share&&res.data.share.title){
                    this.share = res.data.share
                    this.getWxConfig()
                }
                if(res.data.code === 1){
                    this.adviser_list = res.data.lists
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            })
        },
        handleTel(adviser){
            if(!adviser.tel){
                uni.showToast({
                    title:'此置业顾问没有绑定手机号',
                    icon:'none'
                })
                return
            }
            uni.makePhoneCall({
                phoneNumber: adviser.tel
            });
        },
        toChat(id){
            getChatInfo(id, 15)
        },
        navigate(url){
            navigateTo(url)
        }
    }
}
</script>

<style scoped lang="scss">
.adviser{
    padding-bottom: 90rpx;
}
.title{
    padding: 20rpx 20rpx 20rpx 40rpx;
    font-size: 32rpx;
    font-weight: bold;
    position: relative;
    &::before{
        content: "";
        position: absolute;
        left:20rpx;
        top:20rpx;
        bottom:20rpx;
        width: 6rpx;
        background-color: #f65354
    }
}
.adviser-list{
    box-shadow: 0 0 18upx #dedede;
    background-color: #fff;
    margin: 20rpx;
    .adviser-item{
        justify-content: space-between;
        align-items: center;
        padding: 24upx 32upx;
        .head-img{
            width: 90upx;
            height: 90upx;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 32upx;
        }
        .name{
            font-size: 30upx;
            line-height: 1.8;
            font-weight: bold;
        }
        .tel{
            color: #666;
        }
    }
    .icon-box{
        margin-left: 28upx;
    }
}

.bottom-menu{
    position: fixed;
    width: 100%;
    padding: 8rpx 0;
    box-sizing: border-box;
    bottom: 0;
    background-color: #fff;
    border-top: 1rpx solid #dedede;
    .bar-item{
        line-height: 1;
        flex: 1;
        text-align: center;
        color: #333;
        .text{
            margin-top: 8rpx;
        }
        &.active{
            color:  $uni-color-primary;
        }
    }
}
</style>
