<template>
<view class="page">
    <view class="content">
        <!-- #ifdef H5 -->
        <view class="article-content" v-html="content"></view>
        <!-- #endif -->
        <!-- #ifndef H5 -->
        <u-parse :html="content" @linkpress="navigate" :tag-style="tagStyle"></u-parse>
        <!-- #endif -->
    </view>
</view>
</template>

<script>
import uParse from '../components/Parser/index'
export default {
    data() {
        return {
            content:""
        }
    },
    onLoad(options){
        if(options.online_id){
            this.online_id = options.online_id
            this.getData()
        }
    },
    components: {
        uParse
    },
    methods: {
        getData(){
            this.$ajax.get('online/onlineAttention.html',{online_id:this.online_id},res=>{
                this.content = res.data.content
            })
        },
        navigate(){

        }
    },
}
</script>

<style scoped lang="scss">
.content{
    background-color: #fff;
    padding: $uni-spacing-col-base $uni-spacing-row-base;
    img{
        max-width: 100%;
    }
}
</style>
