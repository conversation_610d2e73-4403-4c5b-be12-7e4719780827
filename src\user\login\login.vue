<template>
	<view class="login_tip">
		微信授权登录...
	</view>
</template>

<script>
	import {wxApi} from "../../common/wxApi.js"
	import {
		mapState,
		mapMutations
	} from 'vuex'
	export default {
		data() {
			return {
			}
		},
		computed: {
			...mapState(['user_info','allowOpen'])
		},
		onLoad(){
			if(wxApi.checkIsWx()){ //如果是微信环境
				this.isWx = true
				if(uni.getStorageSync('token')){
					uni.$emit("getDataAgain",{})
					history.go(-1);
					setTimeout(() => {
						uni.switchTab({
							url:"/pages/index/index"
						})
					},100)
					// if(this.pagesLen>1){
					// 	uni.navigateBack()
					// }else{
					// 	uni.switchTab({
					// 		url:"/pages/index/index"
					// 	})
					// }
					// if(this.$store.state.tel){
					// 	history.go(-1);
					// }else{
					// 	uni.redirectTo({
					// 		url:'/user/bind_phone/bind_phone'
					// 	})
					// }
					return
				}
				if(wxApi.getCode().code!=undefined){ //如果获取到了微信code
					console.log(wxApi.getCode().code)
					this.getToken(wxApi.getCode().code)
					return
				}
				const redirect_uri = window.location.origin + "/h5/user/login/login";
				this.$ajax.get('/wap/index/wxAppId',{},res=>{
					if(res.data.appid){
						wxApi.author(redirect_uri,res.data.appid)
					}else{
						uni.showToast({
							title:res.data.msg||'公众号参数配置错误',
							icon:"none"
						})
					}
				})
			}
		},
		onUnload(){
			this.setAllowOpen(false)
		},
		methods:{
			...mapMutations(['getUserInfo','setAllowOpen']),
			getToken(code){
				this.$ajax.get('/wechat/Index/getMemberInfo.html',{code},res=>{
					if(res.data.code == 1){
						this.getUserInfo(res.data.user)
						// 存储token
						uni.setStorageSync('token',res.data.token)
						uni.setStorageSync('newLogin',1)
						uni.showToast({
							title:"登录成功"
						})
						this.$store.state.user_login_status = 2
						if(res.data.user.tel){
							this.$store.state.user_login_status = 3
						}
						// window.location.href = url
						// console.log('记录的链接：',url)
						// console.log('页面栈：',getCurrentPages())
						setTimeout(()=>{
							uni.$emit("getDataAgain",{})
							history.go(-2);
							setTimeout(() => {
								if(window.location.pathname === '/h5/user/login/login'){
									let url = uni.getStorageSync('backUrl')
									if(url){
										uni.removeStorageSync('backUrl')
										window.location.replace(url)
									}
								}else{
									uni.removeStorageSync('backUrl')
								}
							},200)
						},1200)
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
				})
			},
	}
	}
</script>

<style lang="scss">
.login_tip{
	text-align: center;
	padding: 24rpx 60rpx;
}
</style>
