<!--
 * @Description: 
 * @Author: lei
 * @Date: 2021-06-09 10:35:53
-->
<template>
	<view :style="{ height: height }" id="house_map-box" class="house_map-box">
		<movable-area class="move_able" v-if="show">
			<movable-view
				:scale="true"
				:scale-min="scaleMin"
				:scale-value="scaleValue"
				@change="changeMoveable"
				@scale.prevent.stop="scale"
				:scale-max="scaleMax"
				class="house_map"
				id="house_map"
				direction="all"
				:style="{
					width: scaleW + 'px',
					height: scaleH + 'px',
					left: left,
					marginLeft: img_info.margin_left || 0 - img_info.width / 2 + 'px',
					top: top,
          position:'relative',
				}"
			>
				<view class="img">
					<image
						:src="map_img | imgUrl('w_1300')"
					></image>
					<view
						class="mark"
						:class ="now_point == point.id ? 'now_mark' : ''"
						
						@click="toLoudong(index)"
						v-for="(point, index) in point_list"
						:key="index"
						:style="{
							top:
								(point.top / parseFloat(img_info.height)) * scaleH -
								offset_y +
								'px',
							left:
								(point.left / parseFloat(img_info.width)) * scaleW -
								offset_x +
								'px',
							backgroundColor:(now_point == point.id ?point.activeColor : point.color)
						}"
					>
						<text>{{ point.name }}</text>
						<text class="arrow" :style="{borderTop:(now_point == point.id ?point.activeColor :point.arrowColor)}"></text>
					</view>
				</view>
			</movable-view>
		</movable-area>
		<slot>
		<view class="filter flex-box" v-if="show_screen">
			<checkbox-group
				class="filter_items flex-box"
				@change="handleChange"
				:class="{ hide: !show_filter }"
			>
				<label class="item" :style="{background:item.color}" v-for ="item in filterList" :key ="item.id"
					><checkbox class="checkbox" :value="item.id +''" color="#333" checked /><text
						>{{item.leixing}}</text
					></label
				>
				<!-- <label class="item color2"
					><checkbox class="checkbox" value="2" color="#333" checked /><text
						>在售</text
					></label
				>
				<label class="item color3"
					><checkbox class="checkbox" value="3" color="#333" checked /><text
						>尾盘</text
					></label
				>
				<label class="item color4"
					><checkbox class="checkbox" value="4" color="#333" checked /><text
						>售完</text
					></label
				> -->
			</checkbox-group>
			<view
				@click="show_filter = !show_filter"
				class="show_btn"
				hover-class="hover"
				hover-stay-time="200"
				>{{ show_filter ? "收起" : "展开" }}</view
			>
		</view>
		</slot>
	</view>
</template>

<script>
import { formatImg } from "../common/index.js";
export default {
	data() {
		return {
			img_info: this.map_info,
			offset_x: 0,
			offset_y: 0,
			// current_status: [1, 2, 3, 4],
			show_filter: true,
			show: true,
			scaleMin: 0.2,
			scaleMax: 2,
			scaleValue:0.3,
			house_box_width: "100%",
			house_box_heght: "100%",
			left: "50%",
      top: "50%",
			old: {
				left: 0,
				top: 0,
			},
			scaleW: 0,
			scaleH: 0,
      show: true,
		};
	},
	props: {
		map_info: Object,
		map_img: String,
		mark_point: Array,
		now_point: {
			type: [Number, String],
			default: 0,
		},
		height: {
			type: [String],
			default: "65vw",
		},
		show_screen: {
			type: [Boolean],
			default: true,
		},
		filterList:Array,
	},
	watch: {
		// map_info: {
		// 	handler(val, oldVal){
		// 				// this.show =false
		// 				console.log(val);
		// 				console.log("b.c: "+val.c, oldVal.c);
		// 				this.old.left =Math.random()
		// 				this.old.top =Math.random()
		// 				this.show = false;
		// 				this.img_info = val;
		// 				this.left = this.old.left;
		// 				this.top = this.old.top;
		// 				this.$nextTick(() => {
		// 					this.left=0
		// 					this.top =0
		// 					this.init();
		// 				})
    //      },
    //      deep:true //true 深度监听
      
		// },
		map_info(val){
			this.old.left =Math.random()
			this.old.top =Math.random()
			this.show = false;
			this.img_info = val;
			this.left = this.old.left;
			this.top = this.old.top;
			this.$nextTick(() => {

				this.left=0
				this.top =0
				this.init();
			})
		}
	},
  created() {
		this.init()
  },
	computed: {
		// point_list() {
		// 	// this.img_info = this.map_info;
		// 	return this.mark_point.filter((item) =>
		// 		this.current_status.includes(item.sale_status)
		// 	);
		// },
	},
	mounted() {
		this.offset_x = uni.upx2px(120 / 2);
		this.offset_y = uni.upx2px(50 + 8);
	},
	filters: {
		imgUrl(val, param = "") {
			return formatImg(val, param);
		},
	},
	methods: {
		init() {
			this.img_info = this.map_info;
			this.point_list = this.mark_point
			this.$nextTick(() => {
			
			const HOUSSE_MAP_BOX = uni
				.createSelectorQuery()
				.in(this)
				.select("#house_map-box");
			HOUSSE_MAP_BOX.boundingClientRect((data) => {
				// this.$nextTick(() => {
					this.map_box_width = data.width;
					this.map_box_height = data.height;
					//house 是原图    map 是容器
					this.house_box_width = this.img_info.width;
					this.house_box_height = this.img_info.height;
					let scaleHouse = this.house_box_height / this.house_box_width,
						scalemap = this.map_box_height / this.map_box_width;

					if (scaleHouse >= scalemap) {
						this.scaleW = this.map_box_width * 2;
						this.scaleH =
							(this.scaleW / this.house_box_width) * this.house_box_height;
						if (this.house_box_width >= this.scaleW) {
							this.scaleMax = this.house_box_width / this.map_box_width;
							this.scaleMin = this.map_box_width / this.house_box_width;
							this.scaleValue = this.map_box_width / this.house_box_width;
							this.left =
								((parseFloat(this.img_info.left)/ parseFloat(this.img_info.width)) * this.scaleW)+'px';
							this.top =
                ((parseFloat(this.img_info.top)/ parseFloat(this.img_info.height)) *this.scaleH)+'px';
            }
					} else {
						this.scaleH = this.map_box_height * 2;
						this.scaleW =
							(this.scaleH / this.house_box_height) * this.house_box_width;
						if (this.house_box_height > this.scaleH) {
							this.scaleMax = this.house_box_height / this.map_box_height;
							this.scaleMin = this.map_box_height / this.house_box_height;
							this.scaleValue = this.map_box_height / this.house_box_height;
              this.left =
								((parseFloat(this.img_info.left)/ parseFloat(this.img_info.width)) * this.scaleW)+'px';
							this.top =
                ((parseFloat(this.img_info.top)/ parseFloat(this.img_info.height)) *this.scaleH)+'px';
							// this.left =
							// 	this.house_box_width -
							// 	this.scaleW +
							// 	parseFloat(this.img_info.left)+'px'
							// this.top =
							// 	this.house_box_height -
							// 	this.scaleH +
							// 	parseFloat(this.img_info.top)+'px'
            }

          }
					this.show = true;
				// });
			}).exec();

			});
		},
		scale(e) {},
		changeMoveable(e) {},
		stopMove() {},
		toLoudong(index) {
			this.$emit("clickPoint", index);
		},
		handleChange(e) {
			let all_status =[]
			this.filterList.map(item=>{
				all_status.push(item.id)
			})
			let  no_cludes =  this.mark_point.filter(item=>!all_status.includes(item.sale_status))
			let point = this.mark_point.filter(item=>e.detail.value.includes(item.sale_status+''))
			this.point_list = point.concat(no_cludes)
			this.$forceUpdate()
			// this.current_status = e.detail.value.map((item) => parseInt(item));
		},
	},
};
</script>

<style scoped lang="scss">
.house_map-box {
	width: 100%;
	position: relative;
	overflow: hidden;
	.move_able {
		width: 100%;
		height: 100%;
		position: relative;
		.img {
			width: 100%;
			height: 100%;
			position: relative;
			image {
				width: 100%;
				height: 100%;
			}
		}
	}
	.house_map {
		// position: absolute;
		// max-width: 300%;
		// min-width: 100%;
		.map_img {
			height: 100%;
			width: 100%;
		}
		.mark {
			position: absolute;
			font-size: 32rpx;
			font-weight: bold;
			color: #fff;
			height: 50rpx;
			line-height: 50rpx;
			width: 120rpx;
			text-align: center;
			border-radius: 6rpx;
			background-color: rgba($color: #70d298, $alpha: 0.8);
			&.mark1 {
				background-color: rgba($color: #17bfff, $alpha: 0.8);
				.arrow {
					border-top: rgba($color: #17bfff, $alpha: 0.8) 8rpx solid;
				}
			}
			&.mark2 {
				background-color: rgba($color: #70d298, $alpha: 0.8);
				.arrow {
					border-top: rgba($color: #70d298, $alpha: 0.8) 8rpx solid;
				}
			}
			&.mark3 {
				background-color: rgba($color: #ff7213, $alpha: 0.8);
				.arrow {
					border-top: rgba($color: #ff7213, $alpha: 0.8) 8rpx solid;
				}
			}
			&.mark4 {
				background-color: rgba($color: #f3f3f3, $alpha: 0.8);
				color: #333;
				.arrow {
					border-top: rgba($color: #f3f3f3, $alpha: 0.8) 8rpx solid;
				}
			}
			&.now_mark {
				background-color: rgba($color: $uni-color-primary, $alpha: 0.8);
				.arrow {
					border-top: rgba($color: $uni-color-primary, $alpha: 0.8) 8rpx solid;
				}
			}
			.arrow {
				width: 0;
				height: 0;
				border-left: transparent 8rpx solid;
				border-right: transparent 8rpx solid;
				border-top: rgba($color: #70d298, $alpha: 0.8) 8rpx solid;
				position: absolute;
				left: 0;
				top: 50rpx;
				right: 0;
				margin: auto;
				background: rgba(255, 255, 255, 0) !important;
				cursor: pointer;
			}
		}
	}
	.filter {
		align-items: center;
		.filter_items {
			position: absolute;
			align-items: center;
			height: 60rpx;
			padding: 0 40rpx 0 20rpx;
			border-radius: 30rpx;
			background-color: rgba($color: #ffffff, $alpha: 0.8);
			transition: 0.26s;
			right: 70rpx;
			bottom: 55rpx;
			&.hide {
				width: 0;
				overflow: hidden;
				padding: 0;
			}
			.item {
				height: 40rpx;
				line-height: 40rpx;
				padding: 0 16rpx 0 2rpx;
				border-radius: 20rpx;
				font-size: 0;
				background-color: #17bfff;
				color: #fff;
				display: flex;
				align-items: center;
				~ .item {
					margin-left: 16rpx;
				}
				text {
					font-size: 24rpx;
					margin-left: -10rpx;
					white-space: nowrap;
				}
			}
			.color1 {
				background-color: #17bfff;
			}
			.color2 {
				background-color: #70d298;
			}
			.color3 {
				background-color: #ff7213;
			}
			.color4 {
				background-color: #888;
			}
			.checkbox {
				transform: scale(0.5, 0.5);
			}
		}
		.show_btn {
			position: absolute;
			height: 90rpx;
			width: 90rpx;
			border-radius: 50%;
			line-height: 90rpx;
			text-align: center;
			background-color: rgba($color: #ffffff, $alpha: 1);
			z-index: 2;
			right: 10rpx;
			bottom: 40rpx;
		}
		.hover {
			background-color: #f3f3f3;
		}
	}
}
</style>
