<template>
	<view id="uesr-center">
		<view class="header-block">
			<view class="user-info flex-box">
				<image class="user-img" :src="(shopInfo.shoplogo || defaultAvatar) | imgUrl" mode="aspectFill"></image>
				<view class="info">
					<view class="user-id" v-if="shopInfo.shopname || shop_status>-1">{{shopInfo.shopname||''}}</view>
					<view class="user-id" v-else @click="toLogin">去登录</view>
					<view class="user-shenfen">
						<my-icon type="huiyuan" color="#ffffff" size="18"></my-icon>
						<view class="shenfen-text">{{shopInfo.vip}}</view>
						<uni-icons type="arrowright" color="#ffffff"></uni-icons>
					</view>
				</view>
			</view>
			<view class="user-data">
				<view class="data-item">
					<view class="value">{{shopInfo.money_own}}</view>
					<view class="title">金币余额</view>
				</view>
				<view class="data-item">
					<view class="value">{{shopInfo.orderCount}}</view>
					<view class="title">客户预约</view>
				</view>
				<view class="data-item">
					<view class="value">{{shopInfo.totalCount}}</view>
					<view class="title">平台预约量</view>
				</view>
			</view>
		</view>
		<view class="gonggao-box">
			<view class="gonggao flex-box news-box">
				<my-icon type="yinliang" color="#00c07b" size="18"></my-icon>
				<text style="color: #00c07b;margin-left: 10upx;">平台公告</text>
				<swiper class="flex-1" :duration="300" :circular="true" :autoplay="true" :vertical="true">
					<swiper-item v-for="news in newsList" :key="news.id">
						<navigator animation-type="pop-in" animation-duration="260" :url="'/user/my/announcement?id='+news.id" hover-class="navigator-hover">
							<view class="swiper-item">{{news.title}}</view>
						</navigator>
					</swiper-item>
				</swiper>
			</view>
		</view>
		<view class="list-box" @click="handleShop">
			<uni-list-item :title="shop_status==1?'店铺设置':(shop_status==2?'店铺正在审核中,请完善店铺资料':'入驻平台、获取客户')" :note="shop_status==1?'设置店铺信息、电话、地址':(shop_status==2?'平台客服电话：'+kefutel:'超低成本，本地宣传，简单有效，方便快捷！')" show-extra-icon="true" :extra-icon="{color: '#f4744d',size: '30',type: 'settings'}"></uni-list-item>
		</view>
		<view class="top-20 grid-block" v-if="shop_status==1">
			<view class="label-title">管理中心</view>
			<!-- 宫格 -->
			<my-grid @click="toNav" iconType="font" :options="options" column-num="4" :show-border="true" :fontSize="14" className="sm"></my-grid>
		</view>
		<view class="top-20" style="background-color: #fff;">
			<uni-list-item title="联系客服" @click="handleTel" show-extra-icon="true" :extra-icon="{color: '#1296db',size: '28',type: 'phone'}"></uni-list-item>
		</view>
        <my-popup ref="upgrade">
            <view class="apply-box">
                <view class="row bottom-line">选择升级类型</view>
                <view class="upgrade-list flex-box">
                    <view class="upgrade-item flex-1" v-for="item in upgradeList" :key="item.value" @click="subUpgrade(item.value)">
                        <view>{{item.name}}</view>
                        <view>{{item.money}}金币/年</view>
                    </view>
                    <view class="upgrade-item flex-1" style="height:0;background:#fff"></view>
                </view>
                <view class="btn-box">
                    <button class="" @click="$refs.upgrade.hide()">取消</button>
                </view>
            </view>
        </my-popup>
        <my-popup ref="popup">
            <view class="apply-box">
                <view class="row bottom-line">选择会员类别</view>
                <view>
                    <radio-group @change="radioChange">
                        <label v-for="(item,index) in applyList" :key="index" class="list-item bottom-line" hover-class="navigator-hover">
                            <radio color="#f65354" :value="item.value.toString()" :checked="false" />
                            <view class="list-info flex-1">
                                <view class="list-title">{{item.name}}</view>
                            </view>
                        </label>
                    </radio-group>
                </view>
                <view class="btn-box">
                    <button class="default" @click="subApply()">确定提交</button>
                </view>
            </view>
        </my-popup>
	</view>
</template>

<script>
	import {
		uniIcons,
		uniListItem,
	} from "@dcloudio/uni-ui"
	import myIcon from "../../components/icon.vue"
    import myGrid from "../../components/myGrid.vue"
    import myPopup from "../../components/myPopup"
	import {
		navigateTo,
		checkUserStatus,
		showModal,
		formatImg
	} from "../../common/index.js"
	import {
		mapMutations
	} from 'vuex'
	export default {
		components: {
			uniIcons,
			myGrid,
			myIcon,
            uniListItem,
            myPopup
		},
		data() {
			return {
                defaultAvatar: this.$store.state.defaultAvatar,
                shop_status:-1,
                shopInfo:{},
                kefutel:"",
				options: [
					{
                        text: "店铺设置",
                        icon_name:"dianpu",
                        icon_color:"#009cde",
                        icon_size:"26",
						url: "/home/<USER>/setting_shop"
					},
					{
						text: "客户预约",
						icon_name:"shikebiao",
                        icon_color:"#00c07b",
                        icon_size:"26",
						url: "/home/<USER>/client_list"
					},
					{
						text: "案例管理",
						icon_name:"release",
                        icon_color:"#00c07b",
                        icon_size:"26",
						url: "/home/<USER>/manage_case"
					},
					{
						text: "刷新排名",
						icon_name:"shuaxin",
                        icon_color:"#ff9d5b",
                        icon_size:"26",
						url: "/user/recharge"
					},
					{
						text: "会员升级",
						icon_name:"shengji",
                        icon_color:"#ea4c89",
                        icon_size:"26",
						url: "/user/member_upgrade"
					},
					{
                        text: "店铺预览",
                        icon_name:"dianpu",
                        icon_color:"#a59dd1",
                        icon_size:"26",
						url: ""
					},
					{
                        text: "金币充值",
                        icon_name:"chongzhi",
                        icon_color:"#ff6565",
                        icon_size:"26",
						url: "/user/recharge"
					},
					{
                        text: "平台首页",
                        icon_name:"fangzi",
                        icon_color:"#4cb953",
                        icon_size:"26",
						url: "/home/<USER>/index"
					}
				],
				mail_count:0,
                newsList: [],
                applyList:[],
                upgradeList:[]
			};
		},
		onLoad() {
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO || MP-BAIDU
			uni.hideShareMenu()
			// #endif
		},
		onHide() {
			this.setAllowOpen(true)
		},
		onShow() {
			this.getData()
		},
		filters:{
			imgUrl(val){
				return formatImg(val,'w_120')
			}
		},
		methods: {
			...mapMutations(['setAllowOpen']),
			getData() {
				this.$ajax.get('memberShop/member.html', {}, (res) => {
					if (res.data.code == 1) {
                        this.$store.state.user_info.tel = res.data.shop.tel
                        this.shopInfo = res.data.shop

                        // 店铺修改接口后台说需要传递shopid，所以需要再此页面传入放店铺修改页以工提交修改时使用
                        this.options[0].url = this.options[0].url+'?id='+res.data.shop.id

                        this.shop_status = res.data.shop_status
                        this.kefutel = res.data.kefutel
						this.newsList = res.data.news||[]
					} else {
						uni.showToast({
							title: res.data.msg,
                            icon: "none",
                            mask: true
						})
                    }
                    if(res.data.code == 0){
                        setTimeout(()=>{
                            uni.navigateBack()
                        },1000)
                    }
				}, (err) => {

				}, false)
			},
			handleShop() {
				checkUserStatus(()=>{
                    if(this.shop_status==0){
                        // this.$refs.popup.show()
                        // this.getApply()
                        navigateTo('/home/<USER>/setting_shop')
                        return
                    }
					navigateTo('/home/<USER>/setting_shop?id='+this.shopInfo.id)
				})
            },
            getUpgrade(){
                this.$ajax.get('memberShop/upgrade',{},res=>{
                    console.log(res.data)
                    if(res.data.code == 1){
                        this.upgradeList=res.data.group
                    }else{
                        uni.showToast({
                            title:res.data.msg,
                            icon:"none"
                        })
                    }
                })
            },
            subUpgrade(value){
                this.$ajax.post('memberShop/upgrade',{vip:value},res=>{
                    if(res.data.code == 1){
                        // this.upgradeList=res.data.group
                        uni.showToast({
                            title:res.data.msg
                        })
                        this.$refs.upgrade.hide()
                    }else if(res.data.code == -4){
                        showModal({
                            content:"金币不足，是否去充值？",
                            confirm:()=>{
                                navigateTo('/user/recharge')
                            }
                        })
                    }else{
                        uni.showToast({
                            title:res.data.msg,
                            icon:"none"
                        })
                    }
                })
            },
			toNav(e) {
                switch (e.index){
                    case 3:
                        this.refreshShop()
                        break;
                    case 4:
                        this.$refs.upgrade.show()
                        this.getUpgrade()
                        break;
                    case 5:
                        navigateTo("/home/<USER>/detail?id="+this.shopInfo.id)
                        break;
                    default:
                        // uni.redirectTo({
                        //     url:this.options[e.index].url
                        // })
                        navigateTo(this.options[e.index].url)
                }
            },
            refreshShop(){
                var handle = ()=>{
                    this.$ajax.get('memberShop/refresh',{},res=>{
                        uni.showToast({
                            title:res.data.msg,
                            icon:res.data.code==1?'success':'none',
                            mask:true
                        })
                    })
                }
                if(this.shopInfo.grade>1){
                    handle()
                    return
                }
                showModal({
                    content:"普通商户刷新排名需要扣除"+(this.shopInfo.deduct||1)+"个金币，是否刷新?",
                    confirm:()=>{
                        handle()
                    }
                })
            },
			handleTel(){
				uni.makePhoneCall({
					phoneNumber:this.kefutel
				})
			},
			toLogin(){
				navigateTo("/user/login/login")
			}
		}
	}
</script>

<style lang="scss">
#uesr-center {
    .header-block {
        width: 100%;
        padding: 50upx;
        box-sizing: border-box;
        height: 50vw;
        background-color: #09bb07;
        color: #fff;
    }

    .user-info {
        align-items: center;

        .user-img {
            width: 120upx;
            height: 120upx;
            margin-right: 24upx;
            border-radius: 50%;
        }

        .user-id {
            font-size: 36upx;
            padding: 10upx 0;
            margin-bottom: 10upx;
        }

        .user-shenfen {
            color: #ffffff;
            padding: 2upx 20upx;
            height: 46upx;
            line-height: 46upx;
            display: flex;
            align-items: center;
            border-radius: 18upx;
            background-color: #089f06;

            .shenfen-text {
                font-size: $uni-font-size-sm;
                display: inline-block;
                // width: 260upx;
                padding: 0 20upx;
            }
        }
    }

    .user-data {
        // margin-left: 80upx;
        margin-top: 28upx;
        display: flex;
        align-items: center;
        justify-content: center;

        .data-item {
            padding: 10upx 15upx;
            margin: 20upx;
            display: inline-block;
            text-align: center;

            .value {
                font-size: $uni-font-size-blg;
                font-weight: bold;
            }

            .title {
                font-size: 26upx;
            }
        }
    }

    .gonggao-box {
        position: relative;
        height: 80upx;
    }

    .gonggao {
        position: absolute;
        height: 80upx;
        line-height: 80upx;
        top: -35upx;
        left: 24upx;
        right: 24upx;
        align-items: center;
        padding: 0 28upx;
        background-color: #fff;
        border-radius: 18upx;

        .text {
            padding: 2upx 20upx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: $uni-text-color-grey;
        }
    }

    .news-box swiper {
        height: 50upx;
        padding: 10upx;
        line-height: 50upx;
    }

    .news-box swiper view {
        font-size: $uni-font-size-base;
    }

    .list-box {
        background-color: #fff
    }

    .grid-block {
        background-color: #fff;

        .label-title {
            font-size: $uni-font-size-lg;
            padding: 20upx 28upx;
        }
    }
    .apply-box{
        height:100vh;
        width:100%;
        /* #ifdef H5 */
        padding-top: 90upx;
        /* #endif */
        background-color:#fff;
        .row{
            padding:24upx 30upx;
        }
        .list-item{
            display: flex;
            align-items: center;
            padding: $uni-spacing-col-lg $uni-font-size-lg;
            radio{
                padding: 20upx 30upx;
                margin-left: -30upx;
            }
            .list-title{
                font-size: $uni-font-size-lg;
                text-overflow:ellipsis;
                white-space:nowrap;
                line-height:1.5;
                overflow:hidden;
            }
        }
        .upgrade-list{
            padding: 10upx;
            flex-wrap: wrap;
        }
        .upgrade-item{
            min-width: 40%;
            width:45%;
            padding: 20upx;
            margin: 10upx;
            text-align: center;
            border-radius: 10upx;
            background-color: #00c07b;
            color: #fff;
        }
    }
}
</style>
