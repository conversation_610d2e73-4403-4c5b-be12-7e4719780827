<template>
	<view class="page" :class="{ padtop: showTips }" v-if ="showCon">
		<!-- 关注公众号提示 -->
		<view class="tips " v-if="showTips"  @click="guanzhugongzhonghao()">
			<view class="tips-box flex-row">
				<image :src="'/zhaofang/<EMAIL>'| formatIcon" mode="widthFix" />
				<view class="tip-text flex-1">关注{{siteName}}公众号，实时接收找房成功通知 </view>
				<view class="tip-btn" >未关注</view>
			</view>
		</view>
		<view class="radio_box flex-row">
			<view
				class="radio_box_item"
				:class="{ active: params.house_type == 1 }"
				@click="changeHouseType('ershou')"
			>
				<view class="radio_box_item_top flex-row">
					<view class="line ershou"> </view>
					<view class="title">
						二手房
					</view>
				</view>
				<view class="radio_box_item_bottom">
					海量真房源
				</view>
				<view class="radio_box_bg">
					<image
						:src="'/zhaofang/<EMAIL>' | formatIcon"
						mode="widthFix"
					>
					</image>
				</view>
			</view>
			<view
				class="radio_box_item"
				:class="{ active: params.house_type == 2 }"
				@click="changeHouseType('xinfang')"
			>
				<view class="radio_box_item_top flex-row">
					<view class="line"> </view>
					<view class="title">
						新房
					</view>
				</view>
				<view class="radio_box_item_bottom">
					发现好楼盘
				</view>
				<view class="radio_box_bg">
					<image
						:src="'/zhaofang/<EMAIL>' | formatIcon"
						mode="widthFix"
					>
					</image>
				</view>
			</view>
		</view>

		<view class="box">
			<view
				class="box_row bottom-line flex-row"
				v-if="params.house_type == 1"
				@click="showDialog('leixing')"
			>
				<view class="box_row_icon">
					<image :src="imgDomain + '/zhaofang/leixingicon.png'" mode="widthFix">
					</image>
				</view>
				<view class="box_row_name flex-1">
					类型
				</view>
				<view class="box_row_right flex-row">
					<view class="box_row_right_name">
						<template v-if="params.is_sale">
							出售
						</template>
						<template v-if="params.is_sale && params.is_rent">
							、
						</template>
						<template v-if="params.is_rent">
							出租
						</template>
						<!-- {{params['is_sale']==1?:"出售 、":''}} -->
					</view>
					<view class="box_row_right_icon">
						<my-icon type="ic_into" size="28rpx" color="#3A3F42"></my-icon>
					</view>
				</view>
			</view>
			<view class="box_row bottom-line flex-row" @click="toArea">
				<view class="box_row_icon">
					<image
						:src="imgDomain + '/zhaofang/shangquanicon.png'"
						mode="widthFix"
					>
					</image>
				</view>
				<view class="box_row_name flex-1">
					区域/商圈
				</view>
				<view class="box_row_right flex-row">
					<view class="box_row_right_name" v-if="areaname">
						{{ areaname }}区域
					</view>
					<view class="box_row_right_icon">
						<my-icon type="ic_into" size="28rpx" color="#3A3F42"></my-icon>
					</view>
				</view>
			</view>
			<view class="box_row bottom-line flex-row" @click="toXiaoqu" v-if='params.house_type == 1'>
				<view class="box_row_icon">
					<image :src="imgDomain + '/zhaofang/xiaoquicon.png'" mode="widthFix">
					</image>
				</view>
				<view class="box_row_name flex-1">
					小区
				</view>
				<view class="box_row_right flex-row">
					<view class="box_row_right_name" v-if="cidLength > 0">
						{{ cidLength }}个小区
					</view>
					<view class="box_row_right_icon">
						<my-icon type="ic_into" size="28rpx" color="#3A3F42 "></my-icon>
					</view>
				</view>
			</view>
		</view>
		<view class="box">
			<view
				class="box_row bottom-line"
				v-if="
					saleArray.length > 0 && params.is_sale == 1 && params.house_type == 1
				"
			>
				<view class="box_row_item  flex-row" 	@click="showDialog('zongjia')">
					<view class="box_row_icon">
						<image
							:src="imgDomain + '/zhaofang/zongjiaicon.png'"
							mode="widthFix"
						>
						</image>
					</view>
					<view class="box_row_name flex-1">
						出售总价
					</view>
					<view class="box_row_right flex-row">
						<view class="box_row_right_name">
							<text> 您已选择区间：</text><text>{{ chushou_name }}</text>
						</view>
						<view class="box_row_right_icon">
							<my-icon type="ic_into" size="28rpx" color="#3A3F42"></my-icon>
						</view>
					</view>
				</view>
			</view>
			<view
				class="box_row bottom-line"
			
				v-if="newHousePriceList.length > 0 && params.house_type == 2"
			>
				<!-- 新房单价 -->
				<view class="box_row_item  flex-row" 	@click="showDialog('danjia')">
					<view class="box_row_icon">
						<image
							:src="imgDomain + '/zhaofang/zongjiaicon.png'"
							mode="widthFix"
						>
						</image>
					</view>
					<view class="box_row_name flex-1">
						新房单价
					</view>
					<view class="box_row_right flex-row">
						<view class="box_row_right_name">
							<text> 您已选择区间：</text><text>{{ xinfang_name }}</text>
						</view>
						<view class="box_row_right_icon">
							<my-icon type="ic_into" size="28rpx" color="#3A3F42"></my-icon>
						</view>
					</view>
				</view>
			</view>
			<view
				class="box_row bottom-line"
			
				v-if="
					rentArray.length > 0 && params.is_rent == 1 && params.house_type == 1
				"
			>
				<view class="box_row_item flex-row" 	@click="showDialog('zujin')">
					<view class="box_row_icon">
						<image :src="imgDomain + '/zhaofang/priceicon.png'" mode="widthFix">
						</image>
					</view>
					<view class="box_row_name flex-1">
						出租单价
					</view>
					<view class="box_row_right flex-row">
						<view class="box_row_right_name">
							<text> 您已选择区间：</text><text>{{ chuzu_name }}</text>
						</view>
						<view class="box_row_right_icon">
							<my-icon type="ic_into" size="28rpx" color="#3A3F42"></my-icon>
						</view>
					</view>
				</view>
			</view>
			<view
				class="box_row bottom-line"
			
				v-if="mianjiArray.length > 0 && params.house_type == 1"
			>
				<view class="box_row_item flex-row" 	@click="showDialog('mianji')">
					<view class="box_row_icon">
						<image
							:src="imgDomain + '/zhaofang/mianjiicon.png'"
							mode="widthFix"
						>
						</image>
					</view>
					<view class="box_row_name flex-1">
						面积
					</view>
					<view class="box_row_right flex-row">
						<view class="box_row_right_name">
							<text> 您已选择区间：</text><text>{{ mianji_name }}</text>
						</view>
						<view class="box_row_right_icon">
							<my-icon type="ic_into" size="28rpx" color="#3A3F42 "></my-icon>
						</view>
					</view>
				</view>
			</view>
			<view class="box_row bottom-line flex-row" @click="showDialog('huxing')" v-if ="params.house_type == 1">
				<view class="box_row_icon">
					<image :src="imgDomain + '/zhaofang/huxingicon.png'" mode="widthFix">
					</image>
				</view>
				<view class="box_row_name flex-1">
					户型
				</view>
				<view class="box_row_right flex-row">
					<view class="box_row_right_name" v-if="huxingLength > 0">
						{{ huxingLength }}个户型
					</view>
					<view class="box_row_right_icon">
						<my-icon type="ic_into" size="28rpx" color="#3A3F42 "></my-icon>
					</view>
				</view>
			</view>
		</view>
		<view class="box" v-if="params.house_type == 1">
			<view class="box_row bottom-line flex-row" @click="changeDynamic(3)">
				<view class="box_row_icon">
					<image :src="imgDomain + '/zhaofang/zujinicon.png'" mode="widthFix">
					</image>
				</view>
				<view class="box_row_name flex-1">
					经纪人房源
				</view>
				<view class="box_row_right flex-row">
					<view class="box_row_right_img">
						<image
							v-if="params.is_agent == 1"
							:src="imgDomain + '/zhaofang/open_btn.png'"
							mode="widthFix"
						></image>
						<image
							v-if="params.is_agent == 0"
							:src="imgDomain + '/zhaofang/close_btn.png'"
							mode="widthFix"
						></image>
						<!-- <my-icon type ="open_btn" size="28rpx" ></my-icon> -->
					</view>
				</view>
			</view>
			<view class="box_row bottom-line flex-row" @click="changeDynamic(4)">
				<view class="box_row_icon">
					<image :src="imgDomain + '/zhaofang/mianjiicon.png'" mode="widthFix">
					</image>
				</view>
				<view class="box_row_name flex-1">
					个人房源
				</view>
				<view class="box_row_right flex-row">
					<view class="box_row_right_img">
						<image
							v-if="params.is_personal == 1"
							:src="imgDomain + '/zhaofang/open_btn.png'"
							mode="widthFix"
						></image>
						<image
							v-if="params.is_personal == 0"
							:src="imgDomain + '/zhaofang/close_btn.png'"
							mode="widthFix"
						></image>
						<!-- <my-icon type ="close_btn" size="28rpx"  color="#3A3F42"></my-icon> -->
					</view>
				</view>
			</view>
		</view>
		<view class="btn" @click="save">
			确认
		</view>
		<my-popup
			:ref="currentDialog"
			:position="currentDialogPosition"
			height="auto"
			:touch_hide="true"
			@click="stopMove"
		>
			<template v-if="currentDialog == 'fangyuan'">
				<selectPop
					:list="fangyuanList"
					title="请选择房源信息"
					@confirm="confirm"
					type="fangyuan"
					@cancel="cancel"
				></selectPop>
			</template>
			<template v-if="currentDialog == 'leixing'">
				<selectPop
					:list="fangyuanTypeList"
					title="请选择房源类型"
					@confirm="confirm"
					type="leixing"
					@cancel="cancel"
				></selectPop>
			</template>
			<template v-if="currentDialog == 'huxing'">
				<selectPop
					:list="huxingList"
					maxValue="12"
					minValue="1"
					title="请选择户型"
					@confirm="confirm"
					type="huxing"
					@cancel="cancel"
				></selectPop>
			</template>
			<template v-if="currentDialog == 'zujin'">
				<selectPop
					:list="rentArray"
					:isSingleCheck="true"
					title="请选择出租单价"
					@confirm="confirm"
					type="zujin"
					@cancel="cancel"
				></selectPop>
			</template>
			<template v-if="currentDialog == 'zongjia'">
				<selectPop
					:list="saleArray"
					:isSingleCheck="true"
					title="请选择出售总价"
					@confirm="confirm"
					type="zongjia"
					@cancel="cancel"
				></selectPop>
			</template>
			<template v-if="currentDialog == 'mianji'">
				<selectPop
					:list="mianjiArray"
					:isSingleCheck="true"
					title="请选择面积"
					@confirm="confirm"
					type="mianji"
					@cancel="cancel"
				></selectPop>
			</template>
			<template v-if="currentDialog == 'danjia'"> 	<!-- 新房单价 -->
				<selectPop
					:list="newHousePriceList"
					:isSingleCheck="true"
					title="请选择新房单价"
					@confirm="confirm"
					type="danjia"
					@cancel="cancel"
				></selectPop>
			</template>
		</my-popup>
		<myModel :is_show ="showModel" @clickBtn ="clickBtn" :btns="modelBtns">
			<view class="model_box">
				<view class="model_ask">
					{{modelText}}
				</view>
				<view class="model_tip">
					{{modelTip}}
				</view>

			</view>
		</myModel>
		
		<my-popup ref="qrcode_popup" position="top" >
			<view class="qrcode-box" >
					<view class="img_c">
						<image :src="siteImg" mode="aspectFit"></image>
					</view>
				<!-- #ifdef H5 -->
				<view class="img-box" :style ="{backgroundImage:'url('+ModelBg+')'}">
					<div class="flex-box tip_guanzhu">
							<text>关注{{siteName}}公众号</text>
							<text>实时接收消息通知</text>
					</div>
					<image @longtap="saveQrcode" class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="title">长按保存图片</view>
						<view class="tip">相册选取，识别关注</view>
					</view>
				</view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view class="img-box" :style ="{backgroundImage:'url('+ModelBg+')'}">
					<div class="flex-box tip_guanzhu">
							<text>关注{{siteName}}公众号</text>
							<text>实时接收消息通知</text>
					</div>
					<image class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="tip">长按识别二维码关注公众号</view>
					</view>
				</view>
				<!-- #endif -->
				<view class="icon-box" @click="hideQrcode">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
	</view>
</template>

<script>
import { config, showModal } from "../common/index.js";
import myIcon from "../components/myIcon";
// import tabBar from '../components/tabBar'
import selectPop from "./components/selectPop";
import sliderBar from "./components/sliderBar";
import myPopup from "../components/myPopup";
import myModel from "../components/myModel"
export default {
	data() {
		return {
			imgDomain: "", //图片域名
			currentDialog: "", //当前弹框
			currentDialogPosition: "bottom", //弹框位置
			currentIndex: -1, //1出售总价 2 出租单价 3  4 面积 5 新房单价
			fangyuanTypeList: [
				{
					id: 1,
					name: "出租",
					isChecked: false,
				},
				{
					id: 2,
					name: "出售",
					isChecked: false,
				},
			], //房源类型
			saleArray: [], //出售总价 列表
			rentArray: [], //出租总价 列表
			mianjiArray: [], //面积 列表
			fangyuanList: [], //
			newHousePriceList: [], //新房单价 列表
			huxingList: [
				//户型列表
				{
					id: 1,
					name: "一室",
					isChecked: true,
				},
				{
					id: 2,
					name: "二室",
					isChecked: true,
				},
				{
					id: 3,
					name: "三室",
					isChecked: true,
				},
				{
					id: 4,
					name: "四室",
					isChecked: true,
				},
				{
					id: 5,
					name: "五室",
					isChecked: true,
				},
			],
			huxingLength: 5,
			areaLength: "", //订阅区域个数
			cidLength: "", //订阅小区个数
			isClicking: false,
			xinfang_name: "不限",
			chuzu_name: "不限",
			chushou_name: "不限",
			mianji_name: "不限",
			isOpenPersonalHouses:0, //站点是否开启推送个人房源服务  默认0 未开启
			params: {
				mianji: "", //面积 id  选新房不用传
				mianji_section: "", //面积区间 选新房不用传
				is_rent: 1,
				is_sale: 1, //
				sale_price: "", //出售总价    如果没选 出售  不用提交 sale_price
				sale_price_section: "", //出售总价区间    如果没选 出售  不用提交 sale_price_section
				rent_price: "", //出租单价  如果没选 出租  不用提交 rent_price
				rent_price_section: "", //出租单价区间  如果没选 出租  不用提交 rent_price_section
				build_price: "", // 新房单价  house_type 选择二手房 不用传
				build_price_section: "", // 新房单价区间  house_type 选择二手房 不用传
				shi: "1,2,3,4,5",
				cid: "",
				area_ids: "",
				house_type: 1,
				is_agent: 1, //	是否订阅经纪人房源
				is_personal: 0, //是否订阅个人房源
			},
			areaname:'',
			isClicking: false,
			share: {},
			showModel:false,
			modelBtns:[{name: "取消", color: "#666666"}, {name:"确定", color: "#007aff"}],
			modelText:'',
			modelTip:'',
			showCon:false,
			qrcode:"",
			showTips:false,
			siteName:"",
			siteImg:'',
			ModelBg:"",
			money_own: 0, //推送个人房源需要扣除的金币个数 0为不扣费 大于0 为扣除的金币个数
		};
	},
	components: {
		myIcon,
		myPopup,
		selectPop,
		sliderBar,
		myModel
	},
	computed: {},
	onLoad(options) {
		if (options.dingyueType){
			this.params.house_type =options.dingyueType
		}
		this.imgDomain = config.imgDomain;
		this.siteName =config.projectName
		this.checkLogin(this.getData)
		this.ModelBg =config.imgDomain +"/zhaofang/<EMAIL>"
		// this.getData();
		// uni.$on("getDataAgain",()=>{
		// 		this.getData();
		// })
		uni.$on("getCheckedAgain", (data) => {
			if (data.type == "area") {
				this.params.area_ids = data.area_ids.join(",");
				this.areaLength = data.area_len;
			}
			if (data.type == "xiaoqu") {
				this.params.cid = data.cid.join(",");
				this.cidLength = data.cid.length;
			}
		});
		uni.$on("getAreaInfo",(data)=>{
			console.log(data,1233);
			this.params.lat=data.lat
			this.params.lng =data.lng 
			this.params.area_ids =data.id
			this.areaname =data.areaname
		})
	},
	onUnload() {
		uni.$off("getCheckedAgain");
		uni.$off("getDataAgain");
		uni.$off("getAreaInfo")
		uni.removeStorageSync("communityObj");
	},
	filters: {
		formatIcon(val) {
			return config.imgDomain + val;
		},
	},
	onHide(){
		// this.showTips =false
	},
	onShow() {
	},

	methods: {

		checkLogin(callback) {
      this.$ajax.get('member/checkUserStatus', {}, res => {
        if (res.data.code === 1) {
          callback&&callback()
        } else {
					this.$store.state.user_login_status = res.data.status
					if (res.data.status==1){
						this.$navigateTo("/user/login/login")
					}else if (res.data.status==2){
						this.$navigateTo("/user/bind_phone/bind_phone")
					}
				}
				this.showCon=true
      })
    },
		showDialog(type, position = "bottom") {
			this.currentDialog = type;
			this.currentDialogPosition = position;
			this.$nextTick(() => {
				this.$refs[type].show();
			});
		},
		changeHouseType(val) {
			if (val == "xinfang") {
				this.params.house_type = 2;
				this.getData();
			} else if (val == "ershou") {
				this.params.house_type = 1;
				this.getData();
			}
			this.$forceUpdate();
		},
		choosePrice(idx) {
			if (this.currentIndex == idx) {
				this.currentIndex = -1;
			} else {
				this.currentIndex = idx;
			}
		},
		changeSelectPrice(e) {
			switch (e.types) {
				case "1": //总价
					if (e.index >= 0) {
						this.params.sale_price = this.saleArray[e.index].id;
						this.chushou_name = this.saleArray[e.index].name;
						this.params.sale_price_section = this.saleArray[e.index].value;
					} else {
						// this.params.sale_price=this.array[e.index]
					}
					break;
				case "2": //单价
					if (e.index >= 0) {
						this.params.rent_price = this.rentArray[e.index].id;
						this.chuzu_name = this.rentArray[e.index].name;
						this.params.rent_price_section = this.rentArray[e.index].value;
					}

					// console.log(this.params.price,this.priceArray[e.index]);
					break;
				case "5": //新房单价
					if (e.index >= 0) {
						this.params.build_price = this.newHousePriceList[e.index].id;
						this.xinfang_name = this.newHousePriceList[e.index].name;
						this.params.build_price_section = this.newHousePriceList[
							e.index
						].value;
					}
					break;
				case "4": //面积
					if (e.index >= 0) {
						this.params.mianji = this.mianjiArray[e.index].id;
						this.mianji_name = this.mianjiArray[e.index].name;
						this.params.mianji_section = this.mianjiArray[e.index].value;
					}
					break;

				default:
					break;
			}
		},
		changeDynamic(type) {
			switch (type) {
				case 1:
					break;
				case 2:
					break;
				case 3:
					let text = "";
					if (this.params.is_agent == 1) {
						text = "确认关闭推送经纪人房源";
					} else {
						text = "确认开启推送经纪人房源？";
					}
					showModal({
						title: "提示",
						content: text,
						confirmColor: "#007AFF",
						confirm: () => {
							if (this.params.is_agent == 1) {
								this.params.is_agent = 0;
							} else {
								this.params.is_agent = 1;
							}
						},
					});
					break;
				case 4:
					if(this.isOpenPersonalHouses==0){
						uni.showToast({
							title:'暂未开放',
							icon:'none'
						})
						return 
					}
					this.showModel =true
					if (this.params.is_personal == 1) {
						this.modelText = "确认关闭推送个人房源";
					} else {
							this.modelText ="确认开启推送个人房源"
							this.modelTip="每条推送将扣除金币" +
								this.money_own +
								"个 请确保金币余额充足";
					}
					break;

				default:
					break;
			}
		},
		clickBtn(e){
			if (e==1){
				if (this.params.is_personal == 1) {
					this.params.is_personal = 0;
				} else {
					this.params.is_personal = 1;
				}
				this.showModel =false
					this.modelTip=''
			}
			if (e==0){
				this.showModel =false
			}
		},

		stopMove() {},
		getData() {
			this.$ajax.get(
				"house/getSubscriptionList",
				{ house_type: this.params.house_type },
				(res) => {

					if (res.data.share) {
						this.share = res.data.share;
						this.share.link=this.getShareLink()
					}
					this.getWxConfig()
					if (res.data.code == 1) {
						this.params=res.data.data
						if (res.data.data.area_name){
							this.areaname =res.data.data.area_name
						}
						// this.params = Object.assign(this.params, res.data.data);
						if (this.params.cid) {
							this.params.cid =res.data.data.cid
							this.cidLength = this.params.cid.split(",").length;
						} else {
							this.params.cid =''
							this.cidLength = 0;
						}
						if (this.params.area_ids) {
							this.params.area_ids =res.data.data.area_ids
							this.areaLength = this.params.area_ids.split(",").length;
						} else {
							this.params.area_ids =''
							this.areaLength = 0;
						}
						if (this.params.shi) {
							let huxingCheckArr = this.params.shi.split(",");
							this.huxingLength = huxingCheckArr.length;
							this.huxingList.map((item) => {
								item.isChecked = false;
								huxingCheckArr.map((huxing) => {
									if (item.id == huxing) {
										item.isChecked = true;
									}
								});
							});
						} else {
							this.huxingLength = 0;
						}
						if (this.params.is_rent == 1) {
							this.fangyuanTypeList[0].isChecked = true;
						} else {
							this.fangyuanTypeList[0].isChecked = false;
						}
						if (this.params.is_sale == 1) {
							this.fangyuanTypeList[1].isChecked = true;
						} else {
							this.fangyuanTypeList[1].isChecked = false;
						}
					}else {
							this.params={
								mianji: "", //面积 id  选新房不用传
								mianji_section: "", //面积区间 选新房不用传
								is_rent: 1,
								is_sale: 1, //
								sale_price: "", //出售总价    如果没选 出售  不用提交 sale_price
								sale_price_section: "", //出售总价区间    如果没选 出售  不用提交 sale_price_section
								rent_price: "", //出租单价  如果没选 出租  不用提交 rent_price
								rent_price_section: "", //出租单价区间  如果没选 出租  不用提交 rent_price_section
								build_price: "", // 新房单价  house_type 选择二手房 不用传
								build_price_section: "", // 新房单价区间  house_type 选择二手房 不用传
								shi: "1,2,3,4,5",
								cid: "",
								area_ids: "",
								house_type: this.params.house_type,
								lat:'',
								lng:'',
								is_agent: 1, //	是否订阅经纪人房源
								is_personal: 0, //是否订阅个人房源
							}
						
								this.cidLength = 0;
								this.areaLength = 0;
						if (this.params.is_rent == 1) {
							this.fangyuanTypeList[0].isChecked = true;
						} else {
							this.fangyuanTypeList[0].isChecked = false;
						}
						if (this.params.is_sale == 1) {
							this.fangyuanTypeList[1].isChecked = true;
						} else {
							this.fangyuanTypeList[1].isChecked = false;
						}
							if (this.params.shi) {
							let huxingCheckArr = this.params.shi.split(",");
							this.huxingLength = huxingCheckArr.length;
							this.huxingList.map((item) => {
								item.isChecked = false;
								huxingCheckArr.map((huxing) => {
									if (item.id == huxing) {
										item.isChecked = true;
									}
								});
							});
						} else {
							this.huxingLength = 0;
						}
					}
					if (res.data.personal_houses){
						this.isOpenPersonalHouses=res.data.personal_houses
						if (res.data.personal_houses==0){
							this.params.is_personal =0
						}
					}
					this.money_own = res.data.money_own //推送个人房源需要扣除的金币个数 0为不扣费 大于0 为扣除的金币个数
					this.getOptions();
				}
			);
		},
		getShareLink(){
			let link = ""
				// #ifdef H5
				link = window.location.origin+"/h5/findHouse/ai_house_search?dingyueType="+this.params.house_type
				// #endif
				// #ifndef H5
				link = config.apiDomain + "/findHouse/ai_house_search?dingyueType="+this.params.house_type
				// #endif
				return link
		},
		getOptions() {
			this.$ajax.get("House/subscriptionOption", {}, (res) => {
				if (res.data.code == 1) {
					this.saleArray = res.data.sale_price;
					this.rentArray = res.data.rent_price;
					this.newHousePriceList = res.data.build_price;
					this.mianjiArray = res.data.mianji;
					res.data.rent_price.map((item) => {
						if (this.params.rent_price && this.params.rent_price == item.id) {
							this.chuzu_name = item.name;
							item.isChecked =true
						} else if (!this.params.rent_price) {
							this.chuzu_name = "不限";
						}
					});
					res.data.sale_price.map((item) => {
						if (this.params.sale_price && this.params.sale_price == item.id) {
							this.chushou_name = item.name;
							item.isChecked =true
						} else if (!this.params.sale_price) {
							this.chushou_name = "不限";
						}
					});
					res.data.build_price.map((item) => {
						if (this.params.build_price && this.params.build_price == item.id) {
							this.xinfang_name = item.name;
							item.isChecked =true
						} else if (!this.params.build_price) {
							this.xinfang_name = "不限";
						}
					});
					res.data.mianji.map((item) => {
						if (this.params.mianji && this.params.mianji == item.id) {
							this.mianji_name = item.name;
							item.isChecked =true
						} else if (!this.params.mianji) {
							this.mianji_name = "不限";
						}
					});
				}
			});
		},
		toArea() {
			this.$navigateTo("/findHouse/search_area?ids=" + this.params.area_ids);
		},
		toXiaoqu() {
			this.$navigateTo("/findHouse/community?id=" + this.params.id);
		},
		confirm(e) {
			if (this.isClicking) return;
			this.isClicking = true;
			setTimeout(() => {
				this.isClicking = false;
			}, 500);
			let arr = [];
			if (e.type == "leixing") {
				if (e.data.length > 0) {
					this.fangyuanTypeList = e.list;
					e.data.map((item) => {
						arr.push(item.id);
					});
					if (arr.includes(1)) {
						this.params.is_rent = 1;
					} else {
						this.params.is_rent = 0;
					}
					if (arr.includes(2)) {
						this.params.is_sale = 1;
					} else {
						this.params.is_sale = 0;
					}
				} else {
					this.params.is_sale = 0;
					this.params.is_rent = 0;
				}
			} else if (e.type == "huxing") {
				if (e.data.length > 0) {
					this.huxingList = Object.assign(this.huxingList, e.data);
					e.data.map((item) => {
						arr.push(item.id);
					});
					this.huxingLength = arr.length;
					this.params.shi = arr.join(",");
				}
			}else if (e.type=='zongjia'){ //二手房总价
					this.params.sale_price = e.data[0].id;
					this.chushou_name = e.data[0].name;
					this.params.sale_price_section = e.data[0].value;
			}else if (e.type=='zujin'){ //出租单价
					this.params.rent_price = e.data[0].id;
					this.chuzu_name = e.data[0].name;
					this.params.rent_price_section = e.data[0].value;
			}else if (e.type=='mianji'){ //面积
					this.params.mianji = e.data[0].id;
					this.mianji_name = e.data[0].name;
					this.params.mianji_section = e.data[0].value;
			}else if (e.type=='danjia'){ //新房单价
					this.params.build_price = e.data[0].id;
					this.xinfang_name = e.data[0].name;
					this.params.build_price_section = e.data[0].value;
			}
			this.$forceUpdate()
			this.$refs[e.type].hide();
		},
		cancel(e) {
			this.$refs[e.type].hide();
		},
		guanzhugongzhonghao(){
			setTimeout(() => {
				this.$refs.qrcode_popup.show()
			}, 200);
			
			
		},
		hideQrcode(){
			this.$refs.qrcode_popup.hide()
		},
		 // 保存二维码
		saveQrcode(){
			uni.request({
					url:this.qrcode,
					method:'GET',
					responseType: 'arraybuffer',
					success:(res)=>{
							let base64 = uni.arrayBufferToBase64(res);
							const userImageBase64 = 'data:image/jpg;base64,' + base64;
							uni.saveImageToPhotosAlbum({
									filePath: userImageBase64,
									success: result => {
											uni.showToast({
													title: '保存成功，在微信从相册中选取识别吧',
													icon: 'none',
													duration: 4000
											})
									},
									fail: err => {
											console.log(err)
											uni.showToast({
													title: '保存失败，请重试',
													icon: 'none'
											})
									}
							})
					}
			}); 
		},
		save() {
			if (!this.params.house_type) {
				uni.showToast({
					title: "请选择房源类型",
					icon: "none",
				});
				return;
			}
			if (!this.params.cid&&!this.params.area_ids&&this.params.house_type==1) {
				uni.showToast({
					title: "小区和区域最少选择一个",
					icon: "none",
				});
				return;
			}
			if (!this.params.is_agent&&!this.params.is_personal&&this.params.house_type==1) {
				uni.showToast({
					title: " 经纪人房源/个人房源必须有一个为开启状态",
					icon: "none",
				});
				return;
			}
			uni.showLoading({
				title: '正在提交'
			});
			if (this.isClicking) return;
			this.isClicking = true;
			let params = this.params;
			if (params.house_type == 1) {
				//二手房
				delete params.build_price;
				delete params.build_price_section;
				if (params.is_sale == 0) {
					delete params.sale_price;
					delete params.sale_price_section;
				}
				if (params.is_rent == 0) {
					delete params.rent_price;
					delete params.rent_price_section;
				}
			} else {
				//新房
				delete params.is_sale;
				delete params.sale_price;
				delete params.sale_price_section;
				delete params.is_rent;
				delete params.rent_price;
				delete params.rent_price_section;
				delete params.mianji_section;
				delete params.mianji;
				delete params.is_agent;
				delete params.is_personal;
				delete params.cid;
				delete params.shi;
			}
			this.$ajax.post(
				"House/subscribe",
				params,
				(res) => {
					uni.hideLoading();
					uni.showToast({
						title: res.data.msg,
						icon: "none",
					});
					if (res.data.code == 1) {
						
						uni.removeStorageSync("communityObj");
						setTimeout(() => {
							// this.$navigateBack();
							uni.switchTab({
								url: '/pages/index/find_house'
							});
							setTimeout(() => {
								uni.$emit("getListAgain");
							}, 300);
						}, 1000);
					} else {
						this.isClicking = false;
						if (res.data.is_follow==0){
							this.showTips =true
							this.qrcode =res.data.gzhewm
							if(res.data.siteName){
								this.siteName =res.data.siteName
							}
							if(res.data.icon){
								this.siteImg =res.data.icon
							}
							setTimeout(() => {
								this.guanzhugongzhonghao()
							}, 300);
							console.log(this.isClicking);
						}
					}
				},
				(err) => {
					this.isClicking = false;
					console.log(err);
				}
			);
		},
	},
};
</script>

<style scoped lang="scss">
.page {
	background: #f3f2f7;
	z-index: 1;
	&.padtop {
		padding-top: 93rpx;
	}
}
.flex-row {
	display: flex;
	flex-direction: row;
}
.err-tip{
		position: fixed;
		top:88rpx;
		left: 0;
		right: 0;
		z-index: 111;
    justify-content: space-between;
		align-items: center;
    padding: 10upx 24upx;
    background-color: #f44;
    color: #fff;
    .tip-btn{
        padding: 6upx 12upx;
        border: 1upx solid rgb(255, 103, 103);
        border-radius: 6upx;
        font-size: 26upx;
    }
}

.qrcode-box{
	position: relative;
	margin-top: 15vh;
	.tip_guanzhu{
    text-align: center;
		color: #fff;
		font-size: 32rpx;
		line-height: 54rpx;
    padding: 20rpx 40rpx;
		white-space: nowrap;
    flex-direction: column;
		margin: 60rpx auto;
		max-width:584rpx;
		box-sizing: border-box;
		overflow:hidden ;
    z-index: 10;
		border-radius: 10rpx;
		text{
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
		}
		.img_c{
			width: 120rpx;
			height: 120rpx;
			position: absolute;
			top: -60rpx;
			left: 50%;
			transform: translateX(-50%);
			margin: 0 auto ;
			border-radius: 100%;
			overflow: hidden;
			image{
				width: 100%;
				height: 100%;
			}
		}
	.img-box{
		width: 584rpx;
		margin: auto;
		background-position: top;
		background-size: contain;
		background-repeat: no-repeat;
		border-radius: 30rpx;
		overflow: hidden;
		text-align: center;
		
		.title{
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
			color: #333;
		}
		.tip{
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			color: #666;
		}
	}
	.qrcode{
		width: 336rpx;
		height: 336rpx;
	}
	.icon-box{
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
}
.tips {
	position: fixed;
	top: 44px;
	left: 0;
	right: 0;
	background: #fff;
	align-items: center;
	z-index: 10;
	.tips-box{
		margin: 0 48rpx;
		padding: 24rpx;
		font-size: 22rpx;
		align-items: center;
		background: #F5F6FA;
	}
	image{
		width: 36rpx;
	}
	.tip-text{
		font-size: 22rpx;
	}
	.tip-btn{
		padding: 10rpx 20rpx;
		background: #fb656a;
		color: #fff;
		font-size: 22rpx;
		border-radius: 40rpx;

	}
	.text {
		margin-left: 12rpx;
		color: #8a929f;
		font-size: 22rpx;
	}
}
.radio_box {
	align-items: center;
	justify-content: space-between;
	background: #fff;
	margin-bottom: 20rpx;
	padding: 48rpx;
	.radio_box_item {
		box-sizing: border-box;
		position: relative;
		flex: 0 0 48%;
		background: #ffffff;
		padding: 26rpx 26rpx 26rpx 46rpx;
		border: 2rpx solid #dde1e9;
		border-radius: 16rpx;
		&.active {
			border: 2rpx solid #fb656a;
		}
		.radio_box_item_top {
			align-items: center;
			margin-bottom: 12rpx;
			.line {
				width: 8rpx;
				height: 30rpx;
				margin-right: 6rpx;
				background-image: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
				&.ershou {
					background-image: linear-gradient(135deg, #ffa533 0%, #fe6c17 100%);
				}
			}
			.title {
				font-size: 16px;
				color: #2e3c4e;
				font-weight: 600;
			}
		}
		.radio_box_item_bottom {
			font-size: 28rpx;
			color: #8a929f;
		}
		.radio_box_bg {
			position: absolute;
			bottom: 26rpx;
			right: 26rpx;
			width: 64rpx;
			height: 64rpx;
			overflow: hidden;
			image {
				width: 100%;
			}
		}
	}
}
.box {
	background: #fff;
	margin-bottom: 24rpx;
	.box_row {
		padding: 24rpx 32rpx;
		align-items: center;
		justify-content: space-between;
		&_icon {
			width: 44rpx;
			height: 44rpx;
			overflow: hidden;
			margin-right: 20rpx;
			image {
				width: 100%;
			}
		}
		&_name {
			font-size: 28rpx;
			color: #000;
		}
		&_right {
			&_name {
				color: #a0a0a0;
				font-size: 28rpx;
			}
			&_icon {
			}
			&_img {
				width: 92rpx;
				height: 60rpx;
				overflow: hidden;
				image {
					width: 100%;
				}
			}
		}
		.box_row_scroll {
			display: flex;
			justify-content: center;
		}
	}
}
.btn {
	margin: 48rpx 96rpx;
	background: #ff656b;
	border-radius: 10rpx;
	color: #fff;
	font-size: 28rpx;
	text-align: center;
	padding: 26rpx 0;
}
.model_box{
	.model_ask{
		text-align: center;
		font-size: 32rpx;
		margin-bottom: 20rpx;
	}
	.model_tip{
			color: #fb656a;
			font-size: 28rpx;
		}
}
</style>
