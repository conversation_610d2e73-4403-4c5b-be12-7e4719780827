<template>
	<view class="box flex-box bottom-line">
		<cover-view class="box-top" :class="{'bottom-line':item.jzmj||item.zzts||item.fzzts}">
			<cover-view class="item-title flex-row">
				{{ item.xkzh ||item.tdbh||''}}
				<cover-view class="item-title-yijia" v-if="type=='tudi'&&item.yjl>0">溢价{{item.yjl||''}}%</cover-view>
			</cover-view>
			<cover-view class="item-info">
				{{ item.xmmc||item.tdwz ||'' }}
			</cover-view>
			<cover-view class="item-company flex-row">
				<cover-view>{{ item.gsmc||item.cjdw ||'' }}</cover-view>
				<block v-if ="type=='yushou'">
					<cover-view v-if="item.areaname" class="fenge">|</cover-view>
					<cover-view v-if="item.areaname"> {{ item.areaname ||'' }}</cover-view>
				</block>
				<block v-if ="type=='tudi'">
					<cover-view v-if="item.yongtu" class="fenge">|</cover-view>
					<cover-view v-if="item.yongtu"> {{ item.yongtu ||'' }}</cover-view>
				</block>
				
			</cover-view>
		</cover-view>
		<cover-view class="item-box">
			<cover-view class="flex-box data-box">
				<block v-if ="type =='yushou'">
					<cover-view class="flex-1 text-center data-top" v-if ="item.jzmj"  :class='{"fenge-line":isShowTaoshu }'>
						<cover-view class="data-data "
							><cover-view class="data-datas" :class='{"red":red=="1"}'>{{ item.jzmj||0 }}</cover-view
							></cover-view
						>
						<cover-view class="data-title">总面积 m²</cover-view>
					</cover-view>
					<cover-view class="flex-1 text-center data-top" v-if ="isShowTaoshu" :class='{"fenge-line":item.fzzts>=0 }'>
						<cover-view class="data-data flex-box"
							><cover-view class="data-datas" :class='{"red":red=="2"}'> {{ item.zzts||0 }}</cover-view
							>套</cover-view
						>
						<cover-view class="data-title">住宅</cover-view>
					</cover-view>
					<cover-view class="flex-1 text-center data-top" v-if ="isShowTaoshu">
						<cover-view class="data-data flex-box"
							><text class="data-datas" :class='{"red":red=="3"}'>{{ item.fzzts ||0 }}</text
							>套</cover-view
						>
						<cover-view class="data-title">非住宅</cover-view>
					</cover-view>
				</block>
				<block v-if ="type =='tudi'&&item.zt==1">
					<cover-view class="flex-1 text-center data-top" v-if ="item.mushu"  :class='{"fenge-line":item.chengjiaojia}'>
					<cover-view class="data-data flex-row"
						>约<cover-view class="data-datas" :class='{"red":red=="2"}'>{{ item.mushu ||'' }}</cover-view
						>亩</cover-view
					>
					<cover-view class="data-title">成交面积</cover-view>
				</cover-view>
				<cover-view class="flex-1 text-center data-top" v-if ="item.chengjiaojia" :class='{"fenge-line":item.loumianjia>0 }'>
					<cover-view class="data-data flex-row"
						><cover-view class="data-datas" :class='{"red":red=="1"}'>{{ item.chengjiaojia ||''}}</cover-view
						></cover-view
					>
					<cover-view class="data-title">成交价(万元)</cover-view>
				</cover-view>
				<cover-view class="flex-1 text-center data-top" v-if ="item.loumianjia>0">
					<cover-view class="data-data flex-row"
						>约<cover-view class="data-datas" :class='{"red":red=="3"}'>{{ item.loumianjia ||''}}</cover-view
						></cover-view
					>
					<cover-view class="data-title">楼面价(元/m²)</cover-view>
				</cover-view>
				</block>
			</cover-view>
		</cover-view>
		<cover-view class="jiangbei" v-if="idx>=0&&idx<=2">
			<cover-image :src="imgSrc" mode="widthFix"></cover-image>
		</cover-view>
		<cover-view class="biaoqian" v-if="idx>2" :style ="'backgroundImage:url('+biaoqianSrc+')'">
			{{idx+1}}
		</cover-view>
	</view>
</template>

<script>
import {config} from '@/common/config'
export default {
	props: {
		// label:String,
		// name:String,
    item: Object,
    red:{
        type:Number,
        default:0
    },
		idx:{
			type:Number,
            default:-1
		},
		type:{
			type:String,
      default:"yushou"
		}
        
	},
	data() {
		return {};
	},
	computed: {
		imgSrc(){
			return config.imgDomain+'/images/new_icon/record/icon_'+(this.idx+1)+'@2x.png'
		},
		biaoqianSrc(){
			return config.imgDomain+'/images/new_icon/record/biaoqian.png'
		},
		isShowTaoshu() { // 是否开启聊天功能
            return this.$store.state.isShowTaoshu
        },
	},
	methods: {},
};
</script>

<style lang="scss">
.flex-row{
  display: flex;
}
.data-box {
	// padding: 32upx 0;
	background-color: #ffffff;
	.data-top{
		margin: 32rpx 0;
	}
	.data-title {
		margin-bottom: 10upx;
		font-size: $uni-font-size-sm;
		color: #999;
	}
	.data-data {
		font-size: $uni-font-size-sm;
		// font-weight: bold;
		color: #999;
		margin-bottom: 20rpx;
    justify-content: center;
    align-items:center;

	}
	.data-datas {
		font-size: 38upx;
		// font-weight: bold;
		color: #333;
	}
	.red {
		color: $uni-color-primary;
	}
}
.box {
	flex-direction: column;
	padding: 24rpx 24rpx 0 24rpx;
	background: #ffffff;
	border: 1px solid rgba(216,216,216,1);
	box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
	border-radius: 8px;
	position: relative;
	.jiangbei{
		position: absolute;
		top: 0;
		right:24rpx;
		height: 50rpx;
		width: 50rpx;
		image{
			width: 100%;
			height: 100%;
		}
	}
	.biaoqian{
		position: absolute;
		text-align: center;
		top:0;
		right:24rpx;
		height: 50rpx;
		width: 50rpx;
		padding-top: 4rpx;
    	box-sizing: border-box;
		background-repeat: no-repeat;
		background-size: 100% 100%;
		color: #fff;
		
	}
	.box-top {
		padding-bottom: 24rpx;
	}
	.item-title {
		font-size: 16px;
		color: #333333;
		.item-title-yijia{
			margin-left: 24rpx;
			font-size: 22rpx;
			color: #333;

		}
	}
	.item-info {
		margin: 16rpx 0;
		font-size: 14px;
		color: #666666;
		text-overflow: ellipsis;
	}
	.item-company {
		font-size: 14px;
		color: #999999;
		.fenge{
			margin: 0 10rpx;
		}
	}
}
.fenge-line{
	position: relative;
	&:after{
		content: "";
		position: absolute;
		top: 20%;
		bottom: 20%;
		right: 0;
		width: 1px;
		-webkit-transform: scaleX(.5);
		transform: scaleX(.5);
		background-color: $uni-border-color;
	}
}
</style>
