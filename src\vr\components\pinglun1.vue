<template>
	<view>

			<view class="comment">
				<view v-if="commentList.length === 0" class="noComment">
					<text>暂无评论</text>
				</view>
				<!-- 父评论 -->
				<view class="vfor" v-for="(item,index) in commentList" :key="index">
					<view class="item_wrap">
						<view class="left">
							<view class="avatar">
								<image width="58rpx" height="58rpx"  :src="item.avatar"></image>
							</view>
							<view class="nick">
								<text>{{item.nickname}}</text>
							</view>
							<view class="ptime">
								<text>{{item.createTime}}</text>
							</view>
						</view>
						<!-- <view class="right">
							<view class="likes">
								<text v-if="item.likes === 0 || item.likes === null"></text>
								<text v-else>{{item.likes}}</text>
								<u-icon name="thumb-up-fill" :color="status.commentLike[index]?'#ff6b6b':'#3d3d3d'" size="35" @click="likeComment(item.id,index)"></u-icon>
							</view>
						</view> -->
					</view>
					<view class="content" @click="reply(item.nickname,item.id,item.uid)">{{item.content}}</view>
					<view class="reply" v-if="item.replies">
						<!-- <view class="hasReply" v-if="!item.isShow" @click="showComment(item)"> -->
						<view class="hasReply" @click="changeStatus(index)" v-if="!scopesDefault[index]">
							<text>{{item.replies}}条回复</text>
							<!-- <u-icon name="arrow-right" color="#999aaa" size="28"></u-icon> -->
						</view>
						<view v-if="scopesDefault[index]">
							<view class="vfor2" v-for="(item2,index2) in item.commentChildren" :key="index2">
								<view class="item_wrap2">
									<view class="left2">
										<view class="avatar2">
											<image width="58rpx" height="58rpx" shape="circle" :src="item2.avatar"></image>
										</view>
										<view class="nick2">
											<text>{{item2.nickname}}</text>
										</view>
										<view class="ptime2">
											<text>{{item2.createTime}}</text>
										</view>
									</view>
								</view>
								<view class="content2" @click="reply(item2.nickname,item2.id,item2.uid)">
									{{item2.content}}
								</view>
								<my-tree :commentList="item2.commentChildren" :nid="item2.nid" :uid="uid" @updatePage="updatePage"></my-tree>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- <u-mask :show="isInput" @click="maskClick">
				<view class="commentInputView">
					<view class="inputView">
						<u-input v-model="wContent" clearable="false" :focus="focus" confirm-type="评论" @confirm="writePComment"
						 :placeholder="'回复'+nickname" />
					</view>
				</view>
			</u-mask> -->
			<!-- <u-action-sheet :list="myList" v-model="mySheetShow" :cancel-btn="true" @click="mySheetClick()"></u-action-sheet> -->
			<!-- <u-action-sheet :list="otherList" v-model="otherSheetShow" :cancel-btn="true" @click="otherSheetClick()"></u-action-sheet> -->
			<!-- <view class="writeComment">
				<view class="write_wrap">
					<view class="write">
						<u-input v-model="wContent" :clearable="false" confirm-type="评论" @confirm="writeComment" :type="text" placeholder="缺少你的神评..." />
					</view>
					<view class="w_zan">
						<u-icon name="thumb-up-fill" :color="status.like?'#ff6b6b':'#3d3d3d'" size="38" @click="likeNews"></u-icon>
					</view>
					<view class="w_star">
						<u-icon name="star-fill" :color="status.collection?'#ff6b6b':'#3d3d3d'" size="38" @click="starNews"></u-icon>
					</view>
					<view class="w_share">
						<button open-type="share">
							<u-icon name="zhuanfa" color="#3d3d3d" size="38"></u-icon>
						</button>
					</view>
				</view>
			</view> -->
	</view>
</template>

<script>
	import myTree from '../components/pinglun2.vue';
	var timer = null,
		_self;
	export default {
		components: {
			myTree
		},
    props:{
      list:{
        type:Array,
        default:()=>{}
      }
    },
		data() {
			return {
				id: '',
				uid: '',
				news: '',
				city: '',
				isShow: false,
				wContent: '',
				commentList: [],
				status: {},
				isInput: 0,
				nickname: '',
				focus: false,
				cid: '',
				scopesDefault: [],
				scopes: [],
				myList: [{
					text: '回复'
				}, {
					text: '删除'
				}],
				otherList: [{
					text: '回复'
				}, {
					text: '举报'
				}],
				mySheetShow: false,
				otherSheetShow: false
			}
		},
		created(options) {
			// this.id = options.id;
			// this.uid = uni.getStorageSync('uid');
			_self = this;
			
			//为了不显示undefined，添加了定时来缓解加载的时间
			// if (timer != null) {
			// 	clearTimeout(timer);
			// }
			// timer = setTimeout(function() {
				_self.init();
			// }, 300);
		},
		methods: {
			init() {
				// this.$u.api.getCommentTree({
				// 	nid: this.id
				// }).then(res => {
				// 	this.commentList = res.data;
				// 	this.changeCreateTime(this.commentList);
				// 	this.scope();
				// })
				// this.$u.api.getNewsUserStatus({
				// 	nid: this.id
				// }).then(res => {
				// 	this.status = res.data;
				// })
        console.log(this.list,121);
        if(this.list)  {
          this.commentList = this.list
          this.scope()
        }
        
			},
			// 改变评论时间
			changeCreateTime(e) {
				e.map(item => {
					item.createTime = time.timeago(item.createTime);
					if (item.commentChildren.length !== 0) {
						this.changeCreateTime(item.commentChildren)
					}
					return item;
				})
			},
			// 写评论
			writeComment() {
				
					this.$u.api.writeComment({
						nid: this.id,
						pid: 0,
						content: this.wContent
					}).then(res => {
						if (res.msg === '评论成功') {
							this.wContent = '';
							this.$refs.uToast.show({
								title: '评论成功',
								type: 'success'
							})
							this.init()
						} else {
							this.$refs.uToast.show({
								title: '评论出错，请登录',
								type: 'warning'
							})
						}
					})
			},
			// 回复评论
			reply(nickname, cid, uid) {
				this.nickname = nickname;
				this.cid = cid;
        this.mySheetClick (0)
				// if (uid === this.uid) {
				// 	this.mySheetShow = true;
				// } else {
				// 	this.otherSheetShow = true;
				// }
			},
			// 点击用户评论弹出遮罩
			maskClick() {
				this.isInput = false;
				this.focus = false;
				this.wContent = '';
			},
			// 点击操作菜单
			mySheetClick(index) {
				if (index === 0) {
					this.isInput = 1;
					this.focus = 1;
				} else if (index === 1) {
					this.$u.api.deleteNewsCommentById({
						id: this.cid
					}).then(res => {
						if (res.msg === '删除成功') {
							this.$refs.uToast.show({
								title: '删除成功',
								type: 'success'
							})
							this.init();
						}
					})
				}
			},
			otherSheetClick(index) {
				if (index === 0) {
					this.isInput = 1;
					this.focus = 1;
				} else if (index === 1) {
					uni.navigateTo({
						url: `../../mePac/complain/complain?id=${this.cid}&module=资讯评论`
					})
				}
			},
			// 回复父级评论
			writePComment() {
				this.$u.api.writeComment({
					nid: this.id,
					pid: this.cid,
					content: this.wContent
				}).then(res => {
					if (res.msg === '评论成功') {
						this.wContent = '';
						this.isInput = false;
						this.$refs.uToast.show({
							title: '评论成功',
							type: 'success'
						})
						this.init();
					} else {
						this.$refs.uToast.show({
							title: '评论出错，请登录',
							type: 'warning'
						})
					}
				})
			},
			// 展开回复数按钮
			changeStatus(index) {
				if (this.scopesDefault[index] == true) {
					this.$set(this.scopesDefault, index, false)
				} else {
					this.$set(this.scopesDefault, index, true)
				}
			},
			scope() {
				this.commentList.forEach((item, index) => {
					this.scopesDefault[index] = false
					if ('commentChildren' in item) {
						this.scopes[index] = true
					} else {
						this.scopes[index] = false
					}
				})
			},
			updatePage() {
				this.init();
			},
		}
	}
</script>
<style scoped lang="scss">
.comment {
  max-height: 480rpx;
  overflow-y: auto;
}
.vfor {
		.item_wrap {
			display: flex;
			justify-content: space-between;
			margin-top: 20rpx;

			.left {
				display: flex;
				align-items: center;

				.avatar {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          overflow: hidden;
          image {
            width: 100%;
          }
        }

				.nick {
					text {
						font-weight: bold;
						font-size: 37rpx;
						margin-left: 20rpx;
					}
				}

				.ptime {
					text {
						font-size: 27rpx;
						margin-left: 10rpx;
						color: #949494;
					}
				}
			}
		}

		.content {
			margin-left: 82rpx;
			font-size: 33rpx;
			width: 580rpx;
			display: flex;
			align-items: center;
			.reply_to{
				color: #45aaf2;
				margin-right: 10rpx;
			}
		}
	}
.vfor2 {
  margin-left: 60rpx;
		.item_wrap2 {
			display: flex;
			justify-content: space-between;
			margin-top: 20rpx;

			.left2 {
				display: flex;
				align-items: center;

				.avatar2 {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          overflow: hidden;
          image {
            width: 100%;
          }
        }

				.nick2 {
					text {
						font-weight: bold;
						font-size: 37rpx;
						margin-left: 20rpx;
					}
				}

				.ptime2 {
					text {
						font-size: 27rpx;
						margin-left: 10rpx;
						color: #949494;
					}
				}
			}
		}

		.content2 {
			margin-left: 82rpx;
			font-size: 33rpx;
			width: 580rpx;
			display: flex;
			align-items: center;
			.reply_to2{
				color: #45aaf2;
				margin-right: 10rpx;
			}
		}
	}
	.commentInputView {
		width: 750rpx;
		height: 100rpx;
		background-color: #fff;
		border-top: 1rpx solid #eee;
		position: fixed;
		bottom: 0;
		display: flex;
		align-items: center;

		.inputView {
			margin: 30rpx 20rpx;
			background-color: #f4f4f4;
			height: 60rpx;
			width: 650rpx;
			border-radius: 30rpx;
			padding-left: 50rpx !important;
		}
	}
</style>
