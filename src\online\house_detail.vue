<template>
<view class="house_detail">
    <!-- 电子沙盘 -->
    <view class="loudong_card" v-if="sand_info.width">
        <view class="title bottom-line">
            <text>楼栋位置</text>
        </view>
        <movable :map_img="sand_info.img" :map_info="sand_info" :mark_point="sand_point" :show_screen='false' @clickPoint="onClickPoint"></movable>
        <view class="view_img" @click="prevImg()">
            <image class="huxing" src="https://images.tengfangyun.com/images/icon/huxing.png"></image>
            <view class="text">查看户型</view>
        </view>
    </view>
    <!-- <view class=img-box>
        <image :src="info.pic | imgUrl('w_8601')" mode="aspectFit"></image>
    </view> -->
    <view class="countdown-box flex-box">
        <view class="label">
            <my-icon type="naozhong" size="20" color="#fff"></my-icon>
            <text style="margin-left:10rpx;">{{info.is_start==0?"距离开始时间":(info.is_start==1?"距离结束时间":"已结束")}}</text>    
        </view>
        <view class="time-box">
            <text class="time">{{countdown.hours}}</text>
            <text>时</text>
        </view>
        <view class="colon">:</view>
        <view class="time-box">
            <text class="time">{{countdown.minutes}}</text>
            <text>分</text>
        </view>
        <view class="colon">:</view>
        <view class="time-box">
            <text class="time">{{countdown.seconds}}</text>
            <text>秒</text>
        </view>
    </view>
    <view class="house_info bottom-line onpage card">
        <view class="info_data">
            <view class="info_item house_name flex-box">
                <text>{{info.title}}</text>
                <view class="view_img" @click="prevImg()">
                    <image class="huxing" src="https://images.tengfangyun.com/images/icon/huxing.png"></image>
                </view>
            </view>
            <view class="info_item house_price">
                <text>总房价：</text>
                <text class="price">{{info.discount_price}}</text>
                <text class="del" v-if="info.price">原价：{{info.price}}</text>
                <text class="tip">{{info.tip||''}}</text>
            </view>
            <view class="info_item" v-if="info.discount_total">已优惠：{{info.discount_total}}</view>
            <view class="info_item" v-if="info.danjia">销售单价：{{info.danjia}}{{info.danjia_unit||'元/m²'}}</view>
            <view class="info_item">房源户型：{{info.huxing||""}}</view>
            <view class="info_item">
                <text>建筑面积约：{{info.jzmj||""}}</text>
                <text style="margin-left:20rpx" v-if="info.tnmj">套内面积约：{{info.tnmj||""}}</text>
            </view>
        </view>
    </view>
    <view class="info_list bottom-line card">
        <view class="title bottom-line">
            <text>户型信息</text>
        </view>
        <view class="row w-100 bottom-line" v-for="(detail, index) in details" :key="index">
            <text class="label">{{detail.name}}</text>
            <text class="text ">{{detail.value}}</text>
        </view>
    </view>
    <!-- <view class="info_list bottom-line card">
        <view class="title bottom-line">
            <text>房价信息</text>
        </view>
        <view class="row w-100 bottom-line" v-for="(detail, index) in price_details" :key="index">
            <text class="label">{{detail.name}}</text>
            <text class="text ">{{detail.value}}</text>
        </view>
    </view> -->
    <view class="info_list bottom-line card">
        <view class="title bottom-line">
            <text>订单记录</text>
        </view>
        <view class="row w-100 bottom-line" v-for="(item, index) in order_logs" :key="index">
            <text class="order_log">{{item.text||item}}</text>
        </view>
    </view>

    <my-popup ref="popup" position="bottom">
        <view class="user_info">
            <view class="title bottom-line">
                <text>请核实您选择的住房信息</text>
                <view class="close" @click="closePopup()"><my-icon type="jiahao" color="#666" size="22"></my-icon></view>
            </view>
            <view class="house_info bottom-line">
                <view class="info_data">
                    <view class="info_item house_name">{{info.title}}</view>
                    <view class="info_item">总房价：{{info.discount_price}}</view>
                    <view class="info_item" v-if="info.discount_total">已优惠：{{info.discount_total}}</view>
                    <view class="info_item" v-if="info.danjia">销售单价：{{info.danjia}}{{info.danjia_unit||'元/m²'}}</view>
                    <view class="info_item">房源户型：{{info.huxing}}</view>
                    <view class="info_item">建筑面积约：{{info.jzmj}}</view>
                </view>
            </view>
            <view class="choose_advise flex-box bottom-line" v-if="has_adviser">
                <view class="label">专属顾问</view>
                <view class="value" :class="{placeholder:!adviser.id}" @click="toChooseAdviser()">{{adviser.cname||'请选择专属顾问'}}</view>
                <!-- <my-input label="专属顾问" disabled small placeholder="请选择专属顾问" :value="adviser.cname"></my-input> -->
            </view>
            <my-input :value="user_info.uname" small label="姓名" name="uname" @input="handelInput"></my-input>
            <my-input :value="user_info.tel" small label="手机号" name="tel" type="number" @input="handelInput"></my-input>
            <my-input v-if="info.must_sfz" small label="身份证号" name="id_card" @input="handelInput"></my-input>
            <view class="row"></view>
            <!-- <my-input :value="user_info.id_card" small label="身份证号" name="id_card" type="idcard" @input="handelInput"></my-input> -->
            <view class="sub-bar flex-box">
                <view class="dingjin top-line" :class="{has_coupon:coupon.money}">
                    <text>定金：</text>
                    <text class="price">￥{{info.earnest_money}}</text>
                    <text v-if="coupon.money" class="coupon">(定金可再抵{{coupon.money}})</text>
                </view>
                <view class="flex-1 btn" @click="subOrder()">确认提交</view>
            </view>
            <!-- <view class="btn-box flex-box">
                <view class="dingjin">定金：￥1000</view>
                <view class="btn flex-1" @click="subOrder()">确认提交</view>
            </view> -->
        </view>
    </my-popup>

    <view class="flex-box bottom-bar top-line">
        <view class="flex-5 guanzhu right-line" @click="handleConcern">
            <template v-if="info.is_flow">
                <my-icon type="xihuan" color="#f65354"></my-icon>
                <text>关注({{info.flow_number}})</text>
            </template>
            <template v-else>
                <my-icon type="xihuan1" color="#f65354"></my-icon>
                <text>关注({{info.flow_number}})</text>
            </template>
        </view>
        <view class="flex-4 flex-box text-center to-consu" @click="toSignUp()">
            <view>意向报名</view>
        </view>
        <view class="flex-4 flex-box text-center to-tel" @click="toSubOrder()">
            <view>确认选房</view>
        </view>
    </view>
    <chat-tip></chat-tip>
</view>
</template>

<script>
import myIcon from "../components/icon.vue"
import myInput from "../components/form/myInput.vue"
import myPopup from "../components/myPopup.vue"
import movable from '../components/moveableScale.vue'
import bullet from './components/bullet.vue'
import {
    navigateTo,
    formatImg,
} from '../common/index.js'
import {wxShare} from '../common/mixin'
import {mapState} from 'vuex'
export default {
    data() {
        return {
            sand_info:{},
            sand_point:[],
            coupon:{
                
            },
            info: {
                title:"",
                price:"",
                discount_total:"",
                huxing:"",
                jzmj:"",
                flow_number:"",
                earnest_money:""
            },
            details:[],
            price_details:[],
            countdown: {
                hours: "00",
                minutes: "00",
                seconds: "00"
            },
            user_info:{},
            has_adviser:0,
            adviser:{
                id:""
            },
            order_logs:[]
        }
    },
    computed: {
        ...mapState(['online_inviter'])
    },
    mixins:[wxShare],
    components: {
        movable,
        myIcon,
        myInput,
        myPopup,
        bullet
    },
    onLoad(options) {
        if (options.id && options.online_id) {
            this.house_id = options.id
            this.online_id = options.online_id
            this.getData()
        }
        if(options.inviter_id){
            this.inviter_id = options.inviter_id
            this.online_inviter = {
                inviter_id: options.inviter_id,
                online_id: this.online_id
            }
        }
        if(this.online_id===this.online_inviter.online_id){
            this.inviter_id = this.online_inviter.inviter_id
        }
        uni.$on('chooseAdviser', (e)=>{
            this.adviser = e
        })
        uni.$on("getDataAgain",this.getData)
    },
    onUnload(){
        uni.$off('chooseAdviser')
        uni.$off("getDataAgain")
        this.$store.state.allowOpen = true
    },
    filters:{
        imgUrl(val,param){
            return formatImg(val,param)
        }
    },
    methods: {
        getData() {
            this.share = {}
            this.$ajax.get('online/houseDetail.html', {
                house_id: this.house_id,
                online_id: this.online_id
            }, res => {
                this.my_id = res.data.uid || 0 //记录自己的id，分享的时候需要携带此参数
                if (res.data.code === 1) {
                    if(res.data.info.title){
                        uni.setNavigationBarTitle({
                            title:res.data.info.title
                        })
                    }
                    if(res.data.coupon){
                        this.coupon = res.data.coupon
                    }
                    this.order_logs = res.data.order_log||[]
                    this.has_adviser = res.data.hasAdviser
                    // 获取沙盘信息
                    if(res.data.sand&&res.data.sand.pic){
                        let windowWidth = this.$store.state.systemInfo.windowWidth
                        this.sand_info = {
                            img:res.data.sand.pic, // 楼盘背景
                            width:res.data.sand.width,
                            height:res.data.sand.height,
                            margin_left:"0px",
                            top:(0-res.data.sand.top+(windowWidth/3)-20>0?0:0-res.data.sand.top+(windowWidth/3)-20)+'px',
                            left:(0-res.data.sand.left+(windowWidth/2)>0?0:0-res.data.sand.left+(windowWidth/2))+'px',
                        }
                        this.sand_point = [{top:res.data.sand.top,left:res.data.sand.left,name:res.data.sand.number||'',sale_status:res.data.sand.sale_status||1,}]
                    }
                    //户型信息
                    this.info = res.data.info
                    this.details = res.data.house;
                    this.price_details = res.data.price
                    if(res.data.info.is_start==0){ //未开始
                        this.countDown(res.data.info.start_time-Date.parse(new Date())/1000)
                    }else if(res.data.info.is_start==1){ //进行中
                        this.countDown(res.data.info.end_time-Date.parse(new Date())/1000)
                    }else{ // 已结束
                        
                    }
                    if(res.data.self.id){
                        this.user_info = res.data.self
                    }
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                    setTimeout(()=>{
                        uni.navigateBack()
                    },1500)
                }
                if(res.data.share&&res.data.share.title){
                    res.data.share.link=`${window.location.origin}/h5/online/house_detail?id=${this.house_id}&online_id=${this.online_id}&inviter_id=${this.my_id}`
                    this.share = res.data.share
                    this.getWxConfig()
                }
            })
        },
        countDown(remaining_time){
            if(remaining_time<=0){
                return
            }
            // this.countdown.day = parseInt(remaining_time/86400)
            // let hours_str = remaining_time%86400
            // this.countdown.hours = parseInt(hours_str/3600)
            // let minutes_str = hours_str%3600
            this.countdown.hours = parseInt(remaining_time/3600)
            let minutes_str = remaining_time%3600
            
            this.countdown.minutes = parseInt(minutes_str/60)<10?"0"+parseInt(minutes_str/60):parseInt(minutes_str/60)
            this.countdown.seconds = minutes_str%60<10?"0"+minutes_str%60:minutes_str%60
            setTimeout(()=>{
                remaining_time--
                this.countDown(remaining_time)
            },1000)
        },
        toSubOrder(){
            this.$refs.popup.show()
        },
        closePopup(){
            this.$refs.popup.hide()
        },
        handelInput(e){
            this.user_info[e._name] = e.detail.value
        },
        /** 
         * <AUTHOR> 
         * @date 2020-02-28 14:27:24 
         * @desc 提交订单 
         */
        subOrder(){
            if(this.sub_ordering){ //防止多次点击
                return false
            }
            if(!this.user_info.uname){
                uni.showToast({
                    title:"请输入真实姓名",
                    icon:'none'
                })
                return
            }
            if(!this.user_info.tel){
                uni.showToast({
                    title:"请输入手机号",
                    icon:'none'
                })
                return
            }
            if(this.user_info.tel.length<11||this.user_info.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return
            }
            // if(this.has_adviser&&!this.adviser.id){
            //     uni.showToast({
            //         title:"请选择专属顾问",
            //         icon:"none"
            //     })
            //     return
            // }
            this.sub_ordering = true
            uni.showLoading({
                title:"正在提交订单...",
                mask:true
            })
            this.$ajax.post('online/orderDone',{house_id:this.house_id,online_id:this.online_id,tel:this.user_info.tel,uname:this.user_info.uname,id_card:this.user_info.id_card,agent_id:this.inviter_id,adviser_id:this.adviser.id},res=>{
                uni.hideLoading()
                if(res.data.code===1){
                    if(res.data.pay_status===1){ //直接支付成功（价格为0或者优惠券金额全部抵用定金）
                        uni.showToast({
                            title:res.data.msg
                        })
                        this.$refs.popup.hide()
                        return
                    }
                    navigateTo(`/online/pay?order_id=${res.data.order_id}`)
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                    if (res.data.is_limit_buy) {
                        setTimeout(()=>{
                            this.$navigateTo('/online/order_list')
                        }, 500);
                    }
                }
                setTimeout(()=>{
                    this.sub_ordering = false
                },300)
            })
        },
        /** 
         * <AUTHOR> 
         * @date 2020-02-28 14:27:09 
         * @desc 关注和取消关注 
         */
        handleConcern(){
            let api
            if(this.info.is_flow){
                api = "online_my/followHouseCancel"
            }else{
                api = "online_my/followHouse"
            }
            this.$ajax.post(api,{house_id:this.house_id},res=>{
                if(res.data.code === 1){
                    this.info.is_flow = !this.info.is_flow
                    uni.showToast({
                        title:res.data.msg||'操作成功'
                    })
                    this.info.is_flow?this.info.flow_number++:this.info.flow_number--
                }else{
                    uni.showToast({
                        title:res.data.msg||'操作失败',
                        icon: 'none'
                    })
                }
            })
        },
        prevImg(){
            uni.previewImage({
                urls:[this.info.pic]
            })
        },
        toSignUp(){
            navigateTo(`/online/sign_up?online_id=${this.online_id}&inviter_id=${this.inviter_id}`)
        },
        toChooseAdviser(){
            navigateTo(`/online/choose_adviser?online_id=${this.online_id}`)
        }
    },
    onShareAppMessage(){
        // #ifdef MP-BAIDU
        return {
            title:this.share.title,
            content:this.share.content,
            imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):"",
            path:`/online/house_detail?id=${this.house_id}&online_id=${this.online_id}&share_id=${this.my_id}`
        }
        // #endif
        // #ifdef MP-WEIXIN
        return {
            title:this.share.title,
            imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):"",
            path:`/online/house_detail?id=${this.house_id}&online_id=${this.online_id}&share_id=${this.my_id}`
        }
        // #endif
    }
}
</script>

<style lang="scss" scoped>
.house_detail{
    padding-bottom: 120rpx;
}
.loudong_card{
    position: relative;
    background-color: #fff;
    .view_img{
        position: absolute;
        bottom: 10rpx;
        right: 10rpx;
        color: #fff;
        text-align: center;
        .huxing{
            width: 60rpx;
            height: 60rpx;
        }
        .text{
            font-size: 26rpx;
        }
    }
}
.img-box {
    width: 100%;
    height: 80vw;
    background: #fff;
    font-size: 0;
    text-align: center;
    // position: relative;
    image {
        width: 100%;
        height: 100%;
        // position: absolute;
        // left: 0;
        // right: 0;
        // top: 0;
        // bottom: 0;
        // margin: auto;
    }
}


.countdown-box{
    width: 100%;
    margin-bottom: 20rpx;
    padding: 12rpx;
    box-sizing: border-box;
    height: 82rpx;
    line-height: 58rpx;
    background-color: rgba($color: $uni-color-primary, $alpha: 1);
    .label{
        color: #fff;
        padding: 0 8rpx;
    }
    .colon{
        color: #fff;
        padding: 0 8rpx;
    }
    .time-box{
        padding: 0 12rpx;
        border-radius: 8rpx;
        background-color: #fff;
        .time{
            font-size: 36rpx;
            font-weight: bold;
            color: $uni-color-primary;
        }
    }
}

.card {
    margin: 24rpx;
    // padding: 26rpx;
    border-radius: 10rpx;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 0 10px #dedede;
}

    .info_list {
        flex-wrap: wrap;
        margin-bottom: 20rpx;
        background-color: #fff;
        .w-100 {
            width: 100%;
            padding-top: 20rpx;
            padding-bottom: 20rpx;
            box-sizing: border-box;
            .label{
                display: inline-block;
                min-width: 140rpx;
            }
        }
    }

.bottom-bar{
    width: 100%;
    height: 120upx;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    z-index: 2;
    background-color: #fff;
    .to-consu {
        align-items: center;
        justify-content: center;
        color: #fff;
        margin: 20upx 24upx 20upx 20upx;
        background: #00c0eb;
        border-radius: 10upx;
        // background-color: $uni-color-primary;
    }
    .to-tel{
        align-items: center;
        justify-content: center;
        color: #fff;
        background-color: $uni-color-primary;
        margin: 20upx 24upx 20upx 20upx;
        border-radius: 10upx;
    }
    .guanzhu{
        line-height: 120rpx;
        font-size: 28rpx;
        text-align: center;
    }
}
.sub-bar{
    height: 90rpx;
    line-height: 90rpx;
    box-sizing: border-box;
    .dingjin{
        width: 38%;
        text-align: center;
        padding: 0 15rpx;
        font-size: 26rpx;
        background-color: #ffffff;
        box-sizing: border-box;
        overflow: hidden;
        &.has_coupon{
            padding: 6rpx 15rpx;
            line-height: 38rpx;
        }
        .coupon{
            font-size: 24rpx;
            color: #666;
            display: inline-block;
        }
        .price{
            font-size: $uni-font-size-blg;
            font-weight: bold;
            color: $uni-color-primary
        }
    }
    .btn{
        width: 40%;
        text-align: center;
        background-color: $uni-color-primary;
        color: #fff;
        font-size: $uni-font-size-blg;
    }
}



.house_info{
    padding: 20rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
    &.onpage{
        background-color: #fff;
        margin-bottom: 20rpx;
    }
    .info_data{
        padding-left: 13rpx;
        .info_item{
            font-size: 27rpx;
            padding: 15rpx 0;
            .price{
                color: #f65354;
            }
            .del{
                margin-left: 10rpx;
                font-size: 24rpx;
                color: #666;
                text-decoration:line-through;
            }
            &.house_name{
                font-size: 32rpx;
                font-weight: bold;
            }
            .view_img{
                width: 46rpx;
                height: 46rpx;
                margin-left: 15rpx;
                .huxing{
                    width: 100%;
                    height: 100%;
                }
            }
        }
        .house_price{
            .price{
                font-size: 36rpx;
                color: #f65354;
                font-weight: bold;
            }
            .tip{
                margin-left: 15rpx;
                font-size: 22rpx;
                color: #777;
            }
        }
    }
}

.user_info{
    background-color: #f3f3f3;
    // padding: 20rpx;
    .btn-box{
        margin-top: 30rpx;
        padding: 20rpx;
        .btn{
            padding: 25rpx;
            font-size: 32rpx;
            line-height: 1;
            background-color: #f65354;
            color: #fff;
            border-radius: 10rpx;
            text-align: center;
        }
    }
}
.title{
    padding: 20rpx 20rpx 20rpx 40rpx;
    font-size: 32rpx;
    position: relative;
    background-color: #fff;
    &::before{
        content: "";
        position: absolute;
        left:20rpx;
        top:20rpx;
        bottom:20rpx;
        width: 6rpx;
        background-color: #f65354
    }
    .close{
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        padding: 10rpx;
        transform:rotate(45deg);
    }
}

.choose_advise{
    padding: $uni-spacing-col-base;
    line-height: 62rpx;
    height: 62rpx;
    background-color: #fff;
    .label{
        min-width: 130upx;
        max-width: 220upx;
        font-size: $uni-font-size-lg;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 38upx;
        text-align-last:justify;
    }
    .value{
        font-size: 30rpx;
        flex: 1;
        height: 62upx;
        padding: 0 8upx;
        margin-right: 10upx;
        &.placeholder{
            color: #777;
        }
    }
}

.order_log{
    color: #333
}

</style>
