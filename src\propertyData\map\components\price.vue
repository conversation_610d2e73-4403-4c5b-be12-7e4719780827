<template>
  <view>
    <cover-view>
      <cover-view  class="item">
        <cover-view class="left  flex-1">
          <cover-view class="title">{{item.title}}</cover-view>
          <!-- <cover-view class="address">{{item.address}}</cover-view> -->
        </cover-view>
        <cover-view class="info">
          <cover-view class="top flex-box ">
            <cover-view class="left flex-box flex-1">
              <cover-view class="left_title  flex-box flex-1">
                小区在售房源: 
                <cover-view class="left_unit">
                  {{item.count}}套
                </cover-view>
              </cover-view>
              <cover-view class="left_title flex-box">
                均价: 
                <cover-view class="left_unit" v-if ="item.avg_price>0">
                  {{item.avg_price}}元/m²
                </cover-view>
                <cover-view class="left_unit" v-else >
                  未更新
                </cover-view>
              </cover-view>
            </cover-view>
            </cover-view>
            

             <cover-view class="top flex-box ">
            <cover-view class="left flex-box flex-1">
              <cover-view class="left_title  flex-box flex-1">
                小区出租房源: 
                <cover-view class="left_unit">
                  {{item.cz_count}}套
                </cover-view>
              </cover-view>
              <cover-view class="left_title flex-box">
                均价:  
                <cover-view class="left_unit" v-if ="item.cz_avg_price>0">
                   {{item.cz_avg_price}}元/月
                </cover-view>
                <cover-view class="left_unit" v-else >
                  未更新
                </cover-view>
              </cover-view>
            </cover-view>
            </cover-view>
          </cover-view>

        </cover-view>
        <!-- <cover-view class="distance">{{item._distance | distanceFormat}}</cover-view> -->
      </cover-view>
  </view>
</template>

<script>
export default {
  data(){
    return  {

    }
  },
  props:{
    item:Object,
    showtitle:{
      type:[Boolean , String],
      default:false
    }
  },
  filters:{
			distanceFormat(val){
				if(!val){
					return ''
				}
				if(val<1000){
					return Math.ceil(val)+'m'
				}else{
					return (val/1000).toFixed(1)+'km'
				}
			}


  }
}
</script>

<style lang ="scss" scoped>
.list_title{
	font-family: PingFangSC-Medium;
	font-size: 28rpx;
	padding: 8rpx 0;
	font-weight: 600;
	color: #333333;
	align-items: center;

	.list_title_img {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
		object-fit: cover;
	}
}
.item{
			box-sizing: border-box;
			/* display: flex;
			flex-direction: row;
			align-items: center; */
			width: 100%;
			overflow: hidden;
			/* margin-bottom: 24rpx; */
			font-size: 24rpx;
      .left {
        padding: 12rpx 0;
        .left_title {
          font-size: 22rpx;
          color: #999999;
          .left_unit {
            margin-left: 6rpx;
            font-size: 22rpx;
            color: #333;
          }
        }
        
      }
      .title{
				margin-bottom: 8rpx;
				flex-shrink: 0;
				font-size: 28rpx;
        font-family: PingFangSC-Medium;
        font-weight: 600;
        color: #333333;
			}
		}

</style>