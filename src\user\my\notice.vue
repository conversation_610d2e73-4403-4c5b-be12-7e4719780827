<template>
	<view class="notice-list">
        <view class="row flex-box">
            <view @click="checkAll" style="padding:20upx 20upx;margin-left:-20upx">
                 <checkbox color="#f65354" value="" :checked="check_all" />
                 <text style="margin-left:30upx">全选</text>
            </view>
            <view class="del-btn highlight" @click="delMoreThan">删除</view>
        </view>
		<!-- #ifndef MP -->
		<uni-list>
            <checkbox-group @change="checkboxChange">
            <block v-for="(item,index) in listData" :key="index">
                <uni-swipe-action :options="options" @click="bindClick($event,index)">
                    <view class="list-item bottom-line" hover-class="navigator-hover" @click="toDetail(item.id,item.title,item.time,index)">
                        <checkbox color="#f65354" :value="item.id+''" :checked="item.is_check" @click.stop.prevent="stopPrevent" />
                        <view class="list-info flex-1">
                            <view class="list-title">{{item.title}}</view>
                            <view class="list-content">{{item.time}}</view>
							<text class="noread" v-if="!item.if_read"></text>
                        </view>
                    </view>
                </uni-swipe-action>
            </block>
            </checkbox-group>
		</uni-list>
		<!-- #endif -->
		<!-- #ifdef MP -->
		<view class="uni-list">
            <checkbox-group @change="checkboxChange">
			<block v-for="(item,index) in listData" :key="index">
                <uni-swipe-action :options="options" @click="bindClick($event, index)">
                    <view class="list-item bottom-line" hover-class="navigator-hover" @click="toDetail(item.id,item.title,item.time,index)">
                        <checkbox color="#f65354" :value="item.id+''" :checked="false" @click.stop.prevent="stopPrevent" />
                        <view class="list-info flex-1">
                            <view class="list-title">{{item.title}}</view>
                            <view class="list-content">{{item.time}}</view>
                        </view>
                    </view>
                </uni-swipe-action>
            </block>
            </checkbox-group>
		</view>
		<!-- #endif -->
        <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
        <chat-tip></chat-tip>
	</view>
</template>

<script>
    import {uniList,uniListItem,uniLoadMore,uniSwipeAction} from '@dcloudio/uni-ui'
    import {navigateTo,showModal} from "../../common/index.js"
	export default{
		components:{
			uniList,
			uniListItem,
            uniSwipeAction,
            uniLoadMore
		},
		data(){
			return{
                check_all:false,
                check_arr:[],
				options:[
					{
						text: '标记为已读',
						style: {
							backgroundColor: 'rgb(254,156,1)'
						}
					}, {
						text: '删除',
						style: {
							backgroundColor: 'rgb(255,58,49)'
						}
					}
                ],
                listData:[],
                page:1,
				rows:20,
                get_status:"loading",
				content_text:{
					contentdown:"",
					contentrefresh:"正在加载...",
					contentnomore:"没有更多数据了"
				}
			}
        },
        // #ifndef MP
		mounted() {
			this.getData()
		},
        // #endif
        // #ifdef MP
        onLoad(){
            // #ifndef MP-BAIDU  
            uni.hideShareMenu()    //百度小程序不支持
              // #endif
            this.getData()
        },
        // #endif
		methods:{
            getData(){
                if(this.page==1){
                    this.listData = []
                }
                this.get_status = "loading"
                this.$ajax.get('member/mailList.html',{page:this.page,rows:this.rows},res=>{
                    if(res.data.code == 1){
						let list = res.data.list.map(item=>{
							item.is_check = false
							return item
						})
                        this.listData = this.listData.concat(list)
						if(res.data.list.length<this.rows){
							this.get_status = "noMore"
						}else{
							this.get_status = "more"
						}
                    }else{
						this.get_status = "noMore"
					}
                },err=>{
                    console.log(err)
                    this.get_status = "more"
                })
            },
            toDetail(id,title,time,index){
                navigateTo('/user/my/notice_detail?id='+id)
				this.listData[index].if_read = 1
            },
			bindClick(e, index) {
				if(e.index==0){
					this.ready(index)
				}else if(e.index==1){
					showModal({
						title:"提示",
						content:"确定删除此信息吗?",
						confirm:(res)=>{
							this.delNotice(index)
						}
					})
				}
            },
            ready(index){
                let id = this.listData[index].id
                this.$ajax.get('member/mailDetail',{id},res=>{
                    if(res.data.code==1){
                        this.listData[index].if_read = 1
                    }else{
                		uni.showToast({
                			title:"操作失败",
                			icon:"none"
                		})
                	}
                })
            },
            checkAll(e){
                this.check_all = !this.check_all
                this.check_arr = []
                this.listData.forEach(item => {
                    if(this.check_all){
                        item.is_check = true
                        this.check_arr = this.check_arr.concat([item.id])
                    }else{
                        item.is_check = false
                    }
                });
            },
            checkboxChange(e){
                this.check_arr = e.detail.value
                this.check_all = this.check_arr.length==this.listData.length
                this.listData.forEach(item => {
                    if(this.check_arr.includes(item.id)){
                        item.is_check = true
                    }else{
                        item.is_check = false
                    }
                });
            },
            stopPrevent(){

            },
			delNotice(index){
                console.log(index)
                let id = this.listData[index].id
				this.$ajax.get('member/mailDel.html',{id},(res)=>{
					if(res.data.code == 1){
						this.page = 1
						this.getData()
					}
					uni.showToast({
						title:res.data.msg,
						icon: res.data.code==1?'success':'none'
					})
				})
            },
            delMoreThan(){
                if(this.check_arr.length<1){
                    uni.showToast({
                        title:"请至少选择一条信息",
                        icon:"none"
                    })
                    return
                }
                showModal({
                    title:"提示",
                    content:"确定要删除选择的信息吗？",
                    confirm:()=>{
                        handleDel()
                    }
                })
                let handleDel = ()=>{
                    this.$ajax.get('member/mailDel.html',{id:this.check_arr.join(",")},(res)=>{
                        console.log(res.data)
                        if(res.data.code == 1){
                            this.check_all = false
                            this.check_arr = []
                            this.page = 1
                            this.getData()
                        }
						uni.showToast({
						    title:res.data.msg,
						    icon: res.data.code==1?'success':'none'
						})
                    })
                }
            }
        },
        onReachBottom(){
			this.page++
			this.getData()
		}
	}
</script>

<style lang="scss">
    .notice-list{
        padding-top: 130upx
    }
    .row.flex-box{
        padding: $uni-spacing-col-sm $uni-font-size-lg;
        justify-content: space-between;
        box-sizing: border-box;
        align-items: center;
        position: fixed;
        width: 100%;
        /* #ifdef H5 */
        top: 44px;
        /* #endif */
        /* #ifndef H5 */
        top: var(--window-top);
        /* #endif */
    }
    .row .del-btn{
        padding: 4upx 20upx;
        border-radius: 4upx;
        height: 40upx;
        line-height: 40upx;
        background-color: #c0c0c0;
        color: #999
    }
    .row .del-btn.highlight{
        background-color: $uni-color-primary;
        color: #fff
    }
    .list-item{
        display: flex;
        align-items: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: $uni-spacing-col-lg $uni-font-size-lg;
        checkbox{
            padding: 20upx 30upx;
            margin-left: -30upx;
        }
        .list-info{
            overflow: hidden;
            text-overflow:ellipsis;
            white-space:nowrap;
        }
        .list-title{
            width: 100%;
            font-size: $uni-font-size-lg;
            text-overflow:ellipsis;
            white-space:nowrap;
            line-height:1.5;
            overflow:hidden;
        }
        .list-content{
            font-size: $uni-font-size-base;
            white-space:normal;
            display:-webkit-box;
            -webkit-box-orient:vertical;
            -webkit-line-clamp:2;
            overflow:hidden;
            color:#999;
        }
		.noread{
			padding: 8upx;
			position: absolute;
			right: 24upx;
			top: 20upx;
			border-radius: 50%;
			background-color: #f44
		}
    }
</style>
