<template>
	<view :class="{small:small}">
		<view class="input-row" :class="{no_label:!label,'bottom-line':bottomLine}">
			<label v-if="label" for="" :style="{width:label_width}">
				<text>{{label}}</text>
			</label>
			<input :type="type" :maxlength="maxlength" :value="value" :placeholder="placeholder?placeholder:'请输入'+label" @input="handelInput" :disabled="disabled" :style="{'text-align':textAlign}" :password="type==='password'?true:false" @blur="blur" >
			<text v-if="unit" class="unit">{{unit}}</text>
			<slot></slot>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			label:String,
			label_width:{
				type:[String],
				default:"auto"
			},
			disabled:{
				type:[Boolean,Number,String],
				default:false
			}, 
			name:String,
			value:[String,Number],
			maxlength:{
				type:[String,Number],
				default:"140"
			},
			unit:String,
			placeholder:String,
			type:{
				type:String,
				default:'text'
			},
			small:{
				type:[Boolean],
				default:false
			},
			bottomLine:{
				type:[Boolean],
				default:true
			},
			textAlign:{
				type:String,
				default:'left'
			}
		},
		data() {
			return {
			};
		},
		methods:{
			handelInput(e){
				let name = {
					_name:this.name
				}
				this.$emit('input', Object.assign(e,name))
			},
			blur(){
				uni.hideKeyboard()
			}
		}
	}
</script>

<style lang="scss">
	.small{
		.input-row{
			padding: $uni-spacing-col-base;
			input{
				font-size: 30rpx;
			}
		}
	}
	.input-row{
		display: flex;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;
		line-height:62upx;
		background-color: #fff;
		&.no_label{
			padding-left: 0;
			padding-right: 0;
		}
		label{
			min-width: 130upx;
			max-width: 220upx;
			font-size: $uni-font-size-lg;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			margin-right: 38upx;
			text-align-last:justify;
		}
		input{
			font-size: 30rpx;
			flex: 1;
			height: 62upx;
			padding: 0 8upx;
			margin-right: 10upx;
		}
		.unit{
			font-size: $uni-font-size-base;
			color: #333;
		}
		uni-icon{
			line-height: 62upx;
			margin-left: 10upx;
		}
	}
</style>
