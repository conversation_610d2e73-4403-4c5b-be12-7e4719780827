<template>
  <view class="page">
    <view class="sand_card container">
      <template v-if="current_sand.info.width">
        <movable
          :map_img="current_sand.info.img"
          :map_info="current_sand.info"
          ref="moveable"
          :mark_point="current_sand.buildings"
          :now_point="list_params.building_id"
          height="66vw"
          @onMove="handleMove"
          @clickPoint="onClickPoint"
        >
          <view class="status_list">
            <view>
              <view class="color color1"></view>
              <text>选中</text>
            </view>
            <view>
              <view class="color color2"></view>
              <text>有房源</text>
            </view>
            <view>
              <view class="color color3"></view>
              <text>无房源</text>
            </view>
          </view>
        </movable>
      </template>
    </view>
    <view class="filter_container fixed_top" :class="{show:filter_index>0}">
      <view class="filter_item" :class="{highlight: filter_index===1}" @click="showFilter(1)">
        <text>楼栋</text>
        <my-icon type="ic_down" color="#c4c4c4" size="24rpx"></my-icon>
      </view>
      <view class="filter_item" :class="{highlight: filter_index===2}" @click="showFilter(2)">
        <text>户型</text>
        <my-icon type="ic_down" color="#c4c4c4" size="24rpx"></my-icon>
      </view>
      <view class="filter_item" :class="{highlight: filter_index===3}" @click="showFilter(3)">
        <text>状态</text>
        <my-icon type="ic_down" color="#c4c4c4" size="24rpx"></my-icon>
      </view>
      <view class="filter_item" :class="{highlight: filter_index===4}" @click="showFilter(4)">
        <text>排序</text>
        <my-icon type="ic_down" color="#c4c4c4" size="24rpx"></my-icon>
      </view>
    </view>
    <view class="filter_container">
      <view class="filter_item" @click="showFilter(1)">
        <text>楼栋</text>
        <my-icon type="ic_down" color="#c4c4c4" size="24rpx"></my-icon>
      </view>
      <view class="filter_item" @click="showFilter(2)">
        <text>户型</text>
        <my-icon type="ic_down" color="#c4c4c4" size="24rpx"></my-icon>
      </view>
      <view class="filter_item" @click="showFilter(3)">
        <text>状态</text>
        <my-icon type="ic_down" color="#c4c4c4" size="24rpx"></my-icon>
      </view>
      <view class="filter_item" @click="showFilter(4)">
        <text>排序</text>
        <my-icon type="ic_down" color="#c4c4c4" size="24rpx"></my-icon>
      </view>
    </view>
    <scroll-view scroll-y class="filter_panel" :class="{show: filter_index === 1}">
      <view class="ld_container">
        <!-- <view v-for="sand in sand_list" :key="sand.id">
          <view class="cate">{{sand.title}}</view>
          <view class="ld_list">
            <view class="item" @click="selectBuilding({id: ''}, sand.id)" :class="{ current: '' == list_params.building_id&&current_sand.id===sand.id }" >
              不限
            </view>
            <view class="item" @click="selectBuilding(item, sand.id)" :class="{ current: item.id == list_params.building_id, disabled:item.has_house===0 }" v-for="(item, index) in sand.buildings" :key="index">
              {{ item.name }}
            </view>
            <view class="empty"></view>
          </view>
        </view> -->
         <view class="ld_list">
          <view class="item" @click="selectBuilding({id: '', name: ''}, sand_id)" :class="{ current: '' == list_params.building_id&&''==list_params.building_name }" >
            不限
          </view>
          <view class="item" @click="selectBuilding(item, item.sand_id)" :class="{ current: item.id == list_params.building_id&&item.name===list_params.building_name, disabled:item.has_house===0 }" v-for="(item, index) in loudong_list" :key="index">
            {{ item.name }}
          </view>
          <view class="empty"></view>
        </view>
        <view class="status_tip">
          <view class="point"></view>
          <text>为无房源楼栋</text>
        </view>
      </view>
    </scroll-view>
    <view class="filter_panel has_btn" :class="{show: filter_index === 2}">
      <scroll-view scroll-y class="scroll_view">
        <view class="house_list">
          <SaleHouse ref="huxing" :list="hosue_type_list" v-model="selected_houxing" @onselect="onSelectHuxing"/>
        </view>
      </scroll-view>
      <view class="btn_group">
          <view class="btn" @click="initHuxing">重置</view>
          <view class="btn" @click="confirmHuxing">确定</view>
        </view>
    </view>
    <scroll-view scroll-y class="filter_panel" :class="{show: filter_index === 3}">
      <view class="ld_container">
        <view>
          <view class="cate">销售状态</view>
          <view class="ld_list">
            <view class="item" @click="list_params.sale_status = ''" :class="{ current: list_params.sale_status === '' }" >
              不限
            </view>
            <view class="item" @click="list_params.sale_status = 2" :class="{ current: list_params.sale_status === 2 }" >
              在售房源
            </view>
            <view class="item" @click="list_params.sale_status = 1" :class="{ current: list_params.sale_status === 1 }" >
              待售房源
            </view>
            <view class="empty"></view>
          </view>
        </view>
        <view>
          <view class="cate">其他</view>
          <view class="ld_list">
            <view class="item" @click="list_params.ifprice===1?list_params.ifprice=0:list_params.ifprice=1" :class="{ current: list_params.ifprice === 1 }" >
              有价格房源
            </view>
            <view class="empty"></view>
          </view>
        </view>
      </view>
      <!-- <view class="filter_option_list">
        <view class="list_item" :class="{highlight: list_params.status === 0}" @click="list_params.status = 0">不限</view>
        <view class="list_item" :class="{highlight: list_params.status === 1}" @click="list_params.status = 1">在售房源</view>
        <view class="list_item" :class="{highlight: list_params.status === 2}" @click="list_params.status = 2">待售房源</view>
        <view class="list_item" :class="{highlight: list_params.status === 3}" @click="list_params.status = 3">有价格房源</view>
      </view> -->
    </scroll-view>
    <scroll-view scroll-y class="filter_panel" :class="{show: filter_index === 4}">
      <view class="filter_option_list">
        <view class="list_item" :class="{highlight: list_params.sort === 0}" @click="list_params.sort = 0">默认排序</view>
        <view class="list_item" :class="{highlight: list_params.sort === 1}" @click="list_params.sort = 1">价格从低到高</view>
        <view class="list_item" :class="{highlight: list_params.sort === 2}" @click="list_params.sort = 2">价格从高到低</view>
      </view>
    </scroll-view>
    <view class="mask" @click="filter_index = 0" :class="{show: filter_index>0}"></view>
    <view class="house_container">
      <view class="list_header">
        <view class="name">房号</view>
        <view class="info">户型</view>
        <view class="mianji">面积</view>
        <view class="price">总价</view>
        <view class="status">状态</view>
      </view>
      <view class="list_item" v-for="(house, index) in house_list" :key="index" @click="toDetail(house)">
        <view class="name">
          <text>{{ house.title }}</text>
          <my-icon type="ic_into" size="28rpx"></my-icon>
        </view>
        <view class="info">{{ house.shi }}室{{house.ting}}厅{{house.wei}}卫</view>
        <view class="mianji">{{ house.mianji }}m²</view>
        <view class="price highlight" v-if="house.total_price">{{ house.total_price.toFixed(1) }}万</view>
        <view class="price highlight" v-else>待定</view>
        <view class="status">{{ house.sale_status || "未知" }}</view>
      </view>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
    <view class="footer_tip">
      免责声明：
      1.一房一价的房态、价格革新时间为每日24:00
      2.以上房源信息均来源于当地城乡建设部门、开发商公式文件等途径，实际信息可能有变动或者延迟，建议销售现场实际信息为准
      3.房源总价为四舍五入后保留小数点一位的金额
    </view>
    <view class="bottom-bar flex-row">
      <view class="bar-left flex-row flex-1">
        <view v-if="current_adviser && current_adviser.mid && is_open_adviser && build.open_adviser" class="icon-btn" @click="consuDetail(current_adviser.id)">
          <image :src="current_adviser.prelogo | imageFilter('w_120')" class="header_img"></image>
          <text>{{ current_adviser.cname || current_adviser.typename }}</text>
        </view>
        <view class="icon-btn" v-if="navs[0].is_show && (navs[0].operation === 1 || navs[0].operation === 2)" @click="toYuyue(3, navs[0])">
          <my-icon type="yuyue" color="#666" size="50rpx"></my-icon>
          <text>{{ navs[0].name }}</text>
        </view>
        <view class="icon-btn" v-if="navs[0].is_show && (navs[0].operation === 3 || navs[0].operation === 4)" @click="cusList(navs[0].operation)">
          <my-icon type="ic_zixun" color="#666" size="50rpx"></my-icon>
          <text>{{ navs[0].name }}</text>
        </view>
        <view class="icon-btn" v-if="navs[1].is_show === 1" @click="toContrast()">
          <text class="badge" v-if="login_status > 1 && contrastCount > 0">{{ contrastCount > 99 ? "99+" : contrastCount }}</text>
          <text class="badge" v-if="login_status <= 1 && $store.state.temp_huxing_contrast_ids.length > 0">{{
            $store.state.temp_huxing_contrast_ids.length > 99 ? "99+" : $store.state.temp_huxing_contrast_ids.length
          }}</text>
          <my-icon type="pk" color="#666" size="50rpx"></my-icon>
          <text>{{ navs[1].name }}</text>
        </view>
      </view>
      <!-- 置业顾问按钮 -->
      <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show === 1 && (navs[2].operation === 1 || navs[2].operation === 2)" @click="toYuyue(3, navs[2])">{{
        navs[2].name
      }}</view>
      <view
        class="bar-btn btn1 flex-1"
        v-if="navs[2].is_show === 1 && (navs[2].operation === 3 || navs[2].operation === 4)"
        @click="cusList(navs[2].operation)"
        >{{ navs[2].name }}</view
      >
      <!-- 咨询售楼处按钮 -->
      <view class="flex-1" :class="{ alone: navs[2].is_show === 0 }" v-if="navs[3].is_show === 1">
        <tel-btn :user_login_status="login_status" @ok="handleTel()">
          <view class="bar-btn btn2">{{ navs[3].name }}</view>
        </tel-btn>
      </view>
    </view>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
    <sub-form :sub_type="sub_type" :sub_mode="sub_mode" ref="sub_form" @onsubmit="handleSubForm"></sub-form>
  </view>
</template>

<script>
import movable from "../components/moveableScale.vue";
import SaleHouse from "./components/SaleHouse.vue";
import myIcon from "../components/myIcon.vue";
import chatBtn from "../components/open-button/chatBtn";
import telBtn from "../components/open-button/telBtn";
import subForm from "../components/subForm";
import allTel from '../common/all_tel.js'
import getChatInfo from "../common/get_chat_info";
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
  name: "saleList",
  components: {
    movable,
    SaleHouse,
    myIcon,
    chatBtn,
    telBtn,
    subForm,
    uniLoadMore,
  },
  data() {
    return {
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了"
      },
      build: {},
      sand_list: [],
      loudong_list: [],
      current_sand: {
        buildings: [],
        info: {}
      },
      hosue_type_list: [],
      house_list: [],
      filter_index: 0,
      selected_houxing: [],
      list_params: {
        build_id: "",
        sand_id: "",
        page: 1,
        rows: 20,
        building_id: '',
        building_name: '',
        huxing_id: '',
        sale_status: '',
        ifprice: 0, //1有价格，0不限
        sort: 0
      },
      current_adviser: {},
      navs: [{}, {}, {}, {}],
      contrastCount: 0, //对比数量
      sub_type: 0,
      tel_res: {},
      show_tel_pop: false,
    };
  },
  watch:{
    list_params: {
      handler: function(n, o){
        this.filter_index = 0
        if(!this.gethouseing){
          this.getHouseList()
        }
      },
      deep: true
    },
    current_sand: {
      handler: function(n){
        uni.setNavigationBarTitle({
          title: n.title
        })
      },
      deep: true
    }
  },
  computed: {
    is_open_im() {
      return this.$store.state.im.ischat;
    },
    login_status() {
      return this.$store.state.user_login_status;
    },
    sub_mode() {
      return this.$store.state.sub_form_mode;
    },
  },
  onLoad(options) {
    this.build_id = options.build_id || '';
    this.sand_id = Number(options.sand_id) || '';
    if(Number(options.huxing_id)){
      this.list_params.huxing_id = Number(options.huxing_id)
      this.selected_houxing.push(Number(options.huxing_id))
    }
    if (this.build_id && this.sand_id) {
      this.list_params.build_id = this.build_id
      this.list_params.sand_id = this.sand_id
      this.getFilterOptions(this.build_id)
      this.getNav(this.build_id)
      // this.getHouseList();
    }
  },
  methods: {
    getFilterOptions(build_id){
      this.$ajax.get('buildHouse/houseListCondition', {build_id}, res=>{
        if(res.data.code === 1){
          this.sand_list = res.data.sands
          this.loudong_list = res.data.sands.reduce((loudong_item, sand)=>{
            return loudong_item.concat(sand.buildings)
          }, [])
          console.log(this.loudong_list)
          this.hosue_type_list = res.data.huxings
          if(this.sand_list.length>0){
            this.getCurrentSand(this.sand_id)
          }
        }
      })
    },
    getCurrentSand(sand_id){
      let _default_sand_index = 0
      if(sand_id){
        _default_sand_index = this.sand_list.findIndex(item=>item.id == sand_id)
      }else{
        this.current_sand = {
          buildings: [],
          info: {}
        }
        return
      }
      if(_default_sand_index===-1){
        _default_sand_index = 0
      }
      this.current_sand = this.sand_list[_default_sand_index]
      console.log(this.current_sand)
      this.current_sand.buildings.forEach(item=>{
        // 默认都是有房源状态（这个状态是区分楼栋颜色的和此相关的接口的状态不是一个）
        item.sale_status = 2
      })
      // this.hosue_type_list = this.sand_list[_default_sand_index].huxings
      let padding = 0;
      let windowWidth = this.$store.state.systemInfo.windowWidth;
      let box_height = (windowWidth * 3) / 5;
      let box_width = windowWidth - padding * 2;
      let top = "0px";
      let left = "0px";
      if (this.current_sand.height - box_height > this.current_sand.height / 4) {
        top = 0 - this.current_sand.height / 4 + "px";
      }
      if (this.current_sand.width - box_width > 0) {
        left = 0 - (this.current_sand.width / 2 - box_width / 2) + "px";
      }
      this.current_sand.info = {
        img: this.current_sand.pic, // 楼盘背景
        width: this.current_sand.width,
        height: this.current_sand.height,
        top: top,
        left: left,
        margin_left: "0px",
      };
    },
    getHouseList(){
      if(this.list_params.page === 1){
        this.house_list = []
      }
      uni.showLoading({
        title: '加载中...',
        mask: true
      })
      // 置空sand_id
      this.list_params.sand_id = ''
      this.get_status = "loading"
      this.gethouseing = true
      this.$ajax.get('buildHouse/houseList', this.list_params, res=>{
        uni.hideLoading()
        this.gethouseing = false
        if(res.data.code === 1){
          this.get_status = "more"
          if(res.data.list.length<this.list_params.rows){
            this.get_status = 'noMore'
          }
          this.house_list.push(...res.data.list)
          // this.current_sand.buildings.forEach((building, index)=>{
          //   res.data.buildings_rel.forEach(item=>{
          //     if(item.building_id&&item.building_id===building.id){
          //       this.current_sand.buildings[index].has_house = item.has_house
          //     }
          //   })

          //   // 这个状态是区分楼栋颜色的和此相关的接口的状态不是一个
          //   if(this.current_sand.buildings[index].has_house){
          //     building.sale_status = 2
          //   }else{
          //     building.sale_status = 4
          //   }
          // })
          this.loudong_list.forEach((loudong, index)=>{
            res.data.buildings_rel.forEach(item=>{
              if(item.building_id&&loudong.id === item.building_id ){
                this.loudong_list[index].has_house = item.has_house
              }else if(!item.building_id&&loudong.name === item.building){
                this.loudong_list[index].has_house = item.has_house
              }
            })
          })
        }else{
          this.get_status = "noMore"
        }
      },err=>{
        this.gethouseing = false
      })
    },
    onClickPoint(e) {
      this.list_params.building_id = this.current_sand.buildings[e].id;
      this.list_params.building_name = this.current_sand.buildings[e].name;
      this.getHouseList();
    },
    showFilter(num){
      if(this.filter_index === num){
        this.filter_index = 0
      }else{
        this.filter_index = num
      }
    },
    selectBuilding(e, sand_id){
      if(e.has_house === 0){
        return
      }
      if(e.id==this.list_params.building_id&&e.name===this.list_params.building_name){
        return
      }
      this.getCurrentSand(sand_id)
      let _params = Object.assign(this.list_params)
      // 如果切换沙盘了需要清空上一次的户型id
      if(_params.sand_id&&sand_id!==_params.sand_id){
        _params.huxing_id = ''
        this.initHuxing()
        // 重置一下每个楼栋的房源状态
        this.sand_list.forEach(sand=>{
          sand.buildings.forEach(item=>{
            item.has_house = 1
          })
        })
        _params.sand_id = sand_id
      }
      _params.building_id = e.id
      _params.building_name = e.name
      this.list_params = _params
    },
    onSelectHuxing(e){
      this.selected_houxing = e
    },
    confirmHuxing(){
      this.list_params.huxing_id = this.selected_houxing.join(',')
    },
    initHuxing(){
      this.selected_houxing = []
      // this.$refs.huxing.init()
    },
    toDetail(house){
      this.$navigateTo(`/online/sale_detail?id=${house.id}&bid=${this.build_id}`)
    },
    handleAsk(mid,adviser_id){
      if (this.is_open_im == 1) {
        getChatInfo(mid, 3)
      }else{
        this.toAdviserDetail(adviser_id)
      }
    },
    // // 执行拨打电话时间
    handleTel(){
        let phoneNumber=""
        // 如果没有开启虚拟号功能
        if(this.build.use_middle_call==0||this.glabol_middle_tel==0){
            if(this.build.phone&&this.build.sellmobile_part){
                phoneNumber = this.build.phone+','+this.build.sellmobile_part.trim()
                if(this.tel400jing){
                    phoneNumber+="#"
                }
                showModal({
                    title:"温馨提示",
                    content:"请拨打"+this.build.phone+"后转拨分机号"+this.build.sellmobile_part,
                    confirm: (res)=> {
                        uni.makePhoneCall({
                            phoneNumber: phoneNumber
                        });
                    }
                })  
            }else if(this.build.tel){
                uni.makePhoneCall({
                    phoneNumber: this.build.tel,
                    success:()=>{
                      // this.statistics()
                    }
                });
            }else{
                uni.showToast({
                  title:"此楼盘没有绑定联系电话",
                  icon:'none'
                })
            }
        }else{
          this.tel_params = {
            type: 1,
            callee_id: this.build_id,
            scene_type: 1,
            scene_id: this.build_id,
            success: (res)=>{
              this.tel_res = res.data
              this.show_tel_pop = true
            }
          }
          allTel(this.tel_params)
        }
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    getNav(build_id) {
      this.$ajax.get("build/buildNav.html", { bid: build_id }, (res) => {
        if (res.data.code === 1) {
          this.navs = res.data.navs;
        }
      });
    },
    toYuyue(type, nav) {
      if (nav.operation === 2 && nav.group_id) {
        // 跳转团购报名
        this.$navigateTo(`/pages/groups/detail?id=${nav.group_id}`);
      } else {
        this.toSubForme(type);
      }
    },
    //转到顾问列表
    cusList(operation) {
      if (!uni.getStorageSync('token')) {
        this.$navigateTo('/user/login/login')
        return
      }
      this.$navigateTo('/pages/consultant/consuList?id=' + this.build_id)
    },
    toSubForme(type, operation) {
      this.sub_operation = operation || "";
      this.sub_type = type;
      this.$refs.sub_form.showPopup();
    },
    handleSubForm(e) {
      //提交报名
      e.from = "楼盘页";
      e.bid = this.build_id;
      e.type = this.sub_type || "";
      if (this.sub_operation) e.operation = this.sub_operation;
      this.$ajax.post("build/signUp.html", e, (res) => {
        uni.hideLoading();
        if (res.data.code === 1) {
          // 没开启引导登录模式或已经绑定手机号了
          if (this.sub_mode !== 2 || res.data.status === 3) {
            //提示报名成功
            uni.showToast({
              title: res.data.msg,
              icon: "none",
            });
            this.$refs.sub_form.closeSub();
          } else {
            this.$refs.sub_form.getVerify();
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none",
          });
        }
      });
    },
  },
  onReachBottom(){
    if (this.get_status == "more"){
      this.list_params.page++
      this.getHouseList()
    }
  },
};
</script>

<style scoped lang="scss">
.page{
  // #ifdef H5
  min-height: calc(100vh - 44px);
  // #endif
  // #ifndef H5
  min-height: 100vh;
  // #endif
  box-sizing: border-box;
  padding-bottom: 120rpx;
  position: relative;
  background-color: #fff;
}
.filter_panel{
  box-sizing: border-box;
  width: 100%;
  max-height: 60vh;
  min-height: 40vh;
  box-sizing: border-box;
  transform: translateY(-130%);
  transition: 0.2s;
  position: fixed;
  z-index: 10;
  top:calc(86rpx + 44px);
  // margin-top: 44px;
  background-color: #fff;
  padding: 0 48rpx;
  &.has_btn{
    padding-bottom: 120rpx;
  }
  &.show{
    transform: translateY(0);
  }
  .scroll_view{
    max-height: calc(60vh - 120rpx);
  }
  .btn_group{
    padding: 24rpx 0;
    display: flex;
    position: absolute;
    left: 48rpx;
    right: 48rpx;
    bottom: 0;
    .btn{
      flex: 1;
      border-radius: 6rpx;
      padding: 24rpx;
      text-align: center;
      background-color: #f2f2f2;
      color: #999;
      ~.btn{
        margin-left: 48rpx;
        background-color: $uni-color-primary;
        color: #fff;
      }
    }
  }
}

.ld_container{
  .cate{
    margin: 24rpx 0;
    font-size: 30rpx;
    font-weight: bold;
  }
  .ld_list{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .item{
      box-sizing: border-box;
      flex-shrink: 0;
      width: 204rpx;
      min-width: 204rpx;
      height: 60rpx;
      line-height: 60rpx;
      text-align: center;
      margin-bottom: 24rpx;
      color: #999;
      border: 1rpx solid #F2F2F2FF;
      &.current{
        border-color: rgba($color: $uni-color-primary, $alpha: 0.08);
        color: $uni-color-primary;
        background-color: rgba($color: $uni-color-primary, $alpha: 0.08);
      }
      &.disabled{
        background-color: #f2f2f2;
      }
    }
    .empty{
      height: 0;
      width: 204rpx;
    }
  }
  .status_tip{
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #6f6f6f;
    .point{
      width: 20rpx;
      height: 20rpx;
      margin-right: 10rpx;
      background-color: #cdcdcd;
    }
  }
}

.filter_option_list{
  .list_item{
    padding: 24rpx 0;
  }
}

.status_list {
  display: flex;
  justify-content: center;
  box-sizing: border-box;
  width: 100%;
  padding: 24rpx;
  position: absolute;
  bottom: 0;
  color: #fff;
  > view {
    display: flex;
    align-items: center;
    padding: 0 32rpx;
    font-size: 24rpx;
  }
  .color {
    width: 20rpx;
    height: 20rpx;
    margin-right: 12rpx;
    &.color1 {
      background-color: $uni-color-primary;
    }
    &.color2 {
      background-color: #27f09c;
    }
    &.color3 {
      background-color: #cdcdcd;
    }
  }
}

.filter_container {
  box-sizing: border-box;
  padding: 24rpx 48rpx;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  &.fixed_top{
    position: fixed;
    top: 44px;
    left: 0;
    width: 100vw;
    z-index: -1;
    transform: translateY(-100%);
    transition: 0.18s;
    &.show{
      z-index: 11;
      opacity: 1;
      transform: translateY(0);
    }
  }
  .filter_item {
    display: flex;
    align-items: center;
  }
}

.house_container {
  padding: 0 28rpx;
  min-height: 35vh;
  .list_header {
    padding: 16rpx 8rpx;
    font-size: 0;
    background-color: #f2f3f3;
    // display: flex;
    > view {
      box-sizing: border-box;
      padding: 0 6rpx;
      display: inline-block;
      vertical-align: middle;
      font-size: 26rpx;
    }
  }
  .list_item {
    padding: 24rpx 8rpx;
    font-size: 0;
    &:nth-child(odd) {
      background-color: #f2f3f3;
    }
    // display: flex;
    > view {
      box-sizing: border-box;
      padding: 0 6rpx;
      display: inline-block;
      vertical-align: middle;
      font-size: 26rpx;
    }
  }
  .name {
    width: 190rpx;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
  }
  .info {
    width: 160rpx;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
  }
  .mianji {
    width: 110rpx;
  }
  .price {
    width: 130rpx;
  }
  .status {
    width: 70rpx;
  }
}
.highlight {
  color: $uni-color-primary;
}

.mask{
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1;
  background-color: #000;
  opacity: 0;
  transition: 0.2s;
  &.show{
    z-index: 9;
    opacity: 0.5;
  }
}
.footer_tip{
  bottom: 120rpx;
  padding: 24rpx 48rpx;
  white-space: pre-line;
  font-size: 22rpx;
  color: #6F6F6F;
}

.flex-row{
  display: flex;
  flex-direction: row;
}
// 底部菜单
.bottom-bar {
  background-color: #fff;
  box-sizing: border-box;
  height: 110rpx;
  padding: 15rpx 48rpx;
  left: 0;
  z-index: 10;
  .bar-left {
    padding-right: 48rpx;
    justify-content: flex-start;
  }
  .icon-btn {
    // width: 100rpx;
    align-items: center;
    padding: 0;
    margin: 0;
    background-color: #fff;
    line-height: initial;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // flex: 1;
    padding-right: 48rpx;
    overflow: hidden;
    position: relative;
    // & ~ .icon-btn {
    //   margin-left: 24rpx;
    // }
    .header_img {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
    }
    text {
      line-height: 1;
      font-size: 22rpx;
      color: #999;
      display: inline-block;
      width: 100%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .badge {
      display: inline-block;
      box-sizing: border-box;
      width: auto;
      position: absolute;
      top: 0;
      left: 32rpx;
      // right: 38rpx;
      height: 28rpx;
      padding: 0 8rpx;
      min-width: 28rpx;
      border-radius: 14rpx;
      font-size: 22rpx;
      background-color: $uni-color-primary;
      color: #fff;
    }
  }
  .bar-btn {
    // width: 220rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    padding: 0;
    margin: 0;
    border-radius: 0;
    color: #fff;
    &.btn1 {
      background: #fbac65;
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.05);
      border-top-left-radius: 40rpx;
      border-bottom-left-radius: 40rpx;
    }
    &.btn2 {
      background: linear-gradient(90deg, #fb656a 0%, #fbac65 100%);
      box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.3);
      border-top-right-radius: 40rpx;
      border-bottom-right-radius: 40rpx;
    }
  }
}
</style>
