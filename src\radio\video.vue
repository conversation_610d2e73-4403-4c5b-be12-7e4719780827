<template>
    <view class="page">
    <!-- <view class="icon" @click="handletapicon">
      <my-icon type="ic_back" color="#000000" size="54rpx"></my-icon>
    </view> -->
            <scroll-view class="top-menu-view" scroll-x="true">
                <view class="tab-box">
                <view class="tab-item" @click="testTabClick(index)" :class="tabIndex == index ? 'active' : ''"
                    v-for="(item, index) in list" :key="index" style="font-size: 30rpx;">
                    {{ item.label }}
                </view>
                </view>
            </scroll-view>
            <view style="margin-top:20rpx">
                <scroll-view class="top-menu-view" scroll-x="true" style="height:70rpx;">
                    <view class="video-tab">
                        <view class="video-tab-center" @click="handeleever()" :class="{'activeto':ever==1}">
                            全部
                        </view>
                        <view class="video-tab-center" v-for="(item,index) in listto" :key='index'  @click="handeleSort(index)" :class="{'activeto':index==float}" v-if="type==item.type">
                            {{ item.catname }}
                        </view>
                    </view>
                </scroll-view>
            </view>
    <view class="pageData">
        <view class="left" ref="left">
                    <view class="footer_top" v-for="(item, index) in rightList" :key="index" v-cloak>
                        <view class="footer-one" @click=getTapRadio(index,item) style="position: relative;">
                            <image :src="(item.cover || item.video_path) | imageFilter('w_8601')" mode="widthFix"
                                style="width: 330rpx;border-radius: 10rpx;z-index: 0;"></image>
                                <view class="video" :style="{backgroundImage: 'url(' + imageURL + ')','background-repeat':'no-repeat', 
		            backgroundSize:'100% 100%'}"></view>
                            <view class="title">
                                {{ item.format_ctime }}
                            </view>
                            <view class="logo">
                                <my-icon type="ic_wode" color="#ffffff" size="28rpx" style="margin-right: 6rpx"></my-icon>
                                <!-- <image src="./3.png.png"></image> -->
                                <text>{{ item.play_count }}</text>
                            </view>
                        </view>
                        <view class="footer-two">
                            {{ item.title }}
                        </view>
                        <view class="footer-three">
                            <view>
                                <text style="width: 48rpx;height: 48rpx;border-radius: 50%;display: inline-block;margin-right: 10rpx;">
                                    <image style="width: 48rpx;height: 48rpx;border-radius: 50%;margin-right: 10rpx;"
                                        :src="item.author_prelogo"></image>
                                </text>
                                <text>{{ item.author_name }}</text>
                            </view>
                            <view>
                                <my-icon type="ic_guanzhu" color="#8a8a8a" size="28rpx" style="margin-right:6rpx"></my-icon>
                                <!-- <image src="./2.png.png" style="width: 34rpx;height: 32rpx;"></image> -->
                                <text>{{ item.praise_count }}</text>
                            </view>

                    </view>
                </view>
        </view>
        <view class="right" ref="right">
            <view class="footer_top" v-for="(item, index) in leftList" :key="index" v-cloak>
                        <view class="footer-one" @click=getTapRadio(index,item) style="position: relative;">
                            <image :src="(item.cover || item.video_path) | imageFilter('w_8601')" mode="widthFix"
                                style="width: 330rpx;border-radius: 10rpx;z-index: 0;"></image>             
                                    <view class="video" :style="{backgroundImage: 'url(' + imageURL + ')','background-repeat':'no-repeat', 
                                backgroundSize:'100% 100%'}"></view>
                            <view class="title">
                                {{ item.format_ctime }}
                            </view>
                            <view class="logo">
                                <my-icon type="ic_wode" color="#ffffff" size="28rpx" style="margin-right: 6rpx"></my-icon>
                                <!-- <image src="./3.png.png"></image> -->
                                <text>{{ item.play_count }}</text>
                            </view>
                        </view>
                        <view class="footer-two">
                            {{ item.title }}
                        </view>
                        <view class="footer-three">
                            <view>
                                <text style="width: 48rpx;height: 48rpx;border-radius: 50%;display: inline-block;margin-right: 10rpx;">
                                    <image style="width: 48rpx;height: 48rpx;border-radius: 50%;margin-right: 10rpx;"
                                        :src="item.author_prelogo"></image>
                                </text>
                                <text>{{ item.author_name }}</text>
                            </view>
                            <view>
                                <my-icon type="ic_guanzhu" color="#8a8a8a" size="28rpx" style="margin-right:6rpx"></my-icon>
                                <!-- <image src="./2.png.png" style="width: 34rpx;height: 32rpx;"></image> -->
                                <text>{{ item.praise_count }}</text>
                            </view>

                    </view>
                </view>
        </view>
    </view>
     <uni-load-more :status="status" ></uni-load-more>
    </view>
</template>
<script>
// import from '../vr/components/static/css/font.css'
import { uniIcons } from '@dcloudio/uni-ui'
import myIcon from "../components/myIcon.vue"
import {uniLoadMore,} from '@dcloudio/uni-ui'
export default {
    name: "",
    data() {
        return {
            listto:[
            ],
            list: [
            ],
            onputab:'',
            indext: 1,
            tabIndex: 0,
            radio: [],
            imageURL:'../static/icon/video.png',
            id: 0,
            //所有图片
            leftList: [], //左边列图片
            rightList: [], //右边列图片
            leftHeight: 0, //左边列高度
            rightHeight: 0, //右边列高度
            columnWidth: 0, //列宽度
            videoList:[],
            page:1,
            row:10,
            onetab:'',
            status:'',
            float:'',
            float:1,
            type:1,
            float:0,
            commit:'',
            ever:1,
            typeto:1
        }
    },
    components: {
        uniIcons,
        myIcon,
        uniLoadMore
    },
    methods: {
        gettap() {
            uni.showLoading({
                title: '加载中'
            });
        },
        // 全部
        handeleever(){
            this.ever=1
            this.float =''
            this.onetab = ''
            this.getRadioList()
        },
        // 二级分类
        handeleSort(index){
            console.log(index)
            this.float= index
            this.onetab = this.listto[index].id
            this.ever=2
            this.typeto = this.listto[index].type
            this.getRadioList()
        },
        testTabClick(index) {
            this.ever=1
            console.log(index)
            this.page=1
            this.float=0
            this.type=index+1
            this.handleSort()
            this.tabIndex = index
            this.typeto = index+1
            this.onetab =''
            
            uni.showLoading({
                title: '加载中'
            });
            this.getRadioList()
        },
        // tap切换
        handleTap(e) {
            console.log(e,"3213312")
            this.indext = e
        },
        // 视频分类
        handleSort() {
            this.$ajax.get('video/videoCondition', {type:this.type}, res => {
                console.log(res, "视频分类")
                if (res.data.code == 1) {
                    console.log(this.index,"哈哈哈哈或或或或或或或或或")
                    
                    console.log(res.data,"22222222222222")
                    this.list = res.data.types
                    this.listto = res.data.cates
                    this.listto.unshift({
                        catname:'全部'
                    })
                }
            })
        },
        // 第一次获取数据
        getList(){
            this.$ajax.get('video/getVideoList',{catid: this.onetab,page:this.page+1,type:this.typeto},res =>{
                 this.radio = [...this.radio,...res.data.list]
                 this.page++
                 console.log(this.radio)
                  let temp = []
                    let ranm = []
                    this.radio.forEach((item,index)=>{
                        if (index % 2) {
                            temp.push(item)
                        } else {
                            ranm.push(item)
                        }
                    })
                    this.leftList = temp
                    this. rightList = ranm
                 this.status = 'more'
                 if(res.data.list.length<10){
                      this.status = 'nomore'
                 }
            })
        },
        // 视频列表
     getRadioList() {
            this.$ajax.get('video/getVideoList', { catid: this.onetab,page:1,type:this.typeto}, res => {
                console.log(res)
                if (res.data.code == 1) {
                    this.radio = res.data.list
                    // uni.setNavigationBarTitle({
                    //     title: res.data.title,
                    // })
                    if (res.data.share) {
                    this.share = res.data.share
                    }
                    this.getWxConfig()
                    if(this.radio.length==0){
                        this.status ="nomore"
                    }
                    if(this.typeto ==1){
                        this.imageURL ='../static/icon/video.png'
                    }else if( this.typeto ==2){
                        this.imageURL ='../static/icon/hangpai.png'
                    }else if( this.typeto ==3){
                        this.imageURL ='../static/icon/vr.png'
                    }
                    let temp = []
                    let ranm = []
                    this.radio.forEach((item,index)=>{
                        if (index % 2) {
                            temp.push(item)
                        } else {
                            ranm.push(item)
                        }
                    })

                    this.leftList = temp
                    this. rightList = ranm
                    uni.hideLoading();
                } else {
                    this.radio = []
                    uni.hideLoading();
                }
            })
        },
    handletapicon(){
      this.$navigateBack()
    },
        // 视频跳转列表
        getTapRadio(index,val) {
            let indexData=null;
           this.radio.filter((item,ind)=>{
                if(val.id==item.id)   indexData=ind
            })
            if( this.tabIndex ==2){
                this.$navigateTo("/vr/detailto?id="+this.radio[indexData].id)
            }else{
                this.$navigateTo("/vr/prevideotwo?id=" + this.radio[indexData].id + "&catid=" + this.radio[indexData].catid + "&index=" + indexData+"&type="+ this.radio[indexData].type)
            }
        }
    },
    onPullDownRefresh(){
        this.getRadioList() 
        uni.stopPullDownRefresh()
    },
    onReachBottom() {
       this.status ='loading'
       this.getList()
       
    },
    onLoad() {
        this.gettap()
        this.handleSort()
        this.getRadioList() 
    },

}
</script>
<style scoped lang="scss">
.pageData {
    width: 100%;
    display: flex;
    margin-top: 20rpx;
    align-items: flex-start;
    padding: 0 1%;
    box-sizing: border-box;
}

.left,
.right {
    margin: 0 auto;
    width: 48%;
}

.image {
    width: 100%;
    height: auto;
    margin-bottom: 10px;
}

.page {
    background-color: white;
    min-height: calc(100vh - 88rpx);
    // padding: 19rpx 0 0 0;
}

.uni-video-cover-play-button {
    display: none !important;
}

[v-cloak] {
    display: none;
}

.footer {
    // display: flex;
    // align-items: flex-start;
    // height:auto;

}
.footer_top {
    margin-left: 18rpx;
    width: 330rpx;
    margin-top: 28rpx;
    height: auto;

    .footer-one {
        width: 330rpx;
        // margin-top: 18rpx;
        // background-color: skyblue;
        border-radius: 10rpx;
        position: relative;

        .title {
            position: absolute;
            top: 20rpx;
            padding: 2px 4px 2px 4px;
            right: 10px;
            // padding-top: 18rpx;
            // margin-left: 160rpx;
            box-sizing: border-box;
            white-space: nowrap;
            // width: 160rpx;
            // height: 62rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 2px;
            background: #0000007F;
            color: #FFFFFF;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 22rpx;
            line-height: normal;
            box-sizing: border-box;
        }

        .logo {
            position: absolute;
            bottom: 20rpx;
            left: 20rpx;

            image {
                width: 26rpx;
                height: 26rpx;
                margin-right: 6rpx;
            }

            color: #FFFFFF;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 22rpx;
        }
    }

    .footer-two {
        width: 330rpx;
        color: #141414;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 30rpx;
        line-height: normal;
        margin-top: 16rpx;
    }

    .footer-three {
        display: flex;
        justify-content: space-between;
        margin-top: 16rpx;

        view {
            display: flex;
        }

        view:nth-child(1) {
            text:nth-child(1) {
                // width: 48rpx;
                // height: 48prx;
                border-radius: 50%;
                // background-color: pink;
                // margin-right: 10rpx;
            }

            text:nth-child(2) {
                color: #141414;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 26rpx;
                color: #8A8A8A;
                line-height: normal;
            }
        }

        view:nth-child(2) {
            display: flex;
            align-items: center;

            text:nth-child(2) {
                color: #8A8A8A;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 22rpx;
            }
        }
    }

    .footer-four {
        margin-top: 20rpx;
        display: flex;

        text:nth-child(1) {
            width: 70rpx;
            display: inline-block;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 22rpx;
            background-color: #DFA650;
            border-radius: 2px;
            color: #FFFFFF;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 22rpx;
        }

        text:nth-child(2) {
            width: 108rpx;
            display: inline-block;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 22rpx;
            background-color: #DFA650;
            border-radius: 2px;
            color: #FFFFFF;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 22rpx;
        }
    }

}
.footer_top:nth-child(1){
    margin-top: 0rpx; 
    margin-bottom: 28rpx;
}
.footer_top:nth-child(2){
    margin-top: 0rpx;
    margin-bottom: 28rpx;
}

.active {
    font-weight: bolder !important;
}

.footer {
    width: 800rpx;
    display: flex;
    flex-wrap: wrap;
    // margin-top: 38rpx;
    column-count: 2; //想要排成的列数
    column-gap: 0;
}

.page_top {
    padding: 0 0 0 40rpx;
    box-sizing: border-box;
    display: flex;

    // flex-wrap: wrap;
    view {
        margin-right: 62rpx;
        color: #141414;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 30rpx;
        line-height: normal;
    }
}

.tab-box {
    display: flex;
    margin-top:20rpx;
    margin-bottom:20rpx;
    // margin-left: 218rpx;
    margin-top:20rpx;
    justify-content: center;
        .tab-item {
        flex-shrink: 0;
        padding: 24rpx;
        position: relative;
        transition: all 0 linear;
        font-size: 26rpx;
        // &::after {
        //     transition: all 0 linear;
        //     transform: translateX(-50%) scaleX(0);
        //     content: '';
        //     width: 38%;
        //     position: absolute;
        //     left: 50%;
        //     border-bottom: 14rpx solid green;
        //     border-radius: 20rpx;
        // }

        &.active {
            &::after {
                content: '';
                width: 29%;
                position: absolute;
                left: 50%;
                top: 56rpx;
                height: 10rpx;
                background-color: red;
                transform: translateX(-50%) scaleX(1);

                background: linear-gradient(to right, #ff9e02, #ff5700);
                // bottom: 20rpx;
                // border-bottom: 16rpx solid rgba(255, 141, 9, 1);
                border-radius: 20rpx;
            }
        }
    }
}

.uni-list-item {
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-bottom-color: #eee;
    font-size: 14px;
}

.video::-webkit-media-controls-play-button {
    display: none;
}

.uni-list-item__container {
    /* #ifndef APP-NVUE */
    display: flex;
    width: 100%;
    box-sizing: border-box;
    /* #endif */
    padding: 12px 15px;
    flex: 1;
    position: relative;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.uni-list-item__content-title {
    font-size: 14px;
    color: #666;
}

video::-webkit-media-controls-fullscreen-button {
    display: none;
}

.uni-video-cover-duration {
    display: none;
}

.video {
    width: 60rpx;
    height: 60rpx;
    // background-color: red;
    z-index: 111;
    position: absolute;
    top: 50%;
    margin-top: -30rpx;
    left: 50%;
    margin-left: -40rpx;
    background-size: 100% 100%;
}
.pageto {
    width: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 0 1%;
    box-sizing: border-box;
    flex-direction: column;
}
.left,
.right {
    margin: 0 auto;
    width: 100%;
}

.image {
    width: 100%;
    height: auto;
    margin-bottom: 10rpx;
}
  page {
        background-color: #eee;
        height: 100%;
    }

    .pubuBox {
        padding: 22rpx;
    }

    .pubuItem {
        column-count: 2;
        column-gap: 20rpx;
    }

    .item-masonry {
        box-sizing: border-box;
        border-radius: 15rpx;
        overflow: hidden;
        background-color: #fff;
        break-inside: avoid;
        /*避免在元素内部插入分页符*/
        box-sizing: border-box;
        margin-bottom: 20rpx;
        box-shadow: 0px 0px 28rpx 1rpx rgba(78, 101, 153, 0.14);
    }

    .item-masonry image {
        width: 100%;
    }

    .listtitle {
        padding-left: 22rpx;
        font-size: 24rpx;
        padding-bottom: 22rpx;

        .listtitle1 {
            line-height: 39rpx;
            text-overflow: -o-ellipsis-lastline;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            min-height: 39rpx;
            max-height: 78rpx;
        }

        .listtitle2 {
            color: #ff0000;
            font-size: 32rpx;
            line-height: 32rpx;
            font-weight: bold;
            padding-top: 22rpx;

            .listtitle2son {
                font-size: 32rpx;
            }
        }

        .listtitle3 {
            font-size: 28rpx;
            color: #909399;
            line-height: 32rpx;
            padding-top: 22rpx;
        }
    }

    .Index {
        width: 100%;
        height: 100%;
    }
    .onpulldownbottom{
        text-align: center;
        color: #8A8A8A;

    }
    .body-view {
		height: 100vh;
		width: 100%;
		display: flex;
		flex: 1;
		flex-direction: column;
		overflow: hidden;
		align-items: flex-start;
		justify-content: center;
	}

	.top-menu-view {
		display: flex;
		white-space: nowrap;
		width: 100%;
		background-color: #FFFFFF;
		height: 86rpx;
		line-height: 2rpx;
		// border-top: 1rpx solid #d8dbe6;

		.menu-topic-view {
			display: inline-block;
			white-space: nowrap;
			height: 86rpx;
			position: relative;

			.menu-topic-text {
				font-size: 30rpx;
				color: #303133;
				padding: 10rpx 40rpx;
                // padding-top: 0rpx;
			}

			// .menu-topic-act {
			// 	margin-left: 30upx;
			// 	margin-right: 10upx;
			// 	position: relative;
			// 	height: 100%;
			// 	display: flex;
			// 	align-items: center;
			// 	justify-content: center;
			// }

			.menu-topic-bottom {
				position: absolute;
				bottom: 0;
				width: 100%;

				.menu-topic-bottom-color {
					width: 40rpx;
					height: 4rpx;
				}
			}

			.menu-topic-act .menu-topic-bottom {
				display: flex;
				justify-content: center;
			}
		}
	}
    .video-tab{
        display: flex;
        margin-left: 20px;
        margin-top: 20rpx;
        // margin-bottom: 20rpx;
  
        // height: 200rpx;
    }
    .video-tab-center{
        padding: 6rpx 20rpx;
        border-radius: 4rpx;
        line-height: 1;
        margin-right: 24rpx;
        color: #999;
        background-color: #f5f5f5;
        border: 1rpx solid #f5f5f5;
        box-sizing: border-box;
    }
    .activeto{
        background-color: rgba(255, 101, 107, 0.1);
        color: #ff656b;
        border: 0.5px solid #ff656b!important;
    }
    .icon{
        z-index: 100;
        position: absolute;
        top: 24rpx;
        left: 20rpx; 
}
</style>