<template>
<view class="manage_house_list">
    <view class="screen-tab flex-box">
        <view v-if="show_build" class="screen-tab-item flex-1 text-center" @click="switchTab(1)">
            <text>{{buildName}}</text>
            <uni-icons :type="nowTab==1?'arrowup':'arrowdown'" size="16"></uni-icons>
        </view>
        <view class="screen-tab-item flex-1 text-center" @click="switchTab(2)">
            <text>{{loudongNumber}}</text>
            <uni-icons :type="nowTab==2?'arrowup':'arrowdown'" size="16"></uni-icons>
        </view>
        <view class="screen-tab-item flex-1 text-center" @click="switchTab(3)">
            <text>{{unitName}}</text>
            <uni-icons :type="nowTab==3?'arrowup':'arrowdown'" size="16"></uni-icons>
        </view>
    </view>
    <scroll-view scroll-y class="screen-panel" :class="nowTab==1?'show':''" @touchmove.stop.prevent="stopMove">
        <block v-for="item in build_list" :key="item.id">
            <uni-list-item :title="item.business_name" show-arrow="false" @click="selectBuild(item)"></uni-list-item>
        </block>
    </scroll-view>
    <scroll-view scroll-y class="screen-panel" :class="nowTab==2?'show':''" @touchmove.stop.prevent="stopMove">
        <block v-for="item in loudong_list" :key="item.id">
            <uni-list-item :title="item.number" show-arrow="false" @click="selectLoudong(item)"></uni-list-item>
        </block>
    </scroll-view>
    <scroll-view scroll-y class="screen-panel" :class="nowTab==3?'show':''" @touchmove.stop.prevent="stopMove">
        <block v-for="item in unit_list" :key="item.id">
            <uni-list-item :title="item.unit" show-arrow="false" @click="selectUnit(item)"></uni-list-item>
        </block>
    </scroll-view>
    <view class="louceng_list">
        <choose-house v-slot:default="{slotItem}" v-for="ceng in ceng_list" :key="ceng.id" :house_list="ceng.houses" :ceng="ceng.floor" @onClick="handleChoose">
            <text v-if="type==''&&can_edit_house&&can_del_house" class="edit" @click.stop.prevent="handleManage(slotItem)">管理</text>
            <text v-else-if="type==''&&can_edit_house" class="edit" @click.stop.prevent="handleEdit(slotItem)">修改</text>
            <text v-else-if="type==''&&can_del_house" class="edit" @click.stop.prevent="handleDel(slotItem)">删除</text>
        </choose-house>
    </view>
    <view class="mask" :class="nowTab>0?'show':''" @click="nowTab=0" @touchmove.stop.prevent="stopMove"></view>
    <uni-load-more v-if="online_id" :status="get_status" :content-text="content_text"></uni-load-more>
    <view v-else class="tip">请先选择楼盘</view>
    <view v-if="can_add_house" class="add_house" @click="toAddHouse()">
        <my-icon type="zengjia" size="36" color="#fff"></my-icon>
    </view>
</view>
</template>

<script>
import myIcon from "../components/icon.vue"
import {uniLoadMore,uniIcons,uniList,uniListItem} from '@dcloudio/uni-ui'
import chooseHouse from '../components/chooseHouse.vue'
import {navigateTo, showModal} from '../common/index.js'
export default {
    data() {
        return {
            online_id:"",
            number_id:"",
            now_unit_id:"",
            page:1,
            loudongNumber:'选择楼栋',
            buildName:'选择楼盘',
            unitName:'选择单元',
            floorName:'选择楼层',
            nowTab:0,
            build_list:[],
            loudong_list:[],
            unit_list:[],
            ceng_list:[],
            show_build:false,
            get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
            type:'',
            can_add_house:false,
            can_del_house:false,
            can_edit_house:false,
        }
    },
    onLoad(options){
        if (options.online_id){
            this.online_id = options.online_id
            this.getLoudong()
            this.getHouse()
        }else{
            this.show_build = true
            this.getBuilds()
        }
        if(options.build_id){
            this.build_id = options.build_id
        }
        this.type = options.type || ''
        uni.$on('updateData', ()=>{
            this.page = 1
            this.getHouse()
        })
    },
    onUnload(){
        uni.$off('updateData')
    },
    components: {
        myIcon,
        chooseHouse,
        uniLoadMore,
        uniIcons,
        uniList,
        uniListItem
    },
    methods:{
        getBuilds(){
            this.$ajax.get('online/onlines',{},res=>{
                if(res.data.code == 1){
                    this.build_list = res.data.list
                }else{
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
        getLoudong(){
            this.$ajax.get('online/buildingNumber',{online_id:this.online_id},res=>{
                if(res.data.code === 1){
                    this.loudong_list = res.data.number
                }
            })
        },
        getUnit(){
            this.$ajax.get('online/buildingUnit',{online_id:this.online_id,number_id:this.number_id},res=>{
                if(res.data.code === 1){
                    this.unit_list = res.data.lists
                }
            })
        },
        getHouse(){
            if(this.page == 1){
                this.ceng_list=[]
            }
            this.get_status = "loading"
            this.$ajax.get('onlineMy/houseLists',{online_id:this.online_id,number_id:this.number_id,unit_id:this.now_unit_id,page:this.page},res=>{
                this.can_add_house = res.data.addhouse
                this.can_del_house = res.data.delhouse
                this.can_edit_house = res.data.edithouse
                if(res.data.code === 1){
                    this.ceng_list = res.data.lists
                    if(res.data.lists.length<20){
                        this.get_status = "noMore"
                    }else{
                        this.get_status = "more"
                    }
                }else{
                    this.get_status = "noMore"
                }
            })
        },
        switchTab(index){
            if(this.nowTab == index){
                this.nowTab = 0
            }else{
                this.nowTab = index
            }
        },
        selectBuild(item){
            this.buildName = item.business_name
            this.nowTab = 0
            this.online_id = item.id
            this.getLoudong()
            this.getHouse()
        }, 
        selectLoudong(item){
            this.loudongNumber = item.number
            this.nowTab = 0
            this.number_id = item.id
            this.now_unit_id = ""
            this.unitName = "选择单元"
            this.getHouse()
            this.getUnit()
        },
        selectUnit(item){
            this.unitName = item.unit
            this.nowTab = 0
            this.now_unit_id = item.id
            this.getHouse()
        },
        selectFloor(item){
            this.floorName = item.name
            this.nowTab = 0
        },
        handleChoose(e){
            if(this.type === 'choose'){
                uni.$emit('onChooseHouse',{house_id:e.id,number_id:e.building_number,unit_id:e.unit_id})
                uni.navigateBack()   
            }else{
                navigateTo(`/online/house_detail?id=${e.id}&online_id=${this.online_id}`)
            }
        },
        handleManage(e){
            uni.showActionSheet({
                itemList: ['修改', '删除'],
                success:res=>{
                    if(res.tapIndex === 0){
                        this.handleEdit(e)
                    }
                    if(res.tapIndex === 1){
                        this.handleDel(e)
                    }
                }
            })
        },
        handleEdit(e){
            if(e.sale_status){
                uni.showToast({
                    title:'该房源已售完',
                    icon:'none'
                })
                return
            }
            navigateTo(`/adviser/manage_house?type=edit&online_id=${this.online_id}&house_id=${e.id}&number_id=${e.building_number}&unit_id=${e.unit_id}`)
        },
        handleDel(e){
            showModal({
                content: '确认要删除此房源吗？',
                confirm:()=>{
                    this.$ajax.get('onlineMy/delHouse.html',{house_id:e.id,online_id:this.online_id},res=>{
                        if(res.data.code === 1){
                            uni.showToast({
                                title: res.data.msg
                            })
                            this.page = 1
                            this.getHouse()
                        }else{
                            uni.showToast({
                                title: res.data.msg,
                                icon: 'none'
                            })
                        }
                    })
                }

            })
        },
        toAddHouse(){
            navigateTo(`/adviser/manage_house?online_id=${this.online_id}`)
        },
        stopMove(){

        }
    },
}
</script>

<style scoped lang="scss">
.manage_house_list{
    padding-top: 80rpx;
    .screen-tab{
        top: 0;
        margin-top: var(--window-top);
    }
    .screen-panel {
        top: 80rpx;
        margin-top: var(--window-top);
        // margin-top: 170rpx;
    }
    .edit{
        padding: 5rpx 20rpx;
        background-color: #f44;
        border-radius: 8rpx;
        color: #fff;
    }
}

.tip{
    padding: 20rpx;
    margin-top: 30rpx;
    text-align: center;
    color: #999;
}

.add_house{
    position: fixed;
    right: 36upx;
    bottom: 180upx;
    height: 90upx;
    width: 90upx;
    border-radius: 50%;
    display:flex;
    align-items:center;
    justify-content:center;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.9);
    z-index: 96;
    box-shadow: 1upx 1upx 20upx 3upx rgba($color: $uni-color-primary, $alpha: 1);
}
</style>
