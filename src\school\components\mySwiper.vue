<template>
  <view class="swiper-box">
    <swiper
      :style="{ height: height }"
      class="banner"
      :indicator-dots="indicatorDots"
      :indicator-color="indicatorColor"
      :circular="circular"
      :duration="duration"
      :indicator-active-color="indicatorActiveColor"
      :autoplay="autoplay"
      :interval="interval"
      @animationfinish="onAnimationfinish"
    >
      <swiper-item
        v-for="(img, index) in focus"
        :key="index"
        @click="onClick(index)"
      >
        <view class="swiper-item">
          <image
            :src="img.path"
            mode="aspectFill"
          ></image>
        </view>
      </swiper-item>
    </swiper>
    <text class="indicato" v-if="customIndicator && focus.length > 0"
      >{{ current }}/{{ focus.length }}</text
    >
  </view>
</template>

<script>
export default {
  name: 'mySeiper',
  props: {
    focus: Array,
    height: {
      type: String,
      default: '45vw'
    },
    indicatorDots: <PERSON>olean,
    customIndicator: <PERSON>olean,
    circular: Boolean,
    duration: {
      type: Number,
      default: 300
    },
    indicatorColor: {
      type: String,
      default: 'rgba(0,0,0,0.3)'
    },
    indicatorActiveColor: {
      type: String,
      default: '#ff6735'
    },
    autoplay: <PERSON>olean,
    interval: {
      type: [String, Number],
      default: 5000
    },
    total: {
      type: Number,
      default: 3
    }
  },
  data() {
    return {
      current: 1
    }
  },
  components: {},
  methods: {
    onAnimationfinish(e) {
      this.current = e.detail.current + 1
    },
    onClick(index) {
      this.$emit('click', index)
    }
  }
}
</script>

<style lang="scss" scoped>
.swiper-item {
  position: relative;
  height: 100%;
}

.swiper-item image {
  width: 100%;
  height: 100%;
}
.swiper-box {
  position: relative;
  .indicato {
    position: absolute;
    right: 20rpx;
    bottom: 20rpx;
    height: 30rxp;
    line-height: 44rpx;
    padding: 0 20rpx;
    border-radius: 22rpx;
    font-size: $uni-font-size-sm;
    background-color: rgba($color: #000000, $alpha: 0.5);
    color: #fff;
  }
}
</style>
