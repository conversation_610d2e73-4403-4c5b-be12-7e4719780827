<template>
  <view class="page">
    <view class="adv">
      <view class="swiper_container">
        <view class="swiper">
          <my-swiper height="424rpx" :focus="adv" :rounded="false" :autoplay="true" :interval="4000" :indicatorDots="true" :circular="true" indicatorActiveColor="#ffffff"></my-swiper>
        </view>
      </view>
      <view class="search">
        <view class="search-left">
          <!-- <view> -->
            <my-icon type="ic_sousuo" color="#b9b9b9" size="36rpx"></my-icon>
            <input v-model="params.keyword" @confirm="handelSearch" placeholder="找店铺 找办公室 找厂房" placeholder-style="color: #b9b9b9" style="font-size: 28rpx"/>
          <!-- </view> -->
        </view>
        <view class="search-right" @click="toRelease">
          <my-icon type="ic_tianjia" color="#fff" size="30rpx"></my-icon>
          <text class="add-text">发布</text>
        </view>
      </view>
    </view>
    <view class="top-nav">
      <my-grid @click="toNav" :options="nav" column-num="5" :fontSize="12" :show-border="false"></my-grid>
    </view>
    <!-- 广告位 -->
    <!-- <view class="swiper-container" v-if="adv.length > 0">
      <view class="swiper-con">
        <view class="swiper">
          <my-swiper
            :focus="adv"
            is_adv
            :autoplay="true"
            :interval="4000"
            :indicatorDots="adv.length > 1"
            :circular="true"
            indicatorActiveColor="#ffffff"
            height="140rpx"
          ></my-swiper>
        </view>
      </view>
    </view> -->
    <view class="screen-tab flex-box" id="tab_top">
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(1)">
        <text :class="nowTab == 1 ? 'select' : ''">{{ catesName }}</text>
        <my-icon :type="nowTab == 1 ? 'ic_up' : 'ic_down'" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(2)">
        <text :class="nowTab == 2 ? 'select' : ''">{{ areaName }}</text>
        <my-icon :type="nowTab == 2 ? 'ic_up' : 'ic_down'" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(3)" v-if="price.length > 0">
        <text :class="nowTab == 3 ? 'select' : ''">{{ priceName }}</text>
        <my-icon :type="nowTab == 3 ? 'ic_up' : 'ic_down'" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(4)">
        <text :class="nowTab == 4 ? 'select' : ''">{{ distanceName }}</text>
        <my-icon :type="nowTab == 4 ? 'ic_up' : 'ic_down'" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(5)">
        <text :class="nowTab == 5 ? 'select' : ''">{{ typeName }}</text>
        <my-icon :type="nowTab == 5 ? 'ic_up' : 'ic_down'" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <scroll-view scroll-y class="screen-panel" :class="nowTab == 1 ? 'show' : ''" @touchmove.stop.prevent="stopMove">
        <view class="more-screen-item" v-for="cate in cates" :key="cate.id">
          <view class="title">{{cate.title}}</view>
          <view class="options flex-box">
            <view
              class="options-item"
              @click="selectCates(item)"
              :class="params.catid == item.catid && params.parentid == item.parentid ? 'active' : ''"
              v-for="(item, index) in cate.options"
              :key="index"
              >{{ item.name }}</view
            >
          </view>
        </view>
      </scroll-view>
      <scroll-view
        scroll-y
        class="screen-panel"
        :class="nowTab == 2 ? 'show' : ''"
        @touchmove.stop.prevent="stopMove"
        v-if="showTab"
      >
        <addressd :addressd="area" ref="showArea" @changes="changeArea"></addressd>
      </scroll-view>
      <scroll-view scroll-y class="screen-panel" :class="nowTab == 3 ? 'show' : ''" @touchmove.stop.prevent="stopMove">
        <view class="more-screen-item">
          <view class="title">价格</view>
          <view class="options flex-box">
            <view
              class="options-item"
              @click="selectPrice(item.value, item.name)"
              :class="params.price == item.value ? 'active' : ''"
              v-for="(item, index) in price"
              :key="index"
              >{{ item.name }}</view
            >
          </view>
        </view>
      </scroll-view>
      <scroll-view scroll-y class="screen-panel" :class="nowTab == 4 ? 'show' : ''" @touchmove.stop.prevent="stopMove">
        <view class="more-screen-item">
          <view class="title">距离</view>
          <view class="options flex-box">
            <view
              class="options-item"
              @click="selectDistance(item.value, item.name)"
              :class="params.distance == item.value ? 'active' : ''"
              v-for="(item, index) in distance"
              :key="index"
              >{{ item.name }}</view
            >
          </view>
        </view>
      </scroll-view>
      <scroll-view scroll-y class="screen-panel more-panel" :class="nowTab == 5 ? 'show' : ''">
        <view class="more-screen-item">
          <view class="title">面积(㎡)</view>
          <view class="options flex-box">
            <view
              class="options-item"
              @click="selectOption({ space: item.value }, 'space')"
              :class="params.space == item.value ? 'active' : ''"
              v-for="(item, index) in space"
              :key="index"
              >{{ item.name }}</view
            >
          </view>
        </view>
        <view class="more-screen-item" v-if="types.length > 0">
          <view class="title">{{params.catid == 5 ? '土地用途' : '类型'}}</view>
          <view class="options flex-box">
            <view
              class="options-item type-item"
              @click="selectOption({ type_id: item.id }, 'type_id')"
              :class="params.type_id == item.id ? 'active' : ''"
              v-for="(item, index) in types"
              :key="index"
              >{{ item.title }}</view
            >
          </view>
        </view>
        <view class="flex-box padding-20">
          <button size="medium" type="default" @click="resetMore">重置</button>
          <button size="medium" type="primary" @click="selectMore()">确定</button>
        </view>
      </scroll-view>
    </view>
    <view class="filter_list">
      <tab-bar :tabs="filter_list" :fixedTop="false" :showLine="false" :nowIndex="current_filter_index">
        <view
          class="filter_item"
          :class="{ active: index === current_filter_index }"
          :id="'i' + index"
          v-for="(item, index) in filter_list"
          :key="index"
          @click="onClickFilter(item, index)"
          >{{ item.name }}</view
        >
      </tab-bar>
    </view>
    <view class="house_list">
      <block v-for="item in listsData" :key="item.id">
        <list-item v-if="item.parentid == 1" :item-data="item" type="sale" @click="toDetail"></list-item>
        <list-item v-if="item.parentid == 2" :item-data="item" type="rent" @click="toDetail"></list-item>
        <list-item v-if="item.parentid == 3" :item-data="item" type="transfer" @click="toDetail"></list-item>
      </block>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
    <view
      class="mask"
      :class="nowTab > 0 ? 'show' : ''"
      @click="
        () => {
          nowTab = 0
          showPannel = false
        }
      "
      @touchmove.stop.prevent="stopMove"
    ></view>
  </view>
</template>

<script>
import titleBar from '../components/titleBar.vue'
import myIcon from '../components/myIcon'
import myGrid from '../components/myGrid'
// import addressd from '../components/jm-address/jm-address'
import addressd from './components/address'
import { uniLoadMore, uniList, uniListItem } from '@dcloudio/uni-ui'
import tabBar from '../components/tabBar.vue'
import listItem from './components/listItem.vue'
import {checkAuth,isIos,formatImg} from "../common/index.js"
import wx from "weixin-js-sdk"
import mySwiper from '@/components/mySwiper'
import search from "../components/search.vue"
export default {
  components: {
    titleBar,
    myIcon,
    myGrid,
    addressd,
    uniLoadMore,
    uniList,
    uniListItem,
    tabBar,
    listItem,
    mySwiper,
    search
  },
  data() {
    return {
      get_status: 'loading',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了',
      },
      params: {
        parentid: '',
        catid: '',
        price: '',
        distance: '',
        areaid: '',
        space: '',
        keyword: '',
        label: '',
        page: 1,
        rows: 20,
      },
      showTab: false,
      nowTab: 0,
      area: [],
      areaName: '区域',
      typeName: '更多',
      adv: [],
      distance: [],
      space: [],
      types: [],
      filter_list: [],
      listsData: [],
      price: '',
      scrollTop: 0,
      scrollTopOffset: 0,
      showPannel: false,
      current_filter_index: 0,
      isScroll: false,
      nav: [],
      cates: []
    }
  },
  computed: {
    catesName() {
      let name = ''
      if (this.params.catid && this.cates.length > 0) {
        let item = this.cates.find((f) => {
          return f.id == this.params.catid
        })
        if (item && item.options) {
          let option = item.options.find((f) => {
            return f.catid == this.params.catid && f.parentid == this.params.parentid
          })
          if (option && option.name) {
            name = option.name
          } else {
            name = item.title
          }
        }
        // let res = this.cates.find((item) => {
        //   return item.id == this.params.catid
        // })
        // if (res && res.title) {
        //   name = res.title
        // }
      }
      if(name){
        uni.setNavigationBarTitle({
          title: name
        })
      }
      return name||'类别'
    },
    distanceName() {
      let name = '距离'
      if (this.params.distance && this.distance.length > 0) {
        let res = this.distance.find((item) => {
          return item.value == this.params.distance
        })
        if (res && res.name) {
          name = res.name
        }
      }
      return name
    },
    priceName() {
      let name = '价格'
      if (this.params.price && this.price.length > 0) {
        let res = this.price.find((item) => {
          return item.value == this.params.price
        })
        if (res && res.name) {
          name = res.name
        }
      }
      return name
    },
    status_top() {
      return this.$store.state.systemInfo.statusBarHeight
    },
  },
  onLoad(options) {
    for (let key in options) {
      this.params[key] = options[key]
      this.isScroll = true
    }
    this.current_filter_type =''
    if(options.zhongjie){
      this.current_filter_type="zhongjie"
    }
    if(options.info_level){
      this.current_filter_type="info_level"
    }
    if(options.label){
      this.current_filter_type="label"
    }
    this.showTab = true
    this.getScreen()
    this.getData()
  },
  onShow() {
    setTimeout(() => {
      if (this.$store.state.updatePageData) {
        this.$store.state.updatePageData = false
        this.getLocation()
      }
    }, 150)
  },
  onUnload() {
    if (!uni.getStorageSync('no_watch_search_key')) {
      uni.$off('handleSearch')
    } else {
      uni.removeStorageSync('no_watch_search_key')
    }
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop
    const query = uni.createSelectorQuery().in(this)
    if (this.showPannel) {
      uni.pageScrollTo({
        scrollTop: this.scrollTopOffset,
        duration: 0,
      })
    }
  },
  methods: {
    getScreen() {
      this.$ajax.get(
        'estate/conditions',
        { catid: this.params.catid, parentid: this.params.parentid },
        (res) => {
          res.data.area.unshift({ areaid: '', parentid: 0, mapx: '', mapy: '', areaname: '不限' })
          let area = res.data.area
          this.area_list = res.data.area
          // this.area = this.getJiedao(area, 'areaid', 'parentid', 'city')
          this.area = area
          this.cates = res.data.cates
          this.nav = res.data.nav
          this.price = res.data.price
          this.distance = res.data.distance
          this.space = res.data.space
          this.types = res.data.types
          let label = [
            {
              name: '全部',
              type: 'all',
              id: 0,
              optionId:0,
            },
            {
              name: '精选',
              type: 'info_level',
              id: 2,
              optionId:3
            },
            {
              name: '经纪人',
              type: 'zhongjie',
              id: 2,
              optionId:2
            },
          ]
          if (res.data.is_show_personal_tab==1){
						label=[
							{
								name:'全部',
								type:'all',
								id:0,
								optionId:0
							},
							{
								name:'精选',
								type:'info_level',
								id:2,
								optionId:3
							},
							{
								name:'个人',
								type:'zhongjie',
								id:1,
								optionId:1
							},
							{
								name:'经纪人',
								type:'zhongjie',
								id:2,
								optionId:2
							}
						]
					}
          let label2 = res.data.label.map((item) => {
            item.type = 'label'
            return item
          })
          this.filter_list = label.concat(label2)
          if (this.params.label) {
            this.$nextTick(() => {
              this.current_filter_index = this.filter_list.findIndex((item) => item.id == this.params.label)
            })
          }
          if (this.params.zhongjie) {
            this.$nextTick(() => {
              this.current_filter_index = label.findIndex((item) => item.optionId== this.params.zhongjie)
            })
          }
          if (this.params.info_level) {
            this.$nextTick(() => {
              this.current_filter_index = label.findIndex((item) => item.optionId== 3)
            })
          }
          this.showTab = true
        },
        (err) => {
          console.log(err)
        }
      )
    },
    getData() {
      this.params.lat = this.$store.state.position.lat
      this.params.lng = this.$store.state.position.lng
      if (this.params.page == 1) {
        this.listsData = []
      }
      this.showPannel = false
      this.get_status = 'loading'
      this.$ajax.get('estate/index', this.params, (res) => {
        if (this.params.page == 1) {
          this.listsData = []
        }
        this.adv = res.data.adv
        if (res.data.code == 1) {
          this.listsData = this.listsData.concat(res.data.list)
          if (res.data.list.length < this.params.rows) {
            this.get_status = 'noMore'
          } else {
            this.get_status = 'more'
          }
        } else {
          this.get_status = 'noMore'
        }
        if (res.data.share) {
          this.share = res.data.share
          
        }else {
          this.share ={}
        }
        var shareLink = window.location.href.split("?")[0]
        this.share.link =`${shareLink}`
        var opt ="?"
        for (const key in this.params) {
            const element = this.params[key];
            if (this.params[key]&&key!=="page"&&key!=="rows"){
              opt +=`${key}=${this.params[key]}&`
            }
            
        }
        var _opt = opt.substring(0, opt.length-1)
          this.share.link +=_opt
        this.getWxConfig()
        this.$nextTick(()=> {
          if (this.isScroll) {
            this.scroppTo(()=> {
              this.isScroll = false
            })
          }
        })
        uni.stopPullDownRefresh()
      }, (err)=> {
        console.log(err)
        uni.stopPullDownRefresh()
      })
      
    },
    changeArea(e) {
      console.log(e)
      this.current_filter_index = -1
      this.params.areaid = e.district_id ? e.district_id : e.city_id ? e.city_id : e.province_id ? e.province_id : ''
      this.areaName = e.district ? e.district : e.city ? e.city : e.province ? e.province : ''
      if (!this.params.areaid) {
        this.areaName = '区域'
      }
      this.nowTab = 0
      this.params.page = 1
      this.getData()
    },
    selectCates(item) {
      this.current_filter_index = -1
      this.params.catid = item.catid
      this.params.parentid = item.parentid
      this.params.zhongjie = ''
      this.params.info_level = ''
      this.params.label = ''
      this.nowTab = 0
      this.params.page = 1
      this.params.type_id = ''
      this.getScreen()
      this.getData()
    },
    selectPrice(value) {
      this.current_filter_index = -1
      this.params.price = value
      this.nowTab = 0
      this.params.page = 1
      this.getData()
    },
    selectDistance(value) {
      this.current_filter_index = -1
      this.params.distance = value
      this.nowTab = 0
      this.params.page = 1
      // #ifdef MP
      checkAuth('scope.userLocation', {
        authOk: () => {
          this.getLocation()
        },
        success: () => {
          this.getLocation()
        },
        fail: () => {
          this.show_dialog = true
        },
      })
      // #endif
      // #ifdef H5 || APP-PLUS
      this.getLocation()
      // #endif
    },
    getLocation() {
      let url;
				if(isIos()){
					url = this.$store.state.firstUrl
				}else{
					url = window.location.href
				}
				this.$ajax.get('/wechat/index/signature.html',{url:url},res=>{
					if(res.data.code == 1){
						res.data.config.jsApiList = ['getLocation','updateAppMessageShareData','updateTimelineShareData']
						wx.config(res.data.config)
						this.$store.state.getPosition(wx,()=>{
							this.getData()
						})
					}
				})
    },
    getJiedao(a, idStr, pIdStr, chindrenStr) {
      var r = [],
        hash = {},
        id = idStr,
        pId = pIdStr,
        children = chindrenStr,
        i = 0,
        j = 0,
        len = a.length
      for (; i < len; i++) {
        a[i].label = a[i].name
        delete a[i].name
        hash[a[i][id]] = a[i]
      }
      for (; j < len; j++) {
        var aVal = a[j],
          hashVP = hash[aVal[pId]]

        if (hashVP) {
          !hashVP[children] && (hashVP[children] = [])

          hashVP[children].push(aVal)
        } else {
          r.push(aVal)
        }
      }
      return r
    },
    scroppTo(fun) {
      const query = uni.createSelectorQuery().in(this)
      query
        .select('#tab_top')
        .fields({ rect: true, scrollOffset: true }, (data) => {
          if (data) {
            if (data.top <= 44) {
              fun && fun()
              return
            }
            // #ifdef H5
            this.scrollTopOffset = (this.scrollTop || 0) + data.top + 1
            // #endif
            // #ifndef H5
            this.scrollTopOffset = (this.scrollTop || 0) + data.top - (44 + this.status_top) + uni.upx2px(100)
            // #endif
          }
          uni.pageScrollTo({
            duration: 120,
            // #ifdef H5
            scrollTop: this.scrollTopOffset,
            // #endif
            // #ifndef H5
            scrollTop: this.scrollTopOffset,

            // #endif
            success: () => {
              if (fun) {
                fun()
              }
            },
          })
        })
        .exec()
    },
    switchTab(index) {
      this.scroppTo(() => {
        // this.scrollTop =200
        let timeout = setTimeout(() => {
          if (this.nowTab == index) {
            this.nowTab = 0
            this.showPannel = false
            // this.$refs.ershou.allMove()
          } else {
            this.nowTab = index
            if (index == 2) {
              this.$refs.showArea.showAddress()
            }
            this.showPannel = true
            // this.$refs.ershou.stopMove()
          }
        }, 200)
      })
    },
    resetMore() {
      this.params.type_id = ''
      this.params.space = ''
    },
    selectMore() {
      this.current_filter_index = -1
      this.nowTab = 0
      this.params.page = 1
      this.getData()
    },
    selectOption(obj, type) {
      switch (type) {
        case 'type_id':
          if (this.params.type_id === obj.type_id) {
            obj.type_id = ''
          }
        case 'space':
          if (this.params.space === obj.space) {
            obj.space = ''
          }
      }
      this.current_filter_index = -1
      this.params = Object.assign({}, this.params, obj)
    },
    onClickFilter(item, index) {
      this.current_filter_index = index
      this.current_filter_type = item.type
      this.params.info_level = ''
      this.params.type = ''
      if (item.type === 'all') {
        this.typeName = '更多'
        this.resetMore()
        this.params.areaid = ''
        this.areaName = '区域'
        this.params.price = ''
        this.params.distance = ''
        this.params.label = ''
        this.params.lat = ''
        this.params.lng = ''
        this.params.zhongjie = ''
        this.params.page = 1
        this.getData()
        return
      }
      this.params.zhongjie = ''
      this.params.info_level = ''
      this.params.label = ''
      this.params[item.type] = item.id
      this.params.page = 1
      this.getData()
    },
    toDetail(e) {
      if (!e.detail.id) {
        return
      }
      this.$store.state.tempData = e.detail
      if (e.detail.parentid == 1) {
        this.$navigateTo('/commercial/sale/detail?id=' + e.detail.id)
      }
      if (e.detail.parentid == 2) {
        this.$navigateTo('/commercial/rent/detail?id=' + e.detail.id)
      }
      if (e.detail.parentid == 3) {
        this.$navigateTo('/commercial/transfer/detail?id=' + e.detail.id)
      }
    },
    handelInput(e) {
      this.params.keyword = e.detail.value
    },
    handelSearch() {
      this.params.page = 1
      this.getData()
    },
    toNav(e) {
      this.$navigateTo(this.nav[e.index].path)
    },
    toRelease() {
      uni.switchTab({
				url:'/pages/add/add'
			})
    }
  },
  onPullDownRefresh() {
    this.params.page = 1
    this.getData()
  },
  onReachBottom() {
    if (this.get_status == 'more') {
      this.params.page = this.params.page + 1
      this.getData()
    }
  },
}
</script>

<style lang="scss" scoped>
.page {
  background: #fff;
}
.search-box {
  // margin-left: 20rpx;
  align-items: center;
  // padding: 10rpx 20rpx;
  // background-color: #f5f5f5;
  // color: #999;
  border-radius: 8rpx;
  .search-left {
    margin-right: 20rpx;
  }
  .inp {
    margin-left: 20rpx;
    .has_key {
      color: #333;
    }
  }
}
.seach_btn {
  align-items: center;
  padding: 0 24rpx;
  .text {
    margin-left: 16rpx;
  }
}
.swiper-container {
  background: #fff;
  .swiper-con {
    padding: 32rpx 48rpx 0;
  }
  .swiper {
    border-radius: 8rpx;
    overflow: hidden;
  }
}
/* #ifdef H5 */
.screen-tab {
  position: sticky;
  top: 0;
  // margin-top: 44px;
  box-sizing: border-box;
  padding: 0 48rpx;
}
.screen-panel {
  top: 0;
  margin-top: 80rpx;
  display: none;
}
.screen-panel.show {
  top: 37px;
  left: 0;
  display: block;
}
/* #endif */
/* #ifndef H5 */
.screen-tab {
  position: sticky;
  top: var(--window-top);
  box-sizing: border-box;
  padding: 0 48rpx;
}
.screen-panel {
  top: var(--window-top);
  margin-top: 90rpx;
  left: 0;
  // display: none;
  transform: translateY(-130%);
  &.show{
    transform: translateY(0);
    // display: block;
  }
}
/* #endif */
.filter_list {
  padding-left: 48rpx;
  padding-top: 24rpx;
  background-color: #fff;
  .filter_item {
    display: inline-block;
    padding: 10rpx 20rpx;
    border-radius: 4rpx;
    line-height: 1;
    box-sizing: border-box;
    margin-right: 24rpx;
    font-size: 24rpx;
    background-color: #f5f5f5;
    border: 1rpx solid #f5f5f5;
    color: #999;
    &.active {
      background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
      color: $uni-color-primary;
      border: 1rpx solid $uni-color-primary;
    }
  }
}
.house_list {
  min-height: 100vh;
  padding: 0 48rpx;
  background-color: #fff;
}
.more-screen-item {
  padding: 0 48rpx;
  margin-top: 48rpx;
  &:last-child {
    margin-bottom: 48rpx;
  }
}
.more-screen-item .title {
  padding: 0 10rpx 24rpx 10rpx;
  line-height: normal;
  font-weight: bold;
}
.more-screen-item .options .options-item {
  min-width: 198rpx;
  width: fit-content;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 10rpx;
  border-radius: 0;
  border: none;
  &.active {
    background: #FFF2F3;
    color: #FB656A;
    border: none;
  }
}
.search-title {
  padding: 7px 0;
   ::v-deep .uni-input-wrapper {
    text-align: left;
  }
}
.top_custom	{
		margin-top: 20rpx;
	}
	.top_grid{
    display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
    flex-wrap: wrap;
		// padding: 20rpx 0 10rpx;
    margin: 0 48rpx;
		background: #FFFFFF;
		&_item{
			text-align: center;
			width: 80rpx;
			&_icon{
				width: 80rpx;
				height: 80rpx;
				margin: 0 auto;
				overflow: hidden;
				image{
					width: 100%;
					height: 100%;
				}
			}
			&_title{
				margin-top: 4rpx;
				font-size: 24rpx;
				color: #333333;
				letter-spacing: 0;
				text-align: center;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
  .top-nav {
    padding: 32rpx 48rpx;
  }
  .screen-tab-item .select {
    color: #FB656A;
  }
  .adv {
    position: relative;
    margin-bottom: 66rpx;
    .adv-img {
      height: 284rpx;
      image {
        width: 100%;
        height: 284rpx;
      }
    }
    .search {
      position: absolute;
      bottom: -50rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 654rpx;
      height: 96rpx;
      display: flex;
      .search-left {
        flex: 1;
        background: #fff;
        box-shadow: 0px 7px 7px -7px #ccc;
        padding: 28rpx 20rpx 28rpx 70rpx;
        display: flex;
        align-items: center;
        font-size: 28rpx;
        input {
          font-size: 28rpx;
          margin-left: 4rpx;
        }
      }
      .search-right {
        width: 162rpx;
        background: #0A81F3;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 28rpx;
        .add-text {
          margin-left: 8rpx;
        }
      }
    }
  }
</style>