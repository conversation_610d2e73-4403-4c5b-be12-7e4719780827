<template>
  <view class="list" :style="{width: 340*list.length+'rpx'}">
    <view class="item" v-for="(house, index) in list" :key="index" @click="onClick(house)">
      <view class="price">
        <text>{{house.total_price||'待定'}}</text>
      </view>
      <view class="avg_price">{{house.price||'待定'}}元/m²</view>
      <view class="house_spe">
        <text class="text">{{house.shi}}室{{house.ting}}厅{{house.wei}}卫</text>
        <text class="mj">{{house.jzmj}}</text>
      </view>
      <view class="name">{{house.house_name||house.title}}</view>
    </view>
  </view>
</template>

<script>
export default {
  name: '',
  components: {},
  data () {
    return {}
  },
  props:{
    list: {
      type: Array,
      default: ()=>[]
    }
  },
  methods: {
    onClick(house){
      this.$emit('clickitem', house)
      this.$navigateTo(`/online/sale_detail?id=${house.id}&bid=${house.build_id}`)
    }
  }
}
</script>

<style scoped lang="scss">
.list{
  display: flex;
  height: 284rpx;
  flex-direction: row;
  flex-wrap: nowrap;
  background-size: 100%;
  background-position: 0 0;
  background-repeat: no-repeat;
  .item{
    flex-shrink: 0;
    box-sizing: border-box;
    width: 282rpx;
    padding: 24rpx;
    margin-right: 24rpx;
    border: 1rpx solid #e3e3e3;
    background-color: #fff;
    .price{
      margin-bottom: 12rpx;
      font-size: 58rpx;
      font-weight: bold;
      color: $uni-color-primary;
    }
    .avg_price{
      margin-bottom: 24rpx;
      font-size: 24rpx;
      color: #6f6f6f;
    }
    .house_spe{
      margin-bottom: 12rpx;
      font-size: 32rpx;
      font-weight: bold;
      .mj{
        margin-left: 24rpx;
      }
    }
    .name{
      font-size: 28rpx;
      color: #6f6f6f;
    }
  }
}
</style>