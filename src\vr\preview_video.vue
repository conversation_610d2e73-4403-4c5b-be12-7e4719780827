<template>
	<view class="page">
	  <view class="video">
		<swiper :style="{'height':height}" class="banner" :indicator-dots="false"  :autoplay="false"  @change="onChange" :vertical="true" >
		  <swiper-item v-for="(video,index) in videos" :key="index" >
			<view class="video_item" :class ='{"video_play_scale":show_pinglun}'>
			  <!-- #ifndef H5 -->
			  <video  class ="video_play"   :src="video.path" style="width:100%"  :id='"video_play"+index'  :show-fullscreen-btn="false"  :controls="false" :show-play-btn="false" :show-center-play-btn="false "  :vslide-gesture-in-fullscreen="false" :enable-progress-gesture="false" :disable-touch ='show_pinglun'  :show-mute-btn="false" :title="title" :direction="0" @play="playVideo" @pause="pauseVideo" @waiting="waitingVideo" object-fit="contain">
				
  
			  </video>
			  
			  <!-- #endif -->
			  <!-- #ifdef H5 -->
				<view
				  class="video-js"
				  :ref="'video_play'+index"
				  :id='"video_play"+index'
				  style="width: 100%; height: 100%"
				></view>
			  <!-- #endif -->
			  <cover-view class="fix_right">
				  <cover-view  class="prelogo" @click ="toDetail">
					<cover-image mode='widthFix' :src='memberObj.prelogo | imageFilter("m_80")'></cover-image>
				  </cover-view>
				  <cover-view class="right_oper" @click ="collect">
					<cover-view class="right_img">
					  <cover-image mode='widthFix' v-if='is_follow==0'  :src='"/yidongduan/vr_video/collect.png" | imageFilter("m_80")'></cover-image>
					  <cover-image mode='widthFix' v-if='is_follow==1'  :src='"/yidongduan/vr_video/collect_ac.png" | imageFilter("m_80")'></cover-image>
					</cover-view>
					<cover-view class="right_con" :class="{red:is_follow==1}" >
					   {{type==1?'关注':"收藏"}}
					</cover-view>
				  </cover-view>
				  <cover-view class="right_oper" @click ="share">
					<cover-view class="right_img">
					  <cover-image mode='widthFix'  :src='"/yidongduan/vr_video/share.png" | imageFilter("m_80")'></cover-image>
					</cover-view>
					<cover-view class="right_con">
					  转发
					</cover-view>
				  </cover-view>
				  <cover-view class="right_oper" @click="ask">
					<cover-view class="right_img">
					  <cover-image mode='widthFix'  :src='"/yidongduan/vr_video/ask.png" | imageFilter("m_80")'></cover-image>
					</cover-view>
					<cover-view class="right_con">
					  咨询
					</cover-view>
				  </cover-view>
				  <cover-view class="right_oper" @click ="tel">
					<cover-view class="right_img">
					  <cover-image mode='widthFix'  :src='"/yidongduan/vr_video/tel.png" | imageFilter("m_80")'></cover-image>
					</cover-view>
					<cover-view class="right_con">
					  电话
					</cover-view>
				  </cover-view>
				  <!-- <cover-view class="right_oper" @click='showPinglun'>
					<cover-view class="right_img">
					  <cover-image mode='widthFix'  :src='"/yidongduan/vr_video/pinglun.png" | imageFilter("m_80")'></cover-image>
					</cover-view>
					<cover-view class="right_con">
					  评论
					</cover-view>
				  </cover-view> -->
			  </cover-view>
			  <!-- <template v-if ='video.info'> -->
				<view class="fix_bottom">
				  <view class="title">@{{ detail.title}}</view>
				  <view class="info_labels flex-row items-center">
					<template v-for ="(item,idx) in detail.label ">
					  <text :class="['info_label'+idx]" class ="info_label" v-if ='idx<3'  :key ="idx">{{item}}</text>
					</template>
					
				  </view>
				  <view class="info_c">
					{{detail.video_desc}}
				  </view>
				  <view class="info_btn" @click ="toDetail">
				   查看详情
				  </view>
  
				</view>
			  <!-- </template> -->
			  <view class="pinglun" :class="{active:show_pinglun}">
				<view class="title flex-row items-center">
				  <view class="title_c">
					12条评论
				  </view>
				  <view class="close" @click='show_pinglun=false'>
					<image :src="'/zhaofang/<EMAIL>' | imageFilter('m_80') " mode='widthFix'></image>
				  </view>
				</view>
				<view class="pinglun_list">
				  <!-- <yVideoSlide :data ='pinglun_list'></yVideoSlide> -->
				  <!-- <view class="pinglun_item flex-row " v-for ='(item,index) in pinglun_list' :key ="index">
					<view class="pinglun_prelogo">
					  <image mode="widthFix" :src='item.prelogo'></image>
					</view>
					<view class="pinglun_con">
					  <view class="cname">
						{{item.name}}
					  </view>
					  <view class="p_content">
						{{item.content}}
					  </view>
					  <view class="p_oper flex-row items-center">
						<view class="ctime">刚刚</view>
						<view class="reply">
						  回复
						</view>
					  </view>
					  
					</view>
					<template v-if ="item.children.length">
					  <pinglun :data="item"></pinglun>
					</template>
				  </view> -->
				</view>
				<!-- <input v-if ='show_pinglun'  class ="inp" :placeholder="placeholder" type="text"> -->
				
			  </view>
			</view>
		  </swiper-item>
		</swiper>
	  </view>
	  <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
	</view>
  </template>
  
  <script>
  import yVideoSlide from './components/y-video-slide'
  // #ifdef H5
  import videojs from 'video.js'
  import 'video.js/dist/video-js.min.css'
  import 'videojs-flash'
  
  // #endif
  import allTel from '@/common/all_tel.js'
  import getChatInfo from '@/common/get_chat_info'
  import checkLogin from '@/common/utils/check_login'
  export default {
	components:{
	  yVideoSlide
	},
	data(){
	  return  {
		title:'',
		height:"100vh",
		videos:[
		  
		],
		type:1,
		params:{
		  page:1,
		  info_id:'',
  
		},
		pinglun_list:[
		  {
			nickname:'q',
			avatar:'https://images.tengfangyun.com/images/icon/def_head.jpg?x-oss-process=style/w_80',
			content:"点赞",
			id:1,
			replies:1,
			commentChildren:[
			  {
				nickname:'b',
				id:2,
				content:"回复q",
				avatar:'https://images.tengfangyun.com/images/icon/def_head.jpg?x-oss-process=style/w_80',
				commentChildren:[
				  {
					nickname:'b',
					id:3,
					avatar:'https://images.tengfangyun.com/images/icon/def_head.jpg?x-oss-process=style/w_80',
					content:"回复b",
				  },
				]
			  },
			  
  
			]
		  }
		],
		detail:{},
		tel_res:{},
		current:0,
		show_pinglun:false ,
		placeholder:"回复：",
		show_tel_pop:false,
		memberObj:{},
		is_follow:0
	  }
	},
	computed:{
		is_open_im() {
		  return this.$store.state.im.ischat
		}
	},
	onLoad(options){
	  if (options.id) {
		this.params.info_id =  options.id
	  }
	  if(options.type){
		this.type=options.type
	  }
	  if(options.video_id){
		this.params.video_id=options.video_id
	  }
	  // this.params.info_id=68374
	  // this.type=1
	  this.getList()
	  // this.onChange({target:{current:0}})
	  console.log(uni.getSystemInfoSync());
	},
	methods:{
	  setTitle(){
		setTimeout(() => {
			this.title='设置的title'
		}, 300);
	  },
	  guanzhu(){
		let url ='build/followBuild.html'
		if(this.is_follow){
		  url ='build/cancelFollowBuild.html'
		}
		this.$ajax.get(url,{bid:this.detail.id},res=>{
		  if (res.data.code ==1){
			this.is_follow = this.is_follow==0?1:0
		  //  this.$set(this.detail,"is_follow",this.detail.is_follow==0?1:0)
		  }
			uni.showToast({
			  title:res.data?res.data.msg :"",
			  icon:'none'
			})
		  
		})
	  },
	  collect(){
		if (this.type==1) {
		  this.guanzhu()
		}
		if (this.type==2) {
		  this.infoCollect()
		}
		if (this.type==3) {
		  this.estateCollect()
		}
  
  
		// let url ='build/followBuild.html'
		// if(this.type==2){
		//   // url='build/followBuild.html'
		// }
		// this.$ajax.get(url,)
	  },
	  estateCollect(){
		let url ='estate/infoCollect'
		if(this.is_follow){
		  url ='estate/cancelCollect'
		}
		this.$ajax.get(url,{id:this.detail.id},res=>{
		  if (res.data.code ==1){
			this.is_follow = this.is_follow==0?1:0
		  //  this.$set(this.detail,"is_follow",this.detail.is_follow==0?1:0)
		  }
			uni.showToast({
			  title:res.data?res.data.msg :"",
			  icon:'none'
			})
		  
		})
	  },
	  infoCollect(){
		let url ='house/infoCollect.html'
		if(this.is_follow){
		  url ='house/cancelCollect.html'
		}
		this.$ajax.get(url,{id:this.detail.id},res=>{
		  if (res.data.code ==1){
			this.is_follow = this.is_follow==0?1:0
		  //  this.$set(this.detail,"is_follow",this.detail.is_follow==0?1:0)
		  }
			uni.showToast({
			  title:res.data?res.data.msg :"",
			  icon:'none'
			})
		  
		})
	  },
	  ask(){
		console.log(2342);
		var user_id = this.memberObj.mid||this.memberObj.uid||this.memberObj.id
		var identity_id = this.memberObj.adviser_id||this.memberObj.uid||this.memberObj.id
		this.advAsk({user_id:user_id,identity_id:identity_id})
	  },
	  advAsk(e) {
		if (this.is_open_im == 1) {
		  //开聊天
		  // #ifdef MP-WEIXIN
		  if (this.type==1){
			  this.$store.state.buildInfo = {
				id: this.detail.id,
				title: this.detail.title,
				type: 'build',
				image: this.detail.img
			  }
			getChatInfo(e.user_id, 3, this.id)
		  }
		  if (this.type==2 ){
			// let thumb
			// this.imgs = this.detail.imgs
			// if(this.imgs.length>0){
			//   thumb = this.imgs[0]
			// }
			let text = `${this.detail.fangjia?this.detail.fangjia+'万':'面议'}`
			let type='ershou'
			if (this.detail.parentid==2){
			  text =`${this.detail.zujin?this.detail.zujin+'元/月':'面议'}`
			  type='renting'
			}
		   
			this.$store.state.buildInfo = {
			  id: this.detail.id,
			  title: this.detail.title,
			  type: type,
			  image: this.detail.img.length>0?this.detail.img[0].url:"",
			  desc: `${this.detail.shi}室${this.detail.ting}厅${this.detail.wei}卫/${this.detail.mianji}m²/${this.detail.chaoxiang||''}`,
			  price:``
			}
			getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id, this.detail.parentid==1? 6:7)
		  }
		  console.log(123);
		  if (this.type==3){
			
			this.$store.state.buildInfo = {
			  id: this.detail.id,
			  title: this.detail.title,
			  type: 'commercial',
			  catid: this.detail.catid,
			  image: this.detail.img,
			  desc: `${this.detail.mianji}${this.detail.mianji_unit}`,
			  price:`${this.detail.price?this.detail.price+this.detail.price_unit:'面议'}`
			}
			getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id, this.detail.parentid==1?32:(this.detail.parentid==2?33:34))
		  }
		  // this.$store.state.buildInfo = {
		  //   id: this.id,
		  //   title: this.detail.title,
		  //   type: 'build',
		  //   image: this.img[0]
		  // }
		 
		  // #endif
		  // #ifndef MP-WEIXIN
		  checkLogin({
			success: (res)=>{
			  if(this.type ==1 ){
				this.$store.state.buildInfo = {
				  id: this.detail.id,
				  title: this.detail.title,
				  type: 'build',
				  image: this.detail.img
				}
				getChatInfo(e.user_id, 3,this.detail.id)
			  }
			  if (this.type==2 ){
				// let thumb
				// this.imgs = this.detail.imgs
				// if(this.imgs.length>0){
				//   thumb = this.imgs[0]
				// }
				let text = `${this.detail.fangjia?this.detail.fangjia+'万':'面议'}`
				let type='ershou'
				if (this.detail.parentid==2){
				  text =`${this.detail.zujin?this.detail.zujin+'元/月':'面议'}`
				  type='renting'
				}
			  
				this.$store.state.buildInfo = {
				  id: this.detail.id,
				  title: this.detail.title,
				  type: type,
				  image: this.detail.img.length>0?this.detail.img[0].url:"",
				  desc: `${this.detail.shi}室${this.detail.ting}厅${this.detail.wei}卫/${this.detail.mianji}m²/${this.detail.chaoxiang||''}`,
				  price:``
				}
				getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id,this.detail.parentid==1? 6:7)
			  }
			  if (this.type==3 ){
				 this.$store.state.buildInfo = {
				  id: this.detail.id,
				  title: this.detail.title,
				  type: 'commercial',
				  catid: this.detail.catid,
				  image: this.detail.img,
				  desc: `${this.detail.mianji}${this.detail.mianji_unit}`,
				  price:`${this.detail.price?this.detail.price+this.detail.price_unit:'面议'}`
				}
				getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id, this.detail.parentid==1?32:(this.detail.parentid==2?33:34))
			  }
			 
			},
			fail: (res)=>{
			  // TODO
			  this.$navigateTo("/user/login/login")
			},
			complete:(res)=>{
			  this.$store.state.user_login_status = res.status
			}
		  })
		  // #endif
		} else if (this.is_open_im == 0) {
		  //不开聊天
		  if (this.type ==1 ){
			this.consuDetail(e.identity_id)
		  }else {
			//  this.toAgentDetail(e.identity_id)
		  }
		  
		}
	  },
	  // toAgentDetail(id){
	  //   this.$navigateTo('/pages/agent/detail?id=' + id)
	  // },
	  //转到顾问详情
	  consuDetail(id) {
		if (!id) return
		if (this.is_open_adviser == 1 && this.detail.open_adviser == 1) {
			this.$navigateTo('/pages/consultant/detail?id=' + id)
		}else{
		  console.log("没开启聊天且不是置业顾问,不跳转详情")
		}
	  },
	  tel(){
		// if(this.type==1){
		  this.callBuildMiddleNumber()
		// }
  
	  },
	  retrieveTel(){
		allTel(this.tel_params)
	  },
	  // 拨打楼盘虚拟号码
	  callBuildMiddleNumber() {
		let phoneNumber=""
		 if (this.type==1 ){
			if (this.memberObj){
			
				var identity_id = this.memberObj.identity_id||this.memberObj.adviser_id||this.memberObj.uid||this.memberObj.id
				var tel_type = ""
				if(this.memberObj.isAgent){
				  tel_type = '3'
				}
				if(this.memberObj.isAdviser){
				  tel_type = '2'
				}
				if (!this.memberObj.isAgent &&!this.memberObj.isAdviser) {
				  tel_type = 0
				}
				this.callMiddleNumber(tel_type,identity_id,1,this.id,'build_detail',this.id)
			  
			  
				
			}else {
			
			  this.callMiddleNumber(1,this.detail.id,1,this.detail.id,'build_detail',this.detail.id)
			}
		}
		if(this.type==2){
			this.tel_params={
			  type: 4,
			  callee_id: this.detail.id,
			  scene_type: 4,
			  scene_id:this.detail.id,
			}
			allTel(this.tel_params)
		  
		  
		  // this.callMiddleNumber(4, this.detail.id,1,this.detail.id,'build_detail',this.id)
		}
		if(this.type==3 ) {
		  this.tel_params={
			type: 6,
			scene_type: 6,
			scene_id: this.detail.id,
			callee_id: this.detail.id,
		  }
		  allTel(this.tel_params)
		}
	  },
	  // 请求虚拟号接口
	  callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
		this.tel_params = {
		  type,
		  callee_id,
		  scene_type,
		  scene_id,
		  source,
		  bid,
		  success: (res)=>{
			this.tel_res = res.data
			this.show_tel_pop = true
		  }
		}
		// #ifdef MP-WEIXIN
		allTel(this.tel_params)
		// #endif
		// #ifndef MP-WEIXIN
		// 全局开启中间号且楼盘开启中间号需要检测登录
		if(this.is_open_middle_num == 1 && this.detail.use_middle_call > 0){
		  this.tel_params.intercept_login = true
		  this.tel_params.fail = (res)=>{
			if(res.data.code === -1){
			  this.$store.state.user_login_status = 1
			  this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
			}
			if(res.data.code === 2){
			  this.$store.state.user_login_status = 2
			  this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
			}
		  }
		  allTel(this.tel_params)
		}else{
		  allTel(this.tel_params)
		}
		// #endif
	  },
	  // #ifdef H5
	  share(){
		let link = '';
		// let time =parseInt(+new Date()/1000)
		// if (this.currentUserInfo.sid){
		//   link="https://"+window.location.host+"/h5/pages/new_house/detail?id="+this.detail.id +"&isShare=1&shareType="+this.currentUserInfo.shareType+"&shareId="+this.currentUserInfo.sid+'&cusId=' +this.current_adviser_id +'&isshare=' +this.isshare+"&f_time="+time
		// }else {
		  // link = window.location.href
		// }
		link="https://"+window.location.host+"/h5/vr/prevideo?id="+this.params.info_id +"&video_id="+this.videos[this.current].id+"&type="+this.type
		this.copyWechatNum(link, ()=>{
		  uni.showToast({
			title: '复制成功,去发送给好友吧',
			icon: 'none'
		  })
		})
	  },
	  // #endif
	  // #ifndef H5
	  copyWechatNum(cont) {
		uni.setClipboardData({
		  data: cont,
		  success: res => {
			// uni.showToast({
			//   title: "复制成功",
			//   icon: "none"
			// })
		  }
		})
	  },
	  // #endif
	  // #ifdef H5
	  copyWechatNum(cont, callback) {
		let oInput = document.createElement('textarea')
		oInput.value = cont
		document.body.appendChild(oInput)
		oInput.select() // 选择对象;
		oInput.setSelectionRange(0, oInput.value.length);
		document.execCommand('Copy') // 执行浏览器复制命令
		uni.showToast({
		  title: '复制成功',
		  icon: 'none'
		})
		oInput.blur()
		oInput.remove()
		if(callback) callback()
	  },
	  // #endif
	  onChange(e,c){
		let id=  'video_play'+e.target.current
		this.current = e.target.current
		if( this.loadmore && this.current== this.videos.length-1 &&this.videos.length>=1 ) {
		  if (this.isloading ) return 
		  this.isloading =true
		  this.params.page +=1
		  this.getList()
		}
		if (this.type==1){
		  this.getDetail(this.videos[this.current].bid,this.videos[this.current].id)
		}
		if(this.type==2 || this.type==3){
		  this.getDetail(this.videos[this.current].infoid,this.videos[this.current].id)
		}
		// this.$refs['video_play'+e.target.current][0].$el.removeChild('video')
		// #ifdef H5
		//  this['videoPlayer'+ this.current] && this['videoPlayer'+ this.current].play()
		// if(document.getElementById('video_'+this.current)){
		//   this.$refs['video_play'+e.target.current][0].$el.removeChild("video")
		// }
		let video = document.createElement('video')
		video.id = 'video_'+this.current
		//如果需要全屏幕展现播放器需要增加 object-fit: cover;
		// video.style = 'width: 100%; height: 100%;'
		video.controls = false
		video.setAttribute('playsinline', true) //IOS微信浏览器支持小窗内播放
		video.setAttribute('webkit-playsinline', true) //这个bai属性是ios 10中设置可以让视频在小du窗内播放，也就是不是全屏播放的video标签的一个属性
		video.setAttribute('x5-video-player-type', 'h5') //安卓 声明启用同层H5播放器 可以在video上面加东西
		let source = document.createElement('source')
		source.src = this.videos[this.current].path  //设定的流地址
		//根据流地址的后缀设置好播放类型
		if (source.src.indexOf('.mp4') !== -1) {
		  //mp4类型
		  source.type = 'video/mp4'
		} else if (source.src.indexOf('.m3u8') !== -1) {
		  //hls类型
		  source.type = 'application/x-mpegURL'
		} else if (source.src.indexOf('.flv') !== -1) {
		  //flv类型
		  source.type = 'video/flv'
		} else {
		  //rtmp类型
		  source.type = 'rtmp/hls'
		}
		//将播放源添加到video的子级
		video.appendChild(source)
		//挂载到视频容器中
		console.log(this.$refs);
		this.$nextTick(()=>{
		this.$refs['video_play'+e.target.current][0].$el.appendChild(video)
		this['videoPlayer'+ this.current] = videojs(
		  'video_'+this.current,
		  {
			//视频封面图(activityDetail变量是我业务中用到的，请自行根据实际情况做调整)
			// poster: '',
			//视频标题(activityDetail变量是我业务中用到的，请自行根据实际情况做调整)
			// title: this.activityDetail.title,
			// width: '100%',
			// height: '100%',
			// playbackRates: [0.7, 1.0, 1.5, 2.0], //播放速度
			// autoDisable: true,
			preload: 'auto', //auto - 当页面加载后载入整个视频 meta - 当页面加载后只载入元数据 none - 当页面加载后不载入视频
			language: 'zh-CN',
			fluid: true, // 自适应宽高
			muted: false, //  是否静音
			// aspectRatio: aspectRatio, // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
			controls: false, //是否拥有控制条 【默认true】,如果设为false ,那么只能通过api进行控制了。也就是说界面上不会出现任何控制按钮
			autoplay: true, //如果true,浏览器准备好时开始回放。 autoplay: "muted", // //自动播放属性,muted:静音播放
			loop: true, // 导致视频一结束就重新开始。 视频播放结束后，是否循环播放
			techOrder: ['html5', 'flash'], //播放顺序
			screenshot: true,
			controlBar: {
			  volumePanel: {
				//声音样式
				inline: false // 不使用水平方式
			  },
			  timeDivider: true, // 时间分割线
			  durationDisplay: true, // 总时间
			  progressControl: true, // 进度条
			  remainingTimeDisplay: true, //当前以播放时间
			  fullscreenToggle: false, //全屏按钮
			  pictureInPictureToggle: false //画中画
			}
		},function(){
		   this.play(); 
		})
		})
  
		//  this.videoPlayer.play()
		
		// document.getElementById('video_play'+e.target.current)[0].$el.appendChild(video)
		// #endif
		// #ifndef H5
		 this.videoContext = uni.createVideoContext(id,this);
		
	   
		  this.videos.map((item,index)=>{
			let temp = "video_play" + index;
			if(temp !=id){
			   uni.createVideoContext(temp, this).pause();
			}
		  })
		  // this.videoContext.controls =false
		  // this.videoContext.setAttribute('webkit-playsinline', true) //这个bai属性是ios 10中设置可以让视频在小du窗内播放，也就是不是全屏播放的video标签的一个属性
		  // this.videoContext.setAttribute('x5-video-player-type', 'h5') //
		  this.videoContext.play()
		  // #endif
	  },
	  toDetail(){
		// let url =''
		let type=''
		if(this.type==1){
		  this.$navigateTo("/pages/new_house/detail?id=" +this.detail.id)
		}
		if(this.type==2){
		  if (this.detail.parentid ==1){
			type='ershou'
		  }else {
			type='renting'
		  }
		  this.$navigateTo("/pages/"+type+"/detail?id=" +this.detail.id)
		}
		if(this.type==3){
		  if (this.detail.parentid ==1){
			type='sale'
		  }else if(this.detail.parentid ==2) {
			type='rent'
		  }else {
			type='transfer'
		  }
		  this.$navigateTo("/commercial/"+type+"/detail?id=" +this.detail.id)
		}
	  },
	  getList(){
		if(this.params.page ==1) {
		  this.videos =[]
		}
		let url ='video/buildVideoList'
		if (this.type==2){
		url='video/houseVideoList'
		}
		if (this.type==3){
		  url='video/estateVideoList'
		}
		this.$ajax.get(url,this.params,(res=>{
		  if (res.data.code ==1){
			// if(this.type==3){
			//   res.data.list =[{
			//     id:73,
			//     path:"https://images.tengfun.com/attachment/builds/20230227/25e54fe17de58ce5f03b02f12a4ea44f29dd0e7c.mp4",
			//     infoid :73
			//   }]
			// }
			this.videos = this.videos.concat(res.data.list) 
			this.isloading =false
			if(res.data.list.length==0) {
			  this.loadmore =false
			}else {
			  this.loadmore =true
			}
			if (this.params.page ==1){
			  this.onChange({target:{current:0}})
			}
		  }else {
			uni.showToast({
			  title:res.data.msg,
			  icon:"none"
			})
		  }
		})
		)
	  },
	  getDetail(info_id,vid){
		let url ='video/buildVideoDetail'
		if (this.type==2){
		url='video/houseVideoDetail'
		}
		if(this.type==3){
		  url ='video/estateVideoDetail'
		}
		this.$ajax.get(url,{info_id:info_id,id:vid},res=>{
		  console.log(res);
		  let label =[]
		  if (this.type==1){
			this.detail  = res.data.build
			this.detail.label = res.data.build.b_type 
			this.memberObj = res.data.mountMembers.length?res.data.mountMembers[0]:{}
			this.is_follow =res.data.is_follow
		  }
		  if (this.type==2 ){
			this.detail  = res.data.house
			this.memberObj = res.data.agent
			this.detail.label.map(item=>{
			  label.push(item.name)
			})
			this.detail.label =label
			this.is_follow =res.data.is_collect
		  }
		  if ( this.type==3){
			this.detail  = res.data.estate
			
			this.detail.label.map(item=>{
			  label.push(item.name)
			})
			this.detail.label =label
			this.memberObj = res.data.agent
			this.is_follow =res.data.is_collect
		  }
		  this.detail.video_desc  = res.data.videoDesc
		  
		  console.log(this.memberObj);
		})
	  },
	  waitingVideo(){
	  },
	  playVideo(index){
		this.videoContext  =null
        this.videoContext = uni.createVideoContext('video_play'+index);
	  },
  
	  pauseVideo(){
  
	  },
	  showPinglun(){
		// this.videoContext.requestFullScreen()
		this.show_pinglun =true
	  },
	}
  }
  </script>
  
  <style lang="scss" scoped>
  // #ifdef h5
   @import './static/css/video-js.css';
  //  #endif
  .page {
	background: #000;
  }
  .flex-row{
	display: flex;
	flex-direction: row;
  }
  .video_item {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	width: 100vw;
	flex-direction: column;
	position: relative;
	&.video_play_scale{
	  align-items: flex-start;
	  justify-content: flex-start;
	  .video_play{
		height: 300rpx;
	  }
	}
  }
  video{
	z-index: 1;
  }
  .fix_right{
	position: absolute;
	top: 50vh;
	right: 48rpx;
	transform: translateY(-50%);
	z-index: 1000;
	.prelogo {
	  width: 80rpx;
	  height: 80rpx;
	  margin-bottom: 60rpx;
	  overflow: hidden;
	  border-radius: 50%;
	  image {
		width: 100%;
		height: 100%;
	  }
	}
	.right_oper {
	  margin-bottom: 48rpx;
	  text-align: center;
	  .right_img {
		width: 48rpx;
		height: 48rpx;
		margin: 0 auto;
		overflow: hidden;
		image {
		  width: 100%;
		}
	  }
	  .right_con{
		margin-top: 10rpx;
		font-size: 24rpx;
		color: #F8F8F8;
		&.red{
		  color: red;
		}
	  }
	}
  }
  .fix_bottom {
	position: absolute;
	bottom: 0;
	height: 400rpx;
	left: 0;
	right: 0;
	padding: 20rpx 48rpx;
	.title {
	  color: #FFFFFF;
	  font-size:36rpx;
	  font-weight: 600;
	  padding: 18rpx 0;
	}
	.info_labels {
	  // color: #FFD952;
	  font-size: 24rpx;
	  margin: 20rpx 0;
	  .info_label {
		margin-right: 5rpx;
		line-height: 1.3;
		font-size: 22rpx;
		color: #fff;
  
  
		// border: 2rpx solid #FFD952;
		padding: 4rpx 8rpx;
		// border-radius: 4rpx;
		&.info_label0 {
		   border-radius: 4rpx;
		  background: linear-gradient(90deg, #FF3030 0%, #FF8939 100%);
		  // background: radial-gradient(circle at 50% 50%, #FF3030 0%, #FF4C33 47%, #FF8939 98%);
		  // box-shadow: 0 -4rpx 8rpx 0 #F20401;
		}
		 &.info_label1 {
		  border-radius: 4rpx;
		  background: linear-gradient(90deg, #FF1D61 0%, #EF3C87 100%);
  
		}
		&.info_label2{
		  border-radius: 4rpx;
		  background: linear-gradient(90deg, #0A81F3 0%, #4DBCFD 100%);
		}
  
	  }
	}
	.info_c{
	  color: #fff;
	  font-size: 24rpx;
	  margin-top: 16rpx;
	  overflow:hidden;
	  width: 100%;
	  text-overflow:ellipsis;
	  display:-webkit-box;
	  -webkit-line-clamp:3;    // 表示需要显示的行数
	  -webkit-box-orient:vertical; 
	}
	.info_btn {
	  display: inline-block;
	  margin-top: 20rpx;
	  padding: 5rpx 10rpx;
	  font-size: 22rpx;
	  color: #fff;
	  background: #FFFFFF19;
	}
  }
  .pinglun{
	border-radius: 20rpx 20rpx 0px 0px;
	background: #FFFFFF;
	width: 100%;
	// position: absolute;
	// bottom:0;
	// left: 0;
	// right: 0;
	position: relative;
	height: 0;
	overflow-y: auto;
	transition: 0.3s;
	&.active{
	  height: 600rpx;
	  transition: 0.3s;
	}
	.title{
	  position: relative;
	  padding: 20rpx 0;
	  justify-content: center;
	  .title_c {
		color: #222222;
		font-size: 24rpx;
		text-align: center;
	  }
	  .close{
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 40rpx;
		height: 40rpx;
		image {
		  width: 100%;
		}
  
	  }
	}
	.pinglun_list {
	  padding: 28rpx 40rpx 80rpx;
	  position: relative;
	  .pinglun_item {
		margin-bottom: 40rpx;
		.pinglun_prelogo {
		  width: 72rpx;
		  height: 72rpx;
		  min-width: 72rpx;
		  margin-right: 24rpx;
		  overflow: hidden;
		  border-radius: 50%;
		  image{
			width: 100%;
		  }
		}
		.pinglun_con {
		  .cname {
			color: #ABA6A6;
			font-size: 26rpx;
		  }
		  .p_content {
			color: #222222;
			font-size: 28rpx;
		  }
		  .p_oper {
			margin-top: 20rpx;
  
			.ctime {
			  color: #ABA6A6;
			  margin-right: 40rpx;
			  font-size: 26rpx;
			}
			.reply{
			  color: #737373;
			  font-size: 26rpx;
			}
		  }
		}
	  }
	}
	.inp {
		position: absolute;
		background: #F3F3F5;
		height: 72rpx;
		line-height: 72rpx;
		bottom: 20rpx;
		left: 48rpx;
		right: 48rpx;
		border-radius: 48rpx;
	  }
	  // .video_play{
	  //   &.video_play_scale{
  
	  //   }
  
	  // }
  
  
  }
  </style>