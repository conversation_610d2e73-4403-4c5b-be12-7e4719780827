<template>
  <view class="popup-page">
    <view class="popup-box center" :class="{ show: showPopup }">
      <view class="box">
        <view class="title">房源统一核验编码</view>
        <view class="line">
          <view class="data code">
            <text>{{ verification_code }}</text>
            <view class="copy_btn flex-box" @click="copyCode()">
              <my-icon type="copy" color="#fff" size="24rpx"></my-icon>
              <text>复制</text>
            </view>
          </view>
        </view>
        <view class="line qrline" v-if="verification_qrcode">
          <image class="erweima" :src="verification_qrcode" mode="widthFix" @click="preImg"></image>
        </view>
        <view class="line">
          <view class="data heyan" @click="toUrl">已核验</view>
        </view>
        <view class="close-btn" @click="hide">关闭</view>
      </view>
    </view>
    <view class="mask" :class="{ show: showPopup }"></view>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
export default {
  components: {
    myIcon
  },
  data() {
    return {
      showPopup: false,
    }
  },
  props: {
    verification_code: {
      type: String,
      default: '',
    },
    verification_qrcode: {
      type: String,
      default: '',
    },
    verification_url: {
      type: String,
      default: '',
    },
  },
  methods: {
    show() {
      this.showPopup = true
      this.$emit('show')
    },
    hide() {
      this.showPopup = false
      this.$emit('handleHide')
      this.$emit('hide')
    },
    preImg() {
      uni.previewImage({
        current: this.verification_qrcode,
        indicator: 'number',
        urls: [this.verification_qrcode],
      })
    },
    copyCode() {
      // #ifdef H5
      let oInput = document.createElement('textarea')
      oInput.value = this.verification_code
      document.body.appendChild(oInput)
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand('Copy') // 执行浏览器复制命令
      oInput.blur()
      oInput.remove()
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      // #endif
      // #ifndef H5
      uni.setClipboardData({
        data: this.verification_code,
        success: function () {
          uni.showToast({
            title: '复制成功',
            icon: 'none'
          })
        },
      })
      // #endif
    },
    toUrl() {
      if (this.verification_url) {
        this.$navigateTo(this.verification_url)
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.popup-page {
  .popup-box {
    position: fixed;
    overflow-x: hidden;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    opacity: 0;
    z-index: -1;
    transition: 0.26s;
  }
  .popup-box.center.show {
    opacity: 1;
    z-index: 98;
    transform: translateY(0);
  }
  .mask {
    position: fixed;
    height: 100vh;
    width: 100%;
    left: 0;
    background-color: rgba($color: #000000, $alpha: 0);
    z-index: -1;
    transition: 0.3s;
  }
  .mask.show {
    background-color: rgba($color: #000000, $alpha: 0.5);
    z-index: 90;
  }
}
.box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 24rpx 32rpx;
  background-color: #fff;
  width: 600rpx;
  border-radius: 16rpx;
  .title {
    text-align: center;
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 50rpx;
  }
  .line {
    display: flex;
    justify-content: center;
    align-items: center;
    .data {
      position: relative;
    }
    .code {
      margin-bottom: 40rpx;
    }
    .erweima {
      width: 240rpx;
      margin-bottom: 20rpx;
    }
    .heyan {
      margin-bottom: 50rpx;
      color: #00caa9;
    }
  }
  .close-btn {
    line-height: 64rpx;
    width: 236rpx;
    text-align: center;
    border-radius: 32rpx;
    margin: 32rpx auto;
    background: #fb656a;
    box-shadow: 0 2px 8px 0 rgba(251, 101, 106, 0.4);
    color: #fff;
  }
}
.copy_btn{
  position: absolute;
  top: 0;
  right: -100rpx;
  width: 84rpx;
  padding: 4rpx;
  align-items: center;
  justify-content: center;
  background-image: linear-gradient(125deg, #FF5500 0%, #FFA402 100%);
  box-shadow: 0 4rpx 12rpx 0 rgba(255,145,1,0.50);
  border-radius: 4rpx;
  border-radius: 4rpx;
  color: #fff;
  font-size: 22rpx;
}
</style>