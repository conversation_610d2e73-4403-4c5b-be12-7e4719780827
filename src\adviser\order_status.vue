<template>
<view class="order_status">
    <view class="header">
        <view class="user_info flex-box">
            <view class="img-box">
                <image :src="user_info.prelogo | imgUrl('w_80')" mode="aspectFill"></image>
            </view>
            <view class="info-box">
                <view class="name">{{user_info.cname}}</view>
                <view class="tel" @click="handleTel()">
                    <my-icon type="dianhua"></my-icon>
                    <text>{{user_info.tel}}</text>
                </view>
                <view class="address" v-if="user_info.address">
                     <my-icon type="dibiao"></my-icon>
                    <text>{{user_info.address}}</text>
                </view>
            </view>
        </view>
    </view>
    <view class="line-box">
        <!-- #ifndef MP-BAIDU -->
        <time-line :lineData="status_list" custom v-slot:default="{slotItem, slotIndex}" >
            <view class="line-item" :class="{current:slotItem.is_finish||slotIndex<select_index, selected:slotItem.select_status === slotItem.type}" @click="handleSelect(slotItem, slotIndex)">
                <view class="line-header flex-box">
                    <view class="title">{{slotItem.title}}</view>
                    <view class="time">{{slotItem.time}}</view>
                </view>
                <view class="content">{{slotItem.desc||'暂无信息'}}</view>
                <view class="selected" v-if="slotItem.select_status===slotItem.type">
                    <my-icon type="selected" size="26" color="#f65354"></my-icon>
                </view>
            </view>
        </time-line>
        <!-- #endif -->
        <!-- #ifdef MP-BAIDU -->
         <view class="time_line">
            <view class="item" v-for="(item, index) in status_list" :key="index"   :class="{current:item.is_finish||index<select_index}" @click="handleSelect(item, index)">
                <view class="line-item"  :class="{current:item.is_finish||index<select_index, selected:item.select_status === item.type}">
                    <view class="line-header flex-box">
                        <view class="title">{{item.title}}</view>
                        <view class="time">{{item.time}}</view>
                    </view>
                    <view class="content">{{item.desc||'暂无信息'}}</view>
                    <view class="selected" v-if="item.select_status===item.type">
                        <my-icon type="selected" size="26" color="#f65354"></my-icon>
                    </view>
                </view>
            </view>
        </view>
        <!-- #endif -->
    </view>
    <view class="btn-box c-bottom">
        <button class="btn default" @click="showDialog()">确认提交</button>
    </view>
    <my-dialog ref="dialog" :show="show_dialog" @confirmButton="subStatus" @close="show_dialog = false" title="请填写备注">
        <view class="text-box">
            <!-- #ifdef MP -->
            <textarea v-if="show_dialog" fixed maxlength="120" placeholder="请填写备注" v-model="remark"></textarea>
            <!-- #endif -->
            <!-- #ifndef MP -->
            <textarea fixed maxlength="120" placeholder="请填写备注" v-model="remark"></textarea>
            <!-- #endif -->
        </view>
    </my-dialog>
</view>
</template>

<script>
// #ifndef MP-BAIDU 
import timeLine from '../components/timeLine'
// #endif 
import myIcon from '../components/icon'
import myDialog from "../components/dialog.vue"
import {formatImg} from '../common/index.js'
export default {
    data() {
        return {
            user_info: {},
            status_list: [],
            select_status: 0, //需要提交的当前订单状态
            select_index: 0,
            show_dialog:false,
            remark: "",
        }
    },
    components: {
        // #ifndef MP-BAIDU 
        timeLine,
        // #endif     
        myIcon,
        myDialog
    },
    onLoad(options){
        this.order_id = options.id || ''
        this.getData()
    },
    filters:{
        imgUrl(val, param = "") {
            return formatImg(val, param)
        },
    },
    methods: {
        getData(){
            this.$ajax.get('onlineMy/orderLog.html', {order_id:this.order_id}, res=>{
                if(res.data.code === 1 && res.data.list.length>0){
                    // this.status_list = res.data.list
                    this.status_list = res.data.list.map(item=>{
                        item.select_status = 0
                        return item
                    })
                    this.user_info = res.data.to_user
                }else{
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
        handleSelect(e, index){
            this.select_index = index
            this.select_status = e.type
            this.status_list.forEach((key,idx)=>{
                if (idx == index){
                    key.select_status = key.type
                    this.$forceUpdate()
                }else {
                    key.select_status = 0
                }
            })
        },
        showDialog(){
            this.show_dialog = true
        },
        subStatus(){
            this.$ajax.get('onlineMy/confirmOrderLog.html',{order_id:this.order_id,type:this.select_status,remark:this.remark},res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title: res.data.msg
                    })
                    this.select_index = 0
                    this.select_status = 0
                    this.getData()
                }else{
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
        handleTel(){
            if(this.user_info.tel){
                uni.makePhoneCall({
                    phoneNumber:this.user_info.tel
                })
            }
        }
    },
}
</script>

<style scoped lang="scss">
.header{
    padding: 36rpx;
}
.user_info{
    align-items: center;
    padding: 40rpx 30rpx;
    background-color: #fff;
    border-radius: 16rpx;
    border: 1upx solid #f7f7f7;
    box-shadow: 0 0 18upx #eee;
    .img-box{
        width: 90rpx;
        height: 90rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 25rpx;
        image{
            width: 100%;
            height: 100%;
        }
    }
    .name{
        font-size: 32rpx;
        margin-bottom: 10rpx;
    }
    .tel{
        color: #555555;
    }
    .address{
        color: #555555;
    }
}
.line-box{
    padding-bottom: 130rpx;
    margin-left: 30rpx;
}
.line-item{
    background: #f3f3f3;
    padding: 20rpx 24rpx;
    border-radius: 8rpx;
    position: relative;
    border: 1rpx solid #f3f3f3;
    &.current{
        background-color: #e3faf4;
        border: 1rpx solid #e3faf4;
        .content{
            color: #53d2ab;
        }
    }
    &.selected{
        border: 1rpx solid #f65354;
        background-color: #e3faf4;
    }
    .line-header{
        justify-content: space-between;
        margin-bottom: 20rpx;
        .title{
            font-size: 30rpx;
        }
        .time{
            font-size: 26rpx;
            color: #666666;
        }
    }
    .content{
        color: #555555;
        background-color: initial;
    }
    .selected{
        position: absolute;
        bottom: 0;
        right: 1rpx;
    }
}
.c-bottom{
    position: fixed;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
    z-index: 3;
}

.text-box{
    padding: 20upx;
    textarea{
        text-align: left;
        width: 100%;
        height: 150upx;
        border: 1upx solid #f3f3f3;
        padding: 10upx;
        box-sizing: border-box;
    }
}
/* #ifdef MP-BAIDU */
.time_line{
    padding: 20upx 30upx;
    .item{
        position: relative;
        padding: 0 20upx 36upx 32upx;
        border-left: 4upx solid #3399ff;
        .title{
            font-size: 28upx;
            margin-bottom: 15upx;
            line-height: 1.5;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            display: -webkit-box;
        }
        .time{
            font-size: 24upx;
            font-weight: bold;
            color: #999;
        }
        
        
    }
    .item::after{
        content: "";
        height:36upx;
        width: 36upx;
        box-sizing: border-box;
        border-radius: 50%;
        position: absolute;
        border: 4rpx solid #3399ff;
        background-color: #fff;
        left: -20upx;
        top: -8rpx;
    }
    .item.current::before{
        content: "";
        height:20upx;
        width: 20upx;
        border-radius: 50%;
        background-color: #3399ff;
        position: absolute;
        left: -12upx;
        top: 0;
        z-index: 2;
    }
}
    
/* #endif */
</style>
