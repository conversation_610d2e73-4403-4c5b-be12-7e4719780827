<template>
  <view class="house-list">
    <view class="top-tip flex-row">
      <my-icon type="ic_guanyu" color="#ff656b"></my-icon>
      <text class="text">向左滑动删除房源</text>
    </view>
    <view class="contrast_list">
      <uni-swipe-action v-for="(item, index) in house_list" :key="item.id">
        <uni-swipe-action-item :parentData="item">
          <house-type
            :title="item.title"
            :img="item.img_path"
            :price="params.type==1?item.fangjia + '万':item.zujin+'元/m²'"
            :labels="[item.areaname, item.catname]"
            :checked="item.checked"
            @click="toDetail(item.id)"
          >
            <template v-slot:check_box="{ checked }">
              <view class="check-box" @click="handleCheck(index)">
                <my-icon v-if="checked" type="ic_xuanze" color="#ff656b" size="32rpx"></my-icon>
                <view v-else class="check"></view>
              </view>
            </template>
          </house-type>
          <template slot="option" slot-scope="{ parentData }">
            <view class="cancel_collect" @click="delFollow(parentData.contrast_id)">
              <view class="icon-box">
                <my-icon type="ic_delete_w" color="#fff" size="40rpx"></my-icon>
              </view>
            </view>
          </template>
        </uni-swipe-action-item>
      </uni-swipe-action>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <view class="btn-box">
      <view class="btns flex-row">
        <view class="btn" @click="$navigateBack()">添加户型</view>
        <view class="btn active" @click="toContrast()">开始对比</view>
      </view>
    </view>
  </view>
</template>

<script>
import houseType from '../components/houseType'
import myIcon from '../components/myIcon'
import { uniLoadMore, uniSwipeAction, uniSwipeActionItem } from '@dcloudio/uni-ui'
import { showModal } from '../common/index.js'
export default {
  components: {
    houseType,
    myIcon,
    uniLoadMore,
    uniSwipeAction,
    uniSwipeActionItem
  },
  data() {
    return {
      params: {
        page: 1,
        rows: 20,
        type: 0
      },
      house_list: [],
      get_status: '',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      }
    }
  },
  onLoad(options) {
    this.params.type = options.type||1
    if(parseInt(options.no_login)===1){
      this.no_login = parseInt(options.no_login)
      this.getData2()
      return
    }
    this.getData()
  },
  methods: {
    getData() {
      if (this.params.page === 1) {
        this.house_list = []
      }
      this.get_status = 'loading'
      this.$ajax.get('house/contrastList.html', this.params, res => {
        console.log(res.data)
        this.get_status = 'more'
        if (res.data.code === 1) {
          let list = res.data.list.map(item => {
            item.checked = 0
            return item
          })
          this.house_list = this.house_list.concat(list)
          if (res.data.list.length < this.params.rows) {
            this.get_status = 'noMore'
          }
        } else {
          this.get_status = 'noMore'
        }
      })
    },
    getData2() {
      this.get_status = 'loading'
      let params = {
        type:this.params.type
      };
      if(this.params.type == 1){
        params.info_ids = this.$store.state.temp_ershou_contrast_ids.join(',')
        console.log(params)
      }
      if(this.params.type == 2){
        params.info_ids = this.$store.state.temp_renting_contrast_ids.join(',')
      }
      this.$ajax.get('house/contrastListNoLogin.html', params, res => {
        this.get_status = 'noMore'
        if (res.data.code === 1) {
          let list = res.data.list.map(item => {
            item.checked = 0
            return item
          })
          this.house_list = this.house_list.concat(list)
        } else {
          this.get_status = 'noMore'
        }
      })
    },
    handleCheck(index) {
      if (this.house_list[index].checked) {
        this.house_list[index].checked = 0
      } else {
        let checked_num = this.house_list.filter(item => item.checked)
        if (checked_num.length >= 2) {
          uni.showToast({
            title: '一次只可以对比两个',
            icon: 'none'
          })
          return
        }
        this.house_list[index].checked = 1
      }
    },
    toDetail(id){
      if(this.params.type==1){
        this.$navigateTo(`/pages/ershou/detail?id=${id}`)
      }
      if(this.params.type==2){
        this.$navigateTo(`/pages/renting/detail?id=${id}`)
      }
    },
    delFollow(contrast_id) {
      if(this.no_login){
         if(this.params.type === 1){
          this.$store.state.temp_ershou_contrast_ids.splice(index,1)
        }
        if(this.params.type === 2){
          this.$store.state.temp_renting_contrast_ids.splice(index,1)
        }
        this.house_list = []
        this.getData2()
        return
      }
      let handleDel = () => {
        this.$ajax.get('house/delContrast.html', { contrast_id }, res => {
          if (res.data.code == 1) {
            this.params.page = 1
            this.getData()
          }
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        })
      }
      showModal({
        title: '提示',
        content: '确定删除此房源？',
        cancelText: '否',
        confirmText: '确定',
        confirm: () => {
          handleDel()
        }
      })
    },
    toContrast() {
      let checked_num = this.house_list.filter(item => item.checked)
      if (checked_num.length < 2) {
        uni.showToast({
          title: '请选择两个要对比的房源',
          icon: 'none'
        })
        return
      }
      if (checked_num.length > 2) {
        uni.showToast({
          title: '一次最多只能对比两个房源',
          icon: 'none'
        })
        return
      }
      if(this.no_login){
        let info_id1 = checked_num[0].info_id
        let info_id2 = checked_num[1].info_id
        this.$navigateTo(`/contrast/info_detail?type=${this.params.type}&info_id1=${info_id1}&info_id2=${info_id2}`)
        return
      }
      let contrast_id1 = checked_num[0].contrast_id
      let contrast_id2 = checked_num[1].contrast_id
      this.$navigateTo(`/contrast/info_detail?type=${this.params.type}&id1=${contrast_id1}&id2=${contrast_id2}`)
    }
  },
  onReachBottom() {
    if (this.get_status === 'more') {
      this.params.page++
      this.getData()
    }
  }
}
</script>

<style scoped lang="scss">
view {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.house-list{
  padding-bottom: 160rpx;
}
.top-tip {
  align-items: center;
  padding: 24rpx 48rpx;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
  color: $uni-color-primary;
  .text {
    margin-left: 24rpx;
  }
}

.contrast_list {
  // padding: 0 48rpx;
  background-color: #fff;
}

.check-box {
  padding: 32rpx 10rpx;
  margin-right: 10rpx;
  .check {
    width: 32rpx;
    height: 32rpx;
    border: 4rpx solid #dedede;
    border-radius: 50%;
  }
}

.cancel_collect {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  padding: 0 48rpx;
  text-align: center;
  .icon-box {
    width: 80rpx;
    height: 80rpx;
    align-content: center;
    justify-content: center;
    border-radius: 50%;
    background-color: $uni-color-primary;
    box-shadow: 0 2px 8px 0 rgba(251, 101, 106, 0.4);
  }
}

.btn-box {
  padding: 48rpx;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 24rpx;
  .btns {
    line-height: 88rpx;
    border-radius: 44rpx;
    border: 1rpx solid $uni-color-primary;
    overflow: hidden;
    box-shadow: 0 4px 10px 0 rgba(251, 101, 106, 0.2);
    .btn {
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      color: $uni-color-primary;
      background-color: #fff;
      &.active {
        background-color: $uni-color-primary;
        color: #fff;
      }
    }
  }
}
</style>
