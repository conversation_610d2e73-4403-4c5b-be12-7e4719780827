<template>
<view class="list-item flex-box bottom-line" @click="goTo()">
  <view class="img-box list-img" v-if="!noImg" :class="(type==='new_house' ? 'new' :(type==='topic'||type==='info'?'topic_':''))">
    <image :src="type ==='topic'?(itemData.tp_smallimg?itemData.tp_smallimg:'https://tfy.tengfun.com/static/skin/tfy/info/placeholder_9.png?x-oss-process=style/w_240'):(type ==='info'?(itemData.info_smallimg?itemData.info_smallimg:'https://tfy.tengfun.com/static/skin/tfy/info/placeholder_9.png?x-oss-process=style/w_240'):itemData.img )| imgUrl" lazy-load mode="aspectFill"></image>
    <!-- 认证的图标 -->
    <image v-if="itemData.certify" class="left-top-icon" :src="'/images/new_icon/v.png' | imageFilter('m_320')" mode="aspectFill"></image>
    <image v-show="itemData.is_video==1" class="video-icon" src="/static/icon/video.png" mode="widthFix"></image>
  </view>
  <view class="list-info flex-1"  :class="(type==='new_house' ? 'house' :(type==='topic'||type==='info'?'topic':''))">
    <view class="info-title" :class="titleRow==2?'row2':''">
      <text class="ding" v-if="itemData.upgrade_type==2">顶</text><text class="jing" v-if="itemData.is_recommend==1">精</text>
      <text :class="{'red':itemData.ifred,'bold':itemData.ifbold}">{{itemData.title}}</text>
    </view>
    
    <block v-if="type==='new_house'">
      <view class="info-price">
        <text class="average">{{itemData.areaname||''}}</text>
        <text class="total">{{itemData.build_price}}{{itemData.price_unit}}</text>
      </view>
      <view class="label">
        <text :class="'state'+itemData.leixing">{{itemData.status_name}}</text>
        <block><text class="attr1" v-for = "(label,idx) in itemData.build_type" :key= "idx" >{{label}}</text></block>
        <!-- <text>{{itemData.typename}}</text> -->
      </view>
      <view class="info-content" v-if="itemData.newstitle"><text style="background:#fc8b14;color:#fff;padding:2upx 6upx;margin-right:14upx;font-size:24upx;">动态</text>{{itemData.newstitle}}</view>
      <view class="info-content" v-if ="itemData.isgood == 1&&itemData.discount"><text style="background:#eb3e47;color:#fff; padding:2upx 6upx;margin-right:14upx;font-size:24upx;">优惠</text>{{itemData.discount}}</view>
    </block>
    <block v-else-if="type==='ershou'">
      <!-- <view class="info-content">{{itemDataareaname}}</view> -->
      <view class="label renting">
        <text :class="'attr'+itemData.zhongjie">{{itemData.zhongjie==2?"经纪人":"个人"}}</text>
        <text>{{itemData.catname}}</text>
        <text class="area">{{itemData.areaname||''}}</text>
      </view>
      <view class="info-price">
        <text class="average">{{itemData.begintime}}</text>
        <text class="average">{{itemData.shi}}室{{itemData.ting}}厅{{itemData.wei}}卫</text>
        <text class="acreage">{{itemData.mianji}}㎡</text>
        <text class="total">{{itemData.fangjia=='面议'||itemData.fangjia=='0'?'面议':itemData.fangjia+'万'}}</text>
      </view>
    </block>
    <block v-else-if="type==='ershou2'">
      <view class="label renting">
        <text :class="'attr'+itemData.zhongjie">{{itemData.zhongjie==2?"经纪人":"个人"}}</text>
        <text class="area">{{itemData.areaname||''}}</text>
        <text>{{itemData.catname}}</text>
      </view>
      <view class="info-price flex-box">
        <view>
          <text class="price">{{itemData.fangjia=='面议'||itemData.fangjia=='0'?'面议':itemData.fangjia}}</text>
          <text class="price-unit" v-if="itemData.fangjia!=='面议'&& itemData.fangjia!='0'">万</text>
          <text class="average">{{itemData.shi}}室{{itemData.ting}}厅{{itemData.wei}}卫</text>
          <text class="acreage">{{itemData.mianji}}㎡</text>
        </view>
        <text class="average u_time">{{itemData.begintime}}</text>
      </view>
    </block>
    <block v-else-if="type==='renting'">
      <view class="label renting">
        <text :class="'attr'+itemData.zhongjie">{{itemData.zhongjie==2?"经纪人":"个人"}}</text>
        <text>{{itemData.catname}}</text>
        <text class="area">{{itemData.areaname||''}}</text>
      </view>
      <view class="info-price">
        <text class="average">{{itemData.begintime}}</text>
        <text class="average">{{itemData.shi}}室{{itemData.ting}}厅{{itemData.wei}}卫</text>
        <text class="acreage">{{itemData.mianji}}㎡</text>
        <text class="total">{{(itemData.zujin=='0'||!itemData.zujin)?'面议':itemData.zujin+'元/月'}}</text>
      </view>
    </block>
    <block v-else-if="type==='buy_house'">
      <view class="label renting">
        <text>{{itemData.catname}}</text>
        <text class="area">{{itemData.areaname||''}}</text>
      </view>
      <view class="info-price">
        <text class="average">{{itemData.begintime}}</text>
        <text class="average">意向价格: </text>
        <text class="total n_c_right">{{itemData.qgprice?itemData.qgprice+'万元':'面议'}}</text>
      </view>
    </block>
    <block v-else-if="type==='rest_house'">
      <view class="label renting">
        <text>{{itemData.catname}}</text>
        <text class="area">{{itemData.areaname||''}}</text>
      </view>
      <view class="info-price">
        <text class="average">{{itemData.begintime}}</text>
        <text class="average">意向价格: </text>
        <text class="total n_c_right">{{itemData.qzprice?itemData.qzprice+'元/月':'面议'}}</text>
      </view>
    </block>
    <block v-else-if="type==='topic'">
      <!-- <view class="label">
        <text>{{itemData.title}}</text> -->
        <!-- <text class="area">{{itemData.areaname||''}}</text> -->
      <!-- </view> -->
      <view class="info-price">
        <text class="average con" >{{itemData.desc}}</text>
        <!-- <text class="average">意向价格: </text> -->
        <!-- <text class="total">{{itemData.qzprice?itemData.qzprice+'元/月':'面议'}}</text> -->
      </view>
      <view class="info-time">
        <text class="average">更新时间：{{itemData.utime}}</text>
        <!-- <text class="average">意向价格: </text> -->
        <!-- <text class="total">{{itemData.qzprice?itemData.qzprice+'元/月':'面议'}}</text> -->
      </view>
    </block>
    <block v-else-if="type==='info'">
      <!-- <view class="label">
        <text>{{itemData.title}}</text>
      </view> -->
      <view class="info-price">
        <text class="average con info_">{{itemData.desc}}</text> 
      </view>
      <view class="info-time">
        <text class="average">更新时间：{{itemData.utime}}</text> 
      </view>
    </block>
    <slot :itemData="itemData"></slot>
    <!-- <slot :infoList="itemData.other.split(' ')"></slot> -->
    <view v-if="showBtn" class="btn" @click.stop.prevent="clickBtn()">{{btnName}}</view>
  </view>
</view>
</template>

<script>
import {
  formatImg
} from '../common/index.js'
export default {
  props: {
    itemData: Object,
    infoList:Array,
    noImg: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    showBtn: {
      type: Boolean,
      default: false
    },
    titleRow: {
      type: [Number, String],
      default: 1
    },
    btnName: String
  },
  data() {
    return {
        // infoList:this.itemData.other.split(" "),
    };
  },
  filters: {
    imgUrl(val) {
      return formatImg(val, 'w_240')
    },
    stateFomat(val) {
      switch (val) {
        case 1:
          return "待售"
          break;
        case 2:
          return "在售"
          break;
        case 3:
          return "尾盘"
          break;
        case 4:
          return "售罄"
          break;
        default:
          return ""
      }
    }
  },
  methods: {
    goTo() {
      this.$emit('click', {
        detail: this.itemData,
        type: this.type
      })
    },
    clickBtn() {
      this.$emit('clickBtn', {
        detail: this.itemData,
        type: this.type
      })
    }
  }
}
</script>

<style lang="scss">
.list-item {
  padding: 30upx $uni-spacing-row-base;

  /* #ifdef H5 */
  /* border-bottom: 1px solid #dedede; */
  /* #endif */
  .btn {
    position: absolute;
    right: 24upx;
    bottom: 30upx;
    border-radius: 4upx;
    padding: 6upx 16upx;
    font-size: $uni-font-size-sm;
    background-color: $uni-color-primary;
    color: #fff
  }
}

.list-img image.left-top-icon {
  position: absolute;
  width: 30upx;
  height: 30upx;
  left: 8upx;
  top: 8upx
}

.bottom-line::after {
  left: $uni-spacing-row-base;
  right: $uni-spacing-row-base;
}

.list-info .info-title {
  line-height: 1.6;
}
.list-info.topic .info-title {
  line-height: 1.3;
  font-weight: 600;
}

.list-info .info-title .bold {
  font-weight: bold;
  color: #000;
}

.list-info .info-title .red {
  color: #f44;
}

.list-info .info-title .bold.red {
  color: #f44;
}

.list-info .info-title.row2 {
  -webkit-line-clamp: 2;
}

.list-info .label.renting .time {
  color: #666;
}

.ding {
  font-size: $uni-font-size-sm;
  border-radius: 6upx;
  margin-right: 10upx;
  padding: 1upx 10upx;
  color: #fff;
  background-color: #f40
}

.jing {
  font-size: $uni-font-size-sm;
  border-radius: 6upx;
  margin-right: 10upx;
  padding: 1upx 10upx;
  color: #fff;
  background-color: #f40
}
.house.list-info {
  transform: translateY(-10upx);
  width: 100%;
  overflow: hidden;

}
.house .info-content{
  margin: 14upx 0upx;
  // padding: 2upx 0upx;
  // background: #e1faff;
}
// .house .label text{
//   background: #fff;
// }
// .house .label .state2{
//   margin: 0upx;
//   // background: #fff;
//    color: #666666;
//   font-size: 20upx;
// }
// .house .label .state1{
//   margin: 0upx;
//   font-size: 20upx;
//    color: #666666;
// }
// .house .label .state3{
//   margin: 0upx;
//   font-size: 20upx;
//   color: #666666;
// }
/* #ifdef MP-BAIDU */
.list-info .label .state1{
		color: #fff;
		background-color: #17bfff
	}
	.list-info .label .state2{
		color: #fff;
		background-color: #70d298
	}
	.list-info .label .state3{
		color: #fff;
		background-color: #ff7213
	}
	.list-info .label .state4{
		color: #666;
		background-color: #f3f3f3
	}
  /* #endif */ 
.house .label {
  margin: 11upx 0upx;
  white-space: nowrap;
  // overflow: hidden;
}
.house .info-price{
  padding: 4upx 0upx
}
.house .label .attr1 {
  font-size: 22upx;
  color: #53d2ab;
  padding: 5upx;
  margin-right: 5upx;
  border:1upx solid #e1faff;
  // background: #f3f3f3;
}
.new.list-img{
  min-width: 240upx;
}
.topic_{
  border-radius: 10upx;
  overflow: hidden;
  min-width: 240upx;
}
.topic{
  position: relative;
  width: 100%;
  overflow: hidden;
}
.topic .label{
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  white-space: nowrap;
 
}
.topic .label text{
  background: #fff;
  color: #000;
  font-size: 28upx;
  
}
.topic .info-price{
  margin-top: 7upx;
  padding: 1px 10upx;
}
.topic .info-price .con{
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  line-height: 32upx;
  font-size: 24upx;
  
}
.topic .info-price .info_{
  // color: #f44;
  font-size: 24upx;
}
.topic .info-time {
  position: absolute;
  left: 10upx;
  bottom: 2upx;
}



.list-info .info-price{
  align-items: flex-end;
  justify-content: space-between;
}
.list-info .price{
  font-size: 48rpx;
  color: #FB656A;
}
.list-info .price-unit{
  margin: 0 10rpx;
}
.list-info .u_time{
  position: relative;
  bottom:8rpx
}
</style>
