<template>
<view class="item flex-box bottom-line">
    <view class="logo-box">
        <image class="logo" :src="item.shoplogo | imgUrl" mode="aspectFill"></image>
    </view>
    <view class="info-box flex-1" @click="toDetail(item.id)">
        <view class="name-line">
            <text class="name">{{item.shopname}}</text>
            <text class="is-top" v-if="item.iftop>1">置顶</text>
            <view v-if="item.shoptel" class="btn" @click.stop.prevent="handleTel(item)">
                <my-icon type="dianhua" color="#ffffff"></my-icon>
                <text>拨打电话</text>
            </view>
        </view>
        <view class="title">{{item.title}}</view>
        <view class="multigraph" v-if="multigraph">
            <view class="img-box" v-for="(img,idx) in item.imgs" :key="idx">
                <image @click.stop.prevent="viewImg(item.imgs,idx)" class="img" :src="img | imgUrl('w_240')" mode="aspectFill"></image>
            </view>
            <view class="img-box perch"></view>
            <view class="img-box perch"></view>
        </view>
        <view v-else-if="item.prepath" class="img-box">
            <image class="img" :src="item.prepath | imgUrl" mode="widthFix"></image>
        </view>
        <view>
            <my-icon type="chengshi"></my-icon>
            <text>{{item.name || ""}}</text>
        </view>
        <view class="footer">
            <text>{{item.click}}浏览，{{item.pcount||0}}图</text>
            <view class="c-right" @click.stop.prevent="showBar()">
                <slot name="icon">
                    <my-icon type="pinglun" size="22" color="#91aad5"></my-icon>
                </slot>
            </view>
             <!-- #ifndef MP-TOUTIAO ||MP-BAIDU -->
            <slot :slotItem="item" :show_bar="show_bar">
                <view class="handle-bar" :class="{'show':show_bar}">
                    <view v-if="item.shoptel" class="bar-item right-line" @click.stop.prevent="handleTel(item)">
                        <my-icon type="dianhua" color="#ffffff"></my-icon>
                        <text>电话</text>
                    </view>
                    <view class="bar-item right-line">
                        <my-icon type="shikebiao" color="#ffffff"></my-icon>
                        <text>预约</text>
                    </view>
                    <view v-if="!hideShop" class="bar-item" @click.stop.prevent="toShop(item.shopid)">
                        <my-icon type="dianpu" color="#ffffff"></my-icon>
                        <text>店铺</text>
                    </view>
                </view>
            </slot>
            <!-- #endif -->
            <!-- #ifdef MP-TOUTIAO ||MP-BAIDU -->
            <view class="handle-bar" :class="{'show':show_bar}">
                <view v-if="item.shoptel" class="bar-item right-line" @click.stop.prevent="handleTel(item)">
                    <my-icon type="dianhua" color="#ffffff"></my-icon>
                    <text>电话</text>
                </view>
                <view class="bar-item right-line" v-if ="from">
                    <my-icon type="shikebiao" color="#ffffff"></my-icon>
                    <text>预约</text>
                </view>
                <view v-else class="bar-item right-line"  @click.stop.prevent="handleYuyue()">
                    <my-icon type="shikebiao" color="#ffffff"></my-icon>
                    <text>预约</text>
                </view>
                <view v-if="!hideShop" class="bar-item" @click.stop.prevent="toShop(item.shopid)">
                    <my-icon type="dianpu" color="#ffffff"></my-icon>
                    <text>店铺</text>
                </view>
            </view>
            <!-- #endif -->

        </view>
        <view v-if="item.content" class="content" :class="!showAll&&item.content?'part':''" v-html="item.content"></view>
        <view v-if="!showAll&&item.content" class="open" @click="lookAll">全文</view>
    </view>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
</view>
</template>

<script>
import myIcon from "../components/icon"
import {
    navigateTo,
    formatImg
} from '../common/index.js'
import allTel from '../common/all_tel.js'
export default {
    props: {
        dataList: Array,
        item: Object,
        index: Number,
        hideShop: {
            type: Boolean,
            default: false
        },
        multigraph: {
            type: Boolean,
            default: false
        },
         // #ifdef MP-TOUTIAO ||MP-BAIDU
        from: String,
        // #endif
        inPage: String
    },
    data() {
        return {
            show_bar: false,
            show_bar_index: -1,
            showAll: false,
            tel_res: {},
            show_tel_pop:false
        }
    },
    components: {
        myIcon
    },
    filters: {
        imgUrl(img,param='w_6401') {
            return formatImg(img,param)
        }
    },
    methods: {
        showBar() {
            this.show_bar = !this.show_bar
        },
        handleTel(shop_data) {
            let from = 5
            console.log(shop_data);
            this.tel_params = {
                type: from,
                callee_id: shop_data.shopid,
                scene_type: 5,
                scene_id: shop_data.shopid,
                success: (res)=>{
                    this.tel_res = res.data
                    this.show_tel_pop = true
                }
            }
            allTel(this.tel_params)
            this.show_bar=false
        },
        retrieveTel(){
            allTel(this.tel_params)
        },
        handleYuyue(){
            if (!this.from){
                this.$emit("handleYuyue")
                this.show_bar=false
            }
        },
        toDetail(id) {
            this.show_bar = false
            if (this.multigraph) {
                return
            }
            navigateTo("/home/<USER>/detail?id=" + id)
        },
        toShop(id) {
            navigateTo("/home/<USER>/detail?id=" + id)
            this.show_bar=false

        },
        viewImg(urls, index) {
            uni.previewImage({
                current: index.toString(),
                urls: urls,
                indicator: 'number'
            })
        },
        lookAll() {
            this.showAll = true
        }
    }
}
</script>

<style lang="scss">
.item {
    padding: 24upx 28upx;
    background-color: #fff;

    .logo-box {
        height: 90upx;
        width: 90upx;
        margin-right: 15upx;

        .logo {
            height: 100%;
            width: 100%;
            border-radius: 50%;
        }
    }

    .info-box {
        overflow: hidden;

        .name-line {
            padding: 10upx 0;
            margin-bottom: 10upx;
            display: flex;
            position: relative;
            align-items: center;

            .name {
                display: inline-block;
                max-width: 57%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #666;
            }

            .is-top {
                display: inline-block;
                padding: 2upx 10upx;
                margin-left: 10upx;
                border-radius: 6upx;
                font-size: 24upx;
                background-color: #ffda77;
                color: #ff6565;
            }

            .btn {
                position: absolute;
                right: 0;
                padding: 8upx 10upx;
                border-radius: 8upx;
                background-color: #ed414a;
                color: #fff;
            }
        }

        .title {
            font-size: $uni-font-size-blg;
            width: 100%;
            margin-bottom: 20upx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .multigraph {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;

            .img-box {
                flex: 1;
                width: 31%;
                height: 0;
                padding-bottom: 32%;
                margin-bottom: 2%;
                min-width: 32%;
                max-width: 32%;
                position: relative;
                overflow: hidden;

                .img {
                    position: absolute;
                    left: 0;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                    height: auto;
                    // min-height:100%;
                    // min-width: 100%;
                }
            }

            .img-box.perch {
                padding-bottom: 0;
                margin-bottom: 0
            }
        }

        .img-box {
            .img {
                width: 100%;
            }
        }

        .footer {
            height: 70upx;
            line-height: 70upx;
            font-size: 26upx;
            color: #999;
            position: relative;

            .handle-bar {
                padding: 10upx 0;
                line-height: 50upx;
                position: absolute;
                top: 0;
                display: flex;
                right: 44upx;
                width: 0;
                transition: 0.3s;
                border-radius: 8upx;
                background-color: #4d5154;

                .bar-item {
                    flex: 1;
                    min-width: 33.333%;
                    overflow: hidden;
                    // text-overflow: ellipsis;
                    white-space: nowrap;
                    text-align: center;
                    color: #fff;
                    transform: 0.3s;
                }
            }

            .handle-bar.show {
                width: 480upx;
            }
        }

        .content {
            line-height: 1.5;
            font-size: 30upx;
            color: #555;
            background-color: #fff;
        }

        .content.part {
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            word-break: normal;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .open {
            padding: 10upx;
            color: #00c07b;
        }
    }
}
</style>
