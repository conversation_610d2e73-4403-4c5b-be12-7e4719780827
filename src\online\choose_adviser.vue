<template>
<view class="adviser">
<view class="title bottom-line">
    <text>请选择您的专属置业顾问</text>
</view>
<view class="adviser-list">
    <view class="adviser-item flex-box bottom-line" v-for="adviser in adviser_list" :key="adviser.id" @click="handleChoose(adviser)">
        <image class="head-img" :src="adviser.prelogo | imgUrl('w_120')" mode="aspectFill"></image>
        <view>
            <view class="name">{{adviser.cname}}</view>
            <view class="tel">{{adviser.tel}}</view>
        </view>
    </view>
</view>
</view>
</template>

<script>
import {
  formatImg,
} from '../common/index.js'
export default {
    data() {
        return {
            adviser_list:[]
        }
    },
    computed: {
        has_chat(){
            return this.$store.state.im.ischat
        }
    },
    onLoad(options){
        if(options.online_id){
            this.online_id = options.online_id
            this.getData()
        }
    },
     filters: {
        imgUrl(val, param = "") {
            return formatImg(val, param)
        }
    },
    methods: {
        getData(){
            this.$ajax.get('online/buildAdviser',{online_id:this.online_id},res=>{
                if(res.data.code === 1){
                    this.adviser_list = res.data.lists
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            })
        },
        handleChoose(e){
            uni.$emit('chooseAdviser',e)
            uni.navigateBack()
        }
    },
}
</script>

<style scoped lang="scss">
.title{
    padding: 20rpx 20rpx 20rpx 40rpx;
    font-size: 32rpx;
    font-weight: bold;
    position: relative;
    &::before{
        content: "";
        position: absolute;
        left:20rpx;
        top:20rpx;
        bottom:20rpx;
        width: 6rpx;
        background-color: #f65354
    }
}
.adviser-list{
    box-shadow: 0 0 18upx #dedede;
    background-color: #fff;
    margin: 20rpx;
    .adviser-item{
        // justify-content: space-between;
        align-items: center;
        padding: 24upx 32upx;
        .head-img{
            width: 90upx;
            height: 90upx;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 32upx;
        }
        .name{
            font-size: 30upx;
            line-height: 1.8;
            font-weight: bold;
        }
        .tel{
            color: #666;
        }
    }
}
</style>
