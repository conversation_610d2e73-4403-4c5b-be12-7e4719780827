<template>
	<view class="page" :class="{pdb_120: (sharers_info.adviser_id&&is_open_adviser)||sharers_info.agent_id}">
		<view class="header" :style="{backgroundImage:'url('+bgcolor+')'}">
			<view class="head-info">
				<!-- <view class="title">{{yushouData.xmmc}}</view> -->
				<view class="update-tip">{{ yushouData.xkzh }}</view>
				<view class="header-date">{{ yushouData.hprq }}</view>
				<view class="header-date">{{ siteName }}</view>
				<view class="share_icon" @click="showSharePop">
					<my-icon type="ic_fenxiang" size="32rpx" color="#fff"></my-icon>
				</view>
			</view>
		</view>
		<view class="yushou-info">
			<view class="yushou-title flex-box flex-grow">
				<view class="yushou-title-left">{{ yushouData.xkzh }}</view>
				<view class="yushou-area">{{ yushouData.areaname }}</view>
			</view>

			<view class="xiangmumingcheng">{{ yushouData.xmmc }}</view>
			<view class="gongsimingcheng">{{ yushouData.gsmc }}</view>
			<view class="flex-box data-box" v-if="yushouData.jzmj||yushouData.zzts||yushouData.fzzts" >
				<view class="text-center bg-grey" v-if="yushouData.jzmj">
					<view class="data-title">总面积 m²</view>
					<view class="data-data"
						><text class="data-datas">{{
							yushouData.jzmj ||0
						}}</text
						></view
					>
				</view>
				<view class="text-center bg-grey" v-if="isShowTaoshu">
					<view class="data-title">住宅</view>
					<view class="data-data"
						><text class="data-datas red">{{ yushouData.zzts||0 }}</text
						>套</view
					>
				</view>
				<view class="text-center bg-grey" v-if="isShowTaoshu">
					<view class="data-title">非住宅</view>
					<view class="data-data"
						><text class="data-datas">{{ yushouData.fzzts|| 0}}</text
						>套</view
					>
				</view>
			</view>
			<block v-if="build&&build.title">
				<view class="top-20 build-box" @click="toBuild(build.id)">
					<view class="house-info flex-box flex-row">
						<view class="img"
							><image :src="build.img|imgUrl('w_240')" mode="aspectFill"></image
						></view>
						<view class="house-detail ">
							<view class="house-name">{{ build.title }}</view>
							<view class="house-area">{{ build.areaname }}</view>
							<view class="house-price"
								>{{ build.price_type }}:{{ build.build_price
								}}{{ build.price_unit }}</view
							>
						</view>
						<view
							class="dingyue flex-row"
							@click.stop.prevent="handleFollow(news.is_follow, news.bid, index)"
						>
						<text class="status" :class="'status' + build.leixing">{{
							build.status_name
						}}</text>
							<!-- <my-icon :type="news.is_follow == 1?'yidingyue':'quxiaodingyue'" size="28upx" :color="news.is_follow == 1 ? '#999' : '#666'"></my-icon>
							<text :class='news.is_follow == 1 ? "yidingyue" : "weidingyue"'>{{ news.is_follow == 1 ? "已订阅" : "订阅" }}</text> -->
						</view>
					</view>
					<view class="guanzhu" @click.prevent.stop="toSubForme(1)"
						>订阅开盘通知</view
					>
				</view>
			</block>
			<!-- <view class="xuke-info" v-if="yushouData.beizhu">
				<view class="desc-title">{{ title1 }}</view>
				<view class="header-date">{{ yushouData.hprq }}</view>
				<view class="desc-content">{{ yushouData.beizhu }}</view>
			</view> -->
            <block v-if="otherList.length>0">
                <view class="other-list" >
                    <view class="other-title">
                        最近预售历史数据
                    </view>
                    <view class="top-20">
                        <view class="lists">
                            <view class="time-line" v-for ="item in otherList" :key='item.id' @click="toDetail(item.id)"> 
                                <view class="time">
                                    <view class="line-title">
                                        {{item.hprq}}
                                    </view>
                                    <view class="data-card">
                                        <data-card :item='item'></data-card>
                                    </view>
                                </view>               
                            </view>
                        </view>
                    </view>
                </view>
            </block>
            <view class="friend-tips">
                {{friendTips}}
            </view>
		</view>
		<sub-form
			:groupCount="build.groupCount"
			:sub_type="sub_type"
			:sub_mode="sub_mode"
			:sub_title="sub_title"
			:sub_content="sub_content"
			ref="sub_form"
			@onsubmit="handleSubForm"
		></sub-form>
		<view class="sharers_info flex-box" v-if="(sharers_info.adviser_id&&is_open_adviser)||sharers_info.agent_id">
			<view class="img">
				<image :src="sharers_info.prelogo | imageFilter('w_240')" mode="widthFix"></image>
			</view>
				
				<view class="info flex-1">
						<view class="name">{{sharers_info.cname}}</view>
						<view class="identity">{{sharers_info.identity===1?'置业顾问':'经纪人'}}</view>
				</view>
				<view class="btn_box flex-box">
						<view class="btn" @click="handleChat()">微聊</view>
						<view class="btn" v-if ="(sharers_info.adviser_id&&switch_adviser_tel) ||sharers_info.agent_id" @click="handleTel()">电话咨询</view>
				</view>
		</view>
		<share-pop ref="show_share_pop" @copyLink="copyLink" :showHaibao="false" @showCopywriting='showCopywriting'></share-pop>
		<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
		<chat-tip></chat-tip>
		<dingyue ref="dingyue" :type="type" @login="toLogin" @hideOk="$store.state.updatePageData=false" ></dingyue>
		<enturstBtn v-if="sharers_info.agent_id||sharers_info.adviser_id" :to_user="sharers_info" @click="$refs.enturst_popup.show()" />
		<my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
			<enturstBox @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="showLoginPopup('为方便您及时接收消息通知，请输入手机号码')" :to_user="sharers_info" />
		</my-popup>
		<!-- 登录弹窗 -->
		<login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
		<tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
	</view>
</template>

<script>
import { formatImg } from "../../common/index.js";
import sharePop from "../../components/sharePop";
import shareTip from '../../components/shareTip'
import myIcon from "../../components/myIcon";
import myPopup from "../../components/myPopup";
import getChatInfo from '../../common/get_chat_info'
import allTel from '../../common/all_tel.js'
import { config } from "../../common/config";
import dataCard from "../../components/dataCard"
import dingyue from "../../components/dingyue.vue"
import loginPopup from '../../components/loginPopup'
import subForm from "../../components/subForm";
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
export default {
	data() {
		return {
			yushouData: {},
			siteCity: "",
			title1: "",
			title2: "",
			wxhuifuimg: "",
			build: {},
			sub_type: 0,
			sub_title: "",
			sub_content: "",
			sharers_info: {},
			current_user_info: {},
            siteName: '',
			otherList:[],
			show_share_tip:false,
			type:'dingyue',
			link:"",
			login_tip: '',
			friendTips:'',
			shareId:'',
			shareType:'',
			tel_res: {},
			show_tel_pop: false,
		};
	},
	components: {
		myIcon,
		myPopup,
		subForm,
		sharePop,
		dataCard,
		dingyue,
		shareTip,
		loginPopup,
		enturstBtn,
		enturstBox
	},
	computed: {
		imconsu() {
			return this.$store.state.im.adviser;
		},
		ischat() {
			return this.$store.state.im.ischat;
		},
		istelcall() {
			return this.$store.state.im.istelcall;
		},

		sub_mode() {
			return this.$store.state.sub_form_mode;
		},
		bgcolor(){
			return config.imgDomain+'/images/new_icon/record/<EMAIL>'
		},
		is_open_adviser() { //是否开启置业顾问功能
			return this.$store.state.im.adviser
		},
		is_open_im() { // 是否开启聊天功能
			return this.$store.state.im.ischat
		},
		isShowTaoshu() { // 
			return this.$store.state.isShowTaoshu
		},
		switch_adviser_tel(){
			 return this.$store.state.switch_adviser_tel
		}

	},
	filters: {
		imgUrl(img, param = "w_400") {
			if (!img) {
				return "";
			}
			return formatImg(img, param);
		},
	},
	onLoad(options) {
		// 如果是分享链接进来的
		if (options.shareId && (options.type||options.shareType)) {
				this.shareId = options.shareId
				this.shareType = options.type||options.shareType
				this.share_time =options.f_time||''
		}
		if (options.id) {
			this.id = options.id;
			this.getData();
			uni.$on('getDataAgain',()=>{
				this.getData()
			})
		}
	},
	onShow(){
		if(this.$store.state.updatePageData){
			this.getData()
			this.$store.state.updatePageData = false
		}
	},
	onUnload(){
		this.$store.state.updatePageData = false
		uni.$off('getDataAgain')
	},
	methods: {
		getData() {
			let params = {id: this.id};
			if(this.shareId&&this.shareType){
					params = {
							id: this.id,
							sid: this.shareId,
							sharetype: this.shareType
					}
			}
			params.forward_time=this.share_time ||''
			this.$ajax.get("build/bookingDetail.html", params, (res) => {
				if (res.data.shareUser) { //当前用户信息
						this.current_user_info = res.data.shareUser
						if(res.data.shareUser.adviser_id){
								this.current_user_info.identity = 1
								this.current_user_info.identity_id = res.data.shareUser.adviser_id
						}else if(res.data.shareUser.agent_id){
								this.current_user_info.identity = 2
								this.current_user_info.identity_id = res.data.shareUser.agent_id
						}
				}
				if (res.data.share_user) { //分享者信息
						this.sharers_info = res.data.share_user
						if(res.data.share_user.adviser_id){
								this.sharers_info.identity = 1
						}else if(res.data.share_user.agent_id){
								this.sharers_info.identity = 2
						}
				}
				this.siteName=res.data.siteName
				// uni.setNavigationBarTitle({
				// 	title: this.siteName
				// })
                this.friendTips=res.data.declare
				if (res.data.code == 1) {
					if(res.data.build){
						this.build = res.data.build
                    }
                    if (res.data.otherList){
                        this.otherList =res.data.otherList
                    }
					this.yushouData = res.data.detail;
					// 获取登录状态
					this.$ajax.get('member/checkUserStatus', {}, res => {
						if (res.data.code !== 1) {
							this.$store.state.user_login_status = res.data.status
							if (this.$store.state.user_login_status==1){
								this.type="denglu"
								uni.setStorageSync('backUrl', window.location.href)
								this.$store.state.updatePageData=true
								this.showDingyuePop()
							}else if (this.$store.state.user_login_status==2){
								this.type="bangshouji"
								this.$store.state.updatePageData=true
								this.showDingyuePop()
							}
							
						}
					})
					this.siteCity = res.data.siteCity;
					this.title1 = res.data.title1;
					this.title2 = res.data.title2;
				}
				if(res.data.share){
					this.share =res.data.share
				}else {
					this.share = {
						title:res.data.detail.xmmc,
						content:res.data.detail.gsmc,
						pic:res.data.wxhuifuimg||''
					}
				}
				this.share.link=this.getShareLink()
                this.getWxConfig()
				if (res.data.wxhuifuimg) {
					this.wxhuifuimg = res.data.wxhuifuimg;
				}
			});
		},
		toLogin(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/login/login")
		},
		toBind(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/bind_phone/bind_phone")
		},
		toDetail(id){
            this.$navigateTo(`/pages/yushou/detail?id=${id}`)
        },
		// 订阅
		showDingyuePop(){
			this.$refs.dingyue.showPopup()
		},
        
		toBuild(id) {
			if (id) {
				this.$navigateTo("/pages/new_house/detail?id=" + id);
				return;
			}
			this.$navigateTo("/pages/new_house/detail?id=" + this.yushouData.buildid);
		},
		toSubForme(type) {
			let bid;
			if (this.build) {
				bid = this.build.id;
			} else {
				bid = 0;
			}
			this.sub_type = type;
			switch (type) {
				case 0:
					this.sub_title = "团购报名";
					this.sub_content = "为方便通知到您最新的信息，请输入您的手机号码";
					break;
				case 3:
					this.sub_title = "预约优惠报名";
					this.sub_content = "为方便通知到您最新的信息，请输入您的手机号码";
					break;
				default:
					this.sub_title = "团购报名";
					this.sub_content = "为方便通知到您最新的信息，请输入您的手机号码";
			}
			this.$refs.sub_form.showPopup();
			return;
		},
		// 获取分享链接
		getShareLink(){
				let link = window.location.href
				let time =parseInt(+new Date()/1000)
				if (this.current_user_info.identity) { //当前用户是 置业顾问或者经纪人  
						link = `${window.location.origin}${window.location.pathname}?id=${this.id}&shareId=${this.current_user_info.identity_id}&type=${this.current_user_info.identity}&f_time=${time}`
				}
				return link
		},
		showSharePop(){
			this.getShortLink()
			this.$refs.show_share_pop.show()
		},
		// 复制分享链接
		copyLink(){
			this.show_share_tip=true
		},
		getShortLink(){
            this.link=this.getShareLink()
            this.$ajax.get('build/shortUrl.html', {page_url:this.link }, res=>{
                if(res.data.code === 1){
					this.link = res.data.short_url
                }
            })
		},
		showLoginPopup(tip){
			this.login_tip = tip
			this.$refs.login_popup.showPopup()
		},
		handleCloseLogin() {
            if (this.$store.state.user_login_status === 1) {
                uni.removeStorageSync('token')
                this.$navigateTo('/user/login/login')
            }
            if(this.$store.state.user_login_status===2){
                this.$navigateTo('/user/bind_phone/bind_phone')
            }
        },
        onLoginSuccess(res){
            this.$store.state.user_login_status = 3
            if(this.weituo_is_show){
                console.log("登录成功后继续执行委托接口")
                this.$refs.enturst_box.handleEnturst()
            }
        },
		// 复制分享内容
		showCopywriting(){
			console.log("复制内容")
			const content = `【项目名称】${this.yushouData.xmmc}\n【开发公司】${this.yushouData.gsmc}\n【总 面 积】${this.yushouData.jzmj}m²\n【住宅套数】${this.yushouData.zzts}\n【非住宅套数】${this.yushouData.fzzts}\n【链   接】${this.link}`
			this.copyText(content, ()=>{
					uni.showToast({
					title: '复制成功,去发送给好友吧',
					icon: 'none'
					})
			})
		},
		// 复制内容
		copyText(cont, callback) {
			let oInput = document.createElement('textarea')
			oInput.value = cont
			document.body.appendChild(oInput)
			oInput.select() // 选择对象;
			oInput.setSelectionRange(0, oInput.value.length);
			document.execCommand('Copy') // 执行浏览器复制命令
			oInput.blur()
			oInput.remove()
			if(callback) callback()
		},
		// 发起聊天
		handleChat(){
			if(!this.is_open_im){
				if (this.sharers_info.identity == 1) { //置业顾问
					this.$navigateTo('/pages/consultant/detail?id=' + this.sharers_info.adviser_id)
				} else if (this.sharers_info.identity == 2) {
					this.$navigateTo('/pages/agent/detail?id=' + this.sharers_info.agent_id)
				}
				return
			}
			// #ifndef MP-WEIXIN
				getChatInfo(this.sharers_info.mid, 20)
			// #endif
		},
		// 拨打电话
		handleTel(){
			this.tel_params = {
				type: this.sharers_info.identity == 1?'2':'3',
				callee_id: this.sharers_info.mid,
				scene_type:this.sharers_info.identity == 1?'2':'3',
				scene_id:this.sharers_info.mid,
				success: (res)=>{
						this.tel_res = res.data
						this.show_tel_pop = true
				}
			}
			allTel(this.tel_params)
		},
		retrieveTel(){
			allTel(this.tel_params)
		},
		handleSubForm(e) {
			//提交报名
			e.from = '预售详情页'
			if (this.build) {
				e.bid= this.build.id;
			} else {
				e.bid= 0;
			}
			e.type = this.sub_type || ''
			this.$ajax.post('build/signUp.html', e, res => {
				uni.hideLoading()
				if (res.data.code === 1) {
				// 没开启引导登录模式或已经绑定手机号了
				if (this.sub_mode!==2||res.data.status === 3) {
					//提示报名成功
					uni.showToast({
						title: res.data.msg,
						icon: 'none'
					})
					this.$refs.sub_form.closeSub()
				} else {
					this.$refs.sub_form.getVerify()
				}
				} else {
				uni.showToast({
					title: res.data.msg,
					icon: 'none'
				})
				}
			})
		},
	},
	onShareAppMessage() {
		if (this.seo) {
			return {
				title: this.seo.title || "",
				// #ifdef MP-BAIDU
				content: this.seo.description || "",
				imageUrl: this.wxhuifuimg ? formatImg(this.wxhuifuimg, "w_6401") : "",
				// #endif
			};
		}
	},
};
</script>

<style scoped lang="scss">
.pdb_120{
    padding-bottom: 120rpx;
}
.header {
	width: 100%;
	height: 320rpx;
	background-image: linear-gradient(0deg, #f7918f 0%, #fb656a 100%);
	// background: linear-gradient(to bottom right,#8A2BE2,#DC143C);
	display: flex;
	// align-items: center;
	padding: 0 48rpx;
	box-sizing: border-box;
	background-size: 100%;
	background-repeat: no-repeat;
	.head-info {
		width: 100%;
	}
	.title {
		font-size: 46upx;
		color: #fff;
	}
	.update-tip {
		margin-top: 48upx;
		margin-bottom: 24rpx;
		font-size: 40rpx;
		color: #fff;
	}
	.header-date {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 20rpx;
	}
}

.share_icon{
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 24rpx;
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		background-color: rgba(0,0,0, 0.5);
		position: absolute;
		top: 48rpx;
		right: 48rpx;
}
.yushou-title {
	padding: 24rpx 0;
	font-size: 22rpx;
	color: #999999;
	.yushou-title-left {
		flex: 1;
	}
	.yushou-areaname {
		margin-left: auto;
	}
}
.xiangmumingcheng {
	font-size: 36rpx;
	color: #333;
	font-weight: 600;
}
.gongsimingcheng {
	color: #999;
	font-size: 28rpx;
	margin: 24rpx 0;
}
.yushou-info {
	position: relative;
	top: -24rpx;
	padding: 24rpx 48rpx;
	border-radius: 48rpx 48rpx 16rpx 16rpx;
	background: #fff;
}
.house-info {
	margin-bottom: 48upx;
	.img {
		width: 128upx;
		height: 106upx;
		margin-right: 24upx;
		overflow: hidden;
		image {
			width: 100%;
			height: 100%;
		}
	}
	.house-detail {
		justify-content: space-between;
		.house-name {
			font-size: 28upx;
			font-weight: 600;
			color: #333;
		}
		.house-area {
			font-size: 22upx;
			color: #999;
		}
		.house-price {
			font-size: 22upx;
			color: #999;
		}
	}
	.dingyue {
		margin-left: auto;
		font-size: 14px;
		color: #666666;
		align-items: baseline;
		text{
			margin-left: 4upx;
			&.yidingyue{
				color: #999;
			}
		}
	}
}
	.guanzhu {
		background-image: linear-gradient(
			135deg,
			rgba(247, 145, 143, 0.1) 0%,
			rgba(251, 101, 106, 0.1) 100%
		);
		border-radius: 4px;
		color: #fb656a;
		text-align: center;
		padding: 20upx 0;
	}

.data-box {
	padding: 32upx 0;
	background-color: #ffffff;

	.bg-grey {
		padding: 10rpx 24rpx;
		background: #f8f8f8;
		width: 32%;
		margin-right: 2%;
		&:last-child {
			margin-right: 0;
		}
	}
	.data-title {
		margin-bottom: 10upx;
		font-size: 22rpx;
		color: #666;
	}
	.data-data {
		font-size: 22rpx;
		// font-weight: bold;
		color: #666;
	}
	.data-datas {
		font-size: 32upx;
		font-weight: bold;
		color: #333;
	}
	.red {
		color: $uni-color-primary;
	}
}
.build-box {
	background-color: #fff;
	// padding: 0 48upx;
}

.build {
	padding-bottom: 24upx;
}

.build-title {
	font-size: 32upx;
	margin-right: 16upx;
	font-weight: bold;
}

.build-img-box {
	box-sizing: border-box;
	width: 100%;
	height: 60vw;
}

.build-img-box image {
	width: 100%;
	height: 100%;
}

.status {
	display: inline-block;
	margin-right: 20upx;
	font-size: 28upx;
	padding: 0 10upx;
	border-radius: 4upx;
	background-color: $uni-color-primary;
	color: #fff;
	&.status1 {
		color: #fff;
		background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
	}
	&.status2 {
		color: #fff;
		background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
	}
	&.status3 {
		color: #fff;
		background: linear-gradient(to right, #ff8533 0%, #ff7213 100%);
	}
	&.status4 {
		color: #fff;
		background: linear-gradient(to right, #ccc 0%, #ccc 100%);
	}
}

.space {
	display: inline-block;
	margin-left: 10upx;
	font-size: 22upx;
	padding: 0 6upx;
	box-sizing: border-box;
	border-radius: 4upx;
	border: 1upx solid #999;
	color: #999;
}

.row {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.pad12 {
	padding: 24upx 0;
}
.buid-bottom {
	align-items: center;
	font-size: 28upx;
	.price {
		margin-left: auto;
		color: #fb656a;
	}
	.address {
		max-width: 65%;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		color: #666;
	}
}
.pan {
	height: 80upx;
	line-height: 80upx;
	// padding: $uni-spacing-row-base;
	background-color: #fff;
	align-items: center;
	background: rgba(251, 101, 106, 0.05);
	flex: 1;
	box-sizing: border-box;
	// margin: 24upx 0;
	&.btn-first {
		margin-right: 24upx;
	}

	.btn {
		border-radius: 8upx;
		text {
			color: rgba(251, 101, 106, 1);
		}
	}
}
// 获取优惠
.coupon-box {
	height: 140rpx;
	line-height: 1;
	color: #fff;
	padding: 0 48upx;
	position: relative;
	.bg_img {
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
	}
	.coupon_container {
		padding: 0 48rpx;
		align-items: center;
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
	}
	.coupon_name {
		flex: 1;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		display: -webkit-box;
		margin-right: 16rpx;
	}
	.coupon_content {
		font-size: 22rpx;
		flex: 1;
	}
	.btn {
		padding: 0 24rpx;
		margin-bottom: 16rpx;
		height: 48rpx;
		line-height: 48rpx;
		border-radius: 24rpx;
		background-color: #fff;
		color: $uni-color-primary;
	}
}
.xuke-info {
	margin-top: 48rpx;
	.desc-title {
		font-size: 40upx;
		color: #333;
		font-weight: bold;
	}
	.header-date {
		font-size: 22rpx;
		color: #999;
		margin: 24rpx 0;
	}
	.desc-content {
		font-size: 28rpx;
		color: #666;
	}
}
.row-lg {
	padding: 20upx 24upx;
	background-color: #fff;
}
.other-list .other-title{
    font-size: 40rpx;
    color: #333;
    margin-top:60rpx;
    font-weight: bold;
}
.lists{
    .time-line{
        // padding:0 24rpx ;
        padding-left: 24rpx;
        .time{
            position: relative;
            &:before{
                content:'';
                position: absolute;
                width: 2rpx;
                height: 100%;
                background: #D8D8D8;
                top: 10rpx;
                left: -20rpx;
                
            }
            .line-title{
                position: relative;
                color: #FB656A;
                font-size: 22rpx;
                margin-bottom: 16rpx;
                &:before{
                    content:'';
                    position: absolute;
                    background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
                    width: 20rpx;
                    height: 20rpx;
                    top: 50%;
                    transform: translateY(-50%);
                    left: -30rpx;
                    border-radius: 50%;
                }

            }

       
            
         }
         .data-card{
             padding-bottom: 24rpx;
         }

    }
}
.desc {
	padding: 24upx;
	background-color: #fff;
	.desc-title {
		font-size: 40upx;
		color: #f65354;
	}
	.desc-content {
		margin: 20upx 0;
	}
}

// 分享者信息
.sharers_info{
    position: fixed;
    width: 100%;
    height: 120rpx;
    bottom: 0;
    padding: 0 48rpx;
    box-sizing: border-box;
    align-items: center;
    background-color: #fff;
	z-index: 90;
    .img{
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 16rpx;
		overflow: hidden;
		image{
			width: 100%;
			height: 100%;
		}
    }
    .info{
        overflow: hidden;
        .name{
            margin-bottom: 16rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .identity{
            font-size: 24rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: #999;
        }
    }
    .btn_box{
        .btn{
            margin-left: 20rpx;
            padding: 10rpx 34rpx;
            font-size: 26rpx;
            color: $uni-color-primary;
            border: 1px solid $uni-color-primary;
            border-radius: 3px;
            box-shadow: 0 2px 4px 0 rgba(251,101,106,.1);
        }
    }
}
.friend-tips{
	color: #999;
	line-height: 1.5;
    margin-top: 60rpx;
}
</style>
