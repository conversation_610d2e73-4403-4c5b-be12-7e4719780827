// /* 颜色变量 */

// /* 行为相关颜色 */
$uni-color-primary: #ff656b;
// $uni-color-success: #4cd964;
$uni-color-success: #179b16;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

// /* 文字基本颜色 */
$uni-text-color: #333; //基本色
$uni-text-color-inverse: #fff; //反色
$uni-text-color-grey: #999; //辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

// /* 背景颜色 */
$uni-bg-color: #ffffff;
$uni-bg-color-grey: #f7f7f7;
$uni-bg-color-highlight: #f65354;
$uni-bg-color-hover: #f1f1f1; //点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); //遮罩颜色

// /* 边框颜色 */
$uni-border-color: #d8d8d8;

// /* 尺寸变量 */

// /* 文字尺寸 */
$uni-font-size-sm: 24upx;
$uni-font-size-base: 28upx;
$uni-font-size-lg: 30upx;
$uni-font-size-blg: 32upx;

// /* 图片尺寸 */
$uni-img-size-sm: 40upx;
$uni-img-size-base: 52upx;
$uni-img-size-lg: 90upx;

// /* Border Radius */
$uni-border-radius-sm: 4upx;
$uni-border-radius-base: 6upx;
$uni-border-radius-lg: 12upx;
$uni-border-radius-circle: 50%;

// /* 水平间距 */
$uni-spacing-row-sm: 10upx;
$uni-spacing-row-base: 24upx;
$uni-spacing-row-lg: 30upx;
$uni-spacing-row-tft: 42upx;
// /* 左右 */
$uni-spacing-row-lr: 25upx;

// /* 垂直间距 */
$uni-spacing-col-sm: 10upx;
$uni-spacing-col-base: 16upx;
$uni-spacing-col-lg: 24upx;

// /* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

// /* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 40upx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle: 36upx;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 30upx;
