<template>
  <view class="box ">
    <view class="box_invite" v-if="invite">
      <view class="no_number">{{title}}</view>
      <view class="center_box">
        <view class="icon_box">
          <myIcon
            type="jiaoxing"
            color="#ff6735"
            size="60"
          ></myIcon>
        </view>
        <view class="txt_box">
          <view class="sorry">{{tip}}</view>
          <view class="ini_people"></view>
        </view>
      </view>
      <!-- 提交按钮 -->
      <slot>
        <button open-type="share" @click="show_share_tip = true">立刻开通</button>
      </slot>
    </view>
    <view class="box_code" v-else>
      <view class="title">{{ title }}</view>
      <view class="code">
        <view class="img">
          <image :src="image" mode="aspectFill"></image>
        </view>
      </view>
      <view class="msg">{{ tip }}</view>
    </view>
    <!-- 取消 -->
    <view class="cancel" @click="onCancel">
      <myIcon type="guanbi" color="#fff" size="56rpx"></myIcon>
    </view>
  </view>
</template>

<script>
import myIcon from '@/components/icon'
export default {
  components: {
    myIcon
  },
  props: {
    invite: {
      type: Boolean,
      default: false
    },
    image: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '公众号名称'
    },
    tip: {
      type: String,
      default: '长按识别二维码关注公众号'
    }
  },
  data() {
    return {
      show_share_tip: false
    }
  },
  methods: {
    // 点击跳转到邀请界面
    onSubmit() {
      console.log('正在跳转')
      // this.$navigateTo(`/user/service`)
    },
    // 点击取消
    onCancel() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss">
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.box {
  height: 760rpx;
  background-image: url('https://images.tengfangyun.com/images/background/invite.png');
  background-size: 600rpx 760rpx;
  position: fixed;
  top: 20%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 568rpx;
  margin: auto;
  border-radius: 10rpx;
  .box_invite {
    align-items: center;
    justify-content: space-evenly;
    .no_number {
      font-size: 60rpx;
      color: #fdfaf9;
      margin-top: 80rpx;
    }
    .center_box {
      margin-top: 70rpx;
      position: relative;
      width: 508rpx;
      height: 364rpx;
      border-radius: 10rpx;
      background: #fff;
      align-items: center;
      box-shadow: 6rpx 6rpx 20rpx #dedede;
      justify-content: space-around;
      .icon_box {
        top: -15%;
        background: #fff;
        position: absolute;
        justify-content: center;
        align-items: center;
        height: 182rpx;
        width: 182rpx;
        border-radius: 50%;
        box-shadow: 0rpx 6rpx 20rpx #ffebe5;
        left: 0;
        right: 0;
        margin: auto;
      }
      .txt_box {
        margin-top: 140rpx;
        line-height: 50rpx;
        align-items: center;
        .sorry {
          color: #333;
          font-size: 28rpx;
        }
        .ini_people {
          font-size: 28rpx;
          color: #999;
        }
      }
    }
  }
  .box_code {
    align-items: center;
    justify-content: space-evenly;
    .title {
      margin: 40rpx 0;
      font-size: 48rpx;
      color: #fdfaf9;
    }
    .code {
      width: 512rpx;
      height: 512rpx;
      background: #fff;
      border-radius: 10rpx;
      box-shadow: 0 10rpx 10rpx #f5f5f5;
      .img {
        width: 482rpx;
        height: 482rpx;
        background: skyblue;
        margin: auto;
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
    .msg {
      margin-top: 40rpx;
      font-size: 28rpx;
      color: #333;
    }
  }
  //   提交按钮
  button::after {
    border: none;
  }
  button {
    width: 508rpx;
    height: 72rpx;
    line-height: 72rpx;
    background: #ff6735;
    color: #fff;
    border-radius: 50rpx;
    margin: 60rpx auto;
    font-size: 32rpx;
    background-image: linear-gradient(to left, #fb8a65, #ff6735);
  }
  .cancel {
    width: 80rpx;
    height: 80rpx;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    position: absolute;
    bottom: -100rpx;
    left: 0;
    right: 0;
    margin: auto;
  }
}
</style>
