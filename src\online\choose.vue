<template>
<view class="online_choose" :class={show_above:show_above}>
    <view class="screen-tab flex-box" v-if="show_above">
        <view class="screen-tab-item flex-1 text-center" @click="switchTopTab(1)">
            <text>{{loudongNumber}}</text>
            <uni-icons :type="nowTab==1?'arrowup':'arrowdown'" size="16"></uni-icons>
        </view>
        <view class="screen-tab-item flex-1 text-center" @click="switchTopTab(2)">
            <text>{{unitName}}</text>
            <uni-icons :type="nowTab==2?'arrowup':'arrowdown'" size="16"></uni-icons>
        </view>
    </view>
    <view class="uni_bar">
        <tab-bar ref="tabbar" :nowIndex="tabBarIndex" :tabs="unit_list" :theme="2" :fixedTop="false" :equispaced="false" :maxNum="3" small @click="switchTab">
    </tab-bar>
    </view>
    <block v-if="show_above">
        <scroll-view scroll-y class="screen-panel" :class="nowTab==1?'show':''" @touchmove.stop.prevent="stopMove">
            <block v-for="item in loudong_list" :key="item.id">
                <uni-list-item :title="item.number" show-arrow="false" @click="selectLoudong(item)"></uni-list-item>
            </block>
        </scroll-view>
        <scroll-view scroll-y class="screen-panel" :class="nowTab==2?'show':''" @touchmove.stop.prevent="stopMove">
            <block v-if="params.number_id" v-for="item in unit_list" :key="item.id">
                <uni-list-item :title="item.unit||item.name" show-arrow="false" @click="selectUnit(item)"></uni-list-item>
            </block>
        </scroll-view>
    </block>
    <view class="mask" :class="nowTab>0?'show':''" @click="nowTab=0" @touchmove.stop.prevent="stopMove"></view>
    <view class="louceng_list">
        <choose-house v-for="ceng in ceng_list" :key="ceng.id" :house_list="ceng.houses" :ceng="ceng.floor" @onClick="handleClick"></choose-house>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <view class="bottom-menu flex-box">
        <view class="bar-item" @click="navigate('/online/detail?id='+online_id)">
            <my-icon type="home" size="22"></my-icon>
            <view class="text">大厅</view>
        </view>
        <view class="bar-item active">
            <my-icon type="jiudian" size="22" color="#f65354"></my-icon>
            <view class="text">选房</view>
        </view>
        <view class="bar-item" @click="navigate('/online/adviser?online_id='+online_id)">
            <my-icon type="xiaoxi" size="22"></my-icon>
            <view class="text">咨询</view>
        </view>
        <view class="bar-item" @click="navigate('/online/my?online_id='+online_id)">
            <my-icon type="shikebiao" size="22"></my-icon>
            <view class="text">订单</view>
        </view>
    </view>
    <!-- 提示选择置业顾问 -->
    <view class="toast choose_adviser" :class="{show:show_choose_adviser}">
        <view class="close" @click="show_choose_adviser=false">
            <my-icon type="zengjia" size="26" color="#333"></my-icon>
        </view>
        <view class="toast_content">
            <view class="title">请选择房源</view>
            <my-icon type="jiaoxing" size="38" color="#f44648"></my-icon>
            <view class="tip">您当前没有可选的房源，联系置业顾问获取房源</view>
            <view class="btn-box">
                <view class="btn flex-box" @click="toAdviser()">
                    <my-icon type="zixun" color="#00c0eb"></my-icon>
                    <text>立即联系置业顾问</text>
                </view>
            </view>
        </view>
    </view>
    <view class="mask" @click="show_choose_adviser = false" :class="{show:show_choose_adviser}"></view>
    <chat-tip></chat-tip>
</view>
</template>

<script>
import myIcon from "../components/icon.vue"
import tabBar from "../components/tabBar.vue"
import chooseHouse from '../components/chooseHouse.vue'
import {uniLoadMore,uniIcons,uniList,uniListItem} from '@dcloudio/uni-ui'
import {navigateTo,formatImg} from '../common/index.js'
import {wxShare} from '../common/mixin'
export default {
    data() {
        return {
            loudongNumber:'选择楼栋',
            unitName:'选择单元',
            nowTab:0,
            loudong_list:[],
            unit_list:[],
            tabBarIndex:0,
            ceng_list:[],
            params:{
                page:1,
                rows:10
            },
            online_id:"",
            get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
            show_choose_adviser:false,
            show_above:false,
        }
    },
    mixins: [wxShare],
    components: {
        myIcon,
        tabBar,
        chooseHouse,
        uniLoadMore,
        uniIcons,
        uniList,
        uniListItem
    },
    onLoad(options){
        if(options.online_id){
            this.online_id = options.online_id
            this.getLoudong()
            this.getUnit()
        }
        uni.$on("getDataAgain",()=>{
            this.params.page == 1
            this.getData()
        })
    },
    onUnload(){
        uni.$off("getDataAgain")
        this.$store.state.allowOpen = true
    },
    methods:{
        getLoudong(){
            this.$ajax.get('online/buildingNumber',{online_id:this.online_id},res=>{
                if(res.data.code === 1){
                    this.loudong_list = res.data.number
                }
            })
        },
        // getUnits(){
        //     this.$ajax.get('online/buildingUnit',{online_id:this.online_id,number_id:this.params.number_id},res=>{
        //         if(res.data.code === 1){
        //             this.unit_list = res.data.lists
        //         }
        //     })
        // },
        getUnit(){
            if(!this.share){
                this.share = {}
            }
            this.$ajax.get('online/onlineUnits',{online_id:this.online_id,number_id:this.params.number_id||''},res=>{
                if(res.data.code == 1){
                    this.model = res.data.online.model
                    this.unit_list = res.data.onlineUnits.map(item=>{return {id:item.id,name:item.title,unit:item.unit}})
                    this.params.unit_id = res.data.onlineUnits[0].id
                    this.params.online_id = res.data.onlineUnits[0].online_id
                    this.getCeng()
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                    this.get_status = "noMore"
                }
                if(this.share.title){
                    return
                }
                if(res.data.share&&res.data.share.title){
                    this.share = res.data.share
                    this.getWxConfig()
                }
            },err=>{
                console.log(err)
            })
        },
        getCeng(){
            if(this.model==2){
                this.show_choose_adviser = true
            }
            if(this.params.page == 1){
                this.ceng_list=[]
            }
            this.get_status = "loading"
            this.$ajax.get('online/onlineUnitHouses',this.params,res=>{
                if(res.data.code == 1){
                    this.ceng_list = this.ceng_list.concat(res.data.onlineHouses)
                    if(res.data.onlineHouses.length<this.params.rows){
                        this.get_status = "noMore"
                    }else{
                        this.get_status = "more"
                    }
                }else{
                    this.get_status = "noMore"
                }
            },err=>{
                console.log(err)
            })
        },
        selectLoudong(e){
            this.nowTab = 0
            this.loudongNumber = e.number
            this.params.number_id = e.id
            this.params.unit_id = ""
            this.unitName = "选择单元"
            this.tabBarIndex = 0
            // this.getUnits()
            this.getUnit()
            // this.getCeng()
        },
        selectUnit(e){
            this.nowTab = 0
            this.unitName = e.unit||e.name
            this.params.unit_id = e.id
            this.getCeng()
            this.unit_list.map((item,index)=>{
                if(item.id === e.id){
                    this.tabBarIndex = index
                }
            })
        },
        handleClick(e){
            if(this.model === 3){
                navigateTo(`/online/sign_up?online_id=${e.build_online_id}`)
                return
            }
            navigateTo(`/online/house_detail?id=${e.id}&online_id=${e.build_online_id}`)
        },
        navigate(url){
            navigateTo(url)
        },
        switchTopTab(index){
            if(this.nowTab == index){
                this.nowTab = 0
            }else{
                this.nowTab = index
            }
        },
        switchTab(e){
            this.tabBarIndex = e.index
            this.params.unit_id = e.id
            this.params.page = 1
            this.getCeng()
        },
        toAdviser(){
            navigateTo(`/online/adviser?online_id=${this.online_id}`)
        },
        stopMove(){

        }
    },
    onReachBottom(){
        this.params.page++
        this.getCeng()
    },
    onShareAppMessage(){
        // #ifdef MP-BAIDU
        return {
            title:this.share.title,
            content:this.share.content,
            imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):""
        }
        // #endif
        // #ifdef MP-WEIXIN
        return {
            title:this.share.title,
            imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):""
        }
        // #endif
    },
    // #ifdef H5 || APP-PLUS
    onNavigationBarButtonTap(option){
        this.show_above = !this.show_above
    },
    // #endif
}
</script>

<style scoped lang="scss">
.online_choose{
    padding-top: 90rpx;
    padding-bottom: 100rpx;
    &.show_above{
        padding-top: 160rpx;
        .uni_bar{
            position: fixed;
            width: 100%;
            top: 80rpx;
            margin-top: var(--window-top);
        }
    }
    .screen-tab{
        // position: initial;
        top: 0;
        margin-top: var(--window-top);
    }
    .screen-panel {
        top: 80rpx;
        margin-top: var(--window-top);
        // min-height: 80rpx;
        // margin-top: 170rpx;
    }
    .uni_bar{
        position: fixed;
        width: 100%;
        top: 0;
        margin-top: var(--window-top);
        z-index: 97;
    }
    .louceng_list{
        padding: 20rpx 10rpx;
        .louceng_item{
            align-items: center;
            border-radius: 10rpx;
            padding:8rpx 6rpx;
            margin-bottom: 20rpx;
            background-color: #fff;
        }
        .louceng_num{
            padding: 6rpx;
            margin-right: 5rpx;
            text{
                display: inline-block;
                width: 70rpx;
                height: 70rpx;
                line-height: 70rpx;
                border-radius: 50%;
                text-align: center;
                color: $uni-color-primary;
                border: 4rpx solid $uni-color-primary;
            }
        }
        .louceng_block{
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            .house{
                flex: 1;
                max-width: 45%;
                min-width: 40%;
                padding: 12rpx 10rpx;
                margin: 6rpx;
                border-radius: 6rpx;
                background-color: #f5f3f3;
                &.supplement{
                    height: 0;
                    background-color: inherit;
                }
                .house_num{
                    font-size: 30rpx;
                    color: #333;
                    margin-bottom: 10rpx;
                }
                .house_info{
                    justify-content: space-between;
                    font-size: 26rpx;
                    color: #888;
                }
            }
        }
    }
    .bottom-menu{
        position: fixed;
        width: 100%;
        padding: 8rpx 0;
        box-sizing: border-box;
        bottom: 0;
        background-color: #fff;
        border-top: 1rpx solid #dedede;
        .bar-item{
            line-height: 1;
            flex: 1;
            text-align: center;
            color: #333;
            .text{
                margin-top: 8rpx;
            }
            &.active{
                color:  $uni-color-primary;
            }
        }
    }
}

// 弹窗
.toast{
    position: fixed;
    left: 0;
    right: 0;
    padding: 40rpx;
    margin: auto;
    background-color: rgba($color: #000000, $alpha: 0.6);
    border-radius: 20rpx;
    opacity: 0;
    z-index: -1;
    transition: 0.26s;
    transform:scale(0.3,0.3);
    &.show{
        opacity: 1;
        transform:scale(1,1);
        z-index: 99;
    }
    .close{
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        transform:rotate(45deg);
        z-index: 2;
    }
}

// 选择置业顾问弹窗
.choose_adviser{
    width: 75vw;
    top: 25vh;
    background-color: #fff;
    .toast_content{
        text-align: center;
        .title{
            padding-left: 10rpx;
            line-height: 1;
            margin: 40rpx 0;
            font-size: 30rpx;
            font-weight: bold;
            text-align: left;
            border-left: 6rpx solid #f22627;
        }
        .tip{
            font-size: 26rpx;
            color: #666;
            margin: 30rpx 0;
        }
        .btn-box{
            display: inline-block;
            margin-top: 0;
        }
        .btn{
            align-items: center;
            padding: 6rpx 10rpx;
            line-height: 1;
            border: 1rpx solid #00c0eb;
            border-radius: 10rpx;
            font-size: 24rpx;
            color: #00c0eb;
        }
    }
}

.mask{
    position: fixed;
    top: 0;
    bottom: 0;
    width: 100%;
    z-index: -1;
    background-color: rgba($color: #000000, $alpha: 0);
    transition: 0.26s;
    &.show{
        background-color: rgba($color: #000000, $alpha: 0.5);
        z-index: 90;
    }
}
</style>
