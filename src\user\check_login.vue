<template>
  <view class="page">
    <view class="block" v-show="user_login_status == 3">
      <view class="img">
        <image mode="widthFix" :src="icon | imageFilter('m_320')"></image>
      </view>
      <view class="tip_tex">授权成功</view>
    </view>
    <view class="block" v-show="user_login_status == 3">
      <view class="q">1.关注服务号有什么好处?</view>
      <view class="a">无需登录小程序，也可接收用户消息,这么方便确定不试试?</view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import {config} from '@/common/config'
export default {
  components: {},
  data() {
    return {
      icon: '/images/new_icon/auth_success.png'
    }
  },
  computed: {
    ...mapState(['user_login_status', 'siteName'])
  },
  watch:{
    siteName(value){
      if(value){
        this.setShare()
      }
    }
  },
  onLoad() {
    this.getUserStatus()
    this.setShare()
  },
  methods: {
    setShare(){
      this.share = {
        title: this.siteName?`点击授权绑定【${this.siteName}】服务号`:'点击授权绑定服务号',
        content: "授权成功后，可实时收发平台消息通知。",
        pic: config.imgDomain+this.icon
      }
      this.getWxConfig()
    },
    getUserStatus() {
      this.$ajax.get('member/checkUserStatus', {}, res => {
        this.$store.state.user_login_status = res.data.status || ''
        if (res.data.status == 1) {
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
          return
        }
        if (res.data.status === 2) {
          this.$navigateTo('/user/bind_phone/bind_phone')
          return
        }
        if (res.data.code !== 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  background-color: #f3f3f3;
}
.block {
  padding: 48rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
}
.img{
  padding: 48rpx;
  width: 280rpx;
  height: 280rpx;
  margin: auto;
  image{
    width: 100%;
    height: 100%;
  }
}
.tip_tex {
  padding: 24rpx;
  font-size: 40rpx;
  font-weight: bold;
  text-align: center;
}
.q {
  margin-bottom: 24rpx;
  color: #4799f1;
}
.a {
  color: #666;
}
</style>
