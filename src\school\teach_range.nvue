<template>
  <div class="page range_page" :style="{width:window_width+'px'}">
      <map
        class="qqmap"
		:style="{height:(window_height)+'px',width:window_width+'px'}"
        :longitude="center.longitude"
        :latitude="center.latitude"
        :markers="school_point"
        :polygons="polygons"
        @markertap="onClickMarker"
      ></map>
    <div class="range_info" :style="{width:window_width+'px'}">
      <div class="range_title">
        <text>施教范围</text>
       <!-- <div
          class="btn"
          @click="toSchool()"
        > -->
          <text  @click="toSchool()" class="title_text">详细</text>
        <!-- </div> -->
      </div>
      <div class="range_content">
        <div class="range_row" v-if="range_content.target">
          <text class="label">招生对象:</text>
          <text class="value  range_tar">{{ range_content.target }}</text>
        </div>
        <div class="range_row" v-if="range_content.target_cpunt">
          <text class="label">招生人数:</text>
          <text class="value range_num">{{ range_content.target_cpunt }}</text>
        </div>
        <div class="range_row" v-if="range_content.descp">
          <text class="label">对口地段:</text>
          <text class="value range_dis">{{ range_content.descp }}</text>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
    ajax
} from '../common/index.js'
import {navigateTo,formatImg,compareVersion,showModal} from '../common/index'
export default {
  data() {
    return {
      active: 0,
      show_invit:false,
      center: {
        longitude: '',
        latitude: ''
      },
	    window_width:"",
	    window_height:"",
      school_point: [],
      polygons: [],
      range_content: {},
      user_status_tip:"当前服务需要开通VIP后查看",
      userLoginStatus:null,
      satisfyVersion:-1,
      showPopup:false,
    }
  },
  created(){

    this.window_width = this.$store.state.systemInfo.safeArea.width
    this.window_height = this.$store.state.systemInfo.safeArea.height 
  },
  onLoad(options) {
    if (options.school_id) {
      this.school_id = options.school_id
      this.getData()
      this.getRangeTex()
    }
  
  
  },
  onShow(){
    if(this.isToUpVip){
      this.isToUpVip = false
      this.getUserStatus()
    }
  },
  methods: {
    switchTab(index) {
      if (index === this.active || index === 0) {
        this.active = 0
      } else {
        this.active = index
      }
    },
    getData() {
      ajax.get('school/schoolInfo.html', { id: this.school_id }, res => {
        setTimeout(()=>{
          this.getUserStatus()
        },500)
        if (res.data.code === 1) {
          this.center = {
            latitude: res.data.data.lat,
            longitude: res.data.data.lng
          }
          this.school_point = [
            {
              id: this.id,
              school_id: this.id,
              iconPath: 'https://images.tengfangyun.com/images/icon/xiaoxuequ.png',
              latitude: res.data.data.lat,
              longitude: res.data.data.lng,
              width: 32,
              height: 32,
              callout: {
                content: res.data.data.name,
                padding: 10,
                borderRadius: 8
              }
            }
          ]
          if (!res.data.data.teach_range) {
            this.polygons = []
            return
          }
          const color = '#f65354'
          this.polygons = res.data.data.teach_range.map(items => {
            const points = items.paths.map(item => {
              return {
                latitude: parseFloat(item.latitude),
                longitude: parseFloat(item.longitude)
              }
            })
            return {
              id: items.id,
              points,
              strokeWidth: 1,
              strokeColor: color,
              fillColor: `${color}33`
            }
          })
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    // 获取用户账号状态
    getUserStatus(){
      ajax.get('school/checkUserInfo',{},res=>{
        this.userLoginStatus = res.data.loginStatus
        if(this.userLoginStatus===0){
          showModal({
            title:'温馨提示',
            content:"当前服务需要会员登录后查看",
            confirmText:"去登录",
            confirm:()=>{
              this.toLogin()
            },
            cancel:()=>{
              this.back()
            }
          })
        }
        if(this.userLoginStatus === 1){
          
            showModal({
              title:'温馨提示',
              content:"您的账号已被封禁，请开通个人VIP后查看",
              confirmText:"去开通",
              confirm:()=>{
                this.toUpVip()
              },
              cancel:()=>{
                this.back()
              }
            })
        }
        if(this.userLoginStatus === 2){
          
            showModal({
              title:'温馨提示',
              content:"当前服务需要开通个人VIP后查看",
              confirmText:"去开通",
              confirm:()=>{
                this.toUpVip()
              },
              cancel:()=>{
                this.back()
              }
            })
        }
        if(this.userLoginStatus===3){
          // #ifndef MP-WEIXIN
          // this.$refs.login_popup.closeSub()
          // #endif
          this.show_invit = false
        }
      })
    },
    // 登录成功的回调事件
    onLoginSuccess(){
      // 重新获取用户账号状态
      this.getUserStatus()
    },
    // 关闭登录窗口的事件
    handleCloseLogin(){
      // 如果是登录状态怎不做任何处理
      if(this.userLoginStatus !==0){
        return
      }
      let pages = getCurrentPages() 
      if(pages.length>1){
        uni.navigateBack()
      }else{
        uni.switchTab({
          url:'/'
        })
      }
    },
    toUpVip(){
      this.isToUpVip = true
      navigateTo('/pages/my/member_upgrade')
    },
    toLogin(){
      this.isToUpVip = true
      uni.removeStorageSync('token')
      navigateTo('/user/login/login')
    },
    back(){
      if (getCurrentPages().length > 1) {
        uni.navigateBack()
      } else {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
    },
    getRangeTex() {
      ajax.get(
        'school/schoolRange.html',
        { id: this.school_id },
        res => {
          if (res.data.code === 1) {
            this.range_content = res.data.data
          }
          if(res.data.share){
            this.share = res.data.share
          }else{
            this.share = {}
          }
        }
      )
    },
    onClickMarker() {
      console.log(this.id)
    },
    toSchool(){
      let pages = getCurrentPages() 
      if(pages.length>1&&pages[pages.length-2].route==='school/detail'){
        uni.navigateBack()
      }else{
        navigateTo(`/school/detail?id=${this.school_id}`)
      }
    }
  },
  onShareAppMessage() {
    if (this.share) {
      return {
        title: this.share.title||"",
        content:this.share.content||"",
        imageUrl: this.share.pic?formatImg(this.share.pic, 'w_6401'):""
      }
    }
  }
}
</script>

<style scoped lang="scss">
.map-box {
  position: absolute;
  top: 0;
  bottom: 0;
  
}
.title_text{
	background-color: #FEF2EE;
	color: #F00;
	padding: 1px 5px;
	font-size: 12px;
	
}
.range_info {
  position: fixed;
  bottom: 0;
  z-index: 10;
  padding: 30rpx;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  background-color: #fff;
}
.range_title {
  position: relative;
  font-size: 30upx;
  padding: 0 10rpx;
  margin-bottom: 30rpx;
  border-left-width: 4rpx ;
  border-left-style: solid;
  border-left-color:#f65354;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  
}
.btn {
  position: absolute;
  right: 0;
  top: 0;
  flex-direction: row;
  height: 42rpx;
  line-height: 42rpx;
  padding: 0 15rpx;
  border-radius: 22rpx;
  font-size: 24upx;
  background-color: #fef2ee;
  color: #f65354;
}
.range_row {
    padding: 10rpx 0;
    flex-direction: row;
	align-items:flex-start;
    font-size: 28rpx;
    line-height: 1.5;
  }
  .label{
	  flex: 1;
	  font-size:28rpx;
	  color: #999999;
	  margin-right: 5rpx;
  }
  .range_tar{
	flex: 4; 
	lines: 2;
	 color: #999999;
	text-overflow: ellipsis;
  }
  .range_num{
  	flex: 4; 
  	lines: 3;
	 color: #999999;
	text-overflow: ellipsis;
  }
  .range_dis{
	color: #999999;
  	flex: 4; 
  	lines: 3;
	text-overflow: ellipsis;
	
  }
.value {
  margin-left: 10rpx;
  flex: 1;
  font-size: 28upx;
}
.up_vip{
  width: 508rpx;
  height: 72rpx;
  line-height: 72rpx;
  background-color: #ff6735;
  color: #fff;
  border-radius: 50rpx;
  font-size: 32rpx;
  background-image: linear-gradient(to left, #fb8a65, #ff6735);
}
</style>