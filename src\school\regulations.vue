<template>
  <view class="regulations_list">
    <view class="main-info row bottom-line" @click="toSchool()" v-if="school_info.name">
      <image
        class="logo"
        mode="aspectFill"
        :src="school_info.pic | imgUrl('w_240')"
      />
      <view class="info">
        <view class="name">{{ school_info.name }}</view>
        <view class="address">{{ school_info.address }}</view>
      </view>
      <view
        class="radiate left-line text-center"
        @click.stop.prevent="
          navigateTo(`/school/teach_range?school_id=${params.school_id}`)
        "
      >
        <my-icon type="fanwei" size="25" color="#ff6735"></my-icon>
        <view class="text">施教范围</view>
      </view>
    </view>
    <time-line :line_data="recruit_list" @click="onClick"></time-line>
    <no-data
      tip="该学校的招生简章待更新"
      v-if="recruit_list.length === 0 && load_status === 'nomore'"
    ></no-data>
    <uni-load-more v-else :status="load_status"></uni-load-more>
    <view class="fixed-bottom">
      <footer-nav
        @sign="show_popup = true"
        tel
        @makePhoneCall="makePhoneCall"
      ></footer-nav>
    </view>
  </view>
</template>

<script>
import noData from '../components/noData'
import timeLine from './components/timeLine'
import { uniLoadMore } from "@dcloudio/uni-ui";
import myIcon from '@/components/icon'
import footerNav from './components/footerNav'
import {
  navigateTo,
  formatImg
} from '../common/index.js'
import wxApi from '../common/mixin/wx_api';
export default {
  components: { noData, timeLine, uniLoadMore, myIcon, footerNav },
  mixins:[wxApi],
  data() {
    return {
      params: {
        page: 1,
        rows: 20
      },
      load_status: '',
      load_text: {},
      school_info: {},
      recruit_list: []
    }
  },
  onLoad(options) {
    this.params.school_id = options.school_id || ''
    if (this.params.school_id) {
      this.getData()
    }
  },
  filters: {
    imgUrl(val,param) {
      if (!val) {
        return ""
      }
      return formatImg(val, param)
    }
  },
  methods: {
    getData() {
      this.load_status = 'loading'
      this.$ajax.get('school/recruitList.html', this.params, res => {
        this.load_status = 'loadend'
        if (res.data.code === 1) {
          if (this.params.page === 1) {
            this.school_info = res.data.school
            this.share = res.data.share
            this.getWxConfig()
          }
          this.recruit_list = res.data.data
          if (res.data.data.length < this.params.rows) {
            this.load_status = 'nomore'
          }
        } else {
          this.load_status = 'nomore'
        }
      })
    },
    navigateTo(url){
      navigateTo(url)
    },
    onClick(index) {
      navigateTo(`/school/regulations_detail?id=${this.recruit_list[index].id}`)
    },
    toSchool(){
      let pages = getCurrentPages() 
      if(pages.length>1&&pages[pages.length-2].route==='school/detail'){
        uni.navigateBack()
      }else{
        navigateTo(`/school/detail?id=${this.params.school_id}`)
      }
    },
    makePhoneCall() {
      if (!this.school_info.tel) {
        uni.showToast({
          title: '学校电话待更新',
          icon: 'none'
        })
        return
      }
      uni.makePhoneCall({
        phoneNumber: this.detail.tel,
        success: () => {
          console.log('触发拨打电话')
        }
      })
    }
  },
  onReachBottom() {
    if (this.load_status === 'nomore') {
      return
    }
    this.params.page++
    this.getData()
  }
}
</script>

<style scoped lang="scss">
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.regulations_list {
  padding-bottom: 120rpx;
  background-color: #fff;
}
.main-info {
  padding: 30rpx;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom:  40upx;
  .logo {
    width: 90rpx;
    height: 90rpx;
    border-radius: 50%;
    margin-right: 30rpx;
  }
  .info {
    flex: 1;
    .name {
      font-size: 36rpx;
      line-height: 1.5;
      font-weight: bold;
      margin-bottom: 10rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
    }
    .address {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      font-size: 22rpx;
      color: #333;
    }
  }
  .radiate {
    padding: 0 30rpx;
    font-size: $uni-font-size-sm;
    .text {
      margin-top: 10rpx;
      color: #666;
    }
  }
}
.fixed-bottom {
  width: 100%;
  padding: 10rpx 30rpx;
  position: fixed;
  bottom: 0;
  z-index: 3;
}
</style>
