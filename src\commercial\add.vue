<template>
  <view>
    <!-- <view class="money_tip" v-if="upgrade_tip.tips">
      <my-icon type="tishifu" color="#fb656a" size="36rpx"></my-icon>
      <text class="text flex-1">{{ upgrade_tip.tips }}</text>
      <text v-if="upgrade_tip.upgrade_vip === 1" class="btn" @click="$navigateTo('/user/member_upgrade?is_personal=1')"
        >去升级</text
      >
    </view> -->
    <!-- 类别 -->
    <view class="block">
      <!-- <view class="select_btn flex-row" @click="showCatPopup"> -->
      <view class="select_btn flex-row">
        <view>
          <text class="label">类别</text>
          <text class="value" :class="{ has_value: catesName }">{{ catesName || '请选择' }}</text>
        </view>
        <!-- <view>
          <my-icon type="ic_into" size="36rpx" color="#999"></my-icon>
        </view> -->
      </view>
      <my-popup ref="cates_popup">
        <view class="cates_box">
          <view class="title">请选择</view>
          <view class="cates_list">
            <scroll-view scroll-y class="cate">
              <view class="item bottom-line" v-for="item in cates_list" :key="item.id" @click="selectCates(item)">{{
                item.title
              }}</view>
            </scroll-view>
          </view>
        </view>
      </my-popup>
      <my-radio
        v-if="form.parentid"
        @change="radioChange"
        :label="form.parentid.title"
        :range="form.parentid.rules"
        :name="form.parentid.identifier"
        :value="params.parentid"
      ></my-radio>
      <my-input
        v-if="form.useful_years"
        :label="form.useful_years.title"
        :unit="form.useful_years.rules"
        v-model="params.useful_years"
        :type="form.useful_years.type"
        placeholder="请输入"
      ></my-input>
    </view>
    <!-- 小区 -->
    <view class="block">
      <view @click="toSearchCommunity" v-if="(catid == 1 || catid == 2 || catid == 4)&&this.release_need_select_community == 1" >
        <my-input label="小区" show_arrow :value="community_name" disabled placeholder="请选择小区或留空"></my-input>
      </view>
      <view class="select_btn flex-row" @click="showAreaPopup">
        <view class="left">
          <text class="label">区域</text>
          <text class="value" :class="{ has_value: area_name }">{{ area_name || '请选择' }}</text>
        </view>
        <view>
          <my-icon type="ic_into" size="36rpx" color="#999"></my-icon>
        </view>
      </view>
      <my-picker v-if="form.type_id" :options="form.type_id.options" @change="onPickerChange"></my-picker>
      <my-picker v-if="form.land_source" :options="form.land_source.options" @change="onPickerChange"></my-picker>
      <my-radio
        v-if="form.land_certificate"
        @change="radioChange"
        :label="form.land_certificate.title"
        :range="form.land_certificate.rules"
        :name="form.land_certificate.identifier"
        :value="params.land_certificate"
      ></my-radio>
      <my-radio
        v-if="form.owner_certificate"
        @change="radioChange"
        :label="form.owner_certificate.title"
        :range="form.owner_certificate.rules"
        :name="form.owner_certificate.identifier"
        :value="params.owner_certificate"
      ></my-radio>
      <my-popup ref="area_popup">
        <addressPicker :data_list="area_list" @onselect="onAreaChange"></addressPicker>
      </my-popup>
      <my-popup ref="cates_popup">
        <view class="cates_box">
          <view class="title">请选择</view>
          <view class="cates_list">
            <scroll-view scroll-y class="cate">
              <view class="item bottom-line" v-for="item in cates_list" :key="item.id" @click="selectCates(item)">{{
                item.title
              }}</view>
            </scroll-view>
          </view>
        </view>
      </my-popup>
    </view>
    <!-- 图片 -->
    <view class="block">
      <view class="upload-box flex-row" @click="addPhotos">
        <image class="cover_img" v-if="params.cover_path" :src="params.cover_path"></image>
        <view class="upload_btn" v-else>
          <my-icon type="ic_jia" size="60rpx" color="#d8d8d8"></my-icon>
        </view>
        <view class="upload_tip flex-1">
          <text class="tip_title">上传照片</text>
          <text class="tip_content"
            >只能上传房屋图片,不能含有文字、数字、网址、名片、水印等，所有类别图片总计20张。</text
          >
        </view>
        <my-icon type="ic_into" color="#999"></my-icon>
      </view>
    </view>
    <view
      class="block"
      v-if="form.business_status || form.trade_id || otherForm_list.find((f) => f.identifier == 'trade_id')"
    >
      <my-radio
        v-if="form.business_status"
        @change="radioChange"
        :label="form.business_status.title"
        :range="form.business_status.rules"
        :name="form.business_status.identifier"
        :value="params.business_status"
      ></my-radio>
      <view
        v-if="form.trade_id || otherForm_list.find((f) => f.identifier == 'trade_id')"
        class="select_btn flex-row"
        @click="showTradePopup"
      >
        <view>
          <text class="label">经营行业</text>
          <text class="value" :class="{ has_value: tradeName }">{{ tradeName || '请选择' }}</text>
        </view>
        <view>
          <my-icon type="ic_into" size="36rpx" color="#999"></my-icon>
        </view>
      </view>
      <my-popup ref="trade_popup">
        <my-cascader :data_list="trade_list" @onselect="onTradeChange"></my-cascader>
      </my-popup>
    </view>
    <view class="block" v-if="form.can_register || form.can_divisible">
      <my-radio
        v-if="form.can_register"
        @change="radioChange"
        :label="form.can_register.title"
        :range="form.can_register.rules"
        :name="form.can_register.identifier"
        :value ="params.can_register"
      ></my-radio>
      <my-radio
        v-if="form.can_divisible"
        @change="radioChange"
        :label="form.can_divisible.title"
        :range="form.can_divisible.rules"
        :name="form.can_divisible.identifier"
        :value ="params.can_divisible"
      ></my-radio>
    </view>
    <view class="block" v-if="form.floor_type">
      <my-picker v-if="form.floor_type" :options="form.floor_type.options" @change="onPickerChange"></my-picker>
      <view v-if="form.floor_type" class="select_btn flex-row">
        <view class="szlc">
          <view><text class="label">所在楼层</text></view>
          <view class="szlc-val">
            <view class="szlc1" v-if="params.floor_type == 1 || params.floor_type == 2">
              <input type="number" v-model="params.szlc" placeholder="请填写" />
              <text>层</text>
            </view>
            <text v-if="params.floor_type == 2">至</text>
            <view class="szlc2" v-if="params.floor_type == 2">
              <input type="number" v-model="params.szlc2" placeholder="请填写" />
              <text>层</text>
            </view>
            <text>共</text>
            <view>
              <input type="number" v-model="params.louceng" placeholder="请填写" />
              <text>层</text></view
            >
          </view>
        </view>
      </view>
      <my-input
        v-if="form.sizes_width"
        :label="form.sizes_width.title"
        :unit="form.sizes_width.rules"
        v-model="params[form.sizes_width.identifier]"
        :type="form.sizes_width.type"
        placeholder="请输入"
      ></my-input>
      <my-input
        v-if="form.sizes_height"
        :label="form.sizes_height.title"
        :unit="form.sizes_height.rules"
        v-model="params[form.sizes_height.identifier]"
        :type="form.sizes_height.type"
        placeholder="请输入"
      ></my-input>
      <my-input
        v-if="form.sizes_depth"
        :label="form.sizes_depth.title"
        :unit="form.sizes_depth.rules"
        v-model="params[form.sizes_depth.identifier]"
        :type="form.sizes_depth.type"
        placeholder="请输入"
      ></my-input>
      <my-picker v-if="form.zhuangxiu" :options="form.zhuangxiu.options" @change="onPickerChange"></my-picker>
    </view>
    <view class="block">
      <!-- 转让费 -->
      <my-input
        v-if="form.transfer_fee || params.parentid == '3'"
        label="转让费"
        unit="万元"
        v-model="params.transfer_fee"
        type="number"
        placeholder="请输入"
      ></my-input>
      <!-- 剩余租期 -->
      <my-input
        v-if="form.remain_lease || params.parentid == '3'"
        label="剩余租期"
        unit="月"
        v-model="params.remain_lease"
        type="number"
        placeholder="请输入"
      ></my-input>
      <my-radio
        v-if="catid == 5"
        @change="radioChange"
        label="面积单位"
        :range="mianjiRules"
        name="mianji_type"
        :value="params.mianji_type"
      ></my-radio>
      <my-input
        :label="catid == 5?'面积':'建筑面积'"
        :unit="catid == 5 && params.mianji_type == 1 ? '亩' : '㎡'"
        v-model="params.mianji"
        type="digit"
        placeholder="请输入"
      ></my-input>
      <my-input
        v-if="(catid != '5' && parentid == '1') || params.parentid == '1'"
        label="售价(0或者不填为面议)"
        unit="万元"
        v-model="params.fangjia"
        type="digit"
        placeholder="请输入"
      ></my-input>
      <my-radio
        v-if="parentid == '2' || parentid == '3' || params.parentid == '2' || params.parentid == '3'"
        @change="radioChange"
        label="租金类型"
        :range="zujinTypes"
        name="zujin_type"
        :value="params.zujin_type"
      ></my-radio>
      <my-input
        v-if="parentid == '2' || parentid == '3' || params.parentid == '2' || params.parentid == '3'"
        label="租金(0或者不填为面议)"
        :unit="zujin_unit"
        v-model="params.zujin"
        type="digit"
        placeholder="请输入"
      ></my-input>
    </view>
    <view class="block">
      <view>
        <my-input label="标题" :maxlength="50" v-model="params.title" type="text" placeholder="请输入">
        </my-input>
      </view>
      <view @click="toAddDesc">
        <my-input label="描述" :maxlength="1000" show_arrow v-model="params.content" disabled placeholder="请输入"> </my-input>
      </view>
    </view>
    <view class="block">
      <my-input label="联系人"  @input ="handleConcatInput" v-model="params.contact_who" placeholder="请输入"> </my-input>
    </view>
    <!-- 完善更多非必填的信息 -->
    <view class="block" v-if="otherForm_list.length > 0 || (labels && labels.length > 0)">
      <view class="open_menu flex-row" @click="show_more_info = !show_more_info">
        <view class="flex-row">
          <text>完善更多信息</text>
          <text class="tip">（非必填）</text>
        </view>
        <my-icon :type="show_more_info ? 'ic_close' : 'ic_open'" color="#999"></my-icon>
      </view>
      <view class="more_info_box" :class="{ open: show_more_info }">
        <my-checkbox
          v-if="labels.length > 0"
          :maxnum="4"
          @select="handelCheckLabel"
          label="特色标签"
          :range="labels"
          :values="selectLabels"
          name="label"
        ></my-checkbox>
        <block v-for="item in otherForm_list" :key="item.key">
          <my-input
            v-if="inputType.includes(item.type)"
            v-model="params[item.identifier]"
            :label="item.title"
            :unit="item.rules"
            :type="item.type"
            placeholder="请输入"
          ></my-input>
          <my-select
            v-if="item.type == 'select'"
            :value="item.value[0] || ''"
            @change="pickerChange"
            :label="item.title"
            :range="item.rules"
            :name="item.identifier"
            placeholder="请选择"
          ></my-select>
          <my-radio
            v-if="item.type == 'radio'"
            :value="item.value[0]"
            @change="radioChange"
            :label="item.title"
            :range="item.rules"
            :name="item.identifier"
          ></my-radio>
          <my-checkbox
            v-if="item.type == 'checkbox'"
            :values="item.value"
            @select="handelCheckbox"
            :label="item.title"
            :range="item.rules"
            :name="item.identifier"
          ></my-checkbox>
        </block>
      </view>
    </view>
    <!-- 房源统一核验 -->
    <view class="block" v-if="parentid == '1' && if_info_verification_code == 1 && catid != '5'">
      <view class="open_menu flex-row" @click="addVerification()">
        <view class="flex-row">
          <text>房源统一核验</text>
        </view>
        <my-icon type="ic_into" color="#999"></my-icon>
      </view>
    </view>
    <!-- 房源内部编号 -->
    <view class="block"  v-if ='open_info_internal_no'>
      <my-input
        label="房源内部编号"
        show_arrow
        v-model="params.internal_no"
        placeholder="请输入"
      >
      </my-input>
    </view>
    <view class="block">
      <view class="open_menu flex-row">
        <text>有效期</text>
      </view>
      <view class="options-box flex-row">
        <view
          class="options"
          :class="{ active: item.id === params.activetime_id }"
          @click="params.activetime_id === item.id ? (params.activetime_id = '') : (params.activetime_id = item.id)"
          v-for="item in activeTime"
          :key="item.id"
        >
          <text class="title">{{ item.name }}</text>
          <view class="price" v-if="youxiaoqiHasMoney">
            <!-- <text>{{ item.money }}</text>
            <text class="unit">元</text> -->
          </view>
          <text class="tip" v-if="youxiaoqiHasMoney">￥{{ item.money }}</text>
        </view>
        <view class="options vacancy"></view>
      </view>
    </view>
    <!-- 选择推广方式 -->
    <view class="block">
      <view class="open_menu flex-row" @click="show_pay_info = !show_pay_info">
        <view class="flex-row">
          <text>推广方式</text>
          <view class="flex-row">
            <text class="ding">顶</text>
            <text class="jing">精</text>
          </view>
        </view>
        <my-icon :type="show_pay_info ? 'ic_close' : 'ic_open'" color="#999"></my-icon>
      </view>
      <view class="more_info_box" :class="{ open: show_pay_info }">
        <view class="tips" v-if="extension_type == 'jing'">精选后可使房源保持前列，每半小时刷新一次，直至次数用完为止</view>
        <view class="types flex-row">
          <view class="type-item" :class="{ active: extension_type === 'ding' }" @click="extension_type = 'ding'"
            >置顶</view
          >
          <view class="type-item" :class="{ active: extension_type === 'jing' }" @click="extension_type = 'jing'"
            >精选</view
          >
        </view>
        <view class="options-box flex-row" v-show="extension_type === 'ding'">
          <view
            class="options"
            :class="{ active: ding_item.id === params.tops_id }"
            @click="onClickTop(ding_item)"
            v-for="ding_item in extension_ding"
            :key="ding_item.id"
          >
            <text class="title">置顶天数</text>
            <view class="price">
              <text>{{ ding_item.name }}</text>
              <!-- <text class="unit">元</text> -->
            </view>
            <text v-if="use_meal_ding" class="tip">{{ ding_item.title }}</text>
            <text v-else class="tip">￥{{ ding_item.money }}</text>
          </view>
          <view class="options vacancy"></view>
        </view>
        <view class="options-box flex-row" v-show="extension_type === 'jing'">
          <view
            v-if="freeSelected.name && !use_meal_jing"
            class="options"
            :class="{ active: params.freeSelected }"
            @click="onClickFreeJingxuan()"
          >
            <text class="title">精选刷新次数</text>
            <view class="price">
              <text>{{ freeSelected.name }}</text>
              <!-- <text class="unit">元</text> -->
            </view>
            <text class="tip">￥{{ freeSelected.money }}</text>
          </view>
          <view
            class="options"
            :class="{ active: jing_item.id === params.jingxuan_id }"
            @click="onClickJingxuan(jing_item)"
            v-for="jing_item in extension_jing"
            :key="jing_item.id"
          >
            <text class="title">精选刷新次数</text>
            <view class="price">
              <text>{{ jing_item.name }}</text>
              <!-- <text class="unit">元</text> -->
            </view>
            <text v-if="use_meal_jing" class="tip">{{ jing_item.title }}</text>
            <text v-else class="tip">￥{{ jing_item.money }}</text>
          </view>
          <view class="options vacancy"></view>
        </view>
        <view class="tip">再次点击可取消</view>
      </view>
    </view>
    <view class="tips_box flex-row" v-if ="memberVip.length>0">
      <template  v-for ="(item,index) in memberVip">
          <view class="tips_box_item" :class="{'active':currentType==item.type}" v-if ="item.type !='check_release'  ||(item.type =='check_release'&&!params.tops_id&&!params.jingxuan_id)" @click ="changeMemberVip(item.type)" :key ="index" >
            <view class="title_box flex-row">
              <view class="tips_box_title">{{item.title}}</view>
              <view class="icon"><image :src ="icon_bg | imageFilter('m_320')" mode="widthFix"></image></view>
            </view>
            <view class="sanjiao" v-if ="item.type=='upgrade_release'"></view>
            <view class="tuijian" v-if ="item.type=='upgrade_release'">
              荐
            </view>
            <view class="money_box">
              {{item.pay}}
            </view>
            <view class="tip_con">
              {{item.descp}}
            </view>
            <view class="tip_box_bg">
              <image :src ="item.type | filterBg()" mode="widthFix"></image>
            </view>
          </view>
      </template>
    </view>
    <view class="rule flex-row">
      <label class="flex-box" style="align-items: center" @click="on_ready = !on_ready">
        <checkbox color="#ff656c" checked></checkbox>
        <text>已阅读并接受</text>
      </label>
      <text class="rule_link" @click="$navigateTo('/user/agreement?type=push_info')"
        >《{{ $store.state.siteName }}房源信息发布规则》</text
      >
    </view>
    <view
      class="money_tip"
      style="margin-top: 24rpx"
      v-if="
        use_middle_call_house === 1 ||
        (use_middle_call_house === 2 && levelid >= 2) ||
        (use_middle_call_house === 3 && levelid <= 1)
      "
    >
      <my-icon type="tishifu" color="#fb656a" size="36rpx"></my-icon>
      <text class="text flex-1">为避免恶意骚扰，系统已为你开启隐私号保护，呼叫方将看不到你的真实号码</text>
    </view>
    <view class="btn-box">
      <view class="btn" @click="subData">立即发布</view>
    </view>
    <!-- <my-model :is_show="show_model" :btns='[{name: "取消", color: "#666666"}, {name:"确认", color: "#f65354"}]' @clickBtn="clickModelBtn">
      <view v-html="modelContent"></view>
    </my-model> -->
    <showModel :is_show="show_model" :content="modelContent"  cancelText="立即管理房源" confirmText='确认继续发布'  @confirm ="confirm" @cancel ="cancel" @close ="show_model=false"></showModel>
  </view>
</template>

<script>
import myInput from '../components/form/newInput.vue'
import myIcon from '../components/myIcon.vue'
import myPicker from '../components/myPicker.vue'
import mySelect from '../components/form/mySelect.vue'
import myRadio from '../components/form/myRadio.vue'
import myCheckbox from '../components/form/myCheckbox.vue'
import showModel from '../components/showModel.vue'
import myDialog from '../components/dialog.vue'
import myCascader from '../components/myCascader'
import myPopup from '../components/myPopup.vue'
import addressPicker from '../components/addressPicker.vue'
import myModel from '@/components/myModel'
import { showModal, config } from '../common/index.js'
import { mapState, mapMutations } from 'vuex'
export default {
  components: {
    myInput,
    myIcon,
    myPicker,
    mySelect,
    myRadio,
    myCheckbox,
    myDialog,
    myPopup,
    showModel,
    myCascader,
    myModel,
    addressPicker,
  },
  data() {
    return {
      upgrade_tip: {},
      parentid: '',
      catid: '',
      publicParams: {
        title: '',
        type_id: '',
        cover_path: '',
        business_status: '',
        imgs: [],
        imgs1: [],
        imgs2: [],
        videos: [],
        tops_id: '',
        freeSelected: '',
        zhuangxiu: '',
        content: '',
        owner_think: '',
        service_introduce: '',
        label: '',
        activetime_id: '',
        zujin_type: 0,
        mianji_type: 0,
        owner_certificate:'',
        land_certificate:'',
        internal_no:'',
      },
      open_info_internal_no:0, //是否开启房源内部编号
      params: {},
      form: {},
      inputType: ['number', 'text', 'tel', 'digit'],
      catesName: '',
      cates_list: [],
      community_name: '',
      area_name: '',
      area_list: [],
      trade_list: [],
      tradeName: '',
      businessName: '',
      otherForm_list: [],
      show_more_info: false,
      labels: [],
      activeTime: [],
      youxiaoqiHasMoney: false,
      freeActiveTime: 0,
      show_pay_info: false,
      extension_type: 'ding',
      extension_ding: [],
      extension_jing: [],
      freeSelected: {},
      pay_params: {},
      use_meal_ding: false,
      use_meal_jing: false,
      on_ready: true,
      show_model: false,
      modelContent: '',
      modelData: {},
      memberVip:[],
      currentType:'upgrade_release',
      groupLabels: {},
      zujinTypes: [
        {value: 0, name: '月租金', unit: '元/月'},
        {value: 1, name: '年租金', unit: '万元/年'},
      ],
      mianjiRules: [
        {name: 'm²', value: 0},
        {name: '亩', value: 1}
      ],
      selectLabels:[],
      open_release_info_loudong:0,//是否开启选择小区
    }
  },
  onLoad(options) {
    uni.showLoading({
      title: '加载中',
      mask: true,
    })
    // #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
    uni.hideShareMenu()
    // #endif
    this.parentid = options.parentid || 1
    this.catid = options.catid || 1
    this.publicParams.parentid = options.parentid
    if (this.catid == 5) {
      this.publicParams.mianji_type = 1
      this.localKey = "tudizhuanrang"
    }
    // 写字楼添加日租金
    if ((this.catid ==2&&this.parentid==2) ||(this.catid ==2&&this.parentid==3) ){
      this.zujinTypes[2]={value: 2, name: '日租金', unit: '元/m²/天'}
    } 
    this.getWxConfig(
        ['chooseWXPay', 'hideOptionMenu'],
        wx => {
        console.log('执行回调')
        this.wx = wx
        },
        1
    )
    switch (parseInt(this.parentid)) {
      case 1:
        if (this.catid ==1){
          this.localKey ="shangpuchushou"
        }else if (this.catid==2){
          this.localKey ="xiezilouchushou"
        }else if (this.catid==3){
          this.localKey ="changfangchushou"
        }else if (this.catid==4){
          this.localKey ="chekuchushou"
        }
        
        break;
      case 2:
        if (this.catid ==1){
          this.localKey ="shangpuchuzu"
        }else if (this.catid==2){
          this.localKey ="xiezilouchuzu"
        }else if (this.catid==3){
          this.localKey ="changfangchuzu"
        }else if (this.catid==4){
          this.localKey ="chekuchuzu"
        }
        
        break;
      case 3:
        this.localKey ="shengyizhuanrang"
        break;
      default:
        break;
    }
    var draft =uni.getStorageSync(this.localKey)
    if (draft ){
      draft =JSON.parse(draft)
      uni.showModal({
        title: '',
        content: '您有上次未发布完成的信息 是否继续编辑?',
        success:  (res)=> {
          if (res.confirm) {
            // console.log('用户点击确定');
            this.editDraft =true
            this.tempParams=draft
            this.getPageInfo(this.catid)
          } else if (res.cancel) {
            console.log('用户点击取消');
            this.getPageInfo(this.catid)
            this.removeDraft()
          }
        }
      });
    }else {
      this.getPageInfo(this.catid)
    }

    uni.$on('transmit', (e) => {
      this.params.cover_path = e.cover_path || e.imgList[0] || ''
      this.params.videos = e.videos.join(',')
      this.params.imgs1 = e.imgList1.join(',')
      this.params.imgs2 = e.imgList2.join(',')
      this.params.imgs = e.imgList.join(',')
    })
    uni.$on('transmitDesc', (e) => {
      this.params.content = e.content
      this.params.owner_think = e.owner_think
      this.params.service_introduce = e.service_introduce
    })
    uni.$on('transmitVerification', (e) => {
      this.params.verification_code = e.verification_code
      this.params.verification_qrcode = e.verification_qrcode
    })
    uni.$on('getDataAgain', () => {
      this.getPageInfo(this.catid)
    })
  },
  computed: {
    ...mapState(['allowOpen', 'use_middle_call_house', 'if_info_verification_code']),
    icon_bg(){
      return config.imgDomain+"/fabu/<EMAIL>"
    },
    zujin_unit(){
      let unit_value = 0
      if (this.params.zujin_type) {
        unit_value = this.params.zujin_type
      }
      let current_unit = this.zujinTypes.find(f=> f.value == unit_value)
      if (current_unit) {
        return current_unit.unit
      } else {
        return '元/月'
      }
    }
  },
  filters:{
    filterBg(val){
      switch (val) {
        case 'pay_release':
          return config.imgDomain+'/fabu/<EMAIL>'
          break;
        case 'upgrade_release':
          return config.imgDomain+'/fabu/<EMAIL>'
          break;
        case 'check_release':
          return config.imgDomain+'/fabu/<EMAIL>'
          break;
        default:
          break;
      }
    }
  },
  onShow() {
    if (uni.getStorageSync('smallArea')) {
      let smallArea = JSON.parse(uni.getStorageSync('smallArea'))
      this.params.buildid = smallArea.id
      this.community_name = smallArea.community_name || smallArea.name
      this.params.areaid = smallArea.areaid
      this.area_name = this.getArea(this.area_list, smallArea.areaid).areaname
      // this.params.areaid = smallArea.areaid || ''
      this.params.address = smallArea.address || ''
      this.params.lat = smallArea.lat
      this.params.lng = smallArea.lng
    }
    uni.removeStorageSync('smallArea')
  },
  onUnload() {
    uni.$off('transmit')
    uni.$off('getDataAgain')
    uni.$off('transmitVerification')
    if(!this.isSubmit){
      this.params.cname =this.community_name
      // this.params.land_certificate =this.form.land_certificate
      // this.params.owner_certificate =this.form.owner_certificate
      for (let key in this.params) {
        if (key!="catid"&&key != "parentid"&&key!="floor_type" &&key!="contact_who" &&key !="mianji_type"&&key!="zujin_type"&&key!="release_type" &&((typeof this.params[key]=="object"&&this.params[key].length>0)||(typeof this.params[key]!="object"&&this.params[key]))){
          uni.setStorageSync(this.localKey, JSON.stringify(this.params))
        }
      }
    }
  },
  methods: {
    ...mapMutations(['setAllowOpen']),
    getPageInfo(catid, parentid) {
      this.$ajax.get('estateRelease/getReleaseFieldValue', { parentid: parentid || this.parentid, catid }, (res) => {
        uni.hideLoading()
        if (res.data.code === -13) {
          this.upgrade_type = 'add'
          // 提示扣除金币
          showModal({
            content: res.data.msg,
            confirm: () => {
              console.log('执行升级个人vip')
              this.upgrade(catid)
            },
            cancel: () => {
              this.$navigateBack()
            },
          })
          return
        }
        if (res.data.code == 1) {
          let memberVip =res.data.memberIsVip
          if (memberVip.length>0){
            let item ={}
            for (var i = 0; i < memberVip.length; i++) {
                if (memberVip[i].type  === 'upgrade_release') {
                  item =memberVip[i]
                  memberVip.splice(i, 1);
                  break;
                }
            }
            memberVip.unshift(item);
            this.memberVip =memberVip
          }
          this.release_need_select_community = res.data.release_need_select_community
          this.open_info_internal_no = res.data.open_info_internal_no
          this.levelid = res.data.levelid || 2
          this.upgrade_tip = res.data.tips
          this.cates_list = res.data.cates
          // console.log(res.data.cates.find((f) => (f.id = catid)).title)
          this.catesName = res.data.cates.find((f) => f.id == catid).title
          this.labels = res.data.labels
          if (res.data.groupLabels) {
            this.groupLabels = res.data.groupLabels
          }
          this.labels.map((m) => (m.value = m.id))
          this.activeTime = res.data.activeTime
          this.youxiaoqiHasMoney = this.activeTime.some(item=>parseFloat(item.money))
          this.freeActiveTime = res.data.freeActiveTime
          this.$nextTick(() => {
            this.area_list = res.data.area
          })
          this.form = {
            catid: {
              title: '类别',
              type: 'select',
            },
            areaid: {
              title: '区域',
              type: 'select',
            },
            mianji: {
              title: this.catid == 5?'面积':'建筑面积',
              type: 'number',
            },
            title: {
              title: '标题',
              type: 'text',
            },
            content: {
              title: '描述',
              type: 'text',
            },
            contact_who: {
              title: '联系人',
              type: 'text'
            },
            activetime_id: {
              title: '有效期',
              type: 'select'
            }
          }
          switch (this.parentid) {
            case '1':
              this.form.fangjia = { type: 'number', title: '售价' }
              break
            case '2':
              this.form.zujin = { type: 'number', title: '租金' }
              break
            case '3':
              this.form.zujin = { type: 'number', title: '租金' }
              break
            default:
              break
          }
          this.params = {}
          if (this.if_info_verification_code) {
            this.$set(this.params, 'verification_code', '')
            this.$set(this.params, 'verification_qrcode', '')
          }
          this.community_name = ''
          this.area_name = ''
          this.tradeName = ''
          for (let key in this.publicParams) {
            this.$set(this.params, key, this.publicParams[key])
          }
          this.$set(this.params, 'catid', catid)
          this.otherForm_list = []
          res.data.list.forEach((e) => {
            if (this.editDraft){
              if (typeof this.tempParams.wnsb =="string"){
                this.tempParams.wnsb= this.tempParams.wnsb.split(',')
              }
              if (typeof this.tempParams.consumer_ids =="string"){
                this.tempParams.consumer_ids= this.tempParams.consumer_ids.split(',')
              }
              if (typeof this.tempParams.can_register =="string"){
                this.tempParams.can_register= this.tempParams.can_register.split(',')
              }
              if (typeof this.tempParams.can_divisible =="string"){
                this.tempParams.can_divisible= this.tempParams.can_divisible.split(',')
              }
              Object.keys(this.tempParams).map(key=>{
                if (e.identifier ==key ){
                  e.value = [this.tempParams[key]].flat(Infinity).map(item=>Number(item))
                }
              })
            }
            e.key = e.identifier + new Date().getTime()
            this.$set(this.params, e.identifier, '')
            if (e.identifier == 'trade_id') {
              this.$nextTick(() => {
                this.trade_list = e.rules
              })
            }
            if (e.identifier == 'floor_type') {
              this.form.louceng = { type: 'number', title: '总楼层' }
              this.$set(this.params, 'floor_type', 1)
              this.$set(this.params, 'szlc', '')
              this.$set(this.params, 'szlc2', '')
              this.$set(this.params, 'louceng', '')
            }
            if (!e.required) {
              this.otherForm_list.push(e)
            } else {
              if (e.type == 'select') {
                this.form[e.identifier] = {
                  type: 'picker',
                  title: e.title,
                  value: [0],
                  options: [
                    {
                      label: e.title,
                      value: e.identifier == 'floor_type' ? [0] : [],
                      required: e.required == 1 ? true : false,
                      range: [
                        {
                          identifier: e.identifier,
                          title: '',
                          rules: e.rules,
                        },
                      ],
                    },
                  ],
                }
              } else {
                this.form[e.identifier] = e
              }
            }
            if (this.editDraft){
              if (['type_id', 'zhuangxiu', 'floor_type', 'land_source'].includes(e.identifier)) {
              let _index = this.form[e.identifier].options[0].range[0].rules.findIndex(item=>item.value==e.value[0])
              if (_index >= 0) {
                this.form[e.identifier].options[0].value = [_index]
              }
            }

            if (e.identifier == 'trade_id') {
              e.rules.forEach(rule => {
                if (rule.value == e.value[0]) {
                  this.tradeName = rule.name
                }
                if (rule.children.length > 0) {
                  rule.children.forEach(item => {
                    if (item.value == e.value[0]) {
                      this.tradeName = item.name
                    }
                  })
                }
              })
            }
            }
          })
          if (res.data.packagesInfoTops && res.data.packagesInfoTops.length > 0) {
            this.extension_ding = res.data.packagesInfoTops
            this.use_meal_ding = true
          } else {
            this.extension_ding = res.data.tops
          }
          if (res.data.packagesInfoSelecteds && res.data.packagesInfoSelecteds.length > 0) {
            this.extension_jing = res.data.packagesInfoSelecteds
            this.use_meal_jing = true
          } else {
            this.extension_jing = res.data.jingxuan
          }
          this.freeSelected = res.data.freeSelected
          this.params.contact_who = res.data.contact_who
          this.params.release_type = 'upgrade_release'
          if (this.catid == 5) {
            this.params.parentid = 1
          }
          if(this.editDraft) {
            this.area_name =this.tempParams.areaid?res.data.arealist.find(f => f.areaid == this.tempParams.areaid).areaname:''
            this.community_name = this.tempParams.cname
            if (typeof this.tempParams.can_register =="object"){
                this.tempParams.can_register= this.tempParams.can_register.join(',')
              }
              if (typeof this.tempParams.can_divisible =="object"){
                this.tempParams.can_divisible= this.tempParams.can_divisible.join(',')
              }
            for (const key in this.tempParams) {
              if (key!=="cname"){
                this.params[key] = this.tempParams[key]
              }
              if (key == 'label') {
                if (typeof this.tempParams[key]=="string"){
                  this.tempParams[key] =this.tempParams[key].split(',')
                }
                this.selectLabels = this.tempParams[key].map(Number)
              }
              
              // if (Object.hasOwnProperty.call(this.tempParams, key)) {
              //   const element = this.tempParams[key];
                
              // }
            }
            // this.params = Object.assign({},this.tempParams)
          }
          this.$nextTick(() => {
            this.$forceUpdate()
          })
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    showCatPopup() {
      this.$refs.cates_popup.show()
    },
    selectCates(item) {
      uni.redirectTo({
        url: `/commercial/add?parentid=${this.parentid}&catid=${item.id}`
      });
      // uni.showLoading({
      //   title: '加载中',
      // })
      // this.params.catid = item.id
      // this.catesName = item.title
      // this.getPageInfo(item.id)
      // this.$refs.cates_popup.hide()
    },
    toSearchCommunity() {
      this.$navigateTo('/user/search_areas')
      uni.$once('newCommunity', (data) => {
        this.community_name = data.name
        this.params.buildid = data.id
        this.params.areaid = data.areaid
        this.area_name = this.getArea(this.area_list, data.areaid)
      })
    },
    onPickerChange(e) {
      e.forEach((item) => {
        item.forEach((el) => {
          this.params[el.identifier] = el.value
          if (el.identifier == 'zhuangxiu') {
            this.current_zhuangxiuname = el.name
          }
        })
      })
    },
    showAreaPopup() {
      this.$refs.area_popup.show()
    },
    onAreaChange(e) {
      this.area_name = e[e.length - 1].name
      this.params.areaid = e[e.length - 1].value
      this.$refs.area_popup.hide()
    },
    addPhotos() {
      let photos = {
        imgList1: this.params.imgs1.length > 0 ? this.params.imgs1.split(',') : [],
        videos: this.params.videos.length > 0 ? this.params.videos.split(',') : [],
        imgList2: this.params.imgs2.length > 0 ? this.params.imgs2.split(',') : [],
        imgList: this.params.imgs.length > 0 ? this.params.imgs.split(',') : [],
        cover_path: this.params.cover_path,
      }
      if (this.levelid > 1) {
        photos.cover_path = this.params.cover_path
      }
      this.$store.state.photos = photos
      this.$navigateTo(`/commercial/upload?cid=${this.params.buildid || ''}&catid=${this.params.catid}`)
    },
    getArea(arr, id) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].areaid == id) {
          return arr[i]
          break
        }
        if (arr[i].children && arr[i].children.length > 0) {
          let res = this.getArea(arr[i].children, id)
          if (res) {
            return res
          }
        }
      }
    },
    handelCheckbox(e) {
      uni.hideKeyboard()
      let _index = this.otherForm_list.findIndex(f => f.identifier == e._name)
      if (_index >= 0) {
        this.otherForm_list[_index].value = e.value
      }
      this.params[e._name] = e.value.join(',')
    },
    toAddDesc() {
      setTimeout(() => {
        uni.$emit('giveDesc', {
          content: this.params.content,
          owner_think: this.params.owner_think,
          service_introduce: this.params.service_introduce,
        })
      }, 200)
      this.$navigateTo(`/user/add/add_desc?levelid=${this.levelid}&parentid=${this.parentid}`)
    },
    addVerification() {
      setTimeout(() => {
        uni.$emit('giveVerification', {
          verification_code: this.params.verification_code,
          verification_qrcode: this.params.verification_qrcode,
        })
      }, 200)
      this.$navigateTo(`/user/add_verification`)
    },
    showTradePopup() {
      this.$refs.trade_popup.show()
    },
    onTradeChange(item) {
      let select = item[item.length - 1]
      this.params.trade_id = select.value
      this.tradeName = select.name
      this.$refs.trade_popup.hide()
    },
    pickerChange(e) {
      uni.hideKeyboard()
      this.params[e._name] = e.value
    },
    radioChange(e) {
      uni.hideKeyboard()
      this.params[e._name] = e.detail.value
      if (e._name == 'parentid') {
        this.params.label = ''
        this.labels = []
        this.$nextTick(()=>{
          this.labels = this.groupLabels[e.detail.value]
          if (this.labels) {
            this.labels.map((m) => (m.value = m.id))
          }
        })
      }
    },
    handelCheckLabel(e) {
      this.params.label = e.value.join(',')
    },
    onClickTime(item) {
      this.params.activetime_id === item.id ? (this.params.activetime_id = '') : (this.params.activetime_id = item.id)
    },
    onClickTop(ding_item) {
      this.params.tops_id === ding_item.id ? (this.params.tops_id = '') : (this.params.tops_id = ding_item.id)
    },
    onClickFreeJingxuan() {
      (this.params.freeSelected ? (this.params.freeSelected = '') : (this.params.freeSelected = 1)) &&
        (this.params.jingxuan_id = '')
    },
    onClickJingxuan(jing_item) {
      (this.params.jingxuan_id === jing_item.id
        ? (this.params.jingxuan_id = '')
        : (this.params.jingxuan_id = jing_item.id)) && (this.params.freeSelected = '')
      this.$forceUpdate()
    },
    // 执行升级个人永久vip
    upgrade(catid) {
      this.$ajax.post('member/upGradeVip', {}, (res) => {
        if (res.data.code === 1) {
          // 升级成功重新执行之前的接口
          if (this.upgrade_type === 'add') {
            this.getPageInfo(catid)
          }
          if (this.upgrade_type === 'push') {
            this.subData()
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    subData() {
      if (
        (this.params.floor_type == 1 && !this.params.szlc) ||
        (this.params.floor_type == 2 && (!this.params.szlc || !this.params.szlc2))
      ) {
        uni.showToast({
          title: `请填写所在楼层`,
          icon: 'none',
        })
        return
      }
      try {
        Object.keys(this.form).forEach((key) => {
          if (!this.params[key]&&key!="fangjia" &&key!="zujin") {
            uni.showToast({
              title: `请${this.inputType.includes(this.form[key].type) ? '填写' : '选择'}${this.form[key].title}`,
              icon: 'none',
            })
            throw new Error()
          }
        })
      } catch (e) {
        return false
      }
      if (
        (this.params.floor_type == 1 && Number(this.params.szlc)>Number(this.params.louceng)) ||
        (this.params.floor_type == 2 && (Number(this.params.szlc)>Number(this.params.louceng) || Number(this.params.szlc2)>Number(this.params.louceng)))
      ) {
        uni.showToast({
          title: `所在楼层不能大于总层数`,
          icon: 'none',
        })
        return
      }
      if (this.params.business_status == 1 && !this.params.trade_id) {
        uni.showToast({
          title: `请选择经营行业`,
          icon: 'none',
        })
        return
      }
      if (this.params.title.length > 50) {
        uni.showToast({
          title: '标题最多为50个字',
          icon: 'none'
        })
        return
      }
      if (this.params.contact_who.length > 4) {
        uni.showToast({
          title: '联系人最多为4个字',
          icon: 'none'
        })
        return
      }
      if (this.params.content.length > 1000) {
        uni.showToast({
          title: '描述最多1000为个字',
          icon: 'none'
        })
        return
      }
      if (this.memberVip.length==0){
        this.params.release_type =''
      }
      if ((this.params.jingxuan_id||this.params.tops_id )&&this.params.release_type=="check_release"){
        uni.showToast({
          title: '含有付费项目不能选择委托发布 请重新选择 ',
          icon: 'none'
        })
        return
      }
      if (!this.on_ready) {
        uni.showToast({
          title: '请阅读并接受发布规则',
          icon: 'none',
        })
        return
      }
      if((this.parentid == '2' || this.parentid == '3' || this.params.parentid == '2' || this.params.parentid == '3')&&!this.params.zujin){
        this.params.zujin =0
      }
      if(((this.catid != '5' && this.parentid == '1') || this.params.parentid == '1')&&!this.params.fangjia){
        this.params.fangjia =0
      }
      if (this.parentid) {
        this.checkRelease()
      }
    },
    checkRelease() {
      uni.showLoading({
        title: '正在发布',
        mask: true,
      })
      let params = Object.assign({}, this.params)
      if (this.use_meal_ding) {
        params.package_tops_id = params.tops_id
        delete params.tops_id
      }
      if (this.use_meal_jing) {
        params.package_jingxuan_id = params.jingxuan_id
        delete params.jingxuan_id
      }
      this.$ajax.post('estateRelease/checkRelease', params, (res) => {
        if (res.data.code !== 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
          return
        }
        if (res.data.outNumber>0){
          uni.hideLoading()
          this.show_model =true,
          this.modelContent =res.data.msg
          this.modelData =res.data
          return 
        }
        if (res.data.is_release) {
          this.releaseInfo(res.data.info_id)
        } else {
          this.info_id = res.data.info_id
          uni.hideLoading()
          // 提示需要扣除金币或支付
          // this.modelContent = res.data.msg
          // this.show_model = true
          // this.modelData = res.data
          showModal({
            content: res.data.msg,
            confirm: () => {
              // pay_status === 1 代表用户金币足够支付，否则调起支付
              if (res.data.pay_status === 1) {
                this.userReleaseByCorn(res.data.info_id)
              } else {
                this.handlePay(res.data.info_id)
              }
            },
          })
        }
      })
    },
    // 不需要支付时执行的发布接口
    releaseInfo(info_id) {
      this.$ajax.post('estateRelease/releaseInfo',  {info_id} , (res) => {
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg,
            mask: true,
          })
          this.removeDraft()
          setTimeout(() => {
            this.$navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    // 执行扣除金币支付接口
    userReleaseByCorn(info_id) {
      this.$ajax.post('estateRelease/userReleaseByCorn', { info_id }, (res) => {
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg,
            mask: true,
          })
          this.removeDraft()
          setTimeout(() => {
            this.$navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    handlePay(info_id) {
      uni.hideLoading()
      this.wxPay(info_id)
    },
    wxPay(info_id){
      this.$ajax.post('estateRelease/userReleaseByWxPay',{info_id},res=>{
        if(res.data.code === 1){
          let pay_info = res.data.data
          this.wx.chooseWXPay({
            // provider: 'wxpay',
            timestamp:pay_info.timeStamp,
            nonceStr:pay_info.nonceStr,
            package:pay_info.package,
            signType:pay_info.signType,
            paySign:pay_info.paySign,
            success: res => {
                uni.showToast({
                    title: '支付成功'
                })
                this.removeDraft()
                setTimeout(() => {
                    uni.navigateBack()
                }, 1500)
            },
            fail: function(err) {
                console.log('支付失败：', err)
                uni.showToast({
                    title: err.err_desc || err.errMsg,
                    icon: 'none',
                    duration: 5000
                })
            }
          })
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    },
    confirm(){
      this.show_model =false
      if (this.modelData.is_release) {
        uni.showLoading({
          title: '正在发布',
          mask: true
        })
        this.releaseInfo(this.modelData.info_id)
      } else {
        // this.info_id = this.modelData.info_id
        if(this.modelData.pay_status===1){
            uni.showLoading({
              title: '正在发布',
              mask: true
            })
            this.userReleaseByCorn(this.modelData.info_id)
          }else{
            this.handlePay(this.modelData.info_id)
          }
      }
    },
    handleConcatInput(e){
      let text= e.match(/[a-zA-Z0-9_\u4e00-\u9fa5]{0,4}/g)[0]
      this.$nextTick(()=>{
        this.$set(this.params,"contact_who" ,text)
      })
    },
    cancel(){
      this.show_model =false
      this.$navigateTo('/commercial/manage_info?parentid='+this.parentid)
    },
    changeMemberVip(type){
      this.currentType =type
      this.params.release_type =type
    },
    removeDraft(){
      this.isSubmit=true
      uni.removeStorageSync(this.localKey);
    }
  },
}
</script>

<style lang="scss" scoped>
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}
.multiple_input {
  padding: 24rpx 0;
  > .label {
    line-height: 1;
    font-size: 22rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #666;
  }
  .input-item {
    color: #666;
    align-items: center;
    ~ .input-item {
      margin-left: 48rpx;
    }
    .label {
      min-width: 80rpx;
      margin-right: 24rpx;
    }
    input {
      flex: 1;
    }
  }
}

.upload-box {
  padding: 24rpx 0;
  align-items: center;
  .upload_btn {
    width: 128rpx;
    height: 128rpx;
    text-align: center;
    justify-content: center;
    background: #f2f2f2;
  }
  .cover_img {
    width: 128rpx;
    height: 128rpx;
  }
  .upload_tip {
    margin-left: 24rpx;
    margin-right: 6rpx;
    justify-content: center;
    .tip_title {
      font-size: 36rpx;
      color: #666;
      margin-bottom: 16rpx;
    }
    .tip_content {
      font-size: 22rpx;
      color: #999;
    }
  }
}
.block {
  margin-bottom: 20rpx;
  padding: 0 48rpx;
  background-color: #fff;
  input {
    color: #333;
  }
}
.open_menu {
  padding: 24rpx 0;
  justify-content: space-between;
  align-items: center;
  .tip {
    color: #999;
  }
  .ding {
    display: inline-block;
    margin-left: 20rpx;
    height: 36rpx;
    width: 36rpx;
    line-height: 36rpx;
    text-align: center;
    font-size: 22rpx;
    border-radius: 4rpx;
    background-color: $uni-color-primary;
    color: #fff;
  }
  .jing {
    display: inline-block;
    margin-left: 20rpx;
    height: 36rpx;
    width: 36rpx;
    line-height: 36rpx;
    text-align: center;
    font-size: 22rpx;
    border-radius: 4rpx;
    background-color: #00caa7;
    color: #fff;
  }
}
.more_info_box {
  height: 0;
  overflow: hidden;
  &.open {
    height: auto;
  }
  .types {
    justify-content: center;
    margin-bottom: 24rpx;
    .type-item {
      padding: 10rpx 24rpx;
      position: relative;
      font-size: 32rpx;
      &.active {
        color: $uni-color-primary;
      }
      &.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 35%;
        right: 35%;
        height: 8rpx;
        border-radius: 4rpx;
        background-color: $uni-color-primary;
      }
    }
  }
  .tip {
    padding-bottom: 20rpx;
    font-size: 24rpx;
    text-align: center;
    color: #999;
  }
}

.options-box {
  flex-wrap: wrap;
  justify-content: space-between;
  .options {
    width: 31%;
    border: 1rpx solid #d8d8d8;
    border-radius: 8rpx;
    padding: 24rpx 6rpx;
    margin-bottom: 24rpx;
    position: relative;
    color: #666;
    text-align: center;
    overflow: hidden;
    &.vacancy {
      height: 0;
      padding: 0;
      margin: 0;
      border: 0;
    }
    &.active {
      background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
      border-color: $uni-color-primary;
      .title {
        color: #000;
      }
      .price {
        color: #000;
      }
      .tip {
        background-color: $uni-color-primary;
      }
    }
    .title {
      text-align: center;
      margin-bottom: 10rpx;
    }
    .price {
      display: block;
      text-align: center;
      font-size: 50rpx;
      margin-bottom: 32rpx;
      .unit {
        font-size: 22rpx;
      }
    }
    .tip {
      font-size: 22rpx;
      position: absolute;
      bottom: 0;
      width: 100%;
      left: 0;
      padding: 6rpx 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      background-color: #d8d8d8;
      color: #fff;
    }
  }
}

.rule {
  padding: 10rpx 48rpx;
  font-size: 22rpx;
  align-items: center;
  checkbox {
    transform: scale(0.7);
  }
  .rule_link {
    color: #666;
  }
}

.btn-box {
  padding: 48rpx;
  .btn {
    height: 88rpx;
    line-height: 88rpx;
    background: #fb656a;
    box-shadow: 0 8rpx 32rpx 0 rgba(251, 101, 106, 0.4);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    text-align: center;
  }
}
.money_tip {
  padding: 18rpx 48rpx;
  font-size: 26rpx;
  flex-direction: row;
  align-items: center;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.15);
  color: $uni-color-primary;
  .text {
    margin-left: 10rpx;
    word-wrap: normal;
  }
  .btn {
    margin-left: 16rpx;
    padding: 6rpx 16rpx;
    border: 1rpx solid $uni-color-primary;
    font-size: 24rpx;
    border-radius: 8rpx;
  }
}

.select_btn {
  padding: 24rpx 0;
  justify-content: space-between;
  align-items: center;
  .label {
    margin-bottom: 24rpx;
    font-size: 24rpx;
    color: #666;
  }
  .value {
    font-size: 36rpx;
    color: #999;
    &.has_value {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

.template_title {
  font-size: 22rpx;
  color: $uni-color-primary;
}

.title_list {
  max-height: 70vh;
  box-sizing: border-box;
  padding: 24rpx 48rpx;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  background-color: #fff;
  .title_item {
    display: block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    // white-space: nowrap;
    padding: 20rpx 0;
    &.active {
      color: $uni-color-primary;
    }
  }
}
.cates_box {
  background: #fff;
  .title {
    padding: 20rpx;
    text-align: center;
    border-top: 4rpx solid #ff656b;
    background: linear-gradient(0deg, hsla(0, 0%, 96.5%, 0), rgba(251, 101, 106, 0.1));
  }
  .cate {
    padding: 20rpx;
    max-height: 30vh;
    overflow-y: scroll;
    transition: 0.26s;
    box-sizing: border-box;
    .item {
      width: 100%;
      text-align: center;
      padding: 20rpx 10rpx;
    }
  }
}

.szlc {
  width: 100%;
  .szlc-val {
    display: flex;
    flex-direction: row;
    align-items: center;
    view {
      flex: 1;
      display: flex;
      flex-direction: row;
      align-items: center;
      input {
        flex: 1;
      }
      text {
        font-size: 32rpx;
        text-align: right;
      }
    }
    & > text {
      font-size: 32rpx;
      padding: 0 30rpx;
    }
  }
}
.model_content uni-view {
  display: inline;
}
.tips_box{
  justify-content: space-between;
  padding: 24rpx 48rpx;
  flex-wrap: wrap;
  align-items: center;
  background: #fff;
  .tips_box_item{
    position: relative;
    flex: 0 0 48%;
    margin-bottom: 24rpx;
    padding: 24rpx;
    border: 2rpx solid #E5E5E5;
    border-radius: 8rpx;
    
    .title_box{
      align-items: center;
      .tips_box_title{
        font-size: 28rpx;
        font-weight: 600;
        margin-right: 12rpx;
        color: #333333;
      }
      .icon{
        width:28rpx;
        height: 28rpx;
        overflow: hidden;
        image{
          width: 100%;
        }
      }
    }
    .money_box{
      font-size: 22rpx;
      margin: 12rpx 0;
      color: #333333;
    }
    .tip_con{
      font-size: 22rpx;
      color: #999999;
    }
    .tip_box_bg{
      position: absolute;
      right: 10rpx;
      bottom: 10rpx;
      width: 88rpx;
      height: 88rpx;
      overflow: hidden;
      image{
        width: 100%;
      }
    }
    .sanjiao{
      width: 0;
      height: 0;
      position: absolute;
      top: -40rpx;
      right:-40rpx;
      border: 40rpx solid transparent;
      border-top: 40rpx solid#fb656a;
      transform-origin:center;
      transform:  rotate(225deg);
    }
  .tuijian {
    position: absolute;
    top: 3rpx;
    right: 6rpx;
    font-size: 20rpx;
    color: #FFFFFF;
  }
    &.active{
      border: 2rpx solid #FB656A;
      box-shadow: 0 0 4rpx 0 rgba(251,101,106,0.06);
    }
  }
}
.tips {
  font-size: 20rpx;
  padding: 14rpx 24rpx;
  background-color: #ffeeef;
  color: $uni-color-primary;
}
</style>