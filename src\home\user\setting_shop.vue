<template>
<view class="page">
    <view class="head">
        <image class="company-logo" :src="shopData.shoplogo | imgUrl" mode="aspectFill"></image>
        <view class="company-name">{{shopData.shopname}}</view>
    </view>
    <my-select v-if="!shopid" :value="shopData.shoptype" @change="pickerChange" label="会员类型" :range="shoptypeList" name="shoptype"></my-select>
    <my-input maxlength="20" label="店名" :value="shopData.shopname||''" name="shopname" placeholder="请输入您的店名或公司名" @input="handleInput"></my-input>
    <my-input maxlength="32" label="地址" :value="shopData.shopaddr||''" name="shopaddr" placeholder="门店地址或服务区域" @input="handleInput"></my-input>
    <my-input maxlength="11" type="number" label="电话" :value="shopData.shoptel||''" name="shoptel" placeholder="请输入联系电话" @input="handleInput"></my-input>
    <my-input maxlength="30" label="优惠信息" :value="shopData.promotion||''" name="promotion" placeholder="到店礼品或优惠活动不超过30字" @input="handleInput"></my-input>
    <view class="textarea-row bottom-line top-20">
        <view class="block-title bottom-line">店铺介绍:</view>
        <textarea :value="shopData.shopmemo||''" placeholder="请输入店铺介绍、主营产品或业务范围" @input="inputContent" name="shopmemo" />
    </view>
    <view class="top-20 block-title bottom-line">上传店铺LOGO:</view>
    <view class="upload-box">
        <my-upload @uploadDon="uploadDon" action="memberShop/uploadFileByWx" :imgs="shopData.shoplogo?[shopData.shoplogo]:[]" :maxCount="1" :chooseType="1" :clearImg="false"></my-upload>
    </view>
    <view class="btn-box">
        <button class="default" @click="subData()">提交修改</button>
    </view>
</view>
</template>

<script>
import myInput from '../../components/form/myInput'
import mySelect from '../../components/form/mySelect'
import myTextarea from '../../components/form/myTextarea'
import myUpload from '../../components/form/myUpload'
import {formatImg} from '../../common/index'
export default {
    data() {
        return {
            shoptypeList:[{value:0,name:""}],
            shopid:"",
            shopData: {
                shoptype:"",
                shopname: "",
                shopaddr: "",
                shoptel: "",
                promotion: "",
                shopmemo: "",
                shoplogo: ""
            }
        }
    },
    components: {
        myInput,
        myTextarea,
        myUpload,
        mySelect
    },
    onLoad(options) {
        if(options.id){
            this.shopid=options.id
            this.getData()
        }else{
            this.getApply()
        }
    },
    filters: {
        imgUrl(val){
            return formatImg(val,'w_120')
        }
    },
    methods: {
        getData() {
            this.$ajax.get('memberShop/shopSetting.html', {shopid:this.shopid}, res => {
                if (res.data.code == 1) {
                    this.shopData = res.data.shop
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            })
        },
        getApply(){
            this.$ajax.get('memberShop/openShop',{},res=>{
                if(res.data.code == 1){
                    this.shoptypeList = [...this.shoptypeList,...res.data.shoptype]
                }
            })
        },
        pickerChange(e){
            console.log(e)
            this.shopData.shoptype = e.value
        },
        handleInput(e) {
            this.shopData[e._name] = e.detail.value
        },
        inputContent(e) {
            this.shopData.shopmemo = e.detail.value
        },
        uploadDon(e) {
            if (e) {
                this.shopData.shoplogo = e.files.join(',')
            } else {
                this.shopData.shoplogo = ""
            }
        },
        subData() {
            let params = Object.assign({}, this.shopData)
            params.shoplogo = this.shopData.shoplogo
            params.id = this.shopid
            if(this.shopid){
                this.editShop(params)
            }else{
                this.applyShop(params)
            }
        },
        editShop(params){
            this.$ajax.post('memberShop/shopSetting.html', params, res => {
                if (res.data.code == 1) {
                    uni.showToast({
                        title: res.data.msg
                    })
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 1500)
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            })
        },
        applyShop(params){
            this.$ajax.post('memberShop/openShop', params, res => {
                if (res.data.code == 1) {
                    uni.showToast({
                        title: res.data.msg
                    })
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 1500)
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            })
        }
    }
}
</script>

<style lang="scss">
.head {
    width: 100%;
    height: 50vw;
    padding: 28upx;
    box-sizing: border-box;
    background-color: #26ad37;
    text-align: center;

    .company-logo {
        width: 120upx;
        height: 120upx;
        margin-top: 8vw;
        border-radius: 50%;
    }

    .company-name {
        margin-top: 20upx;
        font-size: 46upx;
        font-weight: 500;
        color: #fff;
    }
}

.upload-box {
    background-color: #fff
}

.block-title {
    font-size: 30upx;
    padding: 20upx 30upx;
    background-color: #fff
}

.textarea-row {
    background-color: #fff
}

textarea {
    padding: 20upx;
}
</style>
