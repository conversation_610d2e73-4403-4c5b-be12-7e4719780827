<template>
  <view class = "next">
    <navigator open-type="redirect"/>
  </view>
</template>

<script>
export default {
  data(){
    return{
      roomId:"",
    }
  },
  onLoad(options){
    
    if (options.roomId){
      this.roomId =  options.roomId
      this.redirect() 
    }
    
  },
  methods: {
    redirect(){
      uni.redirectTo (
        {url:"plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id="+this.roomId})
    }
  },
}
</script>

<style>
.next{
  min-height: 100%;
  width: 100%;
  background: #5B5E82;
}
</style>