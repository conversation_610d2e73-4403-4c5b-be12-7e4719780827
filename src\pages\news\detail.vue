<template>
<view class="page news-detail">
    <view class="article">
        <view class="title">{{detail.title}}</view>
        <view class="padding-20 infos flex-box" >
            <text class="time info_from">{{detail.update_time}} {{ siteName }}</text>
            <text class="from info_from" v-if="detail.from">来源：{{detail.from}}</text>
            <view class="c-right" v-if="detail.show_browse==1">
                <my-icon type="yanjing" size="24upx" color="#666"></my-icon>
                <text class="count">{{detail.click_count}}</text>
            </view>
        </view>
        <view class="padding-20 infos flex-box" >
            <text class="author info_from">{{detail.categoryname}}</text>
            <view class = "info_biaoqian" v-if="detail.label&&detail.label.length>0">
                <text class="from info_from" v-for="(item,index) in detail.label" :key = "index" >{{item}}</text>
            </view>
        </view>
        <view class="adviser flex-box" :style="{top:advTop}" v-if="(((shareUserInfo&&shareUserInfo.levelid>1)||(shareUserInfo&&shareUserInfo.id>0))&&imconsu == 1)&& show == true" >
            <!-- https://images.tengfangyun.com/wechat/qrcode/20191121/179924a7bf523ccd58135d26d197f904bb5033b4.png?x-oss-process=style/w_80 -->
            <view class="adv-img"><image mode="widthFix" :src="shareUserInfo.prelogo | imgUrl('w_240')" alt=""></image></view>
            <view class="adv-infos flex-box">
                <view class="adv-name">{{shareUserInfo.cname}}</view>
                <view class="adv-level">{{shareType==1?"置业顾问":"经纪人"}}</view>
            </view>
            <view class="adv-oper  flex-box">
                <view class="adv-ask" @click="ask">微聊</view>
                <view class="adv-tel" @click="tel">电话咨询</view>
            </view>
        </view>
        
        <view class="content contents">
            <video class="video" v-if="detail.videos" :src="detail.videos" :poster="detail.video_cover"></video>
            <!-- #ifdef H5 -->
            <view class="article-content" v-html="detail.content"></view>
            <!-- #endif -->
            <!-- #ifndef H5 -->
            <!-- <u-parse :content="detail.content" @navigate="navigate"></u-parse> -->
            <u-parse :html="detail.content" @linkpress="navigate" :tag-style="tagStyle"></u-parse>
            <!-- #endif -->
        </view>
        <!-- 免责声明 -->
        <view class="mianze">{{detail.declare}}</view>
    
        <block v-if="build">
            <view class="top-20 build-box " @click="toNewHouse()">
                <view class="build">
                    <text class="build-title">{{build.title}}</text>
                    <text class="status" :class="'status'+build.leixing">{{build.status_name}}</text>
                    <text v-for="(item,index) in build.huxing" :key="index" class="space">{{item.shi}}居({{item.mianji}}㎡)</text>
                </view>
                <view class="build-img-box">
                    <image :src="build.img | imgUrl" mode="aspectFill"></image>
                </view>
                <view class="row buid-bottom flex-box pad12">
                    
                    <text class="text address">{{build.address}}</text>
                    <text class="text price">{{build.price_type}}{{build.build_price}}{{build.price_unit}}</text>
                </view>
            </view>
            <!-- 获取优惠 -->
            <view class="coupon-box container" v-if ="build.build_discount.content">
                <image class="bg_img" :src="'/images/new_icon/quan.png' | imageFilter('w_8601')"></image>
                <view class="coupon_container flex-box">
                <text class="coupon_name">{{build.build_discount.content}}</text>
                <view class="desc">
                <view class="btn" v-if="build.build_discount.group_id" @click="$navigateTo(`/pages/groups/detail?id=${build.build_discount.group_id}`)">获取优惠</view>
                <view class="btn" v-else @click="toSubForme(3)">获取优惠</view>
                <view class="coupon_content">结束时间{{build.build_discount.endtime}}</view>
                </view>
                </view>
            </view>
            <!-- <uni-list>
                <uni-list-item title="最新看房活动，楼盘优惠信息通知我" note="免费大巴，专家陪同，名额有限" disabled :showArrow="false" thumb="/static/icon/icon-bus.png"></uni-list-item>
            </uni-list> -->
            <view class="flex-box youhui-btn">
                <!-- 没有开启虚拟号才显示拨打售楼处电话 -->
                <view class="pan btn-first flex-1 text-center" @click="handelTel()" v-if="!istelcall||build.open_adviser===1">
                    <view class="btn">
                        <!-- <my-icon type="dianhuazhengzaibohao" color="#f65354" size="18"></my-icon> -->
                        <text>{{build.open_adviser===1?'咨询置业顾问':'联系售楼处'}}</text>
                    </view>
                </view>
                <view class="pan flex-1 text-center" @click="toNewHouse()">
                    <view class="btn">
                        <!-- <my-icon type="ziyuan" color="#f65354" size="18"></my-icon> -->
                        <text>更多楼盘信息</text>
                    </view>
                </view>
            </view>
        </block>
        <block v-else>
            <uni-list class="default_active">
                <uni-list-item title="最新看房活动，楼盘优惠信息通知我" note="全城看房，专家陪同，名额有限" disabled :showArrow="false" thumb="/static/icon/icon-bus.png"></uni-list-item>
            </uni-list>
            <view class="flex-box youhui-btn">
                <view class="pan flex-1 text-center  btn-first" @click="toSubForme(3)">
                    <view class="btn">
                        <!-- <my-icon type="dianhuazhengzaibohao" color="#f65354" size="18"></my-icon> -->
                        <text>预约优惠</text>
                    </view>
                </view>
                <view class="pan flex-1 text-center" @click="toSubForme(0)">
                    <view class="btn">
                        <!-- <my-icon type="ziyuan" color="#f65354" size="18"></my-icon> -->
                        <text>团购报名</text>
                    </view>
                </view>
            </view>
        </block>
        <view class="banner-box top-20" v-if="adv.length>0">
            <block v-for="(item, idx) in adv" :key="idx">
                <view class="banner-item" @click="toLink(item.link)">
                    <banner :image="item.image" mode="widthFix" height="auto" :is_adv="item.is_show_label==1?true:false"></banner>
                </view>
            </block>
        </view>
        <view class="top-20" v-if="audit_mode==0">
            <!-- 用户点评 -->
            <view class="comment-box container" id="comment">
                <view class="label">
                    <text>用户点评</text>
                    <text class="more" @click="toCommentList">更多</text>
                </view>
                
            <comment-list-new :listData="comment_list" :banReply="true" :showMore="false" :parentId="detail.id" @clickReply="toReply" @praise='handlePraise' @delComment="delComment" type="1"></comment-list-new>
            <view class="to-comment flex-box" @click='toComment'>
                <my-icon type='ic_pinglun' color="#FB656A" size='32upx'></my-icon>
                <view class="to-comment-con">我要评论</view>
            </view>

        </view>
        </view>
        <view class="top-20 gengduo" v-if ="news_list.length>0&&!shareId&&!shareType">
            <view class="label">
                    <text>更多好文</text>
                    <!-- <text class="more" @click="toCommentList">更多</text> -->
                </view>
            <block v-for="(item, index) in news_list" :key="index">
                <newsItem :item-data="item"></newsItem>
            </block>
        </view>
    </view>
    <view class="footer flex-box">
        <view class="footer-left flex-1 flex-box">
            <!-- #ifndef MP-BAIDU -->
            <!-- <view class=" flex-box footer-left-con" @click="handleCreat()" v-if="hasWechat">
                <my-icon type="ic_fenxiang" size="40upx"></my-icon>
                <view class="text">分享</view>
            </view> -->
            <!-- #endif -->
            
            <view class="flex-box footer-left-con" @click="sharePop">
                <my-icon type="ic_fenxiang" size="40upx"></my-icon>
                <view class="text">分享</view>
            </view>
            <view class="flex-box footer-left-con" @click="toComment()" v-if="audit_mode==0">
                <my-icon type="ic_pinglun" size="40upx"></my-icon>
                <view class="text comment-pinglun">评论</view>
            </view>
        </view>
        <view class="flex-1 footer-right footer-right-left flex-box" @click="goNext">
            再看一篇
        </view>
        <view class="flex-1 footer-right footer-right-right flex-box" @click="weituo">
            {{shareId?"委托找房":'获取优惠'}}
        </view>
    </view>
    <view class="right-fixed" v-if ="!shareId">
        <view class="scroll-top" @click="scrollToTop" v-if ="showToTop">
            <my-icon type="ic_ding_" color="#999" size="42upx" ></my-icon>
        </view>
        <view class="home-fixed" @click="goHome">
            <my-icon type="ic_shouyed" color="#999" size="42upx"></my-icon>
        </view>
    </view>
    <view v-if="hb_info.is_open" class="hongbao"   @click="toHbHelp()">
        <view class="hb_content" :animation="num == 0 ? showpic : hidepic" >
          <image class ="hb_img"   src ='/static/icon/1.png'></image>
          
        </view>
        <view class="hb_content hb_content1" :animation="num == 1 ?showpic : hidepic ">
          <image class ="hb_img"   src ='/static/icon/2.png'></image>
        </view>
      </view>
    <my-popup ref="comment" position="bottom">
        <view class="comment-box">
            <view class="textarea-box bottom-line">
                <textarea :value="comment_content" fixed :show-confirm-bar="false" :placeholder="comment_placeholder" :cursor-spacing="80" @input="inputContent" />
            </view>
            <view class="btn-box">
            <button class="small plain" @click="sendComment">发表评论</button>
            </view>
        </view>
    </my-popup>

    <sub-form :groupCount="build.groupCount" :sub_type="sub_type" :sub_mode="sub_mode" :sub_title="sub_title" :sub_content="sub_content" ref='sub_form' @onsubmit="handleSubForm"></sub-form>
    <!-- #ifndef MP-WEIXIN -->
    <enturstBtn v-if="shareUserInfo.agent_id||shareUserInfo.adviser_id" :to_user="shareUserInfo" @click="$refs.enturst_popup.show()" />
    <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false" @show="weituo_is_show=true" @hide="weituo_is_show=false">
        <enturstBox ref="enturst_box" @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="showLoginPopup('为方便您及时接收消息通知，请输入手机号码')" :to_user="shareUserInfo" />
    </my-popup>
    <!-- 登录弹窗 -->
    <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip" :login_success_tip="!weituo_is_show" @success="onLoginSuccess"></login-popup>
    <!-- #endif -->
    <hongbao v-if="hb_result" ref="hongbao" :money="hb_info.hb_money" :expire_seconds="hb_result.expire_seconds" @openHb="openHb"></hongbao>
    <share-pop ref="show_share_pop" @copyLink="copyLink" @appShare="appShare" @handleCreat='handleCreat' @showCopywriting='showCopywriting'></share-pop>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <chat-tip></chat-tip>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
</view>
</template>

<script>
// #ifdef H5
// #endif
// #ifndef MP-WEIXIN
import loginPopup from '../../components/loginPopup'
// #endif
import {
    formatImg,
    showModal,
    config,
    isIos
} from "../../common/index.js"
import newsItem from "../../components/newsItem.vue"
import myIcon from "../../components/myIcon.vue"
import myPopup from '../../components/myPopup.vue'
// import uParse from '../../components/u-parse/u-parse.vue'
import uParse from '../../components/Parser/index'
// import uParse from '../../components/wx-parse/parse.vue'
 import commentListNew from '../../components/commentListNew.vue'
 import checkLogin from '../../common/utils/check_login'
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
import {
    // uniIcons,
    uniList,
    uniListItem
} from '@dcloudio/uni-ui'
import banner from "../../components/banner.vue"
import getChatInfo from '../../common/get_chat_info'
import subForm from '../../components/subForm'
import allTel from '../../common/all_tel.js'
import hongbao from '@/components/hongbao'
import getLocation from '../../common/get_location'
import sharePop from '../../components/sharePop'
import shareTip from '../../components/shareTip'
export default {
    components: {
        newsItem,
        // uniIcons,
        uniList,
        uniListItem,
        myIcon,
        myPopup,
        uParse,
        commentListNew,
        banner,
        // #ifndef MP-WEIXIN
        loginPopup,
        // #endif
        subForm,
        hongbao,
        sharePop,
        shareTip,
        enturstBtn,
        enturstBox

    },
    data() {
        return {
            id: "",
            title: "",
            detail: {},
            tagStyle: {
                video: 'max-width:100%',
                a: 'color: #fb656a;text-decoration-line: underline;'
            },
            snapshot: config.ossSnapshot,
            build: "",
            news_list: [],
            comment_list: [],
            comment_content: "",
            comment_placeholder: "请输入评论内容",
            cardInfo: {
                name: config.projectName,
                today: new Date()
            },
            isComment: false,
            focus: false,
            show: false,
            text: "",
            adv: [],
            poster: "",
            shareUserInfo: {}, //分享者信息
            // ischat : 0,
            shareType: "",
            shareId: '',
            currentInfo: [], //当前用户信息
            // istelcall:this.$store.state.im.istelcall,
            type: '', //置业顾问为1   经纪人为 2
            sub_type: 0,
            sub_title: '',
            sub_content: '',
            showPopup:false,
            login_tip: '',
            show_share_tip:false,
            siteName:'',
            showToTop:false,
            link:'',
            toLogin:true,
            weituo_is_show: false, //委托弹窗是否显示
            tel_res: {},
            show_tel_pop: false,
            hb_info: {},
            hb_result: {},
            hb: '',
            task_id: '',
            hongbao_gif: config.imgDomain+'/hongbao/linghongbao.png',
            saihongbao_gif: config.imgDomain+'/hongbao/saihongbao.gif',
            isLogin: false,
            map_key: '',
            hb_share_query: '',
            num:0,
            showpic:{},
            hidepic:{},
            tuiguang_mp3:"/static/icon/voice/tuiguang_bg.mp3"

        }
    },
    onLoad(options) {
        // this.ischat =this.$store.state.im.ischat

        // 如果是扫码进来的
        // #ifdef MP
        if (options.scene) {
            const params = getSceneParams(decodeURIComponent(options.scene))
            if (params.id) {
                this.id = params.id
                this.getData(params.id)
            } else {
                uni.showToast({
                    title: "没有此文章",
                    icon: "none"
                })
            }
            return
        }
        // #endif
        // 如果是分享链接进来的
        if (options.shareId && (options.type||options.shareType)) {
            this.shareId = options.shareId
            this.shareType = options.type||options.shareType
            this.share_time =options.f_time||''
        }
        if (options.id) {
            this.id = options.id
        } else {
            uni.showToast({
                title: "没有此文章",
                icon: "none"
            })
            return
        }
        if (options.title) {
            this.detail.title = decodeURIComponent(options.title)
            document.title = this.detail.title
            // uni.setNavigationBarTitle({
            //     title: this.detail.title
            // })
        }
        if (this.shareId){
            // 获取登陆状态
            checkLogin({
                fail: (res)=>{
                    if (this.toLogin==false) return 
                    this.toLogin=false
                    if(res.status==1){
                        uni.setStorageSync('backUrl', window.location.href)
                        this.$navigateTo("/user/login/login")
                    }else {
                        this.getData(options.id)
                    }
                },
                success: (res) => {
                    this.getData(options.id)
                },
                complete: (res)=>{
                    this.$store.state.user_login_status = res.status
                }
            })
        }else {
            this.getData(options.id)
        }
        if(options.hb) this.hb = options.hb
        var  animation = uni.createAnimation(
        { timingFunction: "ease",}
    )
    animation.opacity(1).step({ duration: 1000}).translateX('-120rpx').step({ duration: 1000}); 
       //描述动画
    this.showpic = animation.export(); //输出动画
    animation.opacity(0).step({ duration: 1000 ,delay:1000}).translateX('120rpx').step({ duration: 1000,delay:1000});
    this.hidepic = animation.export();
    this.setInterval1 =setInterval(function(){
      this.num++;
      if (this.num == 2) {
        this.num = 0;
      }
      //淡入
      animation.opacity(1).step({ duration: 1000}).translateX('-120rpx').step({ duration: 1000}); 
       //描述动画
      this.showpic = animation.export(); //输出动画
      //淡出
      animation.opacity(0).step({ duration: 1000 ,delay:1000}).translateX('120rpx').step({ duration: 1000,delay:1000});
      this.hidepic = animation.export();
    }.bind(this), 4000);
        // this.loginState()
    },
    onUnload(){
        if (this.innerAudioContext){
            this.innerAudioContext.destroy()
        }
        if (this.setInterval1){
            clearInterval(this.setInterval1)
        }
    },
    computed: {
        audit_mode() {
            return this.$store.state.audit_mode
        },
        imconsu() { //是否全局开启置业顾问
            return this.$store.state.im.adviser
        },
        ischat() { //是否全局开启聊天
            return this.$store.state.im.ischat
        },
        istelcall() { //是否全局开启中间号
            return this.$store.state.im.istelcall
        },
        sub_mode() {
            return this.$store.state.sub_form_mode 
        },
        advTop(){
            // #ifdef MP 
            return this.$store.state.systemInfo.statusBarHeight
            // #endif 
             // #ifndef MP 
            return '44px'
            // #endif 
        }
    },
    filters: {
        imgUrl(img, param = "w_400") {
            if (!img) {
                return ""
            }
            return formatImg(img, param)
        },

        formatToday(nowTime) {
            let year = nowTime.getFullYear()
            let month = nowTime.getMonth() + 1 > 10 ? nowTime.getMonth() + 1 : '0' + (nowTime.getMonth() + 1)
            let day = nowTime.getDate()
            let week
            switch (nowTime.getDay()) {
                case 1:
                    week = "星期一"
                    break
                case 2:
                    week = "星期二"
                    break
                case 3:
                    week = "星期三"
                    break
                case 4:
                    week = "星期四"
                    break
                case 5:
                    week = "星期五"
                    break
                case 6:
                    week = "星期六 "
                    break
                case 0:
                    week = "星期日"
                    break
            }
            return '今天是' + year + '年' + month + '月' + day + '日' + week
        }
    },
    methods: {
        getData(id) {
            uni.showLoading({
                title: "加载中...",
                mask: true
            })
            this.$ajax.get("news/newsDetail.html", {
                id: id,
                sid: this.shareId,
                sharetype: this.shareType,
                forward_time:this.share_time ||''
            }, (res) => {
                if (res.data.code == 1) {
                    // const regex = new RegExp('<img', 'gi');
                    // 正则匹配处理富文本图片过大显示问题
                    // res.data.newDetail.content = res.data.newDetail.content.replace(regex, `<img style="max-width: 100%;"`);
                    this.detail = res.data.newDetail
                    document.title = this.detail.title
                    // uni.setNavigationBarTitle({
                    //     title: this.detail.title
                    // })
                    if (res.data.adv && res.data.adv.length > 0) {
                        this.adv = res.data.adv

                    }
                    this.$nextTick(() => {
                        let imgs = document.querySelectorAll('.content img')
                        let imgArr = []
                        let _this = this
                        for (let i = 0; i < imgs.length; i++) {
                            imgArr.push(imgs[i].src)
                            imgs[i].addEventListener('click', function () {
                                _this.preImg(this.src, imgArr)
                            })
                        }
                    })
                    this.news_list = res.data.newOther
                    this.comment_list = res.data.comment
                    this.build = res.data.build
                    this.poster = res.data.poster
                    this.siteName=res.data.siteName
                    // this.shareUserInfo=res.data.author

                    //当前判断以置业顾问优先
                    if (res.data.userData.adviserInfo) { //当前用户是不是置业顾问
                        // this.currentInfo = res.data.userData.agentInfo
                        // this.type = 2
                        this.currentInfo = res.data.userData.adviserInfo
                        this.type = 1

                    } else if (!res.data.userData.adviserInfo &&res.data.userData.agentInfo) { //当前用户是不是经纪人
                        this.currentInfo = res.data.userData.agentInfo
                        this.type = 2
                    } else {
                        this.currentInfo = []
                        this.type = ""
                    }
                    if (res.data.shareData.data) {
                        this.shareUserInfo = res.data.shareData.data
                        if ( res.data.shareData.sharetype==1){
                            this.shareUserInfo.adviser_id= this.shareUserInfo.id
                        }else if ( res.data.shareData.sharetype==2){
                            this.shareUserInfo.agent_id= this.shareUserInfo.id
                        }
                    }
                    let time =parseInt(+new Date()/1000)
                    if (this.currentInfo && this.currentInfo.id) {
                        this.share = {
                            title: this.detail.title,
                            content: res.data.seo.description,
                            link: window.location.origin+"/h5/pages/news/detail?id="+this.id+ '&isshare=1' + "&shareId=" + this.currentInfo.id + '&type=' + this.type+"&f_time="+time,
                            pic: this.poster.newshbfm
                        }
                    } else {
                        this.share = {
                            title: this.detail.title,
                            content: res.data.seo.description,
                            link: window.location.href.split('&hb=')[0],
                            pic: this.poster.newshbfm
                        }
                    }
                    // if (this.hb_share_query) {
                    //   this.share.link = this.share.link + `&${this.hb_share_query}`
                    // }
                }else {
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
                setTimeout(() => {
                    uni.hideLoading();
                    this.show = true;
                }, 200)
                this.getWxConfig()
                this.loginState()
                // if (this.shareId){
                //     // 获取登陆状态
                //     checkLogin({
                //         fail: (res)=>{
                //             if (this.toLogin==false) return 
                //             this.toLogin=false
                //             if(res.status==1){
                //                 uni.setStorageSync('backUrl', window.location.href)
                //                 this.$navigateTo("/user/login/login")
                //             }
                //         },
                //         success: (res) => {
                //             this.loginState()
                //         },
                //         complete: (res)=>{
                //             this.$store.state.user_login_status = res.status
                //         }
                //     })
                // }else {
                //     this.loginState()
                // }
            })
        },
        weituo(){
            if (this.shareId){
                this.$refs.enturst_popup.show()
            }else {
                this.toSubForme(3)
            }
        },
        formatToday(nowTime) {
            let year = nowTime.getFullYear()
            let month = nowTime.getMonth() + 1
            let day = nowTime.getDate()
            let week
            switch (nowTime.getDay()) {
                case 1:
                    week = "星期一"
                    break
                case 2:
                    week = "星期二"
                    break
                case 3:
                    week = "星期三"
                    break
                case 4:
                    week = "星期四"
                    break
                case 5:
                    week = "星期五"
                    break
                case 6:
                    week = "星期六 "
                    break
                case 0:
                    week = "星期日"
                    break
            }
            return '今天是' + year + '年' + month + '月' + day + '日' + week
        },
        
        preImg(nowImg, imgArr) {
            uni.previewImage({
                current: nowImg,
                indicator: "number",
                urls: imgArr
            })
        },
        sharePop(){
            this.getShortLink()
            this.$refs.show_share_pop.show()
        },
        getShortLink(){
            let time =parseInt(+new Date()/1000)
            if (this.currentInfo && this.currentInfo.id) { //当前用户是 置业顾问或者经纪人  
				this.link ="https://"+ window.location.host+'/h5/pages/news/detail?id=' + this.id+'&isshare=1' + "&shareId=" + this.currentInfo.id + '&type=' + this.type+"&f_time="+time
                
            }else {
                this.link ="https://"+ window.location.host+'/h5/pages/news/detail?id=' + this.id 
            }
            this.$ajax.get('build/shortUrl.html', {page_url: this.link}, res=>{
                if(res.data.code === 1){
                this.link = res.data.short_url
                }
            })
        },
        showCopywriting(){
                const text = `【我正在看】${this.detail.title}
【阅读原文】${this.link}`
                this.copyWechatNum(text, ()=>{
                    this.copy_success = true
                })
            
        },
        // #ifdef H5
        copyLink(){
            this.show_share_tip=true
        },
        // #endif
        // #ifndef H5
        copyWechatNum(cont) {
            uni.setClipboardData({
                data: cont,
                success: res => {
                // uni.showToast({
                //   title: "复制成功",
                //   icon: "none"
                // })
                }
            })
        },
        // #endif
        // #ifdef H5
        copyWechatNum(cont, callback) {
            let oInput = document.createElement('textarea')
            oInput.value = cont
            document.body.appendChild(oInput)
            oInput.style.opacity = 0
            oInput.select() // 选择对象;
            oInput.setSelectionRange(0, oInput.value.length);
            document.execCommand('Copy') // 执行浏览器复制命令
            uni.showToast({
                title: '复制成功',
                icon: 'none'
            })
            oInput.blur()
            oInput.remove()
            if(callback) callback()
        },
        // #endif
        navigate(href, e) {
            console.log(href)
            // this.$navigateTo(href)
        },
        tel(){
            this.callMiddleNumber(
                this.shareType == 1?'2':'3',
                this.shareType == 1?this.shareUserInfo.id:this.shareUserInfo.mid,
                this.shareType == 1?'2':'3',
                this.shareType == 1?this.shareUserInfo.id:this.shareUserInfo.mid,)
        },
        showLoginPopup(tip){
            this.login_tip = tip
            this.$refs.login_popup.showPopup()
        },
        handleCloseLogin() {
            if (this.$store.state.user_login_status === 1) {
                uni.removeStorageSync('token')
                this.$navigateTo('/user/login/login')
            }
            if(this.$store.state.user_login_status===2){
                this.$navigateTo('/user/bind_phone/bind_phone')
            }
        },
        onLoginSuccess(res){
            this.$store.state.user_login_status = 3
            if(this.weituo_is_show){
                console.log("登录成功后继续执行委托接口")
                this.$refs.enturst_box.handleEnturst()
            }
        },
         // 请求虚拟号接口
        callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
            this.tel_params = {
                type,
                callee_id,
                scene_type,
                scene_id,
                source,
                bid,
                success: (res)=>{
                    this.tel_res = res.data
                    this.show_tel_pop = true
                }
            }
            // #ifdef MP-WEIXIN
            allTel(this.tel_params)
            // #endif
            // #ifndef MP-WEIXIN
            this.tel_params.intercept_login = true
            this.tel_params.fail = (res)=>{
                if(res.data.code === -1){
                    this.$store.state.user_login_status = 1
                    uni.removeStorageSync('token')
                    uni.setStorageSync('backUrl', window.location.href)
                    this.$navigateTo("/user/login/login")
                }
                if(res.data.code === 2){
                    this.$store.state.user_login_status = 2
                    this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
                }
            }
            allTel(this.tel_params)
            // #endif
        },
        retrieveTel(){
            allTel(this.tel_params)
        },
        // // 统计拨打电话
        // statistics() {
        //     this.$ajax.get(
        //         'im/callUpStatistics',
        //         {
        //         id: this.shareUserInfo.id,
        //         tel: this.shareUserInfo.tel,
        //         type: 17
        //         },
        //         res => {
        //         console.log(res)
        //         }
        //     )
        // },
        handelTel() {
            if (this.build.open_adviser === 1) {
                this.$navigateTo('/pages/consultant/consuList?id=' + this.build.id)
                return
            }
            if (this.build.phone && this.build.sellmobile_part) {
                showModal({
                    title: "温馨提示",
                    content: "请拨打" + this.build.phone + "后转拨分机号" + this.build.sellmobile_part,
                    confirm: () => {
                        uni.makePhoneCall({
                            phoneNumber: this.build.phone
                        });
                    }
                })
            } else {
                uni.makePhoneCall({
                    phoneNumber: this.build.tel
                });
            }
        },
        toPosition() {
            uni.openLocation({
                latitude: parseFloat(this.build.yzhou),
                longitude: parseFloat(this.build.xzhou),
                success: function () {
                    console.log('success');
                }
            });
        },
        // 点赞评论
        handlePraise(idx){
            const index=idx.index
            const currentCommnet = this.comment_list[index]
            let type =1
            if (currentCommnet.is_praise==1){
                type =2
            }else {
                type =1
            }
            this.$ajax.get('news/commentPraise.html',{comment_id:currentCommnet.id,type},res=>{
                if(res.data.code === 1){
                uni.showToast({
                    title:res.data.msg,
                    mask:true
                })
                if(currentCommnet.is_praise){
                    this.comment_list[index].is_praise = 0
                    this.comment_list[index].praise_count--
                }else{
                    this.comment_list[index].is_praise = 1
                    this.comment_list[index].praise_count++
                }
                // this.comment_list[index].praise = res.data.praise.length
                }else{
                uni.showToast({
                    title:res.data.msg,
                    icon:'none',
                    mask:true
                })
                }
            })
        },
        // toCommunity(id, index) {
        //     this.$store.state.tempData = {
        //         title: this.comment_list[index].title,
        //         nickname: this.comment_list[index].nickname,
        //         content: this.comment_list[index].content,
        //         img: []
        //     }
        //     this.$navigateTo('/pages/community/detail?id=' + id)
        // },
        toCommentList() {
            this.$navigateTo('/pages/comment_list/comment_list?id='+this.detail.id+'&type=1')
            return;
            this.$navigateTo('/pages/new_house/comment?bid=' + this.build.id + '&open=' + this.build.open_adviser)
        },
        consuDetail() {
            if (this.shareType == 1) { //置业顾问
                this.$navigateTo('/pages/consultant/detail?id=' + this.shareUserInfo.id)
            } else if (this.shareType == 2) {
                this.$navigateTo('/pages/agent/detail?id=' + this.shareUserInfo.id)
            }
        },
        ask() {
            if (!uni.getStorageSync('token')) {
                this.$navigateTo('/user/login/login')
                // this.reload = true
                return
            }
            if (this.ischat == 0) {
                // uni.showToast({
                //     title: "当前没有开通聊天功能 请先开通",
                //     icon: "none"

                // })
                this.consuDetail()
                return
            }
            getChatInfo(this.shareType == 1 ? this.shareUserInfo.mid : this.shareUserInfo.id, 11)
        },
        toNewHouse() {
            this.$navigateTo("/pages/new_house/detail?id=" + this.build.id)
        },
        toSubForme(type) {
            let bid
            if (this.build) {
                bid = this.build.id
            } else {
                bid = 0
            }
            this.sub_type = type
            switch (type) {
                case 0:
                    this.sub_title = "团购报名"
                    this.sub_content = "为方便通知到您最新的信息，请输入您的手机号码"
                    break;
                case 3:
                    this.sub_title = "预约优惠报名"
                    this.sub_content = "为方便通知到您最新的信息，请输入您的手机号码"
                    break;
                default:
                    this.sub_title = "团购报名"
                    this.sub_content = "为方便通知到您最新的信息，请输入您的手机号码"
            }
            this.$refs.sub_form.showPopup()
        },
        handleSubForm(e) {
            e.from = "资讯页"
            let bid
            if (this.build) {
                e.bid = this.build.id
            }
            e.type = this.sub_type || ''
            this.$ajax.post('build/signUp.html', e, res => {
                uni.hideLoading()
                if (res.data.code === 1) {
                    if (this.sub_mode!==2||res.data.status === 3) { //提示报名成功
                        uni.showToast({
                            title: res.data.msg,
                            icon: "none"
                        })
                        this.$refs.sub_form.closeSub()
                    }else if(res.data.status === 1){
                        uni.removeStorageSync('token')
                        navigateTo('/user/login/login')
                    }else if(res.data.status === 2){
                        this.$refs.sub_form.getVerify()
                    }
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            })
        },
        toHome() {
            uni.switchTab({
                url: '/pages/index/index'
            })
        },
        handleCreat() {
            this.$navigateTo(`${location.origin}/wapi/poster/branch?type=4&id=${this.id}&header_from=2`)
        },
        inputContent(e) {
            this.comment_content = e.detail.value
        },
        toComment() {
            this.comment_placeholder = "请输入评论内容"
            this.$refs.comment.show()
        },
        toReply(e) {
            this.comment_placeholder = '回复' + e.be_reply
            this.$refs.comment.show()
            // console.log(e)
            if (isIos()) {
                this.isComment = true
            }
            setTimeout(() => {
                this.focus = true;
            }, 30)
        },
        handleBlur() {
            this.focus = false
            this.isComment = false
        },
        handleFocus() {
            if (isIos()) {
                this.isComment = true
            }
            setTimeout(() => {
                this.focus = true;
            }, 30)
        },
        sendComment() {
            if (!this.comment_content) {
                uni.showToast({
                    title: "请输入评论内容",
                    icon: "none"
                })
                return
            }
            this.$ajax.post('news/comment.html', {
                id: this.detail.id,
                content: this.comment_content
            }, res => {
                if (res.data.code == 1) {
                    this.comment_list.unshift(res.data.comment)
                    setTimeout(() => {
                        this.comment_content = ""
                        this.$refs.comment.hide()
                    }, 1500)
                }
                uni.showToast({
                    title: res.data.msg,
                    icon: res.data.code == 1 ? 'success' : 'none',
                    mask: true
                })
            })
        },
        delComment(e) { //删除评论
            let {
                parentIndex,
                index
            } = e
            let id
            if (index == undefined) {
                id = this.comment_list[parentIndex].id
            } else {
                id = this.comment_list[parentIndex].reply[index].id
            }
            this.$ajax.get('news/deletedComment', {
                id
            }, res => {
                if (res.data.code == 1) {
                    if (index == undefined) {
                        this.comment_list.splice(parentIndex, 1)
                    } else {
                        this.comment_list[parentIndex].reply.splice(index, 1)
                    }
                }
                uni.showToast({
                    title: res.data.msg,
                    icon: res.data.code == 1 ? 'success' : 'none'
                })
            })
        },
        doNot() {

        },
        toLink(url) {
            this.$navigateTo(url)
        },
        scrollToTop(){
            uni.pageScrollTo({scrollTop:0})
        },
        goHome(){
            uni.switchTab({
                url:"/"
            })
        },
        weituo(){
            if (this.shareId){
                this.$refs.enturst_popup.show()
            }else {
                this.toSubForme(3)
            }
        },
        goNext(){
            if (this.news_list.length>0){
                this.$navigateTo('/pages/news/detail?id='+this.news_list[0].id)
            }else {
                uni.showToast({
                    title:'没有更多了',
                    icon:"none"
                })
            }
        },
        doTask(){
            if (this.type == 2) { //经纪人任务
                this.$ajax.get("tasks/doTaskReward.html", {
                    task_id: 15,
                    mid: this.currentInfo.id
                }, res => {
                    console.log(res);
                })
            }
        },
        checkHb(){
          this.$ajax.get('WxMoney/checkHb', { info_id: this.id, info_type: 4, hb: this.hb}, res => {
           
            if (res.data.code == -1) {
              uni.setStorageSync('backUrl', window.location.href)
              this.$navigateTo("/user/login/login")
            }
             if (res.data.help_fail_desc) {
              uni.showToast({
                title: res.data.help_fail_desc,
                icon: 'none'
              })
              return 
            }
            if (res.data.code == 1) {
              this.hb_info = res.data.hb_info
               this.is_help_link = res.data.is_help_link   // 是否是助力链接进来的 如果是 （值为1）弹出红包弹框
          this.$nextTick(()=> {
            if (this.is_help_link ==1){
              // this.timeDownStart()
              this.$refs.hongbao.showPopup()    //页面不主动弹出领取弹框改为入口打开显示弹框 或者通过助力链接打开时主动弹出
            }
            
          })
            //   this.hb_share_query = res.data.hb_share_query
              this.map_key = res.data.txmapwapkey
              if (this.hb_info.is_open) {
                if (res.data.help_task && res.data.help_task.id) {
                  this.task_id = res.data.help_task.id
                } else if (this.isLogin) {
                //   this.createHb()
                }
                // if (this.hb_share_query) {
                //   this.share.link += `&${this.hb_share_query}`
                // }
                if (this.hb_info.limit_area) {
                  this.getWxConfig(['getLocation','updateAppMessageShareData','updateTimelineShareData'], (wx)=>{
                    this.wx = wx
                  })
                } else {
                  this.getWxConfig()
                }
                // if (this.hb_info.limit_area) {
                //   this.getCity()
                // }
              }
            }
          }, err => {console.log(err)}, {disableAutoHandle: true})
        },
        createHb(){
          let form = {
            info_id: this.id,
            info_type: 4,
            hb:this.hb
          }
          this.$ajax.post('WxMoney/createhb', form, res => {
            if (res.data.code == 1) {
              this.hb_result = res.data.hb_result
            //   this.$nextTick(()=> {
            //     this.timeDownStart()
            //     this.$refs.hongbao.showPopup()
            //   })
            } else if (res.data.help_fail_desc){
              uni.showToast({
                title: res.data.help_fail_desc,
                icon: 'none'
              })
            }
          })
        },
        getCity(options={}) {
          this.$store.state.getPosition(this.wx, (res)=>{
            this.lat = res.lat
            this.lng = res.lng
            getLocation({
              latitude: res.lat,
              longitude: res.lng,
              map_key: this.map_key||'',
              success: cityRes=>{
                this.current_city = cityRes.city
                options.success && options.success(res)
              },
              fail: err=>{
                console.log(err)
                options.fail && options.fail(err)
              }
            })
          })
        },
        playAudio(){
        this.innerAudioContext = uni.createInnerAudioContext();
        // this.innerAudioContext.autoplay = true;
        this.innerAudioContext.loop = false;
        this.innerAudioContext.src = this.tuiguang_mp3;
        // this.innerAudioContext.pause()
        this.innerAudioContext.onPlay(() => {
            console.log('开始播放');
            this.playing = true
        });
        this.innerAudioContext.onEnded(() => {
            console.log('播放结束');
            this.playing = false
        });
        this.innerAudioContext.onError((res) => {
            this.playing = false
            console.log("播放失败")
            console.log(res.errMsg);
            console.log(res.errCode);
        });
        this.innerAudioContext.play()
    },
        openHb() {
            if (!this.playing) this.playAudio()
            if (this.hb_info.limit_area && !this.current_city) {
            uni.showLoading({
                title: '获取位置信息中，请稍等'
            });
            this.getCity({
              success: () => {
                uni.hideLoading()
                this.getHb()
              }, fail: (err) => {
                console.log(err)
                uni.hideLoading()
                this.getHb()
              }
            })
          } else {
            this.getHb()
          }
        },
        getHb() {
            let form = {
                info_id: this.id,
                info_type: 4,
                hb:this.hb,
                area: this.current_city,
            }
            this.$ajax.post('WxMoney/help', form, (res) => {
                uni.showToast({
                    title: res.data.msg,
                    icon: 'none'
                })
                if (res.data.code == 1) {
                    this.$refs.hongbao.hidenPopup()
                    //  let  link = this.share.link.split("&hb=")[0]
                    //       this.share.link = link +`&${res.data.hb_share_query}`
                    //       this.getWxConfig()
                    //       setTimeout(() => {
                                // this.$navigateTo(`/redEnvelopes/index?info_id=${this.id}&info_type=4&task_id=${this.task_id}`)
                            // }, 500);

                    
                    } else {
                        if ( res.data.state==1){
                            this.$refs.hongbao.hidenPopup()
                        }else if (res.data.state==2) {
                            this.$refs.hongbao.hidenPopup()
                            setTimeout(() => {
                                showModal({
                                    content:res.data.msg +',您也可以参与领取红包',
                                    confirm: res => {
                                    this.$navigateTo(`/redEnvelopes/index?info_id=${this.id}&info_type=4`)
                                    }
                                })
                            }, 1000);
                            
                        }
                    // uni.showToast({
                    //   title: res.data.msg,
                    //   icon: 'none'
                    // })
                    }
                })
        },
        // 倒计时
        timeDownStart() {
          if (this.timer) {
            clearInterval(this.timer)
          }
          this.timer = setInterval(() => {
            if (this.hb_result.expire_seconds > 0) {
              this.hb_result.expire_seconds--
            } else {
              clearInterval(this.timer)
            }
          }, 1000)
        },
        toHbHelp() {
          this.$navigateTo(`/redEnvelopes/index?info_id=${this.id}&info_type=4&task_id=${this.task_id}`)
        },
        // 判断登录
        loginState() {
          checkLogin({
            success: (res) => {
              this.isLogin = true
              this.checkHb()
            },
            fail: (res) => {
              this.isLogin = false
              this.checkHb()
            },
            complete: (res) => {
              this.$store.state.user_login_status = res.status
            },
          })
        },
    },
    onPageScroll(option){
        if (option.scrollTop>=200){
            this.showToTop=true
        }else {
            this.showToTop=false
        }
    }
}
</script>

<style lang="scss" scoped>
// @import url("../../components/u-parse/u-parse.css");
// @import url("../../components/wx-parse/parse.css");
.content view {
    font-size: 32upx;
    line-height: 52upx
}
.news-detail{
    // padding: 0 48upx;
    background: #fff;
    // max-width: 100vw;
    // overflow: hidden;
}
.video {
    width: 100%;
    height: 500upx;
    height: 60vw;
}
.adviser{
    align-items: center;
    position: sticky;
    position: -webkit-sticky;
    background: #fff;
    z-index: 3;
    padding: 28upx 48upx;
    
    top: 40px;
    // width: 100%;
    height: 80upx;
    .adv-img{
        width: 80upx;
        height: 80upx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 16upx;
        image{
            width: 100%;
        }
    }
    .adv-infos{
        flex-direction: column;
         flex: 1;
         height: 100%;
        .adv-name{
            color: #333;
            font-size: 22upx;
        }
        .adv-level{
            color: #999;
            font-size: 22upx;
            margin-top: auto;
        }
    }
    .adv-oper{
        color: #FB656A;
        .adv-ask{
            font-size: 28upx;
            padding: 12upx 36upx;
            border:2upx solid #FB656A;
            border-radius: 8upx;
            box-shadow: 0 2px 4px 0 rgba(251,101,106,0.10);
        }
        .adv-tel{
            font-size: 28upx;
            padding: 12upx 16upx;
            border:2upx solid #FB656A;
            border-radius: 8upx;
            margin-left: 24upx;
            box-shadow: 0 2px 4px 0 rgba(251,101,106,0.10);
        }
    }
}
.page {
    min-height: 100vh;
    padding-bottom: 110upx;
}

.news_page {
    padding-bottom: 120upx;

}
.contents{
    overflow-x: hidden;
}
.article-content ::v-deep p{
    margin-bottom: 30upx!important;
}
.article-content  ::v-deep video{
    max-width: 100%;
    margin-bottom: 20upx;
}

.article {
    background-color: #fff;
    user-select: text;
    // padding: 0 48upx;
    // padding: 0px 10upx;
    .mianze{
        font-size: 22upx;
        color: #999;
        margin: 32upx 48upx 48upx;
        line-height: 2;
    }
    .title{
        padding: 24upx 48upx;
    }
    .infos{
        margin: 24upx 48upx;
    }
    .uni-list{
        padding: 0 48upx;
        box-sizing: border-box;
    }
    .youhui-btn{
        padding: 0 48upx;
    }
    .gengduo{
        padding: 0 48upx;
    }
}

.pad-lr-10 {
    padding: 0px 10upx;
}

.title {
    padding: 24upx 30upx;
    font-size: 42upx;
    line-height: 1.8;
    font-weight: bold;
    color: #333
}

.count {
    margin: 0 10upx;
    font-size: $uni-font-size-sm;
    color: #666
}

// .author {
//     font-size: $uni-font-size-sm;
//     // color: #4db0fd
// }

.from {
    margin-left: 10upx;
    font-size: $uni-font-size-sm;
}
.infos {
    display: flex;
    align-items: center;
}
.infos .info_from{
    display: inline-block;
    overflow: hidden;
    font-size: 22upx;
    color: #999;
}
.infos .info_biaoqian{
    display: flex;
    align-items: center;
    .info_from{
        border: 1upx solid #999;
        padding: 2upx 6upx;
        border-radius: 4upx;
    }
}
.infos .c-right{
    margin-left: auto;
}
.content {
    background-color: #fff;
    margin: $uni-spacing-col-base 48upx;
    overflow-x: hidden;
}

.build-box {
    background-color: #fff;
    padding: 0 48upx;
}

.build {
    padding-bottom: 24upx;
}

.build-title {
    font-size: 32upx;
    margin-right: 16upx;
    font-weight: bold
}

.build-img-box {
    box-sizing: border-box;
    width: 100%;
    height: 60vw;
}

.build-img-box image {
    width: 100%;
    height: 100%;
}

.status {
    display: inline-block;
    margin-right: 20upx;
    font-size: 28upx;
    padding: 0 10upx;
    border-radius: 4upx;
    background-color: $uni-color-primary;
    color: #fff;
    &.status1 {
        color: #fff;
        background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
    }
    &.status2 {
        color: #fff;
        background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
    }
    &.status3 {
        color: #fff;
        background: linear-gradient(to right, #ff8533 0%, #ff7213 100%);
    }
    &.status4 {
        color: #fff;
        background: linear-gradient(to right, #ccc 0%, #ccc 100%);
    }
}

.space {
    display: inline-block;
    margin-left: 10upx;
    font-size: 22upx;
    padding: 0 6upx;
    box-sizing: border-box;
    border-radius: 4upx;
    border: 1upx solid #999;
    color: #999;
}

.row {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}
.pad12{
    padding: 24upx 0;
}
.buid-bottom{
    align-items: center;
    font-size: 28upx;
    .price{
        margin-left: auto;
        color: #FB656A;
    }
    .address{
        max-width: 65%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #666;
    }
}
// 获取优惠
.coupon-box{
  height: 140rpx;
  line-height: 1;
  color: #fff;
  margin: 0 48upx;
  position: relative;
  .bg_img{
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
  .coupon_container{
    padding: 0 48rpx;
    align-items: center;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
  .coupon_name{
    flex: 1;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    margin-right: 16rpx;
  }
  .coupon_content{
    font-size: 22rpx;
    flex: 1;
  }
  .btn{
    padding: 0 24rpx;
    margin-bottom: 16rpx;
    height: 48rpx;
    line-height: 48rpx;
    border-radius: 24rpx;
    background-color: #fff;
    color: $uni-color-primary;
  }
}
.gengduo .label{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    line-height: 1;
    padding: 24upx 0 0;
    // margin-bottom: 11px;
    font-size:36upx;
    font-weight: bold;
}
.container .label {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    line-height: 1;
    padding: 24upx 0;
    // margin-bottom: 24upx;
    font-size:36upx;
    font-weight: bold;
    .more{
        padding: 6upx;
        font-size: 20upx;
        font-weight: initial;
        color: #999;
    }
}
// 用户点评
.comment-box {
    margin-top: 24rpx;
    padding: 0 48upx;
    .comment-item {
        margin-bottom: 48rpx;
        flex-direction: row;
        display: flex;
        .header_img {
            width: 48rpx;
            height: 48rpx;
            border-radius: 50%;
            margin-right: 10rpx;
        }
        .comment_info {
            flex: 1;
            .comment_user_info{
                flex-direction: column;
                box-sizing: border-box;
            }
            .user_name {
                font-size: 22rpx;
                color: #999;
                margin-bottom: 6rpx;
            }
            .comment_title {
                margin-bottom: 16rpx;
                line-height: 1.5;
            }
            .comment_time {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                font-size: 22rpx;
                color: #999;
            }
            .options-box {
                align-items: center;
                font-size: 22rpx;
                color: #d8d8d8;
                text {
                    margin-left: 10rpx;
                }
                .comment_num {
                    flex-direction: row;
                    align-items: center;
                }
                .praise_num {
                    flex-direction: row;
                    align-items: center;
                    margin-left: 26rpx;
                }
            }
        }

        .reply-list {
            margin-top: 20rpx;
            padding: 10rpx 16rpx;
            background-color: #f2f2f2;
        
            .reply-item {
                font-size: 22rpx;
                padding: 8rpx;
                padding-left: 50rpx;
                align-items: flex-start;
                display: block;
                .name_info{
                    align-items: center;
                    display: inline-block;
                    font-size: 22rpx;
                }
                .reply_user_header_img {
                    width: 40rpx;
                    height: 40rpx;
                    margin-left: -50rpx;
                    position: relative;
                    top: 8rpx;
                    border-radius: 50%;
                    margin-right: 10rpx;
                }
                .identity{
                    font-size: 22rpx;
                    line-height: 1;
                    padding: 4rpx 8rpx;
                    margin: 0 10rpx;
                    color: $uni-color-primary;
                    background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
                }
                .reply_content{
                    flex: 1;
                }
            }
        }
    }
    .comment_btn{
        padding: 24rpx;
        justify-content: center;
        color: $uni-color-primary;
        border-radius: 8upx;
        background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
        >text{
        margin-left: 16rpx;
        }
    }
}
.flex-row{
    display: flex;
    box-sizing: border-box;
    flex-direction: row;
}
.price {
    color: $uni-color-primary
}

.pan {
    height: 80upx;
    line-height: 80upx;
    // padding: $uni-spacing-row-base;
    background-color: #fff;
    align-items: center;
    background: rgba(251,101,106,0.05);
    flex: 1;
    box-sizing: border-box;
    // margin: 24upx 0;
    &.btn-first{
        margin-right: 24upx;
    }
    
    .btn{
        
        
        border-radius: 8upx;
        text{
            color:rgba(251,101,106,1)
        }
    }
    
}

.pan.right-line:after {
    top: $uni-spacing-row-base;
    bottom: $uni-spacing-row-base;
}
.to-comment{
    justify-content: center;
    align-items: center;
    height: 80upx;
    
    // padding: 20upx 0;
    color: #FB656A;
    background-image: linear-gradient(to right ,rgba(247,145,143,0.1) ,rgba(251,101,106,0.1));
    border-radius: 8upx;
    .to-comment-con{
        margin-left: 10upx;
        size: 32upx;
    }
}
// .btn {
//     display: inline-block;
//     padding: 0 20upx;
//     border: 1upx solid $uni-color-primary;
//     color: $uni-color-primary;
//     border-radius: 8upx;
// }

// .btn text {
//     margin-left: 10upx;
// }

.footer {
    position: fixed;
    bottom: 0;
    height: 110upx;
    line-height: 110upx;
    width: 100%;
    z-index: 13;
    padding: 14upx 48upx;
    // justify-content: space-between;
    background-color: #fff;
    box-sizing: border-box;

}


// .footer view {
//     text-align: center;
//     // padding: 6upx 0;
//     justify-content: center;
//     align-items: center;
//     font-size: $uni-font-size-sm
// }
.footer-left{
    padding: 6upx 10upx 6upx 0;
}
.footer-left-con{
    // flex-direction: column;
    // align-items: center;
    // padding-right: 32upx;
    align-items: center;
    padding: 0;
    margin: 0;
    background-color: #fff;
    line-height: initial;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-right: 32upx;
    overflow: hidden;
    position: relative;
    .text{
        font-size: 22upx;
        line-height: 1;
        color: #999;
        display: inline-block;
        width: 100%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

    }

}
.comment-pinglun{
    padding-left: 15upx;
}
.footer-right{
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 15px;
    padding: 0;
    margin: 0;
    border-radius: 0;
    color: #fff;
    justify-content: center;
    &.footer-right-left{
        background: #FBAC65;
        box-shadow: 0 0 8upx 0 rgba(0, 0, 0, 0.05);
        border-top-left-radius: 40upx;
        border-bottom-left-radius: 40upx;
    }
    &.footer-right-right{
        background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
        box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.3);
        border-top-right-radius: 20px;
        border-bottom-right-radius: 20px
    }
}


.footer>view.tel {
    font-size: $uni-font-size-base;
    justify-content: center;
    align-items: center;
    color: #fff;
    background-color: $uni-color-primary
}

.footer button {
    line-height: initial;
    padding: 20upx;
    color: #fff;
    background-color: $uni-color-primary;
    background-color: #fff;
}

my-icon {
    line-height: 1
}

.share-box {
    padding: 20upx 0;
    // margin-bottom: 90upx;
    background-color: #fff;

    .text {
        padding: 20upx;
        width: 100%;
        font-weight: bold;
        box-sizing: border-box;
        text-align: center;
    }
}
.right-fixed{
    position: fixed;
    right: 24upx ;
    bottom:216upx;
    background: #fff;
}
.right-fixed .scroll-top{
    padding: 10upx;
    border: 2upx solid #f3f3f3;
    margin-bottom: 20upx;
    box-shadow: 0 0 4px 0 rgba(0,0,0,0.05);
}
.home-fixed{
    display: flex;
    align-items: center;
    padding: 10upx;
    justify-content: center;
    border: 2upx solid #f3f3f3;
}

.card-btn {
    position: fixed;
    z-index: 96;
    bottom: 30vh;
    right: 0;
    padding: 15upx 30upx;
    border-top-left-radius: 30upx;
    border-bottom-left-radius: 30upx;
    background-color: #f65354;
    opacity: 0.8;
    box-shadow: 0 10upx 30upx #f65354;
    color: #fff
}

.card-img {
    width: 70%;
    margin: 0 15%;
    padding: 40upx 0;
}

.comment-box {
    // padding-bottom: 90upx;
    background-color: #fff;

    .btn-box {
        display: flex;
        justify-content: flex-end;
        padding-top: 0;
        margin-top: 24upx;

        .small {
            margin: initial
        }
    }
}

.textarea-box {
    padding: 24upx;

    textarea {
        width: 100%;
        height: 60upx;
        background-color: #fff
    }
}

.row-title .uni-list-item__content-title {
    font-size: 36upx;
    font-weight: bold;
    color: #555
}

.banner-box {
    // padding: 10upx 30upx;
    background-color: #fff;
    padding: 0 48upx;

    .banner-item~.banner-item {
        margin-top: 10upx;
    }
}

/* #ifdef H5 */
#card {
    padding: 50upx 0 10upx 0;
    width: 100%;
    position: fixed;
    left: 100%;
    background-color: #fff;
}

.project-name,
.nowdate {
    margin: 6upx 50upx;
    font-size: 28upx;
    color: #666;
}
.default_active::before{
    background-color:#fff;
}
.default_active::after{
    background-color:#fff;
}
.default_active  ::v-deep  .uni-list-item__container{
    padding-top: 0!important;
}
.color-red {
    color: #f65354;
}

.card_img-box {
    width: 100%;
    margin-top: 40upx;
    padding: 10upx 50upx;
    box-sizing: border-box;
    height: 60vw;
    overflow: hidden;
}

.card_img-box image {
    width: 100%;
    height: 100%;
}

.card_info-box {
    margin: 50upx;
}

.card_info-box .title {
    font-size: 40upx;
    padding: 0;
}

.card-line-box {
    width: 100%;
    box-sizing: border-box;
    padding: 0 50upx;
}

.card-line {
    height: 4upx;
    width: 100%;
    background-color: #f65354;
}

.card-footer {
    margin: 50upx;

    .categoryname {
        font-size: 32upx;
        line-height: 60upx;
        color: #666;
    }

    .update_time {
        font-size: 26upx;
        line-height: 40upx;
        color: #666
    }

    .logo-box {
        width: 30vw;

        .logo {
            width: 100%
        }
    }

    .qrcode-box {
        .qrcode {
            width: 25vw;
            height: 25vw
        }

        .text-center {
            font-size: 24upx;
            color: #666;
        }
    }

}

.overhide {
    position: absolute;
    width: 100%;
    top: 0;
    bottom: 0;
    overflow: hidden;
}

/* #endif */
.to-consu {
    align-items: center;
    justify-content: center;
    color: #fff;
    background: #00c0eb;
    // background-color: $uni-color-primary;
    margin-right: 10upx;
    border-radius: 10upx;

}

.foot_left {
    justify-content: flex-start;
    align-items: flex-start;
    padding: 26rpx 5rpx 26rpx 20rpx;

    flex: 1;

    .left {
        height: 67rpx;
        width: 89rpx;
        overflow: hidden;

        image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            overflow: hidden;
        }
    }

    .right {
        height: 100%;
        width: 100%;
        overflow: hidden;
        margin-left: 10upx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .name {
            font-size: 25upx;
            //  height: 15upx;
            line-height: 1;
        }

        .sub_name {
            font-size: 22upx;
            color: #666;
            //  height: 20upx;
            margin-top: 10upx;
            line-height: 1;
            justify-content: flex-start;
            align-items: center;
            padding: 10rpx；
        }

    }

}

.b_detail {
    height: 120upx;
    background: #fff;
}

.b_detail .to-tel {
    align-items: center;
    justify-content: center;
    color: #fff;
    background-color: $uni-color-primary;
    flex: 0.6;
    margin: 20upx 24upx 20upx 20upx;
    border-radius: 10upx;

}

.b_detail .to-consu {
    align-items: center;
    justify-content: center;
    color: #fff;
    background: #00c0eb;
    margin: 20upx 10upx 20upx 20upx;
    // background-color: $uni-color-primary;
    margin-right: 10upx;

    flex: 0.6;
    border-radius: 10upx;
}
.hongbao {
  position: absolute;
  right: 20rpx;
  width:120rpx;
  height:120rpx;
  white-space: nowrap;
  overflow: hidden;
  bottom: 24rpx;
  .hb_content{
    width:120rpx;
    height:120rpx;
    display: block;
    position: absolute;
    left:120rpx;
    top:0;
  }
  image {
    position:absolute;
    left: 0;
    top:0;
    width: 120rpx;
    height: 120rpx;
  }
}
</style>
