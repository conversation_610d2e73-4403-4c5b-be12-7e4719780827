<template>
  <view class="mask tel-container" v-show="show_tel_pop">
    <view class="tel_info-box">
      <view class="close" @click="onClose">
        <my-icon type="ic_guanbi" size="42rpx" color="#999"></my-icon>
      </view>
      <view class="header_tip">为保护用户隐私，本号码为虚拟号码</view>
      <view class="tel">{{tel_res.virtualNumber}}</view>
      <view class="time flex-box">
        <view class="line"></view>
        <view class="text">{{second>0?'有效期 ' + second+'秒': '已过期'}}</view>
        <view class="line"></view>
      </view>
      <view class="btn" :class="{disable: second===''}" @click="handleMake">{{second===0?'重新获取':'立即拨打'}}</view>
      <view class="footer_tip" v-if="tel_res.callerNumber">请使用手机号<text>{{tel_res.callerNumber}}</text>拨打电话</view>
    </view>
    <view class="mask"></view>
  </view>
</template>

<script>
import myIcon from '@/components/myIcon'
export default {
  name: 'telPop',
  components: {
    myIcon,
  },
  props: {
    tel_res: {
      type: Object,
      default: ()=>{}
    },
    show_tel_pop: Boolean
  },
  model:{
    prop: 'show_tel_pop',
    event: 'close'
  },
  watch: {
    tel_res: {
      handler: function(val){
        this.timeDown(val.expireSeconds)
      },
      deep: true
    },
  },
  data () {
    return {
      second: '',
    }
  },
  methods: {
    timeDown(s){
      this.timer&&clearInterval(this.timer)
      this.second = s
      this.timer = setInterval(()=>{
        if(this.second>0){
          this.second--
        }else{
          clearInterval(this.timer)
        }
      }, 1000)
    },
    handleMake(){
      if(this.second===''){
        return
      }
      if(this.second<=0){
        // uni.showToast({
        //   title: "号码已过期，请重新获取",
        //   icon: 'none'
        // })
        // return
        this.$emit('retrieve')
        return
      }
      uni.makePhoneCall({
        phoneNumber: this.tel_res.virtualNumber
      });
      if(this.tel_res.isstatis){
        this.isstatis()
      }
    },
    isstatis(){
      this.$ajax.get("im/callUpStatistics", {
        id: this.tel_res.mid,
        type: this.tel_res.from,
        info_id: this.tel_res.info_id
      }, res => {
        console.log(res.data);
      }, err => {
        console.log(err)
      }, { disableAutoHandle: true })
    },
    onClose(){
      this.$emit('close', false)
    }
  }
}
</script>

<style scoped lang="scss">
.tel-container{
  &.mask{
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 998;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba($color: #000000, $alpha: 0.32);
  }
  .tel_info-box{
    padding: 72rpx 52rpx;
    position: relative;
    background-color: #fff;
    width: 72vw;
    margin: auto;
    text-align: center;
    border-radius: 32rpx;
    .close{
      position: absolute;
      top: 0;
      right: 0;
      padding: 24rpx;
    }
    .header_tip{
      margin: 40rpx 0;
      font-size: 28rpx;
      color: #999;
    }
    .tel{
      margin-bottom: 24rpx;
      font-size: 60rpx;
      color: #000;
      font-weight: bold;
    }
    .time{
      justify-content: center;
      align-items: center;
      margin-bottom: 72rpx;
      font-size: 28rpx;
      color: $uni-color-warning;
      .text{
        margin: 0 24rpx;
      }
      .line{
        height: 1rpx;
        width: 120rpx;
        background-color: #dedede;
      }
    }
    .btn{
      height: 82rpx;
      line-height: 82rpx;
      width: 100%;
      border-radius: 41rpx;
      font-size: 32rpx;
      font-weight: bold;
      background-color: $uni-color-primary;
      color: #fff;
      &.disable{
        background-color: #dedede;
      }
    }
    .footer_tip{
      margin-top: 48rpx;
      color: #999;
      >text{
        color: $uni-color-primary;
      }
    }
  }
}
</style>