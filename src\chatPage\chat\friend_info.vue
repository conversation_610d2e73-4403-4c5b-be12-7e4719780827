<template>
<view class="page">
    <template v-if="chat_id">
        <agent-card :infoData="friend_info">
            <template v-slot:options="{infoData}">
                <view class="btn-box">
                    <view class="btn" @click="handleBlack()">加入黑名单</view>
                </view>
            </template>
        </agent-card>
    </template>
    <template v-else>
        <agent-card :infoData="friend_info"></agent-card>
    </template>
    <view class="card">
        <view class="card_cate flex-box">
            <view class="card_title" :class="{active: cate === 1}" @click="getData">客访轨迹</view>
            <view class="card_title" :class="{active: cate === 2}" @click="onClickLog">通话记录</view>
        </view>
        <view v-show="cate == 1">
            <time-line :lineData="lineData"></time-line>
        </view>
        <view v-show="cate == 2">
            <time-line :lineData="telLogs" custom>
                <template v-slot="{slotItem,slotIndex}">
                    <view class="tel-item flex-box">
                        <view class="tel-info flex-1">
                            <view class="title_row">{{slotItem.username}}拨打了您的电话, 通话时长{{slotItem.call_duration | durationFilter}}</view>
                            <view class="time">{{slotItem.callon_time}} ~ {{slotItem.callend_time}}</view>
                        </view>
                        <view class="voice flex-box" v-if="slotItem.record_ready&&slotItem.record_file" @click="playVoice(slotItem.record_file, slotIndex)">
                            <image class="play_vioce_icon" :src="play_voice_index === slotIndex?'/static/icon/voice/play_voice_black.gif':'/static/icon/voice/voice_icon_black.png'"></image>
                            <text>{{slotItem.call_duration}}</text>
                        </view>
                    </view>
                </template>
            </time-line>
            <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
        </view>
        <view class="tip" v-if="lineData.length===0&&nodata">还没有此用户的访问轨迹~~</view>
    </view>
</view>
</template>

<script>
import timeLine from '../../components/timeLine'
import agentCard from '../../components/agentCard'
import myIcon from '../../components/myIcon'
import {showModal} from '../../common/index'
import {uniLoadMore} from '@dcloudio/uni-ui'
import { mapState } from 'vuex'
const recorderManager = uni.getRecorderManager();
const innerAudioContext = uni.createInnerAudioContext();
export default {
    data() {
        return {
            chat_id:'',
            cate: 1,
            friend_info:{},
            lineData:[],
            telLogs:[],
            nodata:false,
            tel_log_page: 1,
            play_voice_index: -1,
            get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
        }
    },
    components: {
        timeLine,
        agentCard,
        myIcon,
        uniLoadMore
    },
    computed: {
        ...mapState(['im'])
    },
    onLoad(options){
        if(options.chat_id){
            this.chat_id = options.chat_id
        }
        if(options.user_id){
            this.user_id = options.user_id
            if(options.cate){
                this.cate = parseInt(options.cate)
            }
            if(this.cate === 2){
                this.getTelLog()
            }else{
                this.getData()
            }
            this.getFriendInfo()
        }
        this.to_id = options.to_id
        this.from_id = options.from_id
        innerAudioContext.onPlay(()=>{
            this.play_voice_index = this.current_voice_index
        })
        innerAudioContext.onStop(()=>{
            this.play_voice_index = -1
        })
        innerAudioContext.onEnded(()=>{
            this.play_voice_index = -1
        })
    },
    filters:{
        durationFilter(value){
            if(!value){
                return '0秒'
            }
            let time = parseInt(value)
            let minute = Math.trunc(time/60)
            let second = time%60
            if(minute){
                return `${minute}分${second}秒`
            }else{
                return `${second}秒`
            }
        }
    },
    methods: {
        getFriendInfo(){
            this.$ajax.get('im/contactDetails.html',{user_id:this.user_id},res=>{
                if(res.data.code === 1){
                    this.friend_info = res.data.member
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            },err=>{
                console.log(err)
            })
        },
        getData(){
            this.cate = 1
            this.$ajax.get('im/visitorDetail.html',{user_id:this.user_id}, res=>{
                if(res.data.code === 1){
                    this.lineData = res.data.list
                }else{
                    this.nodata = true
                }
                if(res.data.isCustom){
                    //访客身份
                    uni.setNavigationBarTitle({
                        title: "我的访问记录"
                    });
                }else{
                    // 职业顾问或经纪人身份
                     uni.setNavigationBarTitle({
                        title: "访客记录"
                    });
                }
            })
        },
        onClickLog(){
            this.cate = 2
            if(this.telLogs.length===0){
                this.tel_log_page == 1
                this.getTelLog()
            }
        },
        getTelLog(){
            this.get_status = 'loading'
            if(this.tel_log_page == 1){
                this.telLogs=[]
            }
            this.$ajax.get('im/callRecord.html',{user_id: this.user_id,page:this.tel_log_page, rows:20}, res=>{
                console.log(res.data)
                if(res.data.code === 1){
                    this.telLogs = this.telLogs.concat(res.data.list)
                    if(res.data.list.length<20){
                        this.get_status = "noMore"
                    }else{
                        this.get_status = "more"
                    }
                }else{
                    this.get_status = "noMore"
                }
            })
        },
        handleBlack(){
            showModal({
                title:"提示",
                content:"确定要将此好友加入黑名单吗？",
                confirm:()=>{
                    this.$ajax.get('im/pullBack',{chat_id:this.chat_id},res=>{
                        if(res.data.code === 1){
                            uni.showToast({
                                title:'操作成功'
                            })
                            this.pullBlack()
                            // 从好友列表删除这个好友
                            for (let i = 0; i < this.$store.state.im.friendList.length; i++) {
                                if (this.$store.state.im.friendList[i].chat_id === this.chat_id) {
                                    this.$store.state.im.friendList.splice(i,1)
                                    break;
                                }
                            }
                            setTimeout(()=>{
                                uni.navigateBack({
                                    delta:2
                                })
                            },1500)
                        }else{
                            uni.showToast({
                                title:res.data.msg,
                                icon:"none"
                            })
                        }
                    })
                }
            })
        },
        pullBlack(){
            let blackMesage = {flag:'pullBlack',to_id:this.to_id,from_id:this.from_id}
            this.im.socketTask.send({
                data: JSON.stringify(blackMesage),
                success:(res)=>{
                    console.log(res)
                },
                fail:err=>{
                    console.log(err)
                    uni.showToast({
                        title:err||'操作失败',
                        icon:'none',
                        mask:true
                    })
                }
            });
        },
        playVoice(vioce ,index){
            console.log(index)
            if(!vioce){
                uni.showToast({
                    title: '暂无录音文件',
                    icon: 'none'
                })
                return
            }
            this.current_voice_index = index
            if(index === this.play_voice_index){
                innerAudioContext.stop()
                return
            }
            innerAudioContext.src = vioce
            innerAudioContext.play()
        }
    },
    onUnload(){
        innerAudioContext.stop()
    },
    onReachBottom(){
        if(this.cate === 2&&this.get_status === 'more'){
            this.tel_log_page++;
            this.getTelLog()
        }
    }
}
</script>

<style lang="scss" scoped>
.page{
    min-height: 100vh;
    background-color: #fff;
}
.btn-box{
    padding: 20upx 24upx;
    .btn{
        padding: 6upx 20upx;
        border: 10upx;
        width: 180upx;
        box-sizing:border-box;
        border-radius:8upx;
        background-color: #f44;
        color: #fff;
        text-align: center;
    }
}
.card{
    padding: 20upx 24upx;
    .card_cate{
        justify-content: space-between;
        margin-bottom: 20upx;
        border-bottom: 1upx solid #f1f1f1;
    }
    .card_title{
        flex: 1;
        text-align: center;
        padding: 30upx 20upx;
        font-size: 32upx;
        margin: 0 30rpx;
        &.active{
            color: $uni-color-primary;
            border-bottom: 4rpx solid $uni-color-primary;
        }
    }
}
.tip{
    margin-top: 30upx;
    padding: 24upx 20upx;
    font-size: 28upx;
    text-align: center;
    color: #999;
}
.tel-item{
    align-items: center;
    .tel-info{
        overflow: hidden;
        .title_row{
            font-size: 28rpx;
            line-height: 1.5;
            margin-bottom: 15rpx;
        }
        .time{
            font-size: 24rpx;
            font-weight: bold;
            color: #999;
        }
    }
    .voice{
        min-width: 120rpx;
        .play_vioce_icon{
            width: 36rpx;
            height: 36rpx;
        }
    }
}
</style>
