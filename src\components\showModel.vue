
<template>
<view class="model" @touchmove.stop.prevent="bindTouchmove">
<view class="model_box" v-show="is_show">
    <view class="close" @click="onClose">
        <my-icon type="ic_guanbi" size="42rpx" color="#999"></my-icon>
    </view>
    <view class="model_content">
        <template v-if="content">
            <view v-html ="content">
              <!-- <text>当前账号已超出总发布条数 </text><text style="color:red">（4条）</text><text>,已失效房源可删除或下架,本条继续发布将收费</text> <text style="color:red">10.00元 </text> -->
            </view>
        </template>
        <template v-else>
            <slot />
        </template>
    </view>
    <view class="model_btn_box">
        <view class="cancel btn" :class ="cancelClassName" @click ="cancel">
          {{cancelText}}
        </view>
        <view class="confirm btn" :class ="confirmClassName" @click ="confirm">
          {{confirmText}}
        </view>
        <!-- <view v-for="(btn, index) in btns" :key="index" @click="" class="btn right-line" :style="{color:btn.color}">{{btn.name}}</view> -->
    </view>
</view>
<view class="mask" :class="{show:is_show}"></view>
</view>
</template>

<script>
import myIcon from '../components/myIcon'
export default {
    props:{
        is_show: {
            type: Boolean,
            default: false
        },
        cancelText:{
          type:String,
          default:"取消"
        },
        confirmText:
        {
          type:String,
          default:"确认"
        },
        cancelClassName: {
          type:String,
          default:"cancel_class",
        },
        
        confirmClassName:{
          type:String,
          default:"confirm_class",
        },
        content:'',
    },
    data() {
        return {
          show:false
        }
    },
    components:{
      myIcon
    },
    methods: {
        cancel(){
          // this.is_show =false
          this.$emit("cancel")
        },
        confirm(){
          // this.is_show =false
          this.$emit("confirm")
        },
        onClose(){
           this.$emit("close")
          // this.is_show =false
        },
        bindTouchmove(){}
    }
}
</script>

<style lang="scss">
.model_box{
    position: fixed;
    z-index: 999;
    width: 70%;
    max-width: 300px;
    top: 50%;
    left: 50%;
    padding: 36px 26px;
    transform: translate(-50%,-50%);
    background-color: #fff;
    text-align: center;
    border-radius: 20rpx;
    overflow: hidden;
    .close{
      position: absolute;
      top: 0;
      right: 0;
      padding: 24rpx;
    }
    .model_title{
        padding: 40upx 50upx 10upx 50upx;
        box-sizing: border-box;
        font-size: 36upx;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #555
    }
    .model_content{
      padding: 36upx 46upx;
      min-height: 40px;
      font-size: 15px;
      line-height: 1.8;
      color: #999;
      max-height: 400px;
      text-align: left;
      overflow-y: auto;
    }
    .model_btn_box{
      position: relative;
      line-height: 96upx;
      font-size: 36upx;
      padding: 10rpx 48rpx;
      // display: flex;
      box-sizing: border-box;
      .btn{
        flex: 1;
        font-size: 32upx;
        border-radius: 80rpx;
        color: #fff;
        &.cancel{
          // background: #22A7F2;
          color: #22A7F2;
          margin-bottom: 20rpx;
        }
        &.confirm{
          background: #fb656a;
        }
      }
    }
    .mask{
      transition: 0.3s;
      position: fixed;
      width: 100%;
      top: 0;
      bottom: 0;
      z-index: -1;
      &.show{
        background-color: rgba($color: #000000, $alpha: 0.6);
        z-index: 998;
      }
    }
}
</style>
