<template>
  <view class="pinglun">
    <view class="pinglun_item flex-row " v-for ='(item,index) in pinglun_data.children' :key ="index">
      <view class="pinglun_prelogo">
        <image mode="widthFix" :src='item.prelogo'></image>
      </view>
      <view class="pinglun_con">
        <view class="cname">
          {{item.name}}
        </view>
        <view class="p_content">
          {{item.content}}
        </view>
        <view class="p_oper flex-row items-center">
          <view class="ctime">刚刚</view>
          <view class="reply">
            回复
          </view>
        </view>
      </view>
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  props:{
    data:{
      type:Object,
      default:()=>{}
    }
  },
  data(){
    return  {
      pinglun_data:{}
    }
  },
  created(){
    pinglun_data
  }
}
</script>

<style>

</style>