<template>
<view class="manage_case">
    <tab-bar :tabs="releaseList" col="3" @click="switchTab"></tab-bar>
    <view class="case_list top-20">
        <view v-for="(case_item, index) in caseList" :key="index" class="list-item flex-box bottom-line" @click="goTo(case_item.id)">
            <view class="img-box list-img">
                <image :src="case_item.prepath | imgUrl" lazy-load mode="aspectFill"></image>
            </view>
            <view class="list-info flex-1 house">
                <view class="info-title row2">{{case_item.title}}</view>
                <view class="info_footer flex-box">
                    <view class="info_footer_left flex-1">
                        <view class="address">
                            <my-icon type="chengshi"></my-icon>
                            <text class="address_text">{{case_item.name || ""}}</text>
                        </view>
                        <view class="other_info">
                            <text>{{case_item.click||0}}浏览</text>
                            <text>，{{case_item.pcount||0}}图</text>
                        </view>
                    </view>
                    <view class="info_footer_right">
                        <view @click.stop.prevent="handleDel(index)" class="del"><my-icon type="shanchu" color="#f44" size="13"></my-icon>删除</view>
                    </view>
                </view>
            </view>
        </view>
        <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
    <view class="add_case" @click="addCase()">
        <my-icon type="zengjia" size="36" color="#fff"></my-icon>
    </view>
    <my-popup ref="release">
        <view class="apply-box">
            <view class="row bottom-line">选择发布类型</view>
            <view class="upgrade-list flex-box">
                <view class="upgrade-item flex-1" v-for="item in releaseList" :key="item.index" @click="toRelease(item.index)">
                    <view>{{item.name}}</view>
                    <view>1条/{{item.money||releaseMoney}}金币</view>
                </view>
                <view class="upgrade-item flex-1" style="height:0;background:#fff"></view>
            </view>
            <view class="btn-box">
                <button class="" @click="$refs.release.hide()">取消</button>
            </view>
        </view>
    </my-popup>
</view>
</template>

<script>
import tabBar from "../../components/tabBar.vue"
import {uniLoadMore,uniIcons} from '@dcloudio/uni-ui'
import myIcon from "../../components/icon"
import myPopup from "../../components/myPopup"
import {
    navigateTo,
    formatImg,
    showModal
} from "../../common/index.js"
export default {
    data() {
        return {
            caseList:[],
            typeList:[],
            releaseList:[],
            releaseMoney:"",
            params:{
                rows:20,
                type:1,
                page:1
            },
            get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
        }
    },
    onLoad(){
        this.getRelease()
    },
    components: {
        tabBar,
        myIcon,
        uniLoadMore,
        uniIcons,
        myPopup
    },
    filters:{
        imgUrl(val){
            return formatImg(val,'w_120')
        }
    },
    methods:{
        getData(){
            if(this.params.page == 1){
                this.caseList = []
            }else{
                if(this.get_status=="noMore"){
                    return
                }
            }
            this.get_status = "loading"
            this.$ajax.get('memberShop/caseHomeList.html',this.params, res=>{
                if(res.data.code === 1){
                    this.caseList = res.data.list
                    if(res.data.list.length<this.params.rows){
                        this.get_status = "noMore"
                    }else{
                        this.get_status = "more"
                    }
                }else{
                    this.get_status = "noMore"
                }
            },err=>{
                console.log(err)
            })
        },
        switchTab(e){
            this.params.type = e.index
            this.params.page = 1
            this.getData()
        },
        addCase(){
            this.$refs.release.show()
        },
        getRelease(){
            this.$ajax.get('memberShop/releaseinfo',{},res=>{
                if(res.data.code == 1){
                    this.releaseList = res.data.type.map(item=>{
                        return {name:item.type,index:item.id}
                    })
                    // 接口不在每一项里面显示所需金币，而是不同发布类型都用同一个值
                    this.releaseMoney = res.data.money
                    if(this.releaseList.length>0){
                        this.params.type = this.releaseList[0].index
                        this.getData()
                    }
                }
            })
        },
        toRelease(typeid){
            navigateTo('/home/<USER>/release?typeid='+typeid)
        },
        goTo(id){
            navigateTo(`/home/<USER>/detail?id=${id}`)
        },
        handleDel(index){
            showModal({
                content:"确定删除此案例吗？",
                confirm:()=>{
                    let case_id = this.caseList[index].id
                    this.delCase(case_id)
                }
            })
        },
        delCase(id){
            this.$ajax.get('memberShop/delCase',{id},res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title:res.data.msg||'删除成功'
                    })
                    this.params.page=1
                    this.getData()
                }else{
                    uni.showToast({
                        title:res.data.msg||'删除失败',
                        icon:'none'
                    })
                }
            })
        }
    },
    onReachBottom(){
        this.params.page++
        this.getData()
    }
}
</script>

<style scoped lang="scss">
.manage_case{
    padding-top: 40upx;
}
.add_case{
    position: fixed;
    right: 36upx;
    bottom: 180upx;
    height: 90upx;
    width: 90upx;
    border-radius: 50%;
    display:flex;
    align-items:center;
    justify-content:center;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.9);
    z-index: 96;
    box-shadow: 1upx 1upx 20upx 3upx rgba($color: $uni-color-primary, $alpha: 1);
}
.list-info{
    .info-title{
        height: 86upx;
        margin-bottom: 20upx;
        color: #333;
        &.row2 {
            -webkit-line-clamp: 2;
        }
    }
    .info_footer{
        justify-content: space-between;
        align-items: flex-end;
        .address{
            margin-bottom: 12upx;
            .address_text{
                font-size: 26upx;
                color: #666;
            }
        }
        .other_info{
            color: #888;
            font-size: 26upx;
        }
        .del{
            font-size: 26upx;
            padding: 5upx 12upx;
            border-radius: 12upx;
            border: 1upx solid #f44;
            color: #f44
        }
    }
}
.apply-box{
    height:100vh;
    width:100%;
    /* #ifdef H5 */
    padding-top: 90upx;
    /* #endif */
    background-color:#fff;
    .row{
        padding:24upx 30upx;
    }
    .list-item{
        display: flex;
        align-items: center;
        padding: $uni-spacing-col-lg $uni-font-size-lg;
        radio{
            padding: 20upx 30upx;
            margin-left: -30upx;
        }
        .list-title{
            font-size: $uni-font-size-lg;
            text-overflow:ellipsis;
            white-space:nowrap;
            line-height:1.5;
            overflow:hidden;
        }
    }
    .upgrade-list{
        padding: 10upx;
        flex-wrap: wrap;
    }
    .upgrade-item{
        min-width: 40%;
        width:45%;
        padding: 20upx;
        margin: 10upx;
        text-align: center;
        border-radius: 10upx;
        background-color: #00c07b;
        color: #fff;
    }
}
</style>
