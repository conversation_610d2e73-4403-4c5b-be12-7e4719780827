<template>
  <div class="tabbar" :style="{ backgroundColor: tabBar.backgroundColor }">
    <div class="tab" v-for="(item, index) in tabBar.list" :key="index" @click="toPage(item)">
      <image class="icon" :src="pagePath === item.pagePath ? item.image_checked : item.image_unchecked"></image>
      <div class="text" :style="{ color: pagePath === item.pagePath ? item.selectedColor : item.color }">
        {{ item.text }}
      </div>
    </div>
  </div>
</template>

<script>
import { formatImg } from "../../common/index";
export default {
  name: "TabBar",
  components: {},
  data() {
    return {
      tabBar: {
        color: "",
        selectedColor: "",
        borderStyle: "white",
        backgroundColor: "#FFFFFF",
        list: [],
        // list: [
        //   {
        //     pagePath: "/exhibition/index",
        //     text: "首页",
        //     iconPath: "/exhibition/icon/index.png",
        //     selectedIconPath: "/exhibition/icon/index_active.png",
        //   },
        //   {
        //     pagePath: "/exhibition/introduce",
        //     text: "介绍",
        //     iconPath: "/exhibition/icon/introduce.png",
        //     selectedIconPath: "/exhibition/icon/introduce_active.png",
        //   },
        //   {
        //     pagePath: "/exhibition/dynamic",
        //     text: "动态",
        //     iconPath: "/exhibition/icon/dynamic.png",
        //     selectedIconPath: "/exhibition/icon/dynamic_active.png",
        //   },
        //   {
        //     pagePath: "/exhibition/brand",
        //     text: "品牌馆",
        //     iconPath: "/exhibition/icon/brand.png",
        //     selectedIconPath: "/exhibition/icon/brand_active.png",
        //   },
        // ],
      },
    };
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    query:{
      type: String,
      default: ''
    },
    pagePath: {
      type: String,
      default: '',
    },
  },
  created() {
    this.$ajax.get("buildShow/meetingNav", { meeting_id: this.id }, (res) => {
      this.tabBar.list = res.data.nav
      this.tabBar.list = this.tabBar.list.map((item) => {
        item.image_unchecked = formatImg(item.image_unchecked, "w_80");
        item.image_checked = formatImg(item.image_checked, "w_80");
        if (item.pagePath != '/exhibition/index' && item.pagePath == this.pagePath) {
          uni.setNavigationBarTitle({
            title: item.text
          })
        }
        item.selectedColor = item.selectedColor || "#FF5600";
        item.color = item.color || "#ffffff";
        return item;
      });
    })
  },
  methods: {
    toPage(tab) {
      if (tab.url) {
        this.$navigateTo(tab.url)
      } else {
        this.$navigateTo(tab.pagePath+this.query);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.tabbar {
  height: 50px;
  width: 100%;
  display: flex;
  flex-direction: row;
  position: sticky;
  bottom: 0;
  z-index: 998;
  > .tab {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    > .icon {
      margin-top: 5px;
      width: 24px;
      height: 24px;
    }
    > .text {
      margin-top: 3px;
      margin-bottom: 2px;
      line-height: normal;
      font-size: 11px;
    }
  }
}
</style>
