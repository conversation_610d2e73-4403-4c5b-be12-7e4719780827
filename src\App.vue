<script>
import { showModal } from "./common/index"
// #ifdef H5
import { versionCode } from '../package.json'
// #endif
import redirect from './common/utils/redirect'
export default {
	onLaunch: function (options) {
		console.log('App Launch')
		console.log(options)
		// 解决微信授权登录返回后浏览器使用以前缓存的页面造成报错的问题
		if (uni.getStorageSync('newLogin')) {
			// uni.setStorageSync('reload_time', Date.parse(new Date())+'')
			uni.removeStorageSync('newLogin')
			location.reload();
			return
		}
		uni.removeStorageSync('headerFrom')
		if (options.query && options.query.headerFrom && options.query.token && options.path !== 'redEnvelopes/index') {
			uni.setStorageSync('headerFrom', options.query.headerFrom)
			uni.setStorageSync('token', options.query.token)
		}
		if (process.env.NODE_ENV !== 'development') {
			var ua = window.navigator.userAgent.toLowerCase();
			if (ua.match(/Mobile/i)) {
				this.$store.state.in_mobile = true
			} else {
				this.$store.state.in_mobile = false
			}
			if (ua.match(/MicroMessenger/i) != 'micromessenger') {
				window.location = location.pathname.replace('/h5/', '/m/') + location.search
				return
			}
		}
		redirect(options)
		// 如果没有存储的版本号则要存储下
		if (versionCode && !uni.getStorageSync('tfy_versionCode')) {
			uni.setStorageSync('tfy_versionCode', versionCode)
		}
		// 程序的版本号小于本地存储的版本号说明访问的是缓存页面，需要重载页面
		if (versionCode && (versionCode < uni.getStorageSync('tfy_versionCode'))) {
			location.reload();
			return
		}
		// 如果程序的版本号大于本地存储的版本号，重新存储下版本号
		if (versionCode && (versionCode > uni.getStorageSync('tfy_versionCode'))) {
			uni.setStorageSync('tfy_versionCode', versionCode)
			return
		}
		this.$store.state.systemInfo = uni.getSystemInfoSync()
		if (options.path !== 'user/login/login') {
			this.getSetting()
		}
		this.checkLoginStatus()
		// this.getUnReadCount()
		this.getUpdata()
		// #ifdef APP-PLUS || MP
		uni.onNetworkStatusChange((res) => {
			// console.log(res.isConnected);
			// console.log(res.networkType);
			if (res.isConnected && !this.$store.state.has_setting) {
				// console.log("网络连接成功，重新获取setting")
				this.getSetting()
			}
		});
		// #endif
	},
	onShow: function () {
		// 解决微信授权登录返回后浏览器使用以前缓存的页面造成报错的问题
		// if(uni.getStorageSync('newLogin')){
		// 	uni.removeStorageSync('newLogin')
		// 	location.reload();
		// }
	},
	onHide: function () {
		// console.log('App Hide')
	},
	onPageNotFound: function (res) {// 目前版本有Bug，需要使用一个数组函数，后续版本不需要定义为数组
		let r = redirect(res)
		if (r) {
			return
		}
		uni.switchTab({
			url: '/pages/index/index'
		})
	},
	methods: {
		getSetting() {
			// 记录获取了setting
			this.$store.state.has_setting = true
			this.$ajax.get("index/setting", {}, res => {
				if (res.data.code == 1) {
					// #ifdef H5
					// 友盟统计
					if (res.data.um_h5_app_key) {
						(function (w, d, s, q, i) {
							w[q] = w[q] || [];
							var f = d.getElementsByTagName(s)[0], j = d.createElement(s);
							j.async = true;
							j.id = 'beacon-aplus';
							j.src = 'https://d.alicdn.com/alilog/mlog/aplus/' + i + '.js';
							f.parentNode.insertBefore(j, f);
						})(window, document, 'script', 'aplus_queue', '203467608');
						aplus_queue.push({
							action: 'aplus.setMetaInfo',
							arguments: ['appKey', res.data.um_h5_app_key]
						});
						//是否需要关闭自动PV采集的能力，指定MAN为关闭自动PV
						//注意：对于单页应用，强烈建议您关闭自动PV, 手动控制PV事件的发送时机
						aplus_queue.push({
							action: 'aplus.setMetaInfo',
							arguments: ['aplus-waiting', 'MAN']
						});
						//是否开启调试模式 
						// aplus_queue.push({
						// 	action: 'aplus.setMetaInfo',
						// 	arguments: ['DEBUG', true]
						// });
						setTimeout(() => {
							aplus_queue.push({
								action: 'aplus.sendPV',
								arguments: [{ is_auto: false }, {}]
							});
						}, 300)
					}
					// #endif
					//是否开启审核模式
					this.$store.state.audit_mode = res.data.audit_mode || 0
					// 是否开启首页新闻
					if (res.data.synewsisopen === '1') {
						this.$store.state.home_switchs.open_news = true
					}
					// 是否开启首页楼盘
					if (res.data.sylpisopen === '1') {
						this.$store.state.home_switchs.open_newhouse = true
					}
					// 是否开启首页二手房和出租房
					if (res.data.syershouisopen === '1') {
						this.$store.state.home_switchs.open_info = true
					}
					// 是否开启首页经纪人
					if (res.data.zhongjieisopen === '1') {
						this.$store.state.home_switchs.open_agent = true
					}
					// 是否开启首页置业顾问入驻显示
					if (res.data.allow_advsier_register === '1') {
						this.$store.state.home_switchs.allow_advsier_register = true
					}
					if (res.data.adviserisopen === '1') {
						this.$store.state.home_switchs.open_advsiver = true
					}
					if (res.data.tel400xing === '1') {
						this.$store.state.tel400jing = true
					}
					this.$store.state.loginByBaiduUnion = res.data.loginByBaiduUnion || 0  //百度联合登录状态
					this.$store.state.imgSize = res.data.maxUploadSize
					this.$store.state.statistics = res.data.statistics
					this.$store.state.im.platform = res.data.platform
					this.$store.state.is_open_share_douyin = res.data.is_open_share_douyin || 0  //发送到抖音
					this.$store.state.house_detail_trends = res.data.house_detail_trends//房源动态显示
					if (res.data.ter_adviser == 1 && res.data.site_adviser == 1) {  //是否开启置业顾问
						this.$store.state.im.adviser = 1   //开启
					} else {
						this.$store.state.im.adviser = 0   //关闭
					}
					if (res.data.ter_ischat == 1 && res.data.site_chat == 1) { //是否开启聊天
						this.$store.state.im.ischat = 1     //开启
					} else {
						this.$store.state.im.ischat = 0     //关闭
					}

					this.$store.state.codeLogin = res.data.loginByVerifyCode; //是否开启验证码登录
					// this.$store.state.im.adviser = res.data.ter_advsier
					// this.$store.state.im.ischat = res.data.ter_ischat
					this.$store.state.use_middle_call_house = res.data.use_middle_call_house  //1：个人和中间信息都开启隐私号 2：中介信息开启隐私号 3：个人信息开启隐私号
					this.$store.state.im.istelcall = res.data.ter_telcall  //是否开启中间号  1开  0 关闭
					this.$store.state.im.isNav = res.data.loupannav  //楼盘nav 开启状态 1 开启  0 不开启
					this.$store.state.styles = res.data.adviser_config  // 置业顾问的背景样式
					this.$store.state.shareStyles = res.data.adviserShare // 置业顾问的海报背景颜色
					this.$store.state.agentStyles = res.data.agent_config
					this.$store.state.adv_show_label = res.data.adv_show_label//是否开启广告
					this.$store.state.code_status = res.data.code_status//是否开启注册和找密码
					this.$store.state.siteName = res.data.siteName || ''
					this.$store.state.siteTel = res.data.SiteTel
					this.$store.state.isShowTaoshu = parseInt(res.data.isopenzhuzhai)  //预售是否显示住宅 非住宅套数
					this.$store.state.is_subscribe = res.data.is_subscribe // 是否订阅消息
					this.$store.state.unread_templateid_xcx = [res.data.unread_templateid_xcx]
					// 需要订阅的消息id
					this.$store.state.topicSkin = res.data.topicSkin   //专题模板样式
					//   this.$store.state.topicSkin = 1   //专题模板样式
					this.$store.state.ter_isretail = res.data.ter_isretail //是否开启分销报功能
					this.$store.state.sub_form_mode = parseInt(res.data.submit_sign_method || 0)
					this.$store.state.vrlink_ischeck = res.data.vrlink_ischeck
					this.$store.state.weapp_appid = res.data.xcxappidgh
					// iosapp 一键登录图标
					this.$store.state.appIcon = res.data.app_icon
					this.$store.state.switch_community_expert = res.data.switch_community_expert
					this.$store.state.wxqunopen = res.data.wxqunopen
					this.$store.state.if_info_verification_code = res.data.if_info_verification_code  //是否开启房源统一核验
					this.$store.state.zhuaqu_js = res.data.js  // 湛江的抓取js
					this.$store.state.switch_adviser_tel = res.data.switch_adviser_tel  //是否显示置业顾问的电话
					// 广东新房客服链接
					this.$store.state.codekefu = res.data['code53kf']
					// 广东二手房客服链接
					this.$store.state.ershou_statistics = res.data['code53kf_esf']
					this.$store.state.SiteCity = res.data.SiteCity  //城市
					this.$store.state.wx_service_link = res.data.wx_service_link  //客服链接
					if (res.data.isOpenGrayscale == 1) {  //首页变灰字段
						this.$store.state.huiFilter = "grayscale(0.95)"
					}
				} else {
					this.$store.state.has_setting = false
				}
			}, err => {
				this.$store.state.has_setting = false
				console.log(JSON.stringify(err))
			})
		},
		// 检测用户登录状态
		checkLoginStatus() {
			this.$ajax.get('member/checkUserStatus', {}, res => {
				this.$store.state.user_login_status = res.data.status || ''
			})
		},
		getUpdata() {
			// #ifdef MP
			const updateManager = uni.getUpdateManager()
			updateManager.onCheckForUpdate(function (res) {
				// 请求完新版本信息的回调
				console.log(res.hasUpdate)
			})
			updateManager.onUpdateReady(function () {
				showModal({
					title: '更新提示',
					content: '新版本已经准备好，是否重启应用？',
					confirm: () => {
						// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
						updateManager.applyUpdate()
					}
				})
			})
			updateManager.onUpdateFailed(function (err) {
				// 新版本下载失败
				uni.showToast({
					title: "新版本下载失败",
					icon: "none"
				})
				console.log(err)
			})
			// #endif
			// #ifdef APP-PLUS
			switch (uni.getSystemInfoSync().platform) {
				case "android":
					console.log("运行在安卓");
					var server = "index/downloadApp.html"; //检查更新地址
					var req = { //升级检测数据
						"appid": plus.runtime.appid,
						"version": plus.runtime.version
					};
					this.$ajax.get(server, req, res => {
						if (res.data.code === 1) {
							uni.showModal({ //提醒用户更新
								title: "更新提示",
								content: res.data.content,
								success: (res2) => {
									if (res2.confirm) {
										// plus.runtime.openURL(res.data.url);
										uni.showLoading({
											title: "正在下载更新...",
											mask: true
										})
										uni.downloadFile({
											url: res.data.url,
											success: res3 => {
												console.log(res3.tempFilePath)
												plus.runtime.install(res3.tempFilePath, { force: true }, function onSuccess(widgetInfo) {
													uni.hideLoading()
													uni.showToast({
														title: "安装成功"
													})
												}, function onError(error) {
													uni.hideLoading()
													uni.showToast({
														title: "安装失败",
														icon: "none"
													})
													console.log("安装失败：", error)
												});
											},
											fail: err => {
												console.log("下载失败：", err)
											}
										})
										// plus.runtime.install(filePath, options, installSuccessCB, installErrorCB);
									} else {
										console.log("用户点击取消")
										// uni.setStorageSync('no_update', Date.parse(new Date()))
									}
								}
							})
						}
					})
					break;
				case "ios":
					console.log("运行在iOS");
					break;
				default:
					console.log("运行在开发工具");
					break;
			}
			// #endif
		},
		// 获取未读消息条数
		getUnReadCount() {
			// let startTime = new Date().getTime()
			let timer = setInterval(() => {
				if (!uni.getStorageSync('token')) {
					console.log("未登录")
					return
				}
				this.$ajax.get('im/unRead', {}, res => {
					// let nowTime = new Date()
					// console.log("time:",nowTime.getTime()-startTime)
					// console.log(nowTime.getSeconds())
					// startTime = nowTime
					if (res.data.uncount) {
						uni.$emit('showChatTip', res.data.uncount)
					}
				}, err => {
					console.log(err)
				}, { disableAutoHandle: true })
			}, 60000)
		}
	}
}
</script>

<style lang="scss">
/*每个页面公共css */
page {
	background-color: $uni-bg-color-grey;
}

body {
	font-family: PingFangSC-Regular;
}

.white-bg {
	background-color: #fff;
}

/*隐藏滚动条*/
::-webkit-scrollbar {
	width: 0;
	height: 0;
	color: transparent;
}

.tfy-icon {
	display: inline-block;
	width: 38rpx;
	height: 38rpx;
	background-repeat: no-repeat;
	background-position: center;
	background-size: 100%;
}

.uni-page-head__title {
	user-select: none;
}

.content {
	background-color: $uni-bg-color-grey;

	p {
		margin-bottom: 20rpx;
	}

	img {
		max-width: 100%;
	}
}

view {
	font-size: $uni-font-size-base;
	word-break: break-all;
	// background-color: #ffffff;
}

.flex-row {
	flex-direction: row;
}

checkbox {
	transform: scale(0.8)
}

radio {
	transform: scale(0.8)
}

.navigator-hover {
	background-color: $uni-bg-color-hover
}

.img-sm {
	width: $uni-img-size-sm;
	height: $uni-img-size-sm;
}

.img-base {
	width: $uni-img-size-base;
	height: $uni-img-size-base;
}

.img-lg {
	width: $uni-img-size-lg;
	height: $uni-img-size-lg;
}

.font-sm {
	font-size: $uni-font-size-sm
}

.font-base {
	font-size: $uni-font-size-base
}

.font-lg {
	font-size: $uni-font-size-blg
}

.c-right {
	float: right;
}

.c-left {
	float: left;
}

.top-20 {
	margin-top: $uni-spacing-row-base;
}

.padding-20 {
	margin: 20upx
}

.text-center {
	text-align: center;
}

.flex-box {
	display: flex;
}

.flex-center {
	align-items: center;
}

.flex-1 {
	flex: 1;
}

.flex-2 {
	flex: 2;
}

.flex-3 {
	flex: 3;
}

.flex-4 {
	flex: 4;
}

.flex-5 {
	flex: 5;
}

.inp-box-def {
	height: 65upx;
}

.list-img {
	width: 240upx;
	height: 190upx;
	margin-right: 18upx;
	position: relative;
}

.list-img image {
	width: 100%;
	height: 100%;
}

a {
	color: #333333;
	text-decoration: none
}

.list-img image.video-icon {
	width: 72upx;
	height: 72upx;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
	border-radius: 50%;
	background-color: rgba($color: #000000, $alpha: 0)
}

.row {
	font-size: $uni-font-size-base;
	background: $uni-bg-color;
	padding: $uni-spacing-col-sm $uni-spacing-row-base;
}

.row .label {
	padding: 0 10upx;
	margin-right: 10upx;
	color: #666;
}

.form-row {
	line-height: 60upx;
	position: relative;
	padding: $uni-spacing-col-base $uni-spacing-row-base;
}

.form-row::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 1px;
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	background-color: $uni-border-color;
}

.form-row.flex-box {
	align-items: center;
}

.form-row .label {
	width: 120upx;
	height: 60upx;
}

.form-row input {
	height: 60upx;
}

.bottom-line,
.top-line,
.left-line,
.right-line {
	position: relative;
}

.bottom-line:after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 1px;
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	background-color: $uni-border-color;
}

.top-line:before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 1px;
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	background-color: $uni-border-color;
}

.left-line:before {
	content: "";
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	width: 1px;
	-webkit-transform: scaleX(.5);
	transform: scaleX(.5);
	background-color: $uni-border-color;
}

.right-line:after {
	content: "";
	position: absolute;
	top: 0;
	bottom: 0;
	right: 0;
	width: 1px;
	-webkit-transform: scaleX(.5);
	transform: scaleX(.5);
	background-color: $uni-border-color;
}

.grid-box {
	display: flex
}

.grid-item {
	flex: 1;
	padding: 20upx;
	text-align: center
}

.grid-item image {
	width: 36%;
	height: inherit;
}

.grid-item .text {
	font-size: $uni-font-size-sm;
	color: $uni-text-color-grey;
}

cover-view {
	line-height: inherit
}

.bottom-bar {
	width: 100%;
	position: fixed;
	bottom: 0;
	height: 90upx;
	line-height: 90upx;
	z-index: 99
}

a {
	color: #333333;
	text-decoration: none;
}


// form表单
.btn-box {
	margin-top: 20upx;
	padding: 24upx;
}

uni-button[type=primary] {
	background-color: $uni-color-primary;
}

.button-hover[type=primary] {
	background-color: rgba($color: $uni-color-primary, $alpha: 0.86);
}

.btn-box button.default {
	background-color: $uni-color-primary;
	color: #fff;
}

.btn-box button.default.btn-hover {
	background-color: rgba($color: $uni-color-primary, $alpha: 0.86);
}

.btn-box button.small {
	display: inline-block;
	padding-left: 24upx;
	padding-right: 24upx;
	line-height: 2;
	background-color: $uni-color-primary;
	color: #fff;
	font-size: $uni-font-size-base
}

.btn-box button.small.plain {
	background-color: #fff;
	border: 1upx solid $uni-color-primary;
	color: $uni-color-primary;
}

.btn-box button.base {
	display: inline-block;
	line-height: 2.5;
	width: 45%;
	background-color: $uni-color-primary;
	color: #fff;
	font-size: $uni-font-size-lg
}

button::after {
	border: none;
}

button[size=medium] {
	line-height: 80upx;
	font-size: $uni-font-size-blg;
	width: 45%;
}

// 筛选模块
.screen-tab {
	position: fixed;
	width: 100%;
	height: 80upx;
	line-height: 80upx;
	top: 0;
	background-color: #fff;
	border-bottom: 1upx solid $uni-border-color;
	// box-shadow: 0 0 18upx #dedede;
	z-index: 99
}

.screen-tab-item {
	display: flex;
	justify-content: center;
	align-items: center;
}

.screen-tab-item text {
	display: inline-block;
	max-width: 80%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap
}

.screen-panel-box {
	position: absolute;
	// top: 80upx;
	width: 100%;
	background-color: #fff;
}

.screen-panel {
	position: fixed;
	top: 80upx;
	width: 100%;
	max-height: 50vh;
	overflow-x: hidden;
	background-color: #fff;
	z-index: 98;
	transform: translateY(-101%);
	transition: 0.2s;
}

.screen-panel.more-panel {
	max-height: 60vh;
}

.screen-panel.show {
	transform: translateY(0);
}

.mask {
	position: fixed;
	top: 0;
	bottom: 0;
	width: 100%;
	background-color: rgba($color: #000000, $alpha: 0);
	z-index: -1;
	transition: 0.2s
}

.mask.show {
	background-color: rgba($color: #000000, $alpha: 0.36);
	z-index: 90;
}

.p-top-80 {
	padding-top: 80upx
}



::v-deep uni-modal .uni-modal {
	border-radius: 16rpx;
}



// 列表样式
.more-screen-item {
	padding: $uni-spacing-col-base $uni-spacing-row-base;
}

.more-screen-item .title {
	padding: 10upx;
	font-size: 26upx;
	color: $uni-text-color;
}

.more-screen-item .options {
	flex-wrap: wrap;
}

.more-screen-item .options .options-item {
	width: 22%;
	margin: 1.5%;
	text-align: center;
	padding: 14upx 10upx;
	border: 1upx solid #f3f3f3;
	box-sizing: border-box;
	border-radius: 6upx;
	font-size: $uni-font-size-sm;
	color: #666;
	background-color: #f3f3f3
}

.more-screen-item .options .options-item.active {
	background-color: #fff;
	color: $uni-color-success;
	border: 1upx solid $uni-color-success
}

.list-item {
	background-color: #fff;
	padding: 30upx $uni-spacing-row-base;
	/* #ifdef H5 */
	/* border-bottom: 1px solid #dedede; */
	/* #endif */
}

.list-info .info-title {
	font-size: $uni-font-size-blg;
	// font-weight: bold;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	display: -webkit-box;
	color: #555;
}

.house.list-info .info-title {
	-webkit-line-clamp: 1;

}

.list-info .info-content {
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	display: -webkit-box;
	margin-bottom: 20upx;
	font-size: 22upx;
	margin-top: 10upx;
	color: #666;
}

.list-info .info-price {
	margin-top: 10upx;
}

.list-info .total {
	font-size: $uni-font-size-base;
	font-weight: bold;
	color: #f65354;
	margin-right: 10upx;
	float: right;
}

.list-info .average {
	margin-right: 10upx;
	font-size: 22upx;
	color: #666;
}

.list-info .acreage {
	font-size: 22upx;
	color: #666;
}

.list-info .label {
	margin-top: 10upx;
	font-size: $uni-font-size-sm;
}

.list-info .label.renting {
	margin-top: 20upx;
}

.list-info .label text {
	padding: 1upx 6upx;
	margin: 0 4upx;
	border-radius: 2upx;
	color: #666;
	background-color: #f3f3f3;
}

.list-info .label .area {
	background-color: #ffffff;
}

.list-info .label text.state {
	color: #fff;
	background-color: #f65354
}

.list-info .label .state1 {
	color: #fff;
	background-color: #17bfff
}

.list-info .label .state2 {
	color: #fff;
	background-color: #70d298
}

.list-info .label .state3 {
	color: #fff;
	background-color: #ff7213
}

.list-info .label .state4 {
	color: #666;
	background-color: #f3f3f3
}

.list-info .label.renting .attr1 {
	padding: 1upx 8upx;
	border-radius: 6upx;
	margin-right: 10upx;
	background-color: #70d298;
	color: #FFFFFF;
}

.list-info .label.renting .attr2 {
	padding: 1upx 8upx;
	border-radius: 6upx;
	margin-right: 10upx;
	background-color: #17bfff;
	color: #FFFFFF;
}

.house .label text {
	background: #fff;
	margin: 0;
}

.article-content img {
	max-width: 100%;
	vertical-align: bottom;
	margin-bottom: 20upx;
}

.article-content a {
	color: $uni-color-primary;
	text-decoration-line: underline;
}

.group-content img {
	max-width: 100%;
	vertical-align: bottom;
}

.activity-content img {
	max-width: 100%;
}

table {
	margin: auto;
	border-collapse: collapse;

	th {
		font-weight: bold;
		padding: 0 16rpx;
		border: 1px solid #dedede;
		text-indent: 0;
	}

	td {
		padding: 0 16rpx;
		border: 1px solid #dedede;
		text-indent: 0;
	}
}

// #ifdef H5
.article-content ::v-deep {
	.component-loupan-card {
		background: #fff;
		padding: 0 15px;
	}

	.module-news-content-new {
		display: none;

		&.mobile {
			display: block;
			border: 1px solid #e5e5e5;
			border-radius: 5px;
			margin-bottom: 15px;

			&.border0 {
				border: 0;
			}
		}
	}

	.component-loupan-card .lp-img-text .info-main .tag {
		margin-right: 10px;
		display: inline-block;
		background-color: rgb(245, 245, 246);
		color: rgb(159, 159, 166);
		height: 12px;
		line-height: 12px;
		font-size: 12px;
		vertical-align: top;
		padding: 4px;
		border-radius: 2px;
	}

	.tudi-info {
		padding: 10px 10px 0 10px;
		// background: #fff;
		// border: 1px solid #d8d8d8;
		// box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
		// border-radius: 8px;
		position: relative;
		// margin: 2px 2px 8px 2px;
	}

	.tudi-info .tudi-info-item {
		/* margin: 6px 0; */
		font-size: 14px;
		color: #666;
	}

	.mini_tudi-info-item {
		display: none !important;
	}

	.tudi-info .h5_tudi-info-item {
		font-size: 13px;
		text-decoration: none;
	}

	.tudi-info .tudi-name {
		display: block;
		color: #333;
		font-size: 16px;
	}

	.tudi-info .tudi-weizhi {
		margin: 6px 0;
		display: block;
		color: #999;
	}

	.tudi-info .tudi-danwei {
		display: block;
		color: #999;
	}

	.tudi-info-bottom {
		display: flex;
		flex-wrap: nowrap;
		align-items: center;
		margin-top: 20px;
		text-decoration: none;
	}

	.tudi-info-bottom-item {
		// display: inline-block;
		flex: 1;
		font-size: 12px;
		text-align: center;
		// width: 32%;
		margin-bottom: 10px;
	}

	.model-tudi-line {
		display: inline-block;
		height: 30px;
		width: 1px;
		background-color: #d8d8d8;
		line-height: 30px;
	}

	.tudi-info-top .tudi-name-hprq {
		font-size: 12px;
		margin-left: 2px;
	}

	.tudi-info-bottom-item-top {
		text-align: center;
	}

	.tudi-info-bottom-item-top-item {
		font-size: 12px;
		color: #999;
	}

	.tudi-info-bottom-item-top-num {
		font-size: 18px;
		color: #FF5C6A;
	}

	.tudi-info-bottom-item-bottom {
		display: block;
		text-align: center;
		color: #999;
	}

	.component-loupan-card .lp-img-text {
		padding-bottom: 10px;
		padding-top: 15px;
		position: relative;
	}

	@media (-webkit-device-pixel-ratio: 3) {
		.component-loupan-card .lp-img-text:after {
			transform: scaleY(0.3333);
		}
	}

	@media (-webkit-min-device-pixel-ratio: 1.5),
	not all,
	(min-resolution: 1.5dppx),
	(min-resolution: 144dpi) {
		.component-loupan-card .lp-img-text:before {
			transform: scaleY(0.5);
		}

		.component-loupan-card .lp-card-footer:after {
			transform: scaleY(0.5);
		}

		.component-loupan-card .lp-img-text:after {
			transform: scaleY(0.5);
		}
	}

	// .component-loupan-card .lp-img-text:before {
	//     pointer-events: none;
	//     position: absolute;
	//     content: "";
	//     height: 1px;
	//     background: #e5e5e5;
	//     left: 0;
	//     right: 0;
	//     top: 0;
	//     -webkit-transform-origin: 0 0;
	//     transform-origin: 0 0;
	// }
	// .component-loupan-card .lp-img-text:after {
	// 		pointer-events: none;
	// 		position: absolute;
	// 		content: "";
	// 		height: 1px;
	// 		background: #e5e5e5;
	// 		left: 0;
	// 		right: 0;
	// 		bottom: 0;
	// 		-webkit-transform-origin: 100% 100%;
	// 		transform-origin: 100% 100%;
	// }
	.component-loupan-card .building-info-img {
		position: relative;
		background-size: cover;
		background-repeat: no-repeat;

		.lp-img~a {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 5px 10px;
			background: rgba(0, 0, 0, 0.1);
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}

	.component-loupan-card .lp-img-text .lp-img {
		position: relative;
		display: block;
		padding: 25% 50%;

		&.mini_a_lp_img {
			display: none;
		}
	}

	.component-loupan-card .lp-img-text .lp-img img {
		display: block;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 2px;
	}

	.component-loupan-card .lp-card-footer .text-content,
	.component-loupan-card .lp-img-text .lp-img .loupan-title-wrap,
	.component-loupan-card .lp-img-text .lp-img .loupan-title-wrap .loupan-title,
	.component-loupan-card .lp-img-text .info-item,
	.component-loupan-card .lp-img-text .info-item .address {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.component-loupan-card .lp-img-text .lp-img .loupan-title-wrap {
		position: absolute;
		bottom: 5px;
		left: 10px;
		z-index: 3;
		font-size: 0;
		line-height: 27px;
		color: #fff;
		max-width: 100%;

		&.mini_title_wrap {
			display: none;
		}
	}

	.component-loupan-card .lp-img-text .lp-img .loupan-title-wrap .loupan-title {
		display: inline-block;
		vertical-align: middle;
		font-size: 18px;
		padding-right: 92px;

		max-width: 100%;
		box-sizing: border-box;
	}

	.component-loupan-card a {
		color: #fff;
		text-decoration: none;
	}

	.component-loupan-card .lp-img-text .info-main {
		width: 100%;
		margin-top: 10px;
		font-size: 0;
		position: relative;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.component-loupan-card .lp-img-text .info-main .h5_baoming_link {
		font-size: 14px;
		color: #f22;
		padding: 1px 10px;
		position: absolute;
		top: 1px;
		right: 5px;
		border: 1px solid #f22;
		border-radius: 5px;
	}

	.component-loupan-card .lp-img-text .info-main .mini_baoming_link {
		display: none;
	}

	.component-loupan-card .lp-img-text .info-main .price {
		padding: 5px 0;
	}

	.component-loupan-card .lp-img-text .info-main .price {
		font-size: 16px;
		color: #f22;
		line-height: 24px;
		margin-right: 10px;
	}

	.component-loupan-card .lp-img-text .info-main span {
		display: inline-block;
		vertical-align: middle;
	}

	.component-loupan-card .lp-img-text p {
		margin-bottom: 0 !important;
	}

	.component-loupan-card .lp-img-text .info-main .global-tag {
		margin-right: 5px;
	}

	.global-tag {
		display: inline-block;
		padding: 0 4px;
		line-height: 16px;
		font-size: 12px;
		color: #9f9fa6;
		border-radius: 2px;
		position: relative;
	}

	.global-tag-feature {
		background-color: #f5f5f6;
	}

	.global-tag1 {
		display: inline-block;
		padding: 0 4px;
		line-height: 16px;
		font-size: 12px;
		color: #fff;
		border-radius: 2px;
		background-color: #ac9;
		position: relative;
	}

	.component-loupan-card .lp-img-text .info-item {
		color: #9f9fa6;
		margin-top: 3px;
		line-height: 16px;
		height: 16px;
		font-size: 0;
		max-width: 100%;
		margin-right: 0;
	}

	.component-loupan-card .lp-img-text .info-item span {
		font-size: 12px;
		display: inline-block;
		vertical-align: middle;
	}

	// .component-loupan-card .lp-img-text .info-item .layout {
	// 	margin-right: 10px;
	// }

	.flex {
		display: -webkit-box !important;
		display: -webkit-flex !important;
		display: -ms-flexbox !important;
		display: flex !important;
		-webkit-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
	}

	.flex>.cell {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		-ms-flex: 1;
		flex: 1;
		width: 0;
		-webkit-flex-basis: 0;
		-ms-flex-preferred-size: 0;
		flex-basis: 0;
		max-width: 100%;
		display: block;
		padding: 0 !important;
		position: relative;
	}

	.component-loupan-card .lp-img-text .tel-wrap {
		display: inline-block;
		padding: 4px 10px;
		font-size: 0;
		border-radius: 13px;
		color: #fff;
		line-height: 18px;
		position: absolute;
		bottom: 12px;
		right: 0;
		z-index: 3;
		background: -webkit-linear-gradient(left, #f66, #f22);
		background: linear-gradient(90deg, #f66, #f22);
	}

	.component-loupan-card .lp-img-text .tel-wrap .iconfont {
		vertical-align: middle;
		margin-right: 5px;
		font-family: icon2018 !important;
		font-size: 14px;
		font-style: normal;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}

	.component-loupan-card .lp-img-text .tel-wrap .tel {
		vertical-align: middle;
		font-size: 12px;
	}

	.component-loupan-card .lp-card-footer {
		padding-top: 15px;
		padding-bottom: 15px;
		font-size: 0;
		position: relative;
		height: 22px;
	}

	.component-loupan-card .lp-card-footer:after {
		pointer-events: none;
		position: absolute;
		content: "";
		height: 1px;
		background: #e5e5e5;
		left: 0;
		right: 0;
		bottom: 0;
		-webkit-transform-origin: 100% 100%;
		transform-origin: 100% 100%;
	}

	.component-loupan-card .lp-card-footer .text-content {
		font-size: 14px;
		color: #9f9fa6;
		display: block;
		height: 22px;
		line-height: 22px;
	}

	.component-loupan-card .lp-card-footer .link {
		display: block;
		font-size: 14px;
		color: #f22;
		margin-left: 10px;
		margin-right: 5px;
		height: 22px;
		line-height: 22px;

		&.mini_link {
			display: none;
		}
	}
}

//  #endif</style>
