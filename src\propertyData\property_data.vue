<template>
	<view class="data_page">
		<view class="tipStick" :style="{ top: stickTop }">
			<tab-bar
				:tabs="navs"
				ref="tab"
				:fixedTop="false"
				:nowIndex="currentIndex"
				@click="handelCate"
			>
			</tab-bar>
		</view>
		<view v-if ='show_content'>
		<view class="data_content" v-if ="currentName=='real_estate_market'">
			<!-- 新房 -->
			<view class="new_house_data" v-if ="showItems.xinfangshow==1">
				<view class="data_title">
					新房
				</view>
				<view class="data_content">
					<view class="data_content_top flex-row " v-if="xinfang.buildAvgPrice&&xinfang.buildAvgPrice.price>=0">
						<view class="data_content_top_left">
							<view class="data_content_top_left_box flex-box">
								<view class="data_content_top_left_box_title">
									新房均价
								</view>
								<view class="data_content_top_left_box_price flex-row">
									<text class="box_price">{{xinfang.buildAvgPrice.price}}</text>
									<text class="box_unit">元/m²</text>
									<block v-if ="xinfang.buildAvgPrice.last_month_status>0&&xinfang.buildAvgPrice.last_month_value>0">
									<view class="month_status flex-row" :class ="{'up': xinfang.buildAvgPrice.last_month_status==1,'down':xinfang.buildAvgPrice.last_month_status==2}">
										<image
										mode="widthFix" 
										:src="'/images/property/'+ (xinfang.buildAvgPrice.last_month_status==1?'up':'down')+'@2x.png' | imgUrl"
									></image>
										<text v-if="xinfang.buildAvgPrice.last_month_status==2">-</text>
										<text class="box_unit" >{{xinfang.buildAvgPrice.last_month_value}}%</text>
									</view>
									</block>
								</view>
							</view>
						</view>
						<view class="data_content_top_right">
							<image
								:src="'/images/property/<EMAIL>' | imgUrl"
								alt=""
								mode="widthFix"
							></image>
						</view>
					</view>
					<view class="data_content_bottom flex-row" v-if='xinfang.ysxk&&xinfang.ysxk.mianji'>
						<view class="data_content_bottom_left left_first  flex-1">
							<view class="data_content_top_left_box_title">
								住宅预售面积
							</view>
							<view class="data_content_top_left_box_price">
								<text class="box_unit_yue">约</text>
								<text class="box_price">{{xinfang.ysxk.mianji}}</text>
								<text class="box_unit">m²</text>
							</view>
						</view>
						<view class="data_content_bottom_left flex-1" v-if ="xinfang.tupai&&xinfang.tupai.mianji" >
							<view class="data_content_top_left_box_title">
								土地成交面积
							</view>
							<view class="data_content_top_left_box_price">
								<text class="box_unit_yue">约</text>
								<text class="box_price">{{xinfang.tupai.mianji}}</text>
								<text class="box_unit">亩</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 二手房 -->
			<view class="new_house_data" v-if ="showItems.ershoufangshow==1">
				<view class="data_title">
					二手房
				</view>
				<view class="data_content">
					<view class="data_content_top flex-row ">
						<view class="data_content_top_left">
							<view class="data_content_top_left_box flex-box">
								<view class="data_content_top_left_box_title">
									{{infoAvg.month}}参考均价
								</view>
								<view class="data_content_top_left_box_price">
									<text class="box_price">{{infoAvg.price}}</text>
									<text class="box_unit">元/m²</text>
								</view>
							</view>
						</view>
						<view class="data_content_top_left">
							<view class="data_content_top_left_box flex-box">
								<view class="data_content_top_left_box_title">
									比上月
								</view>
								<view class="data_content_top_left_box_price" :class='{"up":infoAvg.last_month_status==1,"down":infoAvg.last_month_status==2}'>
									<template v-if ="infoAvg.last_month_status>0">
									<image
										mode="widthFix" 
										:src="'/images/property/'+ (infoAvg.last_month_status==1?'up':'down')+'@2x.png' | imgUrl"
									></image>									
									<text class="box_price"><text v-if="infoAvg.last_month_status==2">-</text>{{infoAvg.last_month_value}}</text>
									<text class="box_unit">%</text>
									</template>
									<template v-else >
										<text class="box_unit">持平</text>
									</template>
								</view>
							</view>
						</view>
						<view class="data_content_top_left">
							<view class="data_content_top_left_box flex-box">
								<view class="data_content_top_left_box_title">
									比上年
								</view>
								<view class="data_content_top_left_box_price"  :class='{"up":infoAvg.last_year_status==1,"down":infoAvg.last_year_status==2}'>
									<template v-if ="infoAvg.last_year_status>0">
									<image
										mode="widthFix"
										:src="'/images/property/'+ (infoAvg.last_year_status==1?'up':'down')+'@2x.png' | imgUrl"
									></image>
									<text class="box_price"><text v-if="infoAvg.last_year_status==2">-</text>{{infoAvg.last_year_value}}</text>
									<text class="box_unit">%</text>
									</template>
									<template v-else >
										<text class="box_unit">持平</text>
									</template>
								</view>
							</view>
						</view>
					</view>
					<view class="data_content_bottom flex-row">
						<view class="data_content_bottom_left left_first  flex-1">
							<view class="data_content_top_left_box_title">
								二手房实时挂牌均价
							</view>
							<view class="data_content_top_left_box_price">
								<text class="box_unit_yue">约</text>
								<text class="box_price">{{infoAvg.avg_price}}</text>
								<text class="box_unit">元/m²</text>
							</view>
						</view>
						<view class="data_content_bottom_left flex-1">
							<view class="data_content_top_left_box_title">
								二手房挂牌数量
							</view>
							<view class="data_content_top_left_box_price">
								<text class="box_unit_yue">约</text>
								<text class="box_price">{{infoAvg.info_count}}</text>
								<text class="box_unit">套</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 租房 -->
			<view class="new_house_data" v-if ="showItems.zufangshow==1">
				<view class="data_title">
					出租房
				</view>
				<view class="data_content">
					<view class="data_content_top flex-row ">
						<view class="data_content_top_left">
							<view class="data_content_top_left_box flex-box">
								<view class="data_content_top_left_box_title">
									出租房参考均价
								</view>
								<view class="data_content_top_left_box_price">
									<text class="box_price">{{rentingAvg.price}}</text>
									<text class="box_unit">元/月</text>
								</view>
							</view>
						</view>
						<view class="data_content_top_right">
							<image
								:src="'/images/property/<EMAIL>' | imgUrl"
								alt=""
								mode="widthFix"
							></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="data_data">
            <template v-if="currentName=='real_estate_market' && filter_list.length>0">
                <view class="filter_list" v-show="this.currentName=='real_estate_market'">
                    <view
                        class="filter_item"
                        :class="{ active: index === current_filter_index }"
                        v-for="(item, index) in filter_list"
                        :key="index"
                        @click="onClickFilter(item, index)"
                        ><text>{{ item.name }}</text> <view class="down"></view
                    ></view>
                </view>
                <view class="filter_title flex-row" v-show="this.currentName=='real_estate_market'"> 
                    <view class="filter_title_left flex-row" >
                        <text class="filter_title_left_title"
                            >{{
                                current_filter_name == 'xinfang'? "新房" : "二手房挂牌"
                            }}均价走势</text
                        >
                        <!-- <text class="filter_title_left_subtitle">(全市)</text> -->
						
                    </view>
                    <view class="filter_title_right flex-row" >
                        <view
                            class="filter_date"
                            v-for="(item, index) in filter_date_list"
                            :key="index"
                            @click="onClickFilterDate(item, index)"
                            ><text  :class="{ active: index === current_filter_date_index }">{{ item.name }}</text>
                        </view>
                    </view>
                </view>
                <!-- <view class="filter_subtitle" v-show="this.currentName=='real_estate_market'">
                    均价{{current_filter_date_index==0?houseBuildPrice:ershouBuildPrice}}元/m²
                </view> -->
                <!-- 走势图   -->
                <view class="qiun-columns" >
					<view class="qiun-charts" v-show="this.currentName=='real_estate_market'">
						<canvas canvas-id="canvasLineA" id="canvasLineA" class="charts" @touchstart="touchLineA"></canvas>
					</view>
				</view>
                <!-- 列表 -->
                <view class="date_lists" >
                    <view class="date_list">
                        <template v-if="this.currentName=='real_estate_market'">
                            <template v-if="current_filter_name == 'ershoufang'">
                                <view class="data_title">
                                    区域二手房走势一览
                                </view>
                                <view class="data_ershou_list">
                                    <view
                                        class="data_ershou_list_item bottom-line flex-row"
                                        v-for="(item, index) in areaAvgPrice"
                                        :key="index"
                                    >
                                        <view class="data_ershou_list_item_left flex-row">
                                            <text
                                                class="zoushi_paiming"
                                                :class="index < 3 ? 'zoushi_paiming' + (index + 1) : ''"
                                                >{{ index + 1 }}</text
                                            >
                                            <text class="zoushi_area">{{ item.areaname }}</text>
                                        </view>
                                        <view class="data_ershou_list_item_middle flex-row">
                                            <text class="zoushi_price_unit">均价</text>
                                            <text class="zoushi_price">{{ item.price }}</text>
                                            <text class="zoushi_price_unit">元/m²</text>
                                        </view>
                                        <view
                                            class="data_ershou_list_item_right flex-row"
                                            :class="'data_ershou_list_item_right' + item.price_status"
                                        >
                                            <text class="zoushi_bijiao_unit">环比</text>
                                            <text class="zoushi_bijiao" v-if="item.price_status==1">{{ item.price_status_value}}%</text>
                                            <text class="zoushi_bijiao" v-if="item.price_status==2">-{{ item.price_status_value}}%</text>
                                            <text class="zoushi_bijiao" v-if="item.price_status==0">--</text>
                                            <!-- <text class="zoushi_bijiao">{{ item.price_status_value>0?item.price_status_value+'%':"--"}}</text> -->
                                            <template v-if="item.price_status_value == 1">
                                                <text class="zoushi_bijiao_img">
                                                    <image
                                                        :src="'/images/property/<EMAIL>' | imgUrl"
                                                        alt=""
                                                        mode="widthFix"
                                                    ></image>
                                                </text>
                                            </template>
                                            <template v-if="item.price_status_value == 2">
                                                <text class="zoushi_bijiao_img">
                                                    <image
                                                        :src="'/images/property/<EMAIL>' | imgUrl"
                                                        alt=""
                                                        mode="widthFix"
                                                    ></image>
                                                </text>
                                            </template>
                                        </view>
                                    </view>
                                </view>
                            </template>
                            <!-- 热门新房  小区房价-->
                            <view class="date_list_title" >
                                {{ current_filter_name == 'xinfang' ? "热门新房" : "本月热门小区房价" }}
                            </view>
                        </template>
                        <view class="list">
                            <!-- 新房 -->
                            <template v-if="current_filter_name =='xinfang' && this.currentName=='real_estate_market'">
                                <block v-for="(item, index) in buildRanklist" :key="index">
                                    <newHouseItem
                                        :itemData="item"
                                        :index="index"
                                        type="new_house"
                                        @click="toDetail"
                                    ></newHouseItem>
                                </block>
                            </template>
                            <!-- 小区房价-->
                            <template v-if="current_filter_name == 'ershoufang' && this.currentName=='real_estate_market'">
                                <block v-for="(item, index) in communityList" :key="index">
                                    <itemList
                                        :itemData="item"
                                        type="ershou"
										:index="index"
                                        @click="toDetail"
                                    ></itemList>
                                </block>
                            </template>
                        </view>
                    </view>
                </view>
            </template>
            <template v-if ="currentName =='industry_data'">
                <view class="filter_list">
                    <view
                        class="filter_item"
                        :class="{ active: index === current_filter_yenei_index }"
                        v-for="(item, index) in yenei_filter_list"
                        :key="index"
                        @click="onClickFilter(item, index)"
                        ><text>{{ item.name }}</text> <view class="down"></view
                    ></view>
                </view>
				<template v-if ="current_filter_yenei_index==0">
					<view class="yenei_data">
						<view class="data_content_bottom flex-row">
							<view class="data_content_bottom_left left_first  flex-1">
								<view class="data_content_top_left_box_title">
									本月预售证
								</view>
								<view class="data_content_top_left_box_price">
									<!-- <text class="box_unit_yue">约</text> -->
									<text class="box_price">{{yushouLastData.count}}</text>
									<text class="box_unit">个</text>
								</view>
							</view>
							<view class="data_content_bottom_left left_first  flex-1">
								<view class="data_content_top_left_box_title">
									总面积
								</view>
								<view class="data_content_top_left_box_price">
									<text class="box_unit_yue">约</text>
									<text class="box_price">{{(yushouLastData.jzmj||0 )|foramtUnit}}</text>
									<text class="box_unit">m²</text>
								</view>
							</view>
							<view class="data_content_bottom_left flex-1">
								<view class="data_content_top_left_box_title">
									总套数
								</view>
								<view class="data_content_top_left_box_price">
									<!-- <text class="box_unit_yue">约</text> -->
									<text class="box_price">{{yushouLastData.yszts}}</text>
									<text class="box_unit">套</text>
								</view>
							</view>
						</view>
					</view>
					<view class="yenei_history_data">
						<view class="yenei_history_data_title flex-row">
							<view class="yenei_history_data_title_con">
								预售近一年数据
							</view>
							<!-- <view class="yenei_history_data_title_filter flex-row">
								<picker @change="bindPickerChange" :value="index" :range="yushouHistoryData.month">
									<view class="uni-input">{{yushouHistoryData.month?yushouHistoryData.month[index]:yushouDefaultmonth}}</view>
								</picker>
								<view class="down">
									<my-icon type="ic_down" color="#fb656a" size="22rpx">
									</my-icon>
								</view>
							</view> -->
						</view>

						<view class="yenei_data">
							<view class="data_content_bottom flex-row">
								<view class="data_content_bottom_left left_first  flex-1">
									<view class="data_content_top_left_box_title">
										面积
									</view>
									<view class="data_content_top_left_box_price">
										<text class="box_unit_yue">约</text>
										<text class="box_price">{{(yushouHistoryData.jzmj||0 )|foramtUnit}}</text>
										<text class="box_unit">m²</text>
									</view>
								</view>
								<view class="data_content_bottom_left left_first  flex-1">
									<view class="data_content_top_left_box_title">
										住宅套数
									</view>
									<view class="data_content_top_left_box_price">
										<!-- <text class="box_unit_yue">约</text> -->
										<text class="box_price">{{yushouHistoryData.zzts}}</text>
										<text class="box_unit">套</text>
									</view>
								</view>
								<view class="data_content_bottom_left flex-1">
									<view class="data_content_top_left_box_title">
										非住宅套数
									</view>
									<view class="data_content_top_left_box_price">
										<!-- <text class="box_unit_yue">约</text> -->
										<text class="box_price">{{yushouHistoryData.fzzts}}</text>
										<text class="box_unit">套</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</template>
				<template v-if ="current_filter_yenei_index==1">
					<view class="yenei_data">
						<view class="data_content_bottom flex-row">
							<view class="data_content_bottom_left left_first  flex-1">
								<view class="data_content_top_left_box_title">
									本月成交
								</view>
								<view class="data_content_top_left_box_price">
									<!-- <text class="box_unit_yue">约</text> -->
									<text class="box_price">{{tupaiData.chengJiaoZongShuJin}}</text>
									<text class="box_unit">宗</text>
								</view>
							</view>
							<view class="data_content_bottom_left left_first  flex-1">
								<view class="data_content_top_left_box_title">
									成交额
								</view>
								<view class="data_content_top_left_box_price">
									<text class="box_unit_yue">约</text>
									<text class="box_price">{{tupaiData.chengJiaoMoney}}</text>
									<text class="box_unit">亿</text>
								</view>
							</view>
							<view class="data_content_bottom_left flex-1">
								<view class="data_content_top_left_box_title">
									成交面积
								</view>
								<view class="data_content_top_left_box_price">
									<text class="box_unit_yue">约</text>
									<text class="box_price">{{tupaiData.chengJiaoMianJiJin}}</text>
									<text class="box_unit">亩</text>
								</view>
							</view>
						</view>
					</view>
					<view class="yenei_history_data">
						<view class="yenei_history_data_title flex-row">
							<view class="yenei_history_data_title_con">
								土拍成交近6个月数据
							</view>
							<!-- <view class="yenei_history_data_title_filter flex-row">
								<picker @change="bindTupaiPickerChange" :value="tupaiIndex" :range="tupaiData.years">
									<view class="uni-input">{{tupaiData.years?tupaiData.years[tupaiIndex]:tupaiDefaultYear}}年</view>
								</picker>
								<view class="down">
									<my-icon type="ic_down" color="#fb656a" size="22rpx">
									</my-icon>
								</view>
							</view> -->
						</view>
						<view class="yenei_history_data_table">
							<table  cellspacing="1" cellpadding="0">
								<thead>
									<th class="month">月份</th>
									<th class="price">成交宗数（块）</th>
									<th class="desc">成交面积（亩）</th>
								</thead>
								<tbody>
									<tr v-for="(item,index) in tupaiData.list" :key="index">
										<td>{{item.month}}</td>
										<td>{{item.chengJiaoZongShuJin}}</td>
										<td>{{item.chengJiaoMianJiJin}}</td>
										
									</tr>
								</tbody>
							</table>
						</view>
					</view>
				</template>
            </template>
			<template v-if ="currentName =='deal_data'">
				<view class="filter_list jiaoyishuju">
                    <view class="filter_list_title flex-row"><text class="filter_list_title_con">昨日交易数据</text>
					<!-- <text class="small_text">（全市）</text> -->
					</view>
                </view>
				<view class="yenei_data jiaoyishuju">
					<view class="data_content_bottom flex-row">
						<view class="data_content_bottom_left left_first  flex-1" v-if ="jiaoyiData.deal_count>=0">
							<view class="data_content_top_left_box_title">
								商品房成交量
							</view>
							<view class="data_content_top_left_box_price">
								<!-- <text class="box_unit_yue">约</text> -->
								<text class="box_price">{{jiaoyiData.deal_count || 0}}</text>
								<text class="box_unit">套</text>
							</view>
						</view>
						<view class="data_content_bottom_left left_first  flex-1" v-if ="jiaoyiData.avg_price>=0">
							<view class="data_content_top_left_box_title">
								商品房成交均价
							</view>
							<view class="data_content_top_left_box_price">
								<!-- <text class="box_unit_yue">约</text> -->
								<text class="box_price">{{jiaoyiData.avg_price || 0}}</text>
								<text class="box_unit">元/m²</text>
							</view>
						</view>
						<view class="data_content_bottom_left flex-1" v-if ="jiaoyiData.yesterday_deal_count>=0">
							<view class="data_content_top_left_box_title">
								昨日二手房成交量
							</view>
							<view class="data_content_top_left_box_price" >
								<!-- <text class="box_unit_yue">约</text> -->
								<text class="box_price">{{jiaoyiData.yesterday_deal_count||0}}</text>
								<text class="box_unit">套</text>
							</view>
						</view>
					</view>
				</view>
				<view class="filter_title flex-row">
                    <view class="filter_title_left flex-row">
                        <text class="filter_title_left_title">最近15天成交数量走势</text
                        >
                    </view>
                    <!-- <view class="filter_title_right flex-row">
                        <view
                            class="filter_date"
                            v-for="(item, index) in filter_date_list"
                            :key="index"
                            @click="onClickJiaoyiFilterDate(item, index)"
                            ><text  :class="{ active: index === current_filter_jiaoyi_date_index }">{{ item.name }}</text>
                        </view>
                    </view> -->
                </view>
				<!-- 走势图   -->
                <view class="date">
					<view class="date_box">
						<view class="date-row flex-row" v-for ="(item,index) in jiaoyiArr" :key ="index">
							<view class="date_date">{{item.day}}</view>
							<view class="date_mid" id ="date_mid">
								<view class="date_mid_con" :style="{width:item.bgWidth}"></view>
							</view>
							<view class="date_count">{{item.num}}套</view>
						</view>

					</view>
					<!-- <view class="qiun-columns">
						<view class="qiun-charts" v-show="this.currentName =='deal_data'">
							<canvas canvas-id="canvasLineA" id="canvasLineA" class="charts" @touchstart="touchLineA"></canvas>
						</view>
					</view> -->
				</view>
			</template>
			<template v-if ="currentName =='wangqian'">
				<view class="filter_list jiaoyishuju">
                    <view class="filter_list_title filter_list_title_wangqian  flex-row"><text class="filter_list_title_con">网签数据</text>
					<text class="small_text">{{upTimeWangqian}}</text>
					</view>
                </view>
				<view class="yenei_data jiaoyishuju wangqianinfo">
					<view class="data_content_bottom flex-row">
						<view class="data_content_bottom_left left_first  flex-1" v-for ="(item,index) in wangqianArr" :key ="index">
							<view class="data_content_top_left_box_title" >
								{{item.title}}
							</view>
							<view class="data_content_top_left_box_price">
								<text class="box_price">{{item.num}}</text>
								<text class="box_unit">套</text>
							</view>
						</view>
					</view>
				</view>

				<view class="wangqianinfo" v-for ="(item,index) in wangqianArr" :key ="index">
					<view class="filter_list jiaoyishuju">
            <view class="filter_list_title flex-row"><text class="filter_list_title_con">{{item.title}}</text>
					<!-- <text class="small_text">（全市）</text> -->
            </view>
          </view>
					<view class="yenei_data jiaoyishuju">
					<view class="data_content_bottom flex-row">
						<view class="data_content_bottom_left left_first  flex-1" v-if='item.mianji'>
							<view class="data_content_top_left_box_title">
								面积（m²）
							</view>
							<view class="data_content_top_left_box_price">
								<text class="box_price">{{item.mianji}}</text>
								<text class="box_unit"></text>
							</view>
						</view>
						<view class="data_content_bottom_left left_first  flex-1" v-if='item.avg_price'>
							<view class="data_content_top_left_box_title">
								均价（元/m²）
							</view>
							<view class="data_content_top_left_box_price">
								<text class="box_price">{{item.avg_price}}</text>
								<text class="box_unit"></text>
							</view>
						</view>
						<view class="data_content_bottom_left left_first  flex-1" v-if='item.total_price'>
							<view class="data_content_top_left_box_title">
								总成交额（万元）
							</view>
							<view class="data_content_top_left_box_price">
								<text class="box_price">{{item.total_price}}</text>
								<text class="box_unit"></text>
							</view>
						</view>
					</view>
					</view>
				</view>

				<!-- 网签数据 -->
				<view class="wangqian">
					
				</view>

			</template>
		</view>
		<view class="update_time bottom-line" v-if ="this.currentName !='wangqian'">
			更新时间：{{upTime}}
		</view>
		<view class="site_info flex-row">
			<view class="site_info_qr">
				<image :src ="erweima" mode="widthFix"> </image>
			</view>
			<view class="site_info_site flex-box">
				<view class="site_info_site_name"><text>{{share.index_title||siteName}}</text></view>
				<!-- <view class="site_info_site_subname">
					房产综合服务平台
				</view> -->
			</view>
		</view>
		</view>
	</view>
</template>

<script>
import tabBar from "@/components/tabBar.vue";
import myIcon from "@/components/myIcon.vue";
import { config } from "../common/config.js";
import itemList from "@/components/itemList";
import newHouseItem from "@/components/propertyNewHouseItem";

// #ifndef MP
import uCharts from '@/components/u-charts/u-charts.js';
// #endif
// #ifdef MP
import uCharts from '@/components/wx-charts/wxcharts.js';
// #endif
var _self;
	var canvasLineA =null;
export default {
	components: {
		tabBar,
		itemList,
		newHouseItem,
		myIcon
	},
	data() {
		return {
			cWidth:'650rpx',
			cHeight:'500rpx',
			pixelRatio:1,
			navs: [],
			stickTop: "44px",
			currentIndex: 0,
			currentName:'real_estate_market',
			current_filter_index: 0,
			current_filter_name:'',
			showItems:{
				xinfangshow:1,
				ershoufangshow:1,
				zufangshow:1
			},
			show_content:false,
			filter_list: [
				
			],
			xinfang:{
				buildAvgPrice:{}, //新房均价 不存在返回空数组
				ysxk:{    //预售
					month:'',  //月份
					mianji:''  //住宅预售面积
				},
				tupai:{
					month:'', //月份
					mianji:''  //土地成交面积	
				}

			},
			houseBuildPrice:'',
			ershouBuildPrice:'',
			infoAvg:{  //二手房统计
				month:'',//月份
				price:0, //均价	
				last_month_status:0,//0：持平，1：上涨，2：下降
				last_month_value:0, //比对值
				last_year_status:0,//0：持平，1：上涨，2：下降
				last_year_value:0, //比对值
				avg_price:0, //实时均价
				info_count:0, //实时数量
			},
			rentingAvg:{
				month:'',//月份
				price:0, //均价	
				last_month_status:0,//0：持平，1：上涨，2：下降
				last_month_value:0, //比对值
				last_year_status:0,//0：持平，1：上涨，2：下降
				last_year_value:0, //比对值
			},
			buildHalfAvgPrice:{
				month:[],
				data:[{
					data:[]
				}],
			},//新房半年数据统计
			buildYearAvgPrice:{
				month:[],
				data:[{
					data:[]
				}],
			},//新房全年数据统计
			infoHalfAvgPrice:{
				month:[],
				data:[{
					data:[]
				}],
			},  //二手房半年数据统计
			infoYearAvgPrice:{
				month:[],
				data:[{
					data:[]
				}],
			},//二手房全年数据统计
			buildRanklist:[], //热门楼盘
			areaAvgPrice:[], //区域排行榜
			communityList:[], //热门小区
            yenei_filter_list: [
				{
					name: "预售",
					id: 0,
				},
				{
					name: "土拍",
					id: 1,
				},
			],
			jiaoyi_filter_list:[
				{
					
					name: "交易数据",
					id: 1,
				}
			],
			filter_date_list: [
				{
					name: "半年",
					id: 0,
				},
				{
					name: "一年",
					id: 1,
				},
			],
			current_filter_yenei_index:0, //业内数据土拍 预售转换idx
			current_filter_date_index: 0,
			current_filter_jiaoyi_date_index: 0,
			yushouLastData:{},
			yushouHistoryData:{},
			tupaiData:{years:[]},
			jiaoyiData:{},
			index:0,
			tupaiIndex:0,
			tupaiDefaultYear:'',
			yushouDefaultmonth:'',
			buildYearDeal:{},
			buildHalfDeal:{},
			hangqingShare:{},
			yeneiShare:{},
			jiaoyiShare:{},
			jiaoyiArr:[],
			share:{},
			upTime:'',
			erweima:'',
			siteName:'',
			siteCity:'',
			wangqianArr:[],
			upTimeWangqian:'',
		};
	},
	onLoad(options){
		_self = this;
		this.cWidth=uni.upx2px(650);
		this.cHeight=uni.upx2px(500);
		this.getNavs()
		if (options.type){
			this.currentName=options.type
		}
		document.title = '房产数据'
	},
	filters: {
		imgUrl(val) {
			return config.imgDomain + val+'?x-oss-process=style/m_240';
		},
		foramtUnit(val){
			if(val<10000){
				return parseInt(val) 
			}
			return (val/10000).toFixed(0)+"万"
		}
	},
	methods: {
		getData(){
			this.$ajax.get("HouseData/monthInfoStatistics", {}, (res) => {
				
				if (res.data.SiteCity){
					this.siteCity=res.data.SiteCity
				}
				if (res.data.update){
					this.upTime=res.data.update
				}
				if (res.data.xcxewm){
					this.erweima=res.data.xcxewm
				}
				if (res.data.siteName){
					this.siteName=res.data.siteName
				}
				if (res.data.share){
					this.hangqingShare =res.data.share
					this.setShare()
				}
				// #ifdef H5 || MP-BAIDU
				if (res.data.seo) {
					let seo = res.data.seo;
					this.seo = seo;
				}
				// #endif
				if (res.data.code ==1){
					if ((typeof res.data.buildAvgPrice) =="object"){
						this.xinfang.buildAvgPrice= res.data.buildAvgPrice

						this.xinfang.ysxk= res.data.ysxk
						this.xinfang.tupai= res.data.tupai
					}
					this.rentingAvg = res.data.rentingAvg  //租房统计
					this.infoAvg = res.data.infoAvg  //二手房统计
				}else{
					uni.showToast({title:res.data.msg,icon:"none"})

				}
			})
		},
		getNavs(){
			this.$ajax.get("HouseData/houseDataNav", {}, (res) => {
				if (res.data.code == 1) {
					let filter_list =[
						{
							name: "新房",
							type: 'xinfang',
						},
						{
							name: "二手房",
							type: 'ershoufang',
						},
					];
					res.data.data.map(item=>{
						if (item.type ==1){
							this.showItems.xinfangshow =item.is_show
							filter_list.map(data=>{
								if(data.type=="xinfang"){
									data.is_show = item.is_show
								}
							})
						}
						if (item.type ==2){
							this.showItems.ershoufangshow =item.is_show
							filter_list.map(data=>{
								if(data.type=="ershoufang"){
									data.is_show = item.is_show
								}
							})
						}
						if (item.type ==3){
							this.showItems.zufangshow =item.is_show
						}
					})
					let list =filter_list.filter(item=>item.is_show==1)
					
					if (list.length>0){
						this.current_filter_name =list[0].type
					}
					this.filter_list =list
					let navs =res.data.nav.filter(item=>item.is_show>0)
          let isSetNav  = res.data.nav.filter(item=>item.is_show==3)
          if (isSetNav.length) {
            let obj = {
              name:'网签数据',
              title: "网签数据",
              type:"wangqian",
              is_show:1
            }
            navs.push(obj)
          }
					navs.map((item,idx)=>{
						if (item.is_show!=2) {
							item.name =item.title
						}else{
							item.name ="网签数据"
              item.type="wangqian"
						}
						if(item.type==this.currentName){
							this.currentIndex=idx
						}
					})
					this.navs=navs
					if (this.currentName =='real_estate_market'){ //获取楼市行情数据
						this.getData()
						this.getBuildInfo()
					}
					if (this.currentName =='industry_data'){ //获取业内数据
						this.getYeneiYushouData()
					}
					if (this.currentName =='deal_data'){ //获取交易数据
						this.getjiaoyiData()
					}
          if (this.currentName =='wangqian'){ //获取网签数据
						this.getjiaoyiData()
					}
					this.show_content=true
				}else {
					uni.showToast({title:res.data.msg,icon:"none"})
				}
			})
			// this.$ajax.get("HouseData/dealHouseStatistics", {}, (res) => {
			// 	this.wangqianArr = res.data.yesterdayNetSign.data
			// 	console.log(this.wangqianArr);
			// })
			
		},
		getBuildInfo(){
			this.$ajax.get("houseData/monthInfoTrendAndList", {}, (res) => {
				if (res.data.SiteCity){
					this.siteCity=res.data.SiteCity
				}
				if (res.data.update){
					this.upTime=res.data.update
				}
				if (res.data.xcxewm){
					this.erweima=res.data.xcxewm
				}
				if (res.data.siteName){
					this.siteName=res.data.siteName
				}	
				if (res.data.code ==1){ 
					// 处理图表数据格式
					let buildHalfAvgPrice ={month:[],data:[{name:'',data:[]}]},
						infoHalfAvgPrice={month:[],data:[{name:'',data:[]}]},
						infoYearAvgPrice={month:[],data:[{name:'',data:[]}]},
						buildYearAvgPrice={month:[],data:[{name:'',data:[]}]};
					res.data.buildHalfAvgPrice.map(item=>{
						buildHalfAvgPrice.month.push(item.month)
						buildHalfAvgPrice.data[0].data.push(item.price)
					})
					res.data.infoHalfAvgPrice.map(item=>{
						infoHalfAvgPrice.month.push(item.month)
						infoHalfAvgPrice.data[0].data.push(item.price)
					})
					res.data.infoYearAvgPrice.map(item=>{
						infoYearAvgPrice.month.push(item.month)
						infoYearAvgPrice.data[0].data.push(item.price)
					})
					res.data.buildYearAvgPrice.map(item=>{
						buildYearAvgPrice.month.push(item.month)
						buildYearAvgPrice.data[0].data.push(item.price)
					})
					this.buildYearAvgPrice =buildYearAvgPrice 
					this.infoYearAvgPrice =infoYearAvgPrice 
					this.infoHalfAvgPrice =infoHalfAvgPrice 
					this.buildHalfAvgPrice =buildHalfAvgPrice 
					this.showLineInfo()
					this.buildRanklist=res.data.buildRanklist
					this.areaAvgPrice=res.data.areaAvgPrice
					this.communityList=res.data.communityList
				}
			})
		},
		getYeneiYushouData(month){
			let params ={}
			if (month){
				params={month}
			}
			this.$ajax.get("HouseData/industryDataYsxk", params, (res) => {
				if (res.data.SiteCity){
					this.siteCity=res.data.SiteCity
				}
				if (res.data.update){
					this.upTime=res.data.update
				}
				if (res.data.xcxewm){
					this.erweima=res.data.xcxewm
				}
				if (res.data.siteName){
					this.siteName=res.data.siteName
				}
				if (res.data.share){
					this.yeneiShare =res.data.share
					this.setShare()
				}
				// #ifdef H5 || MP-BAIDU
				if (res.data.seo) {
					let seo = res.data.seo;
					this.seo = seo;
				}
				// #endif
				if (res.data.code ==1 ){
					let yushouLastData ={};
					let yushouHistoryData={}
					yushouLastData.count =res.data.last.count //预售证个数
					yushouLastData.jzmj =res.data.last.jzmj  //预售总面积
					yushouLastData.yszts =res.data.last.yszts  //预售总套数
					yushouHistoryData.zzts =res.data.history.zzts    //住宅套数 
					yushouHistoryData.fzzts =res.data.history.fzzts    //非住宅套数
					yushouHistoryData.jzmj =res.data.history.jzmj  //预售总面积
					yushouHistoryData.month =res.data.months||[]     //月份数组
					this.yushouLastData=yushouLastData
					this.yushouHistoryData =yushouHistoryData
				}else {
					uni.showToast({
						title:res.data.msg,
						icon:"none"
					})
				}
			})
		},
		getYeneiTupaiData(year){   //HouseData/industryDataTuPai
			let params={}
			if (year){
				params ={year}
			}
			this.$ajax.get("HouseData/industryDataTuPai", params, (res) => {
				if (res.data.update){
					this.upTime=res.data.update
				}
				if (res.data.xcxewm){
					this.erweima=res.data.xcxewm
				}
				if (res.data.siteName){
					this.siteName=res.data.siteName
				}
				if (res.data.SiteCity){
					this.siteCity=res.data.SiteCity
				}
				// #ifdef H5 || MP-BAIDU
				if (res.data.seo) {
					let seo = res.data.seo;
					this.seo = seo;
				}
				// #endif
				if (res.data.code ==1 ){
					let tupaiData ={}
					tupaiData.years =res.data.data.years ||[]
					tupaiData.list =res.data.data.list||[]
					tupaiData.chengJiaoMoney =res.data.data.chengJiaoMoney  // 成交金额 单位亿
					tupaiData.chengJiaoZongShuJin=res.data.data.chengJiaoZongShuJin  //成交宗数
					tupaiData.chengJiaoMianJiJin=res.data.data.chengJiaoMianJiJin  //成交面积 
					this.tupaiData=tupaiData
				}else {
					uni.showToast({title:res.data.msg,icon:"none"})
				}
			})
		},
		getjiaoyiData(){  
			this.$ajax.get("houseData/dealHouseStatistics", {}, (res) => {
				if (res.data.update){
					this.upTime=res.data.update
				}
				if (res.data.xcxewm){
					this.erweima=res.data.xcxewm
				}
				if (res.data.siteName){
					this.siteName=res.data.siteName
				}
				if (res.data.SiteCity){
					this.siteCity=res.data.SiteCity
				}
				if (res.data.share){
					this.jiaoyiShare =res.data.share
					this.setShare()
				}
				// #ifdef H5 || MP-BAIDU
				if (res.data.seo) {
					let seo = res.data.seo;
					this.seo = seo;
				}
				// #endif
				if (res.data.code ==1 ){
					if (this.currentName =='wangqian'){
						this.wangqianArr = res.data.yesterdayNetSign.data
            if (res.data.yesterdayNetSign.date) {
              this.upTimeWangqian= res.data.yesterdayNetSign.date
            }
					}else {
						this.jiaoyiData=res.data.infoDeal
						let jiaoyiDateMax =0,jiaoyiCountArr=[]
						res.data.halfMonth.map(item=>{
							jiaoyiCountArr.push(item.num)
						})
						jiaoyiDateMax=Math.max(...jiaoyiCountArr)
						console.log(jiaoyiDateMax);
						res.data.halfMonth.map(item=>{
							item.bgWidth=((item.num)/jiaoyiDateMax)*100+'%'
						})
						this.jiaoyiArr=res.data.halfMonth
					}
					// let buildHalfDeal ={month:[],data:[{name:'',data:[]}]}
					// let buildYearDeal ={month:[],data:[{name:'',data:[]}]}
					// res.data.buildHalfDeal.map((item,index)=>{
					// 		buildHalfDeal.month.push(item.month)
					// 		buildHalfDeal.data[0].data.push(item.deal_count)
					// })
					// res.data.buildYearDeal.map(item=>{
					// 	buildYearDeal.month.push(item.month)
					// 	buildYearDeal.data[0].data.push(item.deal_count)
					// })
					// this.buildHalfDeal=buildHalfDeal
					// this.buildYearDeal=buildYearDeal
					// this.showLine(this.buildHalfDeal)
				}else {
					uni.showToast({title:res.data.msg,icon:"none"})
				}
			})
		},
		setShare(){
			let defaultShare={
				title:this.siteCity+(new Date().getMonth()+1)+'月最新楼市行情数据',
				content:' 查房价，了解最新楼市行情，上'+this.siteName+"!",
				pic:this.erweima,

			}
			if (this.currentName=='real_estate_market'){
				if (this.hangqingShare.title||this.hangqingShare.content){
					this.share =this.hangqingShare
				}else {
					this.share =defaultShare
					this.share.index_title = this.hangqingShare.index_title
				}
				
			}else if (this.currentName =='industry_data'){
				if (this.yeneiShare.title||this.yeneiShare.content){
					this.share =this.yeneiShare
				}else {
					this.share =defaultShare
					this.share.index_title = this.yeneiShare.index_title
				}
				// this.share = this.yeneiShare||defaultShare
			}else if (this.currentName =='deal_data' ||this.currentName =='wangqian' ){
				if (this.jiaoyiShare.title||this.jiaoyiShare.content){
					this.share =this.jiaoyiShare
				}else {
					this.share =defaultShare
					this.share.index_title = this.jiaoyiShare.index_title
				}
				// this.share =this.jiaoyiShare || defaultShare
			}
			this.share.link=`${window.location.origin}${window.location.pathname}?type=${this.currentName}`
			this.getWxConfig()
		},
		showLine(chartData){
				canvasLineA=new uCharts({
					$this:_self,
					canvasId: 'canvasLineA',
					type: 'area',
					padding:[15,0,4,0],
					fontSize:10,
					// enableScroll:true,
					legend:{show:false},
					dataLabel:false,
					dataPointShape:true,
					background:'#FFFFFF',
					pixelRatio:_self.pixelRatio,
					categories: chartData.month,
					series:chartData.data,
					animation: true,
					xAxis: {
						type:'grid',
						gridColor:'#CCCCCC',
						gridType:'dash',
						dashLength:2,
						fontSize:10,
						rotateLabel:true,
						// scrollShow:true,
						// axisLine:false,
						// boundaryGap:"justify"
					},
					yAxis: {
						gridType:'dash',
						gridColor:'#CCCCCC',
						dashLength:8,
						splitNumber:5,
						axisLine:false,
						// min:10,
						// max:180,
						format:(val)=>{
							if (this.currentName=='real_estate_market'){
								return val.toFixed(0)+'元/m²'
							}else if (this.currentName =='deal_data'){
								return val
							}
						}
							
					},
					width: _self.cWidth*_self.pixelRatio,
					height: _self.cHeight*_self.pixelRatio,
					extra: {
						area:{
							type: 'curve',
						},
					}
				});
				
			},
		handelCate(e) {
			this.currentIndex = e.index;
			this.currentName = this.navs[e.index].type
			if (this.currentName=='real_estate_market'){
				this.getData()
				this.getBuildInfo()
				this.setShare()
			}else if (this.currentName =='industry_data') {
				if (this.current_filter_yenei_index ==1){
					this.getYeneiTupaiData()
				}else {
					this.getYeneiYushouData()
				}
				this.setShare()
				
			}else if (this.currentName =='deal_data' ||this.currentName =='wangqian'){
				this.getjiaoyiData()
				this.setShare()
			}
		},
		showLineInfo(){
			if (this.current_filter_name=='xinfang'){  //新房
				if(this.current_filter_date_index==0){   //半年
					this.buildHalfAvgPrice.data[0].name ="新房均价半年走势"
					this.showLine(this.buildHalfAvgPrice)
					
				}else if (this.current_filter_date_index==1){
					this.buildYearAvgPrice.data[0].name ="新房均价全年走势"
					this.showLine(this.buildYearAvgPrice)
				}
			}else if (this.current_filter_name=="ershoufang"){  //二手房
				if(this.current_filter_date_index==0){   //半年
				this.infoHalfAvgPrice.data[0].name ="二手房均价半年走势"
					this.showLine(this.infoHalfAvgPrice)	
				}else if (this.current_filter_date_index==1){
					this.infoYearAvgPrice.data[0].name ="二手房均价全年走势"
					this.showLine(this.infoYearAvgPrice)
				}
			}
		},
		onClickFilter(item, index) {
			if (this.currentName=='real_estate_market'){
				this.current_filter_index = index;
				this.current_filter_name=this.filter_list[index].type
				this.showLineInfo()
			}else if (this.currentName =='industry_data'){
				this.current_filter_yenei_index = index;
				if (this.current_filter_yenei_index ==1){
					this.getYeneiTupaiData()
				}else {
					this.getYeneiYushouData()
				}
			}
		},
		onClickFilterDate(item, index) {
			this.current_filter_date_index = index;
			this.$nextTick(()=>{
				this.showLineInfo()
			})
		},
		onClickJiaoyiFilterDate(item,index){
			this.current_filter_jiaoyi_date_index = index;
			this.$nextTick(()=>{
				if (this.current_filter_jiaoyi_date_index==0){
					this.buildHalfDeal.data[0].name ="历史新房成交半年走势"
					this.showLine(this.buildHalfDeal)
				}else {
					this.buildYearDeal.data[0].name ="历史新房成交全年走势"
					this.showLine(this.buildYearDeal)
				}
			})
		},
		toDetail(e) {
			if (this.currentName=='real_estate_market'&&this.current_filter_name=='xinfang'){//新房
				this.$navigateTo("/pages/new_house/detail?id="+e.detail.id)
			}else if (this.currentName=='real_estate_market'&&this.current_filter_name=='ershoufang'){
				this.$navigateTo("/pages/house_price/detail?id="+e.detail.id)
			}
		},
		bindPickerChange(e){
			this.index = e.target.value
			this.getYeneiYushouData(this.yushouHistoryData.month[this.index])
		},
		bindTupaiPickerChange(e){
			this.tupaiIndex = e.target.value
			this.getYeneiTupaiData(this.tupaiData.years[this.tupaiIndex])
		},
		touchLineA(e) {
			this.$nextTick(()=>{
				canvasLineA .showToolTip(e, {
				format: function (item, category) {
					return category + ' ' + item.name + ':' + item.data
				}
			});
			})
		},
	},
};
</script>

<style scoped lang="scss">
.wangqianinfo {
	.jiaoyishuju .data_content_bottom .data_content_bottom_left{
		min-width: auto;
	}
	.flex-row{
		flex-wrap: wrap;
	}
	.data_content_bottom_left{
		margin-bottom: 10px;
	}
	.data_content_bottom .data_content_bottom_left{
		min-width: 19vw!important;
	}
}
.data_page {
	padding: 0 48rpx;
	background: #fff;
	min-height: calc(100vh - 44px);
	// padding-bottom: 20rpx;
}
.tipStick {
	position: sticky;
	z-index: 10;
}
.data_title {
	margin-top: 48rpx;
	font-size: 40rpx;
	color: #333333;
	font-weight: 600;
}
.flex-row {
	display: flex;
	flex-direction: row;
}
.data_content_top {
	border: 1rpx solid #d8d8d8;
	box-shadow: 0 8rpx 20rpx 0 rgba(0, 0, 0, 0.05);
	border-radius: 8rpx;
	padding: 0 24rpx;
	justify-content: space-between;
	margin-top: 18rpx;
	&_left {
		flex: 1;
		&_box {
			align-items: center;
			flex-direction: column;
			color: #999999;
			font-size: 22rpx;
			&_title {
				margin-top: 24rpx;
				font-size: 22rpx;
			}
			&_price {
				margin: 24rpx 0;
				align-items: center;
				.box_price {
					font-size: 36rpx;
					color: #333333;
					margin: 0 10rpx;
				}
				.box_unit {
					font-size: 22rpx;
				}
				image {
					width: 28rpx;
				}
				.month_status{
					align-items: center;
					margin-left: 10rpx;
					&.up{
						.box_unit {
							color: #fb656a;
						}
					}
					&.down {
						.box_unit {
							color: #65aefb;
						}
					}	
					
				}
				&.up {
					.box_price {
						color: #fb656a;
					}
					.box_unit {
						color: #fb656a;
						align-self: baseline;
					}
					
				}
				&.down {
					.box_price {
						color: #65aefb;
					}
					.box_unit {
						color: #65aefb;
						align-self: baseline;
					}
				}
			}
		}
	}
	&_right {
		width: 260rpx;
		height: 112rpx;
		margin-top: 18rpx;
		image {
			width: 100%;
		}
	}
}
.data_content_bottom {
	margin-top: 24rpx;
	justify-content: space-between;
	color: #999999;
	&_left {
		border: 1rpx solid #d8d8d8;
		box-shadow: 0 8rpx 20rpx 0 rgba(0, 0, 0, 0.05);
		border-radius: 8rpx;
		align-items: center;
		display: flex;
		flex-direction: column;
		&.left_first {
			margin-right: 24rpx;
		}
	}
}
.filter_list {
	// padding-left: 48rpx;
	padding: 24rpx 0 24rpx 0;
	background-color: #fff;
	position: relative;
	.filter_item {
		display: inline-block;
		padding: 20rpx 60rpx;
		border-radius: 4rpx;
		line-height: 1;
		box-sizing: border-box;
		// margin-right: 24rpx;
		font-size: 28rpx;
		background-color: #f8f8f8;
		// border: 1rpx solid #f5f5f5;
		color: #999;

		&.active {
			background-color: #fb656a;
			box-shadow: 0 0 8px 0 rgba(251, 101, 106, 0.4);
			color: #fff;
			// border: 1rpx solid $uni-color-primary;
			position: relative;
			.down {
				position: absolute;
				bottom: -20rpx;
				left: 50%;
				transform: translateX(-50%);
				border-bottom: 10rpx solid transparent;
				border-right: 10rpx solid transparent;
				border-left: 10rpx solid transparent;
				border-top: 10rpx solid #fb656a;
			}
		}
	}
}
.filter_title {
	justify-content: space-between;
	align-items: center;
	&_right {
		.filter_date {
			background: #f8f8f8;
			border-radius: 8rpx;
			color: #999999;
			font-size: 22rpx;
			text{
				margin: 8rpx;
				padding: 4rpx 12rpx;
				display: inline-block;
				&.active {
				background: #fff;
				color: #333333;
			}
			}
			
		}
	}
	&_left {
		align-items: center;
		color: #999999;
		&_title {
			font-size: 40rpx;
			color: #333333;
			font-weight: 600;
			margin-right: 10rpx;
		}
	}
}
.filter_subtitle {
	margin: 26rpx 0;
	font-size: 28rpx;
	color: #666666;
}
.date_list_title {
	font-weight: 600;
	font-size: 40rpx;
	color: #333333;
}
.date_list {
	.data_ershou_list {
		&_item {
			justify-content: space-between;
			align-items: center;
			padding: 24rpx 0;
			margin-top: 24rpx;
			font-size: 28rpx;
			&_left {
				align-items: center;
				width: 26%;
				overflow: hidden;
				text-overflow: ellipsis;
				flex-wrap: nowrap;
				.zoushi_area {
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
					width: calc(100% - 32rpx);
				}
				.zoushi_paiming {
					margin-right: 10rpx;
					width: 32rpx;
					min-width: 32rpx;
					height: 32rpx;
					font-size:24rpx;
					font-weight: 600;
					color: #fb656a;
					// color: #FFFFFF;
					border-radius: 6rpx;
					letter-spacing: 0;
					text-align: center;
					line-height: 32rpx;
					&.zoushi_paiming1 {
						color: #ffffff;
						background-color: #fb656a;
					}
					&.zoushi_paiming2 {
						color: #ffffff;
						background: #fb8b65;
					}
					&.zoushi_paiming3 {
						color: #ffffff;
						background: #fbb665;
					}
				}
			}
			&_middle {
				width: 37%;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
				.zoushi_price {
					font-weight: 600;
					color: #151515;
				}
			}
			&_right {
				width: 37%;
				overflow: hidden;
				text-overflow: ellipsis;
				flex-wrap: nowrap;
				color: #999;
				align-items: center;
				.zoushi_bijiao_unit {
					color: #999;
				}
				&.data_ershou_list_item_right2 {
					color: #65aefb;
				}
				&.data_ershou_list_item_right1 {
					color: #fb656a;
				}
				.zoushi_bijiao {
					margin: 0 10rpx;
				}
				.zoushi_bijiao_img {
					width: 30rpx;
					height: 30rpx;
					image {
						width: 100%;
					}
				}
			}
		}
	}
	.date_list_title {
		margin-top: 48rpx;
	}
}
.yenei_data{
	margin: 24rpx 0;
	
}
.jiaoyishuju{
	.data_content_bottom{
		width: 100%;
		overflow: auto hidden;
		.data_content_bottom_left{
			min-width: 35vw;
		}
	}
}
.yenei_history_data_title{
	justify-content: space-between;
	align-items: center;
	&_con{
		font-size: 40rpx;
		font-weight: 600;
		color: #333333;
	}
	&_filter{
		padding: 4rpx 20rpx;
		color: #fb656a;
		border: 6rpx solid #f8f8f8;
		border-radius: 8rpx;
		align-items: center;
		.uni-input{
			font-size: 22rpx;
		}
	}
}
.yenei_history_data_table{
	margin-top: 48rpx;

}
table{
	width: 100%;
}
table tr td{
	border-left: 0;
	border-right: 0;
	padding: 30rpx 0;
	text-align: center;
}
table tr td:nth-child(1){
	border-left: 1px solid#dedede;
}
table tr:nth-child(2n){
	background: #F8F8F8;
}
table tr td:last-of-type{
	border-right: 1px solid #dedede;
}
table th{
	border-left: 0;
	border-right: 0;
	padding: 30rpx 16rpx;
	text-align: center;
	
}
table thead{
	background: #F8F8F8;
}
table th:nth-child(1){
	border-left: 1px solid#dedede;
}
table th:last-of-type{
	border-right: 1px solid #dedede;
}
.jiaoyishuju .filter_list_title{
	align-items: center;
  &.filter_list_title_wangqian{
    justify-content: space-between;
    .small_text{
      color: #999;
    }
  }
	&_con{
		font-size: 40rpx;
		color: #333333;
		font-weight: 600;
	}
}
.update_time{
	text-align: center;
	font-size: 22rpx;
	color: #999999;
	padding: 48rpx 0;
}
.site_info{
	margin-top: 48rpx;
	padding-bottom: 80rpx;
	&_qr{
		width: 160rpx;
		height: 160rpx;
		min-width: 160rpx;
		overflow: hidden;
		image {
			width: 100%;
		}
	}
	&_site{
		flex-direction: column;
		justify-content: space-between;
		margin-left: 24rpx;
		&_name{
			text{
				font-size: 28rpx;
				color: #333333;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
				// font-weight: 600;
				margin-top: 20rpx;
			}
			
		}
		&_subname{
			margin-bottom: 20rpx;
			font-size: 26rpx;
		}
	}
}
.qiun-charts {
	width: 650upx;
	height: 500upx;
	background-color: #FFFFFF;

}
.charts {
	width: 650upx;
	height: 500upx;
	background-color: #FFFFFF;
}
.date{
	.date_box{
		margin-top: 50rpx;
		.date-row{
			align-items: center;
			justify-content: space-between;
			color: #999;
			margin-bottom: 32rpx;
			&:last-child{
				margin-bottom: 0;
			}
			.date_mid{
				flex: 1;
				height: 24rpx;
				background: #F8F8F8;
				border-radius: 2px 6px 6px 2px;
				.date_mid_con{
					background-image: linear-gradient(36deg, #FFA533 0%, #FE6C17 100%);
					border-radius: 4rpx 12rpx 12rpx 4rpx;
					height: 24rpx;
				}
			}
			.date_date{
				width: 90rpx;
				margin-right: 24rpx;
			}
			.date_count{
				color: #333;
				min-width: 100rpx;
				margin-left: 24rpx;
				text-align: right;
			}
		}
	}
}
</style>
