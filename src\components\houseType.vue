<template>
  <view class="contrast_item flex-row">
    <slot name="check_box" :checked="checked"></slot>
    <view class="img-box">
      <image @click="$emit('click')" :src="img | imageFilter('w_240')" mode="aspectFill"></image>
      <image v-if="has_vr" class="vr-icon" src="/static/icon/vr_b.png" mode="widthFix"></image>
    </view>
    <view class="info-box" @click="$emit('click')">
      <view class="title">{{title}}</view>
      <view class="types flex-row">
        <text class="type" v-for="(label, index) in labels" :key="index" v-if="label">{{label}}</text>
      </view>
      <view class="price-row flex-row">
        <text class="price" v-if="price">{{price}}</text>
        <text class="status" :class="getClass(sale_status)" v-if="sale_status">{{
          sale_status_text
        }}</text>
        <text v-else></text>
        <slot>
          <view class="more flex-row">
            <text>查看详情</text>
            <my-icon type="ic_into" color="#999" size="24rpx"></my-icon>
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from './myIcon'
export default {
  components: {
    myIcon
  },
  data () {
   return {

     }
  },
  props: {
    title:String,
    img:String,
    labels:Array,
    price:String,
    sale_status_text: String,
    sale_status:{
      type:Number,
      default: 0
    },
    checked:{
      type:Number,
      default: 0
    },
    has_vr: {
      type: Number,
      default: 0
    }
  },
  methods: {
    getClass(val) {
      let clas = ''
      switch (val) {
        case 1:
          clas = 'status1'
          break
        case 2:
          clas = 'status2'
          break
        case 3:
          clas = 'status3'
          break
        case 4:
          clas = 'status4'
          break
      }
      return clas
    },
  }
}
</script>

<style scoped lang="scss">
view {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.contrast_item{
  width: 100%;
  padding: 44rpx 48rpx;
  align-items: center;
  position: relative;
  &::after{
    content: "";
		position: absolute;
		bottom: 0;
		left: 48rpx;
		right: 48rpx;
		height: 1px;
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5);
		background-color: $uni-border-color;
  }
  .img-box{
    width: 204rpx;
    height: 172rpx;
    margin-right: 24rpx;
    position: relative;
    image{
      width: 100%;
      height: 100%;
      border-radius: 8rpx;
    }
    .vr-icon{
      width: 60rpx;
      height: 60rpx;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      margin: auto;
    }
  }
  .info-box{
    height: 172rpx;
    flex: 1;
    justify-content: space-between;
    .title{
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      font-size: 32rpx;
      font-weight: bold;
    }
    .types{
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      font-size: 24rpx;
      color: #666;
      .type{
        margin-right: 24rpx;
      }
    }
    .price-row{
      justify-content: space-between;
      align-items: center;
      .price{
        font-size: 32rpx;
        font-weight: bold;
        color: $uni-color-primary;
      }
      .more{
        font-size: 24rpx;
        align-items: center;
        color: #999;
      }
    }
  }
}
// 楼盘销售状态标签
.status {
  line-height: 1;
  font-size: 28rpx;
  padding: 6rpx 10rpx;
  border-radius: 4rpx;
  color: #fff;
  background-color: #53d2ab;
  font-weight: initial;
  &.status1 {
    color: #fff;
    background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
  }
  &.status2 {
    color: #fff;
    background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
  }
  &.status3 {
    color: #fff;
    background: linear-gradient(to right, #ff8533 0%, #ff7213 100%);
  }
  &.status4 {
    color: #fff;
    background: linear-gradient(to right, #ccc 0%, #ccc 100%);
  }
}
</style>