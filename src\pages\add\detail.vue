<template>
  <view>
    <!-- 小区 -->
    <view class="block quick_copy" v-if="parentid == 1 || parentid == 2">
      <view class="open_menu flex-row" @click="show_copy_info = !show_copy_info">
        <view class="flex-row quick_copy_left">
          <view class="quick_copy_left_img">
            <image mode="widthFixed" :src="`${ossDomain}/fabu/zhinengpipei.png`"></image>
          </view>
          <text>智能识别</text>
        </view>
        <my-icon :type="show_copy_info ? 'ic_close' : 'ic_open'" color="#999"></my-icon>
      </view>
      <view class="copy_content" v-if="show_copy_info">
        <textarea name="copy_content" id="copy_content" placeholder="请粘贴需要智能识别的内容" @input="handleContentChange"
          v-model="copy_content" rows="3"></textarea>
      </view>
    </view>

    <!-- 小区 -->
    <view class="block">
      <view @click="toSearchCommunity" v-if="release_need_select_community == 1">
        <my-input label="小区" show_arrow :value="community_name" disabled :type="form.content.type"
          placeholder="请选择小区或留空"></my-input>
      </view>
      <my-picker ref="catPicker" :options="form.leixing.options" @change="onPickerChange"></my-picker>
      <view class="select_btn flex-row" @click="showAreaPopup">
        <view class="left">
          <text class="label">区域</text>
          <text class="value" :class="{ has_value: area_name }">{{ area_name || '请选择' }}</text>
        </view>
        <view>
          <my-icon type="ic_into" size="36rpx" color="#999"></my-icon>
        </view>
      </view>
      <my-popup ref="area_popup">
        <addressPicker :data_list="area_list" @onselect="onAreaChange"></addressPicker>
      </my-popup>
    </view>
    <!-- 图片 -->
    <view class="block" v-if="parentid == '1' || parentid == '2'">
      <view class="upload-box flex-row" @click="addPhotos">
        <image class="cover_img" v-if="params.cover_path" :src="params.cover_path"></image>
        <view class="upload_btn" v-else>
          <my-icon type="ic_jia" size="60rpx" color="#d8d8d8"></my-icon>
        </view>
        <view class="upload_tip flex-1">
          <text class="tip_title">上传照片</text>
          <text class="tip_content">只能上传房屋图片,不能含有文字、数字、网址、名片、水印等，所有类别图片总计20张。</text>
        </view>
        <my-icon type="ic_into" color="#999"></my-icon>
      </view>
    </view>
    <!-- 户型 -->
    <view class="block" v-if="parentid == '1' || parentid == '2'">
      <view class="multiple_input" v-if="open_release_info_loudong == 1">
        <text class="label">房屋坐落</text>
        <view class="flex-row">
          <view class="input-item flex-row flex-5">
            <input v-model="params.loudong" placeholder="栋/幢/弄/胡同" placeholder-style="font-size:28rpx;color:#999" />
            <text class="unit">号楼</text>
          </view>
          <view class="input-item flex-row flex-4">
            <input v-model="params.danyuan" placeholder="单元号" placeholder-style="font-size:28rpx;color:#999" />
            <text class="unit">单元</text>
          </view>
          <view class="input-item flex-row flex-3">
            <input v-model="params.fanghao" placeholder="门牌号" placeholder-style="font-size:28rpx;color:#999" />
            <text class="unit">室</text>
          </view>
        </view>
      </view>
      <my-picker ref="huxingPicker" :options="form.huxing.options" @change="onPickerChange"></my-picker>
      <view class="multiple_input flex-row">
        <view class="input-item flex-row">
          <text class="label">{{ form.szlc.title }}</text>
          <input type="number" v-model="params[form.szlc.identifier]" placeholder="请填写"
            placeholder-style="font-size:28rpx;color:#999" />
          <text class="unit">{{ form.szlc.rules }}</text>
        </view>
        <view class="input-item flex-row">
          <text class="label">共</text>
          <input type="number" v-model="params[form.louceng.identifier]" placeholder="请填写"
            placeholder-style="font-size:28rpx;color:#999" />
          <text class="unit">{{ form.louceng.rules }}</text>
        </view>
      </view>
    </view>
    <!-- 面积和房价 -->
    <view class="block">
      <my-input v-if="parentid !== '3'" :label="form.mianji.title" :unit="form.mianji.rules"
        v-model="params[form.mianji.identifier]" :type="form.mianji.type" placeholder="请输入"
        @input="setTitle"></my-input>
      <my-input :label="form.fangjia.title" :unit="form.fangjia.rules" v-model="params[form.fangjia.identifier]"
        :type="form.mianji.type" placeholder="请输入" @input="setTitle"></my-input>
    </view>
    <!-- 标签 -->
    <view class="block" v-if="form.labels.length > 0">
      <my-checkbox :values="labels_value" :maxnum="4" @select="handelCheckLabel" label="标签"
        :range="form.labels"></my-checkbox>
    </view>
    <!-- 标题 -->
    <view class="block">
      <my-input label="标题" show_arrow v-model="params[form.title.identifier]" :type="form.title.type" placeholder="请输入"
        @input="onInputTitle">
        <template v-slot:label_right>
          <text v-if="parentid <= 2" class="template_title" @click="showTemplateTitle">智能标题</text>
        </template>
      </my-input>
      <view @click="toAddDesc">
        <my-input label="描述" show_arrow v-model="params[form.content.identifier]" disabled :type="form.content.type"
          placeholder="请输入"></my-input>
      </view>
    </view>
    <!-- 联系人 -->
    <view class="block">
      <my-input label="联系人" show_arrow @input="handleConcatInput" v-model="params[form.contact_who.identifier]"
        :type="form.contact_who.type" placeholder="请输入">
        <template v-slot:label_right>
          <text class="template_title"></text>
        </template>
      </my-input>
    </view>
    <!-- 完善更多非必填的信息 -->
    <view class="block" v-if="formList.length > 0">
      <view class="open_menu flex-row" @click="show_more_info = !show_more_info">
        <view class="flex-row">
          <text>完善更多信息</text>
          <text class="tip">（非必填）</text>
        </view>
        <my-icon :type="show_more_info ? 'ic_close' : 'ic_open'" color="#999"></my-icon>
      </view>
      <view class="more_info_box" :class="{ open: show_more_info }">
        <block v-for="(item, index) in formList" :key="index">
          <my-input v-if="inputType.includes(item.type)" v-model="params[item.identifier]" :label="item.title"
            :unit="item.rules" :type="item.type" placeholder="请输入"></my-input>
          <my-select v-if="item.type == 'select'" :value="item.value[0] || ''" @change="pickerChange"
            :label="item.title" :range="item.rules" :name="item.identifier" placeholder="请选择"></my-select>
          <my-radio v-if="item.type == 'radio'" :value="item.value[0]" @change="radioChange" :label="item.title"
            :range="item.rules" :name="item.identifier"></my-radio>
          <my-checkbox v-if="item.type == 'checkbox'" :values="item.value" @select="handelCheckbox" :label="item.title"
            :range="item.rules" :name="item.identifier"></my-checkbox>
        </block>
      </view>
    </view>
    <!-- 房源统一核验 -->
    <view class="block" v-if="(parentid == '1' || parentid == '2') && if_info_verification_code == 1">
      <view class="open_menu flex-row" @click="addVerification()">
        <view class="flex-row">
          <text>房源统一核验</text>
        </view>
        <my-icon type="ic_into" color="#999"></my-icon>
      </view>
    </view>
    <!-- 房源内部编号 -->
    <view class="block" v-if='open_info_internal_no'>
      <my-input label="房源内部编号" show_arrow v-model="params.internal_no" placeholder="请输入">

      </my-input>
    </view>
    <view class="block" v-if="!freeActiveTime">
      <view class="open_menu flex-row">
        <text>有效期</text>
      </view>
      <view class="options-box flex-row">
        <view class="options" :class="{ active: item.id === params.activetime_id }"
          @click="params.activetime_id === item.id ? (params.activetime_id = '') : (params.activetime_id = item.id)"
          v-for="item in youxiaoqi" :key="item.id">
          <text class="title">{{ item.name }}</text>
          <view class="price" v-if="youxiaoqiHasMoney">
            <!-- <text>{{ item.money }}</text>
            <text class="unit">元</text> -->
          </view>
          <text class="tip" v-if="youxiaoqiHasMoney">￥{{ item.money }}</text>
        </view>
        <view class="options vacancy"></view>
      </view>
    </view>
    <view class="block" v-else>
      <view class="open_menu flex-row">
        <text>有效时间</text>
      </view>
      <view class="options-box flex-row">
        <view class="options" :class="{ active: item.id === params.activetime_id }" @click="onClickTime(item)"
          v-for="item in youxiaoqi" :key="item.id">
          <text class="title">{{ item.name }}</text>
          <view class="price" v-if="item.money">
            <!-- <text>{{ item.money }}</text>
            <text class="unit">元</text> -->
          </view>
          <text class="tip" v-if="item.money">￥{{ item.money }}</text>
        </view>
        <view class="options vacancy"></view>
      </view>
    </view>
    <!-- 选择推广方式 -->
    <view class="block">
      <view class="open_menu flex-row" @click="show_pay_info = !show_pay_info">
        <view class="flex-row">
          <text>推广方式</text>
          <view class="flex-row">
            <text class="ding">顶</text>
            <text class="jing">精</text>
          </view>
        </view>
        <my-icon :type="show_pay_info ? 'ic_close' : 'ic_open'" color="#999"></my-icon>
      </view>
      <view class="more_info_box" :class="{ open: show_pay_info }">
        <view class="tips" v-if="extension_type == 'jing'">精选后可使房源保持前列，每半小时刷新一次，直至次数用完为止</view>
        <view class="types flex-row">
          <view class="type-item" :class="{ active: extension_type === 'ding' }" @click="extension_type = 'ding'">置顶
          </view>
          <view class="type-item" :class="{ active: extension_type === 'jing' }" @click="extension_type = 'jing'">精选
          </view>
        </view>
        <view class="options-box flex-row" v-show="extension_type === 'ding'">
          <view class="options" :class="{ active: ding_item.id === params.tops_id }" @click="onClickTop(ding_item)"
            v-for="ding_item in extension_ding" :key="ding_item.id">
            <text class="title">置顶天数</text>
            <view class="price">
              <text>{{ ding_item.name }}</text>
              <!-- <text class="unit">元</text> -->
            </view>
            <text v-if="use_meal_ding" class="tip">{{ ding_item.title }}</text>
            <text v-else class="tip">￥{{ ding_item.money }}</text>
          </view>
          <view class="options vacancy"></view>
        </view>
        <view class="options-box flex-row" v-show="extension_type === 'jing'">
          <view v-if="freeSelected.name && !use_meal_jing" class="options" :class="{ active: params.freeSelected }"
            @click="onClickFreeJingxuan()">
            <text class="title">精选刷新次数</text>
            <view class="price">
              <text>{{ freeSelected.name }}</text>
              <!-- <text class="unit">元</text> -->
            </view>
            <text class="tip">￥{{ freeSelected.money }}</text>
          </view>
          <view class="options" :class="{ active: jing_item.id === params.jingxuan_id }"
            @click="onClickJingxuan(jing_item)" v-for="jing_item in extension_jing" :key="jing_item.id">
            <text class="title">精选刷新次数</text>
            <view class="price">
              <text>{{ jing_item.name }}</text>
              <!-- <text class="unit">元</text> -->
            </view>
            <text v-if="use_meal_jing" class="tip">{{ jing_item.title }}</text>
            <text v-else class="tip">￥{{ jing_item.money }}</text>
          </view>
          <view class="options vacancy"></view>
        </view>
        <view class="tip">再次点击可取消</view>
      </view>
    </view>
    <view class="tips_box flex-row" v-if="memberVip.length > 0">
      <template v-for="(item, index) in memberVip">
        <view class="tips_box_item" :class="{ 'active': currentType == item.type }"
          v-if="item.type != 'check_release' || (item.type == 'check_release' && !params.tops_id && !params.jingxuan_id)"
          @click="changeMemberVip(item.type)" :key="index">
          <view class="title_box flex-row">
            <view class="tips_box_title">{{ item.title }}</view>
            <view class="icon">
              <image :src="icon_bg" mode="widthFix"></image>
            </view>
          </view>
          <view class="sanjiao" v-if="item.type == 'upgrade_release'"></view>
          <view class="tuijian" v-if="item.type == 'upgrade_release'">
            荐
          </view>
          <view class="money_box">
            {{ item.pay }}
          </view>
          <view class="tip_con">
            {{ item.descp }}
          </view>
          <view class="tip_box_bg">
            <image :src="item.type | filterBg()" mode="widthFix"></image>
          </view>
        </view>
      </template>
    </view>
    <!-- <view class="quick_copy flex-row">
        <view class="copy_icon flex-box" :class="{copy_icon_show:showIcon}" @click ="showIcon=false">
          <my-icon type="huodongxiangqu"></my-icon>
          <text class ="copy_name">智能识别</text>
        </view>
        <view class="copy_content flex-1" :class="{copy_content_show:!showIcon}">
         <textarea @input ="handleContentChange" @blur ="showIcon=true" name="copy_content"   cols="30" rows="3" v-model ="copy_content" :focus="!showIcon" placeholder="请输入需要智能识别的内容"></textarea>
        </view>
        
    </view> -->
    <!-- <view class="mask1" v-if ="!showIcon" @click ="showIcon=true"></view> -->

    <view class="rule flex-row">
      <label class="flex-box" style="align-items:center" @click="on_ready = !on_ready">
        <checkbox color="#ff656c" checked></checkbox>
        <text>已阅读并接受</text>
      </label>
      <text class="rule_link"
        @click="$navigateTo('/user/agreement?type=push_info')">《{{ $store.state.siteName }}房源信息发布规则》</text>
    </view>
    <view class="money_tip" style="margin-top: 24rpx"
      v-if="use_middle_call_house === 1 || use_middle_call_house === 2 && levelid >= 2 || use_middle_call_house === 3 && levelid <= 1">
      <my-icon type="tishifu" color="#fb656a" size="36rpx"></my-icon>
      <text class="text flex-1">为避免恶意骚扰，系统已为你开启隐私号保护，呼叫方将看不到你的真实号码</text>
    </view>
    <view class="btn-box">
      <view class="btn" @click="subData">立即发布</view>
    </view>
    <myPopup ref="title_popup">
      <scroll-view class="title_list" scroll-y>
        <view class="title_item bottom-line" :class="{ active: params.title === item }"
          v-for="(item, index) in title_template_list" :key="index" @click="selectTitle(item, index)">{{ item }}</view>
      </scroll-view>
    </myPopup>
    <showModel :is_show="showModel" :content="modelContent" cancelText="立即管理房源" confirmText='确认继续发布' @confirm="confirm"
      @cancel="cancel" @close="close"></showModel>
  </view>
</template>

<script>
import myInput from '../../components/form/newInput.vue'
import myIcon from '../../components/myIcon.vue'
import myPicker from '../../components/myPicker.vue'
import showModel from '../../components/showModel.vue'
import mySelect from '../../components/form/mySelect.vue'
import myRadio from '../../components/form/myRadio.vue'
import myCheckbox from '../../components/form/myCheckbox.vue'
import myDialog from '../../components/dialog.vue'
import myPopup from '../../components/myPopup.vue'
import addressPicker from '../../components/addressPicker.vue'
import { checkAuth, showModal, config } from '../../common/index.js'
import { mapState, mapMutations } from 'vuex'
var tel_reg = {
  key: 'tel',
  reg: new RegExp('(1[3-9][0-9]{9})'),
}
export default {
  components: {
    myInput,
    myIcon,
    myPicker,
    mySelect,
    myRadio,
    myCheckbox,
    myDialog,
    myPopup,
    showModel,
    addressPicker
  },

  data() {
    return {
      formList: [],
      area_list: [],
      open_release_info_loudong: 0,
      release_need_select_community: 0,//是否开启选择小区
      levelid: "",
      memberVip: [],
      currentType: 'upgrade_release',
      form: {
        szlc: {
          type: 'number',
          title: '所在楼层',
          rules: '层',
          identifier: 'szlc',
          required: "true",
          value: ''
        },
        louceng: {
          type: 'number',
          title: '总楼层',
          rules: '层',
          identifier: 'louceng',
          required: "true",
          value: ''
        },
        mianji: {
          type: 'digit',
          title: '建筑面积',
          rules: 'm²',
          identifier: 'mianji',
          required: "true",
          value: ''
        },
        fangjia: {
          type: 'digit',
          title: '售价',
          rules: '万元',
          identifier: 'fangjia',
          value: ''
        },
        huxing: {
          type: 'picker',
          value: [],
          options: [
            {
              label: '户型',
              value: [],
              required: true,
              range: [
                {
                  title: '室',
                  identifier: 'shi',
                  rules: []
                },
                {
                  title: '厅',
                  identifier: 'ting',
                  rules: []
                },
                {
                  title: '卫',
                  identifier: 'wei',
                  rules: []
                }
              ]
            },
            {
              label: '朝向',
              value: [],
              required: true,
              range: [
                {
                  title: '',
                  identifier: 'chaoxiang',
                  rules: []
                }
              ]
            },
            {
              label: '装修',
              value: [],
              required: true,
              range: [
                {
                  title: '',
                  identifier: 'zhuangxiu',
                  rules: []
                }
              ]
            }
          ]
        },
        leixing: {
          type: 'picker',
          value: [0],
          options: [
            {
              label: '房产类型',
              value: [],
              required: true,
              range: [
                {
                  title: '',
                  identifier: 'catid',
                  rules: []
                }
              ]
            },
            // {
            //   label: '区域',
            //   value: [],
            //   required: true,
            //   range: [
            //     {
            //       title: '',
            //       identifier: 'areaid',
            //       rules: []
            //     }
            //   ]
            // }
          ]
        },
        labels: [],
        title: {
          title: '标题',
          value: '',
          identifier: 'title',
          required: "true",
          type: 'text'
        },
        content: {
          title: '描述',
          value: '',
          required: "true",
          identifier: 'content',
          type: 'text'
        },
        contact_who: {
          title: '联系人',
          value: '',
          required: "true",
          identifier: 'contact_who',
          type: 'text'
        },
        loudong: "",
        danyuan: "",
        fanghao: "",
      },
      showModel: false,
      modelContent: "",
      modelData: {},
      show_more_info: false,
      show_pay_info: false,
      youxiaoqi: [],
      youxiaoqiHasMoney: false,
      extension_type: 'ding',
      extension_ding: [],
      extension_jing: [],
      freeSelected: {},
      community_name: '',
      area_name: '',
      inputType: ['number', 'text', 'tel', 'digit'],
      parentid: '',
      imgList: [],
      imgCount: 9,
      videoList: [],
      freeActiveTime: 0,
      releasePrice: '',
      params: {
        title: '',
        catid: '',
        areaid: '',
        address: '',
        mianji: '',
        zhuangxiu: '',
        cover_path: '',
        videos: '',
        imgs: '',
        imgs1: '',
        imgs2: '',
        contact_who: '',
        content: '',
        owner_think: '',
        service_introduce: '',
        release_type: 'upgrade_release',
        activetime_id: '', //有效期id
        tops_id: '', //置顶套餐id
        jingxuan_id: '', //精选套餐id
        freeSelected: '',// 是否使用免费的精选
        internal_no: "",   //房源内部编号
      },
      show_dialog: false,
      on_ready: true,
      upgrade_tip: {},
      use_meal_ding: false,
      use_meal_jing: false,
      title_template_list: [],
      title_template_index: 0,
      labels_value: [],
      // showIcon:true,
      show_copy_info: false,
      copy_content: '',
      open_info_internal_no: 0
    }

  },
  onLoad(options) {
    // uni.showLoading({
    // 	title:"加载中..."
    // })
    // #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
    uni.hideShareMenu()
    // #endif
    if (options.catid) {
      this.parentid = options.catid
      this.localKey = 'ershouSale'
      switch (this.parentid) {
        case "2":
          this.form.fangjia.title = "租金"
          this.form.fangjia.identifier = "zujin"
          this.form.fangjia.rules = "元/月"
          this.localKey = 'ershouRent'
          break;
        case "3":
          this.form.fangjia.title = "求租预算"
          this.form.fangjia.identifier = "qzprice"
          this.form.fangjia.rules = "元/月"
          this.form.szlc = {}
          this.form.louceng = {}
          this.form.mianji = {}
          this.localKey = 'qiuzu'
          break;
        case "4":
          this.form.fangjia.title = "求购预算"
          this.form.fangjia.identifier = "qgprice"
          this.form.mianji.identifier = "qgmianji"
          this.form.szlc = {}
          this.form.louceng = {}
          this.localKey = 'qiugou'
          break;
      }
    }
    setTimeout(() => {
      //解决连续跳转页面会卡顿的问题
      // this.getPageInfo()
      var draft = uni.getStorageSync(this.localKey)
      if (draft) {
        draft = JSON.parse(draft)
        uni.showModal({
          title: '',
          content: '您有未发布的信息是否重新编辑',
          success: (res) => {
            if (res.confirm) {
              this.editDraft = true
              this.tempParams = draft
              this.getPageInfo()
            } else if (res.cancel) {
              console.log('用户点击取消');
              this.getPageInfo()
              this.removeDraft()
            }
          }
        });
      } else {
        this.getPageInfo()
      }
    }, 200)
    this.getWxConfig(
      ['chooseWXPay', 'hideOptionMenu'],
      wx => {
        console.log('执行回调')
        this.wx = wx
      },
      1
    )
    uni.$on('transmit', e => {
      this.params.cover_path = e.cover_path || e.indoor_images[0] || ''
      this.params.videos = e.indoor_videos.join(',') //视频
      this.params.imgs1 = e.indoor_images.join(',') //室内图
      this.params.imgs2 = e.house_type.join(',') //户型图
      this.params.imgs = e.outdoor.join(',') //室外图
    })
    uni.$on('transmitDesc', e => {
      this.params.content = e.content
      this.params.owner_think = e.owner_think
      this.params.service_introduce = e.service_introduce
    })
    uni.$on('transmitVerification', (e) => {
      this.params.verification_code = e.verification_code
      this.params.verification_qrcode = e.verification_qrcode
    })
  },
  onHide() {
    this.setAllowOpen(true)
  },
  onUnload() {
    this.setAllowOpen(true)
    if (!this.isSubmit) {
      var temParams = Object.assign({}, this.params)
      temParams.cname = this.community_name
      for (let key in temParams) {
        if (key != "contact_who" && key != "release_type" && temParams[key]) {
          uni.setStorageSync(this.localKey, JSON.stringify(temParams))
        }
      }
    }
    // this.imgList = []
    // this.videoList = []
  },
  computed: {
    ...mapState(['allowOpen', 'use_middle_call_house', 'if_info_verification_code']),
    icon_bg() {
      return config.imgDomain + "/fabu/<EMAIL>"
    },
    ossDomain() {
      return config.imgDomain
    }
  },
  filters: {
    filterBg(val) {
      switch (val) {
        case 'pay_release':
          return config.imgDomain + '/fabu/<EMAIL>'
          break;
        case 'upgrade_release':
          return config.imgDomain + '/fabu/<EMAIL>'
          break;
        case 'check_release':
          return config.imgDomain + '/fabu/<EMAIL>'
          break;
        default:
          break;
      }
    }
  },
  onShow() {
    if (!this.allowOpen) {
      this.getPageInfo()
    }
    if (uni.getStorageSync('smallArea')) {
      let smallArea = JSON.parse(uni.getStorageSync('smallArea'))
      this.params.buildid = smallArea.id
      this.community_name = smallArea.community_name || smallArea.name
      this.params.areaid = smallArea.areaid
      this.area_name = this.getArea(this.area_list, smallArea.areaid).areaname
      // this.params.areaid = smallArea.areaid || ''
      this.params.address = smallArea.address || ''
      this.params.lat = smallArea.lat
      this.params.lng = smallArea.lng
      this.setTitle()
    }
    uni.removeStorageSync('smallArea')
  },
  methods: {
    ...mapMutations(['setAllowOpen']),
    addPhotos() {
      let photos = {
        indoor_images: this.params.imgs1 ? this.params.imgs1.split(',') : [],
        indoor_videos: this.params.videos ? this.params.videos.split(',') : [],
        house_type: this.params.imgs2 ? this.params.imgs2.split(',') : [],
        outdoor: this.params.imgs ? this.params.imgs.split(',') : [],
      }
      if (this.levelid > 1) {
        photos.cover_path = this.params.cover_path
      }
      this.$store.state.photos = photos
      this.$navigateTo(`/user/upload_info_img?cid=${this.params.buildid || ''}&levelid=${this.levelid}`)
    },
    onPickerChange(e) {
      e.forEach(item => {
        item.forEach(el => {
          this.params[el.identifier] = el.value
        })
      })
      this.setTitle()
    },
    changeMemberVip(type) {
      this.currentType = type
      this.params.release_type = type
    },

    handleOtherData(resData) {
      this.formList = resData.map(item => {
        if (!item.value) {
          item.value = []
        }
        return item
      })
      if (this.editDraft) {
        this.formList.map(item => {
          this.params[item.identifier] = item.value.join(',')
        })
      }
      // #ifdef MP-BAIDU
      // 百度小程序如果刚开始没有图片上传第一张不会显示,所以默认添加一张空的图片，然后在上传组件在删除掉这个空的图片
      this.imgList = ['']
      this.videoList = ['']
      // #endif
    },
    // 处理发布的选项
    handleData(data) {
      this.community_name = data.cname ? data.cname : this.community_name;
      for (let key in data) {
        // 去掉本地存储的小区名称
        if (key !== "cname" && key != "areaname") {
          this.params[key] = data[key]
        }
      }
      if (typeof data.label == "string") {
        data.label = data.label.split(",")
      }
      //数据类型转换防止下边用到因类型错误报错
      if (!data.label) {
        data.label = []
      }
      if (typeof data.label === 'object') {
        this.params.label = data.label.join(',')
      }
      if (this.form.huxing.options[0].range.length > 0) {
        let huxing_value = []
        let shi_index = this.form.huxing.options[0].range[0].rules.findIndex(item => item.value === data.shi)
        let ting_index = this.form.huxing.options[0].range[1].rules.findIndex(item => item.value === data.ting)
        let wei_index = this.form.huxing.options[0].range[2].rules.findIndex(item => item.value === data.wei)
        if (shi_index >= 0) huxing_value.push(shi_index)
        if (ting_index >= 0) huxing_value.push(ting_index)
        if (wei_index >= 0) huxing_value.push(wei_index)
        this.form.huxing.options[0].value = huxing_value
      }
      if (this.form.huxing.options[1].range.length > 0) {
        let chaoxiang_index = this.form.huxing.options[1].range[0].rules.findIndex(item => item.value === parseInt(data.chaoxiang))
        if (chaoxiang_index >= 0) this.form.huxing.options[1].value = [chaoxiang_index]
      }
      if (this.form.huxing.options[2].range.length > 0) {
        let zhuangxiu_index = this.form.huxing.options[2].range[0].rules.findIndex(item => item.value === parseInt(data.zhuangxiu))
        if (zhuangxiu_index >= 0) this.form.huxing.options[2].value = [zhuangxiu_index]
      }
      this.$refs.huxingPicker.initValue()
      this.$refs.catPicker.initValue()
      if (data.label) {
        this.labels_value = data.label.map(item => parseInt(item))
      }
      // this.labels_value = data.label.map(item=>parseInt(item))
      let type_index = this.form.leixing.options[0].range[0].rules.findIndex(item => item.value === data.catid)
      if (type_index >= 0) {
        this.form.leixing.options[0].value = [type_index]
      } else {
        this.form.leixing.options[0].value = []
      }
      this.params.content = this.copy_content.replace(tel_reg.reg, '').replace(/(联系电话|手机号|电话)：?/, '')
      this.setTitle()
    },
    getPageInfo() {
      this.$ajax.get('release/getReleaseFieldValue.html', { parentid: this.parentid }, res => {
        uni.hideLoading()
        if (res.data.code === -13) {
          this.upgrade_type = 'add'
          // 提示扣除金币
          showModal({
            content: res.data.msg,
            confirm: () => {
              console.log('执行升级个人vip')
              this.upgrade()
            },
            cancel: () => {
              this.$navigateBack()
            }
          })
          return
        }
        if (res.data.code == 1) {
          let memberVip = res.data.memberVip
          if (memberVip.length > 0) {
            let item = {}
            for (var i = 0; i < memberVip.length; i++) {
              if (memberVip[i].type === 'upgrade_release') {
                item = memberVip[i]
                memberVip.splice(i, 1);

                break;
              }
            }
            memberVip.unshift(item);
            this.memberVip = memberVip
          }
          this.upgrade_tip = res.data.tips
          this.levelid = res.data.levelid || 2
          this.open_release_info_loudong = res.data.release_info_loudong
          this.release_need_select_community = res.data.release_need_select_community
          this.open_info_internal_no = res.data.open_info_internal_no
          this.imgCount = res.data.maxUploadNum
          this.$store.state.imgSize = res.data.maxUploadSize
          this.form.huxing.options[0].range = res.data.list
            .filter(item => {
              return ['shi', 'ting', 'wei'].includes(item.identifier)
            })
            .map(item => {
              // 处理室厅卫名称为数字的问题
              item.rules.map(el => {
                if (!isNaN(el.name)) {
                  el.name = el.name + item.title
                }
                return el
              })
              return item
            })
          this.form.huxing.options[1].range = res.data.list.filter(item => item.identifier === 'chaoxiang')
          this.form.huxing.options[2].range = res.data.list.filter(item => item.identifier === 'zhuangxiu')
          this.form.leixing.options[0].range[0].rules = res.data.catelist
          // this.form.leixing.options[1].range[0].rules = res.data.arealist.map(item => {
          //   return { value: item.areaid, name: item.areaname }
          // })
          this.form.labels = res.data.labels.map(item => {
            return {
              name: item.name,
              value: item.id
            }
          })
          this.releasePrice = res.data.releasePrice
          this.freeActiveTime = res.data.freeActiveTime
          this.youxiaoqi = res.data.activeTime
          this.youxiaoqiHasMoney = this.youxiaoqi.some(item => parseFloat(item.money))
          if (res.data.packagesInfoTops && res.data.packagesInfoTops.length > 0) {
            this.extension_ding = res.data.packagesInfoTops
            this.use_meal_ding = true
          } else {
            this.extension_ding = res.data.tops
          }
          if (res.data.packagesInfoSelecteds && res.data.packagesInfoSelecteds.length > 0) {
            this.extension_jing = res.data.packagesInfoSelecteds
            this.use_meal_jing = true
          } else {
            this.extension_jing = res.data.jingxuan
          }
          this.freeSelected = res.data.freeSelected
          this.params.contact_who = res.data.contact_who
          this.otherData = res.data.otherlist  //智能识别需要处理这个数据暂时存储下来
          if (this.editDraft) {  //自动保存处理数据
            if (typeof this.tempParams.wnsb == "string") {
              this.tempParams.wnsb = this.tempParams.wnsb.split(',')
            }
            if (typeof this.tempParams.peitao == "string") {
              this.tempParams.peitao = this.tempParams.peitao.split(',')
            }
            res.data.otherlist = res.data.otherlist.map(item => {
              Object.keys(this.tempParams).map(key => {
                if (item.identifier == key) {
                  item.value = [this.tempParams[key]].flat(Infinity).map(Number)
                }
              })
              return item
            })
          }
          this.$nextTick(() => {
            this.area_list = res.data.area
            if (this.editDraft) {
              var current_area = this.getArea(this.area_list, this.tempParams.areaid)
              if (current_area) {
                this.area_name = current_area.areaname
              }

            }
          })
          // setTimeout(() => {
          if (this.editDraft) {
            this.handleData(this.tempParams)
          }
          this.handleOtherData(res.data.otherlist)
          // }, 200);

          // this.handleData(res.data.otherlist)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    showAreaPopup() {
      this.$refs.area_popup.show()
    },
    onAreaChange(e) {
      this.area_name = e[e.length - 1].name
      this.params.areaid = e[e.length - 1].value
      this.$refs.area_popup.hide()
    },
    handelCheckLabel(e) {
      this.params.label = e.value.join(',')
    },
    pickerChange(e) {
      uni.hideKeyboard()
      this.params[e._name] = e.value
      this.setTitle()
    },
    radioChange(e) {
      uni.hideKeyboard()
      this.params[e._name] = e.detail.value
    },
    handelCheckbox(e) {
      uni.hideKeyboard()
      this.params[e._name] = e.value.join(',')
    },
    onInputTitle() {
      this.title_template_index = -1
    },
    setTitle() {
      let zhuangxiu = ''
      if (this.params.zhuangxiu) {
        this.formList.forEach(item => {
          if (item.identifier === 'zhuangxiu') {
            item.rules.forEach(c_item => {
              if (c_item.value === this.params.zhuangxiu) {
                zhuangxiu = c_item.name
                this.current_zhuangxiuname = zhuangxiu
              }
            })
          }
        })
      }
      if (this.parentid == 1 || this.parentid == 2) {
        this.createTitle()
      }
      // var title = `${this.community_name}${this.params.shi === undefined ? '' : this.params.shi + '室'}${
      //   this.params.ting === undefined ? '' : this.params.ting + '厅'
      // }${this.params.wei === undefined ? '' : this.params.wei + '卫'}${
      //   this.parentid == 1&&this.params.fangjia?this.params.fangjia+'万':(this.params.zujin?this.params.zujin+'元/月':'')}${
      //   this.params.mianji === undefined ? '' : this.params.mianji + 'm²'
      // }${zhuangxiu}`
      // if(title){
      //   this.params.title = title + (this.parentid==1?'出售':'出租')
      // }
    },
    toSearch() {
      this.$navigateTo('/user/search_areas')
    },
    toAddDesc() {
      setTimeout(() => {
        uni.$emit('giveDesc', { content: this.params.content, owner_think: this.params.owner_think, service_introduce: this.params.service_introduce })
      }, 200)
      this.$navigateTo(`/user/add/add_desc?levelid=${this.levelid}&parentid=${this.parentid}`)
    },
    addVerification() {
      setTimeout(() => {
        uni.$emit('giveVerification', {
          verification_code: this.params.verification_code,
          verification_qrcode: this.params.verification_qrcode,
        })
      }, 200)
      this.$navigateTo(`/user/add_verification`)
    },
    onClickTime(item) {
      this.params.activetime_id === item.id ? (this.params.activetime_id = '') : (this.params.activetime_id = item.id)
    },
    onClickTop(ding_item) {
      this.params.tops_id === ding_item.id ? (this.params.tops_id = '') : (this.params.tops_id = ding_item.id)
    },
    onClickFreeJingxuan() {
      (this.params.freeSelected ? (this.params.freeSelected = '') : (this.params.freeSelected = 1)) && (this.params.jingxuan_id = '')
    },
    onClickJingxuan(jing_item) {
      (this.params.jingxuan_id === jing_item.id ? (this.params.jingxuan_id = '') : (this.params.jingxuan_id = jing_item.id)) && (this.params.freeSelected = '')
    },
    toSearchCommunity() {
      this.$navigateTo('/user/search_areas')
      uni.$once('newCommunity', (data) => {
        this.community_name = data.name
        this.params.buildid = data.id
        this.params.areaid = data.areaid
        this.area_name = this.getArea(this.area_list, data.areaid)
        // this.onAreaChange ({
        //   name:"d大同天下",
        //   areaid:data.areaid
        // })
      })
    },
    getArea(arr, id) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].areaid == id) {
          return arr[i]
          break;
        }
        if (arr[i].children && arr[i].children.length > 0) {
          let res = this.getArea(arr[i].children, id)
          if (res) {
            return res
          }
        }
      }
    },
    mapMark() {
      // 标注
      // #ifdef APP-PLUS
      plus.key.hideSoftKeybord()
      uni.chooseLocation({
        keyword: '',
        success: res => {
          this.params.address = res.address
          this.params.lat = res.latitude
          this.params.lng = res.longitude
        }
      })
      // uni.getLocation({
      // 	type: 'gcj02',
      // 	geocode: true,
      // 	success: (res)=> {
      // 		console.log(res)
      // 		uni.chooseLocation({
      // 			keyword: res.address.district+res.address.street,
      // 			success: (res)=> {
      // 				this.params.address = res.address
      // 				this.params.lat = res.latitude
      // 				this.params.lng = res.longitude
      // 			}
      // 		})
      // 	},
      // 	fail:()=>{
      // 		console.log("定位失败")
      // 		uni.chooseLocation({
      // 			keyword:"",
      // 			success: (res)=> {
      // 				this.params.address = res.address
      // 				this.params.lat = res.latitude
      // 				this.params.lng = res.longitude
      // 			}
      // 		})
      // 	}
      // });
      // #endif
      // #ifdef MP
      checkAuth('scope.userLocation', {
        authOk: () => {
          uni.chooseLocation({
            keyword: '',
            success: res => {
              this.params.address = res.address
              this.params.lat = res.latitude
              this.params.lng = res.longitude
            }
          })
        },
        success: () => {
          uni.chooseLocation({
            keyword: '',
            success: res => {
              this.params.address = res.address
              this.params.lat = res.latitude
              this.params.lng = res.longitude
            }
          })
        },
        fail: () => {
          this.show_dialog = true
        }
      })
      // #endif
      // #ifdef H5
      uni.chooseLocation({
        keyword: '',
        success: res => {
          this.params.address = res.address
          this.params.lat = res.latitude
          this.params.lng = res.longitude
        }
      })
      // #endif
    },
    searchAreaName(key, callback) {
      if (!key || key == '') {
        return
      }
      this.$ajax.get('house/searchCommunity.html', { keyword: key }, (res) => {
        if (res.data.code == 1) {
          this.houseList = res.data.list
          callback && callback()
        } else {
          this.houseList = []
        }
      }, (err) => {

      }, false)
    },
    handleContentChange(v) {
      var str = v.detail.value
      this.$ajax.get("release/getHouseMatching", { parentid: this.parentid, keywords: str }, (res) => {
        if (res.data.code == 1) {
          this.handleData(res.data.data)
          if (res.data.data.peitao || res.data.data.wnsb || res.data.data.danjia) {
            if (typeof res.data.data.wnsb == "string") {
              res.data.data.wnsb = res.data.data.wnsb.split(',')
            }
            if (typeof res.data.data.peitao == "string") {
              res.data.data.peitao = res.data.data.peitao.split(',')
            }
            this.otherData = this.otherData.map(item => {
              Object.keys(res.data.data).map(key => {
                if (item.identifier == key) {
                  item.value = [res.data.data[key]].flat(Infinity).map(Number)
                }
              })
              return item
            })
            this.editDraft = true
            this.handleOtherData(this.otherData)
            setTimeout(() => {
              this.editDraft = false
            }, 200);
          }
        }
      })
    },
    subData() {
      // 表单验证
      if (!this.params.catid) {
        uni.showToast({
          title: '请选择房产类型',
          icon: 'none'
        })
        return
      }
      if (!this.params.areaid) {
        uni.showToast({
          title: '请选择区域',
          icon: 'none'
        })
        return
      }
      if (this.parentid == '1' || this.parentid == '2') {
        if (this.params.shi === undefined) {
          uni.showToast({
            title: "请选择室",
            icon: 'none'
          })
          return
        }
        if (this.params.ting === undefined) {
          uni.showToast({
            title: "请选择厅",
            icon: 'none'
          })
          return
        }
        if (this.params.wei === undefined) {
          uni.showToast({
            title: "请选择卫",
            icon: 'none'
          })
          return
        }
        if (!this.params.chaoxiang) {
          uni.showToast({
            title: "请选择朝向",
            icon: 'none'
          })
          return
        }
        if (!this.params.zhuangxiu) {
          uni.showToast({
            title: "请选择装修",
            icon: 'none'
          })
          return
        }
      }
      for (let key in this.form) {
        let item = this.form[key]
        if (item.required && !this.params[item.identifier]) {
          uni.showToast({
            title: `请输入${item.title}`,
            icon: 'none'
          })
          return
        }
      }
      if (Number(this.params[this.form.louceng.identifier]) < Number(this.params[this.form.szlc.identifier])) {
        uni.showToast({
          title: '所在楼层不能大于总层数',
          icon: 'none'
        })
        return
      }
      if (this.params.title.length > 50) {
        uni.showToast({
          title: '标题最多为50个字',
          icon: 'none'
        })
        return
      }
      if (this.params.contact_who.length > 4) {
        uni.showToast({
          title: '联系人最多为4个字',
          icon: 'none'
        })
        return
      }
      if (this.params.content.length > 1000) {
        uni.showToast({
          title: '描述最多1000为个字',
          icon: 'none'
        })
        return
      }
      if (this.memberVip.length == 0) {
        this.params.release_type = ''
      }
      if ((this.params.jingxuan_id || this.params.tops_id) && this.params.release_type == "check_release") {
        uni.showToast({
          title: '含有付费项目不能选择委托发布 请重新选择 ',
          icon: 'none'
        })
        return
      }
      // if(["1","2"].includes(this.parentid)&&!this.params.videos&&!this.params.imgs){
      // 	uni.showToast({
      // 		title:"请至少上传一张图片",
      // 		icon:"none"
      // 	})
      // 	return
      // }
      if (!this.on_ready) {
        uni.showToast({
          title: '请阅读并接受发布规则',
          icon: 'none'
        })
        return
      }
      if (!this.params[this.form.fangjia.identifier]) {
        this.params[this.form.fangjia.identifier] = 0
      }
      if (this.parentid) {
        this.checkRelease()
      }
    },
    confirm() {
      this.showModel = false
      uni.showLoading({
        title: '正在发布',
        mask: true
      })
      if (this.modelData.is_release) {
        this.releaseInfo(this.modelData.info_id)
      } else {
        this.info_id = this.modelData.info_id
        if (this.modelData.pay_status === 1) {
          this.userReleaseByCorn(this.modelData.info_id)
        } else {
          this.handlePay(this.modelData.info_id)
        }
      }
    },
    handleConcatInput(e) {
      let text = e.match(/[a-zA-Z0-9_\u4e00-\u9fa5]{0,4}/g)[0]
      this.$nextTick(() => {
        this.$set(this.params, "contact_who", text)
      })
    },
    cancel() {
      this.showModel = false
      this.$navigateTo('/user/manage_info?cate_id=' + this.parentid)
    },
    close() {
      this.showModel = false
    },
    // 检测是否需要支付，后端会将信息存入临时表返回其id,不需要支付执行发布接口，需要支付执行扣除金币接口(仅发布使用)
    checkRelease() {
      uni.showLoading({
        title: '正在发布',
        mask: true
      })
      let params = Object.assign({}, this.params)
      if (this.use_meal_ding) {
        params.package_tops_id = params.tops_id
        delete params.tops_id
      }
      if (this.use_meal_jing) {
        params.package_jingxuan_id = params.jingxuan_id
        delete params.jingxuan_id
      }
      this.$ajax.post('release/checkRelease.html', params, res => {
        if (res.data.code !== 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
          return
        }
        if (res.data.outNumber > 0) {
          uni.hideLoading()
          this.showModel = true,
            this.modelContent = res.data.msg
          this.modelData = res.data
          return
        }
        if (res.data.is_release) {
          this.releaseInfo(res.data.info_id)
        } else {
          this.info_id = res.data.info_id
          uni.hideLoading()
          // 提示需要扣除金币或支付
          showModal({
            content: res.data.msg,
            confirm: () => {
              // pay_status === 1 代表用户金币足够支付，否则调起支付
              if (res.data.pay_status === 1) {
                this.userReleaseByCorn(res.data.info_id)
              } else {
                this.handlePay(res.data.info_id)
              }
            }
          })
        }
      })
    },
    // 不需要支付时执行的发布接口
    releaseInfo(info_id) {
      this.$ajax.post('release/releaseInfo.html', { info_id }, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg,
            mask: true
          })
          this.removeDraft()
          setTimeout(() => {
            this.$navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    // 执行扣除金币支付接口
    userReleaseByCorn(info_id) {
      this.$ajax.get('release/userReleaseByCorn.html', { info_id }, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg,
            mask: true
          })
          this.removeDraft()
          setTimeout(() => {
            this.$navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    handlePay(info_id) {
      uni.hideLoading()
      this.wxPay(info_id)
    },
    wxPay(info_id) {
      this.$ajax.post('release/userReleaseByWxPay.html', { info_id }, res => {
        if (res.data.code === 1) {
          let pay_info = res.data.data
          this.wx.chooseWXPay({
            // provider: 'wxpay',
            timestamp: pay_info.timeStamp,
            nonceStr: pay_info.nonceStr,
            package: pay_info.package,
            signType: pay_info.signType,
            paySign: pay_info.paySign,
            success: res => {
              uni.showToast({
                title: '支付成功'
              })
              this.removeDraft()
              setTimeout(() => {
                uni.navigateBack()
              }, 1500)
            },
            fail: function (err) {
              console.log('支付失败：', err)
              uni.showToast({
                title: err.err_desc || err.errMsg,
                icon: 'none',
                duration: 5000
              })
            }
          })
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    /**
     * 执行升级个人永久vip
     */
    upgrade() {
      this.$ajax.post('member/upGradeVip', {}, res => {
        if (res.data.code === 1) {
          // 升级成功重新执行之前的接口
          if (this.upgrade_type === 'add') {
            this.getPageInfo()
          }
          if (this.upgrade_type === 'push') {
            this.subData()
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    showTemplateTitle() {
      this.createTitle()
      if (this.title_template_list.length === 0 || this.title_template_list[0] === '出售' || this.title_template_list[0] === '出租') {
        uni.showToast({
          title: '请先完善基础信息',
          icon: 'none'
        })
        return
      }
      this.$refs.title_popup.show()
    },
    // 生成智能标题
    createTitle() {
      // var type = ''
      // switch(this.parentid){
      //   case 1:
      //     type = '出售'
      //     break
      //   case 2:
      //     type = '出租'
      //     break
      //   case 3:
      //     type = '求租'
      //     break
      //   case 4:
      //     type = '求购'
      // }
      var title0 = `${this.community_name}${(this.params.shi || this.params.shi === 0) ? this.params.shi + '室' : ''}${(this.params.ting || this.params.ting === 0) ? this.params.ting + '厅' : ''}${(this.params.wei || this.params.wei === 0) ? this.params.wei + '卫' : ''}${this.parentid == 1 && this.params.fangjia ? this.params.fangjia + '万' : (this.params.zujin ? this.params.zujin + '元/月' : '')}${this.params.mianji ? this.params.mianji + 'm²' : ''}${this.current_zhuangxiuname || ''}${this.parentid == 1 ? '出售' : '出租'}`

      if (this.parentid == 1) {
        var title1 = `新上！急售好房，${this.community_name}${(this.params.shi || this.params.shi === 0) ? this.params.shi + '室' : ''}${(this.params.ting || this.params.ting === 0) ? this.params.ting + '厅' : ''}${(this.params.wei || this.params.wei === 0) ? this.params.wei + '卫' : ''}${this.params.mianji ? this.params.mianji + 'm²' : ''}${this.current_zhuangxiuname || ''}`

        var title2 = `品质小区！${this.community_name}${(this.params.shi || this.params.shi === 0) ? this.params.shi + '室' : ''}${(this.params.ting || this.params.ting === 0) ? this.params.ting + '厅' : ''}${(this.params.wei || this.params.wei === 0) ? this.params.wei + '卫' : ''}${this.params.mianji ? this.params.mianji + 'm²' : ''}全明户型 朝向采光好`

        var title3 = `居家自住必看好房！${this.community_name}${(this.params.shi || this.params.shi === 0) ? this.params.shi + '室' : ''}${(this.params.ting || this.params.ting === 0) ? this.params.ting + '厅' : ''}${(this.params.wei || this.params.wei === 0) ? this.params.wei + '卫' : ''}随时看房`

        var title4 = `${this.community_name} 房东置换急售 送装修 采光好 诚心出售 随时看房`
        var title5 = `${this.community_name} 新出业主自住婚房 中间位置 真实出售业主置换`
        this.title_template_list = [title0, title1, title2, title3, title4, title5]
        if (this.levelid <= 1) {
          var title6 = `房东直售！${this.community_name}${(this.params.shi || this.params.shi === 0) ? this.params.shi + '室' : ''}${(this.params.ting || this.params.ting === 0) ? this.params.ting + '厅' : ''}${(this.params.wei || this.params.wei === 0) ? this.params.wei + '卫' : ''}${this.parentid == 1 && this.params.fangjia ? this.params.fangjia + '万' : (this.params.zujin ? this.params.zujin + '元/月' : '')}${this.params.mianji ? this.params.mianji + 'm²' : ''}${this.current_zhuangxiuname || ''}`
          this.title_template_list.push(title6)
          var title7 = `${this.community_name}房东自住好房出售${(this.params.shi || this.params.shi === 0) ? this.params.shi + '室' : ''}${(this.params.ting || this.params.ting === 0) ? this.params.ting + '厅' : ''}${(this.params.wei || this.params.wei === 0) ? this.params.wei + '卫' : ''}${this.parentid == 1 && this.params.fangjia ? this.params.fangjia + '万' : (this.params.zujin ? this.params.zujin + '元/月' : '')}${this.params.mianji ? this.params.mianji + 'm²' : ''}${this.current_zhuangxiuname || ''}`
          this.title_template_list.push(title7)
        }
      } else {
        var title1 = `品质小区！${this.community_name}${(this.params.shi || this.params.shi === 0) ? this.params.shi + '室' : ''}${(this.params.ting || this.params.ting === 0) ? this.params.ting + '厅' : ''}${(this.params.wei || this.params.wei === 0) ? this.params.wei + '卫' : ''}${this.params.mianji ? this.params.mianji + 'm²' : ''}拎包入住 随时看房`

        var title2 = `${this.community_name}${(this.params.shi || this.params.shi === 0) ? this.params.shi + '室' : ''}${(this.params.ting || this.params.ting === 0) ? this.params.ting + '厅' : ''}${(this.params.wei || this.params.wei === 0) ? this.params.wei + '卫' : ''}繁华商圈 近学校 好房出租`

        var title3 = `精装修！${this.community_name}${(this.params.shi || this.params.shi === 0) ? this.params.shi + '室' : ''}${(this.params.ting || this.params.ting === 0) ? this.params.ting + '厅' : ''}${(this.params.wei || this.params.wei === 0) ? this.params.wei + '卫' : ''}${this.params.mianji ? this.params.mianji + 'm²' : ''}温馨舒适 有钥匙好房急租`
        this.title_template_list = [title0, title1, title2, title3]
      }
      if (this.title_template_index >= 0) {
        this.params.title = this.title_template_list[this.title_template_index]
      }
      if (this.params.title === '出售' || this.params.title === '出租') {
        this.params.title = ""
      }
    },
    selectTitle(title, index) {
      this.title_template_index = index
      this.$refs.title_popup.hide()
      this.params.title = title
    },
    removeDraft() {
      this.isSubmit = true
      uni.removeStorageSync(this.localKey);
    }
  },
  // #ifdef APP_PLUS
  onBackPress() {
    // 收起软键盘
    plus.key.hideSoftKeybord()
  }
  // #endif
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.flex-row {
  flex-direction: row;
}

.multiple_input {
  padding: 24rpx 0;

  >.label {
    line-height: 1;
    font-size: 22rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #666;
  }

  .input-item {
    color: #666;
    align-items: center;

    ~.input-item {
      margin-left: 48rpx;
    }

    .label {
      min-width: 80rpx;
      margin-right: 24rpx;
    }

    input {
      flex: 1;
    }
  }
}

.upload-box {
  padding: 24rpx 0;
  align-items: center;

  .upload_btn {
    width: 128rpx;
    height: 128rpx;
    text-align: center;
    justify-content: center;
    background: #f2f2f2;
  }

  .cover_img {
    width: 128rpx;
    height: 128rpx;
  }

  .upload_tip {
    margin-left: 24rpx;
    margin-right: 6rpx;
    justify-content: center;

    .tip_title {
      font-size: 36rpx;
      color: #666;
      margin-bottom: 16rpx;
    }

    .tip_content {
      font-size: 22rpx;
      color: #999;
    }
  }
}

.block {
  margin-bottom: 20rpx;
  padding: 0 48rpx;
  background-color: #fff;

  input {
    color: #333;
  }
}

.open_menu {
  padding: 24rpx 0;
  justify-content: space-between;
  align-items: center;

  .tip {
    color: #999;
  }

  .ding {
    display: inline-block;
    margin-left: 20rpx;
    height: 36rpx;
    width: 36rpx;
    line-height: 36rpx;
    text-align: center;
    font-size: 22rpx;
    border-radius: 4rpx;
    background-color: $uni-color-primary;
    color: #fff;
  }

  .jing {
    display: inline-block;
    margin-left: 20rpx;
    height: 36rpx;
    width: 36rpx;
    line-height: 36rpx;
    text-align: center;
    font-size: 22rpx;
    border-radius: 4rpx;
    background-color: #00caa7;
    color: #fff;
  }
}

.more_info_box {
  height: 0;
  overflow: hidden;

  &.open {
    height: auto;
  }

  .types {
    justify-content: center;
    margin-bottom: 24rpx;

    .type-item {
      padding: 10rpx 24rpx;
      position: relative;
      font-size: 32rpx;

      &.active {
        color: $uni-color-primary;
      }

      &.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 35%;
        right: 35%;
        height: 8rpx;
        border-radius: 4rpx;
        background-color: $uni-color-primary;
      }
    }
  }

  .tip {
    padding-bottom: 20rpx;
    font-size: 24rpx;
    text-align: center;
    color: #999;
  }
}

.options-box {
  flex-wrap: wrap;
  justify-content: space-between;

  .options {
    width: 31%;
    border: 1rpx solid #d8d8d8;
    border-radius: 8rpx;
    padding: 24rpx 6rpx;
    margin-bottom: 24rpx;
    position: relative;
    color: #666;
    text-align: center;
    overflow: hidden;

    &.vacancy {
      height: 0;
      padding: 0;
      margin: 0;
      border: 0;
    }

    &.active {
      background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
      border-color: $uni-color-primary;

      .title {
        color: #000;
      }

      .price {
        color: #000;
      }

      .tip {
        background-color: $uni-color-primary;
      }
    }

    .title {
      text-align: center;
      margin-bottom: 10rpx;
    }

    .price {
      display: block;
      text-align: center;
      font-size: 50rpx;
      margin-bottom: 32rpx;

      .unit {
        font-size: 22rpx;
      }
    }

    .tip {
      font-size: 22rpx;
      position: absolute;
      bottom: 0;
      width: 100%;
      left: 0;
      padding: 6rpx 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      background-color: #d8d8d8;
      color: #fff;
    }
  }
}

.rule {
  padding: 10rpx 48rpx;
  font-size: 22rpx;
  align-items: center;

  checkbox {
    transform: scale(0.7);
  }

  .rule_link {
    color: #666;
  }
}

.btn-box {
  padding: 48rpx;

  .btn {
    height: 88rpx;
    line-height: 88rpx;
    background: #fb656a;
    box-shadow: 0 8rpx 32rpx 0 rgba(251, 101, 106, 0.4);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    text-align: center;
  }
}

.money_tip {
  padding: 18rpx 48rpx;
  font-size: 26rpx;
  flex-direction: row;
  align-items: center;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.15);
  color: $uni-color-primary;

  .text {
    margin-left: 10rpx;
    word-wrap: normal;
  }

  .btn {
    margin-left: 16rpx;
    padding: 6rpx 16rpx;
    border: 1rpx solid $uni-color-primary;
    font-size: 24rpx;
    border-radius: 8rpx;
  }
}

.select_btn {
  padding: 24rpx 0;
  justify-content: space-between;
  align-items: center;

  .label {
    margin-bottom: 24rpx;
    font-size: 24rpx;
    color: #666;
  }

  .value {
    font-size: 36rpx;
    color: #999;

    &.has_value {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

.template_title {
  font-size: 22rpx;
  color: $uni-color-primary;
}

.title_list {
  max-height: 70vh;
  box-sizing: border-box;
  padding: 24rpx 48rpx;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  background-color: #fff;

  .title_item {
    display: block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    // white-space: nowrap;
    padding: 20rpx 0;

    &.active {
      color: $uni-color-primary;
    }
  }
}

.tips_box {
  justify-content: space-between;
  padding: 24rpx 48rpx;
  flex-wrap: wrap;
  align-items: center;
  background: #fff;

  .tips_box_item {
    position: relative;
    flex: 0 0 48%;
    margin-bottom: 24rpx;
    padding: 24rpx;
    border: 2rpx solid #E5E5E5;
    border-radius: 8rpx;

    .title_box {
      align-items: center;

      .tips_box_title {
        font-size: 28rpx;
        font-weight: 600;
        margin-right: 12rpx;
        color: #333333;
      }

      .icon {
        width: 28rpx;
        height: 28rpx;
        overflow: hidden;

        image {
          width: 100%;
        }
      }
    }

    .money_box {
      font-size: 22rpx;
      margin: 12rpx 0;
      color: #333333;
    }

    .tip_con {
      font-size: 22rpx;
      color: #999999;
    }

    .tip_box_bg {
      position: absolute;
      right: 10rpx;
      bottom: 10rpx;
      width: 88rpx;
      height: 88rpx;
      overflow: hidden;

      image {
        width: 100%;
      }
    }

    .sanjiao {
      width: 0;
      height: 0;
      position: absolute;
      top: -40rpx;
      right: -40rpx;
      border: 40rpx solid transparent;
      border-top: 40rpx solid#fb656a;
      transform-origin: center;
      transform: rotate(225deg);
    }

    .tuijian {
      position: absolute;
      top: 3rpx;
      right: 6rpx;
      font-size: 20rpx;
      color: #FFFFFF;
    }

    &.active {
      border: 2rpx solid #FB656A;
      box-shadow: 0 0 4rpx 0 rgba(251, 101, 106, 0.06);
    }
  }
}

.tips {
  font-size: 20rpx;
  padding: 14rpx 24rpx;
  background-color: #ffeeef;
  color: $uni-color-primary;
}

.quick_copy {

  // position:fixed;
  // right:10rpx;
  // bottom:400rpx;
  // width: auto;
  // z-index: 15;
  // background: #fff;
  .quick_copy_left {
    align-items: center;

    .quick_copy_left_img {
      width: 60rpx;
      height: 60rpx;
      margin-right: 24rpx;

      image {
        width: 100%
      }
    }
  }

  .copy_icon {
    width: 0;
    height: 80rpx;
    overflow: hidden;
    align-items: center;
    transition: width 0 ease 2s;

    .copy_name {
      font-size: 22rpx;
      color: #999999;
    }

    &.copy_icon_show {
      width: 100rpx;
      transition: width 0s ease 2s;
    }
  }

  .copy_content {
    // width: 0;
    height: 192rpx;
    // border: 2rpx solid #D8D8D8;
    // border-radius: 8rpx;
    overflow: hidden;
    background: #F8F8F8;
    margin: 24rpx 0 48rpx;
    border-radius: 8rpx;
    padding: 24rpx;
    color: #999;

    &.copy_content_show {
      width: 80vw;
    }

    transition: width 2s;

  }

}
</style>
