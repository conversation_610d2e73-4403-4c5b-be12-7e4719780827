<template>
  <view class="page">
    <view class="top">
      <image src="/static/icon/top.png" mode="widthFix"></image>
      <view class="top_box">
        <view class="search-box">
            <my-icon type="ic_sousuo" @click="handleSearch" color="#999999" size="38rpx"></my-icon>
            <input
              type="text"
              confirm-type="search"
              v-model="keywords"
              @confirm="handleSearch()"
              placeholder="搜索意向楼盘"
            />
        </view>
        <view class="num w">
          <view>
            <view>{{statistics.todayAddCount || 0}}</view>
            <text>本日新增</text>
          </view>
        <view>
          <view>{{statistics.monthAddCount || 0}}</view>
          <text>本月新增</text>
        </view>
        <view>
          <view>{{statistics.cumulativeTotal || 0}}</view>
          <text>累计成交</text>
        </view>
        </view>
        <view class="sort w">
          <text :class="{ sort_on: type === 0 }" @click="checkType(0)">全部</text>
          <text :class="{ sort_on: type === 1 }" @click="checkType(1)"
            >未认领</text
          >
          <text :class="{ sort_on: type === 2 }" @click="checkType(2)"
            >已认领</text
          >
        </view>
      </view>
    </view>
    <view class="list w">
      <view class="info" v-for="(item, index) in list" :key="index">
        <!-- <image class="tag" src="/static/icon/ershou.png"></image> -->
        <image class="tag" src="/static/icon/new.png"></image>
        <view class="valid_info">
          <text class="tip valid" v-if="item.is_valid == 1">有效客户</text>
          <text class="tip invalid" v-if="item.is_valid == 2">无效客户</text>
        </view>
        <view class="mes">{{ item.name }} {{ item.tel }}
          <view class="mes_level">
              <text class="level level1" v-if="item.level==3">A级</text>
              <text class="level level2" v-if="item.level==2">B级</text>
              <text class="level level3" v-if="item.level==1">C级</text>
              <text v-if="item.is_claim == 1&&item.is_owner_claim == 0&&item.is_owner_viewed == 0" class="notfollow">未跟进</text>
          </view>
        </view>
        <view class="mes">
          <text>{{ item.title }} <text class="mes_order" v-if="item.signup_time>1">{{ item.signup_time }}次报名</text></text>
          <text class="mes_time">{{ item.utime }}</text>
        </view>
        <view class="claim claim_blue" v-if="item.is_claim == 1&&item.is_owner_claim == 0&&item.is_owner_viewed == 0" @click="claim(item.id,2)">查看</view>
        <view class="claimed" v-if="item.is_claim == 1&&item.is_owner_claim == 1">
          <text @click="followup(item.id)">跟进</text>
          <text @click="showCopywriting(item)">复制</text>
          <text @click="handleTel(item.id)">电话</text>
        </view>
         <view class="claimed" v-if="item.is_claim == 1&&item.is_owner_claim == 0&&item.is_owner_viewed == 1">
          <text @click="followup(item.id)">跟进</text>
          <text @click="showCopywriting(item)">复制</text>
          <text @click="handleTel(item.id)">电话</text>
        </view>
        <view class="claim" v-if="item.is_claim == 0" :list="list" @click="claim(item.id,1)"
          >立即认领</view
        >
        <view class="mes" v-if="item.is_claim == 1">{{item.claim_cname}}认领了此客户</view>
      </view>
      <uni-load-more
        :status="get_status"
        :content-text="content_text"
      ></uni-load-more>
    </view>
  </view>
</template>
<script>
import { uniLoadMore } from "@dcloudio/uni-ui";
import copyText from "../../common/utils/copy_text";
import myIcon from "../../components/myIcon";

export default {
  components: {
    uniLoadMore,
    myIcon
  },
  data() {
    return {
      params: {
        page: 1,
        rows: 10,
        is_claim: "",
        keyword:''
      },
      list: [],
      type: 0,
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了",
      },
      statistics:[],
      keywords:''
    };
  },
  props: {},
  computed: {},
  onLoad(options) {
    if (options.type == "0" || options.type == "1") {
      this.params.is_claim = options.type;
      this.type = parseInt(options.type) + 1;
      this.params.page = 1;
      this.getData();
    }
    this.getData();
  },
  methods: {
    checkType(type) {
      if (type == 0) {
        this.params.is_claim = "";
      } else {
        this.params.is_claim = type - 1;
      }
      this.type = type;
      this.params.page = 1;
      this.keywords = ''
      this.getData();
    },
    getData() {
      if (this.params.page === 1) {
        this.list = [];
      }
      this.params.keyword = this.keywords
      this.get_status = "loading";
      // 客户列表
      this.$ajax.get("customerServicer/customerList", this.params, (res) => {
        if (res.data.code == 1) {
          if (this.params.page == 1) {
            this.list = res.data.list;
            this.statistics = res.data.statistics
          } else {
            this.list = this.list.concat(res.data.list);
          }
          if (this.list.length < this.params.rows) {
            this.get_status = "noMore";
          } else {
            this.get_status = "more";
          }
        } else {
          this.get_status = "noMore";
        }
      });
    },
    // 点击认领
    claim(id,type) {
      this.$ajax.post("customerServicer/claimInfo", { info_id: id ,caim_type:type}, (res) => {
        if (res.data.code == 1) {
          uni.showToast({
            title: res.data.msg,
            duration: 2000,
          });
          // 切换已认领状态
          this.params.page = 1;
          this.getData();
          // this.$set(this.list[index],'is_claim',1)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none",
            duration: 2000,
          });
        }
      });
    },
    // 电话
    handleTel(id) {
      this.$ajax.post(
        "customerServicer/infoCall",
        {
          info_id: id,
        },
        (res) => {
          if (res.data.code == 1) {
            uni.makePhoneCall({
              phoneNumber: res.data.info.tel,
            });
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        }
      );
    },
    // 复制
    showCopywriting(item) {
      this.$ajax.get(
        "customerServicer/infoCopyCheck",
        {
          info_id: item.id,
        },
        (res) => {
          if (res.data.code == 1) {
            var info = res.data.info;
            const content = `客户姓名：${info.cname || "暂无姓名"}\n手机号码：${
              info.tel
            }\n意向小区：${info.community ||
              "未跟进"}\n跟进进度：${info.speed || "未跟进"}\n跟进时间：${info.last_follow_time}`;
            copyText(content, () => {
              uni.showToast({
                title: "客户信息复制成功",
                icon: "none",
              });
            });
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        }
      );
    },
    // 跳转跟进页
    followup(id) {
      this.$ajax.get("customerServicer/checkClaim", { info_id: id }, (res) => {
        if (res.data.code == 1) {
          this.$navigateTo("followup?id=" + id);
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none",
            duration: 2000,
          });
        }
      });
    },
    handleSearch() {
      this.list = []
      this.params.page = 1;
      this.getData();
    },
    onReachBottom() {
      if (this.get_status == "more") {
        this.params.page = this.params.page + 1;
        this.getData();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.page {
  background: #f8f8f8;
  .w {
    width: 90%;
    margin: 0 auto;
  }
  .list {
    .info {
      background: #fff;
      border-radius: 8rpx;
      margin-top: 24rpx;
      padding-bottom: 48rpx;
      .claimed {
        margin: 48rpx 24rpx 0 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        text,
        view {
          text-align: center;
          padding: 20rpx 0;
          border-radius: 8rpx;
        }
        text {
          color: #fb656a;
          width: 30%;
          background: #ffffff;
          border: 1rpx solid rgba(251, 101, 106, 1);
          box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(251, 101, 106, 0.1);
        }
        .claimed_btn {
          flex: 1;
          background-image: linear-gradient(125deg, #ff5500 0%, #ffa402 100%);
          box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(255, 145, 1, 0.5);
          color: #ffffff;
          border: none;
        }
      }
      .claim {
        font-size: 32rpx;
        color: #ffffff;
        font-weight: 500;
        padding: 20rpx 0;
        text-align: center;
        margin: 48rpx 24rpx 0 24rpx;
        background-image: linear-gradient(125deg, #ff5500 0%, #ffa402 100%);
        box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(255, 145, 1, 0.5);
        border-radius: 8rpx;
        &.claim_blue{
          background-image: linear-gradient(125deg, #498fe2 0%, #74adf3 100%);
          box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(116, 173, 243, 0.5);
        }
      }
      .mes {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 24rpx 24rpx 0 24rpx;
        .mes_time{
          font-size: 24rpx;
        }
        .mes_order{
          color: #fff;
          font-size: 24rpx;
          padding: 0 6rpx;
          margin-left: 20rpx;
          background: #ffa402;
          height: 36rpx;
          line-height: 36rpx;
          display: inline-block;
          border-radius: 4rpx;
        }
      }
      .mes_level{
        display: flex;
        justify-content: space-between;
        align-items: center;
        .notfollow{
          display: inline-block;
          font-size: 22rpx;
          margin-left: 10rpx;
        }
        .level{
          color: #fff;
          width: 66rpx;
          height: 34rpx;
          font-size: 24rpx;
          text-align: center;
          line-height: 34rpx;
          border-radius: 4rpx;
        }
        .level1{
          background: #FB656A;
        }
        .level2{
          background: #fbc365;
        }
        .level3{
          background: #d8d8d8;
        }
      }
      .valid_info {
        padding-top: 24rpx;
        .tip {
          display: inline-block;
          width: 136rpx;
          height: 48rpx;
          line-height: 48rpx;
          text-align: center;
          font-size: 22rpx;
          border-radius: 0rpx 32rpx 32rpx 0rpx;
          &.valid {
            background-image: linear-gradient(125deg, #ff5500 0%, #ffa402 100%);
            color: #fff;
          }
          &.invalid {
            background: #f2f2f2;
            color: #d8d8d8;
          }
        }
      }

      .tag {
        float: right;
        width: 90rpx;
        height: 90rpx;
      }
    }
  }
  .top {
    position: relative;
    background: #fff;
    .search-box {
      display: flex;
      margin: 0 5%;
      align-items: center;
      padding: 10rpx 16rpx;
      background-color: #f5f5f5;
      color: #999;
      border-radius: 8rpx;
      width: 40%;
      margin-bottom: 50rpx;
      .search-left {
        margin-right: 20rpx;
      }
      input{
        width: 100%;
        font-size: 28rpx;
        margin-left: 10rpx;
      }
    }
    .sort {
      padding-bottom: 24rpx;
      margin-top: 50rpx;
      text {
        display: inline-block;
        background: #f5f5f5;
        color: #999999;
        border: 2rpx solid #f5f5f5;
        border-radius: 4rpx;
        width: 120rpx;
        height: 48rpx;
        line-height: 48rpx;
        text-align: center;
        font-size: 24rpx;
        margin-right: 26rpx;
      }
      .sort_on {
        color: #ff5500;
        border: 2rpx solid #ff5500;
        background-image: linear-gradient(
          90deg,
          rgba(255, 85, 0, 0.2) 0%,
          rgba(255, 164, 2, 0.2) 100%
        );
      }
    }
    .top_box{
      position: relative;
      top: -160rpx;
      height: 220rpx;
    }
    .num {
      display: flex;
      justify-content: space-around;
      background: #fff;
      box-shadow: 0rpx -6rpx 8rpx 0rpx rgba(0, 0, 0, 0.08);
      border-radius: 16rpx 16rpx 0rpx 0rpx;
      text-align: center;
      padding: 48rpx 0 0 0;
      > view {
        view {
          font-size: 40rpx;
          color: #333333;
        }
        text {
          font-size: 22rpx;
          color: #666666;
        }
      }
    }
    image {
      width: 100%;
    }
  }
}
</style>
