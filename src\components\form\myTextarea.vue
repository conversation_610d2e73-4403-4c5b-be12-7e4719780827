<template>
	<view>
		<view class="textarea-row bottom-line">
			<label for="">{{label}}</label>
			<textarea :value="value" :placeholder="placeholder?placeholder:'请输入'+label" @input="handelInput" auto-height  />
			<text v-if="unit" class="unit">{{unit}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			label:{
				type:String,
				default:""
			},
			name:String,
			value:String,
			unit:String,
			placeholder:String
		},
		data() {
			return {
			};
		},
		methods:{
			handelInput(e){
				let name = {
					_name:this.name
				}
				this.$emit('input', Object.assign(e,name))
			}
		}
	}
</script>

<style lang="scss">
	.textarea-row{
		display: flex;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;
		line-height:62upx;
		background-color: #fff;
	}
	.textarea-row label{
		min-width: 130upx;
		max-width: 220upx;
		font-size: $uni-font-size-lg;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-right: 38upx;
		text-align-last:justify;
	}
	.textarea-row .unit{
		font-size: $uni-font-size-sm;
		color: $uni-text-color-grey;
	}
	.textarea-row uni-icon{
		line-height: 62upx;
		margin-left: 10upx;
	}
	.textarea-row textarea{
		 // #ifndef MP-BAIDU 
		padding: 10upx;   //百度小程序编辑的时候下边会有点遮住
		// #endif 
		width:100%;
		min-height: 25upx;
		// box-sizing: border-box;
		// height: auto !important;
	}
</style>
