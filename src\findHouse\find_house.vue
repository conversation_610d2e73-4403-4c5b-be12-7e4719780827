<template>
  <view class="find_house-box">
    <image class="banner" mode="aspectFill" :src="service_list[tab_index].banner | imageFilter('w_8001')"></image>
    <view class="container">
      <view class="form-box">
        <view class="cate-list flex-row bottom-line">
          <template  v-for="(item, index) in service_list">
            <view
              class="cate-item"
              :key="index"
              :class="{ active: tab_index === index }"
              v-if="item.is_show"
              @click="switchType(item.type, index)"
              >{{ item.name }}</view
            >
            </template>
        </view>
        <view class="form-body">
          <view class="need-box bottom-line">
            <view class="children_type flex-row" v-if="service_list[tab_index].types.length > 0" @click="showAction()">
              <text class="text">{{service_list[tab_index].types[chil_type-1].name}}</text>
              <my-icon type="ic_into" size="30rpx" color="#666"></my-icon>
            </view>
            <image class="need-icon" :src="$imgDomain + '/images/new_icon/fankui.png'"></image>
            <input class="flex-1" type="text" v-model="form.des" :placeholder="service_list[tab_index].placeholder" placeholder-style="font-size:28rpx;color:#999" />
          </view>
          <view class="form-info flex-row bottom-line">
            <view class="form-info-item right-line">
              <input type="text" maxlength="10" v-model="form.name" placeholder="请输入姓名" placeholder-style="color:#999" />
            </view>
            <view class="form-info-item">
              <input type="number" maxlength="11" v-model="form.tel" placeholder="请输入手机号" placeholder-style="color:#999" />
            </view>
          </view>
          <view class="tip flex-row">
            <my-icon type="ic_dun" size="28rpx" color="#999999"></my-icon>
            <text class="text">隐私保护已开启</text>
          </view>
          <view class="button-box">
            <view
              class="button"
              hover-class="button-hover"
              :hover-start-time="60"
              :hover-stay-time="180"
              @click="onSubmit()"
              >{{service_list[tab_index].btn_name||''}}</view
            >
          </view>
          <view class="service-num">当前有{{service_list[tab_index].num||''}}位{{service_list[tab_index].servicer||''}}为您服务</view>
        </view>
      </view>
      <view class="service_list flex-row" v-if="form_type===1&&service_list[0].show_nav">
        <view class="service_item flex-row red" @click="$navigateTo('/pages/new_house/new_house')">
          <view class="info">
            <text class="title">新房</text>
            <text class="sub_title">热门新楼盘></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_xinfang.png' | imageFilter('m_320')"></image>
        </view>
        <view class="service_item flex-row blue" @click="$navigateTo('/pages/map_find/map_find')">
          <view class="info">
            <text class="title">地图找房</text>
            <text class="sub_title">查看附近好房源></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_map.png' | imageFilter('m_320')"></image>
        </view>
        <view class="service_item flex-row green" @click="$navigateTo('/pages/consultant/consultant')">
          <view class="info">
            <text class="title">置业顾问</text>
            <text class="sub_title">优选置业顾问></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_zhiye.png' | imageFilter('m_320')"></image>
        </view>
        <view class="service_item flex-row yellow" @click="$navigateTo('/pages/community/community')">
          <view class="info">
            <text class="title">楼市圈</text>
            <text class="sub_title">楼市最新动态></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_quanzi.png' | imageFilter('m_320')"></image>
        </view>
      </view>
      <view class="service_list flex-row" v-if="form_type===2&&service_list[1].show_nav">
        <view class="service_item flex-row green" @click="$navigateTo('/pages/renting/renting')">
          <view class="info">
            <text class="title">租房</text>
            <text class="sub_title">海量真房源></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_zufang.png' | imageFilter('m_320')"></image>
        </view>
        <view class="service_item flex-row yellow" @click="$navigateTo('/pages/agent/agent')">
          <view class="info">
            <text class="title">经纪人</text>
            <text class="sub_title">优选经纪人></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_jingjiren.png' | imageFilter('m_320')"></image>
        </view>
        <view class="service_item flex-row red" @click="$navigateTo('/pages/ershou/ershou')">
          <view class="info">
            <text class="title">二手房</text>
            <text class="sub_title">精选房源></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_ershoufang.png' | imageFilter('m_320')"></image>
        </view>
        <view class="service_item flex-row blue" @click="$navigateTo('/pages/house_price/house_price')">
          <view class="info">
            <text class="title">查房价</text>
            <text class="sub_title">房价估值走势></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_chafangjia.png' | imageFilter('m_320')"></image>
        </view>
      </view>
      <view class="service_list flex-row" v-if="form_type===3&&service_list[2].show_nav">
        <view class="service_item flex-row green" @click="$navigateTo('/home/<USER>/list?type=65')">
          <view class="info">
            <text class="title">买建材</text>
            <text class="sub_title">真材实料></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_jiancai.png' | imageFilter('m_320')"></image>
        </view>
        <view class="service_item flex-row blue" @click="$navigateTo('/home/<USER>/list?type=64')">
          <view class="info">
            <text class="title">选家居</text>
            <text class="sub_title">更多家居></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_jiaju.png' | imageFilter('m_320')"></image>
        </view>
        <view class="service_item flex-row yellow" @click="$navigateTo('/home/<USER>/list?type=62')">
          <view class="info">
            <text class="title">找公司</text>
            <text class="sub_title">专业公司></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_zhuangxiu.png' | imageFilter('m_320')"></image>
        </view>
        <view class="service_item flex-row red" @click="$navigateTo('/home/<USER>/list?type=1')">
          <view class="info">
            <text class="title">看案例</text>
            <text class="sub_title">经典案例></text>
          </view>
          <image class="service_icon" :src="'/images/new_icon/find_anli.png' | imageFilter('m_320')"></image>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from '@/components/myIcon'
import mySwiper from '@/components/mySwiper'
export default {
  components: {
    myIcon,
    mySwiper
  },
  data() {
    return {
      tab_index: 0,
      service_list: [
        {
          banner: '',
          types: []
        },
        {
          banner: '',
          types: []
        },
        {
          banner: '',
          types: []
        }
      ],
      form_type: 1,
      chil_type: 1,
      form: {
        des: '',
        name: '',
        tel: '',
        from: "找房"
      },
      find_swiper_list: []
    }
  },
  onLoad(options){
    this.find_swiper_list = this.new_house_swipers
    if(options.type){
      this.form_type = parseInt(options.type)
    }
    this.getData()
  },
  onShow() {
    // #ifdef H5
    document.title = '找房'
    // #endif
  },
  methods: {
    getData(){
      this.$ajax.get('build/findHouseNum', {}, res=>{
        if(res.data.code === 1){
          this.service_list = res.data.list.map((item, index)=>{
            if(!item.banner){
              item.banner = `/images/new_icon/find_banner${index+1}.png`
            }
            return item
          })

          let tab_index = this.service_list.findIndex(item=>(item.type == this.form_type&&item.is_show == 1))
          if(tab_index>=0){
            this.$nextTick(()=>{
              this.tab_index = tab_index
            })
            this.setShare()
          }else {
            this.tab_index =this.service_list.findIndex(item=>(item.is_show == 1))
            this.form_type = this.tab_index+1
          }
        }
      })
    },
   // 切换委托类型
    switchType(type, index) {
      this.tab_index = index
      this.chil_type = 1
      this.form.des = ""
      this.form_type = type
      this.setShare()
      switch (type) {
        case 1:
          this.find_swiper_list = this.new_house_swipers
          break
        case 2:
          this.find_swiper_list = this.sell_swipers
          break
        case 3:
          this.find_swiper_list = this.home_swipers
          break
      }
    },
    // 设置分享配置
    setShare(){
      this.share = {
        title: this.service_list[this.tab_index].share_title||'找房、卖房、找装修更轻松',
        content: this.service_list[this.tab_index].share_conbtent||'专属置业顾问、经纪人、装修服务商为您服务。',
        link: `${window.location.origin}/h5/pages/index/find_house?type=${this.form_type}`,
        pic: this.service_list[this.tab_index].share_pic,
      }
      this.getWxConfig()
    },
    showAction(){
      let items = this.service_list[this.tab_index].types.map(item=>item.name)
      uni.showActionSheet({
        itemList: items,
        success: e=>{
          this.chil_type = this.service_list[this.tab_index].types[e.tapIndex].type
        }
      })
    },
    onSubmit() {
      switch (this.form_type) {
        case 1:
          this.subHouseData()
          break
        case 2:
          this.subSaleHouse()
          break
        case 3:
          this.subHomeData()
      }
    },
    /**
     * <AUTHOR>
     * @date 2020-07-08 09:19:39
     * @desc 委托找房
     */
    subHouseData() {
      uni.showLoading({
        title: '正在提交',
        mask: true
      })
      this.form.type = this.chil_type
      this.$ajax.post(
        'build/signUp.html',
        this.form,
        res => {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        },
        err => {
          uni.hideLoading()
        }
      )
    },
    /** 
     * <AUTHOR> 
     * @date 2020-08-31 16:15:55 
     * @desc 委托房源
     */
    subSaleHouse(){
      uni.showLoading({
        title: '正在提交',
        mask: true
      })
      this.form.cate_id = this.chil_type
      this.$ajax.post('house/entrustHouse.html',this.form, res=>{
        uni.hideLoading()
        if(res.data.code === 1){
          uni.showToast({
            title: res.data.msg
          })
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      },err=>{
        uni.hideLoading()
      })
    },
    // 装修报名
    subHomeData() {
      uni.showLoading({
        title: '提交中...',
        mask: true
      })
      this.form.type = this.chil_type
      const params = Object.assign({from: 6},this.form)
      this.$ajax.get(
        'memberShop/signUp',
        params,
        res => {
          uni.hideLoading()
          if (res.data.code == 1) {
            uni.showToast({
              title: res.data.msg
            })
            this.sendMessage()
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
          }
        },
        err => {
          uni.hideLoading()
        }
      )
    },
    // 家装报名成功发送消息
    sendMessage() {
      this.$ajax.get('memberShop/sendAdminNotice.html', {}, res => {})
    }
  },
  onTabItemTap(e){
    uni.$emit('onTabItemTap', e)
  }
}
</script>

<style scoped lang="scss">
page {
  background-color: #fff;
}
.banner{
  height: 60vw;
  width: 100%;
}
.service_list{
  margin-top: 24rpx;
  margin-bottom: 48rpx;
  flex-wrap: wrap;
  justify-content: space-between;
  .service_item{
    width: 316rpx;
    height: 110rpx;
    padding: 24rpx;
    margin-bottom: 24rpx;
    border-radius: 16rpx;
    align-items: center;
    justify-content: space-between;
    background-color: #f7f7f7;
    &.red{
      background-color: rgba($color: $uni-color-primary, $alpha: 0.05);
      .sub_title{
        color: $uni-color-primary;
      }
    }
    &.blue{
      background-color: rgba($color: #4CC7F6, $alpha: 0.05);
      .sub_title{
        color: #4CC7F6;
      }
    }
    &.green{
      background-color: rgba($color: #00CAA7, $alpha: 0.05);
      .sub_title{
        color: #00CAA7;
      }
    }
    &.yellow{
      background-color: rgba($color: #FFD650, $alpha: 0.05);
      .sub_title{
        color: #FFD650;
      }
    }
    .title{
      font-size: 32rpx;
    }
    .sub_title{
      font-size: 22rpx;
    }
    .service_icon{
      width: 64rpx;
      height: 64rpx;
    }
  }
}
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

// .find_house-box {
//   padding-top: 44px;
// }

.title-left {
  align-items: center;
  padding: 10rpx 48rpx;
  .text {
    margin-left: 20rpx;
  }
}

.container {
  padding: 0 48rpx;
}

.form-box {
  margin-top: -48rpx;
  border-radius: 24rpx;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 2;
  .cate-list {
    justify-content: space-between;
    .cate-item {
      flex: 1;
      padding: 28rpx;
      text-align: center;
      &.active {
        background-image: linear-gradient(0deg, rgba(246, 246, 246, 0) 0%, rgba(251, 101, 106, 0.3) 100%);
        color: $uni-color-primary;
      }
    }
  }
  .form-body {
    padding: 24rpx;
    padding-bottom: 48rpx;
    .need-box {
      flex-direction: row;
      align-items: center;
      padding: 10rpx 0;
      .children_type{
        margin-right: 24rpx;
        align-items: center;
        .text{
          margin-right: 16rpx;
        }
      }
      .need-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 15rpx;
      }
    }
    .form-info {
      padding: 24rpx 0;
    }
    .form-info-item {
      flex: 1;
      padding: 10rpx 40rpx;
    }
    .tip {
      align-items: center;
      padding: 10rpx 30rpx;
      color: #999;
      .text {
        margin-left: 10rpx;
        font-size: 22rpx;
      }
    }
  }
}

.button-box {
  margin-top: 48rpx;
}

.button {
  padding: 24rpx;
  font-size: 32rpx;
  text-align: center;
  border-radius: 8rpx;
  background-color: $uni-color-primary;
  box-shadow: 0 4px 12px 0 rgba(251,101,106,0.40);
  color: #fff;
  &.button-hover {
    opacity: 0.8;
  }
}

.service-num {
  margin-top: 48rpx;
  padding: 10rpx 20rpx;
  height: 60rpx;
  border-radius: 30rpx;
  text-align: center;
  border: 1rpx solid $uni-color-primary;
  color: $uni-color-primary;
}

</style>
