<template>
	<view class="lists">
		<!-- #ifdef H5 -->
		<view class="common-item bottom-line" v-for="(item,index) in listData" :key="index" @click="handleAction(index)">
		<!-- #endif -->
		<!-- #ifndef H5 -->
		<view class="common-item bottom-line" v-for="(item,index) in listData" :key="index" @click="handleReply(index)" @longtap="handleAction(index)">
		<!-- #endif -->
			<view class="user flex-box">
				<image class="user-img" :src="(item.img||item.prelogo) | imgUrl('w_80')"></image>
				<text class="user-name">{{item.nickname}}</text>
				<image v-if="item.leader_path" class="level-icon" :src="item.leader_path | imgUrl('w_80')" mode="widthFix"></image>
				<text class="time">{{item.ctime||item.updtime}}</text>
<!-- 				<view v-if="adoptId==item.id" class="icon-box">
					<image src="../static/icon/cn.png"></image>
				</view> -->
				<view v-if="answer&&!item.owner" @click.stop.prevent="adoption(item.id)">
					<view class="xs_status gray" ></view>
					<text class="xs_status_text">确认</text>
				</view>
				<block v-if="adoptId==item.id">
					<view class="xs_money">
						<my-icon type="hongbao" color="#f65354"></my-icon>
						<text style="font-size:20upx;">￥</text><text>{{money}}</text>
					</view>
					<view class="xs_status green"></view>
					<text class="xs_status_text">优选</text>
				</block>
			</view>
			<view class="common-text">
				{{item.content}}<text v-if="isleader==1&&item.status===0" class="status">(待审核)</text>
			</view>
<!-- 			<view class="handle handle-bar" v-if="answer">
				<button type="primary" class="small plain" @click.stop.prevent="adoption(item.id)">采纳</button>
			</view> -->
			<view class="reply-list" v-if="item.children">
				<!-- #ifdef H5 -->
				<view class="reply-item" v-for="(reply_item,idx) in item.children" :key="idx" @click.stop.prevent="handleAction(index,idx)">
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view class="reply-item" v-for="(reply_item,idx) in item.children" :key="idx" @click.stop.prevent="handleReply(index,idx)" @longtap.stop.prevent="handleAction(index,idx)">
				<!-- #endif -->
					<text class="reply-item-name">{{reply_item.nickname}}回复{{reply_item.toNickname}}:</text>
					<text>{{reply_item.content}}</text>
					<text v-if="isleader==1&&reply_item.status===0" class="status">(待审核)</text>
				</view>
			</view>
		</view>
		<view v-if="showMore" class="look-more" @click="moreComment">查看更多</view>
	</view>
</template>

<script>
	import {formatImg} from "../common/index.js"
	import myIcon from "../components/icon.vue"
	import copyText from '../common/utils/copy_text'
	export default {
		props:{
			listData: Array,
			type:{
				type:[Number,String],
				default:1
			},
			showMore:{
				type:[String,Boolean],
				default:true
			},
			answer:{
				type:Boolean,
				default:false
			},
			money:{
				type:[String,Number],
				default:0
			},
			isleader:{
				type:[String,Number],
				default:0
			},
			parentId:[String,Number],
			adoptId:[String,Number],
			banReply:{
				type:Boolean,
				default:false
			}
		},
		components: {
			myIcon
		},
		data() {
			return {
				id:""
			}
		},
		filters:{
			imgUrl(img, param=""){
				if(!img){
					return ""
				}
				return formatImg(img, param)
			}
		},
		methods: {
			moreComment(){
				this.$navigateTo('/pages/comment_list/comment_list?id='+this.parentId+'&type='+this.type)
			},
			handleReply(parentIndex,index){
				if(this.longTap||this.banReply){
					return
				}
				if(index!==undefined){
					// return
					let be_reply = this.listData[parentIndex].children[index].nickname
					let be_commentid = this.listData[parentIndex].children[index].id
					this.$emit('clickReply',{parentIndex,index,be_reply,be_commentid})
				}else{
					let be_reply = this.listData[parentIndex].nickname
					let be_commentid = this.listData[parentIndex].id
					this.$emit('clickReply',{parentIndex,be_reply,be_commentid})
				}
			},
			handleAction(parentIndex,index){
				// 防止出发点击事件
				this.longTap = true
				setTimeout(()=>{
					this.longTap = false
				},500)
				let isMy = false
				let comment_content
				let actionArr
				let nica
				if(index==undefined&&!this.banReply){
					nica = 0
					comment_content = this.listData[parentIndex].content
					if(this.listData[parentIndex].owner == 1){
						isMy = true
					}
					actionArr = ['回复', '复制', isMy||this.isleader==1?'删除':'举报']
					if(this.isleader==1){
						actionArr.push('禁言')
						actionArr.push('审核通过')
					}
				}
				if(index==undefined&&this.banReply){
					nica = 1
					comment_content = this.listData[parentIndex].content
					if(this.listData[parentIndex].owner == 1){
						isMy = true
					}
					actionArr = [ '复制', isMy||this.isleader==1?'删除':'举报']
					if(this.isleader==1){
						actionArr.push('禁言')
						actionArr.push('审核通过')
					}
				}
				if(index!=undefined){
					nica = 0
					comment_content = this.listData[parentIndex].children[index].content
					if(this.listData[parentIndex].children[index].owner == 1){
						isMy = true
					}
					actionArr = ['回复','复制', isMy||this.isleader==1?'删除':'举报']
					if(this.isleader==1){
						actionArr.push('禁言')
						actionArr.push('审核通过')
					}
				}
				uni.showActionSheet({
					itemList: actionArr,
					success: res=> {
						switch(res.tapIndex){
							case 0-nica:
								this.handleReply(parentIndex,index)
								break
							case 1-nica:
								copyText(comment_content, ()=>{
									uni.showToast({
										title:"复制成功",
										icon:"none"
									})
								})
								break
							case 2-nica:
								if(isMy||this.isleader==1){
									console.log("删除")
									this.$emit('delComment',{parentIndex,index})
								}else{
									console.log("举报")
									let id;
									if(index!==undefined){
										id = this.listData[parentIndex].children[index].id
									}else{
										id = this.listData[parentIndex].id
									}
									this.toJubao(id)
								}
								break
							case 3-nica:
								let uid
								if(index!==undefined){
									uid = this.listData[parentIndex].children[index].uid
								}else{
									uid = this.listData[parentIndex].uid
								}
								console.log("uid:"+uid,"执行禁言")
								this.$ajax.post('building_circle/forbidden',{uid:uid},res=>{
									uni.showToast({
										title:res.data.msg,
										icon:res.data.code===1?'success':'none'
									})
								})
								break
							case 4-nica:
								this.handlePass(parentIndex, index)
						}
					},
					fail: function (res) {
						console.log(res.errMsg);
					}
				});
			},
			// 审核评论
			handlePass(parentIndex, index){
				let comment_id
				if(index!==undefined){
					comment_id = this.listData[parentIndex].children[index].id
				}else{
					comment_id = this.listData[parentIndex].id
				}
				console.log("comment_id:"+comment_id,"审核评论")
				this.$ajax.post('building_circle/verifyReply',{id:comment_id, status: 1},res=>{
					uni.showToast({
						title:res.data.msg,
						icon:res.data.code===1?'success':'none'
					})
					if(res.data.code === 1){
						if(index!==undefined){
							this.listData[parentIndex].children[index].status = 1
						}else{
							this.listData[parentIndex].status = 1
						}
					}
				})
			},
			toJubao(id){
				this.$navigateTo('/user/inform/inform?id='+id+'&type=4')
			},
			adoption(id){
				this.$emit('adoption',{id})
			}
		}
	}
</script>

<style lang="scss">
	.lists{
		background-color: #fff;
	}
	.common-item{
		padding: $uni-spacing-row-base 18upx;
	}
	.common-item .user{
		// margin-bottom: 16upx;
		align-items: center;
	}
	.common-item .user-img{
		width: 60upx;
		height: 60upx;
		border-radius: 50%;
		margin-right: $uni-spacing-row-base;
	}
	.common-item .level-icon{
		width: 38upx;
		height: 38upx;
		margin-right: 40upx;
	}
	.common-item .user-name{
		margin-right: 10upx;
		font-size: 26upx;
		color: #666;
	}
	.common-item .time{
		flex: 1;
		font-size: 22upx;
		color: #666;
	}
	.common-item .common-text{
		padding-left: 80upx;
		word-break: break-all;
	}
	.common-item .reply-list{
		padding-left: 100upx;
		width: 100%;
		box-sizing: border-box;
	}
	.common-item .reply-item{
		margin-top: 10upx;
		padding: 20upx;
		font-size: 24upx;
		background-color: #f3f3f3
	}
	.common-item .reply-item-name{
		color: #36648B
	}
	.grey{
		color: #999
	}
	.look-more{
		padding: $uni-spacing-row-base;
		text-align: center;
		font-size: $uni-font-size-base;
		color: #666;
		border-radius:10upx;
	}
	.handle-bar{
		width: 100%;
		display: flex;
		justify-content: flex-end;
	}
	.handle-bar .handle button.plain{
		font-size: 24upx;
		background-color: $uni-color-primary;
		color: $uni-color-primary;
	}
	.icon-box{
		width: 90upx;
		height: 90upx;
		position: absolute;
		top: 0;
		right: 0;
	}
	.icon-box image{
		height: 100%;
		width: 100%;
	}
	.xs_money{
		margin-right: 60upx;
		float: right;
		color: #f65354
	}
	.xs_status{
		position: absolute;
		right: 20upx;
		top: 20upx;
		height: 0;
		width: 0;
		border-left: 70upx solid transparent;
	}
	.xs_status.green{
		border-top: 70upx solid #66cc66;
	}
	.xs_status.gray{
		border-top: 70upx solid #999;
	}
	.xs_status_text{
		position: absolute;
		right: 15upx;
		top: 35upx;
		font-size: 18upx;
		letter-spacing:6upx;
		transform:rotate(45deg);
		color: #ffffff;
	}
	.status{
		margin-left: 24rpx;
		color: #f65354;
	}
</style>
