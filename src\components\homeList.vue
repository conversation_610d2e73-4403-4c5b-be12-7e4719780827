<template>
<view class="data-list">
    <view class="item flex-box" v-for="(item,index) in dataList" :key="item.id">
        <view class="logo-box">
            <image class="logo" :src="item.shoplogo | imgUrl" mode="aspectFill"></image>
        </view>
        <view class="info-box flex-1" @click="toDetail(item.id)">
            <view class="name-line">
                <text class="name">{{item.shopname}}</text>
                <text class="is-top" v-if="item.iftop>1">置顶</text>
                <view v-if="item.shoptel" class="btn" @click.stop.prevent="handleTel(item.shoptel)">
                    <my-icon type="dianhua" color="#ffffff"></my-icon>
                    <text>拨打电话</text>
                </view>
            </view>
            <view class="title">{{item.title}}</view>
            <view class="multigraph" v-if="multigraph">
                <view class="img-box" v-for="(img,idx) in item.imgs" :key="idx">
                    <image @click.stop.prevent="viewImg(item.imgs,idx)" class="img" :src="img | imgUrl" mode="aspectFill"></image>
                </view>
                <view class="img-box perch"></view>
                <view class="img-box perch"></view>
            </view>
            <view v-else class="img-box">
                <image class="img" :src="item.prepath | imgUrl" mode="widthFix"></image>
            </view>
            <view>
                <my-icon type="chengshi"></my-icon>
                <text>{{item.name || ""}}</text>
            </view>
            <view class="footer">
                <text>{{item.click}}浏览，{{item.pcount}}图</text>
                <view class="c-right" @click.stop.prevent="showBar(index)">
                    <my-icon type="pinglun" size="22" color="#91aad5"></my-icon>
                </view>
                <view class="handle-bar" :class="{'show':show_bar_index == index}">
                    <view v-if="item.shoptel" class="bar-item right-line" @click.stop.prevent="handleTel(item.shoptel)">
                        <my-icon type="dianhua" color="#ffffff"></my-icon>
                        <text>电话</text>
                    </view>
                    <view class="bar-item right-line">
                        <my-icon type="shikebiao" color="#ffffff"></my-icon>
                        <text>预约</text>
                    </view>
                    <view v-if="!hideShop" class="bar-item" @click.stop.prevent="toShop(item.shopid)">
                        <my-icon type="dianpu" color="#ffffff"></my-icon>
                        <text>店铺</text>
                    </view>
                </view>
            </view>
            <view v-if="item.content" class="content" :class="!showAll&&item.content?'part':''">{{item.content}}</view>
            <view v-if="!showAll&&item.content" class="open" @click="lookAll">全文</view>
        </view>
    </view>
</view>
</template>

<script>
import myIcon from "../components/icon"
import {
    navigateTo,
    formatImg
} from '../common/index.js'
export default {
    props: {
        dataList: Array,
        hideShop: {
            type: Boolean,
            default: false
        },
        multigraph: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            show_bar_index: -1,
            showAll: false
        }
    },
    components: {
        myIcon
    },
    filters: {
        imgUrl(img) {
            return formatImg(img,'w_6401')
        }
    },
    methods: {
        showBar(index) {
            if (this.show_bar_index == index) {
                this.show_bar_index = -1
            } else {
                this.show_bar_index = index
            }
        },
        handleTel(tel) {
            uni.makePhoneCall({
                phoneNumber: tel
            })
        },
        toDetail(id) {
            if (this.multigraph) {
                return
            }
            navigateTo("/home/<USER>/detail?id=" + id)
        },
        toShop(id) {
            navigateTo("/home/<USER>/detail?id=" + id)
            console.log(id)
        },
        viewImg(urls, index) {
            uni.previewImage({
                current: index.toString(),
                urls: urls,
                indicator: 'number'
            })
        },
        lookAll() {
            this.showAll = true
        }
    }
}
</script>

<style lang="scss">
.data-list {
    .item {
        padding: 24upx 28upx;
        background-color: #fff;

        .logo-box {
            height: 90upx;
            width: 90upx;
            margin-right: 15upx;

            .logo {
                height: 100%;
                width: 100%;
                border-radius: 50%;
            }
        }

        .info-box {
            overflow: hidden;

            .name-line {
                padding: 10upx 0;
                margin-bottom: 10upx;
                display: flex;
                position: relative;
                align-items: center;

                .name {
                    display: inline-block;
                    max-width: 60%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    color: #666;
                }

                .is-top {
                    display: inline-block;
                    padding: 2upx 10upx;
                    margin-left: 10upx;
                    border-radius: 6upx;
                    font-size: 24upx;
                    background-color: #ffda77;
                    color: #ff6565;
                }

                .btn {
                    position: absolute;
                    right: 0;
                    padding: 8upx 10upx;
                    border-radius: 8upx;
                    background-color: #ed414a;
                    color: #fff;
                }
            }

            .title {
                font-size: $uni-font-size-blg;
                width: 100%;
                margin-bottom: 20upx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .multigraph {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;

                .img-box {
                    flex: 1;
                    width: 31%;
                    height: 0;
                    padding-bottom: 32%;
                    margin-bottom: 2%;
                    min-width: 32%;
                    max-width: 32%;
                    position: relative;
                    overflow: hidden;

                    .img {
                        position: absolute;
                        left: 0;
                        right: 0;
                        top: 0;
                        bottom: 0;
                        margin: auto;
                        height: auto;
                        // min-height:100%;
                        // min-width: 100%;
                    }
                }

                .img-box.perch {
                    padding-bottom: 0;
                    margin-bottom: 0
                }
            }

            .img-box {
                .img {
                    width: 100%;
                }
            }

            .footer {
                height: 70upx;
                line-height: 70upx;
                font-size: 26upx;
                color: #999;
                position: relative;

                .handle-bar {
                    padding: 10upx 0;
                    line-height: 50upx;
                    position: absolute;
                    top: 0;
                    display: flex;
                    right: 44upx;
                    width: 0;
                    transition: 0.3s;
                    border-radius: 8upx;
                    background-color: #4d5154;

                    .bar-item {
                        flex: 1;
                        min-width: 33.333%;
                        overflow: hidden;
                        // text-overflow: ellipsis;
                        white-space: nowrap;
                        text-align: center;
                        color: #fff;
                        transform: 0.3s;
                    }
                }

                .handle-bar.show {
                    width: 480upx;
                }
            }

            .content {
                line-height: 1.5;
                font-size: 30upx;
                color: #555;
                background-color: #fff;
            }

            .content.part {
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                word-break: normal;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .open {
                padding: 10upx;
                color: #00c07b;
            }
        }
    }
}
</style>
