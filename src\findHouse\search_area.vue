<template>
	<view class="area_page">
		<view class="area_list">
			<view
				class="area_item"
				v-for="(item, index) in areaList"
				:key="item.areaid"
				:class="{ checked: item.isChecked, current: currentIdx == index }"
				@click="handleClickParent(index)"
			>
				<view class="parent  flex-row">
					<view>{{ item.areaname }}</view>
					<!-- <view
						class="icon"
						v-if="!item.city"
						:style="{
							background: item.isChecked ? '#2D84FB' : '#fff',
							borderColor: item.isChecked ? '#2D84FB' : '#A0A0A0',
						}"
						@click.prevent.stop="selectAllCity(item)"
					>
						<my-icon type="wancheng" color="#fff" size="28rpx"></my-icon>
					</view>
					<view class="icon no_border" v-else> 
						<my-icon
							:type="currentIdx == index ? 'ic_open ' : 'ic_into'"
							color="#3A3F42"
						></my-icon>
					</view> -->
					<view
							class="icon"
							:style="{
								background: item.isChecked ? '#2D84FB' : '#fff',
								borderColor: item.isChecked ? '#2D84FB' : '#A0A0A0',
							}"
						>
							<my-icon type="wancheng" color="#fff" size="28rpx"></my-icon>
						</view>
				</view>
				<!-- <view
					class="children_box"
					:class="{ current: currentIdx == index }"
					v-if="item.city && item.city.length > 0"
				>
					<view
						class="children flex-row"
						v-for="(child, idx) in item.city"
						:key="child.areaid"
						@click.prevent.stop="changeSelect(index, idx, child)"
					>
						<view class="child_name">
							{{ child.areaname }}
						</view>
						<view
							class="icon"
							:style="{
								background: child.isChecked ? '#2D84FB' : '#fff',
								borderColor: child.isChecked ? '#2D84FB' : '#A0A0A0',
							}"
						>
							<my-icon type="wancheng" color="#fff" size="28rpx"></my-icon>
						</view>
					</view>
				</view> -->
			</view>
		</view>
		<!-- <view class="btn  flex-row" @click="save">
			<view class="save flex-1">保存</view>
		</view> -->
	</view>
</template>

<script>
import myIcon from "../components/myIcon";
export default {
	components: {
		myIcon,
	},
	data() {
		return {
			areaList: [],
			currentIdx: 0,
			ids:'',
			// ids: [],
		};
	},
	onLoad(options) {
		if (options.ids) {
			this.ids =options.ids
			// this.ids = options.ids.split(",");
		}
		this.getData();
	},
	methods: {
		formatArea(a, idStr, pIdStr, chindrenStr) {
			var r = [],
				hash = {},
				id = idStr,
				pId = pIdStr,
				children = chindrenStr,
				i = 0,
				j = 0,
				len = a.length;
			for (; i < len; i++) {
				a[i].label = a[i].name;
				delete a[i].name;
				hash[a[i][id]] = a[i];
			}
			for (; j < len; j++) {
				var aVal = a[j],
					hashVP = hash[aVal[pId]];
				if (hashVP) {
					!hashVP[children] && (hashVP[children] = []);
					hashVP[children].push(aVal);
				} else {
					r.push(aVal);
				}
			}
			return r;
		},
		getData() {
			this.$ajax.get("house/areaData.html", {}, (res) => {
				// let area = res.data.area;
				this.areaList =res.data.area.filter(item=>item.parentid==0)
				this.areaList.map((item) => {
					item.isChecked = false;
					if (item.areaid ==this.ids){
						item.isChecked =true
					}
					return item
				})
				// this.areaList = this.formatArea(area, "areaid", "parentid", "city");
				// this.areaList.map((item) => {
				// 	item.isChecked = false;

				// 	if (item.city && item.city.length > 0) {
				// 		// 处理不限显示在上边
				// 		item.city.map((city, index) => {
				// 			if (isNaN(city.areaid)) {
				// 				let obj = city;
				// 				item.city.splice(index, 1);
				// 				item.city.unshift(obj);
				// 			}
				// 			return city;
				// 		});
				// 	}
				// 	this.ids.map((id) => {
				// 		if (id == item.areaid) {
				// 			//处理数据默认显示
				// 			item.isChecked = true;
				// 			if (item.city && item.city.length > 0) {
				// 				item.city.map((city) => {
				// 					city.isChecked = true;
				// 					item.isChecked = true;
				// 					return city;
				// 				});
				// 			}
				// 		} else {
				// 			if (item.city && item.city.length > 0) {
				// 				item.city.map((city) => {
				// 					if (id == city.areaid) {
				// 						city.isChecked = true;
				// 					}
				// 				});
				// 			}
				// 		}
				// 	});
				// });
			});
		},
		//全选 与取消全选
		selectAllCity(item) {
			item.isChecked = !item.isChecked;
			if (item.city && item.city.length > 0) {
				item.city.map((city) => {
					city.isChecked = item.isChecked;
				});
			}
			this.$forceUpdate();
		},
		//判断选中的数量  用于点击不限全选 与取消全选
		changeSelect(pidx, idx, item) {
			if (isNaN(parseInt(item.areaid))) {
				//点击的是全部
				this.setCheckedAll(pidx, idx, !item.isChecked);
			} else {
				item.isChecked == true ? (item.isChecked = false) : (item.isChecked = true);
				if (
					this.getCheckedLength(pidx, idx) ==
					this.areaList[pidx]["city"].length - 1
				) {
					this.areaList[pidx].city.map((city) => {
						if (isNaN(parseInt(city.areaid))) {
							city.isChecked = item.isChecked;
						}
					});
					this.areaList[pidx].isChecked = item.isChecked;
				} else {
					this.areaList[pidx].isChecked = false;
				}
			}
			this.$forceUpdate();
		},
		//获取当前城市下选中的个数
		getCheckedLength(pidx, idx) {
			var len = 0;
			this.areaList.map((area, index) => {
				if (index == pidx) {
					var arr = area.city.filter((city, i) => {
						return city.isChecked;
						// if (index ==pidx&&city.checked){
						// 	arr.push(city)
						// }
					});
					len = arr.length;
				}
			});
			return len;
		},
		// 设置当前城市全选
		setCheckedAll(pidx, idx, status) {
			this.areaList.map((area, index) => {
				if (index == pidx) {
					area.city.map((city, i) => {
						city.isChecked = status;
						area.isChecked = status;
					});
				}
			});
		},
		toMap(idx){
			
			uni.showModal({
				title: '温馨提示',
				content: "您确认订阅 "+this.areaList[idx].areaname +" 区域吗？",
				confirmText:'去选点',
				success:  (res) =>{
					if (res.confirm) {
						this.$navigateTo(`/findHouse/map?id=${this.areaList[idx].areaid}&lat=${this.areaList[idx].tx_mapy}&lng=${this.areaList[idx].tx_mapx}&areaname=${this.areaList[idx].areaname}`)
						console.log('用户点击确定');
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
		},
		handleClickParent(idx) {
			// if (!this.areaList[idx].city) return;
			this.currentIdx = idx;
			this.areaList.map(item=>item.isChecked=false)
			this.$set(this.areaList[idx],'isChecked',true)
			console.log(this.areaList[idx]);
			this.$forceUpdate()
			// this.areaList[idx].isChecked = true
			setTimeout(() => {
				this.toMap(idx)
			}, 200);
			
			// this.$navigateTo(`/findHouse/map?id=${this.areaList[idx].areaid}&lat=${this.areaList[idx].tx_mapy}&lng=${this.areaList[idx].tx_mapx}` )
		},
		save() {
			let area_ids = [],
				area_len = 0;
			this.areaList.map((item, index) => {
				if (item.isChecked == true) {
					area_ids.push(item.areaid);
					if (item.city) {
						// area_len += item.city.length - 1;
					} else {
						// area_len++;
					}
				} else {
					item.city &&
						item.city.map((city) => {
							if (city.isChecked) {
								area_ids.push(city.areaid);
								// area_len += 1;
							}
						});
				}
			});
			area_len=area_ids.length
			this.$navigateBack();
			// uni.navigateBack();
			// uni.switchTab({
			// 	url:'/pages/index/find_house'
			// })
			// setTimeout(() => {
			// 	uni.$emit("getCheckedAgain", { type: "area", area_ids, area_len });
			// }, 300);
		},
	},
};
</script>

<style scoped lang="scss">
.flex-row {
	display: flex;
	flex-direction: row;
}
// .area_page {
// 	padding-bottom: 160rpx;
// }
.area_list {
	background: #fff;

	.area_item {
		.children_box {
			max-height: 0;
			overflow: hidden;
			transition: all 0.8s;
			&.current {
				max-height: 1000rpx;
			}
		}
		.icon {
			width: 40rpx;
			height: 40rpx;
			border: 2rpx solid;
			border-radius: 50%;
			display: flex;
			justify-content: center;
			align-items: center;
			&.no_border {
				border-width: 0;
			}
		}
		.parent {
			padding: 28rpx 32rpx;
			justify-content: space-between;
			align-items: center;
		}
		.children {
			background: #f3f2f7;
			padding: 28rpx 32rpx;
			justify-content: space-between;
			align-items: center;
		}
	}
}
.btn {
	position: fixed;
	bottom: 48rpx;
	width: 100%;
	.save {
		background: #ff656b;
		margin: 0 32rpx;
		border-radius: 5px;
		padding: 26rpx 0;
		color: #fff;
		font-size: 36rpx;
		text-align: center;
		justify-content: center;
		align-items: center;
	}
}
</style>
