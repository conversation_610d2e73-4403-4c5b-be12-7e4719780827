<template>
    <my-popup ref="sharePop" position="bottom" :touch_hide="true">
        <view class="share-pop"> 
          <!-- 涉嫌诱导分享 不显示分享有礼 -->
            <!-- <view class="share-title flex-row" v-if="isHongbao">
              <view class="share-title-line"></view>
              <view class="share-title-icon">
                <image :src="hongbaoShare_icon" mode="aspectFill"></image>
              </view>
              <view class="share-title-text">转发有礼</view>
              <view class="share-title-line"></view>
            </view> -->
            <view class="share-options flex-row">
                <!-- #ifdef APP-PLUS -->
                <view class="flex-1 item" @click="appShare('WXSenceTimeline')">
                    <view class="icon-box friend">
                    <my-icon type="pengyouquan" color="#fff" size="52rpx"></my-icon>
                    </view>
                    <view class="text">分享到朋友圈</view>
                </view>
                <view class="flex-1 item" @click="appShare('WXSceneSession')">
                    <view class="icon-box friend">
                    <my-icon type="weixin" color="#fff" size="52rpx"></my-icon>
                    </view>
                    <view class="text">分享给好友</view>
                </view>
                <!-- #endif -->
                <!-- #ifdef H5 -->
                <view class="flex-1 item" @click="copyLink">
                    <view class="icon-box friend">
                    <my-icon type="pengyouquan" color="#fff" size="52rpx"></my-icon>
                    </view>
                    <view class="text">分享到朋友圈</view>
                </view>
                <view class="flex-1 item" @click="copyLink">
                    <view class="icon-box friend">
                    <my-icon type="weixin" color="#fff" size="52rpx"></my-icon>
                    </view>
                    <view class="text">分享给好友</view>
                </view>
                <!-- #endif -->
                <!-- #ifdef MP -->
                <view class="flex-1 item">
                    <view class="icon-box friend">
                    <my-icon type="pengyouquan" color="#fff" size="52rpx"></my-icon>
                    </view>
                    <view class="text">分享到朋友圈</view>
                </view>
                <button open-type="share" class="flex-1 item">
                    <view class="icon-box friend">
                    <my-icon type="weixin" color="#fff" size="52rpx"></my-icon>
                    </view>
                    <view class="text">分享给好友</view>
                </button>
                <!-- #endif -->
                <view v-if="!isHongbao" class="flex-1 item" @click="showCopywriting()">
                    <view class="icon-box copytext">
                    <my-icon type="copy" color="#fff" size="52rpx"></my-icon>
                    </view>
                    <view class="text">复制文本</view>
                </view>
                <!-- #ifdef H5 || MP-WEIXIN -->
                <view class="flex-1 item" @click="handleCreat()" v-if ="showHaibao">
                    <image v-if="isHongbao" :src="hongbaoShare_img" class="icon-hongbao-haibao" mode="aspectFill"></image>
                    <view v-else class="icon-box poster">
                    <my-icon type="ic_erweima" color="#fff" size="52rpx"></my-icon>
                    </view>
                    <view class="text">生成海报</view>
                </view>
                <!-- #endif -->
            </view>
            <view class="cancel" @click="hide()">取消</view>
        </view>

    </my-popup>
</template>

<script>
import myPopup from './myPopup.vue'
import myIcon from './myIcon'
import { config } from './../common/index'
export default {
    components:{
        myPopup,
        myIcon
    },
    data(){
        return {
          hongbaoShare_img: config.imgDomain + '/hongbao/zhuanfa.png',
          hongbaoShare_icon: config.imgDomain + '/hongbao/hongbao_zhuanfa.png'
        }
    },
    props:{
      showHaibao:{
        type: Boolean,
        default: true
      },
      isHongbao:{
        type: Boolean,
        default: false
      }
    },
    methods: {
        show(){
            this.$refs.sharePop.show()
        },
        hide(){
          uni.showTabBar()
            this.$refs.sharePop.hide()
        },
        // #ifdef APP-PLUS
        appShare(type){
            this.$emit('appShare',type)
        },
        //  #endif 
        // #ifdef H5
        copyLink(){
            this.$emit('copyLink')
        },
        handleCreat(){
             this.$emit('handleCreat')
        },
        //  #endif 
        // #ifdef MP-WEIXIN
        handleCreat(){
            this.$emit('handleCreat')
        },
        //  #endif 
        showCopywriting(){
            this.$emit('showCopywriting')
        },


    },

}
</script>

<style lang="scss" scoped>

view{
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}
.flex-row{
    display: flex;
    flex-direction: row;
    
}
.share-pop{
  background-color: #fff;
  .cancel {
    padding: 32rpx;
    padding-bottom: 48rpx;
    font-size: 32rpx;
    text-align: center;
  }
}
// 分享选项
.share-options{
  background-color: #f5f5f5;
  padding: 24rpx 0;
  padding-top: 56rpx;
  .item{
    align-items: center;
    page-break-after: 24rpx;
    .text{
      margin-top: 12rpx;
      font-size: 24rpx;
      color: #999;
    }
    .icon-box{
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      align-items: center;
      justify-content: center;
    }
    .friend{
      background-color: #00c800;
    }
    .copytext{
      background-color: #fb656a;
    }
    .poster{
      background-color: #fbac65;
    }
  }
}
// 复制文案
.copy-text-box{
  padding: 24rpx 32rpx;
  background-color: #fff;
  width: 600rpx;
  // height: 100%;
  margin-left: 75rpx;
  border-radius: 16rpx;
  .title{
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }
  .info-row{
    line-height: 1.6;
    color: #333;
    .label{
      color: #999;
    }
    .value{
      flex: 1;
      &.highlight{
        color: $uni-color-primary;
      }
    }
  }
  .button{
    align-items: center;
    justify-content: center;
    line-height: 64rpx;
    width: 236rpx;
    text-align: center;
    border-radius: 32rpx;
    margin: 32rpx auto;
    background: #FB656A;
    box-shadow: 0 2px 8px 0 rgba(251,101,106,0.40);
    color: #fff;
  }
  .disabled-btn{
    background-color: #f5f5f5;
    box-shadow: none;
    color: #999;
    >.text{
      margin-left: 12rpx;
    }
  }
}
.share-title {
  background: #f5f5f5;
  justify-content: center;
  align-items: center;
  margin-bottom: -6rpx;
  padding-top: 20rpx;
  z-index: 9;
  .share-title-line {
    width: 168rpx;
    height: 2rpx;
    background: #818080;
  }
  .share-title-icon {
    margin-left: 16rpx;
    image {
      width: 48rpx;
      height: 48rpx;
    }
  }
  .share-title-text {
    font-size: 24rpx;
    color: #4b4b4b;
    margin-left: 10rpx;
    margin-right: 16rpx;
  }
}
.icon-hongbao {
  width: 128rpx;
  height: 100rpx;
}
.icon-hongbao-haibao {
  width: 128rpx;
  height: 100rpx;
}
</style>