<template>
	<view class="content build_news">
		<view class="banner-box" v-if="adv.length>0">
				<block v-for="(item, idx) in adv" :key="idx">
					<view class="banner-item" @click="toLink(item.link)">
						<banner :image="item.image" mode="widthFix" height="auto"></banner>
					</view>
				</block>
			</view>
			<view class="tab-list flex-row bottom-line" :style="{top:advTop}">
				<!-- <view class="tab-item" @click="switchTab('share_list')">楼市圈</view>
				<view class="tab-item active" @click="switchTab('news_list')">楼盘动态</view>
				<view class="tab-item" @click="switchTab('community_photos')" v-if="switch_community_expert===1">小区专家</view>
      				<view class="tab-item" @click="switchTab('news_loushi')" v-if="switch_community_expert===0">楼市资讯</view> -->

					  <view  class="tab-item" v-for="(item,index) in navs" :class="{'active':float==index} "  :key="index+1">  
               <navigator :url="item.path"> {{ item.label }}</navigator>
      				</view>
			</view>
		<view class="new_list">
			
			<view class="news_list">
				<view
					class="news_item"
					:class="'level' + news.level"
					v-for="(news, index) in news_list"
					:key="news.id"
					@click="$navigateTo('/pages/news/detail?id=' + news.id)"
				>
					<view class="time flex-row">
						<text class="attr">{{
							news.level === 3 ? "头条" : news.level === 2 ? "关注" : "动态"
						}}</text>
						<text>{{ news.create_time }}</text>
					</view>
					<view class="house-info flex-row">
						<view class="img"
							><image :src="news.img|imgUrl('w_240')" mode="widthFix"></image
						></view>
						<view class="house-detail">
							<view class="house-name">{{ news.build_name }}</view>
							<view class="house-area">{{ news.areaname }}</view>
							<view class="house-price"
								>{{ news.price_type }}:{{ news.build_price
								}}{{ news.price_unit }}</view
							>
						</view>
						<view
							class="dingyue flex-row"
							@click.stop.prevent="handleFollow(news.is_follow, news.bid, index)"
						>
							<my-icon :type="news.is_follow == 1?'yidingyue':'quxiaodingyue'" size="28upx" :color="news.is_follow == 1 ? '#999' : '#666'"></my-icon>
							<text :class='news.is_follow == 1 ? "yidingyue" : "weidingyue"'>{{ news.is_follow == 1 ? "已订阅" : "订阅" }}</text>
						</view>
					</view>
					<view class="title">{{ news.title }}</view>
					<view class="sub_title" v-if="news.sub_title">{{
						news.sub_title
					}}</view>
					<view class="guanzhu" @click.prevent.stop="toSubForme(3, news.bid)"
						>咨询优惠详情</view
					>
				</view>
			</view>
			<uni-load-more
				:status="get_status"
				:content-text="content_text"
			></uni-load-more>
		</view>
		<sub-form
			:sub_type="sub_type"
			:sub_mode="sub_mode"
			ref="sub_form"
			@onsubmit="handleSubForm"
		></sub-form>
		<chat-tip></chat-tip>
	</view>
</template>

<script>
import shareItem from "../../components/shareItem";
import myIcon from "../../components/myIcon";
import banner from "../../components/banner";
import subForm from "../../components/subForm";
import { uniLoadMore } from "@dcloudio/uni-ui";
import { formatImg } from "../../common/index.js";
import { mapState } from "vuex";
export default {
	data() {
		return {
			get_status: "loading",
			content_text: {
				contentdown: "",
				contentrefresh: "正在加载...",
				contentnomore: "没有更多数据了",
			},
			get_status2: "loading",
			content_text2: {
				contentdown: "",
				contentrefresh: "正在加载...",
				contentnomore: "没有更多数据了",
			},
			bid: "",
			news_list: [],
			share_list: [],
			newsBid: "",
			share: {},
			news_params: {
				page: 1,
				rows: 20,
			},
			build: {},
			sub_type: 0,
			float:1,
			title: "",
			contrastCount: 0,
			adv:[],
			navs: [],
			
		};
	},
	computed: {
		...mapState(["tel400jing"]),
		glabol_middle_tel() {
			return this.$store.state.im.istelcall;
		},
		is_open_im() {
			return this.$store.state.im.ischat;
		},
		sub_mode() {
			return this.$store.state.sub_form_mode;
		},
		login_status() {
			return this.$store.state.user_login_status;
		},
		switch_community_expert() {
      return this.$store.state.switch_community_expert
    },
		advTop() {
      // #ifndef H5
      return 0
      // #endif
      // #ifdef H5
      return '44px'
      // #endif
    }
	},
	components: { uniLoadMore, subForm, shareItem, myIcon,banner },
	onLoad(options) {
		if (options.bid) {
			this.newsBid = options.bid;
		}
		this.handleBudiling()
		this.getData();
		//  uni.setNavigationBarTitle({
		//    title:this.title?`${this.title}动态`:"动态"
		//  })
	},
	filters: {
		levelIcon(val) {
			if (!val) {
				return "";
			}
			let icon = "";
			switch (val) {
				case 1:
					icon = formatImg("/images/new_icon/<EMAIL>", "w_80");
					break;
				case 2:
					icon = formatImg("/images/new_icon/<EMAIL>", "w_80");
					break;
				case 3:
					icon = formatImg("/images/new_icon/<EMAIL>", "w_80");
					break;
			}
			return icon;
		},
		imgUrl(img, param = "") {
			return formatImg(img, param);
		},
	},
	methods: {
		handleBudiling(){
          console.log("222244444444")
          // 获取楼市去信息
          this.$ajax.get('building_circle/navs', {},res=>{
              console.log(res)
              if(res.data.code==1){
                this.navs = res.data.navs
              }
          })
    },
		getData() {
			this.get_status = "loading";
			if (this.news_params.page == 1) {
				this.news_list = [];
			}
			this.$ajax.get(
				"news/buildNews",
				this.news_params,
				(res) => {
					if (res.data.build) this.build = res.data.build;
					this.contrastCount = res.data.contrastCount || 0;
					if (res.data.title) {
						uni.setNavigationBarTitle({
							title: `${res.data.title}动态`,
						});
					} else {
						uni.setNavigationBarTitle({
							title: `楼盘动态`,
						});
					}
					this.get_status = "more";
					if (res.data.code == 1) {
						this.news_list = this.news_list.concat(res.data.list);
						if (res.data.list.length < this.news_params.rows) {
							this.get_status = "noMore";
						}
					} else {
						this.get_status = "noMore";
					}
					let siteCity ='',siteName=''
					if(res.data.SiteCity){
						siteCity=res.data.SiteCity
					}
					if(res.data.SiteName){
						siteName=res.data.SiteName
					}
					// if (res.data.share) {
					// 	this.share = res.data.share;
					// }else {
						let nowTime=new Date()
						let month = nowTime.getMonth() + 1 >= 10 ? nowTime.getMonth() + 1 : '0' + (nowTime.getMonth() + 1)
						let day = nowTime.getDate()>=10?nowTime.getDate(): '0' +(nowTime.getDate())
						let week=''

						const curDay=month+'月'+day+'日'
						switch (nowTime.getDay()) {
								case 1:
									week = "星期一"
									break
								case 2:
									week = "星期二"
									break
								case 3:
									week = "星期三"
									break
								case 4:
									week = "星期四"
									break
								case 5:
									week = "星期五"
									break
								case 6:
									week = "星期六 "
									break
								case 0:
									week = "星期日"
									break
							}

						this.share.title=siteName+'|为您呈现'+curDay + week +'楼盘新鲜动态'
						this.share.content=siteCity + "楼市行情、项目热点、开盘攻略、楼市情报关注【"+siteName+"】"
						this.share.pic=formatImg('/images/icon/icon3/<EMAIL>',"")
						console.log(this.share);
						this.getWxConfig()
					// }
					
					if (res.data.adv) {
						this.adv = res.data.adv;
					}
					// this.adv=[{
					// 		image: "https://images.tengfangyun.com/attachment/other/20190928/53ffb262062da7d51b3010805a13d8427e42549c.jpeg",
					// 		link: "/pages/ershou/ershou"
					// 	}]
					uni.stopPullDownRefresh();
				},
				(err) => {
					uni.stopPullDownRefresh();
				}
			);
		},
		switchTab(type){
				if(type==='news_list'&&this.share_list.length===0){
				this.params.page = 1
				this.getData()
				}
				if(type==='share_list'){
					this.$navigateTo('/pages/community/community')
				}
				if(type==='news_loushi'){
				// this.news_params.page = 1
				// this.getData()
				this.$navigateTo('/pages/news/news')
				}
				if(type==='community_photos'){
					this.$navigateTo('/pages/house_price/photos')
				}
			},
		
		// 处理关注或取消关注楼盘
		handleFollow(is_follow, bid, index) {
			if (is_follow == 1) {
				this.cancelFollow(bid, index);
			} else {
				this.follow(bid, index);
			}
		},
		// 关注楼盘
		follow(bid, index) {
			this.$ajax.get("build/followBuild.html", { bid }, (res) => {
				if (res.data.code === 1) {
					uni.showToast({
						title: res.data.msg,
					});
					this.news_list[index].is_follow = 1;
				} else {
					uni.showToast({
						title: res.data.msg,
						icon: "none",
					});
				}
			});
		},
		// 取消关注楼盘
		cancelFollow(bid, index) {
			this.$ajax.get("build/cancelFollowBuild.html", { bid }, (res) => {
				if (res.data.code === 1) {
					uni.showToast({
						title: res.data.msg,
					});
					this.news_list[index].is_follow = 0;
				} else {
					uni.showToast({
						title: res.data.msg,
						icon: "none",
					});
				}
			});
		},

	
		toSubForme(type, bid) {
			this.bid = "";
			this.bid = bid;
			this.sub_type = type;
			this.$refs.sub_form.showPopup();
		},
		handleSubForm(e) {
			//提交报名
			e.from = "楼盘动态";
			e.bid = this.bid;
			e.type = this.sub_type || "";
			this.$ajax.post("build/signUp.html", e, (res) => {
				uni.hideLoading();
				if (res.data.code === 1) {
					// 没开启引导登录模式或已经绑定手机号了
					if (this.sub_mode !== 2 || res.data.status === 3) {
						//提示报名成功
						uni.showToast({
							title: res.data.msg,
							icon: "none",
						});
						this.$refs.sub_form.closeSub();
					} else {
						this.$refs.sub_form.getVerify();
					}
				} else {
					uni.showToast({
						title: res.data.msg,
						icon: "none",
					});
				}
			});
		},
		
		
		showTips() {
			uni.showModal({
				title: "免责声明",
				content:
					"  本页面内容，旨在满足广大用户的信息需求而采集提供，如有异议请及时联系我们，本页面内容不代表本网站观点或意见，仅供用户参考以借鉴",
				showCancel: false,
				confirmText: "我知道了",
			});
		},
	},
	onReachBottom() {
		if (this.get_status !== "more") {
			return;
		}
		this.news_params.page++;
		this.getData();
	},
	onPullDownRefresh() {
		this.news_params.page = 1;
		this.getData();

	},
	onShareAppMessage() {
		if (this.share) {
			return {
				title: this.share.title || "",
				content: this.share.content || "",
				imageUrl: this.share.pic ? formatImg(this.share.pic, "w_6401") : "",
			};
		} else {
			return {
				title: this.title + "最新动态" || "",
			};
		}
	},
};
</script>

<style lang="scss" scoped>
view {
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}
.flex-row {
	flex-direction: row;
}
.banner-box{
	padding: 32upx 48upx;
	.banner-item{
		border-radius: 8upx;
		overflow: hidden;
		height: 140upx;
	}
}
.content {
	//   padding-top: 90rpx;
	// padding-bottom: 120rpx;
	.top {
		padding: 50upx 32upx;
		text {
			font-size: 28upx;
		}
		.tips {
			margin-left: 15upx;
			color: cornflowerblue;
		}
	}
	.tab-list {
		padding: 0 48rpx;
		justify-content: space-between;
		// position: fixed;
		// top: var(--window-top);
		// width: 100%;
		position: sticky;
		background-color: #fff;
		z-index: 2;
		.tab-item {
			flex: 1;
			padding: 24rpx;
			text-align: center;
			position: relative;
			&.active {
				color: $uni-color-primary;
				&::after {
					content: "";
					height: 8rpx;
					border-radius: 4rpx;
					background-color: $uni-color-primary;
					position: absolute;
					bottom: 0;
					width: 48rpx;
					left: 0;
					right: 0;
					margin: auto;
				}
			}
		}
	}
	.flex-row {
		display: flex;
		flex-direction: row;
	}
	.news_list {
		padding: 24upx 48rpx 0;
		.news_item {
			padding-left: 20rpx;
			padding-bottom: 48rpx;
			position: relative;
			&::before {
				content: "";
				position: absolute;
				width: 2rpx;
				top: -10rpx;
				bottom: 0;
				left: -0.5px;
				background-color: #f5f5f5;
			}
			&::after {
				content: "";
				position: absolute;
				width: 20rpx;
				height: 20rpx;
				border-radius: 50%;
				background-image: linear-gradient(180deg, #f7918f 0%, #fb656a 100%);
				top: 10rpx;
				left: -10rpx;
			}
			.attr {
				line-height: 1;
			}
			&.level1 {
				&::after {
					background-image: linear-gradient(to right, #ff706b, #fdaa5e);
				}
				.time {
					.attr {
						background-image: linear-gradient(to right, #ff706b, #fdaa5e);
					}
				}
			}
			&.level2 {
				&::after {
					background-image: linear-gradient(180deg, #8cd3fc 0%, #4cc7f6 100%);
				}
				.time {
					.attr {
						background-image: linear-gradient(180deg, #8cd3fc 0%, #4cc7f6 100%);
					}
				}
			}
			&.level3 {
				&::after {
					background-image: linear-gradient(180deg, #f7918f 0%, #fb656a 100%);
				}
				.time {
					.attr {
						background-image: linear-gradient(180deg, #f7918f 0%, #fb656a 100%);
					}
				}
			}
			.house-info {
				margin-bottom: 48upx;
				padding: 0 20upx;
				.img {
					width: 128upx;
					height: 106upx;
					margin-right: 24upx;
					image {
						width: 100%;
					}
				}
				.house-detail {
					justify-content: space-between;
					.house-name {
						font-size: 28upx;
						font-weight: 600;
						color: #333;
					}
					.house-area {
						font-size: 22upx;
						color: #999;
					}
					.house-price {
						font-size: 22upx;
						color: #999;
					}
				}
				.dingyue {
					margin-left: auto;
					font-size: 14px;
					color: #666666;
					align-items: baseline;
					text{
						margin-left: 4upx;
						&.yidingyue{
							color: #999;
						}
					}
				}
			}
			.time {
				align-items: center;
				padding-left: 20rpx;
				margin-bottom: 24rpx;
				font-size: 22rpx;
				color: #999;
				.attr {
					line-height: 1;
					padding: 4rpx 10rpx;
					margin-right: 10rpx;
					font-size: 24rpx;
					color: #fff;
					background-image: linear-gradient(180deg, #f7918f 0%, #fb656a 100%);
					border-top-left-radius: 8rpx;
					border-bottom-right-radius: 8rpx;
				}
			}
			.title {
				font-weight: bold;
				font-size: 32rpx;
				padding: 0 20rpx;
				margin-bottom: 24rpx;
			}
			.sub_title {
				padding: 0 20rpx;
				font-size: 28rpx;
				color: #666;
				margin-bottom: 24rpx;
			}
			.guanzhu {
				background-image: linear-gradient(
					135deg,
					rgba(247, 145, 143, 0.1) 0%,
					rgba(251, 101, 106, 0.1) 100%
				);
				border-radius: 4px;
				color: #fb656a;
				text-align: center;
				padding: 20upx 0;
			}
		}
	}
	.share-list {
		padding: 24rpx 48rpx;
		.adviser-box {
			align-items: center;
			justify-content: space-between;
			padding: 24rpx 0;
			margin-bottom: 24rpx;
			.prelogo {
				margin-right: 24rpx;
				width: 54rpx;
				height: 54rpx;
				border-radius: 27rpx;
				background-color: #f5f5f5;
			}
			.adviser-info {
				flex: 1;
				overflow: hidden;
				line-height: 1;
				.name {
					align-items: center;
					font-size: 26rpx;
					margin-bottom: 16rpx;
					.text {
						margin-right: 16rpx;
					}
				}
				.level_icon {
					width: 28rpx;
					height: 28rpx;
				}
				.level {
					font-size: 24rpx;
					color: #999;
				}
			}
			.btn-list {
				line-height: 1;
				.btn {
					margin-left: 24rpx;
					padding: 16rpx 16rpx;
					min-width: 120rpx;
					text-align: center;
					color: $uni-color-primary;
					border: 1rpx solid $uni-color-primary;
					border-radius: 8rpx;
				}
			}
		}
	}
}
.build_news {
	background: #fff;
}



</style>
