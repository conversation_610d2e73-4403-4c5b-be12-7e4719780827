<template>
	<view class="shop-list">
            <view class="advier-list apply-list" v-if =" apply_list.length>0&&(is_master==1||is_manager==1)" >
                <view class="title">
                    新成员申请
                </view>
                <view
                    class="adviser-item flex-row bottom-line"
                    @click="goDetail(item.id)"
					v-for="item in apply_list"
					:key="item.id"
                >
                    <view class="header_img">
                        <image
                            mode="aspectFill"
                            :src="item.prelogo|imageFilter('w_400')"
                        ></image>
                    </view>
                    <view class="info">
                        <view class="name flex-row">
                            <text class="text">{{item.cname}}</text>
                        
                        </view>
                        <view class="data flex-row">
                            <text>请求加入团队</text>
                        </view>
                    </view>
                    <view class="adviser-right flex-row">
                        <view class="btns first" @click.prevent.stop='agree(item.apply_id,2)'>
                            <view class="btn">同意</view>
                        </view>
                        <view class="btns" @click.prevent.stop='agree(item.apply_id,3)'>
                            <view class="btn">拒绝</view>
                        </view>
                    </view>
                </view>
            </view>

		<!-- 经纪人列表 -->
		<view class="advier-list">
            <view class="title">
                所有经纪人
            </view>
				<!-- 当前用户是老板 -->
				<template v-if="is_master==1">
				
				<uni-swipe-action v-for="(item,index) in agent_list"
				:key="item.id" >  
					<uni-swipe-action-item >
						<view class="list-item flex-row bottom-line" @click="goDetail(item.id)">
							<view class="header_img">
								<image
									mode="aspectFill"
									:src="item.prelogo|imageFilter('w_400')"
								></image>
								
							</view>
							<image v-if="index==1"
								mode="widthFix"
								class="brand"
								src="https://images.tengfangyun.com/images/new_icon/No2.png"
								></image>
								<image v-if="index==0"
								mode="widthFix"
								class="brand"
								src="https://images.tengfangyun.com/images/new_icon/No1.png"
								></image>
								<image v-if="index==2"
								mode="widthFix"
								class="brand"
								src="https://images.tengfangyun.com/images/new_icon/No3.png"
								></image>					
							<view class="info">
								<view class="name flex-row">
									<text class="text">{{item.cname}}</text>
									<text class="dianzhang" v-if="item.is_manager==1">店长</text>
									
								</view>
								<view class="data flex-row">
									<text>{{item.advice_num||0}}人咨询过他</text>
								</view>
							</view>
							<view class="adviser-right flex-row">
								<view class="btns" :class="{'btns-disabled':item.is_manager==1}" @click.prevent.stop="setMange(item.id,item.is_manager)">
									<view class="btn">设为店长</view>
								</view>
							</view>
						</view>
					
					<template  slot="option" :scope="item">
						<view class="cancel_collect" @click.prevent.stop="delMember(item.id)">
							<view class="icon-box">
								<my-icon type="ic_delete_w" color="#fff" size="40rpx"></my-icon>
							</view>
						</view>
					</template>
					</uni-swipe-action-item>
				</uni-swipe-action>
				</template>
				<template v-else-if="is_manager==1">
					<view
						class="adviser-item flex-row bottom-line"
						v-for="(item,index) in agent_list"
						:key="item.id"
						@click="goDetail(item.id)"
					>
						<view class="header_img">
							<image
								mode="aspectFill"
								:src="(item.prelogo ) | imageFilter('w_120')"
							></image>
							
						</view>
						<image v-if="index==1"
								mode="widthFix"
								class="brand"
								src="https://images.tengfangyun.com/images/new_icon/No2.png"
								></image>
								<image v-if="index==0"
								mode="widthFix"
								class="brand"
								src="https://images.tengfangyun.com/images/new_icon/No1.png"
								></image>
								<image v-if="index==2"
								mode="widthFix"
								class="brand"
								src="https://images.tengfangyun.com/images/new_icon/No3.png"
								></image>
						<view class="info">
							<view class="name flex-row">
								<text class="text">{{item.cname || "经纪人" }}</text>
							</view>
							<view class="data flex-row">
								<text>{{item.advice_num||0}}人咨询过他</text>
							</view>
						</view>
						<view class="adviser-right">
							<view class="adviser-right flex-row" @click.prevent.stop="delMember(item.id)">
									<view class="btns">
										<view class="btn">移除会员</view>
									</view>
								</view>
						</view>
					
					</view>
				</template>
				<template v-else>
					<view
						class="adviser-item flex-row bottom-line"
						v-for="(item,index) in agent_list"
						:key="item.id"
						@click="goDetail(item.id)"
					>
						<view class="header_img">
							<image
								mode="aspectFill"
								:src="(item.prelogo ) | imageFilter('w_120')"
							></image>
							
						</view>
						<image v-if="index==1"
								mode="widthFix"
								class="brand"
								src="https://images.tengfangyun.com/images/new_icon/No2.png"
								></image>
								<image v-if="index==0"
								mode="widthFix"
								class="brand"
								src="https://images.tengfangyun.com/images/new_icon/No1.png"
								></image>
								<image v-if="index==2"
								mode="widthFix"
								class="brand"
								src="https://images.tengfangyun.com/images/new_icon/No3.png"
								></image>
						<view class="info">
							<view class="name flex-row">
								<text class="text">{{item.cname || "经纪人" }}</text>
							</view>
							<view class="data flex-row">
								<text>{{item.advice_num||0}}人咨询过他</text>
							</view>
						</view>
						
							<view class="adviser-right flex-row">
                                <view class="btns btn-round first" @click.prevent.stop='advAsk(item)'>
                                <my-icon type="ic_zixun1" size="45rpx" color='#ff656c'></my-icon>
                                </view>
                                <view class="btns btn-round" @click.prevent.stop='handleTel(item)'>
                                    <!-- <view class="btn">电话</view> -->
                                    <my-icon type="ic_dianhua1" size="45rpx" color= '#ff656c'></my-icon>
                                </view>
                            </view>
						</view>
				</template>
		</view>
		<uni-load-more
			:status="get_status"
			:content-text="content_text"
		></uni-load-more>
		<view class="footer" v-if="is_master==1||is_manager==1">
			<view class="invite" @click="showSharePop">邀请同事加入</view>
		</view>
		<share-pop ref="show_share_pop" @copyLink="copyLink" @appShare="appShare" :showHaibao="false" @showCopywriting='showCopywriting'></share-pop>
		<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
		<chat-tip></chat-tip>
		<!-- #ifndef MP-WEIXIN -->
        <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip"></login-popup>
        <!-- #endif -->
		<tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
	</view>
</template>

<script>
import myIcon from "../components/myIcon";
import sharePop from "../components/sharePop";
import shareTip from "../components/shareTip";
import allTel from '../common/all_tel.js'
import getChatInfo from '../common/get_chat_info.js'
import { uniLoadMore ,uniSwipeAction, uniSwipeActionItem } from "@dcloudio/uni-ui";
// #ifndef MP-WEIXIN
import loginPopup from '../components/loginPopup'
// #endif
import { formatImg, showModal } from "../common/index.js";

export default {
	components: {
		myIcon,
		uniLoadMore,
		uniSwipeAction,
		uniSwipeActionItem,
		sharePop,
		shareTip,
		// #ifndef MP-WEIXIN
        loginPopup,
        // #endif
	},
	data() {
		return {
			params: {
				page: 1,
				rows: 10,
				id: 0,
			},
			get_status: "",
			content_text: {
				contentdown: "",
				contentrefresh: "正在加载...",
				contentnomore: "没有更多数据了",
			},
			agent_list: [], //经纪人列表
            login_tip: "",
			apply_list:[],
			is_master:0,
			is_manager:0,
			current_uid:0,
			user_info:{},
			link:"",
			show_share_tip:false,
			tel_res: {},
			show_tel_pop: false
		};
	},
	computed: {
		is_open_im() {
            return this.$store.state.im.ischat
        },
        is_open_middle_num() {
            return this.$store.state.im.istelcall
        },
        login_status() {
            return this.$store.state.user_login_status
        }
	},
	onLoad(option) {
		if (option.id) {
			this.params.id = option.id;
		}
		uni.showLoading({
			title: "加载中...",
			mask: true,
		});
		this.getData();
	},

	methods: {
		// 获取经纪人列表数据
		getData() {
			this.get_status = "loading";
			if (this.params.page === 1) {
				this.agent_list = [];
			}

			let url = "agentCompany/listStoreMember";
			this.$ajax.get(url, this.params, (res) => {
				uni.hideLoading();
				
				this.$store.state.user_login_status = res.data.status;
				if (res.data.share) {
					this.share = res.data.share;
					this.getWxConfig()
				}else {
					this.share={
						title: "门店经纪人",
						content: "门店经纪人",
						pic:''
					}
					this.getWxConfig()
				}
				if (res.data.current_uid&&(res.data.is_master&&res.data.is_master==1||res.data.is_manager&&res.data.is_manager==1)){
					this.current_uid=res.data.current_uid
					this.getLink(this.current_uid)
				}
				if (res.data.code === 1) {
					if (res.data.list.length < this.params.rows) {
						this.get_status = "noMore";
					} else {
						this.get_status = "more";
					}
					this.apply_list=res.data.listApply
					this.agent_list = this.agent_list.concat(res.data.list);
					this.is_master=res.data.is_master
					this.is_manager=res.data.is_manager
					
				} else {
					this.get_status = "noMore";
					this.params.page >= 1 && this.params.page--; //默认第一页为 0
				}
				uni.stopPullDownRefresh();
			});
		},
		agree(id,status){  //deelApply
			this.$ajax.get('agentCompany/deelApply',{id,status},res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title:res.data.msg
					})
					setTimeout(() => {
						this.getData()
					}, 1000);
                }else {
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            })

		},
		setMange(uid,is_manager){
			if(is_manager==1){
				return 
			}
			this.$ajax.get('agentCompany/setStoreManager',{uid,store_id:this.params.id},res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title:res.data.msg
					})
					setTimeout(() => {
						this.getData()
					}, 1000);
                }else {
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            })
		},
		delMember(uid){
			this.$ajax.get('agentCompany/delStoreMember',{uid,store_id:this.params.id},res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title:res.data.msg
					})
					setTimeout(() => {
						this.getData()
					}, 1000);
                }else {
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            })
		},
		showSharePop(){
			this.getShortLink()
			this.$refs.show_share_pop.show()
		},
		getShortLink(){
			if (this.is_manager==1||this.is_master==1){
				this.link = 'https://'+window.location.host+'/h5/shops/be_invited?user_id='+this.current_uid+"&store_id="+this.params.id
			}else {
				link=window.location.href
			}
			this.$ajax.get('build/shortUrl.html', {page_url: this.link}, res=>{
                if(res.data.code === 1){
                this.link = res.data.short_url
                }
            })

		},
		 // 和经纪人发起聊天
        advAsk(e) {
            if (this.is_open_im == 0) {
                this.$navigateTo('/pages/agent/detail?id=' + id)
                return
            }
            // #ifdef MP-WEIXIN
            getChatInfo(e.id, 8)
            //  #endif
            // #ifndef MP-WEIXIN
            this.checkLogin('为方便您及时接收消息通知，请输入手机号码', ()=>{
                getChatInfo(e.id, 8)
            })
            //  #endif
        },
        // 拨打经纪人电话
        handleTel(e) {
			this.tel_params = {
				type: 3,
				callee_id: e.id,
				scene_type:3,
				scene_id:e.id,
				success: (res)=>{
					this.tel_res = res.data
					this.show_tel_pop = true
				}
			}
            // #ifdef MP-WEIXIN
            allTel(this.tel_params)
            // #endif
            // #ifndef MP-WEIXIN
			this.tel_params.intercept_login = true
			this.tel_params.fail = (res)=>{
				if(res.data.code === -1){
						this.$store.state.user_login_status = 1
						this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
						this.$refs.login_popup.showPopup()
				}
				if(res.data.code === 2){
						this.$store.state.user_login_status = 2
						this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
						this.$refs.login_popup.showPopup()
				}
			}
            allTel(this.tel_params)
            // #endif
        },
		retrieveTel(){
			allTel(this.tel_params)
		},
         // 关闭登录弹窗时
        handleCloseLogin() {
            if (this.$store.state.user_login_status === 1) {
                uni.removeStorageSync('token')
                this.$navigateTo('/user/login/login')
            }
            if (this.$store.state.user_login_status === 2) {
                this.$navigateTo('/user/bind_phone/bind_phone')
            }
        },
        checkLogin(tip, callback) {
            this.$ajax.get('member/checkUserStatus', {}, res => {
							if (res.data.code === 1) {
								callback&&callback()
							}else if(res.data.status == 1){
								uni.removeStorageSync('token')
								this.$navigateTo('/user/login/login')
							}else if(res.data.status == 2){
								this.$store.state.user_login_status = res.data.status
								this.login_tip = tip
								this.$refs.login_popup.showPopup()
							}
            })
        },
		getLink(){
			this.$ajax.get('im/contactDetails.html',{user_id:this.current_uid},res=>{
                if(res.data.code === 1){
                    this.user_info = res.data.member
                }
            })
		},
		showCopywriting(){
			let text=''
			if (this.is_manager==1||this.is_master==1){
				
				text = `【邀请者】${this.user_info.nickname||''}
【邀请链接】${this.link}`
			}else {
				text = `【经纪人列表】'经纪人列表'
【查看链接】${this.link}`
			}
			this.copyWechatNum(text, ()=>{
				this.copy_success = true
			})
		},
		// #ifdef H5
		copyLink(){
			if (this.is_manager==1||this.is_master==1){
				this.share.title=this.user_info.nickname+'邀请您加入团队'
				this.share.content="邀请加入团队"
				this.share.link = 'https://'+window.location.host+'/h5/shops/be_invited?user_id='+this.current_uid+"&store_id="+this.params.id
			}else {
				this.share.link=window.location.href
			}
			this.getWxConfig()
			this.show_share_tip=true
		},
		// #endif
			// #ifndef H5
		copyWechatNum(cont) {
			uni.setClipboardData({
				data: cont,
				success: res => {
				// uni.showToast({
				//   title: "复制成功",
				//   icon: "none"
				// })
				}
			})
		},
		// #endif
		// #ifdef H5
		copyWechatNum(cont, callback) {
			let oInput = document.createElement('textarea')
			oInput.value = cont
			document.body.appendChild(oInput)
			oInput.select() // 选择对象;
			oInput.setSelectionRange(0, oInput.value.length);
			document.execCommand('Copy') // 执行浏览器复制命令
			uni.showToast({
				title: '复制成功',
				icon: 'none'
			})
			oInput.blur()
			oInput.remove()

			if(callback) callback()
		},
		// #endif

		goDetail(id) {
			if (!id) return;
			this.$navigateTo("/pages/agent/detail?id=" + id);
		},
	},
	// 触底加载
	onReachBottom() {
		this.params.page++;
		this.getData();
	},
	onShareAppMessage() {
			return {
				title: this.share.title,
				content: this.share.content || "",
				imageUrl: this.share.pic ? formatImg(this.share.pic, "w_6401") : "",
			};
		
	},

};
</script>

<style scoped lang="scss">
view {
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}
.flex-row {
	flex-direction: row;
}
.shop-list{
	padding-bottom: 120upx;
}
// 经纪人列表
.advier-list {
	padding: 24upx 0;
	background-color: #fff;
	&.apply-list{
		padding-bottom: 0;
	}
	.list-item{
			justify-content: space-between;
			align-items: flex-start;
			padding:24upx 48upx 24rpx 48upx;
			width: 100%;
			position: relative;
		}
	.adviser_index {
			margin-top: 20rpx;
			margin-right: 20rpx;
			font-size: 28rpx;
			font-weight: bold;
			color: #333;
		}
		.header_img {
			width: 88rpx;
			height: 88rpx;
			margin-right: 15rpx;
			overflow: hidden;
			position: relative;
			background-color: #ffffff;
			border-radius: 50%;
			margin-bottom: 8upx;
			image {
				// width:88upx;
				width: 100%;
				height: 100%;
				margin: 0 auto;
				
			}
			
			
		}
		.brand{
				position: absolute;
				left: 36upx;
				bottom: 4upx;
				width: 110upx;
		}
		
		image {
			width: 100%;
		}
		
		
		.info {
			flex: 1;
			overflow: hidden;
			.name {
				display: flex;
				align-items: center;
				margin-bottom: 22rpx;
				.text {
					// flex: 1;
					font-size: 32rpx;
				}
                .dianzhang{
                    background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
                    border-radius: 2px;
                    color: #fff;
                    font-size: 22upx;
                    padding: 2upx 10upx;
                    margin-left: 10upx;
                }
			}
			.mgl-20 {
				margin-left: 20rpx;
			}
			.data {
				display: inline-block;
				margin-bottom: 6rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				font-size: 22rpx;
				color: #999;
			}
		}

		.adviser-right {
			align-items: flex-end;
            align-self: center;
            .btns{
                font-size: 14px;
                color: #FB656A;
                padding: 12upx 16upx;
                background: #FFFFFF;
                border: 1px solid #FB656A;
                box-shadow: 0 4upx 8upx 0 rgba(251,101,106,0.10);
                border-radius: 8upx;
                &.first{
                    margin-right: 24upx;
                }
				&.btns-disabled{
					border: 2upx solid #D8D8D8;
					color: #D8D8D8;
					box-shadow:none;

				}
				&.btn-round{
					background: rgba(251, 101, 106,0.1);;
					border: none;
					box-shadow: 0 4upx 8upx 0 rgba(251,101,106,0.10);
					border-radius: 50%;
					width: 64upx;
					height: 64upx;
					align-items: center;
				}
            }
		}
    .title{
        padding: 24upx 48upx 12upx;
        font-size: 40upx;
        color: #333333;
        letter-spacing: 0;
        font-weight: 600;
    }
	.adviser-item {
		justify-content: space-between;
		align-items: flex-start;
		padding:12upx 48upx 12rpx 48upx;
		
		
		.btn-list {
			align-items: center;
			text {
				color: #999;
			}
			.btn {
				width: 64rpx;
				height: 64rpx;
				~ .btn {
					margin-left: 30rpx;
				}
				.icon-box {
					width: 64rpx;
					height: 64rpx;
					justify-content: center;
					text-align: center;
					border-radius: 50%;
				}
				.icon {
					width: 64rpx;
					height: 64rpx;
				}
			}
		}
		.zan {
			align-items: center;
			margin-top: 20rpx;
			.zan_num {
				margin-left: 6rpx;
				font-size: 22rpx;
				color: #d8d8d8;
			}
		}
	}
}
.cancel_collect {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  padding: 0 48rpx;
  text-align: center;
  .icon-box {
    width: 80rpx;
    height: 80rpx;
    align-content: center;
    justify-content: center;
    border-radius: 50%;
    background-color: $uni-color-primary;
    box-shadow: 0 2px 8px 0 rgba(251, 101, 106, 0.4);
  }
}
.footer{
	position: fixed;
	bottom: 24upx;
	width: 100%;
	// background: #FB656A;
	.invite{
		margin: 0 48upx;
		text-align: center;
		background: #FB656A;
		box-shadow: 0 4px 12px 0 rgba(251,101,106,0.40);
		border-radius: 22px;
		color: #fff;
		font-size: 32upx;
		padding: 28upx 0;
	}
}
</style>
