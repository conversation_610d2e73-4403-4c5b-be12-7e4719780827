<template>
  <view id="qqmap" class="qqmap"></view>
</template>

<script>
/* eslint-disable */
window.qqMapOnLoad = function() {
  uni.$emit('initMap')
}
/* eslint-enable */
import {
  loadRemoteJs,
  isArray,
  hexColorToRgba
} from '../common/utils/index'
const qqMap = {
  components: {},
  data() {
    return {
      geometries: []
    }
  },
  props: {
    scale: {
      type: Number,
      default: 16
    },
    viewMode: {
      type: String,
      default: '2D'
    },
    longitude: {
      type: [String, Number],
      default: ''
    },
    latitude: {
      type: [String, Number],
      default: ''
    },
    markers: [Array],
    polygons: [Array],
    circles: {
      type: Array,
      default: () => {
        return []
      }
    },
    singleCircle:{
      type:Boolean,
      default:false
    },
    singleMarker:{
      type:Boolean,
      default:false
    },
    mapkey: {
      type: String,
      default: ''
    },
    base: {
      type: String,
      default: 'vector'
    },
  
  
  },
  watch: {
    latitude(val) {
      if (this.qqmap) {
        this.qqmap.panTo(new qq.maps.LatLng(val, this.longitude))
      }
    },
    longitude(val) {
      if (this.qqmap) {
        this.qqmap.panTo(new qq.maps.LatLng(this.latitude, val))
      }
    },
    scale(val){
      if(this.qqmap){
        this.qqmap.zoomTo(val)
      }
    },
    markers(val) {
      if (val) {
        if (this.qqmap) {
          this.setMarkers(val)
        }
      }
    },
    polygons(val) {
      if (val) {
        if (this.qqmap) {
          this.setPolygons(val)
        }
      }
    },
    circles(val) {
      console.log(val);
      if (val) {
        if (this.qqmap) {
          this.setCircles(val)
        }
      }
    }
  },
  destroyed() {
    // this.qqmap && this.qqmap.destroy()
    window.qq = null
    document.getElementById('qq_map') &&
      document.getElementById('qq_map').remove()
  },
  mounted() {
    loadRemoteJs(
      `https://map.qq.com/api/js?v=2.exp&key=${this.mapkey}&callback=qqMapOnLoad`,
      'qq_map'
    ).then(() => {
      uni.$on('initMap', this.initMap)
    })
  },
  methods: {
    // 初始化地图
    initMap() {
      if (this.qqmap) {
        return
      }
      console.log(this.base);
      this.qqmap = new qq.maps.Map(document.getElementById('qqmap'), {
        zoom: this.scale, // 设置地图缩放级别
        center: new qq.maps.LatLng(this.latitude, this.longitude), // 设置地图中心点坐标
        disableDefaultUI: true,
        mapTypeId:this.base=="weixing"?qq.maps.MapTypeId.HYBRID:qq.maps.MapTypeId.ROADMAP
      })
      

      qq.maps.event.addListener(this.qqmap, 'center_changed', () => {
        // 监听视野变化
        const latLng = this.qqmap.getCenter()
        if (latLng.lat && latLng.lng) {
          this.$emit('regionchange', this.qqmap.getCenter())
        }
      })
      qq.maps.event.addListener(this.qqmap, 'click', (e) => {
        // 监听点击事件
        console.log(e.latLng);
        const latLng = e.latLng
        if (latLng.lat && latLng.lng) {
          this.$emit('click', e.latLng)
          this.addCircles(this.circles)
          if (this.singleMarker){
            this.setMarkers(this.markers)
          }
        }
      })
      // 设置标记点
      this.setMarkers(this.markers)
      // 设置多边形区域
      this.setPolygons(this.polygons)
      // 设置圆形区域
      this.setCircles(this.circles)
    },
    // 获取多边形绘制数据
    getPolygonInfo(arr) {
      const geometries = []
      if (isArray(arr)) {
        arr.forEach(item => {
          const geometrie = {
            styleId: item.id,
            id: item.id,
            paths: [],
            strokeColor: item.strokeColor,
            fillColor: item.fillColor,
            strokeWeight: item.strokeWidth
          }
          console.log(item);
          item.points.forEach(path => {
            geometrie.paths.push(
              new qq.maps.LatLng(
                parseFloat(path.latitude),
                parseFloat(path.longitude)
              )
            )
          })
          geometries.push(geometrie)
          // styles[item.id] = new qq.maps.PolygonStyle({
          //   color: hexColorToRgba(item.fillColor),
          //   showBorder: true,
          //   borderColor: item.strokeColor
          // })
        })
      }
      return geometries
    },
    // 设置多边形区域
    setPolygons(polygons) {
      if (!this.qqmap) {
        return
      }
      if (this.polygonArr) {
        this.clearPolygon()
        this.addPolygon(polygons)
        return
      }
      this.polygonArr = []
      this.addPolygon(polygons)
    },
    // 添加多边形
    addPolygon(polygons) {
      const geometries = this.getPolygonInfo(polygons)
      geometries.forEach(item => {
        let polygon = new qq.maps.Polygon({
          id: 'polygon-layer',
          map: this.qqmap,
          strokeWeight: item.strokeWeight,
          strokeColor: item.strokeColor,
          fillColor: new qq.maps.Color(...hexColorToRgba(item.fillColor)),
          path: item.paths
        })
        this.polygonArr.push(polygon)
      })
    },
    // 删除多边形
    clearPolygon() {
      if (this.polygonArr) {
        this.polygonArr.forEach((item, index) => {
          this.polygonArr[index].setMap(null)
        })
        this.polygonArr = []
      }
    },
    // 设置标记点
    setMarkers(markers) {
      if (!this.qqmap) {
        return
      }
      if (this.markerArr) {
        this.clearMarker()
        this.addMarker(markers)
        return
      }
      this.markerArr = []
      this.addMarker(markers)
    },
    // 添加标记点
    addMarker(markers) {
      markers.forEach(marker => {
        let m = new qq.maps.Marker({
          position: new qq.maps.LatLng(marker.latitude, marker.longitude),
          icon: new qq.maps.MarkerImage(
            marker.iconPath,
            new qq.maps.Size(marker.width, marker.height),
            new qq.maps.Point(0, 0),
            new qq.maps.Point(marker.width / 2, marker.height),
            new qq.maps.Size(marker.width, marker.height)
          ),
          map: this.qqmap
        })
        qq.maps.event.addListener(m, 'click', evt => {
          evt.detail = { markerId: marker.id }
          this.$emit('markertap', evt)
        })
        this.markerArr.push(m)
      })
      this.addOverlay(markers)
    },
    // 清除标记点
    clearMarker() {
      if (this.markerArr) {
        this.markerArr.forEach((item, index) => {
          this.markerArr[index].setMap(null)
        })
        this.markerArr = []
      }
    },
    createOverlay(marker) {
      console.log(marker);
      var _self = this
      var myOverlay = function(opts) {
        qq.maps.Overlay.call(this, opts)
      }
      myOverlay.prototype = new qq.maps.Overlay()
      //实现构造方法
      myOverlay.prototype.construct = function() {
        const defaultBgColor = '#ffffff'
        const defaultColor = '#333333'
        //创建了覆盖物的容器，这里我用了一个div，并且设置了样式
        this.dom = document.createElement('div')
        var divStyle = this.dom.style
        divStyle.position = 'absolute'
        divStyle.zIndex = 1
        divStyle.whiteSpace = 'nowrap'
        divStyle.transform = 'translateX(-50%) translateY(-100%)'
        divStyle.padding = `${marker.callout.padding || 8}px`
        divStyle.borderRadius = `${marker.callout.borderRadius || 10}px`
        divStyle.backgroundColor = `${marker.callout.bgColor || defaultBgColor}`
        divStyle.color = `${marker.callout.color || defaultColor}`
        divStyle.textAlign = `${marker.callout.textAlign || 'center'}`
        divStyle.marginTop = '-7px'
        // //将初始化的html填入到了窗口里，这根据您自己的需要决定是否加这属性
        this.dom.innerHTML = this.get('content')

        var triangle = document.createElement('div')
        triangle.setAttribute(
          'style',
          `position: absolute;white-space: nowrap;border-width: 6px;border-style: solid;border-color: ${marker
            .callout.bgColor ||
            defaultBgColor} transparent transparent;border-image: initial;font-size: 12px;padding: 0px;background-color: transparent;width: 0px;height: 0px;transform: translate(-50%, 100%);left: 50%;bottom: 0;`
        )
        this.dom.appendChild(triangle)

        //将dom添加到覆盖物层
        this.getPanes().overlayMouseTarget.appendChild(this.dom)
        this.dom.addEventListener('click', () => {
          var evt = marker
          evt.detail = { markerId: marker.id }
          _self.$emit('markertap', evt)
        })
      }

      //自定义的方法，用于设置myOverlay的html
      myOverlay.prototype.html = function(html) {
        this.dom.innerHTML = html
      }
      //实现绘制覆盖物的方法（覆盖物的位置在此控制）
      myOverlay.prototype.draw = function() {
        //获取地理经纬度坐标
        var position = this.get('position')
        if (position) {
          var pixel = this.getProjection().fromLatLngToDivPixel(position)
          this.dom.style.left = pixel.getX() + 'px'
          this.dom.style.top = pixel.getY() + 'px'
        }
      }
      //实现析构方法（类生命周期结束时会自动调用，用于释放资源等）
      myOverlay.prototype.destroy = function() {
        //移除dom
        this.dom.parentNode.removeChild(this.dom)
      }

      var label = new myOverlay({
        map: this.qqmap,
        position: new qq.maps.LatLng(marker.latitude, marker.longitude),
        content: marker.callout.content
      })
      // if (this.singleMarker){
      //   this.clearOverlay()
      //   this.overlayArr =[label]
      // }else {
        this.overlayArr.push(label)
      // }
    },
    // 添加自定义覆盖物
    addOverlay(markers) {
      if (!this.overlayArr) {
        this.overlayArr = []
      }
      this.clearOverlay()
      markers.forEach(marker => {
        if (marker.callout) {
          this.createOverlay(marker)
        }
      })
    },
    // 自定义覆盖物
    clearOverlay() {
      if (this.overlayArr) {
        this.overlayArr.forEach((item, index) => {
          this.overlayArr[index].destroy()
        })
        this.overlayArr = []
      }
    },
    setCircles(circles) {
      if (!this.qqmap) {
        return
      }
      if (this.circleArr) {
        this.clearrCircles()
        this.addCircles(circles)
        return
      }
      this.circleArr = []
      this.addCircles(circles)
    },
    addCircles(circles) {
      circles.forEach(circle => {
        if (!circle.latitude || !circle.longitude || !circle.radius) {
          return
        }
        var c = new qq.maps.Circle({
          map: this.qqmap,
          center: new qq.maps.LatLng(circle.latitude, circle.longitude),
          radius: circle.radius,
          fillColor: circle.fillColor
            ? new qq.maps.Color(...hexColorToRgba(circle.fillColor))
            : new qq.maps.Color(41, 91, 255, 0.16),
          strokeColor: circle.color || new qq.maps.Color(41, 91, 255, 1),
          strokeWeight: circle.strokeWidth
        })
        if (this.singleCircle){
          this.clearrCircles()
          this.circleArr[0]=c
        }else {
          this.circleArr.push(c)
        }
        
      })
    },
    // 清除标记点
    clearrCircles() {
      if (this.circleArr) {
        this.circleArr.forEach((item, index) => {
          this.circleArr[index].setMap(null)
        })
        this.circleArr = []
      }
    }
  }
}
export default qqMap
</script>

<style scoped lang="scss"></style>
