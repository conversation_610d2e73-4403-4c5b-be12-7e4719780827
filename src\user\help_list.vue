<template>
  <view class="page">
    <view class="collapse" v-for="(block, index) in help_list" :key="index">
      <view class="collapse-title flex-box bottom-line">
        <text>{{block.typename}}</text>
      </view>
      <view class="collapse-list">
        <view class="collapse-item flex-box" v-for="item in block.children" :key="item.id" @click="toDetail(item.id)">
          <text>{{item.title}}</text>
          <my-icon type="ic_into" color="#999"></my-icon>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from "../components/myIcon"
export default {
  components: {
    myIcon
  },
  data () {
   return {
      help_list:[]
    }
  },
  onLoad(){
    this.getData()
  },
  methods: {
    getData(){
      this.$ajax.get('article/faq', {}, res=>{
        if(res.data.code === 1){
          this.help_list = res.data.lists
        }
      })
    },
    toDetail(id){
      this.$navigateTo(`/user/help_detail?id=${id}`)
    }
  }
}
</script>

<style scoped lang="scss">
.page{
  padding-bottom: 32rpx;
}
.collapse{
  margin: 32rpx 48rpx;
  padding: 0 24rpx;
  border-radius: 8rpx;
  background-color: #fff;
  border: 1rpx solid #dedede;
  .collapse-title{
    padding: 24rpx 0;
    justify-content: space-between;
    font-weight: bold;
  }
  .collapse-list{
    .collapse-item{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      justify-content: space-between;
      padding: 24rpx 0;
    }
  }
}
</style>