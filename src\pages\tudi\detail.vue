<template>
	<view class="page" :class="{pdb_120: (sharers_info.adviser_id&&is_open_adviser)||sharers_info.agent_id}">
		<view class="header" :style="{backgroundImage:'url('+bgcolor+')'}">
			<view class="head-info">
				<!-- <view class="title">{{tudiData.xmmc}}</view> -->
				<view class="update-tip">{{ tudiData.tdbh }}</view>
				<view class="update-name" v-if="tudiData.tdname">{{ tudiData.tdname }}</view>
				<view class="header-date">{{ tudiData.yongtu }}</view>
				<view class="header-date">{{ siteName }}</view>
				<view class="share_icon" @click="showSharePop">
					<my-icon type="ic_fenxiang" size="32rpx" color="#fff"></my-icon>
				</view>
			</view>
		</view>
		<view class="yushou-info">
			<view class="yushou-title flex-box flex-grow" v-if="tudiData.zt==1">
				<view class="yushou-title-left">成交时间：{{ tudiData.cjrq }}</view>
				<view class="yushou-area" v-if='tudiData.yjl>0'>溢价<text class="red">{{ tudiData.yjl }}</text>%</view>
			</view>
			<view class="yushou-title flex-box flex-grow" v-if="tudiData.zt==2">
				<view class="yushou-title-left">报名中</view>
			</view>
			<view class="yushou-title flex-box flex-grow" v-if="tudiData.zt==3">
				<view class="yushou-title-left">拍卖中</view>
			</view>
			<view class="yushou-title flex-box flex-grow" v-if="tudiData.zt==4">
				<view class="yushou-title-left">流拍</view>
			</view>

			<view class="xiangmumingcheng">{{ tudiData.tdwz }}</view>
			
			<view class="data-box bg-grey">
				<view class="gongsimingcheng" v-if='tudiData.cjdw'>{{ tudiData.cjdw }}</view>
				<view class="box flex-box" :class='{"bottom-line":tudiData.zt==1}'>
                    
					<view class="text-center flex-1 fenge">
                        <view class="data-title">总面积</view>
                        <view class="data-data"
                            ><text class="data-datas">{{
								showMushu==1?tudiData.mushu: tudiData.mianji 
                            }}</text
                            ></view
                        >
						<view class="data-title mtop12">{{showMushu==1?"亩":'m²'}}</view>
                    </view>
                    <view class="text-center flex-1 fenge">
                        <view class="data-title">起拍价</view>
                        <view class="data-data"
                            ><text class="data-datas">{{ tudiData.qishijia }}</text
                            ></view
                        >
						<view class="data-title mtop12">万元</view>
					</view>	
					<view class="text-center flex-1">
                        <view class="data-title">最大</view>
                        <view class="data-data"
                            ><text class="data-datas">{{ tudiData.rongjilv }}</text
                            ></view
                        >
						<view class="data-title mtop12">容积率</view>
                    </view>
					
                    
                </view>
                <view class="box flex-box"   v-if='tudiData.zt==1'>
					<view class="text-center flex-1 fenge" >
                        <view class="data-title">成交价</view>
                        <view class="data-data"
                            ><text class="data-datas red">{{ tudiData.chengjiaojia }}</text
                            ></view
                        >
						<view class="data-title mtop12">万元</view>
                    </view>
					<view class="text-center flex-1 fenge">
                        <view class="data-title">地价 </view>
                        <view class="data-data"
                            >约<text class="data-datas red">{{ tudiData.dijia }}</text
                            ></view
                        >
						<view class="data-title mtop12">万元/亩</view>
                    </view>
                    
                    <view class="text-center flex-1" >
                        <view class="data-title">楼面价 </view>
                        <view class="data-data"
                            >约<text class="data-datas red" >{{ tudiData.loumianjia }}</text
                            ></view
                        >
						<view class="data-title mtop12">元/m²</view>
                    </view>
                </view>
                
			</view>
            <view class="xuke-info" v-if="tudiData.content">
				<view class="desc-title">地块详情</view>
				<view class="desc-content" v-html='tudiData.content'></view>
			</view>
			<view class="zhoubian" v-if="detail.lat&&detail.lng" @click="goMap">
				<view class="desc-title flex-box"><view class="zhoubian-title">地块区位</view>  <view class="more">详情</view></view>
				<view class="map-box">
					<!-- #ifndef H5 -->
					<map
						class="qqmap"
						:longitude="tudiData.lng"
						:latitude="tudiData.lat"
						:markers="tudi_point"
						:polygons="polygons"
						:scale="15"
						base="weixing"
					></map>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<qqMap
						:mapkey="qqmapkey"
						:scale="15"
						base="weixing"
						:longitude="tudiData.lng"
						:latitude="tudiData.lat"
						:markers="tudi_point"
						:polygons="polygons"
						
					></qqMap>
					<!-- #endif -->
				</view>

			</view>
		
			<view class="dikuai" v-if='other.length>0'>
				<view class="desc-title">周边地块</view>
				<view class="lists">
					<view class="time-line" v-for ="item in other" :key='item.id' @click="toDetail(item.id)"> 
						<view class="time">
							<view class="line-title">
								{{item.cjrq||''}}
							</view>
							<view class="data-card">
								<data-card :item='item' type="tudi"></data-card>
							</view>
						</view>               
					</view>
				</view>
			</view>
			<view class="friend-tips">
                {{friendTips}}
            </view>
		</view>
		
		<view class="sharers_info flex-box" v-if="(sharers_info.adviser_id&&is_open_adviser)||sharers_info.agent_id">
				<view class="img">
					<image :src="sharers_info.prelogo | imageFilter('w_240')" mode="widthFix"></image>
					</view>
				<view class="info flex-1">
						<view class="name">{{sharers_info.cname}}</view>
						<view class="identity">{{sharers_info.identity===1?'置业顾问':'经纪人'}}</view>
				</view>
				<view class="btn_box flex-box">
						<view class="btn" @click="handleChat()">微聊</view>
						<view class="btn" v-if ="(sharers_info.adviser_id&&switch_adviser_tel) ||sharers_info.agent_id"  @click="handleTel()">电话咨询</view>
				</view>
		</view>
		<share-pop ref="show_share_pop" @handleCreat="handleCreat" @copyLink="copyLink" @showCopywriting='showCopywriting'></share-pop>
		<chat-tip></chat-tip>
		<dingyue ref="dingyue" @dingyue="dingyue" :type="type" @login="toLogin" @hideOk="$store.state.updatePageData=false" ></dingyue>
		<enturstBtn v-if="sharers_info.agent_id||sharers_info.adviser_id" :to_user="sharers_info" @click="$refs.enturst_popup.show()" />
        <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
        <enturstBox @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="showLoginPopup('为方便您及时接收消息通知，请输入手机号码')" :to_user="sharers_info" />
        </my-popup>
		<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>

		<!-- 登录弹窗 -->
		<login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
		<chat-tip></chat-tip>
			<tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
	</view>
</template>

<script>
// #ifdef H5
import {setSeo} from '../../common/mixin'
import qqMap from '@/components/qqMap'
// #endif
// import uParse from '../../components/wx-parse/parse.vue'
import dataCard from "../../components/dataCard"
import {getSceneParams, formatImg, config} from "../../common/index.js"
import sharePop from "../../components/sharePop";
import 	shareTip from "../../components/shareTip";
import myIcon from "../../components/myIcon";
import myPopup from "../../components/myPopup";
import dingyue from "../../components/dingyue.vue";
import getChatInfo from '../../common/get_chat_info'
import allTel from '../../common/all_tel.js'
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
import loginPopup from '@/components/loginPopup'
export default {
	data() {
		return {
			id:'',
			tudiData:{
				cjdw:"",
				tdwz:"",
				mianji:"",
				cjrq:"",
				yjl:"",
				mushu:""
			},
			detail:{},
			content:"",
			qrcode:"",
			cardImg:"",
			 // #ifdef H5
			qqmapkey:__uniConfig.qqMapKey,
			// #endif
			tudi_point:[],
			polygons:[],
			other:[],
			showMushu:0,
			sharers_info: {},
			current_user_info: {},
			type:'dingyue',
			link:"",
			login_tip:'',
			show_share_tip:false,
			friendTips:'',
			tel_res: {},
			show_tel_pop: false,
		};
	},
	components: {
		// #ifdef H5
		qqMap,
		// #endif
		myIcon,
		myPopup,
		dataCard,
		sharePop,
		dingyue,
		shareTip,
		loginPopup,
		enturstBtn,
		enturstBox
	},
	computed: {
		imconsu() {
			return this.$store.state.im.adviser;
		},
		ischat() {
			return this.$store.state.im.ischat;
		},
		istelcall() {
			return this.$store.state.im.istelcall;
		},
		siteName() { // 是否开启聊天功能
			return this.$store.state.siteName
		},
		

		sub_mode() {
			return this.$store.state.sub_form_mode;
		},
		bgcolor(){
			return config.imgDomain+'/images/new_icon/record/<EMAIL>'
		},
		

		is_open_adviser() { //是否开启置业顾问功能
			return this.$store.state.im.adviser
		},
		is_open_im() { // 是否开启聊天功能
			return this.$store.state.im.ischat
		},
		switch_adviser_tel(){
			return this.$store.state.switch_adviser_tel
		}
	},
	filters: {
		imgUrl(img, param = "w_400") {
			if (!img) {
				return "";
			}
			return formatImg(img, param);
		},
	},
    onLoad(options){
		// 如果是分享链接进来的
		if (options.shareId && (options.type||options.shareType)) {
				this.shareId = options.shareId
				this.shareType = options.type||options.shareType
				this.share_time =options.f_time||''
		}
        if(options.id){
            this.id = options.id
			this.getData()
			uni.$on('getDataAgain',()=>{
				this.getData()
			})
        }
	},
	onUnload(){
        uni.$off('getDataAgain')
        this.$store.state.updatePageData = false
	},
	onShow(){
		if(this.$store.state.updatePageData){
			this.getData()
			this.$store.state.updatePageData = false
		}
	},
	methods: {
		getData(){
			let params = {id: this.id};
			if(this.shareId&&this.shareType){
					params = {
							id: this.id,
							sid: this.shareId,
							sharetype: this.shareType
					}
			}
      this.type="dingyue"
			params.forward_time=this.share_time ||''
            this.$ajax.get('build/tudiDetail.html',params,res=>{
                // #ifdef H5 || MP-BAIDU
                if(res.data.seo){
                    let seo = res.data.seo
                    if(res.data.share&&res.data.share.pic){
                        seo.image = formatImg(res.data.share.pic,'w_8001')
                    }
                    this.seo = seo
                }
				// #endif
				 // 获取登录状态
                    this.$ajax.get('member/checkUserStatus', {}, res => {
                        if (res.data.code !== 1) {
							this.$store.state.user_login_status = res.data.status
							if (this.$store.state.user_login_status==1){
								this.type='denglu'
								this.$store.state.updatePageData=true
								uni.setStorageSync('backUrl', window.location.href)
								this.showDingyuePop()
							}else {
								this.type='bangshouji'
								this.$store.state.updatePageData=true
								this.showDingyuePop()
							}
                            
                        }
                    })
				if (res.data.disclaimer){
					this.friendTips=res.data.disclaimer
				}
				if (res.data.shareUser) { //当前用户信息
						this.current_user_info = res.data.shareUser
						if(res.data.shareUser.adviser_id){
								this.current_user_info.identity = 1
								this.current_user_info.identity_id = res.data.shareUser.adviser_id
						}else if(res.data.shareUser.agent_id){
								this.current_user_info.identity = 2
								this.current_user_info.identity_id = res.data.shareUser.agent_id
						}
				}
				if (res.data.share_user) { //分享者信息
						this.sharers_info = res.data.share_user
						if(res.data.share_user.adviser_id){
								this.sharers_info.identity = 1
						}else if(res.data.share_user.agent_id){
								this.sharers_info.identity = 2
						}
				}
                if(res.data.code == 1){
                    this.tudiData = res.data.detail
                    const regex = new RegExp('<img', 'gi');
                    const regex2 = new RegExp('style=""', 'gi');
					// 正则匹配处理富文本图片过大显示问题
					if ( res.data.detail.content){
						res.data.detail.content = res.data.detail.content.replace(regex2,"").replace(regex, `<img style="max-width: 100%;"`);
					}
                    
					this.detail = res.data.detail
					this.tudi_point = [
						{
						id: this.tudiData.id,
						iconPath: 'none',
						latitude: res.data.detail.lat,
						longitude: res.data.detail.lng,
						width: 10,
						height: 10,
						callout: {
							content: res.data.detail.tdbh+"示意图",
							padding: 10,
							borderRadius: 8
						}
						}
					]
					this.other=res.data.other
					if (res.data.tupai_calculation_method){
						this.showMushu=res.data.tupai_calculation_method
					}
					if (!res.data.detail.tupai_range) {
						this.polygons = []
					}else {
						const color = '#f65354'
						this.polygons = res.data.detail.tupai_range.map(items => {
							const points = items.paths.map(item => {
							return {
								latitude: parseFloat(item.latitude),
								longitude: parseFloat(item.longitude)
							}
							})
							return {
								id: items.id,
								points,
								strokeWidth: 4,
								strokeColor: color,
								fillColor: `${color}33`
							}
						})
					}
				}
				if (res.data.share){
					this.share=res.data.share
				}else {
					this.share={
						title:this.tudiData.tdbh||'',
						content:this.tudiData.tdwz||'',
						pic:''
					}
				}
				this.share.link=this.getShareLink()
				this.getWxConfig()
            })
		},
		handleCreat(){
			// #ifdef H5
			window.open(`/wapi/poster/landAuction?id=${this.id}&header_from=2`,'_self')
			// #endif
		},
		toLogin(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/login/login")
		},
		toBind(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/bind_phone/bind_phone")
		},
		goMap(){
			this.$navigateTo("/statistics/map?id="+this.tudiData.id+"&lat="+this.tudiData.lat+"&lng="+this.tudiData.lng)
		},
		// 订阅
		showDingyuePop(){
			this.$refs.dingyue.showPopup()
		},
		showSharePop(){
			this.getShortLink()
			this.$refs.show_share_pop.show()
		},
		toDetail(id){
            this.$navigateTo(`/pages/tudi/detail?id=${id}`)
        },
		dingyue(){
            this.$ajax.get("build/subscribeBooking",{type:2},res=>{  //type 1为预售 2 为土拍
                if (res.data.code ==-1){
					this.$store.state.updatePageData=true
					uni.setStorageSync('backUrl', window.location.href)
					this.$navigateTo("/user/login/login")
                }else if (res.data.code ==2){
					this.$store.state.updatePageData=true
                    this.$navigateTo("/user/bind_phone/bind_phone")
                }else if(res.data.code ==1){
                    uni.showToast({
                        title:res.data.msg,
                        icon:"success"
                    })
                    setTimeout(() => {
                        this.$refs.dingyue.hide()
                    },500)
                }else if (res.data.code ==0){  //订阅失败
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                    if (res.data.gzhewm){
                        this.qrcode=res.data.gzhewm
                        setTimeout(() => {
                            this.$refs.qrcode_popup.show()
                        }, 500);
                    }
                    this.$refs.dingyue.hide()
                }
            },err=>{},{disableAutoHandle:true})

		},
		showLoginPopup(tip){
			this.login_tip = tip
			this.$refs.login_popup.showPopup()
		},
		handleCloseLogin() {
            if (this.$store.state.user_login_status === 1) {
                uni.removeStorageSync('token')
                this.$navigateTo('/user/login/login')
            }
            if(this.$store.state.user_login_status===2){
                this.$navigateTo('/user/bind_phone/bind_phone')
            }
        },
        onLoginSuccess(res){
            this.$store.state.user_login_status = 3
            if(this.weituo_is_show){
                console.log("登录成功后继续执行委托接口")
                this.$refs.enturst_box.handleEnturst()
            }
        },
		// 获取分享链接
		getShareLink(){
			let link = window.location.href
			let time =parseInt(+new Date()/1000)
			if (this.current_user_info.identity) { //当前用户是 置业顾问或者经纪人  
					link = `${window.location.origin}${window.location.pathname}?id=${this.id}&shareId=${this.current_user_info.identity_id}&type=${this.current_user_info.identity}&f_time=${time}`
			}
			return link
		},
		getShortLink(){
            this.link=this.getShareLink()
            this.$ajax.get('build/shortUrl.html', {page_url:this.link }, res=>{
                if(res.data.code === 1){
					this.link = res.data.short_url
                }
            })
        },
		// 复制分享链接
		copyLink(){
			this.show_share_tip=true
			// this.copyText(this.getShareLink(), ()=>{
			// 	uni.showToast({
			// 	title: '复制成功,去发送给好友吧',
			// 	icon: 'none'
			// 	})
			// })
		},
		// 复制分享内容
		showCopywriting(){
			console.log("复制内容")
			let mianji=this.showMushu==1?(this.tudiData.mushu+"亩"): (this.tudiData.mianji+"m²")
			let jiage=this.tudiData.zt==1?`【成交价】${this.tudiData.chengjiaojia}万元`:`【起拍价】${this.tudiData.qishijia}万元`
			const content = `【地块编号】${this.tudiData.tdbh}\n【拿地企业】${this.tudiData.cjdw}\n【地块位置】${this.tudiData.tdwz}\n【土地面积】${mianji}\n【成交总额】${this.tudiData.cjyy}亿元 ${this.tudiData.yjl?'溢价 '+this.tudiData.yjl+'%':''}\n${jiage}\n【地块详情】${this.link}`
			this.copyText(content, ()=>{
				uni.showToast({
				title: '复制成功,去发送给好友吧',
				icon: 'none'
				})
			})
		},
		// 复制内容
		copyText(cont, callback) {
			let oInput = document.createElement('textarea')
			oInput.value = cont
			document.body.appendChild(oInput)
			oInput.select() // 选择对象;
			oInput.setSelectionRange(0, oInput.value.length);
			document.execCommand('Copy') // 执行浏览器复制命令
			oInput.blur()
			oInput.remove()
			if(callback) callback()
		},
		// 发起聊天
		handleChat(){
			if(!this.is_open_im){
				if (this.sharers_info.identity == 1) { //置业顾问
					this.$navigateTo('/pages/consultant/detail?id=' + this.sharers_info.adviser_id)
				} else if (this.sharers_info.identity == 2) {
					this.$navigateTo('/pages/agent/detail?id=' + this.sharers_info.agent_id)
				}
				return
			}
			// #ifndef MP-WEIXIN
			// this.checkLogin('当前操作需要绑定手机号，请输入您的手机号', ()=>{
				getChatInfo(this.sharers_info.mid, 24)
			// })
			// #endif
		},
		// 拨打电话
		handleTel(){
			this.tel_params = {
				type: this.sharers_info.identity == 1?'2':'3',
				callee_id: this.sharers_info.mid,
				scene_type:this.sharers_info.identity == 1?'2':'3',
				scene_id:this.sharers_info.mid,
				success: (res)=>{
					this.tel_res = res.data
					this.show_tel_pop = true
				}
			}
			allTel(this.tel_params)
		},
		retrieveTel(){
			allTel(this.tel_params)
		}
	},
	onShareAppMessage() {
		if (this.seo) {
			return {
				title: this.seo.title || "",
				// #ifdef MP-BAIDU
				content: this.seo.description || "",
				imageUrl: this.wxhuifuimg ? formatImg(this.wxhuifuimg, "w_6401") : "",
				// #endif
			};
		}
	},
};
</script>

<style scoped lang="scss">
.pdb_120{
    padding-bottom: 120rpx;
}
.header {
	width: 100%;
	height: 320rpx;
	background-image: linear-gradient(0deg, #f7918f 0%, #fb656a 100%);
	// background: linear-gradient(to bottom right,#8A2BE2,#DC143C);
	display: flex;
	// align-items: center;
	padding: 0 48rpx;
	box-sizing: border-box;
	background-size: 100%;
	background-repeat: no-repeat;
	.head-info {
		width: 100%;
	}
	.title {
		font-size: 46upx;
		color: #fff;
	}
	.update-tip {
		margin-top: 48upx;
		margin-bottom: 24rpx;
		font-size: 40rpx;
		color: #fff;
	}
	.update-name {
		margin-top: 8upx;
		margin-bottom: 16rpx;
		font-size: 36rpx;
		color: #fff;
	}
	.header-date {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 16rpx;
	}
}
.yushou-title {
	padding: 24rpx 0;
	font-size: 22rpx;
	color: #999999;
	.yushou-title-left {
		flex: 1;
	}
	.yushou-area {
		margin-left: auto;
	}
    .red{
        color: #f00;
    }
}
.xiangmumingcheng {
	font-size: 36rpx;
	color: #333;
	font-weight: 600;
}

.yushou-info {
	position: relative;
	top: -24rpx;
	padding: 24rpx 48rpx;
	border-radius: 48rpx 48rpx 16rpx 16rpx;
	background: #fff;
}



.data-box {
	position: relative;
	margin-top: 60rpx;
	.gongsimingcheng {
		position: absolute;
		left: 50%;
		transform: translateX((-50%));
		color: #fff;
		top: -50rpx;
		white-space: nowrap;
    	padding: 10rpx 20rpx;
		border-radius: 20px;
		background-image: linear-gradient(90deg,#fb656a,#fbac65);
		font-size: 28rpx;
		margin: 24rpx 0;
	}
    &.bg-grey {
		background: #f8f8f8;
		&:last-child {
			margin-right: 0;
		}
	}
	padding: 32upx 0;
	background-color: #ffffff;
    .box{
        padding: 24rpx 0;
    }
	
	.data-title {
		margin-bottom: 4upx;
		font-size: 26rpx;
		color: #666;
		&.mtop12{
			margin-top: 4rpx;
			margin-bottom: 0;
		}
	}
	.data-data {
		font-size: 22rpx;
		// font-weight: bold;
		color: #666;
	}
	.data-datas {
		font-size: 32upx;
		font-weight: bold;
		color: #333;
	}
	.red {
		color: $uni-color-primary;
	}
}
.fenge{
	position: relative;
	&:after{
		content: "";
		position: absolute;
		top: 20%;
		bottom: 20%;
		right: 0;
		width: 1px;
		-webkit-transform: scaleX(.5);
		transform: scaleX(.5);
		background-color: $uni-border-color;
	}
}
.xuke-info {
	margin-top: 48rpx;
	.desc-title {
		font-size: 40upx;
		color: #333;
		font-weight: bold;
	}
	.header-date {
		font-size: 22rpx;
		color: #999;
		margin: 24rpx 0;
	}
	.desc-content {
        margin-top: 40rpx;
		font-size: 28rpx;
		color: #666;
	}
}
.zhoubian{
	margin-top: 48rpx;
	.desc-title {
		
		margin-bottom: 48rpx;
		align-items: center;
		.zhoubian-title{
			font-size: 40upx;
			color: #333;
			font-weight: bold;
		}
		.more{
			margin-left: auto;
		}
	}
}
.map-box {
	width: 100%;
	height: 400rpx;
	transition: 0.3s;
	.qqmap {
		width: 100%;
		height: 100%;
	}
}

.dikuai{
	.desc-title {
		font-size: 40upx;
		color: #333;
		font-weight: bold;
		padding: 24rpx 0;
	}
	.lists{
    // padding: 0 48rpx;
    .time-line{
        // padding:0 24rpx ;
        padding-left: 24rpx;
        .time{
            position: relative;
            &:before{
                content:'';
                position: absolute;
                width: 2rpx;
                height: 100%;
                background: #D8D8D8;
                top: 10rpx;
                left: -20rpx;
                
            }
            .line-title{
                position: relative;
                color: #FB656A;
                font-size: 22rpx;
                margin-bottom: 16rpx;
                &:before{
                    content:'';
                    position: absolute;
                    background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
                    width: 20rpx;
                    height: 20rpx;
                    top: 50%;
                    transform: translateY(-50%);
                    left: -30rpx;
                    border-radius: 50%;
                }

            }

       
            
         }
         .data-card{
             padding-bottom: 24rpx;
         }

    }
}
}
.share_icon{
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 24rpx;
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		background-color: rgba(0,0,0, 0.5);
		position: absolute;
		top: 48rpx;
		right: 48rpx;
}
// 分享者信息
.sharers_info{
    position: fixed;
    width: 100%;
    height: 120rpx;
    bottom: 0;
    padding: 0 48rpx;
    box-sizing: border-box;
    align-items: center;
    background-color: #fff;
	z-index: 90;
    .img{
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 16rpx;
		overflow: hidden;
		image{
			width: 100%;
			height: 100%;
		}
    }
    
    .info{
        overflow: hidden;
        .name{
            margin-bottom: 16rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .identity{
            font-size: 24rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: #999;
        }
    }
    .btn_box{
        .btn{
            margin-left: 20rpx;
            padding: 10rpx 34rpx;
            font-size: 26rpx;
            color: $uni-color-primary;
            border: 1px solid $uni-color-primary;
            border-radius: 3px;
            box-shadow: 0 2px 4px 0 rgba(251,101,106,.1);
        }
    }
}
.friend-tips{
	color: #999;
	line-height: 1.5;
    margin-top: 60rpx;
}
</style>
