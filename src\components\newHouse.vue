<template>
	<view class="house-list">
		<block v-for="(item, index) in listsData" :key="index">
			<new-house-item :item-data="item" :info-list="infoList[index]" type="new_house" @click="toDetail"></new-house-item>
		</block>
	</view>
</template>

<script>
	import newHouseItem from '../components/newHouseItem.vue'
	export default {
		props:{
			listsData:Array,
			infoList:Array
		},
		components:{
			newHouseItem
		},
		data() {
			return {
				
			}
		},
		methods:{
			toDetail(e){
				this.$store.state.tempData = e.detail
				if(e.detail.id){
					this.$navigateTo('/pages/new_house/detail?id='+e.detail.id)
				}
			}
		}
	}
</script>

<style lang="scss">
.house-list{
	padding: 0 48rpx;
	background-color: #ffffff;
}
	.ding{
		font-size:$uni-font-size-sm;
		border-radius: 6upx;
		margin-right: 10upx;
		padding: 1upx 8upx;
		color: #fff;
		background-color: #f40
	}

</style>
