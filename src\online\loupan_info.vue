<template>
<view class="page " :class="(advInfo&&advInfo.advid > 0&&imconsu == 1)?'news_page':''">
		<view class="article">
            <view class="content">
				<!-- #ifdef H5 -->
				<view class="article-content" v-html="detail.content"></view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<!-- <u-parse :content="detail.content" @navigate="navigate"></u-parse> -->
				<u-parse :html="detail.content" @linkpress="navigate" :tag-style="tagStyle"></u-parse>
				<!-- #endif -->
            </view>
		</view>
</view>
</template>

<script>
import uParse from '../components/Parser/index'
// import {drawCard,showModal} from "../../common/index.js"
import {wxShare} from '../common/mixin'
export default {
    data(){
        return{
            id:"",
            title:"",
            detail:'',
            share:{},
            tagStyle:{
                video:'max-width:100%'
            },
        }
    },
    mixins: [wxShare],
    components:{uParse},
    onLoad(options){
        if(options.id){
				this.id = options.id
				this.getData(options.id)
			}else{
				uni.showToast({
					title:"没有此楼盘",
					icon:"none"
				})
				return
			}
    },
    methods:{
			getData(id){
				uni.showLoading({
					title:"加载中...",
					mask:true
				})
				this.$ajax.get("build/buildContent.html",{id:id,},(res)=>{
                    if (res.data.share&&res.data.share.title){
                        this.share= res.data.share
                        this.getWxConfig()
                    }
					if(res.data.code==1){
                        uni.hideLoading();
                        this.detail=res.data.build
                        if(res.data.build.title){
                            uni.setNavigationBarTitle({
                                title: res.data.build.title+'楼盘简介'
                            })  
                        }
                        
                    }
                })
            }
    },
    onShareAppMessage(res) {
        return {
            title:this.share.title||"",
            content:this.share.content||'',
            path:"/online/loupan_info/info?id="+this.id
        }
    }        





}
</script>

<style lang="scss">
.page{
		// min-height: 100vh;
		padding-bottom: 90upx;
	}
.content{
    background-color: #fff;
    padding: $uni-spacing-col-base $uni-spacing-row-base
}

</style>