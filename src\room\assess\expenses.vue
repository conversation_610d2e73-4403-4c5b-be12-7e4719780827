<template>
    <view class="page">
        <view class="page_top">
            <view class="page_center">
                <text @click="handleSecond()" :class="{ 'active': flag == 0 }">二手房</text>
                <text @click="handleNew()" :class="{ 'active': flag == 1 }">新房</text>
            </view>
            <view :class="{ 'page_bottom': flag == 0, 'page_bottomtwo': flag == 1 }">

            </view>
        </view>
        <view class="search_top" v-show="flag == 0">
            <view class="search-box">
                <view class="bold">房屋面积</view>
                <view class="text-a">
                    <view>130</view>
                    <view>平米</view>
                </view>
            </view>
            <view class="search-box">
                <view class="bold">房屋总价</view>
                <view class="text-a">
                    <view>140</view>
                    <view>平米</view>
                </view>
            </view>
            <view class="search-box">
                <view class="bold">房屋原价</view>
                <view class="text-a">
                    <view>140</view>
                    <view>平米</view>
                </view>
            </view>
            <view class="search-box">
                <view class="bold">计征方式</view>
                <view class="text-b">
                    <view>按差价计算</view>
                    <uni-icons type="forward" size="10" color="#979797"></uni-icons>
                </view>
            </view>
            <view class="search-box">
                <view class="bold">房屋性质</view>
                <view class="text-b">
                    <view>普通住宅</view>
                    <uni-icons type="forward" size="10" color="#979797"></uni-icons>
                </view>
            </view>
            <view class="search-box">
                <view class="bold">房产购置年限</view>
                <view class="text-b">
                    <view>满2年</view>
                    <uni-icons type="forward" size="10" color="#979797"></uni-icons>
                </view>
            </view>
            <view class="search-box">
                <view class="bold">买房首次购房</view>
                <view class="icon-box" style="margin-right: 12rpx;">
                    <view class="icon-small-wrp">
                        <icon class="icon-small" type="success" size="20"></icon>
                    </view>
                </view>
            </view>
            <view class="search-box">
                <view class="bold">计征方式</view>
                <view class="icon-box" style="margin-right: 12rpx;">
                    <view class="icon-small-wrp">
                        <icon class="icon-small" type="circle" size="20"></icon>
                    </view>
                </view>
            </view>
            <view class="search-box">
                <view class="bold">计征方式</view>
                <input type="text" confirm-type="search" @confirm="handleSearch()" />
                <uni-icons type="forward" size="10" color="#979797"></uni-icons>
            </view>
            <view class="btn">
                <button type="primary" @click="handelaTap()">开始计算</button>
            </view>
            <view style="margin-left: 100rpx;margin-top: 36rpx;font-size: 28rpx;color: #979797;">
                此结果仅供参考，实际费用以当地缴费为准
            </view>
            <view class="echart">
                <view>
                    <view id="container" class="cust" style="height: 400rpx;width: 400rpx;"></view>
                </view>
            </view>
            <view class="footer-bottom">
                <view class="footer-left">
                    <text>房屋总价：</text>
                    <text>140.0万</text>
                </view>
                <view class="footer-radious">
                    <text style="background-color: #5CCDCF;"></text>
                    <text>契税：</text>
                    <text>21000.0元</text>
                </view>
                <view class="footer-one">
                    <text style="background-color: #FFEC3E;"></text>
                    <text>个人所得税：</text>
                    <text>14000.0元</text>
                </view>
                <view class="footer-one">
                    <text style="background-color: #90BFFC;"></text>
                    <text>综合地价税：</text>
                    <text>14000.0元</text>
                </view>
                <view class="footer-one">
                    <text style="background-color: #FFAD61;"></text>
                    <text>工本印花税：</text>
                    <text>5元</text>
                </view>
                <view class="footer-two">
                    <text style="background-color: #41EB87;"></text>
                    <text>增值税：</text>
                    <text>0元</text>
                </view>
                <view class="footer-left">
                    <text>税费合计：</text>
                    <text>49005.0元</text>
                </view>
            </view>
        </view>
        <view class="search_top" v-show="flag == 1">
            <view class="search-box">
                <view class="bold">房屋面积</view>
                <view class="text-a">
                    <view style="color: #979797;">请输入房屋面积</view>
                    <view>平米</view>
                </view>
            </view>
            <view class="search-box">
                <view class="bold">房屋单价</view>
                <view class="text-a">
                    <view style="color: #979797;">请输入房屋单价</view>
                    <view>元/平米</view>
                </view>
            </view>
            <view class="search-box">
                <view class="bold">房屋性质</view>
                <view class="text-b">
                    <view>普通住宅</view>
                    <uni-icons type="forward" size="10" color="#979797"></uni-icons>
                </view>
            </view>
            <view class="search-box">
                <view class="bold">首次购房</view>
                <view class="icon-box" style="margin-right: 12rpx;">
                    <view class="icon-small-wrp">
                        <icon class="icon-small" type="success" size="20"></icon>
                    </view>
                </view>
            </view>
            <view class="btn">
                <button type="primary" @click="handelaTap()">开始计算</button>
            </view>
            <view style="margin-left: 100rpx;margin-top: 36rpx;font-size: 28rpx;color: #979797;">
                此结果仅供参考，实际费用以当地缴费为准
            </view>
        </view>

    </view>
</template>

<script>
// import {circle} from "../assess/compoents/circle.vue"
import * as echarts from 'echarts';
import { uniIcons } from "@dcloudio/uni-ui";
import myIcon from "@/components/myIcon";
export default {
    data() {
        return {
            flag: 0,
            retive: true,
        }

    },
    components: {
        myIcon,
        uniIcons,
        // circle,
    },
    methods: {
        // 二手房
        handleSecond() {
            this.flag = 0
        },
        // 新房
        handleNew() {
            this.flag = 1
        },
        // echart图表调用
        getechart() {
            var dom = document.getElementById('container');
            var myChart = echarts.init(dom, null, {
                renderer: 'canvas',
                useDirtyRect: false
            });
            var app = {};

            var option;

            option = {
                //   title: {
                //     text: 'Referer of a Website',
                //     subtext: 'Fake Data',
                //     left: 'center'
                //   },
                //   tooltip: {
                //     trigger: 'item'
                //   },
                //   legend: {
                //     orient: 'vertical',
                //     left: 'left'
                //   },
                series: [
                    {
                        //   name: 'Access From',
                        type: 'pie',
                        radius: '50%',
                        color: ["#5ccdcf", "#ffad61", "#90bffc", "#ffec3e"],
                        // grid:{
                        //     top:102,
                        //     left:"35%",
                        //     width:200
                        // },
                        data: [
                            { value: 1200, },
                            { value: 745, },
                            { value: 590, },
                            { value: 484, },
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        startAngle: 150,
                        labelLine: {
                            //
                            show: false
                        }
                    }
                ]
            };

            if (option && typeof option === 'object') {
                myChart.setOption(option);
            }

            window.addEventListener('resize', myChart.resize);
        },
        //
        // 交易服务跳转
        handelaTap(){
            this.$navigateTo('../assess/service')
        }
    },
    created() {

    },
    mounted() {
        this.getechart()
        // var myChart = echarts.init(dom, null, {
        //     renderer: 'canvas',
        //     useDirtyRect: false
        // });
        // var app = {};

        // var option;

        // option = {
        //     //   title: {
        //     //     text: 'Referer of a Website',
        //     //     subtext: 'Fake Data',
        //     //     left: 'center'
        //     //   },
        //     //   tooltip: {
        //     //     trigger: 'item'
        //     //   },
        //     //   legend: {
        //     //     orient: 'vertical',
        //     //     left: 'left'
        //     //   },
        //     series: [
        //         {
        //             //   name: 'Access From',
        //             type: 'pie',
        //             radius: '50%',
        //             color:["#5ccdcf","#ffad61","#90bffc","#ffec3e"],
        //             // grid:{
        //             //     top:102,
        //             //     left:"35%",
        //             //     width:200
        //             // },
        //             data: [
        //                 { value: 1200, },
        //                 { value: 745, },
        //                 { value: 590, },
        //                 { value: 484, },
        //             ],
        //             emphasis: {
        //                 itemStyle: {
        //                     shadowBlur: 10,
        //                     shadowOffsetX: 0,
        //                     shadowColor: 'rgba(0, 0, 0, 0.5)'
        //                 }
        //             },
        //             startAngle:150,
        //             labelLine: {
        //                 //
        //                 show: false
        //             }
        //         }
        //     ]
        // };

        // if (option && typeof option === 'object') {
        //     myChart.setOption(option);
        // }

        // window.addEventListener('resize', myChart.resize);
    }
}
</script>

<style scoped lang="scss">
body {
    background-color: white !important;
}

.cust {
    position: absolute;
    top: -80rpx;
    right: -80rpx;
}

.page {
    overflow: hidden;
    min-height: calc(100vh - 90rpx);
    background: white;
    border-top: 1rpx solid rgba(240, 240, 240, 1);

    .btn uni-button {
        background-color: #446EEF !important;
        width: 654rpx !important;
        height: 96rpx;
    }

    .btn {
        margin-top: 88rpx;
    }

    .page_top {
        width: 100%;
        height: 116rpx;
        font-size: 32rpx;
        // line-height: 116rpx;
        border-bottom: 1rpx solid rgba(240, 240, 240, 1);
        ;
    }

    .page_center {
        padding: 36rpx 0 0 0;
        box-sizing: border-box;
    }

    .page_center text:nth-child(1) {
        margin-left: 214rpx;
        color: #979797;
    }

    .page_center text:nth-child(2) {
        margin-left: 130rpx;
        color: #979797;
    }

    .page_bottom {
        background-color: #446eef;
        width: 60rpx;
        height: 8rpx;
        border-radius: 4px;
        margin-top: 10rpx;
        margin-left: 232rpx;
    }

    .page_bottomtwo {
        background-color: #446eef;
        width: 60rpx;
        height: 8rpx;
        border-radius: 4px;
        margin-top: 10rpx;
        margin-left: 442rpx;
    }

    .active {
        color: #232323 !important;
        font-weight: bold !important;
    }
}

.search-box {
    width: 654rpx;
    height: 116rpx;
    // background: red;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid rgba(240, 240, 240, 1);

    .text-a {
        display: flex;
        justify-content: space-between;
        align-items: center;

        view:nth-child(1) {
            font-size: 32rpx;
            color: #3a3a3a;
            margin-right: 20rpx;
        }

        view:nth-child(2) {
            color: #979797;
            font-size: 28rpx;
        }
    }

    .text-b {
        display: flex;
        justify-content: space-between;
        align-items: center;

        view:nth-child(1) {
            font-size: 32rpx;
            color: #3a3a3a;
            margin-right: 12rpx;
        }

        view:nth-child(2) {
            color: #979797;
            font-size: 28rpx;
        }

    }

    .one {
        font-size: 32rpx;
        color: #3a3a3a;
    }

    .two {
        font-size: 28rpx;
        color: #979797;
    }
}

view:nth-child(1) {
    color: #232323;
    font-size: 32rpx;
}

.uni-icons {
    color: #979797;
    width: 24rpx;
    height: 24rpx;
}

.bold {
    font-weight: bold;
}

.search-box-title .uni-input-form {
    width: 10rpx !important;
}

.search-box-title .uni-input-input {
    width: 10rpx !important;
}

.echart {
    width: 240rpx;
    height: 240rpx;
    background-color: #f3f3f3;
    margin: 0 auto;
    margin-top: 48rpx;
    border-radius: 50%;
    position: relative;
}

.footer-bottom {
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin-top: 20rpx;
}

.footer-left {
    margin-top: 28rpx;

    text:nth-child(1) {
        color: #979797;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
        margin-right: 20rpx;
        margin-left: 236rpx;
    }

    text:nth-child(2) {
        color: #3A3A3A;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
    }
}

.footer-radious {
    margin-top: 28rpx;

    text:nth-child(1) {
        width: 18rpx;
        height: 18rpx;
        display: inline-block;
        border-radius: 50%;
        margin-left: 256rpx;
        margin-right: 18rpx;
    }

    text:nth-child(2) {
        color: #979797;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
        margin-right: 20rpx;
    }
}

.footer-one {
    margin-top: 28rpx;
    text:nth-child(1) {
        margin-left: 178rpx;
        width: 18rpx;
        height: 18rpx;
        border-radius: 50%;
        display: inline-block;
        margin-right: 12rpx;
    }

    text:nth-child(2) {
        color: #979797;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
        margin-right: 20rpx;
    }

    text:nth-child(3) {
        color: #3A3A3A;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
    }
}

.footer-two {
    margin-top: 28rpx;

    text:nth-child(1) {
        margin-left: 236rpx;
        width: 18rpx;
        height: 18rpx;
        border-radius: 50%;
        display: inline-block;
        margin-right: 12rpx;
    }
    text:nth-child(2){
        margin-right: 20rpx;
        color: #979797;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
    }
    text:nth-child(3) {
        color: #3A3A3A;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 28rpx;
    }
}</style>