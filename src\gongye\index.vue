<template>
  <view class="gongye-page">
    <!-- 轮播图 -->
    <view class="swiper-container" v-if="advs.length > 0">
      <my-swiper 
        :focus="advs" 
        :autoplay="true" 
        :interval="4000" 
        :indicatorDots="true" 
        :circular="true" 
        indicatorActiveColor="#f65354" 
        height="50vw"
        :rounded="false">
      </my-swiper>
    </view>
    
    <!-- TAB切换 -->
    <view class="tab-container">
      <tab-bar 
        class="tab" 
        :tabs="tabList"
        :bgColor="'#fff'"
        :fixedTop="false"
        :showLine="true"
        :nowIndex="tabId"
        :equispaced="false"
        :maxNum="4"
        @click="switchTab">
      </tab-bar>
    </view>
    
    <!-- 数据列表 -->
    <view class="list">
      <view class="detail" v-for="(item, index) in list" :key="index" @click="toParkDetail(item.id)">
        <view class="top">
          <image class="pic" :src="item.cover_image | imageFilter" mode="aspectFill"></image>
        </view>
        <view class="info">
          <view class="tit_info flex-row">
            <text class="tit">{{ item.title }}</text>
            <text class="area">{{ item.areaname }}</text>
          </view>
          <!-- 载体资源 -->
          <view class="info-row" v-if="item.carrier">
            <text class="info-label">载体资源：</text>
            <text class="info-content">{{ item.carrier }}</text>
          </view>
          <!-- 产业集群 -->
          <view class="info-row" v-if="item.cluster">
            <text class="info-label">产业集群：</text>
            <text class="info-content">{{ item.cluster }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
  </view>
</template>

<script>
import mySwiper from "@/components/mySwiper.vue";
import tabBar from "@/components/tabBar.vue";
import { uniLoadMore } from '@dcloudio/uni-ui';

export default {
  name: 'gongye',
  components: {
    mySwiper,
    tabBar,
    uniLoadMore,
  },
  data() {
    return {
      advs: [], // 轮播图数据
      cates: [], // 分类数据
      tabList: [], // TAB列表数据
      tabId: 0, // 当前选中的TAB
      list: [], // 园区列表数据
      param: {
        page: 1,
        rows: 10,
        cate_id: 0, // 分类ID，0表示全部
      },
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了"
      },
      share: {
        title: '',
        content: '',
        pic: '',
        link: '',
        link2: ''
      }
    }
  },
  onLoad(options) {
    console.log(options);
    this.getInitData();
  },
  onReachBottom() {
    if (this.get_status === "noMore") {
      return;
    }
    this.param.page++;
    this.getParkList();
  },
  methods: {
    // 获取初始数据（轮播图和分类）
    getInitData() {
      console.log("开始请求初始数据---------------------");
      this.$ajax.get('park/index', {}, (res) => {
        console.log(res);
        if (res.data.code == 1) {
          // 处理轮播图数据
          this.advs = (res.data.advs || []).map(item => ({
            ...item,
            image: item.path // mySwiper组件需要image字段
          }));
          
          // 处理分类数据，在前面添加"产业园区"
          this.cates = res.data.cates || [];
          this.tabList = [
            { id: 0, name: "产业园区" },
            ...this.cates.map(item => ({
              id: item.id,
              name: item.cate_name
            }))
          ];
          
          // 处理分享数据
          if (res.data.forward) {
            this.share = {
              title: res.data.forward.title || '产业园区',
              content: res.data.forward.desc || '产业园区推荐',
              pic: res.data.forward.pic || (this.advs.length > 0 ? this.advs[0].path : ''),
              link: res.data.forward.link || '',
              link2: res.data.forward.link2 || ''
            };
          } else {
            // 如果没有forward数据，使用默认数据
            this.share = {
              title: '产业园区',
              content: '产业园区推荐',
              pic: this.advs.length > 0 ? this.advs[0].path : '',
              link: '',
              link2: ''
            };
          }

          // #ifdef H5
             this.initWxShare();
          // #endif
          
          // 获取园区列表数据
          this.getParkList();
        }
      });
    },
    
    // 获取园区列表数据
    getParkList() {
      if (this.param.page === 1) {
        this.list = [];
      }
      this.get_status = "loading";
      
      this.$ajax.get('park/parks', this.param, (res) => {
        console.log(res)
        if (res.data.code == 1) {
          this.list = this.list.concat(res.data.list);
          
          if (res.data.list.length < this.param.rows) {
            this.get_status = "noMore";
          } else {
            this.get_status = "more";
          }
        } else {
          this.get_status = "noMore";
        }
      });
    },
    
    // 切换TAB
    switchTab(tabInfo) {
      this.tabId = tabInfo.index;
      this.param.cate_id = tabInfo.id;
      this.param.page = 1;
      this.getParkList();
    },
    
    // 跳转到园区详情
    toParkDetail(id) {
      this.$navigateTo('/gongye/detail?id=' + id);
    },
    
    // #ifdef H5
    // 初始化微信分享配置
    initWxShare() {
      // 使用项目标准的微信分享方式
      this.getWxConfig();
    }
    // #endif
  },
  
  // 小程序分享给朋友
  onShareAppMessage() {
    return {
      title: this.share.title || '产业园区',
      path: '/gongye/index',
      imageUrl: this.share.pic || (this.advs.length > 0 ? this.advs[0].path : '')
    }
  },
  
  // 小程序分享到朋友圈
  onShareTimeline() {
    return {
      title: this.share.title || '产业园区',
      query: '',
      imageUrl: this.share.pic || (this.advs.length > 0 ? this.advs[0].path : '')
    }
  }
}
</script>

<style lang="scss" scoped>
.gongye-page {
  background: #f5f5f5;
  min-height: 100vh;
  
  /* .swiper-container {
    margin-bottom: 20rpx;
  } */
  
  .tab-container {
    background: #fff;
    /* margin-bottom: 20rpx; */
    
    .tab {
      padding: 0 24rpx;
    }
  }
  
  .flex-row {
    display: flex;
  }
  
  .list {
    margin: 0 24rpx;
    
    .detail {
      width: 100%;
      margin-bottom: 30rpx;
      background: #fff;
      border-radius: 20rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
      
      .top {
        position: relative;
        height: 360rpx;
        
        .pic {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .info {
        padding: 24rpx;
        
        .tit_info {
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24rpx;
          
          .tit {
            color: #333;
            font-size: 34rpx;
            font-weight: 600;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .area {
            color: #666;
            font-size: 26rpx;
            background: #f5f5f5;
            padding: 6rpx 16rpx;
            border-radius: 24rpx;
          }
        }
        
        .info-row {
          display: flex;
          margin-bottom: 16rpx;
          line-height: 1.5;
          
          .info-label {
            color: #666;
            font-size: 26rpx;
            flex-shrink: 0;
            font-weight: 500;
          }
          
          .info-content {
            color: #333;
            font-size: 26rpx;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .gongye-page {
    .list-container {
      .park-item {
        .park-info {
          padding: 24rpx;
          
          .park-title {
            font-size: 32rpx;
          }
          
          .park-meta,
          .park-cluster,
          .park-carrier {
            font-size: 26rpx;
          }
        }
      }
    }
  }
}
</style>