<template>
<view class="list_page">
    <view class="online_list">
        <view class="online_item" v-for="item in online_list" :key="item.id" @click="toDetail(item.id)">
            <view class="img-box">
                <image mode="aspectFill" :src="item.pic | imgUrl('w_6401')" :lazy-load="true"></image>
            </view>
            <view class="info">
                <view class="name">{{item.name||item.business_name}}</view>
                <view class="time">{{item.is_start==1?'距离结束时间：':(item.is_start==0?'距离开始时间：':'已结束')}}<text v-if="item.is_start!=2" class="day">{{item | timeFormat(item.is_start)}}</text></view>
                <view class="house_num"><text class="num">{{item.total_count}}</text>套房源</view>
            </view>
        </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <chat-tip></chat-tip>
</view>
</template>

<script>
import myIcon from '../components/icon'
import {uniLoadMore} from '@dcloudio/uni-ui'
import {navigateTo,formatImg} from '../common/index.js'
import {wxShare} from '../common/mixin'
export default {
    data() {
        return {
            online_list:[],
            get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
            params:{
                page:1,
                rows:10
            }
        }
    },
    mixins: [wxShare],
    components: {
        myIcon,
        uniLoadMore
    },
    onLoad(){
        this.$store.state.online_inviter = {}
        this.getData()
    },
    filters:{
        imgUrl(val, param = "") {
            return formatImg(val, param)
        },
        timeFormat(val,param){
            let nowTime = Date.parse(new Date())/1000

            let day = ""
            let hour = ""
            let minutes = ""
            if(param == 1 ){ //进行中
                day = parseInt((val.end_time-nowTime)/86400)
                hour = parseInt((val.end_time-nowTime)%86400/3600)
                minutes = parseInt((val.end_time-nowTime)%86400%3600/60)
            }
            if(param == 0 ){ //未开始
                day = parseInt((val.start_time-nowTime)/86400)
                hour = parseInt((val.start_time-nowTime)%86400/3600)
                minutes = parseInt((val.start_time-nowTime)%86400%3600/60)
            }
            if(day||hour){
                return `${day}天${hour}时`
            }else{
                return `${hour}时${minutes}分`
            }
            
        }
    },
    methods: {
        getData(){
            if(this.params.page == 1){
                this.online_list=[]
                this.share = {}
            }
            this.get_status = "loading"
            this.$ajax.get('online/onlines',this.params,res=>{
                if(res.data.share&&!this.is_setshare){
                    this.share = res.data.share
                    this.is_setshare = true
                    this.getWxConfig()
                }
                if(res.data.code === 1){
                    this.online_list = this.online_list.concat(res.data.list)
                    if(res.data.list.length<this.params.rows){
                        this.get_status = "noMore"
                    }else{
                        this.get_status = "more"
                    }
                }else{
                    this.get_status = "noMore"
                }
            },err=>{
                console.log(err)
            })
        },
        toDetail(id){
            navigateTo(`/online/detail?id=${id}`)
        }
    },
    onReachBottom(){
        this.params.page++
        this.getData()
    },
}
</script>

<style scoped lang="scss">
.online_list{
    .online_item{
        margin: 0 30rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 0 20rpx #dedede;
        border-radius: 8rpx;
        overflow: hidden;
        background-color: #fff;
        .img-box{
            width: 100%;
            height: 50vw;
            // padding: 1rpx;
        //     border-radius: 6rpx;
        //    overflow: hidden;
            box-sizing: border-box;
            image{
                width: 100%;
                height: 100%;
            }
        }
        .info{
            padding: 20rpx;
            padding-bottom: 35rpx;
            .name{
                margin-bottom: 10rpx;
                font-size: 36rpx;
                font-weight: bold;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                color: #333;
            }
            .time{
                margin: 10rpx 0;
                font-size: 28rpx;
                color: #666;
                .day{
                    font-size: 26rpx;
                    padding: 0 10rpx;
                    border-radius: 5rpx;
                    background-color: $uni-color-primary;
                    color: #fff
                }
            }
            .house_num{
                .num{
                    margin-right: 5rpx;
                    color: $uni-color-primary;
                }
                font-size: 26rpx;
                color: #666
            }
        }
    }
}

</style>
