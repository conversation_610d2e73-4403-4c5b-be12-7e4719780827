<template>
  <view class="news" :class="{has_bg_color}">
    <view class="title">最新动态</view>
    <view class="news_list">
      <view class="box" v-for="news in list" :key="news.id" @click="toDetail(news)">
        <image class="box-img" :src="news.img | imageFilter('w_240')" mode="aspectFill"></image>
        <view class="box-right">
          <view class="right-title">{{news.title}}</view>
          <view class="right-time">{{news.create_time}}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'exNews',
  props:{
    has_bg_color: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: ()=>[]
    }
  },
  data () {
    return {}
  },
  methods: {
    toDetail(news){
      if(news.id){
        this.$navigateTo('/pages/news/detail?id='+news.id)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.news {
  padding: 48rpx 48rpx 0 48rpx;
  &.has_bg_color{
    .title{
      color: #fff;
    }
    .news_list{
      border: none;
    }
  }
  .title {
    font-size: 40rpx;
    font-weight: bold;
    color: #353535;
  }
  .news_list {
    margin-top: 24rpx;
    border: 1rpx solid #D8D8D8;
    border-radius: 16rpx;
    background-color: #fff;
    .box {
      padding: 24rpx;
      display: flex;
      height: 152rpx;
      .box-img {
        width: 200rpx;
        height: 100%;
        flex-shrink: 0;
      }
      .box-right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 24rpx;
        .right-title {
          font-size: 32rpx;
          font-weight: bold;
          color: #353535;
          text-overflow: -o-ellipsis-lastline;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          word-break: break-all;
        }
        .right-time {
          font-size: 22rpx;
          color: #999999;
        }
      }
    }
  }
}
</style>