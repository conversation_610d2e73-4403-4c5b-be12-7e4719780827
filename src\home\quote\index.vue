<template>
<view class="page">
    <view class="head">
        <image v-if="bgImg" class="img" :src="bgImg | imgUrl" mode='widthFix'></image>
    </view>
    <view class="row text-center bottom-line">目前已为<text class="highlight">{{count}}</text>位客户预约</view>
    <my-input label="手机号码" maxlength="11" type="number" placeholder="设计师怎么联系您呢？" name="tel" @input="handleInput"></my-input>
    <my-input label="您的称呼" maxlength="10" type="text" placeholder="Hi-您怎么称呼？" name="name" @input="handleInput"></my-input>
    <my-input label="您的小区" type="text" placeholder="您的小区？" name="cname" @input="handleInput"></my-input>
    <view class="btn-box">
        <button class="default" hover-class="btn-hover" @click="subData">免费获取三份报价</button>
    </view>
</view>
</template>

<script>
import myInput from '../../components/form/myInput'
import {formatImg} from '../../common/index'
import {wxShare} from '../../common/mixin'
export default {
    data() {
        return {
            bgImg:"",
            count:0,
            content:"",
            params: {}
        }
    },
    mixins:[wxShare],
    components: {
        myInput
    },
    onLoad(options) {
        this.params.type = options.type || 2; //默认1; 1->申请设计 2->报价
        this.params.from = options.from || 6; //默认1; 1->设计 6->其他
        this.params.cid = options.cname || ''; //小区名
        this.getData()
    },
    filters:{
        imgUrl(val){
            return formatImg(val)
        }
    },
    methods: {
        handleInput(e) {
            this.params[e._name] = e.detail.value
        },
        getData(){
            this.$ajax.get('memberShop/design',{type:2},res=>{
                if(res.data.image){
                    this.bgImg = res.data.image
                }
                if(res.data.count){
                    this.count = res.data.count
                }
                if(res.data.share){
                    this.share = res.data.share
                    this.getWxConfig()
                }else{
                    this.share = {}
                }
            })
        },
        subData() {
            if (this.params.cname == '') {
                uni.showToast({
                    title: "请输入小区名",
                    icon: "none"
                })
                return
            }
            this.$ajax.get('memberShop/signUp', this.params, res => {
                if (res.data.code == 1) {
                    uni.showToast({
                        title: res.data.msg
                    })
                    this.sendMessage()
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 2000)
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            })
        },
        sendMessage(){
            this.$ajax.get('memberShop/sendAdminNotice.html',{},res=>{})
        },
    }
}
</script>

<style lang="scss">
.img {
    width: 100%;
    height: 0;
}

.head {
    font-size: 0
}

.highlight {
    font-size: 32upx;
    font-weight: 600;
    color: $uni-color-primary
}
</style>
