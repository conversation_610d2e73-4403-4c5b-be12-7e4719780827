.school_detail {
  padding-bottom: 150rpx;
  background-color: #fff;
}
.main-info {
  padding: 24rpx;
  align-items: center;
  justify-content: space-between;
  .logo {
    width: 90rpx;
    height: 90rpx;
    border-radius: 50%;
    margin-right: 30rpx;
  }
  .info {
    flex: 1;
    .name {
      font-size: 36rpx;
      line-height: 1.5;
      font-weight: bold;
      margin-bottom: 10rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
    }
    .address {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      font-size: 26rpx;
      color: #555555;
    }
  }
  .radiate {
    padding: 0 30rpx;
    font-size: $uni-font-size-sm;
    .text {
      margin-top: 10rpx;
      color: #666;
    }
  }
}

.views {
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  .pre_logos {
    .pre_logo {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      background-color: #f3f3f3;
      ~ .pre_logo {
        margin-left: -20rpx;
      }
    }
  }
  .view_num {
    font-size: $uni-font-size-sm;
    color: #666;
  }
}

.card {
  margin: 24rpx;
  border-radius: 10rpx;
  box-shadow: 0 0 10rpx #dedede;
  .title {
    position: relative;
    font-size: $uni-font-size-lg;
    padding-left: 10rpx;
    margin: 30rpx 24rpx 10rpx 24rpx;
    border-left: 4rpx solid $uni-color-primary;
    flex-direction: row;
    .btn {
      position: absolute;
      right: 0;
      top: 0;
      flex-direction: row;
      height: 52rpx;
      line-height: 52rpx;
      padding: 0 15rpx;
      border-radius: 22rpx;
      font-size: $uni-font-size-sm;
      background-color: #fef2ee;
      color: $uni-color-primary;
    }
    .tag {
      margin-left: 6rpx;
      height: 36rpx;
      line-height: 36rpx;
      padding: 0 26rpx;
      border-bottom-left-radius: 30rpx;
      border-top-right-radius: 30rpx;
      background-color: $uni-color-primary;
      color: #fff;
      font-size: 20rpx;
      position: relative;
      // transform: scale(0.9);
    }
    .tag::before {
      content: "";
      position: absolute;
      left: 8rpx;
      top: 8rpx;
      width: 10rpx;
      height: 10rpx;
      border-radius: 50%;
      background-color: #fff;
    }
  }
  .content {
    padding: 24rpx;
    background-color: #fff;
    .row {
      padding: 15rpx 0;
    }
    .label {
      line-height: 1.5;
      color: #666;
    }
    .value {
      flex: 1;
      flex-direction: row;
      align-items: center;
      white-space: pre-wrap;
      line-height: 1.5;
      .school_type {
        margin-right: 10rpx;
      }
      .tag {
        margin-right: 8rpx;
      }
    }
    .highlight {
      color: $uni-color-primary;
    }
  }
  .footer {
    padding: 24rpx;
    text-align: center;
    background-color: #fff8f6;
    color: $uni-color-primary;
  }
}

.news_list {
  .time {
    color: #666;
  }
}

.community_list {
  .school_count {
    flex-direction: row;
    align-items: flex-end;
    margin-bottom: 40rpx;
    font-size: 30rpx;
    color: $uni-color-primary;
    .text {
      margin-left: 10rpx;
    }
  }
}

.title-row {
  flex-direction: row;
  align-items: center;
  &.label {
    justify-content: space-between;
    padding: 24rpx;
  }
  .text {
    padding: 0 10rpx;
    font-size: 32rpx;
    border-left: 4rpx solid $uni-color-primary;
  }
  .more {
    flex-direction: row;
    align-items: center;
    color: #999999;
  }
  .cate {
    margin: 0 20rpx;
    padding: 10rpx 0;
    position: relative;
    &.current {
      color: #333;
    }
    &.current::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 10rpx;
      right: 10rpx;
      height: 4rpx;
      background-color: $uni-color-primary;
    }
  }
}

.house-list {
  .more {
    padding: 24rpx;
    margin: 30rpx;
    text-align: center;
    font-size: 24rpx;
    background-color: #f3f3f3;
  }
}
.fixed-bottom {
  width: 100%;
  padding: 10rpx 30rpx;
  position: fixed;
  bottom: 0;
  z-index: 999;
}
.house {
  flex-direction: row;
  padding: 24rpx 30rpx;
  .image {
    width: 210rpx;
    height: 146rpx;
    margin-right: 15rpx;
  }
  .info {
    flex: 1;
    overflow: hidden;
    .title {
      margin-bottom: 15rpx;
      font-size: $uni-font-size-base;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      color: #000;
    }
    .spec {
      flex-direction: row;
      margin-bottom: 10rpx;
      font-size: $uni-font-size-sm;
      color: #888;
    }
    .distance {
      margin-bottom: 10rpx;
      font-size: $uni-font-size-sm;
      color: #888;
    }
    .price {
      margin: 10rpx 0;
      font-size: $uni-font-size-lg;
      font-weight: bold;
      color: #f52a20;
    }
    .agent {
      flex-direction: row;
      margin-top: 10rpx;
      align-items: center;
      font-size: $uni-font-size-sm;
      color: #888;
      .prelogo {
        width: 30rpx;
        height: 30rpx;
        border-radius: 50%;
        margin-right: 10rpx;
      }
      .vertical {
        margin: 0 8rpx;
      }
    }
  }
}
