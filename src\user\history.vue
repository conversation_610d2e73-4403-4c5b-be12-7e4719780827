<template>
  <view class="history-list">
    <block v-for="(item, index) in history_list" :key="index">
      <newHouseItem v-if="item.info_type===1" :itemData="item" @click="$navigateTo(`/pages/new_house/detail?id=${item.id}`)"></newHouseItem>
      <houseItem v-if="item.info_type===2&&item.parentid===1" :itemData="item" @click="$navigateTo(`/pages/ershou/detail?id=${item.id}`)"></houseItem>
      <houseItem v-if="item.info_type===2&&item.parentid===2" :itemData="item" type="renting" @click="$navigateTo(`/pages/renting/detail?id=${item.id}`)"></houseItem>
    </block>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
  </view>
</template>

<script>
import houseItem from '../components/houseItem'
import newHouseItem from '../components/newHouseItem'
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
  components: {
    houseItem,
    newHouseItem,
    uniLoadMore
  },
  data () {
    return {
      get_status:"loading",
      content_text:{
        contentdown:"",
        contentrefresh:"正在加载...",
        contentnomore:"没有更多数据了"
      },
      history_list:[],
      params:{
        page:1,
        rows:20
      }
    }
  },
  onLoad(){
    this.getData()
  },
  methods: {
    getData(){
      this.get_status = "loading"
      this.$ajax.get('member/userBrowseHistory.html',this.params,res=>{
        if(res.data.code !== 1){
          this.get_status = "noMore"
          return
        }
        this.history_list = this.history_list.concat(res.data.list)
        if(res.data.list.length<this.params.rows){
          this.get_status = "noMore"
        }else{
          this.get_status = "more"
        }
      })
    }
  },
  onReachBottom(){
    if(this.get_status === 'more'){
      this.params.page++
      this.getData()
    }
  }
}
</script>

<style scoped lang="scss">
.history-list{
  padding: 0 48rpx;
  background-color: #fff;
}
</style>