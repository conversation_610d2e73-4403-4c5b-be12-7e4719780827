<template>
    <view class="liveList" v-show='showData'>
		<view class="search-btn">
				<view class="btns" v-for='search in searchList' :key="search.id" :class="{active:params.status==search.id}" @click="searchClick(search.id)">{{search.name}}
				</view>
				<view class="apply-btns flex-box" @click="$refs.joinLive.show()">
					<my-icon type="zhibo" color="#FB656A" size="40upx"></my-icon>
					<view class="apply">申请直播</view>
				</view>
			
		</view>
        <view class="live">
			<!-- <block > -->
				<view class="item flex-box" style="box-sizing: border-box;display:flex; flex:1;overflow: hidden;width: 100%; position: relative;padding: 12upx 24upx;margin-bottom: 20upx;" v-for="(item,index) in list" :key="index">
				<wx-open-launch-weapp :username="weapp_appid" id="launch-btn" :path='"/online/next.html?roomId="+item.room_id' style="width:100%;height:100%;position: absolute;z-index:10000;overflow:hidden">		
        		<script type="text/wxtag-template">

					<style>

					</style>
					<div style="width:100%;height:148px;opacity:0;">{{item.name}}</div>
					</script>
				</wx-open-launch-weapp>
				
					
						<view class="imgs">
							<image class="v-thumb" mode="widthFix"  :src="item.img_url|imgUrl('w_240')"></image>
							<image class="video-icon" mode="widthFix" src="/static/icon/video.png" ></image>
							<view class="see">
								<my-icon type="yanjing" color="#fff" size="10px"></my-icon>
								<span class="num" >{{item.viewer_num}}</span>
							</view>
							
							<view class="status huifang" v-if="item.status==103" >
								{{item.status|formatStatus}}
							</view>
							<view class="status" v-else >
								{{item.status|formatStatus}}
							</view>
						
						</view>
						
						<view class="v-ftinfo flex-box" >
							<view class="title flex-box">{{item.name}}</view>
							<view class="middle flex-box" >
								<my-icon type="zhibo" color="#B5A15B" size="14px"></my-icon>
								<view class="build-name" v-if="item.title" >{{item.title}}</view>
								<view class="live-time" >{{item.start_time}}</view>
							</view>
						</view>
					
        		
				</view>
			<!-- </block> -->
        </view>
		<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
		<my-popup ref="joinLive" position="center" height="800upx"  :touch_hide="true">
			<view class="live-bg">
				<view class="live-img">
				<image src="https://images.tengfangyun.com/images/new_icon/live-pop-bg.png?x-oss-process=style/w_240" ></image>
				<view class="live-pop-top live-pop-absu">
					<view class="site-name">{{siteName}}</view>
					<view class="site-tips">诚邀您加入直播</view>
				</view>
				<view class="live-pop-middle live-pop-absu">
					<view class="live-pop-con flex-box">
						<view class="flex-box live-pop-info">
							<my-icon type="T-xuanfanggonglve" size="80upx" color='#FC835E'></my-icon>
							<view class="info-name">选房攻略</view>
						</view>
						<view class="flex-box live-pop-info">
							<my-icon type="T-xinfangkaipan" size="80upx" color='#FC835E'></my-icon>
							<view class="info-name">新房开团</view>
						</view>
						<view class="flex-box live-pop-info">
							<my-icon type="T-huodongxianchang" size="80upx" color='#FC835E'></my-icon>
							<view class="info-name">活动现场</view>
						</view>
					</view>

				</view>
				<view class="live-pop-bottom live-pop-absu">
					<view class="live-pop-tel">
						咨询电话：{{siteTel}}
					</view>
					<view class="live-pop-btn flex-box" @click="makePhoneCall">
						立即咨询
					</view>
				</view>
			</view>
			</view> 
			

		</my-popup>
    </view>
</template>

<script>
import {navigateTo,formatImg} from "../common/index.js"
import myIcon from "@/components/myIcon"
import myPopup from '../components/myPopup.vue'
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
    data(){
        return {
			list:[],
			searchList:[
				{
					id:0,name:'不限',
				},
				{
					id:101,name:'正在直播',
					
				},
				{id:103,name:"精彩回放"},
			],
			showApplyLive:false,
			share:{},
			seo:{},
			params:{
				page:1,
				rows:10,
				status:0
			},
			get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
			},
			live_height:'initial',
			siteTel:'',
			siteName:'',
			showData:false,
        }
	},
	components:{
		myIcon,
		myPopup,
		uniLoadMore
	},
	filters:{
		formatStatus(val){
			// 101: 直播中, 102: 未开始, 103: 已结束, 104: 禁播, 105: 暂停中, 106: 异常，107：已过期 
			switch (val){
				case 101:
                    return "正在直播"
					break;
				case 102:
					return "尚未开始"
					break;
				case 103:
					return "精彩回放"
					break;
				case 104:
					return "已被禁播"
					break;
				case 105:
					return "正在暂停"
					break;
				case 106:
					return "出现异常"
					break;	
				case 107:
					return "已经过期"
					break;
				
				default:
					return ""
					break;
			}
		},
		imgUrl(val,param=""){
			return formatImg(val, param)
		},
	},
    onShow(){
        this.getLiveList()
	},
	onLoad(){
		this.getWxConfig()
	},
	computed: {
		weapp_appid(){
			return this.$store.state.weapp_appid
		},
	},	
    methods: {
		searchClick(id){
			if (this.params.status==id) return 
			this.params.status=id,
			this.params.page =1
			this.getLiveList()
		},
		handleErrorFn(e){
			uni.showToast({
				title:"1123,"+JSON.stringify(e)
			})
		},
		handleLaunchFn(e){
			console.log('success');
			uni.showToast({
				title:JSON.stringify(e)
			})
		},

        getLiveList(){
			this.get_status = "loading"
			if(this.params.page ==1){ this.list =[]}
			this.$ajax.get("LiveVideo/index.html",this.params,(res)=>{
				if (res.data.share){
						this.share = res.data.share
					}
					this.getWxConfig()
				uni.setNavigationBarTitle({
					title:res.data.site_name?(res.data.site_name+"直播列表"):"直播列表"
				})	
				if (res.data.code == 1){
				
					this.list =this.list.concat(res.data.list)
					console.log(this.list);
					this.siteName=res.data.site_name
					this.siteTel=res.data.site_tel
					if(res.data.list.length<this.params.rows){
                        this.get_status = "noMore"
                    }else{
                        this.get_status = "more"
                    }
				}else {
					this.get_status = "noMore"
				}
					this.showData=true
			},(err)=>{
				this.showData=true
			})
            
		},
		 // 统计拨打电话
        statistics() {
            // this.$ajax.get(
            //     'im/callUpStatistics',
            //     {
            //     id: '',
            //     tel: this.siteTel,
            //     type: 18
            //     },
            //     res => {
            //     console.log(res)
            //     }, { disableAutoHandle: true }
            // )
        },
		makePhoneCall(){
			uni.makePhoneCall({
				phoneNumber:this.siteTel
			})
			this.statistics()
		},
		goVideoPlay(id){
			this.$navigateTo("/online/next?roomId="+id)
			// if (status == 103 ){
			// 	navigateTo("/online/videos?roomId="+id+"&roomName="+name)
			// 	return
			// }
			// navigateTo("plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id="+id)
		},   
	},
	onReachBottom(){
		if ( this.get_status == "more"){
			this.params.page++
			this.getLiveList()
		}
	
    },
	onShareAppMessage() {
		return {
			title:this.share.title||"",
            content:this.share.content||"",
            imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):""
		}
	}
}
</script>

<style scoped>
	.search-btn {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: 25upx 24upx 13upx;
		background: #fff;
	} 
	.search-btn .btns{
		padding:8upx 16upx;
		margin-left: 25upx;
		background: #F5F5F5;
		border: 2upx solid #f5f5f5;
		color: #999999;
		border-radius: 4upx;
		
	}
	/* .search-btn .btns:last-child{
		margin-left: auto;
	} */

	.search-btn .btns.active{
		background-color: rgba(255, 101, 107, 0.1);
		color: #ff656b;
		border: 2upx solid #ff656b;
	}
	.search-btn .apply-btns{
		margin-left: auto;
		flex-direction: column;
		/* justify-content: center; */
		align-items: center;
		font-size: 22upx;
	}
	.search-btn .apply-btns .apply{
		font-size: 20upx;
		color: #333;
	}

    .liveList{
		width: 100%;
	}
    .live {width: 100%;padding-bottom: 30upx; display: flex;flex-direction: column;}
    .live .item {
		box-sizing: border-box;
		overflow: hidden;
		width: 100%; 
		position: relative;
		padding: 12upx 24upx;
		background: #fff;
		margin-bottom: 20upx;
		flex-direction: column;

	}

	.live .item .see{
		position: absolute;
		left: 12upx;
		bottom: 7upx;
		background:rgba(0,0,0,0.4);
		padding: 10upx 20upx;
		border-radius: 40upx;
		color: #fff;
		font-size: 20upx;
	}
	.live .item .see .num{
		margin-left: 10upx;
	}
	.live .item .v-ftinfo .live-time{
		font-size: 24upx;
	}
	.item .imgs .status{
		position: absolute;
		left: 12upx;
		top: 12upx;
		color: #fff;
		text-align: center;
        min-width: 140upx;
        font-size: 25upx;
        padding: 10upx 20upx;
		border-radius: 30upx;
		background-image: linear-gradient(to right,rgba(251, 171, 101, 1),rgba(251, 101, 105, 1));
	}
	.item .imgs .status.huifang{
		background-image: linear-gradient(to right,rgba(159, 170, 218, 1),rgba(122, 151, 231, 1));
	}
    .live .item .v-thumb {
		/* height: 296upx; */
		overflow: hidden;
		width: 100%;
		height: 100%;
		/* object-fit: cover; */
	}
    .imgs image.video-icon {
        width: 16vw;
        height: 0;
        position: absolute!important;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0);
    }
    .imgs {
        position: relative;
		height: 296upx;
		background: #f3f3f3;
		overflow: hidden;
    }
	.imgs image{
		width: 100%;
	}
    .live .item .v-ftinfo {
		padding: 20upx;
		width: 100%;
		box-sizing: border-box;
    	flex-direction: column;
	}
    .live .item .v-ftinfo .title {
		color: #333;
		overflow: hidden;
		margin: 20upx 0;
		font-size: 40upx;
		font-weight: 800;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
	}
	.live-bg{
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		
	}
	.live-bg image{
		height: 100%;
		width: 100%;
	}
	.live-pop-absu{
		position: absolute;
		width: 100%;

	}
	.live-pop-top{
		top: 0;
		height: 37%;
	}
	.live-pop-top .site-name{
		text-align: center;
		margin-top: 50upx;
		font-size: 48upx;
		color: #fff;
	}
	.live-pop-top .site-tips{
		margin-top: 40upx;
		text-align: center;
		color: #fff;
		font-size: 36upx;
	}
	.live-pop-middle{
		top: 40%;
	}
	.live-pop-middle .live-pop-con{
		justify-content: space-between;
		align-items: center;
		padding: 40upx 48upx;
		font-size: 28upx;
		color: #666;
	}
	.live-pop-middle .live-pop-con .live-pop-info{
		flex-direction: column;
		align-items: center;
		
	}
	.live-pop-middle .live-pop-con .live-pop-info .info-name{
		margin-top: 24upx;
	}
	.live-pop-bottom{
		bottom: 52upx;
		padding: 0 48upx;
	}
	.live-pop-bottom .live-pop-btn {
		width: calc(100% - 96upx);
		background: #FB656A;
		border-radius: 40upx;
		justify-content: center;
		align-items: center;
		padding: 20upx 0;
		color: #fff;
		font-size: 28upx;
		margin-top: 28upx;
	}
	.live-img{
		height: 100%;
		width: 80%;
		margin: 0 auto;
		position: relative;
		box-sizing: border-box;
	}

   
	.live .item .v-ftinfo .middle{
		justify-content: space-between;
		align-items: center;
		flex: 1;
		color: #999;
		/* width: 100%; */
	}
	.live .item .v-ftinfo .middle .head-img{
		width: 60upx;
		height: 60upx;
		overflow: hidden;
		border-radius: 50%;
		margin-right: 20upx;
	}
	.live .item .v-ftinfo .middle .head-img image{
		width: 100%;
	}
	.live .item .v-ftinfo .middle .build-name{
		flex: 1;
		color: #333;
		margin-left: 20upx;
		font-size: 22upx;
	} 
	.live .item .v-ftinfo .build-info{
		margin-top: 22upx;
	}
	.live .item .v-ftinfo .build-name{
		align-items: center;
		color: rgba(181, 161, 91, 1);
		padding: 8upx 16upx;
		font-size: 28upx;
		border-radius: 4upx;
	}
    
    .more {
        clear: both;
        margin-top: 10upx;
        text-align: center;
        padding: 10upx 0upx;
        background: #F7F7F7;
        color: #777777;
    }
</style>