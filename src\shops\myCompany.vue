<template>
    <view class="my-company" :class="{'ismanger':is_manager}">
        <view class="company-top">
            <!-- <view class="title flex-row" >
                <view class="title-info">{{is_manager?'我的公司':"公司详情"}}</view>
                
            </view> -->
            <view class="company-infos flex-row">
                <view class="info-left flex-box">
                    <view class="company-name">{{company.name}}</view>
                    <view class="company-address flex-row"><my-icon size="22upx" color="#999" type="weizhi"></my-icon><text class="text">{{company.address}}</text></view>
                    <view class="company-tel flex-row"><my-icon size="22upx" color="#999" type="ic_dianhua1"></my-icon><text class="text">{{company.tel}}</text></view>

                </view>
                <view class="icon_mid" v-if ="is_manager"  @click="$navigateTo(`/shops/editCompany?id=${company.id}`)">
                    <my-icon type="ic_xiugai" size="40upx" color="#999"></my-icon>
                </view>
                <view class="info-img">
                    <image :src='company.logo|formatImg("w_240")' mode="widthFix"></image>
                </view>
            </view>
        </view>
        <view class="company-shops">
            <view class="title flex-row">
                <view class="title-info">{{is_manager?"我的门店":'门店列表'}}</view>
                <view class="title-edit" v-if="is_manager"  @click="editShop=!editShop">管理门店</view>
            </view>
            <view class="shop-no-list flex-box" v-if='listsData.length==0&&showData'>
                <view class="no-data-img">
                </view>
                <view class="no-data-tips">还没有门店</view>
                <view class="addShop" v-if ="is_manager" @click="$navigateTo(`/shops/addShop?id=${company.id}`)">立即添加</view>
            </view>
            <view class="shop-list" v-else >
                <!-- :twice="item.twice"  :rent="item.rent"-->
                <shop-item v-for="(item,index) in listsData" :key='item.id' @click='handleGoDetail(item.id)'  :fromList="false" :image="item.image" :checked="item.checked" :name="item.name" :address="item.address"  :agent="item.agent_num">
                    <template v-slot:check_box="{checked}" v-if="editShop" >
                        <view class="check-box" @click.prevent.stop="checkItems(index)">
                            <my-icon v-if="checked" type="ic_xuanze" color="#ff656b" size="32rpx"></my-icon>
                            <view v-else class="check"></view>
                        </view>
                    </template>
                </shop-item>
                <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
                
            </view>
            
        </view>
        <view  v-if ="listsData.length>0&&showData&&is_manager" class="addShop-data" @click="operShops">
            <view class="add">{{editShop?"删除店铺":'添加店铺'}}</view>
        </view>
    </view>
</template>

<script>
import shopItem from './components/shopItem'
import myIcon from '../components/myIcon'
import {formatImg} from '../common/index.js'
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
    components:{
        shopItem,
        myIcon,
        uniLoadMore,
    },
    data(){
        return {
            listsData:[],
            get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
            company:{},
            showData:false,
            page:1,
            rows:6,
            editShop:false,
            company_id:0,
            is_manager:false,
        }
    },
    onLoad(options){
        if (options.id){
            this.company_id=options.id
        }
        this.getData()
        uni.$on("getDataAgain",this.getShopList)
    },
    onUnload(){
        uni.$off("getDataAgain")
    },
    filters:{
        formatImg(val, param = "") {
            return formatImg(val, param)
        }
    },
    methods: {
        handleGoDetail(id){
            this.$navigateTo(`/shops/detail?id=${id}`)
        },
        operShops(){
            if (this.editShop){
                this.delShop()
            }else {
                this.$navigateTo('/shops/addShop?id='+this.company.id)
            }
        },
        delShop(){
            let ids=[]
            console.log(this.listsData);
            this.listsData.filter(item=>
                item.checked==1
            ).map(item=>{
                ids.push(item.id)
            })
            if (ids.length>0){
                ids=ids.join(',')
            }else {
                ids=''
            }
            if(!ids){
                uni.showToast({
                    title:'请选择店铺',
                    icon:'none'
                })
                return;
            }
                this.$ajax.post('agentCompany/delStore',{id:ids},res=>{
					if(res.data.code === 1){
						uni.showToast({
							title:res.data.msg,
                        })
                        setTimeout(() => {
                            this.getShopList()
                        }, 1000);
                        
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:'none'
						})
					}
				})
        },
        getData(){
            let url="",params={}
            if (this.company_id){
                url="agentCompany/getCompanyDetail"
                params={id:this.company_id}
            }else {
                url="agentCompany/selfCompanyDetail"
            }
            this.$ajax.get(url,params,res=>{
					if(res.data.code === 1){
                        this.company=res.data.company
                        if ((this.company_id&&res.data.company.is_master==1)|| !this.company_id){
                            this.is_manager=true
                        }
                        if(res.data.share){
                            this.share=res.data.share
                        }else{
                            this.share={
                                title:this.company.name,
                                content:this.company.address,
                                pic:this.company.logo
                            }
                        }
                        this.getWxConfig()
                        this.getShopList()
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:'none'
						})
                    }
				})
        },
        checkItems(index){
            console.log(index);
            if (this.listsData[index].checked) {
                this.listsData[index].checked = 0
            } else {
                this.listsData[index].checked = 1
            }
        },
        getShopList(){
            this.get_status='loading'
            if (this.page ==1){
                this.listsData=[]
            }
            this.showData=false
            this.$ajax.get('agentCompany/agentStoreList',{company_id:this.company.id,page:this.page,rows:this.rows},res=>{
                
					if(res.data.code === 1&&res.data.list.length>0){
                        let list = res.data.list.map(item => {
                            item.checked = 0
                            return item
                        })
                        this.listsData=this.listsData.concat(list)
                        if (res.data.list.length < this.rows) {
                            this.get_status = "nomore";
                        }else{
							this.get_status = "more"
						}
                    } else {
                        
                        this.get_status = "nomore";
                    }
					
                    this.showData=true
				})
        }
    },
    onReachBottom(){
        if (this.get_status == "more"){
            this.page++
            this.getShopList()
        }
    },
    onShareAppMessage() {
    
      return {
        title: this.share.title || '',
        content: this.share.content || '',
        imageUrl: this.share.pic ? formatImg(this.share.pic, 'w_6401') : ''
      }
  },


}
</script>

<style lang="scss" scoped>

.my-company{
    padding:24upx  48upx 0 ;
    background: #fff;
    &.ismanger{
        padding:24upx  48upx 120upx;
    }
    .flex-row{
        display: flex;
        flex-direction: row;

    }
    .flex-box{
        display: flex;
        flex-direction: column;
    }
    .addShop-data{
        position: fixed;
        bottom: 24upx;
        left: 0;
        width: 100%;
        z-index: 15;
        // background: #fff;
        .add{
            margin: 0 48upx;
            background: #FFFFFF;
            border: 1px solid #FB656A;
            border-radius: 22px;
            font-size: 16px;
            color: #FB656A;
            text-align: center;
            padding: 28upx 0;
        }
    }

   } 
    .company-top{
        .title{ 
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24upx;
            .title-info{
                font-size: 40upx;
                color: #333333;
                font-weight: 600;
            }
            .title-edit{
                font-size: 22upx;
                color: #999999;
                align-items: center;
            }

        }
    }
    .company-infos{
        padding: 48upx;
        border: 2upx solid #D8D8D8;
        box-shadow: 0 4upx 32upx 0 rgba(0,0,0,0.05);
        border-radius: 28upx;
        
        justify-content: space-between;
        .info-left {
            flex:1;
            .company-address{
                margin: 20upx 0;
                align-items: center;
                .text{
                    margin-left: 20upx;
                }
            }
            .company-tel{
                align-items: center;
                .text{
                    margin-left: 20upx;
                }
            }
            .company-name{
                font-size: 32upx;
                color: #333333;
                font-weight: 600;
            }   
            
        }
        .icon_mid{
            margin-right: 25upx;
        }
        .info-img{
            width: 128upx;
            height: 128upx;
            overflow: hidden;
            border-radius: 8upx;
            image{
                width: 100%;
            }
        }

    }
    .company-shops{
        .title{ 
            justify-content: space-between;
            align-items: center;
            margin: 24upx 0;
           
            .title-info{
                font-size: 40upx;
                color: #333333;
                font-weight: 600;
            }
            .title-edit{
                font-size: 22upx;
                color: #999999;
                align-items: center;
            }
           
        }
        

        .shop-no-list{
            align-items: center;
            .no-data-img{
                width: 300upx;
                height: 180upx;
                overflow: hidden;
                background-image: url("https://images.tengfangyun.com/images/new_icon/<EMAIL>");
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }
            .no-data-tips{
                font-size: 32upx;
                color: #333333;
                margin-top: 24upx;
            }
            .addShop{
                padding: 22upx 48upx;
                font-size: 16px;
                color: #FFFFFF;
                text-align: center;
                background: #FB656A;
                box-shadow: 0 4px 15px 0 rgba(251,101,106,0.40);
                border-radius: 22px;
                margin-top: 110upx;
            }
        }
    }
    .check-box {
        padding: 32rpx 10rpx;
        margin-right: 10rpx;
        .check {
            width: 32rpx;
            height: 32rpx;
            border: 4rpx solid #dedede;
            border-radius: 50%;
        }
    }
    // .shop-list{
    //     background: #fff;
    // }


</style>