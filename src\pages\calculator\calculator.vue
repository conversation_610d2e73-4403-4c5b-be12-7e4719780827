<template>
	<view class="p-top-80 cal">
		<tab-bar :tabs="navs" :nowIndex="type" @click="switchTab"></tab-bar>
		<view class="top-20">
			<view class="container">
				<my-radio :value="mode" :range="[{ value: '0', name: '房屋总价' }, { value: '1', name: '贷款总额' }]" label="计算方式"
					bottomLine @change="onModeChange"></my-radio>
			</view>
			<view class="container" v-if="mode == '0'">
				<my-input type="digit" :value="loan.total" label="房屋总价" unit="万" name="total" bottomLine
					@input="handleInput($event, 'total')"></my-input>
				<my-select :value="loan.loanScale" label="首付比例" :range="firstPayment" name="loanScale"
					@change="selectFirstPayScale" :bottomLine="type === 2"></my-select>
				<my-input v-if="type === 2" type="digit" :value="loan.totalLimit" label="贷款总额度" disabled unit="万"
					placeholder=" " bottomLine></my-input>
			</view>
			<view class="container">
				<my-input v-if="type === 2 && mode == '1'" type="digit" :value="loan.totalLimit" label="贷款总额度"
					@input="handleInput($event, 'totalLimit')" unit="万" placeholder="请输入贷款总额度" bottomLine></my-input>
				<block v-if="type === 0 || type === 2">
					<my-input type="digit" :value="loan.loanLimit > 0 ? loan.loanLimit : ''" label="商贷额度"
						:disabled="type === 0 && mode == '0'" unit="万" name="loanLimit" placeholder="请输入商贷额度" bottomLine
						@input="handleInput($event, 'loanLimit')"></my-input>
					<my-select :value="loan.loanTime" label="商贷期限" :range="times" name="loanTime" bottomLine
						@change="handleChange"></my-select>
					<my-select :value="rateType" label="利率方式" :range="rate_types" bottomLine
						@change="selectRateType"></my-select>
					<!-- 旧版基准利率 -->
					<my-select v-if="rateType === 2" :value="loan.loanRate" label="商贷利率" :range="brates" name="loanRate"
						@change="handleChange"></my-select>
					<!-- LPR利率 -->
					<template v-if="rateType === 1">
						<my-input type="digit" v-model="max_lpr" label="LPR" showLabelIcon unit="%" name="lpr"
							@input="computeLoanRate" @clickLabel="showLprTip" placeholder=" " bottomLine></my-input>
						<my-input type="digit" v-model="jidian" label="基点" showLabelIcon unit="BP(‱)" name="jidian"
							@input="computeLoanRate" @clickLabel="showJidianTip" placeholder="请输入基点值"></my-input>
						<!-- 计算出的商贷利率 -->
						<view class="my-input flex-box">
							<view class="input-row flex-1">
								<label>
									<text>商贷利率</text>
								</label>
								<view class="flex-end flex-box">
									<view class="lpr_jidian">
										<text>{{ max_lpr }}%</text>
										<text style="margin: 0 16rpx">+</text>
										<text>{{ jidian || 0 }}‱</text>
										<text style="margin: 0 16rpx">=</text>
									</view>
									<input :value="loan.loanRate" placeholder="" placeholder-style="color:#999999">
									<text class="unit">%</text>
								</view>
							</view>
						</view>
					</template>
				</block>
			</view>
			<view class="container">
				<block v-if="type === 1 || type === 2">
					<my-input type="digit" :value="loan.aloanLimit > 0 ? loan.aloanLimit : ''" label="公积金贷款额度"
						:disabled="type === 1 && mode == '0'" unit="万" name="aloanLimit" placeholder="请输入公积金贷款额度"
						@input="handleInput($event, 'aloanLimit')"></my-input>
					<my-select :value="loan.aloanTime" label="公积金贷款期限" :range="times" name="aloanTime"
						@change="handleChange"></my-select>
					<my-select :value="loan.aloanRate" label="公积金贷款利率" :range="arates" name="aloanRate"
						@change="handleChange"></my-select>
				</block>
			</view>
			<view class="btn-box">
				<view class="btn btn-lg" @click="subData()">点击计算</view>
			</view>
			<myPopup ref="lpr_popup" position="center" touch_hide :height="lpr_popup_height">
				<view class="lpr_tip_box" id="lpr_popup">
					<view class="cont">
						<view class="title">什么是LPR?</view>
						<view class="desc">
							自2019年10月8日起，新发放商业性个人住房贷款利率以最近一个月相应期限的贷款市场报价利率（LPR）为定价基准加点形成。
						</view>
						<view class="desc">
							即：最新商贷利率=LPR+基点
						</view>
						<view class="title">
							<text>最新LPR是多少?</text>
							<text class="title_tip" v-if="update_time">{{ update_time }}</text>
						</view>
						<view class="desc table">
							<view class="tr">
								<view class="th">贷款年限</view>
								<view class="th">LPR</view>
							</view>
							<view class="tr">
								<view class="td">五年及以上</view>
								<view class="td">{{ max_lpr }}%</view>
							</view>
							<view class="tr">
								<view class="td">1~5年</view>
								<view class="td">由银行自主选择</view>
							</view>
							<view class="tr">
								<view class="td">1年以内</view>
								<view class="td">{{ min_lpr }}%</view>
							</view>
						</view>
					</view>
					<view class="btn" @click="hideLprTip">知道了</view>
				</view>
			</myPopup>
			<myPopup ref="jidian_popup" position="center" touch_hide :height="jidian_popup_height">
				<view class="lpr_tip_box" id="jidian_popup">
					<view class="cont">
						<view class="title">什么是基点?</view>
						<view class="desc">
							由贷款银行按照地区信贷政策要求和借款人综合情况确定的一个数值。
						</view>
						<view class="desc">
							即：最新商贷利率=LPR+基点
						</view>
					</view>
					<view class="btn" @click="hideJidianTip">知道了</view>
				</view>
			</myPopup>
		</view>
	</view>
</template>

<script>
import tabBar from '../../components/tabBar.vue'
import myInput from '../../components/form/newInput'
import myRadio from '../../components/form/myRadio'
import mySelect from '../../components/form/mySelect'
import myPopup from '../../components/myPopup'
// #ifdef H5
import { statistics } from '../../common/statistics'
// #endif
export default {
	data() {
		return {
			mode: '0',
			lpr_popup_height: "initial",
			jidian_popup_height: "initial",
			navs: [
				{
					name: "商业贷款"
				},
				{
					name: "公积金贷款"
				},
				{
					name: "组合贷款"
				}
			],
			type: 0,
			firstPayment: [
				{ name: "20%", value: 20 },
				{ name: "25%", value: 25 },
				{ name: "30%", value: 30 },
				{ name: "35%", value: 35 },
				{ name: "40%", value: 40 },
				{ name: "45%", value: 45 },
				{ name: "50%", value: 50 },
				{ name: "55%", value: 55 },
				{ name: "60%", value: 60 },
				{ name: "65%", value: 65 },
				{ name: "70%", value: 70 }
			],
			times: [
				{ name: "10年(120期)", value: 10 },
				{ name: "15年(180期)", value: 15 },
				{ name: "20年(240期)", value: 20 },
				{ name: "25年(300期)", value: 25 },
				{ name: "30年(360期)", value: 30 }
			],
			// 商贷利率
			brates: [],
			// 公积金利率
			arates: [],
			rateType: 1, //利率方式 1：LPR; 2：旧版基准利率
			rate_types: [
				{
					value: 1,
					name: '按最新LPR利率'
				},
				{
					value: 2,
					name: '按旧版基准利率'
				}
			],
			jidian: '',
			max_lpr: '',
			min_lpr: '',
			update_time: '',
			loan: {
				total: "",    // 商品总价
				loanScale: 15,    //首付比例
				totalLimit: '',     //贷款总额
				loanLimit: '',    // 贷款金额
				loanTime: 10,    // 商贷贷款年限
				loanRate: 4.65,   // 商贷利率
				aloanTime: 10,    // 公积金贷款年限
				aloanRate: 3.25   // 公积金贷款利率
			}
		}
	},
	components: {
		tabBar,
		myInput,
		myRadio,
		mySelect,
		myPopup
	},
	onLoad(options) {
		if (options.shoufubili) {
			this.loan.loanScale = options.shoufubili
			console.log(this.loan.loanScale);
			this.selectFirstPayScale({ value: parseInt(options.shoufubili), _name: 'loanScale' })
		}
		if (options.total) {
			this.loan.total = options.total
			this.loan.loanLimit = options.total * (1 - this.loan.loanScale / 100)
		}
		// #ifdef H5
		statistics()
		// #endif
		this.getRate()
	},
	methods: {
		getRate() {
			this.$ajax.get('build/lendingRate', {}, res => {
				let loanRate = parseFloat(res.data.config.shangdaililv)
				let aloanRate = parseFloat(res.data.config.gongjijinlilv)
				this.max_lpr = parseFloat(res.data.config.lpryinianyishang)
				this.min_lpr = parseFloat(res.data.config.lpryinianyinei)
				if (res.data.config.update_time) {
					this.update_time = res.data.config.update_time
				}
				if (res.data.config.shoufubili) {
					this.firstPayment = res.data.config.shoufubili
				}
				this.loan.aloanRate = aloanRate
				if (this.rateType === 1) {
					this.loan.loanRate = this.max_lpr
				} else {
					this.loan.loanRate = loanRate
				}

				// 商业贷款利率
				let discount = 0.9
				let brates = []
				while (discount <= 1.40) {
					console.log(discount)
					let item = {}
					item.value = (loanRate * discount).toFixed(3) - 0
					if (discount < 1) {
						item.name = `基准利率${discount * 10}折(${item.value}%)`
					}
					if (discount == 1) {
						item.name = `基准利率(${item.value}%)`
					}
					if (discount > 1) {
						console.log(discount - 1)
						item.name = `基准利率上浮${(discount - 1).toFixed(2) * 100}%(${item.value}%)`
					}
					brates.push(item)
					discount = (discount + 0.05).toFixed(2) - 0
				}
				// console.table(brates,['name', 'value'])
				this.brates = brates


				// 公积金
				let discount2 = 0.7
				let arates = []
				while (discount2 <= 1.1) {
					let item = {}
					item.value = (aloanRate * discount2).toFixed(3) - 0
					if (discount2 < 1) {
						item.name = `基准利率${discount2 * 10}折(${item.value}%)`
					}
					if (discount2 == 1) {
						item.name = `基准利率(${item.value}%)`
					}
					if (discount2 > 1) {
						item.name = `基准利率上浮${(discount2 - 1).toFixed(2) * 100}%(${item.value}%)`
					}
					arates.push(item)
					discount2 = (discount2 + 0.05).toFixed(2) - 0
				}
				// console.table(arates,['name', 'value'])
				this.arates = arates
				if (res.data.share) {
					this.share = res.data.share
					this.getWxConfig()
				}
			})
		},
		// 切换贷款类型
		switchTab(e) {
			if (this.type === e.index) {
				return
			}
			this.type = e.index
			this.loan.total = ""
			this.loan.totalLimit = this.mode == '0' ? 0 : ''
			this.loan.loanLimit = 0
			this.loan.aloanLimit = 0
			switch (this.type) {
				case 0:
					this.loan.loanTime = this.times[0].value
					if (this.rateType === 1) {
						this.computeLoanRate()
					} else {
						this.loan.loanRate = this.brates[2].value
					}
					this.loan.aloanTime = 0
					this.loan.aloanRate = 0
					break
				case 1:
					this.loan.loanTime = 0
					this.loan.loanRate = 0
					this.loan.aloanTime = this.times[0].value
					this.loan.aloanRate = this.arates[6].value
					break
				case 2:
					this.loan.loanTime = this.times[0].value
					if (this.rateType === 1) {
						this.computeLoanRate()
					} else {
						this.loan.loanRate = this.brates[2].value
					}
					this.loan.aloanTime = this.times[0].value
					this.loan.aloanRate = this.arates[6].value
			}
		},
		onModeChange(e) {
			this.mode = e.detail.value
			this.loan.totalLimit = this.mode == '0' ? 0 : ''
		},
		handleInput(e, name) {
			console.log(e)
			this.loan[name] = parseFloat(e)
			if (name == "total") {
				this.loan.totalLimit = parseFloat((this.loan.total * (1 - this.loan.loanScale / 100)).toFixed(2))
				if (this.type == 0) {
					this.loan.loanLimit = this.loan.totalLimit
					this.loan.aloanLimit = 0
				} else if (this.type == 1) {
					this.loan.aloanLimit = this.loan.totalLimit
					this.loan.loanLimit = 0
				} else if (this.type == 2) {
					this.loan.loanLimit = 0
					this.loan.aloanLimit = 0
				}
			}
			if (name == 'loanLimit' && this.type == 2) {
				if (this.mode === '1') {
					console.log(this.loan.loanTotal)
					this.loan.aloanLimit = this.loan.totalLimit - this.loan.loanLimit
				} else {
					this.loan.aloanLimit = parseFloat((this.loan.total * (1 - this.loan.loanScale / 100) - this.loan.loanLimit).toFixed(2))
				}
			}
			if (name == 'aloanLimit' && this.type == 2) {
				if (this.mode === '1') {
					this.loan.loanLimit = this.loan.totalLimit - this.loan.aloanLimit
				} else {
					this.loan.loanLimit = parseFloat((this.loan.total * (1 - this.loan.loanScale / 100) - this.loan.aloanLimit).toFixed(2))
				}
			}
		},
		// 选择首付比例
		selectFirstPayScale(e) {
			console.log(e);
			this.loan[e._name] = e.value
			this.loan.totalLimit = parseFloat((this.loan.total * (1 - this.loan.loanScale / 100)).toFixed(2))
			switch (this.type) {
				case 0:
					this.loan.loanLimit = this.loan.totalLimit
					this.loan.aloanLimit = 0
					break
				case 1:
					this.loan.aloanLimit = this.loan.totalLimit
					this.loan.loanLimit = 0
					break
				case 2:
					this.loan.loanLimit = 0
					this.loan.aloanLimit = 0
			}
		},
		handleChange(e) {
			this.loan[e._name] = e.value
			// 商贷或组合贷款
			// if(this.type !== 1){
			// 	this.loan.loanLimit = parseFloat((this.loan.total*(1-this.loan.loanScale/100)).toFixed(2))
			// }
			// // 公积金贷款
			// if(this.type === 1){
			// 	this.loan.aloanLimit = parseFloat((this.loan.total*(1-this.loan.loanScale/100)).toFixed(2))
			// }
			// 组合贷款
			// if(this.type === 2){
			// 	if(this.loan.aloanLimit){
			// 		this.loan.aloanLimit = parseFloat((this.loan.aloanLimit*(1-this.loan.loanScale/100)).toFixed(2))
			// 	}
			// }
		},
		// 选择利率方式
		selectRateType(e) {
			this.rateType = e.value
			if (this.rateType === 2) {
				this.loan.loanRate = this.brates[2].value
			} else {
				// this.loan.loanRate = this.max_lpr
				this.computeLoanRate()
			}
		},
		computeLoanRate() {
			this.loan.loanRate = parseFloat((parseFloat(this.max_lpr) + (this.jidian / 100)).toFixed(2))
		},
		showLprTip() {
			const query = uni.createSelectorQuery().in(this);
			query.select('#lpr_popup').boundingClientRect(data => {
				this.lpr_popup_height = data.height + 'px'
				this.$nextTick(() => {
					this.$refs.lpr_popup.show()
				})
			}).exec();
			// this.$refs.lpr_popup.show()
		},
		hideLprTip() {
			this.$refs.lpr_popup.hide()
		},
		showJidianTip() {
			const query = uni.createSelectorQuery().in(this);
			query.select('#jidian_popup').boundingClientRect(data => {
				this.jidian_popup_height = data.height + 'px'
				this.$nextTick(() => {
					this.$refs.jidian_popup.show()
				})
			}).exec();
		},
		hideJidianTip() {
			this.$refs.jidian_popup.hide()
		},
		subData() {
			if (!this.loan.total && this.mode == '0') {
				uni.showToast({
					title: "请输入房屋总价",
					icon: "none"
				})
				return
			}
			if (this.type == 2 && !this.loan.loanLimit) {
				uni.showToast({
					title: "请输入商业贷款额度",
					icon: "none"
				})
				return
			}
			if (this.type == 2 && !this.loan.aloanLimit) {
				uni.showToast({
					title: "请输入公积金贷款额度",
					icon: "none"
				})
				return
			}
			console.log(this.loan)
			this.$navigateTo('/pages/calculator/res?data=' + JSON.stringify(this.loan) + "&loan_type=" + this.type)
		}
	}
}
</script>

<style lang="scss">
.container {
	padding: 0 48rpx;
	background-color: #fff;
	margin-bottom: 20rpx;
}

.cal .input-row label {
	width: 220upx;
	text-align-last: inherit;
}

.cal .select-row label {
	width: 220upx;
	text-align-last: inherit;
}

.btn-box {
	padding: 24rpx 48rpx;
}

.btn-box .btn.btn-lg {
	width: 100%;
	padding: 10upx;
	border-radius: 44rpx;
	line-height: 88upx;
	text-align: center;
	line-height: 60upx;
	box-sizing: border-box;
	font-size: 32rpx;
	color: #fff;
	background: #FB656A;
	box-shadow: 0 4px 12px 0 rgba(251, 101, 106, 0.40);
}

.my-input {
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 0;
	background-color: #fff;

	label {
		display: block;
		font-size: 22rpx;
		margin-bottom: 24rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		color: #666;
	}

	.lpr_jidian {
		font-size: 36rpx;
		color: #666;
	}

	.flex-end {
		// justify-content: flex-end;
		align-items: center;
	}

	input {
		width: 70rpx;
		text-align: right;
		margin-right: 8rpx;
	}
}

.lpr_tip_box {
	background-color: #fff;
	width: 80vw;
	margin: 0 auto;
	box-sizing: border-box;
	border-radius: 8rpx;

	.cont {
		padding: 48rpx;
	}

	.title {
		font-size: 38rpx;
		font-weight: bold;
		margin: 20rpx 0;

		.title_tip {
			margin-left: 16rpx;
			font-weight: initial;
			font-size: 24rpx;
			color: #666;
		}
	}

	.desc {
		line-height: 1.8;

		&.table {
			border-top: 1rpx solid #f3f3f3;
			border-left: 1rpx solid #f3f3f3;
		}

		.tr {
			text-align: center;
			display: flex;

			.th {
				flex: 1;
				padding: 16rpx 10rpx;
				background-color: #f3f3f3;
				border-bottom: 1rpx solid #f3f3f3;
				border-right: 1rpx solid #f3f3f3;
			}

			.td {
				flex: 1;
				padding: 16rpx 10rpx;
				border-bottom: 1rpx solid #f3f3f3;
				border-right: 1rpx solid #f3f3f3;
			}
		}
	}

	.btn {
		margin-top: 20rpx;
		border-top: 1rpx solid #f3f3f3;
		padding: 24rpx;
		font-size: 36rpx;
		font-weight: bold;
		color: $uni-color-primary;
		text-align: center;
	}
}
</style>
