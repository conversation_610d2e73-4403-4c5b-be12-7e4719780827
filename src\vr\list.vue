<template>
<view>
  <tab-bar  v-if='filter_list.length'  :tabs="filter_list" :fixedTop="true" :showLine="false" :nowIndex="current_filter_index"  @click="switchTab">
      </tab-bar>
  <view class="vr_list">
    
    <view class="vr_item" v-for="(item,i) in vr_list" :key="i" @click="handleClick(item)">
      <image class="vr_cover" :src="item.cover | imageFilter('w_400')" mode="aspectFill"></image>
      <view class="build_title" v-if="item.buildTitle">{{item.buildTitle}}</view>
      <view class="title">{{item.title}}</view>
      <view class="time">更新时间: {{item.utime}}</view>
      <image class="vr-icon" src="/static/icon/vr_b.png" mode="widthFix"></image>
    </view>
    <view class="vr_item empty"></view>
  </view>
  <uni-load-more v-if="!build_id" :status="get_status" :content-text="content_text"></uni-load-more>
</view>
</template>

<script>
import {uniLoadMore} from '@dcloudio/uni-ui'
import tabBar from "../components/tabBar"
export default {
  components: {
    uniLoadMore,
    tabBar
  },
  data() {
    return {
      build_id: '',
      vr_list: [],
      page: 1,
      get_status:"loading",
      content_text:{
        contentdown:"",
        contentrefresh:"正在加载...",
        contentnomore:"没有更多数据了"
      },
      filter_list:[
        {
          type:0,
          name:'全部'
        },
        {
          type:1,
          name:'楼盘'
        },
        {
          type:2,
          name:'户型图'
        }
      ],
      current_filter_index:0,
      current_filter_type:0
    }
  },
  onLoad(options) {
    if (options.bid) {
      this.build_id = options.bid
      this.getBuildVr()
    }else if(options.cid){
      this.cid =options.cid
      this.filter_list.length=0
      this.getCVr()
    }else{
      this.getVrList()
    }
  },
  methods: {
    getBuildVr() {
      uni.showLoading({
        title: "加载中"
      })
      this.$ajax.get("build/vrList.html", {info_id: this.build_id, type: 1 }, res => {
        uni.hideLoading()
        if(res.data.share){
          this.share = res.data.share
          this.getWxConfig()
        }
        if(res.data.buildTitle){
          uni.setNavigationBarTitle({
            title: res.data.buildTitle+'VR列表'
          })
        }
        if(res.data.code === 1){
          this.vr_list = res.data.vrs
          if(this.current_filter_index >0){
            this.vr_list =this.vr_list.filter(item=>item.type==this.current_filter_type)
          }
          this.get_status ='nomore'
          
        }else{
          this.get_status ='nomore'
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      }, err=>{
        uni.hideLoading()
      })
    },
    getCVr(){  //community/vrList?cid=571
        this.$ajax.get("community/vrList", {cid: this.cid}, res => {
        // #ifdef H5 || MP-BAIDU
        if (res.data.seo) {
          this.seo = res.data.seo
        }
        // #endif
        if(res.data.share){
          this.share = res.data.share
          this.getWxConfig()
        }
        if(res.data.code === 1){
          this.vr_list = res.data.list
          this.get_status ='nomore'
        }else{
          this.get_status ='nomore'
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
        uni.hideLoading()
      }, err=>{
        uni.hideLoading()
        this.get_status ='nomore'
      })
    },
    switchTab(e){
      this.current_filter_index = e.index
      this.current_filter_type =e.type 
      if (this.build_id){
        this.getBuildVr()
      }else {
        this.page =1
        this.getVrList()
      }
      
    },
    getVrList(){
      if (this.page ==1) {
          this.vr_list =[]
        }
      this.get_status = "loading"
      this.$ajax.get("build/vrIndex", {page: this.page,type:this.current_filter_type}, res=>{
        if(res.data.share&&this.page === 1){
          this.share = res.data.share
          this.getWxConfig()
        }
        if(res.data.code === 1&&res.data.lists.length>0){
            this.vr_list =this.vr_list.concat(res.data.lists) 
            this.get_status = "more"
        }else{
          this.get_status = "noMore"
        }
      })
    },
    handleClick(e){
      if (!this.cid){
        if(e.type==1){
          this.toVr(e)
        }else{
          this.toHouseType(e)
        }
      }else {
        this.toCVr(e)
      }
      
    },
    toCVr(e){
      this.$navigateTo('/vr/detail?cid=' + e.id)
    },
    toVr(e){
      this.$navigateTo('/vr/detail?build_vrid=' + e.id)
    },
    toHouseType(e){
      this.$navigateTo(`/pages/new_house/photo?bid=${e.bid}&img_id=${e.info_id}`)
    }
  },
  onReachBottom(){
    if(this.get_status!=='more'||this.build_id){
      return
    }
    this.page++
    this.getVrList()
  }
}
</script>

<style scoped lang="scss">
.vr_list{
  padding:36rpx;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .vr_item{
    flex: 1;
    min-width: 40%;
    max-width: 50%;
    margin: 12rpx;
    background-color: #fff;
    position: relative;
    .vr_cover{
      width: 100%;
      height: 320rpx;
      border-radius: 6rpx;
      overflow: hidden;
    }
    .build_title{
      margin: 16rpx;
      font-size: 28rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .title{
      margin: 16rpx;
      // min-height: 76rpx;
      font-size: 28rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      color: #333;
    }
    .time{
      padding: 0 16rpx;
      padding-bottom: 24rpx;
      font-size: 24rpx;
      color: #999;
    }
    .vr-icon {
      width: 80rpx;
      height: 0;
      position: absolute;
      top: 116rpx;
      left: 0;
      right: 0;
      margin: auto;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0);
    }
    &.empty{
      height: 0;
    }
  }
}
</style>
