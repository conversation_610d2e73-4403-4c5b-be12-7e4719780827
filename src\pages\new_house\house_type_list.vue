<template>
  <view class="house_list">
    <block v-for="item in list" :key="item.id">
      <house-type
        :title="`${item.shi}室${item.ting}厅${item.wei}卫 约${item.mianji}m²`"
        :img="item.img"
        :has_vr="item.vr?1:0"
        :sale_status="item.status_id"
        :sale_status_text="item.status"
        :labels="[item.desc, item.title]"
        :checked="item.checked"
        @click="$navigateTo(`/pages/new_house/photo?bid=${item.bid}&img_id=${item.id}`)"
      >
        <template>
          <view v-if="list_type==='select'" class="db_btn" @click.stop.prevent="handleSelect(item)">选择</view>
          <view v-else class="db_btn" @click.stop.prevent="addContrast(item.id)">+对比</view>
        </template>
      </house-type>
    </block>
    <!-- 底部操作菜单 -->
    <view class="bottom-bar flex-row">
      <view class="bar-left flex-row flex-1">
        <!-- <view class="icon-btn" @click="handleFollow">
          <my-icon :type="is_follow?'ic_guanzhu_red':'ic_guanzhu'" :color="is_follow?'#ff656b':'#666'" size="50rpx"></my-icon>
          <text>关注</text>
        </view> -->
        <view class="icon-btn" v-if="navs[0].is_show&&(navs[0].operation===1||navs[0].operation===2)" @click="toYuyue(3, navs[0])">
          <my-icon type="yuyue" color="#666" size="50rpx"></my-icon>
          <text>{{navs[0].name}}</text>
        </view>
        <view class="icon-btn" v-if="navs[0].is_show&&(navs[0].operation===3||navs[0].operation===4)" @click="cusList( navs[0].operation)">
          <my-icon type="ic_zixun" color="#666" size="50rpx"></my-icon>
          <text>{{navs[0].name}}</text>
        </view>
        <view class="icon-btn" v-if="navs[1].is_show===1" @click="toContrast()">
          <text class="badge" v-if="login_status>1&&contrastCount>0">{{contrastCount>99?'99+':contrastCount}}</text>
          <text class="badge" v-if="login_status<=1&&$store.state.temp_huxing_contrast_ids.length>0">{{$store.state.temp_huxing_contrast_ids.length>99?'99+':$store.state.temp_huxing_contrast_ids.length}}</text>
          <my-icon type="pk" color="#666" size="50rpx"></my-icon>
          <text>{{navs[1].name}}</text>
        </view>
      </view>
      <!-- 置业顾问按钮 -->
      <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show===1&&(navs[2].operation===1||navs[2].operation===2)" @click="toYuyue(3, navs[2])">{{navs[2].name}}</view>
      <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show===1&&(navs[2].operation===3||navs[2].operation===4)" @click="cusList(navs[2].operation)">{{navs[2].name}}</view>
      <!-- 咨询售楼处按钮 -->
      <view class="flex-1" :class="{alone:navs[2].is_show===0}" v-if="navs[3].is_show===1" @click="handleTel()">
        <view class="bar-btn btn2">{{navs[3].name}}</view>
      </view>
    </view>
    <sub-form :groupCount="build.groupCount" :sub_type="sub_type" :sub_mode="sub_mode" ref='sub_form' @onsubmit="handleSubForm"></sub-form>
    <!-- <view class="share_btn" v-if="login_status>1" @click="toContrast()">去对比({{contrastCount}})</view>
    <view class="share_btn" v-if="login_status<=1" @click="toContrast()">去对比({{$store.state.temp_huxing_contrast_ids.length>99?'99+':$store.state.temp_huxing_contrast_ids.length}})</view> -->
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import myIcon from '../../components/myIcon.vue'
import {showModal} from '../../common/index'
import houseType from '../../components/houseType'
import allTel from '../../common/all_tel.js'
import {mapState} from 'vuex'
import subForm from '../../components/subForm'
export default {
  components: {
    myIcon,
    houseType,
    subForm
  },
  data() {
    return {
      bid:'',
      list: [],
      params: {
        page: 1,
        rows: 50
      },
      list_type: "",
      contrastCount: 0,
      is_follow: 0,
      sub_type: 0,
      build:{},
      navs: [
        {},
        {},
        {},
        {}
      ],
      tel_res: {},
      show_tel_pop: false
    }
  },
  computed:{
    ...mapState(['tel400jing']),
    glabol_middle_tel(){
        return this.$store.state.im.istelcall
    },
    sub_mode() {
        return this.$store.state.sub_form_mode 
    },
    login_status() {
      return this.$store.state.user_login_status
    }
  },
  onLoad(options) {
    this.params.bid = options.bid || ''
    this.list_type = options.list_type || ''
    this.getData()
    this.getNav()
  },
  methods: {
    getData() {
      uni.showLoading({
        title: "正在获取数据",
      })
      this.$ajax.get('build/getBuildAllTypePic.html', this.params, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          this.list = res.data.list
          this.build = res.data.build
          this.is_follow = res.data.is_follow||0
          this.contrastCount = res.data.contrastCount
          if(res.data.build.title){
            uni.setNavigationBarTitle({
              title:res.data.build.title+'户型图'
            })
          }
          if(res.data.share){
            this.share = res.data.share 
          }else{
            this.share = {
              title: `${res.data.build.title}户型图`,
              content: `${res.data.build.title}全部户型图`,
              pic: this.list.length>0?this.list[0].img:''
            }
          }
          this.getWxConfig()
        }
      },err=>{
        uni.hideLoading()
      })
    },
    getNav(){
      this.$ajax.get('build/buildNav.html',{bid:this.params.bid},res=>{
        if(res.data.code === 1){
          this.navs = res.data.navs
        }
      })
    },
    toYuyue(type, nav){
      if(nav.operation===2&&nav.group_id){
        // 跳转团购报名
        this.$navigateTo(`/pages/groups/detail?id=${nav.group_id}`)
      }else{
        this.toSubForme(type)
      }
    },
    toSubForme(type) {
      this.sub_type = type
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e) {
      //提交报名
      e.from = '楼盘页'
      e.bid = this.params.bid
      e.type = this.sub_type || ''
      this.$ajax.post('build/signUp.html', e, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          // 没开启引导登录模式或已经绑定手机号了
          if (this.sub_mode!==2||res.data.status === 3) {
            //提示报名成功
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            this.$refs.sub_form.closeSub()
          } else {
            this.$refs.sub_form.getVerify()
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    cusList(operation){
      // if(operation===4){
      //   console.log("和置业顾问发起聊天")
      //   return
      // }
      if (!uni.getStorageSync('token')) {
        this.$navigateTo('/user/login/login')
        return
      }
      this.$navigateTo('/pages/consultant/consuList?id=' + this.params.bid)
    },
    handleTel(){
        let phoneNumber=""
        // 如果没有开启虚拟号功能
        if(this.build.use_middle_call==0||this.glabol_middle_tel==0){
            if(this.build.phone&&this.build.sellmobile_part){
                phoneNumber = this.build.phone+','+this.build.sellmobile_part.trim()
                if(this.tel400jing){
                    phoneNumber+="#"
                }
                showModal({
                    title:"温馨提示",
                    content:"请拨打"+this.build.phone+"后转拨分机号"+this.build.sellmobile_part,
                    confirm: (res)=> {
                        uni.makePhoneCall({
                            phoneNumber: phoneNumber
                        });
                    }
                })  
            }else if(this.build.tel){
                uni.makePhoneCall({
                    phoneNumber: this.build.tel,
                    success:()=>{
                      // this.statistics()
                    }
                });
            }else{
                uni.showToast({
                  title:"此楼盘没有绑定联系电话",
                  icon:'none'
                })
            }
        }else{
          this.tel_params = {
            type: 1,
            callee_id: this.params.bid,
            scene_type: 1,
            scene_id: this.params.bid,
            success: (res)=>{
              this.tel_res = res.data
              this.show_tel_pop = true
            }
          }
          allTel(this.tel_params)
        }
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    // 处理关注或取消关注楼盘
    handleFollow(){
    if(this.is_follow){
        this.cancelFollow()
    }else{
        this.follow()
    }
    },
    // 关注楼盘
    follow(){
    this.$ajax.get('build/followBuild.html',{bid:this.params.bid},res=>{
        if(res.data.code === 1){
        uni.showToast({
            title:res.data.msg
        })
        this.is_follow = 1
        }else{
        uni.showToast({
            title:res.data.msg,
            icon:'none'
        })
        }
    })
    },
    // 取消关注楼盘
    cancelFollow(){
        this.$ajax.get('build/cancelFollowBuild.html',{bid:this.params.bid},res=>{
            if(res.data.code === 1){
            uni.showToast({
                title:res.data.msg
            })
            this.is_follow = 0
            }else{
            uni.showToast({
                title:res.data.msg,
                icon:'none'
            })
            }
        })
    },
    addContrast(img_id) {
      this.$ajax.get(
        'build/addContrast.html',
        { img_id },
        res => {
          if (res.data.code === -1) {
            this.$store.state.user_login_status = 1
            // 检测是否已添加
            if(this.$store.state.temp_huxing_contrast_ids.includes(img_id)){
              uni.showToast({
                title:"该户型已经添加",
                icon:'none'
              })
              return
            }
            this.$store.state.temp_huxing_contrast_ids.push(img_id)
            return
          }
          if (res.data.code === 1) {
            uni.showToast({
              title: res.data.msg
            })
            this.contrastCount++
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
          }
        },
        err => {},
        { disableAutoHandle: true }
      )
    },
    handleSelect(e){
      uni.$emit('select_house_type', e)
      this.$navigateBack()
    },
    toContrast(){
      if(this.login_status>1){
        this.$navigateTo('/contrast/house_list')
      }else{
        this.$navigateTo(`/contrast/house_list?no_login=1`)
      }
    }
  }
}
</script>

<style scoped lang="scss">

view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}
.house_list {
  background: #fff;
  padding-bottom: 140rpx;
}
.db_btn {
  line-height: 30rpx;
  padding: 0 8rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
  border: 1rpx solid $uni-color-primary;
  color: $uni-color-primary;
}

.share_btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 24rpx;
  display: block;
  margin: 0;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  color: #fff;
  background: $uni-color-primary;
  box-shadow: 0 4px 12px 0 rgba($uni-color-primary, 0.4);
}
    
// 底部菜单
.bottom-bar {
    background-color: #fff;
    height: 110rpx;
    padding: 15rpx 48rpx;
    left: 0;
    z-index: 10;
    .bar-left{
        padding-right: 10rpx;
        justify-content: flex-start;
    }
    .icon-btn {
        // width: 100rpx;
        align-items: center;
        padding: 0;
        margin: 0;
        background-color: #fff;
        line-height: initial;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        // flex: 1;
        padding-right: 32rpx;
        overflow: hidden;
        position: relative;
        // & ~ .icon-btn {
        //   margin-left: 24rpx;
        // }
        .header_img{
            width: 50rpx;
            height: 50rpx;
            border-radius: 50%;
        }
        text {
            line-height: 1;
            font-size: 22rpx;
            color: #999;
            display: inline-block;
            width: 100%;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .badge{
          display: inline-block;
          box-sizing: border-box;
          width: auto;
          position: absolute;
          top: 0;
          left: 32rpx;
          // right: 38rpx;
          height: 28rpx;
          padding: 0 8rpx;
          min-width: 28rpx;
          border-radius: 14rpx;
          font-size: 22rpx;
          background-color: $uni-color-primary;
          color: #fff;
        }
    }
    .bar-btn {
        // width: 220rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        font-size: 30rpx;
        padding: 0;
        margin: 0;
        border-radius: 0;
        color: #fff;
        &.btn1 {
            background: #FBAC65;
            box-shadow: 0 0 4px 0 rgba(0,0,0,0.05);
            border-top-left-radius: 40rpx;
            border-bottom-left-radius: 40rpx;
        }
        &.btn2 {
            background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
            box-shadow: 0 0 4px 0 rgba(255,80,0,0.30);
            border-top-right-radius: 40rpx;
            border-bottom-right-radius: 40rpx;
        }
    }
}
</style>
