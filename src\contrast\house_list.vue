<template>
  <view class="house-list">
    <view class="top-tip flex-row">
      <my-icon type="ic_guanyu" color="#ff656b"></my-icon>
      <text class="text">向左滑动删除户型</text>
    </view>
    <view class="contrast_list">
      <uni-swipe-action v-for="(item, index) in house_list" :key="item.id">
        <uni-swipe-action-item :parentData="item">
          <house-type
            :title="`${item.shi}室${item.ting}厅${item.wei}卫 约${item.mianji}m²`"
            :img="item.path"
            :sale_status="item.status_id"
            :sale_status_text="item.status"
            :labels="[item.desc, item.title]"
            :checked="item.checked"
            @click="$navigateTo(`/pages/new_house/photo?bid=${item.bid}&img_id=${item.id}`)"
          >
            <template v-slot:check_box="{ checked }">
              <view class="check-box" @click="handleCheck(index)">
                <my-icon v-if="checked" type="ic_xuanze" color="#ff656b" size="32rpx"></my-icon>
                <view v-else class="check"></view>
              </view>
            </template>
          </house-type>
          <template slot="option" slot-scope="{ parentData }">
            <view class="cancel_collect" @click="delFollow(parentData.contrast_id, index)">
              <view class="icon-box">
                <my-icon type="ic_delete_w" color="#fff" size="40rpx"></my-icon>
              </view>
            </view>
          </template>
        </uni-swipe-action-item>
      </uni-swipe-action>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <view class="contrasted_list" v-if="contrasted_list.length>0">
      <view class="label">我发起的对比</view>
      <view class="contrasted-item flex-row" v-for="item in contrasted_list" :key="item.id" @click="toContrastedDetail(item)">
        <view class="contrast contrast_a">
          <image mode="aspectFill" :src="item.data[0].img | imageFilter"></image>
          <text class="title">{{item.data[0].title}}</text>
          <text class="desc">{{item.data[0].desc}}</text>
          <text class="desc">{{item.data[0].shi}}室{{item.data[0].ting}}厅{{item.data[0].wei}}卫{{item.data[0].mianji}}m²</text>
        </view>
        <view class="icon-box">
          <my-icon type="pk" color="#fff"></my-icon>
        </view>
        <view class="contrast contrast_b">
          <image mode="aspectFill" :src="item.data[1].img | imageFilter"></image>
          <text class="title">{{item.data[1].title}}</text>
          <text class="desc">{{item.data[1].desc}}</text>
          <text class="desc">{{item.data[1].shi}}室{{item.data[1].ting}}厅{{item.data[1].wei}}卫{{item.data[1].mianji}}m²</text>
        </view>
        <view class="del_icon_box" @click.stop.event="deleteContrasted(item)">
          <my-icon type="qingchu" color="#333" size="40rpx"></my-icon>
        </view>
      </view>
    </view>
    <view class="btn-box">
      <view class="btns flex-row">
        <view class="btn" @click="$navigateBack()">添加户型</view>
        <view class="btn active" @click="toContrast()">开始对比</view>
      </view>
    </view>
  </view>
</template>

<script>
import houseType from '../components/houseType'
import myIcon from '../components/myIcon'
import { uniLoadMore, uniSwipeAction, uniSwipeActionItem } from '@dcloudio/uni-ui'
import { showModal } from '../common/index.js'
export default {
  components: {
    houseType,
    myIcon,
    uniLoadMore,
    uniSwipeAction,
    uniSwipeActionItem
  },
  data() {
    return {
      params: {
        page: 1,
        rows: 20
      },
      house_list: [],
      contrasted_list:[
        // {
        //   id:1,
        //   data:[
        //     {id:12,img:'',title:'保利海德家园', desc:'E区A户型',shi:2,ting:2,wei:2,mianji:134},
        //     {id:13,img:'',title:'保利海德家园', desc:'E区A户型',shi:2,ting:2,wei:2,mianji:134}
        //   ]
        // },
        // {
        //   id:2,
        //   data:[
        //     {id:12,img:'',title:'保利海德家园', desc:'E区A户型',shi:2,ting:2,wei:2,mianji:134},
        //     {id:13,img:'',title:'保利海德家园', desc:'E区A户型',shi:2,ting:2,wei:2,mianji:134}
        //   ]
        // }
      ],
      get_status: '',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      }
    }
  },
  onLoad(options) {
    if(parseInt(options.no_login)===1){
      this.no_login = parseInt(options.no_login)
      this.getData2()
      return
    }
    this.getData()
  },
  methods: {
    getData() {
      if (this.params.page === 1) {
        this.house_list = []
      }
      this.get_status = 'loading'
      this.$ajax.get('build/contrastList.html', this.params, res => {
        console.log(res.data)
        this.get_status = 'more'
        if (res.data.code === 1) {
          let list = res.data.list.map(item => {
            item.checked = 0
            return item
          })
          this.house_list = this.house_list.concat(list)
          if (res.data.list.length < this.params.rows) {
            this.get_status = 'noMore'
          }
        } else {
          this.get_status = 'noMore'
        }
      })
    },
    getData2() {
      this.get_status = 'loading'
      this.$ajax.get('build/contrastListNoLogin.html', {img_ids:this.$store.state.temp_huxing_contrast_ids.join(',')}, res => {
        this.get_status = 'noMore'
        if (res.data.code === 1) {
          let list = res.data.list.map(item => {
            item.checked = 0
            return item
          })
          this.house_list = this.house_list.concat(list)
        } else {
          this.get_status = 'noMore'
        }
      })
    },
    getContrasted(){
      this.$ajax.get('', {}, res=>{
        if(res.data.code === 1){
          this.contrasted_list = res.data.list
        }
      })
    },
    handleCheck(index) {
      if (this.house_list[index].checked) {
        this.house_list[index].checked = 0
      } else {
        let checked_num = this.house_list.filter(item => item.checked)
        if (checked_num.length >= 2) {
          uni.showToast({
            title: '一次只可以对比两个',
            icon: 'none'
          })
          return
        }
        this.house_list[index].checked = 1
      }
    },
    delFollow(contrast_id, index) {
      if(this.no_login){
        this.$store.state.temp_huxing_contrast_ids.splice(index,1)
        this.house_list = []
        this.getData2()
        return
      }
      let handleDel = () => {
        this.$ajax.get('build/delContrast.html', { contrast_id }, res => {
          if (res.data.code == 1) {
            this.params.page = 1
            this.getData()
          }
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        })
      }
      showModal({
        title: '提示',
        content: '确定删除此户型？',
        cancelText: '否',
        confirmText: '确定',
        confirm: () => {
          handleDel()
        }
      })
    },
    toContrast() {
      let checked_num = this.house_list.filter(item => item.checked)
      if (checked_num.length < 2) {
        uni.showToast({
          title: '请选择两个要对比的户型',
          icon: 'none'
        })
        return
      }
      if (checked_num.length > 2) {
        uni.showToast({
          title: '一次最多只能对比两个户型',
          icon: 'none'
        })
        return
      }
      if(this.no_login){
        let img_id1 = checked_num[0].id
        let img_id2 = checked_num[1].id
        this.$navigateTo(`/contrast/house_detail?img_id1=${img_id1}&img_id2=${img_id2}`)
        return
      }
      let contrast_id1 = checked_num[0].contrast_id
      let contrast_id2 = checked_num[1].contrast_id
      this.$navigateTo(`/contrast/house_detail?id1=${contrast_id1}&id2=${contrast_id2}`)
    },
    // 去生成的对比详情
    toContrastedDetail(e){
      this.$navigateTo(`/contrast/house_detail?id1=${e.data[0].id}&id2=${e.data[1].id}&huxing_contrast_id=${e.id}&sharers_id=${4090}&is_share=1`)
    },
    // 删除已经生成的对比
    deleteContrasted(e){
      let handleDel = () => {
        this.$ajax.get('build/delContrast.html', { contrast_id: e.id }, res => {
          if (res.data.code == 1) {
            this.getContrasted()
          }
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        })
      }
      showModal({
        title: '提示',
        content: '确定删除吗？',
        cancelText: '否',
        confirmText: '确定',
        confirm: () => {
          handleDel()
        }
      })
    }
  },
  onReachBottom() {
    if (this.get_status === 'more') {
      this.params.page++
      this.getData()
    }
  }
}
</script>

<style scoped lang="scss">
view {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.house-list{
  padding-bottom: 160rpx;
}

.contrasted_list{
  padding: 24rpx 48rpx;

  .label {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 48rpx;
  }
  .contrasted-item{
    // overflow: hidden;
    align-items: center;
    position: relative;
    margin-bottom: 24rpx;
    .icon-box{
      width: 64rpx;
      height: 64rpx;
      border-radius: 32rpx;
      background-color: $uni-color-primary;
      box-shadow: 0 2px 8px 0 rgba(251,101,106,0.40);
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 2;
    }
    .del_icon_box{
      position: absolute;
      top: -20rpx;
      right: -20rpx;
    }
  }
  .contrast{
    flex: 1;
    padding: 24rpx;
    position: relative;
    line-height: 1;
    color: #333;
    image{
      width: 232rpx;
      height: 128rpx;
      border-radius: 8rpx;
      background-color: #f5f5f5;
      margin-bottom: 24rpx;
    }
    .title{
      font-size: 32rpx;
      font-weight: bold;
    }
    .desc{
      margin-top: 16rpx;
      font-size: 24rpx;
    }
    &.contrast_a{
      border-top-left-radius: 16rpx;
      border-bottom-left-radius: 16rpx;
      background-color: #ffd7d9;
      &::after{
        content: "";
        position: absolute;
        // height: 100%;
        // width: 64rpx;
        top: 0;
        right: -64rpx;
        border-width: 160rpx 32rpx 160rpx 32rpx;
        border-style: solid;
        border-color: #ffd7d9 transparent transparent #ffd7d9;
      }
    }
    &.contrast_b{
      border-top-right-radius: 16rpx;
      border-bottom-right-radius: 16rpx;
      background-color: #dcdffd;
      &::after{
        content: "";
        position: absolute;
        bottom: 0;
        left: -64rpx;
        border-width: 160rpx 32rpx 160rpx 32rpx;
        border-style: solid;
        border-color: transparent #dcdffd #dcdffd transparent;
      }
    }
  }
}


.top-tip {
  align-items: center;
  padding: 24rpx 48rpx;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
  color: $uni-color-primary;
  .text {
    margin-left: 24rpx;
  }
}

.contrast_list {
  // padding: 0 48rpx;
  background-color: #fff;
}

.check-box {
  padding: 32rpx 10rpx;
  margin-right: 10rpx;
  .check {
    width: 32rpx;
    height: 32rpx;
    border: 4rpx solid #dedede;
    border-radius: 50%;
  }
}

.cancel_collect {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  padding: 0 48rpx;
  text-align: center;
  .icon-box {
    width: 80rpx;
    height: 80rpx;
    align-content: center;
    justify-content: center;
    border-radius: 50%;
    background-color: $uni-color-primary;
    box-shadow: 0 2px 8px 0 rgba(251, 101, 106, 0.4);
  }
}

.btn-box {
  padding: 48rpx;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 24rpx;
  .btns {
    line-height: 88rpx;
    border-radius: 44rpx;
    border: 1rpx solid $uni-color-primary;
    overflow: hidden;
    box-shadow: 0 4px 10px 0 rgba(251, 101, 106, 0.2);
    .btn {
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      color: $uni-color-primary;
      background-color: #fff;
      &.active {
        background-color: $uni-color-primary;
        color: #fff;
      }
    }
  }
}
</style>
