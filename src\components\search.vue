<template>
<view class="search-title flex-box">
  <view class="inp-box-def search-box flex-1 flex-box">
    <view class="icon-box">
      <my-icon type="ic_sousuo" color="#999999" size="46rpx"></my-icon>
    </view>
    <input type="text" :disabled="disabled" :value="value" confirm-type="search" @input="handelInput" @confirm="handelSearch" :focus="focus" :maxlength="20" :placeholder="placeholder" />
    <slot></slot>
    <!-- <view :class="clas" v-if="ishow" @click="handelClick">
      <my-icon :type="type" :color="color" :size="size"></my-icon>
      <text>{{btn}}</text>
    </view> -->
  </view>
</view>
</template>

<script>
import  myIcon from '../components/myIcon.vue'
export default {
  props: {
    placeholder: String,
    value: String,
    focus: {
      type: <PERSON>olean,
      default: false
    },
   disabled:{
      type: <PERSON>olean,
      default: false
   }
    
   
  },
  data() {
    return {

    };
  },
  components: {
    myIcon
  },
  methods: {
    handelInput(e) {
      this.$emit('input', e)
    },
    handelSearch(e) {
      this.$emit('confirm', e)
    },
    
  },
}
</script>

<style lang="scss" scoped>
.search-title {
  width: 100%;
  padding: 15upx 48rpx;
  box-sizing: border-box;
  flex: 1;
}

.search-box {
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.search-box .icon-box {
  width: 60upx;
  margin-left: 28upx;
  /* #ifndef H5 */
  margin-top: 7upx;
  /* #endif */
  position: absolute;
  z-index: 1;
}

.search-box input {
  height: 64upx;
  // padding: 6upx 10upx;
  padding-left: 90upx;
  box-sizing: border-box;
  font-size: $uni-font-size-sm;
  border-radius: 8rpx;
  // border: 1upx solid $uni-border-color;
  flex: 2;
  background: #f5f5f5;
}
</style>
