<template>
	<view class="addshop">
        <view class="imgs">
            <!-- <image src="https://images.tengfangyun.com/images/new_icon/<EMAIL>" mode="widthFixed"></image> -->

        </view>
        <view class="info">
            <view class="title">基本信息</view>
            <view class="tips">请填写门店相关信息</view>
            <input class="sub_tel"  maxlength="10" type="text" placeholder="店铺名称" @input="inputName" />
            <input class="sub_tel"  type="text" placeholder="店铺介绍" @input="inputDesc" />
            <picker  @change="pickerChange" :value="index" :range="areaList" range-key="areaname">
				<!-- <view :class="index>=0?'':'novalue'">{{index>=0?areaList[index].name:'请选择'}}</view> -->
                {{index>=0?areaList[index].areaname:'请选择区域'}}
			</picker>
            <input class="sub_tel"  maxlength="10" type="text" placeholder="店铺地址" @input="inputAddress" />
            <view class="sub_lngLat" @click="GetLatLng">{{position}}</view>
            <input class="sub_tel"  maxlength="12" type="number" placeholder="联系电话" @input="inputTel" />
            <input class="sub_tel"  maxlength="10" type="text" placeholder="店长姓名" @input="inputShoperName" />
            <input class="sub_tel"  maxlength="12" type="number" placeholder="店长电话" @input="inputShoperTel" />
        </view>
        <view class="logo">
            <view class="title">logo</view>
            <view class="tips">请上传门店logo</view>
            <view class="upload-box">
          <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadDon">
            <image class="img" mode="aspectFill" v-if="params.image" :src="params.image | imageFilter('w_240')"></image>
            <view v-else class="upload-btn flex-box">
              <my-icon type="ic_jia" size="96rpx" color="#d8d8d8"></my-icon>
            </view>
          </my-upload>
        </view>
        </view>
        <view class="submit" @click="submit">立即提交</view>
    </view>
</template>

<script>
    import myIcon from "../components/myIcon"
    import myUpload from "../components/form/myUpload"
	// import {checkUserStatus, showModal} from "../../common/index.js"
	export default {
		components:{
            myIcon,
            myUpload
        },
        computed: {
            position(){
                if (this.params.tx_lat&&this.params.tx_lng){
                    return  `${this.params.tx_lat},${this.params.tx_lng}`
                }else {
                    return '点击选取经纬度'
                }
            },
			
        },
		data() {
			return {
                index:0,
                params:{
                    tx_lat:'',//腾讯纬度
                    tx_lng:'',//腾讯经度
                    name:'',// 门店名称
                    introduce:'',// 门店介绍
                    address:'',// 门店地址
                    tel:"", //公司tel
                    manager_tel:'',//店长电话
                    manager_name:'',//店长电话
                    image:'',// 门店图片
                    company_id:"",
                },
				areaList:[{areaid:0,areaname:"请选择区域"}]
			};
		},
		onLoad(options){
            const {id}=options
            this.params.company_id =id
            this.getAreaList()
		},
		methods:{
			inputName(e){
                this.params.name =e.detail.value
            },
            inputTel(e){
                this.params.tel =e.detail.value
            },
            inputDesc(e){
                this.params.introduce =e.detail.value
            },
            inputAddress(e){
                this.params.address =e.detail.value
            },
            inputShoperName(e){
                this.params.manager_name =e.detail.value
            },
            inputShoperTel(e){
                this.params.manager_tel =e.detail.value
            },
            uploadDon(e){
                this.params.image=e.files.join(',')
            },
            GetLatLng(){
                let that = this;
                uni.chooseLocation({ //打开地理位置
                    success:(res)=>{ //调用成功打印回调
                    console.log(res);
                        that.params.tx_lat=res.latitude
                        that.params.tx_lng=res.longitude
                    },
                    fail:()=>{
                        that.params.tx_lat=''
                        that.params.tx_lng=''
                    }
                })
            },
            getAreaList(){
                this.$ajax.get('agentCompany/areaList',{},res=>{
					if(res.data.code === 1){
						this.areaList=[{areaid:0,areaname:"请选择区域"}].concat(res.data.list)
					}else{
						this.areaList=[{areaid:0,areaname:"请选择区域"}]
					}
				})
            },
            pickerChange(e){
                this.index=e.detail.value
                this.params.area_id =this.areaList[e.detail.value].areaid
                console.log(this.params.area_id);

            },
            submit(){
                this.$ajax.post('agentCompany/addAgentStore',this.params,res=>{
					if(res.data.code === 1){
						uni.showToast({
							title:res.data.msg
						})
						setTimeout(()=>{
                            uni.$emit("getDataAgain",{})
							this.$navigateBack()
						},2000)
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:'none'
						})
					}
				})
            }

		}
	}
</script>

<style lang="scss">
	.addshop{
        background: #fff;
        .imgs{
            width: 100%;
            height: 224upx;
            overflow: hidden;
            background-image: url('https://images.tengfangyun.com/images/new_icon/<EMAIL>');
            background-size: 100% 100%
            // image{
            //     width: 100%;
            // }
        }
        .info{
            padding: 0 48upx;
            .title{
                font-size: 40upx;
                color: #333333;
                font-weight: 600;
            }
            .tips{
                margin: 16upx 0 48upx;
                color: #999;
                font-size: 28upx;
            }
            input{
                padding: 24rpx;
                font-size: 28rpx;
                background-color: #f5f5f5;
                margin-bottom: 24upx;
            }
            picker{
                padding: 24rpx;
                font-size: 28rpx;
                background-color: #f5f5f5;
                margin-bottom: 24upx;
                color: #999;
                border: none;
            }
            .sub_lngLat{
                height: 40upx;
                padding: 24upx;
                font-size: 28rpx;
                background-color: #f5f5f5;
                margin-bottom: 24upx;
                color: #999;

            }
        }
        .logo{
            padding: 0 48upx;
            .title{
                font-size: 40upx;
                color: #333333;
                font-weight: 600;
            }
            .tips{
                margin: 16upx 0 48upx;
                color: #999;
                font-size: 28upx;
            }
            .upload-btn{
                width: 200rpx;
                height: 200rpx;
                align-items: center;
                justify-content: center;
                background: #F5F5F5

            }
            .img{
                width:200rpx;
                height:200rpx;
            }

        }
        .submit{
            margin: 106upx 48upx 24upx;
            padding: 28upx 0;
            text-align: center;
            background: #FB656A;
            color: #fff;
            font-size: 32upx;
            box-shadow: 0 4px 12px 0 rgba(251,101,106,0.40);
            border-radius: 22px;
        }

    }
</style>
