<template>
    <view class="tuiguang">
        <!-- <view class="header" :style="{ backgroundImage: `url(${bg_pic})` }">
            <block>
               
            </block>
        </view> -->
        <view class="tab-list flex-row bottom-line" :style="{ top: advTop }">
            <view class="tab-item" @click="switchTab('share_list')" :class="{ 'active': float == 0 }">付费推广</view>
            <view class="tab-item" @click="switchTab('news_list')" :class="{ 'active': float == 1 }">操作管理</view>
        </view>
        <view class="content">
            <view class="contents">
                <view class="flexbuju flex-row">
                    <view class="tuiguangfs " v-for="(item, index) in tab" :key="index" @click="tuiguangClick(item)">
                        <view :class="float == 1 ? 'tgbuju opreat' : 'tgbuju'">
                            <template>
                                <span v-if="item.type == 'member_upgrade'" class="youhui"></span>
                                <image class="tgimg" :src="item.icon"></image>
                                <span class="tgtitle" :class="item.type == 'member_upgrade' ? 'spacer' : ''">{{
                                    item.name
                                    }}</span>
                                <span v-if="item.type !== 'info_list'" class="tgmask">{{ item.desc }}</span>
                                <span v-else class="tgmask">{{ myonehouse.is_show ? `下架${catid == 1 || catid ==
                                    2 ? "房源" : "信息"}`
                                    : `上架${catid == 1 || catid == 2 ? "房源" : "信息"}` }}</span>
                            </template>

                        </view>
                    </view>
                </view>
            </view>

        </view>
        <!-- 下架弹层 -->
        <my-popup ref="xiajia">
            <view class="xaijia">
                <view class="title">请选择下架原因</view>
                <view class="status-list">
                    <view class="status-item bottom-line" @click="onSelectStatus(item)" v-for="item in xiajia_status"
                        :key="item.value">
                        <view class="flex-box price_form" v-if="item.value == 1 && show_price">
                            <input type="text" v-model="complate_price" placeholder="请输入成交价格">
                            <view class="unit">{{ catid == 1 ? '万' : '元/月' }}</view>
                            <view class="btn" @click.stop.prevent="subStatus(item)">提交</view>
                        </view>
                        <text v-else>{{ item.title }}</text>
                    </view>
                </view>
            </view>
        </my-popup>

        <!-- 单个刷新滑块验证 -->
        <sub-form ref="sub_form" @referbatch="referbatch" :refretype="refretype"
            @onereferbatch="onereferbatch"></sub-form>
    </view>
</template>
<script>
import subForm from '../../user/components/refreshsubForm.vue'
import myPopup from "../../components/myPopup.vue"
import pay from '../../components/pay.vue'
import { formatImg, showModal, config } from "../../common/index.js"
export default {
    components: {
        subForm,
        myPopup,
        pay
    },
    data() {
        return {
            float: 0,
            show_price: false, //是否显示填写下架成交价格
            complate_price: "",//下架成交价格
            xiajia_status: [],
            houseid: '',
            refretype: '',//one 单 
            uuid: '',
            verify_code: '',
            type: '', //type==1 为小区 type=='' 为新房
            bg_pic: '',
            tab: [

            ],
            opertion: [],
            arr: [],
            tabId: '',
            top_tip: {},
            zhidingId: '',
            zhidingInfo: {
                id: '',
                title: '',
                items: []
            },
            jingxuanId: -1,
            jingxuanInfo: {
                id: '',
                money_own: "",
                money: "",
                title: "",
                items: []
            },
            jingxuan_num: {},
            h5WxPayApi: "",
            h5AliPayApi: "",
            mpWxPayApi: "",
            pay_params: {},
            item_title: '',
            item_id: '',
            catid: '',
            parentid: '',
            geturl: '',
            vip_info: {

            },
        }
    },

    computed: {
        myonehouse() {
            return this.$store.state.myonehouse
        },
        advTop() {
            // #ifndef H5
            return 0
            // #endif
            // #ifdef H5
            return '44px'
            // #endif
        }
    },
    onLoad(options) {

        this.catid = parseInt(options.catid) || 1
        if (options.catid) {
            this.catid = parseInt(options.catid) || 1
            if (this.catid === 5) {
                this.geturl = '/commercial/manage_info?catid=' + this.catid
            } else {
                this.geturl = '/user/manage_info?cate_id=' + this.catid
            }
        }
        if (options.parentid) {
            this.parentid = parseInt(options.parentid) || 1
            this.geturl = '/commercial/manage_info?parentid=' + this.parentid
        }
        if (!this.myonehouse.id) {
            let self = this;
            uni.showModal({
                title: '温馨提示',
                content: '未获取到房源相关信息，请返回房源列表，再次进行操作！',
                showCancel: false, // 不显示取消按钮
                confirmText: '确定', // 确定按钮的文字
                success: function (res) {
                    if (res.confirm) {
                        self.gettourl();
                    }
                }
            });
        }


        this.gettgnav();
        this.getVipInfo();
        this.getXiajiaStatus();

    },
    // onShow(){
    //     if (this.$store.state.updatePageData&&this.login_status!=3){
    //         this.getData()
    //         this.$store.state.updatePageData=false 
    //     }
    // },
    // onUnload(){
    //     this.$store.state.updatePageData=false 
    // },
    methods: {
        switchTab(type) {
            if (type === 'share_list') {
                this.float = 0
                this.tab = this.arr
            }
            else {
                this.float = 1
                this.tab = this.opertion
            }
        },
        getVipInfo() {
            this.$ajax.get('member/memberShip.html', {}, res => {
                if (res.data.code === 1) {
                    this.vip_info = res.data.user
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
        gettourl() {
            setTimeout(() => {
                uni.redirectTo({
                    url: this.geturl
                });
            }, 1000);
        },
        onSelectStatus(item) {
            if (item.value === 1) {
                this.show_price = true
                return
            }
            this.handleXiajia(this.myonehouse.id, item.value)
        },
        subStatus(item) {
            this.handleXiajia(this.myonehouse.id, item.value)
            this.$refs.xiajia.hide()
        },
        // 执行下架接口
        handleXiajia(ids, status = "") {
            let params = { ids: ids, is_show: 0, complate_status: status }
            if (this.complate_price) {
                params.complate_price = this.complate_price
            }
            let api
            if (this.parentid || this.catid === 5) {
                api = 'estateRelease/setInfoBatchShow'
                this.sahngyexiajia(api, params);
            } else {
                api = 'release/setInfoBatchShow.html'
                this.ershoufangxiajia(api, params);
            }


        },
        // 商业下架
        sahngyexiajia(api, params) {
            this.$ajax.post(api, params, res => {
                if (res.data.code == 1) {
                    uni.showToast({
                        title: res.data.msg
                    })
                    this.gettourl();
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            })

        },
        ershoufangxiajia(api, params) {
            this.$ajax.get(api, params, res => {
                if (res.data.code == 1) {
                    uni.showToast({
                        title: res.data.msg
                    })
                    this.gettourl();
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            })
        },
        /**
         * 下架原因
         */
        getXiajiaStatus() {

            let api
            if (this.parentid || this.catid === 5) {
                api = 'estateRelease/complateStatus'
            } else {
                api = 'release/complateStatus'
            }
            this.$ajax.get(api, {}, res => {
                if (res.data.code === 1) {
                    this.xiajia_status = res.data.status
                }
            })
        },
        /**
         * 单个刷新
         */
        onereferbatch(e) {
            this.uuid = e.uuid;
            this.verify_code = e.verify_code;
            //单个刷新
            let data = {
                id: this.myonehouse.id, code: this.verify_code, uuid: this.uuid
            }
            let api
            if (this.parentid || this.catid === 5) {
                api = 'info_refresh/checkSingleEstateRefresh'
            } else {
                api = 'info_refresh/checkSingleRefresh'
            }
            this.$ajax.get(api, data, res => {
                if (res.data.code !== 1) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                    })
                    return
                }
                if (res.data.is_free === 1) {
                    this.onerefrebatshreal(this.myonehouse.id, this.uuid, this.verify_code)
                }
                if (res.data.is_free === 2) {
                    showModal({
                        title: "温馨提示",
                        content: res.data.msg,
                        confirm: () => {
                            this.onerefrebatshreal(this.myonehouse.id, this.uuid, this.verify_code)
                        }, cancel: () => {
                            console.log('取消');
                        }
                    })
                }
                if (res.data.is_free === 3) {
                    // 提示积分不足,需要去充值
                    showModal({
                        title: "温馨提示",
                        content: res.data.msg,
                        confirm: () => {
                            this.$navigateTo('/user/recharge?type=2')
                        }
                    })
                }

            }, err => {
                this.refreshing = false
                uni.showToast({
                    title: '请重新操作',
                    icon: 'none',
                    duration: 2500,
                })
            })
        },
        onerefrebatshreal(id, uuid, code) {
            let api
            if (this.parentid || this.catid === 5) {
                api = 'info_refresh/singleEstateRefresh'
                this.shangyerefre(api, id, uuid, code)
            } else {
                api = 'info_refresh/singleRefresh'
                this.ershoufangrefre(api, id, uuid, code)
            }


        },
        ershoufangrefre(api, id, uuid, code) {
            uni.showLoading({
                title: "刷新中..."
            })
            this.$ajax.get(api, { id: id, uuid: uuid, code: code }, (res) => {
                uni.hideLoading()
                if (res.data.code == 1) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2500,
                    })
                    setTimeout(() => {
                        //返回页面
                        this.gettourl();
                    }, 2000)
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            }, (err) => {

            }, false)
        },
        shangyerefre(api, id, uuid, code) {
            uni.showLoading({
                title: "刷新中..."
            })
            this.$ajax.post(api, { id: id, uuid: uuid, code: code }, (res) => {
                uni.hideLoading()
                if (res.data.code == 1) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2500,
                    })
                    setTimeout(() => {
                        //返回页面
                        this.gettourl();
                    }, 2000)
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            }, (err) => {

            }, false)
        },
        huakuaishow() {
            this.refretype = 'one'
            this.$refs.sub_form.showPopup();
            this.$refs.sub_form.getVerify(); // 显示组件时执行 getVerify 方法
        },
        gettgnav() {
            const self = this;
            this.$ajax.get('PayGuide/nav', { id: self.myonehouse.id }, res => {
                if (res.data.code === 1) {
                    if (res.data.data) {
                        //获取背景图 res.data.data.bg_pic
                        self.bg_pic = res.data.data.bg_pic
                        //获取navs res.data.data.navs
                        self.arr = res.data.data.navs
                        self.opertion = res.data.operate
                        //求租求购没有塞红包
                        if (self.catid == 3 || self.catid == 4) {
                            self.arr = self.arr.filter(item => item.type !== 'info_red_envelope');
                        }
                        self.tab = self.arr

                    }
                }
            })
        },
        tuiguangClick(item) {
            const self = this;
            this.$store.state.tempData = self.detial


            //判断是二手房还是商业地产
            let data
            if (self.parentid) {
                data = `id=${self.myonehouse.id}&parentid=${self.parentid}`
            } else {
                data = `id=${self.myonehouse.id}&catid=${self.catid}`
            }

            if (item.type == "info_refresh") {
                //进行单个刷新
                if (self.myonehouse.id) {
                    self.huakuaishow();
                }
                else {
                    uni.showToast({
                        title: '未获取到房源相关信息，请返回房源列表，再次进行操作！',
                        icon: 'none'
                    })
                    return
                }

            }
            else {
                if (item.type == "info_list") {
                    //判断是否是下架状态
                    if (!self.myonehouse.is_show == 0) {
                        //进行房源下架操作
                        self.$refs.xiajia.show()
                        return
                    }
                }
                else if (item.type == "info_red_envelope") {
                    if (self.catid == 5 || self.parentid) {
                        data = `id=${self.myonehouse.id}&type=3`
                    } else {
                        data = `id=${self.myonehouse.id}&type=2`
                    }

                }
                //推广套餐
                else if (item.type == "member_upgrade" && this.vip_info.levelid === 1) {
                    data = `is_personal=${this.vip_info.levelid}`
                }
                let url = item.path.includes('?') ? '/' + item.path + `&${data}` : '/' + item.path + `?${data}`
                self.$navigateTo(url)
            }
        },
        checkShuaxin(id) {
            uni.showLoading({
                title: "刷新中..."
            })
            this.$ajax.get('member/checkRefresh', { id }, res => {
                if (res.data.code !== 1) {
                    uni.hideLoading();
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                    return
                }
                if (res.data.is_free === 1) {
                    // 可以免费刷新,直接刷新
                    this.shuaxin(id)
                }
                if (res.data.is_free === 2) {
                    // 提示需要使用积分刷新
                    uni.hideLoading();
                    showModal({
                        title: "温馨提示",
                        content: res.data.msg,
                        confirm: () => {
                            this.shuaxin(id)
                        }
                    })
                }
                if (res.data.is_free === 3) {
                    // 提示积分不足,需要去充值
                    uni.hideLoading();
                    showModal({
                        title: "温馨提示",
                        content: res.data.msg,
                        confirm: () => {
                            this.$navigateTo('/user/recharge?type=2')
                        }
                    })
                }
            }, err => {
                this.refreshing = false
            })
        },
        shuaxin(id) {
            this.$ajax.get('member/informationRefresh.html', { id }, (res) => {
                if (res.data.code == 1) {
                    uni.hideLoading();
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2500,
                    })
                    //返回页面
                    setTimeout(() => {
                        this.$navigateBack()
                    }, 3000)

                } else {
                    uni.hideLoading();
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            }, (err) => {

            }, false)
        },




        stopMove() { },





    }



}
</script>
<style lang="scss" scoped>
.flex-row {
    flex-direction: row;
}

.tuiguang {
    min-height: 100vh;

    .header {
        height: 400rpx;
        width: 100%;
        padding: 0 48rpx;
        position: relative;
        box-sizing: border-box;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        //background-image: url(@/static/icon/tuiguang.png);

        .title {
            font-size: 80rpx;
            color: #333;
            padding-top: 220rpx;
            font-weight: bold;
            font-family: "Alibaba PuHuiTi";
            font-weight: 700;

        }


    }

    .content {
        width: 100%;
        flex-shrink: 0;
        border-radius: 40rpx;
        border: 2rpx solid rgba(255, 255, 255, 0);
        border-bottom: none;
        border-bottom-left-radius: 0rpx;
        border-bottom-right-radius: 0rpx;
        position: absolute;
        top: 68rpx;

        .contents {
            padding: 14rpx;

            .title {
                color: #4E5969;
                font-family: "PingFang SC";
                font-size: 28rpx;
                font-style: normal;
                font-weight: 500;
                line-height: 44rpx;
                text-align: left;
            }

            .flexbuju {
                width: 100%;
                // height: 540rpx;
                flex-shrink: 0;
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-start;

                .tuiguangfs {
                    text-align: center;
                    width: 30%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin: 0rpx 10rpx;

                    .tgbuju {
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        gap: 20rpx;
                        flex-shrink: 0;
                        border-radius: 12px;
                        border: 1px solid rgba(255, 255, 255, 0);
                        background: linear-gradient(180deg, rgba(255, 170, 169, 0.09) 0%, rgba(255, 229, 229, 0.19) 100%);
                        height: 256rpx;
                        width: 100%;
                        margin-top: 46rpx;


                        .tgimg {
                            width: 96rpx;
                            height: 96rpx;
                        }

                        .tgtitle {
                            color: #131315;
                            font-size: 28rpx;
                            font-weight: 400;
                        }

                        .tgmask {
                            font-size: 24rpx;
                            color: #86909C;
                        }

                        .spacer {
                            padding-bottom: 32rpx;
                        }
                    }

                    .opreat {
                        background: #fff !important;
                    }


                    .youhui {
                        position: absolute;
                        right: 0;
                        top: 0;
                        background-image: url("https://images.tengfangyun.com/icon/pay/tg_yh.png");
                        background-repeat: no-repeat;
                        background-size: 100%;
                        background-position: right top;
                        width: 64rpx;
                        height: 64rpx;

                    }

                }
            }
        }

    }

    .info_title {
        padding: 16rpx 48rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .tip {
        font-size: 22rpx;
        color: #999;
    }

    .tip .highlight {
        color: $uni-color-primary;
        font-size: $uni-font-size-lg;
    }

    .scroll {
        margin-top: 32rpx;
        height: 136rpx;

        &.murows {
            height: 176rpx;
        }
    }

    .day_list {
        justify-content: space-between;
        flex-wrap: wrap;
        overflow-y: scroll;

        .day_item {
            line-height: 1;
            margin-bottom: 24rpx;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            height: 112rpx;
            min-width: 152rpx;
            text-align: center;
            border-radius: 4rpx;
            border: 1rpx solid #d8d8d8;
            max-width: 152rpx;

            &.vacancy {
                border: 0;
                margin: 0;
                padding: 0;
                height: 0;
            }

            &.active {
                background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
                border-color: $uni-color-primary;

                .day {
                    color: $uni-color-primary;
                }

                .money {
                    background-color: $uni-color-primary;
                    color: #fff;
                }
            }

            .day {
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #666;

                .value {
                    font-size: 40rpx;
                    font-weight: bold;

                    &.small {
                        font-size: 32rpx;
                        font-weight: initial;
                    }
                }

                .unit {
                    margin-bottom: -8rpx;
                    margin-left: 10rpx;
                    font-size: 22rpx;
                }
            }

            .money {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                padding: 4rpx 10rpx;
                font-size: 22rpx;
                color: #999;
                background-color: #d8d8d8;
            }
        }
    }

    .btn-box {
        padding: $uni-spacing-row-base 0;
    }

    .btn-box .btn.btn-lg {
        width: 100%;
        border-radius: 10upx;
        height: 88upx;
        text-align: center;
        line-height: 88upx;
        box-sizing: border-box;
        font-size: 32rpx;
        color: #fff;
        background: #FB656A;
        box-shadow: 0 4px 16px 0 rgba(251, 101, 106, 0.40);
        border-radius: 44rpx;
    }

    .btn-box .crude {
        width: 100%;
        line-height: 88rpx;
        text-align: center;
        color: #999;
        margin-bottom: 24rpx;
    }

    .xaijia {
        background-color: #f7f7f7;

        .title {
            padding: 24rpx;
            font-size: 30rpx;
            text-align: center;
            color: #666;
        }

        .status-list {
            background-color: #fff;
        }

        .status-item {
            padding: 24rpx;
            text-align: center;
        }

        .price_form {
            justify-content: center;
            align-items: center;

            input {
                font-size: 28rpx;
                border: 1rpx solid #f2f2f2;
                padding: 8rpx;
                width: 220rpx;
            }

            .unit {
                font-size: 26rpx;
                margin-left: 16rpx;
            }

            .btn {
                margin-left: 24rpx;
                padding: 10rpx 24rpx;
                border-radius: 8rpx;
                font-size: 26rpx;
                background-color: $uni-color-primary;
                color: #fff;
            }
        }
    }

}

.tab-list {
    padding: 0 48rpx;
    display: flex;
    justify-content: space-between;
    position: sticky;
    background-color: #fff;
    z-index: 2;

    .tab-item {
        flex: 1;
        padding: 24rpx;
        text-align: center;
        position: relative;
        color: #666;

        &.active {
            color: black;

            &::after {
                content: '';
                height: 8rpx;
                border-radius: 4rpx;
                background-color: $uni-color-primary;
                position: absolute;
                bottom: 0;
                width: 48rpx;
                left: 0;
                right: 0;
                margin: auto;
            }
        }
    }
}

.lists-box {
    padding: 24rpx 48rpx;
    padding-bottom: 120rpx;

    .adviser-box {
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 0;
        margin-bottom: 24rpx;

        .prelogo {
            margin-right: 24rpx;
            width: 54rpx;
            height: 54rpx;
            border-radius: 27rpx;
            background-color: #f5f5f5;
        }

        .adviser-info {
            flex: 1;
            overflow: hidden;
            line-height: 1;

            .name {
                align-items: center;
                font-size: 26rpx;
                margin-bottom: 16rpx;

                .text {
                    margin-right: 16rpx;
                    display: inline-block;
                    max-width: 180rpx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }

            .level_icon {
                width: 28rpx;
                height: 28rpx;
            }

            .level {
                font-size: 24rpx;
                color: #999;
            }
        }

        .btn-list {
            line-height: 1;

            .btn {
                margin-left: 24rpx;
                padding: 16rpx 16rpx;
                min-width: 108rpx;
                text-align: center;
                color: $uni-color-primary;
                border: 1rpx solid $uni-color-primary;
                border-radius: 8rpx;
                box-shadow: 0 2px 4px 0 rgba(251, 101, 106, 0.1);
            }
        }
    }
}
</style>