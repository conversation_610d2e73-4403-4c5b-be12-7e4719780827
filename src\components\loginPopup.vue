<template>
<view>
    <my-popup ref="loginpopup" position="center" :height="login_box_height+'px'" :touch_hide="false">
        <view class="login_box" id="login_box">
            <view class="close_bar">
                <view class="close" @click="closeSub()">
                    <my-icon type="ic_guanbi" size="42rpx" color="#999"></my-icon>
                </view>
            </view>
            <view class="sub_title">{{sub_title}}</view>
            <view class="sub_content">{{sub_content}}</view>
            <view class="sub_form">
                <input class="sub_tel" maxlength="11" type="number" placeholder="请输入手机号" @input="inputPhone" />
                <view class="flex-box sms_code_inp">
                    <input class="flex-1" type="text" placeholder="请输入验证码" @input="inputCode" />
                    <view class="send-code" :class="sending?'disable':''" @click="getVerify()">{{time?time+'s':'获取验证码'}}</view>
                </view>
                <view class="btn-box">
                    <button class="default" @click="subData()">登录</button>
                </view>
            </view>
            <view class="verify_block" v-show="show_verify">
                <drag-verify ref="verify" :verify_img="verify_img" :verify_fail="verify_fail" :verify_success="verify_success" @ondragend="onDragEnd" @onrefreshend="onRefreshEnd" @onrefresh="onRefresh"></drag-verify>
            </view>
        </view>
    </my-popup>
</view>
</template>

<script>
import myPopup from './myPopup.vue'
import myIcon from './myIcon.vue'
import dragVerify from "./dragVerify.vue"
export default {
    data() {
        return {
            sending: false,
            login_box_height: 'initial',
            time: 0,
            show_verify: false,
            code_token: '',
            verify_img: '',
            verify_fail: false,
            verify_success: false,
            show_sms_code:false
        }
    },
    props: {
        sub_title: {
            type: [String],
            default: '温馨提示'
        },
        sub_content: {
            type: [String],
            default: '当前操作需要绑定手机号，请输入您的手机号'
        },
        login_success_tip:{
            type:Boolean,
            default:true
        }
    },
    components: {
        myIcon,
        myPopup,
        dragVerify
    },
    methods: {
        showPopup() {
            this.$nextTick(()=>{
                const query = uni.createSelectorQuery().in(this);
                setTimeout(() => {
                    query.select('#login_box').boundingClientRect(data => {
                        this.login_box_height = data.height
                        this.$refs.loginpopup.show()
                    }).exec(); 
                }, 50);
               
            })
        },
        inputName(e) {
            this.name = e.detail.value
        },
        inputPhone(e) {
            this.tel = e.detail.value
        },
        inputCode(e) {
            this.sms_code = e.detail.value
        },
        closeSub() {
            this.$refs.loginpopup.hide()
            this.show_verify = false
            this.$emit('onclose')
        },
        // 获取滑块验证码
        getVerify() {
            if (this.sending) {
                return
            }
            if (!this.tel) {
                uni.showToast({
                    title: '请输入手机号',
                    icon: 'none'
                })
                return
            }
            this.$ajax.get('member/slideToken', {}, res => {
                if (res.data.code === 1) {
                    this.verify_img = res.data.url
                    this.code_token = res.data.imgCode
                    this.show_verify = true
					// #ifdef APP-PLUS
					if(uni.getSystemInfoSync().platform == 'ios'){
						plus.key.hideSoftKeybord()
					}
					// #endif
					
                }
            }, err => {

            })
        },
        onRefresh(){
            this.getVerify()
        },
        // 滑块验证码重置完成的回调
        onRefreshEnd() {
            this.verify_success = false
            this.verify_fail = false
        },
        // 用户滑动验证码结束的回调
        onDragEnd(value){
            this.sendSmsCode(value)
        },
        sendSmsCode(verify_code) {
            if(!verify_code){
                uni.showToast({
                    title:"请先滑动验证",
                    icon:"none"
                })
                return
            }
            if(!this.tel){
                uni.showToast({
                    title:'请输入手机号',
                    icon:'none'
                })
                return
            }
            if(this.tel.length<11||this.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return
            }
            this.$ajax.get('member/checkSlideCode', {
                tel: this.tel,
                imgCode: this.code_token,
                code: verify_code
            }, res => {
                if (res.data.code === 1) {
                    this.verify_success = true
                    this.sms_token = res.data.code_token || ''
                    uni.showToast({
                        title: res.data.msg
                    })
                    // 显示填写短信验证码
                    this.show_sms_code = true
                    this.$nextTick(()=>{
                        const query = uni.createSelectorQuery().in(this);
                        setTimeout(() => {
                            query.select('#login_box').boundingClientRect(data => {
                            this.login_box_height = data.height
                        }).exec();
                        // 隐藏拖动验证码
                        this.show_verify = false
                        }, 50);
                        
                    })
                    this.time = 60
                    this.sending = true
                    if (this.timer) {
                        clearInterval(this.timer)
                    }
                    this.timer = setInterval(() => {
                        if (this.time <= 0) {
                            clearInterval(this.timer)
                            this.sending = false
                            return
                        }
                        this.time--
                    }, 1000)
                } else {
                    this.verify_fail = true
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                    this.getVerify()
                }
            }, err => {
                this.verify_fail = true
                uni.showToast({
                    titley: '验证失败',
                    icon: 'none'
                })
                this.getVerify()
            })
        },
        subData(){
            if(!this.tel){
                uni.showToast({
                    title:'请输入手机号',
                    icon:'none'
                })
                return
            }
            if(this.tel.length<11||this.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return
            }
            if(this.show_sms_code){
                this.authRegister()
            }else{
                // this.$emit('onsubmit',{tel:this.tel,sms_code:this.sms_code})
                // this.getVerify()
                if(this.show_verify){
                    uni.showToast({
                        title:'请先拖动滑块进行验证',
                        icon:'none'
                    })
                }else{
                    this.authRegister()
                }
            }
        },
        authRegister(){ //一键登录
            if(!this.sms_code){
                uni.showToast({
                title:'请输入短信验证码',
                icon:'none'
                })
                return
            }
            this.$ajax.get('member/authRegister',{tel:this.tel,code_token:this.sms_token,code:this.sms_code},res=>{
                if (res.data.token) {
                    uni.setStorageSync('token', res.data.token)
                }
                if(res.data.code === 1){
                    this.$refs.loginpopup.hide()
                    this.show_verify = false
                    if(this.login_success_tip){
                        uni.showToast({
                            title:'提交成功,请继续操作',
                            icon:'none'
                        })
                    }
                    this.$store.state.user_login_status = 3
                    this.$emit('success',{token:res.data.token,msg:res.data.msg})
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                    this.$emit('error',{msg:res.data.msg})
                }
            })
        },
    }
}
</script>

<style scoped lang="scss">
.login_box{
  background-color: #fff;
  padding: 30rpx 40rpx;
  margin: 0 40rpx;
  border-radius: 5rpx;
  position: relative;
  overflow-y: hidden;
  .close_bar{
    text-align: right;
    .close{
      display: inline-block;
      padding: 15rpx;
      margin-top: -5px;
      margin-right: -10px;
    }
  }
  .sub_title{
    margin-bottom: 20rpx;
    font-size: 44rpx;
    font-weight: bold;
    color: #444;
  }
  .sub_tip{
    font-size: 24rpx;
    color: #888;
    margin-bottom: 40rpx;
    text{
      color: $uni-color-primary;
    }
  }
  .sub_content{
    font-size: 32rpx;
    line-height: 1.5;
    color: #333;
  }
  .sub_form{
    margin-top: 25rpx;
    .sms_code_inp{
      align-items: center;
    }
    .sub_tel{
      margin-bottom: 20rpx;
    }
    input{
      padding: 20rpx;
      border: 1rpx solid #f3f3f3;
    }
    .send-code{
      margin-left: 10rpx;
      color: $uni-color-primary;
      &.disable{
        color: #888;
      }
    }
    .btn-box{
      padding: 10px 0;
    }
  }
  .verify_block{
    position: absolute;
    left: 0;
    right: 0;
    top: 80rpx;
    bottom: 160rpx;
    background-color: #fff;
    z-index: 2
  }
}

</style>
