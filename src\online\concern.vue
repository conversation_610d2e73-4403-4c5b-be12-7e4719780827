<template>
<view class="concern">
    <view class="concern_list">
        <view class="build" v-for="(build,index) in concern_list" :key="index">
            <view class="title bottom-line"><text>{{build.title}}</text></view>
            <choose-house :house_list="build.houses" :show_ceng="false" show_del @onClick="handleClick" @onDel="handleDel"></choose-house>
        </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
</view>
</template>

<script>
import chooseHouse from '../components/chooseHouse.vue'
import {
    navigateTo,
    showModal
} from '../common/index.js'
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
    data() {
        return {
            concern_list:[],
            params:{
                page:1,
                rows:10
            },
            get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
        }
    },
    components: {
        chooseHouse,
        uniLoadMore
    },
    onLoad(){
        this.getData()
    },
    methods:{
        getData(){
            if(this.params.page == 1){
                this.ceng_list=[]
            }
            this.get_status = "loading"
            this.$ajax.get('online_my/followHouseLists',this.params,res=>{
                if (res.data.code === 1){
                    this.concern_list = res.data.lists
                    if(res.data.lists.length<this.params.rows){
                        this.get_status = "noMore"
                    }else{
                        this.get_status = "more"
                    }
                }else{
                    this.get_status = "noMore"
                }
            })
        },
        handleClick(e){
            navigateTo(`/online/house_detail?id=${e.house_id}&online_id=${e.build_online_id}`)
        },
        handleDel(e){
            showModal({
                content:"确定要取消关注吗？",
                confirm:res=>{
                    this.delEr(e.house_id)
                }
            })
        },
        delEr(house_id){
            this.$ajax.post('online_my/followHouseCancel',{house_id},res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title:res.data.msg||'操作成功'
                    })
                    this.params.page = 1
                    this.getData()
                }else{
                    uni.showToast({
                        title:res.data.msg||'操作失败',
                        icon: 'none'
                    })
                }
            })
        }
    },
    onReachBottom(){
        this.params.page++
        this.getData()
    },
}
</script>

<style scoped lang="scss">
.title{
    padding: 20rpx 20rpx 20rpx 40rpx;
    font-size: 32rpx;
    position: relative;
    background-color: #fff;
    &::before{
        content: "";
        position: absolute;
        left:20rpx;
        top:20rpx;
        bottom:20rpx;
        width: 6rpx;
        background-color: #f65354
    }
}
</style>
