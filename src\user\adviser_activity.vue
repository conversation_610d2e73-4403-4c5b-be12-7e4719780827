<template>
	<view id="recharge">
    <!-- 顶部卡片 -->
    <view class="header">
      <image class="bg_img" :src="'/images/new_icon/<EMAIL>' | imageFilter('m_320')" mode="aspectFill"></image>
      <view class="info">
        <view class="title">我的活跃度</view>
        <view class="integral">{{activity}}</view>
        <view class="btn" @click="$navigateTo('/user/adviser_upgrade')">兑换特权</view>
      </view>
    </view>

    <!-- 任务列表 -->
    <view class="task-box" v-if="task_list.length>0">
      <view class="title-row flex-row">
        <text class="title">今日任务</text>
        <!-- <view class="task_num flex-row">
          <text>任务数</text>
          <text class="complete_num">{{complete_num}}</text>
          <text>/{{task_list.length}}</text>
        </view> -->
      </view>
      <view class="task_list">
        <view
          class="task_item bottom-line flex-row"
          hover-class="on_hover"
          v-for="(item, index) in task_list"
          :key="index"
          @click="doTask(item.path)"
        >
          <view class="task_info">
            <view class="task_title">{{ item.name }}</view>
          </view>
          <view class="task_icon">
            <image :src="item.give_type | iconFormat"></image>
          </view>
          <text class="num">+{{ item.give_num }}</text>
          <!--<view class="task_btn">去完成</view>-->
          <my-icon v-if="item.status" type="ic_qiandaowancheng" color="#72dd66" size="36rpx"></my-icon>
          <my-icon v-else type="jinru" color="#ffffff"></my-icon>
        </view>
      </view>
    </view>


    <!-- 活跃度记录 -->
    <view class="logs">
      <view class="log-item" :class="{mgt20:index>0&&parseInt(item.month)==parseInt(logs[index-1].month)}" v-for="(item, index) in logs" :key="item.id">
        <text class="month" v-if="index===0||parseInt(item.month)<parseInt(logs[index-1].month)">{{item.month}}月</text>
        <view class="detail">
          <view class="service flex-row">
            <text class="name">{{item.subject}}</text>
            <text v-html="item.paycost"></text>
          </view>
          <view class="time">{{item.pubtime}}</view>
        </view>
      </view>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
	</view>
</template>

<script>
import {uniLoadMore} from '@dcloudio/uni-ui'
	export default {
    components: {
      uniLoadMore
    },
		data() {
			return {
        get_status:"loading",
        content_text:{
          contentdown:"",
          contentrefresh:"正在加载...",
          contentnomore:"没有更多数据了"
        },
				activity:0,
        task_list:[],
        logs:[],
        params:{
          page:1,
          rows:20
        }
			};
		},
		onLoad(options){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			this.getData()
		},
		methods:{
			getData(){
        if(this.params.page == 1){
          this.list=[]
        }
        this.get_status = "loading"
				this.$ajax.get('adviser/activeLog.html',this.params,res=>{
					if(res.data.code == 1){
            this.activity = res.data.active
              if(res.data.list.length<this.params.rows){
              this.get_status = "noMore"
            }else{
              this.get_status = "more"
            }
            this.logs = this.logs.concat(res.data.list)
          }else{
            this.get_status = "noMore"
          }
				})
			}
    },
    onReachBottom(){
      if(this.get_status !== "more"){
        return
      }
      this.params.page ++
      this.getData()
    }
	}
</script>

<style lang="scss">
#recharge{
  background-color: #fff;
  min-height: calc(100vh - 44px);
}
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row{
  flex-direction: row;
}

.header {
  margin: 24rpx 48rpx 48rpx 48rpx;
  height: 278rpx;
  position: relative;
  border-radius: 30rpx;
  overflow: hidden;
  .bg_img {
    width: 100%;
    height: 100%;
    background-image: linear-gradient(180deg, #ff676c 0%, #fe8e8d 100%);
  }
  .info {
    position: absolute;
    left: 48rpx;
    top: 48rpx;
    .title {
      line-height: 1;
      margin-bottom: 10rpx;
      font-size: 22rpx;
      color: #fff;
    }
    .integral{
      font-size: 64rpx;
      color: #fff;
    }
    .btn{
      margin-top: 24rpx;
      line-height: 48rpx;
      padding: 0 24rpx;
      border-radius: 24rpx;
      font-size: 22rpx;
      border: 1rpx solid #fff;
      color: #fff;
    }
  }
}

// 任务
.task-box {
  .title-row {
    justify-content: space-between;
    align-items: flex-end;
    padding: 20rpx 48rpx;
    background-color: #fff;
    .title {
      font-size: 40rpx;
      font-weight: bold;
    }
    .task_num {
      font-size: 22rpx;
    }
    .complete_num{
        color: $uni-color-primary;
    }
  }
  .task_list {
    padding: 0 48rpx;
    background: #fff;
  }

  .task_item {
    display: flex;
    align-items: center;
    padding: 20upx 24upx;

    .task_icon {
      width: 70upx;
      min-width: 70upx;
      height: 70upx;
      margin-right: 10upx;
      image {
        width: 100%;
        height: 100%;
      }
    }

    .task_info {
      flex: 1;
      overflow: hidden;
      margin-right: 20upx;

      .task_title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 30upx;
      }

      .task_note {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 24upx;
        color: #666;
      }
    }

    .num {
      margin-right: 10upx;
      font-size: 28upx;
      display: inline-block;
      width: 96upx;
      color: #ff6b9c;
    }

    .task_btn {
      padding: 5upx 20upx;
      height: 50upx;
      box-sizing: border-box;
      line-height: 40upx;
      border-radius: 10upx;
      font-size: 26upx;
      background: #f00;
      color: #fff;
    }
  }
}

// 活跃度记录
.logs{
  background-color: #f5f5f5
}
.log-item{
  margin-bottom: 0;
  &.mgt20{
    margin-top: 20rpx;
  }
  .month{
    padding: 24rpx 48rpx;
  }
  .detail{
    padding: 24rpx 48rpx;
    background-color: #fff;
    ~.detail{
      margin-top: 20rpx;
    }
    .service{
      justify-content: space-between;
      margin-bottom: 16rpx;
      overflow: hidden;
      .name{
        font-weight: bold;
        margin-right: 24rpx;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .time{
      font-size: 24rpx;
      color: #999;
    }
  }
}
</style>
