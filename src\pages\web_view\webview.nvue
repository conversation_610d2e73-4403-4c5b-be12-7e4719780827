<template>
	<view>
		<web-view :src="url" :webview-styles="webviewStyles" @message="handleMessage"></web-view>
	</view>
</template>

<script>
	import { navigateTo,formatImg } from '../../common/index.js'

    
	export default {
		data() {
			return {
				url:"",
				share:{},
				title:"",
				webviewStyles: {
                    progress: {
                        color: '#f65354'
                    }
                }
			}
		},
		onLoad(options){
			console.log(options)
			if(options.url){
				console.log(options.url,11112)
				if(options.url.includes('@@@')){
					const regex = new RegExp('@@@', 'gi');
					const regex2 = new RegExp('@@', 'gi');
					this.url = decodeURI(options.url).replace(regex,'?').replace(regex2,'=')
					console.log(this.url)
					return
				}
				this.url = decodeURIComponent(options.url)
				console.log(this.url)
			}
			if(options.path){
				console.log(options.path,122345)
				if(options.path.includes('@@@')){
					const regex = new RegExp('@@@', 'gi');
					const regex2 = new RegExp('@@', 'gi');
					this.url = decodeURI(options.path).replace(regex,'?').replace(regex2,'=')
					console.log(this.url)
					return
				}
				
				this.url = decodeURIComponent(options.path)
				console.log(this.url)	
			}
			
		},
		filters: {
			imgUrl(val, param = "") {
				return formatImg(val, param)
			}
		},
		methods: {
			handleMessage(e){
				console.log(e);
				
				if(e.type='navigate'&&e.url){
					navigateTo(e.url)
					return
				}
				if(e.fun){
					e.fun()
					return
				}
				if(e.detail.data[0].type=="share"){
					this.share= e.detail.data[0].share
					this.title= e.detail.data[0].title
					// uni.setNavigationBarTitle({
					// 	title:this.title||""
					// })
					return
				}
				
			}
		},
		onShareAppMessage: function (res) {
			console.log(this.url)
			let urlArr = this.url.split("?")
			let ids =urlArr[1].split("=")
			let url =urlArr[0]+"@@@"+ids[0]+"@@"+ids[1]
			return {
				title:this.share.title,
				content:this.share.content,
				imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):"",
				path:'/pages/web_view/webview?path='+decodeURIComponent(url)
			}
			
		}
	}
</script>

<style>

</style>
