/**
 * <AUTHOR>
 * @date 2020-04-21 10:19:07
 * @desc 常用工具
 */


// 判断是不是数组
const isArray =
  Array.isArray ||
  function (obj) {
    return obj instanceof Array
  }

/**
 * <AUTHOR>
 * @date 2020-05-09 15:49:10
 * @desc 动态加载远程js文件
 */
const loadRemoteJs = function (src, id) {
  return new Promise(resolve => {
    id && document.getElementById(id) && document.getElementById(id).remove()
    var script = document.createElement('script')
    script.src = src
    if (id) {
      script.id = id
    }
    var s = document.getElementsByTagName('script')[0]
    s.parentNode.insertBefore(script, s)
    if (script.readyState) {
      script.onreadystatechange(() => {
        if (
          script.readyState === 'complete' ||
          script.readyState === 'loaded'
        ) {
          resolve()
        }
      })
    } else {
      script.onload = function () {
        resolve()
      }
    }
  })
}

/**
 * <AUTHOR>
 * @date 2020-05-11 11:59:51
 * @desc 将16进制颜色转换成rgba
 */
const hexColorToRgba = function (hexColor, alphaMaxVal = 1) {
  if (hexColor.indexOf('#') !== 0) {
    return hexColor
  }
  hexColor = hexColor.replace('#', '')

  // 用于分割16进制色彩通道
  const reg = new RegExp('\\w{1,2}', 'g')
  // 分割颜色通道
  let rgbaArray255 = hexColor.match(reg)
  rgbaArray255 = rgbaArray255.map((channel, index) => {
    // 计算每个通道的10进制值
    const colorVal = parseInt(channel, 16)
    if (index === 3) {
      // 这是alpha通道
      return Math.round((colorVal / (255 / alphaMaxVal)) * 100) / 100
    }
    return colorVal
  })
  return rgbaArray255
  // return 'rgba(' + rgbaArray255.join(',') + ')'
}

module.exports = {
  isArray,
  loadRemoteJs,
  hexColorToRgba
}
