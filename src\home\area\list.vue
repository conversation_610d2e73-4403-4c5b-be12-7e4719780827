<template>
<view class="page">
    <!-- #ifdef MP-WEIXIN || MP-BAIDU -->
    <view :class="bgwhite==true?'top flex-box bgwhite':'top flex-box'">
        <view class="c-left"></view>
        <view class="inp-box-def search-box flex-1">
            <uni-icons type="search" color="#666666" size="26"></uni-icons>
            <input type="text" v-model="keywords" confirm-type="search" @confirm="handleSearch" placeholder="你家在哪里？"  />
        </view>
        <view class="c-right"></view>
    </view>
    <!-- #endif -->
    <my-swiper :focus="focus" :autoplay="true" :interval="4000" :indicatorDots="true" :circular="true" indicatorActiveColor="#f65354" height="44vw"></my-swiper>
    <tab-bar :tabs="navs" :nowIndex="currentIndex" :fixedTop="false" @click="switchTab"></tab-bar>
    <view class="list-box top-20">
        <view class="list-item flex-box bottom-line" v-for="item in listsData" :key="item.id" @click="toDetail(item)">
            <view class="img-box">
                <image :src="item.pre_img_path | imgUrl" mode="aspectFill"></image>
            </view>
            <view class="list-info flex-1">
                <view class="info-title">{{item.name}}</view>
                <view class="label">案例：<text class="green">{{item.count}}</text>工地：<text class="green">{{item.gdcount}}</text>热度：<text class="orange">{{item.clicksum}}</text></view>
                <view class="address">{{item.address||"　"}}</view>
            </view>
        </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
</view>
</template>

<script>
import mySwiper from '../../components/mySwiper'
import tabBar from '../../components/tabBar'
import {
    uniIcons,
    uniLoadMore
} from '@dcloudio/uni-ui'
import {
    navigateTo,
    formatImg
} from '../../common/index'
import {wxShare} from '../../common/mixin'
export default {
    data() {
        return {
            focus: [],
            navs: [{
                areaid: 0,
                name: "全部"
            }],
            listsData: [],
            keywords:"",
            get_status: "loading",
            content_text: {
                contentdown: "",
                contentrefresh: "正在加载...",
                contentnomore: "没有更多数据了"
            },
            currentIndex:0
        }
    },
    mixins:[wxShare],
    components: {
        mySwiper,
        tabBar,
        uniLoadMore,
        uniIcons
    },
    onLoad() {
        this.params = {
            page: 1,
            rows: 20,
            areaid: "",
            name: ""
        }
        this.getFocus()
        this.getNavs()
        this.getData()
    },
    filters:{
        imgUrl(val){
            return formatImg(val,'w_320')
        }
    },
    methods: {
        getData() {
            if (this.params.page == 1) {
                this.listsData = []
            }
            this.get_status = "loading"
            this.$ajax.get('memberShop/myHome', this.params, res => {
                console.log(res.data)
                if (res.data.code == 1) {
                    this.listsData = this.listsData.concat(res.data.list)
                    if (res.data.list.length < this.params.rows) {
                        this.get_status = "noMore"
                    } else {
                        this.get_status = "more"
                    }
                } else {
                    this.get_status = "noMore"
                    this.params.page > 1 && this.params.page--
                }
                if(res.data.share){
                    this.share = res.data.share
                    this.getWxConfig()
                }else{
                    this.share = {}
                }
            })
        },
        getNavs() {
            this.$ajax.get("house/communityCondition.html", {}, (res) => {
                let areaData = res.data.list.map((item) => {
                    return {
                        areaid: item.areaid,
                        name: item.areaname
                    }
                })
                this.navs = this.navs.concat(areaData)
            })
        },
        getFocus(){
            this.$ajax.get('memberShop/focus',{},res=>{
                if(res.data.code == 1){
                    this.focus = res.data.focus
                }
            })
        },
        switchTab(e) {
            this.currentIndex=e.index
            this.params.areaid = e.areaid
            this.params.page = 1
            this.getData()
        },
        toDetail(data) {
            this.$store.state.tempData = data
            navigateTo('/home/<USER>/detail?id=' + data.id)
        },
        handleSearch(e){
            this.params.name = e.detail.value||''
            this.params.page = 1
            this.getData()
        }
    },
    onNavigationBarSearchInputConfirmed(e) {
        this.params.name = e.text||''
        this.params.page = 1
        this.getData()
    },
    onShareAppMessage(){
        if (this.seo){
              return{
                title:this.seo.title,
                image:this.seo.image||"",
                path:"/home/<USER>/list"
           }   
        }else {
                return{
                    title:"家装", 
                }
        }
       
    }
}
</script>

<style lang="scss">
.top {
    width: 100%;
    padding: 15upx;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    z-index: 9;
    border-bottom: 1upx solid rgba(255, 255, 255, 0);
    transition: 0.3s;

    .uni-icon {
        height: 60upx;
        width: 60upx;
        line-height: 60upx;
        position: absolute;
        z-index: 2;
        margin-left: 10upx;
    }
}

.bgwhite {
    background-color: #fff;
    border-bottom: 1upx solid $uni-border-color;
    box-shadow: 0 0 26upx #dedede;
}

.search-box {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.7);
    border: 1upx solid rgba(255, 255, 255, 0.5);
    transition: 0.3s;

    input {
        height: 60upx;
        padding-left: 70upx;
        font-size: $uni-font-size-sm;
        border-radius: 6upx;
    }
}

.bgwhite .search-box {
    border: 1upx solid $uni-border-color;
}

.list-item {
    align-items: center;

    .img-box {
        margin-right: 20upx;
        width: 236upx;
        height: 160upx;

        image {
            width: 100%;
            height: 100%;
        }
    }

    .list-info {
        .info-title {
            font-size: 34upx;
            line-height: 1.6;
            color: #333;
        }

        .label {
            color: #666;

            text {
                margin-right: 10upx;
                font-size: 26upx;
                background-color: #fff;
            }

            .green {
                color: #009900;
            }

            .orange {
                color: #ff6600;
            }
        }

        .address {
            margin-top: 16upx;
            font-size: 26upx;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            display: -webkit-box;
        }
    }
}
</style>
