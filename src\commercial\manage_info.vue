<template>
  <view class="p-top-180">
    <title-bar custom>
      <view class="pagehead">信息管理</view>

      <view slot="right" class="seach_btn flex-box refresh-all" @click.prevent.stop="refreshAll()">
        <my-icon type="ic_shuaxin_" size="46rpx" color="#fff"></my-icon>
      </view>


    </title-bar>
    <!-- <tab-bar :tabs="tabs" ref="tabbar" uniform col="3" @click="switchTab">
			<search slot="search" @input="handelInput" @confirm="handelSearch" placeholder="请输入搜索内容"></search>
		</tab-bar> -->
    <view class="top-box">
      <search @input="handelInput" @confirm="handelSearch" placeholder="请输入搜索内容"></search>
    </view>
    <view class="screen-tab flex-box">
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(1)">
        <text>{{ parentName }}</text>
        <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <!-- <view class="screen-tab-item flex-1 text-center" @click="switchTab(2)">
				<text>{{stockName}}</text>
				<my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
			</view> -->
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(2)" v-if="params.catid != 5">
        <text>{{ cateName }}</text>
        <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(5)" v-if="params.catid == 5">
        <text>{{ landParentName }}</text>
        <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(3)">
        <text>{{ statusName }}</text>
        <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item flex-1 text-center" @click="switchTab(4)">
        <text>{{ spreadName }}</text>
        <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
      </view>
      <view class="screen-tab-item text-center" @click="toggleBatchOptions()">
        <view class="batch_icon-box" :class="{ open: show_batch_options }">
          <my-icon type="ic_guanbi" color="#333" size="40rpx"></my-icon>
        </view>
      </view>
      <view class="batch_options-box" :class="{ show: show_batch_options }">
        <view class="batch_options">
          <view class="arrow"></view>
          <view class="options-item bottom-line" @click="toAdd()">
            <my-icon type="ic_luru" color="#fff" size="50rpx"></my-icon>
            <text class="text">录入房源</text>
          </view>
          <view class="options-item bottom-line" @click="batchShangjia()">
            <my-icon type="ic_shangjia" color="#fff" size="50rpx"></my-icon>
            <text class="text">批量上架</text>
          </view>
          <view class="options-item bottom-line" @click="batchXiajia()">
            <my-icon type="ic_xiajia" color="#fff" size="50rpx"></my-icon>
            <text class="text">批量下架</text>
          </view>
          <view class="options-item bottom-line" @click="batchRefre()">
            <my-icon type="huanyihuan" color="#fff" size="50rpx"></my-icon>
            <text class="text">批量刷新</text>
          </view>
          <view class="options-item" @click="batchDel()">
            <my-icon type="ic_delete_w" color="#fff" size="50rpx"></my-icon>
            <text class="text">批量删除</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 类型（出售、出租、转让） -->
    <scroll-view scroll-y class="screen-panel" :class="nowTab == 1 ? 'show' : ''" @touchmove.stop.prevent="stopMove">
      <block v-for="(item, index) in estate_type_list" :key="index">
        <uni-list-item :title="item.name" show-arrow="false" @click="selectParentType(item)"></uni-list-item>
      </block>
    </scroll-view>
    <!-- 类别 -->
    <scroll-view scroll-y class="screen-panel" :class="nowTab == 2 ? 'show' : ''" @touchmove.stop.prevent="stopMove">
      <template v-if="params.parentid != 3">
        <block v-for="(item, index) in cate_type_list" :key="index">
          <uni-list-item :title="item.name" show-arrow="false"
            @click="selectCateType(item.type, item.name)"></uni-list-item>
        </block>
      </template>
      <!-- 生意转让 -->
      <template v-if="params.parentid == 3">
        <uni-list-item title="商铺" show-arrow="false" @click="selectCateType(1, '商铺')"></uni-list-item>
      </template>
    </scroll-view>
    <!-- 房源审核状态 -->
    <scroll-view scroll-y class="screen-panel" :class="nowTab == 3 ? 'show' : ''" @touchmove.stop.prevent="stopMove">
      <block v-for="(item, index) in status_list" :key="index">
        <uni-list-item :title="item.name" show-arrow="false"
          @click="selectStatus(item.info_level, item.name)"></uni-list-item>
      </block>
    </scroll-view>
    <!-- 房源推广类型 -->
    <scroll-view scroll-y class="screen-panel" :class="nowTab == 4 ? 'show' : ''" @touchmove.stop.prevent="stopMove">
      <block v-for="(item, index) in spread_type_list" :key="index">
        <uni-list-item :title="item.name" show-arrow="false"
          @click="selectSpreadType(item.type, item.name)"></uni-list-item>
      </block>
    </scroll-view>
    <!-- 土地转让（出售、出租、转让） -->
    <scroll-view scroll-y class="screen-panel" :class="nowTab == 5 ? 'show' : ''" @touchmove.stop.prevent="stopMove">
      <block v-for="(item, index) in land_parent_list" :key="index">
        <uni-list-item :title="item.name" show-arrow="false" @click="selectLandParent(item)"></uni-list-item>
      </block>
    </scroll-view>
    <!-- 房源列表 -->
    <view v-for="(item, index) in listData" :key="index" class="house_item top-20 flex-box"
      :class="{ shift_right: show_batch_options }">
      <view class="check-box" v-if="show_batch_options" @click="handleCheck(index)">
        <my-icon v-if="item.checked" type="ic_xuanze" color="#ff656b" size="38rpx"></my-icon>
        <view v-else class="check"></view>
      </view>
      <view class="house">
        <view class="refresh-time flex-box top-operate-box">
          <view class="flex-box flex-1 top-operate-box_left">
            <view class="status">
              <text>状态：</text>
              <text :class="'status' + item.is_show">{{ item.is_show ? '已上架' : '已下架' }}</text>
              <text v-if="item.info_level === 0">(待审)</text>
            </view>
            <text class="time">刷新时间：{{ item.begintime }}</text>
            <text class="time">有效期：<text style="color: red;">{{ item.endtime }}</text></text>
          </view>
          <view class="top-operate flex-box">
            <view class="item flex-box" @click="shanchu(item.id, index)">
							<my-icon type="ic_delete1x" color="#d8d8d8" size="40rpx"></my-icon>
							<text class='item_name'>删除</text>
						</view>
          </view>
        </view>
        <template v-if="item.parentid == 1">
          <list-item :item-data="item" :endTime="true" type="sale" :showBottom="false" @click="toDetail"></list-item>
        </template>
        <template v-if="item.parentid == 2">
          <list-item :item-data="item" :endTime="true" type="rent" :showBottom="false" @click="toDetail"></list-item>
        </template>
        <template v-if="item.parentid == 3">
          <list-item :item-data="item" :endTime="true" type="transfer" :showBottom="false"
            @click="toDetail"></list-item>
        </template>
        <view class="operate-box flex-box">
          
          <view class="operate flex-box">
						<view class="item" :class="{'color-green':item.info_level===2}" @click="getJingxuanNew(item)">
							<view class="icon-box">
								<my-icon type="ic_jingxuan_" size="46rpx" :color="item.info_level===2?'#ffffff':'#d8d8d8'"></my-icon>
							</view>
							<text>精选</text>
						</view>
						<view class="item" :class="{'color-red':item.upgrade_type==2}" @click="getZhidingNew(item)">
							<view class="icon-box">
								<my-icon type="ic_ding_" size="46rpx" :color="item.upgrade_type==2?'#ffffff':'#d8d8d8'"></my-icon>
							</view>
							<text>置顶</text>
						</view>
					</view>
					<view class="flex-box">
						<view class="status_btn" @click="huakuaishow(item.id, index)">刷新</view>
						<view class="status_btn" @click="xiugai(item.id)">编辑</view>
						<view class="status_btn" @click="setStatus(item.is_show, item.id)">{{item.is_show?'下架':'上架'}}</view>
						<view class="status_btns" @click="tuiguang(item)">推广</view>
					</view>

        </view>
      </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <!-- 置顶弹层 -->
    <my-popup ref="zhiding">
      <view style="font-size: 0">
        <image style="width: 100%; margin-bottom: -4rpx" @click="stopMove()"
          src="https://images.tengfangyun.com/images/new_icon/huojian.png?x-oss-process=style/m_240" mode="aspectFill">
        </image>
        <view style="background: #fff">
          <view class="info_title">信息标题：{{ zhidingInfo.title }}</view>
          <view style="padding: 0 48rpx">
            <view class="tip" v-if="top_tip.upgrade_type == 2">提示：当前置顶推广有效期至{{ top_tip.upgrade_time }}，您可以选择增加推广时间。
            </view>
            <view class="tip" v-else>提示：置顶信息，排名靠前，请选择置顶天数</view>
            <scroll-view class="scroll" :class="{ murows: zhidingInfo.items.length > 4 }" scroll-y>
              <view class="day_list flex-box">
                <view class="day_item" :class="{ active: zhidingId === day.id }" v-for="day in zhidingInfo.items"
                  :key="day.id" @click="onClickTop(day)">
                  <view class="day">
                    <text class="value">{{ day.name }}</text>
                    <text class="unit">{{ day.unit }}</text>
                  </view>
                  <view class="money" v-if="day.type === 'meal'">{{ day.title }}</view>
                  <view class="money" v-else>{{ day.money }}金币</view>
                </view>
                <view class="day_item vacancy"></view>
                <view class="day_item vacancy"></view>
              </view>
            </scroll-view>
            <!-- <view class="tip">
							您当前拥有金币：{{zhidingInfo.money_own}}个，将扣除金币：{{zhidingInfo.money}}个
						</view> -->
            <view class="btn-box">
              <view class="btn btn-lg" @click="setZhiding(zhidingInfo.id)">确定</view>
              <view class="btn crude" @click="$refs.zhiding.hide()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </my-popup>
    <!-- 精选弹层 -->
    <my-popup ref="jingxuan">
      <view style="font-size: 0">
        <image style="width: 100%; margin-bottom: -1rpx" @click="stopMove()"
          src="https://images.tengfangyun.com/images/new_icon/jingxuan.png?x-oss-process=style/m_240" mode="aspectFill">
        </image>
        <view style="background: #fff">
          <!-- <view class="row">
						<view style="font-size: 30upx;">选择自动刷新次数</view>
						<view class="tip">提示：自动刷新时间为06:00~24:00 每30分钟刷新一次</view>
					</view> -->
          <view class="info_title">信息标题：{{ jingxuanInfo.title }}</view>
          <view style="padding: 0 48rpx">
            <view class="tip" v-if="jingxuan_num">当前账户可免费设置精选房源{{ jingxuan_num.allow_num }}条，已设置{{ jingxuan_num.use_num
              }}条，剩余{{
                jingxuan_num.surplus_num
              }}条。</view>
            <view class="tip" v-else>提示：自动刷新时间为06:00~24:00 每30分钟刷新一次</view>
            <scroll-view class="scroll" :class="{ murows: jingxuanInfo.items.length > 4 }" scroll-y>
              <view class="day_list flex-box">
                <view class="day_item" :class="{ active: jingxuanId === jingxuan.id }"
                  v-for="jingxuan in jingxuanInfo.items" :key="jingxuan.id" @click="onClickJingxuan(jingxuan)">
                  <view class="day">
                    <text class="value">{{ parseInt(jingxuan.name) }}</text>
                    <text class="unit">次</text>
                  </view>
                  <view class="money" v-if="jingxuan.type === 'meal'">{{ jingxuan.title }}</view>
                  <view class="money" v-else>{{ jingxuan.money }}金币</view>
                </view>
                <view class="day_item vacancy"></view>
                <view class="day_item vacancy"></view>
              </view>
            </scroll-view>
            <view class="btn-box">
              <view class="btn btn-lg" @click="setJingxuan(jingxuanInfo.id)">确定</view>
              <view class="btn crude" @click="$refs.jingxuan.hide()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </my-popup>

    <my-popup ref="xiajia" @hide="onStatusHide">
      <view class="xaijia">
        <view class="title">请选择下架或删除原因</view>
        <view class="status-list">
          <view class="status-item bottom-line" @click="onSelectStatus(item)" v-for="item in xiajia_status"
            :key="item.value">
            <view class="flex-box price_form" v-if="item.value == 1 && show_price">
              <input type="text" v-model="complate_price" placeholder="请输入成交价格" />
              <view class="unit">{{ chengjiao_unit }}</view>
              <view class="btn" @click.stop.prevent="subStatus(item)">提交</view>
            </view>
            <text v-else>{{ item.title }}</text>
          </view>
        </view>
      </view>
    </my-popup>
    <my-popup ref="acitive_time">
      <view class="xaijia">
        <view class="title">信息已失效，请选择有效期</view>
        <view style="background: #fff">
          <view style="padding: 24rpx 48rpx 0 48rpx">
            <view>
              <view class="day_list flex-box">
                <view class="day_item" :class="{ active: select_active_time_value === item.id }"
                  v-for="item in active_time" :key="item.id" @click="select_active_time_value = item.id">
                  <view class="day">
                    <text class="value small">{{ item.name }}</text>
                    <!-- <text class="unit">天</text> -->
                  </view>
                  <view class="money" v-if="youxiaoqiHasMoney">￥{{ item.money }}</view>
                </view>
                <view class="day_item vacancy"></view>
                <view class="day_item vacancy"></view>
              </view>
            </view>
            <view class="btn-box">
              <view class="btn btn-lg" @click="setActiveTime()">确定</view>
              <view class="btn crude" @click="$refs.acitive_time.hide()">取消</view>
            </view>
          </view>
        </view>
        <!-- <view class="status-list">
          <view class="status-item bottom-line" @click="onSelectTimes(item)" v-for="item in active_time" :key="item.value">
            <text>{{item.name}}</text>
            <text v-if="youxiaoqiHasMoney">(￥{{item.money}})</text>
          </view>
        </view> -->
      </view>
    </my-popup>
    <!-- 复制分享文案 -->
    <my-popup ref="text_popup" position="center" :height="text_popup_height">
      <view class="copy-text-box" id="copy-text">
        <view class="title">{{ detail.title }}</view>
        <view class="info-box">
          <view class="info-row flex-row" v-if="detail.name">
            <text class="label">小区：</text>
            <text class="value">{{ detail.name }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">面积：</text>
            <text class="value">{{ `${detail.mianji}${detail.mianji_unit}` }}</text>
          </view>
          <view class="info-row flex-row" v-if='detail.parentid == 1'>
            <text class="label">售价：</text>
            <text class="value">{{ detail.price ? detail.price + detail.price_unit : '面议' }}</text>
            <text class="value" v-if="detail.danjia && detail.catid != 5">单价{{ detail.danjia }}{{ detail.danjia_unit
              }}</text>
          </view>
          <view class="info-row flex-row" v-if='detail.parentid == 2 || detail.parentid == 3'>
            <text class="label">租金：</text>
            <text class="value">{{ detail.price ? detail.price + detail.price_unit : '面议' }}</text>
          </view>
          <template v-if='detail.parentid == 3'>
            <view class="info-row flex-row">
              <text class="label">转让费：</text>
              <text class="value">{{ detail.transfer_fee > 0 ? detail.transfer_fee + '万元' : detail.transfer_fee
                }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">剩余租期：</text>
              <text class="value">{{ detail.remain_lease > 0 ? detail.remain_lease + '月' : detail.remain_lease }}</text>
            </view>
          </template>
          <view class="info-row flex-row">
            <text class="label">{{ detail.catid == 5 ? '土地用途：' : '类型：' }}</text>
            <text class="value">{{ detail.type_title }}</text>
          </view>
          <view class="info-row flex-row" v-if="detail.catid < 3">
            <text class="label">楼层：</text>
            <text class="value" v-if="detail.szlc">{{ detail.floor_title }}{{ detail.szlc }}层{{ detail.szlc2 !== 0 ?
              `至${detail.szlc2}层` : '' }}/共{{ detail.louceng || '' }}层</text>
            <text class="value" v-else>{{ detail.floor_title }}共{{ detail.louceng || '' }}层</text>
          </view>
          <template v-if="detail.catid == 1">
            <view class="info-row flex-row">
              <text class="label">经营状态：</text>
              <text class="value">{{ detail.business_status == 1 ? '经营中' : '空置中' }}</text>
            </view>
            <view class="info-row flex-row" v-if="detail.trade_title">
              <text class="label">经营行业：</text>
              <text class="value">{{ detail.trade_ptitle }}-{{ detail.trade_title }}</text>
            </view>
            <template v-if='detail.sizes_width || detail.sizes_height || detail.sizes_depth'>
              <view class="info-row flex-row">
                <text class="label">规格：</text>
                <text class="value">{{ detail.sizes_width ? ('面宽' + detail.sizes_width + 'm、')
                  : "" }}{{ detail.sizes_height ? ('层高' + detail.sizes_height + 'm、') : "" }}{{ detail.sizes_depth ?
                    ('进深'
                      + detail.sizes_depth + 'm') : "" }}</text>
              </view>
            </template>
            <view class="info-row flex-row" v-if="detail.consumer_ids">
              <text class="label">客流人群：</text>
              <text class="value">{{ detail.consumer_ids }}</text>
            </view>
          </template>
          <template v-if="detail.catid == 2">
            <view class="info-row flex-row">
              <text class="label">可注册：</text>
              <text class="value">{{ detail.can_register == 1 ? '是' : '否' }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">可拆分：</text>
              <text class="value">{{ detail.can_divisible == 1 ? '是' : '否' }}</text>
            </view>
          </template>
          <template v-if="detail.catid == 5">
            <view class="info-row flex-row">
              <text class="label">土地来源：</text>
              <text class="value">{{ detail.land_source_title }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">流转年限：</text>
              <text class="value">{{ detail.useful_years }}年</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">土地使用证：</text>
              <text class="value">{{ detail.land_certificate == 1 ? '有' : '无' }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">所有权证：</text>
              <text class="value">{{ detail.owner_certificate == 1 ? '有' : '无' }}</text>
            </view>
          </template>
          <view class="info-row flex-row" v-if="detail.label && detail.label.length > 0">
            <text class="label">卖点：</text>
            <text class="value">{{ detail.label.map(item => item.name).join(' ') }}</text>
          </view>
          <view class="button disabled-btn flex-row" v-if="copy_success">
            <my-icon type="check-circle" size="30rpx" color="#999"></my-icon>
            <text class="text">文本已复制</text>
          </view>
          <view class="button" v-else @click="copywriting">复制文本</view>
        </view>
      </view>
    </my-popup>
    <view class="mask" :class="nowTab > 0 ? 'show' : ''" @click="nowTab = 0" @touchmove.stop.prevent="stopMove"></view>
    <!-- <view class="refresh-all" @click="refreshAll">
      <my-icon type="ic_shuaxin_" size="72rpx" color="#fff"></my-icon>
    </view> -->
    <douyinPop ref='douyinPop' @copyLink="copyLink" @sendToDouyin="chooseImg" @saihongbao='toSaiHongbao'
      @handleCreat="handleCreat" @showCopywriting="showCopywriting" @appShare="appShare">

    </douyinPop>

    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>

    <!-- 单个/批量 刷新滑块验证 -->
    <sub-form ref="sub_form" @referbatch="referbatch" :refretype="refretype" @onereferbatch="onereferbatch"></sub-form>
    <!-- <sub-form ref="sub_form"></sub-form> -->
  </view>
</template>

<script>
import titleBar from "../components/titleBar.vue";
import subForm from '../../src/user/components/refreshsubForm.vue'
import tabBar from '../components/tabBar.vue'
import search from '../components/search.vue'
import myIcon from '../components/myIcon.vue'
import listItem from './components/listItem.vue'
import demandItem from '../components/demandItem.vue'
import myPopup from '../components/myPopup.vue'
import mySelect from '../components/form/mySelect.vue'
import myCheckbox from '../components/form/myCheckbox.vue'
import myInput from '../components/form/myInput.vue'
import pay from '../components/pay.vue'
import { uniLoadMore, uniListItem } from '@dcloudio/uni-ui'
import { formatImg, showModal, config } from '../common/index.js'
import douyinPop from "./components/douyinPop.vue"
import shareTip from '@/components/shareTip'
export default {
  data() {
    return {
      refretype: '',//one 单 -multi 批
      multipleSelection: [],
      uuid: '',
      verify_code: '',
      batchids: '',
      index: 0,
      styleObject: {
        left: 0,
        top: 0,
      },
      array: [],
      parentName: '商业出售',
      cateName: '全部',
      stockName: '房源库',
      statusName: '审核状态',
      spreadName: '推广方式',
      landParentName: '全部',
      nowTab: -1,
      estate_type_list: [
        {
          name: '二手房',
          cate_id: 1
        },
        {
          name: '出租房',
          cate_id: 2
        },
        {
          name: '求购',
          cate_id: 4
        },
        {
          name: '求租',
          cate_id: 3
        },
        {
          name: '商业出售',
          parentid: 1,
        },
        {
          name: '商业出租',
          parentid: 2,
        },
        {
          name: '生意转让',
          parentid: 3,
        },
        {
          name: '土地转让',
          catid: 5,
        }
      ],
      cate_type_list: [
        {
          name: '全部',
          type: 0,
        },
        {
          name: '商铺',
          type: 1,
        },
        {
          name: '写字楼',
          type: 2,
        },
        {
          name: '厂房',
          type: 3,
        },
        {
          name: '车库',
          type: 4,
        },
      ],
      spread_type_list: [
        {
          name: '不限',
          type: 0,
        },
        {
          name: '置顶',
          type: 1,
        },
        {
          name: '精选',
          type: 2,
        },
        // {
        // 	name:"标红",
        // 	type:3
        // },
        // {
        // 	name:"加粗",
        // 	type:4
        // },
      ],
      land_parent_list: [
        {
          parentid: 0,
          name: '全部',
        },
        {
          parentid: 1,
          name: '出售',
        },
        {
          parentid: 2,
          name: '出租',
        },
        {
          parentid: 3,
          name: '转让',
        },
      ],
      status_list: [
        {
          info_level: -1,
          name: '不限',
        },
        {
          info_level: 0,
          name: '待审核',
        },
        {
          info_level: 1,
          name: '审核通过',
        },
        // {
        // 	status:3,
        // 	name:"审核不通过"
        // },
      ],
      show_batch_options: false, //是否显示批量操作菜单
      params: {
        page: 1,
        parentid: 1,
        catid: 1,
        rows: 10,
        is_show: -1,
        info_level: -1,
        upgrade_type: 0,
        keyword: '',
      },
      zhidingId: '',
      zhidingInfo: {
        id: '',
        title: '',
        items: [],
      },
      api: '',
      get_status: 'loading',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了',
      },
      listData: [],
      jingxuanId: -1,
      jingxuanInfo: {
        id: '',
        money_own: '',
        money: '',
        title: '',
        items: [],
      },
      h5WxPayApi: '',
      h5AliPayApi: '',
      mpWxPayApi: '',
      pay_params: {},
      top_tip: {},
      jingxuan_num: {},
      xiajia_status: [],
      active_time: [],
      youxiaoqiHasMoney: false,
      select_active_time_value: '',
      show_price: false, //是否显示填写下架成交价格
      complate_price: '', //下架成交价格
      chengjiao_unit: '', //成交价格单位
      detail: {},
      text_popup_height: "",
      copy_success: false,
      currentUserInfo: {},
      show_share_tip: false
    }
  },
  components: {
    subForm,
    tabBar,
    search,
    listItem,
    demandItem,
    myIcon,
    myPopup,
    mySelect,
    myCheckbox,
    myInput,
    uniLoadMore,
    uniListItem,
    pay,
    douyinPop,
    shareTip,
    titleBar
  },
  onLoad(options) {

    // #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
    uni.hideShareMenu()
    // #endif
    this.params.parentid = parseInt(options.parentid) || 0
    this.params.catid = parseInt(options.catid) || 0
    if (options.catid) {
      this.parentName = this.estate_type_list.find((item) => item.catid === parseInt(options.catid)).name
    } else {
      this.parentName = this.estate_type_list.find((item) => item.parentid === parseInt(this.params.parentid)).name
    }
    uni.showLoading({
      title: '加载中...',
      mask: true,
    })
    this.getData()
    this.getXiajiaStatus()
    this.getWxConfig(
      ['chooseWXPay', 'updateAppMessageShareData', 'updateTimelineShareData'],
      wx => {
        console.log('执行回调')
        this.wx = wx
      },
      1
    )
    this.getAcitiveTime()
    uni.$on('getDataAgain', () => {
      this.getData()
      this.getXiajiaStatus()
    })

  },
  onShow() {
    if (this.$store.state.updatePageData) {
      this.$store.state.updatePageData = false
      this.params.page = 1
      this.getData()
    }
  },
  filters: {
    imgUrl(val) {
      return formatImg(val, 'w_240')
    },
  },
  computed: {
    is_open_share_douyin() {
      return this.$store.state.is_open_share_douyin
    },
    HongBaoSrc() {
      return config.imgDomain + '/hongbao/saihongbao/<EMAIL>'
    }
  },
  methods: {
    toggleBatchOptions() {
      this.show_batch_options = !this.show_batch_options;
      this.nowTab = -1;
      this.listData.forEach((item) => {
        item.showPicker = false;
        item.isUp = false;
      });
    },
    adjustPickerPosition(index) {
      // 确保 this.$refs.dropdown 存在
      if (!this.$refs[`dropdown-${index}`] || !this.$refs[`dropdown-${index}`][0]) {
        console.warn(`Dropdown reference not found for index: ${index}`);
        return false;
      }

      const dropdownRect = this.$refs[`dropdown-${index}`][0].$el.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const dropdownHeight = dropdownRect.height;
      const pickerHeight = 150; // 假设下拉菜单的高度为 150px

      if (dropdownRect.bottom + pickerHeight > windowHeight) {
        return true; // 向上展开
      } else {
        return false; // 向下展开
      }
    },
    getPickerStyle(index) {
      if (!this.$refs[`dropdown-${index}`] || !this.$refs[`dropdown-${index}`][0]) {
        return {};
      }
      const dropdownRect = this.$refs[`dropdown-${index}`][0].$el.getBoundingClientRect();
      const position = this.listData[index].isUp ? 'top' : 'bottom';
      const offset = this.listData[index].isUp ? -150 : 10; // 调整偏移量
      if (position == 'bottom') {
        return {
          right: '0rpx',
          top: '60rpx'
        };
      } else {
        return {
          right: '0rpx',
          top: '-10rpx',
        };
      }

    },
    tapPopup(e, index) {
      const self = this
      self.listData[index].showPicker = false; // 关闭选择器
      switch (e.title) {
        case '删除':
          self.shanchu(e.item_id, index)
          break;
        case '分享':
          self.showShare()
          break;
        case '发送到抖音':
          self.chooseImg()
          break;
        case '上架':
          self.tuiguangdetial(e.item_id, 3)
          break;
        case '下架':
          self.batchXiajia(e.item_id)
          break;
        case '置顶':
          self.tuiguangdetial(e.item_id, 1)
          break;
        case '精选':
          self.tuiguangdetial(e.item_id, 2)
          break;
        case '取消':
          self.allcloseBubbleMenu()
          break;
        // case '一键刷新':
        // 	self.refreshAll()
        // 	break;
      }
    },
    allcloseBubbleMenu() {
      this.listData.forEach((item) => {
        item.showPicker = false;
        item.isUp = false;
      });
    },
    showBubbleMenu(item, index) {
      this.$store.state.myonehouse = item
      // 关闭所有其他下拉选项
      this.listData.forEach((item, idx) => {
        if (idx !== index) {
          item.showPicker = false;
          item.isUp = false;
        }
      });
      this.listData[index].showPicker = !this.listData[index].showPicker;
      this.listData[index].isUp = this.adjustPickerPosition(index);
      this.detail = item
      this.array = [{ title: item.is_show ? '下架' : '上架', item_id: item.id }, { title: '置顶', item_id: item.id }, { title: '精选', item_id: item.id }]
      // this.array = []
      if (item.parentid != 3 && item.parentid != 4) {
        if (this.is_open_share_douyin == 1) {
          const dy = { title: '发送到抖音', item_id: item.id }
          this.array.push(dy);
        }
        const fx = { title: '分享', item_id: item.id }
        this.array.push(fx);
      }
      const sc = { title: '删除', item_id: item.id }
      this.array.push(sc);
      const qx = { title: '取消', item_id: item.id }
      this.array.push(qx);
      const query = uni.createSelectorQuery().in(this);
      query.selectAll('.itemCircle').boundingClientRect(data => {
        const ref = data[index]
        const height = uni.getSystemInfoSync().screenHeight//屏幕高度
        this.styleObject.left = ref.left
        this.styleObject.top = ref.top - ref.height
        const boxHeight = 212 + this.styleObject.top//模态框高度+距离顶部高度
        if (boxHeight > height) {
          this.styleObject.top = ref.top - 212
        }
        this.show_fenxiang = !this.show_fenxian
      }).exec();

    },
    tuiguangdetial(id, type) {
      if (this.params.catid) {
        this.$navigateTo('/user/my/tuiguang_detail?item_type=' + type + '&id=' + id + '&catid=' + this.params.catid)
      } else {
        this.$navigateTo('/user/my/tuiguang_detail?item_type=' + type + '&id=' + id + '&parentid=' + this.params.parentid)
      }

    },
    tuiguang(item) {
      this.$store.state.myonehouse = item
      if (this.params.catid) {
        this.$navigateTo(`/user/my/tuiguang?catid=${this.params.catid}`)
      } else {
        this.$navigateTo(`/user/my/tuiguang?parentid=${this.params.parentid}`)
      }


    },
    getData() {
      this.getuserInfo()
      this.get_status = 'loading'
      if (this.params.page == 1) {
        this.listData = []
      }
      this.$ajax.get(
        'estateRelease/getReleaseList',
        this.params,
        (res) => {

          if (res.data.code == 1) {

            let list_data = res.data.list.map((item) => {
              item.checked = false
              return item
            })
            this.listData = this.listData.concat(list_data)
            if (this.listData.length > 0) {
              this.listData = this.listData.map(item => ({
                ...item,
                checked: false,
                isUp: false,
                showPicker: false
              }));
            }
            if (res.data.list.length < this.params.rows) {
              this.get_status = 'noMore'
            } else {
              this.get_status = 'more'
            }
          }
          if (res.data.code == 0) {
            this.get_status = 'noMore'
          }
          uni.hideLoading()

          //try catch 防御性编程实践 经测试 不加 直接调用 也不会报错
          try{
            uni.stopPullDownRefresh();
          }catch(e){
          }

        },
        (err) => {
          uni.hideLoading()
          //try catch 防御性编程实践 经测试 不加 直接调用 也不会报错
          try{
              uni.stopPullDownRefresh();
          }catch(e){}
        },
        false
      )
    },
    getuserInfo() {
      this.$ajax.get('member/index.html', {}, (res) => {
        if (res.data.user) {

          // if (res.data.is_adviser){
          // 	res.data.user.adviser={
          // 		uncheck:res.data.adviser.uncheck||0,
          // 		uncheck_cname:res.data.adviser.uncheck_cname ||'',
          // 		uncheck_prelogo:res.data.adviser.uncheck_prelogo||''
          // 	}
          // }
          // if (res.data.user.levelid>1){
          // 	res.data.user.agent={
          // 		uncheck:res.data.agent.uncheck||0,
          // 		uncheck_cname:res.data.agent.uncheck_cname ||'',
          // 		uncheck_prelogo:res.data.agent.uncheck_prelogo||''
          // 	}
          // }
          if (res.data.user.agent_invalid) {
            this.currentUserInfo.shareType = 2
            this.currentUserInfo.sid = res.data.user.id
          } else if (res.data.is_adviser) {
            this.currentUserInfo.shareType = 1
            this.currentUserInfo.sid = res.data.adviser.id
          } else {
            this.currentUserInfo.shareType = ''
            this.currentUserInfo.sid = ''
          }
          this.userInfo = res.data.user
          // console.log(res.data.user);

        } else {
          this.currentUserInfo = {}
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
        }

      })
    },
    getXiajiaStatus() {
      this.$ajax.get('estateRelease/complateStatus', {}, (res) => {
        if (res.data.code === 1) {
          this.xiajia_status = res.data.status
        }
      })
    },
    getAcitiveTime() {
      this.$ajax.get('estateRelease/getAcitiveTime', {}, res => {
        if (res.data.code === 1) {
          this.active_time = res.data.list
          this.youxiaoqiHasMoney = this.active_time.some(item => parseFloat(item.money))
        }
      })
    },
    selectParentType(item) {
      if (item.cate_id) {
        uni.redirectTo({
          url: `/user/manage_info?cate_id=${item.cate_id}`
        });
        return
      }
      if (item.parentid && (item.parentid == 1 || item.parentid == 2)) {
        this.cateName = '全部'
      }
      if (item.parentid && item.parentid == 3) {
        this.cateName = '商铺'
      }
      if (item.catid && item.catid == 5) {
        this.landParentName = '全部'
      }
      this.params.parentid = item.parentid || 0
      this.params.catid = item.catid || 0
      this.parentName = item.name
      this.params.page = 1
      this.nowTab = -1
      this.getData()
    },
    selectCateType(index, name) {
      this.params.catid = index
      this.cateName = name
      this.params.page = 1
      this.nowTab = -1
      this.getData()
    },
    // 晒选推广方式
    selectSpreadType(type, name) {
      switch (type) {
        case 0:
          if (this.params.info_level === 2) {
            this.params.info_level = -1
          }
          this.params.upgrade_type = ''
          break
        case 1:
          // 置顶
          if (this.params.info_level === 2) {
            this.params.info_level = -1
            this.statusName = '不限'
          }
          this.params.upgrade_type = 1
          break
        case 2:
          // 精选
          this.params.upgrade_type = ''
          this.params.info_level = 2
          this.statusName = '不限'
          break
        default:
          this.params.info_level = ''
          this.params.upgrade_type = ''
      }
      this.spreadName = name
      this.params.page = 1
      this.nowTab = -1
      this.getData()
    },
    // 土地（出售、出租、转让）
    selectLandParent(item) {
      this.params.parentid = item.parentid
      this.landParentName = item.name
      this.params.page = 1
      this.nowTab = -1
      this.getData()
    },
    // 筛选审核状态
    selectStatus(info_level, name) {
      if (this.params.info_level === 2) {
        this.spreadName = '不限'
      }
      this.params.info_level = info_level
      this.statusName = name
      this.params.page = 1
      this.nowTab = -1
      this.getData()
    },
    switchTab(index) {
      if (this.nowTab == index) {
        this.nowTab = 0
      } else {
        this.nowTab = index
      }
      this.show_batch_options = false
    },
    handleCheck(index) {
      this.listData[index].checked ? (this.listData[index].checked = false) : (this.listData[index].checked = true)
    },
    handelInput(e) {
      this.params.keyword = e.detail.value
    },
    handelSearch() {
      this.params.page = 1
      this.getData()
    },
    toDetail(e) {
      if (e.detail.parentid) {
        this.$store.state.tempData = e.detail
        if (e.detail.parentid == 1) {
          this.$navigateTo('/commercial/sale/detail?id=' + e.detail.id)
        }
        if (e.detail.parentid == 2) {
          this.$navigateTo('/commercial/rent/detail?id=' + e.detail.id)
        }
        if (e.detail.parentid == 3) {
          this.$navigateTo('/commercial/transfer/detail?id=' + e.detail.id)
        }
      }
    },
    checkShuaxin(id, index) {
      this.$ajax.get(
        'estateRelease/checkRefresh',
        { id },
        (res) => {
          if (res.data.code !== 1) {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
            return
          }
          if (res.data.is_free === 1) {
            // 可以免费刷新,直接刷新
            this.shuaxin(id, index)
          }
          if (res.data.is_free === 2) {
            // 提示需要使用积分刷新
            showModal({
              title: '温馨提示',
              content: res.data.msg,
              confirm: () => {
                this.shuaxin(id, index)
              },
            })
          }
          if (res.data.is_free === 3) {
            // 提示积分不足,需要去充值
            showModal({
              title: '温馨提示',
              content: res.data.msg,
              confirm: () => {
                this.$navigateTo('/user/recharge?type=2')
              },
            })
          }
        },
        (err) => {
          this.refreshing = false
        }
      )
    },
    shuaxin(id, index) {
      this.$ajax.post(
        'estateRelease/refreshInfo',
        { id },
        (res) => {
          if (res.data.code == 1) {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2500,
            })
            this.params.page = 1
            this.getData()
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
          }
        },
        (err) => { },
        false
      )
    },
    getDeduction(type, callback) {
      this.$ajax.get('member/tips.html', {}, (res) => {
        if (res.data.code == 1) {
          this.tips = res.data
          callback(this.tips[type])
        } else {
          uni.showToast({
            title: '获取需要扣除金币信息失败',
            icon: 'none',
          })
        }
      })
    },
    biaohong(id, index) {
      if (this.listData[index].ifred == 1) {
        uni.showToast({
          title: '此信息已经是标红状态',
          icon: 'none',
        })
        return
      }
      let handle = () => {
        this.$ajax.get(
          'member/informationAddRed.html',
          { id },
          (res) => {
            if (res.data.code == 1) {
              uni.showToast({
                title: res.data.msg,
              })
              this.listData[index].ifred = 1
            } else {
              uni.showToast({
                title: res.data.msg,
                icon: 'none',
              })
            }
          },
          (err) => { },
          false
        )
      }
      let showTip = (num) => {
        showModal({
          title: '提示',
          content: '标红需要扣除' + num + '个金币，确定要标红吗？',
          confirm: () => {
            handle()
          },
        })
      }
      this.getDeduction('red', showTip)
    },
    jiacu(id, index) {
      if (this.listData[index].ifbold == 1) {
        uni.showToast({
          title: '此信息已经是加粗状态',
          icon: 'none',
        })
        return
      }
      let handle = () => {
        this.$ajax.get(
          'member/informationAddBold.html',
          { id },
          (res) => {
            if (res.data.code == 1) {
              uni.showToast({
                title: res.data.msg,
              })
              this.listData[index].ifbold = 1
            } else {
              uni.showToast({
                title: res.data.msg,
                icon: 'none',
              })
            }
          },
          (err) => { },
          false
        )
      }
      let showTip = (num) => {
        showModal({
          title: '提示',
          content: '加粗需要扣除' + num + '个金币，确定要加粗吗？',
          confirm: () => {
            handle()
          },
        })
      }
      this.getDeduction('bold', showTip)
    },
    getZhiding(item) {
      let id = item.id
      let title = item.title
      this.zhidingId = ''
      this.$ajax.get(
        'estateRelease/infoTops',
        { catid: item.catid, id },
        (res) => {
          // 获取当前信息的置顶信息
          if (res.data.info) {
            this.top_tip = res.data.info
          } else {
            this.top_tip = {}
          }
          if (res.data.code == 1) {
            // res.data.days.unshift({name:"请选择",value:0})
            // this.zhidingInfo = res.data
            let tops = res.data.tops.map(item => {
              var reg = /^[0-9]+天$/
              if (reg.test(item.name)) {
                item.name = parseFloat(item.name)
                item.unit = '天'
              } else {
                item.unit = ''
              }
              return item
            })
            let package_tops = []
            if (res.data.packagesInfoTops && res.data.packagesInfoTops.length > 0) {
              package_tops = res.data.packagesInfoTops.map((item) => {
                item.type = 'meal'
                var reg = /^[0-9]+天$/
                if (reg.test(item.name)) {
                  item.name = parseFloat(item.name)
                  item.unit = '天'
                } else {
                  item.unit = ''
                }
                return item
              })
            }
            this.zhidingInfo = {
              title: title,
              id: id,
              money_own: res.data.money_own,
              items: tops.concat(package_tops),
            }

            this.$refs.zhiding.show()
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
          }
        },
        (err) => {
          // console.log(err)
        },
        false
      )
    },
    setZhiding(info_id) {
      if (!this.zhidingId) {
        uni.showToast({
          title: '请选择需要置顶的天数',
          icon: 'none',
        })
        return
      }
      let params = { id: info_id }
      // 如果选择的是购买的套餐
      if (this.top_type === 'meal') {
        params.package_tops_id = this.zhidingId
      } else {
        params.tops_id = this.zhidingId
      }
      this.$ajax.post('estateRelease/checkInfoTop', params, (res) => {
        if (res.data.code == 1) {
          if (res.data.is_top === 1 && res.data.pay_status === 1) {
            // 不需要支付，直接设置置顶成功
            uni.showToast({
              title: res.data.msg,
            })
            this.$refs.zhiding.hide()
            setTimeout(() => {
              this.params.page = 1
              this.getData()
            }, 1500)
            return
          }
          // 设置支付接口和支付所需要的参数
          this.payApi = 'estateRelease/infoTopByWxPay'
          this.pay_params = {
            id: info_id,
            tops_id: this.zhidingId,
          }
          showModal({
            content: res.data.msg,
            confirm: () => {
              if (res.data.is_top === 1) {
                // 使用金币支付
                this.cornPayOfZhiding(info_id)
              } else {
                this.handlePay()
              }
            },
          })
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    cornPayOfZhiding(info_id) {
      this.$ajax.get('estateRelease/infoTopByCorn', { tops_id: this.zhidingId, id: info_id }, (res) => {
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg,
          })
          this.$refs.zhiding.hide()
          setTimeout(() => {
            this.params.page = 1
            this.getData()
          }, 1500)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },

    getZhidingNew(item) {
			if(item.info_level==0){
				uni.showToast({
					title: '当前信息待审核,无法设置置顶',
					icon: 'none'
				})
				return
			}
			if(item.is_show==0){
				uni.showToast({
					title: '当前信息已下架,无法设置置顶',
					icon: 'none'
				})
				return
			}
			if(item.endtime.includes('已到期')){
				uni.showToast({
					title: '当前信息已到期,无法设置置顶',
					icon: 'none'
				})
				return
			}

			this.$store.state.myonehouse = item;
			let url = `/user/my/tuiguang_detail?item_type=1&id=${item.id}&parentid=${this.params.parentid}`
			this.$navigateTo(url)
		},

		getJingxuanNew(item) {
			if(item.info_level===2){
				showModal({
						title: '提示',
						content: "当前信息已经是精选状态，是否取消精选？（取消精选条数不会返还）",
						cancelText: '否',
						confirmText: '是',
						confirm: () => {
							this.cancelJingxuan(item.id)
						}
					});
				return 
			}
			if(item.info_level==0){
				uni.showToast({
					title: '当前信息待审核,无法设置精选',
					icon: 'none'
				})
				return
			}
			if(item.is_show==0){
				uni.showToast({
					title: '当前信息已下架,无法设置精选',
					icon: 'none'
				})
				return
			}
			if(item.endtime.includes('已到期')){
				uni.showToast({
					title: '当前信息已到期,无法设置精选',
					icon: 'none'
				})
				return
			}

			this.$store.state.myonehouse = item;
			let url = `/user/my/tuiguang_detail?item_type=2&id=${item.id}&parentid=${this.params.parentid}`
			this.$navigateTo(url)

			
		},

    getJingxuan(item) {
      let id = item.id
      let title = item.title
      this.jingxuanId = -1
      this.$ajax.get(
        'estateRelease/selectedList',
        { catid: item.catid },
        (res) => {
          if (res.data.code == 3) {
            showModal({
              title: '提示',
              content: '当前信息已经是精选状态，是否取消精选？（取消精选条数不会返还）',
              cancelText: '否',
              confirmText: '是',
              confirm: () => {
                this.cancelJingxuan(id)
              },
            })
            return
          }
          // 获取当前用户的免费精选条数
          if (res.data.tip) {
            this.jingxuan_num = res.data.tip
          } else {
            this.jingxuan_num = {}
          }
          if (res.data.code == 1) {
            // this.jingxuanInfo = res.data
            // 后台返回数据不兼容组件要求格式，在这里处理下
            if (res.data.freeSelected.id !== undefined) {
              res.data.selectedInfo.unshift(res.data.freeSelected)
            }
            let package_selecteds = res.data.packagesInfoSelecteds.map((item) => {
              item.type = 'meal'
              return item
            })
            let selecteds = res.data.selectedInfo
            this.jingxuanInfo = {
              title: title,
              id: id,
              money_own: res.data.money_own,
              items: selecteds.concat(package_selecteds),
            }
            this.$refs.jingxuan.show()
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
          }
        },
        (err) => {
          // console.log(err)
        },
        false
      )
    },
    cancelJingxuan(id) {
      if(res.data.code==1){
				//取消精选成功
				const index = this.listData.findIndex(item => item.id === id);
				if(index > -1) {
					this.listData.splice(index, 1, {...this.listData[index], info_level: 1});
				}
			}
      this.$ajax.get('member/cancelRecommend.html', { id }, (res) => {
        uni.showToast({
          title: res.data.msg,
          icon: 'none',
        })
      })
    },
    onClickJingxuan(e) {
      this.jingxuan_type = e.type
      this.jingxuanId = e.id
    },
    setJingxuan(info_id) {
      if (this.jingxuanId === -1) {
        uni.showToast({
          title: '请选择刷新次数',
          icon: 'none',
        })
        return
      }
      let params = { id: info_id }
      if (this.jingxuanId) {
        // 如果选择的是购买的套餐
        if (this.jingxuan_type === 'meal') {
          params.package_jingxuan_id = this.jingxuanId
        } else {
          params.jingxuan_id = this.jingxuanId
        }
      } else {
        params.freeSelected = 1
      }
      this.$ajax.post(
        'estateRelease/infoSelected',
        params,
        (res) => {
          if (res.data.code == 1) {
            if (res.data.is_selected === 1 && res.data.pay_status === 1) {
              // 不需要支付，直接设置精选成功
              uni.showToast({
                title: res.data.msg,
              })
              this.$refs.jingxuan.hide()
              setTimeout(() => {
                this.params.page = 1
                this.getData()
              }, 1500)
              return
            }
            // 设置支付接口和支付所需要的参数
            this.payApi = 'estateRelease/infoSelectedByWxPay'
            this.pay_params = {
              id: info_id,
              jingxuan_id: this.jingxuanId,
            }
            showModal({
              content: res.data.msg,
              confirm: () => {
                if (res.data.is_selected === 1) {
                  // 使用金币支付
                  this.cornPayOfJingxuan(info_id)
                } else {
                  this.handlePay()
                }
              },
            })
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
          }
        },
        (err) => {
          // console.log(err)
        },
        false
      )
    },
    cornPayOfJingxuan(info_id) {
      this.$ajax.post('estateRelease/infoSelectedByCorn', { jingxuan_id: this.jingxuanId, id: info_id }, (res) => {
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg,
          })
          this.$refs.jingxuan.hide()
          setTimeout(() => {
            this.params.page = 1
            this.getData()
          }, 1500)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    xiugai(id) {
      this.$navigateTo('/commercial/edit_info?info_id=' + id + '&parentid=' + this.params.parentid)
    },
    shanchu(id, status) {
      showModal({
        title: '提示',
        content: '是否确定删除此信息',
        confirm: () => {
          if (Number(id)) {
            this.ids = id
            this.status_type = 'del'
            this.$refs.xiajia.show()
          } else {
            this.handelDel(id, status)
          }
        },
      })
    },
    handelDel(id, status = '') {
      let params = { id: id, complate_status: status }
      if (this.complate_price) {
        params.complate_price = this.complate_price
      }
      this.$ajax.post('estateRelease/delInfo', params, (res) => {
        if (res.data.code == 1) {
          uni.showToast({
            title: res.data.msg,
          })
          // this.listData.splice(index,1)
          this.show_batch_options = false
          this.params.page = 1
          this.getData()
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    onClickTop(e) {
      this.top_type = e.type
      this.zhidingId = e.id
    },
    refreshAll() {
      if (this.refreshing) {
        return
      }
      this.refreshing = true
      let now_time = new Date().getTime()
      if (this.next_click_time && now_time - this.next_click_time < 1000 * 10) {
        uni.showToast({
          title: '您的操作太频繁，请稍后再试！',
          icon: 'none',
        })
        this.refreshing = false
        return
      }
      this.$ajax.get(
        'estateRelease/checkRefreshAll',
        { catid: this.params.catid },
        (res) => {
          this.refreshing = false
          this.next_click_time = new Date().getTime()
          if (res.data.code !== 1) {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
            this.refreshing = false
            return
          }
          if (res.data.is_free === 1) {
            // 可以免费刷新,直接刷新
            this.realRefreshAll()
          }
          if (res.data.is_free === 2) {
            // 提示需要使用积分刷新
            showModal({
              title: '温馨提示',
              content: res.data.msg,
              confirm: () => {
                this.realRefreshAll()
              },
            })
          }
          if (res.data.is_free === 3) {
            // 提示积分不足,需要去充值
            showModal({
              title: '温馨提示',
              content: res.data.msg,
              confirm: () => {
                this.$navigateTo('/user/recharge?type=2')
              },
            })
          }
        },
        (err) => {
          this.refreshing = false
        }
      )
    },
    realRefreshAll() {
      this.$ajax.post(
        'estateRelease/refreshAll',
        { catid: this.params.catid },
        (res) => {
          this.refreshing = false
          this.next_click_time = new Date().getTime()
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
          if (res.data.code == 1) {
            this.params.page = 1
            this.getData()
          }
        },
        (err) => {
          this.refreshing = false
        }
      )
    },
    toAdd() {
      uni.switchTab({
        url: '/pages/add/add',
      })
    },
    setStatus(is_show, info_id) {
      if (is_show === 0) {
        // 上架先判断是否失效
        this.goTuiGuangPage(info_id)
        // 检测和设置上架
        // this.checkInfo(info_id)
      } else {
        this.batchXiajia(info_id)
      }
    },
    // 去新的推广页面上架
		goTuiGuangPage(info_id) {
      console.log(info_id);
			const item = this.listData.find(item => item.id === info_id)
      console.log(this.listData);
      console.log(item);
			if(item.is_show==1){
				uni.showToast({
					title: '当前信息已上架',
					icon: 'none'
				})
				return
			}
			this.$store.state.myonehouse = item
			const url = `/user/my/tuiguang_detail?item_type=3&id=${info_id}&parentid=${this.params.parentid}`
			this.$navigateTo(url)
		},
    // 判断是否失效
    checkInfoInvalid(info_id) {
      this.$ajax.get('estateRelease/checkInfoInvalid', { id: info_id }, res => {
        if (res.data.code == 0) {
          this.ids = info_id
          this.select_active_time_value = ''
          this.$refs.acitive_time.show()
        } else {
          this.checkInfo(info_id)
        }
      })
    },
    // 判断是否可以不用花钱直接上架
    checkInfo(info_id, activetime_id) {
      this.$ajax.post('estateRelease/checkInfoShow', { id: info_id, activetime_id }, (res) => {
        if (res.data.code !== 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
          return
        }
        // 不需要付费直接可以上架
        if (res.data.is_release === 1 && res.data.pay_status === 1) {
          uni.showToast({
            title: res.data.msg,
          })
          this.params.page = 1
          this.getData()
        } else {
          this.payApi = 'estateRelease/setInfoShowByWxPay'
          this.pay_params = {
            order_id: res.data.order_id,
          }
          // 提示需要扣除金币或支付
          showModal({
            content: res.data.msg,
            confirm: () => {
              // is_release === 1 代表用户金币足够支付，否则调起支付
              if (res.data.is_release === 1) {
                if (activetime_id) {
                  this.cornPayOfActivetime(info_id, activetime_id)
                } else {
                  this.cornPayOfSheleves(info_id)
                }
                // this.cornPayOfSheleves(info_id)
              } else {
                if (this.ios_examine_isopen === 1) {
                  uni.showToast({
                    title: 'iOS系统暂不支持该功能',
                    icon: 'none'
                  })
                  return
                }
                this.handlePay()
              }
            },
          })
        }
      })
    },
    // 使用金币支付上架信息
    cornPayOfSheleves(info_id) {
      this.$ajax.get('estateRelease/setInfoShowByCorn', { id: info_id }, (res) => {
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg,
          })
          this.params.page = 1
          this.getData()
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    // 失效房源使用金币支付上架
    cornPayOfActivetime(info_id, activetime_id) {
      this.$ajax.get('estateRelease/infoShowAndActivityByCorn', { id: info_id, activetime_id }, res => {
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg
          })
          this.params.page = 1
          this.getData()
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    handlePay() {
      this.wxPay()
    },
    wxPay() {
      this.$ajax.post(this.payApi, this.pay_params, res => {
        if (res.data.code === 1) {
          let pay_info = res.data.data
          this.wx.chooseWXPay({
            // provider: 'wxpay',
            timestamp: pay_info.timeStamp,
            nonceStr: pay_info.nonceStr,
            package: pay_info.package,
            signType: pay_info.signType,
            paySign: pay_info.paySign,
            success: res => {
              uni.showToast({
                title: '支付成功'
              })
              this.$refs.zhiding.hide()
              this.$refs.jingxuan.hide()
              setTimeout(() => {
                // this.$navigateBack()
                this.params.page = 1
                this.getData()
              }, 1500)
            },
            fail: function (err) {
              console.log('支付失败：', err)
              uni.showToast({
                title: err.err_desc || err.errMsg,
                icon: 'none',
                duration: 5000
              })
            }
          })
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    // 批量不需要支付
    batchShangjia() {
      let ids = this.listData.filter((item) => item.checked).map((item) => item.id)
      if (!ids.length) {
        uni.showToast({
          title: '请至少选择一条信息',
          icon: 'none',
        })
        return
      }
      this.$ajax.post('estateRelease/setInfoBatchShow', { ids: ids.join(','), is_show: 1 }, (res) => {
        if (res.data.code === 1) {
          this.show_batch_options = false
          this.params.page = 1
          this.getData()
        }
        uni.showToast({
          title: res.data.msg,
          icon: res.data.code === 1 ? 'success' : 'none',
        })
      })
    },
    batchRefre() {
      let ids = this.listData.filter(item => item.checked).map(item => item.id)
      this.multipleSelection = ids;
      ids = ids.join(',')
      if (!ids) {
        uni.showToast({
          title: "请至少选择一条信息",
          icon: 'none'
        })
        return
      }
      //批量
      this.batchids = ids
      this.refretype = 'multi'
      this.$refs.sub_form.showPopup();
      this.$refs.sub_form.getVerify(); // 显示组件时执行 getVerify 方法
      this.show_batch_options = false
    },
    huakuaishow(id, index) {
      //单个
      this.batchids = id
      this.refretype = 'one'
      this.$refs.sub_form.showPopup();
      this.$refs.sub_form.getVerify(); // 显示组件时执行 getVerify 方法
      this.show_batch_options = false

    },
    /**
   * 
   * 批量刷新
   */
    referbatch(e) {
      this.uuid = e.uuid;
      this.verify_code = e.verify_code;
      //批量刷新
      let selectedCount = this.multipleSelection.length
      let data = {
        id: this.batchids, code: this.verify_code, uuid: this.uuid
      }
      this.$ajax.post('info_refresh/checkEstateBatchRefresh', data, res => {
        if (res.data.code !== 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
          return
        }
        if (res.data.is_free === 1) {
          showModal({
            title: `提示：已选择${selectedCount}条数据`,
            content: res.data.msg,
            confirm: () => {
              this.refrebatshreal(this.batchids, this.uuid, this.verify_code)
            },
            cancel: () => {
              this.multipleSelection = []
            }
          })
        }
        if (res.data.is_free === 2) {
          // 提示需要使用积分刷新
          showModal({
            title: `提示：已选择${selectedCount}条数据`,
            content: res.data.msg,
            confirm: () => {
              this.refrebatshreal(this.batchids, this.uuid, this.verify_code)
            },
            cancel: () => {
              this.multipleSelection = []
            }
          })
        }
        if (res.data.is_free === 3) {
          // 提示积分不足,需要去充值
          showModal({
            title: "温馨提示",
            content: res.data.msg,
            confirm: () => {
              this.$navigateTo('/user/recharge?type=2')
            }
          })
        }

      }, err => {
        this.refreshing = false
        uni.showToast({
          title: '请重新操作',
          icon: 'none',
          duration: 2500,
        })
      })


    },
    refrebatshreal(ids, uuid, code) {
      uni.showLoading({
        title: "刷新中..."
      })
      this.$ajax.post('info_refresh/batchRefreshEstate', { id: ids, uuid: uuid, code: code }, (res) => {
        uni.hideLoading()
        if (res.data.code == 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
            duration: 2500,
          })
          setTimeout(() => {
            this.params.page = 1
            this.getData()
          }, 2500)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
        }
      }, (err) => {

      }, false)

    },
    /**
   * 单个刷新
   */
    onereferbatch(e) {
      this.uuid = e.uuid;
      this.verify_code = e.verify_code;
      //单个刷新
      let data = {
        id: this.batchids, code: this.verify_code, uuid: this.uuid
      }
      this.$ajax.get('info_refresh/checkSingleEstateRefresh', data, res => {
        if (res.data.code !== 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
          return
        }
        if (res.data.is_free === 1) {
          this.onerefrebatshreal(this.batchids, this.uuid, this.verify_code)
        }
        if (res.data.is_free === 2) {
          showModal({
            title: "温馨提示",
            content: res.data.msg,
            confirm: () => {
              this.onerefrebatshreal(this.batchids, this.uuid, this.verify_code)
            }, cancel: () => {
              console.log('取消');
            }
          })
        }
        if (res.data.is_free === 3) {
          // 提示积分不足,需要去充值
          showModal({
            title: "温馨提示",
            content: res.data.msg,
            confirm: () => {
              this.$navigateTo('/user/recharge?type=2')
            }
          })
        }

      }, err => {
        this.refreshing = false
        uni.showToast({
          title: '请重新操作',
          icon: 'none',
          duration: 2500,
        })
      })
    },
    onerefrebatshreal(ids, uuid, code) {
      uni.showLoading({
        title: "刷新中..."
      })
      this.$ajax.post('info_refresh/singleEstateRefresh', { id: ids, uuid: uuid, code: code }, (res) => {
        uni.hideLoading()
        if (res.data.code == 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
            duration: 2500,
          })
          setTimeout(() => {
            this.params.page = 1
            this.getData()
          }, 2500)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
        }
      }, (err) => {

      }, false)

    },
    /**
     * 批量和单条下架
     */
    batchXiajia(info_id) {
      let ids
      if (!info_id) {
        ids = this.listData.filter((item) => item.checked).map((item) => item.id)
        ids = ids.join(',')
      } else {
        ids = info_id
      }
      if (!ids) {
        uni.showToast({
          title: '请至少选择一条信息',
          icon: 'none',
        })
        return
      }
      showModal({
        title: '温馨提示',
        content: '下架后重新上架可能额外收费，确定要下架吗？',
        confirm: () => {
          if (Number(ids)) {
            this.ids = ids
            this.status_type = 'xiajia'
            // 如果是单条选择状态
            this.$refs.xiajia.show()
          } else {
            // 批量直接执行下架
            this.handleXiajia(ids)
          }
        },
      })
    },
    onSelectStatus(item) {
      if (item.value === 1) {
        this.show_price = true
        return
      }
      this.subStatus(item)
    },
    subStatus(item) {
      if (this.status_type === 'xiajia') {
        this.handleXiajia(this.ids, item.value)
      }
      if (this.status_type === 'del') {
        this.handelDel(this.ids, item.value)
      }
      this.$refs.xiajia.hide()
    },
    onStatusHide() {
      this.ids = ''
      this.complate_price = ''
      this.show_price = false
    },
    // 执行下架接口
    handleXiajia(ids, status = '') {
      let params = { ids: ids, is_show: 0, complate_status: status }
      if (this.complate_price) {
        params.complate_price = this.complate_price
      }
      this.$ajax.post('estateRelease/setInfoBatchShow', params, (res) => {
        if (res.data.code === 1) {
          this.show_batch_options = false
          this.params.page = 1
          this.getData()
        }
        uni.showToast({
          title: res.data.msg,
          icon: res.data.code === 1 ? 'success' : 'none',
        })
      })
    },
    batchDel() {
      let ids = this.listData.filter((item) => item.checked).map((item) => item.id)
      if (!ids.length) {
        uni.showToast({
          title: '请至少选择一条信息',
          icon: 'none',
        })
        return
      }
      this.shanchu(ids)
    },
    setActiveTime() {
      if (!this.select_active_time_value) {
        uni.showToast({
          title: '改信息已经失效，请选择有效期',
          icon: 'none'
        })
        return
      }
      this.$refs.acitive_time.hide()
      this.checkInfo(this.ids, this.select_active_time_value)
    },
    stopMove() { },
    showShare() {
      this.$refs.douyinPop.show()
    },

    chooseImg() {
      this.$navigateTo("/user/sendToDouyin?id=" + this.detail.id)
      this.$refs.douyinPop.hide()
    },
    toSaiHongbao() {
      this.$navigateTo("/user/saihongbao?id=" + this.detail.id + "&type=3",)
    },
    getShortLink() {
      let time = parseInt(+new Date() / 1000)
      let house_type = "ershou"
      if (this.detail.parentid == 2) {
        house_type = "renting"
      }
      // #ifdef H5
      if (this.currentUserInfo.sid) {
        this.link = "https://" + window.location.host + "/h5/pages/" + house_type + "/detail?id=" + this.detail.id + "&isShare=1&shareType=" + this.currentUserInfo.shareType + "&shareId=" + this.currentUserInfo.sid + "&f_time=" + time
      } else {
        this.link = "https://" + window.location.host + "/h5/pages/" + house_type + "/detail?id=" + this.detail.id
      }
      // #endif
      this.$ajax.get("build/shortUrl.html", { page_url: this.link }, (res) => {
        if (res.data.code == 1) {
          this.link = res.data.short_url
        }
      })
    },
    // #ifdef H5
    copyLink() {
      this.show_share_tip = true
      this.getWxConfig(
        ['chooseWXPay', 'updateAppMessageShareData', 'updateTimelineShareData'],
        wx => {
          console.log('执行回调')
          this.wx = wx
        },
        1
      )
      // this.copyContent(link, ()=>{
      // 	this.$refs.douyinPop.hide()
      // 	uni.showToast({
      // 		title: '复制成功,去发送给好友吧',
      // 		icon: 'none'
      // 	})
      // })
    },
    // #endif
    // #ifndef H5
    copyContent(cont) {
      uni.setClipboardData({
        data: cont,
        success: res => {
          // uni.showToast({
          //   title: "复制成功",
          //   icon: "none"
          // })
        }
      })
    },
    // #endif
    // #ifdef H5
    copyContent(cont, callback) {
      let oInput = document.createElement('textarea')
      oInput.value = cont
      document.body.appendChild(oInput)
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      oInput.blur()
      oInput.remove()
      if (callback) callback()
    },
    // #endif
    // #ifdef APP-PLUS
    appShare(type = 'WXSceneSession') {
      let href = '';
      let time = parseInt(+new Date() / 1000)
      if (this.currentUserInfo.sid) {
        href = config.apiDomain + "/h5/pages/ershou/detail?id=" + this.detail.id + "&isShare=1&shareType=" + this.currentUserInfo.shareType + "&shareId=" + this.currentUserInfo.sid + "&f_time=" + time
      } else {
        href = config.apiDomain + "/h5/pages/ershou/detail?id=" + this.detail.id
      }
      uni.share({
        provider: 'weixin',
        type: 0,
        title: this.detail.title || "",
        scene: type,
        imageUrl: formatImg(this.detail.img[0], 'w_220') || "",
        summary: this.detail.shi + '室' + this.detail.ting + '厅' + this.detail.wei + '卫/' + this.detail.mianji + 'm²/' + this.detail.fangjia + '万元',
        href: href,
        success: function (res) {
          console.log("success:" + JSON.stringify(res));
          uni.showToast({
            title: '分享成功',
            icon: 'none'
          })
        },
        fail: function (err) {
          uni.showToast({
            title: '分享失败:' + JSON.stringify(err),
            icon: 'none'
          })
          console.log("fail:" + JSON.stringify(err));
        }
      })
    },
    // #endif
    handleCreat() {
      let type = 2
      if (this.detail.parentid == 2) {
        type = 3
      }
      this.$navigateTo(`${location.origin}/wapi/poster/branch?type=${type}&id=${this.detail.id}`)
      this.$refs.douyinPop.hide()
    },
    showCopywriting() {
      this.getInfoDetail(this.detail.id, () => {
        const query = uni.createSelectorQuery().in(this)
        query.select('#copy-text').fields({ rect: true, scrollOffset: true, size: true }, data => {
          this.text_popup_height = data.height + 'px'
        }).exec();
        this.copy_success = false
        this.$refs.text_popup.show()
        this.$refs.douyinPop.hide()
      })
    },
    copywriting() {
      let type = `【售价】${this.detail.fangjia ? this.detail.fangjia + '万' : '面议'}${this.detail.danjia ? ' 单价' + this.detail.danjia + '元/m²' : ''}`
      if (this.detail.parentid == 2) {
        type = `【租金】${this.detail.zujin ? this.detail.zujin + '元/月' : '面议'}`
      }

      const text = `${this.detail.title}
${this.detail.title ? '【小区】' + this.detail.title + '\n' : ''}【户型】${this.detail.shi}室${this.detail.ting}厅${this.detail.wei}卫${this.detail.mianji}m²
${type}
【类型】${this.detail.catname}
【卖点】${this.detail.label.length > 0 ? this.detail.label.map(item => item.name).join(' ') + '\n' : ''}【楼层】${this.detail.floor ? this.detail.floor + '/共' + this.detail.louceng + '层' : '共' + this.detail.louceng + '层'}
【查看】${this.link}`
      this.copyContent(text, () => {
        this.copy_success = true
      })
    },
  },
  onPullDownRefresh() {
    this.params.page = 1
    this.getData()
  },
  onReachBottom() {
    this.params.page++
    this.getData()
  }
}


</script>
<style lang="scss" scoped>
.p-top-180 {
  padding-top: 160upx;
}

.pagehead {
  font-weight: 700;
  font-size: 16px;
  line-height: 30px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.house_item {
  padding: 0 48rpx;
  background-color: #fff;

  .house {
    width: 100%;
    padding-top: 0rpx !important;
  }

  &.shift_right {
    width: 100%;
  }

  .check-box {
    padding: 32rpx 10rpx;
    margin-right: 10rpx;

    .check {
      width: 30rpx;
      height: 30rpx;
      border: 4rpx solid #dedede;
      border-radius: 50%;
    }
  }
}

.refresh-time {
  font-size: $uni-font-size-sm;
  color: $uni-text-color-grey;

  .status {
    font-size: 22rpx;

    .status1 {
      color: #00CAA7;
    }

    .status0 {
      color: $uni-color-primary;
    }
  }

  .time {
    // flex: 1;
    margin-left: 24rpx;
    font-size: 22rpx;
  }

  .top-operate {
    position: relative;
    left: 6px;
    top: 6rpx;
  }
}

.operate-box {
  align-items: flex-end;
  justify-content: space-between;
  background-color: #fff;
}

.operate-box {
  padding: 24rpx 0;

  .item {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22rpx;
    color: #999;

    ~.item {
      margin-left: 16rpx;
    }

    .icon-box {
      height: 48rpx;
      width: 48rpx;
      overflow: hidden;
      margin-right: 10rpx;
      text-align: center;
      line-height: 48rpx;
      border-radius: 50%;
      background-color: #f5f5f5;
      border-radius: 100%;

      image {
        width: 100%;
        height: 100%;
      }
    }

    text {
      font-size: 22rpx;
    }

    &.color-green {
      color: #00caa9;

      .icon-box {
        background-color: #00caa9;
      }
    }

    &.color-red {
      color: $uni-color-primary;

      .icon-box {
        background-color: $uni-color-primary;
      }
    }

    &.color-blue {
      margin-left: 56rpx;
      color: #2fc6f3;

      .icon-box {
        background-color: #2fc6f3;
      }
    }
  }

  .status_btn {
    padding: 10rpx 10rpx;
    line-height: 1;
    border: 1rpx solid $uni-color-primary;
    border-radius: 4rpx;
    font-size: 22rpx;
    color: $uni-color-primary;

    ~.status_btn {
      margin-left: 16rpx;
    }
  }
}

.top-operate-box {
  align-items: flex-end;
  // justify-content: space-between;
  padding-top: 14rpx;
  padding-bottom: 26rpx;

  .top-operate-box_left {
    align-items: center;
  }

  .top-operate {
    justify-self: flex-end;

    .item {
      padding: 10rpx 10rpx 0 10rpx;
      flex-direction: row;
      align-items: center;
      font-size: 22rpx;
      color: #999;

      &.item_refresh {
        .item_name {
          color: #0280FF;
        }
      }

      .item_name {
        font-size: 22rpx;
        color: #d8d8d8;
        margin-left: 8rpx;
      }
    }
  }

}

.status_btn {
  padding: 10rpx 10rpx;
  line-height: 1;
  border: 1rpx solid $uni-color-primary;
  border-radius: 4rpx;
  font-size: 22rpx;
  color: $uni-color-primary;

  ~.status_btn {
    margin-left: 16rpx;
  }
}

.status_btns {
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
  background: linear-gradient(270deg, #F53F3F 0%, #F5A2A2 100%);
  box-shadow: 0rpx 8rpx 16rpx 0rpx rgba(245, 74, 74, 0.20);
  color: #FFFFFF;
  font-size: 24rpx;
  height: 36rpx;
  margin-left:16rpx;

  /* ~.status_btns {
    border-radius: 8rpx;
    box-shadow: none !important;
    margin-left: 24rpx;
    background: url('@/static/icon/myfy_more.png') no-repeat;
    background-size: contain;
    width: 40rpx;
    height: 40rpx;
    position: relative;
  } */
}

.fenxiang_options-box {
  position: absolute;
  top: 58%;
  right: 35rpx;
  transition: 0.26s;
  transform: translateY(-103%);
  z-index: -60;
  line-height: 50rpx;

  &.show {
    transform: translateY(0);
    z-index: 2;
  }

  .batch_options {
    position: relative;
    padding: 0 24rpx;
    border-radius: 10rpx;
    background-color: #ffffff;
    color: #9c9c9c;


    .arrow {
      border-width: 20rpx;
      border-style: solid;
      border-color: transparent transparent #9c9c9c transparent;
      position: absolute;
      top: -36rpx;
      right: 20rpx;
    }

    .options-item {
      padding: 10rpx 0;
      display: flex;
      align-items: center;

      .text {
        padding: 0 24rpx;
      }
    }
  }

}

.info_title {
  padding: 16rpx 48rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.scroll {
  margin-top: 32rpx;
  height: 136rpx;

  &.murows {
    height: 176rpx;
  }
}

.day_list {
  justify-content: space-between;
  flex-wrap: wrap;
  overflow-y: scroll;

  .day_item {
    line-height: 1;
    margin-bottom: 24rpx;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: 112rpx;
    min-width: 152rpx;
    text-align: center;
    border-radius: 4rpx;
    border: 1rpx solid #d8d8d8;
    max-width: 152rpx;

    &.vacancy {
      border: 0;
      margin: 0;
      padding: 0;
      height: 0;
    }

    &.active {
      background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
      border-color: $uni-color-primary;

      .day {
        color: $uni-color-primary;
      }

      .money {
        background-color: $uni-color-primary;
        color: #fff;
      }
    }

    .day {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #666;

      .value {
        font-size: 40rpx;
        font-weight: bold;

        &.small {
          font-size: 32rpx;
          font-weight: initial;
        }
      }

      .unit {
        margin-bottom: -8rpx;
        margin-left: 10rpx;
        font-size: 22rpx;
      }
    }

    .money {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      padding: 4rpx 10rpx;
      font-size: 22rpx;
      color: #999;
      background-color: #d8d8d8;
    }
  }
}

.btn-box {
  padding: $uni-spacing-row-base 0;
}

.btn-box .btn.btn-lg {
  width: 100%;
  border-radius: 10upx;
  height: 88upx;
  text-align: center;
  line-height: 88upx;
  box-sizing: border-box;
  font-size: 32rpx;
  color: #fff;
  background: #FB656A;
  box-shadow: 0 4px 16px 0 rgba(251, 101, 106, 0.40);
  border-radius: 44rpx;
}

.btn-box .crude {
  width: 100%;
  line-height: 88rpx;
  text-align: center;
  color: #999;
  margin-bottom: 24rpx;
}

.row {
  // height: 62upx;
  // line-height: 62upx;
  padding: 24upx 30upx;
}

.tip {
  font-size: 22rpx;
  color: #999;
}

.tip .highlight {
  color: $uni-color-primary;
  font-size: $uni-font-size-lg;
}

.red {
  color: #f44
}


.refresh-all {
  height: 60upx;
  width: 60upx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.9);
  box-shadow: 0 4px 16px 0 rgba(251, 101, 106, 0.40);
  z-index: 89;
}


.row .tip {
  margin-top: 10upx;
  color: #999;
  font-size: 24upx
}

.info-price .total.n_c_right {
  float: initial
}




/* #ifdef H5 */
.top-box {
  position: fixed;
  top: 44px;
  width: 100%;
  background-color: #fff;
  z-index: 100;
}

.screen-tab {
  top: 0;
  margin-top: 174upx;
  box-sizing: border-box;
  padding: 0 28rpx;
}

.screen-panel {
  top: 0;
  margin-top: 250rpx;

  &.show {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

/* #endif */
/* #ifndef H5 */
.top-box {
  position: fixed;
  top: var(--window-top);
  width: 100%;
  background-color: #fff;
  z-index: 100;
}

.screen-tab {
  top: var(--window-top);
  margin-top: 93rpx;
  box-sizing: border-box;
  padding: 0 28rpx;
}

.screen-panel {
  top: var(--window-top);
  margin-top: 160rpx;
}

/* #endif */
.screen-tab-item {
  background-color: #fff;
}

.batch_icon-box {
  width: 50rpx;
  height: 50rpx;
  padding: 10rpx;
  transform: rotate(45deg);
  transition: 0.26s;

  &.open {
    transform: rotate(90deg);
  }
}

.batch_options-box {
  position: absolute;
  top: 86rpx;
  right: 35rpx;
  transition: 0.26s;
  transform: translateY(-150%);
  z-index: -60;

  &.show {
    transform: translateY(0);
    z-index: 2;
  }

  .batch_options {
    position: relative;
    padding: 0 24rpx;
    border-radius: 10rpx;
    background-color: #333;
    color: #fff;

    .arrow {
      border-width: 20rpx;
      border-style: solid;
      border-color: transparent transparent #333 transparent;
      position: absolute;
      top: -36rpx;
      right: 20rpx;
    }

    .options-item {
      padding: 10rpx 0;
      display: flex;
      align-items: center;

      .text {
        padding: 0 24rpx;
      }
    }
  }
}


.xaijia {
  background-color: #f7f7f7;

  .title {
    padding: 24rpx;
    font-size: 30rpx;
    text-align: center;
    color: #666;
  }

  .status-list {
    background-color: #fff;
  }

  .status-item {
    padding: 24rpx;
    text-align: center;
  }

  .price_form {
    justify-content: center;
    align-items: center;

    input {
      font-size: 28rpx;
      border: 1rpx solid #f2f2f2;
      padding: 8rpx;
      width: 220rpx;
    }

    .unit {
      font-size: 26rpx;
      margin-left: 16rpx;
    }

    .btn {
      margin-left: 24rpx;
      padding: 10rpx 24rpx;
      border-radius: 8rpx;
      font-size: 26rpx;
      background-color: $uni-color-primary;
      color: #fff;
    }
  }
}


// 复制文案
.copy-text-box {
  padding: 24rpx 32rpx;
  background-color: #fff;
  width: 80%;

  // height: 100%;
  margin-left: 10%;
  box-sizing: border-box;
  border-radius: 16rpx;

  .title {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }

  .info-row {
    line-height: 1.6;
    color: #333;

    .label {
      color: #999;
    }

    .value {
      flex: 1;

      &.highlight {
        color: $uni-color-primary;
      }
    }
  }

  .button {
    align-items: center;
    justify-content: center;
    line-height: 64rpx;
    width: 236rpx;
    text-align: center;
    border-radius: 32rpx;
    margin: 32rpx auto;
    background: #FB656A;
    box-shadow: 0 2px 8px 0 rgba(251, 101, 106, 0.40);
    color: #fff;
  }

  .disabled-btn {
    background-color: #f5f5f5;
    box-shadow: none;
    color: #999;

    >.text {
      margin-left: 12rpx;
    }
  }
}


.tuiguangs {
  border-radius: 8rpx;
  box-shadow: none !important;
  /*
  background: url('@/static/icon/myfy_more.png') no-repeat center center;
  */
  background-size: 86% 100%;
  width: 70rpx;
  height: 40rpx;
  position: relative;
}

.dropdown-container {
  position: relative;
}

.dropdown {
  cursor: pointer;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  display: inline-block;
  user-select: none;
  background-color: #fff;
}

.dropdown .selected-value {
  font-size: 14px;
  color: #333;
}

.dropdown .arrow {
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-top: 10rpx solid #333;
  margin-left: 20rpx;
  display: inline-block;
  vertical-align: middle;
  transition: transform 0.3s;
}

.dropdown.up .arrow {
  transform: rotate(180deg);
}

.picker-options {
  position: absolute;
  background-color: #fff;
  border: 2rpx solid #ccc;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 100%;
  overflow-y: auto;
  width: 210rpx;
  right: 0rpx;
  top: 45rpx;
}

.picker-options.up {
  transform: translateY(-100%);
}

.picker-options view {
  padding: 20rpx;
  font-size: 28rpx;
  color: #797f87;
  border-bottom: 2rpx solid #B3B9C1;
  width: 140rpx;
  margin: 0 auto;
  text-align: center;
}

.picker-options view:last-child {
  border-bottom: none !important;
}
</style>
