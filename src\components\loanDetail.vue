<template>
	<view class="p-content">
    <view v-show="tab===0">
      <view class="m-payment flex-row">
        <text>月供</text>
        <text class="m_u">￥</text>
        <text class="payment">{{loan.yuegong}}</text>
        <text>(30年)</text>
      </view>
      <view class="detail flex-row">
        <view class="detail-item">
          <view class="label">总价</view>
          <view class="value">
            <text>{{total.toFixed(2)}}万</text>
          </view>
        </view>
        <view class="detail-item">
          <view class="label">首付</view>
          <view class="value">
            <text>{{downPayments}}万</text>
            <text class="tip">({{shoufubili}}成)</text>
          </view>
        </view>
        <view class="detail-item">
          <view class="label">贷款</view>
          <view class="value">
            <text>{{loan.loanLimit}}万</text>
          </view>
        </view>
        <view class="detail-item">
          <view class="label">利息</view>
          <view class="value">
            <text>{{loan.lixi}}万</text>
          </view>
        </view>
      </view>
    </view>
    <view v-show="tab===1">
      <view class="m-payment">
        <text>首月</text>
        <text class="m_u">￥</text>
        <text class="payment">{{loan2.firstMonth}}</text>
        <text>(30年 递减{{loan2.dijian}})</text>
      </view>
      <view class="detail">
        <view class="detail-item">
          <view class="label">总价</view>
          <view class="value">
            <text>{{total.toFixed(2)}}万</text>
          </view>
        </view>
        <view class="detail-item">
          <view class="label">首付</view>
          <view class="value">
            <text>{{downPayments}}万</text>
            <text class="tip">({{shoufubili}}成)</text>
          </view>
        </view>
        <view class="detail-item">
          <view class="label">贷款</view>
          <view class="value">
            <text>{{loan2.loanLimit}}万</text>
          </view>
        </view>
        <view class="detail-item">
          <view class="label">利息</view>
          <view class="value">
            <text>{{loan2.lixi}}万</text>
          </view>
        </view>
      </view>
    </view>
	</view>
</template>

<script>
	export default {
		props:{
      tab:{
        type:Number,
        default:0
      },
			total:{
				type: [Number,String],
				default:0
			},
			downPayments:{
				type: [Number,String],
				default:0
			},
			lilv:{
				type:Number,
				default:4.9/100/12
      },
      shoufubili:{
        type:Number,
				default:3
      },
			time:{
				type:Number,
				default:30*12
			}
		},
		data() {
			return {
				
			};
		},
		computed:{
			loan(){
				let loanLimit = parseFloat(this.total)-this.downPayments
				let yuegong = loanLimit*this.lilv*Math.pow((1+this.lilv),this.time)/(Math.pow((1+this.lilv),this.time)-1)
				let lixi = yuegong*this.time-loanLimit
				return {
					loanLimit:loanLimit.toFixed(2),
					yuegong:(yuegong*10000).toFixed(2),
					lixi:lixi.toFixed(2)
				}
			},
			loan2(){
				let loanLimit = parseFloat(this.total)-this.downPayments
				let firstMonth = (loanLimit/this.time)+(loanLimit-0)*this.lilv
				let dijian = loanLimit/this.time*this.lilv
				let lixi = ((loanLimit/this.time+loanLimit*this.lilv)+loanLimit/this.time*(1+this.lilv))/2*this.time-loanLimit
				return {
					loanLimit:loanLimit.toFixed(2),
					firstMonth:(firstMonth*10000).toFixed(2),
					dijian:(dijian*10000).toFixed(2),
					lixi:lixi.toFixed(2)
				}
			}
		}
	}
</script>

<style lang="scss">
	.p-content {
    .m-payment {
      width: 100%;
      justify-content: center;
      align-items: center;
      padding: 20rpx;
      box-sizing: border-box;
      text-align: center;
      border-bottom: 1rpx solid #f3f3f3;
      font-size: 22rpx;
      color: #999;
      .m_u{
        margin-left: 20rpx;
        color:#333
      }
      .payment{
        margin-right: 20rpx;
        font-size: 40rpx;
        color: #333;
      }
    }
    .detail{
      display: flex;
      box-sizing: border-box;
      padding: 20rpx 0;
      .detail-item{
        flex: 1;
        text-align: center;
        .label{
          margin-bottom: 10rpx;
          font-size: 22rpx;
          color: #999;
        }
        .value{
          .tip{
            font-size: 22rpx;
            color: #999;
          }
        }
      }
    }
  }
		
</style>
