<template>
  <view class="list">
    <view v-for="(item, index) in list" :key="index" class="house flex-row" @click="onClick(item.id)">
      <view class="image_box">
        <image src="https://images.tengfangyun.com/attachment/build/20210522/899696affb5dea4289981e83be64ee7238052969.png?x-oss-process=style/w_220" mode="aspectFill"></image>
      </view>
      <view class="info flex-1">
        <view class="flex-row desc">
          <view class="huxing">{{item.shi}}室{{item.ting}}厅{{item.wei}}卫</view>
          <view class="mianji">
            <text class="label">建面</text>
            <text class="val">{{item.mianji}}m²</text>
          </view>
        </view>
        <view class="ld">
          <text>{{item.desc}}</text>
        </view>
        <view class="price">
          <text>{{item.prices | priceFilter}}</text>
        </view>
      </view>
      <view class="seleter">
        <view class="ring" :class="{active: value.includes(item.id)}">
          <view class="ring_center"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SaleHouse',
  components: {},
  data () {
    return {
      selecteds: []
    }
  },
  props: {
    list: {
      type:Array,
      default: ()=>[]
    },
    value: {
      type:Array,
      default: ()=>[]
    }
  },
  filters:{
    priceFilter(value){
      if(value.length===0||value.length[0] === 0){
        return '待定'
      }else{
        return value.join('-')+'万'
      }
    }
  },
  methods: {
    // init(){
    //   this.selecteds = []
    // },
    onClick(id){
      this.selecteds = Array.from(this.value)
      if(this.selecteds.includes(id)){
        this.selecteds = this.selecteds.filter(item=>item!==id)
      }else{
        this.selecteds.push(id)
      }
      this.$emit('input', this.selecteds)
      this.$emit('onselect', this.selecteds)
    }
  }
}
</script>

<style scoped lang="scss">
.house{
  padding: 24rpx 0;
  align-items: center;
}
.flex-row{
  display: flex;
  flex-direction: row;
}
.image_box{
  width: 212rpx;
  height: 152rpx;
  margin-right: 12rpx;
  image{
    width: 100%;
    height: 100%;
  }
}

.info{
  .desc{
    margin-bottom: 20rpx;
  }
  .huxing{
    font-weight: bold;
    font-size: 32rpx;
  }
  .mianji{
    margin-left: 24rpx;
    font-weight: bold;
    .label{
      font-size: 24rpx;
    }
    .val{
      margin-left: 12rpx;
      font-size: 32rpx;
    }
  }
  .ld{
    margin-bottom: 12rpx;
    font-size: 24rpx;
    color: #989898;
  }
  .price{
    font-weight: bold;
    font-size: 28rpx;
    color: $uni-color-primary;
  }
}

.seleter{
  .ring{
    width: 32rpx;
    height: 32rpx;
    padding: 4rpx;
    box-sizing: border-box;
    border-radius: 50%;
    border: 1rpx solid #c2c2c2;
    &.active{
      border-color: $uni-color-primary;
      .ring_center{
        opacity: 1;
      }
    }
    .ring_center{
      opacity: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background-color: $uni-color-primary;
    }
  }
}
</style>