<template>
	<view class="my-lists">
		<block v-for="(item,index) in listData" :key="index">
		<view class="list-item bottom-line" :class="showType==1?'flex-box':''" @click="toDetail(index)">
			<view class="img-box">
				<image :src="item.img | imgUrl" mode="aspectFill"></image>
			</view>
			<view class="info-box" :class="showType==1?'flex-1':''">
				<view class="title">
					<view class="title-text">{{item.title}}</view>
				</view>
				<template>
				<view class="desc">
					<text>{{item.shi}}室{{item.ting}}厅{{item.wei}}卫</text>
					<text class="fenge">|</text>
					<text>{{item.mianji}}㎡</text>
					<text class="fenge">|</text>
					<text>{{item.cmname||''}}</text>
					<text v-if="dataType==1" class="price">{{item.fangjia?item.fangjia+'万元':'面议'}}</text>
					<text v-if="dataType==2" class="price">{{item.zujin?item.zujin+'元/月':'面议'}}</text>
				</view>
				<view class="other">
					<my-icon type="chengshi"></my-icon>
					<text>{{item.areaname}}</text>
				</view>
				</template>
				<slot :item="item"></slot>
			</view>
		</view>
		</block>
	</view>
</template>

<script>
	import {formatImg} from "../common/index.js"
	import myIcon from "./icon.vue"
	export default {
		props:{
			listData: Array,
			showType: {
				type: Number,
				default: 1
			},
			dataType:{
				type:Number,
				default:1
			}
		},
		components:{
			myIcon
		},
		data() {
			return {
				
			};
		},
		filters:{
			imgUrl(val){
				return formatImg(val,'w_6401')
			}
		},
		methods:{
			toDetail(index){
				this.$emit('click',{index:index})
			}
		}
	}
</script>

<style lang="scss">
	.my-lists .flex-box .img-box{
		width: 240upx;
		height: 180upx;
		margin-right: 20upx;
	}
	.my-lists .img-box{
		width: 100%;
		height: 60vw;
		margin-right: 20upx;
	}
	.my-lists .img-box image{
		width: 100%;
		height: 100%;
	}
	.my-lists .title{
		display: flex;
		margin-top: 20upx;
		font-weight: bold;
	}
	.my-lists .title .title-text{
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-size: $uni-font-size-blg;
	}
	.my-lists .price{
		margin-left: 20upx;
		float: right;
		font-size: $uni-font-size-blg;
		color: $uni-color-primary;
	}
	.my-lists .desc{
		margin: 10upx 0;
		font-size: $uni-font-size-sm;
	}
	.my-lists .other{
		font-size: $uni-font-size-sm;
		color: $uni-text-color-grey
	}
	.my-lists .fenge{
		padding: 0 10upx
	}
</style>
