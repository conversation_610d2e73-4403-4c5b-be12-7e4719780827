<template>
<view class="client_list">
    <view class="client-list">
        <view class="client-item flex-box" v-for="client in client_list" :key="client.uid" @click="toFrirndDetail(client)">
            <view class="flex-box flex-1">
                <image class="head-img" :src="client.prelogo | imgUrl('w_120')" mode="aspectFill"></image>
                <view>
                    <view class="name">{{client.uname}}</view>
                    <view class="tel">{{client.ctime}}</view>
                </view>
            </view>
            <view class="flex-box" v-if="client.tel">
                <view @click.stop.prevent="handleTel(client.tel)" class="icon-box">
                    <my-icon type="dianhua1" color="#f65354" size="26"></my-icon>
                </view>
            </view>
        </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
</view>
</template>

<script>
import {
  formatImg,
  navigateTo,
} from '../common/index.js'
import {uniLoadMore,} from '@dcloudio/uni-ui'
import myIcon from '../components/icon.vue'
export default {
    data() {
        return {
            client_list:[],
            page:1,
            rows:20,
            get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
        }
    },
    onLoad(options){
        this.online_id = options.online_id||''
        this.getData()
    },
    components: {
        uniLoadMore,
        myIcon,
    },
    filters: {
        imgUrl(val, param = "") {
            return formatImg(val, param)
        }
    },
    methods: {
        getData(){
            if(this.page == 1){
                this.clinet_list=[]
            }
            this.get_status = "loading"
            this.$ajax.get('onlineMy/customerList.html',{online_id:this.online_id,page:this.page,rows:this.rows},res=>{
                if(res.data.code === 1){
                    this.client_list = res.data.list
                    if(res.data.list.length<this.rows){
                        this.get_status = "noMore"
                    }else{
                        this.get_status = "more"
                    }
                }else{
                    this.get_status = "noMore"
                }
            })
        },
        toFrirndDetail(client){
            console.log(client)
            navigateTo(`/adviser/client_detail?id=${client.uid}&online_id=${client.online_id}`)
        },
        handleTel(tel){
            uni.makePhoneCall({
                phoneNumber: tel
            })
        }
    },
    onReachBottom(){
        this.page++
        this.getData()
    }
}
</script>

<style scoped lang="scss">
.client-list{
    background-color: #fff;
    .client-item{
        justify-content: space-between;
        align-items: center;
        padding: 24upx 32upx;
        .head-img{
            width: 90upx;
            height: 90upx;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 32upx;
        }
        .name{
            font-size: 30upx;
            line-height: 1.8;
            font-weight: bold;
        }
        .tel{
            color: #666;
        }
    }
    .icon-box{
        margin-left: 28upx;
    }
}
</style>
