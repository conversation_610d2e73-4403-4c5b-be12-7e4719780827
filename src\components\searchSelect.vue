<template>
<view class="search_select">
    <view class="select-row" @click="showPopup()">{{option.id?option.name:'请选择'}}</view>
    <my-popup ref="select">
        <view class="select-box">
            <input type="text" class="search" @input="handleInput" placeholder="请输入关键字">
            <scroll-view class="select-list_box" scroll-y>
                <view class="select-list">
                    <view v-for="(item, index) in options" :key="index" class="list-item bottom-line" @click="handleSelect(item)">{{item.name}}</view>
                </view>
            </scroll-view>
        </view>
    </my-popup>
</view>
</template>

<script>
import myPopup from './myPopup'
export default {
    props:{
        range: Array,
        option:Object, //当前选项
        fromRemote:{ //是否从远程获取
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            search_key:""
        }
    },
    components: {
        myPopup
    },
    computed: {
        options(){
            if(this.fromRemote){
                return this.range
            }
            if(!this.search_key){
                return this.range
            }
            return this.range.filter(item=>{
                return item.name.includes(this.search_key)
            })
        }
    },
    methods: {
        handleInput(e){
            this.search_key = e.detail.value
            this.$emit('onInput',e)
        },
        handleSelect(data){
            this.$refs.select.hide()
            this.$emit("onSelect",data)
        },
        showPopup(){
            this.$refs.select.show()
        }
    }
}
</script>

<style lang="scss">
.select-box{
    padding: 20upx;
    background-color: #fff;
}
.select-row{
    padding: 20upx 24upx;
    color: #666
}
.select-list_box{
    height: 300upx;
    overflow-x: hidden;
    .select-list{
        .list-item{
            padding: 20upx;
        }
    }
}
.search{
    height: 62upx;
    padding: 8upx;
    margin-right: 10upx;
}

</style>
