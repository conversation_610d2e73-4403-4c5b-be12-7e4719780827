<template>
<view class="house_map-box" :style="{height:height}" id="house_map-box">
    <view class="house_map" id="move_el" :style="{height:img_info.height+'px',width:img_info.width+'px',left:img_info.left||'50%',marginLeft:img_info.margin_left||(0-img_info.width/2)+'px',top:img_info.top||0+'px'}">
        <image class="map_img" :src="map_img | imgUrl('w_1300')"></image>
        <view class="mark" :class="now_point==point.id?'now_mark':'mark'+point.sale_status" @click="toLoudong(index)" v-for="(point, index) in point_list" :key="index" :style="{top:point.top-offset_y+'px',left:point.left-offset_x+'px'}">
            <text>{{point.name}}</text>
            <text class="arrow"></text>
        </view>
    </view>
    <view class="filter flex-box" v-if="show_screen">
        <checkbox-group class="filter_items flex-box" @change="handleChange" :class="{hide:!show_filter}">
            <label class="item color1"><checkbox class="checkbox" value="1" color="#333" checked /><text>待售</text></label>
            <label class="item color2"><checkbox class="checkbox" value="2" color="#333" checked /><text>在售</text></label>
            <label class="item color3"><checkbox class="checkbox" value="3" color="#333" checked /><text>尾盘</text></label>
            <label class="item color4"><checkbox class="checkbox" value="4" color="#333" checked /><text>售完</text></label>
        </checkbox-group>
        <view @click="show_filter=!show_filter" class="show_btn" hover-class="hover" hover-stay-time="200">{{show_filter?'收起':'展开'}}</view>
    </view>
</view>
</template>

<script>
import {
  navigateTo,
  formatImg,
} from '../common/index.js'
export default {
    data() {
        return {
            img_info:this.map_info,
            offset_x:0,
            offset_y:0,
            current_status:[1,2,3,4],
            show_filter:true
        }
    },
    props:{
        map_info:Object,
        map_img:String,
        mark_point:Array,
        now_point:{
            type:[Number,String],
            default:0
        },
        height:{
            type:[String],
            default:"65vw"
        },
        show_screen:{
            type:[Boolean],
            default:true
        }
    },
    watch:{
        map_info(val){
            this.img_info = val
        }
    },
    computed:{
        point_list(){
            // this.img_info = this.map_info;
            return this.mark_point.filter(item=>this.current_status.includes(item.sale_status))
        }
    },
    mounted(){
        this.offset_x = uni.upx2px(120/2)
        this.offset_y = uni.upx2px(50+8)
        const HOUSSE_MAP_BOX =uni.createSelectorQuery().in(this).select('#house_map-box')
        HOUSSE_MAP_BOX.boundingClientRect(data => {
            this.$nextTick(()=>{
                this.map_box_width = data.width
                this.map_box_height = data.height
            })
        }).exec();
        this.$nextTick(()=>{
            let move_el = document.getElementById("move_el")
            var _this = this
            move_el.addEventListener('touchstart',function(e){
                _this.handleTouchstart(e)
            })
            move_el.addEventListener('touchmove',function(e){
                e.preventDefault();
                _this.handleTouchmove(e)
            })
        })
    },
    filters: {
        imgUrl(val, param = "") {
            return formatImg(val, param)
        }
    },
    methods:{
        handleTouchstart(e){
            this.touch_start = {
                page_x:e.changedTouches[0].pageX,
                page_y:e.changedTouches[0].pageY,
                img_left:e.currentTarget.offsetLeft,
                img_top:e.currentTarget.offsetTop,
            }
            this.img_info = {
                width:this.img_info.width,
                height:this.img_info.height,
                left:e.currentTarget.offsetLeft+'px',
                top:e.currentTarget.offsetTop+'px',
                margin_left:"0px"
            }
        },
        handleTouchmove(e){
            let left = this.touch_start.img_left-(this.touch_start.page_x-e.changedTouches[0].pageX)
            let top = this.touch_start.img_top-(this.touch_start.page_y-e.changedTouches[0].pageY)
            if(left>=0){
                left = 0
            }
            if(top>=0){
                top = 0
            }
            if(left<=this.map_box_width-this.img_info.width){
                left = this.map_box_width-this.img_info.width
            }
            if(top<=this.map_box_height-this.img_info.height){
                top = this.map_box_height-this.img_info.height
            }
            this.img_info = {
                width:this.img_info.width,
                height:this.img_info.height,
                left:left+'px',
                top:top+'px',
                margin_left:"0px"
            }
        },
        toLoudong(index){
            this.$emit('clickPoint',index)
        },
        handleChange(e){
            this.current_status = e.detail.value.map(item=>parseInt(item))
        }
    }
}
</script>

<style scoped lang="scss">
.house_map-box{
    width: 100%;
    position: relative;
    overflow: hidden;
    .house_map{
        position: absolute;
        // max-width: 300%;
        // min-width: 100%;
        .map_img{
            height: 100%;
            width: 100%;
        }
        .mark{
            position: absolute;
            font-size: 32rpx;
            font-weight: bold;
            color: #fff;
            height: 50rpx;
            line-height: 50rpx;
            width: 120rpx;
            text-align: center;
            border-radius: 6rpx;
            background-color: rgba($color: #70d298, $alpha: 0.8);
            &.mark1{
                background-color: rgba($color: #17bfff, $alpha: 0.8);
                .arrow{
                    border-top: rgba($color: #17bfff, $alpha: 0.8) 8rpx solid;
                }
            }
            &.mark2{
                background-color: rgba($color: #70d298, $alpha: 0.8);
                .arrow{
                    border-top: rgba($color: #70d298, $alpha: 0.8) 8rpx solid;
                }
            }
            &.mark3{
                background-color: rgba($color: #ff7213, $alpha: 0.8);
                .arrow{
                    border-top: rgba($color: #ff7213, $alpha: 0.8) 8rpx solid;
                }
            }
            &.mark4{
                background-color: rgba($color: #f3f3f3, $alpha: 0.8);
                color: #333;
                .arrow{
                    border-top: rgba($color: #f3f3f3, $alpha: 0.8) 8rpx solid;
                }
            }
            &.now_mark{
                background-color: rgba($color: $uni-color-primary, $alpha: 0.8);
                .arrow{
                    border-top: rgba($color: $uni-color-primary, $alpha: 0.8) 8rpx solid;
                }
            }
            .arrow{
                width: 0;
                height: 0;
                border-left: transparent 8rpx solid;
                border-right: transparent 8rpx solid;
                border-top: rgba($color: #70d298, $alpha: 0.8) 8rpx solid;
                position: absolute;
                left: 0;
                top: 50rpx;
                right: 0;
                margin: auto;
                background: rgba(255, 255, 255, 0) !important;
                cursor: pointer;
            }
        }
    }
    .filter{
        align-items: center;
        .filter_items{
            position: absolute;
            align-items: center;
            height: 60rpx;
            padding: 0 40rpx 0 20rpx;
            border-radius: 30rpx;
            background-color: rgba($color: #ffffff, $alpha: 0.8);
            transition: 0.26s;
            right: 70rpx;
            bottom: 55rpx;
            &.hide{
                width: 0;
                overflow: hidden;
                padding: 0;
            }
            .item{
                height: 40rpx;
                line-height: 40rpx;
                padding: 0 16rpx 0 2rpx;
                border-radius: 20rpx;
                font-size: 0;
                background-color: #17bfff;
                color: #fff;
                display: flex;
                align-items: center;
                ~.item{
                    margin-left: 16rpx;
                }
                text{
                    font-size: 24rpx;
                    margin-left: -10rpx;
                }
            }
            .color1{
                background-color: #17bfff;
            }
            .color2{
                background-color: #70d298;
            }
            .color3{
                background-color: #ff7213;
            }
            .color4{
                background-color: #888;
            }
            .checkbox{
                transform: scale(0.5,0.5);
            }
        }
        .show_btn{
            position: absolute;
            height: 90rpx;
            width: 90rpx;
            border-radius: 50%;
            line-height: 90rpx;
            text-align: center;
            background-color: rgba($color: #ffffff, $alpha: 1);
            z-index: 2;
            right: 10rpx;
            bottom: 40rpx;
        }
        .hover{
            background-color: #f3f3f3;
        }
    }
}
</style>
