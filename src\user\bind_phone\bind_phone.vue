<template>
	<view id="reg">
		<my-input label="手机号:" :placeholder="type=='edit'?'请输入新手机号':'请输入手机号'" type="number" @input="inputPhone"></my-input>
		<my-input label="验证码:" placeholder="请输入验证码" type="number" @input="inputCode">
			<view class="send-code" :class="sending?'disable':''" @click="sendCode">{{time?time+'s':'获取验证码'}}</view>
		</my-input>
		<view class="check_box" v-if="type=='edit'" >
			<checkbox-group  @change="checkboxChange">
				<label>
						<checkbox :value="check_edit" />同步更新全部信息的联系电话
				</label>
			</checkbox-group>
		</view>
		<view class="btn-box">
			<view class="btn btn-lg" @click="subData()">{{type=='edit'?'提交修改':'立即绑定'}}</view>
		</view>
	</view>
</template>

<script>
	import myInput from "../../components/form/myInput.vue"
	import {
		mapState,
		mapMutations
	} from 'vuex'
	import {showModal} from "../../common/index.js"
	export default {
		data() {
			return {
				type:"bind",
				time:0,
				tel:"",
				code:"",
				code_token:"",
				sending:false,
				sendType:"", //1:是不检测是否存在手机号强制发送验证码，2:修改手机号时发送验证码，默认是"" 绑定手机号发送验证码
				check_edit:"1",
				is_checked:0,
			};
		},
		onLoad(options){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			if(options.type){
				this.type = options.type
				uni.setNavigationBarTitle({
					title: "修改手机号"
				})
			}
			// 如果是微信传图
			if(options.wxupimg){
				this.page_type = 'wx_up_img'
			}
			this.pagesLen = getCurrentPages().length;
		},
		computed:{
			...mapState(['user_info','allowOpen'])
		},
		components:{
			myInput
		},
		onUnload(){
			this.setAllowOpen(false)
		},
		methods: {
			...mapMutations(['setAllowOpen']),
			inputPhone(e){
				this.tel = e.detail.value
			},
			inputCode(e){
				this.code = e.detail.value
			},
			checkboxChange(e){
				this.is_checked =e.detail.value.length>0?e.detail.value.join(','):0
			},
			checkPhone(tel){ //检测手机号格式
				if(!tel){
					uni.showToast({
						title:"请输入手机号",
						icon:"none"
					})
					return false
				}
				if(tel.length!==11||tel[0]!=1){
					uni.showToast({
						title:"手机号格式不正确",
						icon:"none"
					})
					return false
				}
				return true
			},
			checkCode(code){ //检测验证码
				if(!code){
					uni.showToast({
						title:"请输入验证码",
						icon:"none"
					})
					return false
				}
				return true
			},
			sendCode() { //发送验证码
				if(this.sending){
					return
				}
				if(!this.checkPhone(this.tel)){
					return
				}
				if(this.type == 'edit'){
					this.sendType = 2
				}
				this.$ajax.get('member/sendCode',{tel:this.tel,type:this.sendType},(res)=>{
					if(res.data.code == 1){
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
						this.code_token = res.data.code_token
						this.time = 60
						this.timer()
						this.sending = true
					}
					if(res.data.code == 0){
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
					// 如果已经存在此手机号
					if(res.data.code==-2){
						if(this.type == 'edit'){
							uni.showToast({
								title:res.data.msg,
								icon:"none"
							})
							return
						}
						showModal({
							title: '提示',
							content: res.data.msg,
							confirm: ()=> {
								this.sendType = 1
								this.sendCode()
							}
						});
					}
				})
			},
			timer(){ //倒计时
				if(timer){
					clearInterval(timer)
				}
				let timer = setInterval(()=>{
					if(this.time<=0){
						clearInterval(timer)
						this.sending = false
						return
					}
					this.time--
				},1000)
			},
			subData(){ //提交绑定手机号
				if(this.disableSub){
					return
				}
				if(!this.checkPhone(this.tel)||!this.checkCode(this.code)){
					return
				}
				this.disableSub = true
				let params = {
					tel:this.tel,
					code:this.code,
					code_token:this.code_token
				}
				setTimeout(()=>{
					this.disableSub = false
				},1000)
				if(this.type == 'edit'){
					console.log("修改手机号")
					params.sync=this.is_checked
					this.$ajax.get('member/updatePhone',params,res=>{
						if(res.data.code==1){
							uni.showToast({
								title:res.data.msg,
								duration:2000
							})
							this.user_info.tel = this.tel
							setTimeout(()=>{
								if(this.pagesLen>1){
									uni.navigateBack()
								}else{
									uni.switchTab({
										url:"/pages/index/index"
									})
								}
								uni.$emit("getDataAgain",{})
							},2000)
						}else{
							this.disableSub = false
							uni.showToast({
								title:res.data.msg,
								duration:2000,
								icon:"none"
							})
						}
					},err=>{
						this.disableSub = false
					})
					return
				}
				params.type = this.sendType
				let bind_phone_api = 'member/bindPhone'
				// #ifdef H5
				if (window.QFH5) { //如果是千帆则使用另一个接口绑定手机号
                    bind_phone_api = "member/bindPhoneByQF"
				}
				if (window.mag) { //如果是马甲则使用另一个接口绑定手机号
                    bind_phone_api = "member/bindPhoneByMJIA"
				}
				// #endif
				this.$ajax.get(bind_phone_api,params,(res)=>{
					if(res.data.code==1){
						if(res.data.token){ //绑定手机号成功后台返回一个新的token
							uni.setStorageSync('token',res.data.token)
						}
						this.$store.state.user_login_status = 3
						if(this.page_type === 'wx_up_img'){
							uni.showToast({
								title:"绑定成功，请关闭页面继续传图",
								duration:3000,
								icon:'none'
							})
							setTimeout(()=>{
								uni.switchTab({
									url:"/pages/index/index"
								})
							},3000)
						}else{
							uni.showToast({
								title:res.data.msg,
								duration:2000
							})
							setTimeout(()=>{
								if(this.pagesLen>1){
									uni.$emit("getDataAgain",{})
									uni.navigateBack()
								}else{
									uni.switchTab({
										url:"/pages/index/index"
									})
								}
							},2000)
						}
					}else{
						this.disableSub = false
						uni.showToast({
							title:res.data.msg,
							duration:2000,
							icon:"none"
						})
					}
					console.log(res.data)
				},err=>{
					this.disableSub = false
				})
			}
		}
	}
</script>

<style lang="scss">
	#reg{
		.btn-box{
			padding: $uni-spacing-row-base;
		}
		.btn-box .btn.btn-lg{
			width: 100%;
			padding: 10upx;
			border-radius: 10upx;
			height: 80upx;
			text-align: center;
			line-height: 60upx;
			box-sizing: border-box;
			font-size: $uni-font-size-lg;
			color: #fff;
			background-color: $uni-color-primary;
		}
		.send-code{
			padding: 0 20upx;
			border-radius: 10upx;
			background-color: $uni-color-primary;
			color: #fff;
		}
		.send-code.disable{
			background-color: #f1f1f1;
			color: #666
		}
		.check_box {
			width: 100%;
			padding: 20rpx;
			background-color: #fff;
		}
	}
</style>
