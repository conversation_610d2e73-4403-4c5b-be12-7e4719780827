<template>
  <view>
    <view class="top-tip flex-row">
      <my-icon type="ic_guanyu" color="#ff656b"></my-icon>
      <text class="text">点击即可添加对比</text>
    </view>
    <view class="house_list">
      <view class="house_item bottom-line" v-for="item in list" :key="item.id" @click="addContrast(item.id)">
        <view class="title">{{item.desc}}</view>
        <view class="info flex-row">
          <text>{{`${item.shi}室${item.ting}厅${item.wei}卫`}}</text>
          <text>约{{item.mianji}}m²</text>
          <text>{{item.jiage}}元/m²</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
export default {
  components: {
    myIcon
  },
  data () {
   return {
     list:[],
     params:{
       page:1,
       rows:50
     }
    }
  },
  onLoad(options){
    this.params.bid = options.bid || ''
    this.getData()
  },
  methods: {
    getData(){
      this.$ajax.get('build/getBuildAllTypePic.html',this.params,res=>{
        if(res.data.code === 1){
          this.list = res.data.list
        }
      })
    },
    addContrast(img_id){
      this.$ajax.get('build/addContrast.html',{img_id},res=>{
        if(res.data.code === -1){
          uni.$emit('replaceHouse',img_id)
          this.$navigateBack(2)
          return
        }
        if(res.data.id){
          uni.$emit('replaceHouse',res.data.id)
          this.$navigateBack(2)
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      },err=>{

      },{disableAutoHandle:true})
    }
  }
}
</script>

<style scoped lang="scss">
view {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.top-tip {
  align-items: center;
  padding: 24rpx 48rpx;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
  color: $uni-color-primary;
  .text {
    margin-left: 24rpx;
  }
}

.house_list{
  background-color: #fff;
}

.house_item{
  padding: 24rpx 48rpx;
  .title{
    font-size: 40rpx;
    margin-bottom: 16rpx;
  }
  .info {
    justify-content: space-between;
    font-size: 28rpx;
    color: #666;
  }
}
</style>