<template>
  <view class="time_line">
    <view class="item" :class="{last: index===lineData.length-1}" v-for="(item, index) in lineData" :key="index">
      <view class="time" v-if="item.time">
        <text class="label" v-if="item.speed">{{ item.speed }}</text>
        <text>{{ item.time }}</text>
      </view>
      <slot name="content" :slotData="item" :slotIndex="index">
        <view class="data_content">{{ item.content }}</view>
      </slot>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    lineData: Array
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scope>
.time_line {
  padding: 24rpx 48rpx;
  .item {
    position: relative;
    padding: 0 0 48rpx 32rpx;
    .time {
      margin-bottom: 32rpx;
      color: #999;
    }
    .label{
      padding: 4rpx 24rpx;
      font-size: 24rpx;
      border-radius: 4rpx;
      background-color: $uni-color-primary;
      color: #fff;
      margin-right: 24rpx;
    }
    .data_content {
      margin-bottom: 15upx;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      color: #666;
    }
  }
  .item::before {
    content: '';
    width: 1rpx;
    position: absolute;
    left: -3rpx;
    top: 12rpx;
    bottom: -12rpx;
    background-color: #d8d8d8;
  }
  .item.last::before {
    content: '';
    width: 1rpx;
    position: absolute;
    left: -3rpx;
    top: 12rpx;
    bottom: 12rpx;
    background-color: #d8d8d8;
  }
  .item::after {
    content: '';
    height: 11rpx;
    width: 11rpx;
    box-sizing: border-box;
    border-radius: 50%;
    position: absolute;
    background-color: $uni-color-primary;
    left: -6rpx;
    top: 12rpx;
  }
  // .current::before {
  //   content: '';
  //   height: 20upx;
  //   width: 20upx;
  //   border-radius: 50%;
  //   background-color: $uni-color-primary;
  //   position: absolute;
  //   left: -12upx;
  //   top: 0;
  //   z-index: 2;
  // }
}
</style>
