<template>
	<view>
		<view class="top-20 row">
			<map style="width: 100%; height: 360upx;" id="map" :scale="scale" :latitude="lat"  :title="title" :longitude="lng" :markers="markers" :enable-zoom="true" :enable-scroll="true" @tap="viewMap" @markertap="viewMap2($event)"></map>
			<view class="grid-box">
				<view v-for="(nav,index) in navs" :key="index" class="grid-item" hover-class="navigator-hover" @click="getCovers(nav.name)">
					<!-- <image :src="nav.icon" mode="widthFix"></image> -->
					<view><my-icon :type="nav.font" :color="nav.color" size="26"></my-icon></view>
					<view class="text">{{nav.name}}</view>
				</view>
			</view>
		</view>
		<view style="background-color: #ffffff;">
			<block v-for="(item,index) in markers" :key="index" >
				<uni-list-item v-if="item.distance" disabled :showArrow="false" :title="item.title" badge-type="default" :show-badge="true" :badge-text="item.distance+'米'"></uni-list-item>
			</block>
		</view>
	</view>
</template>

<script>
	import {uniListItem} from '@dcloudio/uni-ui'
	import myIcon from '../components/icon.vue'
	export default {
		data() {
			return {
				navs:[
					{
						name:"商业",
						font:"shangye",
						color:"#d81e06",
						icon:"/static/icon/shop.png"
					},
					{
						name:"教育",
						font:"jiaoyu",
						color:"#ffa659",
						icon:"/static/icon/school.png"
					},
					{
						name:"医疗",
						font:"yiliao",
						color:"#22b573",
						icon:"/static/icon/hospital.png"
					},
					{
						name:"交通",
						font:"jiaotong",
						color:"#38aae6",
						icon:"/static/icon/traffic.png"
					}
				]
			}
		},
		props:{
			infoid:[Number,String],
			infotype:{
				type:String,
				default:""
			},
			scale:[Number,String],
			lat:String,
			lng:String,
			scale:[Number,String],
			markers:Array,
			title:String,
		},
		components:{
			uniListItem,
			myIcon
		},
		methods:{
			getCovers(keywords='商业'){
				this.$emit("getCovers",keywords)
			},
			viewMap(e){
				// console.log(e)
				this.$navigateTo("/propertyData/map/map?id="+this.infoid+"&type="+this.infotype+"&lat="+this.lat+"&lng="+this.lng)
			},
			viewMap2(e){
				// console.log(e)
				this.$navigateTo("/propertyData/map/map?id="+this.infoid+"&type="+this.infotype+"&lat="+this.lat+"&lng="+this.lng)
			}
		}
	}
</script>

<style>

</style>
