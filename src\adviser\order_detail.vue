<template>
<view class="confirm_order">
    <view class="card">
        <view class="title flex-box bottom-line">
            <text class="label">客户信息</text>
        </view>
        <view class="card_content">
            <view class="info_item flex-box">
                <view class="label">客户姓名</view>
                <view class="value">{{order_info.uname}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">客户手机号</view>
                <view class="value">{{order_info.tel}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">身份证号码</view>
                <view class="value">{{order_info.id_card}}</view>
            </view>
        </view>
    </view>
    <view class="card">
        <view class="title flex-box bottom-line">
            <text class="label">楼盘名称</text>
            <text>{{order_info.build_name}}</text>
        </view>
        <view class="card_content text-center">
            <view class="info_item flex-box">
                <view class="label">订单编号</view>
                <view class="value">{{order_info.order_sn}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">意向房源</view>
                <view class="value">{{order_info.house_name}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">建筑面积</view>
                <view class="value">{{order_info.jzmj}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">款项名称</view>
                <view class="value">{{order_info.money_type}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">款项金额</view>
                <view class="value">{{order_info.money}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">支付状态</view>
                <view class="value">{{order_info.pay_status_text}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">专属职业顾问</view>
                <view class="value">{{order_info.adviser_name}}</view>
            </view>
            <view class="info_item flex-box">
                <view class="label">订单生成时间</view>
                <view class="value">{{order_info.ctime}}</view>
            </view>
        </view>
    </view>
    <view class="btn-box" v-if="order_info.is_confirm===1&&!order_info.write_off">
        <button class="default" @click="writeOff()">核销</button>
    </view>
    <view class="btn-box" v-if="order_info.is_confirm===0">
        <!-- #ifdef MP -->
        <button open-type="share" class="default">分享给客户</button>
        <!-- #endif -->
        <!-- #ifndef MP -->
        <button class="default" @click="handleShare()">分享给客户</button>
        <!-- #endif -->
    </view>
    <!-- #ifdef H5 -->
    <div class="share_mask" v-show="show_share" @click="show_share=false">
        <div class="icon-box">
            <img class="icon" src="https://images.tengfangyun.com/images/icon/share_tip_icon.png" mode="widthFix" />
        </div>
    </div>
    <!-- #endif -->
    <!-- #ifdef APP-PLUS -->
    <my-popup ref="popup" position="bottom" @handleHide="showPopup=false">
    <view class="app-share-box flex-box">
        <button open-type="share" class="flex-1 item" @click="appShare('WXSceneSession')">
            <image style="height:64upx;width:64upx" src="https://images.tengfangyun.com/images/icon/wechat.png"></image>
            <view class="text">分享给好友</view>
        </button>
        <view class="flex-1 item" @click="appShare('WXSenceTimeline')">
            <image style="height:64upx;width:64upx" src="https://images.tengfangyun.com/images/icon/time_line.png"></image>
            <view class="text">分享到朋友圈</view>
        </view>
    </view>
    </my-popup>
    <!-- #endif -->
</view>
</template>

<script>
import {
    navigateTo,
    formatImg,
} from '../common/index.js'
import {config,showModal} from "../common/index.js"
import myPopup from "../components/myPopup"
import {wxShare} from '../common/mixin'
export default {
    data() {
        return {
            order_info:{},
            show_share:false,
            write_off_number:"",
        }
    },
    mixins:[wxShare],
    components:{
        myPopup
    },
    onLoad(options){
        this.order_id = options.id||''
    },
    onShow(){
        if(!this.get_once&&this.order_id){
            this.getData()
        }
    },
    filters:{
        orderStatus(val){
            let status
            switch (val){
                case 0:
                    status = "待付款"
                    break;
                case 1:
                    status = "已付款"
                    break;
                case 2:
                    status = "处理中"
                    break;
                case 3:
                    status = "已退款"
                    break;
                case 5:
                    status = "待使用"
                    break;
                case 6:
                    status = "已失效"
                    break;
                default:
                    status = ""
            }
            return status
        }
    },
    methods: {
        /** 
         * <AUTHOR> 
         * @date 2020-03-03 13:36:52 
         * @desc 获取订单详情 
         */
        getData(){
            this.$ajax.get('online/orderDetailByUserConfirm.html',{order_id:this.order_id},res=>{
                if(res.data.share){
                    res.data.share.link = `${window.location.origin}/h5/online/confirm_order?order_id=${this.order_id}`
                    this.share = res.data.share
                }else{
                    this.share = {
                        title:"",
                        content:"",
                        link:`${window.location.origin}/h5/online/confirm_order?order_id=${this.order_id}`
                    }
                }
                this.getWxConfig()
                if(res.data.code == 1){
                    this.get_once = true
                    this.order_info = res.data.order
                }else{
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                    setTimeout(()=>{
                        uni.navigateBack()
                    },1500)
                }
            })
        },
        handleShare(){
            // #ifdef H5
            this.show_share = true
            // #endif
            // #ifdef APP-PLUS
            this.showPopup = true
            this.$refs.popup.show()
            // #endif
        },
        // #ifdef APP-PLUS
        appShare(type = 'WXSceneSession') {
            uni.share({
                provider: 'weixin',
                type: 0,
                title: this.share.title||"",
                scene: type,
                imageUrl: this.share.pic?formatImg(this.share.pic,'w_6401'):"",
                summary: this.share.content,
                href: `${config.apiDomain}/h5/online/confirm_order?order_id=${this.order_id}`,
                success: function (res) {
                uni.showToast({
                    title: '分享成功',
                    icon: 'none'
                })
                },
                fail: function (err) {
                    uni.showToast({
                        title: '分享失败',
                        icon: 'none'
                    })
                    console.log("fail:" + JSON.stringify(err));
                }
            })
        },
        // #endif
        writeOff(){
            showModal({
                content:"确定核销此订单吗？",
                confirm:()=>{
                    this.$ajax.get('online/writeOffOrder',{order_id:this.order_id,write_off_number:this.write_off_number},res=>{
                        if(res.data.code === 1){
                            uni.showToast({
                                title: res.data.msg
                            })
                            this.getData()
                        }else{
                            uni.showToast({
                                title: res.data.msg,
                                icon: 'none'
                            })
                        }
                    })
                }
            })
        }
    },
    onShareAppMessage(){
        // #ifdef MP-BAIDU
        return {
            title:this.share.title||'',
            content:this.share.content||'',
            imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):"",
            path:`/online/confirm_order?order_id=${this.order_id}`
        }
        // #endif
        // #ifdef MP-WEIXIN
        return {
            title:this.share.title||'',
            imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):"",
            path:`/online/confirm_order?order_id=${this.order_id}`
        }
        // #endif
    }
}
</script>

<style scoped lang="scss">
// #ifdef H5
.confirm_order{
    padding-top: 64px
}
// #endif
.card{
    margin: 0 15rpx 24rpx 15rpx;
    padding: 26rpx;
    border-radius: 10rpx;
    background-color: #fff;
    box-shadow: 0 0 10px #dedede;
    .title{
        justify-content: space-between;
        padding: 26rpx 0;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        .label{
            color: #666;
        }
    }
    .card_content{
        margin-top: 20rpx;
    }
}

.info_item{
    padding: 15rpx 0;
    width: 100%;
    box-sizing: border-box;
    justify-content: space-between;
    .label{
        display: inline-block;
        text-align: left;
        min-width: 140rpx;
        color: #666;
    }
}

@keyframes fade {
    from {
        opacity: 1.0;
    }
    50% {
        opacity: 0.1;
    }
    to {
        opacity: 1.0;
    }
}
.share_mask{
    position: fixed;
    width: 100%;
    top: 0;
    bottom: 0;
    background-color: rgba($color: #000000, $alpha: 0.5);
    z-index: 99;
    .icon-box{
        width: 120upx;
        position: absolute;
        top: 20upx;
        right: 60upx;
        animation: fade 800ms infinite;
        -webkit-animation: fade 800ms infinite;
    }
    .icon{
        width: 100%;
    }
}


.app-share-box {
  padding: 20upx 0;
  background-color: #fff;

  .tip {
    padding: 10px;
    width: 100%;
    font-weight: 700;
    box-sizing: border-box;
    text-align: center;
  }

  button {
    line-height: initial;
    padding: 10upx 20upx;
    background-color: #fff;
  }

  .item {
    text-align: center;
    padding: 10upx 20upx;
    line-height: inherit;
  }
}
</style>
