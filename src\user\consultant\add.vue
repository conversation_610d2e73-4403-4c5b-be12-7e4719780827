<template>
<view class="addcons">
  <view class="block">
    <view class="info-row avatar-row">
      <view class="label">头像</view>
      <view class="right flex-row">
        <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadHeaderImg">
          <image class="img" mode="aspectFill" v-if="params.prelogo" :src="params.prelogo | imageFilter('w_120')"></image>
          <view v-else class="upload-btn">
            <my-icon type="ic_jia" size="46rpx" color="#d8d8d8"></my-icon>
          </view>
        </my-upload>
        <view class="icon-box">
          <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
        </view>
      </view>
    </view>
  </view>
  <view class="block">
    <view class="info-row">
      <view class="label">姓名</view>
      <view class="right flex-row">
        <input type="text" v-model="params.cname" placeholder="请输入" placeholder-style="text-align:right;font-size:32rpx;color:#999">
        <view class="icon-box">
          <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
        </view>
      </view>
    </view>
  </view>
  <view class="block">
    <view class="info-row">
      <view class="label">微信号</view>
      <view class="right flex-row">
        <input type="text" v-model="params.wechat" placeholder="请输入" placeholder-style="text-align:right;font-size:32rpx;color:#999">
        <view class="icon-box">
          <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
        </view>
      </view>
    </view>
  </view>

  <view class="block" v-if="buildShow==true">
    <view class="info-row">
      <view class="label">楼盘信息</view>
      <view class="buildname flex-1" @click="buildsShow">{{(params.build_names)?(params.build_names):"请选择楼盘信息"}}</view>
      <view class="icon-box">
        <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
      </view>
    </view>
  </view>
  <view class="block">
    <view class="info-row">
      <view class="label">手机号</view>
      <view class="right flex-row">
        <input type="number" maxlength="11" v-model="params.tel" placeholder="请输入" placeholder-style="text-align:right;font-size:32rpx;color:#999">
        <view class="icon-box">
          <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
        </view>
      </view>
    </view>
  </view>
  <view class="block">
    <view class="info-row">
      <view class="label">验证码</view>
      <view class="right flex-row">
        <input class="flex-1" type="number" maxlength="6" v-model="params.code" placeholder="请输入取验证码" placeholder-style="text-align:right;font-size:32rpx;color:#999">
        <view class="send-code" :class="sending?'disable':''" @click="sendCode">{{time?"重新获取 "+time+'s':'获取验证码'}}</view>
        </view>
    </view>
  </view>
  <view class="block">
    <view class="info-row">
      <view class="label">服务时间</view>
      <view class="right flex-row">
        <my-select oneRow :value="params.service_type" @change="pickerChange" :range="service_times" name="cid"></my-select>
      </view>
    </view>
  </view>
  <myPopup ref='select_build'>
    <view style="background-color:#fff">
      <view class="top-box flex-row">
        <search @input="inputValue" @confirm="searchBuild"  placeholder="请输入楼盘名称"></search>
        <view class="search"  @click="searchBuild">
          <text>搜索</text>
        </view> 
      </view>
      <scroll-view scroll-y class="buildAll" @touchmove.stop.prevent="stopMove" @scrolltolower="loadLazy">    
        <view class="uni-list">
          <radio-group @change="radioChange">
            <label class="uni-list-cell uni-list-cell-pd flex-box" v-for="(item, index) in buildList" :key="item.id">
              <view>
                <radio :value="''+item.id" :checked="index === current" />
              </view>
              <view>{{item.title}}</view>
            </label>
          </radio-group>
        </view>
        <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
      </scroll-view>
    </view>
  </myPopup>

  <view class="block">
    <view class="info-row">
      <view class="label">邀请码</view>
      <view class="right flex-row">
        <input type="text" v-model="params.invited" placeholder="请输入" placeholder-style="text-align:right;font-size:32rpx;color:#999">
        <view class="icon-box">
          <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
        </view>
      </view>
    </view>
  </view>
  <view class="block">
    <view class="info-row">
      <view class="label">转发标题</view>
      <view class="right flex-row">
        <input type="text" v-model="params.card_title" placeholder="请输入转发标题" placeholder-style="text-align:right;font-size:32rpx;color:#999">
        <view class="icon-box">
          <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
        </view>
      </view>
    </view>
  </view>
  <view class="block">
    <view class="info-column">
      <view class="label">个性签名</view>
      <textarea type="text" v-model="params.minfo" placeholder="请输入" placeholder-style="font-size:32rpx;color:#999"></textarea>
    </view>
  </view>
  <view class="block">
    <view class="upload-row">
      <view class="title flex-row">
        <text>微信二维码</text>
      </view>
      <view class="upload-box">
        <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadQrCode">
          <image class="img" mode="aspectFill" v-if="params.wechat_img" :src="params.wechat_img | imageFilter('w_240')"></image>
          <view v-else class="upload-btn">
            <my-icon type="ic_jia" size="96rpx" color="#d8d8d8"></my-icon>
          </view>
        </my-upload>
      </view>
    </view>
  </view>
  <view class="block">
    <view class="upload-row">
      <view class="title flex-row">
        <text>自拍照或者工牌照</text>
      </view>
      <view class="upload-box">
        <my-upload showSlot :chooseType="1" :maxCount="1" @uploadDon="uploadVoucherImg">
          <image class="img" mode="aspectFill" v-if="params.voucher_img" :src="params.voucher_img | imageFilter('w_240')"></image>
          <view v-else class="upload-btn">
            <my-icon type="ic_jia" size="96rpx" color="#d8d8d8"></my-icon>
          </view>
        </my-upload>
      </view>
    </view>
  </view>
  <view class="btn" @click="subData()">立即提交</view>
  <view class="tip">
    <view class="tip_info"> 
      <text>注：为了避免他人冒充置业顾问/经纪人我们需要验证您的名片或者工牌 谢谢您的配合</text>
    </view>
    <view class="tip_info"> 
      <text>如果遇到图片无法上传或者其他的问题请联系客服，我们将及时为您解决</text>

    </view>
    <view class="add_kf" @click="copy" v-if ="wxqunopen" >加客服微信号：{{kfwechat}} </view>
  </view>
</view>
</template>

<script>
import mySelect from "../../components/form/mySelect.vue"
import myIcon from "../../components/myIcon"
import myPopup from "../../components/myPopup"
import myUpload from "../../components/form/myUpload.vue"
import search from "../../components/search.vue"

import {
  mapMutations
} from 'vuex'
import {
  uniList,
  uniListItem,
  uniLoadMore
} from '@dcloudio/uni-ui'
import {wxShare} from '../../common/mixin'
export default {
  data() {
    return {
      id: "",
      time: "",
      select: "false", //楼盘信息列表
      buildList: [], //楼盘信息数组
      buildShow: true, //楼盘信息搜索不到的时候显示输入楼盘信息 默认为选择楼盘信息 暂时不用这个功能
      page: 1,
      searchBuilds: "",
      current: "",
      params: {
        minfo: "",
        cname: "",
        prelogo: "",
        wechat: "",
        build_ids: "",
        tel: "",
        code: "",
        code_token: "",
        invited: "",
        service_type:0,
        wechat_img:'',
        voucher_img:'',
        card_title:''
      },
      service_times:[
        {value:0,
         name:"全天接听" 
        },
        {value:1,
         name:"晚10点到早8点拒接" 
        },
        {value:2,
         name:"全天拒接" 
        },
      ],
      disable:false,
      sending:false,
      rows:25,
      get_status: "",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了"
      },
      kfwechat:'',
    }
  },
  mixins:[wxShare],
  onLoad(options) {
    if (options.cname) {
      this.params.cname = options.cname
    }
    if (options.share) {
      this.getUserData()
    }
    if (options.wxkf) {
      this.kfwechat = options.wxkf
    }
    if (options.tel) {
      this.params.tel = options.tel
    }
    if (options.invited) {
      this.params.invited = options.invited
      this.disable = true;
    }
    this.getShare()
  },
  onShow() {

  },
  computed:{
    wxqunopen(){
      return this.$store.state.wxqunopen
    }
  },
  components: {
    myIcon,
    mySelect,
    myUpload,
    uniList,
    uniListItem,
    uniLoadMore,
    myPopup,
    search
  },
  methods: {
    ...mapMutations(['getUserInfo', 'setAllowOpen']),
     getUserData() {
      // #ifdef MP-BAIDU
      if (this.geting) {
        return
      }
      this.geting = true
      setTimeout(() => {
        this.geting = false
      }, 300);
      // #endif
      // 获取会员信息
      this.$ajax.get('Adviser/adv_reg', {}, (res) => {
        if (res.data.code == 1) {
          this.params.cname = res.data.data.cname
          this.params.tel = res.data.data.tel,
          this.kfwechat = res.data.wxqunkefu
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
        }
        // #ifdef MP-BAIDU
        this.geting = false
        // #endif
      }, (err) => {
        // #ifdef MP-BAIDU
        this.geting = false
        // #endif
      }, false)
    },
    getShare(){
      this.$ajax.get('/wap/Adviser/getInviteCode.html',{},res=>{
        if(res.data.share){
          if(res.data.invitecode){
            res.data.share.link=window.location.origin+'/h5/pages/consultant/add?invited='+res.data.invitecode+"&share=1"
          }else{
            res.data.share.link=window.location.origin+'/h5/pages/consultant/add?share=1'
          }
          this.share = res.data.share
          this.getWxConfig(['chooseImage','uploadImage','getLocalImgData','updateAppMessageShareData','updateTimelineShareData'])
        }else{
          this.share = {
            title:"邀请您成为置业顾问",
            link:window.location.origin+'/h5/pages/consultant/add?share=1'
          }
          this.getWxConfig(['chooseImage','uploadImage','getLocalImgData'])
        }
      },err=>{
        this.share = {
          title:"邀请您成为置业顾问",
          link:window.location.origin+'/h5/pages/consultant/add?share=1'
        }
        this.getWxConfig(['chooseImage','uploadImage','getLocalImgData'])
      })
    },
    // 获取楼盘信息
    getBuilds() {
      if(this.page === 1){
        this.buildList = []
      }
      this.$ajax.get('Adviser/getBuildList', {
        page: this.page,
        rows: this.rows,
        key: this.searchBuilds
      }, (res) => {
        if (res.data.code == 1) {
          if (res.data.data.length < this.rows) {
            this.get_status = "noMore"
          } else {
            this.get_status = "more"
          }
          this.buildList = this.buildList.concat(res.data.data)
        } else {
          this.get_status = "noMore"
          // uni.showToast({
          //   title: res.data.msg,
          //   icon: "none"
          // })
        }
      })
    },
     pickerChange(e) {
      this.params.service_type = e.value
    },
    buildsShow() {
      this.select = "true"
      this.$refs.select_build.show()
      this.getBuilds()
    },
     //复制
    copy() {
      // #ifdef H5
      const textString = this.kfwechat.toString();
      let input = document.querySelector('#copy-input');
      if (!input) {
        input = document.createElement('input');
        input.id = "copy-input";
        input.readOnly = "readOnly"; // 防止ios聚焦触发键盘事件
        input.style.position = "absolute";
        input.style.left = "-1000px";
        input.style.zIndex = "-1000";
        document.body.appendChild(input)
      }

      input.value = textString;
      // ios必须先选中文字且不支持 input.select();
      selectText(input, 0, textString.length);
      if (document.execCommand('copy')) {
        document.execCommand('copy');
        uni.showToast({
          title: "已复制 打开微信添加好友吧",
          icon: "none"
        })
      }
      input.blur();

      function selectText(textbox, startIndex, stopIndex) {
        if (textbox.createTextRange) { //ie
          const range = textbox.createTextRange();
          range.collapse(true);
          range.moveStart('character', startIndex); //起始光标
          range.moveEnd('character', stopIndex - startIndex); //结束光标
          range.select(); //不兼容苹果
        } else { //firefox/chrome
          textbox.setSelectionRange(startIndex, stopIndex);
          textbox.focus();
        }
      }
      // #endif
      // #ifndef H5
      uni.setClipboardData({
        data: this.kfwechat,
        success: function () {
          console.log('success');
         
        },
      });
      // #endif
    },
    searchBuild() {
      uni.hideKeyboard()
      this.page = 1
      this.getBuilds() 
    },
    stopMove() {

    },
    loadLazy() {
      if(this.get_status === "noMore"){
        return
      }
      this.page++
      this.getBuilds()
    },
    radioChange(evt) {
      for (let i = 0; i < this.buildList.length; i++) {
        if (this.buildList[i].id == evt.target.value) {
          this.current = i;
          this.params.build_ids = this.buildList[i].id
          this.params.build_names = this.buildList[i].title
          this.select = "false"
          this.$refs.select_build.hide()
          break;
        }
      }

    },
    input(e) {
      this.params[e._name] = e.detail.value
    },
    inputImgCode(e) {
      this.imgcode = e.detail.value
    },
    inputValue(e) {
      this.searchBuilds = e.detail.value
    },
    uploadHeaderImg(e){
      this.params.prelogo = e.files.join(',')
    },
    uploadQrCode(e){
      this.params.wechat_img = e.files.join(',')
    },
    uploadVoucherImg(e){
      this.params.voucher_img = e.files.join(',')
    },
    checkPhone(tel) { //检测手机号格式

      if (!tel) {
        uni.showToast({
          title: "请输入手机号",
          icon: "none"
        })
        return false
      }
      if (tel.length !== 11 || tel[0] != 1) {
        uni.showToast({
          title: "手机号格式不正确",
          icon: "none"
        })
        return false
      }
      return true
    },
    checkCode(code) { //检测验证码
      if (!code) {
        uni.showToast({
          title: "请输入验证码",
          icon: "none"
        })
        return false
      }
      return true
    },
    checkWechat(wechat) { //检测微信号
      if (!wechat) {
        uni.showToast({
          title: "请输入微信号",
          icon: "none"
        })
        return false
      }
      return true
    },
    checkName(name) {
      if (!name) {
        uni.showToast({
          title: "请输入姓名",
          icon: "none"
        })
        return false
      }
      return true
    },
    sendCode() { //发送验证码
      if (this.sending) {
        return
      }
      if (!this.checkPhone(this.params.tel)) {
        return
      }
      // #ifdef MP-BAIDU
      let params = {
        tel: this.params.tel,
        // code: this.params.imgcode
      }

      this.$ajax.get('adviser/sendCode', params, (res) => {

        if (res.data.code == 1) {
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
          this.params.code_token = res.data.code_token
          this.time = 60
          this.timer()
          this.sending = true
        }
        if (res.data.code == 0) {
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
          this.refCode()
        }
      })
      // #endif
      // #ifndef MP-BAIDU
      this.$ajax.get('adviser/sendCode', {
        tel: this.params.tel,
        // code: this.params.imgcode
         }, (res) => {
        if (res.data.code == 1) {
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
          this.params.code_token = res.data.code_token
          this.time = 60

          this.timer()
          this.sending = true
        }
        if (res.data.code == 0) {
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
          this.refCode()
        }
      })
      // #endif
    },

    timer() { //倒计时
      if (timer) {
        clearInterval(timer)
      }
      let timer = setInterval(() => {
        if (this.time <= 0) {
          clearInterval(timer)
          this.sending = false
          return
        }
        this.time--
      }, 1000)
    },
    refCode() {
      this.img_code += '?'
    },
    subData() {
      if (!this.checkWechat(this.params.wechat)) return;

      if (!this.checkPhone(this.params.tel)) return;
      if (!this.checkCode(this.params.code)) return;
      if (!this.checkName(this.params.cname)) return;
      if (!this.params.voucher_img) {
        uni.showToast({
          title: "请上传自拍照或者工牌照",
          icon: "none"
        })
        return
      }
      if (!this.params.wechat_img) {
        uni.showToast({
          title: "请上传二维码",
          icon: "none"
        })
        return
      }
      if (this.params.build_names == "") {
        uni.showToast({
          title: "楼盘信息不能为空",
          icon: "none"
        })
        return
      }
      uni.showLoading({
        title:"正在提交...",
        mask:true
      })
      this.$ajax.post('Adviser/adv_reg', this.params, (res) => {
        if (res.data.code == 1) {
          uni.hideLoading()
          uni.showToast({
            title: res.data.msg,
            mask:true
          })
          this.$store.state.updatePageData = true;
          setTimeout(() => {
              this.$navigateBack()
          }, 1000);
        
        } else {
          this.clicking = false
          uni.showToast({
            title: res.data.msg,
            icon: "none"
          })
        }
      },err=>{
        uni.hideLoading()
      })
    },

  },
  onShareAppMessage(){
    let title =  this.params.cname||""
    return {
			  title: title+"邀请您加入置业顾问",
			  content: "欢迎入驻置业顾问",
			 path:'/user/consultant/add?share=1'
			}
  }
}
</script>

<style lang="scss">
view{
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.flex-row{
  flex-direction: row;
}
.block{
  padding: 0 48rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  .buildname{
    font-size: 32rpx;
    text-align: right;
    margin-right: 10rpx;
  }
  .info-row{
    width: 100%;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 48rpx 0;
    &.avatar-row{
      padding: 24rpx 0;
      .upload-btn{
        width: 96rpx;
        height: 96rpx;
      }
      .img{
        width: 96rpx;
        height: 96rpx;
      }
    }
    input{
      text-align: right;
    }
  }
  .info-column{
    padding: 48rpx 0;
    .label{
      margin-bottom: 20rpx;
    }
    textarea{
      width: 100%;
      height: 200rpx;
    }
  }
  .label{
    width: 160rpx;
    font-size: 32rpx;
    color: #666;
  }
  .right{
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    .icon-box{
      margin-left: 16rpx;
    }
    .send-code{
      padding: 0 20rpx;
      margin-left: 24rpx;
      line-height: 50rpx;
      border-radius: 25rpx;
      border: 1rpx solid $uni-color-primary;
      color: $uni-color-primary;
      &.disable{
        color: #999;
        background-color: #f5f5f5;
        border-color: #999;
      }
    }
  }
  .title{
    justify-content: space-between;
    align-items: center;
    font-size: 32rpx;
    margin-bottom: 24rpx;
    color: #666;
  }
  .upload-row{
    padding: 48rpx 0;
  }
  .upload-btn{
    width: 25vw;
    height:25vw;
    align-items: center;
    justify-content: center;
    border-radius: 8rpx;
    background-color: #f5f5f5;
  }
  .img{
    width: 25vw;
    height: 25vw;
    border-radius: 8rpx;
  }
}

.addcons {

  .build {
    position: fixed;
    bottom: 0upx;
    top: 0upx;
    left: 0upx;
    right: 0upx;
    overflow-y: auto;
    // background: #f5f5f5;
    z-index: 10000;
       background-color: rgb(0, 0, 0);
       opacity: 0.2;
  }
  .top-box{
        align-items: center;
        padding: 30upx 10upx;
        search{
          flex: 1;
        }
        .search {
        color: #28bdfb;
        border-radius: 40upx;
        border: 3upx solid #28bdfb;
        padding: 8upx 20upx;
        margin: 5upx 20upx;
        align-items: center;
      }

      .search text {
        color: #28bdfb;
        font-size: 24upx;
      }
    }
    .buildAll {
      height: 60vh;
      width: 100%;
      overflow-y: auto;
      background: #fff;
      .uni-list {
        radio-group {
          padding-left: 50upx;
        }

        .uni-list-cell {
          justify-content: flex-start;
          align-items: center;
          padding: 20upx 0upx;
        }
      }
    // }
  }

  .upload {
    margin-top: 20rpx;
    // display: flex;
    // justify-content: flex-start;
    // align-items: center;

    .upload-box {
      .info {
        text-align: center;
        width: 33%;
      }
    }
  }
  .btn{
    margin: 24rpx 48rpx;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    background-color: $uni-color-primary;
    box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
    &.ordinary{
      background-color: #fff;
      color: $uni-color-primary;
      border: 1rpx solid $uni-color-primary;
    }
  }
  .tip{
    padding: 24rpx 48rpx;
    .tip_info{
      font-size: 22upx;
      color: #999;
      margin-bottom: 10upx;

    }
    .add_kf{
      color: #10aeff;
      font-size: 25upx;
      padding: 10upx 0upx;
    }
  }
}

// /* #ifdef H5 */
// .addcons .textarea-row  .uni-textarea-textarea  {
//     height: 50upx !important;
//   }
//   .addcons .textarea-row uni-textarea{
//     height: 50upx !important;

//   }
//  .addcons .uni-textarea-wrapper{
//     height: 50upx !important;
//   }
// .addcons  .uni-textarea-compute, .uni-textarea-placeholder, .uni-textarea-textarea{
//     height: 50upx;
//   }
// /* #endif */
</style>
