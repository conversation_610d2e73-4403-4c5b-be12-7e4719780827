<template>
  <view>
    <view class="block">
      <view class="label">
        <text>核验编码</text>
      </view>
      <textarea v-model="verification.verification_code" :disabled='cont.verification_code?true:false&& cont.disabled' rows="3" maxlength="300" placeholder="核验编码" placeholder-style="font-size:28rpx;color:#999"></textarea>
    </view>
    <view class="block">
      <view class="label">
        <text>核验二维码</text>
      </view>
      <view class="upload-box">
        <my-upload @uploadDon="uploadDon" :disabled='cont.verification_qrcode&& cont.disabled?true:false' :chooseType="1" :maxCount="1" :imgs="qrcodeList" message="不可修改" :allowDel="cont.verification_qrcode&& cont.disabled?false:true"></my-upload>
      </view>
    </view>
    <view class="btn-box">
      <view class="btn" @click="subData">确定</view>
    </view>
  </view>
</template>

<script>
import myUpload from '../components/form/myUpload'
export default {
  components: {
    myUpload
  },
  data() {
    return {
      verification: {
        verification_code: '',
        verification_qrcode: '',
      },
      qrcodeList: [],
      cont:{}
    }
  },
  onLoad() {
    uni.$once('giveVerification',(data)=>{
      this.verification = data
      this.cont = Object.assign({},data)
      if (data.verification_qrcode) {
        this.qrcodeList.push(data.verification_qrcode)
      }
      console.log(this.verification)
    })
  },
  methods: {
    uploadDon(e){
      this.qrcodeList = e.files
    },
    subData(){
      this.verification.verification_qrcode = this.qrcodeList[0]
      uni.$emit('transmitVerification', this.verification)
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.block{
  padding: 0 48rpx;
  padding-top: 24rpx;
  background-color: #fff;
  .label{
    padding: 24rpx 0;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
  }
  textarea{
    width: 100%;
    height: 200rpx;
    padding: 20rpx;
    box-sizing: border-box;
    line-height: 1.5;
    font-size: 28rpx;
    background-color: #f3f3f3;
  }
}
.btn-box{
  padding: 48rpx;
  .btn{
    height: 88rpx;
    line-height: 88rpx;
    background: #FB656A;
    box-shadow: 0 8rpx 32rpx 0 rgba(251,101,106,0.40);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    text-align: center;
  }
}
</style>