<template>
	<view id="reg">
		<my-input label="手机号:" placeholder="请输入手机号" type="number" @input="inputPhone"></my-input>
		<my-input label="图形验证码:" placeholder="请输入图形验证码" type="text" @input="inputImgCode">
            <image class="img_code" mode="widthFix" :src="img_code" @click="refCode()"></image>
        </my-input>
		<my-input label="验证码:" placeholder="请输入验证码" type="number" @input="inputCode">
			<view class="send-code" :class="sending?'disable':''" @click="sendCode">{{time?time+'s':'获取验证码'}}</view>
		</my-input>
        <my-input label="输入新密码:" placeholder="请输入新密码" type="password" @input="inputPwd"></my-input>
        <my-input label="确认新密码:" placeholder="请再次输入新密码" type="password" @input="inputPwdAgain"></my-input>
		<view class="btn-box">
			<view class="btn btn-lg" @click="subData()">立即重置</view>
		</view>
	</view>
</template>

<script>
	import myInput from "../../components/form/myInput.vue"
	import {showModal,config} from "../../common/index.js"
	export default {
		data() {
			return {
				time:0,
				tel:"",
				code:"",
                code_token:"",
                userpwd:"",
                userpwd2:"",
                sending:false,
                // #ifndef MP-BAIDU
				img_code:(config.apiDomain||window.location.origin)+'/wapi/member/captcha',
				// #endif
				// #ifdef MP-BAIDU
				img_code:''
				// #endif
			};
		},
		onLoad(options){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			// #ifdef MP-BAIDU
			this.getCaptcha()
			// #endif
		},
		components:{
			myInput
		},
		methods: {
			// #ifdef MP-BAIDU
			getCaptcha(){
				this.$ajax.get('member/createCaptcha',{},res=>{
					if(res.data.url){
						this.img_code = res.data.url
						this.img_code_token = res.data.imgCode
					}else{
						uni.showToast({
							title:res.data.msg||'获取图形验证码失败',
							icon:'none'
						})
					}
				})
			},
			// #endif
			inputPhone(e){
				this.tel = e.detail.value
            },
            inputImgCode(e){
                this.imgcode = e.detail.value
            },
			inputCode(e){
				this.code = e.detail.value
            },
            inputPwd(e){
                this.userpwd = e.detail.value
            },
            inputPwdAgain(e){
                this.userpwd2 = e.detail.value
            },
			checkPhone(tel){ //检测手机号格式
				if(!tel){
					uni.showToast({
						title:"请输入手机号",
						icon:"none"
					})
					return false
				}
				if(tel.length!==11||tel[0]!=1){
					uni.showToast({
						title:"手机号格式不正确",
						icon:"none"
					})
					return false
				}
				return true
			},
			checkCode(code){ //检测验证码
				if(!code){
					uni.showToast({
						title:"请输入验证码",
						icon:"none"
					})
					return false
				}
				return true
            },
            checkPwd(){
                if(this.userpwd==""){
                    uni.showToast({
						title:"请输入密码",
						icon:"none"
					})
					return false
				}
				if(this.userpwd.length<8){
                    uni.showToast({
						title:"密码长度不能小于8位",
						icon:"none"
					})
					return false
                }
					var regNum=/\d/;
          var regString =/[a-zA-Z]/;
          var regSpe = /[^a-zA-Z\d\s]/
          var reg =/\s/
          if (!regNum.test(this.userpwd) ||!regString.test(this.userpwd) ||!regSpe.test(this.userpwd)){
						uni.showToast({
						title:"密码须同时包含数字、字母以及特殊符号(!@#$%^&*()等非空格)",
						icon:"none"
					})
              return false
          }
          if (reg.test(this.userpwd)){
              uni.showToast({
								title:"密码不能包含空格",
								icon:"none"
							})
              return false
          }
                if(this.userpwd!==this.userpwd2){
                    uni.showToast({
						title:"两次输入的密码不一致",
						icon:"none"
					})
					return false
                }
                return true
            },
            refCode(){
                // #ifndef MP-BAIDU
				this.img_code+='?'
				// #endif
				// #ifdef MP-BAIDU
				this.getCaptcha()
				// #endif
            },
			sendCode() { //发送验证码
				if(this.sending){
					return
				}
				if(!this.checkPhone(this.tel)){
					return
				}
				// #ifdef MP-BAIDU
				let params = {tel:this.tel,code:this.imgcode,imgCode:this.img_code_token}
				this.$ajax.get('member/sendCodeByFindPwdByBd',params,(res)=>{
					if(res.data.code == 1){
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
						this.code_token = res.data.code_token
						this.time = 60
						this.timer()
						this.sending = true
					}
					if(res.data.code == 0){
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
						this.refCode()
					}
				})
				// #endif
				// #ifndef MP-BAIDU
				this.$ajax.get('member/sendCodeByFindPwd',{tel:this.tel,code:this.imgcode},(res)=>{
					if(res.data.code == 1){
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
						this.code_token = res.data.code_token
						this.time = 60
						this.timer()
						this.sending = true
					}
					if(res.data.code == 0){
						uni.showToast({
							title:res.data.msg,
							icon:"none"
                        })
                        this.refCode()
					}
				})
				// #endif
			},
			timer(){ //倒计时
				if(timer){
					clearInterval(timer)
				}
				let timer = setInterval(()=>{
					if(this.time<=0){
						clearInterval(timer)
						this.sending = false
						return
					}
					this.time--
				},1000)
			},
			subData(){ //提交绑定手机号
				if(this.disableSub){
					return
				}
				if(!this.checkPhone(this.tel)||!this.checkCode(this.code)){
					return
                }
                if(!this.checkPwd()){
                    return
                }
				this.disableSub = true
				let params = {
					tel:this.tel,
					code:this.code,
                    code_token:this.code_token,
                    pwd:this.userpwd
				}
                setTimeout(()=>{
                    this.disableSub = false
                },3000)
				this.$ajax.get('member/resetPwd',params,(res)=>{
					if(res.data.code==1){
						uni.showToast({
							title:res.data.msg,
							duration:2000
						})
						setTimeout(()=>{
							uni.navigateBack()
						},2500)
					}else{
						this.disableSub = false
						uni.showToast({
							title:res.data.msg,
							duration:2000,
							icon:"none"
						})
					}
					console.log(res.data)
				},err=>{
					this.disableSub = false
				})
			}
		}
	}
</script>

<style lang="scss">
	#reg{
		.btn-box{
			padding: $uni-spacing-row-base;
		}
		.btn-box .btn.btn-lg{
			width: 100%;
			padding: 10upx;
			border-radius: 10upx;
			height: 80upx;
			text-align: center;
			line-height: 60upx;
			box-sizing: border-box;
			font-size: $uni-font-size-lg;
			color: #fff;
			background-color: $uni-color-primary;
		}
        .img_code{
            width: 35%;
            height: 0;
        }
		.send-code{
			padding: 0 20upx;
			border-radius: 10upx;
			background-color: $uni-color-primary;
			color: #fff;
		}
		.send-code.disable{
			background-color: #f1f1f1;
			color: #666
		}
	}
</style>
