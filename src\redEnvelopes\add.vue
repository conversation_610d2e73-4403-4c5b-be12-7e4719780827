<template>
  <view class="page">
    <view class="img">
      <image  :src="top_img" mode="widthFix"></image>
    </view>
    <view class="top">
      <view class="label">金额</view>
      <input placeholder="0.00" class="input" v-model="pay_params.money" @input="inputChange"  placeholder-style="color: #BEBEBE" type="number" />
      <view class="label">元</view>
    </view>
    <view class="top">
      <view class="label">份数</view>
      <input placeholder="请输入红包份数" class="input" v-model="pay_params.hb_count" placeholder-style="color: #BEBEBE" type="number" @blur ="hbCountChange" />
      <view class="label">份</view>
    </view>
    <view class="count_tips">最小{{min_count}} 份 最大{{max_count}} 份</view>
    <view class="show">¥{{money || '0.00'}}</view>
    <view class="tips">含服务费：{{shouxufei}} 元</view>
    <view class="btn" @click="toPay()">塞钱进红包</view>
  </view>
</template>

<script>
import myPopup from '../components/myPopup.vue'
import pay from '../components/pay.vue'
import { config } from '../common/index'
export default {
  components: {
    myPopup,
    pay
  },
  data() {
    return {
      pay_params: {
        info_id: '',
        info_type: '',
        money: '',
        hb_count:'' ,
        
      },
      money:"",
      hbConfig:{
        help_sai_min_money:0, //塞红包最小金额
        help_min_money:0, //红包最小金额
        help_max_money:0, //红包最大金额
        help_sai_service_rate:0
      },
      max_count:0, 
      min_count:0,
      top_img: config.imgDomain + '/hongbao/sai_banner.png',
    }
  },
  onLoad(options) {
    this.pay_params.info_id = options.id
    this.pay_params.info_type = options.type
    this.getHbConfig()
    this.getWxConfig(
        ['chooseWXPay', 'hideOptionMenu'],
        wx => {
        console.log('执行回调')
        this.wx = wx
        },
        1
    )
  },
  computed: {
    shouxufei(){
      if (!this.pay_params.money) return 0
      let fuwufee =Number(this.pay_params.money) *this.hbConfig.help_sai_service_rate/100
      return Math.round(fuwufee*Math.pow(10, 2))/Math.pow(10, 2)
    }
  },
  watch:{
    "pay_params.money"(val,oval){
      this.money = Math.round((Number(val) + this.shouxufei)*Math.pow(10, 2))/Math.pow(10, 2)
      let  min_count =Math.floor(Number(this.pay_params.money)/ Number(this.hbConfig.help_max_money));
      this.min_count = min_count>0?min_count:1
      this.max_count =Math.floor(Number(this.pay_params.money)/ Number(this.hbConfig.help_min_money));
      this.$set(this.pay_params,"hb_count",this.max_count)
    }
  },
  methods: {
    getHbConfig() {
      this.$ajax.get('WxMoney/saiHbConfig',{}, res => {
        if (res.data.code == 1) {
          this.hbConfig = res.data.config
        }
         
      })
    },
    inputChange(e){
      e.target.value = (e.target.value.match(/^\d*(\.?\d{0,2})/g)[0]) || null
      this.$nextTick(() => {
          this.pay_params.money= e.target.value
      })
    },
    hbCountChange(e){
      var  val =e.detail.value
      if (val>this.max_count) {
        uni.showToast({
          title:"红包份数不能大于" +this.max_count+'份',
          icon:"none"
        })
      this.$set(this.pay_params,"hb_count",this.max_count)
      }
      if (val<this.min_count) {
        uni.showToast({
          title:"红包份数不能小于" +this.min_count+'份',
          icon:"none"
        })
        this.$set(this.pay_params,"hb_count",this.min_count)
      }
      
    },
    toPay() {
       if (!this.pay_params.money) {
        uni.showToast({
          title:"请输入红包金额",
          icon:"none"
        })
        return
      }
      var hb_min_count = Math.floor(Number(this.pay_params.money)/ Number(this.hbConfig.help_max_money) );
      hb_min_count = hb_min_count>0?hb_min_count:1
    	var hb_max_count = Math.floor(Number(this.pay_params.money)/ Number(this.hbConfig.help_min_money) );
      if (this.pay_params.money< this.hbConfig.help_sai_min_money) {
        uni.showToast({
          title:"红包金额不能小于"+this.hbConfig.help_sai_min_money+'元',
          icon:"none"
        })
        return
      }
      if (this.pay_params.hb_count>hb_max_count) {
        uni.showToast({
          title:"红包份数不能大于" +hb_max_count+'份',
          icon:"none"
        })
        return
      }
      if (this.pay_params.hb_count==0  ||this.pay_params.hb_count<hb_min_count) {
        uni.showToast({
          title:"红包份数不能小于" +hb_min_count+'份',
          icon:"none"
        })
        return
      }
      
      this.wxpay()
    },
    wxpay() {
      this.pay_params.pay_type = 1
      this.$ajax.post('WxMoney/saihb', this.pay_params, res => {
        if (res.data.code == 1) {
          let pay_info = res.data.data
          this.wx.chooseWXPay({
            // provider: 'wxpay',
            timestamp:pay_info.timeStamp,
            nonceStr:pay_info.nonceStr,
            package:pay_info.package,
            signType:pay_info.signType,
            paySign:pay_info.paySign,
            success: res => {
                uni.showToast({
                    title: '支付成功'
                })
                setTimeout(() => {
                    uni.navigateBack()
                }, 1500)
            },
            fail: function(err) {
                console.log('支付失败：', err)
                uni.showToast({
                    title: err.err_desc || err.errMsg,
                    icon: 'none',
                    duration: 5000
                })
            }
          })
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  padding: 38rpx 48rpx;
}
.img{
  position: relative;
  image{
    width: 100%;
  }
}
.top {
  display: flex;
  align-items: center;
  border-radius: 4rpx;
  background: #FFFFFF;
  padding: 28rpx 44rpx 28rpx 28rpx;
  height: 100rpx;
  box-sizing: border-box;
  .label {
    color: #000;
    font-size: 32rpx;
  }
  .input {
    text-align: right;
    flex: 1;
    font-size: 32rpx;
    margin-right: 16rpx;
    margin-top: 4rpx;
  }
}
.count_tips {
  font-size: 22rpx;
  color: #9B9B9B;
  text-align: right;
  margin: 20rpx 0;
}
.tips {
  font-size: 22rpx;
  color: #9B9B9B;
  text-align: center;
  margin: 20rpx 0;
}
.show {
  color: #000;
  text-align: center;
  font-size: 72rpx;
  margin-top: 154rpx;
}
.btn {
  width: 340rpx;
  height: 84rpx;
  line-height: 84rpx;
  text-align: center;
  border-radius: 10rpx;
  background: #FF5B5B;
  margin: 0 auto;
  margin-top: 86rpx;
  color: #fff;
  font-size: 32rpx;
}
</style>