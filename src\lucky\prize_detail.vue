<template>
  <view>
    <view class="card" v-if="type==='detail'&&prize_detail.id">
      <view class="user_info bottom-line">
        <view class="row">
          <view class="label">中奖人</view>
          <view class="value">{{user_info.cname}}</view>
        </view>
        <view class="row">
          <view class="label">联系电话</view>
          <view class="value">{{user_info.tel}}</view>
        </view>
        <view class="row">
          <view class="tip">{{prize_detail.activity_name}}</view>
        </view>
      </view>
      <view class="prize_info">
        <image class="pic" :src="prize_detail.pic | imageFilter"></image>
        <view class="name">{{prize_detail.name}}</view>
        <view class="btn" @click="handleWriteOff">派奖</view>
      </view>
    </view>
    <view class="prize_list" v-if="type==='list'">
      <view class="prize_info out" v-for="(item, index) in prize_list" :key="index">
        <image class="pic" :src="item.pic | imageFilter('w_240')"></image>
        <view class="info">
          <view class="name">{{item.name}}</view>
          <view class="activity_name">活动：{{item.activity_name}}</view>
        </view>
        <view>剩余<text class="lh">{{item.surplus_write_off}}</text>件</view>
      </view>
    </view>
    <view class="bottom_nav" v-if="prize_id">
      <view class="nav_item" @click="checkTab('detail')" :class="{active: type=='detail'}">
        <my-icon type="hexiao" :color="type==='detail'?'#e22f1a':'#999999'" size="44rpx"></my-icon>
        <text>奖品兑换</text>
      </view>
      <view class="nav_item" @click="checkTab('list')" :class="{active: type=='list'}">
        <my-icon type="jiangpinliebiao" :color="type==='list'?'#e22f1a':'#999999'" size="44rpx"></my-icon>
        <text>奖品列表</text>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
export default {
  name: '',
  components: {
    myIcon
  },
  data () {
    return {
      type: 'detail',
      prize_id: '',
      prize_detail: {},
      user_info: {},
      prize_list: []
    }
  },
  onLoad(options){
    this.type = options.type||'detail'
    if(options.id){
      this.prize_id = options.id
      this.getData(this.prize_id)
    }
    if(this.type === 'list'){
      this.getPrizeList()
    }
  },
  methods: {
    getData(id){
      this.$ajax.get('activity/writeOffInfo.html', {id}, res=>{
        if(res.data.code !== 1){
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
          return
        }
        this.prize_detail = res.data.prize
        this.user_info = res.data.prize_user
      })
    },
    getPrizeList(){
      this.$ajax.get('activity/grantPrizeList', {}, res=>{
        if(res.data.code === 1){
          this.prize_list = res.data.list
        }
      })
    },
    checkTab(type){
      if(this.type === type){
        return
      }
      this.type = type
      if(this.type === 'list'){
        this.getPrizeList()
      }
      if(this.type === 'detail'){
        this.getData(this.prize_id)
      }
    },
    handleWriteOff(){
      this.$ajax.get('activity/confirmWriteOff.html', {id: this.prize_id}, res=>{
        if(res.data.code !== 1){
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
          return
        }
        uni.showToast({
          title: res.data.msg
        })
      })
      console.log("执行兑奖")
    }
  }
}
</script>

<style scoped lang="scss">
.bottom_nav{
  position: fixed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  .nav_item{
    flex: 1;
    height: 66rpx;
    padding: 12rpx 48rpx;
    font-size: 24rpx;
    color: #999;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    &.active{
      color: $uni-color-primary;
    }
  }
}
.card{
  margin: 24rpx;
  padding: 48rpx;
  border-radius: 12rpx;
  background-color: #fff;
  .user_info{
    padding-bottom: 24rpx;
    margin-bottom: 48rpx;
  }
  .row{
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    .label{
      width: 112rpx;
      margin-right: 24rpx;
      padding: 0;
      color: #999;
    }
    .value{
      font-weight: bold;
      color: #333;
    }
    .tip{
      font-weight: bold;
      color: #e22f1a;
    }
  }
}

.prize_info{
  display: flex;
  align-items: center;
  justify-content: space-between;
  &.out{
    margin: 24rpx;
    border-radius: 12rpx;
    padding: 24rpx;
    background-color: #fff;
    .name{
      margin-bottom: 24rpx;
    }
  }
  .pic{
    width: 108rpx;
    height: 108rpx;
    margin-right: 20rpx;
    border-radius: 8rpx;
    background-color: #f8f8f8;
  }
  .info{
    flex: 1;
  }
  .name{
    flex: 1;
    font-size: 32rpx;
    font-weight: bold;
    margin-right: 24rpx;
  }
  .activity_name{
    font-size: 24rpx;
    color: #e22f1a;
  }
  .btn{
    width: 176rpx;
    text-align: center;
    height: 64rpx;
    line-height: 64rpx;
    background-image: linear-gradient(180deg, #FF8163 0%, #E22F1A 100%);
    box-shadow: 0 8rpx 16rpx 0 rgba(240,86,61,0.40);
    border-radius: 44rpx;
    color: #fff;
  }
  .lh{
    color: #e22f1a;
  }
}


.prize_list{
  padding-bottom: 120rpx;
}
</style>