<template>
  <view class="detail">
    <view class="swiper-box" v-if="is_share">
      <!-- <swiper vertical autoplay circular>
        <swiper-item>
          <view class="sharers_info flex-row">
            <image class="prelogo" mode="aspectFill" :src="sharers_info.prelogo"></image>
            <view class="info">
              <text class="cname">{{ sharers_info.cname }}</text>
              <text class="title">快来帮我看一下，哪一个房子更适合我？</text>
            </view>
          </view>
        </swiper-item>
      </swiper> -->
    </view>
    <view class="house-box flex-row" v-if="contrast_list.length === 2">
      <view class="item">
        <view class="label" v-if="is_share">A房源</view>
        <view class="house" @click="previewImage('a')">
          <image :src="contrast_list[0].img_path | imageFilter('w_240')" mode="aspectFill"></image>
          <view class="build_title">{{ contrast_list[0].title }}</view>
          <view class="btn flex-row" v-if="!is_share" @click.stop.prevent="$navigateBack()">
            <my-icon type="huanyihuan"></my-icon>
            <text>换一个</text>
          </view>
        </view>
      </view>
      <view class="pk-icon-box">
        <my-icon type="pk" color="#fff" size="28rpx"></my-icon>
      </view>
      <view class="item">
        <view class="label" v-if="is_share">B房源</view>
        <view class="house house2" @click="previewImage('b')">
          <image :src="contrast_list[1].img_path | imageFilter('w_240')" mode="aspectFill"></image>
          <view class="build_title">{{ contrast_list[1].title }}</view>
          <view class="btn flex-row" v-if="!is_share" @click.stop.prevent="$navigateBack()">
            <my-icon type="huanyihuan"></my-icon>
            <text>换一个</text>
          </view>
        </view>
      </view>
    </view>
    <view class="recommend flex-row" v-if="contrast_list.length === 2 && is_share">
      <view class="left">
        <view
          class="prelogos"
          v-if="contrast_list[0].support_log.length > 0 || contrast_list[1].support_log.length > 0"
        >
          <image
            class="prelogo"
            v-for="(prelogo, index) in contrast_list[0].support_log"
            :key="index"
            mode="aspectFill"
            :src="prelogo | imageFilter('w_80')"
          ></image>
        </view>
        <view class="btn" @click="handleRecommend(contrast_list[0].id, 'a')">推荐A</view>
      </view>
      <view class="center flex-1">
        <view class="nums flex-row">
          <text class="num">{{ contrast_list[0] && contrast_list[0].support }}人</text>
          <text class="num">{{ contrast_list[1] && contrast_list[1].support }}人</text>
        </view>
        <view class="progress-box flex-row">
          <view
            class="progress progress1 flex-1"
            :style="contrast_list | getProportion(contrast_list[0].support, contrast_list[1].support, 'a')"
          ></view>
          <view class="icon-box">
            <my-icon type="pk" color="#ff7c82" size="26rpx"></my-icon>
          </view>
          <view
            class="progress progress2 flex-1"
            :style="contrast_list | getProportion(contrast_list[0].support, contrast_list[1].support, 'b')"
          ></view>
        </view>
        <view class="swiper-box" v-if="is_share">
          <swiper vertical autoplay circular>
            <swiper-item>
              <view class="sharers_info flex-row">
                <!-- <image class="prelogo" mode="aspectFill" :src="sharers_info.prelogo"></image> -->
                <view class="info">
                  <!-- <text class="cname">{{ sharers_info.cname }}</text> -->
                  <text class="title">哪个房子更适合我？</text>
                </view>
              </view>
            </swiper-item>
            <swiper-item>
              <view class="sharers_info flex-row">
                <!-- <image class="prelogo" mode="aspectFill" :src="sharers_info.prelogo"></image> -->
                <view class="info">
                  <!-- <text class="cname">{{ sharers_info.cname }}</text> -->
                  <text class="title">哪个房子更适合我？</text>
                </view>
              </view>
            </swiper-item>
          </swiper>
        </view>
      </view>
      <view class="right">
        <view
          class="prelogos"
          v-if="contrast_list[0].support_log.length > 0 || contrast_list[1].support_log.length > 0"
        >
          <image
            class="prelogo"
            v-for="(prelogo, index) in contrast_list[1].support_log"
            :key="index"
            mode=""
            :src="prelogo | imageFilter('w_80')"
          ></image>
        </view>
        <view class="btn btn_b" @click="handleRecommend(contrast_list[1].id, 'b')">推荐B</view>
      </view>
    </view>
    <view class="table-box flex-1">
      <scroll-view scroll-y @scroll="onTabScroll" @scrolltolower="onScrollBottom" :lower-threshold="20">
        <view class="table" id="tb">
          <view class="tr">
            <view class="th text-center">基础信息</view>
          </view>
          <view class="tr flex-row" v-if="type == 1">
            <view class="th">总价</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{
              item.price == '面议' || item.price == '0' ? '面议' : item.price + item.price_unit
            }}</view>
          </view>
          <view class="tr flex-row" v-if="type == 2">
            <view class="th">租金</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{
              item.price == '面议' || item.price == '0' ? '面议' : item.price + item.price_unit
            }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">小区名称</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.community_name || '-' }}</view>
          </view>
          <view class="tr flex-row">
            <view class="th">面积</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.mianji }}{{ item.mianji_unit }}</view>
          </view>
          <view class="tr flex-row" v-if="contrast_list[0] && contrast_list[0].catid < 3">
            <view class="th">楼层</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">
              <text>{{ item.floor_title }}</text>
              <text v-if="item.szlc"
                >{{ item.szlc }}层{{ item.szlc2 !== 0 ? `至${item.szlc2}层` : '' }}/共{{ item.louceng || '' }}层</text
              >
              <text v-else>共{{ item.louceng || '' }}层</text>
            </view>
          </view>
          <view class="tr flex-row" v-if="type == 1">
            <view class="th">单价</view>
            <block v-for="(item, index) in contrast_list" :key="index">
              <view class="td" v-if="item.danjia">{{ item.danjia || '' }}元/㎡</view>
              <view class="td" v-else>暂未更新</view>
            </block>
          </view>
          <view class="tr flex-row" v-if="contrast_list[0] && contrast_list[0].catid < 3">
            <view class="th">装修情况</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.zhuangxiu }}</view>
          </view>
          <template v-if="contrast_list[0] && contrast_list[0].catid == 1">
            <view class="tr flex-row">
              <view class="th">经营状态</view>
              <view class="td" v-for="(item, index) in contrast_list" :key="index">{{item.business_status == 1 ? '经营中' : '空置中'}}</view>
            </view>
            <view class="tr flex-row">
              <view class="th">经营行业</view>
              <view class="td" v-for="(item, index) in contrast_list" :key="index">
                <text v-if="item.trade_title">{{item.trade_ptitle}}-{{item.trade_title}}</text>
                <text v-else>-</text>
              </view>
            </view>
            <view class="tr flex-row">
              <view class="th">规格</view>
              <view class="td" v-for="(item, index) in contrast_list" :key="index">面宽{{item.sizes_width}}m、层高{{item.sizes_height}}m、进深{{item.sizes_depth}}m</view>
            </view>
            <view class="tr flex-row">
              <view class="th">客流人群</view>
              <view class="td" v-for="(item, index) in contrast_list" :key="index">
                <text v-if="item.consumer_ids != 0">{{item.consumer_ids || '-'}}</text>
                <text v-else>-</text>
              </view>
            </view>
          </template>
          <template v-if="contrast_list[0] && contrast_list[0].catid == 2">
            <view class="tr flex-row">
              <view class="th">可注册</view>
              <view class="td" v-for="(item, index) in contrast_list" :key="index">{{item.can_register == 1 ? '是' : '否'}}</view>
            </view>
            <view class="tr flex-row">
              <view class="th">可拆分</view>
              <view class="td" v-for="(item, index) in contrast_list" :key="index">{{item.can_divisible == 1 ? '是' : '否'}}</view>
            </view>
          </template>
          <template v-if="contrast_list[0] && contrast_list[0].catid == 5">
            <view class="tr flex-row">
              <view class="th">土地来源</view>
              <view class="td" v-for="(item, index) in contrast_list" :key="index">{{item.land_source_title}}</view>
            </view>
            <view class="tr flex-row">
              <view class="th">流转年限</view>
              <view class="td" v-for="(item, index) in contrast_list" :key="index">{{item.useful_years}}年</view>
            </view>
            <view class="tr flex-row">
              <view class="th">土地使用证</view>
              <view class="td" v-for="(item, index) in contrast_list" :key="index">{{item.land_certificate == 1 ? '有' : '无'}}</view>
            </view>
            <view class="tr flex-row">
              <view class="th">所有权证</view>
              <view class="td" v-for="(item, index) in contrast_list" :key="index">{{item.owner_certificate == 1 ? '有' : '无'}}</view>
            </view>
          </template>
          <view class="tr flex-row" v-if="type == 1">
            <view class="th">首付</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.sfje || '-' }}</view>
          </view>
          <view class="tr flex-row" v-if="type == 1">
            <view class="th">贷款年限</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.year || '30年' }}</view>
          </view>
          <view class="tr flex-row" v-if="type == 1">
            <view class="th">月供</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.emTotal || '-' }}</view>
          </view>
          <view class="tr flex-row" v-if="type == 1">
            <view class="th">利息</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">{{ item.lixi || '-' }}</view>
          </view>
          <!-- <view class="tr flex-row">
            <view class="th">房型标签</view>
            <view class="td" v-for="(item, index) in contrast_list" :key="index">
              <text v-for="(label, idx) in item.label" :key="idx">{{label.name}}</text>
            </view>
          </view> -->
          <view class="tr">
            <view class="th text-center">配套设施</view>
          </view>
          <block>
            <view class="tr flex-row" v-for="(info, idx) in wnsb" :key="idx">
              <block v-if="info.name.trim() !== '无' && info.name.trim() !== '暂未更新'">
                <view class="th">{{ info.name }}</view>
                <view class="td" v-for="(item, index) in info.value" :key="index">{{ item ? '有' : '无' }}</view>
              </block>
            </view>
          </block>
        </view>
        <view class="tip" style="margin-bottom: 120rpx">免责声明：以上信息均为参考，实际情况以发布者为准</view>
      </scroll-view>
      <!-- #ifdef H5 || APP-PLUS -->
      <view class="btn-box" v-if="info_contrast_id && is_share" :class="{ show: show_share_btn || table_height_small }">
        <view class="btns flex-row">
          <view class="btn" @click="showShareTip">帮好友转发</view>
          <view class="btn active" @click="createPk(contrast_list[0].id)"
            >创建房源PK</view
          >
        </view>
      </view>
      <view class="share_btn" v-else-if="info_contrast_id" :class="{ show: show_share_btn || table_height_small }" @click="showShareTip"
        >分享好友帮我选</view
      >
      <view class="share_btn" v-else :class="{ show: show_share_btn || table_height_small }" @click="creatContrastId">生成分享链接</view>
      <!-- #endif -->
      <!-- #ifdef MP -->
      <view class="btn-box" v-if="info_contrast_id && is_share">
        <view class="btns flex-row">
          <button open-type="share" class="btn">帮好友转发</button>
          <view class="btn active" @click="createPk(contrast_list[0].id)"
            >创建房源PK</view
          >
        </view>
      </view>
      <button class="share_btn" v-else-if="info_contrast_id" :class="{ show: show_share_btn || table_height_small }" open-type="share">
        分享好友帮我选
      </button>
      <view class="share_btn" v-else :class="{ show: show_share_btn || table_height_small }" @click="creatContrastId">生成分享链接</view>
      <!-- #endif -->
    </view>
    <shareTip :show="show_share" @hide="show_share = false"></shareTip>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
import shareTip from '../components/shareTip'
import chatBtn from '../components/open-button/chatBtn'
import { formatImg } from '../common/index.js'
import getChatInfo from '../common/get_chat_info'
export default {
  components: {
    myIcon,
    shareTip,
    chatBtn,
  },
  data() {
    return {
      is_share: 0,
      contrast_list: [],
      show_share_btn: false,
      info_contrast_id: '',
      sharers_id: '',
      sharers_info: {},
      show_share: false,
      table_height_small: false,  //table高度太小会直接显示分享按钮
      type: '',
      wnsb: [],
    }
  },
  computed: {
    login_status() {
      return this.$store.state.user_login_status
    },
    is_open_im() {
      //是否开通了聊天共功能
      return this.$store.state.im.ischat
    },
  },
  onLoad(options) {
    this.type = options.type || 1
    this.id1 = options.id1 || ''
    this.id2 = options.id2 || ''
    this.info_id1 = options.info_id1 || ''
    this.info_id2 = options.info_id2 || ''
    this.sharers_id = options.sharers_id || ''
    this.info_contrast_id = options.info_contrast_id || ''
    this.is_share = options.is_share
    if (options.is_share && (!options.info_contrast_id || options.info_contrast_id === 'undefined')) {
      console.log('没有创建房源对比关系就分享了,跳转到对比列表')
      uni.redirectTo({
        url: '/contrast/info_list',
      })
      return
    }
    if (this.info_id1 && this.info_id2) {
      this.getContrastDetail2(this.info_id1, this.info_id2)
      uni.$on('replaceInfo', (data) => {
        if (this.info_id1 == data) {
          setTimeout(() => {
            uni.showToast({
              title: '此房源已在对比中',
              icon: 'none',
            })
          }, 200)
          return
        }
        this.info_id2 = data
        this.getContrastDetail2(this.info_id1, this.info_id2)
      })
      return
    }
    this.getContrastDetail(this.id1, this.id2, this.info_contrast_id, this.sharers_id)
    uni.$on('replaceInfo', (data) => {
      if (this.id1 == data) {
        setTimeout(() => {
          uni.showToast({
            title: '此房源已在对比中',
            icon: 'none',
          })
        }, 200)
        return
      }
      this.id2 = data
      this.getContrastDetail(this.id1, this.id2)
    })
  },
  onShow() {
    this.getTableHeight()
  },
  onUnload() {
    uni.$off('replaceErshou')
  },
  filters: {
    getProportion(contrast_list, support_a, support_b, type) {
      if (contrast_list.length < 2) {
        if (type === 'a') {
          return `min-width:50%;background-image:url(${formatImg('/images/new_icon/progress1.png', 'w_80')})`
        }
        if (type === 'b') {
          return `min-width:50%;background-image:url(${formatImg('/images/new_icon/progress2.png', 'w_80')})`
        }
      }
      if (support_a == 0 && support_b == 0) {
        if (type === 'a') {
          return `min-width:50%;background-image:url(${formatImg('/images/new_icon/progress1.png', 'w_80')})`
        }
        if (type === 'b') {
          return `min-width:50%;background-image:url(${formatImg('/images/new_icon/progress2.png', 'w_80')})`
        }
      }
      if (type === 'a') {
        return `min-width:${(support_a * 100) / (support_a + support_b)}%;background-image:url(${formatImg(
          '/images/new_icon/progress1.png',
          'w_80'
        )})`
      }
      if (type === 'b') {
        return `min-width:${(support_b * 100) / (support_a + support_b)}%;background-image:url(${formatImg(
          '/images/new_icon/progress2.png',
          'w_80'
        )})`
      }
    },
  },
  methods: {
    getContrastDetail(id1, id2, info_contrast_id, sharers_id) {
      let params = { contrast_id: id1 + ',' + id2, type: this.type }
      if (info_contrast_id) params.info_contrast_id = info_contrast_id
      if (sharers_id) params.create_share_uid = sharers_id
      this.$ajax.get('estate/contrast', params, (res) => {
        if (res.data.code !== 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
          return
        }
        if (res.data.share) this.share = res.data.share
        if (res.data.createShareUser) this.sharers_info = res.data.createShareUser
        this.contrast_list = res.data.list
        let wnsb = []
        this.contrast_list.forEach((item, index) => {
          if (item.wnsb) {
            let item_wnsb = item.wnsb.split(',')
            item_wnsb.forEach((info) => {
              let curr_index = wnsb.findIndex((el) => el.name === info)
              if (curr_index === -1) {
                let wnsb_item = { name: info, value: new Array(this.contrast_list.length).fill(0) }
                wnsb_item.value[index] = 1
                wnsb.push(wnsb_item)
              } else {
                wnsb[curr_index].value[index] = 1
              }
            })
          }
        })
        this.wnsb = wnsb
        console.log(this.wnsb)
        if(this.sharers_id){
          this.setWxConfig()
        }
      })
    },
    getContrastDetail2(info_id1, info_id2) {
      this.$ajax.get(
        'estate/contrastListNoLogin',
        { info_ids: info_id1 + ',' + info_id2, type: this.type },
        (res) => {
          if (res.data.code !== 1) {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
            })
            return
          }
          if (res.data.share) this.share = res.data.share
          this.contrast_list = res.data.list
        }
      )
    },
    creatContrastId() {
      this.$store.state.allowOpen = true
      uni.showLoading({
        title: '正在生成',
        mask: true,
      })
      let api = 'estate/shareContrast'
      let params = {}
      if (this.id1 && this.id2) {
        params = { contrast_id: this.id1 + ',' + this.id2 }
      }
      if (this.info_id1 && this.info_id2) {
        api = 'estate/addShareContrast'
        params = { info_ids: this.info_id1 + ',' + this.info_id2 }
      }
      this.$ajax.get(api, params, (res) => {
        uni.hideLoading()
        if (res.data.code === 1) {
          this.info_contrast_id = res.data.info_contrast_id
          if (!this.sharers_id && res.data.create_share_uid) this.sharers_id = res.data.create_share_uid
          if (res.data.contrast_id) {
            this.id1 = res.data.contrast_id.split(',')[0]
            this.id2 = res.data.contrast_id.split(',')[1]
          }
          this.setWxConfig()
          uni.showToast({
            title: '链接生成成功',
          })
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    setWxConfig(){
      const link = `${window.location.origin}/h5/contrast/commercial_detail?type=${this.type}&id1=${this.id1}&id2=${this.id2}&info_contrast_id=${this.info_contrast_id || ''}&sharers_id=${this.sharers_id}&is_share=1`
      if(this.share){
        this.share.link = link
      }else{
        this.share = {
          link:link,
          pic: this.contrast_list[0].img_path,
          title:'快来帮我看一下，哪一个户型更适合我？',
          content:"我分享了一个户型对比投票，赶快来看看吧。"
        }
      }
      this.getWxConfig()
    },
    showShareTip() {
      this.show_share = true
      let path = `${window.location.origin}/h5/contrast/commercial_detail?type=${this.type}&id1=${this.id1}&id2=${
        this.id2
      }&info_contrast_id=${this.info_contrast_id || ''}&sharers_id=${this.sharers_id}&is_share=1`
      console.log(path)
    },
    createPk(id) {
      if (this.type == 1) {
        this.$navigateTo(`/commercial/sale/detail?id=${id}`)
      }
      if (this.type == 2) {
        this.$navigateTo(`/commercial/rent/detail?id=${id}`)
      }
      if (this.type == 3) {
        this.$navigateTo(`/commercial/transfer/detail?id=${id}`)
      }
    },
    handleAsk(agent_id) {
      getChatInfo(agent_id, 6)
    },
    handleRecommend(info_id, type) {
      this.$ajax.get('estate/supportContrast', { info_contrast_id: this.info_contrast_id, info_id }, (res) => {
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg,
          })
          if (type === 'a') {
            this.contrast_list[0].support++
            this.contrast_list[0].support_log.push(res.data.prelogo)
          }
          if (type === 'b') {
            this.contrast_list[1].support++
            this.contrast_list[1].support_log.push(res.data.prelogo)
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
          })
        }
      })
    },
    previewImage(type) {
      if (type === 'a') {
        uni.previewImage({
          urls: [formatImg(this.contrast_list[0].img_path, 'w_860')],
          current: 0,
        })
      }
      if (type === 'b') {
        uni.previewImage({
          urls: [formatImg(this.contrast_list[1].img_path, 'w_860')],
          current: 0,
        })
      }
    },
    onTabScroll(e) {
      if (!this.is_share) {
        this.show_share_btn = true
      }
      if (e.detail.scrollTop <= 60) {
        this.show_share_btn = false
      }
    },
    onScrollBottom(e) {
      this.show_share_btn = true
    },
    getTableHeight() {
      const query = uni.createSelectorQuery().in(this)
      query
      .select('.table')
      .boundingClientRect((data) => {
        if (data && this.contrast_list.length > 0) {
          if (data.height < 500) {
            this.table_height_small = true
          }
        } else {
          setTimeout(() => {
            this.getTableHeight()
          },500)
        }
      })
      .exec()
    }
  },
  onShareAppMessage(type) {
    let path = `/contrast/commercial_detail?type=${this.type}&id1=${this.id1}&id2=${this.id2}&sharers_id=${this.sharers_id}&is_share=1`
    if (this.info_contrast_id) {
      path = `/contrast/commercial_detail?type=${this.type}&id1=${this.id1}&id2=${this.id2}&info_contrast_id=${
        this.info_contrast_id || ''
      }&sharers_id=${this.sharers_id}&is_share=1`
    }
    return {
      path,
      title: (this.share && this.share.title) || '快来帮我看一下，哪一个房子更适合我？',
      content: (this.share && this.share.content) || '',
      imageUrl:
        this.share && this.share.pic
          ? formatImg(this.share.pic, 'w_6401')
          : formatImg(contrast_list[0].img_path, 'w_6401'),
    }
  },
}
</script>

<style scoped lang="scss">
.detail {
  height: calc(100vh - 44px);
  padding: 24rpx 48rpx;
  background-color: #fff;
}
view {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.swiper-box {
  // position: fixed;
  // left: 24rpx;
  // right: 72rpx;
  // top: 44px;
  // z-index: 98;
  // border-top-right-radius:16rpx;
  // border-bottom-right-radius:16rpx;
  swiper {
    height: 70rpx;
    color: #666;
  }
}

.sharers_info {
  .prelogo {
    width: 64rpx;
    height: 64rpx;
    border-radius: 32rpx;
    margin-right: 16rpx;
  }
  .info {
    flex: 1;
    overflow: hidden;
    .cname {
      line-height: 1;
      margin-bottom: 16rpx;
      font-size: 26rpx;
      color: #333;
    }
    .title {
      text-align: center;
      font-size: 22rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 10rpx 10rpx;
      box-sizing: border-box;
      // border-radius: 8rpx;
      // background-color: rgba(#000, 0.5);
    }
  }
}

.recommend {
  margin-top: 48rpx;
  justify-content: space-between;
  align-items: center;
  .prelogos {
    display: block;
    margin-bottom: 24rpx;
    height: 48rpx;
    .prelogo {
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      float: left;
      ~ .prelogo {
        margin-left: -24rpx;
      }
    }
    .prelogo2 {
      float: right;
      ~ .prelogo2 {
        margin-right: -24rpx;
      }
    }
  }
  .center {
    margin: 0 24rpx;
    .nums {
      justify-content: space-between;
      font-size: 24rpx;
      color: #999;
    }
    .progress-box {
      align-items: center;
      width: 100%;
    }
    .progress {
      height: 24rpx;
      border-radius: 12rpx;
      background-size: auto 100%;
      background-repeat: repeat-x;
      &.progress1 {
        margin-right: -20rpx;
      }
      &.progress2 {
        margin-left: -20rpx;
      }
    }
    .icon-box {
      position: relative;
      z-index: 1;
      justify-content: center;
      align-items: center;
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      border: 1rpx solid #f5f5f5;
      background-color: #fff;
    }
  }
  .btn {
    line-height: 64rpx;
    border-radius: 32rpx;
    padding: 0 32rpx;
    background-color: #ff7c82;
    color: #fff;
    &.btn_b {
      background-color: #8795f5;
    }
  }
}

.house-box {
  align-items: center;
  justify-content: space-between;
  .item {
    width: 280rpx;
  }
  .label {
    text-align: center;
    margin-bottom: 10rpx;
    color: #333;
  }
  .house {
    padding: 24rpx;
    border-radius: 16rpx;
    line-height: 1;
    background-color: #ff7c81;
    color: #fff;
    box-shadow: 0 4px 8px 0 rgba(251, 101, 106, 0.2);
    overflow: hidden;
    > image {
      width: 232rpx;
      height: 128rpx;
      border-radius: 8rpx;
    }
    .build_title {
      margin-top: 24rpx;
      font-size: 32rpx;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
    }
    .desc {
      font-size: 24rpx;
      margin-top: 16rpx;
    }
    .btn {
      margin-top: 24rpx;
      align-items: center;
      justify-content: center;
      line-height: 48rpx;
      padding: 0 24rpx;
      border-radius: 24rpx;
      text-align: center;
      font-size: 24rpx;
      color: #333;
      background-color: #fff;
    }
    &.house2 {
      background-color: #8997fa;
      box-shadow: 0 4px 10px 0 rgba(139, 153, 255, 0.2);
    }
  }
  .pk-icon-box {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    background: $uni-color-primary;
    box-shadow: 0 2px 8px 0 rgba($uni-color-primary, 0.4);
  }
}

.status1 {
  color: #4cc7f6;
}
.status2 {
  color: #00caa7;
}
.status3 {
  color: #ff7213;
}
.status4 {
  color: #666;
}

.table-box {
  margin-top: 72rpx;
  overflow: hidden;
  position: relative;
  > scroll-view {
    height: 100%;
  }
}

.table {
  line-height: 1.5;
  border: 1rpx solid #d8d8d8;
  border-bottom: 0;
  color: #333;
  .tr {
    .td {
      flex: 1;
    }
  }
  .th {
    justify-content: center;
    min-width: 180rpx;
    padding: 20rpx;
    text-align: center;
    font-size: 28rpx;
    background-color: #f5f5f5;
    border-bottom: 1rpx solid #d8d8d8;
    color: #666;
  }
  .td {
    justify-content: center;
    padding: 20rpx;
    align-items: center;
    font-size: 28rpx;
    border-bottom: 1rpx solid #d8d8d8;
    border-left: 1rpx solid #d8d8d8;
    .btn_box {
      margin-top: 10rpx;
      justify-content: center;
      align-items: center;
      font-size: 22rpx;
      color: #fff;
      width: 120rpx;
      height: 46rpx;
      border-radius: 23rpx;
      padding: 0 10rpx;
      background: linear-gradient(90deg, #fb656a 30%, #fbac65 100%);
      > .zx {
        margin-left: 10rpx;
      }
    }
  }
}

.share_btn {
  position: absolute;
  bottom: -88rpx;
  left: 0;
  right: 0;
  display: block;
  margin: 0;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  color: #fff;
  background: $uni-color-primary;
  box-shadow: 0 4px 12px 0 rgba($uni-color-primary, 0.4);
  transition: 0.36s;
  &.show {
    bottom: 24rpx;
  }
}

.tip {
  padding: 24rpx 0;
  color: #999;
  font-size: 24rpx;
}

.btn-box {
  padding: 0;
  position: absolute;
  bottom: -118rpx;
  left: 0;
  right: 0;
  display: block;
  transition: 0.36s;
  &.show {
    bottom: 24rpx;
  }
  .btns {
    line-height: 88rpx;
    border-radius: 44rpx;
    border: 1rpx solid $uni-color-primary;
    overflow: hidden;
    box-shadow: 0 4px 10px 0 rgba(251, 101, 106, 0.2);
    .btn {
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      color: $uni-color-primary;
      background-color: #fff;
      &.active {
        background-color: $uni-color-primary;
        color: #fff;
      }
    }
  }
}
</style>
