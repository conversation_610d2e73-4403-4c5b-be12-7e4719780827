<template>
  <view class="page"  @touchstart="firstPlayingAudio">
    <view class="video" v-if ='videos.length'>
      <swiper :style="{'height':height}" class="banner" :indicator-dots="false"  :autoplay="false"  @change="onChange" :vertical="true" :current ="current" >
        <swiper-item v-for="(video,index) in videos" :key="video.id" >
          <view class="video_item" :class ='{"video_play_scale":show_pinglun}'>
            <video  class ="video_play"   :src="video.path" style="width:100%"  :id='"video_play"+video.attached[0].id'  :show-fullscreen-btn="false"  :controls="false" :show-play-btn="false" :show-center-play-btn="false "  :vslide-gesture-in-fullscreen="false" :enable-progress-gesture="false" :disable-touch ='show_pinglun'  :show-mute-btn="false" :title="title" :direction="0" @play="playVideo" @pause="pauseVideo" @waiting="waitingVideo" object-fit="contain">
            </video>
            
            
            <cover-view class="fix_right" @click ="toDetail">
                <cover-view  class="prelogo">
                  <cover-image mode='widthFix' :src='detail.prelogo | imageFilter("m_80")'></cover-image>
                </cover-view>
                <cover-view class="right_oper" @click ="collect">
                  <cover-view class="right_img">
                    <cover-image mode='widthFix' v-if='is_follow==0'  :src='"/yidongduan/vr_video/collect.png" | imageFilter("m_80")'></cover-image>
                    <cover-image mode='widthFix' v-if='is_follow==1'  :src='"/yidongduan/vr_video/collect_ac.png" | imageFilter("m_80")'></cover-image>
                  </cover-view>
                  <cover-view class="right_con" :class="{red:is_follow==1}" >
                     点赞
                  </cover-view>
                </cover-view>

                <cover-view class="right_oper" @click ="shares">
                  <cover-view class="right_img">
                    <cover-image mode='widthFix'  :src='"/yidongduan/vr_video/share.png" | imageFilter("m_80")'></cover-image>
                  </cover-view>
                  <cover-view class="right_con">
                    转发
                  </cover-view>
                </cover-view>
                <cover-view class="right_oper" @click="ask">
                  <cover-view class="right_img">
                    <cover-image mode='widthFix'  :src='"/yidongduan/vr_video/ask.png" | imageFilter("m_80")'></cover-image>
                  </cover-view>
                  <cover-view class="right_con">
                    咨询
                  </cover-view>
                </cover-view>
                <cover-view class="right_oper" @click ="tel">
                  <cover-view class="right_img">
                    <cover-image mode='widthFix'  :src='"/yidongduan/vr_video/tel.png" | imageFilter("m_80")'></cover-image>
                  </cover-view>
                  <cover-view class="right_con">
                    电话
                  </cover-view>
                </cover-view>
                <!-- <cover-view class="right_oper" @click='showPinglun'>
                  <cover-view class="right_img">
                    <cover-image mode='widthFix'  :src='"/yidongduan/vr_video/pinglun.png" | imageFilter("m_80")'></cover-image>
                  </cover-view>
                  <cover-view class="right_con">
                    评论
                  </cover-view>
                </cover-view> -->
            </cover-view>
            <!-- <template v-if ='video.info'> -->
              <cover-view class="fix_bottom">
                <cover-view class="title">@{{ detail.build_title}}</cover-view>
                <cover-view class="info_labels flex-row items-center" v-if='detail.label&&detail.label.length'>
                  <cover-view class ="info_label" v-for ="(item,index) in detail.label " :key ="index">{{item}}</cover-view>
                </cover-view>
                <cover-view class="info_c">
                  {{detail.content}}
                </cover-view>
                <cover-view class="info_btn" @click ="toDetail">
                 查看详情
                </cover-view>

              </cover-view>

          </view>
        </swiper-item>
      </swiper>
    </view>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
  </view>
</template>

<script>

// #ifdef H5

import shareTip from '@/components/shareTip'
import wx from "weixin-js-sdk"
// #endif
import allTel from '@/common/all_tel.js'
import getChatInfo from '@/common/get_chat_info'
import checkLogin from '@/common/utils/check_login'
export default {
  components:{
     shareTip
  },
  data(){
    return  {
      title:'',
      height:"100vh",
      videos:[
        
      ],
      type:1,
      params:{
        page:1,
        info_id:'',
        video_id:""

      },
      detail:{},
      tel_res:{},
      current:0,
      show_pinglun:false ,
      placeholder:"回复：",
      show_tel_pop:false,
      memberObj:{},
      is_follow:0,
      show_share_tip:false
    }
  },
  computed:{
      is_open_im() {
        return this.$store.state.im.ischat
      }
  },
  onLoad(options){
    if (options.id) {
      this.params.info_id =  options.id
    }
    if(options.type){
      this.type=options.type
    }
    if(options.video_id){
      this.params.video_id=options.video_id
    }
    // this.params.info_id=68374
    // this.type=1
    this.getList()
    // this.onChange({target:{current:0}})
    console.log(uni.getSystemInfoSync());
  },
  methods:{
    setTitle(){
      // setTimeout(() => {
      //     this.title='设置的title'
      // }, 300);
    },
    firstPlayingAudio(){
      if (this.isClicked) return
      this.isClicked =true 
       this.videoContext&& this.videoContext.play()
    },
    guanzhu(){
      let url ='build/followBuild.html'
      if(this.is_follow){
        url ='build/cancelFollowBuild.html'
      }
      this.$ajax.get(url,{bid:this.detail.id},res=>{
        if (res.data.code ==1){
          this.is_follow = this.is_follow==0?1:0
        //  this.$set(this.detail,"is_follow",this.detail.is_follow==0?1:0)
        }
          uni.showToast({
            title:res.data?res.data.msg :"",
            icon:'none'
          })
        
      })
    },
    collect(){
      let url ='building_circle/praise'
      this.$ajax.post(url,{id:this.videos[this.current].id,value:this.is_follow?0:1},res=>{
          this.is_follow = res.data.value
          uni.showToast({
            title:res.data?res.data.msg :"",
            icon:'none'
          })
        
      })
    },
    infoCollect(){
      let url ='house/infoCollect.html'
      if(this.is_follow){
        url ='house/cancelCollect.html'
      }
      this.$ajax.get(url,{id:this.detail.id},res=>{
        if (res.data.code ==1){
          this.is_follow = this.is_follow==0?1:0
        //  this.$set(this.detail,"is_follow",this.detail.is_follow==0?1:0)
        }
          uni.showToast({
            title:res.data?res.data.msg :"",
            icon:'none'
          })
        
      })
    },
    ask(){
      if (this.is_open_im == 1) {
        //开聊天
        getChatInfo(this.detail.uid, 29)
      } else if (this.is_open_im == 0) {
        //不开聊天
        this.consuDetail(this.detail.adviser_id)
      }
      // console.log(2342);
      // var user_id = this.memberObj.mid||this.memberObj.uid||this.memberObj.id
      // var identity_id = this.memberObj.adviser_id||this.memberObj.uid||this.memberObj.id
      // this.advAsk({user_id:user_id,identity_id:identity_id})
    },
    advAsk(e) {
      if (this.is_open_im == 1) {
        //开聊天
        // #ifdef MP-WEIXIN
        if (this.type==1){
            this.$store.state.buildInfo = {
              id: this.detail.id,
              title: this.detail.title,
              type: 'build',
              image: this.detail.img
            }
          getChatInfo(e.user_id, 3, this.id)
        }
        if (this.type==2 ){
          // let thumb
          // this.imgs = this.detail.imgs
          // if(this.imgs.length>0){
          //   thumb = this.imgs[0]
          // }
          let text = `${this.detail.fangjia?this.detail.fangjia+'万':'面议'}`
          let type='ershou'
          if (this.detail.parentid==2){
            text =`${this.detail.zujin?this.detail.zujin+'元/月':'面议'}`
            type='renting'
          }
         
          this.$store.state.buildInfo = {
            id: this.detail.id,
            title: this.detail.title,
            type: type,
            image: this.detail.img.length>0?this.detail.img[0].url:"",
            desc: `${this.detail.shi}室${this.detail.ting}厅${this.detail.wei}卫/${this.detail.mianji}m²/${this.detail.chaoxiang||''}`,
            price:``
          }
          getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id, this.detail.parentid==1? 6:7)
        }
        if (this.type==3){
          
          this.$store.state.buildInfo = {
            id: this.detail.id,
            title: this.detail.title,
            type: 'commercial',
            catid: this.detail.catid,
            image: this.detail.img,
            desc: `${this.detail.mianji}${this.detail.mianji_unit}`,
            price:`${this.detail.price?this.detail.price+this.detail.price_unit:'面议'}`
          }
          getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id, this.detail.parentid==1?32:(this.detail.parentid==2?33:34))
        }
        // this.$store.state.buildInfo = {
        //   id: this.id,
        //   title: this.detail.title,
        //   type: 'build',
        //   image: this.img[0]
        // }
       
        // #endif
        // #ifndef MP-WEIXIN
        checkLogin({
          success: (res)=>{
            if(this.type ==1 ){
              this.$store.state.buildInfo = {
                id: this.detail.id,
                title: this.detail.title,
                type: 'build',
                image: this.detail.img
              }
              getChatInfo(e.user_id, 3,this.detail.id)
            }
            if (this.type==2 ){
              // let thumb
              // this.imgs = this.detail.imgs
              // if(this.imgs.length>0){
              //   thumb = this.imgs[0]
              // }
              let text = `${this.detail.fangjia?this.detail.fangjia+'万':'面议'}`
              let type='ershou'
              if (this.detail.parentid==2){
                text =`${this.detail.zujin?this.detail.zujin+'元/月':'面议'}`
                type='renting'
              }
            
              this.$store.state.buildInfo = {
                id: this.detail.id,
                title: this.detail.title,
                type: type,
                image: this.detail.img.length>0?this.detail.img[0].url:"",
                desc: `${this.detail.shi}室${this.detail.ting}厅${this.detail.wei}卫/${this.detail.mianji}m²/${this.detail.chaoxiang||''}`,
                price:``
              }
              getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id,this.detail.parentid==1? 6:7)
            }
            if (this.type==3 ){
               this.$store.state.buildInfo = {
                id: this.detail.id,
                title: this.detail.title,
                type: 'commercial',
                catid: this.detail.catid,
                image: this.detail.img,
                desc: `${this.detail.mianji}${this.detail.mianji_unit}`,
                price:`${this.detail.price?this.detail.price+this.detail.price_unit:'面议'}`
              }
              getChatInfo((this.memberObj&&this.memberObj.agent_id)?this.memberObj.agent_id:this.memberObj.id, this.detail.parentid==1?32:(this.detail.parentid==2?33:34))
            }
           
          },
          fail: (res)=>{
            // TODO
            uni.setStorageSync('backUrl', window.location.href)
            this.$navigateTo("/user/login/login")
          },
          complete:(res)=>{
            this.$store.state.user_login_status = res.status
          }
        })
        // #endif
      } else if (this.is_open_im == 0) {
        //不开聊天
        if (this.type ==1 ){
          this.consuDetail(e.identity_id)
        }else {
          //  this.toAgentDetail(e.identity_id)
        }
        
      }
    },
    // toAgentDetail(id){
    //   this.$navigateTo('/pages/agent/detail?id=' + id)
    // },
    //转到顾问详情
    consuDetail(id) {
      if (!id) return
      if (this.is_open_adviser == 1 && this.detail.open_adviser == 1) {
          this.$navigateTo('/pages/consultant/detail?id=' + id)
      }else{
        console.log("没开启聊天且不是置业顾问,不跳转详情")
      }
    },
    tel(){
     this.tel_params = {
        type: 2,
        callee_id: this.detail.adviser_id,
        scene_type: 2,
        scene_id: this.detail.adviser_id,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      allTel(this.tel_params)

    },
    retrieveTel(){
      allTel(this.tel_params)
    },

    // #ifdef H5
    shares(){

      this.share.link="https://"+window.location.host+"/h5/vr/pre_comm_video?id="+this.videos[this.current].id +"&video_id="+this.videos[this.current].attached[0].id
      this.getWxConfig()
      this.show_share_tip =true
      // this.copyWechatNum(link, ()=>{
      //   uni.showToast({
      //     title: '复制成功,去发送给好友吧',
      //     icon: 'none'
      //   })
      // })
    },
    // #endif
    // #ifndef H5
    copyWechatNum(cont) {
      uni.setClipboardData({
        data: cont,
        success: res => {
          // uni.showToast({
          //   title: "复制成功",
          //   icon: "none"
          // })
        }
      })
    },
    // #endif
    // #ifdef H5
    copyWechatNum(cont, callback) {
      let oInput = document.createElement('textarea')
      oInput.value = cont
      document.body.appendChild(oInput)
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      oInput.blur()
      oInput.remove()
      if(callback) callback()
    },
    // #endif
    onChange(e,c){
      let id=  'video_play'+ this.videos[e.target.current].attached[0].id
      this.current = e.target.current
      this.detail = this.videos[this.current]
      this.getDetail()
      // this.$refs['video_play'+e.target.current][0].$el.removeChild('video')
       this.videoContext = uni.createVideoContext(id,this);
        this.videos.map((item)=>{
          let temp = "video_play" + item.attached[0].id;
          if(temp !=id){
             uni.createVideoContext(temp, this).pause();
          }
        })
        try {
          this.videoContext.play()
        } catch (error) {
          // this.firstPlayingAudio()
        }
        
        uni.setNavigationBarTitle({
          title:this.detail.build_title||"楼市圈视频"
        })
        // this.videoContext.controls =false
        // this.videoContext.setAttribute('webkit-playsinline', true) //这个bai属性是ios 10中设置可以让视频在小du窗内播放，也就是不是全屏播放的video标签的一个属性
        // this.videoContext.setAttribute('x5-video-player-type', 'h5') //
        // wx.ready(() => {   //需在用户可能点击分享按钮前就先调用
            
        // })
        
        
      setTimeout(() => {
          if(this.current== this.videos.length-1 && this.videos.length>1 ) {
            if (this.isloading ) return 
            this.isloading =true
            this.params.next_info_id =  this.videos[this.current].id
            this.params.prev_info_id  =''
            this.getList()
          }
          if(this.current== 0 &&this.videos.length>1 &&  this.isGetted && !this.isPreNomore) {
            if (this.isloading ) return 
            this.isloading =true
            this.params.prev_info_id = this.videos[0].id
            this.params.next_info_id =''
            this.getList()
          }
      }, 1000);

        
    },
    getDetail(){
      this.$ajax.get("video/buildingCircleVideoDetail",{id:this.videos[this.current].id},res=>{
        if(res.data.share){
          this.share =res.data.share
        }
        this.share.link="https://"+window.location.host+"/h5/vr/pre_comm_video?id="+this.videos[this.current].id +"&video_id="+this.videos[this.current].attached[0].id
        this.getWxConfig()
          if(res.data.code ==1 ){
            this.is_follow =res.data.isPraise
          }
      })
    },
    getList(){
      if (!this.isGetted){
        this.videos =[]
      }
      let url ='video/buildingCircleVideoList'
      this.$ajax.get(url,this.params,(res=>{
        if (res.data.code ==1){
          if (res.data.share ){
            this.share=res.data.share
          }else {
            this.share = {}
          }
          if(res.data.list.length){
            res.data.list.map(item=>{
              if (item.attached&&item.attached.length){
                item.attached = item.attached.filter(i=>i.type==3)
              }
              item.path =item.attached[0].path
              return item
            })
          }
          
         
        
         if(this.params.prev_info_id){
           if ( res.data.list.length) {
            //  let videos =[]
              // res.data.list.map(item=>{
              //   videos.unshift(item)
              // })
              this.videos = [...res.data.list,...this.videos]
            }else {
              this.isPreNomore =true
            }
          this.current =  res.data.list.length
         }else {
           this.videos = this.videos.concat(res.data.list) 
         }
         if (!this.params.prev_info_id && !this.params.next_info_id){
            // this.detail = this.videos[this.current]
            this.$nextTick(()=>{
              setTimeout(() => {
                this.onChange({target:{current:0}})
              }, 300);
              
            })
          }
          
          this.isGetted =true
        this.isloading =false
          // if(res.data.list.length==0) {
          //   this.loadmore =false
          // }else {
          //   this.loadmore =true
          // }
          
        }else {
          uni.showToast({
            title:res.data.msg,
            icon:"none"
          })
        }
      })
      )
    },
    toDetail(){
      if(!this.detail.build_id) return 
      this.$navigateTo("/pages/new_house/detail?id="+this.detail.build_id)
    },
    waitingVideo(){
    },
    playVideo(index){
//       this.videoContext  =null
//       this.videoContext = uni.createVideoContext('video_play'+index);
    },

    pauseVideo(){

    },
    showPinglun(){
      // this.videoContext.requestFullScreen()
      this.show_pinglun =true
    },
  }
}
</script>

<style lang="scss" scoped>
// #ifdef h5
 @import './static/css/video-js.css';
//  #endif
.page {
  background: #000;
}
.flex-row{
  display: flex;
  flex-direction: row;
}
.video_item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  flex-direction: column;
  position: relative;
.video_play{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;                 
  }
  // &.video_play_scale{
  //   align-items: flex-start;
  //   justify-content: flex-start;
  //   .video_play{
  //     height: 300rpx;
  //   }
  // }
}
video{
  z-index: 1;
}
.fix_right{
  position: absolute;
  top: 50vh;
  right: 48rpx;
  transform: translateY(-50%);
  z-index: 1000;
  .prelogo {
    width: 80rpx;
    height: 80rpx;
    margin-bottom: 60rpx;
    overflow: hidden;
    border-radius: 50%;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .right_oper {
    margin-bottom: 48rpx;
    text-align: center;
    .right_img {
      width: 48rpx;
      height: 48rpx;
      margin: 0 auto;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
    .right_con{
      margin-top: 10rpx;
      font-size: 24rpx;
      color: #F8F8F8;
      &.red{
        color: red;
      }
    }
  }
}
.fix_bottom {
  position: absolute;
  bottom: 0;
  height: 400rpx;
  left: 0;
  right: 0;
  z-index:10;
  padding: 20rpx 48rpx;
  .title {
    color: #FFFFFF;
    font-size:36rpx;
    font-weight: 600;
    padding: 18rpx 0;
  }
  .info_labels {
    color: #FFD952;
    font-size: 24rpx;
    margin: 20rpx 0;
    .info_label {
      margin-right: 5rpx;
      // border: 2rpx solid #FFD952;
      padding: 4rpx 6rpx;
      border-radius: 4rpx;

    }
  }
  .info_c{
    color: #fff;
    font-size: 24rpx;
    margin-top: 16rpx;
    overflow:hidden;
    width: 100%;
    text-overflow:ellipsis;
    display:-webkit-box;
    -webkit-line-clamp:3;    // 表示需要显示的行数
    -webkit-box-orient:vertical; 
  }
  .info_btn {
    display: inline-block;
    margin-top: 20rpx;
    padding: 5rpx 10rpx;
    font-size: 22rpx;
    color: #fff;
    background: #FFFFFF19;
  }
}
.pinglun{
  border-radius: 20rpx 20rpx 0px 0px;
  background: #FFFFFF;
  width: 100%;
  // position: absolute;
  // bottom:0;
  // left: 0;
  // right: 0;
  position: relative;
  height: 0;
  overflow-y: auto;
  transition: 0.3s;
  &.active{
    height: 600rpx;
    transition: 0.3s;
  }
  .title{
    position: relative;
    padding: 20rpx 0;
    justify-content: center;
    .title_c {
      color: #222222;
      font-size: 24rpx;
      text-align: center;
    }
    .close{
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 40rpx;
      height: 40rpx;
      image {
        width: 100%;
      }

    }
  }
  .pinglun_list {
    padding: 28rpx 40rpx 80rpx;
    position: relative;
    .pinglun_item {
      margin-bottom: 40rpx;
      .pinglun_prelogo {
        width: 72rpx;
        height: 72rpx;
        min-width: 72rpx;
        margin-right: 24rpx;
        overflow: hidden;
        border-radius: 50%;
        image{
          width: 100%;
        }
      }
      .pinglun_con {
        .cname {
          color: #ABA6A6;
          font-size: 26rpx;
        }
        .p_content {
          color: #222222;
          font-size: 28rpx;
        }
        .p_oper {
          margin-top: 20rpx;

          .ctime {
            color: #ABA6A6;
            margin-right: 40rpx;
            font-size: 26rpx;
          }
          .reply{
            color: #737373;
            font-size: 26rpx;
          }
        }
      }
    }
  }
  .inp {
      position: absolute;
      background: #F3F3F5;
      height: 72rpx;
      line-height: 72rpx;
      bottom: 20rpx;
      left: 48rpx;
      right: 48rpx;
      border-radius: 48rpx;
    }
    // .video_play{
    //   &.video_play_scale{

    //   }

    // }


}
</style>