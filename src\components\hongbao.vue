<template>
    <my-popup ref="hongbao_popup" position="center" top_0>
      <view class="hongbao-pop" @click="openHb">
        <image v-if="type == 1" :src="hongbaoPop_img | imgUrl" mode="widthFix"></image>
        <image v-else :src="zhuanfa_img | imgUrl" mode="widthFix" class="zhuanfa-img"></image>
        <view  class="close-icon" :class="{'zhuanfa-close': type == 2}" @click.stop="hidenPopup()">
          <my-icon type="guanbi" color="#fff" size="56rpx"></my-icon>
        </view>
        <template v-if="type == 1">
          <view class="hongbao-title">助力红包</view>
          <view class="hongbao-money">
            <text class="num">{{money}}</text>
            <text class="unit">¥</text>
          </view>
          <view class="count-down flex-row" v-if="expire_seconds">
            <text v-if="expire_seconds > 0">{{expire_seconds | secondsFormat}}</text>
            <!-- <text v-if="time_down.day != '00'">{{time_down.day}}天</text>
            <text v-if="time_down.hour != '00'">{{time_down.hour}}:</text>
            <text v-if="time_down.minute != '00'">{{time_down.minute}}:</text>
            <text v-if="time_down.second != '00'">{{time_down.second}}</text> -->
            <!-- <text>{{time_down.day}}天{{time_down.hour}}:{{time_down.minute}}:{{time_down.second}}</text> -->
            <text v-if="expire_seconds > 0">后失效</text>
            <text v-else>已失效</text>
          </view>
        </template>
        <template v-else>
          <view class="zhuanfa-title">转发海报</view>
          <view class="zhuanfa-bottom">
            <view class="zhuanfa-text">邀请5个好友关注</view>
            <view class="zhuanfa-text">可以再领1次现金红包</view>
            <view class="zhuanfa-btn" @click="zhuanfa()">转发海报</view>
          </view>
        </template>
      </view>
    </my-popup>
</template>

<script>
import myPopup from './myPopup.vue'
import {formatImg, config} from '../common/index.js'
import myIcon from './myIcon.vue'
export default {
  props: {
    // hongbaoData: Object,
    money: Number | String,
    expire_seconds: Number,
    palyMusic:{
      type:Boolean,
      default:true
    }
  },
  components: {
    myPopup,
    myIcon
  },
  data() {
    return {
      hongbaoPop_img: config.imgDomain+'/hongbao/lingqu.png',
      zhuanfa_img: config.imgDomain+'/hongbao/relay.png',
      type: 1,
      new_seconds: '',
      playVideo:false,
      tuiguang_mp3:"/static/icon/voice/tuiguang_bg.mp3"
    }
  },
  filters:{
    imgUrl(val){
      return formatImg(val,'m_6401')
    },
    secondsFormat(val){
      let day = Math.floor(val / 86400)
      let hour = Math.floor((val % 86400) / 3600)
      let minute = Math.floor(((val % 86400) % 3600) / 60)
      let second = ((val % 86400) % 3600) % 60
      let str = ''
      if (day > 0) {
        str = str + (day < 10 ? '0' + day : day) + '天'
      }
      if (hour > 0 || day > 0) {
        str = str + (hour < 10 ? '0' + hour : hour) + ':'
      }
      if (minute > 0 || hour > 0 || day > 0) {
        str = str + (minute < 10 ? '0' + minute : minute) + ':'
      }
      if (second > 0 || minute > 0 || hour > 0 || day > 0) {
        str = str + (second < 10 ? '0' + second : second)
      }
      return str
    }
  },
  beforeDestroy() {
    this.innerAudioContext =null
  },
  methods: {
    showPopup() {
      this.$refs.hongbao_popup.show()
    },
    hidenPopup() {
      this.$refs.hongbao_popup.hide()
    },
    openHb() {
      // if(this.palyMusic) this.playAudio()
      this.$emit('openHb')
    },
    zhuanfa() {
      this.$emit('zhuanfa')
    },
    playAudio(){
        this.innerAudioContext = uni.createInnerAudioContext();
        // this.innerAudioContext.autoplay = true;
        this.innerAudioContext.loop = false;
        this.innerAudioContext.src = this.tuiguang_mp3;
        // this.innerAudioContext.pause()
        this.innerAudioContext.onPlay(() => {
            console.log('开始播放');
            this.playVideo = true
        });
        this.innerAudioContext.onEnded(() => {
            console.log('播放结束');
            this.playVideo = false
        });
        this.innerAudioContext.onError((res) => {
            this.playVideo = false
            console.log("播放失败")
            console.log(res.errMsg);
            console.log(res.errCode);
        });
        this.innerAudioContext.play()
    },
    
  }
}
</script>

<style lang="scss" scoped>
.hongbao-pop {
  position: fixed;
  top: 50%;
  left: 0;
  width: 100%;
  transform: translateY(-50%);
  z-index: 99;
  image {
    width: 100%;
  }
  .zhuanfa-img {
    margin: 0 88rpx;
    width: 574rpx;
  }
  .close-icon {
    position: absolute;
    right: 130rpx;
    top: 74rpx;
    &.zhuanfa-close {
      top: -12rpx;
    }
  }
  .hongbao-title{
    position: absolute;
    color: #f8311a;
    top: 160rpx;
    left: 50%;
    font-size: 36rpx;
    transform: translateX(-50%);
  }
  .hongbao-money {
    position: absolute;
    top: 200rpx;
    left: 50%;
    transform: translateX(-50%);
    .num {
      color: #FF5B5B;
      font-size: 96rpx;
    }
    .unit {
      position: absolute;
      bottom: 24rpx;
      left: -46rpx;
      color: #FF5B5B;
      font-size: 36rpx;
    }
  }
  .count-down {
    position: absolute;
    bottom: 98rpx;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    font-size: 24rpx;
  }
  .zhuanfa-title {
    position: absolute;
    top: 200rpx;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    font-size: 52rpx;
  }
  .zhuanfa-bottom {
    position: absolute;
    top: 324rpx;
    left: 50%;
    transform: translateX(-50%);
    .zhuanfa-text {
      color: #fff;
      text-align: center;
    }
    .zhuanfa-btn {
      width: 360rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: linear-gradient(180deg, #f6fff9 0%, #ffd4d7 100%);
      box-shadow: 0 4rpx 18rpx #d22513;
      border-radius: 40rpx;
      margin-top: 20rpx;
      color: #ce5a4f;
      font-size: 30rpx;
    }
  }
}
</style>