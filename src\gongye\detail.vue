<template>
  <view class="park-detail-page">
    <!-- 轮播图区域 -->
    <view class="swiper-container">
      <swiper
        class="banner"
        :indicator-dots="true"
        :circular="true"
        :duration="300"
        indicator-active-color="#f65354"
        @change="swiperChange"
        :current="swiperCurrent"
      >
        <swiper-item v-for="(item, index) in images" :key="index">
          <view class="swiper-item" @click="previewImages(index)">
            <image :src="item.path | imageFilter" mode="aspectFill"></image>
          </view>
        </swiper-item>
      </swiper>
      
      <!-- 图片数量显示 -->
      <view class="img-total">共{{ images.length }}张</view>
      
      <!-- 图册和全景按钮 -->
      <view class="cate-box">
        <view class="cate-list flex-row">
          <view class="cate active" @click="previewImages(0)">
            图册
          </view>
          <view class="cate" @click="openVR" v-if="vrs && vrs.length > 0">
            全景
            <text class="arrow-icon">》</text>
          </view>
        </view>
      </view>
      
      <!-- 返回按钮 -->
      <view class="card-btn fixed card-btn-bg flex-box" style="left:20rpx" @click="$navigateBack()">
        <my-icon type="ic_back" color="#fff" size="44rpx"></my-icon>
      </view>

    </view>
    
    <!-- 园区基本信息 -->
    <view class="park-info container">
      <view class="park-content-wrapper">
        <view class="park-header">
          <view class="park-title-row">
            <text class="park-title">{{ detail.title }}</text>
            <view class="status-tag">{{ detail.areaname }}</view>
          </view>
          <view class="park-address">
            <text>{{ detail.address }}</text>
            <view v-if="detail.lat && detail.lng" class="nav-action" @click="openLocation">
              <image class="icon" :src="'/build/v_3/location.png' | imageFilter('m_320')" style="width: 36rpx; height: 36rpx;"></image>
              <text>导航</text>
            </view>
          </view>
        </view>
        
        <!-- 载体资源标签 -->
        <view class="tags-section" v-if="detail.carrier">
          <view class="tags-title">载体资源</view>
          <view class="tags-container">
            <view class="tag carrier-tag" v-for="(tag, idx) in carrierTags" :key="idx">
              {{ tag }}
            </view>
          </view>
        </view>
        
        <!-- 产业集群文字 -->
        <view class="info-section" v-if="detail.cluster">
          <view class="info-title">产业集群</view>
          <view class="info-content">
            <text>{{ detail.cluster }}</text>
          </view>
        </view>
        
        <!-- 运营公司 -->
        <view class="info-section" v-if="detail.company">
          <view class="info-title">运营公司</view>
          <view class="info-content">
            <text>{{ detail.company }}</text>
          </view>
        </view>
        
        <!-- 招商热线 -->
        <view class="info-section" v-if="detail.mobile">
          <view class="info-title">招商热线</view>
          <view class="info-content">
            <text class="phone-number">{{ detail.mobile }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 园区描述 -->
    <view class="park-desc container" v-if="htmlContent">
      <view class="desc-header">
        <view class="desc-title">园区简介</view>
      </view>
      <view class="desc-content">
        <rich-text class="html-content" :nodes="htmlContent"></rich-text>
      </view>
    </view>
    
    <!-- TAB切换区域 -->
    <view class="tab-section" v-if="hasVisibleTabs">
      <view class="tab-container">
        <view 
          class="tab-item" 
          :class="{ active: currentTab === index }" 
          v-for="(tab, index) in tabs" 
          :key="index"
          v-if="shouldShowTab(index)"
          @click="switchTab(index)"
        >
          {{ tab.name }}
        </view>
      </view>
      
      <!-- TAB内容区域 -->
      <view class="tab-content">
        <!-- 园区动态 -->
        <view v-if="currentTab === 0 && shouldShowTab(0)" class="news-list">
          <view 
            class="news-item" 
            :class="{ 'has-image': item.img && item.img.trim() }"
            v-for="item in newsList" 
            :key="item.id"
            @click="toNewsDetail(item.id)"
          >
            <view class="news-content">
              <view class="news-title">{{ item.title }}</view>
              <view class="news-summary" v-if="!item.img || !item.img.trim()">{{ item.summary || item.ctime }}</view>
              <view class="news-meta">
                <text class="news-time">{{ item.create_time || item.ctime }}</text>
              </view>
            </view>
            <image v-if="item.img && item.img.trim()" class="news-image" :src="item.img | imageFilter" mode="aspectFill"></image>
          </view>
          <view v-if="newsList.length === 0" class="empty-state">
            <text>暂无内容</text>
          </view>
        </view>
        
        <!-- 载体资源 -->
        <view v-if="currentTab === 1 && shouldShowTab(1)" class="estates-list">
          <estate-item 
            v-for="item in estatesList" 
            :key="item.id"
            :item-data="item"
            @click="toEstateDetail"
          ></estate-item>
          <view v-if="estatesList.length === 0" class="empty-state">
            <text>暂无内容</text>
          </view>
        </view>
        
        <!-- 招商政策 -->
        <view v-if="currentTab === 2 && shouldShowTab(2)" class="policies-list">
          <view 
            class="policy-item" 
            v-for="item in policiesList" 
            :key="item.id"
          >
            <view class="policy-header">
              <text class="policy-title">{{ item.title }}</text>
            </view>
            <view class="policy-content">
              <text>{{ item.descp }}</text>
            </view>
          </view>
          <view v-if="policiesList.length === 0" class="empty-state">
            <text>暂无内容</text>
          </view>
        </view>
        
      </view>
    </view>
    
    <!-- 底部固定操作区 -->
    <view class="bottom-bar flex-row">
      <view class="bar-left flex-row flex-1">
        <view v-if="detail.lat && detail.lng" class="icon-btn btn-outline" @click="openLocation">
          <text>立即前往</text>
        </view>
        <view class="icon-btn btn-booking" @click="showBooking">
          <text>在线预约</text>
        </view>
      </view>
      <view class="bar-btn btn-call flex-1" @click="makeCall">
        <text>联系电话</text>
      </view>
    </view>
    
    <!-- 详情弹窗 -->
    <view class="popup-mask" v-if="showDetailPopup" @click="hideDetailPopup" :class="{ 'popup-closing': detailPopupClosing }">
      <view class="detail-popup" @click.stop :class="{ 'popup-closing': detailPopupClosing }">
        <view class="popup-header">
          <text class="popup-title">园区概况</text>
          <view class="close-btn" @click="hideDetailPopup">
            <my-icon type="ic_close" color="#666" size="32rpx"></my-icon>
          </view>
        </view>
        <scroll-view class="popup-content" scroll-y>
          <rich-text class="html-content" :nodes="htmlContent"></rich-text>
        </scroll-view>
      </view>
    </view>
    
    <!-- 预约弹窗 -->
    <view class="popup-mask booking-mask" v-if="showBookingPopup" @click="hideBookingPopup" :class="{ 'booking-closing': bookingPopupClosing }">
      <view class="booking-popup" @click.stop :class="{ 'booking-closing': bookingPopupClosing }">
        <view class="booking-header">
          <text class="booking-title">在线预约</text>
        </view>
        <view class="booking-form">
          <view class="form-item">
            <input class="form-input" placeholder="请输入姓名" v-model="bookingForm.name" />
          </view>
          <view class="form-item">
            <input class="form-input" placeholder="请输入手机号" v-model="bookingForm.phone" />
          </view>
          <view class="form-actions">
            <view class="form-btn confirm-btn" @click="submitBooking">确认提交</view>
            <view class="form-btn cancel-btn" @click="hideBookingPopup">取消</view>
          </view>
        </view>
      </view>
    </view>
    
  </view>
</template>

<script>
import myIcon from '@/components/myIcon.vue'
import estateItem from './components/estateItem.vue'

export default {
  name: 'ParkDetail',
  components: {
    myIcon,
    estateItem
  },
  data() {
    return {
      id: '',
      detail: {},
      images: [],
      vrs: [],
      swiperCurrent: 0,
      descExpanded: false,
      currentTab: 0,
      tabs: [
        { name: '园区动态', key: 'news' },
        { name: '载体资源', key: 'estates' },
        { name: '招商政策', key: 'policies' },
/*         { name: '园区概况', key: 'overview' } */
      ],
      newsList: [],
      estatesList: [],
      policiesList: [],
      htmlContent: '',
      showDetailPopup: false,
      showBookingPopup: false,
      detailPopupClosing: false,
      bookingPopupClosing: false,
      bookingForm: {
        name: '',
        phone: ''
      },
      share: {
        title: '',
        content: '',
        pic: '',
        link: '',
        link2: ''
      }
    }
  },
  computed: {
    carrierTags() {
      return this.detail.carrier ? this.detail.carrier.split('、').filter(tag => tag.trim()) : [];
    },
    hasVisibleTabs() {
      return this.shouldShowTab(0) || this.shouldShowTab(1) || this.shouldShowTab(2);
    }
  },
  onLoad(options) {
    this.id = options.id;
    this.getDetail();
  },
  methods: {
    // 获取园区详情
    getDetail() {
      this.$ajax.get('park/detail', { id: this.id }, (res) => {
        if (res.data.code == 1) {
          this.detail = res.data.detail;
          this.images = res.data.images || [];
          this.vrs = res.data.vrs || [];
          
          // 处理分享数据
          if (res.data.forward) {
            this.share = {
              title: res.data.forward.title || this.detail.title || '',
              content: res.data.forward.desc || this.detail.address || '',
              pic: res.data.forward.pic || (this.images.length > 0 ? this.images[0].path : ''),
              link: res.data.forward.link || '',
              link2: res.data.forward.link2 || ''
            };
          } else {
            // 如果没有forward数据，使用默认数据
            this.share = {
              title: this.detail.title || '',
              content: this.detail.address || '',
              pic: this.images.length > 0 ? this.images[0].path : '',
              link: '',
              link2: ''
            };
          }
          // #ifdef H5
          this.initWxShare();
          // #endif
          
          // 默认加载园区动态
          this.getNewsList();
          // 获取富文本内容
          this.getDetailContent();
        }
      });
    },
    
    // 获取园区动态
    getNewsList() {
      this.$ajax.get('park/news', { id: this.id }, (res) => {
        if (res.data.code == 1) {
          this.newsList = res.data.list || [];
          this.adjustCurrentTab();
        }
      });
    },
    
    // 获取载体资源
    getEstatesList() {
      this.$ajax.get('park/esates', { id: this.id }, (res) => {
        if (res.data.code == 1) {
          this.estatesList = res.data.list || [];
          this.adjustCurrentTab();
        }
      });
    },
    
    // 获取招商政策
    getPoliciesList() {
      this.$ajax.get('park/policies', { id: this.id }, (res) => {
        if (res.data.code == 1) {
          this.policiesList = res.data.list || [];
          this.adjustCurrentTab();
        }
      });
    },
    
    // 获取详细内容
    getDetailContent() {
      this.$ajax.get('park/content', { id: this.id }, (res) => {
        if (res.data.code == 1) {
          let content = res.data.content || '';
          // 处理富文本中的图片，添加样式限制
          if (content) {
            // 处理img标签
            content = content.replace(/<img([^>]*?)>/gi, (match, attrs) => {
              // 移除可能存在的width和height属性
              attrs = attrs.replace(/\s*(width|height)\s*=\s*["'][^"']*["']/gi, '');
              // 添加样式属性
              const style = 'style="max-width:100%;width:100%;height:auto;display:block;margin:10px 0;border-radius:4px;"';
              return `<img${attrs} ${style}>`;
            });
            
            // 处理image标签（如果存在）
            content = content.replace(/<image([^>]*?)>/gi, (match, attrs) => {
              // 移除可能存在的width和height属性
              attrs = attrs.replace(/\s*(width|height)\s*=\s*["'][^"']*["']/gi, '');
              // 添加样式属性
              const style = 'style="max-width:100%;width:100%;height:auto;display:block;margin:10px 0;border-radius:4px;"';
              return `<image${attrs} ${style}>`;
            });
          }
          this.htmlContent = content;
        }
      });
    },
    
    // 轮播图切换
    swiperChange(e) {
      this.swiperCurrent = e.detail.current;
    },
    
    // 预览图片
    previewImages(index) {
      const imageUrls = this.images.map(item => item.path);
      uni.previewImage({
        urls: imageUrls,
        current: imageUrls[index]
      });
    },
    
    // 打开VR
    openVR() {
      if (this.vrs && this.vrs.length > 0) {
        const vrUrl = this.vrs[0].path;
        // #ifdef H5
        window.open(vrUrl);
        // #endif
        // #ifndef H5
        uni.navigateTo({
          url: '/pages/web_view/web_view?url=' + encodeURIComponent(vrUrl)
        });
        // #endif
      }
    },
    
    // 显示概况详情
    showOverviewDetail() {
      this.showDetailPopup = true;
    },
    
    // 隐藏详情弹窗
    hideDetailPopup() {
      this.detailPopupClosing = true;
      setTimeout(() => {
        this.showDetailPopup = false;
        this.detailPopupClosing = false;
      }, 300);
    },
    
    // 切换TAB
    switchTab(index) {
      if (!this.shouldShowTab(index)) {
        return;
      }
      this.currentTab = index;
      switch (index) {
        case 0:
          if (this.newsList.length === 0) {
            this.getNewsList();
          }
          break;
        case 1:
          if (this.estatesList.length === 0) {
            this.getEstatesList();
          }
          break;
        case 2:
          if (this.policiesList.length === 0) {
            this.getPoliciesList();
          }
          break;
      }
    },
    
    // 检查标签页是否应该显示
    shouldShowTab(index) {
      switch (index) {
        case 0:
          return this.newsList.length > 0;
        case 1:
          return this.estatesList.length > 0;
        case 2:
          return this.policiesList.length > 0;
        default:
          return false;
      }
    },
    
    // 调整当前标签页到第一个可见的标签页
    adjustCurrentTab() {
      if (!this.shouldShowTab(this.currentTab)) {
        for (let i = 0; i < this.tabs.length; i++) {
          if (this.shouldShowTab(i)) {
            this.currentTab = i;
            break;
          }
        }
      }
    },
    
    // 跳转到新闻详情
    toNewsDetail(id) {
      this.$navigateTo('/pages/news/detail?id=' + id);
    },
    
    // 打开地图导航
    openLocation() {
      if (this.detail.lat && this.detail.lng) {
        uni.openLocation({
          latitude: parseFloat(this.detail.lat),
          longitude: parseFloat(this.detail.lng),
          name: this.detail.title,
          address: this.detail.address
        });
      } else {
        uni.showToast({
          title: '暂无位置信息',
          icon: 'none'
        });
      }
    },
    
    // 显示预约弹窗
    showBooking() {
      this.showBookingPopup = true;
    },
    
    // 隐藏预约弹窗
    hideBookingPopup() {
      this.bookingPopupClosing = true;
      setTimeout(() => {
        this.showBookingPopup = false;
        this.bookingPopupClosing = false;
      }, 300);
    },
    
    // 提交预约
    submitBooking() {
      if (!this.bookingForm.name) {
        uni.showToast({
          title: '请输入姓名',
          icon: 'none'
        });
        return;
      }
      // 验证姓名：只能是汉字，最多20个字符
      const nameRegex = /^[\u4e00-\u9fa5]{1,20}$/;
      if (!nameRegex.test(this.bookingForm.name)) {
        uni.showToast({
          title: '请输入正确的姓名格式',
          icon: 'none'
        });
        return;
      }
      if (!this.bookingForm.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        });
        return;
      }
      // 验证手机号
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.bookingForm.phone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none'
        });
        return;
      }
      
      // 提交预约信息
      this.$ajax.post('park/signUp', {
        park_id: this.id,
        name: this.bookingForm.name,
        tel: this.bookingForm.phone
      }, (res) => {
        if (res.data.code == 1) {
          uni.showToast({
            title: '预约成功',
            icon: 'success'
          });
          this.hideBookingPopup();
          this.bookingForm = { name: '', phone: '' };
        } else {
          uni.showToast({
            title: res.data.msg || '预约失败',
            icon: 'none'
          });
        }
      });
    },
    
    // 拨打电话
    makeCall() {
      if (this.detail.mobile) {
        uni.makePhoneCall({
          phoneNumber: this.detail.mobile
        });
      } else {
        uni.showToast({
          title: '暂无联系电话',
          icon: 'none'
        });
      }
    },
    
    // 跳转到载体资源详情
    toEstateDetail(e) {
      const item = e.detail;
      let url = '';
      
      // 根据parentid确定跳转页面
      switch (item.parentid) {
        case 1:
          url = `/commercial/sale/detail?id=${item.id}`;
          break;
        case 2:
          url = `/commercial/rent/detail?id=${item.id}`;
          break;
        case 3:
          url = `/commercial/transfer/detail?id=${item.id}`;
          break;
        default:
          // 默认跳转到出售页面
          url = `/commercial/sale/detail?id=${item.id}`;
      }
      
      if (url) {
        this.$navigateTo(url);
      }
    },
    
    // #ifdef H5
    // 初始化微信分享配置
    initWxShare() {
      // 使用项目标准的微信分享方式
      this.getWxConfig();
    }
    // #endif
  },
  
  // 小程序分享给朋友
  onShareAppMessage() {
    return {
      title: this.share.title || this.detail.title || '产业园区',
      path: `/gongye/detail?id=${this.id}`,
      imageUrl: this.share.pic || (this.images.length > 0 ? this.images[0].path : '')
    }
  },
  
  // 小程序分享到朋友圈
  onShareTimeline() {
    return {
      title: this.share.title || this.detail.title || '产业园区',
      query: `id=${this.id}`,
      imageUrl: this.share.pic || (this.images.length > 0 ? this.images[0].path : '')
    }
  }
}
</script>

<style lang="scss" scoped>
.park-detail-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
  
  // 轮播图与信息区域的叠加效果
  .swiper-container + .park-info {
    margin-top: -60rpx; // 向上覆盖轮播图
    position: relative;
    z-index: 10;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1); // 添加阴影增强层次感
  }
}

.container {
  margin: 0;
  background: #fff;
  border-radius: 0;
  margin-bottom: 20rpx;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-1 {
  flex: 1;
}

.flex-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 轮播图区域
.swiper-container {
  position: relative;
  height: calc(75vw);
  // 直接给容器添加右下角大圆角，并通过调整位置实现效果
  border-radius: 0 0 120rpx 0; // 使用大圆角值
  overflow: hidden; // 裁剪内容
  // 由于底部被遮盖60rpx，大圆角会在可见区域显示出合适的圆角效果
  
  .banner {
    width: 100%;
    height: 100%;
    
    .swiper-item {
      width: 100%;
      height: 100%;
      
      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  
  .img-total {
    position: absolute;
    bottom: 110rpx; // 向上移动，避免被圆角遮挡
    left: 20rpx;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
  }
  
  .cate-box {
    position: absolute;
    bottom: 110rpx; // 向上移动，避免被圆角遮挡
    right: 60rpx;
    
    .cate-list {
      display: flex;
      gap: 16rpx;
      
      .cate {
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        padding: 12rpx 20rpx;
        border-radius: 24rpx;
        font-size: 26rpx;
        display: flex;
        align-items: center;
        gap: 8rpx;
        
        &.active {
          background: #fff;
          color: #333;
        }
        
        .arrow-icon {
          font-size: 20rpx;
          color: #fff;
        }
      }
    }
  }
  
  .card-btn {
    position: absolute;
    top: 40rpx; // 向下移动一点，避免被顶部圆角影响
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    
    &:first-of-type {
      left: 20rpx;
    }
    
    &:last-of-type {
      right: 20rpx;
    }
  }
  
  .card-btn-bg {
    background: rgba(0, 0, 0, 0.5);
  }
}

// 园区信息
.park-info {
  padding: 32rpx;
  border-radius: 64rpx 0 20rpx 20rpx; // 增大圆角半径与轮播图底部圆角呼应
  box-sizing: border-box; // 确保 padding 不会被内容撑开
  width: 100%; // 明确设置宽度
  
  .park-content-wrapper {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden; // 防止内容溢出，确保右侧padding生效
  }
  
  .park-header {
    margin-bottom: 32rpx;
    width: 100%; // 确保容器宽度
    box-sizing: border-box;
    
    .park-title-row {
      display: flex;
      justify-content: space-between;
      align-items: flex-start; // 改为 flex-start 避免对齐问题
      margin-bottom: 16rpx;
      width: 100%;
      box-sizing: border-box;
      
      .park-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
        flex: 1;
        min-width: 0; // 允许 flex 项目收缩
        margin-right: 16rpx; // 与右侧标签保持间距
        word-break: break-all; // 长文本自动换行
        word-wrap: break-word;
        line-height: 1.4;
      }
      
      .status-tag {
        background: #f5f5f5;
        color: #999;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        white-space: nowrap; // 标签不换行
        flex-shrink: 0; // 标签不收缩
      }
    }
    
    .park-address {
      display: flex;
      align-items: center;
      gap: 8rpx;
      color: #666;
      font-size: 28rpx;
      width: 100%;
      box-sizing: border-box;
      
      > text {
        flex: 1;
        min-width: 0; // 允许文本收缩
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 16rpx; // 与右侧导航按钮保持间距
      }
      
      .nav-action {
        display: flex;
        align-items: center;
        gap: 8rpx;
        color: #989898;
        font-size: 24rpx;
        background: transparent;
        padding: 0;
        border-radius: 0;
        flex-shrink: 0; // 导航按钮不收缩
        white-space: nowrap;
      }
    }
  }
  
  .tags-section {
    margin-bottom: 24rpx;
    width: 100%;
    box-sizing: border-box;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .tags-title {
      font-size: 30rpx;
      color: #333;
      margin-bottom: 16rpx;
      font-weight: 600;
      position: relative;
      padding-left: 16rpx;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 20rpx;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 3rpx;
      }
    }
    
    .tags-container {
      display: flex;
      flex-wrap: wrap;
      margin: -6rpx -6rpx -6rpx 10rpx; // 调整左边距，避免超出容器
      padding-left: 16rpx;
      box-sizing: border-box;
      width: calc(100% - 16rpx); // 为左侧padding留出空间
      
      .tag {
        padding: 8rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        margin: 6rpx;
        
        &.carrier-tag {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: #fff;
        }
      }
    }
  }
  
  .info-section {
    margin-bottom: 24rpx;
    width: 100%;
    box-sizing: border-box;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .info-title {
      font-size: 30rpx;
      color: #333;
      margin-bottom: 16rpx;
      font-weight: 600;
      position: relative;
      padding-left: 16rpx;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 20rpx;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 3rpx;
      }
    }
    
    .info-content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      padding-left: 16rpx;
      box-sizing: border-box;
      width: calc(100% - 16rpx); // 为左侧padding留出空间
      word-break: break-all; // 长文本自动换行
      word-wrap: break-word;
      
      .phone-number {
        color: #f65354;
        font-weight: 800;
        font-size: 48rpx;
      }
    }
  }
}

// 园区描述
.park-desc {
  padding: 32rpx;
  position: relative;
  z-index: 5;
  box-sizing: border-box; // 确保 padding 不会被内容撑开
  width: 100%; // 明确设置宽度
  
  .desc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    width: 100%;
    box-sizing: border-box;
    
    .desc-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      position: relative;
      padding-left: 16rpx;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 20rpx;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 3rpx;
      }
    }
  }
  
  .desc-content {
    color: #666;
    font-size: 28rpx;
    line-height: 1.8;
    word-break: break-all;
    word-wrap: break-word;
    padding-left: 16rpx;
    box-sizing: border-box;
    width: calc(100% - 16rpx); // 为左侧padding留出空间
    
    .html-content {
      font-size: 28rpx;
      line-height: 1.8;
      color: #666;
      word-break: break-all;
      word-wrap: break-word;
    }
  }
}

// TAB区域
.tab-section {
  .tab-container {
    display: flex;
    background: #fff;
    margin: 0;
    border-radius: 16rpx 16rpx 0 0;
    position: relative;
    z-index: 5;
    
    .tab-item {
      flex: none;
      min-width: 120rpx;
      text-align: center;
      padding: 24rpx 32rpx;
      font-size: 28rpx;
      color: #666;
      position: relative;
      
      &.active {
        color: #f65354;
        font-weight: 600;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background: linear-gradient(90deg, #f65354 0%, #ff8a80 100%);
          border-radius: 2rpx;
        }
      }
    }
  }
  
  .tab-content {
    background: #fff;
    margin: 0;
    border-radius: 0 0 16rpx 16rpx;
    min-height: 400rpx;
    position: relative;
    z-index: 5;
  }
}

// 新闻列表
.news-list {
  padding: 32rpx;
  box-sizing: border-box;
  width: 100%;
  
  .news-item {
    padding: 24rpx 0;
    border-bottom: 1px solid #f0f0f0;
    box-sizing: border-box;
    width: 100%;
    
    &:last-child {
      border-bottom: none;
    }
    
    // 无图片样式（默认）
    &:not(.has-image) {
      display: block;
      
      .news-content {
        width: 100%;
        box-sizing: border-box;
        
        .news-title {
          font-size: 32rpx;
          color: #333;
          font-weight: 500;
          margin-bottom: 16rpx;
          line-height: 1.4;
          word-break: break-all;
          word-wrap: break-word;
        }
        
        .news-summary {
          font-size: 28rpx;
          color: #666;
          line-height: 1.6;
          margin-bottom: 16rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          word-break: break-all;
          word-wrap: break-word;
        }
        
        .news-meta {
          .news-time {
            font-size: 26rpx;
            color: #999;
          }
        }
      }
    }
    
    // 有图片样式
    &.has-image {
      display: flex;
      align-items: flex-start;
      width: 100%;
      box-sizing: border-box;
      
      .news-content {
        flex: 1;
        min-width: 0; // 允许flex项目收缩
        margin-right: 20rpx;
        
        .news-title {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          margin-bottom: 12rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          line-height: 1.4;
          word-break: break-all;
          word-wrap: break-word;
        }
        
        .news-meta {
          margin-top: auto;
          
          .news-time {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
      
      .news-image {
        width: 160rpx;
        height: 120rpx;
        border-radius: 8rpx;
        object-fit: cover;
        flex-shrink: 0;
      }
    }
  }
}

// 载体资源列表
.estates-list {
  padding: 32rpx;
  box-sizing: border-box;
  width: 100%;
}

// 招商政策列表
.policies-list {
  padding: 32rpx;
  box-sizing: border-box;
  width: 100%;
  
  .policy-item {
    padding: 32rpx 24rpx;
    background: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 12rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    box-sizing: border-box;
    width: 100%;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .policy-header {
      margin-bottom: 20rpx;
      padding-bottom: 16rpx;
      border-bottom: 1px solid #f5f5f5;
      width: 100%;
      box-sizing: border-box;
      
      .policy-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
        line-height: 1.4;
        word-break: break-all;
        word-wrap: break-word;
        width: 100%;
        box-sizing: border-box;
      }
    }
    
    .policy-content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.8;
      margin-bottom: 16rpx;
      word-break: break-all;
      word-wrap: break-word;
      white-space: pre-wrap;
      width: 100%;
      box-sizing: border-box;
      
      text {
        display: block;
        width: 100%;
        box-sizing: border-box;
      }
    }
    
    .policy-meta {
      padding-top: 12rpx;
      border-top: 1px solid #f8f8f8;
      width: 100%;
      box-sizing: border-box;
      
      .policy-time {
        font-size: 24rpx;
        color: #999;
        font-style: italic;
      }
    }
  }
}

// 园区概况
.overview-content {
  padding: 32rpx;
  
  .overview-text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 20rpx;
  }
  
  .overview-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    color: #f65354;
    font-size: 26rpx;
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 80rpx 0;
  color: #999;
  font-size: 28rpx;
  
  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
    display: block;
  }
}

// 底部操作区
.bottom-bar {
  position: fixed;
  width: unset;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 24rpx 20rpx 24rpx;
  z-index: 100;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.08);
  
  .bar-left {
    gap: 20rpx;
    flex: 1.8;
    
    .icon-btn {
      flex: 1;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      border-radius: 40rpx;
      transition: all 0.3s ease;
      
      &.btn-outline {
        color: #666;
        background: transparent;
        border: 2rpx solid #ddd;
        
        &:hover {
          border-color: #999;
          color: #333;
        }
        
        &:active {
          opacity: 0.7;
        }
      }
      
      &.btn-booking {
        color: #fff;
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        
        &:hover {
          transform: translateY(-2rpx);
          box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
        }
        
        &:active {
          transform: translateY(0);
          opacity: 0.9;
        }
      }
    }
  }
  
  .bar-btn {
    height: 80rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 220rpx;
    
    &.btn-call {
      background: linear-gradient(135deg, #f65354 0%, #ff8a80 100%);
      color: #fff;
      margin-left: 20rpx;
      flex: 1.5;
      
      .iconfont {
        font-size: 26rpx !important;
        line-height: 1;
      }
      
      &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 4rpx 12rpx rgba(246, 83, 84, 0.3);
      }
      
      &:active {
        transform: translateY(0);
        opacity: 0.9;
      }
    }
  }
}

// 弹窗动画
@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes popupSlideOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(100%);
  }
}

@keyframes bookingSlideIn {
  from {
    opacity: 0;
    transform: translateY(100%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes bookingSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(100%) scale(0.9);
  }
}

// 弹窗样式
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  animation: fadeIn 0.3s ease-out forwards;
  
  &.booking-mask {
    align-items: center;
  }
  
  &.popup-closing {
    animation: fadeOut 0.3s ease-in forwards;
  }
  
  &.booking-closing {
    animation: fadeOut 0.3s ease-in forwards;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.detail-popup {
  width: 100%;
  height: 844rpx;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  display: flex;
  flex-direction: column;
  animation: popupSlideIn 0.4s ease-out forwards;
  
  &.popup-closing {
    animation: popupSlideOut 0.3s ease-in forwards;
  }
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1px solid #f0f0f0;
    
    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .close-btn {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  .popup-content {
    flex: 1;
    padding: 20rpx 40rpx 40rpx 32rpx;
    width:unset;
    max-height: 730rpx;
    .html-content {
      font-size: 28rpx;
      line-height: 1.8;
      color: #666;
      padding-right: 20rpx;
      padding-bottom: 20rpx;
    }
  }
}

.booking-popup {
  width: 600rpx;
  background: #fff;
  border-radius: 20rpx;
  margin: auto;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  animation: bookingSlideIn 0.4s ease-out forwards;
  
  &.booking-closing {
    animation: bookingSlideOut 0.3s ease-in forwards;
  }
  
  .booking-header {
    text-align: center;
    padding: 40rpx 32rpx;
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    
    .booking-title {
      font-size: 34rpx;
      font-weight: 600;
      color: #fff;
    }
  }
  
  .booking-form {
    padding: 40rpx 32rpx;
    
    .form-item {
      margin-bottom: 28rpx;
      position: relative;
      
      .form-input {
        width: 100%;
        height: 88rpx;
        padding: 0 24rpx;
        border: 2rpx solid #e0e0e0;
        border-radius: 12rpx;
        font-size: 28rpx;
        box-sizing: border-box;
        transition: all 0.3s ease;
        
        &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 2rpx rgba(102, 126, 234, 0.1);
        }
        
        &::placeholder {
          color: #999;
        }
      }
    }
    
    .form-actions {
      display: flex;
      gap: 24rpx;
      margin-top: 40rpx;
      
      .form-btn {
        flex: 1;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 44rpx;
        font-size: 30rpx;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &.cancel-btn {
          background: #f5f5f5;
          color: #666;
          
          &:hover {
            background: #ebebeb;
          }
          
          &:active {
            background: #e0e0e0;
          }
        }
        
        &.confirm-btn {
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          color: #fff;
          
          &:hover {
            transform: translateY(-2rpx);
            box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
          }
          
          &:active {
            transform: translateY(0);
            opacity: 0.9;
          }
        }
      }
    }
  }
}

.highlight {
  color: #f65354;
}

// 全局样式控制富文本中的图片
/deep/ .html-content img {
  max-width: 100% !important;
  height: auto !important;
  display: block;
  margin: 20rpx 0;
  border-radius: 8rpx;
}

/deep/ .html-content image {
  max-width: 100% !important;
  height: auto !important;
  display: block;
  margin: 20rpx 0;
  border-radius: 8rpx;
}
</style>