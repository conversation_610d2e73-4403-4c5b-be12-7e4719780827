<template>
	<view class="">
		<view class="jubao-box">
			<view class="row bottom-line">选择举报类型</view>
			<view>
				<radio-group @change="radioChange">
					<label v-for="(item,index) in options" :key="index" class="list-item bottom-line" hover-class="navigator-hover">
						<radio color="#f65354" :value="item.type" :checked="false" />
						<view class="list-info flex-1">
							<view class="list-title">{{item.title}}</view>
						</view>
					</label>
				</radio-group>
			</view>
			<template v-if ="informUserId">
				<view class="row ">举报原因</view>
				<view class="list-item  flex-1">
						<textarea name="reason" placeholder="请输入举报原因" v-model ="params.content" id="reason" :rows="3"></textarea>
				</view>
				<view class="row ">上传图片</view>
				<view class="upload-box">
					<my-upload @uploadDon="uploadDon" :chooseType="1" :maxCount="1" :imgs="params.images"></my-upload>
			</view>
		</template>
			<view class="btn-box">
				<button class="default" @click="subJubao()">确定提交</button>
			</view>
		</view>
		<chat-tip></chat-tip>
	</view>
</template>

<script>
import myUpload from "../../components/form/myUpload.vue";
	export default {
		data() {
			return {
				options:[],
				api:"house/report.html",
				params:{},
				informUserId:0,
				type:0
			};
		},
		components:{
			myUpload,
		},
		onLoad(options){
			if(options.id){
				this.id = options.id
			}
			switch(options.type){ //options.type:3和4代表举报帖子和举报帖子的评论，后台接收的id参数名是news_id;options.type:5代表商业地产详情举报，后台接收的id参数名是id;options.type:默认举报的是二手房出租房等，后台接收的id参数名是id;且两种情况为是不同接口，所以在此判断处理下  type :6 聊天举报
				case '3':
					this.api = 'news/commentReport.html'
					this.params.news_id = this.id
					this.params.type = 1
					this.options = [
						{type:"0",title:"违法信息"},
						{type:"1",title:"分类错误"},
						{type:"2",title:"虚假信息"},
						{type:"3",title:"谩骂他人"},
						{type:"4",title:"政治敏感"},
						{type:"5",title:"造谣诽谤"}
					]
					break
				case '4':
					this.api = 'news/commentReport.html'
					this.params.news_id = this.id
					this.params.type = 2
					this.options = [
						{type:"0",title:"违法信息"},
						{type:"2",title:"虚假信息"},
						{type:"3",title:"谩骂他人"},
						{type:"4",title:"政治敏感"},
						{type:"5",title:"造谣诽谤"}
					]
					break
        case '5':
          this.api = 'estate/report'
          this.params.id = this.id
          this.options = [
						{type:"0",title:"违法信息"},
						{type:"1",title:"分类错误"},
						{type:"2",title:"虚假信息"},
						{type:"3",title:"经纪人冒充个人"},
						{type:"4",title:"无效号码"},
						{type:"5",title:"信息已过期"}
					]
          break
				case '6':
					this.type = options.type
          this.api = 'im/addReport'
					this.$set(this.params,'content','')
					this.$set(this.params,"images",[])
					this.$set(this.params,"type",0)
					this.informUserId = options.informUserId||0   //被举报者id  
					// this.params.type = options.userType
					this.params.be_uid = options.informUserId 

          this.options = [
						{type:"1",title:"违法信息"},
						{type:"2",title:"骚扰信息"},
						{type:"3",title:"其他信息"},
						// {type:"3",title:"经纪人冒充个人"},
						// {type:"4",title:"无效号码"},
						// {type:"5",title:"信息已过期"}
					]
          break
				default:
					this.api = 'house/report.html'
					this.params.id = this.id
					this.options = [
						{type:"0",title:"违法信息"},
						{type:"1",title:"分类错误"},
						{type:"2",title:"虚假信息"},
						{type:"3",title:"经纪人冒充个人"},
						{type:"4",title:"无效号码"},
						{type:"5",title:"信息已过期"}
					]
			}
		},
		methods:{
			radioChange(e){
				let index = parseInt(e.detail.value)
				if(this.type!=6){
					this.params.content = this.options[index].title
				}else {
					this.params.type= this.options[index-1].type
				}
			},
			uploadDon(e){ 
				this.params.images = e.files
			},
			subJubao(){
				if(!this.params.content&&this.type!=6){
					uni.showToast({
						title:'请选择举报类型',
						icon:"none"
					})
					return
				}
				if(this.params.type<1&&this.type==6){
					uni.showToast({
						title:'请选择举报类型',
						icon:"none"
					})
					return
				}
				this.$ajax.post(this.api,this.params,res=>{
					uni.showToast({
						title:res.data.msg,
						icon:"none"
					})
					if(res.data.code == 1){
						setTimeout(()=>{
							uni.navigateBack()
						},1500)
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.jubao-box{
		height:100vh;
		width:100%;
		background-color:#fff;
		.row{
			padding:24upx 30upx;
		}
		.upload-box{
			padding:24upx 30upx;
		}
	}
	.list-item{
	    display: flex;
	    align-items: center;
	    padding: $uni-spacing-col-lg $uni-font-size-lg;
	    radio{
	        padding: 20upx 30upx;
	        margin-left: -30upx;
	    }
			textarea{
				width: 100%;
				height:120rpx;
				padding:10rpx;
				border: 2rpx solid #f3f3f3;
				border-radius: 10rpx;
			}
	    .list-title{
	        font-size: $uni-font-size-lg;
	        text-overflow:ellipsis;
	        white-space:nowrap;
	        line-height:1.5;
	        overflow:hidden;
	    }
	}
</style>
