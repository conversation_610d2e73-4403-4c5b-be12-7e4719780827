<template>
<view class="order_detail">
    <view class="title bottom-line">
        <text>户型信息</text>
    </view>
    <view class="order-item" @click="toHouse()">
        <view class="order-content flex-box">
            <view class="img-box">
                <image :src="order_info.pic | imgUrl('w_240')" mode="aspectFill"></image>
            </view>
            <view class="flex-1">
                <view class="order-desc">{{order_info.title}}</view>
                <view class="name">{{order_info.name}} {{order_info.huxing}}</view>
            </view>
        </view>
    </view>
    <view class="card">
         <view class="title bottom-line">
             <text>核销码</text>
             <text class="sub_title">点击核销码可复制</text>
         </view>
         <view class="card_content" @click="handleCopy()">
             <text class="verification_code">{{order_info.write_off_number}}</text>
         </view>
    </view>
    <view class="card">
        <view class="title bottom-line">
             <text>订单总价</text>
             <text class="price">￥{{order_info.total_money}}</text>
         </view>
         <view class="card_content">
             <view class="list_item">姓名：{{order_info.uname}}</view>
             <view class="list_item">支付时间：{{order_info.pay_time}}</view>
             <view class="list_item">订单号：{{order_info.order_sn}}</view>
             <!-- 后台说没有有效期功能 -->
             <!-- <view class="list_item">有效期：{{order_info.uname}}</view> -->
         </view>
    </view>
</view>
</template>

<script>
import {
    navigateTo,
    formatImg,
} from '../common/index.js'
export default {
    data() {
        return {
            order_info:{},
        }
    },
    onLoad(options){
        if(options.id){
            this.order_id = options.id
            this.getData()
        }
    },
    filters:{
        imgUrl(val,param){
            return formatImg(val,param)
        },
    },
    methods: {
        getData(){
            this.$ajax.get('online/orderDetail',{order_id:this.order_id},res=>{
                if(res.data.code == 1){
                    this.order_info = res.data.order_info
                    this.business = res.data.business
                }
            })
        },
    },
}
</script>

<style scoped lang="scss">
.card{
    margin: 24rpx;
    padding: 26rpx;
    border-radius: 10rpx;
    background-color: #fff;
    box-shadow: 0 0 10px #dedede;
    .title{
        padding: 26rpx 0;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        &.qr_code_title{
            font-weight: initial;
            font-size: 32rpx;
            padding-bottom: 40rpx;
        }
        .sub_title{
            margin-left: 25rpx;
            font-size: 25rpx;
            font-weight: initial;
            color: #666;
        }
        .price{
            float: right;
            font-weight: bold;
            color: #f00;
        }
    }
    .card_content{
        margin-top: 20rpx;
        .img{
            margin-top: 40rpx;
            width: 350rpx;
            height: 350rpx;
        }
        .tip{
            color: #555;
        }
        .btn{
            margin-top: 20rpx;
            display: inline-block;
            width: 150rpx;
            height: 50rpx;
            line-height: 50rpx;
            border: 1rpx solid #dedede;
            border-radius: 25rpx;
            font-size: 26rpx;
            color: #555;
            &.highlight{
                color: $uni-color-primary;
                border-color: $uni-color-primary;
            }
        }
        .verification_code{
            display: inline-block;
            padding-bottom: 20rpx;
            font-size: 36rpx;
            font-weight: bold;
        }
        .list_item{
            padding: 5rpx;
            line-height: 50rpx;
            // height: 50rpx;
            // line-height: 50rpx;
        }
        .handler{
            padding: 10rpx;
            text{
                display: inline-block;
                // height: 40rpx;
                // line-height: 40rpx;
                padding: 5rpx 15rpx;
                border-radius: 5rpx;
                // background-color: #f8a306;
                color: #fff;
            }
            .c-btn{
                background-color: #f8a306;
            }
        }
    }
}
.order-item{
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
    .order-content{
        padding: 20rpx;
        .img-box{
            width: 170rpx;
            height: 130rpx;
            margin-right: 20rpx;
            image{
                width: 100%;
                height: 100%;
            }
        }
        .order-desc{
            line-height: 1.4;
            font-size: 30rpx;
            height: 80rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            color: #333;
        }
        .name{
            margin-top: 10rpx;
            color: #666;
            font-size: 28rpx;
        }
    }
}
.title {
    padding: 20rpx 20rpx 20rpx 40rpx;
    font-size: 32rpx;
    position: relative;
    background: #fff;

    &::before {
        content: "";
        position: absolute;
        left: 20rpx;
        top: 20rpx;
        bottom: 20rpx;
        width: 6rpx;
        background-color: #f65354
    }
}
</style>
