<template>
  <view class="page" :style="{backgroundColor: activity_detail.bg_color, backgroundImage: `url(${activity_detail.bg_pic})`}">
    <image v-if="activity_detail.head_pic" class="banner" :src="activity_detail.head_pic" mode="widthFix"></image>
    <view class="prize_btn" @click="$navigateTo('/lucky/my_prizes?id='+activity_detail.id)">
      <image :src="'/lucky/prize_list_btn.png' | imageFilter"></image>
      <view class="text">我的抽奖</view>
    </view>
    <view class="time-box flex-box">
      <view>{{ is_start === 0 ? '距离开始' : is_start === 1 ? '距离结束' : '已结束' }}：</view>
      <view class="time-item">
        <text>{{ time_down.day }}</text>
      </view>
      <view class="colon">天</view>
      <view class="time-item">
        <text>{{ time_down.hour }}</text>
      </view>
      <view class="colon">时</view>
      <view class="time-item" >
        <text>{{ time_down.minute }}</text>
      </view>
      <view class="colon">分</view>
      <view class="time-item">
        <text>{{ time_down.second }}</text>
      </view>
      <view class="colon">秒</view>
    </view>
    <view class="notice" :style="{backgroundImage: `url(${image_domain}/lucky/notice_border.png)`}">
      您还有<text class="number">{{surplus}}</text>次抽奖机会
    </view>
    <view class="wheel_container">
      <image class="bg" mode="widthFix" :src="activity_detail.turntable_bg"></image>
      <view class="whell_box" v-show="show_wheel">
        <LuckyWheel
        v-if="wheel.prizes.length>0"
        ref="luckyWheel"
        :blocks="wheel.blocks"
        :prizes="wheel.prizes"
        :buttons="wheel.buttons"
        width="287px"
        height="287px"
        :default-style="wheel.defaultStyle"
        :default-config="wheel.defaultConfig"
        @start="wheelStart"
        @end="wheelEnd"
      />
      </view>
    </view>
    <view class="prize_container">
      <view v-if="log_list.length>0" class="header" :style="{backgroundImage: `url(${image_domain}/lucky/container_top.png)`}">
        <text>获奖名单</text>
      </view>
      <view class="container" v-if="log_list.length>0">
        <view class="top" :style="{backgroundImage: `url(${image_domain}/lucky/border_top.png)`}"></view>
        <view class="center" :style="{backgroundImage: `url(${image_domain}/lucky/container_center.png)`}">
          <swiper class="logs" vertical disable-touch :display-multiple-items="log_list.length>4?4:log_list.length" :style="{height: log_list.length>4?4*60+'rpx': log_list.length*60+'rpx'}"  :interval="2000" :circular="true" autoplay>
            <swiper-item class="log_item" v-for="item in log_list" :key="item.id">
                <image class="point" :src="'/lucky/point.png' | imageFilter('m_320')"></image>
                <text class="flex-1">{{item.tel}}</text>
                <text>获得{{item.name}}</text>
            </swiper-item>
          </swiper>
        </view>
        <view class="bottom" :style="{backgroundImage: `url(${image_domain}/lucky/border_bottom.png)`}"></view>
      </view>

      <view v-if="log_list.length>0&&activity_detail.is_show_share" class="connect_center" :style="{backgroundImage: `url(${image_domain}/lucky/connect_center.png)`}"></view>

      <view class="container" v-if="activity_detail.is_show_share">
        <view class="top" :style="{backgroundImage: `url(${image_domain}/lucky/border_top2.png)`}"></view>
        <view class="center2" :style="{backgroundImage: `url(${image_domain}/lucky/container_center.png)`}">
          <view class="flex-box label">
            <image class="point" :src="'/lucky/point.png' | imageFilter('m_320')"></image>
            <text class="text">获取更多抽奖机会</text>
            <image class="point" :src="'/lucky/point.png' | imageFilter('m_320')"></image>
          </view>
          <view class="list">
            <view class="list_item flex-box">
              <view class="icon-box">
                <my-icon type="ic_fenxiang" color="#ffffff"></my-icon>
              </view>
              <view class="right_info">
                <view>
                  <view class="title">邀请好友参与活动</view>
                  <view class="tip">获得{{activity_detail.share_add}}次抽奖机会</view>
                </view>
                <view class="button" @click="show_share = true">立即分享</view>
              </view>
            </view>
          </view>
        </view>
        <view class="bottom" :style="{backgroundImage: `url(${image_domain}/lucky/border_bottom.png)`}"></view>
      </view>

      <view class="container mgt48">
        <view class="connect_center2" v-if="log_list.length>0||activity_detail.is_show_share" :style="{backgroundImage: `url(${image_domain}/lucky/connect_center2.png)`}"></view>
        <view class="top" :style="{backgroundImage: `url(${image_domain}/lucky/border_top2.png)`}"></view>
        <view class="center2" :style="{backgroundImage: `url(${image_domain}/lucky/container_center.png)`}">
          <view class="flex-box label">
            <image class="point" :src="'/lucky/point.png' | imageFilter('m_320')"></image>
            <text class="text">活动规则</text>
            <image class="point" :src="'/lucky/point.png' | imageFilter('m_320')"></image>
          </view>
          <view class="rule content" v-if="activity_detail.rule" v-html="activity_detail.rule"></view>
        </view>
        <view class="bottom" :style="{backgroundImage: `url(${image_domain}/lucky/border_bottom.png)`}"></view>
      </view>
    </view>
    <my-popup ref="prize_popup" position="center" :height="current_prize.is_prize?'694rpx':'646rpx'">
      <view class="prize_popup">
        <view class="header" :style="{backgroundImage: `url(${image_domain}/lucky/popup_header.png)`}">
          <image class="icon" :class="{icon2: current_prize.is_prize}" mode="widthFix" :src="(current_prize.is_prize?'/lucky/gift_box.png': '/lucky/face.png') | imageFilter('m_320')"></image>
        </view>
        <image v-if="current_prize.is_prize" class="prize_pic" mode="widthFix" :src="current_prize.pic | imageFilter('m_320')"></image>
        <view v-else class="no_prize_name">{{current_prize.name}}</view>
        <view class="prize_name">{{current_prize.is_prize?current_prize.name:'很遗憾您没有中奖～'}}</view>
        <view class="btn" @click="$refs.prize_popup.hide()">再抽一次</view>
        <view class="cancel" @click="$refs.prize_popup.hide()">取消</view>
      </view>
    </my-popup>
    <my-popup ref="add_user_info" position="center" height="660rpx">
      <view class="add_user_info">
        <view class="header">
          <view class="header_title">请先完善领奖人信息</view>
        </view>
        <view class="form">
          <view class="form-item">
            <input type="text" v-model="user_info.cname" maxlength="4" placeholder="联系人">
          </view>
          <view class="form-item">
            <input type="number" v-model="user_info.tel" maxlength="11" placeholder="手机号">
            <view class="tip">手机号将作为领奖凭证</view>
          </view>
        </view>
        <view class="btn" @click="addUserInfo">开始抽奖</view>
        <view class="cancel" @click="$refs.add_user_info.hide()">取消</view>
      </view>
    </my-popup>
    <my-popup ref="qrcode_popup" position="top">
      <view class="qrcode-box">
        <view class="img-box">
          <image class="qrcode" :src="qrcode" mode="aspectFill"></image>
          <view>
            <view class="tip">长按识别二维码关注公众号， 关注成功即可抽奖</view>
          </view>
        </view>
        <view class="icon-box" @click="$refs.qrcode_popup.hide()">
          <my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
        </view>
      </view>
    </my-popup>
    <shareTip :show="show_share" @hide="show_share = false" />
  </view>
</template>

<script>
import LuckyWheel from './components/wheel.vue'
import myIcon from '../components/myIcon'
import myPopup from '../components/myPopup'
import shareTip from '../components/shareTip'
import {config} from '../common/config'
import {formatImg} from '../common/index'
import getLocation from './get_location'
export default {
  components: {
    LuckyWheel,
    myIcon,
    myPopup,
    shareTip
  },
  data () {
    return {
      activity_detail: {
        id: '',
        check_tel: 0,
        check_info: 0,
        check_follow: 0,
        model: 1,
        is_show: 1,
        end_time: Date.parse(new Date()) / 1000,
        start_time: Date.parse(new Date()) / 1000,
        share_pic: "",
        head_pic: "",
        bg_color: "#e22f1a",
        bg_pic: "",
        turntable_bg: "",
        start_button: "",
        triangle_color1: "#FCFCFC",
        triangle_color2: "#F2DEDE",
        font_color: '#F52E2E',
        number: 2,
        area_limit_name: "",
        rule: ""
      },
      now: Date.parse(new Date()) / 1000,
      is_start: 0,
      image_domain: config.imgDomain,
      log_list:[],
      show_wheel: false,
      wheel: {
        blocks: [{ padding: '3px', background: '#e22f1a' }],
        prizes: [],
        buttons: [
          // { radius: '35px', background: '#d64737' },
          
          // { radius: '25px', background: '#ffdea0' }
        ],
        defaultStyle: {
          fontColor: '#DFA300',
          fontSize: '13px',
        },
        defaultConfig: {
          gutter: '1px',
          stopRange: 0.82,
          speed: 15,
          accelerationTime: 1000,
          decelerationTime: 6000,
          speedFunction: 'cubic'
        }
      },
      current_city: '',
      current_prize: {},
      surplus: 0, //剩余抽奖次数
      user_info: {
        cname: "",
        tel: ""
      },
      qrcode: '',
      show_share: false
    }
  },
  onLoad(options){
    if(options.id){
      this.user_info.id = options.id
      this.getData(options.id, options.shareId||'')
    }
    uni.$on("getDataAgain", ()=>{
      this.getData()
    })
  },
  onHide(){
    this.geting = false
  },
  computed:{
    time_down() {
      let difference
      if (this.activity_detail.start_time < this.now) {
        difference = this.activity_detail.end_time - this.now
      } else {
        difference = this.activity_detail.start_time - this.now
      }
      if (difference < 0) {
        return {
          day: '00',
          hour: '00',
          minute: '00',
          second: '00'
        }
      }
      let day = Math.floor(difference / 86400)
      let hour = Math.floor((difference % 86400) / 3600)
      let minute = Math.floor(((difference % 86400) % 3600) / 60)
      let second = ((difference % 86400) % 3600) % 60
      return {
        day: day < 10 ? '0' + day : day,
        hour: hour < 10 ? '0' + hour : hour,
        minute: minute < 10 ? '0' + minute : minute,
        second: second < 10 ? '0' + second : second
      }
    }
  },
  onUnload(){
    uni.$off("getDataAgain")
  },
  methods: {
    getData(id, share_id){
      this.$ajax.get('activity/luckDraw.html', {id, share_id}, res=>{
        if(res.data.code !== 1){
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
          return
        }
        this.map_key = res.data.txmapwapkey
        this.activity_detail = res.data.activity
        this.goods_list = res.data.goods
        this.surplus = res.data.surplus
        this.log_list = res.data.prize_list
        this.wheel.defaultStyle.fontColor = this.activity_detail.font_color

        this.wheel.buttons=[{ radius: '20px', pointer: true, imgs:[{src: this.activity_detail.start_button,width: '90px', top: '-52px'}] }]

        this.wheel.prizes = this.goods_list.map((item, index)=>{
          item.background = index % 2 ? this.activity_detail.triangle_color1 : this.activity_detail.triangle_color2,
          item.imgs = [{src: formatImg(item.pic, 'w_100'), width:'28px', height: '28px', top: '36%'}],
          item.fonts = [{ text: item.name, top: '12%' }]
          return item
        })
        this.timeDownStart()
        

        this.share = {
          title: res.data.activity.share_title||res.data.title,
          content: res.data.activity.share_content,
          link: `${window.location.origin}${window.location.pathname}?id=${res.data.activity.id}&shareId=${res.data.uid}`,
          pic: res.data.activity.share_pic,
        }
        if(this.activity_detail.area_limit_name){
          this.getWxConfig(['getLocation','updateAppMessageShareData','updateTimelineShareData'], (wx)=>{
            console.log(wx)
            this.wx = wx
            this.getCity()
          })
        }else{
          this.getWxConfig()
        }

        this.$nextTick(()=>{
          this.show_wheel = true
        })
      })
    },
    // 倒计时
    timeDownStart() {
      if(this.activity_detail.end_time< this.now){
        this.is_start = 2
      }else if(this.activity_detail.start_time < this.now){
        this.is_start = 1
      }else{
        this.is_start = 0
      }
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        this.now = Date.parse(new Date()) / 1000
      }, 1000)
    },
    getCity(options={}){
      this.$store.state.getPosition(this.wx, (res)=>{
        getLocation({
          latitude: res.lat,
          longitude: res.lng,
          map_key: this.map_key||'',
          success: res=>{
            this.current_city = res.city
            options.success && options.success(res)
          },
          fail: err=>{
            console.log(err)
            options.fail && options.fail(err)
          }
        })
      })
    },
    wheelStart () {
      let now = Date.parse(new Date()) / 1000
      if (this.activity_detail.start_time > now) {
        uni.showToast({
          title: '活动还没开始！',
          icon: 'none',
          duration: 5000,
          mask: true
        })
        return
      } else if (this.activity_detail.end_time < now) {
        uni.showToast({
          title: '活动已经结束！',
          icon: 'none',
          duration: 5000,
          mask: true
        })
        return
      }
      if(this.activity_detail.area_limit_name&&!this.current_city){
        this.getCity({
          success: ()=>{
            this.getPrize()
          },
          fail: (err)=>{
            console.log(err)
            // uni.showToast({
            //   title: '获取城市失败',
            //   icon: 'none'
            // })
            this.getPrize()
          }
        })
      }else{
        this.getPrize()
      }
    },
    getPrize(){
      if(this.geting){
        return
      }
      this.$store.state.allowOpen = true
      this.current_prize = {}
      this.geting = true
      this.$ajax.get('activity/doLuckDraw.html', {id: this.activity_detail.id, area_limit_name: this.current_city}, res=>{
        if(res.data.gzhewm){
          // 需要关注公众号
          this.qrcode = res.data.gzhewm
          this.$refs.qrcode_popup.show()
          this.geting = false
          return
        }
        if(res.data.is_perfect){
          // 需要完善信息
          this.$refs.add_user_info.show()
          this.geting = false
          return
        }
        if(res.data.code === 0){
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
          this.geting = false
          return
        }

        if(res.data.code === 1){
          this.surplus = res.data.surplus
          this.$refs.luckyWheel.play()
          setTimeout(() => {
            let index = this.goods_list.findIndex(item=>item.id === res.data.data.prize.id)
            // let index = Math.random() * this.wheel.prizes.length >> 0
            this.$refs.luckyWheel.stop(index)
          }, 2000)
        }
      }, err=>{
        this.geting = false
      })
    },
    addUserInfo(){
      this.$ajax.post('activity/addLuckDrawUserInfo.html', this.user_info, res=>{
        if(res.data.code === 1){
          this.$refs.add_user_info.hide()
          this.wheelStart()
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    wheelEnd (prize) {
      this.current_prize = prize
      this.geting = false
      setTimeout(()=>{
        this.$refs.prize_popup.show()
      }, 500)
      // uni.showToast({
      //   title: `恭喜您抽中${prize.title}`,
      //   icon: 'none'
      // })
    },
  }
}
</script>

<style scoped lang="scss">
.page{
  height: 100vh;
  overflow-x: hidden;
  background-size: cover;
  background-position: 50%;
  background-repeat: no-repeat;
}

.time-box{
  justify-content: center;
  padding: 48rpx 24rpx;
  color: #fff;
  >view{
    font-size: 32rpx;
  }
  .time-item{
    margin: 0 12rpx;
    color: #ffc553;
  }
}

.notice{
  width: 540rpx;
  height: 122rpx;
  line-height: 120rpx;
  margin: auto;
  margin-bottom: -48rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  text-align: center;
  font-size: 32rpx;
  color: #fff;
  .number{
    margin: 0 10rpx;
    font-size: 40rpx;
    color: #ffcd46;
  }
}
.banner{
  width: 100%;
}

.prize_btn{
  position: absolute;
  top: 0;
  right: 32rpx;
  width: 92rpx;
  height: 196rpx;
  z-index: 9;
  image{
    width: 100%;
    height: 100%;
  }
  .text{
    box-sizing: border-box;
    width: 88rpx;
    padding: 33rpx;
    line-height: 1.4;
    position: absolute;
    right: 0;
    top: 0;
    font-size: 22rpx;
    color: #FFCD46;
  }
}

.wheel_container{
  position: relative;
  width: 345px;
  height: 345px;
  margin: auto;
}
.bg{
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
}
.whell_box{
  width: 287px;
  height: 287px;
  margin: auto;
  position: relative;
  top: 30px;
  // top: 80px;
}

.mgt48{
  margin-top: 48rpx;
}
.prize_container{
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 48rpx;
  view{
    box-sizing: border-box;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .container{
    position: relative;
    .point{
      width: 22rpx;
      height: 22rpx;
    }
    .label{
      align-items: center;
      justify-content: center;
      .text{
        font-size: 32rpx;
        font-weight: bold;
        margin: 0 16rpx;
      }
    }
    .list{
      .list_item{
        align-items: center;
        .icon-box{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          background-image: linear-gradient(180deg, #FFE87C 0%, #FFCD46 100%);
        }
        .right_info{
          padding: 24rpx 0;
          flex: 1;
          display: flex;
          margin-left: 24rpx;
          align-items: center;
          justify-content: space-between;
          .title{
            margin-bottom: 8rpx;
            font-size: 28rpx;
            font-weight: bold;
          }
          .tip{
            font-size: 24rpx;
            color: #e22f1a;
          }
          .button{
            width: 144rpx;
            text-align: center;
            height: 64rpx;
            line-height: 64rpx;
            font-size: 24rpx;
            background-image: linear-gradient(180deg, #FF8163 0%, #E22F1A 100%);
            box-shadow: 0 8rpx 16rpx 0 rgba(240,86,61,0.40);
            border-radius: 32rpx;
            border-radius: 32rpx;
            color: #fff;
          }
        }
      }
    }
    .rule{
      margin-top: 28rpx;
      line-height: 1.8;
      padding-bottom: 1rpx;
      color: #666;
      background-color: #fff;
    }
  }
  .header{
    width: 480rpx;
    height: 86rpx;
    line-height: 86rpx;
    margin-bottom: -12rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
  }
  .top{
    width: 654rpx;
    height: 50rpx;
    margin-bottom: -1rpx;
  }
  .center{
    padding: 24rpx 48rpx 0 48rpx;
    width: 654rpx;
    .logs{
      height: 80rpx;
    }
    .log_item{
      display: flex;
      align-items: center;
      line-height: 40rpx;
      .point{
        margin-right: 16rpx;
      }
    }
  }
  .center2{
    padding: 0 48rpx;
    width: 654rpx;
    margin-top: -1rpx;
  }
  .bottom{
    margin-top: -1rpx;
    width: 654rpx;
    height: 48rpx;
  }
  .connect_center{
    width: 480rpx;
    height: 48rpx;
  }
  .connect_center2{
    width: 724rpx;
    height: 212rpx;
    position: absolute;
    top: -126rpx;
    left: -37rpx;
  }
}

.add_user_info{
  width: 600rpx;
  height: 660rpx;
  box-sizing: border-box;
  padding-bottom: 24rpx;
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 48rpx;
  background: #fff;
  .header{
    width: 100%;
    height: 132rpx;
    padding: 44rpx;
    box-sizing: border-box;
    background-image: linear-gradient(180deg, #FF8163 0%, #E22F1A 100%);
    border-radius: 48rpx 48rpx 0 0;
    color: #fff;
    .header_title{
      padding-left: 24rpx;
      border-left: 12rpx solid #fff;
      font-size: 32rpx;
    }
  }

  .form{
    margin: 48rpx 0;
    .form-item{
      margin-bottom: 24rpx;
      height: 88rpx;
      width: 504rpx;
      margin: 24rpx 0;
      input{
        padding: 24rpx;
        box-sizing: border-box;
        background-color: #f8f8f8;
        height: 100%;
      }
      .tip{
        margin-top: 12rpx;
        font-size: 24rpx;
        color: $uni-color-primary;
      }
    }
  }


  .btn{
    width: 504rpx;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    background-image: linear-gradient(180deg, #FF8163 0%, #E22F1A 100%);
    box-shadow: 0 8rpx 16rpx 0 rgba(240,86,61,0.40);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #fff;
  }

  .cancel{
    padding: 24rpx;
    text-align: center;
    color: #999;
  }
}

.prize_popup{
  width: 600rpx;
  height: 646rpx;
  box-sizing: border-box;
  padding-bottom: 24rpx;
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 48rpx;
  background: #fff;
  &.prize_popup2{
    height: 696rpx;
  }
  .header{
    height: 152rpx;
    width: 100%;
    background-size: 100% auto;
    background-position: 0 0;
    background-repeat: no-repeat;
    margin-bottom: 48rpx;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    .icon{
      width: 192rpx;
      &.icon2{
        width: 320rpx;
      }
    }
  }
  .prize_pic{
    width: 130rpx;
  }
  .prize_name{
    flex: 1;
    padding: 38rpx 24rpx;
    font-size: 32rpx;
    color: #666;
  }
  .no_prize_name{
    padding: 12rpx 24rpx;
    font-size: 48rpx;
    color: #DFA300;
  }
  .btn{
    width: 504rpx;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    background-image: linear-gradient(180deg, #FF8163 0%, #E22F1A 100%);
    box-shadow: 0 8rpx 16rpx 0 rgba(240,86,61,0.40);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #fff;
  }

  .cancel{
    padding: 24rpx;
    text-align: center;
    color: #999;
  }
}


.qrcode-box{
  position: relative;
  margin-top: 15vh;
  .img-box{
    width: 584rpx;
    text-align: center;
    padding: 12rpx;
    margin: auto;
    background-color: #fff;
    border-radius: 8rpx;
    overflow: hidden;
    .title{
      font-size: 36rpx;
      font-weight: bold;
      text-align: center;
      color: #333;
    }
    .tip{
      padding: 0 24rpx;
      padding-bottom: 48rpx;
      font-size: 30rpx;
      text-align: center;
      color: #666;
    }
  }
  .qrcode{
    width: 560rpx;
    height: 560rpx;
  }
  .icon-box{
    position: absolute;
    bottom: -80rpx;
    width: 52rpx;
    height: 52rpx;
    left: 0;
    right: 0;
    margin: auto;
  }
}
</style>