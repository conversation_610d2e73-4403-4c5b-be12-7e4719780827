import {config} from '../common/config'
var chat = {
    data(){
        return {
            page_show_num: 0
        }
    },
    onShow(){
        this.page_show_num++
        if (this.page_show_num > 1 && this.im.socketOpen){
            console.log("开始检测")
            uni.$emit("checkSocket")
        }
    },
    methods: {
        /**
         * 连接socket
         */
        handleConnectSocket(){
            if (!this.im.imToken) {
                uni.showToast({
                    title: "聊天token为空",
                    icon: 'none'
                })
                return
            }
            this.im.socketTask = uni.connectSocket({
                url: `${config.imSocket}?imToken=${this.im.imToken}&userToken=${this.userToken}`,
                success: res=> {
                    console.log(res)
                },
                fail:err=>{
                    console.log(err)
                }
            });
            // 监听socket连接成功事
            this.im.socketTask.onOpen(res => {
                this.im.socketOpen = true
                this.onMessage()
                console.log('WebSocket连接已打开！');
                // 发送一条初始化消息
                this.sendMessage(this.initMsg)
                // 执行socket链接成功事件
                this.socketOnOpen&&this.socketOnOpen()
                this.heartbeat()
                uni.$off("checkSocket")
                uni.$on("checkSocket", () => {
                    this.checkSocketStatus()
                })
                if (this.$refs.socket_colse) {
                    this.$refs.socket_colse.hide()
                }
            });
        },
        /**
         * 监听socket连接成功事件
         */
        onOpen(){
            this.im.socketTask.onSocketOpen( res=> {
                this.im.socketOpen = true
                console.log('WebSocket连接已打开！');
                this.sendMessage(this.initMsg)
                this.heartbeat()
                if (this.$refs.socket_colse) {
                    this.$refs.socket_colse.hide()
                }
            });
        },
        // 检测socket是否已经断开
        checkSocketStatus() {
            if (this.im.socketOpen) {
                // 发送一条心跳检测测试是否成功
                this.im.socketTask.send({
                    data: JSON.stringify({ flag: 'Heartbeat' }),
                    success: () => {
                        console.log("心跳正常")
                        // uni.showToast({
                        //     title: "心跳正常",
                        //     icon: 'none'
                        // })
                    },
                    fail: err => {
                        this.im.socketOpen = false
                        // uni.showToast({
                        //     title: "聊天意外断开，重新连接",
                        //     icon: 'none'
                        // })
                        if (this.timer) {
                            clearInterval(this.timer)
                        }
                        console.log("心跳断开")
                        // 重新连接
                        this.connectChatAgain()
                    }
                });
            } else {
                // 重新连接
                this.connectChatAgain()
            }
        },
        /**
         * 维持连接
         */
        heartbeat(){
            if (this.timer){
                clearInterval(this.timer)
            }
            let time_out = 30000
            this.timer = setInterval(()=>{
                if (!this.im.socketOpen && this.timer){
                    clearInterval(this.timer)
                    return
                }
                this.sendMessage({ flag: 'Heartbeat' })
            }, time_out)
            // let startTime = new Date().getTime();
            // let count = 0;
            // let _this = this
            // function handle() {
            //     if (!_this.im.socketOpen){
            //         return
            //     }
            //     count++;
            //     var offset = new Date().getTime() - (startTime + count * time_out);
            //     var nextTime = time_out - offset;
            //     if (nextTime < 0) nextTime = 0;
            //     setTimeout(handle, nextTime);
            //     _this.sendMessage({ flag:'Heartbeat'})
            // }
            // setTimeout(handle, time_out);
        },
        /**
         * 接收消息
         */
        onMessage(){
            this.im.socketTask.onMessage( res=> {
                let resData = JSON.parse(res.data)
                // console.log('收到服务器消息：', resData);
                switch (resData.flag){
                    // 聊天消息
                    case 'sendMessage':
                        this.checkMsg(resData)
                        break;
                    // 发送消息后的回调消息状态
                    case 'sendMessageStatus':
                        this.pushMyMsg(resData.chat_id, resData.type, resData.type === 'text' ? resData.content:JSON.stringify(resData.content), resData.is_online)
                        // 如果对方不在线
                        if (!resData.is_online && !this.is_auto_replay) {
                            // 获取自动回复
                            this.getAutoReplay(resData.chat_id)
                        }
                        break;
                    // 对方把自己加入黑名单消息
                    case 'pullBlack':
                        this.handleBlack(resData.black_user_id)
                        break;
                    // 对方把自己从黑名单恢复的消息
                    case 'removeBlack':
                        this.handleRemoveBlack(resData.black_user_id)
                        break;
                    // 对方邀请评价
                    case 'inviteEvaluate':
                        this.handleInviteEvaluate(resData)
                        break;
                    // 对方申请获取电话号码
                    case 'applyTel':
                        this.handleApplyTel(resData)
                        break;
                    default:
                        break
                }
            });
        },
        /**
         * 添加本地系统消息
         * @param {Object} msg 本地消息,例如查看对方微信名片
         * @param {String} type 本地消息类型,wechat:微信名片,其他值和消息类型一直
         */
        addSystemMessage(msg, type='text'){
            let send_msg = {
                from_id: this.im.myChatInfo.platformid,
                to_id: this.im.nowChat.platformid,
                platform: this.im.platform,
                ctime: Date.parse(new Date()) / 1000,
                time: Date.parse(new Date()) / 1000,
                local_time: new Date().getTime(),
                type: type,
                content: msg
            }
            this.saveMsg(send_msg, -1, this.im.chatIndex)
            this.im.chatIndex = 0
        },
        /**
         * 获取黑名单状态
         * @param {Number} owner_id 操作加黑者的id
         * @param {Number} passive_id 被加黑者的id
         * @param {Number} friend_id 好友的id
         * @param {Number} my_id 自己的id
         */
        getBlack(owner_id, passive_id, friend_id, my_id){
            if (owner_id === friend_id && passive_id === my_id){
                // 对方把自己加黑
                uni.showToast({
                    title: "对方已拒绝您的会话",
                    icon: 'none'
                })
                return 1
            }
            if (owner_id === my_id && passive_id === friend_id){
                // 自己把对方加黑
                uni.showToast({
                    title: "您已将对方加入黑名单",
                    icon: 'none'
                })
                return 2
            }
            return 0
        },
        /**
         * 发送消息
         * @param {Object} msg 消息内容
         * @param {String} type 消息类型 text:文本;image:图片;map:位置;build:楼盘
         */
        sendMessage(msg, type = "text"){
            // init:好友列表初始化; Heartbeat:心跳维持; directChat:直聊初始化;unReadMessage:请求未读消息;messageLog:请求聊天记录
            let types = ['init', 'Heartbeat', 'directChat', 'unReadMessage', 'messageLog']
            if (types.includes(msg.flag)){
                if(this.im.socketOpen&&msg){
                    this.im.socketTask.send({
                        data: JSON.stringify(msg)
                    });
                }
                return
            }
            /**
             * 给好友发送消息逻辑
             */
            // 获取好友之间是不是黑名单状态
            if (!this.im.nowChat || !this.im.nowChat.platform_id){
                uni.showToast({
                    title:"没有建立聊天连接",
                    icon:'none'
                })
                return
            }
            let blackStatue = this.getBlack(this.im.nowChat.owner_id, this.im.nowChat.passive_id, this.im.nowChat.platform_id, this.im.myChatInfo.platform_id)
            // console.log("黑名单状态", blackStatue)
            if(blackStatue){
                return
            }
            if(msg==""){
                uni.showToast({
                    title:"请输入内容",
                    icon:"none"
                })
                return
            }
            // 检测敏感词
            if(type === 'text'){
                for (let i = 0; i < this.badwords.length; i++){
                    if (msg.includes(this.badwords[i]) && this.badwords[i]){
                        uni.showToast({
                            title: `发送内容包含敏感词："${this.badwords[i]}"，请删除后发送`,
                            icon: 'none',
                            duration: 3600
                        })
                        return
                    }
                }
            }
            console.log(this.im.myChatInfo)
            let send_msg = {
                flag:'sendMessage',
                user_id: this.im.myChatInfo.user_id,
                from_nickname: this.im.myChatInfo.nickname,
                from_headimage: this.im.myChatInfo.headimage,
                from_id: this.im.myChatInfo.platform_id,
                to_id: this.im.nowChat.platform_id,
                chat_id: this.im.nowChat.chat_id,
                type: type,
                content:msg
            }
            if (!this.im.socketOpen){
                uni.showToast({
                    title:"聊天已断开",
                    icon:'none'
                })
                this.im.socketOpen = false
                return
            }
            if(this.im.socketOpen&&send_msg){
                this.im.socketTask.send({
                    data: JSON.stringify(send_msg),
                    success:()=>{
                        this.saveMsg(send_msg, 1, this.im.chatIndex)
                        this.im.chatIndex = 0
                        this.send_content = ""
                    },
                    fail:err=>{
                        console.log(err)
                        this.saveChatErr(err)
                        uni.showToast({
                            title:'发送失败',
                            icon:'none',
                            mask:true
                        })
                    }
                });
            }else{
                uni.showToast({
                    title:"发送失败,请重试",
                    icon:"none"
                })
            }
        },
        /**
         * 将聊天消息推送到服务器
         * @param {String} chat_id 聊天好友关系id
         * @param {String} type 消息类型
         * @param {String} content 消息内容
         * @param {Number} is_online 对方是否在在线- 1：在线 0：不在线
         * @param {Number} is_auto 是否是对方自动回复的消息 - 1：是 0：不是
         */
        pushMyMsg(chat_id, type, content, is_online, is_auto=0){
            this.$ajax.post('im/chat.html', { chat_id, type, content, is_online, is_auto}, res=>{
                if(res.data.code ==0){
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                      })
                }
            })
        },
        /**
         * 获取自动回复消息
         */
        getAutoReplay(chat_id){
            console.log(chat_id)
            this.$ajax.get('im/getCustomReply.html', { to_id: this.im.nowChat.user_id},res=>{
                if(res.data.code === 1){
                    this.checkMsg({
                        chat_id: chat_id,
                        content: res.data.data.content,
                        from_headimage: this.im.nowChat.headimage,
                        from_nickname: this.im.nowChat.nickname,
                        from_id: this.im.nowChat.platform_id,
                        type: 'text',
                    })
                    // 将自动回复推送到服务器
                    setTimeout(()=>{
                        this.pushMyMsg(chat_id, 'text', res.data.data.content, 1, 1)
                    },1200)
                    this.is_auto_replay = 1
                }
            })
        },
        /**
         * 检查消息接收到的消息是否是当前好友列中的消息
         * @param {Object} msg 接收到的消息
         */
        checkMsg(msg) {
            let chat_id = msg.chat_id
            let friend_index;
            // 检测收到的消息是不是属于当前好友列表中的
            for (let i = 0; i < this.im.friendList.length; i++) {
                if (this.im.friendList[i].chat_id === chat_id) { //后台返回的platformid是字符串,消息的from_id是数字
                    friend_index = i;
                    break;
                }
            }
            // 如果消息不是当前好友列表中的好友发送的
            if (friend_index === undefined) {
                // 将好友插入到好友列表
                let user_id = ""
                if(msg.user_id){
                    user_id = msg.user_id
                }else{
                    user_id = msg.from_id.split('_')[2]
                }
                let friend = { headimage: msg.from_headimage, nickname: msg.from_nickname, chat_id: msg.chat_id, platform_id: msg.from_id, user_id: user_id, time: msg.time, owner_id: 0, passive_id:0, uncount: 0 }
                this.im.friendList.unshift(friend)
                friend_index = 0
            }
            this.saveMsg(msg, 0, friend_index)
        },
        /**
         * 存储消息到本地
         * @param {Object} send_msg 消息内容
         * @param {Number} is_my 是否时自己发送的消息 1:自己,0:好友,-1系统
         * @param {Number} friend_index 好友所在好友列表中的索引值
         */
        saveMsg(send_msg, is_my, friend_index){
            console.log("保存消息", send_msg.content)
            let time = new Date()
            let msg_time = (time.getHours() < 10 ? '0' + time.getHours() : time.getHours()) + ':' + (time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes())
            let msg = { is_my, time: msg_time, ctime: Date.parse(time) / 1000, content: send_msg.content, type: send_msg.type }
            // 如果是聊天页面且是当前回话的消息则将消息添加到当前回话的聊天记录中
            let pages = getCurrentPages()
            let nowPage = pages[pages.length - 1].route
            if ((send_msg.chat_id == this.im.nowChat.chat_id && nowPage === 'chatPage/chat/chat')||is_my===1||is_my===-1) {
                console.log("当前会话")
                this.im.nowChat.chatList.push(msg)
                // console.log(this.im.nowChat.chatList)
                this.$nextTick(() => {
                    // #ifdef MP-BAIDU
                    setTimeout(()=>{
                        uni.$emit('handleScroll')
                    },100)
                    // #endif
                    // #ifndef MP-BAIDU
                    uni.$emit('handleScroll')
                    // #endif
                    
                })
            }else{
                // 显示消息通知
                if (nowPage === 'chatPage/chat/chat'){
                    uni.$emit('showNotice', send_msg)
                }
                if (this.im.friendList[friend_index].uncount){
                    console.log("uncount++")
                    this.im.friendList[friend_index].uncount++
                }else{
                    console.log("uncount=1")
                    this.im.friendList[friend_index].uncount = 1
                }
            }
            if (this.im.friendList.length > friend_index){
                this.im.friendList[friend_index].chat = {
                    type: send_msg.type,
                    content: send_msg.content,
                    time: msg_time
                }
            }

            // 将此此对话从好友列表中移动到第一条
            if (friend_index > 0) {
                let newChat = this.im.friendList.splice(friend_index, 1)
                this.im.friendList.unshift(newChat[0])
            }
        },
        /**
         * 处理黑名单事件消息逻辑
         * @param {Number,String} friend_id 好友id
         */
        handleBlack(friend_id){
            let pages = getCurrentPages()
            let nowPage = pages[pages.length - 1].route
            // 记录对方已把自己拉黑
            for (let i = 0; i < this.im.friendList.length; i++) {
                if (this.im.friendList[i].platform_id === friend_id) { //后台返回的platformid是字符串,消息的from_id是数字
                    this.im.friendList[i].owner_id = friend_id
                    this.im.friendList[i].passive_id = this.im.myChatInfo.platform_id
                    break;
                }
            }
            // 如果是聊天页面，且是当前好友
            if (nowPage === 'chatPage/chat/chat' && this.im.nowChat.platform_id === friend_id) {
                uni.showToast({
                    title: "对方已关闭会话",
                    icon: "none"
                })
                setTimeout(()=>{
                    uni.navigateBack()
                },1500)
            }
            // 如果是好友信息页面且是当前好友
            if (nowPage === 'chatPage/chat/friend_info' && this.im.nowChat.platform_id === friend_id) {
                uni.showToast({
                    title: "对方已关闭会话",
                    icon: "none"
                })
                setTimeout(() => {
                    uni.navigateBack({
                        delta: 2
                    })
                }, 1500)
            }
        },
        /**
         * 处理好友从黑名单把自己恢复到正常好友事件
         * @param {Number,String} friend_id 好友id
         */
        handleRemoveBlack(friend_id){
            for (let i = 0; i < this.im.friendList.length; i++) {
                if (this.im.friendList[i].platform_id === friend_id) { //后台返回的platformid是字符串,消息的from_id是数字
                    this.im.friendList[i].owner_id = 0
                    this.im.friendList[i].passive_id = 0
                    break;
                }
            }
        },
        /**
         * 对方邀请评价
         * @param {Object} e
         */
        handleInviteEvaluate(e){
            let pages = getCurrentPages()
            let nowPage = pages[pages.length - 1].route
            if (e.from_id == this.im.nowChat.platform_id && nowPage === 'chatPage/chat/chat') {
                uni.$emit('onInviteEvaluate',e)
            }
        },
        /**
         * 处理对方申请获取电话号码
         * @param {Object} e 
         */
        handleApplyTel(e){
            if(e.tel){
                // 如果是接收对方发送的手机号
                this.addSystemMessage({ tel: e.tel }, 'giveTel')
            }else{
                // 如果是接收到对方申请获取手机号
                this.addSystemMessage({}, 'applyTel')
            }
            
        },
        // 重新连接socket
        // connectChatAgain() {
        //     this.$ajax.get('im/chatInfo.html', { agent_id: this.user_id }, res => {
        //         this.im.imToken = res.data.imToken || ''
        //         if (!this.im.socketOpen) {
        //             this.handleConnectSocket()
        //             return
        //         }
        //     })
        // },
        /**
         * 关闭socket
         */
        closeSocket(type){
            this.closeType = type
            this.im.socketOpen = false
            this.im.socketTask.close({
                success:()=>{
                    console.log('socket关闭成功')
                    this.im.socketTask = null
                    // uni.showToast({
                    //     title: "聊天已关闭!",
                    //     icon: "none"
                    // })
                },
                fail: err=>{
                    console.log("socket关闭失败", err)
                }
            });
        },
        /**
         * 监听socket错误
         */
        onSocketError(){
            this.im.socketTask.onError( res=> {
                console.log('socket错误:', res)
                // this.im.socketOpen = false
                // uni.showToast({
                //     title: "连接失败",
                //     icon: "none"
                // })
            });
        },
        /**
         * 监听socket关闭事件
         */
        onClose(){
            this.im.socketTask.onClose( res=> {
                this.im.socketOpen = false
                if (this.timer){
                    clearInterval(this.timer)
                }
                if (this.connectPage === 'list') {
                    let pages = getCurrentPages()
                    // if (pages[pages.length - 1].route === "chatPage/chat/list" && this.$refs.socket_colse) {
                    //     this.$refs.socket_colse.show()
                    // }
                    if (pages.length>0&&pages[pages.length - 1].route === "chatPage/chat/chat" && this.$refs.socket_colse) {
                        uni.navigateBack()
                    }
                    // if (this.closeType !=='active'){
                    //     this.$refs.socket_colse.show()
                    // }
                    if (this.closeType !== 'active'&&this.$refs.socket_colse) {
                        this.$refs.socket_colse.show()
                    }
                } else if (this.connectPage === 'chat'){
                    if (this.$refs.socket_colse) {
                        this.$refs.socket_colse.show()
                    }
                }
                console.log('WebSocket 已关闭！', res);
            });
        },
        saveChatErr(err){
            var params = {
                from_id: this.im.myChatInfo.user_id||'',
                from_user: this.im.myChatInfo.nickname||'',
                to_id: this.im.nowChat.user_id||'',
                to_user: this.im.nowChat.nickname||''
            }
            if (Object.prototype.toString.call(err) ==='[object String]'){
                params.content = err
            }
            if (Object.prototype.toString.call(err) ==='[object Object]'){
                params.content = JSON.stringify(err)
            }
            this.$ajax.post('im/chatSendErrorLog', params, res=>{
                console.log(res.data)
            })
        }
    },
}

module.exports = chat