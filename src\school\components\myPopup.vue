<template>
  <view class="popup-page">
    <view
      @touchmove.stop.prevent="stopMove"
      class="popup-box"
      :class="{
        show: show,
        bottom: position == 'bottom',
        top: position == 'top',
        left: position == 'left',
        right: position == 'right',
        center: position == 'center'
      }"
      :style="{ bottom: bottom, height: height }"
    >
      <slot></slot>
    </view>
    <view
      class="mask"
      v-if="show"
      :class="{ show: show && showMask }"
      @click="clickMask"
      @touchmove.stop.prevent="stopMove"
    ></view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    position: {
      type: String,
      default: 'bottom'
    },
    showMask: {
      type: Boolean,
      default: true
    },
    touch_hide: {
      type: Boolean,
      default: true
    },
    bottom: {
      type: [String],
      default: '0'
    },
    height: {
      type: [String],
      default: 'initial'
    }
  },
  methods: {
    hide() {
      this.$emit('hide')
    },
    clickMask() {
      if (!this.touch_hide) {
        return
      }
      this.hide()
    },
    stopMove() {}
  }
}
</script>

<style lang="scss">
.popup-page {
  .popup-box {
    position: fixed;
    overflow-x: hidden;
    // background-color: #fff;
    z-index: 98;
  }
  .popup-box.bottom {
    width: 100%;
    bottom: 0;
    max-height: 100vh;
    transform: translateY(100%);
    transition: 0.3s;
  }
  .popup-box.bottom.show {
    transform: translateY(0);
  }
  .popup-box.top {
    width: 100%;
    // top: 0;
    /* #ifdef H5 */
    top: 0;
    /* #endif */
    /* #ifndef H5 */
    top: var(--window-top);
    /* #endif */
    max-height: 100vh;
    transform: translateY(-100%);
    transition: 0.4s;
  }
  .popup-box.top.show {
    transform: translateY(0);
  }
  .popup-box.center {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    opacity: 0;
    z-index: -1;
    transition: 0.26s;
  }
  .popup-box.center.show {
    opacity: 1;
    z-index: 98;
    transform: translateY(0);
  }
  .popup-box.left {
    top: 0;
    bottom: 0;
    left: 0;
    max-width: 60vw;
    min-width: 30vw;
    transform: translateX(-100%);
    transition: 0.3s;
  }
  .popup-box.left.show {
    transform: translateX(0);
  }
  .popup-box.right {
    top: 0;
    bottom: 0;
    right: 0;
    max-width: 60vw;
    min-width: 30vw;
    transform: translateX(100%);
    transition: 0.3s;
  }
  .popup-box.right.show {
    transform: translateX(0);
  }
  .mask {
    position: fixed;
    top: 0;
    height: 100vh;
    width: 100%;
    background-color: rgba($color: #000000, $alpha: 0);
    z-index: -1;
    transition: 0.3s;
  }
  .mask.show {
    background-color: rgba($color: #000000, $alpha: 0.5);
    z-index: 90;
  }
}
</style>
