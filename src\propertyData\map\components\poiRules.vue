<template>
<view>
    <my-popup ref="sub_form_popup" position="center"  :touch_hide="false">
        <view class="poi_box" >
            <view class="sub_header">
                <view class="sub_title">{{title}}</view>
            </view>
            <view class="form_box">
                <view class="sub_content">{{content}}</view>
                <view class="sub_form flex-box">
                    <view class="btn-box" @click="subData()">
                          {{sub_submit}}  
                        <!-- <button class="default" @click="subData()"></button> -->
                    </view>
                </view>
            </view>

        </view>
    </my-popup>
</view>
</template>

<script>
import myPopup from '@/components/myPopup.vue'
import myIcon from '@/components/myIcon.vue'
export default {
    data() {
        return {
          content:'楼盘POI密度计算为楼盘3公里周边的生活便利配套，数据来源于腾讯百度地图供应商。POI密度越高，表明3公里范围便民生活配套越成熟。由于城市建筑新增和拆除及企业新建和倒闭等数据具有随机波动性，数据可能存在延时更新情况，平台数据与算法得出的结果可能不能即时与实际完全一致，数据仅供参考，最终以开发商提供的数据为准。',
         
        }
    },
    props: {
        sub_submit: {
            type: [String],
            default: '关闭'
        },
        title: {
            type: [String],
            default: 'POI配套密度声明'
        },
        
    },

    components: {
        myIcon,
        myPopup,
    },

    methods: {
        showPopup() {
          this.$refs.sub_form_popup.show()
        },
        hide(){
            this.$refs.sub_form_popup.hide()
        },
        subData(){
          this.$emit("close")
          this.hide()
        }
    }
}
</script>

<style scoped lang="scss">
.sub_header{
      padding: 0 48rpx 24rpx;
      // color: #fff;
      // background-image: linear-gradient(-41deg, #F7918F 0%, #FB656A 100%);
      position: relative;
      // border-top-left-radius: 16rpx;
      // border-top-right-radius: 16rpx;
      .sub_title{
        font-family: PingFangSC-Medium;
        font-size: 18px;
        color: #0E0B0F;
        font-weight: 600;
    }
}

  .sub_content{
    font-size: 24rpx;
    // margin-top: 48rpx;
    line-height: 1.5;
    color: #666666;
  }
  .poi_box {
    background: #fff;
    position: absolute;
    height: 620rpx;
    width: 80vw;
    padding: 48rpx;
    top: 50%;
    left: 10vw;
    border-radius: 16rpx;
    margin-top: -310rpx;
    box-sizing: border-box;
    
    
  }
  .sub_form {
    justify-content: center;
  }

   
    .btn-box{
      display: inline-block;
      background-image: linear-gradient(90deg, #FFA857 0%, #FF6069 100%);
      box-shadow: 0px 6rpx 12rpx 0 rgba(255,109,102,0.3);
      border-radius: 8rpx;
      padding: 20rpx 56rpx;
      margin-top: 60rpx;
      font-size: 32rpx;
      color: #FFFFFF;
      
    }

</style>
