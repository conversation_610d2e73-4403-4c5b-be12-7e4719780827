<template>
  <view class="page">
    <view class="map-box">
      <!-- #ifndef H5 -->
      <map
        class="qqmap"
        :longitude="center.longitude"
        :latitude="center.latitude"
        :markers="school_point"
        :polygons="polygons"
        @markertap="onClickMarker"
      ></map>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <qqMap
        :mapkey="qqmapkey"
        :scale="15"
        :longitude="center.longitude"
        :latitude="center.latitude"
        :markers="school_point"
        :polygons="polygons"
        @markertap="onClickMarker"
      ></qqMap>
      <!-- #endif -->
    </view>
    <view class="range_info">
      <view class="range_title">
        <text>施教范围</text>
        <view
          class="btn"
          @click="toSchool()"
        >
          <!-- <text style="font-size:38rpx">-</text> -->
          <text>详细</text>
        </view>
      </view>
      <view class="range_content">
        <!-- <view class="range_row">
          <view class="label">招生时间:</view>
          <view class="value">{{ range_content.time }}</view>
        </view> -->
        <view class="range_row" v-if="range_content.target">
          <view class="label">招生对象:</view>
          <view class="value">{{ range_content.target }}</view>
        </view>
        <view class="range_row" v-if="range_content.target_cpunt">
          <view class="label">招生人数:</view>
          <view class="value">{{ range_content.target_cpunt }}</view>
        </view>
        <view class="range_row" v-if="range_content.descp">
          <view class="label">对口地段:</view>
          <view class="value">{{ range_content.descp }}</view>
        </view>
      </view>
    </view>
    <!-- #ifndef MP-WEIXIN -->
    <login-popup ref="login_popup" @onclose="handleCloseLogin" sub_content="当前服务需要会员登录后查看" :login_success_tip="false" @success="onLoginSuccess()"></login-popup>
    <!-- #endif -->
    <!-- 查询次数 邀请好友-->
    <myPopup :show="show_invit" position="top" @hide="show_invit = false">
      <invitePop invite title="温馨提示" :tip="user_status_tip">
        <button class="up_vip" v-if="userLoginStatus===0" @click="toLogin()">去登录</button>
        <button class="up_vip" v-else @click="toUpVip()">去开通</button>
      </invitePop>
    </myPopup>
  </view>
</template>

<script>
// #ifdef H5
import qqMap from '@/components/qqMap'
// #endif
// #ifndef MP-WEIXIN
import loginPopup from '../components/loginPopup'
// #endif
import myPopup from './components/myPopup'
import invitePop from './components/invitePop'
import {navigateTo} from '../common/index'
import wxApi from '../common/mixin/wx_api';
export default {
  components: { 
    // #ifdef H5
    qqMap,
    // #endif
    // #ifndef MP-WEIXIN
    loginPopup,
    // #endif
    myPopup,
    invitePop
  },
  mixins:[wxApi],
  data() {
    return {
      active: 0,
      show_invit:false,
      center: {
        longitude: '',
        latitude: ''
      },
      qqmapkey:__uniConfig.qqMapKey,
      school_point: [],
      polygons: [],
      range_content: {},
      user_status_tip:"当前服务需要开通VIP后查看",
      userLoginStatus:null
    }
  },
  onLoad(options) {
    if (options.school_id) {
      this.school_id = options.school_id
      this.getData()
      this.getRangeTex()
    }
  },
  onShow(){
    if(this.isToUpVip){
      this.isToUpVip = false
      this.getUserStatus()
    }
  },
  methods: {
    switchTab(index) {
      if (index === this.active || index === 0) {
        this.active = 0
      } else {
        this.active = index
      }
    },
    getData() {
      this.$ajax.get('school/schoolInfo.html', { id: this.school_id }, res => {
        setTimeout(()=>{
          this.getUserStatus()
        },500)
        if (res.data.code === 1) {
          this.center = {
            latitude: res.data.data.lat,
            longitude: res.data.data.lng
          }
          this.school_point = [
            {
              id: this.id,
              school_id: this.id,
              iconPath: 'https://images.tengfangyun.com/images/icon/xiaoxuequ.png',
              latitude: res.data.data.lat,
              longitude: res.data.data.lng,
              width: 32,
              height: 32,
              callout: {
                content: res.data.data.name,
                padding: 10,
                borderRadius: 8
              }
            }
          ]
          if (!res.data.data.teach_range) {
            this.polygons = []
            return
          }
          const color = '#f65354'
          this.polygons = res.data.data.teach_range.map(items => {
            const points = items.paths.map(item => {
              return {
                latitude: parseFloat(item.latitude),
                longitude: parseFloat(item.longitude)
              }
            })
            return {
              id: items.id,
              points,
              strokeWidth: 1,
              strokeColor: color,
              fillColor: `${color}33`
            }
          })
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    // 获取用户账号状态
    getUserStatus(){
      this.$ajax.get('school/checkUserInfo',{},res=>{
        this.userLoginStatus = res.data.loginStatus
        if(this.userLoginStatus===0){
          this.$refs.login_popup.closeSub()
          this.user_status_tip = "当前服务需要会员登录后查看"
          this.show_invit = true
        }
        if(this.userLoginStatus === 1){
          this.$refs.login_popup.closeSub()
          this.user_status_tip = "您的账号已被封禁，请开通个人VIP后查看"
          this.show_invit = true
        }
        if(this.userLoginStatus === 2){
          this.$refs.login_popup.closeSub()
          this.user_status_tip = "当前服务需要开通个人VIP后查看"
          this.show_invit = true
        }
        if(this.userLoginStatus===3){
          this.$refs.login_popup.closeSub()
          this.show_invit = false
        }
      })
    },
    // 登录成功的回调事件
    onLoginSuccess(){
      // 重新获取用户账号状态
      this.getUserStatus()
    },
    // 关闭登录窗口的事件
    handleCloseLogin(){
      // 如果是登录状态怎不做任何处理
      // if(this.userLoginStatus !==0){
      //   return
      // }
      // let pages = getCurrentPages() 
      // if(pages.length>1){
      //   uni.navigateBack()
      // }else{
      //   uni.switchTab({
      //     url:'/'
      //   })
      // }
    },
    toUpVip(){
      this.isToUpVip = true
      navigateTo('/user/member_upgrade?is_personal=1')
    },
    toLogin(){
      uni.removeStorageSync('token')
      navigateTo('/user/login/login')
    },
    getRangeTex() {
      this.$ajax.get(
        'school/schoolRange.html',
        { id: this.school_id },
        res => {
          if (res.data.code === 1) {
            this.range_content = res.data.data
          }
          if(res.data.share){
            this.share = res.data.share
            this.getWxConfig()
          }
        }
      )
    },
    onClickMarker() {
      console.log(this.id)
    },
    toSchool(){
      let pages = getCurrentPages() 
      if(pages.length>1&&pages[pages.length-2].route==='school/detail'){
        uni.navigateBack()
      }else{
        navigateTo(`/school/detail?id=${this.school_id}`)
      }
    }
  }
}
</script>

<style scoped lang="scss">
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.map-box {
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  transition: 0.3s;
  .qqmap {
    width: 100%;
    height: 100%;
  }
}
.range_info {
  position: fixed;
  width: 100%;
  bottom: 0;
  z-index: 10;
  padding: 30rpx;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  background-color: #fff;
  .range_title {
    position: relative;
    font-size: $uni-font-size-lg;
    padding: 0 10rpx;
    margin-bottom: 30rpx;
    border-left: 4rpx solid $uni-color-primary;
    flex-direction: row;
    .btn {
      position: absolute;
      right: 0;
      top: 0;
      flex-direction: row;
      height: 52rpx;
      line-height: 52rpx;
      padding: 0 15rpx;
      border-radius: 22rpx;
      font-size: $uni-font-size-sm;
      background-color: #fef2ee;
      color: $uni-color-primary;
    }
  }
  .range_row {
    padding: 10rpx 0;
    flex-direction: row;
    font-size: 28rpx;
    line-height: 1.5;
    .label {
      color: #999999;
      margin-right: 5rpx;
    }
    .value {
      margin-left: 10rpx;
      flex: 1;
    }
  }
}
.up_vip{
  width: 508rpx;
  height: 72rpx;
  line-height: 72rpx;
  background: #ff6735;
  color: #fff;
  border-radius: 50rpx;
  margin: 60rpx auto;
  font-size: 32rpx;
  background-image: linear-gradient(to left, #fb8a65, #ff6735);
}
</style>
