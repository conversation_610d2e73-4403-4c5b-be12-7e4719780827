<template>
	<view class="comment-lists" :class="{'overhide':isComment}">
		<comment-list :listData="comment_list" :showMore="false" @clickReply="toReply" @delComment="delComment"></comment-list>
		<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
		<view class="comment-box top-line">
			<view class="flex-1 textarea-box">
				<textarea :value="comment_content" fixed :focus="focus" @focus="handleFocus()" @blur="handleBlur()" :show-confirm-bar="false" :placeholder="comment_placeholder" :cursor-spacing="15" @input="inputContent" />
			</view>
			<view class="btn-box">
				<button class="small plain" @click="sendComment">发表评论</button>
			</view>
		</view>
		<chat-tip></chat-tip>
	</view>
</template>

<script>
	import commentList from '../../components/commentList.vue'
	import {uniLoadMore} from '@dcloudio/uni-ui'
	import {isIos} from '../../common/index'
	export default {
		components:{
			commentList,
			uniLoadMore
		},
		data() {
			return {
				comment_list:[],
				api:"",
				page:1,
				rows:20,
				focus:false,
				comment_content:"",
				comment_placeholder:"请输入评论内容",
				get_status:"loading",
				content_text:{
					contentdown:"",
					contentrefresh:"正在加载...",
					contentnomore:"没有更多数据了"
				},
				isComment:false,
				scrollTop:0
			}
		},
		onLoad(options){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			if(options.id){
				this.id = options.id
			}else{
				return
			}
			switch(options.type){
				case '1':
				this.api = "news/newsCommentList.html"
				case '2':
				this.api = 'news/getReplyList.html'
				break
				default:
				this.api = 'news/newsCommentList.html'
			}
			this.getData()
		},
		methods: {
			getData(){
				if(this.page==1){
					this.comment_list = []
				}
				this.get_status = "loading"
				this.$ajax.get(this.api,{id:this.id,page:this.page,rows:this.rows},res=>{
					if(res.data.code == 1){
						this.comment_list = this.comment_list.concat(res.data.list)
						res.data.list.length<15?this.get_status = "noMore":this.get_status = "more"
					}else{
						this.get_status = "noMore"
						this.page>1?this.page--:this.page=1
					}
				},err=>{
					this.get_status = "noMore"
					this.page>1?this.page--:this.page=1
				})
			},
			inputContent(e){
				this.comment_content = e.detail.value
			},
			toReply(e){
				console.log(e)
				this.is_reply = true; //回复中
				this.replyParentIndex = e.parentIndex
				this.be_commentid = e.be_commentid
				this.comment_placeholder="回复:"+e.be_reply
				if(isIos()){
					this.isComment = true
				}
				this.$nextTick(()=>{
					this.focus = true;
				})
			},
			handleBlur(){
				this.focus=false
				this.isComment = false
				setTimeout(()=>{
					if(isIos){
						uni.pageScrollTo({
							scrollTop: this.scrollTop,
							duration: 0
						});
					}
				},30)
			},
			handleFocus(){
				this.focus=true
				if(isIos()){
					this.isComment = true
				}
			},
			sendComment(){
				if(!this.comment_content){
					uni.showToast({
						title:"请输入评论内容",
						icon:"none"
					})
					return
				}
				if (this.isSubmiting) return 
        this.isSubmiting =true
				if(this.is_reply){
					this.$ajax.post('news/commentReply.html',{id:this.be_commentid,content:this.comment_content},res=>{
						if(res.data.code == 1){
							this.comment_list[this.replyParentIndex].children.unshift(res.data.comment)
							this.init_input()
						}
						uni.showToast({
							title:res.data.msg,
							icon:res.data.code==1?'success':'none',
							mask:true
						})
						this.isSubmiting =false
					})
				}else{
					this.$ajax.post('news/comment.html',{id:this.id,content:this.comment_content},res=>{
						if(res.data.code == 1){
							this.comment_list.unshift(res.data.comment)
							this.init_input()
						}
						uni.showToast({
							title:res.data.msg,
							icon:res.data.code==1?'success':'none',
							mask:true
						})
						this.isSubmiting =false
					})
				}
			},
			delComment(e){ //删除评论
				let {parentIndex,index} = e
				let id
				if(index==undefined){
					id = this.comment_list[parentIndex].id
				}else{
					id = this.comment_list[parentIndex].children[index].id
				}
				this.$ajax.get('news/deletedComment',{id},res=>{
					console.log(res.data)
					if(res.data.code == 1){
						if(index==undefined){
							this.comment_list.splice(parentIndex,1)
						}else{
							this.comment_list[parentIndex].children.splice(index,1)
						}
					}
					uni.showToast({
						title:res.data.msg,
						icon:res.data.code==1?'success':'none'
					})
				})
			},
			init_input() {
				this.comment_content = "";
				this.is_reply = false;
				this.comment_placeholder="请输入评论内容";
				this.focus = false;
			},
		},
		onPageScroll(e){
			if(isIos&&!this.isComment){
				this.scrollTop = e.scrollTop
			}
		},
		onReachBottom() {
			this.page++
			this.getData()
		}
	}
</script>

<style lang="scss">
	.overhide{
		position: absolute;
		width: 100%;
		top: 0;
		bottom: 0;
		overflow: hidden;
	}
	.comment-lists{
		background-color: #fff;
		padding-bottom: 110upx;
	}
	.row-title .uni-list-item__content-title{
		font-size: 36upx;
		font-weight: bold;
		color: #555
	}
	.comment-box{
		position: fixed;
		display: flex;
		align-items: flex-end;
		width: 100%;
		bottom: 0;
		background-color: #fff;
		.btn-box{
			display: flex;
			justify-content: flex-end;
			margin-top: 0;
			.small{
				margin: initial
			}
		}
	}
	.textarea-box{
		padding: 24upx;
		height: 94upx;
		box-sizing: border-box;
		textarea{
			width:100%;
			height:46upx;
			line-height: 1.5;
			background-color: #fff
		}
	}
</style>
