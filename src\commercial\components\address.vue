<!-- 本插件只是个框架,适用于地址数据要从后台逐级获取的需求 -->
<!-- 可对照注释根据业务需求更改 -->
<!-- 作者：qq315500033 -->


<template>
	<view>
		<!-- 选择地址展示 -->
		
		<!-- 选择地址模态框 -->
		<view class="jm-modal" :class="showFlag==true?'show1':''">
			<view class="dialog">
				<!-- 省份列表 -->
				<view class="addList">
					<view v-for="(item,index) in addressd" :key='index' :class="{check:item.areaid==co}" @tap="clickAddress(1,item.areaid,item.areaname,item.children)">
						{{item.areaname}}
					</view>
				</view>
				<!-- 城市列表 -->
				<view class="addList">
					<view v-for="(item,index) in addressd2" :key='index' :class="{check:item.areaid==ct}" @tap="clickAddress(2,item.areaid,item.areaname,item.children)">
						{{item.areaname}}
					</view>
				</view>
				<!-- 地区列表 -->
				<view class="addList">
					<view v-for="(item,index) in addressd3" :key='index' :class="{check:item.areaid==cs}" @tap="clickAddress(3,item.areaid,item.areaname)">
						{{item.areaname}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	var _self;
	export default {
		data() {
			return {
				//模态框状态
				showFlag: false,
				// 省份列表
				addressd1: [],
				// 城市列表
				addressd2: [],
				// 地区列表
				addressd3: [],
				//省份id 默认为64，可根据想要默认展示的id自行更改
				co: '',
				//默认省份名称
				coname: '',
				//城市id 默认为64，可根据想要默认展示的id自行更改
				ct: '',
				//默认城市名称
				ctname: '',
				//地区id 默认为575，可根据想要默认展示的id自行更改
				cs: '',
				//默认地区名称
				csname: ''
			};
		},
		props: {
			//载入的标签数据
			addressd: Array
		},
		mounted() {
			_self = this;
			
			//默认获取省份列表
			// this.getadd(1, 0);
			//默认获取城市列表
			// this.getadd(2, this.co);

			// // 声明默认地址,并传送给父组件
			// this.emitData();

		},
		methods: {
			//呼出模态框
			showAddress() {
				// 呼出模态框
				this.showFlag = true
				this.$forceUpdate()
			},
			// 关闭模态框
			hideAddress() {
				// 关闭模态框
				this.showFlag = null;
				// 声明默认地址,并传送给父组件
				this.emitData();

			},
			// 声明默认地址,并传送给父组件
			emitData() {
				_self.data = {
					province: _self.coname,
					city: _self.ctname,
					district: _self.csname,
					province_id:_self.co,
					city_id:_self.ct,
					district_id:_self.cs
				}
        console.log(_self.data)
			},
			// 模态框地址点击赋值并获取下一级
			// --flag--- 1(省份点击);2(城市点击);3(地区点击)；
			// --id----(点击的地址id);
			// --name--(点击的地址名称);
			clickAddress(flag, id, name,children) {
				//判断点击的状态
				switch (flag) {
					case 1:
						_self.addressd2=[]
						_self.addressd3=[]
						_self.coname = name;
						_self.co = id;
						_self.ct=''
						_self.cs=''
						_self.ctname=''
						_self.csname=''
						if (children && children.length > 0){
							_self.getadd(2, id);
						}else {
							_self.emitData();
							_self.$emit("changes", _self.data);
							_self.hideAddress();
						}
						
						break;
					case 2:
						_self.ctname = name;
						_self.ct = id;
						_self.cs=''
						_self.addressd3=[]
						_self.csname=''
						if(children){
							_self.getadd(3, id);
						}else{
							_self.emitData();
							_self.$emit("changes", _self.data);
							_self.hideAddress();
						}
						break;
					case 3:
						_self.csname = name;
						_self.cs = id;
						_self.emitData();
						_self.$emit("changes", _self.data);
						_self.hideAddress();
						break;
					default:
						return;
				}

			},
			getadd(flag, id) {
				switch (flag) {
					case 1:
						_self.addressd1 = _self.addressd;
						break;
					case 2:
						var newArr = [];
						for (var i = 0; i < _self.addressd.length; i++) {
							if (_self.addressd[i].areaid == id) {
								newArr = _self.addressd[i].children
							}
						}
						_self.addressd2 = newArr;
            if (newArr.length > 0) {
              _self.ctname = newArr[0].areaname;
						  _self.ct = newArr[0].areaid;
            }
						break;
					case 3:
						var zhenArr=[]
						for (var i = 0; i < _self.addressd2.length; i++) {
							if (_self.addressd2[i].areaid == id) {
								zhenArr = _self.addressd2[i].children
							}
						}
						_self.addressd3 = zhenArr;
						_self.csname = zhenArr[0].areaname;
						_self.cs = zhenArr[0].areaid;
					default:
						return;
				}
			}
		}
	}
	
</script>

<style>
	.choice {
		background-color: #fff;
		padding: 20upx;
		font-size: 28upx;
	}

	.addList {

		box-sizing: border-box;
		overflow-y: scroll;
		width: 31%;
		float: left;
		margin-left: 1%;
		margin-right: 1%;
		font-size: 28upx;
	}

	.addList view {
		height: 70upx;
		line-height: 70upx;
		width: 100%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.check {
		color: #f65354;
		background-color: #fff;
	}

	.jm-modal {
		position: relative;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 1110;
		opacity: 0;
		outline: 0;
		text-align: center;
		/* -ms-transform: scale(1.185);
		transform: scale(1.185); */
		backface-visibility: hidden;
		perspective: 2000upx;
		background: rgba(0, 0, 0, 0.6);
		/* transition: all 0.3s ease-in-out 0s; */
		pointer-events: none;
	}

	.jm-modal::before {
		content: "\200B";
		display: inline-block;
		height: 100%;
		vertical-align: middle;
	}

	.show1 {
		opacity: 1;
		transition-duration: 0.3s;
		-ms-transform: scale(1);
		transform: scale(1);
		overflow-x: hidden;
		overflow-y: auto;
		pointer-events: auto;
	}

	.dialog {
		position: relative;
		display: inline-block;
		vertical-align: middle;
		margin-left: auto;
		margin-right: auto;
		width: 100%;
		background-color: #ffffff;
		overflow: hidden;
	}

	.action {
		position: absolute;
		right: 30upx;
	}

	.content {
		position: absolute;
		text-align: center;
		width: calc(100% - 340upx);
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		margin: auto;
		height: 60upx;
		font-size: 32upx;
		line-height: 60upx;
		cursor: none;
		pointer-events: none;
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	}

	.showBox {
		background-color: #fff;
		display: flex;
		position: relative;
		align-items: center;
		min-height: 100upx;
		justify-content: space-between;
	}
</style>
