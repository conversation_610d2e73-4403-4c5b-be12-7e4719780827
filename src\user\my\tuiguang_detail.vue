<template>
    <view class="tuiguang_page">
        <view class="header">
            <span class="title" v-if="item_type != 3">设为{{ pageType }}房源</span>
            <span class="title" v-else>设置房源有效期</span>
            <span class="icon">进行中</span>
        </view>
        <view class="tgmask">信息标题：<span class="tgmask">{{ myonehouse.title }}</span></view>
        <template v-if="item_type == 1">
            <swiper class="banner" :indicator-dots="zdgroupedItems.length > 1 ? true : false" :circular="true"
                :duration="300" :autoplay="false">
                <template v-for="(group, groupIndex) in zdgroupedItems">
                    <swiper-item :key="groupIndex">
                        <view class="options">
                            <view class="optionsitem" v-for="(item, index) in group" :key="index"
                                @click="selectItem(item, index)" :class="{ selected: zhidingId === item.id }">
                                <view class=" itemtitle">
                                    <span v-if="item.unit">{{ pageType }}</span>
                                    <span>{{ item.name }}</span>
                                    <span>{{ item.unit }}</span>
                                </view>
                                <view class="itemcontent"">
                                <span class=" money">{{ formatTemplateMoney(item.money) }}</span>

                                    <view class="money" v-if="item.type === 'meal'">{{ item.title }}</view>
                                    <span class="itemmask" v-else>金币</span>
                                </view>
                            </view>
                        </view>
                    </swiper-item>
                </template>

            </swiper>
        </template>
        <template v-else-if="item_type == 2">
            <swiper class="banner" :indicator-dots="jxgroupedItems.length > 1 ? true : false" :circular="true"
                :duration="300" :autoplay="false">
                <template v-for="(group, youxiaoqiIndex) in jxgroupedItems">
                    <swiper-item :key="youxiaoqiIndex">
                        <view class="options">
                            <view class="optionsitem" v-for="(item, index) in group" :key="index"
                                @click="selectItemjx(item, index)" :class="{ selected: jingxuanId === item.id }">
                                <view class=" itemtitle">
                                    <span v-if="item.unit">{{ pageType }}</span>
                                    <span v-else>自动刷新</span>
                                    <span>{{ item.name }}</span>
                                    <span v-if="!item.type">次</span>
                                </view>
                                <view class="itemcontent"">
                                <span class=" money">{{ formatTemplateMoney(item.money) }}</span>
                                    <view class="money" v-if="item.type === 'meal'">{{ item.title }}</view>
                                    <span class="itemmask" v-else>金币</span>
                                </view>
                            </view>
                        </view>
                    </swiper-item>
                </template>

            </swiper>
        </template>
        <template v-else>
            <swiper class="banner" :indicator-dots="youxiaoqiItems.length > 1 ? true : false" :circular="true"
                :duration="300" :autoplay="false" v-if="youxiaoqi">
                <template v-for="(group, jxgroupIndex) in youxiaoqiItems">
                    <swiper-item :key="jxgroupIndex">
                        <view class="options">
                            <view class="optionsitem" v-for="(item, index) in group" :key="index"
                                @click="selectItemyxq(item, index)"
                                :class="{ selected: select_active_time_value === item.id }">
                                <view class=" itemtitle">
                                    <span>{{ item.name }}</span>
                                </view>
                                <view class="itemcontent"">
                                    <!-- <view class=" money" v-if="youxiaoqiHasMoney">￥{{ item.money }}</view> -->
                                    <view class=" money">￥{{ formatTemplateMoney(item.money) }}</view>
                                </view>
                            </view>
                        </view>
                    </swiper-item>
                </template>

            </swiper>
        </template>
        <template v-if="item_type == 3">
            <view class="tip_box" v-if="!shangjiadesc">
                <view class="tip" v-if="houseyxt">
                    提示：<span class="zdtime">{{ houseyxt }}</span>，您可以选择增加有效时间。
                </view>
            </view>
            <!-- <view class="tip_box" v-else>
                <view class="tip" v-if="shangjiadesc">
                    提示：<span class="zdtime">{{ shangjiadesc }}</span>，您可以选择支付方式。
                </view>
            </view> -->
            <view class="tgday" v-if="youxiaoqi">已选择：{{ pageType }} <span class="tgtype pr">{{ item_day }}</span>
                <span v-if="item_time">有效期至</span><span class="tgtype">{{ item_time }}</span>
            </view>
        </template>
        <template v-else>
            <view class="block">
                <view class="open_menu flex-row" @click="addVerification()">
                    <view class="flex-row">
                        <span class="tyicon">{{ pageType }}</span> <text>{{ pageType }}房源权益描述</text>
                    </view>
                    <my-icon type="ic_into" color="#999"></my-icon>
                </view>
            </view>
            <view class="tip_box" v-if="item_type == 1">

                <view class="tip" v-if="top_tip.upgrade_type == 2">
                    提示：当前置顶推广有效期至<span class="zdtime">{{ top_tip.upgrade_time }}</span>，您可以选择增加推广时间。
                </view>
                <view class="tip" v-else>提示：置顶信息，排名靠前，请选择置顶天数</view>
                <view class="tgday">已选择：{{ pageType }} <span class="tgtype pr">{{ item_day }}</span>
                    <span v-if="item_time">有效期至</span><span class="tgtype">{{ item_time }}</span>
                </view>
            </view>
            <view class="tip_box" v-else-if="item_type == 2">

                <view class="tip" v-if="jingxuan_num">
                    当前账户可免费设置精选房源
                    <span class="zdtime"> {{ jingxuan_num.allow_num }}</span>
                    条；已设置
                    <span class="zdtime"> {{ jingxuan_num.use_num }}</span>
                    条；剩余
                    <span class="zdtime"> {{ jingxuan_num.surplus_num }}</span>
                    条。
                    <!-- <template v-if="freeSelected">；免费精选刷新次数： <span class="zdtime"> {{ freeSelected.name }}</span>次。</template>
        <template v-else>。</template> -->

                </view>
                <view class="tip" v-else>提示：自动刷新时间为06:00~24:00 每30分钟刷新一次</view>
                <view class="tgday">已选{{ pageType }}：自动刷新 <span class="tgtype">{{ jingxuancount }}</span>{{ jingxuanunit }}
                </view>
            </view>
        </template>

        <!-- 支付方式选择布局 -->
        <view class="payment-select" v-if="showPaymentSelect" @click="showPaymentPopup">
            <view class="payment-select-left">
                <text>支付方式</text>
                <view class="payment-method">
                    <my-icon 
                        v-if="selectedPaymentMethod === 'wxpay'"
                        type="wx" size="48rpx" color="#00b42a">
                    </my-icon>
                    <my-icon 
                        v-else
                        type="jinbi1" size="48rpx" color="#FAE97D">
                    </my-icon>
                    <text>{{ selectedPaymentMethod === 'gold' ? '金币支付' : '微信支付' }}</text>
                    <text 
                        class="amount">
                        {{requiredGold}}{{ selectedPaymentMethod === 'gold' ? '个' : '元' }}
                    </text>
                </view>
            </view>
            <view class="payment-select-right">
                <my-icon type="ic_open" size="24" color="#C8C9CC"></my-icon>
            </view>
        </view>

        <view class="agreement flex-box">
            <view class="check_box" @click="agree_agreement = !agree_agreement">
                <my-icon v-if="agree_agreement" type="ic_xuanze" color="#fb656a"></my-icon>
                <text v-else class="no_checked"></text>
            </view>
            <view class="agreement_title">
                <text>同意</text>
                <text style="color: gray;" @click="$navigateTo('/user/agreement?type=payGuide')">《推广服务协议》</text>
            </view>
        </view>
        <view style="height: 120rpx;">
            <view class="btn_box" @click="submitForm()">
                <view class="btn_qd">确定</view>
            </view>
        </view>

        <!-- 支付方式选择弹窗 -->
        <my-popup ref="paymentPopup" position="bottom">
            <view class="payment-popup">
                <view class="payment-header">
                    <text>请选择支付方式</text>
                    <text class="close" @click="closePaymentPopup">取消</text>
                </view>
                <view class="payment-options">
                    <template v-if="hasEnoughGold">
                        <view class="payment-item" @click="selectPaymentMethod('gold')">
                            <view class="left">
                                <my-icon type="jinbi1" size="48rpx" color="#FAE97D"></my-icon>
                                <text class="name">金币支付</text>
                                <text class="amount">余额{{user_info.money_own}}个 <text class="reduce">本次扣除{{requiredGold}}个</text></text>
                            </view>
                            <view class="right">
                                <my-icon v-if="selectedPaymentMethod === 'gold'" type="wancheng" color="#000000"></my-icon>
                            </view>
                        </view>
                        
                        <view class="payment-item" @click="selectPaymentMethod('wxpay')">
                            <view class="left">
                                <my-icon type="wx" size="48rpx" color="#00b42a"></my-icon>
                                <text class="name">微信支付</text>
                                <text class="amount">支付金额 {{payAmount}} 元</text>
                            </view>
                            <view class="right">
                                <my-icon v-if="selectedPaymentMethod === 'wxpay'" type="wancheng" color="#000000"></my-icon>
                            </view>
                        </view>
                    </template>
                    
                    <template v-else>
                        <view class="payment-item" @click="selectPaymentMethod('wxpay')">
                            <view class="left">
                                <my-icon type="wx" size="48rpx" color="#00b42a"></my-icon>
                                <text class="name">微信支付</text>
                                <text class="amount">支付金额 {{payAmount}} 元</text>
                            </view>
                            <view class="right">
                                <my-icon v-if="selectedPaymentMethod === 'wxpay'" type="wancheng" color="#000000"></my-icon>
                            </view>
                        </view>
                        
                        <view class="payment-item disabled">
                            <view class="left">
                                <my-icon type="jinbi1" size="48rpx" color="#FAE97D"></my-icon>
                                <text class="name">金币支付</text>
                                <text class="amount">余额 {{user_info.money_own}} 个 <text class="reduce">本次扣除{{requiredGold}}个</text></text>
                            </view>
                            <view class="right">
                                <text class="insufficient">金币不足</text>
                            </view>
                        </view>
                    </template>
                </view>
            </view>
        </my-popup>
    </view>
</template>

<script>
import myIcon from '../../components/myIcon.vue'
import { showModal, navigateTo, formatImg, formatMoney } from "../../common/index.js"
import myPopup from "../../components/myPopup"
import {
	mapState,
	mapMutations
} from 'vuex'
export default {
    components: {
        myIcon,
        myPopup
    },
    data() {
        return {
            youxiaoqi: false,
            shangjiadesc: '',
            houseyxt: '',
            sucessdesc: '',
            catid: '',
            parentid: '',
            pay_type: 'wechatpay',
            paymentStatus: 'PENDING',
            pollingInterval: null,
            jingxuan_type: '',//是否是购买的套餐
            zhiding_type: '',//是否是购买的套餐

            agree_agreement: true,
            item_day: '',
            item_time: '',
            pageType: '',
            selectedDays: 30,
            paymentMethod: "",
            agreedToTerms: false,
            prices: {
                days30: 30,
                days60: 60,
                days90: 90,
            },
            totalCost: 0,
            item_id: '',
            item_type: 0,
            item_upgrade_time: '',
            zhidingId: '',
            zhidingInfo: {
                id: '',
                title: '',
                items: []
            },
            jingxuanId: -1,
            select_active_time_value: '',//选择有效期
            active_time: [],//有效期
            youxiaoqiHasMoney: false,
            jingxuancount: 0,
            jingxuanunit: '',
            jingxuanInfo: {
                id: '',
                money_own: "",
                money: "",
                title: "",
                items: []
            },
            jingxuan_num: {
                allow_num: 0,
                use_num: 0,
                surplus_num: 0
            },
            freeSelected: {},
            h5WxPayApi: "",
            h5AliPayApi: "",
            mpWxPayApi: "",
            pay_params: {},
            isActive: false,
            geturl: '',
            tgmask: '',
            top_tip: {},//判断续费/推广
            selectedPaymentMethod: '',
            payAmount: 0,
            requiredGold: 0,
            hasEnoughGold: false,
            showPaymentSelect: false, // 是否显示支付方式选择布局
        };
    },
    computed: {

        ...mapState(['user_info']),

        myonehouse() {
            return this.$store.state.myonehouse
        },
        // 计算属性来组items数组
        zdgroupedItems() {
            const items = this.zhidingInfo.items;
            const numberOfGroups = Math.ceil(items.length / 4);
            const groups = [];
            for (let i = 0; i < numberOfGroups; i++) {
                groups.push(items.slice(i * 4, (i + 1) * 4));
            }
            return groups;
        },
        jxgroupedItems() {

            const items = this.jingxuanInfo.items.map(item => {
                let name = item.name;
                if (typeof name === 'string') {
                    name = name.replace('次', ''); // 去掉 name 中的 '次' 字
                }
                return {
                    ...item,
                    name: name
                };
            });
            const numberOfGroups = Math.ceil(items.length / 4);
            const groups = [];
            for (let i = 0; i < numberOfGroups; i++) {
                groups.push(items.slice(i * 4, (i + 1) * 4));
            }
            return groups;
        }, youxiaoqiItems() {
            const items = this.active_time;
            const numberOfGroups = Math.ceil(items.length / 4);
            const groups = [];
            for (let i = 0; i < numberOfGroups; i++) {
                groups.push(items.slice(i * 4, (i + 1) * 4));
            }
            return groups;
        },
    },
    async onLoad(options) {
        // 检查user_info是否存在,不存在则获取
        if (!this.user_info || !this.user_info.id) {
            await this.fetchUserInfo();
        }
        this.item_title = options.item_title
        this.item_id = options.id
        this.item_type = options.item_type
        //判断是商业还是二手房
        if (options.catid) {
            this.catid = parseInt(options.catid) || 1
            if (this.catid === 5) {
                this.geturl = '/commercial/manage_info?catid=' + this.catid
            } else {
                this.geturl = '/user/manage_info?cate_id=' + this.catid
            }
        }

        this.getWxConfig(
			['chooseWXPay', 'updateAppMessageShareData', 'updateTimelineShareData'],
			wx => {
				console.log('执行回调')
				this.wx = wx
			},
			1
		)


        if (options.parentid) {
            this.parentid = parseInt(options.parentid) || 1
            this.geturl = '/commercial/manage_info?parentid=' + this.parentid
        }

        if (this.item_type == 1) {
            this.pageType = '置顶'
            this.getZhiding(this.myonehouse, this.item_title)
        }
        else if (this.item_type == 2) {
            this.pageType = '精选'
            this.getJingxuan(this.myonehouse, this.item_title)
        }
        else {
            this.getAcitiveTime()//获取上架日期数据显示
            this.checkInfoInvalid(this.item_id)//判断是否失效
        }
        uni.$on('settgcation', (e) => {
            this.tgmask = e
        })

        if (!this.myonehouse.id) {
            let self = this;
            uni.showModal({
                title: '温馨提示',
                content: '未获取到房源相关信息，请返回房源列表，再次进行操作！',
                showCancel: false, // 不显示取消按钮
                confirmText: '确定', // 确定按钮的文字
                success: function (res) {
                    if (res.confirm) {
                        self.gettourl();
                    }
                }
            });
        }
    },

    methods: {

        ...mapMutations(['getUserInfo', 'setAllowOpen']),


        async fetchUserInfo() {
            const res = await this.$ajax.get('member/index.html', {}, (res) => {
                if (res.data.user) {
                    if (res.data.is_adviser) {
                        res.data.user.adviser = {
                            uncheck: res.data.adviser.uncheck || 0,
                            uncheck_cname: res.data.adviser.uncheck_cname || '',
                            uncheck_prelogo: res.data.adviser.uncheck_prelogo || ''
                        }
                    }
                    if (res.data.user.levelid > 1) {
                        res.data.user.agent = {
                            uncheck: res.data.agent.uncheck || 0,
                            uncheck_cname: res.data.agent.uncheck_cname || '',
                            uncheck_prelogo: res.data.agent.uncheck_prelogo || ''
                        }
                    }
                    console.log(res.data.user);
                    this.getUserInfo(res.data.user);
                }
            })
        },

        formatTemplateMoney(value) {
            return formatMoney(value);
        },

        gettourl() {
            let self = this;
            if (!self.geturl) {
                uni.switchTab({
                    url: '/pages/index/index'
                })
            }
            setTimeout(() => {
                uni.redirectTo({
                    url: self.geturl
                });
            }, 2000);
        },
        getAcitiveTime() {
            let api
            if (this.parentid || this.catid === 5) {
                api = 'NewEstateRelease/getAcitiveTime'
            } else {
                api = 'NewRelease/getAcitiveTime'
            }
            this.$ajax.get(api, {}, res => {
                if (res.data.code === 1) {
                    this.active_time = res.data.list
                    //   查看是否需要默认选择一个
                    if (this.active_time) {
                        this.select_active_time_value = this.active_time[0].id; // 更新当前选中的项
                        this.item_day = this.active_time[0].name
                        this.addDaysToCurrentDate(this.active_time[0].num, this.active_time[0].name)
                        this.selectItemyxq(this.active_time[0],0)
                    }
                }
            })
        },
        // 判断是否失效
        checkInfoInvalid(info_id) {
            let api
            if (this.parentid || this.catid === 5) {
                api = 'NewEstateRelease/checkInfoInvalid'
            } else {
                api = 'NewRelease/checkInfoInvalid'
            }
            this.$ajax.get(api, { id: info_id }, res => {
                if (res.data.code == 0) {
                    this.youxiaoqi = true
                    this.houseyxt = res.data.msg
                    //提示该房源已经失效
                } else {
                    //判断
                    if (this.myonehouse.id) {
                        let self = this;
                        uni.showModal({
                            title: '温馨提示',
                            content: res.data.msg,
                            showCancel: false, // 不显示取消按钮
                            confirmText: '确定', // 确定按钮的文字
                            success: function (res) {
                                if (res.confirm) {
                                    if (self.parentid || self.catid === 5) {
                                        self.sycheckInfo(info_id, this.select_active_time_value)
                                    } else {
                                        self.escheckInfo(info_id, this.select_active_time_value)
                                    }
                                }
                            }
                        });
                    }

                }
            })
        },
        sycheckInfo(info_id, activetime_id) {
            let pay_method = this.selectedPaymentMethod === 'gold' ? 'gold' : 'wxpay';
            let is_corn = pay_method === 'gold' ? 1 : 0;
            this.$ajax.post('NewEstateRelease/checkInfoShow', { id: info_id, activetime_id,is_corn:is_corn }, (res) => {
                this.shangjiadesc = res.data.msg
                if (res.data.code !== 1) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    })
                    setTimeout(() => {
                        this.gettourl();
                    }, 2000);
                    return
                }
                // 不需要付费直接可以上架
                if (res.data.is_release === 1 && res.data.pay_status === 1) {
                    uni.showToast({
                        title: res.data.msg,
                        duration: 2000
                    })
                    this.gettourl();
                    return
                } else {
                    this.h5WxPayApi = '/wapi/estateRelease/setInfoShowByWxPayWap'
                    this.h5AliPayApi = '/wapi/estateRelease/setInfoShowByAliPay'
                    //this.mpWxPayApi = 'estateRelease/setInfoShowByWxPay'
                    this.mpWxPayApi = 'NewEstateRelease/setInfoShowByWxPay'
                    this.pay_params = {
                        order_id: res.data.order_id,
                    }

                    //这里需要根据选择的支付方式来判断
                    let pay_msg = "";
                    if (pay_method === 'gold') {
                        //金币支付 再判断是否足够
                        if (this.user_info.money_own >= parseFloat(this.requiredGold)) {
                            pay_msg = "本次操作需扣除"+this.requiredGold+"个金币,您确定要执行该操作吗?";
                        } else {
                            pay_msg = "金币余额不足,请先充值金币";
                            uni.showToast({
                                title: res.data.msg,
                                icon: "none",
                                duration: 2000
                            });
                            return;
                        }
                    }else{
                        pay_msg = "本次操作需支付"+this.payAmount+"元,您确定要执行该操作吗?";
                    }
                    // 提示需要扣除金币或支付
                    showModal({
                        content: pay_msg,
                        confirm: () => {
                            // is_release === 1 代表用户金币足够支付，否则调起支付
                            if (pay_method === 'gold') {
                                if (activetime_id) {
                                    this.cornPayOfActivetime(info_id, activetime_id)
                                } else {
                                    this.cornPayOfSheleves(info_id)
                                }
                            } else {
                                this.handlePayinfo()
                            }
                        },
                    })
                }
            })
        },
        // 判断是否可以不用花钱直接上架
        escheckInfo(info_id, activetime_id) {
            //这里需要根据选择的支付方式来判断
            let pay_method = this.selectedPaymentMethod === 'gold' ? 'gold' : 'wxpay';
            let is_corn = pay_method === 'gold' ? 1 : 0;
            this.$ajax.get('NewRelease/checkInfoShow.html', { id: info_id, activetime_id,is_corn:is_corn }, res => {
                if (res.data.code !== 1) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    })
                    setTimeout(() => {
                        this.gettourl();
                    }, 2000);

                    return
                }
                // 不需要付费直接可以上架
                if (res.data.is_release === 1 && res.data.pay_status === 1) {
                    uni.showToast({
                        title: res.data.msg,
                        duration: 2000
                    })
                    this.gettourl();
                    return
                } else {
                    this.h5WxPayApi = "/wapi/release/setInfoShowByWxPayWap.html"
                    this.h5AliPayApi = "/wapi/release/setInfoShowByAliPay.html"
                    //this.mpWxPayApi = "release/setInfoShowByWxPay.html"
                    this.mpWxPayApi = "NewRelease/setInfoShowByWxPay.html"
                    this.pay_params = {
                        order_id: res.data.order_id
                    }
                    //this.youxiaoqi = false
                    let pay_msg = "";
                    if (pay_method === 'gold') {
                        //金币支付 再判断是否足够
                        if (this.user_info.money_own >= parseFloat(this.requiredGold)) {
                            pay_msg = "本次操作需扣除"+this.requiredGold+"个金币,您确定要执行该操作吗?";
                        } else {
                            pay_msg = "金币余额不足,请先充值金币";
                            uni.showToast({
                                title: res.data.msg,
                                icon: "none",
                                duration: 2000
                            });
                            return;
                        }
                    }else{
                        pay_msg = "本次操作需支付"+this.payAmount+"元,您确定要执行该操作吗?";
                    }

                    // 提示需要扣除金币或支付
                    showModal({
                        content: pay_msg,
                        confirm: () => {
                            // is_release === 1 代表用户金币足够支付，否则调起支付
                            if (pay_method === 'gold') {
                                if (activetime_id) {
                                    this.cornPayOfActivetime(info_id, activetime_id)
                                } else {
                                    this.cornPayOfSheleves(info_id)
                                }
                            } else {
                                this.handlePayinfo()
                            }
                        }
                    })
                }
            })
        },
        // 使用金币支付上架信息
        cornPayOfSheleves(info_id) {
            let api
            if (this.parentid || this.catid === 5) {
                api = 'NewEstateRelease/setInfoShowByCorn'
            } else {
                api = 'NewRelease/setInfoShowByCorn.html'
            }
            this.$ajax.get(api, { id: info_id }, async (res) => {
                if (res.data.code === 1) {
                    uni.showToast({
                        title: res.data.msg,
                        duration: 2000
                    });
                    //使用金币购买精选成功  更新用户金币余额
                    await this.fetchUserInfo();
                    this.gettourl();
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
        // 失效房源使用金币支付上架
        cornPayOfActivetime(info_id, activetime_id) {
            let api
            if (this.parentid || this.catid === 5) {
                api = 'NewEstateRelease/infoShowAndActivityByCorn'
            } else {
                api = 'NewRelease/infoShowAndActivityByCorn'
            }
            this.$ajax.get(api, { id: info_id, activetime_id }, async (res) => {
                if (res.data.code === 1) {
                    uni.showToast({
                        title: res.data.msg,
                        duration: 2000
                    })
                    //使用金币购买精选成功  更新用户金币余额
                    await this.fetchUserInfo();
                    this.gettourl();
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
        cancelJingxuan(id) {
            this.$ajax.get('member/cancelRecommend.html', { id }, res => {
                uni.showToast({
                    title: res.data.msg,
                    icon: 'none'
                })
            })
        },
        getJingxuan(item, title) {
            uni.showLoading({
                title: "加载中..."
            })

            let api
            let data
            if (this.parentid || this.catid === 5) {
                api = 'NewEstateRelease/selectedList'

                data = { catid: item.catid }
            } else {
                api = 'NewRelease/selectedList.html'
                data = { id: item.id }
            }

            this.$ajax.get(api, data, (res) => {
                if (res.data.code == 3) {
                    showModal({
                        title: '提示',
                        content: "当前信息已经是精选状态，是否取消精选？（取消精选条数不会返还）",
                        cancelText: '否',
                        confirmText: '是',
                        confirm: () => {
                            this.cancelJingxuan(id)
                        }
                    })
                    return
                }
                // 获取当前用户的免费精选条数
                if (res.data.tip) {
                    this.jingxuan_num = res.data.tip
                    this.freeSelected = res.data.freeSelected

                } else {
                    this.jingxuan_num = {
                        allow_num: 0,
                        use_num: 0,
                        surplus_num: 0
                    }
                }

                if (res.data.freeSelected) {
                    this.freeSelected = res.data.freeSelected
                } else {
                    this.freeSelected = {}
                }
                if (res.data.code == 1) {
                    this.tgmask = res.data.desc
                    // this.jingxuanInfo = res.data
                    // 后台返回数据不兼容组件要求格式，在这里处理下
                    /* if (res.data.freeSelected.id !== undefined) {
                        res.data.selectedInfo.unshift(res.data.freeSelected)
                    } */
                    let package_selecteds = res.data.packagesInfoSelecteds.map(item => {
                        item.type = "meal"
                        return item
                    })

                    if (res.data.freeSelected.id !== undefined) {
                        package_selecteds.unshift(res.data.freeSelected)
                    }

                    let selecteds = res.data.selectedInfo
                    this.jingxuanInfo = {
                        title: title,
                        id: item.id,
                        money_own: res.data.money_own,
                        items:package_selecteds.concat(selecteds)
                        //items:selecteds.concat(package_selecteds)
                    }
                    //设置默认选中的精选推广方式
                    if (this.jingxuanInfo.items) {
                        this.jingxuanId = this.jingxuanInfo.items[0].id; // 更新当前选中的项
                        this.jingxuancount = this.jingxuanInfo.items[0].name
                        if (this.jingxuanInfo.items[0].type == 'meal') {
                            this.jingxuanunit = '';
                        } else {
                            this.jingxuanunit = '次';
                        }
                        this.selectItemjx(this.jingxuanInfo.items[0],0)

                    }
                    uni.hideLoading();
                } else {
                    uni.hideLoading();
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            }, (err) => {
                uni.hideLoading();
                console.log(err)
            }, false)
        },
        getZhiding(item, title) {
            uni.showLoading({
                title: "加载中..."
            })
            let api
            let data
            if (this.parentid || this.catid === 5) {
                api = 'NewEstateRelease/infoTops'
                data = {
                    catid: item.catid, id: item.id
                }
            } else {
                api = 'NewRelease/infoTops.html'
                data = {
                    id: item.id
                }
            }

            this.zhidingId = ''
            this.$ajax.get(api, data, (res) => {
                // 获取当前信息的置顶信息
                if (res.data.info) {
                    this.top_tip = res.data.info


                } else {
                    this.top_tip = {}
                }
                if (res.data.code == 1) {
                    this.tgmask = res.data.desc
                    // res.data.days.unshift({name:"请选择",value:0})
                    // this.zhidingInfo = res.data
                    let tops = res.data.tops.map(item => {
                        var reg = /^[0-9]+天$/
                        if (reg.test(item.name)) {
                            item.name = parseFloat(item.name)
                            item.unit = '天'
                        } else {
                            item.unit = ''
                        }
                        item.is_package = 0;
                        return item
                    })
                    let package_tops = []
                    if (res.data.packagesInfoTops && res.data.packagesInfoTops.length > 0) {
                        package_tops = res.data.packagesInfoTops.map(item => {
                            item.type = "meal"
                            var reg = /^[0-9]+天$/
                            if (reg.test(item.name)) {
                                item.name = parseFloat(item.name)
                                item.unit = '天'
                            } else {
                                item.unit = ''
                            }
                            item.is_package = 1;
                            return item
                        })
                    }
                    this.zhidingInfo = {
                        title: title,
                        id: item.id,
                        money_own: res.data.money_own,
                        items: package_tops.concat(tops)
                    }

                    if (this.zhidingInfo.items) {
                        this.zhidingId = this.zhidingInfo.items[0].id; // 更新当前选中的项
                        this.item_day = this.zhidingInfo.items[0].name + this.zhidingInfo.items[0].unit
                        this.addDaysToCurrentDate(this.zhidingInfo.items[0].name, this.zhidingInfo.items[0].num)
                        this.selectItem(this.zhidingInfo.items[0],0)
                    }

                    uni.hideLoading();
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                    uni.hideLoading()
                }
            }, (err) => {
                console.log(err)
                uni.hideLoading()
            }, false)
        },
        selectItem(e, index) {
            this.zhidingId = e.id; // 更新当前选中的项
            this.item_day = e.name + e.unit
            this.addDaysToCurrentDate(e.num, e.name)
            this.zhiding_type = e.type
            // 检查是否是非套餐置顶
            if (this.zhiding_type != 'meal') {
                // 获取所选项的金币和金额
                const selectedItem = this.zhidingInfo.items.find(item => item.id === this.zhidingId);
                if (selectedItem) {
                    //金币和金额固定比例是1
                    this.requiredGold = formatMoney(selectedItem.money);
                    this.payAmount = selectedItem.money ? this.requiredGold : 0;
                    // 检查金币余额
                    this.hasEnoughGold = this.user_info.money_own >= parseFloat(this.requiredGold);
                    // 设置默认支付方式
                    this.selectedPaymentMethod = this.hasEnoughGold ? 'gold' : 'wxpay';
                    // 显示支付方式选择布局
                    if(this.requiredGold>0){
                        this.showPaymentSelect = true;
                    }else{
                        this.showPaymentSelect = false;
                    }
                    return;
                }else{
                    this.showPaymentSelect = false;
                }
            } else {
                this.showPaymentSelect = false;
            }
        },
        selectItemjx(e, index) {
            this.jingxuanId = e.id; // 更新当前选中的项
            this.jingxuancount = e.name

            if (e.type == 'meal') {
                this.jingxuanunit = '';
            } else {
                this.jingxuanunit = '次';
            }
            this.jingxuan_type = e.type//选择的类型
            this.sucessdesc = this.jingxuancount + this.jingxuanunit

            // 检查是否是非套餐精选
            if (this.jingxuan_type != 'meal') {
                // 获取所选项的金币和金额
                const selectedItem = this.jingxuanInfo.items.find(item => item.id === this.jingxuanId);
                if (selectedItem) {
                    //金币和金额固定比例是1
                    this.requiredGold = formatMoney(selectedItem.money);
                    this.payAmount = selectedItem.money ? this.requiredGold : 0;
                    // 检查金币余额
                    this.hasEnoughGold = this.user_info.money_own >= parseFloat(this.requiredGold);
                    // 设置默认支付方式
                    this.selectedPaymentMethod = this.hasEnoughGold ? 'gold' : 'wxpay';
                    // 显示支付方式选择布局
                    //等于0时不需要选择支付方式
                    if(this.requiredGold>0){
                        this.showPaymentSelect = true;
                    }else{
                        this.showPaymentSelect = false;
                    }
                    return;
                }else{
                    this.showPaymentSelect = false;
                }
            } else {
                this.showPaymentSelect = false;
            }
        },
        selectItemyxq(e, index) {
            this.select_active_time_value = e.id; // 更新当前选中的项
            this.sucessdesc = e.name;
            this.item_day = e.name;
            this.addDaysToCurrentDate(e.num, e.name);

            // 获取所选项的金币和金额
            const selectedItem = this.active_time.find(item => item.id === this.select_active_time_value);
            if (selectedItem) {
                this.requiredGold = formatMoney(selectedItem.money);
                this.payAmount = selectedItem.money ? this.requiredGold : 0;
                // 检查金币余额
                this.hasEnoughGold = this.user_info.money_own >= parseFloat(this.requiredGold);
                // 设置默认支付方式
                this.selectedPaymentMethod = this.hasEnoughGold ? 'gold' : 'wxpay';
                // 显示支付方式选择布局
                if(this.requiredGold>0){
                    this.showPaymentSelect = true;
                }else{
                    this.showPaymentSelect = false;
                }
            }else{
                this.showPaymentSelect = false;
            }
        },
        addDaysToCurrentDate(days, unit) {
            if (unit) {
                let today = new Date();
                if (this.top_tip && !isNaN(new Date(this.top_tip.upgrade_time).getTime())) {
                    today = new Date(this.top_tip.upgrade_time);
                    today.setDate(today.getDate() + days);
                } else {
                    // 否则使用当前日期，并加上天数
                    today.setDate(today.getDate() + days);
                }
                this.item_time = this.formatDate(today);
                this.sucessdesc = this.item_time
            } else {
                this.item_time = ''
            }
        },
        formatDate(date) {
            // 格式化日期时间到 YYYY-MM-DD hh:mm:ss
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            // const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}`;
        },
        sysetJingxuan(info_id) {
            if (this.jingxuanId === -1) {
                uni.showToast({
                    title: '请选择刷新次数',
                    icon: 'none',
                })
                return
            }
            let params = { id: info_id }
            if (this.jingxuanId) {
                // 如果选择的是购买的套餐
                if (this.jingxuan_type === 'meal') {
                    params.package_jingxuan_id = this.jingxuanId
                } else {
                    params.jingxuan_id = this.jingxuanId
                }
            } else {
                params.freeSelected = 1
            }
            
            this.$ajax.post(
                'NewEstateRelease/infoSelected',
                params,
                (res) => {
                    if (res.data.code == 1) {
                        if (res.data.is_selected === 1 && res.data.pay_status === 1) {
                            // 不需要支付，直接设置精选成功
                            uni.showToast({
                                title: res.data.msg,
                                duration: 2000
                            })
                            this.gettourl();
                            return;
                        }
                        // 设置支付接口和支付所需要的参数
                        this.h5WxPayApi = '/wapi/estateRelease/infoSelectedByWxPayWap'
                        this.h5AliPayApi = '/wapi/estateRelease/infoSelectedByAliPay'
                        //this.mpWxPayApi = 'estateRelease/infoSelectedByWxPay'
                        this.mpWxPayApi = 'NewEstateRelease/infoSelectedByWxPay.html'
                        this.pay_params = {
                            id: info_id,
                            jingxuan_id: this.jingxuanId,
                        }

                        //这里需要根据选择的支付方式来判断
                        let pay_method = this.selectedPaymentMethod === 'gold' ? 'gold' : 'wxpay';
                        let pay_msg = "";
                        if (pay_method === 'gold') {
                            //金币支付 再判断是否足够
                            if (this.user_info.money_own >= parseFloat(this.requiredGold)) {
                                pay_msg = "本次操作需扣除"+this.requiredGold+"个金币,您确定要执行该操作吗?";
                            } else {
                                pay_msg = "金币余额不足,请先充值金币";
                                uni.showToast({
                                    title: res.data.msg,
                                    icon: "none",
                                    duration: 2000
                                });
                                return;
                            }
                        }else{
                            pay_msg = "本次操作需支付"+this.payAmount+"元,您确定要执行该操作吗?";
                        }
                        //此处金币扣除 调用方法 this.sycornPayOfJingxuan(info_id)
                        showModal({
                            content: pay_msg,
                            confirm: () => {
                                if (pay_method === 'gold') {
                                    // 使用金币支付
                                    this.sycornPayOfJingxuan(info_id)
                                } else {
                                    this.handlePayinfo()
                                }
                            },
                        })
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        })
                        this.gettourl();
                    }
                },
                (err) => {
                    console.log(err)
                },
                false
            )
        },
        sycornPayOfJingxuan(info_id) {
            this.$ajax.post('NewEstateRelease/infoSelectedByCorn', { jingxuan_id: this.jingxuanId, id: info_id }, async(res) => {
                if (res.data.code === 1) {
                    uni.showToast({
                        title: res.data.msg,
                        duration: 2000
                    })
                    //使用金币购买精选成功  更新用户金币余额
                    await this.fetchUserInfo();
                    this.gettourl();
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                    })
                }
            })
        },
        setJingxuan(info_id) {
            if (this.jingxuanId === -1) {
                uni.showToast({
                    title: "请选择刷新次数",
                    icon: "none"
                })
                return
            }
            let params = { id: info_id }
            if (this.jingxuanId) {
                // 如果选择的是购买的套餐
                if (this.jingxuan_type === 'meal') {
                    params.package_jingxuan_id = this.jingxuanId
                } else {
                    params.jingxuan_id = this.jingxuanId
                }
            } else {
                params.freeSelected = 1
            }

            this.$ajax.get('NewRelease/infoSelected.html', params, res => {
                if (res.data.code == 1) {
                    if (res.data.is_selected === 1 && res.data.pay_status === 1) {
                        // 不需要支付，直接设置精选成功
                        uni.showToast({
                            title: res.data.msg,
                            duration: 2000
                        })
                        this.gettourl();
                        return ;
                    }

                    // 设置支付接口和支付所需要的参数
                    this.h5WxPayApi = "/wapi/release/infoSelectedByWxPayWap.html"
                    this.h5AliPayApi = "/wapi/release/infoSelectedByAliPay.html"
                    this.mpWxPayApi = "NewRelease/infoSelectedByWxPay.html"
                    this.pay_params = {
                        id: info_id,
                        jingxuan_id: this.jingxuanId
                    }
                    //这里需要根据选择的支付方式来判断    
                    let pay_method = this.selectedPaymentMethod === 'gold' ? 'gold' : 'wxpay';
                    let pay_msg = "";
                    if (pay_method === 'gold') {
                        //金币支付 再判断是否足够
                        if(this.user_info.money_own >= parseFloat(this.requiredGold)){
                            pay_msg = "本次操作需扣除"+this.requiredGold+"个金币,您确定要执行该操作吗?";
                        }else{
                            pay_msg = "金币余额不足,请先充值金币";
                            uni.showToast({
                                title: res.data.msg,
                                icon: "none",
                                duration: 2000
                            });
                            return;
                        }
                    } else {
                        pay_msg = "本次操作需支付"+this.payAmount+"元,您确定要执行该操作吗?";
                    }
                    //此处调用金币支付 this.cornPayOfJingxuan(info_id)
                    showModal({
                        content: pay_msg,
                        confirm: () => {
                            if (pay_method === 'gold') {
                                // 使用金币支付
                                this.cornPayOfJingxuan(info_id)
                            } else {
                                this.handlePayinfo()
                            }
                        }
                    })
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    })
                    this.gettourl();
                }
            }, err => {
                console.log(err)
            }, false)
        },
        /** 
         * <AUTHOR> 
         * @date 2020-08-27 14:10:27 
         * @desc 使用金币购买精选 
         */
        cornPayOfJingxuan(info_id) {
            this.$ajax.get('NewRelease/infoSelectedByCorn.html', { jingxuan_id: this.jingxuanId, id: info_id }, async (res) => {
                if (res.data.code === 1) {
                    uni.showToast({
                        title: res.data.msg,
                        duration: 2000
                    })
                    //使用金币购买精选成功  更新用户金币余额
                    await this.fetchUserInfo();
                    this.gettourl();
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
        sysetZhiding(info_id) {
            if (!this.zhidingId) {
                uni.showToast({
                    title: '请选择需要置顶的天数',
                    icon: 'none',
                })
                return
            }
            let params = { id: info_id }
            // 如果选择的是购买的套餐
            if (this.zhiding_type === 'meal') {
                console.log('套餐置顶');
                params.package_tops_id = this.zhidingId
            } else {
                console.log('普通置顶,不是套餐置顶');
                params.tops_id = this.zhidingId
            }
            this.$ajax.post('NewEstateRelease/checkInfoTop', params, (res) => {
                if (res.data.code == 1) {
                    if (res.data.is_top === 1 && res.data.pay_status === 1) {
                        // 不需要支付，直接设置置顶成功
                        uni.showToast({
                            title: res.data.msg,
                            duration: 2000
                        })
                        this.gettourl();
                        return
                    }
                    // 设置支付接口和支付所需要的参数
                    this.h5WxPayApi = '/wapi/estateRelease/infoTopByWxPayWap'
                    this.h5AliPayApi = '/wapi/estateRelease/infoTopByAliPay'
                    //this.mpWxPayApi = 'estateRelease/infoTopByWxPay'
                    this.mpWxPayApi = 'NewEstateRelease/infoTopByWxPay.html'
                    this.pay_params = {
                        id: info_id,
                        tops_id: this.zhidingId,
                    }

                    //这里需要根据选择的支付方式来判断
                    let pay_method = this.selectedPaymentMethod === 'gold' ? 'gold' : 'wxpay';
                    let pay_msg = "";
                    if (pay_method === 'gold') {
                        //金币支付 再判断是否足够
                        if (this.user_info.money_own >= parseFloat(this.requiredGold)) {
                            pay_msg = "本次操作需扣除"+this.requiredGold+"个金币,您确定要执行该操作吗?";
                        } else {
                            pay_msg = "金币余额不足,请先充值金币";
                            uni.showToast({
                                title: res.data.msg,
                                icon: "none",
                                duration: 2000
                            });
                            return;
                        }
                    }else{
                        pay_msg = "本次操作需支付"+this.payAmount+"元,您确定要执行该操作吗?";
                    }
                    showModal({ 
                        content: pay_msg,
                        confirm: () => {
                            if (pay_method === 'gold') {
                                // 使用金币支付
                                this.cornPayOfZhiding(info_id)
                            } else {
                                this.handlePayinfo()
                            }
                        },
                    })
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    })
                    this.gettourl();
                }
            })
        },
        /** 
     * <AUTHOR> 
     * @date 2024-09-12 14:10:04 
     * @desc 设置置顶 
     */
        essetZhiding(info_id) {
            if (!this.zhidingId) {
                uni.showToast({
                    title: "请选择需要置顶的天数",
                    icon: "none"
                })
                return
            }
            let params = { id: info_id }
            // 如果选择的是购买的套餐
            if (this.zhiding_type === 'meal') {
                console.log('套餐置顶');
                params.package_tops_id = this.zhidingId
            } else {
                console.log('普通置顶');
                params.tops_id = this.zhidingId
            }
            this.$ajax.get('NewRelease/checkInfoTop.html', params, (res) => {
                if (res.data.code == 1) {
                    if (res.data.is_top === 1 && res.data.pay_status === 1) {
                        // 不需要支付，直接设置置顶成功
                        uni.showToast({
                            title: res.data.msg,
                            duration: 2000
                        })
                        this.gettourl();
                        return
                    }

                    // 设置支付接口和支付所需要的参数
                    this.h5WxPayApi = "/wapi/release/infoTopByWxPayWap.html"
                    this.h5AliPayApi = "/wapi/release/infoTopByAliPay.html"
                    //this.mpWxPayApi = "release/infoTopByWxPay.html"
                    //2024-12-10 修改为 
                    this.mpWxPayApi = "NewRelease/infoTopByWxPay.html"

                    this.pay_params = {
                        id: info_id,
                        tops_id: this.zhidingId
                    }
                    //这里需要根据选择的支付方式来判断
                    let pay_method = this.selectedPaymentMethod === 'gold' ? 'gold' : 'wxpay';
                    let pay_msg = "";
                    if (pay_method === 'gold') {
                        //金币支付 再判断是否足够
                        if (this.user_info.money_own >= parseFloat(this.requiredGold)) {
                            pay_msg = "本次操作需扣除"+this.requiredGold+"个金币,您确定要执行该操作吗?";
                        } else {
                            pay_msg = "金币余额不足,请先充值金币";
                            uni.showToast({
                                title: res.data.msg,
                                icon: "none",
                                duration: 2000
                            });
                            return;
                        }
                    } else {
                        //微信支付
                        pay_msg = "本次操作需支付"+this.payAmount+"元,您确定要执行该操作吗?";
                    }
                    showModal({
                        content: pay_msg,
                        confirm: () => {
                            if (pay_method === 'gold') {
                                this.cornPayOfZhiding(info_id)
                            } else {
                                this.handlePayinfo()
                            }
                        }
                    })

                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none",
                        duration: 2000
                    })
                    this.gettourl();
                }
            })
        },
        /** 
         * <AUTHOR> 
         * @date 2020-08-27 14:10:15 
         * @desc 使用金币购买置顶 
         */
       cornPayOfZhiding(info_id) {

            let api
            if (this.parentid || this.catid === 5) {
                api = 'NewEstateRelease/infoTopByCorn'
            } else {
                api = 'NewRelease/infoTopByCorn.html'
            }
            this.$ajax.get(api, { tops_id: this.zhidingId, id: info_id }, async (res) => {
                if (res.data.code === 1) {
                    uni.showToast({
                        title: res.data.msg,
                        duration: 2000
                    })

                    //使用金币购买置顶成功  更新用户金币余额
                    await this.fetchUserInfo();
                    this.gettourl();
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
        submitForm() {
            if (!this.agree_agreement) {
                uni.showToast({
                    title: '请先同意推广服务协议',
                    icon: 'none'
                })
                return
            }
            // 其他情况继续原有逻辑
            const now_time = Date.now();
            if (this.submit_time && now_time - this.submit_time < 5000) {
                uni.showToast({
                    title: "您的操作过于频繁",
                    icon: 'none'
                })
                return
            }
            this.submit_time = now_time
            //判断是置顶还是精选
            console.log("this.item_type==="+this.item_type);
            if (this.item_type == 1) {
                console.log('置顶操作parentid=='+this.parentid+'catid=='+this.catid);
                if (this.parentid || this.catid === 5) {
                    console.log('操作商业置顶');
                    this.sysetZhiding(this.item_id)
                } else {
                    console.log('操作二手房置顶');
                    this.essetZhiding(this.item_id)
                }

            } else if (this.item_type == 2) {
                if (this.parentid || this.catid === 5) {
                    this.sysetJingxuan(this.item_id)
                } else {
                    this.setJingxuan(this.item_id)
                }

            } else {
                //进行上架操作
                this.setActiveTime()
            }

        },
        setActiveTime() {
            if (!this.select_active_time_value) {
                uni.showToast({
                    title: '改信息已经失效，请选择有效期',
                    icon: 'none'
                })
                return
            }
            if (this.parentid || this.catid === 5) {
                this.sycheckInfo(this.item_id, this.select_active_time_value)
            } else {
                this.escheckInfo(this.item_id, this.select_active_time_value)
            }

        },
        handlePayinfo() {

            console.log('handlePayinfo-----------');

            // this.handlePaymentSuccess()
            // return
            // #ifdef H5
            console.log('H5Pay');
            this.wxPay()
            // #endif
            // #ifdef APP-PLUS
            this.appPay()
            console.log('appPay');
            // #endif
            // #ifdef MP-WEIXIN
            this.mpWxPay()
            // #endif
        },

        //代码存在历史遗留问题  不需要纠结具体的变量命名 下方就是实现的微信公众号拉起微信支付
        wxPay() {
            uni.showLoading({
                title: '正在支付...'
            });
			this.$ajax.post(this.mpWxPayApi, this.pay_params, res => {
				if (res.data.code === 1) {
					let pay_info = res.data.data
					this.wx.chooseWXPay({
						// provider: 'wxpay',
						timestamp: pay_info.timeStamp,
						nonceStr: pay_info.nonceStr,
						package: pay_info.package,
						signType: pay_info.signType,
						paySign: pay_info.paySign,
						success: res => {
                            uni.hideLoading();
							uni.showToast({
								title: '支付成功'
							})
							setTimeout(() => {
							    this.gettourl();
							}, 1500)
						},
						fail: function (err) {
							console.log('支付失败：', err);
                            uni.hideLoading();
							uni.showToast({
								title: err.err_desc || err.errMsg,
								icon: 'none',
								duration: 5000
							})
						}
					})
				} else {
                    uni.hideLoading();
					uni.showToast({
						title: res.data.msg,
						icon: 'none'
					})
				}
			})
		},








        // #ifdef H5
        /**
         * h5支付
         */
        H5Pay() {
            //进行支付
            /* if (!this.pay_type) {
                uni.showToast({
                    title: '请选择支付方',
                    icon: 'none'
                })
                return
            }
            if (this.pay_type === 'alipay') {
                let pay_url = `${this.h5AliPayApi}?token=${uni.getStorageSync("token") || ""}`
                for (let key in this.pay_params) {
                    pay_url += `&${key}=${this.pay_params[key]}`
                }
                window.open(pay_url)
            } else if (this.pay_type === 'wechatpay') {
                let pay_url = `${this.h5WxPayApi}?token=${uni.getStorageSync("token") || ""}`
                for (let key in this.pay_params) {
                    pay_url += `&${key}=${this.pay_params[key]}`
                }
                window.open(pay_url)
            } */
            // 启动轮询
            // this.startPolling();
            //当前是微信公众号环境 直接调取微信公众号支付
            console.log('当前是微信公众号环境,直接调取微信公众号支付');
            this.mpWxPay();
        },
        startPolling() {
            clearInterval(this.pollingInterval);
            this.pollingInterval = setInterval(() => {
                this.checkPaymentStatus();
            }, 5000); // 每5秒查询一次
            uni.navigateBack({
                delta: 100 // 关闭当前页面及之前的所有页面
            });

            this.gettourl();
        },
        checkPaymentStatus() {
            uni.request({
                url: `http://localhost:3000/payment/status/${this.orderId}`,
                method: 'GET',
                success: (response) => {
                    if (response.statusCode === 200) {
                        this.paymentStatus = response.data.status;
                        if (this.paymentStatus === 'SUCCESS') {
                            this.handlePaymentSuccess();
                        } else if (this.paymentStatus === 'FAILURE') {
                            this.handlePaymentFailure();
                        }
                    } else {
                        console.error('Failed to fetch payment status:', response);
                    }
                },
                fail: (error) => {
                    console.error('Request failed:', error);
                }
            });
        },
        //支付成功跳转页面
        handlePaymentSuccess() {
            clearInterval(this.pollingInterval);
            uni.showToast({
                title: '支付成功！',
                duration: 3000
            });
            this.gettourl();

        },
        handlePaymentFailure() {
            clearInterval(this.pollingInterval);
            uni.showToast({
                title: '支付失败！',
                icon: 'none',
                duration: 5000
            });
            this.gettourl();
        },
        // #endif
        // #ifdef MP-WEIXIN
        /**
         * 微信小程序支付
         */
        mpWxPay() {
            uni.showLoading({
                title: '正在支付...'
            })

            this.$ajax.post(this.mpWxPayApi, this.pay_params, res => {
                if (res.data.code === 1) {
                    let pay_info = res.data.data
                    uni.requestPayment({
                        provider: 'wxpay',
                        timeStamp: pay_info.timeStamp,
                        nonceStr: pay_info.nonceStr,
                        package: pay_info.package,
                        signType: pay_info.signType,
                        paySign: pay_info.paySign,
                        success: res => {
                            //支付成功跳转支付页面
                            this.handlePaymentSuccess();
                        },
                        fail: err => {
                            console.log(err)
                            uni.showToast({
                                title: err.err_desc || (err.errMsg == 'requestPayment:fail cancel' ? '已取消支付' : err.errMsg),
                                icon: 'none',
                                duration: 5000
                            })
                        }
                    })
                    uni.hideLoading()
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
        // #endif
        // #ifdef APP-PLUS
        /**
         * app支付
         */
        appPay() {
            //进行支付
            if (!this.pay_type) {
                uni.showToast({
                    title: '请选择支付方',
                    icon: 'none'
                })
                return
            }
            let api, provider
            if (this.pay_type === 'alipay') {
                api = this.appAliPayApi
                provider = 'alipay'
            } else if (this.pay_type === 'wechatpay') {
                api = this.appWxPayApi
                provider = 'wxpay'
            }
            this.$ajax.get(api, this.pay_params, res => {
                if (res.data.code == 1) {
                    this.handelAppPay(res.data.pay_info, provider)
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        mask: true
                    })
                }
            })
        },
        handelAppPay(orderInfo, provider) {
            uni.requestPayment({
                provider: provider,
                orderInfo: orderInfo,
                success: res => {
                    this.handlePaymentSuccess();
                },
                fail: (err) => {
                    console.log(err)
                    this.$emit('pay_fail', err)
                    uni.showToast({
                        title: err.err_desc || (err.errMsg == 'requestPayment:fail cancel' ? '已取消支付' : err.errMsg),
                        icon: 'none',
                        duration: 5000
                    })
                }
            })
        },
        // #endif
        stopMove() {

        },
        addVerification() {
            var self = this;
            setTimeout(() => {
                uni.$emit('gitetgcation', {
                    tgmask: this.tgmask,
                    type: self.item_type
                })
            }, 200)
            this.$navigateTo(`/user/add_tuiguang`)
        },
        closePaymentPopup() {
            this.$refs.paymentPopup.hide();
        },
        selectPaymentMethod(method) {
            this.selectedPaymentMethod = method;
            this.$refs.paymentPopup.hide();
        },
        showPaymentPopup() {
            console.log('showPaymentPopup');
            this.$refs.paymentPopup.show();
        },
    },
};
</script>

<style lang="scss" scoped>
.flex-row {
    flex-direction: row;
}

.tuiguang_page {

    font-family: "PingFang SC";
    background-color: #fff;
    padding: 0rpx 40rpx;
    min-height: 100vh;

    .header {
        display: flex;
        gap: 24rpx;
        padding: 30rpx 0rpx;

        .title {
            color: #131315;
            font-size: 48rpx;
            font-weight: bold;
        }

        .icon {
            display: flex;
            padding: 4rpx 16rpx;
            justify-content: center;
            align-items: center;
            border-radius: 8rpx;
            background-image: linear-gradient(270deg, #F53F3F 0%, #F5A2A2 100%);
            color: #FFF;
            font-size: 24rpx;
            font-weight: 500;
            letter-spacing: 2rpx;
        }
    }

    .tgmask {
        color: #4E5969;
        font-size: 28rpx;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        margin-bottom: 32rpx;
    }

    .zdtime {
        color: orange;
        text-decoration: underline;
        padding: 2px;
        font-size: 28rpx
    }

    .tgtype {
        color: #F53F3F;
        font-size: 28rpx;
        font-weight: 400;
        padding: 0rpx 5rpx;

    }

    .pr {
        padding: 0rpx 10rpx;
    }



    .tgday {
        color: #4E5969;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        padding: 10rpx 0rpx;
    }

    .btn_box {
        display: flex;
        height: 38rpx;
        padding: 24rpx 40rpx;
        justify-content: center;
        align-items: center;
        gap: 8rpx;
        flex-shrink: 0;
        border-radius: 999px;
        background: linear-gradient(270deg, #F53F3F 0%, #F5A2A2 100%);
        box-shadow: 0px 2px 12px 0px rgba(245, 80, 80, 0.30);
        margin-top: 32rpx;

        .btn_qd {
            color: #FFF;
            text-align: center;
            font-size: 32rpx;
            font-style: normal;
            font-weight: bold;
            letter-spacing: 4rpx;
        }
    }

    .agreement {
        margin-top: 32rpx;
        color: #4E5969;
        align-items: center;
        margin-bottom: 34rpx;
        padding: 0 20rpx;
        font-size: 28rpx;

        .check_box {
            margin-right: 15rpx;
            width: 40rpx;
            height: 40rpx;
        }

        .no_checked {
            display: inline-block;
            width: 36rpx;
            height: 36rpx;
            border-radius: 50%;
            box-sizing: border-box;
            border: 4rpx solid #dedede;
        }
    }



    .btns {
        display: flex;
        align-items: flex-start;
        gap: 32rpx;
        padding: 32rpx 20rpx;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;

        .btn_item {
            display: flex;
            padding: 20rpx;
            justify-content: center;
            align-items: center;
            gap: 8rpx;
            flex: 1 0 0;
            background: #FFF;
            border-radius: 16rpx;
        }

        .btn_selected {
            border: 2rpx solid #E5E6EB;
        }

        .btn_wx {
            border: 2rpx solid #00B42A;

        }

        .btn_zfb {
            border: 2rpx solid #165DFF;
        }


        .wx {
            color: #00B42A;
        }

        .zfb {
            color: #4E5969;
        }
    }

    .options {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;



        .optionsitem {
            flex-basis: calc(40% - 10px);
            margin: 5px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            background-color: #fff;
            border-radius: 12px;
            border: 2px solid #ee9c9c;
            height: 192rpx;
            padding: 20rpx;
            flex-direction: column;

            .itemtitle {
                font-size: 32rpx;
                color: #4E5969;
                font-size: 32rpx;
                font-weight: 400;

                span {
                    padding-right: 8rpx;
                }
            }

            .itemcontent {
                display: flex;
                align-items: center;

                .money {
                    display: flex;
                    align-items: center;
                    color: #F53F3F;
                    font-size: 46rpx;
                    font-weight: bold;
                    padding-right: 10rpx;
                }

                .itemmask {

                    color: #F53F3F;
                    font-size: 24rpx;
                    font-style: normal;
                    font-weight: bold;
                }

            }

        }

        .optionsitem.selected {
            border-radius: 24rpx;
            border: 4rpx solid #F53F3F;
            background: #FEF5F5;
            box-shadow: 0px 4rpx 16rpx 0rpx rgba(245, 89, 85, 0.20);
        }


    }

    .banner {
        height: 570rpx;

        .swiper_con {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            align-items: center;


        }

        .swiper_item {
            flex-basis: calc(40% - 10px);
            margin: 5px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            background-color: #fff;
            border-radius: 12px;
            border: 2px solid #ee9c9c;
            height: 192rpx;
            padding: 20rpx;
            flex-direction: column;
        }
    }


    .block {
        margin: 20rpx 0rpx;
        padding: 0 20rpx;

        input {
            color: #333;
        }

        .open_menu {
            display: flex;
            padding: 24rpx 0;
            justify-content: space-between;
            align-items: center;
            border-top: 0.5px solid #F2F3F5;
            border-bottom: 0.5px solid #F2F3F5;

            .tyicon {
                margin-right: 16rpx;
                border-radius: 2px;
                background: #FFECE8;
                color: #F53F3F;
                font-size: 24rpx;
                font-weight: 400;
                padding: 2px 8px;

            }
        }
    }

    .tip_box_jingxuan {
        padding: 0 20rpx;
        .tip {
            font-size: 32rpx;
            color: #999;
        }
        .tip .highlight {
            color: $uni-color-primary;
            font-size: 40rpx;
        }
    }

    .tip_box {
        padding: 0 20rpx;
        .tip {
            font-size: 22rpx;
            color: #999;
        }
        .tip .highlight {
            color: $uni-color-primary;
            font-size: $uni-font-size-lg;
        }
    }

    .payment-popup {
        background-color: #fff;
        border-radius: 24rpx 24rpx 0 0;
        padding: 32rpx;
        
        .payment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32rpx;
            
            text {
                font-size: 32rpx;
                color: #333;
            }
            
            .close {
                color: #999;
            }
        }
        
        .payment-options {
            .payment-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 24rpx 0;
                border-bottom: 1px solid #eee;
                
                &:last-child {
                    border-bottom: none;
                }
                
                &.disabled {
                    opacity: 0.6;
                }
                
                .left {
                    display: flex;
                    align-items: center;
                    
                    .payment-icon {
                        width: 40rpx;
                        height: 40rpx;
                        margin-right: 16rpx;
                    }
                    
                    .name {
                        font-size: 28rpx;
                        color: #333;
                        margin-left:16rpx;
                        margin-right: 16rpx;
                    }
                    
                    .amount {
                        font-size: 28rpx;
                        color: #F53F3F;
                    }
                    .reduce {
                        color: #999;
                        margin-left: 16rpx;
                    }
                }
                
                .right {
                    .insufficient {
                        font-size: 24rpx;
                        color: #999;
                    }
                }
            }
        }
    }

    .payment-select {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 20rpx;
        background: #fff;
        border-top: 0.5px solid #F2F3F5;
        border-bottom: 0.5px solid #F2F3F5;
        margin: 20rpx 0;

        .payment-select-left {
            display: flex;
            align-items: center;
            font-family: "PingFang SC";
            color: #131315;
            font-size: 32rpx;
            font-weight: 500;
            
            text {
                margin-right: 40rpx;
            }
            
            .payment-method {
                display: flex;
                align-items: center;
                
                text {
                    color: #323233;
                    font-size: 28rpx;
                }
                text:first-child {
                    margin-right: 12rpx;
                }

                .amount{
                    color: #F53F3F;
                }
            }
        }

        .payment-select-right {
            display: flex;
            align-items: center;
        }
    }
}
</style>