<template>
<view class="content  topic">
  <block v-if = 'plistsData.tp_skin == 1'>
  <info :listsData="listsData" :infoList="infoList"></info>
  </block>
  <block v-if = 'plistsData.tp_skin == 2'>
  <skinOne :listsData="listsData" :infoList="infoList"></skinOne>
  </block>
  <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
  <chat-tip></chat-tip>
</view>
</template>

<script>
import {wxShare} from '../common/mixin'
import info from "../components/info.vue"
import skinOne from "./components/skinOne.vue"

import search from "../components/search.vue"
import myIcon from "../components/icon.vue"
import {
  uniLoadMore,
  uniIcons,
  uniList,
  uniListItem
} from '@dcloudio/uni-ui'
import myDialog from "../components/dialog.vue"
import {
  checkAuth,
  formatImg,
  navigateTo
} from "../common/index.js"
export default {
  components: {
    info,
    uniLoadMore,
    uniIcons,
    uniList,
    uniListItem,
    search,
    myDialog,
    skinOne,
    myIcon
  },
  data() {
    return {
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了"
      },
      params: {
        page: 1,
        rows: 6,
        id:""
      },
      listsData: [],
      infoList:[],
      plistsData:[],

      
    }
  },
  mixins: [wxShare],
  onLoad(options) {
    if (options.id){
      this.params.id = options.id
      this.getData()
    } 
   
  },
  onShow() {
  
  },
  methods: {
    getData() {
      if (this.params.page == 1) {
        this.listsData = []
      }
      this.get_status = "loading"
      this.$ajax.get('topic/index', this.params, (res) => {
        
        if (res.data.code == 1) {
          this.listsData = this.listsData.concat(res.data.info)
          this.plistsData = res.data.topic
          uni.setNavigationBarTitle({
            title:res.data.topic.tp_title+'专题条目列表'
          })
          if (res.data.info.length < this.params.rows) {
            this.get_status = "noMore"
          } else {
            this.get_status = "more"
          }
        } else {
          this.get_status = "noMore"
          
        }
        if (res.data.share&&res.data.share.title) {
          this.share = res.data.share
          this.getWxConfig()
        }
      }, (err) => {
        console.log(err)
      })
    },
  },
  onReachBottom() {
    this.params.page = this.params.page + 1
    this.getData()
  }
 
}
</script>

<style>
.p-top-180 {
  padding-top: 170upx;
}

.sel-tab {
  box-shadow: 0 0 0 #fff;
}

.seach_btn {
  color: #28bdfb;
  border-radius: 40upx;
  border: 3upx solid #28bdfb;
  padding: 8upx 10upx;
  margin: 5upx 20upx;
  align-items: center;
}

.seach_btn text {
  color: #28bdfb;
  font-size: 24upx;
}
/* #ifdef H5 */
.top-box {
  position: fixed;
  top: 44px;
  width: 100%;
  background-color: #fff;
  z-index: 100;
  align-items: center;
  justify-content: space-between;
}
.top-box search{
  flex: 1;
}

.screen-tab {
  top: 44px;
  margin-top: 90upx;
}

.screen-panel {
  top: 44px;
  margin-top: 170upx;
}
/* #endif */
/* #ifndef H5 */
.top-box {
  position: fixed;
  top: var(--window-top);
  width: 100%;
  background-color: #fff;
  z-index: 100;
  align-items: center;
  justify-content: space-between;
}
.top-box search{
  flex: 1;
}

.screen-tab {
  top: var(--window-top);
  margin-top: 90upx;
}

.screen-panel {
  top: var(--window-top);
  margin-top: 170upx;
}
/* #endif */
</style>
