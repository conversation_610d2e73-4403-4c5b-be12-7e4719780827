<template>
  <view class="w">
    <!-- <view class="info">
      <div class="detail">
        <view v-if="info.owner_name_cc">业主姓名：{{ info.owner_name }}</view>
        <view v-if="info.owner_tel_cc">业主电话：{{ info.owner_tel }}</view>
        <view v-if="info.community_name"
          >小区名称：{{ info.community_name }}</view
        >
        <view>
          <text v-if="info.loudong || info.loudong != 0"
            >{{ info.loudong }}号楼</text
          >
          <text v-if="info.danyuan || info.danyuan != 0"
            >{{ info.danyuan }}单元</text
          >
          <text v-if="info.danyuan || info.danyuan != 0"
            >{{ info.fanghao }}号</text
          >
        </view>

        <view v-if="info.is_valid == 1">有效房源</view>
        <view v-else>无效房源</view>
      </div>
    </view> -->
    <!-- <view @click="add" class="add">跟进</view> -->
    <view class="status">
      <view class="list">
        <view class="info">
          <text>小区名称</text>
          <template v-if="community_abled == 1">
            <view class="community_name_box" @click="toSearchCommunity">
              <my-input
                class="community_name"
                label=""
                :value="community_name"
                disabled
                type="text"
                placeholder="请选择小区或留空"
              ></my-input>
            </view>
          </template>
          <template v-else>
            <view class="community_name_box">
              <my-input
                class="community_name"
                label=""
                :value="community_name"
                disabled
                type="text"
                placeholder=" "
              ></my-input>
            </view>
          </template>
          <!-- <input type="text" v-model="params.community_name" /> -->
        </view>
        <view class="info-num" v-if="params.info_type == 1">
          <text style="margin-left:0">楼栋</text
          ><input type="number" name="" id="" v-model="params.loudong" />
          <text>单元</text
          ><input type="number" name="" id="" v-model="params.danyuan" />
          <text>房号</text
          ><input type="number" name="" id="" v-model="params.fanghao" />
        </view>
        <view class="info">
          <text>联系人</text>
          <input type="text" v-model="params.owner_name" />
        </view>
        <view class="info">
          <text>电话号码</text>
          <input type="text" v-model="params.owner_tel" />
        </view>
        <view class="info">
          <text>跟进内容 <text class="red">*</text></text>
          <radio-group class="uni-list" @change="radioChange">
            <label
              class="uni-list-cell uni-list-cell-pd"
              v-for="(item, index) in radioItems"
              :key="index"
            >
              <view>
                <radio
                  :id="item.name"
                  :value="item.name"
                  :checked="index === current"
                ></radio>
              </view>
              <view>
                <label class="label-2-text" :for="item.name">
                  <text>{{ item.value }}</text>
                </label>
              </view>
            </label>
          </radio-group>
        </view>

        <view class="info">
          <text></text>
          <textarea
            name=""
            id="content"
            cols="30"
            rows="10"
            v-model="params.content"
          ></textarea>
        </view>
      </view>
      <view class="update" @click.prevent.stop="update">更新状态</view>
    </view>
    <!-- <uni-steps :options="list" :active="active" class="steps" /> -->

    <view class="tab">
      <!-- <aloys-tab :tabs="tabs" @change="onTabChange"> -->
      <!-- <view slot="content0" class="xxx"> -->
      <!-- {{ info.content }} -->
      <prettySteps v-for="(item, index) in list2" :item="item" :key="index">
      </prettySteps>
      <!-- </view> -->
      <!-- <view slot="content1" class="xxx">
          <uni-steps
            :options="list2"
            :active="active2"
            class="steps"
            direction="column"
          />
        </view> -->
      <!-- <view slot="content2" class="xxx">C</view> -->
      <!-- </aloys-tab> -->
      <uni-load-more
        :status="get_status"
        :content-text="content_text"
      ></uni-load-more>
    </view>
  </view>
</template>
<script>
import MyIcon from "../../components/myIcon.vue";
// import aloysTab from "../components/aloys-tab/aloys-tab.vue";
import prettySteps from "@/components/pretty-steps/pretty-steps.vue";
import { uniLoadMore } from "@dcloudio/uni-ui";
import myInput from "@/components/form/newInput.vue";

export default {
  components: {
    MyIcon,
    // aloysTab,
    prettySteps,
    uniLoadMore,
    myInput,
  },
  data() {
    return {
      current: 0,
      radioItems: [
        {
          name: "1",
          value: "有效房源",
          // checked: "true",
        },
        {
          name: "2",
          value: "无效房源",
        },
      ],
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了",
      },
      info_params: {
        page: 1,
        rows: 20,
        info_type: 1,
        info_id: "",
        // buildid:''
      },
      tabs: [
        {
          title: "报备详情",
        },
        // {
        //   title: "跟进列表",
        // },
        // {
        //   title: "资料照片",
        // },
      ],
      active2: "",
      // list: [
      //   { title: "待审核" },
      //   { title: "报备有效" },
      //   { title: "已到访" },
      //   { title: "已认筹" },
      //   { title: "已认购" },
      //   { title: "已成交" },
      //   { title: "已无效" },
      // ],
      list2: [],
      params: {
        info_id: "",
        info_type: "",
        is_valid: 1,
        content: "",
        buildid: "",
        loudong: "",
        danyuan: "",
        fanghao: "",
        owner_name: "",
        owner_tel: "",
      },
      community_abled: "",
      param: {
        page: 1,
        rows: 20,
        info_type: 1,
        info_id: "",
      },
      community_name: "",
      // info: "",
    };
  },
  computed: {},
  onLoad(options) {
    console.log(options);
    if (options) {
      this.info_params.info_type = options.type;
      this.info_params.info_id = options.id;
      this.params.info_type = options.type;
      this.params.info_id = options.id;
      this.param.info_type = options.type;
      this.param.info_id = options.id;
      // this.params.buildid = options.buildid;
    }
    uni.$on("updataPage", () => {
      this.getData();
    });
    uni.$on("hideShareMenu", () => {
      this.getData();
    });
    this.getData();
  },
  onUnload() {
    uni.$off("updataPage");
    uni.$off("hideShareMenu");
  },
  onShow() {
    if (uni.getStorageSync("smallArea")) {
      let smallArea = JSON.parse(uni.getStorageSync("smallArea"));
      this.community_name = smallArea.community_name || smallArea.name;
      this.params.buildid = smallArea.id;
    }
    uni.removeStorageSync("smallArea");
  },
  methods: {
    getData() {
      if (this.info_params.page == 1) {
        this.listsData = [];
      }
      this.get_status = "loading";
      this.$ajax.get("infoServicer/infoFollowList", this.info_params, (res) => {
        if (res.data.code == 1) {
          // let item1
          this.list2 = res.data.list;
          if (this.info_params.page == 1) {
            //  this.listsData = res.data.list;
            this.list2 = res.data.list;
          } else {
            this.list2 = this.list2.concat(res.data.list);
          }
          if (res.data.list.length < this.info_params.rows) {
            this.get_status = "noMore";
            // this.showLike = true;
          } else {
            this.get_status = "more";
          }
        }
      });
      this.$ajax.get("infoServicer/infoFollowForm", this.param, (res) => {
        if (res.data.code == 1) {
          // this.params.is_valid = res.data.is_valid
          this.params.content = "有效房源";
          this.params.buildid = res.data.cid;
          // this.params.community_name = res.data.community_name
          this.community_name = res.data.community_name;
          this.params.loudong = res.data.loudong;
          this.params.danyuan = res.data.danyuan;
          this.params.fanghao = res.data.fanghao;
          this.params.owner_name = res.data.owner_name;
          this.params.owner_tel = res.data.owner_tel;
          this.community_abled = res.data.community_abled;
          // this.current = res.data.is_valid-1
        }
        console.log(res.data, "-----");
      });
    },
    radioChange: function(e) {
      this.params.is_valid = e.detail.value;
      // this.params.is_valid = parseInt(e.target.value);
      console.log(this.params.is_valid);
      if (this.params.is_valid == 1) {
        this.params.content = "有效房源";
      } else if(this.params.is_valid == 2){
        this.params.content = "无效房源";
      }
    },
    toSearchCommunity() {
      this.$navigateTo("/user/search_areas");
      uni.$once("newCommunity", (data) => {
        this.community_name = data.name;
        this.params.buildid = data.id;
        console.log(data, "--");
      });
    },
    update() {
      uni.showLoading({
        title: "正在提交",
        mask: true,
      });
      console.log(this.params);
      this.$ajax.post("infoServicer/addInfoFollow", this.params, (res) => {
        if (res.data.code == 1) {
          uni.showToast({
            title: res.data.msg,
          });
          uni.$emit("updataPage");
          this.getData();
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none",
          });
        }
      });
    },
    change() {
      if (this.active < this.list1.length - 1) {
        this.active += 1;
      } else {
        this.active = 0;
      }
    },
    add() {
      this.$navigateTo(
        "status?type=" +
          this.info_params.info_type +
          "&id=" +
          this.info_params.info_id
      );
    },
    onReachBottom() {
      if (this.get_status == "more") {
        this.info_params.page = this.info_params.page + 1;
        this.getData();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.w {
  background: #fff;
  .tab {
    padding: 50rpx;
  }
  > .steps {
    margin-top: 50rpx;
  }
  .add {
    // display: inline-block;
    margin: 30rpx 30rpx 0 30rpx;
    background-image: linear-gradient(90deg, #869df7 0%, #5b76ec 100%);
    color: #fff;
    padding: 20rpx 0;
    border-radius: 10rpx;
    text-align: center;
  }
  .info {
    padding: 0 30rpx 0 30rpx;
    .detail {
      color: #fff;
      padding: 30rpx;
      border-radius: 10rpx;
      background-image: linear-gradient(90deg, #869df7 0%, #5b76ec 100%);
      .iconfont {
        position: relative;
        top: 4rpx;
        left: 26rpx;
      }
      > view {
        line-height: 50rpx;
      }
    }
  }
  .status {
    background: #fff;
    .update {
      margin: 0 30rpx 0 30rpx;
      padding: 20rpx 0;
      background: #0074fd;
      color: #fff;
      border-radius: 40rpx;
      text-align: center;
    }
    .list {
      padding: 30rpx 30rpx 0 30rpx;
      .info-num {
        display: flex;
        margin-bottom: 40rpx;
        align-items: center;
        justify-content: space-between;
        input {
          border: 2rpx solid #b1b1b1;
          flex: 1;
          color: #b1b1b1;
          height: 60rpx;
          line-height: 60rpx;
          border-radius: 10rpx;
          padding-left: 10rpx;
        }
        text {
          margin: 0 20rpx;
        }
      }
      .info {
        display: flex;
        margin-bottom: 40rpx;
        .community_name_box {
          flex: 1;
        }
        .community_name {
          flex: 1;
          height: 60rpx;
          border: 2rpx solid #b1b1b1;
          color: #b1b1b1;
          font-size: 28rpx;
          line-height: 60rpx;
          border-radius: 10rpx;
          padding-left: 10rpx;
          .uni-input-placeholder {
            font-size: 28rpx;
          }
          ::v-deep .uni-input-input {
            font-size: 28rpx !important;
          }
        }
        .red {
          color: red;
        }
        .uni-list {
          display: flex;
          align-items: center;
          .uni-list-cell {
            margin-right: 20rpx;
            display: flex;
            align-items: center;
          }
        }
        input {
          flex: 1;
          height: 60rpx;
          border: 2rpx solid #b1b1b1;
          color: #b1b1b1;
          font-size: 28rpx;
          line-height: 60rpx;
          border-radius: 10rpx;
          padding-left: 10rpx;
          .into {
            position: absolute;
            right: -130rpx;
            top: 40rpx;
          }
        }
        textarea {
          flex: 1;
          height: 200rpx;
          border: 2rpx solid #b1b1b1;
          margin-top: 10rpx;
          padding: 20rpx;
          line-height: 42rpx;
          color: #b1b1b1;
          font-size: 28rpx;
        }
        text {
          width: 160rpx;
          height: 60rpx;
          line-height: 60rpx;
        }
      }
    }
  }
}
</style>
