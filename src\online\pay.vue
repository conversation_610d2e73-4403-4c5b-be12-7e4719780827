<template>
<div class="pay">
    <!-- <view class="money">
        <view class="pay_money">{{pay_money}}</view>
        <view class="tip">定金支付金额（元）</view>
    </view> -->
    <view class="title bottom-line">
        <text>户型信息</text>
    </view>
    <view class="order-item" @click="toHouse()">
        <view class="order-content flex-box">
            <view class="img-box">
                <image :src="info.pic | imgUrl('w_240')" mode="aspectFill"></image>
            </view>
            <view class="flex-1">
                <view class="order-desc">{{info.title}}</view>
                <view class="name">{{info.name}} {{info.huxing}}</view>
            </view>
        </view>
    </view>
    <view class="uni-list">
        <view class="title bottom-line">
            <text>选择支付方式</text>
        </view>
        <radio-group @change="radioChange">
            <label class="pay_item flex-box input-row">
                <my-icon type="weixinzhifu" color="#09bb07" size="24"></my-icon>
                <view class="pay_name flex-1">微信支付(支持各大银行)</view>
                <radio color="#f65354" value="wechatpay" checked />
            </label>
        </radio-group>
        <view style="margin-top:20rpx;">
            <my-select v-if="order_info.coupon&&order_info.coupon.length>0" :value="coupon_id" @change="pickerChange" label="请选择优惠券" :range="order_info.coupon"></my-select>
            <checkbox-group @change="onChange">
                <label class="pay_item flex-box input-row">
                    <view class="pay_name flex-1">是否使用余额(您的总余额：{{order_info.balance}})</view>
                    <checkbox color="#f65354" :value="use_balance" :checked="use_balance==='1'" />
                    <text>{{use_balance=='1'?'使用':'不使用'}}</text>
                </label>
            </checkbox-group>
        </view>
    </view>
    <view class="money_detail">
        <view class="row flex-box">
            <view class="label">订单总金额：</view>
            <view class="val">￥{{order_info.money}}</view>
        </view>
        <view class="row flex-box" v-if="coupon_id">
            <view class="label">使用优惠券：</view>
            <view class="val">-￥{{coupon_money}}</view>
        </view>
        <view class="row flex-box" v-if="use_balance==1">
            <view class="label">使用余额：</view>
            <view class="val">-￥{{order_info.money-coupon_money>order_info.balance?order_info.balance:(order_info.money-coupon_money>0?order_info.money-coupon_money:0)}}</view>
        </view>
        <view class="row flex-box">
            <view class="label">还需支付金额：</view>
            <view class="val pay_money">￥{{pay_money}}</view>
        </view>
    </view>
    <view class="btn-box">
        <button class="default" @click="handlePay()">{{order_info.money==0?'完成':'立即支付'}}</button>
    </view>
</div>
</template>

<script>
import myIcon from "../components/icon.vue"
import mySelect from "../components/form/mySelect.vue"
import {
    navigateTo,
    formatImg,
    isIos,
} from '../common/index.js'
import wx from "weixin-js-sdk"
export default {
    data() {
        return {
            params:{},
            payType:'',
            use_balance:"1",
            coupon_id:"",
            coupon_money:0,
            order_info:{
                money:0,
                balance:0
            },
            info:{
                title:'',
                name:'',
                huxing:''
            }
        }
    },
    onLoad(options) {
        if (options.order_id) {
            this.order_id = options.order_id
            this.getData()
        }
        this.getConfig().then(res2=>{
            wx.ready( () => {
                wx.checkJsApi({
                    jsApiList: [
                    'chooseWXPay',
                    'hideOptionMenu'
                    ],
                    success: (res) => {
                        wx.hideOptionMenu();
                        console.log(JSON.stringify(res));
                    },
                    fail:err=>{
                        console.log(err)
                    }
                })
            })
        })
    },
    components: {
        myIcon,
        mySelect
    },
    computed:{
        pay_money(){
            let money = 0
            if(this.use_balance=='1'){
                money = this.order_info.money-this.coupon_money-this.order_info.balance
            }else{
                money = this.order_info.money-this.coupon_money
            }
            if(money<0){
                return 0 
            }
            return money
        }
    },
    filters:{
        imgUrl(val,param){
            return formatImg(val,param)
        },
    },
    methods: {
        getConfig(){
            return new Promise((resolve,reject)=>{
                let url;
                if(isIos()){
                    url = this.$store.state.firstUrl
                }else{
                    url = window.location.href
                }
                this.$ajax.get('/wechat/index/signaturePay.html',{url:url},res=>{
                    if(res.data.code == 1){
                        // res.data.config.debug = true
                        res.data.config.jsApiList = ['chooseWXPay','hideOptionMenu']
                        wx.config(res.data.config)
                        resolve(res.data)
                    }else{
                        reject(res.data)
                    }
                },err=>{
                    reject(err)
                })
            })
        },
        getData(){
            this.$ajax.get('online/userDiscount.html',{order_id:this.order_id},res=>{
                if(res.data.code === 1){
                    let coupon = [{name:"不使用",value:0}]
                    res.data.order_info.coupon.map(item=>{
                        coupon.push({name:item.name,value:item.coupon_id,money:item.money||''})
                    })
                    res.data.order_info.coupon = coupon
                    this.order_info = res.data.order_info
                    this.info = res.data.info
                }
            })
        },
        radioChange(e){
            this.payType = e.detail.value
        },
        onChange(e){
            if(e.detail.value.length>0){
                this.use_balance = "1"
            }else{
                this.use_balance = "0"
            }
        },
        pickerChange(e){
            this.coupon_id = e.value
            this.order_info.coupon.map(item=>{
                if(item.value==e.value){
                    if(item.name=='不使用'){
                        this.coupon_money = 0
                    }else{
                        this.coupon_money = item.money
                    }
                }
            })
        },
        handlePay() {
            this.$ajax.post('online/recharge',{
                order_id: this.order_id,
                coupon_id:this.coupon_id,
                use_balance:this.use_balance
            },(payInfo)=>{
                if(payInfo.data.code == 1){
                    if(payInfo.data.pay_status){ //如果直接支付成功，不用再调起支付
                        uni.showToast({
                            title: payInfo.data.msg
                        })
                        setTimeout(() => {
                            // uni.navigateBack()
                            navigateTo(`/online/order_detail?id=${this.order_id}`)
                            uni.$emit('getOrderList',{})
                        }, 1500)
                        return
                    }
                    this.choosePay(payInfo.data.data.timeStamp,payInfo.data.data.nonceStr,payInfo.data.data.package,payInfo.data.data.signType,payInfo.data.data.paySign)
                }else{
                    uni.showToast({
                        title:payInfo.data.msg,
                        icon:"none",
                        duration:5000
                    })
                }
            })
        },
        choosePay(timeStamp, nonceStr, payPackage, signType, paySign){
            console.log("开始调起支付")
            wx.chooseWXPay({
                // provider: 'wxpay',
                timestamp:timeStamp,
                nonceStr:nonceStr,
                package:payPackage,
                signType:signType,
                paySign:paySign,
                success: (res)=> {
                    uni.showToast({
                        title:"支付成功"
                    })
                    setTimeout(()=>{
                        // uni.navigateBack()
                        navigateTo(`/online/order_detail?id=${this.order_id}`)
                        uni.$emit('getOrderList',{})
                    },1500)
                },
                fail: function (err) {
                    console.log("支付失败：",err);
                    uni.showToast({
                        title:err.err_desc||err.errMsg,
                        icon:"none",
                        duration:5000
                    })
                }
            });
        },
        toHouse(){
            if(this.info&&this.info.id&&this.info.online_id){
                navigateTo(`/online/house_detail?id=${this.info.id}&online_id=${this.info.online_id}`)
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.money {
    text-align: center;
    padding: 30rpx 20rpx;
    background-color: #f65354;
    color: #fff;

    .pay_money {
        margin-bottom: 20rpx;
        font-size: 50rpx;
        font-weight: bold;
    }
}


.order-item{
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
    .order-content{
        padding: 20rpx;
        .img-box{
            width: 170rpx;
            height: 130rpx;
            margin-right: 20rpx;
            image{
                width: 100%;
                height: 100%;
            }
        }
        .order-desc{
            line-height: 1.4;
            font-size: 30rpx;
            height: 80rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            color: #333;
        }
        .name{
            margin-top: 10rpx;
            color: #666;
            font-size: 28rpx;
        }
    }
}

.title {
    padding: 20rpx 20rpx 20rpx 40rpx;
    font-size: 32rpx;
    position: relative;
    background: #fff;

    &::before {
        content: "";
        position: absolute;
        left: 20rpx;
        top: 20rpx;
        bottom: 20rpx;
        width: 6rpx;
        background-color: #f65354
    }

    .close {
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        padding: 10rpx;
        transform: rotate(45deg);
    }
}

.input-row{
    display: flex;
    padding: 16upx 24upx;
    line-height:62upx;
    background-color: #fff;
}

.pay_item{
    align-items: center;
    justify-content: space-between;
    .pay_name{
        margin-left: 15rpx;
    }
}

.money_detail{
    margin-top: 20rpx;
    padding: 10rpx 0;
    background-color: #fff;
    .row{
        justify-content: space-between;
        .pay_money{
            font-size: 36rpx;
            font-weight: bold;
            color: #f65354;
        }
    }
}
</style>
