<template>
  <view>
    <view class="tabs">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="current == index && 'active'"
        @click="current = index"
      >
        {{ tab.title }}
      </view>
    </view>
    <swiper :current="current" class="swiper-box" @change="onChange">
      <swiper-item v-for="(item, index) in tabs" :key="index">
        <scroll-view scroll-y="true" style="height: 100%;">
          <!-- #ifdef MP -->
          <slot name="{{'content' + index}}"></slot>
          <!-- #endif -->
          <!-- #ifndef MP -->
          <slot :name="'content' + index"></slot>
          <!-- #endif -->
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
export default {
  name: "swiperTab",
  props: {
    currentTab: {
      type: Number,
      default: 0,
    },
    tabs: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      current: this.currentTab,
    };
  },
  methods: {
    onChange(e) {
      this.current = e.detail.current;
      this.$emit("change", e.detail.current);
    },
  },
};
</script>
<style lang="scss" scoped>
.tabs {
  height: 60rpx;
  display: flex;
  margin-bottom: 30rpx;
  .tab-item {
    text-align: center;
    color: #333333;
    margin-right: 30rpx;
    &.active {
      color: #0e67d1;
      border-bottom: 4rpx solid #0e67d1;
    }
  }
}
</style>
