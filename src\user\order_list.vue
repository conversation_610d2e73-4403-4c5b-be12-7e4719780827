<template>
  <view>
    <view class="order_list">
      <view class="order_item" v-for="(order, index) in listsData" :key="order.id">
        <view class="header flex-row">
          <text>订单号：{{order.out_trade_no}}</text>
        </view>
        <view class="main">
          <view class="title" @click="$navigateTo(`/pages/groups/detail?id=${order.group_id}`)">{{order.group_name}}</view>
          <view class="time">{{order.ctime}}</view>
        </view>
        <view class="footer flex-row">
          <view class="flex-row user_info">
            <my-icon type="yonghu" size="32rpx" color="#1acdac"></my-icon>
            <text class="name">{{order.cname}}</text>
            <text class="tel" v-if="order.tel">{{order.tel}}</text>
          </view>
          <view class="btn" :class="{disable:order.status}" @click="showTip(order)">立即使用</view>
        </view>
        <text class="status" v-if="order.status==1">已退款</text>
        <text class="status success" v-else>{{order.write_off?'已核销':'待核销' }}</text>
      </view>
    </view>
    <my-popup ref="tip_popup" position="center" height="460rpx" :touch_hide="false">
      <view class="tip-box">
        <view class="tip_content">
          <view class="title">使用指南</view>
          <view class="text">亲，请前往活动详情页查看使用细则或拨打咨询电话联系工作人员进行核销。</view>
          <view class="btn_group flex-row">
            <view class="btn1" @click="toGroup">前往查看</view>
            <view class="btn2" @click="handleTel">拨打电话</view>
          </view>
          <view class="close_btn" @click="$refs.tip_popup.hide()">
            <my-icon type="guanbi" color="#999" size="52rpx"></my-icon>
          </view>
        </view>
      </view>
    </my-popup>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
  </view>
</template>

<script>
import { uniLoadMore } from '@dcloudio/uni-ui'
import myIcon from '@/components/myIcon'
import myPopup from '@/components/myPopup'
export default {
  components: {
    uniLoadMore,
    myIcon,
    myPopup
  },
  data() {
    return {
      get_status: 'loading',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      },
      page: 1,
      listsData: [],
      currentOrder:{}
    }
  },
  onLoad() {
    this.getData()
  },
  methods: {
    getData() {
      this.get_status = 'loading'
      this.$ajax.get('order/myGroupOrder.html', {page: this.page, rows: 20}, res => {
        if (res.data.code == 1) {
          this.listsData = this.listsData.concat(res.data.list)
          if(res.data.list.length<20){
            this.get_status = "noMore"
          }else{
            this.get_status = "more"
          }
        } else {
          this.get_status = 'noMore'
        }
      })
    },
    showTip(order){
      if(order.status == 1){
        return
      }
      this.currentOrder = order
      this.$refs.tip_popup.show()
    },
    toGroup(){
      if(this.currentOrder.group_id){
        this.$navigateTo(`/pages/groups/detail?id=${this.currentOrder.group_id}`)
      }
    },
    handleTel(){
      if(this.currentOrder.group_tel){
        uni.makePhoneCall({
          phoneNumber: this.currentOrder.group_tel
        })
      }else{
        uni.showToast({
          title: '该团购活还没有联系电话',
          icon: 'none'
        })
      }
    }
  },
  onReachBottom(){
    if(this.get_status!='more'){
      return
    }
    this.page++
    this.getData()
  }
}
</script>

<style scoped lang="scss">
view{
  display: flex;
  flex-direction: column;
  &.flex-row{
    flex-direction: row;
  }
}
.order_list{
  padding: 24rpx 0;
  background-color: #f8f8f8;
  .order_item{
    margin: 24rpx 48rpx;
    padding: 0 24rpx;
    background-color: #fff;
    margin-bottom: 24rpx;
    border: 1rpx solid #eee;
    box-shadow: 0 4rpx 10rpx 0 rgba(0,0,0,0.03);
    border-radius: 16rpx;
    position: relative;
    overflow: hidden;
    .header{
      justify-content: space-between;
      padding: 24rpx 0;
      font-size: 24rpx;
      color: #999;
      border-bottom: 1rpx dashed #eee;
    }
    .main{
      .title{
        margin: 24rpx 0;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        line-height: 1.5;
        font-size: 32rpx;
        color: #333;
      }
      .time{
        font-size: 22rpx;
        color: #999;
      }
    }
    .footer{
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 0;
      font-size: 28rpx;
      .user_info{
        font-size: 28rpx;
        align-items: center;
        .name{
          margin: 0 18rpx;
          color: #1acdac;
        }
      }
      .prelogo{
        width: 32rpx;
        height: 32rpx;
        border-radius: 50%;
        margin-right: 16rpx;
        background-color: #d8d8d8;
      }
      .btn{
        width: 128rpx;
        height: 48rpx;
        border-radius: 24px;
        border-radius: 24px;
        line-height: 48rpx;
        text-align: center;
        color: #fff;
        font-size: 22rpx;
        background: $uni-color-primary;
        box-shadow: 0 4rpx 12rpx 0 rgba($color: $uni-color-primary, $alpha: 0.4);
        &.disable{
          background: #d8d8d8;
          box-shadow: none
        }
      }
    }

    .status{
        position: absolute;
        right: -32rpx;
        top: 12rpx;
        font-size: 22rpx;
        color: #fff;
        background-color: #d8d8d8;
        display: inline-block;
        padding: 4rpx 32rpx;
        transform: rotate(45deg);
        &.success{
          background-color: #1acdac;
        }
      }
  }
}

.tip-box{
  box-sizing: border-box;
  width: 80vw;
  margin: auto;
  .tip_content{
    background-color: #fff;
    padding: 48rpx;
    border-radius: 18rpx;
    position: relative;
  }
  .title{
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
    text-align: center;
  }
  .text{
    color: #666;
    line-height: 1.5;
    margin-bottom: 48rpx;
  }

  .btn_group{
    padding: 0 48rpx;
    box-sizing: border-box;
    width: 100%;
    justify-content: space-between;
    .btn2{
      line-height: 64rpx;
      width: 160rpx;
      max-width: 160rpx;
      text-align: center;
      border-radius: 32rpx;
      font-size: 28rpx;
      color: #fff;
      background-image: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
      box-shadow: 0 0 4px 0 rgba(255,80,0,0.30);
    }
    .btn1{
      line-height: 64rpx;
      width: 160rpx;
      max-width: 160rpx;
      text-align: center;
      border-radius: 32rpx;
      font-size: 28rpx;
      color: #fff;
      background:  #65AEFB;
      box-shadow: 0 0 4px 0 rgba(0,0,0,0.05);
    }
  }
  .close_btn{
    position: absolute;
    top: 0;
    right: 24rpx;
    width: 48rpx;
    height: 48rpx;
    margin: auto;
    margin-top: 24rpx;
  }
}
</style>
