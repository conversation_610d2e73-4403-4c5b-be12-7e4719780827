import {showModal, navigateTo, isIos} from './index'
import wx from 'weixin-js-sdk'
import {statistics} from './statistics'
var updateVip = {
	methods:{
		showUpdateVip(content="您的权限不足是否升级VIP"){
			showModal({
				title:"请升级vip",
				content:content,
				confirm:function(){
					navigateTo('/pages/my/member_upgrade')
				}
			})
		}
	}
}

var checkEnv = {
	created(){
		var ua = window.navigator.userAgent.toLowerCase();
		if(ua.match(/MicroMessenger/i) !== 'micromessenger'){
			window.location.href = location.pathname.replace('/h5/','/m/')+location.search
			return;
		}
	}
}
var wxShare = {
	methods: {
		getWxConfig(js_api_list){
			statistics()
			if(!this.share){
				return
			}
			let url;
			if(isIos()){
				url = this.$store.state.firstUrl
			}else{
				url = window.location.href
			}
			this.$ajax.get('/wechat/index/signature.html',{url:url},res=>{
				if(res.data.code == 1){
					res.data.config.jsApiList = js_api_list||['updateAppMessageShareData','updateTimelineShareData']
					// if (open_tag_list){
						res.data.config.openTagList = ['wx-open-launch-weapp']
					// }
					res.data.config.debug = false
					wx.config(res.data.config)
					this.initShare()
				}
			})
		},
		initShare(){
			wx.ready(() => {   //需在用户可能点击分享按钮前就先调用
				wx.updateAppMessageShareData({ 
					title: this.share.title||'', // 分享标题
					desc: this.share.content, // 分享描述
					link: this.share.link || window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
					imgUrl: this.share.pic+'?x-oss-process=style/w_220', // 分享图标
					success: function () {
						// 设置成功
						// console.log("设置成功")
					}
				})
				wx.updateTimelineShareData({ 
					title: this.share.title||'', // 分享标题
					link: this.share.link || window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
					imgUrl:this.share.pic+'?x-oss-process=style/w_220', // 分享图标
					success:  ()=> {
						// 设置成功
						// console.log("设置成功")
						console.log(this.share)
					}
				})
			});
		}
	}
}
module.exports = {
	updateVip,
	checkEnv,
	wxShare
}