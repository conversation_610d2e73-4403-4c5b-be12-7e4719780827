<template>
  <view class="page">
    <view class="block">
      <view class="checkbox-box bottom-line">
        <view class="label">已绑定楼盘：</view>
        <view class="checkbox-row">
          <view class="checkbox-list flex-box">
            <view v-for="(item, index) in has_builds" :key="index" class="checkbox-item active">
              <text>{{ item.title }}</text>
              <view class="icon-box" @click="delBuild(index)">
                <my-icon type="qingchu" size="32rpx" color="#333"></my-icon>
              </view>
            </view>
            <view class="checkbox-item" @click="$refs.build_list.show()">+添加楼盘</view>
          </view>
        </view>
      </view>
    </view>
    <view class="btn-box">
      <view class="button" @click="subData">提交修改</view>
    </view>
    <my-popup position="bottom" ref="build_list">
      <view class="build_list">
        <view class="search_box">
          <my-search @confirm="handleSearch" v-model="keyword" placeholder="请输入楼盘名称">
            <view class="search_btn" slot="right" @click="handleSearch">搜索</view>
          </my-search>
        </view>
        <scroll-view scroll-y class="list">
          <view
            class="build_item bottom-line"
            v-for="(item, index) in build_list"
            :key="index"
            @click="onSelectBuild(item)"
            >{{ item.title }}</view
          >
          <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
        </scroll-view>
      </view>
    </my-popup>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
import mySearch from '../components/mySearch'
import myPopup from '../components/myPopup'
import { uniLoadMore } from '@dcloudio/uni-ui'
export default {
  components: {
    myIcon,
    mySearch,
    myPopup,
    uniLoadMore
  },
  data() {
    return {
      has_builds: [],
      keyword: '',
      get_status: 'more',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      },
      build_list: []
    }
  },
  onLoad(){
    this.getHasBuilds()
  },
  methods: {
    delBuild(index) {
      this.has_builds.splice(index, 1)
    },
    getHasBuilds(){
      this.$ajax.get('adviser/bindBuildInfo.html',{},res=>{
        if(res.data.code === 1){
          this.has_builds = res.data.build
        }
      })
    },
    handleSearch() {
      this.getBuilds()
    },
    getBuilds() {
      this.build_list = []
      if (!this.keyword) {
        return
      }
      this.get_status = 'loading'
      this.$ajax.get(
        'Adviser/getBuildList',
        {
          page: 1,
          rows: 20,
          key: this.keyword
        },
        res => {
          if (res.data.code == 1) {
            if (res.data.data.length < this.rows) {
              this.get_status = 'noMore'
            } else {
              this.get_status = 'more'
            }
            this.build_list = res.data.data
          } else {
            this.get_status = 'noMore'
            // uni.showToast({
            //   title: res.data.msg,
            //   icon: "none"
            // })
          }
        }
      )
    },
    onSelectBuild(e) {
      if (this.has_builds.findIndex(item => item.id == e.id) > -1) {
        uni.showToast({
          title: '您已选择该楼盘',
          icon: 'none'
        })
        return
      }
      this.$refs.build_list.hide()
      this.has_builds.push(e)
    },
    subData() {
      if (this.has_builds.length === 0) {
        uni.showToast({
          title: '请至少选择一个楼盘',
          icon: 'none'
        })
        return
      }
      let ids = this.has_builds.map(item => item.id).join(',')
      this.$ajax.get('adviser/changeProject.html',{build_ids: ids}, res=>{
        uni.showToast({
          title: res.data.msg,
          icon:'none'
        })
        if(res.data.code === 1){
          setTimeout(() => {
            this.$navigateBack()
          }, 1500)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.page{
  box-sizing: border-box;
  min-height: calc(100vh - 44px);
  padding-bottom: 160rpx;
  background-color: #fff;
}
.block {
  background-color: #fff;
}
.checkbox-box {
  padding: 24rpx 48rpx;
  .label {
    font-size: 22rpx;
    color: #666;
    margin-bottom: 24rpx;
  }
}
.checkbox-row {
  display: flex;
  align-items: center;
  background-color: #fff;
  .checkbox-list {
    flex: 1;
    flex-wrap: wrap;
    min-height: 56upx;
    margin-right: 10upx;
    .checkbox-item {
      min-width: 15%;
      box-sizing: border-box;
      line-height: 1;
      padding: 12upx 16upx;
      margin: 8upx 12rpx;
      border-radius: 4upx;
      text-align: center;
      font-size: 28rpx;
      border: 1upx solid #d8d8d8;
      color: #666;
      position: relative;
      &.active {
        border-color: $uni-color-primary;
        background-color: $uni-color-primary;
        color: #fff;
      }
      .icon-box {
        position: absolute;
        top: -16rpx;
        right: -16rpx;
      }
    }
  }
}

.build_list {
  padding: 24rpx 48rpx;
  background-color: #fff;
  .search_btn {
    color: $uni-color-primary;
  }
  .list {
    min-height: 30vh;
    max-height: 60vh;
    padding: 24rpx 0;
  }
  .build_item {
    padding: 24rpx 0;
  }
}

.btn-box {
  position: fixed;
  bottom: 24rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 24rpx 48rpx;
  .button {
    line-height: 88rpx;
    border-radius: 44rpx;
    background-color: $uni-color-primary;
    box-shadow: 0 4px 10px 0 rgba(251, 101, 106, 0.2);
    color: #fff;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
  }
}
</style>
