<template>
  <view class="picker-box">
    <view class="selecter flex-row" @click="showPicker">
      <view class="selecter-info">
        <view class="labels">
          <text class="selecter_label" v-for="(option, index) in options" :key="index">{{ option.label }}</text>
        </view>
        <view class="contents" :class="isColor==2?'input-red':''"  v-if="all_selected && all_selected.length > 0">
          <text :class="isColor==2?'input-red':''"  class="selecter_content" v-for="(item, index) in all_selected" :key="index">{{
            item | formatSelectedOk
          }}</text>
        </view>
        <view class="contents" v-else>
          <text class="selecter_placeholder">请选择</text>
        </view>
      </view>
      <my-icon type="ic_into" color="#999" size="32rpx"></my-icon>
    </view>
    <my-popup ref="picker">
      <view class="my-picker">
        <view class="label-list flex-row">
          <view
            class="label"
            v-for="(option, index) in options"
            :key="index"
            :class="{ active: current === index }"
            @click="handleCheck(index)"
          >
            <text class="label_text">{{ option.label }}</text>
            <text class="value_name">{{ option | formatSelected }}</text>
          </view>
        </view>
        <view class="operation flex-row">
          <view class="tip">请选择{{ options[current].label }}</view>
          <view class="btn" @click="handleSelectted">确定</view>
        </view>
        <picker-view :value="options[current].value" @change="onChange">
          <picker-view-column v-for="(column, index) in options[current].range" :key="index">
            <view class="picker-view-column-item" v-for="(item, idx) in column.rules" :key="idx">{{ item.name }}</view>
          </picker-view-column>
        </picker-view>
      </view>
    </my-popup>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
import myPopup from '../components/myPopup'
export default {
  components: {
    myIcon,
    myPopup,
  },
  data() {
    return {
      all_selected:[],
      current: 0,
      // options: options
    }
  },
  watch:{
    options:{
      deep:true,
      handler(val){
        if(this.watch_once){
          return
        }
        this.initValue(()=>{
          this.watch_once = true
        })
      }
    }
  },
  props:{
    isColor:Number,
    options:{
      type:Array,
      default:[]
    },
    disabled:{
      type:[String,Boolean,Number],
      default:false
    },
    disabledText:{
      type:[String],
      default:""
    }
  },
  filters: {
    formatSelected(option) {
      let value = ''
      option.value.forEach((item, index) => {
        if(item<0){
          value += ''
          return
        }
        if(option.range[index].rules.length>0){
          value += option.range[index].rules[item].name
        }
      })
      if (value) {
        return value
      } else {
        return '请选择'
      }
    },
    formatSelectedOk(selected) {
      if(!selected){
        return '请选择'
      }
      const res = selected.map(item => item.name).join('')
      return res || '请选择'
    }
  },
  created(){
    this.initValue()
  },
  methods: {
    initValue(callback){
      this.options.forEach((item, index)=>{
        if(item.value.length>0){
          this.options[index].selected = this.getSelected(item.value, index)
        }
      })
      this.all_selected = this.getAllselected()
      callback && callback()
    },
    showPicker() {
      if(this.disabled) {
        if(this.disabledText){
          uni.showToast({
            title: this.disabledText,
            icon:'none'
          });
          
        }
        return 
      } 
      this.$refs.picker.show()
      // 初始化第一项
      this.handleCheck(0)
    },
    /**
     * 选择器选择后
     */
    onChange(e) {
      // 获取选中的索引
      this.options[this.current].value = e.detail.value
      // 获取选中的值
      this.options[this.current].selected = this.getSelected(e.detail.value||0,this.current)
    },
    // 获取某项选中的值
    getSelected(valus, index){
      let selected = valus.map((item, idx) => {
        if(item>=0){
          let select_item = this.options[index].range[idx].rules[item]
          select_item.identifier = this.options[index].range[idx].identifier
          return select_item
        }else{
          return ''
        }
      })
      return selected
    },
    /**
     * 切换选择器选项
     */
    handleCheck(index) {
      this.current = index
      // 初始化选中的索引为第一项
      if (this.options[index].value.length === 0) {
        let value_len = this.options[index].range.length
        while (value_len) {
          this.options[index].value.push(0)
          value_len--
        }
      }
      // 初始化选中项的value后执行一次onChange事件用来初始化选中的值
      this.onChange({ detail: { value: this.options[index].value } })
    },
    /**
     * 点击确定
     */
    handleSelectted() {
      // 判断必选项是否必需选择且没有选择
      let no_val = this.options.find(item => item.required && item.value.length === 0)
      let noValIndex = this.options.findIndex(item => item.required && item.value.length === 0)
      if (no_val) {
        this.handleCheck(noValIndex)
        // uni.showToast({
        //   title: `请选择${no_val.label}`,
        //   icon: 'none'
        // })
        return
      }
      this.all_selected = this.getAllselected()
      // const value = this.options.map(item=>item.value)
      // console.log(value)
      this.$refs.picker.hide()
      this.$emit('change', this.all_selected)
    },
    getAllselected(){
      return this.options.filter(item=>item.selected).map(item => item.selected)
    }
  }
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.picker-box{
  background-color: #fff;
}

.selecter {
  padding: 24rpx 0;
  justify-content: space-between;
  align-items: center;
  .labels {
    margin-bottom: 24rpx;
    flex-direction: row;
    .selecter_label {
      line-height: 1;
      font-size: 22rpx;
      padding-right: 10rpx;
      color: #666;
      ~ .selecter_label {
        padding-left: 10rpx;
        border-left: 2rpx solid #666;
      }
    }
  }
  .contents {
    flex-direction: row;
    .selecter_placeholder {
      line-height: 1;
      // padding: 0 10rpx;
      font-size: 36rpx;
      // font-weight: bold;
      color: #999;
    }
    .selecter_content {
      line-height: 1;
      padding-right: 16rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      ~ .selecter_content {
        padding-left: 16rpx;
        border-left: 4rpx solid #333;
      }
    }
  }
}

.my-picker {
  background-color: #fff;
}
.label-list {
  .label {
    flex: 1;
    text-align: center;
    padding: 20rpx;
    border-top: 4rpx solid #fff;
    .label_text {
      font-size: 22rpx;
      margin-bottom: 6rpx;
      color: #666;
    }
    &.active {
      border-top: 4rpx solid $uni-color-primary;
      background-image: linear-gradient(0deg, rgba(246, 246, 246, 0) 0%, rgba(251, 101, 106, 0.1) 100%);
      .value_name {
        color: $uni-color-primary;
      }
    }
  }
}

.operation {
  padding: 24rpx 32rpx;
  align-items: center;
  background-color: #f5f5f5;
  .tip {
    flex: 1;
    text-align: center;
    font-size: 22rpx;
    color: #999;
  }
  .btn {
    color: $uni-color-primary;
  }
}
picker-view {
  height: 400rpx;
}
.picker-view-column-item {
  line-height: 70rpx;
  text-align: center;
}
.input-red{
  background-color: #f5f5f5!important;
}
</style>
