<template>
    <div class="payment-success">
        <view class="header">
            <!-- <image src="../../static/icon/tg_success.png"></image> -->
            <span class="zfsuccess">支付成功</span>
        </view>
        <view class="content">
            <view class="orderinfo">
                <text class="name">房源编号: <span class="info">{{ itemid }}</span></text>
                <text class="name">推广方式: <span class="info" v-if="itemtype === 1">置顶房源</span>
                    <span class="info" v-else-if="itemtype === 2">精选房源</span>
                    <span class="info" v-else-if="itemtype === 3">上架房源</span></text>

                <text class="name">
                    <span v-if="itemtype == '1'">置顶次数:</span>
                    <span v-if="itemtype == '2'">有效期至:</span>
                    <span v-else>上架天数:</span>
                    <span class="info">{{ expirationdesc }}</span></text>
            </view>
            <view class="tgfgx"></view>
            <view class=" free-promotion">
                <view class="msg">
                    <text class="free">免费加速</text>
                    <text>添加客服好友，截屏本页面发送给客服，免费获得1W+朋友圈推广一次</text>
                </view>
                <!-- #ifdef H5 -->
                <view class="qrcode">
                    <!-- <image @longtap="saveQrcode" src="../../static/icon/code.png"></image> -->
                </view>
                <view class="tishi">
                    <text class="toast">长按识别/保存<span class="copy" @click="copyqrcode">复制微信号</span></text>
                </view>
                <!-- #endif -->
                <!-- #ifndef H5 -->
                <view class="qrcode">
                    <image src="../../static/icon/code.png"></image>
                </view>
                <view class="tishi">
                    <text class="toast">长按识别/保存<span class="copy">复制微信号</span></text>
                </view>
                <!-- #endif -->

            </view>
        </view>
    </div>
</template>

<script>

export default {
    components: {

    },
    data() {
        return {
            itemtype: "",
            itemid: '',
            expirationdesc: ""
        };
    },
    onLoad(options) {
        console.log('options:', options);
        this.expirationdesc = options.desc;
        this.itemid = options.id;
        this.itemtype = parseInt(options.item_type);
    },
    methods: {
        hideQrcode() {
            console.log("close");
        },
        saveQrcode() {
            // 保存二维码
            console.log(1);
            let qrcode = 'https://images.tengfangyun.com/attachment/config/20201118/bd1d2f565a9da9854ec27735cd0ad215d59ccf93.jpeg';
            uni.request({
                url: qrcode,
                method: 'GET',
                responseType: 'arraybuffer',
                success: (res) => {
                    let base64 = uni.arrayBufferToBase64(res);
                    const userImageBase64 = 'data:image/jpg;base64,' + base64;
                    uni.saveImageToPhotosAlbum({
                        filePath: userImageBase64,
                        success: result => {
                            uni.showToast({
                                title: '保存成功，在微信从相册中选取识别吧',
                                icon: 'none',
                                duration: 4000
                            })
                        },
                        fail: err => {
                            console.log(err)
                            uni.showToast({
                                title: '保存失败，请重试',
                                icon: 'none'
                            })
                        }
                    })
                }
            });
        },

        copyqrcode(content) {
            content = '复制微信号'
            // #ifdef H5
            let oInput = document.createElement('input');
            oInput.value = content;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            document.execCommand("Copy"); // 执行浏览器复制命令
            uni.showToast({
                title: "复制成功",
                icon: "none"
            })
            oInput.remove()
            // #endif
            // #ifndef H5
            uni.setClipboardData({
                data: content,
                success: function () {
                    uni.showToast({
                        title: "复制成功"
                    })
                }
            });
            // #endif
        },

    },
};
</script>

<style lang="scss" scoped>
.payment-success {
    font-family: "PingFang SC";
    padding: 0rpx 32rpx;

    .header {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        image {
            width: 168rpx;
            height: 216rpx;
        }

        .zfsuccess {
            color: #131315;
            font-size: 32rpx;
            font-style: normal;
            font-weight: bold;

        }
    }

    .orderinfo {
        margin-top: 48rpx;
        display: flex;
        padding: 32rpx;
        flex-direction: column;
        align-items: flex-start;
        gap: 32rpx;
        align-self: stretch;
        background: #FFF;
        border-radius: 12rpx;
        text-align: center;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 400;

        .name {
            color: #86909C;
        }

        .info {
            padding-left: 32rpx;
            color: #131315;
        }
    }

    .tgfgx {
        // background-image: url(@/static/icon/tg_fgx.png);
        background-repeat: no-repeat;
        background-size: cover;
        width: 343px;
        height: 16px;
    }

    .free-promotion {

        padding: 32rpx;
        gap: 32rpx;
        background: #FFF;
        border-radius: 12rpx;

        .msg {
            display: flex;
            padding: 8rpx 32rpx 0px 0px;
            align-items: flex-start;
            gap: 16px;
            align-self: stretch;

            .free {
                white-space: nowrap;
                color: #00B42A;
                text-align: center;
                padding: 4rpx 16rpx;
                gap: 20rpx;
                border-radius: 8rpx;
                border: 2rpx solid #00B42A;
                background: #E8FFEA;
                font-size: 24rpx;
                font-style: normal;
                font-weight: 400;

            }
        }

        .qrcode {
            display: flex;
            padding: 32rpx 0px;
            flex-direction: column;
            align-items: center;
            gap: 32rpx;
            align-self: stretch;

            image {
                width: 320rpx;
                height: 320rpx;
                z-index: 10;
            }
        }

        .tishi {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 16px;
            align-self: stretch;
            text-align: center;
            font-size: 28rpx;
            font-style: normal;
            font-weight: 400;

            .toast {
                color: #86909C;

            }

            .copy {
                padding-left: 20rpx;
                color: #00B42A;

            }
        }
    }
}
</style>