<template>
	<view class="community_page">
		<view class="comm_tips">
			如果您只想接收这里订阅的小区房源，就不需要再订阅该小区对应的区域/商圈了 最多可以订阅5个小区
		</view>
		<view class="add flex-row" @click="toAdd">
			<view class="title">添加小区</view>
			<view class="icon">
				<myIcon type="jiahao" color="#fff" size="28rpx"></myIcon>
			</view>
		</view>
		<view class="community_list">
			<view
				class="community_item add flex-row"
				v-for="(item, index) in communityList"
				:key="item.id"
				@click="deleteItem(index)"
			>
				<view class="title">{{ item.title }}</view>
				<view class="icon icon_co">
					<view class="co">-</view>
					<!-- <myIcon type="yichu" color="#D73737"></myIcon> -->
				</view>
			</view>
		</view>
		<view class="btn  flex-row" @click="save">
			<view class="save flex-1">保存</view>
		</view>
	</view>
</template>

<script>
import myIcon from "../components/myIcon";
import { showModal } from "../common/index";
export default {
	components: {
		myIcon,
	},
	data() {
		return {
			communityList: [],
			cidArr: [],
			id: "",
		};
	},
	onLoad(options) {
		if (options.id) {
			this.id = options.id;
			if (uni.getStorageSync("communityObj")) {
				//由于 当前页面保存以后没有提交数据到服务器  保存以后到上一个页面  如果上个页面也没有提交数据再次进入到本页面就会出现数据丢失   保存到本地存储  等提交到服务器成功以后把本条本地存储清除
				this.communityList = JSON.parse(uni.getStorageSync("communityObj"));
			} else {
				this.getCommunityList();
			}
		}
		uni.$on("getChooseCommunityAgain", (data) => {
			let fannalCidArr = [];
			if (data.data.length > 0) {
				data.data.map((item) => {
					//去重
					if (!this.cidArr.includes(item.id + "")) {
						fannalCidArr.push(item);
					}
				});
			}
			this.communityList = this.communityList.concat(fannalCidArr);
		});
	},
	onUnload() {
		uni.$off("getChooseCommunityAgain");
	},
	methods: {
		toAdd() {
			let cidArr = [],
				cids = "";
			if (this.communityList.length > 0) {
				this.communityList.map((item) => {
					cidArr.push(item.id);
				});
				this.cidArr = cidArr;
				cids = this.cidArr.join(",");
			}
			this.$navigateTo("/findHouse/search_community?cids=" + cids);
		},

		getCommunityList() {
			this.$ajax.get("House/communityData", { id: this.id }, (res) => {
				if (res.data.code == 1) {
					let cids = [];
					res.data.data.map((item) => {
						item.title = item.community_name;
					});
					this.communityList = res.data.data;
				}
			});
		},
		deleteItem(idx) {
			this.i = idx
			showModal({
				content: "确认删除此小区吗？",
				confirm: (idx) => {
					console.log(idx);
					this.communityList.splice(this.i, 1);
					this.setLocalStorage();
				},
			});
		},
		setLocalStorage() {
			if (this.communityList.length){
				uni.setStorageSync("communityObj", JSON.stringify(this.communityList));
			}else {
				uni.removeStorageSync('communityObj');
			}
			
		},
		save() {
			let cid = [];
			this.communityList.map((item) => {
				cid.push(item.id);
			});
			if (cid.length>5){
				uni.showToast({
					title:"最多可以订阅5个小区",
					icon:'none'
				})
				return 
			}
			this.setLocalStorage();
			this.$navigateBack();
			setTimeout(() => {
				uni.$emit("getCheckedAgain", { type: "xiaoqu", cid });
			}, 300);
		},
	},
};
</script>

<style scoped lang="scss">
.flex-row {
	display: flex;
	flex-direction: row;
}
.comm_tips{
	padding: 40rpx;
	font-size: 24rpx;
	background: #fff;
	line-height: 2;
	color: #999;
}
.add {
	justify-content: space-between;
	align-items: center;
	padding: 32rpx 44rpx;
	background: #fff;
	margin-top: 2rpx;
	&.community_item {
		.icon {
			background: #fff;
		}
	}
	.title {
		font-size: 28rpx;
		color: #000000;
	}
	.icon {
		width: 40rpx;
		height: 40rpx;
		background: #37d747;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		overflow: hidden;
		&.icon_co {
			background: #d73737;
			.co {
				width: 16rpx;
				height: 2rpx;
				background: #fff;
			}
		}
	}
}
.btn {
	position: fixed;
	bottom: 48rpx;
	width: 100%;
	.save {
		background: #ff656b;
		margin: 0 32rpx;
		border-radius: 5px;
		padding: 26rpx 0;
		color: #fff;
		font-size: 36rpx;
		text-align: center;
		justify-content: center;
		align-items: center;
	}
}
</style>
