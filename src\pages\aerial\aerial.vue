<template>
    <view class="page">
        <view class="tab-box">
            <view class="tab-item" @click="testTabClick(index)" :class="tabIndex == index ? 'active' : ''"
                v-for="(item, index) in list" :key="index">
                {{ item }}
            </view>
        </view>
        <view class="dynamic" v-if="tabIndex == 0">
            <view class="dynamic_top">
                <view class="dynamic-one">
                    <text class="dynamic-y"></text>
                    <view class="dynamic-x">
                        <text>经纪人</text>
                        <text>发布了小视频</text>
                    </view>
                </view>
                <view class="dynamic-two">
                    2023新楼盘不多 老楼盘货源逐步减少 楼市也....
                </view>
                <view class="dynamic-three">
                    <image src="../../static/icon/video.png"></image>
                </view>
                <view class="dynamic-four">
                    <view>2023-03-09</view>
                    <view>
                        <my-icon type="yanjing" color="#8a929f" size="28rpx" style="margin-right: 6rpx"></my-icon>
                        <text>50</text>
                    </view>
                </view>
            </view>

        </view>
        <view v-if="tabIndex == 1">
            <view class="dynamic_center">
                <view class="dynamic-one">
                    <text class="dynamic-y"></text>
                    <view class="dynamic-x">
                        <text>人和天地 3室2厅2卫 145m2</text>
                        <text>总价</text>
                        <text>180</text>
                        <text>万</text>
                        <text>｜城东</text>
                    </view>
                </view>
                <view class="dynamic-two">
                    人和天地 凤凰层 豪华装修 全屋石材上墙 红木家具价格可谈 有意联系
                </view>
                <view class="dynamic-three">
                    <image src="../../static/icon/video.png"></image>
                </view>
                <view class="dynamic-five">
                    <view class="dynamic-five-center">
                        <text class="touxiang"></text>
                        <text>经纪人</text>
                        <text>世纪房地产公司</text>
                        <view class="phone">
                            <my-icon type="ic_tel" color="#fb772d" size="38rpx"></my-icon>
                        </view>
                        <view class="wei">
                            <my-icon type="ic_huifu" color="#fb772d" size="38rpx"></my-icon>
                        </view>
                    </view>
                </view>
                <view class="dynamic-four">
                    <view>2023-03-09</view>
                    <view>
                        <my-icon type="yanjing" color="#8a929f" size="28rpx" style="margin-right: 6rpx"></my-icon>
                        <text>50</text>
                    </view>
                </view>
            </view>

        </view>
        <view v-if="tabIndex == 2">
            <view class="dynamic_bottom">
                <view class="dynamic_bottom-one">
                    <view>
                        人和天地 凤凰层 豪华装修 全屋石材上墙 红木家具
                        价格可谈 有意联系
                    </view>
                    <view>

                    </view>
                    <view>
                        <text>人和天地</text>
                        <text>1分钟前</text>
                    </view>
                    <view>
                    </view>
                </view>
                <view class="dynamic_bottom-two">
                    <view>
                        人和天地 凤凰层 豪华装修 全屋石材上墙
                    </view>
                    <view>
                        <image src="../../static/icon/assress.png"></image>
                        <image src="../../static/icon/assress.png"></image>
                        <image src="../../static/icon/assress.png"></image>
                    </view>
                    <view>
                        <text>人和天地</text>
                        <text>1分钟前</text>
                    </view>
                    <view></view>
                </view>
                <template>
                    <view class="dynamic_footer">
                        <view class="dynamic_footer-top">
                            <view class="dynamic-left">
                                <view>
                                    人和天地 凤凰层 豪华装修 全屋石材上墙
                                </view>
                                <view>
                                    <text>人和天地</text>
                                    <text>1分钟前</text>
                                </view>
                            </view>
                            <view class="dynamic-right">
                                <image src="../../static/icon/assress.png"></image>
                            </view>
                        </view>
                    </view>
                    <view class="dynamic_footer-bottom">
                    </view>
                </template>
                <template>
                    <view class="dynamic_footer">
                        <view class="dynamic_footer-top">
                            <view class="dynamic-left">
                                <view>
                                    人和天地 凤凰层 豪华装修 全屋石材上墙
                                </view>
                                <view>
                                    <text>人和天地</text>
                                    <text>1分钟前</text>
                                </view>
                            </view>
                            <view class="dynamic-right">
                                <image src="../../static/icon/assress.png"></image>
                            </view>
                        </view>
                    </view>
                    <view class="dynamic_footer-bottom">
                    </view>
                </template>
            </view>
        </view>
        <view v-if="tabIndex == 3">
            <view class="radio">
                <view class="radio-left">
                    <view class="radio-one">
                        <image src="../../static/icon/assress.png"></image>
                    </view>
                    <view class="radio-two">
                        2023新楼盘不多 老楼盘
                        货源逐步减少 楼市也....
                    </view>
                    <view class="radio-three">
                        <view>
                            <text></text>
                            <text>经纪人</text>
                        </view>
                        <text>楼房</text>
                    </view>
                </view>
                <view class="radio-left">
                    <view class="radio-one">
                        <image src="../../static/icon/assress.png"></image>
                    </view>
                    <view class="radio-two">
                        2023新楼盘不多 老楼盘
                        货源逐步减少 楼市也....
                    </view>
                    <view class="radio-three">
                        <view>
                            <text></text>
                            <text>经纪人</text>
                        </view>
                        <text>楼房</text>
                    </view>
                </view>
                <view class="radio-left">
                    <view class="radio-one">
                        <image src="../../static/icon/assress.png"></image>
                    </view>
                    <view class="radio-two">
                        2023新楼盘不多 老楼盘
                        货源逐步减少 楼市也....
                    </view>
                    <view class="radio-three">
                        <view>
                            <text></text>
                            <text>经纪人</text>
                        </view>
                        <text>楼房</text>
                    </view>
                </view>
                <view class="radio-left">
                    <view class="radio-one">
                        <image src="../../static/icon/assress.png"></image>
                    </view>
                    <view class="radio-two">
                        2023新楼盘不多 老楼盘
                        货源逐步减少 楼市也....
                    </view>
                    <view class="radio-three">
                        <view>
                            <text></text>
                            <text>经纪人</text>
                        </view>
                        <text>楼房</text>
                    </view>
                </view>
            </view>
        </view>


    </view>
</template>
<script>
// import from '../vr/components/static/css/font.css'
import { uniIcons } from '@dcloudio/uni-ui'
import myIcon from "../../components/myIcon.vue"
export default {
    name: "",
    data() {
        return {
            list: ['动态', '二手房', '热点资讯', '视频'],
            indext: 1,
            tabIndex: 0,
            radio: [],
            id: 0,
        }
    },
    components: {
        uniIcons,
        myIcon
    },
    methods: {
        gettap() {
            uni.showLoading({
                title: '加载中'
            });
        },
        testTabClick(index) {
            console.log(index)
            this.tabIndex = index
            // uni.showLoading({
            //     title: '加载中'
            // });
            this.getRadioList()
        },
        // tap切换
        handleTap(e) {
            console.log(e)
            this.indext = e
        },
        // 视频分类
        handleSort() {

        },
        // 视频列表
        getRadioList() {

        },
    },
    onLoad() {
        // this.gettap()
        this.handleSort()
        this.getRadioList()
    },

}
</script>
<style scoped lang="scss">
// @import '../vr/components/static/css/font.css';
.page {
    background-color: white;
    min-height: calc(100vh - 88rpx);
    // padding: 19rpx 0 0 0;
}

.uni-video-cover-play-button {
    display: none !important;
}

[v-cloak] {
    display: none;
}

.footer_top {
    margin-left: 30rpx;
    width: 330rpx;
    margin-top: 28rpx;

    .footer-one {
        width: 330rpx;
        height: 200rpx;
        // margin-top: 18rpx;
        background-color: skyblue;
        border-radius: 10rpx;
        position: relative;

        .title {
            position: absolute;
            top: 20rpx;
            left: 110rpx;
            // padding-top: 18rpx;
            // margin-left: 160rpx;
            box-sizing: border-box;
            white-space: nowrap;
            // width: 160rpx;
            // height: 62rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 2px;
            background: #0000007F;
            color: #FFFFFF;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 22rpx;
            line-height: normal;
            box-sizing: border-box;
        }

        .logo {
            position: absolute;
            top: 152rpx;
            left: 26rpx;

            image {
                width: 26rpx;
                height: 26rpx;
                margin-right: 6rpx;
            }

            color: #FFFFFF;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 22rpx;
        }
    }

    .footer-two {
        width: 330rpx;
        color: #141414;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 30rpx;
        line-height: normal;
        margin-top: 16rpx;
    }

    .footer-three {
        display: flex;
        justify-content: space-between;
        margin-top: 16rpx;

        view {
            display: flex;
        }

        view:nth-child(1) {
            text:nth-child(1) {
                // width: 48rpx;
                height: 48prx;
                border-radius: 50%;
                // background-color: pink;
                // margin-right: 10rpx;
            }

            text:nth-child(2) {
                color: #141414;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 30rpx;
                line-height: normal;
            }
        }

        view:nth-child(2) {
            display: flex;
            align-items: center;

            text:nth-child(2) {
                color: #8A8A8A;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 22rpx;
            }
        }
    }

    .footer-four {
        margin-top: 20rpx;
        display: flex;

        text:nth-child(1) {
            width: 70rpx;
            display: inline-block;
            height: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 22rpx;
            background-color: #DFA650;
            border-radius: 2px;
            color: #FFFFFF;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 22rpx;
        }

        text:nth-child(2) {
            width: 108rpx;
            display: inline-block;
            height: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 22rpx;
            background-color: #DFA650;
            border-radius: 2px;
            color: #FFFFFF;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 22rpx;
        }
    }

}

.active {
    font-weight: bolder !important;
}

.footer {
    width: 800rpx;
    display: flex;
    flex-wrap: wrap;
    // margin-top: 38rpx;
}

.page_top {
    padding: 0 0 0 40rpx;
    box-sizing: border-box;
    display: flex;

    // flex-wrap: wrap;
    view {
        margin-right: 62rpx;
        color: #141414;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 30rpx;
        line-height: normal;
    }
}

.tab-box {
    display: flex;
    margin-left: 30rpx;

    .tab-item {
        flex-shrink: 0;
        padding: 24rpx;
        position: relative;
        transition: all 0 linear;

        &::after {
            transition: all 0 linear;
            transform: translateX(-50%) scaleX(0);
            content: '';
            width: 50%;
            position: absolute;
            left: 50%;
            bottom: 20rpx;
            border-bottom: 6rpx solid rgba(255, 141, 9, 1);
            border-radius: 4rpx;
        }

        &.active {
            &::after {
                content: '';
                width: 40%;
                position: absolute;
                left: 50%;
                top: 70rpx;
                transform: translateX(-50%) scaleX(1);
                bottom: 20rpx;
                border-bottom: 6rpx solid rgba(255, 141, 9, 1);
            }
        }
    }
}

.dynamic_top {
    margin-top: 46rpx;
    margin-left: 38rpx;

    .dynamic-one {
        height: 72rpx;
        display: flex;

        .dynamic-y {
            display: inline-block;
            width: 72rpx;
            height: 72rpx;
            border-radius: 50%;
            background-color: pink;
        }

        .dynamic-x {
            margin-left: 24rpx;

            text:nth-child(1) {
                display: block;
                color: #141414;
                font-family: PingFang SC;
                font-weight: medium;
                font-size: 28rpx;
                line-height: normal;
            }

            text:nth-child(2) {
                display: block;
                color: #8A929F;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 22rpx;
                line-height: normal;
            }
        }

    }

    .dynamic-two {
        margin-top: 20rpx;
        color: #141414;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 30rpx;
        line-height: normal;
    }

    .dynamic-three {
        width: 690rpx;
        height: 380rpx;
        background-color: skyblue;
        border-radius: 10rpx;
        margin-top: 20rpx;
        position: relative;

        image {
            width: 100rpx;
            height: 100rpx;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -50rpx;
            margin-left: -50rpx;
        }
    }

    .dynamic-four {
        margin-top: 20rpx;
        width: 690rpx;
        display: flex;
        justify-content: space-between;

        view {
            color: #8A929F;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 24rpx;
            line-height: normal
        }
    }
}

.dynamic_center {
    margin-top: 46rpx;
    margin-left: 38rpx;

    .dynamic-one {
        height: 72rpx;
        display: flex;

        .dynamic-y {
            display: inline-block;
            width: 72rpx;
            height: 72rpx;
            border-radius: 50%;
            background-color: pink;
        }

        .dynamic-x {
            margin-left: 24rpx;

            text:nth-child(1) {
                display: block;
                color: #141414;
                font-family: PingFang SC;
                font-weight: medium;
                font-size: 28rpx;
                line-height: normal;
            }

            text:nth-child(2) {
                color: #FC4C4C;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 22rpx;
                margin-right: 4rpx;
                line-height: normal;
            }

            text:nth-child(3) {
                color: #FC4C4C;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 28rpx;
                line-height: normal;
            }

            text:nth-child(4) {
                margin-right: 4rpx;
                color: #FC4C4C;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 22rpx;
                line-height: normal;
            }

            text:nth-child(5) {
                color: #8A929F;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 22rpx;
                line-height: normal;
            }
        }

    }

    .dynamic-two {
        margin-top: 20rpx;
        color: #141414;
        font-family: PingFang SC;
        font-weight: regular;
        font-size: 30rpx;
        line-height: normal;
    }


    .dynamic-three {
        width: 690rpx;
        height: 380rpx;
        background-color: skyblue;
        border-radius: 10rpx;
        margin-top: 20rpx;
        position: relative;

        image {
            width: 100rpx;
            height: 100rpx;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -50rpx;
            margin-left: -50rpx;
        }
    }

    .dynamic-four {
        margin-top: 20rpx;
        width: 690rpx;
        display: flex;
        justify-content: space-between;

        view {
            color: #8A929F;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 24rpx;
            line-height: normal
        }
    }

    .dynamic-five {
        width: 690rpx;
        height: 91rpx;
        border-radius: 10rpx;
        background: #F4F4F4;
        margin-top: 20rpx;
        display: flex;
        align-items: center;

        .dynamic-five-center {
            display: flex;

            .touxiang {
                width: 56rpx;
                height: 56rpx;
                background-color: pink;
                border-radius: 50%;
                margin-right: 12rpx;
                margin-left: 26rpx;
            }

            text:nth-child(2) {
                color: #141414;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 28rpx;
                line-height: 56rpx;
                margin-right: 28rpx;
            }

            text:nth-child(3) {
                color: #141414;
                font-family: PingFang SC;
                font-weight: regular;
                font-size: 28rpx;
                line-height: 56rpx;
                margin-right: 122rpx;
            }

            .phone {
                width: 60rpx;
                height: 60rpx;
                background-color: white;
                border-radius: 50%;
                margin-right: 32rpx;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .wei {
                width: 60rpx;
                height: 60rpx;
                background-color: white;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
}

.dynamic_bottom {
    margin-top: 20rpx;
    margin-left: 30rpx;

    .dynamic_bottom-one {
        width: 690rpx;

        view:nth-child(1) {
            width: 670rpx;
            color: #141414;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 30rpx;
            line-height: normal;
        }

        view:nth-child(2) {
            width: 690rpx;
            height: 302rpx;
            background-color: yellow;
            margin-top: 20rpx;
        }

        view:nth-child(3) {
            margin-top: 20rpx;
            color: #8A929F;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 24rpx;
            line-height: normal;
            display: flex;
            justify-content: space-between;
        }

        view:nth-child(4) {
            height: 2rpx;
            background-color: #f3f3f3;
            margin-top: 20rpx;
        }
    }

    .dynamic_bottom-two {
        margin-top: 20rpx;

        view:nth-child(1) {
            color: #141414;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 15px;
            line-height: normal;

        }

        view:nth-child(2) {
            margin-top: 20rpx;

            image {
                width: 224rpx;
                height: 140rpx;
                margin-right: 8rpx;
            }
        }

        view:nth-child(3) {
            display: flex;
            justify-content: space-between;
            margin-top: 20rpx;
            color: #8A929F;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 24rpx;
            line-height: normal;
            width: 690rpx;
        }

        view:nth-child(4) {
            height: 2rpx;
            background-color: #f3f3f3;
            margin-top: 20rpx;
            width: 690rpx;
        }
    }

    .dynamic_footer {
        margin-top: 20rpx;
        display: flex;
        width: 690rpx;

        .dynamic_footer-top {
            display: flex;
            justify-content: space-between;

            .dynamic-left {
                width: 420rpx;

                view:nth-child(1) {
                    color: #141414;
                    font-family: PingFang SC;
                    font-weight: regular;
                    font-size: 30rpx;
                    line-height: normal;
                }

                view:nth-child(2) {
                    margin-top: 32rpx;
                    display: flex;
                    justify-content: space-between;
                    color: #8A929F;
                    font-family: PingFang SC;
                    font-weight: regular;
                    font-size: 12px;
                    line-height: normal;
                }
            }

            .dynamic-right {
                width: 224rpx;
                height: 140rpx;

                // background: pink;
                image {
                    width: 224rpx;
                    height: 140rpx;
                }

                margin-left: 42rpx;
            }

        }
    }

    .dynamic_footer-bottom {
        width: 690rpx;
        margin-top: 20rpx;
        height: 2rpx;
        background-color: #f3f3f3;
    }
}

.radio {
    margin-top: 46rpx;
    display: flex;
    flex-wrap: wrap;
    .radio-left {
        width: 330rpx;
        margin-left: 30rpx;
        margin-bottom: 40rpx;
        .radio-one {
            width: 330rpx;
            height: 540rpx;
            border-radius: 10rpx;
            image {
                width: 100%;
                height: 100%;
                border-radius: 10rpx;
            }
        }

        .radio-two {
            margin-top: 24rpx;
            width: 330rpx;
            color: #141414;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 30rpx;
            line-height: normal;
        }

        .radio-three {
            margin-top: 24rpx;
            width: 330rpx;
            display: flex;
            justify-content: space-between;
            color: #8A929F;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 24rpx;
            line-height: normal;
            align-items: center;
            view {
                display: flex;
                align-items: center;
                text:nth-child(1) {
                    width: 48rpx;
                    height: 48rpx;
                    background-color: skyblue;
                    border-radius: 50%;
                    margin-right: 8rpx;
                }
            }
        }
    }
}</style>