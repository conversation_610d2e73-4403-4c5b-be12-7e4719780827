<template>
    <view class="page">
        <template v-if="loading">
            <!-- <view class="head-top"> -->
            <!-- <view class="header-center">
                <view class="header-logo-top">
                    粉丝数量
                </view>
                <view  class="header-logo-center">
                    <view>
                        <view>1009</view>
                    </view>
                    <view class="header-logo-right">
                        <view></view>
                        <view></view>
                        <view></view>
                        <view></view>
                        <view></view>
                    </view>
                </view>
                <view  class="header-logo-bottom">
                    <view class="header-bottom-left">
                        <text>矩阵账号 :</text>
                        <text>5</text>
                        <text>个</text>
                    </view>
                    <view class="header-bottom-right">
                        <text>昨日播放 :</text>
                        <text>2.3</text>
                        <text>万</text>
                        <uni-icons type="forward" size="20" color="#979797"></uni-icons>
                    </view>
                </view>
            </view>
        </view> -->
        <!-- <view class="logo">
            <view class="logo-left">
                <view>主页访问总数</view>
                <view>+23</view>
                <view class="logo-tex">
                    <text>本月累计 :</text>
                    <text>256</text>
                    <text>人</text> 
                </view>
            </view>
            <view class="logo-right">
                <view>7日新增</view>
                <view>+23</view>
                <view class="logo-tex">
                    <text>本月累计 :</text>
                    <text>256</text>
                    <text>人</text> 
                </view>
            </view>
        </view> -->
        <view class="title">
            <view>
                日期:
            </view>
            <view class="title-date">{{ statistics.ctime }}</view>
        </view>
        <!-- <view class="nav">
            {{ database}}
        </view> -->
        <view class="center-list">
            <view class="list" v-for="(item,index) in statistics.list" :key="item.value">
                <view  class="list-three">
                    <view>{{item.title}}：</view>
                    <view>{{ item.value }}</view>
                </view>
            </view>
        </view>
        </template>
    </view>

</template>
<script>
import { uniIcons } from "@dcloudio/uni-ui";
export default {
  data() {
    return {
        float:1,
        statistics:'',
        database:'',
        type:1,
        loading:false,
        id:''
    }
  },
  components:{
    uniIcons,
  },
  onLoad(options) {
    // uni.setNavigationBarColor({
    // frontColor: '#ffffff',
    // backgroundColor: '#313a4c',
    // })
    this.id =options.id
        this.getNewspaper()
  },    
  methods:{
    getNewspaper(){
        this.$ajax.get('platform_data/getPackageUsageStatistics',{id:this.id},(res) => {
            if(res.data.code==1){
                this.statistics= res.data.statistics
                uni.setNavigationBarTitle({
                    title:res.data.title,
                })
                this.loading = true
            }     
        })
    },
  }
}
</script>
<style scoped lang="scss">
    page{
        background-color: #f8f8f8;
    }
    .head-top{
        height: 208rpx;
        background-color: #313a4c;
    }
    .header-center{
        width: 654rpx;
        height: 238rpx;
        background-color: white;
        position: relative;
        top: 72rpx;
        left: 50%;
        margin-left:-327rpx;
        border-radius: 16rpx;
        padding-top: 24rpx;
        // box-sizing: border-box;
    }
    .header-logo-top{
        width: 606rpx;
        margin: 0 auto;
        height: 40rpx;
        font-size: 28rpx;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: LEFT;
        color: #999999;
        line-height: 40rpx;
    }
    .header-logo-center{
        width: 606rpx;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        view{
            font-size: 48rpx;
            color: #333333;
            margin-top: 24rpx;
        }
        .header-logo-right {
            display: flex;
        }
        .header-logo-right view{
            width: 64rpx;
            height: 64rpx;
            border-radius: 50%;
            background-color: pink;
            position: relative;
            left:10rpx;
        }
        // .header-logo-right view+view{
        //     background-color: red;
        //     position:relative;
        //     right:0;

        // }
    }
    .header-logo-bottom{
        width: 606rpx;
        margin: 0 auto;
        display: flex;
        margin-top: 26rpx;
        justify-content: space-between;
    }
    .header-bottom-left text{
        margin-right: 14rpx;
        color: #999999;
    }
    .header-bottom-left text:nth-child(2){
        color: #333333;
    }
    .header-bottom-right{
        display:flex;
        align-items: center;
    }
    .header-bottom-right text{
        margin-right: 14rpx;
        color: #999999;
    }
    .header-bottom-right text:nth-child(2){
        color: #333333;
    }
    .logo {
        width: 654rpx;
        height: 238rpx;
        margin: 0 auto;
        margin-top: 160rpx;
        display: flex;
        justify-content: space-between;
    }
    .logo-left{
        width:308rpx;
        height: 238rpx;
        background-color: white;
        border-radius: 8px;
        padding-top: 24rpx;
        padding-left: 24rpx;
        box-sizing: border-box;
    }
    .logo-left view:nth-child(1){
        color: #999999;
        font-size: 28rpx;
    }
    .logo-left view:nth-child(2){
        color: #333333;
        font-size: 48rpx;
        margin-top: 24rpx;
        margin-bottom: 24rpx;
    }
    .logo-right{
        width:308rpx;
        height: 238rpx;
        background-color: white;
        border-radius: 8px;
        padding-top: 24rpx;
        padding-left: 24rpx;
        box-sizing: border-box;     
    }
    .logo-tex text{
        margin-right: 14rpx;
        color: #999999;
    }
    .logo-tex text:nth-child(2){
        color: #333333;
    }
    .logo-right view:nth-child(1){
        color: #999999;
        font-size: 28rpx;
    }
    .logo-right view:nth-child(2){
        color: #333333;
        font-size: 48rpx;
        margin-top: 24rpx;
        margin-bottom: 24rpx;
    }
    .title{
        margin: 0 auto;
        width: 606rpx;    
        margin-top: 50rpx; 
        display: flex;
        align-items: center;
        line-height: 44rpx;
    }
    .title image{
        width:48rpx;
        height: 48rpx;
        margin-right: 8rpx;
    }
    .title view:nth-child(1){
        font-size: 36rpx;
        color: #333333;
    }
    .title-date{
        color: #999999;
        font-size: 28rpx;
        line-height: 10rpx;
        margin-left: 20rpx;
    }
    .center-list {
        margin: 0 48rpx;
        margin-top: 26rpx;
        display: flex;
        flex-wrap: wrap;
        border-radius: 16rpx;
        overflow: hidden;
        display:flex;
        //  background-color: #f8f8f8; 
        justify-content: space-between;
        margin-bottom: 30rpx;
        .list{
           width: 49.9%;
            // width:302rpx;
            background: white;
            margin-bottom: 2rpx;
            
            padding: 0 0 24rpx 24rpx;
            box-sizing: border-box;
            .list-one{
                display: flex;
                align-items: center;
                font-size: 24rpx;
                color: #999999;
                image{
                    width:64rpx;
                    height: 64rpx;
                    margin-right: 16rpx;
                }
            }
              .list-two {
                color: #333333;
                font-size: 48rpx;
                margin-top: 24rpx;
              }
              .list-three {
                margin-top: 24rpx;
              }
              .list-three view:nth-child(1){
                    font-size: 24rpx;
                    color: #999999;
                    margin-right: 8rpx;
                    margin-bottom: 10rpx;
              }
        }        
    }
    .footer{
        height: 100rpx;
        background-color: white;
        width: 100%;
       position: fixed;
       bottom: 0;
        image{
            width: 48rpx;
            height: 48rpx;
        }
        .footer-bottom{
            width: 568rpx;
            padding-top: 10rpx;
            display: flex;
            justify-content: space-between;
            margin: 0 auto;
            text-align: center;
            color: #999999;
            font-size: 20rpx;
        }
    }
    .active{
        color: #2D84FB
    }
    .nav{
        margin: 20rpx;
        margin-left: 50rpx;
    }

</style>