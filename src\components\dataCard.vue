<template>
	<view class="box flex-box bottom-line">
		<view class="box-top" :class="{'bottom-line':item.jzmj||item.zzts||item.fzzts}">
			<view class="item-title">
				{{ item.xkzh ||item.tdbh||''}}
				<text class="item-title-yijia" v-if="type=='tudi'&&item.yjl>0">溢价{{item.yjl||''}}%</text>
			</view>
			<view class="item-info">
				{{ item.xmmc||item.tdwz ||'' }}
			</view>
			<view class="item-company">
				<text>{{ item.gsmc||item.cjdw ||'' }}</text>
				<block v-if ="type=='yushou'">
					<text v-if="item.areaname" class="fenge">|</text>
					<text v-if="item.areaname"> {{ item.areaname ||'' }}</text>
				</block>
				<block v-if ="type=='tudi'">
					<text v-if="item.yongtu" class="fenge">|</text>
					<text v-if="item.yongtu"> {{ item.yongtu ||'' }}</text>
				</block>
				
			</view>
		</view>
		<view class="item-box">
			<view class="flex-box data-box">
				<block v-if ="type =='yushou'">
					<view class="flex-1 text-center data-top" v-if ="item.jzmj"  :class='{"fenge-line":isShowTaoshu }'>
						<view class="data-data"
							><text class="data-datas" :class='{"red":red=="1"}'>{{ item.jzmj||0 }}</text
							></view
						>
						<view class="data-title">总面积 m²</view>
					</view>
					<view class="flex-1 text-center data-top" v-if ="isShowTaoshu" :class='{"fenge-line":item.fzzts>=0 }'>
						<view class="data-data"
							><text class="data-datas" :class='{"red":red=="2"}'> {{ item.zzts||0 }}</text
							>套</view
						>
						<view class="data-title">住宅</view>
					</view>
					<view class="flex-1 text-center data-top" v-if ="isShowTaoshu">
						<view class="data-data"
							><text class="data-datas" :class='{"red":red=="3"}'>{{ item.fzzts ||0 }}</text
							>套</view
						>
						<view class="data-title">非住宅</view>
					</view>
				</block>
				<block v-if ="type =='tudi'&&item.zt==1">
					<view class="flex-1 text-center data-top" v-if ="item.mushu"  :class='{"fenge-line":item.chengjiaojia}'>
					<view class="data-data"
						>约<text class="data-datas" :class='{"red":red=="2"}'>{{ item.mushu ||'' }}</text
						>亩</view
					>
					<view class="data-title">成交面积</view>
				</view>
				<view class="flex-1 text-center data-top" v-if ="item.chengjiaojia" :class='{"fenge-line":item.loumianjia>0 }'>
					<view class="data-data"
						><text class="data-datas" :class='{"red":red=="1"}'>{{ item.chengjiaojia ||''}}</text
						></view
					>
					<view class="data-title">成交价(万元)</view>
				</view>
				<view class="flex-1 text-center data-top" v-if ="item.loumianjia>0">
					<view class="data-data"
						>约<text class="data-datas" :class='{"red":red=="3"}'>{{ item.loumianjia ||''}}</text
						></view
					>
					<view class="data-title">楼面价(元/m²)</view>
				</view>
				</block>
			</view>
		</view>
		<view class="jiangbei" v-if="idx>=0&&idx<=2">
			<image :src="imgSrc" mode="widthFix"></image>
		</view>
		<view class="biaoqian" v-if="idx>2" :style ="'backgroundImage:url('+biaoqianSrc+')'">
			
			{{idx+1}}
		</view>
	</view>
</template>

<script>
import {config} from '../common/config'
export default {
	props: {
		// label:String,
		// name:String,
        item: Object,
        red:{
            type:Number,
            default:0
        },
		idx:{
			type:Number,
            default:-1
		},
		type:{
			type:String,
            default:"yushou"
		}
        
	},
	data() {
		return {};
	},
	computed: {
		imgSrc(){
			return config.imgDomain+'/images/new_icon/record/icon_'+(this.idx+1)+'@2x.png'
		},
		biaoqianSrc(){
			return config.imgDomain+'/images/new_icon/record/biaoqian.png'
		},
		isShowTaoshu() { // 是否开启聊天功能
            return this.$store.state.isShowTaoshu
        },
	},
	methods: {},
};
</script>

<style lang="scss">
.data-box {
	// padding: 32upx 0;
	background-color: #ffffff;
	.data-top{
		margin: 32rpx 0;
	}
	.data-title {
		margin-bottom: 10upx;
		font-size: $uni-font-size-sm;
		color: #999;
	}
	.data-data {
		font-size: $uni-font-size-sm;
		// font-weight: bold;
		color: #999;
		margin-bottom: 20rpx;
	}
	.data-datas {
		font-size: 38upx;
		// font-weight: bold;
		color: #333;
	}
	.red {
		color: $uni-color-primary;
	}
}
.box {
	flex-direction: column;
	padding: 24rpx 24rpx 0 24rpx;
	background: #ffffff;
	border: 0 solid #d8d8d8;
	box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
	border-radius: 8px;
	position: relative;
	.jiangbei{
		position: absolute;
		top: 0;
		right:24rpx;
		height: 50rpx;
		width: 50rpx;
		image{
			width: 100%;
			height: 100%;
		}
	}
	.biaoqian{
		position: absolute;
		text-align: center;
		top:0;
		right:24rpx;
		height: 50rpx;
		width: 50rpx;
		padding-top: 4rpx;
    	box-sizing: border-box;
		background-repeat: no-repeat;
		background-size: 100% 100%;
		color: #fff;
		
	}
	.box-top {
		padding-bottom: 24rpx;
	}
	.item-title {
		font-size: 16px;
		color: #333333;
		.item-title-yijia{
			margin-left: 24rpx;
			font-size: 22rpx;
			color: #333;

		}
	}
	.item-info {
		margin: 16rpx 0;
		font-size: 14px;
		color: #666666;
	}
	.item-company {
		font-size: 14px;
		color: #999999;
		.fenge{
			margin: 0 10rpx;
		}
	}
}
.fenge-line{
	position: relative;
	&:after{
		content: "";
		position: absolute;
		top: 20%;
		bottom: 20%;
		right: 0;
		width: 1px;
		-webkit-transform: scaleX(.5);
		transform: scaleX(.5);
		background-color: $uni-border-color;
	}
}
</style>
