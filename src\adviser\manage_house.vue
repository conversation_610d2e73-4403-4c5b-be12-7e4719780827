<template>
<view class="add_house">
    <view class="card">
        <!-- <view class="title bottom-line">房源信息：</view> -->
        <view class="card_content">
            <my-select small label="所属楼栋" :value="house_info.number_id" @change="pickerChange" :range="loudong_list" name="number_id"></my-select>
            <my-select small label="所属单元" v-if = "house_info.number_id"  :value="house_info.unit_id" @change="pickerChange" :range="unit_list" name="unit_id"></my-select>
            <my-select small label="所在楼层"  v-if ='house_info.unit_id' :value="house_info.floor" @change="pickerChange" :range="floor_list" name="floor"></my-select>
            <!-- <my-input small label="所在楼层" :value="house_info.floor" type="number" placeholder="请输入所在楼层" name="floor" @input="handleInput"></my-input> -->
            <my-input small label="房号" :value="house_info.name" type="number" placeholder="请输入房号" name="name" @input="handleInput"></my-input>
            <my-input small label="室" :value="house_info.shi" placeholder="请输入室" type="number" name="shi" @input="handleInput"></my-input>
            <my-input small label="厅" :value="house_info.ting" placeholder="请输入厅" type="number" name="ting" @input="handleInput"></my-input>
            <my-input small label="卫" :value="house_info.wei" placeholder="请输入卫" type="number" name="wei" @input="handleInput"></my-input>
            <!-- #ifdef H5 -->
            <my-input small label="建筑面积" :value="house_info.jzmj" unit="m²" placeholder="请输入建筑面积" type="number" name="jzmj" @input="handleInput"></my-input>
            <!-- #endif -->
            <!-- #ifndef H5 -->
            <my-input small label="建筑面积" :value="house_info.jzmj" unit="m²" placeholder="请输入建筑面积" type="digit" name="jzmj" @input="handleInput"></my-input>
            <!-- #endif -->
            <my-input small label="折扣后总价" :value="house_info.discount_price" placeholder="请输入房源折扣后价格" type="text" name="discount_price" @input="handleInput"></my-input>
            <!-- #ifdef H5 -->
            <my-input small label="定金" :value="house_info.earnest_money" placeholder="请输入定金" type="number" name="earnest_money" @input="handleInput"></my-input>
            <!-- #endif -->
            <!-- #ifndef H5 -->
            <my-input small label="定金" :value="house_info.earnest_money" placeholder="请输入定金" type="digit" name="earnest_money" @input="handleInput"></my-input>
            <!-- #endif -->
        </view>
    </view>
    <view class="btn-box">
        <button class="default" @click="handleManage()">{{type==='edit'?'修改房源':'添加房源'}}</button>
    </view>
</view>
</template>

<script>
import myInput from "../components/form/myInput.vue"
import mySelect from "../components/form/mySelect.vue"
export default {
    data() {
        return {
            house_info:{
                online_id: "",//项目id
                number_id: "", //楼栋id
                earnest_money: "", //定金
                floor: "", // 楼层
                jzmj: "", // 建筑面积
                discount_price: "", // 折扣后总价
                name: "", // 房号
                unit_id: "", //  单元id
                shi: "", //室
                ting: "", // 厅
                wei: "" // 卫
            },
            type:"add",
            loudong_list:[],
            unit_list:[],
            floor_list:[],
        }
    },
    components: {
        myInput,
        mySelect
    },
    onLoad(options){
        this.house_info.online_id = options.online_id || ''
        this.online_id = options.online_id || ''
        this.getLoudong()

        if(options.type==='edit'&&options.online_id&&options.house_id&&options.number_id&&options.unit_id){
            uni.setNavigationBarTitle({
                title:"修改房源"
            })
            this.type = 'edit'
            // 没办法后台就是需要这么多参数
            this.params = {
                house_id: options.house_id,
                online_id: options.online_id,
                number_id: options.number_id,
                unit_id: options.unit_id,
            }
            this.getHouse(this.params)
        }
    },
    methods: {
        getHouse(params){
            params.online_id = this.online_id
            this.$ajax.get('online/houseDetailByAdviser.html',params,res=>{
                if(res.data.code === 1){
                    this.getUnit(res.data.house.number_id)
                    this.getFloor(res.data.house.unit_id)
                    this.house_info = res.data.house
                }
            })
        },
        handleInput(e){
            this.house_info[e._name] = e.detail.value
        },
        getLoudong(){
            this.$ajax.get('online/buildingNumber',{online_id:this.online_id},res=>{
                if(res.data.code === 1){
                    this.loudong_list = res.data.number.map(item=>{
                        return {value:item.id,name:item.number}
                    })
                }
            })
        },
        getUnit(number_id){
            this.$ajax.get('online/buildingUnit',{online_id:this.online_id,number_id:number_id},res=>{
                if(res.data.code === 1){
                    this.unit_list = res.data.lists.map(item=>{
                        return {value:item.id,name:item.unit}
                    })
                }
            })
        },
        getFloor(unit_id){
            // wap/online/unitFloors.html?online_id=39&unit_id=
            this.$ajax.get('online/unitFloors',{online_id:this.online_id,unit_id},res=>{
                if(res.data.code === 1){
                    console.log(res.data);
                    
                    this.floor_list = res.data.lists.map(item=>{
                        return {value:item.id,name:item.floor}
                    })
                }
            })
        },
        pickerChange(e){
            this.house_info[e._name] = e.value
            if(e._name==='number_id'){
                this.house_info.unit_id =''
                this.house_info.floor=''
                this.getUnit(e.value)
            }else if (e._name==='unit_id'){
                this.house_info.floor=''
                this.getFloor(e.value)
            }
        },
        handleManage(){
            if(this.type === 'edit'){
                this.editHouse()
            }else{
                this.addHouse()
            }
        },
        addHouse(){
            this.$ajax.post('online/addHouseByAdviser.html',this.house_info,res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title:res.data.msg
                    })
                    uni.$emit('updateData',{})
                    uni.$emit('onChooseHouse',{house_id:res.data.house_id,number_id:res.data.number_id,unit_id:res.data.unit_id})
                    setTimeout(()=>{
                        uni.navigateBack()
                    },1500)
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            })
        },
        editHouse(){
            this.$ajax.post('online/editHouseByAdviser.html',this.house_info,res=>{
                if(res.data.code === 1){
                    uni.showToast({
                        title:res.data.msg
                    })
                    uni.$emit('updateData',{})
                    setTimeout(()=>{
                        uni.navigateBack()
                    },1500)
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            })
        },
    },
}
</script>

<style scoped lang="scss">
.add_house{
    padding-top: 20rpx;
}
.card{
    margin: 0 24rpx 24rpx 24rpx;
    padding: 26rpx;
    border-radius: 10rpx;
    background-color: #fff;
    box-shadow: 0 0 10px #dedede;
    .title{
        padding: 26rpx 0;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
    }
    // .card_content{
    //     margin-top: 20rpx;
    // }
}
</style>
