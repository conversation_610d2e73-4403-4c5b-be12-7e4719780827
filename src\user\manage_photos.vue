<template>
  <view>
    <view class="right mid">
      <!-- fixed -->
      <view class="add" @click="$navigateTo(`/user/house_price/publication`)">+</view>
      <view class="user_comments_list" v-for="(item, index) in list" :key="index">
        <view class="top flex-row" v-if="index===0||item.month!==list[index-1].month||item.day!==list[index-1].day">
          <view class="user_box">
            <!-- <image :src="item.prelogo | imageFilter('w_120')" mode="aspectFill"></image> -->
            <view class="user_name">{{ item.month }}月</view>
            <view class="day">{{ item.month }}-{{ item.day }}</view>
          </view>
          <view class="infor_box row">
            <view class="left row">
              <text class="community_name">{{ item.community_name }}</text>
              <!-- <text class="shop_name">{{ item.tname }}</text> -->
            </view>
            <view class="right row">
              <view>
                <myIcon v-if="item.ispraise" type="ic_zan" color="#FB656A" size="32rpx"></myIcon>
                <myIcon v-else type="ic_zan" color="#d8d8d8" size="32rpx"></myIcon>
              </view>
              <view class="num">{{ item.praise }}</view>
            </view>
          </view>
        </view>
        <view class="user_right bottom-line">
          <view class="txt">{{ item.content }}</view>
          <view class="media_box">
            <!-- 上传的图片 -->
            <view v-if="!item.isvideo" class="imgs_box row">
              <view class="img_item" v-for="(img, idx) in item.medias" :key="idx">
                <image mode="aspectFill" :src="img | imageFilter('w_240')" @click="previewImage(img, item.medias)"></image>
              </view>
              <view class="img_item perch"></view>
              <view class="img_item perch"></view>
            </view>
            <!-- 上传的视频 -->
            <view v-else class="video_box" @click="viewVideo(item.medias[0])">
              <image class="video_item" :src="item.medias[0] | imageFilter('w_400')" mode="aspectFill"></image>
              <view class="bofang">
                <myIcon type="ic_video" size="80rpx" color="#fff"></myIcon>
              </view>
            </view>
          </view>
          <view class="del-row">
            <view class="del_btn" @click="onDelete(item)">
              <my-icon type="ic_delete1x" color="#d8d8d8" size="46rpx"></my-icon>
              <text class="text">删除</text>
            </view>
          </view>
        </view>
      </view>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
import { uniLoadMore } from '@dcloudio/uni-ui'
import {formatImg, showModal} from '../common/index'
export default {
  components: {
    myIcon,
    uniLoadMore
  },
  data() {
    return {
      list: [],
      get_status: "",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了",
      },
      params:{
        rows:20,
        page:1
      }
    }
  },
  onLoad(options) {
      this.getData()
      uni.$on('getCommunityPhoto',()=>{
        this.params.page = 1
        this.getData()
      })
  },
  onUnload(){
    uni.$off('getCommunityPhoto')
  },
  methods: {
    getData(){
      this.get_status = "loading"
      if(this.params.page === 1){
        this.list = []
      }
      this.$ajax.get('member/selfPubList',this.params, res=>{
        console.log(res.data)
        if(res.data.code === 1){
          this.get_status = "more"
          this.list = this.list.concat(res.data.list)
        }else{
          this.get_status = "noMore"
        }
      })
    },
    onDelete(e){
      showModal({
        title: '提示',
        content: '确定删除吗？',
        confirm: () => {
          console.log(e.id)
          this.$ajax.post('member/delCommunityPub',{id:e.id},res=>{
            if(res.data.code === 1){
              uni.showToast({
                title: res.data.msg
              })
              this.params.page = 1
              this.getData()
            }else{
              uni.showToast({
                title: res.data.msg,
                icon: 'none'
              })
            }
          })
        }
      })
    },
    viewVideo(url){
      this.$navigateTo(`/vr/preview_video?url=${url}`)
    },
    // 查看预览图
    previewImage(img,img_list) {
      img_list = img_list.map(item=>formatImg(item,'w_800'))
      uni.previewImage({
        urls: img_list,
        current: formatImg(img,'w_800')
      })
    },
  },
  onReachBottom(){
    console.log(this.get_status)
    if(this.get_status !== "noMore"){
      this.params.page ++
      this.getData()
    }
  },
}
</script>

<style scoped lang="scss">
page{
  background-color: #fff;
}
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  &.row{
    flex-direction: row;
    padding: 0;
  }
  &.content{
    padding: 0;
  }
}
// 导航
.nav {
  position: fixed;
  border-bottom: 2rpx solid #eee;
  width: 100%;
  background: #fff;
  z-index: 10;
  flex-direction: row;
  padding: 30rpx;
  justify-content: space-between;
  .nav-item {
    flex: 1;
    margin: 20rpx 5rpx;
    text-align: center;
    color: #666;
    &.active {
      font-weight: bold;
      color: #fb656a;
    }
  }
  .nav-item:first-child {
    border-right: 2rpx solid #eee;
  }
}

// 内容

.mid {
  padding: 0 48rpx;
  .title {
    font-size: 32rpx;
    color: #333333;
    padding: 24rpx 0;
  }
  .content {
    justify-content: space-between;
    flex-wrap: wrap;
    .video {
      position: relative;
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 30rpx;
      &.perch{
        height: 0;
      }
      image {
        width: 100%;
        height: 100%;
      }
      .bofang {
        position: absolute;
        border-radius: 50%;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }
}
.right {
  .user_comments_list {
    margin-bottom: 20rpx;
    .top {
      margin-top: 20rpx;
      margin-bottom: 10rpx;
      justify-content: space-between;
      align-items: center;
      &.flex-row{
        flex-direction: row;
        // align-items: flex-start;
      }
      .user_box {
        align-items: center;
        margin-right: 20rpx;
        image {
          margin-right: 16rpx;
          width: 64rpx;
          height: 64rpx;
          border-radius: 50%;
        }
        .user_name {
          font-size: 32rpx;
          font-weight: bold;
        }
        .day {
          margin-top: 10rpx;
          font-size: 26rpx;
          color: #999;
        }
      }
      .up_time {
        color: #999;
        font-size: 22rpx;
      }
    }
    .num{
      margin-left: 6rpx;
    }
    .user_right {
      margin-left: 74rpx;
      padding-bottom: 24rpx;
      .txt {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
        // 文本溢出隐藏
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      .media_box {
        flex-wrap: wrap;
        .imgs_box {
          width: 100%;
          flex-wrap: wrap;
          justify-content: space-between;
          .img_item{
            width: 178rpx;
            height: 178rpx;
            margin-top: 24rpx;
            &.perch{
              height: 0;
              margin: 0;
            }
          }
          image {
            width: 100%;
            height: 100%;
          }
        }
        .video_box {
          position: relative;
          width: 480rpx;
          height: 320rpx;
          image {
            margin-top: 24rpx;
            width: 100%;
            height: 100%;
          }
          .bofang {
            position: absolute;
            border-radius: 50%;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.5);
          }
        }
      }
    }
    .infor_box {
        justify-content: space-between;
        flex: 1;
        font-size: 22rpx;
        // margin-bottom: 16rpx;
        .left {
          align-items: center;
          &.row{
            font-size: 22rpx;
          }
          .community_name {
            color: #fb656a;
            margin-right: 16rpx;
          }
          .shop_name {
            color: #999;
          }
        }
        .right {
          align-items: center;
          color: #d8d8d8;
        }
      }
      .del-row{
        margin-top: 16rpx;
        flex-direction: row;
        justify-content: flex-end;
        .del_btn {
          flex-direction: row;
          align-items: center;
          font-size: 24rpx;
          color: #d8d8d8;
          .text{
            margin-left: 6rpx;
          }
        }
      }
  }
}
.add {
  z-index: 1;
  right: 6%;
  bottom: 20%;
  position: fixed;
  font-size: 80rpx;
  width: 96rpx;
  height: 96rpx;
  line-height: 86rpx;
  background-image: linear-gradient(135deg, #f7918f 0%, #fb656a 100%);
  box-shadow: 0 3px 6px 0 rgba(250, 112, 115, 0.7);
  border-radius: 50%;
  text-align: center;
  color: #fff;
}
</style>
