<template>
<view class="page">
    <view class="head">
        <image class="company-logo" @click="toShop(shopInfo.id)" :src="shopInfo.shoplogo | imgUrl" mode="aspectFill"></image>
        <view class="company-name" @click="toShop(shopInfo.id)">{{shopInfo.shopname}}</view>
        <!-- #ifdef MP -->
        <button open-type="share" class="share_btn">
            <my-icon type="fenxiang1" color="#fff" size="20"></my-icon>
        </button>
        <!-- #endif -->
    </view>
    <!-- #ifndef MP-TOUTIAO ||MP-BAIDU -->
    <home-list-item :multigraph="true" :item="currentInfo" inPage='case_detail' v-slot:default="{slotItem,show_bar}">
        <view class="handle-bar" :class="{'show':show_bar}">
            <view v-if="slotItem.shoptel" class="bar-item right-line" @click.stop.prevent="handleTel('case', slotItem)">
                <my-icon type="dianhua" color="#ffffff"></my-icon>
                <text>电话</text>
            </view>
            <view class="bar-item right-line" @click.stop.prevent="handleYuyue()">
                <my-icon type="shikebiao" color="#ffffff"></my-icon>
                <text>预约</text>
            </view>
            <view class="bar-item" @click.stop.prevent="toShop(slotItem.shopid)">
                <my-icon type="dianpu" color="#ffffff"></my-icon>
                <text>店铺</text>
            </view>
        </view>
    </home-list-item>
    <!-- #endif-->
    <!-- #ifdef MP-TOUTIAO ||MP-BAIDU -->
    <home-list-item  :item="currentInfo" inPage='case_detail'  @handleYuyue="handleYuyue" @handleTel="getChildData">
    </home-list-item>
    <!-- #endif -->
    <view class="row top-20 text-center bottom-line">
        <view class="name">更多推荐</view>
    </view>
    <!-- #ifndef MP-TOUTIAO || MP-BAIDU -->
    <home-list-item v-for="item in otherInfo" :key="item.id" :item="item" v-slot:default="{slotItem,show_bar}">
        <view class="handle-bar" :class="{'show':show_bar}">
            <view v-if="slotItem.shoptel" class="bar-item right-line" @click.stop.prevent="handleTel('case', slotItem)">
                <my-icon type="dianhua" color="#ffffff"></my-icon>
                <text>电话</text>
            </view>
            <view class="bar-item right-line" @click.stop.prevent="handleYuyue()">
                <my-icon type="shikebiao" color="#ffffff"></my-icon>
                <text>预约</text>
            </view>
            <view class="bar-item" @click.stop.prevent="toShop(slotItem.shopid)">
                <my-icon type="dianpu" color="#ffffff"></my-icon>
                <text>店铺</text>
            </view>
        </view>
    </home-list-item>
    <!-- #endif-->
    <!-- #ifdef MP-TOUTIAO ||MP-BAIDU -->
    <home-list-item v-for="(item) in otherInfo" :key="item.id" :item="item" inPage='shop' :hideShop="true" @handleYuyue="handleYuyue" @handleTel="getChildData">
    </home-list-item>
    <!-- #endif -->

    <!-- <home-list-item :dataList="otherInfo"></home-list-item> -->
    <my-popup ref="popup">
        <view class="popup-content">
            <view class="head">
                <image class="company-logo" :src="shopInfo.shoplogo | imgUrl"></image>
                <view class="company-name">{{shopInfo.shopname}}</view>
            </view>
            <view class="row bottom-line">在线预约【免费设计/获取报价】</view>
            <my-input label="手机号码" maxlength="11" type="number" placeholder="设计师怎么联系您呢？" name="tel" @input="handleInput"></my-input>
            <my-input label="您的称呼" maxlength="10" type="text" placeholder="Hi-您怎么称呼？" name="name" @input="handleInput"></my-input>
            <view class="btn-box">
                <button class="default" hover-class="btn-hover" @click="subData">立即预约</button>
            </view>
            <view class="btn-box bottom">
                <button class="" hover-class="btn-hover" @click="closePopup">取消</button>
            </view>
        </view>
    </my-popup>
    <view class="flex-box bottom-bar">
        <view class="flex-1 flex-box text-center to-buy top-line right-line" @click="handleTel('shop', shopInfo)">
            <my-icon type="dianhua" color="#333333" size="22"></my-icon>
            <view>电话</view>
        </view>
        <view class="flex-1 flex-box to-common top-line" v-if="ischat" @click="showWechat()">
            <my-icon type="zixun" size="22" color="#28bdfb"></my-icon>
			<view class="text" style="color:#28bdfb">咨询</view>
        </view>
        <view class="flex-2 flex-box text-center to-tel" @click="handleYuyue()">
            <my-icon type="fabu1" color="#ffffff" size="22"></my-icon>
            <view>在线预约</view>
        </view>
    </view>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
</view>
</template>

<script>
import homeListItem from '../../components/homeListItem'
import myIcon from '../../components/icon'
import myPopup from '../../components/myPopup'
import myInput from '../../components/form/myInput'
import {
    navigateTo,
    formatImg
} from '../../common/index'
import {wxShare} from '../../common/mixin'
import allTel from '../../common/all_tel.js'
import getChatInfo from '../../common/get_chat_info'
export default {
    data() {
        return {
            page: 1,
            shopInfo: {
                shoplogo: ""
            },
            currentInfo: {
                click:"",
                pcount:""
            },
            otherInfo: [],
            params: { //预约报名提交的参数
                type: 2,
                from: 6
            },
            tel_res: {},
            show_tel_pop: false
        }
    },
    mixins:[wxShare],
    computed: {
        ischat() {
            return this.$store.state.im.ischat 
        }
    },
    components: {
        homeListItem,
        myIcon,
        myPopup,
        myInput
    },
    onLoad(options) {
        if (options.id) {
            this.id = options.id
        }
        this.getData()
    },
    filters: {
        imgUrl(val){
            return formatImg(val,'w_120')
        }
    },
    methods: {
        getData() {
            this.$ajax.get('memberShop/caseDetail.html', {
                id: this.id
            }, res => {
                if (res.data.code == 1) {
                    this.shopInfo = res.data.shop
                    this.currentInfo = res.data.currentInfo
                    this.otherInfo = res.data.otherInfo
                    if(res.data.share&&res.data.share.title){
                        this.share = res.data.share
                    }else{
                        this.share = {
                            title:res.data.currentInfo.title,
                            content:res.data.currentInfo.shopname,
                            pic:res.data.currentInfo.imgs[0]||''
                        }
                    }
                    this.getWxConfig()
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            })
        },
        handleYuyue() {
            this.$refs.popup.show()
        },
        getChildData(data){
            let info =data;
            this.handleTel("case",info)
        },
        closePopup() {
            this.$refs.popup.hide()
        },
        handleInput(e) {
            this.params[e._name] = e.detail.value
        },
        subData() {
            this.params.cid = this.currentInfo.id
            this.params.mid = this.currentInfo.shopid
            this.$ajax.get('memberShop/signUp', this.params, res => {
                if (res.data.code == 1) {
                    uni.showToast({
                        title: res.data.msg
                    })
                    this.sendMessage(this.params.mid,this.params.cid)
                    setTimeout(() => {
                        this.closePopup()
                    }, 2000)
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none",
                        duration: 3000
                    })
                }
            })
        },
        sendMessage(mid,cid){
            this.$ajax.get('memberShop/sendAdminNotice.html',{mid,cid},res=>{})
        },
        toShop(id) {
            navigateTo("/home/<USER>/detail?id=" + id)
            console.log(id)
        },
        handleTel(type, shop_data,shoptel) {
            let shopid
            if(type === 'case'){
                shopid = shop_data.shopid
            }
            if(type === 'shop'){
                shopid = shop_data.id
            }
            this.tel_params = {
                type: 5,
                callee_id: shopid,
                scene_type: 5,
                scene_id: this.id,
                success: (res)=>{
                    this.tel_res = res.data
                    this.show_tel_pop = true
                }
            }
            allTel(this.tel_params)
        },
        retrieveTel(){
            allTel(this.tel_params)
        },
        showWechat(){
            getChatInfo(this.currentInfo.uid, 14)
        }
    }
}
</script>

<style lang="scss">
.page {
    padding-bottom: 90upx;
}

.share_btn{
    position: absolute;
    top: 30upx;
    right: 20upx;
    padding: 15upx;
    background: rgba($color: #000000, $alpha: 0.6);
    width: 40upx;
    height: 40upx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    box-sizing: initial;
}
.head {
    width: 100%;
    height: 50vw;
    padding: 28upx;
    box-sizing: border-box;
    background-color: #26ad37;
    text-align: center;

    .company-logo {
        width: 120upx;
        height: 120upx;
        margin-top: 8vw;
        border-radius: 50%;
    }

    .company-name {
        margin-top:20upx;
        font-size: 46upx;
        font-weight: 500;
        color: #fff;
    }
}

.row .name {
    display: inline-block;
    padding: 20upx 28upx;
    line-height: 1.4;
    font-size: 30upx;
    color: #00c07b;
    border-bottom: 4upx solid #00c07b;
    background-color: #fff;
}

.bottom-bar {
    my-icon {
        line-height: 1;
        margin-right: 10upx;
    }

    .to-tel {
        align-items: center;
        justify-content: center;
        color: #fff;
        background-color: $uni-color-primary;
    }

    .to-buy {
        align-items: center;
        justify-content: center;
        color: #666666;
        background-color: #fff;
    }

    .to-common {
        align-items: center;
        justify-content: center;
        color: #666666;
        background-color: #fff;
    }
}

.popup-content {
    height: 100vh;
    box-sizing: border-box;
    padding-bottom: 90upx;
    background-color: #fff;

    .row {
        color: #999;
    }

    .bottom {
        width: 100%;
        box-sizing: border-box;
        position: absolute;
        bottom: 90upx;
    }
}

.handle-bar {
    padding: 10upx 0;
    line-height: 50upx;
    position: absolute;
    top: 0;
    display: flex;
    right: 44upx;
    width: 0;
    transition: 0.3s;
    border-radius: 8upx;
    background-color: #4d5154;

    .bar-item {
        flex: 1;
        min-width: 33.333%;
        overflow: hidden;
        // text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
        color: #fff;
        transform: 0.3s;
    }
}

.handle-bar.show {
    width: 480upx;
}
</style>
