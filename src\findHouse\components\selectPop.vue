<template>
	<view class="dia_box" v-if="show">
			<view class="sel_list">
				<view class="sel_title bottom-line flex-row">
					{{ title }}
				</view>
				<scroll-view scroll-y class="sel_list_con">
					<view
						class="sel_item flex-row bottom-line "
						v-for="(item, index) in currentList"
						:key="index"
						@click="changeSelect(item)"
					>
						<view class="sel_item_name">
							{{ item.name }}
						</view>
						<view
							class="sel_item_icon"
							:style="{
								background: item.isChecked ? '#2D84FB' : '#fff',
								borderColor: item.isChecked ? '#2D84FB' : '#A0A0A0',
							}"
						>
							<my-icon type="wancheng" color="#fff" size="28rpx"></my-icon>
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="sel_btn sel-confirm" @click="comfirm">
				确定
			</view>
			<view class="sel_btn sel-cancel" @click="cancel">
				取消
			</view>
	</view>
</template>

<script>
import myIcon from "../../components/myIcon";
export default {
	data() {
		return {
			currentList: [],
			show: true,
		};
	},
	components: {
		myIcon,
	},
	props: {
		list: {
			type: Array,
			default: [],
		},
		title: {
			type: String,
			default: "",
		},
		type: {
			type: String,
			default: "",
		},
		isSingleCheck: {
			type: Boolean,
			default: false,
		},
	},
	created() {
		// this.currentList=[]
	},
	mounted() {
		this.currentList = this.list;
	},
	watch: {
		list(val) {
			this.currentList = val;
		},
	},
	beforeDestroy() {
		this.show = false;
	},
	destroyed() {},
	methods: {
		changeSelect(item) {
			if (this.isSingleCheck){
				this.list.map(list=>{
					list.isChecked =false
					if (list.id==item.id){
						item.isChecked =true
					}
				})
				this.$forceUpdate()
				return 
			}
			item.isChecked = !item.isChecked;
		},
		comfirm() {
			let data = this.currentList.filter((item) => item.isChecked);
			let type = this.type;
			this.$emit("confirm", {
				type,
				data,
				list: this.currentList,
			});
		},
		cancel() {
			let type = this.type;
			this.$emit("cancel", {
				type,
			});
		},
	},
};
</script>

<style scoped lang="scss">
.flex-row {
	display: flex;
	justify-content: space-between;
}
.dia_box {
	.sel_list {
		margin: 0 32rpx 22rpx;
		border-radius: 20rpx;
		background: #fff;
		.sel_list_con{
			max-height: 40vh;
			overflow-y: auto;
		}
		.sel_title {
			padding: 24rpx;
			font-size: 28rpx;
			color: #a0a0a0;
			justify-content: center;
			align-items: center;
		}
		.sel_item {
			padding: 20rpx 54rpx;
			justify-content: space-between;
			align-items: center;
			&:last-child {
				position: static;
			}
			&_name {
				font-size: 36rpx;
				color: #3a3f42;
			}
			&_icon {
				width: 40rpx;
				height: 40rpx;
				border: 2rpx solid;
				border-radius: 50%;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
	.sel_btn {
		margin: 20rpx 32rpx 10rpx;
		background: #fff;
		padding: 24rpx 0;
		color: #2d84fb;
		font-size: 36rpx;
		text-align: center;
		border-radius: 20rpx;
		font-weight: 600;
		&.sel-cancel {
			margin-top: 10rpx;
			margin-bottom: 20rpx;
		}
	}
}
</style>
