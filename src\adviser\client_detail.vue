<template>
<view class="friend_detail">
    <view class="title bottom-line">
        <text>客户轨迹</text>
    </view>
    <view class="card">
        <!-- <view class="card_title">客访轨迹</view> -->
        <time-line :lineData="lineData"></time-line>
        <view class="tip" v-if="lineData.length===0&&nodata">还没有此客户的访问轨迹~~</view>
    </view>
</view>
</template>

<script>
import timeLine from '../components/timeLine'
export default {
    data() {
        return {
            lineData:[],
            nodata:false
        }
    },
    onLoad(options){
        if(options.id&&options.online_id){
            this.uid = options.id
            this.online_id = options.online_id
            this.getData()
        }
    },
    components: {
        timeLine
    },
    methods: {
        getData(){
            uni.showLoading({
                title:"正在加载数据"
            })
            this.$ajax.get('onlineMy/customerDetail.html',{online_id: this.online_id,uid: this.uid},res=>{
                if(res.data.code === 1){
                    this.lineData = res.data.list.map(item=>{
                        return {content:item.log_desc,time:item.log_ctime}
                    })
                }else{
                    this.nodata = true
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
                uni.hideLoading()
            },err=>{
                uni.hideLoading()
                uni.showToast({
                    title:"请求失败，请重试",
                    icon:'none'
                })
            })
        }
    },
}
</script>

<style scoped lang="scss">
.title{
    padding: 20rpx 20rpx 20rpx 40rpx;
    font-size: 32rpx;
    font-weight: bold;
    position: relative;
    &::before{
        content: "";
        position: absolute;
        left:20rpx;
        top:20rpx;
        bottom:20rpx;
        width: 6rpx;
        background-color: #f65354
    }
}
.card{
    padding: 20upx 24upx;
    .card_title{
        padding: 30upx 20upx;
        margin-bottom: 20upx;
        font-size: 32upx;
        border-bottom: 1upx solid #f1f1f1;
    }
}
</style>
