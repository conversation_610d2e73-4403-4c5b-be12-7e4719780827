<template>
<view class="atpage">
    <view class="atitle">{{annData.title}}</view>
    <view class="author-row">
        <text class="time">{{annData.time}}</text>
        <text class="author">{{annData.author}}</text>
    </view>
    <view class="acontent">
        <!-- #ifdef H5 -->
        <view class="article-content" v-html="annData.content"></view>
        <!-- #endif -->
        <!-- #ifndef H5 -->
        <u-parse :html="annData.content"></u-parse>
        <!-- #endif -->
    </view>
</view>
</template>

<script>
// #ifndef H5
import uParse from '../../components/Parser/index'
// #endif
export default {
    data() {
        return {
            id: '',
            annData: "",
            title: ""
        }
    },
    // #ifndef H5
    components: {
        uParse,
    },
    // #endif
    onLoad(options) {
        this.id = options.id || ''
        this.getData()
    },
    methods: {
        getData() {
            this.$ajax.get('member/announceDetail.html', {
                id: this.id
            }, res => {
                if (res.data.code !== 1) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                    return
                }
                uni.setNavigationBarTitle({
                    title: res.data.data.title
                })
                // const regex = new RegExp('<img', 'gi');
                // // 正则匹配处理富文本图片过大显示问题
                // res.data.data.content = res.data.data.content.replace(regex, `<img style="max-width: 100%;"`);
                this.annData = res.data.data
            })
        }
    }
}
</script>

<style lang="scss">
.atpage {
    background-color: #fff;
    min-height: 90vh;
}

.atitle {
    padding: 20upx 24upx;
    margin-bottom: 20upx;
    font-size: 38upx;
    font-weight: bold;
    color: #333;
}

.author-row {
    padding: 20upx 24upx;

    .author {
        margin-left: 10upx;
        color: #4db0fd;
    }
}

.acontent {
    padding: 20upx 24upx;
}
</style>
