<template>
<view class="p-top-180 new_house">
  <view class="top-box flex-box" @click="$navigateTo('/pages/search/search?text=新房')">
    <search @input="handleInput" disabled @confirm="handleSearch" :value="params.keyword" placeholder="你想住哪里？">
      <!-- <view class="seach_btn flex-box" v-if="is_open_adviser" @click.prevent.stop="$navigateTo('/pages/consultant/consultant')">
          <image class="ic_kefu" :src="img_domain+'/images/new_icon/ic_kefu.png?x-oss-process=style/m_240'"></image>
          <text>置业顾问</text>
    </view>  -->
    <view class="seach_btn flex-box" @click.prevent.stop="$navigateTo('/pages/map_find/map_find?type=1')">
          <my-icon type="weizhi" size="42rpx" color="#666"></my-icon>
          <text>地图</text>
    </view> 
    </search>
  </view>

  <!-- 菜单 -->
  <view class="top_grid flex-row" v-if ="nav&&nav.is_show ==1" >
			<view class="top_grid_item flex-1" v-for ="(item,index) in nav.nav" :key ="index" @click='$navigateTo(item.path)'>
				<view class="top_grid_item_icon">
					<image :src="item.icon|imgUrl('w_80')" mode ="widthFixed"></image>
				</view>
				<view class="top_grid_item_title">
					{{item.title}}
				</view>
			</view>
			
		</view>
  <!-- 模块 -->
  <view class="model flex-row" v-if ="module_nav&&module_nav.is_show == 1&&module_nav.module_nav&&module_nav.module_nav.length==4">
        <template v-if ="module_nav.style==1">
        <view class="model_icon" v-for ="(item,index) in module_nav.module_nav" :key="index" @click ="$navigateTo(item.path)" >
          <image :src="item.icon|imgUrl('w_240')" mode="aspectFit"> </image>
        </view>
        </template>
        <template v-if ="module_nav.style==2">
          <view class="model_style flex-row ">
            <view class="model_style_item"  v-for ="(item,index) in module_nav.module_nav" :key="index" @click ="$navigateTo(item.path)" :style ="{background:item.bgcolor}"  >
              <view class="model_style_item_top flex-row">
                <view class="line" :style="{backgroundColor:item.color}"></view>
                <view class="title">{{item.title}}</view>
              </view>
              <view class="model_style_item_bottom">
                {{item.descr}}
              </view>
              <view class="bg_img">
                <image :src="item.icon" mode="widthFix" alt=""></image>
              </view>

            </view>

          </view>

        </template>
      </view>
  <!-- 广告位 -->
  <view class="swiper-container" v-if ="adv.length>0">
    <view class="swiper-con">
      <view class="swiper">
            <my-swiper :focus="adv"  :autoplay="true" :interval="4000" :indicatorDots="adv.length>1" :circular="true" indicatorActiveColor="#ffffff" height="140rpx"></my-swiper>
      </view>
    </view>
  </view>
  <view class="screen-tab flex-box sel-tab" id ="tab_top">
    <view class="screen-tab-item flex-1 text-center" @click="switchTab(1)">
      <text>{{areaName}}</text>
      <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
    </view>
    <view class="screen-tab-item flex-1 text-center" @click="switchTab(2)">
      <text>{{priceName}}</text>
      <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
    </view>
    <view class="screen-tab-item flex-1 text-center" @click="switchTab(3)">
      <text>{{distanceName}}</text>
      <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
    </view>
    <view class="screen-tab-item flex-1 text-center" @click="switchTab(4)">
      <text>更多</text>
      <my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
    </view>
    <scroll-view scroll-y class="screen-panel" :class="nowTab==1?'show':''" @touchmove.stop.prevent="stopMove" v-if ="showTab">
        <addressd :addressd = "area" ref="showArea" @changes="changeArea"></addressd>
      <!-- <block v-for="item in area" :key="item.areaid">
        <uni-list-item :title="item.areaname" show-arrow="false" @click="selectArea(item.areaid, item.areaname)"></uni-list-item>
      </block> -->
    </scroll-view>
    <scroll-view scroll-y class="screen-panel" :class="nowTab==2?'show':''" @touchmove.stop.prevent="stopMove">
      <block v-for="(item,index) in price" :key="index">
        <uni-list-item :title="item.name" show-arrow="false" @click="selectPrice(item.value, item.name)"></uni-list-item>
      </block>
    </scroll-view>
    <scroll-view scroll-y class="screen-panel" :class="nowTab==3?'show':''" @touchmove.stop.prevent="stopMove">
      <block v-for="(item,index) in distance" :key="index">
        <uni-list-item :title="item.name" show-arrow="false" @click="selectDistance(item.value, item.name)"></uni-list-item>
      </block>
    </scroll-view>
    <scroll-view scroll-y class="screen-panel more-panel " :class="nowTab==4?'show':''" @touchmove.stop.prevent="stopMove">
      <view class="more-screen-item">
        <view class="title">类型</view>
        <view class="options flex-box">
          <view class="options-item" @click="selectOption({type_id:item.id},'type_id')" :class="params.type_id==item.id?'active':''" v-for="(item,index) in types" :key="index">{{item.typename}}</view>
        </view>
      </view>
      <view class="more-screen-item">
        <view class="title">销售状态</view>
        <view class="options flex-box">
          <view class="options-item" @click="selectOption({status:item.id},'status')" :class="params.status==item.id?'active':''" v-for="(item,index) in status" :key="index">{{item.leixing}}</view>
        </view>
      </view>
      <view class="more-screen-item">
        <view class="title">装修</view>
        <view class="options flex-box">
          <view class="options-item" @click="selectOption({renovation_id:item.id},'renovation_id')" :class="params.renovation_id==item.id?'active':''" v-for="(item,index) in renovation" :key="index">{{item.jiaofang}}</view>
        </view>
      </view>
      <view class="more-screen-item">
        <view class="title">状态</view>
        <view class="options flex-box">
          <view class="options-item" @click="selectOption({progress_id:item.id},'progress_id')" :class="params.progress_id==item.id?'active':''" v-for="(item,index) in progress" :key="index">{{item.jindu}}</view>
        </view>
      </view>
      <view class="flex-box padding-20">
        <button size="medium" type="default" @click="resetMore">重置</button>
        <button size="medium" type="primary" @click="selectMore()">确定</button>
      </view>
    </scroll-view>
  </view>
  <view class="filter_list" v-if="filter_list.length>0"  :style="{paddingTop:padding_top_add+10+'px'}">
    <tab-bar :tabs="filter_list" :fixedTop="false" :showLine="false" :nowIndex="current_filter_index">
      <view class="filter_item" :class="{active:index===current_filter_index}" :id="'i' + index" v-for="(item,index) in filter_list" :key="index" @click="onClickFilter(item, index)">{{item.name}}</view>
    </tab-bar>
  </view>
  <template v-if ="showList">
    <view class="house_list " :class ="{'house_little_list':likeData.length==0||!showLike}" v-if ="listsData.length>0">
      <new-house :listsData="listsData" :infoList="infoList"></new-house>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
    <view class="house_list" v-else>
      <view class="no_data flex-row">
        <view class="icon flex-row">
          <myIcon type="tishifu" fontSize ="30"></myIcon>
        </view>
        <view class="no_data_text">
          没有找到符合条件的楼盘 换个条件试试吧
        </view>
      </view>
    </view>
    <view class="house_list" v-if ="likeData.length>0&&showLike">
      <view class="xihuan flex-row">
        <view class="line flex-1">

        </view>
        <view class="xihuan_text">
          猜你喜欢
        </view>
        <view class="line flex-1">

        </view>
      </view>
      <new-house :listsData="likeData"  :infoList="infoList"></new-house>
    </view>
  </template>
  <my-dialog ref="dialog" @cancelButton="getData" :show="show_dialog" @close="show_dialog = false" title="温馨提示" openType="openSetting">
    <view class="set-nickname-box">
      <view class="row">只有获取位置权限才能获取附近的房源</view>
    </view>
  </my-dialog>
  <view class="mask" :class="nowTab>0?'show':''" @click="()=>{nowTab=0;showPannel=false}" @touchmove.stop.prevent="stopMove"></view>
 
  <chat-tip></chat-tip>
</view>
</template>

<script>
import newHouse from "../../components/newHouse.vue"
import mySwiper from "../../components/mySwiper.vue"
import search from "../../components/search.vue"
import tabBar from "../../components/tabBar.vue"
import myIcon from "../../components/myIcon.vue"
import {
  uniLoadMore,
  uniList,
  uniListItem
} from '@dcloudio/uni-ui'
import myDialog from "../../components/dialog.vue"
import {isIos,formatImg } from "../../common/index.js"
import wx from "weixin-js-sdk"
import {statistics} from '../../common/statistics'
import addressd from '../../components/jm-address/jm-address'
export default {
  components: {
    newHouse,
    uniLoadMore,
    uniList,
    uniListItem,
    search,
    tabBar,
    myDialog,
    addressd,
    myIcon,
    mySwiper
  },
  data() {
    return {
      get_status: "loading",
      likeData:[],
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了"
      },
      color: '#28bdfb',
      nowTab: 0,
      areaName: '区域',
      priceName: '价格',
      distanceName: '距离',
      area: [],
      price: [],
      distance: [],
      status: [],
      types: [],
      filter_list:[],
      current_filter_index:0,
      renovation: [],
      progress: [],
      params: {
        distance: "",
        page: 1,
        rows: 20,
        keyword:""
      },
      listsData: [],
      show_dialog: false,
      infoList:[],
      qu:[],
      showTab:false,
      adv:[],
      nav:{},
      module_nav:{},
      scrollTop:0,
      showPannel:false,
      scrollTopOffset:0,
      padding_top_add:0,
      showList:false,
      showLike:false,
    }
  },
  computed:{
    is_open_adviser() {
      return this.$store.state.im.adviser
    },
    img_domain() {
      return this.$imgDomain
    },
    status_top(){
      return this.$store.state.systemInfo.statusBarHeight
    }
  },
  onLoad(options) {
    for (let key in options){
      this.params[key] = options[key]
    }
    if(options.keyword){
      this.params.keyword = decodeURIComponent(options.keyword)
    }
    if(options.areaid){
      this.params.areaid = options.areaid
    }
    if(!uni.getStorageSync('no_watch_search_key')){
      uni.$on('handleSearch',(e)=>{
        this.params.keyword = e
        this.params.page = 1
        this.getData()
      })
    }
    this.getData()
    this.getScreen()
  },
  onPageScroll(e){
      this.scrollTop = e.scrollTop
			if (this.showPannel) {
				uni.pageScrollTo({
				scrollTop:this.scrollTopOffset,
				duration: 0
				});
			}
		},
  onShow() {
    setTimeout(() => {
      if (this.$store.state.updatePageData) {
        this.$store.state.updatePageData = false
        this.getLocation()
      }
    }, 150)
  },
  filters:{
    imgUrl(val, param = "") {
        return formatImg(val, param)
    },
  },
  onUnload(){
    if(!uni.getStorageSync('no_watch_search_key')){
      uni.$off('handleSearch')
    }else{
      uni.removeStorageSync('no_watch_search_key')
    }
  },
  methods: {
    getLocation(){
      let url;
      if(isIos()){
        url = this.$store.state.firstUrl
      }else{
        url = window.location.href
      }
      this.$ajax.get('/wechat/index/signature.html',{url:url},res=>{
        if(res.data.code == 1){
          res.data.config.jsApiList = ['getLocation','updateAppMessageShareData','updateTimelineShareData']
          wx.config(res.data.config)
          this.$store.state.getPosition(wx,()=>{
            this.getData()
          })
        }else{
          this.getData()
        }
      })
    },
    getQu(obj){
				for (let i = 0; i < obj.length; i++) {
				if (obj[i].parentid==0){
					this.qu.push(obj[i])
				}
			}
		},
    getJiedao (a,idStr,pIdStr,chindrenStr) {
        var r = [], hash = {}, id = idStr, pId = pIdStr, children = chindrenStr, i = 0, j = 0, len = a.length;  
        for(; i < len; i++){  
        a[i].label= a[i].name;	
        delete a[i].name; 
            hash[a[i][id]] = a[i];
        }  
        for(; j < len; j++){  
            var aVal = a[j], hashVP = hash[aVal[pId]];  

            if(hashVP){  
                !hashVP[children] && (hashVP[children] = []);  
          
                hashVP[children].unshift(aVal);  
            }else{  
                r.unshift(aVal);  
            }  
        } 
        return r;
    },
    updateArrayNames(arr) {
      if (Array.isArray(arr)) { // 如果是数组
         arr.forEach((item) => {
          if (item.children) {
          item.city = item.children; // 修改属性名
          delete item.children;
          this.updateArrayNames(item.city); // 递归调用
        } else {
          Object.values(arr).forEach((item) => this.updateArrayNames(item)); // 如果不是children属性，则遍历该元素的值并递归调用
        }
      }); 
         }
        return arr;
    },
    changeArea(e){
      this.current_filter_index = -1
      this.areaName = e.district?e.district:(e.city?e.city:(e.province?e.province:""))
      this.params.areaid = e.district_id?e.district_id:(e.city_id?e.city_id:(e.province_id?e.province_id:""))
      this.nowTab = 0
      this.params.page = 1
      this.getData()
    },
    scroppTo(fun){
      const query = uni.createSelectorQuery().in(this);
      query.select('#tab_top').fields({rect:true,scrollOffset:true},data => {
        if(data.top<=this.status_top+44){
          fun&&fun()
          return
        }
        // #ifdef H5
        this.scrollTopOffset=(this.scrollTop||0) + data.top-uni.upx2px(80),
        // #endif
        // #ifndef H5
        this.scrollTopOffset=(this.scrollTop||0) + data.top-uni.upx2px(80),
        // #endif
        uni.pageScrollTo({
          duration:120,
          // #ifdef H5
          scrollTop: this.scrollTopOffset,
          // #endif
          // #ifndef H5
					scrollTop:this.scrollTopOffset,
					// #endif
					success:()=>{
						if (fun){
							fun()
						}
					}
        })
      }).exec();
    },
    getScreen() {
      this.$ajax.get('build/buildCondition', {}, (res) => {
        res.data.areas.unshift({areaid:'',parentid:0,mapx:'',mapy:'',areaname:"全部"})
        this.area =this.updateArrayNames(res.data.areas)        
        // this.area = res.data.areas.filter((item)=>{        
        //   item['city']=item['children'];    
        //   return item       
        // }) 
          console.log( this.area,"areaareaarea") 
          // console.log(res.data.area)
        if(this.params.areaid){
          this.getParentId(area,parseInt(this.params.areaid),true)
        }   
        this.price = res.data.price
        this.distance = res.data.distance
        this.status = res.data.status
        this.types = res.data.types
        this.renovation = res.data.renovation
        this.progress = res.data.progress 
        let all_filter = [{type:'all',name:'全部'}]
        this.filter_list = all_filter.concat(res.data.cate)
        if(this.params.label){
          this.$nextTick(()=>{
            this.current_filter_index = this.filter_list.findIndex(item=>item.id==this.params.label)
            console.log(this.current_filter_index)
          })
        }
        this.showTab=true
        
      }, (err) => {
        console.log(err)
      })
    },
    getParentId(arr,id,is_first){
      const res = arr.find(item=>{
        return item.areaid === parseInt(id)
      })
      if(res&&is_first){
        this.areaName = res.areaname
      }
      if(res&&res.parentid){
        this.getParentId(arr,res.parentid)
      }else{
        this.co_id = id
      }
    },
    getData() {
      this.params.lat = this.$store.state.position.lat
      this.params.lng = this.$store.state.position.lng
      // if (this.params.page == 1) {
      //   this.listsData = []
      // }
      this.get_status = "loading"
      this.showPannel=false
      
      this.$ajax.get('build/index', this.params, (res) => {
        if(res.data.nav){
          this.nav =res.data.nav
        }
        if (res.data.module_nav){
          this.module_nav =res.data.module_nav
        }
        if (res.data.code == 1) {
          res.data.list.map(item=>{
            if (item.build_type &&item.build_type.length>2){
              item.build_type.length=2
            }
          })
          res.data.list2&&res.data.list2.map(item=>{
            if (item.build_type &&item.build_type.length>2){
              item.build_type.length=2
            }
          })
          this.likeData = res.data.list2? res.data.list2 :[]
          if (this.params.page==1){
            this.listsData =res.data.list
          }else {
            this.listsData = this.listsData.concat(res.data.list)
          }
          
          this.showList=true
          if (res.data.list.length < this.params.rows) {
            this.get_status = "noMore"
            this.showLike =true
          } else {
            this.get_status = "more"
          }
        } else {
          this.showLike =true
          this.listsData =[]
          this.get_status = "noMore"
        }
        
        if(res.data.share) {
          this.share = res.data.share
        }else{
          this.share = {}
        }
        this.getWxConfig()
        if (res.data.adv&&this.adv.length==0){
          res.data.adv.map(item=>{
            item.url=item.wap_link
          })
          this.adv=res.data.adv
        }
        uni.stopPullDownRefresh();
      }, (err) => {
        console.log(err)
        this.listsData =[]
        uni.stopPullDownRefresh();
      })
    },
    stopMove() {

    },
    switchTab(index) {
      this.scroppTo(()=>{
					let timeout =setTimeout(() => {
          if (this.nowTab == index) {
            this.nowTab = 0
            this.showPannel=false
          } else {
            this.nowTab = index
            this.showPannel=true
            if (index==1){
              this.$refs.showArea.showAddress()
            }
          }
        },500)
      })
    },
    selectArea(id, name) {
      this.current_filter_index = -1
      this.params.areaid = id
      this.areaName = name
      this.nowTab = 0
      this.params.page = 1
      this.scrollTopOld =this.scrollTop
      this.getData()
    },
    selectPrice(value, name) {
      this.scrollTopOld =this.scrollTop
      this.current_filter_index = -1
      this.params.price = value
      this.priceName = name
      this.nowTab = 0
      this.params.page = 1
      this.getData()
    },
    selectDistance(id, name) {
      this.scrollTopOld =this.scrollTop
      this.current_filter_index = -1
      this.params.distance = id
      this.distanceName = name
      this.nowTab = 0
      this.params.page = 1
      // this.getData()
      // #ifdef MP
      checkAuth('scope.userLocation', {
        
        authOk: () => {
          this.getLocation()
        },
        success: () => {
          this.getLocation()
        },
        fail: () => {
          this.show_dialog = true
        }
      })
      // #endif
      // #ifdef H5 || APP-PLUS
      this.getLocation()
      // #endif
    },
    selectOption(obj, type) {
      this.scrollTopOld =this.scrollTop
      switch (type) {
        case "type_id":
          if (this.params.type_id == obj.type_id) {
            obj.type_id = ""
          }
          case "renovation_id":
            if (this.params.renovation_id == obj.renovation_id) {
              obj.renovation_id = ""
            }
            case "status":
              if (this.params.status == obj.status) {
                obj.status = ""
              }
              case "progress_id":
                if (this.params.progress_id == obj.progress_id) {
                  obj.progress_id = ""
                }
      }
      this.params = Object.assign({}, this.params, obj)
    },
    selectMore() {
      this.current_filter_index = -1
      this.nowTab = 0
      this.params.page = 1
      this.getData()
    },
    resetMore() {
      this.params.type_id = ""
      this.params.renovation_id = ""
      this.params.progress_id = ""
      this.params.status=""
    },
    handleInput(e) {
      this.keyword = e.detail.value
    },
    handleSearch(e) {
      this.params.keyword = e.detail.value
      this.params.page = 1
      this.getData()
    },
    onClickFilter(item, index){
      this.current_filter_index = index
      if(item.type === 'all'){
        this.areaName = '全部'
        this.priceName = '价格'
        this.distanceName = '距离'
        this.params.price = ''
        this.params.distance = ''
        this.params.type_id = ''
        this.params.areaid = ''
        this.params.status = ''
        this.params.label = ''
        this.params.page = 1
        this.resetMore()
        this.getData()
        return
      }
      this.params.label=item.id
      this.params.page = 1
      this.getData()
    },
  },
  onPullDownRefresh() {
    this.params.page = 1
    this.getData()
  },
  onReachBottom() {
    if (this.get_status == "more"){
      this.params.page = this.params.page + 1
      this.getData()
    }
  }
}
</script>

<style lang="scss">
.p-top-180 {
  padding-top: 90rpx;
}
.flex-row{
  display: flex;
  flex-direction: row;
}
.new_house{
  background: #fff;
}
.sel-tab {
  box-shadow: 0 0 0 #fff;
}
.swiper-container{
  background: #fff;
  margin-bottom: 20rpx;
  .swiper-con{
    padding:0 48rpx;
  }
  .swiper{
    border-radius: 8rpx;
    overflow: hidden;
  }
}


.seach_btn {
  margin-left: 24rpx;
  align-items: center;
  .ic_kefu{
    width: 46rpx;
    height: 46rpx;
    margin-right: 10rpx;
  }
  text{
    font-size: 28rpx;
    color: #333;
    margin-left: 10rpx;
  }
}

/* #ifdef H5 */
.top-box {
  position: fixed;
  top: 44px;
  width: 100%;
  background-color: #fff;
  z-index: 100;
  align-items: center;
  justify-content: space-between;
}
.top-box search{
   flex: 1;
}

.screen-tab{
		position: sticky;
		top: 85px;
		// margin-top: 20rpx;
		box-sizing: border-box;
		padding: 0 48rpx;
	}
	.screen-panel {
		top:0;
    display: none;
		margin-top: 44px;
		// margin-top: 80rpx;
	}
	.screen-panel.show {
		left: 0;
    display: block;
    top:75px;
	}
/* #endif */
/* #ifndef H5 */
.top-box {
  position: fixed;
  top: var(--window-top);
  width: 100%;
  background-color: #fff;
  z-index: 100;
  align-items: center;
  justify-content: space-between;
}
.top-box search{
   flex: 1;
}

.screen-tab{
		position: sticky;
		top: 0;
		margin-top: 20rpx;
		box-sizing: border-box;
		padding: 0 48rpx;
	}
	.screen-panel {
		position: absolute;
		top: 85px;
		display: none;
    line-height: 1;
	}
	.screen-panel.show {
		display: block;
		left: 0;
	}
/* #endif */

.filter_list{
  padding-left: 48rpx;
  padding-top: 24rpx;
  background-color: #fff;
  transition: 0.3s;
  .filter_item{
    display: inline-block;
    padding: 10rpx 20rpx;
    border-radius: 4rpx;
    line-height: 1;
    box-sizing: border-box;
    margin-right: 24rpx;
    font-size: 24rpx;
    background-color: #f5f5f5;
    border: 1rpx solid #f5f5f5;
    color: #999;
    &.active{
      background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
      color: $uni-color-primary;
      border: 1rpx solid $uni-color-primary;
    }
  }
}
.top_grid{
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		background: #FFFFFF;
		&_item{
			text-align: center;
			&_icon{
				width: 64rpx;
				height: 64rpx;
				margin: 0 auto;
				overflow: hidden;
				image{
					width: 100%;
					height: 100%;
				}
			}
			&_title{
				margin-top: 15rpx;
				font-size: 24rpx;
				color: #333333;
				letter-spacing: 0;
				text-align: center;
			}
		}
	}
.model{
  padding: 24rpx 48rpx 0;
  flex-wrap: wrap;
  background: #fff;
  .model_icon{
    width: 48%;
    height: 120rpx;
    background: #FFFFFF;
    // border: 2rpx solid #D8D8D8;
    // box-shadow: 0 0 4px 0 rgba(0,0,0,0.04);
    border-radius: 4px;
    overflow: hidden;
    margin-right: 4%;
    margin-bottom: 24rpx;
    box-sizing: border-box;
    &:nth-child(2n){
      margin-right: 0;
    }
    image{
      width: 100%;
      height: 100%;

    }
  }
  .model_style{
    align-items: center;
    flex-wrap: wrap;
    
    width: 100%;
    &_item{
      width: 48%;
      padding: 20rpx 10rpx;
      // border: 2rpx solid #f3f3f3;
      border-radius: 10rpx;
      margin-right: 4%;
      margin-bottom:24rpx;
      box-sizing: border-box;
      position: relative;
      background-repeat: no-repeat;
      background-position: right bottom;
      &:nth-child(2n){
        margin-right: 0;
      }
      &_top{
        align-items: center;
        .line{
          height: 32rpx;
          width: 8rpx;
          margin-top: 4rpx;
          margin-right: 10rpx;

        }
        .title{
          font-size: 32rpx;
          color: #333;
          font-weight: 700;
          display: inline-block;
          max-width: 90%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      &_bottom{
        font-size: 24rpx;
        margin-top: 4rpx;
        color: #999;
        display: inline-block;
        max-width: 90%;
        z-index: 2;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .bg_img {
        width:96rpx;
        height: 96rpx;
        position: absolute;;
        right: 0 ;
        bottom: 0;
        image{
          width: 100%;

        }
      }

    }
  }

}
.more-screen-item .options .options-item {
  line-height: 1;
}
.house_list{
  &.house_little_list{
    min-height: 100vh;
  }
  // min-height: 80vh;
  .no_data{
    justify-content: center;
    align-items: center;
    padding: 40rpx 0;
    .no_data_text{
      font-size: 30rpx;
      color: #333;
      margin-left: 10rpx;

    }
    .icon {
      justify-content: center;
      align-items: center;
    }
  }
  .xihuan{
    justify-content: center;
    align-items: center;
    padding: 0 0 20rpx ;
    .xihuan_text{
      margin: 0 20rpx;
      color: #968E9F;
      font-size: 30rpx;

    }
    .line{
      height: 2rpx;
      background:#E1E3EE;
    }
  }
}
</style>
