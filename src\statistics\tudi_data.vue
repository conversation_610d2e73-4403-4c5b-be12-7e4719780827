<template>
	<view class="page" :class="{pdb_120: (sharers_info.adviser_id&&is_open_adviser)||sharers_info.agent_id}">
		<view class="header" :style="{ backgroundImage: 'url(' + bgcolor + ')' }">
			<view class="head-info">
				<view class="title">土拍榜</view>
				<view class="update-tip flex-box">
					<picker :range="months" @change="getMonth"
						>{{ siteName }} {{ params.month || "" }}</picker
					>
					<my-icon type="ic_down" size="14px" color="#fff"></my-icon>
				</view>
				<view class="update-tip">
					<view class="dingyue flex-box" @click="showDingyuePop">
						<my-icon type="ic_jia" color="#fff" size="24rpx"></my-icon
						><text class="text">订阅</text>
					</view>
				</view>
			</view>
			<view class="share_icon" @click="showSharePop">
				<my-icon type="ic_fenxiang" size="32rpx" color="#fff"></my-icon>
			</view>
		</view>

		<view class="yushou-data-info">
			<view class="middle-bar">
				<view class="middle-bar-info flex-box">
					<view
						class="mid-bar flex-box flex-row"
						:class="{ active: params.sort == 1 }"
						@click="clickTab(1)"
					>
						<view class="mid-bar-info">成交价</view>
					</view>
					<view
						class="mid-bar flex-box flex-row"
						:class="{ active: params.sort == 2 }"
						@click="clickTab(2)"
					>
						<!-- <image></image> -->
						<view class="mid-bar-info">面积</view>
					</view>
					<view
						class=" mid-bar flex-box flex-row"
						:class="{ active: params.sort == 3 }"
						@click="clickTab(3)"
					>
						<!-- <image></image> -->
						<view class="mid-bar-info">楼面价</view>
					</view>
					<view
						class=" mid-bar flex-box flex-row"
						:class="{ active: params.sort == 4 }"
						@click="clickTab(4)"
					>
						<!-- <image></image> -->
						<view class="mid-bar-info">溢价</view>
					</view>
				</view>
			</view>
			<view class="top-20">
				<view class="lists">
					<view
						class="copy-text flex-box"
						@click="copyContent"
						v-if="textContent"
					>
						<view class="text-content">
							{{ textContent }}
						</view>
						<view class="copy flex-box">
							<my-icon type="copy" color="#fff" size="24rpx"></my-icon>
							<view class="copy-btn">复制</view>
						</view>
					</view>
					<view class="shujubaobiao" v-if="showJianbao">
						<view class="zhankai" @click="showGongying = !showGongying"
							><my-icon
								:type="showGongying ? 'ic_up' : 'ic_down'"
								color="#fb656a"
								size="28rpx"
							></my-icon
						></view>
						<view class="baobiao-title"> {{ params.month }}月数据统计 </view>
						<view class="baobiao-box">
							<view class="baobiao-con flex-1 flex-box ">
								<view class="flex-1 fengexian ">
									<view class="data-data flex-box flex-1"
										><text class="data-datas">{{
											jianbao.chengJiaoZongShu.value || 0
										}}</text
										>个</view
									>
									<view class="data-info flex-box">
										<view class="data-title">成交宗数</view>
										<view class="data-bijiao flex-box">
											<block v-if="jianbao.chengJiaoZongShu.status == 2">
												<my-icon
													type="ic_down"
													color="#00CAA7"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_down"
													>{{ jianbao.chengJiaoZongShu.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.chengJiaoZongShu.status == 1">
												<my-icon
													type="ic_up"
													color="#fb656a"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_up"
													>{{ jianbao.chengJiaoZongShu.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.chengJiaoZongShu.status == 0">
												<view class="bijiao-text">持平</view>
											</block>
											<block v-if="jianbao.chengJiaoZongShu.status == 3">
												<view class="bijiao-text">--</view>
											</block>
										</view>
									</view>
								</view>
								<view class="flex-1">
									<view class="data-data flex-box"
										><text class="data-datas">{{
											jianbao.chengJiaoMianJi.value || 0
										}}</text
										>亩</view
									>
									<view class="data-info flex-box">
										<view class="data-title">成交面积</view>
										<view class="data-bijiao flex-box">
											<block v-if="jianbao.chengJiaoMianJi.status == 2">
												<my-icon
													type="ic_down"
													color="#00CAA7"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_down"
													>{{ jianbao.chengJiaoMianJi.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.chengJiaoMianJi.status == 1">
												<my-icon
													type="ic_up"
													color="#fb656a"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_up"
													>{{ jianbao.chengJiaoMianJi.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.chengJiaoMianJi.status == 0">
												<view class="bijiao-text">持平</view>
											</block>
											<block v-if="jianbao.chengJiaoMianJi.status == 3">
												<view class="bijiao-text">--</view>
											</block>
										</view>
									</view>
								</view>
							</view>
							<view class="baobiao-con flex-1 flex-box ">
								<view class="flex-1 fengexian ">
									<view class="data-data flex-box flex-1"
										><text class="data-datas">{{
											jianbao.chengJiaoJunJia.value || 0
										}}</text
										>元/m²</view
									>
									<!-- <view class="data-data"><text class="data-datas red">{{jianbao.chengJiaoJunJia.value||0}}</text></view> -->
									<view class="data-info flex-box">
										<view class="data-title">成交均价</view>
										<view class="data-bijiao flex-box">
											<block v-if="jianbao.chengJiaoJunJia.status == 2">
												<my-icon
													type="ic_down"
													color="#00CAA7"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_down"
													>{{ jianbao.chengJiaoJunJia.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.chengJiaoJunJia.status == 1">
												<my-icon
													type="ic_up"
													color="#fb656a"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_up"
													>{{ jianbao.chengJiaoJunJia.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.chengJiaoJunJia.status == 0">
												<view class="bijiao-text">持平</view>
											</block>
											<block v-if="jianbao.chengJiaoJunJia.status == 3">
												<view class="bijiao-text">--</view>
											</block>
										</view>
									</view>
								</view>
								<view class="flex-1">
									<view class="data-data flex-box flex-1"
										><text class="data-datas">{{
											jianbao.louMianJia.value || 0
										}}</text
										>元/m²</view
									>
									<view class="data-info flex-box">
										<view class="data-title">楼面均价</view>
										<view class="data-bijiao flex-box">
											<block v-if="jianbao.louMianJia.status == 2">
												<my-icon
													type="ic_down"
													color="#00CAA7"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_down"
													>{{ jianbao.louMianJia.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.louMianJia.status == 1">
												<my-icon
													type="ic_up"
													color="#fb656a"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_up"
													>{{ jianbao.louMianJia.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.louMianJia.status == 0">
												<view class="bijiao-text">持平</view>
											</block>
											<block v-if="jianbao.louMianJia.status == 3">
												<view class="bijiao-text">--</view>
											</block>
										</view>
									</view>
									<!-- <view class="data-data"><text class="data-datas">{{tudiData.total_area||0}}</text></view> -->
								</view>
							</view>
							<view class="baobiao-con flex-1 flex-box" v-if="showGongying">
								<view class="flex-1 fengexian">
									<view class="data-data flex-box flex-1"
										><text class="data-datas">{{
											jianbao.gongYingZongShu.value || 0
										}}</text
										>个</view
									>
									<view class="data-info flex-box">
										<view class="data-title">供应宗数</view>
										<view class="data-bijiao flex-box">
											<block v-if="jianbao.gongYingZongShu.status == 2">
												<my-icon
													type="ic_down"
													color="#00CAA7"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_down"
													>{{ jianbao.gongYingZongShu.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.gongYingZongShu.status == 1">
												<my-icon
													type="ic_up"
													color="#fb656a"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_up"
													>{{ jianbao.gongYingZongShu.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.gongYingZongShu.status == 0">
												<view class="bijiao-text ic_down">持平</view>
											</block>
											<block v-if="jianbao.gongYingZongShu.status == 3">
												<view class="bijiao-text ic_down">--</view>
											</block>
										</view>
									</view>
								</view>
								<view class="flex-1">
									<view class="data-data flex-box flex-1"
										><text class="data-datas">{{
											jianbao.gongYingMianJi.value || 0
										}}</text
										>m²</view
									>
									<!-- <view class="data-data"><text class="data-datas">{{tudiData.count||0}}</text>万m²</view> -->
									<view class="data-info flex-box">
										<view class="data-title ">供应面积</view>
										<view class="data-bijiao flex-box">
											<block v-if="jianbao.gongYingMianJi.status == 2">
												<my-icon
													type="ic_down"
													color="#00CAA7"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_down"
													>{{ jianbao.gongYingMianJi.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.gongYingMianJi.status == 1">
												<my-icon
													type="ic_up"
													color="#fb656a"
													size="22rpx"
												></my-icon>
												<view class="bijiao-text ic_up"
													>{{ jianbao.gongYingMianJi.status_value }}%</view
												>
											</block>
											<block v-if="jianbao.gongYingMianJi.status == 0">
												<view class="bijiao-text ic_down">持平</view>
											</block>
											<block v-if="jianbao.gongYingMianJi.status == 3">
												<view class="bijiao-text ic_down">--</view>
											</block>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view
						class="time-line"
						v-for="(item, index) in listData"
						:key="item.id"
						@click="toDetail(item.id)"
					>
						<view class="time">
							<view class="line-title">
								{{ item.cjrq }}
							</view>
							<view class="data-card">
								<data-card
									:item="item"
									:idx="index"
									type="tudi"
									:red="params.sort"
								></data-card>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="friend-tips">
                {{friendTips}}
            </view>
		</view>
		<view
			class="sharers_info flex-box"
			v-if="
				(sharers_info.adviser_id && is_open_adviser) || sharers_info.agent_id
			"
		>
			<view class="img">
				<image
					:src="sharers_info.prelogo | imageFilter('w_240')"
					mode="widthFix"
				></image>
			</view>
			
			<view class="info flex-1">
				<view class="name">{{ sharers_info.cname }}</view>
				<view class="identity">{{
					sharers_info.identity === 1 ? "置业顾问" : "经纪人"
				}}</view>
			</view>
			<view class="btn_box flex-box">
				<view class="btn" @click="handleChat()">微聊</view>
				<view class="btn" v-if ="(sharers_info.adviser_id&&switch_adviser_tel) ||sharers_info.agent_id" @click="handleTel()">电话咨询</view>
			</view>
		</view>
		<share-pop
			ref="show_share_pop"
			@copyLink="copyLink"
			:showHaibao="false"
			@showCopywriting="showCopywriting"
		></share-pop>
		<dingyue
			ref="dingyue"
			@dingyue="dingyue"
			:type="type"
			@login="toLogin"
			@bindPhone="toBind"
		></dingyue>
		<my-popup ref="qrcode_popup" position="top">
			<view class="qrcode-box">
				<!-- #ifdef H5 -->
				<view class="img-box">
					<view class="title titles">数据报告将通过服务号发送</view>
					<view class="tip red">请关注{{ siteName }}公众号</view>
					<image
						@longtap="saveQrcode"
						class="qrcode"
						:src="qrcode"
						mode="aspectFill"
					></image>
					<view>
						<view class="title">长按保存图片</view>
						<view class="tip">相册选取，识别关注</view>
					</view>
				</view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view class="img-box">
					<view class="title titles">数据报告将通过服务号发送</view>
					<view class="tip red">请关注{{ siteName }}公众号</view>
					<image class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="tip">长按识别二维码关注公众号</view>
					</view>
				</view>
				<!-- #endif -->
				<view class="icon-box" @click="$refs.qrcode_popup.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
		<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
		<enturstBtn :to_user="sharers_info"  v-if="sharers_info.agent_id||sharers_info.adviser_id" @click="$refs.enturst_popup.show()" />
		<my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
		<enturstBox @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="showLoginPopup('为方便您及时接收消息通知，请输入手机号码')" :to_user="sharers_info" />
		</my-popup>
		<!-- 登录弹窗 -->
        <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
		<chat-tip></chat-tip>
		<tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
	</view>
</template>

<script>
import dataCard from "../components/dataCard";
import myIcon from "../components/myIcon";
import { config } from "../common/config";
// import { uniLoadMore } from "@dcloudio/uni-ui";
// #ifndef MP-WEIXIN
import loginPopup from "../components/loginPopup";
// #endif
import sharePop from "../components/sharePop";
import getChatInfo from "../common/get_chat_info";
import allTel from '../common/all_tel.js'
import myPopup from "../components/myPopup.vue";
import shareTip from "../components/shareTip.vue";
import dingyue from "../components/dingyue.vue";
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
export default {
	data() {
		return {
			get_status: "loading",
			content_text: {
				contentdown: "",
				contentrefresh: "正在加载...",
				contentnomore: "没有更多数据了",
			},
			listData: [],
			tabs: [],
			tip: "正在加载...",
			params: {
				keyword: "",
				sort: 1,
			},
			currentIndex: 0,
			currentIdx: "1",
			bgcolor: "",
			// 分享者信息
			sharers_info: {},
			// 当前用户的信息
			current_user_info: {},
			login_tip: "",
			months: [],
			defaultMonth: "",
			tips: "",
			qrcode: "",
			textContent: "",
			showGongying: false,
			showJianbao: true,
			jianbao: {
				chengJiaoJunJia: {},
				louMianJia: {},
				gongYingMianJi: {},
				gongYingZongShu: {},
				chengJiaoZongShu: {},
				chengJiaoMianJi: {},
			},
			show_share_tip:false,
			type: "dingyue",
			shareId:'',
			shareType:'',
			show_share_pop:false,
			siteCity:'',
			friendTips:'',
			tel_res: {},
			show_tel_pop: false,
		};
	},
	components: {
		// uniLoadMore,
		dataCard,
		sharePop,
		myIcon,
		// #ifndef MP-WEIXIN
		loginPopup,
		// #endif
		myPopup,
		dingyue,
		shareTip,
		enturstBtn,
		enturstBox
	},
	onLoad(options) {
		// 如果是分享链接进来的
		if (options.shareId && (options.type||options.shareType)) {
			this.shareId = options.shareId;
			this.shareType = options.type||options.shareType;
			this.share_time =options.f_time||''
		}
		if (options.sort) {
			this.params.sort = parseInt(options.sort);
		}
		if (options.month) {
			this.params.month = options.month;
		}
		this.bgcolor = config.imgDomain + "/images/new_icon/record/<EMAIL>";
		this.getData();
		this.getStatisData();
		uni.$on("getDataAgain", () => {
			this.getStatisData();
		});
	},
	onUnload() {
		uni.$off("getDataAgain");
	},
	onShow() {
		if (this.$store.state.updatePageData) {
			this.getStatisData();
			this.$store.state.updatePageData = false;
		}
	},
	computed: {
		siteName() {
			return this.$store.state.siteName;
		},
		is_open_adviser() {
			//是否开启置业顾问功能
			return this.$store.state.im.adviser;
		},
		is_open_im() {
			// 是否开启聊天功能
			return this.$store.state.im.ischat;
		},
		switch_adviser_tel(){
			return this.$store.state.switch_adviser_tel;
		}
	},
	methods: {
		getData() {
			this.listData = [];
			this.$ajax.get(
				"build/tudi.html",
				this.params,
				(res) => {
					this.months = res.data.months;
					if (!this.params.month) {
						this.params.month = this.months[0];
					}
					//console.log("yyj:"+JSON.stringify(res.data.seo));
					if (res.data.code == 1) {
						this.listData = res.data.list;
						this.tips = res.data.declare;
					} else {
						uni.showToast({
							title: "暂无数据",
							icon: "none",
						});
					}
					this.getShare();
					this.textContent = "";
					if (res.data.code == 1) {
						this.getText();
					} else {
						this.showJianbao = false;
					}
				},
				(err) => {
					console.log(err);
				}
			);
		},
		getStatisData() {
			let params = {};
			if (this.shareId && this.shareType) {
				params = {
					sid: this.shareId,
					sharetype: this.shareType,
				};
			}
			params.forward_time=this.share_time ||''
			this.$ajax.get("build/tudiStatistics", params, (res) => {
				if (res.data.share) {
					this.share = res.data.share;
				}
				this.type = "dingyue";
				if (res.data.shareUser) {
					//当前用户信息
					this.current_user_info = res.data.shareUser;
					if (res.data.shareUser.adviser_id) {
						this.current_user_info.identity = 1;
						this.current_user_info.identity_id = res.data.shareUser.adviser_id;
					} else if (res.data.shareUser.agent_id) {
						this.current_user_info.identity = 2;
						this.current_user_info.identity_id = res.data.shareUser.agent_id;
					}
				}
				if (res.data.share_user) {
					//分享者信息
					this.sharers_info = res.data.share_user;
					if (res.data.share_user.adviser_id) {
						this.sharers_info.identity = 1;
					} else if (res.data.share_user.agent_id) {
						this.sharers_info.identity = 2;
					}
				}
				// 获取登录状态
				this.$ajax.get("member/checkUserStatus", {}, (res) => {
					if (res.data.code !== 1) {
						this.$store.state.user_login_status = res.data.status;
						if (this.$store.state.user_login_status==1){
							this.type = "denglu";
							uni.setStorageSync('backUrl', window.location.href)
							this.$store.state.updatePageData = true;
							this.showDingyuePop();
						}else if (this.$store.state.user_login_status==2){
							this.type='bangshouji'
							this.$store.state.updatePageData=true
							this.showDingyuePop()
						}
					}
				});
				if (res.data.siteCity) {
					this.siteCity = res.data.siteCity;
				}
				this.getShare()
				if (res.data.disclaimer){
					this.friendTips=res.data.disclaimer
				}
			});
		},
		toLogin() {
			this.$refs.dingyue.hide();
			this.$navigateTo("/user/login/login");
		},
		toBind(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/bind_phone/bind_phone")
		},
		getText() {
			this.$ajax.get(
				"build/tudiBriefingByTop",
				{ month: this.params.month },
				(res) => {
					if (res.data.code == 1) {
						this.showJianbao = true;
						this.jianbao = res.data.data;
						this.textContent = res.data.content;
					}
				}
			);
		},
		getShare() {
			let type = "";
			if (this.params.sort == 1) {
				type = "成交价";
			} else if (this.params.sort == 2) {
				type = "面积";
			} else if (this.params.sort == 3) {
				type = "楼面价";
			} else if (this.params.sort == 4) {
				type = "溢价";
			}
			if (!this.share){
				this.share={
					title:''
				}
			}
			this.share.title=`${this.params.month}月${this.siteCity}土地市场成交数据榜单`,
			this.share.link=this.getShareLink()
			this.getWxConfig()
		},
		showSharePop(){
			this.getShortLink()
			this.$refs.show_share_pop.show()
		},
		getMonth(e) {
			this.params.month = this.months[e.detail.value];
			this.getData();
		},
		clickTab(type) {
			this.params.sort = type;
			this.getData();
		},
		handleSearch(e) {
			this.params.page = 1;
			this.params.keyword = e;
			this.getData();
		},
		toDetail(id) {
			this.$navigateTo(`/pages/tudi/detail?id=${id}`);
		},
		// 获取分享链接
		getShareLink() {
			let link = "";
			let time =parseInt(+new Date()/1000)
			link = `${window.location.origin}${window.location.pathname}?sort=${this.params.sort}&month=${this.params.month}&shareId=${this.shareId}&type=${this.shareType}&f_time=${time}`;

			if (this.current_user_info.identity) {
				//当前用户是 置业顾问或者经纪人
				link = `${window.location.origin}${window.location.pathname}?shareId=${this.current_user_info.identity_id}&type=${this.current_user_info.identity}&sort=${this.params.sort}&month=${this.params.month}&f_time=${time}`;
			}
			return link;
		},
		getShortLink(callback){
            this.link=this.getShareLink()
            this.$ajax.get('build/shortUrl.html', {page_url:this.link }, res=>{
                if(res.data.code === 1){
					this.link = res.data.short_url
				}
				if (callback){
					callback()
				}
            })
            
        },
		// 复制分享链接
		copyLink() {
			this.show_share_tip=true
		},
		// 复制分享内容
		showCopywriting() {
			console.log("复制内容");
			const content = `【我正在看】${
				this.siteName
			}土地榜单\n【链接】${this.link}`;
			this.copyText(content, () => {
				uni.showToast({
					title: "复制成功,去发送给好友吧",
					icon: "none",
				});
			});
		},
		copyContent() {
			this.getShortLink(()=>{
				const content = `${this.textContent}\n【链接】${this.link}`;
				this.copyText(content, () => {
					uni.showToast({
						title: "复制成功,去发送给好友吧",
						icon: "none",
					});
				});
			})
			
		},
		// 复制内容
		copyText(cont, callback) {
			let oInput = document.createElement("textarea");
			oInput.value = cont;
			document.body.appendChild(oInput);
			oInput.select(); // 选择对象;
			oInput.setSelectionRange(0, oInput.value.length);
			document.execCommand("Copy"); // 执行浏览器复制命令
			oInput.blur();
			oInput.remove();
			if (callback) callback();
		},
		// 发起聊天
		handleChat() {
			if (!this.is_open_im) {
				if (this.sharers_info.identity == 1) {
					//置业顾问
					this.$navigateTo(
						"/pages/consultant/detail?id=" + this.sharers_info.adviser_id
					);
				} else if (this.sharers_info.identity == 2) {
					this.$navigateTo(
						"/pages/agent/detail?id=" + this.sharers_info.agent_id
					);
				}
				return;
			}
			// #ifndef MP-WEIXIN
			this.checkLogin("当前操作需要绑定手机号，请输入您的手机号", () => {
				getChatInfo(this.sharers_info.mid, 25);
			});
			// #endif
		},
		// 拨打电话
		handleTel() {
			this.tel_params = {
				type: this.sharers_info.identity == 1?'2':'3',
				callee_id: this.sharers_info.mid,
				scene_type:this.sharers_info.identity == 1?'2':'3',
				scene_id:this.sharers_info.mid,
				success: (res)=>{
					this.tel_res = res.data
					this.show_tel_pop = true
				}
			}
			// #ifdef MP-WEIXIN
			allTel(this.tel_params)
			// #endif
			// #ifndef MP-WEIXIN
			this.tel_params.intercept_login = true
			this.tel_params.fail = (res)=>{
				if(res.data.code === -1){
						this.$store.state.user_login_status = 1
						uni.removeStorageSync('token')
						this.$navigateTo('/user/login/login')
					}
					if(res.data.code === 2){
						this.$store.state.user_login_status = 2
						this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
						this.$refs.login_popup.showPopup()
					}
			}
			allTel(this.tel_params)
			// #endif
		},
		retrieveTel(){
				allTel(this.tel_params)
		},
		 // 保存二维码
        saveQrcode(){
            uni.request({
                url:this.qrcode,
                method:'GET',
                responseType: 'arraybuffer',
                success:(res)=>{
                    let base64 = uni.arrayBufferToBase64(res);
                    const userImageBase64 = 'data:image/jpg;base64,' + base64;
                    uni.saveImageToPhotosAlbum({
                        filePath: userImageBase64,
                        success: result => {
                            uni.showToast({
                                title: '保存成功，在微信从相册中选取识别吧',
                                icon: 'none',
                                duration: 4000
                            })
                        },
                        fail: err => {
                            console.log(err)
                            uni.showToast({
                                title: '保存失败，请重试',
                                icon: 'none'
                            })
                        }
                    })
                }
            }); 
        },
		// 检测登录状态
		checkLogin(tip, callback) {
			this.$ajax.get("member/checkUserStatus", {}, (res) => {
				if (res.data.code === 1) {
					callback && callback();
				} else {
					this.$store.state.user_login_status = res.data.status;
					this.login_tip = tip;
					this.$refs.login_popup.showPopup();
				}
			});
		},
		showLoginPopup(tip){
			this.login_tip = tip
			this.$refs.login_popup.showPopup()
		},
		handleCloseLogin() {
            if (this.$store.state.user_login_status === 1) {
                uni.removeStorageSync('token')
                this.$navigateTo('/user/login/login')
            }
            if(this.$store.state.user_login_status===2){
                this.$navigateTo('/user/bind_phone/bind_phone')
            }
        },
        onLoginSuccess(res){
            this.$store.state.user_login_status = 3
            if(this.weituo_is_show){
                console.log("登录成功后继续执行委托接口")
                this.$refs.enturst_box.handleEnturst()
            }
        },
		// 订阅
		showDingyuePop() {
			this.$refs.dingyue.showPopup();
		},
		dingyue() {
			this.$ajax.get(
				"build/subscribeBooking",
				{ type: 2 },
				(res) => {
					if (res.data.code == -1) {
						uni.setStorageSync('backUrl', window.location.href)
						this.$refs.dingyue.hide();
						this.$navigateTo("/user/login/login");
					} else if (res.data.code == 2) {
						this.$refs.dingyue.hide();
						this.$navigateTo("/user/bind_phone/bind_phone");
					} else if (res.data.code == 1) {
						uni.showToast({
							title: res.data.msg,
							icon: "success",
						});
						setTimeout(() => {
							this.$refs.dingyue.hide();
						}, 500);
					} else if (res.data.code == 0) {
						//订阅失败
						uni.showToast({
							title: res.data.msg,
							icon: "none",
						});
						if (res.data.gzhewm) {
							this.qrcode = res.data.gzhewm;
							setTimeout(() => {
								this.$refs.qrcode_popup.show();
							}, 500);
						}
						this.$refs.dingyue.hide();
					}
				},
				(err) => {},
				{ disableAutoHandle: true }
			);
		},
	},
	onShareAppMessage() {
		if (this.share) {
			return {
				title: this.share.title || "",
				content: this.share.content || "",
				imageUrl: this.share.pic ? formatImg(this.share.pic, "w_6401") : "",
			};
		}
	},
	onNavigationBarSearchInputConfirmed(e) {
		this.handleSearch(e.text);
	},
};
</script>

<style scoped lang="scss">
.page {
	background: #fff;
}
.pdb_120 {
	padding-bottom: 120rpx;
}
.header {
	width: 100%;
	height: 400rpx;
	background-image: linear-gradient(0deg, #f7918f 0%, #fb656a 100%);
	display: flex;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	padding: 48rpx 48rpx 0;
	box-sizing: border-box;
	position: relative;
	.head-info {
		width: 100%;
		.update-tip {
			// margin-top: 32upx;
			color: #fff;
			text-align: center;
			align-items: center;
			flex: 1;
			.data-infos {
				align-items: center;
			}
			.line {
				width: 80rpx;
				height: 1rpx;
				background-color: #fff;
			}
			.site-name {
				margin: 0 10rpx;
			}
		}
	}
	.title {
		font-size: 80rpx;
		color: #ffffff;
	}
	.update-tip {
		margin-top: 16upx;
		color: #fff;
	}
}
.yushou-data-info {
	position: relative;
	top: -20px;
	background: #fff;
	border-radius: 48rpx 48rpx 0 0;
}
.fengexian {
	position: relative;
}
.fengexian:after {
	content: "";
	position: absolute;
	top: 20%;
	bottom: 20%;
	right: 0;
	width: 1px;
	-webkit-transform: scaleX(0.5);
	transform: scaleX(0.5);
	background-color: $uni-border-color;
}
.data-box {
	position: absolute;
	background: #ffffff;
	padding: 24rpx 0;
	bottom: -60rpx;
	left: 48rpx;
	right: 48rpx;
	border: 2rpx solid #d8d8d8;
	box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.08);
	border-radius: 8px;
	// transform: translateY(-50%);
	.data-title {
		margin-bottom: 16upx;
		font-size: 22rpx;
		color: #999;
	}
	.data-data {
		font-size: 22rpx;
		// font-weight: bold;
		color: #999;
	}
	.data-datas {
		font-size: 32rpx;
		// font-weight: bold;
		color: #333;
	}
	.red {
		color: $uni-color-primary;
	}
}
.middle-bar {
	padding: 24rpx 48rpx;
	.middle-bar-info {
		background: #f8f8f8;
		border-radius: 44rpx;
	}
	.mid-bar {
		flex: 1;
		justify-content: center;
		align-items: center;
		padding: 20rpx 0;
		&.active {
			background-image: linear-gradient(57deg, #ff5500 27%, #ffa402 80%);
			box-shadow: 0 2px 6px 0 rgba(255, 145, 1, 0.5);
			border-radius: 22px;
			.mid-bar-info {
				color: #fff;
			}
		}
	}
}
.tab-bars {
	padding: 0 48rpx;
	.cate {
		align-items: flex-end;
		max-width: 100%;
	}
	text {
		color: #333;
		transition: 0.2s;
		padding-right: 20rpx;
		&.active {
			font-weight: bold;
			font-size: 40rpx;
		}
		& ~ text {
			padding-left: 20rpx;
		}
	}
}

.lists {
	padding: 0 48rpx;
	overflow: hidden;
	.time-line {
		// padding:0 24rpx ;
		padding-left: 24rpx;
		.time {
			position: relative;
			&:before {
				content: "";
				position: absolute;
				width: 2rpx;
				height: 100%;
				background: #d8d8d8;
				top: 10rpx;
				left: -20rpx;
			}
			.line-title {
				position: relative;
				color: #333;
				font-size: 22rpx;
				margin-bottom: 16rpx;
				&:before {
					content: "";
					position: absolute;
					background-image: linear-gradient(180deg, #f7918f 0%, #fb656a 100%);
					width: 20rpx;
					height: 20rpx;
					top: 50%;
					transform: translateY(-50%);
					left: -30rpx;
					border-radius: 50%;
				}
			}
		}
		.data-card {
			padding-bottom: 24rpx;
			position: relative;
			.jiangbei {
				position: absolute;
				top: 10rpx;
				right: 40rpx;
				height: 50rpx;
				width: 50rpx;
				image {
					width: 100%;
				}
			}
		}
	}
	.copy-text {
		flex-direction: column;
		.text-content {
			background: rgba(251, 101, 106, 0.08);
			border-radius: 8rpx;
			font-size: 28rpx;
			padding: 24rpx;
			color: #333333;
		}
		.copy {
			background-image: linear-gradient(125deg, #ff5500 0%, #ffa402 100%);
			box-shadow: 0 2px 6px 0 rgba(255, 145, 1, 0.5);
			border-radius: 2px;
			padding: 4rpx 8rpx;
			align-items: center;
			margin-left: auto;
			margin-top: 8rpx;
			margin-bottom: 50rpx;
			.copy-btn {
				margin-left: 4rpx;
				font-size: 22rpx;
				color: #ffffff;
			}
		}
	}
	.shujubaobiao {
		background: #ffffff;
		border: 2rpx solid #d8d8d8;
		box-shadow: 0 4rpx 8rpx 0 rgba(0, 0, 0, 0.08);
		border-radius: 16rpx;
		position: relative;
		margin-bottom: 48rpx;
		.zhankai {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			bottom: 0;
		}
		.baobiao-title {
			padding: 24rpx 24rpx 0;
			font-size: 22rpx;
			font-weight: bolder;
			color: #333333;
		}
		.baobiao-box {
			padding: 24rpx 0;
		}
		.baobiao-con {
			padding: 12rpx 24rpx;
			.data-title {
				font-size: 22rpx;
				color: #999;
				text-align: center;
			}
			.data-info {
				justify-content: center;
				align-items: center;
			}
			.data-bijiao {
				font-size: 22rpx;
				align-items: flex-end;
				margin-left: 10rpx;
				.bijiao-text {
					font-size: 22rpx;
					color: #666;
					&.ic_down {
						color: #00caa7;
					}
					&.ic_up {
						color: #fb656a;
					}
				}
			}
			.data-data {
				font-size: 22rpx;
				// font-weight: bold;
				color: #999;
				align-items: center;
				justify-content: center;
				margin-bottom: 16upx;
			}
			.data-datas {
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
			}
			.mtop12 {
				margin-top: 24rpx;
			}
		}
	}
}

.share_icon {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 24rpx;
	width: 66rpx;
	height: 66rpx;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.5);
	position: absolute;
	right: 48rpx;
}

// 分享者信息
.sharers_info {
	position: fixed;
	width: 100%;
	height: 120rpx;
	bottom: 0;
	padding: 0 48rpx;
	box-sizing: border-box;
	align-items: center;
	background-color: #fff;
	z-index: 90;
	.img{
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 16rpx;
		overflow: hidden;
		image{
			width: 100%;
			height: 100%;
		}
    }
	.info {
		overflow: hidden;
		.name {
			margin-bottom: 16rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
		.identity {
			font-size: 24rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			color: #999;
		}
	}
	.btn_box {
		.btn {
			margin-left: 20rpx;
			padding: 10rpx 34rpx;
			font-size: 26rpx;
			color: $uni-color-primary;
			border: 1px solid $uni-color-primary;
			border-radius: 3px;
			box-shadow: 0 2px 4px 0 rgba(251, 101, 106, 0.1);
		}
	}
}
.friend-tips {
	color: #999;
	line-height: 1.5;
	padding: 0 48rpx;
}
.dingyue {
	align-items: center;
	justify-content: flex-start;
	border: 2rpx solid #fff;
	border-radius: 8rpx;
	padding: 4rpx 8rpx;
	width: 100rpx;

	.text {
		margin-left: 10rpx;
	}
}
//公众号二维码弹框
.qrcode-box {
	position: relative;
	margin-top: 15vh;
	.img-box {
		width: 584rpx;
		padding: 12rpx;
		margin: auto;
		background-color: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		.title {
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
			color: #333;
			&.titles {
				margin-top: 36rpx;
			}
		}
		.tip {
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			color: #666;
			&.red {
				padding-bottom: 8rpx;
				color: #f00;
			}
		}
	}
	.qrcode {
		width: 560rpx;
		height: 560rpx;
	}
	.icon-box {
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
}
.friend-tips{
	color: #999;
	line-height: 1.5;
    margin-top: 60rpx;
}
</style>
