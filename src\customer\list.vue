<template>
  <view>  
    <view class="top">
        <view class="top-img"><image :src="topSrc" mode="widthFix"></image></view>
        <view class="data">
          <view class="top-bottom flex-row">
            <view class="bottom-item">
              <view class="bottom-item-top">{{statistics_data.todayCount}}</view>
              <view class="bottom-item-bottom">今日新增</view>
            </view>
            <view class="bottom-item">
              <view class="bottom-item-top">{{statistics_data.weekCount}}</view>
              <view class="bottom-item-bottom">本月新增</view>
            </view>
            <view class="bottom-item">
              <view class="bottom-item-top">{{statistics_data.totalCount}}</view>
              <view class="bottom-item-bottom">累计</view>
            </view>
        </view>
        </view>
    </view>
    <view class="sticky">
      <view class="cate_list flex-box">
         <view class="cate_item" :class="{active: type === 0}" @click="checkType(0)">最新</view>
        <view class="cate_item" :class="{active: type === 1}" @click="checkType(1)">转发</view>
        <view class="cate_item" :class="{active: type === 2}" @click="checkType(2)">平台</view>
        <view class="cate_item" :class="{active: type === 3}" @click="checkType(3)">自建</view>
        <view class="add_btn flex-row" @click="addCustomer">
          <!-- <my-icon type="icon-jiahao" color="#fff"></my-icon> -->
          + 新增</view>
      </view>
      <view class="search_box ">
        <search @input="onInputKey" @confirm="onConfirmKey" :value="keywords" placeholder="请输入手机号或客户名"></search>
      </view>
    </view>
    <view class="customer_list">
      <customerItem v-for="(item, index) in list_data" :key="index" :customer="item"></customerItem>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
  </view>
</template>

<script>
import customerItem from './components/customerItem'
import search from '@/components/search'
import {config} from '@/common/config.js'
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
  name: 'CustomerList',
  components: {customerItem, search, uniLoadMore},
  data () {
    return {
      type: 0,
      get_status:"loading",
      content_text:{
        contentdown:"",
        contentrefresh:"正在加载...",
        contentnomore:"没有更多数据了"
      },
      page: 1,
      keywords:"",
      list_data:[],
      topSrc:config.imgDomain+"/images/background/customer-top-bg.png",
      statistics_data:{
        todayCount:0,
        totalCount:0,
        weekCount:0
      }
    }
  },
  onLoad(options){  
      if (options.type){
        this.type = options.type 
      }
      this.getStatistics()
      this.checkType(this.type) 
      uni.$on('getListAgain',(e)=>{
        this.type=e.type
        this.checkType(this.type)
      })  
  },
  onUnload(){
    uni.$off('getListAgain')
  },
  methods: {
    checkType(type){
      this.type = type
      this.keywords = ""
      this.page = 1
      this.getData()
    },
    onInputKey(e){
      this.keywords = e.detail.value
    },
    getStatistics(){
      this.$ajax.get('im/statisticsCustomer.html',{} , res=>{
        if(res.data.code == 1){
          this.statistics_data = res.data.data
          
        }
      })
    },
    onConfirmKey(e){
      this.keywords = e.detail.value
      this.page = 1
      this.getData()
    },
    getData(){
      if(this.page === 1){
        this.list_data = []
      }
      let params={
          authorize_type: this.type,
          keywords: this.keywords,
          page: this.page,
          rows: 20
        }
      this.get_status = "loading"
      this.$ajax.get('im/customerList.html',params , res=>{
        if(res.data.code == 1&&res.data.list){
          this.list_data = this.list_data.concat(res.data.list)
          this.get_status = "more"
          if(res.data.list.length<20){
            this.get_status = "noMore"
          }
        }else{
          this.get_status = "noMore"
        }
      })
    },
    addCustomer(){
      this.$navigateTo('/customer/add')
    },
  },
  onReachBottom(){
    if(this.get_status!=='more') return
    this.page++
    this.getData()
  },
}
</script>

<style scoped lang="scss">
.top {
  position: relative;
  background: #ffffff;
  .top-img{
    padding-bottom: 80rpx;
    image{
      width: 100%;
    }
  }
  .top-bottom{
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    left: 48rpx;
    right: 48rpx;
    bottom: 0;
    background: #fff;
    box-shadow: 0 -6rpx 8rpx 0 rgba(0,0,0,0.08);
    border-radius: 16rpx 16rpx 0 0;
    .bottom-item{
      padding: 48rpx 0;
      flex: 1;
      text-align: center;
      .bottom-item-top{
        font-size: 40rpx;
        color: #333333;
        font-weight: bold;
      }
      .bottom-item-bottom{
        font-size: 22rpx;
        color: #666666;
      }
    }
  }
}
.cate_list{
  background-color: #fff;
  justify-content: space-between;
  align-items: center;
  padding: 0 48rpx;
  padding-top: 20rpx;
  .cate_item{
    padding: 12rpx 24rpx;
    // margin: 0 24rpx;
    background: #F5F5F5;
    position: relative;
    color: #999;
    border-radius: 10rpx;
    transition: 0.26s;
    &.active{
      // color: #333;
      color: #FB7277 ;
      background: #FEE0E1;
      // border: 1px solid #FB7277;
    }
    // &.active::after{
    //   content: "";
    //   position: absolute;
    //   width: 32%;
    //   bottom: 0;
    //   left: 0;
    //   right: 0;
    //   margin: auto;
    //   height: 8rpx;
    //   border-radius: 4rpx;
    //   background-color: $uni-color-primary;
    // }
  }
  .add_btn{
    margin: 0 24rpx;
    height: 48rpx;
    line-height: 48rpx;
    text-align: center;
    padding: 0 16rpx;
    border-radius: 8rpx;
    // background-color: $uni-color-primary;
    color: #fff;
    background-image: linear-gradient(125deg, #FF5500 0%, #FFA402 100%);
    box-shadow: 0 2px 6px 0 rgba(255,145,1,0.50);
    border-radius: 4px;
  }
}

.customer_list{
  padding: 24rpx 48rpx;
  background-color: #f8f8f8;
}

.search_box{
  padding-top: 12rpx;
  background-color: #fff;
}

.sticky{
  background-color: #fff;
  position: sticky;
  // #ifdef H5
  top: 44px;
  // #endif
  // #ifndef H5
  top: 0;
  // #endif
  z-index: 9;
}
</style>