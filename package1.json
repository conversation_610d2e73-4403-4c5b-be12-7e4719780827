{"name": "tfw_new", "version": "4.0.5", "private": true, "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "deploy:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build && node ./deploy", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js"}, "dependencies": {"@dcloudio/uni-app-plus": "^2.0.0-28820200820001", "@dcloudio/uni-h5": "^2.0.0-28820200820001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-mp-alipay": "^2.0.0-28820200820001", "@dcloudio/uni-mp-baidu": "^2.0.0-28820200820001", "@dcloudio/uni-mp-qq": "^2.0.0-28820200820001", "@dcloudio/uni-mp-toutiao": "^2.0.0-28820200820001", "@dcloudio/uni-mp-weixin": "^2.0.0-28820200820001", "@dcloudio/uni-stat": "^2.0.0-28820200820001", "flyio": "^0.6.2", "js-audio-recorder": "^1.0.6", "regenerator-runtime": "^0.12.1", "vconsole": "^3.3.4", "vue": "^2.6.12", "vuex": "^3.5.1", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^2.0.0-28820200820001", "@dcloudio/uni-migration": "^2.0.0-28820200820001", "@dcloudio/uni-template-compiler": "^2.0.0-28820200820001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0-28820200820001", "@dcloudio/vue-cli-plugin-uni": "^2.0.0-28820200820001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0-28820200820001", "@dcloudio/webpack-uni-mp-loader": "^2.0.0-28820200820001", "@dcloudio/webpack-uni-pages-loader": "^2.0.0-28820200820001", "@types/html5plus": "^1.0.1", "@types/uni-app": "^1.4.3", "@vue/cli-plugin-babel": "3.5.1", "@vue/cli-service": "^4.5.4", "ali-oss": "^6.10.0", "babel-plugin-import": "^1.11.0", "eslint": "^6.8.0", "eslint-plugin-vue": "^6.2.2", "mini-types": "^0.1.4", "miniprogram-api-typings": "^2.12.0", "node-sass": "^4.14.1", "postcss-comment": "^2.0.0", "sass-loader": "^8.0.2", "ssh2-sftp-client": "^5.2.2", "vue-template-compiler": "^2.6.12"}, "overrideBrowserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}}