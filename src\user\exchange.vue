<template>
	<view id="recharge">
    <!-- 顶部卡片 -->
    <view class="header">
      <image class="bg_img" :src="'/images/new_icon/<EMAIL>' | imageFilter('m_320')" mode="aspectFill"></image>
      <view class="info">
        <view class="title">金币</view>
        <view class="integral">{{jinbi}}</view>
      </view>
    </view>
    <view class="exchange-box">
      <view class="inp-box flex-row bottom-line">
        <input type="number" @input="handelInput" placeholder="请输入需要消耗的金币" placeholder-style="font-size:26rpx">
        <view class="computed">
          <text>可以兑换</text>
          <text>{{amount*exchange}}</text>
          <text>个积分</text>
        </view>
      </view>
      <view class="tip">
        注：1个金币可以兑换{{exchange}}个积分
      </view>
      <!-- 兑换按钮 -->
      <view class="button-box">
        <view class="button" @click="subOrder()">确定兑换</view>
      </view>
    </view>
	</view>
</template>

<script>
	import {uniListItem} from "@dcloudio/uni-ui"
	export default {
		data() {
			return {
				jinbi:0,
				amount:0,
				exchange:1
			};
		},
		components:{
			uniListItem
		},
		onLoad(options){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			this.jinbi = this.$store.state.user_info.money_own
			if(options.jinbi){
				this.jinbi = options.jinbi
			}
			this.getData()
		},
		methods:{
			getData(){
				this.$ajax.get('member/integralExchange.html',{},res=>{
					if(res.data.code == 1){
						this.jinbi = res.data.money_own
						this.exchange = res.data.exchange
					}
				})
			},
			handelInput(e){
				this.amount = parseFloat(e.detail.value)||0
			},
			subOrder(){
				if(!this.amount){
					uni.showToast({
						title:"请输入需要使用的金币数量",
						icon:"none"
					})
					return
				}
				if(this.amount<1){
					uni.showToast({
						title:"一次至少使用个金币",
						icon:"none"
					})
					return
				}
				if(this.amount%1){
					uni.showToast({
						title:"兑换数量必须为整数",
						icon:"none"
					})
					return
				}
				this.$ajax.post('member/integralExchange.html',{exchange:this.amount},(res)=>{
					if(res.data.code == 1){
						setTimeout(()=>{
							uni.navigateBack()
						},1500)
					}
					uni.showToast({
						title:res.data.msg,
						icon:res.data.code == 1?'success':'none'
					})
				})
			}
		}
	}
</script>

<style lang="scss">
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row{
  flex-direction: row;
}

#recharge{
  background-color: #fff;
}
.header {
  margin: 0 48rpx;
  margin-top: 24rpx;
  height: 278rpx;
  position: relative;
  border-radius: 30rpx;
  overflow: hidden;
  .bg_img {
    width: 100%;
    height: 100%;
    background-image: linear-gradient(180deg, #ff676c 0%, #fe8e8d 100%);
  }
  .info {
    position: absolute;
    left: 48rpx;
    top: 64rpx;
    .title {
      line-height: 1;
      margin-bottom: 10rpx;
      font-size: 26rpx;
      color: #fff;
    }
    .integral{
      font-size: 64rpx;
      color: #fff;
    }
  }
}

.exchange-box{
  padding: 24rpx 48rpx;
}

.inp-box{
  padding: 20rpx 0;
  justify-content: space-between;
  input{
    padding: 0 20rpx;
  }
  .computed{
    display: block;
  }
}
.tip{
  margin-top: 24rpx;
  font-size: 22rpx;
  color: #999999;
}

.button-box {
  margin-top: 48rpx;
  padding: 24rpx 48rpx;
  padding-bottom: 32rpx;
  background-color: #fff;
  .button {
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    background: #fb656a;
    box-shadow: 0 8rpx 32rpx 0 rgba(251, 101, 106, 0.4);
    border-radius: 44rpx;
    border-radius: 44rpx;
  }
}
</style>
