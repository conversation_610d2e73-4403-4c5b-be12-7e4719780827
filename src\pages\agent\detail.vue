<template>
  <view class="agent-container" v-show ="showContent">
    <!-- <view class="card-box" id="card-box">
      <view class="bg"></view>
      <view
        class="adviser-card"
        :class="{
          jin: detail.levelid === 1,
          yin: detail.levelid === 2,
          tong: detail.levelid === 3
        }"
      >
        <view class="adviser-info flex-row">
          <view class="info">
            <view class="name-row flex-row">
              <text class="name">{{ detail.cname }}</text>
            </view>
            <view class="name-row flex-row">
              <text class="level_name level_names">入驻{{siteName}}: {{ detail.entry_time }}</text>
            </view>
            <view class="builds" @click="makePhone(detail.tel)">
              <my-icon type="dianhua" color="#fb656a" size="32  rpx"></my-icon>
              <text class="text">{{ detail.tel }}</text>
            </view>
          </view>
          <view class="header-img-box">
            <image class="header-img" :src="detail.img | imageFilter('w_240')" mode="widthFix"></image>
          </view>
        </view>
        <view class="adviser-data">
          <view class="autograph" v-if="detail.tname" @click="goDetail(detail.store_id)">
            <text class="label">门店：</text>
            <text>{{detail.tname}}</text>
            <view class="icon" v-if ="detail.store_id"><my-icon type="ic_into" color="#999"  size="30rpx"></my-icon></view>
          </view>
          <view class="autograph" v-if="detail.address">
            <text class="label">地址：</text>
            <text>{{detail.address}}</text>
          </view>
          
        </view>
      </view>
    </view> -->
      <view class="back  flex-row">
      <view class="icon-box flex-row"  @click="goBack">
          <my-icon type="ic_back" color="#fff" size="48rpx"></my-icon>
      </view>
      <text class ="title-con" >{{detail.cname}}</text>
    </view>
    <view class="top-card">

      <view class="bg"></view>
      <view class="card-box flex-row">
        <view class="left">
          <view class="shop-name flex-row" @click="goDetail(detail.store_id)">
            <text class="shop-name-con">{{detail.tname}}</text>
            <view class="icon" v-if ="detail.store_id"><my-icon type="ic_into" color="#999"  size="30rpx"></my-icon></view>
          </view>
          <view class="agent-name left-con"><text>{{detail.cname }}</text></view>
          <view class="tel flex-row" v-if="detail.commission_proportion">
            <view class="">佣金比例：</view>
            <text class="">{{detail.commission_proportion}}</text>
          </view>
          <view class="tel flex-row" v-if="detail.cooperate_commission">
            <view class="">合作分佣比例：</view>
            <text  class="">{{detail.cooperate_commission}}</text>
          </view>
          <view class="tel flex-row">
            <view class="tel-icon">
              <image  :src="'/images/icon/agent/<EMAIL>'| imageFilter('m_320')" mode="widthFix"></image>
            </view>
            <view class="tel-con"><text>{{detail.tel}}</text></view>
          </view>
          <view class="address tel flex-row">
            <view class="tel-icon">
              <image  :src="'/images/icon/agent/<EMAIL>'|imageFilter('m_320')" mode="widthFix"></image>
              
            </view>
            <view class="tel-con"><text>{{detail.address}}</text></view>
          </view>
        </view>
        <view class="middle">
          
        </view>
        <view class="right">
          <image mode="aspectFill" :src="detail.img | imageFilter('w_6401')"></image>
        </view>
      </view>
    </view>
    <view class="options flex-row">
      <!-- #ifndef MP -->
      <view class="item" @click="$refs.share_popup.show()">
        <view class="icon_box share">
          <image  :src="'/images/icon/agent/<EMAIL>'|imageFilter('m_320')" mode="widthFix"></image>
        </view>
        <text>立即转发</text>
      </view>
      <!-- #endif -->
      <!-- #ifdef MP -->
      <button class="item" open-type="share">
        <view class="icon_box share">
          <image  :src="'/images/icon/agent/<EMAIL>'|imageFilter('m_320')" mode="widthFix"></image>
          <!-- <my-icon type="zhaunfa" color="#fff" size="56rpx"></my-icon> -->
        </view>
        <text>立即转发</text>
      </button>
      <!-- #endif -->
      <view class="item" @click="showWeituo">
        <view class="icon_box zan">
          <image  :src="'/images/icon/agent/<EMAIL>'|imageFilter('m_320')" mode="widthFix"></image>
          <!-- <my-icon type="dianzan" color="#fff" size="56rpx"></my-icon> -->
        </view>
        <text>在线委托</text>
      </view>
      <view class="item" @click="handleCreat()">
        <view class="icon_box post">
            <image  :src="'/images/icon/agent/<EMAIL>'|imageFilter('m_320')" mode="widthFix"></image>
          <!-- <my-icon type="erweima1x" color="#fff" size="56rpx"></my-icon> -->
        </view>
        <text>生成海报</text>
      </view>
    </view>
    <!-- 服务小区 -->
    <view class="estate-box" v-if="estateList.length>0">
      <view class="label">服务小区</view>
      <!-- @click="goEstate(item.id)" 链接去掉 -->
      <scroll-view scroll-x style="width: 100%" class="estate-list">
        <view class="estate-item" v-for="item in estateList"
          :key="item.id" >
            <view class="estate-name">{{item.name}}</view>
        </view>
      </scroll-view>
    </view>
    <!-- 精选房源 -->
    <view class="builds-box" v-if="jingxuanHouseList.length>0">
      <view class="label">精选房源</view>
      <view class="builds-list">
        <scroll-view scroll-x style="width: 100%" class="build-list">
          <view
            class="build-item"
            v-for="item in jingxuanHouseList"
            :key="item.id"
            @click="goJingxuanDetail(item)"
          >
            <image class="build_img" :src="item.img | imageFilter('w_400')" mode="aspectFill"></image>
            <view class="build">
              <view class="build-name"><text>{{item.title}}</text></view>
              <view class="mid flex-row ">
                <view class ="mid-shitingwei flex-row"><text v-if ="item.shi">{{item.shi}}室</text><text v-if ="item.ting">{{item.ting}}厅</text><text v-if ="item.wei">{{item.wei}}卫</text></view>
                <text v-if ="item.mianji">{{item.mianji}}m²</text>
              </view>
              <view class="price flex-row" v-if ="item.fangjia"><text class="price-con">{{item.fangjia}}</text>{{(item.parentid==1||item.parentid==4)?"万元":"元/月"}}</view>
            </view>
            <view class="jingxuan-label">
              精选
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    <!-- 搜索 -->
    <view class="house-box">
      <view class="label">TA的房源</view>
      <view class="search-box">
        <my-search mode="small_round" :placeholder="'搜索房源'" v-model="params.keyword" @confirm="handleSearch"></my-search>
      </view>
    </view>
    <!-- 房源列表 -->
    <view class="house-list">
      <view class="house-cate flex-row">
        <!-- <view class="cate-item" :class="{ active: params.cate_id === 1 }" @click="getErshou">二手房</view>
        <view class="cate-item" :class="{ active: params.cate_id === 2 }" @click="getRenting">租房</view> -->
        <tab-bar :tabs="tabs" ref="tab" :fixedTop="false" :nowIndex="tab_index" @click="getList" :showLine='false'>
		    </tab-bar>
        <view class="house_count flex-row">
          <text class="label">房源：</text>
          <text>{{detail.count}}</text>
        </view>
      </view>
      <block v-if="params.cate_id == 1">
        <block v-for="(item, index) in listData" :key="index">
          <house-item :item-data="item" type="ershou" @click="toDetail"></house-item>
        </block>
      </block>
      <block v-if="params.cate_id == 2">
        <block v-for="(item, index) in listData" :key="index">
          <house-item :item-data="item" type="renting" @click="toDetail"></house-item>
        </block>
      </block>
      <block v-if="params.parentid">
        <block v-for="item in listData" :key="item.id">
          <list-item v-if="item.parentid == 1" :item-data="item" type="sale" :showBottom="false" @click="toEstate"></list-item>
          <list-item v-if="item.parentid == 2" :item-data="item" type="rent" :showBottom="false" @click="toEstate"></list-item>
          <list-item v-if="item.parentid == 3" :item-data="item" type="transfer" :showBottom="false" @click="toEstate"></list-item>
          <!-- <house-item :item-data="item" type="renting" @click="toDetail"></house-item> -->
        </block>
      </block>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <my-popup ref="wechat" position="center" height="95vw">
      <view class="qrcode-box">
        <image class="wechat-img" v-if="detail.wechat_img" :src="detail.wechat_img | imageFilter('w_6401')" mode="widthFix"></image>
        <view class="no-wechat" v-else>暂未上传，请点击咨询或拨打电话</view>
        <view class="btn-box">
          <!-- #ifdef H5 -->
          <view class="btn btn-lg">长按保存到相册</view>
          <!-- #endif -->
          <!-- #ifndef H5 -->
          <view class="btn btn-lg" @click="downImg(detail.wechat_img)">保存到相册</view>
          <!-- #endif -->
        </view>
      </view>
    </my-popup>
    <sub-form :sub_type="7" sub_title="委托" ref='sub_form' @onsubmit="handleSubForm"></sub-form>
    <chat-tip></chat-tip>
    <!-- 底部操作菜单 -->
    <view class="bottom-bar flex-row">
      <view class="bar-left flex-row flex-1">
        <view class="icon-btn" @click="showSharePop">
          <my-icon type="ic_fenxiang" size="50rpx"></my-icon>
          <text>分享</text>
        </view>
      </view>
      <view class="bar-left flex-row flex-1">
        <view class="icon-btn" @click="showWechat">
          <my-icon type="weiixn" size="50rpx"></my-icon>
          <text>微信</text>
        </view>
      </view>
      <!-- <view class="bar-left flex-row flex-1">
        <view class="icon-btn" @click="showSubform">
          <my-icon type="weituo" size="50rpx"></my-icon>
          <text>委托</text>
        </view>
      </view> -->
      <view class="bar-right flex-row flex-3">
        <view class="bar-btn btn1 flex-1" v-if="is_open_im" @click="handleChat()">在线咨询</view>
        <view class="bar-btn btn2 flex-1" :class="{alone: !is_open_im}" @click="handleTel()">电话咨询</view>
      </view>
    </view>
    <sharePop ref="share_popup" @handleCreat="handleCreat()" @copyLink="copyLink()" @showCopywriting="showCopywriting()"></sharePop>
    <!-- 复制分享文案 -->
    <my-popup ref="text_popup" position="center" :height="text_popup_height">
      <view class="copy-text-box" id="copy-text">
        <view class="title">{{detail.title}}</view>
        <view class="info-box">
          <view class="info-row flex-row">
            <text class="label">姓名：</text>
            <text class="value">{{detail.cname}}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">门店：</text>
            <text class="value">{{detail.tname}}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">地址：</text>
            <text class="value">{{(detail.address||'')}}</text>
          </view>
          <view class="button disabled-btn flex-row" v-if="copy_success">
            <my-icon type="check-circle" size="30rpx" color="#999"></my-icon>
            <text class="text">文本已复制</text>
          </view>
          <view class="button" v-else @click="copywriting">复制文本</view>
        </view>
      </view>
    </my-popup>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <enturstBtn :to_user="detail" @click="$refs.enturst_popup.show()" />
    <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
      <enturstBox @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" isDetail=1  @popup_login="showLoginPopup('为方便您及时接收消息通知，请输入手机号码')" :to_user="detail" />
    </my-popup>
    <!-- 登录弹窗 -->
        <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
        <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import myIcon from '../../components/myIcon.vue'
import mySearch from '../../components/mySearch.vue'
import houseItem from '../../components/houseItem.vue'
import listItem from '../../commercial/components/listItem.vue'
import myPopup from '../../components/myPopup.vue'
import { uniLoadMore } from '@dcloudio/uni-ui'
import { formatImg, getSceneParams} from '../../common/index.js'
import { config} from '../../common/config.js'
import getChatInfo from '../../common/get_chat_info'
import allTel from '../../common/all_tel.js'
import subForm from "../../components/subForm.vue";
import shareTip from '../../components/shareTip.vue'
import sharePop from "../../components/sharePop.vue";
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
import loginPopup from '../../components/loginPopup'
import tabBar from '@/components/tabBar'
export default {
  data() {
    return {
      level_icons: [
        {
          color: '#fecc69',
          icon: '/images/new_icon/<EMAIL>'
        },
        {
          color: '#b6d1f8',
          icon: '/images/new_icon/<EMAIL>'
        },
        {
          color: '#e3af79',
          icon: '/images/new_icon/<EMAIL>'
        }
      ],
      id: '',
      browse_user_list: [],
      get_status: 'loading',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      },
      params: {
        page: 1,
        rows: 10,
        cate_id: 1,
        keyword: '',
        sid:'',
        sharetype:""
      },
      showContent:false,
      detail: {},
      listData: [],
      jingxuanHouseList:[],
      siteName:'',
      show_share_pop:false,
      copy_success:false,
      text_popup_height:'',
      show_share_tip:false,
      link:'',
      currentUserInfo:{},
      toLogin:true,
      sid:'',
      login_tip:'',
      shareType:"",
      opacity:0,
      tel_res: {},
      show_tel_pop:false,
      tabs: [
        { name: '二手房', cate_id: 1 },
        { name: '租房', cate_id: 2 },
        { name: '商业出售', parentid: 1 },
        { name: '商业出租', parentid: 2 },
        { name: '生意转让', parentid: 3 },
      ],
      tab_index: 0,
      type: '',
      estateList:[],//服务小区
    }
  },
  // filters:{
  //   imgUrlFormat(val){
  //     return config.imgDomain+val
  //   }
  // },
  computed: {
    is_open_im() {
      //是否开通了聊天共功能
      return this.$store.state.im.ischat
    },
    is_open_middle_num() {
      //是否开通了中间号功能
      return this.$store.state.im.istelcall
    },
    login_status() {
      //当前用户的登录状态
      return this.$store.state.user_login_status
    },
    level_icon() {
      //级别的图标
      if (!isNaN(this.detail.levelid - 1) && this.detail.levelid - 1 >= 0) {
        return this.level_icons[this.detail.levelid - 1].icon
      } else {
        return '/images/new_icon/<EMAIL>'
      }
    },
    level_color() {
      //级别的颜色
      if (this.is_zan && !isNaN(this.detail.levelid - 1) && this.detail.levelid - 1 >= 0) {
        return this.level_icons[this.detail.levelid - 1].color
      } else {
        return '#dedede'
      }
    },
    hasWechat() {
      return this.$store.state.hasWechat
    }
  },
  components: {
    myIcon,
    mySearch,
    houseItem,
    listItem,
    myPopup,
    subForm,
    sharePop,
    shareTip,
    tabBar,
    uniLoadMore, 
    loginPopup,
    enturstBtn,
    enturstBox
  },
  // #ifndef H5
  onShow() {
    if (this.reload) {
      this.reload = false
      this.getData()
    }
  },
    // #endif
  onLoad(options) {
    uni.$on("getDataAgain",()=>{
      this.params.page =1
      if (this.params.parentid) {
         this.getEstate()
      }
      if(this.params.cate_id){
        this.getData()
      }
    })
    // #ifdef MP
    if (options.scene) {
      const params = getSceneParams(decodeURIComponent(options.scene))
      if (params.id) {
        this.params.id = params.id
        this.getDetail(params.id)
        this.getData()
      }
      return
    }
    // #endif
    if (JSON.stringify(this.$store.state.tempData) != '{}') {
      this.detail = this.$store.state.tempData
      let title = (this.detail.tname&&this.detail.cname)?this.detail.tname+'-'+this.detail.cname:(this.detail.tname||this.detail.cname)
      this.share = {
        title:title,
        content:this.detail.address,
        pic:this.detail.img
      }
    }
    if(options.cate_id) this.params.cate_id = parseInt(options.cate_id)
    if(options.parentid) this.params.parentid = parseInt(options.parentid)
    if(options.shareId){
      this.sid=options.shareId
      this.shareType=options.shareType
      this.share_time =options.f_time||''
    }
    if (options.id) {
      this.params.id = options.id
      this.getDetail(options.id)
      if (options.parentid) {
        this.params.cate_id = null
        this.tab_index = this.tabs.findIndex(f=>f.parentid === parseInt(this.params.parentid ))
        this.getEstate()
      } else {
        this.tab_index = this.tabs.findIndex(f=>f.cate_id === parseInt(this.params.cate_id ))
        this.getData()
      }
    }
  },
  onUnload() {
    this.$store.state.tempData = {}
    uni.$off("getDataAgain")
  },

  methods: {
    getData() {
      if (this.params.page == 1) {
        this.listData = []
      }
      let { uid, parentid, ...params} = this.params
      this.get_status = 'loading'
      this.$ajax.get('member/agentDetail.html', params, res => {
        if (res.data.code == 1) {
          // #ifdef APP-PLUS
          let tname = this.detail.tname ? this.detail.tname + '-' : ''
          let cname = this.detail.cname || ''
          this.share = {
            title:`我是${this.detail.tname}${this.detail.cname}，今天房源已经更新，马上点击查看，欢迎咨询！`,
            content: `帮您找房、委托卖房，竭诚为您服务。联系电话：${this.detail.tel}`,
            pic: this.detail.img
          }
          // #endif
          this.listData = this.listData.concat(res.data.house)
          if(!res.data.house){
            this.get_status = "noMore"
          }else{
            this.get_status = "more"
          }
        } else {
          this.get_status = 'noMore'
        }
        if (res.data.visitor_id) {
          this.visitor_id = res.data.visitor_id
        }
      })
    },
    goEstate(id){
      this.$navigateTo("/pages/house_price/detail?id="+id)
    },
    getEstate() {
      if (this.params.page == 1) {
        this.listData = []
      }
      this.params.uid = this.params.id
      let { id, sid, sharetype, cate_id, ...params} = this.params
      this.get_status = 'loading'
      this.$ajax.get('agent/estateList', params, res => {
        if (res.data.code == 1) {
           // #ifdef H5 || MP-BAIDU
          if (res.data.seo) {
            let seo = res.data.seo
            if (this.detail.img) {
              seo.image = formatImg(this.detail.img, 'w_8001')
            }
            this.seo = seo
          }
          // #endif
          // #ifdef APP-PLUS
          let tname = this.detail.tname ? this.detail.tname + '-' : ''
          let cname = this.detail.cname || ''
          this.share = {
            title:`我是${this.detail.tname}${this.detail.cname}，今天房源已经更新，马上点击查看，欢迎咨询！`,
            content: `帮您找房、委托卖房，竭诚为您服务。联系电话：${this.detail.tel}`,
            pic: this.detail.img
          }
          // #endif
          this.listData = this.listData.concat(res.data.list)
          if(!res.data.list){
            this.get_status = "noMore"
          }else{
            this.get_status = "more"
          }
        } else {
          this.get_status = 'noMore'
        }
        if (res.data.visitor_id) {
          this.visitor_id = res.data.visitor_id
        }
      })
    },
    getJingxuan(uid) {
      this.$ajax.get('agent/recHouses', {uid}, res => {
        if (res.data.code == 1) {
          this.jingxuanHouseList = res.data.lists
        }
      })
    },
    goBack(){
      this.$navigateBack()
    },
    showWeituo(){
      this.$refs.enturst_popup.show()
    },
    getDetail(id) {
      this.$ajax.get('member/agentInfo.html', { id,sid:this.sid,sharetype:this.shareType, forward_time:this.share_time ||''}, res => {
        if (res.data.code == 1) {
          if (res.data.agent.communities) {
            this.estateList = res.data.agent.communities
          }
          this.detail = res.data.agent
          this.detail.agent_id = res.data.agent.id
          this.detail.prelogo = res.data.agent.img
          this.mid = res.data.mid
          this.siteName=res.data.siteName
          this.getJingxuan(this.detail.agent_id)
          let title = `我是${this.detail.tname}${this.detail.cname}，今天房源已经更新，马上点击查看，欢迎咨询！`
          this.currentUserInfo=res.data.shareUser
          if (this.currentUserInfo.agent_id>0){
            // this.sid=this.currentUserInfo.agent_id
            // this.shareType=2
            this.currentUserInfo.shareType=2
            this.currentUserInfo.sid=this.currentUserInfo.agent_id
          }else if (this.currentUserInfo.adviser_id>0){
            // this.sid=this.currentUserInfo.adviser_id
            // this.shareType=1
            this.currentUserInfo.shareType=1
            this.currentUserInfo.sid=this.currentUserInfo.adviser_id
          }else {
            this.currentUserInfo={
              sid:this.sid,
              shareType:this.shareType
            }
          }
          let time =parseInt(+new Date()/1000)
          this.share = {
            title:title,
            content:`帮您找房、委托卖房，竭诚为您服务。联系电话：${this.detail.tel}`,
            pic:this.detail.img,
            link:window.location.origin+'/h5/pages/agent/detail?id='+this.params.id+'&shareId='+this.currentUserInfo.sid+'&shareType='+this.currentUserInfo.shareType+"&f_time="+time
          }
          // this.share.link=window.location.origin+'/pages/agent/detail?id='+this.params.id+'&shareId='+this.params.sid+'&shareType='+this.params.sharetype
          this.getWxConfig()

          if (this.sid&&this.shareType){
              // 获取登陆状态
              this.$ajax.get('member/checkUserStatus', {}, res => {
                  if (res.data.code === 1) {
                  } else {
                    if (this.toLogin==false) return 
                    this.toLogin=false
                    this.$store.state.user_login_status = res.data.status
                    if(this.$store.state.user_login_status==1){
                        uni.setStorageSync('backUrl', window.location.href)
                        this.$navigateTo("/user/login/login")
                    }
                  }
                  })
          }
          this.showContent=true;
        }
        
      })
    },
    getErshou() {
      this.params.page = 1
      this.params.cate_id = 1
      this.getData()
    },
    getRenting() {
      this.params.page = 1
      this.params.cate_id = 2
      this.getData()
    },
    getList(item) {
      this.params.page = 1
      // this.params.keyword = ''
      if (item.cate_id) {
        this.type = ''
        this.params.parentid = null
        this.params.cate_id = item.cate_id
        this.tab_index = this.tabs.findIndex(f=>f.cate_id === parseInt(this.params.cate_id ))
        this.getData()
      }
      if (item.parentid) {
        this.type = 'estate'
        this.params.cate_id = null
        this.params.parentid = item.parentid
        this.tab_index = this.tabs.findIndex(f=>f.parentid === parseInt(this.params.parentid ))
        this.getEstate()
      }
    },
    toDetail(e) {
      if (!e.detail.id) {
        return
      }
      this.$store.state.tempData = e.detail
      if (this.params.cate_id == 1) {
        this.$navigateTo('/pages/ershou/detail?id=' + e.detail.id)
      } else if (this.params.cate_id == 2) {
        this.$navigateTo('/pages/renting/detail?id=' + e.detail.id)
      }
    },
    toEstate(e) {
      if (!e.detail.id) {
        return
      }
      this.$store.state.tempData = e.detail
      if (this.params.parentid == 1) {
        this.$navigateTo('/commercial/sale/detail?id=' + e.detail.id)
      } else if (this.params.parentid == 2) {
        this.$navigateTo('/commercial/rent/detail?id=' + e.detail.id)
      } else if (this.params.parentid == 3) {
        this.$navigateTo('/commercial/transfer/detail?id=' + e.detail.id)
      }
    },
    handleSearch(e) {
      this.params.page = 1
      if (this.params.parentid) {
        this.getEstate()
      } else {
        this.getData()
      }
    },
    toHome() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    },
    showLoginPopup(tip){
        this.login_tip = tip
        this.$refs.login_popup.showPopup()
    },
    handleCloseLogin() {
        if (this.$store.state.user_login_status === 1) {
            uni.removeStorageSync('token')
            this.$navigateTo('/user/login/login')
        }
        if(this.$store.state.user_login_status===2){
            this.$navigateTo('/user/bind_phone/bind_phone')
        }
    },
    onLoginSuccess(res){
        this.$store.state.user_login_status = 3
        if(this.weituo_is_show){
            console.log("登录成功后继续执行委托接口")
            this.$refs.enturst_box.handleEnturst()
        }
    },
    showSubform() {
			this.$refs.sub_form.showPopup();
    },
    goDetail(id){
      if (id == 0 ) return 
      this.$navigateTo(`/shops/detail?id=${id}`)
    },
    goJingxuanDetail(build){
      switch (build.parentid) {
        case 1:  //二手房
          this.$navigateTo(`/pages/ershou/detail?id=${build.id}`)
          break;
         case 2:  //房屋出租
          this.$navigateTo(`/pages/renting/detail?id=${build.id}`)
          break;
        case 3:  //求组
          this.$navigateTo(`/needPage/rest_house/detail?id=${build.id}`)
          break;
        case 4:  //求购
          this.$navigateTo(`/needPage/buy_house/detail?id=${build.id}`)
          break;
      
        default:
          break;
      }
    },
    handleSubForm(options){
      let {name,selectIndex,tel,desc}=options
      // this.form.cate_id = this.chil_type
      this.$ajax.post('house/entrustHouse.html',{cate_id:selectIndex,name,tel,des:desc,agent_id:this.params.id }, res=>{
        uni.hideLoading()
        if(res.data.code === 1){
          uni.showToast({
            title: res.data.msg
          })
          this.$refs.sub_form.hide()
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    // #ifdef H5
    copyLink(){
      this.show_share_tip = true
    },
    // #endif
    showCopywriting(){
      const query = uni.createSelectorQuery().in(this)
      query.select('#copy-text').fields({rect:true,scrollOffset:true,size:true},data => {
        this.text_popup_height = data.height+'px'
      }).exec();
      this.copy_success = false
      this.$refs.text_popup.show()
      this.$refs.share_popup.hide()
    },
    copywriting(){
      const content = `${this.detail.cname}
【门店】${this.detail.tname}
【地址】${this.detail.address}
【链接】${this.link}`
        this.copyContent(content,()=>{
          this.copy_success = true
        })
    },
    getShortLink(){
      let time =parseInt(+new Date()/1000)
      this.link = window.location.origin+"/h5/pages/agent/detail?id="+this.params.id+"&shareId="+this.currentUserInfo.sid+"&shareType="+this.currentUserInfo.shareType+"&f_time="+time
      this.$ajax.get('build/shortUrl.html', {page_url: this.link}, res=>{
        if(res.data.code === 1){
          this.link = res.data.short_url
        }
      })
    },
    showSharePop(){
      this.getShortLink()
      this.$refs.share_popup.show()
    },
    // 复制内容
    copyContent(content, callback){
      // #ifndef H5
      uni.setClipboardData({
        data: content,
        success: res => {
          if(callback) callback()
        }
      })
      // #endif
      // #ifdef H5
      let oInput = document.createElement('textarea')
      oInput.value = content
      document.body.appendChild(oInput)
      oInput.style.opacity = 0
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      oInput.blur()
      oInput.remove()
      if(callback) callback()
      // #endif
    },
    handleCreat() {
      this.$navigateTo(`${location.origin}/wapi/poster/branch?type=5&id=${this.params.id}&header_from=2`)
    },
    downImg(img) {
      uni.getImageInfo({
        src: img,
        success: res => {
          this.saveCard(res.path)
        },
        fail: err => {
          uni.showToast({
            title: '图片下载失败,请检查下载域名白名单',
            icon: 'none',
            duration: 2000
          })
        }
      })
    },
    saveCard(img) {
      // #ifdef H5
      uni.showToast({
        title: '请长按海报保存到手机',
        icon: 'none'
      })
      // #endif
      // #ifndef H5
      uni.saveImageToPhotosAlbum({
        filePath: img,
        success: result => {
          uni.showToast({
            title: '保存成功，从相册中分享给好友吧',
            icon: 'none',
            duration: 4000
          })
        },
        fail: err => {
          console.log(err)
          uni.showToast({
            title: '保存失败，请重试',
            icon: 'none'
          })
        }
      })
      // #endif
    },
    toPosition() {
      if (this.detail.lat > 0 && this.detail.lng > 0) {
        uni.openLocation({
          latitude: parseFloat(this.detail.lat),
          longitude: parseFloat(this.detail.lng),
          name: this.detail.tname,
          address: this.detail.address,
          success: function() {
            console.log('success')
          }
        })
      }
    },
    handelCollect() {
      uni.showToast({
        title: '收藏成功',
        icon: 'none'
      })
    },
    showWechat(){
      this.$refs.wechat.show()
    },
    handleChat() {
      if (this.is_open_im === 0) {
        this.showWechat()
        return
      }
      if (!uni.getStorageSync('token')) {
        this.$navigateTo('/user/login/login')
        this.reload = true
        return
      }
      getChatInfo(this.params.id, 1)
    },
    makePhone(tel) {
      uni.makePhoneCall({
        phoneNumber: tel
      })
    },
    
    handleTel() {
      this.tel_params = {
        type: 3,
        scene_id:this.detail.id,
        scene_type:3,
        callee_id:this.detail.id,
        source: 'agent_detail',
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      if (this.is_open_middle_num == 1) {
        allTel(this.tel_params)
      } else {
        this.$ajax.get(
          'im/callUpStatistics.html',
          { id: this.detail.id, tel: this.detail.tel, type: 1 },
          res => {
            uni.makePhoneCall({
              phoneNumber: this.detail.tel
            })
          },
          err => {
            uni.showToast({
              title: '获取号码失败，请重试',
              icon: 'none'
            })
          }
        )
      }
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    // #ifdef MP-BAIDU
    baiduShareImg() {
      swan.shareFile({
        filePath: this.cardImg,
        // success:res=>{
        // 	uni.showToast({
        // 		title:"分享成功"
        // 	})
        // },
        fail: err => {
          uni.showToast({
            title: '分享失败',
            icon: 'none'
          })
        }
      })
    },
    // #endif
    doNot() {},
    doTask() {
      if (this.detail.id === this.mid) {
        this.$ajax.get('tasks/doTaskReward.html', { task_id: 12, mid: this.mid }, () => {})
      }
    }
  },
  onReachBottom() {
    //监听上拉触底事件
    this.params.page++
    this.getData()
  },
  onShareAppMessage(res) {
    this.doTask()
    let title = this.detail.introduce ? this.detail.introduce : this.detail.tname
    return {
      title: title || '',
      // #ifdef MP-BAIDU
      content: this.detail.address || '',
      imageUrl: this.detail.img ? formatImg(this.detail.img, 'w_6401') : ''
      // #endif
    }
  },
  // #ifdef APP-PLUS
  onBackPress() {
    if (this.showPopup) {
      this.showPopup = false
      this.$refs.share.hide()
      return true
    }
  }
  // #endif
}
</script>

<style scoped lang="scss">

view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.agent-container {
  padding-bottom: 130rpx;
  background-color: #f6f6f6;
}

.house-box{
  display: flex;
  flex-direction: initial;
  padding: 48rpx 24rpx;
  justify-content: space-between;
  align-items: center;
  .label {
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
  }
  .search-box{
    width: 300rpx;
    height: 64rpx;
    box-sizing: border-box;
    z-index: 9;
    transition: 0.26s;
    // padding: 20rpx 48rpx;
    // position: sticky;
    // top:var(--window-top);
    ::v-deep .my-search{
      background: #fff;
      // border: 1rpx solid rgba(235,235,235,1);
      border-radius: 32rpx;
    }
  }
}

.estate-box{
  padding-left: 24rpx;
  margin-top: 48rpx;
  .label {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
    color: #333;
  }
  .estate-list{
    white-space: nowrap;
    margin-top: 24rpx;
    .estate-item{
      display: inline-block;
      padding: 24rpx 16rpx;
      border-radius: 12rpx;
      background: #fff;
      margin-right: 24rpx;
    }
  }
}

.top-card{
  padding-bottom: 180rpx;
  box-sizing: border-box;
  position: relative;
  .bg{
    height: 330rpx;
    width: 100%;
    background-image: linear-gradient(125deg, #FF5500 0%, #FFA402 100%);;
  }
  .card-box{
    position: absolute;
    height: 340rpx;
    background: #FFFFFF;
    // border: 2rpx solid #D8D8D8;
    box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.05);
    border-radius: 28rpx;
    // padding: 0 0 48rpx 48rpx;
    left: 24rpx;
    right: 24rpx;
    bottom: 0;
    .left{
      max-width: calc(100% - 300rpx);
      padding: 48rpx 0 0 48rpx;
      flex: 1;
      .left-con{
        text{
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .shop-name{
        font-size: 28rpx;
        color: #333333;
        align-items: center;
        .shop-name-con{
          max-width: calc(100% - 40rpx);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .agent-name{
        font-size: 48rpx;
        color: #333333;
        font-weight: 600;
        margin: 24rpx 0;
      }
      .tel{
        display: flex;
        align-items: center;
        .tel-icon{
          width: 40rpx;
          height: 40rpx;
          // overflow: hidden;
          image{
            width: 100%;
          }
        }
        .tel-con{
          // max-width: 100%;
          overflow: hidden;
          // text-overflow: ellipsis;
          // white-space: nowrap;
          text{
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
        }
      }
      .address{
        margin-top: 24rpx;
        font-size: 14px;
        color: #333333;
        &.tel{
          .tel-icon{
            width: 40rpx;
            // height: 100rpx;
            // overflow: hidden;
            image{
              width: 40rpx;
            }
            
          }
      }
      
      }
    }
    .right{
      width: 300rpx;
      height: 342rpx;
      position: relative;
      margin-right: -2rpx;
      margin-top: -2rpx;
      vertical-align: bottom;
      clip-path: polygon(0 0 ,100% 0px, 100% 100%,76rpx 100%);
      image{
        width: 100%;
        height: 100%;
        border-top-right-radius: 28rpx;
        border-bottom-right-radius: 28rpx;
        // min-width:300rpx;
        // min-height: 340rpx;
      }
      
    }
    .middle{
      width: 76rpx;
      height: 342rpx;
      margin-top: -2rpx;
      background: #FF6700;
      clip-path: polygon(0 0, 4rpx 0, 76rpx 100%, 72rpx 100%);
      margin-right: -76rpx;
      z-index: 2;
    }
  }
}
// .card-box{
//   width: 100%;
//   // height: 346rpx;
//   padding: 48rpx 48rpx 0 48rpx;
//   position: relative;
//   z-index: 1;
//   background-color:#fff;
//   // margin-bottom: 160rpx;
//   .bg{
//     height: 346rpx;
//     background-color: #fa6469;
//     position: absolute;
//     top: 0;
//     width: 100%;
//     left: 0;
//     z-index: -1;
//   }
// }
// // 头部的卡片
// .adviser-card {
//   // position: absolute;
//   // left: 48rpx;
//   // right: 48rpx;
//   border-radius: 28rpx;
//   margin-bottom: 24rpx;
//   background-color:#fff;
//   border: 1px solid #D8D8D8;
//   box-shadow: 0 4px 16px 0 rgba(0,0,0,0.05);
//   // &.jin {
//   //   background: linear-gradient(135deg, #E5BA72 0%, #F4DBB3 100%);
//   // }
//   // &.yin {
//   //   background: linear-gradient(270deg, #D1DBE9 0%, #B3C3DA 100%);
//   // }
//   // &.tong {
//   //   background: linear-gradient(90deg, #e3af79 0%, #f9cfaa 100%);
//   // }
//   .level-icon {
//     width: 108rpx;
//     height: 108rpx;
//     position: absolute;
//     top: 48rpx;
//     right: 72rpx;
//   }
//   .adviser-info {
//     padding: 24rpx 48rpx;
//     align-items: center;
//     position: relative;
//     z-index: 3;
//     &::after{
//       content:'';
//       position: absolute;
//       bottom: 0;
//       left: 48rpx;
//       right: 48rpx;
//       height: 1rpx;
//       background-color: rgba($color: #000000, $alpha: 0.05);
//     }
//     .header-img-box {
//       width: 128rpx;
//       height: 128rpx;
//       margin-left: 16rpx;
//       border-radius: 50%;
//       overflow: hidden;
//       position: relative;
//       background-color: #f3f3f3;
//       .header-img {
//         width: 100%;
//         position: absolute;
//       }
//     }
//     .info {
//       flex: 1;
//       color: #333;
//       overflow: hidden;
//       .name-row {
//         align-items: center;
//         margin-bottom: 24rpx;
//       }
//       .name {
//         font-size: 32rpx;
//         margin-right: 16rpx;
//         font-weight: bold;
//       }
//       .level_name {
//         // height: 32rpx;
//         line-height: 32rpx;
//         padding: 0 15rpx;
//         border-radius: 16rpx;
//         font-size: 22rpx;
//         color: #999;
//         flex: 1;
//         &.level_names{
//           padding: 0;
//         }
//       }
//       .builds {
//         flex-direction: row;
//         align-items: center;
//         overflow: hidden;
//         white-space: nowrap;
//         text-overflow: ellipsis;
//         font-size: 26rpx;
//         color: $uni-color-primary;
//         .text{
//           margin-left: 16rpx;
//         }
//       }
//       .activity {
//         display: block;
//         overflow: hidden;
//         white-space: nowrap;
//         text-overflow: ellipsis;
//         font-size: 22rpx;
//       }
//     }
//   }

//   .adviser-data {
//     padding: 24rpx 48rpx;
//     justify-content: space-between;
    
//   }
//   .autograph{
//     line-height: 1.5;
//     margin-bottom: 20rpx;
//     font-size: 22rpx;
//     display: -webkit-box;
//     overflow: hidden;
//     text-overflow: ellipsis;
//     -webkit-line-clamp: 2;
//     -webkit-box-orient: vertical;
//     color: #333;
//     .label{
//       color: #999;
//     }
//     .icon{
//       float:right;
//       margin-top: 4rpx;
//     }
//   }

//   .browse_user_list{
//     align-items: center;
//     padding: 24rpx 48rpx;
//     .browse_user{
//       .prelogo{
//         width: 28rpx;
//         height: 28rpx;
//         margin-left: -14rpx;
//         border-radius: 50%;
//         background-color: #f5f5f5;
//       }
//     }
//     .browse_num{
//       margin-left: 16rpx;
//       font-size: 22rpx;
//       color: #fff;
//     }
//   }
// }
.options{
  line-height: 1;
  align-items: center;
  justify-content: space-between;
  padding: 0 48rpx;
  background: #fff;
  border-radius: 6px;
  margin: 48rpx 24rpx 0 24rpx;

  button{
    margin: 0;
    padding: 0;
    border-radius: 0;
    background: none;
    font-size: 28rpx;
    line-height: 1;
  }
  .item{
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 205rpx;
    padding: 24rpx;
    border-radius: 8rpx;
    // background-color: #f5f5f5;
    .icon_box{
      width: 80rpx;
      height: 80rpx;
      // border-radius: 50%;
      align-items: center;
      justify-content: center;
      margin-bottom: 16rpx;
      image {
        width: 80rpx;
        height: 80rpx;
      }
      // &.share{
      //   background-color: #00CAA7;
      //   box-shadow: 0 2px 8px 0 rgba(0,202,167,0.40);
      // }
      // &.zan{
      //   background-color: #FB656A ;
      //   box-shadow: 0 2px 8px 0 rgba(251,101,106,0.40);
      // }
      // &.post{
      //   background-color: #4CC7F6;
      //   box-shadow: 0 2px 8px 0 rgba(76,199,246,0.40);
      // }
    }
  }
}
//精选房源
.builds-box {
  margin-top: 48rpx;
  // padding: 0 48rpx;
  padding: 0 24rpx;
  .label {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 48rpx;
    color: #333;
  }
  .builds-list{
    background: #fff;
    padding: 24rpx 0 24rpx 24rpx;
    border-radius: 12rpx;
  }
  .build-list {
    white-space: nowrap;
  }
  .build-item {
    display: inline-block;
    font-size: 0;
    margin-right: 24rpx;
    width:280rpx;
    position: relative;
    .build_img {
      width: 100%;
      border-radius: 8rpx;
      height: 200rpx;
    }
    .jingxuan-label{
      padding: 6rpx;
      position: absolute;
      right: 0 ;
      top: 0;
      font-size: 11px;
      background-image: linear-gradient(135deg, #69D4BB 0%, #00CAA7 100%);
      border-radius:  0 8rpx 0 16rpx;
      color: #fff;
    }
    .build{
      .build-name{
        font-size: 32rpx;
        color: #333333;
        font-weight: 500;
        overflow: hidden;
        width: 100%;
        margin-top: 16rpx;
        text{
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .mid{
        margin-top: 8rpx;
        font-size: 20rpx;
        color: #999999;
        align-items: center;
        .mid-shitingwei{
          margin-right: 30rpx;
          font-size: 20rpx;
        }

      }
      .price{
        margin-top: 8rpx;
        align-items: center;
        font-size: 28rpx;
        color: #333333;
        .price-con{
          font-size: 32rpx;
          color: #FB656A;
          font-weight: 500;
        }
      }
      
    }
    // .build_name {
    //   line-height: 1;
    //   margin-top: 24rpx;
    //   font-size: 28rpx;
    //   color: #333;
    // }
  }
}

// 房源列表
.house-list {
  margin: 0 24rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  background-color: #fff;
  ::v-deep .nav-item.active:before {
    width: 40rpx;
    height: 8rpx;
    border-radius: 40rpx;
  }
  ::v-deep .bottom-line:after {
    height: 0;
  }
}
.house-cate {
  align-items: center;
  padding: 10rpx 0;
  height: 92rpx;
  // position: relative;
  overflow: hidden;
  white-space: nowrap;
  .cate-item {
    display: inline-block;
    margin-right: 48rpx;
    padding: 10rpx 0;
    font-size: 32rpx;
    // position: relative;
    transition: 0.1s;
    color: #333;
    &.active {
      font-size: 40rpx;
      font-weight: bold;
      // color: $uni-color-primary;
      // &::after {
      //   content: '';
      //   position: absolute;
      //   height: 6rpx;
      //   border-radius: 3rpx;
      //   left: 20rpx;
      //   right: 20rpx;
      //   bottom: 0;
      //   background-color: $uni-color-primary;
      // }
    }
  }
  .house_count{
    // position: absolute;
    // right: 0;
    // height: 40rpx;
    // line-height: 40rpx;
    // top: 0;
    // bottom: 0;
    // margin: auto;
    width: fit-content;
    padding-left: 50rpx;
    font-size: 24rpx;
    .label{
      color: #999;
    }
  }
}

// 底部菜单
// 底部菜单
.bottom-bar {
  background-color: #fff;
  height: 110rpx;
  padding: 15rpx 48rpx;
  z-index: 10;
  .icon-btn {
    // width: 100rpx;
    align-items: center;
    padding: 0;
    margin: 0;
    background-color: #fff;
    line-height: initial;
    display: flex;
    flex-direction: column;
    & ~ .icon-btn {
      margin-left: 48rpx;
    }
    text {
      line-height: 1.5;
      font-size: 22rpx;
      color: #333;
    }
  }
  .bar-btn {
    // width: 220rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    padding: 0;
    margin: 0;
    border-radius: 0;
    color: #fff;
    &.alone{
      border-radius: 40rpx;
    }
    &.btn1 {
      background: #FBAC65;
      box-shadow: 0 0 4px 0 rgba(0,0,0,0.05);
      border-top-left-radius: 40rpx;
      border-bottom-left-radius: 40rpx;
    }
    &.btn2 {
      background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
      box-shadow: 0 0 4px 0 rgba(255,80,0,0.30);
      border-top-right-radius: 40rpx;
      border-bottom-right-radius: 40rpx;
    }
  }
}

.qrcode-box{
  padding: 5vw;
  width: 80vw;
  margin-left: 10vw;
  border-radius: 16rpx;
  // margin-bottom: 90upx;
  background-color: #fff;
  .wechat-img{
    width: 70vw;
  }
  .no-wechat{
    text-align: center;
    margin-top: 24rpx;
    padding: 180rpx 24rpx;
    font-size: 30rpx;
    color: #999;
  }
  button{
    line-height: initial;
    padding: 10upx 20px;
    background-color: #fff;
  }
}

.share-box{
  padding: 20upx;
  background-color: #fff;
  .tip{
    padding: 10px;
    width: 100%;
    font-weight: 700;
    box-sizing: border-box;
    text-align: center;
  }
  button{
    line-height: initial;
    padding: 10upx 20px;
    background-color: #fff;
  }
  .item{
    text-align: center;
    padding: 10upx 20px;
    line-height: inherit;
  }
}

.card-img{
  width: 80%;
  margin: 0 10%;
  padding: 40upx 0;
}
.btn-box{
  padding: 0;
}
.btn-box .btn.btn-lg{
  width: 100%;
  padding: 10upx;
  border-radius: 10upx;
  height: 80upx;
  text-align: center;
  line-height: 60upx;
  box-sizing: border-box;
  font-size: $uni-font-size-lg;
  color: #fff;
  background-color: $uni-color-primary;
}

canvas{
  position: absolute;
  left: -100vw
}

/* #ifdef H5 */
#card {
  padding-bottom: 30px;
  width: 100%;
  position: fixed;
  // z-index: 99;
  left: -110vw;
  .header-box {
		justify-content: center;
		align-items: center;
		// text-align: center;
		padding: 24upx;
		box-sizing: border-box;
		width: 100%;
    height: 62vw;
    padding: 60upx 24upx;
    color: #fff;
		background-color: $uni-color-primary;
    .header {
      width: 100%;
      justify-content: center;
		  align-items: center;
      image {
       height: 180upx;
        width: 180upx;
        border: 6upx solid #fe7958;
        border-radius: 50%;
        margin-bottom: 20upx;
      }
      .name {
        font-size: 32upx;
        font-weight: bold;
		    margin-right: 10upx;
      }
      .info{
        margin: 10upx 0;
      }
      .position {
        display: inline-block;
        max-width: 100%;
        padding: 10upx 6upx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 30upx;
      }
    }
  }
}
// .card_img-box{
// 	width: 100%;
// 	height: 70vw
// }
// .card_img-box image{
// 	width: 25%;
// 	height: 25vw;
// 	margin-top: 15vw;
// 	margin-left: 37.5vw;
// 	border-radius: 50%;
// }
.card_info-box {
  margin: 40upx;
  padding: 20upx 30upx;
  font-size: 30upx;
  color: #555;
  background-color: #f3f3f3;
}
.text-right {
  text-align: right;
}
.card_info-box {
  .title {
    font-size: 38upx;
    line-height: 1.3;
    height: 110upx;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 40upx;
    color: #000;
  }
  .card_info{
    align-items: center;
  }
  .price {
    font-weight: bold;
    color: #f65354;
  }
}
.card-footer {
  margin: 30upx;
  font-size: 34px;
  line-height: 50upx;
  .text {
    padding: 20upx;
    color: #333;
  }
  .tip {
    font-size: 26upx;
    color: #666;
  }
  .qrcode {
    width: 30vw;
    height: 30vw;
  }
}
/* #endif */

// 复制文案
.copy-text-box{
  padding: 24rpx 32rpx;
  background-color: #fff;
  width: 600rpx;
  // height: 100%;
  margin-left: 75rpx;
  border-radius: 16rpx;
  .title{
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }
  .info-row{
    line-height: 1.6;
    color: #333;
    .label{
      color: #999;
    }
    .value{
      flex: 1;
      &.highlight{
        color: $uni-color-primary;
      }
    }
  }
  .button{
    align-items: center;
    justify-content: center;
    line-height: 64rpx;
    width: 236rpx;
    text-align: center;
    border-radius: 32rpx;
    margin: 32rpx auto;
    background: #FB656A;
    box-shadow: 0 2px 8px 0 rgba(251,101,106,0.40);
    color: #fff;
  }
  .disabled-btn{
    background-color: #f5f5f5;
    box-shadow: none;
    color: #999;
    >.text{
      margin-left: 12rpx;
    }
  }
}
.back{
        position: fixed;
        width: 100%;
        height: 88rpx;
        padding: 2px 10rpx;
        align-items: center;
        justify-content: space-between;
        background-image:  linear-gradient(125deg, #FF5500 0%, #FFA402 100%);
        z-index: 10;
        .title-con{
          flex: 1;
          text-align: center;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 32rpx;
          color: #fff;
        }
        .icon-box{
            // height: 44px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            justify-content: center;
            align-items: center;
            padding: 8px;
            // background: rgba(0, 0, 0, 0.6);
            justify-content: center;
            align-items: center;
            &.icon-share{
              justify-self: end;
              margin-left: auto;
            }
        }
    }
   ::v-deep .nav-box {
    flex: 1;
    overflow: hidden;
    .nav-item {
      width: auto;
      padding: 0 20rpx;
    }
  }
</style>
