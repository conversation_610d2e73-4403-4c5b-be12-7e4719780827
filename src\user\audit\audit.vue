
<template>
  <view class="ershou_detail_content">
    <!-- 焦点图 -->
    <view class="focus-box">
      <swiper
        class="banner"
        :indicator-dots="false"
        :circular="true"
        :duration="300"
        indicator-active-color="#f65354"
        @change="swiperChange"
        :current="swiperCurrent"
      >
        <swiper-item v-for="(item,index) in focus" :key="item.index">
          <view v-if="item.type === 'vr'" class="swiper-item">
            <image :src="(item.cover || imgs[0]) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/vr_b.png" mode="widthFix"></image>
          </view>
          <view v-if="item.type === 'video'" class="swiper-item">
            <image :src="(item.cover || item.url) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/video.png" mode="widthFix"></image>
          </view>
          <view v-if="item.type === 'img'" class="swiper-item" @click="preImg(index)">
            <image :src="item.url | imageFilter('w_6401')" mode="aspectFill"></image>
          </view>
          <view v-if="item.type === 'huxing'" class="swiper-item" @click="preHuxing(index)">
            <image :src="item.url | imageFilter('w_6401')" mode="aspectFill"></image>
          </view>
        </swiper-item>
      </swiper>
      <view class="icon-box flex-row icon-share"  @click="showCopywriting">
          <my-icon type="copy" color="#fff" size="36rpx"></my-icon>
      </view>
      <view class="icon-box flex-row icon-search"  @click="showInput">
          <my-icon type="ic_sousuo_w" color="#fff" size="36rpx"></my-icon>
      </view>
      <view class="number">编号：{{detail.id}}</view>
      <view class="img-total">共{{ focusLen }}张</view>
      <view class="cate-box">
        <view class="cate-list flex-row">
          <view
            v-if="detail.vr"
            class="cate"
            @click="switchFocus('vr')"
            :class="cateActive == 'vr' ? 'active' : ''"
            >VR</view
          >
          <view
            v-if="detail.videos.length > 0"
            class="cate"
            @click="switchFocus('video')"
            :class="cateActive == 'video' ? 'active' : ''"
            >视频</view
          >
          <view
            v-if="detail.img.length > 0"
            class="cate"
            @click="switchFocus('img')"
            :class="cateActive == 'img' ? 'active' : ''"
            >图片</view
          >
          <view
            v-if="detail.huxing&&detail.huxing.length > 0"
            class="cate"
            @click="switchFocus('huxing')"
            :class="cateActive == 'huxing' ? 'active' : ''"
            >户型</view
          >
        </view>
      </view>
    </view>
    <!-- 内容部分 -->
    <view class="container">
      <!-- 标题 -->
      <view class="house_title">{{detail.title}}</view>
      <template v-if ='detail.parentid ==1 ||detail.parentid==2'>
      
      <!-- 主要信息 -->
      <view class="main_info-box flex-row">
        <view class="info">
          <view class="huxing flex-row">
            <view class="price flex-row">
              <template v-if ="detail.parentid==1">
                <text class="value">{{detail.fangjia=='面议'||detail.fangjia=='0'?'面议':detail.fangjia}}</text>
                <text v-if="detail.fangjia!=='面议'&&detail.fangjia!=='0'" class="unit">万</text>
              </template>
              <template v-if ="detail.parentid==2">
                <text class="value">{{detail.zujin=='面议'||detail.zujin=='0'?'面议':detail.zujin}}</text>
              <text v-if="detail.zujin!=='面议'&&detail.zujin!=='0'" class="unit">元/月</text>
              </template>
            </view>
            <view class="stw flex-row">
              <text class="value">{{detail.shi}}</text>
              <text class="unit">室</text>
              <text class="value">{{detail.ting}}</text>
              <text class="unit">厅</text>
              <text class="value">{{detail.wei}}</text>
              <text class="unit">卫</text>
            </view>
            <view class="mianji flex-row">
              <text class="value">{{detail.mianji}}</text>
              <text class="unit">m²</text>
            </view>
          </view>
          <!-- 房源标签 -->
          <view class="label-list flex-row" v-if="detail.label&&detail.label.length>0">
            <!-- <text class="attr" :class="'attr' + detail.zhongjie">{{ detail.zhongjie==1?'个人':'经纪人' }}</text>
            <text class="label">{{ detail.catname||'' }}</text>
            <text class="label">{{ detail.areaname||'' }}</text> -->
            <text class="label" :style="{color:item.color,borderColor:item.color}" v-for="(item, index) in detail.label" :key="index">{{ item.name }}</text>
          </view>
        </view>
        <view class="btn">+对比</view>
      </view>
      <!-- 基础信息 -->
      <view class="block">
        <view class="label">房源信息</view>
        <view class="info-list flex-row">
          <view class="info-list-item">
            <template v-if ="detail.parentid ==1">
              <view class="label">单价</view>
              <view class="data">{{detail.danjia||''}}元/㎡</view>
            </template>
            <template v-if ="detail.parentid ==2">
              <view class="label">区域</view>
              <view class="data">{{detail.areaname||''}}</view>
            </template>
          </view>
          <view class="info-list-item">
            <view class="label">朝向</view>
            <view class="data">{{detail.chaoxiang||'南'}}</view>
          </view>
          <view class="info-list-item">
            <view class="label">装修</view>
            <view class="data">{{detail.zhuangxiu||'不详'}}</view>
          </view>
          <view class="info-list-item">
            <view class="label">楼层</view>
            <view class="data" v-if="detail.floor">{{detail.floor||''}}/共{{detail.louceng||''}}层</view>
            <view class="data" v-else>共{{detail.louceng||''}}层</view>
          </view>
          <view class="info-list-item">
            <view class="label">类型</view>
            <view class="data">{{detail.catname}}</view>
          </view>
          <view class="info-list-item">
            <view class="label">更新</view>
            <view class="data">{{detail.begintime||''}}</view>
          </view> 
          <view class="info-list-item" v-if="show_more_info &&detail.parentid ==1">
            <view class="label">区域</view>
            <view class="data">{{detail.areaname||''}}</view>
          </view>
          <view class="info-list-item" v-if="show_more_info  &&detail.parentid ==2">
            <view class="label">小区</view>
            <view class="data">{{housePrice.title||'不详'}}</view>
          </view>
          <view class="info-list-item" v-if="show_more_info">
            <view class="label">来源</view>
            <view class="data">{{detail.zhongjie==1?'个人':'经纪人'}}</view>
          </view>
          <view class="info-list-item" v-if="show_more_info">
            <view class="label">浏览</view>
            <view class="data">{{detail.hit||0}}</view>
          </view>
          <view class="xiajia_icon" v-if="detail.is_show === 0">
            <my-icon type='yixiajia' size="180rpx" color='#e94e50'></my-icon>
          </view>
        </view>
        <template v-if='agent&&agent.levelid>1'>
          <view class="more_btn has_agent" @click="show_more_info=!show_more_info">{{show_more_info?'收起':'查看更多'}}</view>
        </template>
        <template v-else>
          <view class="more_btn" @click="show_more_info=!show_more_info">{{show_more_info?'收起':'查看更多'}}</view>
        </template>
      </view>
      <!-- 聊天咨询按钮 -->
      <template v-if ="agent&&agent.levelid>1">
      <view class="btn_list-box flex-row" v-if="is_open_im">
        <view class="btn-item">
          <chatBtn :user_login_status="login_status" >
            <view class="flex-row">
              <view class="img">
                <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
              </view>
              <!-- <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon> -->
              <text>咨询楼层</text>
            </view>
          </chatBtn>
        </view>
        <view class="btn-item">
          <chatBtn :user_login_status="login_status">
            <view class="flex-row">
              <view class="img">
                <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
              </view>
              <!-- <my-icon type="huxing" color="#ff656b" size="42rpx"></my-icon> -->
              <text>房源价格解析</text>
            </view>
          </chatBtn>
        </view>
      </view>
      </template>
      <!-- 房源介绍 -->
      <view class="block house_desc">
        <view class="label">房源特色</view>
        <view class="wnsb-list flex-row" v-if="detail.wnsb&&detail.wnsb.length>0">
          <text class="label" v-for="(item, index) in detail.wnsb" :key="index">{{ item }}</text>
        </view>
        <view class="content_info">
          <view class="desc">
            <text class="cate">核心卖点</text>
            <text>{{detail.content}}</text>
          </view>
          <view class="desc" v-if="detail.owner_think">
            <text class="cate">业主心态</text>
            <text>{{detail.owner_think}}</text>
          </view>
          <view class="desc" v-if="detail.service_introduce">
            <text class="cate">服务介绍</text>
            <text>{{detail.service_introduce}}</text>
          </view>
          <view class="tip">联系我时，请说在{{site_name}}看到的信息，谢谢</view>
        </view>
      </view>
      <!-- 广告位 -->
      <swiper v-if="advs&&advs.length>0" class="ext_swiper" autoplay :interval="3000">
        <swiper-item v-for="(item, index) in advs" :key="index" >
          <image :src="item.image | imageFilter('w_8601')" mode="aspectFill"></image>
          <view class="marker">广告</view>
        </swiper-item>
      </swiper>
      <!-- 房源动态 -->
      <template v-if ="agent&&agent.levelid>1">
      <view class="block house_news">
        <view class="label flex-row">
              房源动态
        </view>
        <view class="house_news_info_box">
          <view class="house_news_info bottom-line flex-row">
            <view class="house_news_info_item">
              <view class="house_news_info_item_num">
                {{detail.hit}}
              </view>
              <view class="house_news_info_item_title">
                浏览人数
              </view>
            </view>
            <view class="house_news_info_item">
              <view class="house_news_info_item_num">
                {{detail.tel_volume}}
              </view>
              <view class="house_news_info_item_title">
                近30天咨询
              </view>
            </view>
          </view>
          <view class="house_info_timeline">
              <view class="time_line">
                  <view class="item" v-for="(item, index) in detail.price_changes" :key="index"  >
                      <view class="line-item"  >
                          <view class="content_c flex-row">
                            
                            <view class="content_con flex-row"><text >{{item.title}}</text> <text class ="blod"> {{item.price}}</text><text>{{item.price_uint}}</text> <my-icon v-if ="item.type==2&&item.diff<0" type="ic_down" color="rgb(0, 202, 167)"></my-icon>
                            <my-icon v-if ="item.type==2&&item.diff>0" type="ic_up" color="rgb(251, 101, 106)"></my-icon> </view>
                            <chatBtn :user_login_status="login_status">
                            <view class="content_sub">
                                {{item.type|formatType}}
                            </view>
                            </chatBtn>
                          </view>
                          <view class="line-header flex-box">
                              <view class="time">{{item.time}}</view>
                          </view>
                          
                      </view>
                  </view>
              </view>
          </view>
        </view>
      </view>
      </template>
      <!-- 所属小区 -->

      <view class="block community-box" v-if="housePrice.id">
        <view class="label flex-row">
          <text>{{housePrice.title}}</text>
          <view class="into flex-row" >
            <text>小区详情</text>
            <my-icon type="ic_into" color="#999" size="28rpx"></my-icon>
          </view>
        </view>
        <view class="community flex-row">
          <image class="img" mode="aspectFill" :src="housePrice.img | imageFilter('w_240')"></image>
          <view class="info">
            <view class="price_row flex-row">
              <view class="price flex-row">
                <text class="label">参考均价</text>
                <text class="number">{{housePrice.avg_price}}</text>
                <text class="unit">元/m²</text>
              </view>
            </view>
            <view class="increase flex-row">
              <text class="label">比上月</text>
              <my-icon type="ic_up" v-if="housePrice.house_status===1" color="#fb656a"></my-icon>
              <my-icon type="ic_down" v-else-if="housePrice.house_status===2" color="#179B16"></my-icon>
              <text v-else class="ping">持平</text>
              <view class="increase-text">
                <text v-if="housePrice.house_status!==0" class="value" :class="{down:housePrice.house_status===2}">{{housePrice.house_value}}</text>
                <text v-if="housePrice.house_status!==0" class="unit" :class="{down:housePrice.house_status===2}">%</text>
              </view>
            </view>
            <view class="address flex-row">
              <text class="label">小区地址</text>
              <text class="value">{{housePrice.address||'暂未更新'}}</text>
            </view>
          </view>
        </view>
        <view class="more_btn no_bg" v-if="housePrice.count>0">查看同小区{{housePrice.count}}套在售房源></view>
      </view>
      <!-- 聊天咨询按钮 -->
      <template v-if ="agent&&agent.levelid>1&&detail.parentid ==1">
      <view class="btn_list-box flex-row" v-if="is_open_im">
        <view class="btn-item">
          <chatBtn :user_login_status="login_status">
            <view class="flex-row">
              <view class="img">
                <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
              </view>
              <!-- <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon> -->
              <text>咨询房贷首付</text>
            </view>
          </chatBtn>
        </view>
        <view class="btn-item">
          <chatBtn :user_login_status="login_status">
            <view class="flex-row">
              <view class="img">
                <image class="img_c" mode ="widthFixed" :src ="'/images/newhouse_detail/<EMAIL>'|iconformat"></image>
              </view>
              <!-- <my-icon type="huxing" color="#ff656b" size="42rpx"></my-icon> -->
              <text>咨询详细税费</text>
            </view>
          </chatBtn>
        </view>
      </view>
      </template>
      <!-- TA的其他房源 -->
      <view class="block" v-if="agent&&agent.levelid>1&&other_house.length>0&&detail.parentid ==1">
        <view class="label">
          <text>TA的其他房源</text>
          <text class="more">更多</text>
        </view>
        <swiper class="other_house-list" :duration="300" :display-multiple-items="2" next-margin="88rpx">
          <swiper-item v-for="(item, index) in other_house" :key="index">
            <view class="swiper-item" >
              <view class="img-box">
                <image :src="item.img  | imageFilter('w_320')" mode="aspectFill"></image>
              </view>
              <view class="house_type">{{ item.title }}</view>
              <view class="aligin-end flex-row">
                <text class="stw">{{ item.shi }}室{{ item.ting }}厅{{ item.wei }}卫</text>
                <text class="mianji" >{{
                  item.mianji
                }}m²</text>
              </view>
              <view class="price-box flex-row">
                <view class="flex-row">
                  <text class="price">{{ item.fangjia }}</text><text class="unit">万</text>
                </view>
                <view class="db_btn">+对比</view>
              </view>
            </view>
          </swiper-item>
          <swiper-item v-if="other_house.length < 2"></swiper-item>
        </swiper>
      </view>
      <!-- 位置及周边 -->
      <view class="block" v-if="detail.lat&&detail.lng">
        <view class="label">位置及周边</view>
        <mapNearby :scale="mapData.scale" :cirles ="cirles"  :enableZoom="false" :enableScroll ='false' :lat="detail.lat" :lng="detail.lng" :markers="mapData.covers"></mapNearby>
      </view>
      <!-- 推荐房源 -->
      <view class="block" v-if="recommend_list.length>0&&!sid&&!shareType">
        <view class="label mgb0">推荐房源</view>
        <block v-for="(item) in recommend_list" :key="item.id">
          <house-item :item-data="item" type="ershou"></house-item>
        </block>
      </view>
      <view class="entrant_button" v-if ="agent&&agent.levelid>1">
        房源不合适?委托TA帮我找房
      </view>
      </template>
      <template v-if ='detail.parentid==3 ||detail.parentid==4'>
        <view class="info_detail flex-row bottom-line">
          <image class="header_img" :src="agent.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
          <view class="center flex-1">
            <view class="user_name">{{agent.cname}}</view>
            <view class="detail_bottom flex-row">
              <text class="time">{{detail.begintime}}</text>
              <text class="area" v-if="detail.areaname">{{detail.areaname}}</text>
              <text>{{detail.catname}}</text>
            </view>
          </view>
          <view class="info_price" v-if='detail.parentid==3'>{{detail.qzprice?detail.qzprice+'元/月':'面议'}}</view>
          <view class="info_price" v-if='detail.parentid==4'>{{detail.qgprice?detail.qgprice+'万元':'面议'}}</view>
        </view>
        <view class="map_box" v-if="detail.lat&&detail.lng">
          <view class="label">目标区域</view>
          <map class="map" :scale="14" :longitude="detail.lng" :latitude="detail.lat" :markers="[{id:1,latitude:detail.lat,longitude:detail.lng}]"></map>
        </view>
        <view class="desc_box">
          <view class="label">求租描述</view>
          <view class="desc">
            <text>{{detail.content}}</text>
            <view class="tip">联系我时，请说在{{site_name}}看到的信息，谢谢</view>
          </view>
          <view class="xiajia_icon" v-if="detail.is_show === 0">
            <my-icon type='yixiajia' size="180rpx" color='#e94e50'></my-icon>
          </view>
        </view>
      </template>
      <!-- 免责声明 -->
      <view class="shengming"  v-if="disclaimer">
        <view class="shengming_title flex-row">
          <text>免责声明</text>
          <text class="label">举报</text>
        </view>
        <view class="shengming_content" v-html="disclaimer"></view>
      </view>

      <!-- 底部操作菜单 -->
      <view class="footer flex-row">
        <view class="footer_btn_read" @click ="toDetail">
          浏览
        </view>
        
        <Dropdown class ="drop_down">
          <view class='footer_btn footer_btn_member' 
            >会员操作</view
          >
          <view class="options_list" slot="dropdown_list">
            <DropdownItem
              plain
              :round="false"
              size="big"
              @click="showGenjin"
            >
              会员跟进
            </DropdownItem>
            <DropdownItem
              plain
              :round="false"
              size="big"
              @click="setHuiyuanGroup"
              >设置会员组</DropdownItem
            >
            <DropdownItem @click="setVip()" 
              >{{memberInfo.is_vip==1?'取消个人vip':'设置个人vip'}}</DropdownItem
            >
            <DropdownItem @click="frozeVip()" 
              >{{memberInfo.isfrozen==1?'解封会员':'封禁会员'}}</DropdownItem
            >
          </view>
        </Dropdown>
        <Dropdown class ="drop_down">
          <view class="footer_btn shangjia" 
            >信息操作</view
          >
          <view class="options_list" slot="dropdown_list">
            <DropdownItem
              plain
              :round="false"
              size="big"
              v-if="detail.is_show==0"
              @click="shangjiaInformation(1)"
            >
            信息上架
            </DropdownItem>
            <DropdownItem
              plain
              :round="false"
              size="big"
              v-if="detail.is_show==1"
              @click="shangjiaInformation(0)"
              >信息下架</DropdownItem
            >
            <DropdownItem
              plain
              :round="false"
              size="big"
              @click="infomationTypeChange(0)"
              v-if="detail.info_level==1"
            >
            转为待审
            </DropdownItem>
            <DropdownItem
              plain
              :round="false"
              size="big"
              v-if="detail.info_level==0"
              @click="infomationTypeChange(1)"
              >转为正常</DropdownItem
            >
            <DropdownItem @click="refresh()" 
              >信息刷新</DropdownItem
            >
            <DropdownItem @click="showCopywriting($event,'noShowTel')" 
              >复制推广信息</DropdownItem
            >
            <DropdownItem @click="toHistory()" 
              >历史记录</DropdownItem
            >
          </view>
        </Dropdown>
                
      </view>
    </view>
      <!-- 分享选项 -->
      <share-pop ref="share_popup" @copyLink="show_share_tip=true" @appShare="appShare" @handleCreat='handleCreat' @showCopywriting='showCopywriting'></share-pop>
      <!-- 复制分享文案 -->
      <my-popup ref="text_popup" position="center" :height="text_popup_height">
        <view class="copy-text-box" id="copy-text">
          <view class="title">{{detail.title}}</view>
          <view class="info-box">
            <view class="info-row flex-row" v-if="housePrice.title">
              <text class="label">小区：</text>
              <text class="value">{{housePrice.title}}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">户型：</text>
              <text class="value">{{`${detail.shi}室${detail.ting}厅${detail.wei}卫${detail.mianji}m²`}}</text>
            </view>
            <template v-if ="detail.parentid==1 ||detail.parentid==4">
              <view class="info-row flex-row">
                <text class="label">售价：</text>
                <text class="value">{{detail.fangjia?detail.fangjia+'万': '面议'}}</text>
                <text class="value" v-if="detail.danjia&&detail.parentid==1">单价{{detail.danjia}}</text>
              </view>
            </template>
            <template v-if ="detail.parentid==2 ||detail.parentid==3">
              <view class="info-row flex-row">
                <text class="label">租金：</text>
                <text class="value">{{detail.zujin?detail.zujin+'元/月': '面议'}}</text>
              </view>
            </template>
            <view class="info-row flex-row">
              <text class="label">类型：</text>
              <text class="value">{{detail.catname}}</text>
            </view>
            <view class="info-row flex-row" v-if="detail.label&&detail.label.length>0&&(detail.parentid ==1||detail.pareneid==2)">
              <text class="label">卖点：</text>
              <text class="value">{{detail.label.map(item=>item.name).join(' ')}}</text>
            </view>
            <view class="info-row flex-row" v-if='detail.parentid ==1||detail.pareneid==2'>
              <text class="label">楼层：</text>
              <text class="value" v-if="detail.floor">{{detail.floor||''}}/共{{detail.louceng||''}}层</text>
              <text class="value" v-else>共{{detail.louceng||''}}层</text>
            </view>
            <view class="info-row flex-row" v-if ="showTel">
              <text class="label">电话：</text>
              <text class="value">{{detail.tel?detail.tel:''}}</text>   
              <!-- <text class="value" v-if ="agent&&agent.shareId" >{{agent.tel?agent.tel:''}}</text>  -->
              <!-- <text class="value" v-if ="agent&&!agent.shareId&&agent.levelid>1" >{{detail.tel?detail.tel:''}}</text>    -->
            </view>
            <view class="button disabled-btn flex-row" v-if="copy_success">
              <my-icon type="check-circle" size="30rpx" color="#999"></my-icon>
              <text class="text">文本已复制</text>
            </view>
            <view class="button" v-else @click="copywriting">复制文本</view>
          </view>
        </view>
      </my-popup>
      <enturstBtn v-if="agent&&(agent.agent_id||agent.adviser_id)" :to_user="agent"  />
      
      <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
        <enturstBox @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="showLoginPopup('为方便您及时接收消息通知，请输入手机号码')" :to_user="agent" :isDetail="isDetail" />
      </my-popup>
      <my-popup ref="genjinPop" height="580rpx"   position="center">
        <view class="form">
          <view class="form_item bottom-line flex-row">
            <view class="label">跟进内容</view>
            <input class ="item_inp flex-1" type="text" placeholder="请输入跟进内容" v-model="genjinParams.content">
          </view>
          <view class="form_item bottom-line flex-row">
            <view class="label">财务进账</view>
            <view class="flex-1 item_inp">
                <radio-group class="flex-row item_radio" @change="caiwuChange">
                <label class="flex-row item_radio_item" v-for="(item, index) in [{name:'无',value:'0'},{name:'有',value:'1'}]" :key="item.value">
                    <view class="flex-row">
                        <radio :value="item.value" :checked="index === genjinParams.is_income" />
                    </view>
                    <view>{{item.name}}</view>
                </label>
            </radio-group>
            </view>
          </view>
          <view class="form_item bottom-line flex-row" v-if='genjinParams.is_income==1'>
            <view class="label">进账金额</view>
            <input class ="item_inp flex-1" type="text" placeholder="请输入进账金额" v-model="genjinParams.income_amount">
          </view>
          <view class="btns flex-row ">
            <view class="genjin_btn "  @click ="subGenjin">确认</view>
            <view class="genjin_btn cancel"  @click ="genjinPopHide">取消</view>
          </view>
        </view>
      </my-popup>
      <my-popup ref="frozenPop" height="380rpx"   position="center">
        <view class="form">
          <view class="form_item bottom-line flex-row">
            <view class="label">设置原因</view>
            <view class="flex-1 item_inp">
                <input class ="item_inp flex-1" type="text" placeholder="请输入设置原因" v-model="frozenParams.reason">
            </view>
          </view>
          <view class="btns flex-row ">
            <view class="genjin_btn" @click="subFrozen">确认</view>
             <view class="genjin_btn cancel"  @click="frozenPopHide">取消</view>
          </view>
        </view>
      </my-popup>
      <my-popup ref="inputPop" height="380rpx"   position="center">
        <view class="form">
          <view class="form_item bottom-line flex-row">
            <view class="label">房源编号</view>
            <view class="flex-1 item_inp">
                <input class ="item_inp flex-1" type="text" placeholder="请输入房源编号" v-model="bianhao">
            </view>
          </view>
          <view class="btns flex-row ">
            <view class="genjin_btn" @click="toOther">管理房源</view>
             <view class="genjin_btn cancel"  @click="inputPopHide">取消</view>
          </view>
        </view>
      </my-popup>
      <my-popup ref="vipPop" height="380rpx"   position="center">
        <view class="form">
          <view class="form_item bottom-line flex-row">
            <view class="label">设置原因</view>
            <view class="flex-1 item_inp">
                <input class ="item_inp flex-1" type="text" placeholder="请输入设置原因" v-model="vipParams.reason">
            </view>
          </view>
          <view class="btns flex-row ">
            <view class="genjin_btn" @click="subVip">确认</view>
             <view class="genjin_btn cancel"  @click="vipPopHide">取消</view>
          </view>
        </view>
      </my-popup>
      <my-popup ref="groupPop" height="580rpx"   position="center">
        <view class="form">
          <view class="form_item bottom-line flex-row">
            <view class="label">选择会员组</view>
            <view class="item_inp flex-1" >
              <picker @change="bindPickerChange" :value="groupIndex" :range="levels" range-key="levelname">
                <view class="uni-input">{{groupIndex >= 0?levels[groupIndex].levelname:'请选择会员组'}}</view>
              </picker>
            </view>
          </view>
          <view class="form_item flex-row bottom-line" v-if ="groupIndex>0">
            <view class="label">到期时间</view>
            <view class="flex-1 item_inp">
                <picker  mode="date" @change="bindPickerLevelupTimeChange"   :value="groupParams.levelup_time" >
                <view class="uni-input">{{groupParams.levelup_time?groupParams.levelup_time:'请选择到期时间'}}</view>
              </picker>
            </view>
          </view>
          <view class="form_item bottom-line flex-row" >
            <view class="label">设置原因</view>
            <input class ="item_inp flex-1" type="text" placeholder="请输入设置原因" v-model="groupParams.reason">
          </view>
          <view class="btns flex-row ">
            <view class="genjin_btn" @click ="subGroup">确认</view>
             <view class="genjin_btn cancel"  @click ="groupPopHide">取消</view>
          </view>
        </view>
      </my-popup>
          <!-- 登录弹窗 -->
      <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
    <!-- <chat-tip></chat-tip> -->
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
  </view>
</template>

<script>
import {
  uniList,
  uniListItem
} from '@dcloudio/uni-ui'
import {
  formatImg,
  showModal,
  config,
  isIos
} from '../../common/index.js'
import Dropdown from '@/components/Dropdown'
import DropdownItem from '@/components/DropdownItem'
import wx from 'weixin-js-sdk'
import myIcon from '../../components/myIcon.vue'
import loanDetails from "../../components/loanDetail.vue";
import mapNearby from "../../components/mapNearby.vue";
import houseItem from '../../components/houseItem.vue'
import myPopup from '../../components/myPopup.vue'
import shareTip from '../../components/shareTip.vue'
import loginPopup from '../../components/loginPopup'
import encryptionTel from '../../common/encryption_tel.js'
import checkLogin from '../../common/utils/check_login'
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
import chatBtn from '@/components/open-button/chatBtn'
import sharePop from '../../components/sharePop'
import { getLonAndLat } from '@/common/utils/getLonAndLat'
export default {
  components: {
    uniList,
    uniListItem,
    myIcon,
    loanDetails,
    mapNearby,
    houseItem,
    myPopup,
    shareTip,
    loginPopup,
    enturstBtn,
    enturstBox,
    chatBtn,
    sharePop,
    Dropdown,
    DropdownItem
  },
  data() {
    return {
      id: "",
      detail: {
        fangjia:"",
        img: [],
        videos: [],
        huxing: [],
      },
      agent: {},
      is_collect: 0,
      housePrice: {},
      loan_type: 0,
      other_house:[], //TA的其他房源
      recommend_list:[], //推荐的房源
      focus: [],
      focusLen: "",
      swiperCurrent: 0,
      cateActive: "",
      mapList: [],
      mapData: {
        scale: 12,
        covers: [],
      },
      opacity:0,
      login_tip:'',
      show:false,
      show_more_info: false,
      contrastCount: 0,
      disclaimer: '',
      text_popup_height: '',
      copy_success: false,
      show_share_tip: false,
      currentUserInfo:{},
      sid:'',
      shareType:'',
      link:'',
      login_tip: '',
      toLogin:true,
      shangdaililv: 4.9,
      borrow:{},
      advs:[],
      showBackBtn:1,
      iconColor:"#fff",
      sub_type:0,
      isDetail:0,
      genjinParams:{
        uid:'',
        content:"",
        is_income:0,
        income_amount:'',
      },
      groupParams:{
        levelid:"",
        id:'',
        levelup_time:"",
        reason:''
      },
      frozenParams:{
        id:'',
        isfrozen:'',
        reason:''
      },
      vipParams:{
        id:'',
        is_vip:'',
        reason:''
      },
      levels:[],
      memberInfo:{},
      groupIndex:-1,
      showTel:true,
      bianhao:'',
      moteNum:0,
    }
  },
  onLoad(options) {

    if (JSON.stringify(this.$store.state.tempData) != "{}") {
      var tempDate = JSON.parse(JSON.stringify(this.$store.state.tempData))
      tempDate.img = ""
      Object.assign(this.detail, tempDate)
      document.title = this.detail.title
      // uni.setNavigationBarTitle({
      //   title: this.detail.title
      // })
      this.$store.state.tempData = {}
    } else if (options.title) {
      this.detail.title = decodeURIComponent(options.title)
      document.title = this.detail.title
      // uni.setNavigationBarTitle({
      //   title: this.detail.title
      // })
    }
    

     let url;
      if(isIos()){
        url = this.$store.state.firstUrl
      }else{
        url = window.location.href
      }
    this.$ajax.get('/wechat/index/signature.html',{url:url},res=>{
        if(res.data.code == 1){
            res.data.config.jsApiList = ['hideOptionMenu']
            wx.config(res.data.config)
            wx.hideOptionMenu();
        }
    })
    if (options.id) {
      this.id = options.id
      checkLogin({
        fail: (res)=>{
          console.log("没登录");
          if (this.toLogin==false) return 
          this.toLogin=false
          if(this.$store.state.user_login_status==1){
              uni.setStorageSync('backUrl', window.location.href)
              this.$navigateTo("/user/login/login")
          }
        },
        success:()=>{
          this.getData(options.id)
        },
        complete:(res)=>{
          this.$store.state.user_login_status = res.status
        }
      })
      
    }
    
  },
  onShow() {
    if (this.reload) {
      this.reload = false
      this.$store.state.allowOpen = true
      this.getData(this.id)
    }
  },
  onPageScroll(e){
    this.opacity =e.scrollTop/180
    if (this.opacity>1){
      this.opacity=1
    }
    if (this.opacity<0){
      this.opacity=0
    }
    
    if (this.opacity>0.3){
      this.iconColor="#000"
    }else {
      this.iconColor="#fff"
    }
  },
  onUnload() {

    this.$store.state.buildInfo = {}
    this.$store.state.tempData = {}
  },
  computed: {
    is_open_im() {
        return this.$store.state.im.ischat 
    },
    is_open_middle_num() {
        return this.$store.state.im.istelcall 
    },
    hasWechat() {
        return this.$store.state.hasWechat 
    },
    login_status() {
      return this.$store.state.user_login_status
    },
    status_top(){
      return 0
    },
    site_name(){
      return this.$store.state.siteName
    },
    oneKm(){
      return getLonAndLat(this.detail.lng,this.detail.lat,0,1000)
    },
    twoKm(){
      return getLonAndLat(this.detail.lng,this.detail.lat,0,2000)
    },
    threeKm(){
      return getLonAndLat(this.detail.lng,this.detail.lat,0,3000)
    },
    cirles(){
      if(this.detail &&this.detail.lat) {
        return [
          {
							longitude:this.detail.lng,
							latitude:this.detail.lat,
							color:"#ff0000",
							radius:1000,
							strokeWidth:1,
						},
						{
							longitude:this.detail.lng,
							latitude:this.detail.lat,
							color:"#ff9c00",
							radius:2000,
							strokeWidth:1
						},
						{
							longitude:this.detail.lng,
							latitude:this.detail.lat,
							color:"#fee500",
							fillColor:"#00000026",
							radius:3000,
							strokeWidth:1
						}
        ]
      }
    }
  },
  filters:{
    iconformat(val){
      return config.imgDomain+val
    },
    formatType(val){
      if (val=="1") return "咨询优惠"
      if (val=="2") return "咨询底价"
      if (val=="3") return "咨询成交价"
    }
  },
  methods: {
    // hideMenu(){
    //   let url;
    //   if(isIos()){
    //     url = this.$store.state.firstUrl
    //   }else{
    //     url = window.location.href
    //   }
    //   this.$ajax.get('/wechat/index/signature.html',{url:url},res=>{
    //     if(res.data.code == 1){
    //       res.data.config.jsApiList = ['hideMenuItems']
    //       wx.config(res.data.config)
    //     }
    //   })
    // },
    // 刷新
    refresh(){
      uni.showModal({
        content: '确认刷新该信息吗？',
        success: (res) =>{
          if (res.confirm) {
            this.$ajax.post('infoAudit/infoRefresh',{id:this.detail.id},res=>{
              if (res.data.code ==1){
                this.getData(this.detail.id)
              }
              uni.showToast({
                title: res.data.msg,
                icon:'none'
              });
            })
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });
    },
    // 转为正常  专待审
    infomationTypeChange(status){
      let text = '确定将该房源转为待审吗？'
      if(status ==1){
        text = '确定将该房源转为正常吗？'
      }
      uni.showModal({
        content: text,
        success: (res) =>{
          if (res.confirm) {
            this.$ajax.post('infoAudit/infoCheck',{id:this.detail.id,status},res=>{
              if (res.data.code ==1){
                this.getData(this.detail.id)
              }
              uni.showToast({
                title: res.data.msg,
                icon:'none'
              });
            })
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });

    },
    // 上架房源
    shangjiaInformation(status){
      let text = '确定上架该房源吗？'
      if(status ==0){
        text = '确定下架架该房源吗？'
      }
      uni.showModal({
        content: text,
        success:(res)=>{
          if (res.confirm) {
            this.$ajax.post('infoAudit/infoShow',{id:this.detail.id,status},res=>{
              if (res.data.code ==1){
                this.getData(this.detail.id)
              }
              uni.showToast({
                title: res.data.msg,
                icon:'none'
              });
            })
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });

    },
    // 封禁会员
    frozeVip(){
        this.$refs.frozenPop.show()
    },
    // 设置会员
    setVip(){
      this.$refs.vipPop.show()
    },
    // 设置会员组
    setHuiyuanGroup(){
      this.$refs.groupPop.show()
      this.groupParams.levelid = this.memberInfo.levelid
      this.groupParams.levelup_time =this.formatDate(this.memberInfo.levelup_time) 
      this.$forceUpdate()
    },
    bindPickerChange(e){
      this.groupIndex =e.detail.value
      this.groupParams.levelid =this.levels[e.detail.value].id
    },
    //会员跟进
    showGenjin(){
      this.$refs.genjinPop.show()
    },
    // 管理其他房源
    showInput(){
      this.$refs.inputPop.show()
    },
    toOther(){
      if(!this.bianhao){
        uni.showToast({
          title: '请输入房源编号',
          icon:'none'
        });
        return
      }
      this.$navigateTo("/user/audit/audit?id="+this.bianhao )
      this.inputPopHide()
    },
    getData(id) {
      
      let params = {
        id: id,
      }
      this.$ajax.get('release/previewInfoDetail.html', params, (res) => {
        if (res.data.code == 0) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
          this.show=true;
          return
        }
        if (res.data.borrow){
          this.borrow=res.data.borrow
        }
        // this.disclaimer = res.data.disclaimer
        res.data.house.wnsb = res.data.house.wnsb?res.data.house.wnsb.split(','):[]
        this.detail = res.data.house
        this.levels =res.data.levels
        this.memberInfo =res.data.member
        this.groupIndex =this.levels.findIndex(item=>item.id ==this.memberInfo.levelid)
        
        // this.showBackBtn=res.data.switch_wap_info_title
        if(res.data.adv&&res.data.adv.length>0){
          this.advs = res.data.adv
        }
        // if (res.data.agent.levelid&&res.data.agent.levelid>1){
          // this.agent = res.data.agent
          // if (this.agent.id){
          //   this.agent.infoDetailId=this.agent.id
          // }
        // }
        
        // if (this.currentUserInfo.sid&&this.sid){
        //   this.agent = this.currentUserInfo
        // }
        // if(res.data.community.length>0){
        //   this.housePrice = res.data.community[0]
        // }
        // 设置地图中心点
        if (this.detail.lat && this.detail.lng) {
          // this.getCovers('商业')
          this.mapData.covers = [
            {
              latitude: this.detail.lat,
              longitude: this.detail.lng,
              width: 30,
              height: 30,
              iconPath: '/static/icon/center.png'
            }
          ]
        }
        // this.other_house = res.data.others
        // this.recommend_list = res.data.recommendHouse
        // this.is_collect = res.data.is_collect
        document.title = res.data.house.title
        // uni.setNavigationBarTitle({
        //   title: res.data.house.title
        // })
        this.video_num = res.data.house.videos.length
        this.img_num = res.data.house.img.length
        // 合并图片视频和全景图
        this.imgs = res.data.house.img
        let imgs = res.data.house.img.map((item) => {
          return {
            type: 'img',
            url: item
          }
        })
        let videos = res.data.house.videos.map((item) => {
          return {
            type: 'video',
            url: item
          }
        })
        this.focus = [...videos, ...imgs]
        if(res.data.house.vr){
          this.vr_num = 1
          this.focus.unshift({
            type: 'vr',
            url: res.data.house.vr
          })
        }else{
          this.vr_num = 0
        }
        if(res.data.house.huxing&&res.data.house.huxing.length>0){
          this.huxing_num = res.data.house.huxing.length
          let huxing = res.data.house.huxing.map((item) => {
            return {
              type: 'huxing',
              url: item
            }
          })
          this.focus = [...this.focus, ...huxing]
        }else{
          res.data.house.huxing = []
        }
        this.focusLen = this.focus.length
        this.cateActive = this.focus[0].type
        let link=''
        if (this.currentUserInfo.sid){
          link="https://"+window.location.host+"/h5/pages/ershou/detail?id="+this.id +"&isShare=1&shareType="+this.currentUserInfo.shareType+"&shareId="+this.currentUserInfo.sid
        }else {
          link = window.location.href
        }
        this.share = {
          title:res.data.house.title,
          content:res.data.house.shi+'室'+res.data.house.ting+'厅'+res.data.house.wei+'卫/'+res.data.house.mianji+'m²/'+res.data.house.fangjia+'万元',
          pic:res.data.house.img[0]||'',
          link:link
        }
        // this.getWxConfig()
        this.show =true
      })
    },
    formatDate(timeStr){
      let data = new Date(timeStr*1000)
      console.log(data);
      let year  = data.getFullYear()
      let month = data.getMonth()+1
      let day = data.getDate()
      return  year+'-'+month+'-'+day
    },
    confirm(type){
      let text ='',
      params ={id:this.id}
      if (type=='shenhe') {
        text ="确认通过审核吗？"
        params.info_level=1
        params.is_show=0
      }else if (type=='shangjia'){
        text ="确认通过审核并上架该信息吗？"
        params.info_level=1
        params.is_show=1
      }
      showModal({
        content:text,
        confirm: () => {
          this.confirmInfo(type,params)
        }
      })
    },
    confirmInfo(type,params){
      this.$ajax.get("release/setInfoState.html",params,res=>{
        uni.showToast({
          title:res.data.msg,
          icon:'none'
        })
        if (res.data.code==1){
          if (type=="shenhe"){
            this.audit ="已审核"
          }if(type =="shangjia"){
            this.shelves ="已上架"
          }
        }
      })
    },
    swiperChange(e) {
      this.swiperCurrent = e.detail.current
      this.cateActive = this.focus[this.swiperCurrent].type
    },
    switchFocus(type) {
      this.cateActive = type
      switch (type) {
        case 'vr':
          this.swiperCurrent = 0
          break;
        case 'video':
          this.swiperCurrent = this.vr_num
          break;
        case 'img':
          this.swiperCurrent = this.vr_num+this.video_num
          break;
        case 'huxing':
          this.swiperCurrent = this.vr_num+this.img_num + this.video_num
          break;
        default:
          this.swiperCurrent = 0
      }
    },
    // 获取地图附近周边
    getCovers(e, type = 2) {
      let params = {
        id: this.id,
        keywords: e?e.type:'',
        type: type
      }
      let api ='map/mapNearbyMatches.html'
      
      this.$ajax.get(
        api,
        params,
        res => {
          if (res.data.code != 1) {
            return
          }
         	if (!res.data.done&&this.moteNum <5 &&!e){
						this.moteNum ++
						this.getCovers(e,type)
						return 
					}
          let covers=[]
          res.data.matches.map(cover => {
            let icon,color,bgColor,title
            switch(cover.keyword)
						{
						case '商业':
							icon = '/static/icon/foot.png'
							bgColor= "#ffbabc"
							title="商"
							color="#fff"
							break
						case '教育':
							icon = '/static/icon/edu.png'
							title="教"
							bgColor="#34dec1"
							color="#fff"
							break
						case '医疗':
							icon = '/static/icon/yiliao.png'
							title="医"
							bgColor="#feb9bb"
							color="#fff"
							break
						case '交通':
							icon = '/static/icon/jiaotong.png'
							bgColor="#66d1fa"
							title ="交"
							color="#fff"
							break
						default:
							icon = '/static/icon/center.png'
						}
						if (cover.data&&cover.data.length) {
								cover.data.map(item=>{
                 let ob = {
                    width: 30,
                    height: 30,
                    iconPath: icon,
                    latitude: item.location.lat,
                    longitude: item.location.lng,
                    title: item.title,
                    address: item.address,
                    _distance: item._distance,
                    callout: {
                      content:  ((e && e.scale<=14) || !e)?title:item.title,
                      padding: 5,
											fontSize:10,
											boxShadow:'none',
											bgColor,
											color,
											borderRadius: 4,
											borderColor:bgColor,
											display:'ALWAYS'
                    },
                    distance: parseInt(item._distance)
                  }
                 	covers.push(ob)
									return item
                })
            }
            
						return cover
          })
          covers.push({
            latitude: this.detail.lat,
            longitude: this.detail.lng,
            width: 30,
            height: 30,
            iconPath: '/static/icon/center.png'
          })
          covers.push({
						latitude: this.oneKm.lat,
						id:"a"+1,
						longitude: this.oneKm.lon,
						width: -1,
						height:-1,
						label: {
							content:'1公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff0000",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5,
							borderColor:'#ffffff'
						},
						iconPath: '/static/icon/center.png'
					})
					covers.push({
						latitude: this.twoKm.lat,
						longitude: this.twoKm.lon,
						width: -1,
						height: -1,
						id:"a"+2,
						label: {
							content:'2公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#ff9c00",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
						},
						iconPath: '/static/icon/center.png'
					})
					covers.push({
						latitude: this.threeKm.lat,
						longitude: this.threeKm.lon,
						width: -1,
						height: -1,
						id:"a"+3,
						label: {
							content:'3公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#fee500",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
							},
						iconPath: '/static/icon/center.png'
					})
          this.mapData.covers = covers
        },
        err => {}
      )
    },

    showCopywriting(e,type){
      console.log(type);
      const query = uni.createSelectorQuery().in(this)
      query.select('#copy-text').fields({rect:true,scrollOffset:true,size:true},data => {
        this.text_popup_height = data.height+'px'
      }).exec();
      if (type){
        this.showTel =false
      }else {
        this.showTel =true
      }
      this.copy_success = false
      this.$refs.text_popup.show()
      this.$refs.share_popup.hide()
    },
			getShortLink(link){
        this.link = link||window.location.href
				this.$ajax.get("build/shortUrl.html",{page_url:this.link},(res)=>{
					if(res.data.code ==1){
						this.link=res.data.short_url
					}
				})
      },
      showLoginPopup(tip){
          this.login_tip = tip
          this.$refs.login_popup.showPopup()
      },
    copywriting(){
      let tel="",price='',maidian='',louceng=''
      if (this.detail.tel){
        tel = tel='【电话】'+this.detail.tel+'\n'
      }
      if(this.detail.parentid ==1||this.detail.pareneid==2){
        maidian =`【卖点】${this.detail.label.length>0?this.detail.label.map(item=>item.name).join(' ')+'\n':''}`
        louceng=`【楼层】${this.detail.floor?this.detail.floor+'/共'+this.detail.louceng+'层':'共'+this.detail.louceng+'层'}`
      }
      let link =window.location.origin +'/h5/pages/ershou/detail?id='+this.id
      if (this.detail.parentid == "2"){
        link =window.location.origin +'/h5/pages/renting/detail?id='+this.id
      }
      if (this.detail.parentid == "3"){
        link =window.location.origin +'/h5/needPage/rest_house/detail?id='+this.id
      }
      if (this.detail.parentid == "4"){
        link =window.location.origin +'/h5/needPage/buy_house/detail?id='+this.id
      }
      if (this.detail.parentid==1 ||this.detail.parentid == "4"){
        price = `【售价】${this.detail.fangjia?this.detail.fangjia+'万':'面议'}${this.detail.danjia?' 单价'+this.detail.danjia+'元/m²':''}`
      }else if (this.detail.parentid ==2 ||this.detail.parentid == "3"){
        price = `【租金】${this.detail.zujin?this.detail.zujin+'元/月':'面议'}`
      }
        const text = `${this.detail.title}
${this.housePrice&&this.housePrice.title?'【小区】'+this.housePrice.title+'\n':''}【户型】${this.detail.shi}室${this.detail.ting}厅${this.detail.wei}卫${this.detail.mianji}m²
${price}
【类型】${this.detail.catname}
${maidian}${louceng}
${this.showTel?tel:''}【查看】${link}`
        this.copyContent(text, ()=>{
          this.copy_success = true
        })
    },
    // #ifndef H5
    copyContent(cont) {
      uni.setClipboardData({
        data: cont,
        success: res => {
          // uni.showToast({
          //   title: "复制成功",
          //   icon: "none"
          // })
        }
      })
    },
    // #endif
    // #ifdef H5
    copyContent(cont, callback) {
      let oInput = document.createElement('textarea')
      oInput.value = cont
      document.body.appendChild(oInput)
      oInput.style.opacity = 0
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      oInput.blur()
      oInput.remove()
      if(callback) callback()
    },
    // #endif
    showWeituo(type){
      // if (type =="share"){
      //   this.isDetail=0
      // }else if (type =="bottom"){
      //   this.isDetail=1
      // }
      if (this.agent.shareId){
        this.isDetail=0
      }else {
        this.isDetail=1
      }
      this.$refs.enturst_popup.show()
    },
    handleTel() {

      if (this.detail.shixiao === 1) {
        uni.showToast({
          title: "此信息已失效",
          icon: 'none'
        })
        return false
      }

      var tel_params = []
      if(this.agent&&this.agent.shareId){
        tel_params = [
          this.agent.id,
          this.agent.id,
          this.agent.tel,
          "agent",
          6,
          "",
          0,
          1,
          ''
        ]
      }else{
        tel_params = [
          this.detail.uid,
          this.detail.uid,
          this.detail.tel,
          "agent",
          6,
          "",
          this.detail.id,
          1,
          ''
        ]
      }
      var replenish_params = {
        intercept_login: true,
        fail: (res)=>{
          switch(res.data.code){
            case -1:
              uni.removeStorageSync('token')
              this.reload = true
              this.$navigateTo('/user/login/login')
              break
            case 2:
              this.reload = true
              this.$navigateTo('/user/bind_phone/bind_phone')
              break
            case -5:
              showModal({
                title: "安全验证，防恶意骚扰已开启",
                content: "验证后可免费发布查看信息。",
                confirm: () => {
                  if (res.data.is_agent){
                    this.$navigateTo('/user/member_upgrade')
                  }else{
                    this.$navigateTo('/user/member_upgrade?is_personal=1')
                  }
                }
              })
              break
            case -10:
              console.log("账号被封禁")
              uni.showToast({
                title: res.data.msg,
                icon: 'none'
              })
              break
            default:
              uni.showToast({
                title: res.data.msg,
                icon: 'none'
              })
          }
        }
      }
      encryptionTel(...tel_params, replenish_params)
      // return
      // if (this.agent&&this.agent.shareId){  //转发过来的拨打转发者的电话
      //     // 如果有分享者信息则不要传信息id 传信息id的话会接口查找信息对应的手机号而不是分享者的手机号
      //     encryptionTel(this.agent.id, this.agent.id, this.agent.tel, "agent", 6,"",0)
      //     return  
      // }

      // // 如果是个人发布的，且开启了vip验证，且自己是vip身份，且开启了虚拟号功能则使用虚拟号拨打
      // if (this.detail.zhongjie == "1" && this.parameter.personal_vip_status == 1 && this.is_open_middle_num == 1 && this.parameter.is_vip === 1) { //可能有的客户数据库是数字类型有的是字符串类型
      //   encryptionTel(this.detail.uid , this.detail.uid,this.detail.tel, "agent", 6,"",this.detail.id)
      //   return
      // }
      // if(this.detail.zhongjie=='2'){ //如果是中介发布的房源可以直接拨打号码
      //   // this.onTel()
      //   encryptionTel(this.detail.uid , this.detail.uid, this.detail.tel, "agent", 6,"",this.detail.id)
      //   return
      // }
      // // 以下为个人发布的房源且没有开启中间号的情况
      // if (this.parameter.personal_vip_status === 1) {
      //   // 如果开启了个人vip验证则需要登录
      //   if (this.parameter.is_login === 1) { //如果已经登录
      //     if (this.parameter.isfrozen === 1) {
      //       uni.hideToast({
      //         title: "您的会员已到期或已锁定",
      //         icon: "none"
      //       })
      //     } else if (this.parameter.levelid > 1||this.parameter.is_vip === 1) {
      //       this.onTel()
      //     } else {
      //       showModal({
      //         title: "安全验证，防恶意骚扰已开启",
      //         content: "验证后可免费发布查看信息。",
      //         confirm: () =>{
      //           this.$navigateTo('/user/member_upgrade?is_personal=1')
      //         }
      //       })
      //     }
      //   } else if (this.parameter.is_login === -1) { //登录但是没绑定手机号
      //     this.reload = true
      //     // #ifdef H5 || APP-PLUS || MP-BAIDU
      //     this.$navigateTo('/user/login/login')
      //     // #endif
      //     // #ifdef MP-WEIXIN
      //     this.$navigateTo('/user/bind_phone/bind_phone')
      //     // #endif
      //   } else {
      //     this.reload = true
      //     this.$navigateTo('/user/login/login')
      //   }
      // } else { //没开启vip验证可以直接拨打电话
      //   this.onTel()
      // }
    },
    onTel() {
      encryptionTel(this.detail.uid , this.detail.uid, this.detail.tel, "agent", 6,"",this.detail.id)
    },
    preImg(index) {
      let img_index = index - this.detail.videos.length
      let img_urls = this.detail.img.map((item) => {
        return formatImg(item, 'w_860')
      })
      uni.previewImage({
        current: img_urls[img_index],
        indicator: "number",
        urls: img_urls
      })
    },
    preHuxing(index) {
      let img_index = index - (this.detail.videos.length + this.detail.img.length)
      let img_urls = this.detail.huxing.map((item) => {
        return formatImg(item, 'w_860')
      })
      uni.previewImage({
        current: img_urls[img_index],
        indicator: "number",
        urls: img_urls
      })
    },
    toVr() {
      this.$navigateTo('/vr/detail?infoid=' + this.id)
    },
    preVideo(url) {
      this.$navigateTo('/vr/preview_video?url=' + url)
    },
    toPosition() {
      if (this.detail.lat > 0 && this.detail.lng > 0) {
        uni.openLocation({
          latitude: parseFloat(this.detail.lat),
          longitude: parseFloat(this.detail.lng),
          name: this.detail.cmname,
          address: this.detail.address,
          success: function () {
            console.log('success');
          }
        });
      }
    },
    viewMap(e) {
      if (this.detail.lat > 0 && this.detail.lng > 0) {
        this.$navigateTo("/propertyData/map/map?id=" + this.detail.id + '&type=2&lat=' + this.detail.lat + '&lng=' + this.detail.lng)
      } else {
        uni.showToast({
          title: "未标记地图位置",
          icon: "none"
        })
      }
    },
    toJubao() {
      this.$navigateTo('/user/inform/inform?id=' + this.id + '&type=1')
    },
    // 将房源加入对比
    addContrast(info_id){
      this.$ajax.get('house/addContrast.html',{info_id},res=>{
        if(res.data.code === -1){
          this.$store.state.user_login_status = 1
          // 检测是否已添加
          if(this.$store.state.temp_ershou_contrast_ids.includes(info_id)){
            uni.showToast({
              title:"该房源已经添加",
              icon:'none'
            })
            return
          }
          this.$store.state.temp_ershou_contrast_ids.push(info_id)
          return
        }
        if(res.data.code === 1){
          uni.showToast({
            title:res.data.msg
          })
          this.contrastCount ++
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      },err=>{

      },{disableAutoHandle:true})
    },
    toContrast(){
      if(this.login_status>1){
        this.$navigateTo('/contrast/info_list?type=1')
      }else{
        this.$navigateTo(`/contrast/info_list?type=1&no_login=1`)
      }
    },
    showSharePop(){
      this.getShortLink()
      this.$refs.share_popup.show()
    },
    goBack(){
      if (this.sid){
        uni.switchTab({
            url: '/pages/index/index'
        });
      }else {
        this.$navigateBack()
      }
    },
    doNot() {

    },
    caiwuChange(e){
      this.genjinParams.is_income = e.detail.value
    },
    bindPickerLevelupTimeChange(e){
      console.log(e);
      this.groupParams.levelup_time =e.detail.value
    },
    subGenjin(){
      this.genjinParams.uid = this.memberInfo.id
      this.$ajax.post("infoAudit/memberFollow",this.genjinParams,res=>{
        if (res.data.code ==1){
          this.getData(this.id)
          this.genjinPopHide()
        }
        uni.showToast({
          title: res.data.msg,
          icon:"none"
        });
        
      })
    },
    genjinPopHide(){
      this.$refs.genjinPop.hide()
    },
    subFrozen(){
      this.frozenParams.id = this.memberInfo.id
      this.frozenParams.isfrozen  = this.memberInfo.isfrozen==1?0:1
      this.$ajax.post("infoAudit/setMemberFrozen",this.frozenParams,res=>{
        if (res.data.code ==1){
          this.getData(this.id)
          this.frozenPopHide()
        }
        uni.showToast({
          title: res.data.msg,
          icon:"none"
        });
      })
    },
    frozenPopHide(){
      this.$refs.frozenPop.hide()
    },
    subGroup(){
      this.groupParams.id = this.memberInfo.id
      // this.groupParams.isfrozen  = this.memberInfo.isfrozen==1?0:1
      this.$ajax.post("infoAudit/setMemberLevel",this.groupParams,res=>{
        if (res.data.code ==1){
          this.getData(this.id)
          this.groupPopHide()
        }
        uni.showToast({
          title: res.data.msg,
          icon:"none"
        });
      })
    },
    groupPopHide(){
      this.$refs.groupPop.hide()
    },
    subVip(){
      this.vipParams.id = this.memberInfo.id
      this.vipParams.is_vip = this.memberInfo.is_vip==1?0:1
      this.$ajax.post("infoAudit/setMemberVip",this.vipParams,res=>{
        if (res.data.code ==1){
          this.getData(this.id)
          this.vipPopHide()
        }
        uni.showToast({
          title: res.data.msg,
          icon:"none"
        });
      })
    },
    toHistory(){
      this.$navigateTo("/user/audit/history?uid="+this.detail.uid+"&id="+this.detail.id)
    },
    vipPopHide(){
      this.$refs.vipPop.hide()
    },
    inputPopHide(){
      this.$refs.inputPop.hide()
    },
    toDetail(){
      if (this.detail.info_level==0){
        uni.showToast({
          title: '当前信息状态为待审，请先设置信息状态为正常后再查看',
          icon:'none'
        });
        return 
      }
      if(this.detail.parentid==1){
        this.$navigateTo("/pages/ershou/detail?id="+this.detail.id)
      }
      if(this.detail.parentid==2){
        this.$navigateTo("/pages/renting/detail?id="+this.detail.id)
      }
      if(this.detail.parentid==3){
        this.$navigateTo("/needPage/rest_house/detail?id="+this.detail.id)
      }
      if(this.detail.parentid==4){
        this.$navigateTo("/needPage/buy_house/detail?id="+this.detail.id)
      }
    }

  },
  onNavigationBarButtonTap(option){
    if(option.index==0){
      // this.handleCreat()
      this.getShortLink()
      this.$refs.share_popup.show()
    }
    if(option.index==1){
      this.handleCollect()
    }
  }
}
</script>

<style scoped lang="scss">
.ershou_detail_content {
  padding-bottom: 160rpx;
  color: #333;
  background-color: #fff;
}

view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.back{
        position: fixed;
        width: 100%;
        height: 88rpx;
        padding: 2px 10rpx;
        align-items: center;
        justify-content: space-between;
        z-index: 1;
        .title-con{
          flex: 1;
          text-align: center;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 32rpx;
          // width: calc(100% - 64px);
          // over
        }
        .icon-box{
            // height: 44px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            justify-content: center;
            align-items: center;
            padding: 8px;
            background: rgba(0, 0, 0, 0.6);
            justify-content: center;
            align-items: center;
            &.icon-share{
              justify-self: end;
              margin-left: auto;
            }
            
        }
    }

// 顶部焦点图
.focus-box {
  position: relative;
  swiper.banner {
    height: 75vw;
  }
  .swiper-item {
    height: 100%;
  }
  .swiper-item image {
    width: 100%;
    height: 100%;
  }
  .swiper-item image.video-icon {
    width: 16vw;
    height: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0);
  }
  .number{
    position: absolute;
    padding: 4rpx 20rpx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    border-top-right-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    left: 0;
    bottom: 20rpx;
    color: #fff;
  }
  .icon-box{
      // height: 44px;
      position: absolute;
      top: 10rpx;
      right: 10rpx;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      justify-content: center;
      align-items: center;
      padding: 8px;
      background: rgba(0, 0, 0, 0.6);
      justify-content: center;
      align-items: center;
      &.icon-search{
        top:80rpx
      }
  }
  .img-total {
    position: absolute;
    padding: 4rpx 20rpx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    border-radius: 20rpx;
    right: 20rpx;
    bottom: 20rpx;
    color: #fff;
  }
  .cate-box {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 24rpx;
    display: block;
    text-align: center;
    font-size: 0;
    .cate-list {
      display: inline-block;
      border-radius: 6rpx;
      overflow: hidden;
    }
  }
  .cate {
    display: inline-block;
    padding: 8upx 20upx;
    font-size: 22rpx;
    background-color: #fff;
    &.active {
      background: linear-gradient(45deg, #fd9ea3, #fb656a);
      color: #fff;
    }
  }
}

.container {
  padding: 0 48rpx;
}

.block{
  margin-top: 24rpx;
  >.label{
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    line-height: 1;
    padding: 24rpx 0;
    margin-bottom: 24rpx;
    font-size: 40rpx;
    font-weight: bold;
    &.mgb0{
      margin-bottom: 0;
    }
    .more {
      padding: 8rpx;
      font-size: 22rpx;
      font-weight: initial;
      color: #999;
      &.pd-r-48{
        padding-right: 48rpx;
      }
    }
  }
}

// 房源标题
.house_title{
  font-size: 40rpx;
  line-height: 1.5;
  margin: 24rpx 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 主要信息
.main_info-box{
  align-items: center;
  justify-content: space-between;
  line-height: 1;
  .huxing{
    align-items:flex-end;
    line-height: 1;
    .value{
      font-size:36rpx;
      font-weight: bold;
      color: $uni-color-primary;
    }
    .unit{
      font-size:26rpx;
      margin-bottom: 2rpx;
      color: $uni-color-primary;
    }
  }
  .price{
    align-items:flex-end;
    margin-right: 24rpx;
  }
  .stw{
    align-items:flex-end;
    margin-right: 24rpx;
  }
  .mianji{
    align-items:flex-end;
    font-size: 22rpx;
    color: $uni-color-primary;
  }
  .btn{
    line-height: 64rpx;
    padding: 0 30rpx;
    color: #fff;
    background: $uni-color-primary;
    box-shadow: 0 2px 8px 0 rgba($uni-color-primary,0.40);
    border-radius: 32rpx;
  }
}

// 房源标签
.label-list{
  margin-top: 24rpx;
  .attr1 {
    padding: 4rpx 10rpx;
    margin-right: 10rpx;
    line-height: 1;
    font-size: 22rpx;
    background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
    color: #fff;
  }
  .attr2 {
    padding: 6rpx 10rpx;
    margin-right: 10rpx;
    line-height: 1;
    font-size: 22rpx;
    background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
    color: #fff;
  }
  .label {
      line-height: 1;
      padding: 4rpx 10rpx;
      font-size: 22rpx;
      color: #999;
      border: 0.5rpx solid #d8d8d8;
      // background: #f2f2f2;
      margin-left: 8rpx;
    }
}

// 其他信息
.info-list{
  flex-wrap: wrap;
  position: relative;
  .xiajia_icon{
    position: absolute;
    width: 180rpx;
    height: 180rpx;
    left: 0;
    right: 0;
    top: -32rpx;
    margin: auto;
  }
  .info-list-item{
    line-height: 1;
    margin-bottom: 48rpx;
    // padding: 10rpx 0;
    min-width: 30%;
    flex: 1;
    &.mgb0{
      margin-bottom: 0;
    }
    .label{
      font-size: 24rpx;
      margin-bottom: 24rpx;
      color: #999;
    }
    .data{
      font-size: 32rpx;
    }
  }
}

// 房源介绍
.house_desc{
  margin-top: 24rpx;
  .content_info{
    line-height:1.8;
    // min-height: 160rpx;
    .desc{
      margin-bottom: 36rpx;
      color: #666;
    }
    .cate{
      margin-bottom: 20rpx;
      line-height: 1;
      padding-left: 10rpx;
      border-left: 6rpx solid $uni-color-primary;
      font-weight: bold;
      color: #333;
    }
  }
  // 经纪人信息
  .agent-box{
    padding: 20rpx;
    justify-content: space-between;
    align-items: center;
    background-color: #f2f2f2;
    .header_img{
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    .agent_info{
      flex: 1;
      overflow: hidden;
      margin-right: 20rpx;
      .name{
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 32rpx;
        margin-bottom: 10rpx;
      }
      .shop_name{
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 22rpx;
        color: #999;
      }
    }
    .into-btn{
      height: 60rpx;
      line-height: 60rpx;
      padding: 0 24rpx;
      border-radius: 30rpx;
      font-size: 24rpx;
      color: #fff;
      background-color: $uni-color-primary;
    }
  }

  .selling_point{
    // padding: 10rpx 0;
    margin-top: 48rpx;
    .label{
      margin-bottom: 10rpx;
      color: #999;
    }
    .content_info{
      line-height: 1.5;
    }
    .view_more{
      margin: 10rpx 48rpx;
      padding: 20rpx;
      text-align: center;
      font-size: 30rpx;
      background-color: #fff4f4;
      color: $uni-color-primary;
    }
  }
}

// 房源标签
.wnsb-list{
  // margin-bottom: 24rpx;
  flex-wrap: wrap;
  .label {
      line-height: 1;
      padding: 4rpx 10rpx;
      font-size: 22rpx;
      color: #999;
      border: 0.5rpx solid #d8d8d8;
      // background: #f2f2f2;
      margin-left: 8rpx;
      margin-bottom: 24rpx;
    }
}

// 轮播广告图
.ext_swiper{
  margin-top: 48rpx;
  height: 140rpx;
  swiper-item{
    height: 100%;
    background-color: #f5f5f5;
    border-radius:16rpx;
    overflow: hidden;
    position: relative;
    >image{
      height: 100%;
      width: 100%;
    }
    .marker{
      line-height: 1;
      padding: 4rpx 10rpx;
      position: absolute;
      right: 12rpx;
      bottom: 10rpx;
      font-size: 20rpx;
      border-radius: 4rpx;
      background-color: rgba($color: #000000, $alpha: 0.5);
      color: #fff;
    }
  }
}

// 小区
.community-box{
  justify-content: space-between;
  >.label{
    justify-content: space-between;
    .into{
      font-weight: initial;
      color: #999;
    }
  }
}
.community{
  margin-bottom: 24rpx;
  .img{
    width: 140rpx;
    height: 140rpx;
    margin-right: 20rpx;
    border-radius: 8rpx;
  }
  >.info{
    flex: 1;
    justify-content: space-between;
    line-height: 1;
    overflow: hidden;
    .price_row{
      justify-content:space-between;
      .into{
        line-height: 1;
        font-size: 22rpx;
        align-items: center;
        color: #999;
      }
    }
    .label{
      display: inline-block;
      text-align-last:justify;
      min-width: 120rpx;
      margin-right: 15rpx;
      font-size: 28rpx;
      font-weight: initial;
      color: #999;
    }
    .value{
      flex: 1;
      font-size: 28rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .price{
      line-height: 1;
      align-items: center;
      font-size: 28rpx;
      font-weight: bold;
      color: $uni-color-primary;
      .unit{
        margin-left: 8rpx;
        font-size: 22rpx;
        font-weight: initial;
        color: #333;
      }
    }
    .increase{
      align-items: center;
      .increase-text{
        align-items:center;
        flex-direction:row
      }
      .ping{
        font-size: 28rpx;
      }
      .value{
        font-size: 28rpx;
        font-weight: bold;
        color: $uni-color-primary;
        &.down{
          color: #179B16;
        }
      }
      .unit{
        position: relative;
        // top: 5rpx;
        left: 5rpx;
        color: $uni-color-primary;
        &.down{
          color: #179B16;
        }
      }
    }
    .yaers{
      align-items: center;
    }
  }
}

// 参考月贷
.card {
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 28rpx;
  box-sizing: border-box;
  overflow: hidden;
  .tab-box {
    justify-content: space-between;
    margin-bottom: 10rpx;
    background-image: url('https://images.tengfangyun.com/images/new_icon/fd_tab.png');
    background-size: 100%;
    background-position-y: -24rpx;
    &.right{
      background-image: url('https://images.tengfangyun.com/images/new_icon/fd_tab2.png');
    }
    .tab {
      flex: 1;
      text-align: center;
      padding: 20rpx;
      color: #999;
      &.active {
        // background-color: #fff3f3;
        color: #333;
      }
    }
  }
}

// TA的其他房源

.other_house-list {
  height: 50vw;
  .swiper-item {
    margin-right: 24rpx;
    .img-box {
      border-radius: 8rpx;
      overflow: hidden;
      height: 200rpx;
    }
    image {
      width: 100%;
      height: 100%;
    }
    .house_type {
      display: block;
      margin-top: 10rpx;
      font-size: 32rpx;
      font-weight: bold;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .aligin-end {
      align-items: flex-end;
    }
    .stw {
      align-items: center;
      margin-top: 6rpx;
      margin-right: 10rpx;
      font-size: 24rpx;
      color: #999;
    }
    .price-box {
      line-height: 1;
      align-items: flex-center;
      justify-content: space-between;
      margin-top: 10rpx;
      .db_btn{
        line-height: 30rpx;
        padding: 0 8rpx;
        border-radius: 15rpx;
        font-size: 22rpx;
        border: 1rpx solid $uni-color-primary;
        color: $uni-color-primary;
      }
    }
    .mianji{
      font-size: 22rpx;
      color: #999;
    }
    .price {
      font-size: 32rpx;
      font-weight: bold;
      color: $uni-color-primary;
    }
    .unit{
      margin-left: 8rpx;
    }
  }
}

.more_btn{
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin: 0 48rpx;
  padding: 0 24rpx;
  height: 80rpx;
  border-radius: 8rpx;
  color: $uni-color-primary;
  background-color: rgba($uni-color-primary, 0.1);
  &.no_bg{
    background-color: rgba($uni-color-primary, 0);
  }
  &.has_agent{
    background-color: rgba($uni-color-primary, 0);
    color: #333;
  }
}
.footer{
  position: fixed;
  z-index: 11;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 48rpx;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  .footer_btn_read{
    font-size: 32rpx;
  }
  .footer_btn{
    // width: calc( 100% - 96rpx);
    // margin: 0 auto;
    // flex: 1;
    // height: 80rpx;
    // line-height: 50rpx;
    // font-size: 30rpx;
    // text-align: center;
    // padding: 15rpx 0;
    // // color:#ff656b;
    // // border: 2rpx solid #ff656b;
    // // border-radius: 10rpx;
    // color: #fff;
    // margin-left: 48rpx;
    // background: linear-gradient(90deg,#ffa857,#ff6069);
    // box-shadow: 0 3px 6px 0 rgb(255 109 102 / 30%);
    // background: #ff656b;
    // border-radius:80rpx;
      width: 280rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 32rpx;
      color: #fff;
      border-radius:40rpx;
      background: -webkit-linear-gradient(left,#ffa857,#ff6069);
      background: linear-gradient(90deg,#ffa857,#ff6069);
      box-shadow: 0 6rpx 12rpx 0 rgba(255, 109, 102, 0.3);
    &.shangjia{
      width: 220rpx;
      background: linear-gradient(135deg,#4daaff,#2671ff);
      box-shadow: 0 6rpx 12rpx 0 rgba(38,113,255,30%)
    }
    
  }
}
// 底部操作菜单
.bottom-bar {
  background-color: #fff;
  height: 110rpx;
  padding: 15rpx 48rpx;
  left: 0;
  z-index: 10;
  .bar-left{
    padding-right: 28rpx;
    justify-content: space-between;
  }
  .icon-btn {
    // width: 100rpx;
    align-items: center;
    padding: 0;
    margin: 0;
    background-color: #fff;
    line-height: initial;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // flex: 1;
    max-width: 130rpx;
    padding-right: 32rpx;
    overflow: hidden;
    position: relative;
    &.last{
      padding-right: 48rpx;
    }
    // & ~ .icon-btn {
    //   margin-left: 24rpx;
    // }
    .header_img{
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
    }
    .cname{
      overflow: hidden;
      white-space: nowrap;
      display:block;
      width: 96rpx;
    }
    text {
      line-height: 1;
      font-size: 22rpx;
      color: #999;
      display: inline-block;
      width: 100%;
      text-align: center;
      overflow: hidden;
      // text-overflow: ellipsis;
      white-space: nowrap;
    }
    .badge{
      display: inline-block;
      box-sizing: border-box;
      width: auto;
      position: absolute;
      top: 0;
      left: 32rpx;
      // right: 38rpx;
      height: 28rpx;
      padding: 0 8rpx;
      min-width: 28rpx;
      border-radius: 14rpx;
      font-size: 22rpx;
      background-color: $uni-color-primary;
      color: #fff;
    }
  }
  .bar-btn {
    // width: 220rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    padding: 0;
    margin: 0;
    border-radius: 0;
    color: #fff;
    &.alone{
      border-radius: 40rpx;
    }
    &.btn1 {
      background: #FBAC65;
      box-shadow: 0 0 4px 0 rgba(0,0,0,0.05);
      border-top-left-radius: 40rpx;
      border-bottom-left-radius: 40rpx;
    }
    &.btn2 {
      background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
      box-shadow: 0 0 4px 0 rgba(255,80,0,0.30);
      border-top-right-radius: 40rpx;
      border-bottom-right-radius: 40rpx;
    }
  }
}










// 免责声明
.shengming {
  margin-top: 32rpx;
  color: #999;
  .shengming_title {
    font-size: 30rpx;
    margin-bottom: 16rpx;
    justify-content: space-between;
    align-items: center;
    .label{
      line-height: 1;
      padding: 4rpx 8rpx;
      font-size: 22rpx;
      border: 1rpx solid #d8d8d8;
    }
  }
  .shengming_content {
    font-size: 26rpx;
    line-height: 1.8;
  }
}

// 复制文案
.copy-text-box{
  padding: 24rpx 32rpx;
  background-color: #fff;
  width: 600rpx;
  // height: 100%;
  margin-left: 75rpx;
  border-radius: 16rpx;
  .title{
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }
  .info-row{
    line-height: 1.6;
    color: #333;
    .label{
      color: #999;
    }
    .value{
      flex: 1;
      &.highlight{
        color: $uni-color-primary;
      }
    }
  }
  .button{
    align-items: center;
    justify-content: center;
    line-height: 64rpx;
    width: 236rpx;
    text-align: center;
    border-radius: 32rpx;
    margin: 32rpx auto;
    background: #FB656A;
    box-shadow: 0 2px 8px 0 rgba(251,101,106,0.40);
    color: #fff;
  }
  .disabled-btn{
    background-color: #f5f5f5;
    box-shadow: none;
    color: #999;
    >.text{
      margin-left: 12rpx;
    }
  }
}

// 报名按钮
.btn_list-box {
  margin-top: 32rpx;
  .btn-item {
    padding: 20rpx 5rpx;
    flex: 1;
    align-items: center;
    justify-content: center;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.05);
    color: $uni-color-primary;
    ~ .btn-item {
      margin-left: 14rpx;
    }
    text {
      font-size: 32rpx;
      margin-left: 16rpx;
    }
    .img{
      width: 40rpx;
      height: 40rpx;
      overflow: hidden;
      .img_c{
        width: 100%;
        height: 100%;
      }
    }
  }
}

// 房源动态
.house_news {
  .house_news_info{
    padding: 20rpx 0;
    margin-left: 20rpx;
    margin-right: 20rpx;
    &_item{
      flex: 1;
      align-items: center;
      &_num{
        margin-bottom: 20rpx;
      }
    }
  }
}

.time_line{
    padding: 20upx 30upx;
    margin-top: 40rpx;
    .item{
        position: relative;
        padding: 0 20upx 36upx 32upx;
        border-left: 2rpx solid #f3f3f3;
        &:last-child{
          border-left: 0;
        }
        .line-item{
          margin-top: -20rpx;
          .content_c{
            justify-content: space-between;
            align-items: center;
            .content_sub{
              color: #fb656a;
            }
            .blod{
              color: #ff656b;
              font-weight: 600;
            }
          }
          .line-header{
            margin-top: 15rpx;
          }
        }
        .title{
            font-size: 28upx;
            margin-top: -10rpx;
            line-height: 1.5;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            display: -webkit-box;
        }
        .time{
            font-size: 24upx;
            font-weight: bold;
            color: #999;
        }
        
        
    }
    .item::after{
        content: "";
        height:12upx;
        width: 12upx;
        box-sizing: border-box;
        border-radius: 50%;
        position: absolute;
        // border: 4rpx solid #f3f3f3;
        background-color: #f3f3f3;
        left: -6rpx;
        top: -6rpx;
    }
    .item.current::before{
        content: "";
        height:20upx;
        width: 20upx;
        border-radius: 50%;
        background-color: #3399ff;
        position: absolute;
        left: -12upx;
        top: 0;
        z-index: 2;
    }
}
.entrant_button{
  padding: 20rpx 5rpx;
  width: 80%;
  margin: 20rpx auto 0;
  background-color: rgba(255, 101, 107, 0.05);
  color: #ff656b;
  text-align: center;
  font-size: 30rpx;
}
	.info_detail{
		align-items: flex-end;
		line-height: 1;
		padding: 24rpx 0;
		.header_img{
			width: 64rpx;
			height: 64rpx;
			margin-right: 16rpx;
			border-radius: 50%;
			background-color: #f5f5f5;
		}
		.center{
			font-size: 22rpx;
			.user_name{
				margin-bottom: 16rpx;
			}
			.detail_bottom{
				align-items: center;
				color: #999;
				>text{
					padding: 0 16rpx;
					&.time{
						padding-left: 0;
					}
					&.area{
						border-right: 1rpx solid #999;
					}
				}
			}
		}

		.info_price{
			font-size: 32rpx;
			font-weight: bold;
			color: $uni-color-primary;
		}
	}

	.map_box{
		margin-top: 48rpx;
		.label{
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 16rpx;
		}
		.map{
			height: 340rpx;
			width: 100%;
		}
	}
  .desc_box{
		margin-top: 48rpx;
		position: relative;
		.xiajia_icon{
			position: absolute;
			width: 180rpx;
			height: 180rpx;
			left: 0;
			right: 0;
			top: -32rpx;
			margin: auto;
		}
		.label{
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 16rpx;
		}
		.desc{
			line-height: 1.8;
			min-height: 160rpx;
		}
	}
  .form{
    background: #fff;
    width: 80vw;
    padding: 60rpx 0;
    margin: 0 auto;
    border-radius: 20rpx;
    .form_item{
      align-items: center;
      padding: 24rpx 48rpx;
      .item_inp {
        text-align: right;
        .item_radio {
          justify-content: flex-end;
          .item_radio_item {
            margin-right: 10rpx;
          }
        }
      }
    }
    .btns {
      justify-content: space-between;
      align-items: center;
      padding:24rpx 48rpx;
      .genjin_btn {
        padding: 12rpx 24rpx;
        background: #ff656b;
        color: #fff;
        flex:1;
        text-align: center;
        border-radius: 40rpx;
        &.cancel{
          background: #fff;
          color: #999;

        }
      }
    }
  }
</style>