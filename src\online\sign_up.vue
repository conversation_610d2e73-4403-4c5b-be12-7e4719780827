<template>
<view class="sign_up">
    <view class="row" v-if="coupon&&coupon.id">
        <view class="activity flex-box">
            <view class="act_left flex-1">
                <view class="title">{{coupon.discount||coupon.name}}</view>
            </view>
            <view class="act_right" @click="navigate('/online/choose?online_id='+params.online_id+'&build_id='+params.build_id)">
            <view class="xuanfang">选房大厅</view>
            <view class="countdown flex-box">
                    <view class="label">
                        <text>{{coupon.is_start==0?"距离开始":(coupon.is_start==1?"距离结束":"已结束")}}</text>
                    </view>
                    <view class="time-box">
                        <text class="time">{{countdown.day}}</text>
                        <text>天：</text>
                    </view>
                    <view class="time-box">
                        <text class="time">{{countdown.hours}}</text>
                        <text>时</text>
                    </view>
            </view>
            </view>
        </view>
    </view>
    <form @submit="formSubmit" @reset="formReset">
        <!-- <view class="row label-row bottom-line">{{title}}{{leixing}}</view> -->
        <view class="row form-row flex-box">
            <view class="label">姓名</view>
            <input name="uname" maxlength="10" placeholder="请输入姓名" />
        </view>
        <view class="row form-row flex-box">
            <view class="label">手机号</view>
            <input name="tel" type="number" maxlength="11" placeholder="请输入手机号" />
        </view>
        <view class="row form-row flex-box">
            <view class="label">身份证号</view>
            <input name="id_card" type="id_card" placeholder="请输入身份证号" />
        </view>
        <view class="btn-box">
            <button formType="submit" class="default">确认提交</button>
        </view>
        <view class="text-center" style="font-size: 24upx;color: #999;">
            <text>您的信息将被严格保密，请准确填写</text>
            <text class="xuzhi" @click="toNotice()">选房须知</text>
        </view>

        <!-- <view class="info_list bottom-line">
            <view class="row_title bottom-line">
                <text>订单记录</text>
            </view>
            <view class="row w-100 bottom-line" v-for="(item, index) in bullet_list" :key="index">
                <text class="order_log">{{item.name}}{{item.content}}</text>
            </view>
        </view> -->
        <view class="bullet-box" v-if ="bullet_list.length>0"  :style ="{height:height}">
            <swiper class="banner" :indicator-dots="false" :interval="2000" :circular="true" :vertical="true" :autoplay= "true" :duration="600" indicator-active-color="#f65354" :display-multiple-items="count"  :current="swiperCurrent">
                <swiper-item v-for="(item,index) in bullet_list" :key="index">
                        <view class="con flex-box" id="con">
                            <image :src= 'item.prelogo | imgUrl("w_220")' mode = "widthFix"></image>
                            <view class="name">{{item.name}}</view>
                            <view class="con_">{{item.content}}</view>
                        </view>
                </swiper-item>
                <swiper-item v-if= "bullet_list.length<count">
                </swiper-item>
            </swiper>
        </view>

    </form>
    <view class="bottom-menu flex-box">
        <view class="bar-item" @click="navigate('/online/detail?id='+params.online_id)">
            <my-icon type="home" size="22"></my-icon>
            <view class="text">大厅</view>
        </view>
        <view class="bar-item" @click="navigate('/online/choose?online_id='+params.online_id+'&build_id='+params.build_id)">
            <my-icon type="jiudian" size="22"></my-icon>
            <view class="text">选房</view>
        </view>
        <view class="bar-item" @click="navigate('/online/adviser?online_id='+params.online_id)">
            <my-icon type="xiaoxi" size="22"></my-icon>
            <view class="text">咨询</view>
        </view>
        <view class="bar-item">
            <my-icon type="shikebiao" size="22" @click="navigate('/online/my?online_id='+params.online_id+'&build_id='+params.build_id)"></my-icon>
            <view class="text">订单</view>
        </view>
    </view>
</view>
</template>

<script>
import myIcon from "../components/icon.vue"
import {
    navigateTo,
    formatImg,
} from '../common/index.js'
import {wxShare} from '../common/mixin'
import {mapState, mapMutations} from 'vuex'
export default {
    data() {
        return {
            bm_num:'',
            params:{
                
            },
            height:'',
            count:5,
            swiperCurrent:0,
            bullet_list:[],
            coupon:{}, // 优惠券
            countdown:{
                day:0,
                hours:0
            }
        }
    },
    computed: {
        ...mapState(['online_inviter'])
    },
    mixins:[wxShare],
    components: {
        myIcon,
    },
    onLoad(options){
        if(options.online_id){
            this.params.online_id = options.online_id
            this.params.build_id = options.build_id || ''
            if(options.inviter_id){
                this.setOnlineInviter({
                    inviter_id: options.inviter_id,
                    online_id: this.params.online_id
                })
            }
            if(this.params.online_id===this.online_inviter.online_id){
                this.params.inviter_id = this.online_inviter.inviter_id
            }
            this.getData()
        }
        uni.$on("getDataAgain",this.getData)
    },
    onUnload(){
        uni.$off("getDataAgain")
        this.$store.state.allowOpen = true
    },
    filters:{
        imgUrl(val,param = 'w_8601'){
            if(!val){
                return ""
            }
            return formatImg(val,param)
        }
    },
    methods: {
        ...mapMutations(['setOnlineInviter']),
        getData(){
            this.share = {}
            this.$ajax.get('Marketing/booking',{online_id:this.params.online_id},res=>{
                this.my_id = res.data.uid || 0 //记录自己的id，分享的时候需要携带此参数
                if(res.data.share&&res.data.share.title){
                    res.data.share.link=`${window.location.origin}/h5/online/sign_up?online_id=${this.params.online_id}&inviter_id=${this.my_id}`
                    this.share = res.data.share
                    this.getWxConfig()
                }
                let bullet_list=[]
                if(res.data.bubbles&&res.data.bubbles.length>0){
                    bullet_list = res.data.bubbles
                    //   bullet_list= bullet_list.slice(0,5)  // 调试改变数据
                    let len = bullet_list.length
                    if(len>1&&len<=this.count){
                        //动态改变高度
                        this.height =uni.upx2px((len-1)*90)+"px"
                        //比实际数量少显示一个
                        this.count =len-1
                    }else if (len==1){
                        this.height = uni.upx2px((len)*90)+"px"
                        this.count =1
                    }else {
                        this.height = uni.upx2px(this.count*90)+"px"
                    }
                    this.bullet_list =bullet_list
                }
                if(res.data.coupon){
                    this.coupon = res.data.coupon
                    if(res.data.coupon.is_start==0){ //未开始
                        this.countDown(res.data.coupon.start_time-Date.parse(new Date())/1000)
                    }else if(res.data.coupon.is_start==1){ //进行中
                        this.countDown(res.data.coupon.end_time-Date.parse(new Date())/1000)
                    }else{ // 已结束
                        
                    }
                }
            })
        },
        countDown(remaining_time){
            if(remaining_time<=0){
                return
            }
            this.countdown.day = parseInt(remaining_time/86400)
            let hours_str = remaining_time%86400
            this.countdown.hours = parseInt(hours_str/3600)
            let minutes_str = hours_str%3600
            this.countdown.minutes = parseInt(minutes_str/60)<10?"0"+parseInt(minutes_str/60):parseInt(minutes_str/60)
            this.countdown.seconds = minutes_str%60<10?"0"+minutes_str%60:minutes_str%60
            this.timer = setTimeout(()=>{
                remaining_time--
                this.countDown(remaining_time)
            },1000)
        },
        formSubmit(e){
            if(e.detail.value.tel.length<11||e.detail.value.tel[0]!=1){
                uni.showToast({
                    title:"手机号格式不正确",
                    icon:"none"
                })
                return
            }
            let id_card_reg = /(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/
            if(e.detail.value.id_card&&!id_card_reg.test(e.detail.value.id_card)){
                uni.showToast({
                    title:"身份证号格式不正确",
                    icon:"none"
                })
                return false
            }
            let params = Object.assign(this.params,e.detail.value)
            this.$ajax.post('Marketing/booking',params,res=>{
                if(res.data.code == 1){
                    uni.showToast({
                        title:res.data.msg
                    })
                    if(getCurrentPages().length>1){
                        setTimeout(()=>{
                            uni.navigateBack()
                        },1500)
                    }else{
                        navigateTo(`/online/detail?id=${this.params.online_id}`)
                    }
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            })
        },
        toNotice(){
            navigateTo(`/online/notice?online_id=${this.params.online_id}`)
        },
        navigate(url){
            navigateTo(url)
        }
    },
}
</script>

<style scoped lang="scss">
.label-row{
    padding-left: 34upx;
    font-size: 26upx;
    color: #999999
}
.row_title{
    padding: 20rpx 20rpx 20rpx 40rpx;
    font-size: 32rpx;
    position: relative;
    &::before{
        content: "";
        position: absolute;
        left:20rpx;
        top:20rpx;
        bottom:20rpx;
        width: 6rpx;
        background-color: #f65354
    }
}

.info_list {
    margin-top: 30rpx;
    flex-wrap: wrap;
    margin-bottom: 20rpx;
    background-color: #fff;
    .w-100 {
        width: 100%;
        padding-top: 20rpx;
        padding-bottom: 20rpx;
        box-sizing: border-box;
        .label{
            display: inline-block;
            min-width: 140rpx;
        }
    }
}

.bottom-menu{
    position: fixed;
    width: 100%;
    padding: 8rpx 0;
    box-sizing: border-box;
    bottom: 0;
    background-color: #fff;
    border-top: 1rpx solid #dedede;
    .bar-item{
        line-height: 1;
        flex: 1;
        text-align: center;
        color: #333;
        .text{
            margin-top: 8rpx;
        }
        &.active{
            color:  $uni-color-primary;
        }
    }
}



.activity{
    padding: 24rpx 20rpx;
    margin:10rpx 0;
    border-radius: 10rpx;
    background: linear-gradient(to right,#ee1140, #fd7128);
    color: #fff;
    display: flex;
    justify-content: space-between;
    .act_left{
        margin-right: 10rpx;
    }
    .title{
        margin-right: 10rpx;
        font-size: 30rpx;
        line-height: 1.6;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
    }
    .act_right{
        text-align: right;
    }
    .xuanfang{
        padding: 8upx 20upx;
        border-radius: 24upx;
        margin-bottom: 15rpx;
        background-color: #fff;
        color: $uni-color-primary;
        display: inline-block
    }
    .countdown{
        .label{
            padding: 0;
            margin: 0;
            color: #fff;
            font-size: 26rpx;
        }
        .time-box{
            font-size: 26rpx;
            .time{
                margin: 0 10rpx;
                padding: 0 10rpx;
                border-radius: 5rpx;
                background: #ffff33;
                color: $uni-color-primary;
            }
        }
    }
}
.xuzhi{
    padding: 22rpx;
    color:#f65354;
}

.bullet-box{
    width: 95%;
    margin: 100upx auto;
    max-height: 400upx;
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background: #fff;
   
}
.bullet-box swiper {
    height:100%;
    width: 100%;
    padding:15rpx;
}
.bullet-box .con{
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20upx;
    padding: 20upx 0;
}
.bullet-box .con .name{
    width: 120upx;
    margin-left: 20upx;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 24upx;
    white-space: nowrap;
}
.bullet-box .con .con_{
    font-size: 24upx;
    flex: 1;
    white-space: nowrap;
    overflow : hidden;
    text-overflow: ellipsis;
   
}
.bullet-box image{
    width: 50upx;
    height: 50upx;
    border-radius: 50upx;
}

</style>
