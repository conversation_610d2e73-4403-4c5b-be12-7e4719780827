<template>
  <view class="estate-item" @click="$emit('click', { type: 'estate', detail: itemData })">
    <view class="estate-box">
      <view class="img-box">
        <view class="level-box">
          <text class="level level2" v-if="itemData.info_level === 2">精选</text>
        </view>
        <image class="img" :src="itemData.img | imgUrl" lazy-load mode="aspectFill"></image>
        <image v-if="itemData.is_vr || itemData.vr" class="video-icon" src="/static/icon/vr.png"></image>
        <image v-else-if="itemData.is_video == 1" class="video-icon" src="/static/icon/video.png"></image>
        <view v-if="itemData.parentid == 1" class="img-label img-label-sale">出售</view>
        <view v-if="itemData.parentid == 2" class="img-label img-label-rent">出租</view>
        <view v-if="itemData.parentid == 3" class="img-label img-label-transfer">转让</view>
      </view>
      <view class="info">
        <view class="title_box">
          <view class="title">
            <text v-if="itemData.upgrade_type == 2" class="ding">顶</text>
            <text :class="{
              red: itemData.ifred,
              bold: itemData.ifbold,
            }">{{ itemData.title }}</text>
          </view>
        </view>
        
        <!-- 基本信息 -->
        <view class="center-info">
          <text class="area-info">{{ itemData.areaname }}</text>
          <text class="jiange" v-if="itemData.mianji">|</text>
          <text class="mj" v-if="itemData.mianji">{{ itemData.mianji }}{{ itemData.mianji_unit || 'm²' }}</text>
          <text class="jiange" v-if="itemData.catname">|</text>
          <text class="type">{{ itemData.catname }}</text>
        </view>
        
        <!-- 标签 -->
        <view class="labels" v-if="itemData.label && itemData.label.length > 0">
          <text class="label" :style="{ color: label.color, borderColor: label.color }"
            v-for="(label, index) in itemData.label" :key="index">{{ label.name }}</text>
        </view>
        
        <!-- 价格信息 -->
        <view class="bottom-info flex-box">
          <view class="bottom-left">
            <!-- 出售 -->
            <template v-if="itemData.parentid == 1">
              <text class="mianyi" v-if="itemData.fangjia == '面议' || itemData.fangjia == '0' || !itemData.fangjia">面议</text>
              <text class="price" v-else>{{ itemData.fangjia }}</text>
              <text class="price-unit" v-if="itemData.fangjia !== '面议' && itemData.fangjia != '0' && itemData.fangjia">{{ itemData.price_unit || '万' }}</text>
              <text class="average_price" v-if="itemData.danjia">{{ itemData.danjia }}{{ itemData.danjia_unit || '元/m²' }}</text>
            </template>
            <!-- 出租 -->
            <template v-if="itemData.parentid == 2">
              <text class="mianyi" v-if="itemData.zujin == '面议' || itemData.zujin == '0' || !itemData.zujin">面议</text>
              <text class="price" v-else>{{ itemData.zujin }}</text>
              <text class="price-unit" v-if="itemData.zujin !== '面议' && itemData.zujin != '0' && itemData.zujin">元/月</text>
            </template>
            <!-- 转让 -->
            <template v-if="itemData.parentid == 3">
              <text class="mianyi" v-if="itemData.fangjia == '面议' || itemData.fangjia == '0' || !itemData.fangjia">面议</text>
              <text class="price" v-else>{{ itemData.fangjia }}</text>
              <text class="price-unit" v-if="itemData.fangjia !== '面议' && itemData.fangjia != '0' && itemData.fangjia">{{ itemData.price_unit || '万' }}</text>
              <text class="transfer-fee" v-if="itemData.transfer_fee">转让费{{ itemData.transfer_fee }}万</text>
            </template>
          </view>
          <view class="bottom-right">
            <text class="u-time">{{ itemData.begintime }}</text>
          </view>
        </view>
        
        <!-- 经纪人信息 -->
        <view class="agent_info flex-row" v-if="itemData.levelid > 1">
          <image class="header_img" :src="itemData.prelogo | imageFilter('w_80')"></image>
          <text class="c_name">{{ itemData.cname }}</text>
          <text class="b_name flex-1">{{ itemData.tname }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { formatImg } from "../../common/index.js";

export default {
  name: 'EstateItem',
  props: {
    itemData: {
      type: Object,
      required: true
    }
  },
  filters: {
    imgUrl(val) {
      return formatImg(val, "w_240");
    },
    imageFilter(val, param = 'w_80') {
      return formatImg(val, param);
    }
  }
}
</script>

<style scoped lang="scss">
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.estate-item {
  padding: 24rpx 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .estate-box {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
  }
  
  .img-box {
    width: 204rpx;
    height: 172rpx;
    margin-right: 16rpx;
    position: relative;
    border-radius: 8rpx;
    overflow: hidden;
    
    .img {
      width: 100%;
      height: 100%;
    }
    
    .level-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;
      
      .level {
        display: block;
        margin-bottom: 5rpx;
        padding: 2rpx 10rpx;
        font-size: 22rpx;
        border-bottom-left-radius: 20rpx;
        color: #fff;
        
        &.level2 {
          background: linear-gradient(135deg, #69D4BB 0%, #00CAA7 100%);
        }
      }
    }
    
    .video-icon {
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      left: 20rpx;
      bottom: 20rpx;
    }
    
    .img-label {
      position: absolute;
      top: 8rpx;
      left: 8rpx;
      padding: 4rpx 8rpx;
      font-size: 20rpx;
      color: #fff;
      border-radius: 4rpx;
      
      &.img-label-sale {
        background: #f65354;
      }
      
      &.img-label-rent {
        background: #1890ff;
      }
      
      &.img-label-transfer {
        background: #52c41a;
      }
    }
  }
  
  .info {
    flex: 1;
    overflow: hidden;
    
    .title_box {
      margin-bottom: 12rpx;
      
      .title {
        font-size: 32rpx;
        line-height: 1.4;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        
        .ding {
          display: inline-block;
          padding: 2rpx 8rpx;
          margin-right: 8rpx;
          font-size: 20rpx;
          background: #f65354;
          color: #fff;
          border-radius: 4rpx;
        }
        
        .red {
          color: #f65354;
        }
        
        .bold {
          font-weight: bold;
        }
      }
    }
    
    .center-info {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 12rpx;
      
      .area-info {
        margin-right: 8rpx;
      }
      
      .jiange {
        margin: 0 8rpx;
        color: #ddd;
      }
      
      .mj, .type {
        margin-right: 8rpx;
      }
    }
    
    .labels {
      margin-bottom: 12rpx;
      
      .label {
        display: inline-block;
        padding: 4rpx 8rpx;
        margin-right: 8rpx;
        margin-bottom: 8rpx;
        font-size: 22rpx;
        border: 1rpx solid;
        border-radius: 4rpx;
      }
    }
    
    .bottom-info {
      margin-bottom: 12rpx;
      
      .bottom-left {
        .mianyi {
          font-size: 32rpx;
          color: #f65354;
          font-weight: 600;
        }
        
        .price {
          font-size: 32rpx;
          color: #f65354;
          font-weight: 600;
        }
        
        .price-unit {
          font-size: 26rpx;
          color: #f65354;
          margin-left: 4rpx;
        }
        
        .average_price {
          font-size: 24rpx;
          color: #999;
          margin-left: 16rpx;
        }
        
        .transfer-fee {
          font-size: 24rpx;
          color: #52c41a;
          margin-left: 16rpx;
        }
      }
      
      .bottom-right {
        .u-time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
    
    .agent_info {
      align-items: center;
      
      .header_img {
        width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
        background-color: #f5f5f5;
      }
      
      .c_name,
      .b_name {
        margin-left: 16rpx;
        font-size: 24rpx;
        color: #666;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
</style> 