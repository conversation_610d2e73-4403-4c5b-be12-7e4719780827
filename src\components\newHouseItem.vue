<template>
  <view class="house flex-row" :class="{'bottom-line':(mode===1&&from!='find_house'|| (mode==1&&from=='find_house'&&showLine))}" @click="$emit('click',{type:'new_house',detail:itemData})">
    <view class="img-box">
      <view class="level-box">
        <!-- <text class="level level1" v-if="itemData.isvip">精选</text> -->
        <!-- <text class="level level2" v-if="itemData.isgood">优惠</text> -->
      </view>
      <image class="img" :src="itemData.img | imageFilter('w_240')" lazy-load mode="aspectFill"></image>
      <image v-if="itemData.is_vr==1||itemData.quanjing" class="video-icon" :class="{center:mode===2}" src="/static/icon/vr.png"></image>
      <image v-else-if="itemData.is_video==1" class="video-icon" :class="{center:mode===2}" src="/static/icon/video.png"></image>
      <text class="area_name" v-if="mode===2">{{ itemData.areaname || "" }}</text>
      <image class="cache" v-if="itemData.flagship_is_show" :src="itemData.flagship_bg_img | imageFilter('w_1200')"></image>
      
    </view>
    <view class="info">
      <view class="title-box">
        <view class="title">
          
          <text>{{ itemData.title }}</text>
        </view>
        <text class="attr" v-if ="itemData.status_name" :style ="{background:itemData.status_color}">{{itemData.status_name}}</text>
        <!-- <text :class="'attr'+itemData.leixing">{{itemData.status_name}}</text> -->
      </view>
      <view class="bottom-info flex-box" v-if="mode===2">
        <view class="bottom-left">
          <text class="price">{{itemData.build_price}}</text>
          <text class="price-unit">{{itemData.price_unit}}</text>
        </view>
      </view>
      <view class="center-info" :class="{colum:mode===1}">
        <view class="area flex-row" :class="{'area_other':itemData.has_help_hb}" v-if="mode===1">
          <view class='flex-1'>
            <text class="area_name">{{ itemData.areaname || "" }}</text>
            <text v-if="itemData.mj">建面：{{itemData.mj}}</text>
          </view>
          
          <image class="has_help_hb" v-if="itemData.has_help_hb" :src="help_hb"></image>
        </view>
        <view class="label-row">
          <view class="container" v-if="itemData.flagship_is_show">
            <view class="rz_strip flex-row" >
              <image class="rz_icon" :src="'/build/icon/rz.png' | imageFilter('m_320')"></image>
              <view class="text_block">
                <text class="text">官方认证</text>
                <text class="text" v-if="itemData.flagship_name">{{itemData.flagship_name}}</text>
                <text v-show="!itemData.flagship_name&&itemData.build_type" class="text" v-for="(label, index) in itemData.build_type" :key="index">{{ label }}</text>
              </view>
            </view>
          </view>
          <text class="label" v-else v-for = "(label,idx) in itemData.build_type" :key= "idx" >{{label}}</text>
        </view>
        <!-- <text :class="'status'+itemData.leixing">{{itemData.status_name}}</text> -->
      </view>
      <view class="bottom-info flex-box" v-if="mode===1">
        <view class="bottom-left">
          <text class="price-unit price-type">{{itemData.build_price!='一房一价'?itemData.price_type:''}}</text>
          <text class="price small">{{itemData.build_price}}</text>
          <text class="price-unit">{{itemData.price_unit}}</text>
        </view>
        <view class="bottom-right">
          <text class="u-time">{{ itemData.visit_desc }}</text>
        </view>
      </view>
      <template v-if="mode===2">
        <view class="hui-row" v-if="itemData.discount">
          <text class="hui">惠</text>
          <text class="text">{{itemData.discount}}</text>
        </view>
        <view class="tuan-row" v-else-if="itemData.group_title">
          <text class="tuan">团</text>
          <text class="text">{{itemData.group_title}}</text>
        </view>
      </template>
      <view class="other-info" :class="{open:!other_info_onclose}" v-else-if="other_info_num>0" @click.stop.prevent="(other_info_num>1)&&(other_info_onclose=!other_info_onclose)">
        <view class="open" v-if="other_info_onclose&&other_info_num>1">
          <my-icon type="ic_open" size="22rpx" color="#FB656A"></my-icon>
        </view>
        <view class="close" v-if="!other_info_onclose&&other_info_num>1">
          <my-icon type="ic_close" size="22rpx" color="#fff"></my-icon>
        </view>
        <view class="item hongbao" v-if="itemData.hb_is_open">
          <image :src="hongbaoIcon" mode="aspectFit"></image>
          <text class="text">红包</text>
        </view>
        <view class="item" v-if="itemData.discount">
          <text class="icon hui">惠</text>
          <text class="text">{{itemData.discount}}</text>
        </view>
        <view class="item" v-if="itemData.group_title">
          <text class="icon tuan">团</text>
          <text class="text">{{itemData.group_title}}</text>
        </view>
        <view class="item" v-if="itemData.live_room&&itemData.live_desc">
          <view class="icon zhi">
            <my-icon type="zhibo" color="#fff" size="22rpx"></my-icon>
          </view>
          <text class="text">{{itemData.live_desc||'直播预告'}}</text>
        </view>
      </view>
      <view class="rank" v-if="itemData.ranks&&itemData.ranks.id">
        <image class="icon" :src="itemData.ranks.icon"></image>
        <view class="text">{{itemData.ranks.title}}</view>
      </view>
      <view class="push flex-row"   v-if ="from=='find_house'&&itemData.is_match">
        <view class="is_push pushed" v-if="itemData.is_push==1">已推送</view>
        <view class="is_push" v-else >
          猜你喜欢
        </view>
        <view class="tiaojian">{{itemData.is_match}}</view>
      </view>

    </view>
  </view>
</template>
<style scoped lang="scss">
view{
  line-height: 1;
}
.flex-row{
  display: flex;
  flex-direction: row;
}
.house {
  display: flex;
  padding: 41rpx 0;
  .img-box {
    width: 204rpx;
    height: 172rpx;
    margin-right: 16rpx;
    position: relative;
    border-radius: 8rpx;
    overflow: hidden;
    .img {
      width: 100%;
      height: 100%;
    }
    .level-box{
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;
      .level{
        display: block;
        margin-bottom: 8rpx;
        padding: 4rpx 10rpx;
        font-size: 22rpx;
        border-bottom-left-radius: 20rpx;
        color: #fff;
        &.level1{
          background: linear-gradient(135deg, #69D4BB 0%, #00CAA7 100%);
        }
        &.level2{
          background: linear-gradient(132deg, #F7918F 0%, #FB656A 100%);
        }
      }
    }
    .video-icon{
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      left: 20rpx;
      bottom: 20rpx;
      &.center{
        left: initial;
        right: 12rpx;
        bottom: 12rpx;
        top: initial;
        margin: auto;
      }
    }
    .area_name{
      position: absolute;
      left: 12rpx;
      bottom: 12rpx;
      font-size: 22rpx;
      color: #fff;
    }
    // .has_help_hb{
    //   position: absolute;
    //   top: 5rpx;
    //   left: 5rpx;
    //   height:34rpx;
    //   width: 66rpx;
    // }
  }
  .info {
    flex: 1;
    overflow: hidden;
    .title-box{
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      
      .attr{
        font-size: 22rpx;
        padding: 6rpx 10rpx;
        border-radius: 4rpx;
        line-height: 22rpx;
        background: #53d2ab;
        // margin-right: 10rpx;
        // background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
        color: #fff;
      }
      .attr1 {
        font-size: 22rpx;
        padding: 6rpx 10rpx;
        border-radius: 4rpx;
        // margin-right: 10rpx;
        background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
        color: #fff;
      }
      .attr2 {
        font-size: 22rpx;
        padding: 6rpx 10rpx;
        border-radius: 4rpx;
        // margin-right: 10rpx;
        background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
        color: #fff;
      }
      .attr3 {
        font-size: 22rpx;
        padding: 6rpx 10rpx;
        border-radius: 4rpx;
        // margin-right: 10rpx;
        background: linear-gradient(to right, #ff8533 0%, #ff7213 100%);
        color: #f3f3f3;
      }
      .attr4 {
        font-size: 22rpx;
        padding: 6rpx 10rpx;
        border-radius: 4rpx;
        // margin-right: 10rpx;
        background: linear-gradient(to right, #ccc 0%, #ccc 100%);
        color: #fff;
      }
      .is_push {
        font-size: 22rpx;
        padding: 6rpx 10rpx;
        border-radius: 4rpx;
        display: inline-block;
        min-width: 120rpx;
        // margin-right: 10rpx;
        background: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
        color: #fff;
      }
    }
    .title {
      flex: 1;
      font-size: 32rpx;
      line-height: 1.3;
      margin-top: -4rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      .ding {
        font-size: 22rpx;
        border-radius: 4upx;
        margin-right: 10upx;
        padding: 2upx 10upx;
        color: #fff;
        background-color: #f40;
      }
      .jing {
        font-size: $uni-font-size-sm;
        border-radius: 4upx;
        margin-right: 10upx;
        padding: 2upx 10upx;
        color: #fff;
        background-color: #f40;
      }
    }
    .center-info {
      display: flex;
      align-items: center;
      margin-top: 16rpx;
      font-size: 22rpx;
      color: #999;
      &.colum{
        align-items: flex-start;
        flex-direction: column;
        .area{
          margin-bottom: 16rpx;
          align-items: center;
          width:100%;
          &.area_other{
            margin-bottom: 6rpx;
          }
          .area_name{
            margin-right: 16rpx;
          }
          .has_help_hb{
            width: 84rpx;
            height: 44rpx;
            justify-self: flex-end;
          }
        }
      }
      .area {
        font-size: 22rpx;
        margin-right: 10rpx;
      }
      .status1{
        color: #4cc7f6;
      }
      .status2{
        color: #00caa7;
      }
      .status3{
        color: #666;
      }
      .status4{
        color: #ff7213;
      }
    }

    .label-row{
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }
    .label{
      display: inline-block;
      font-size: 22rpx;
      padding: 4rpx 6rpx;
      color: #999;
      border: 0.5px solid #d8d8d8;
      border-radius: 4rpx;
      ~.label{
        margin-left: 10rpx;
      }
    }

    .bottom-info {
      margin-top: 16rpx;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      flex-wrap: wrap;
      text {
        font-size: 22rpx;
      }
      .price {
        font-size: 32rpx;
        font-weight: bold;
        color: #fb656a;
        &.small{
          font-size: 32rpx;
        }
      }
      
      .price-unit {
        font-size: 22rpx;
        color: #fb656a;
        margin: 0 10rpx 0 10rpx;
        &.price-type{
          margin-left: 0;
          color: #999;
        }
      }
      .mj {
        margin-left: 10rpx;
      }
      .bottom-right{
        text-align: right;
        // flex-shrink:0;
        flex: 1;
        overflow: hidden;
        font-size: 0;
      }
      .u-time {
        display: inline-block;
        max-width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        position: relative;
        font-size: 22rpx;
        color: #999;
      }
    }

    .hui-row{
      box-sizing: border-box;
      display: inline-block;
      height: 32rpx;
      line-height: 30rpx;
      margin-top: 16rpx;
      font-size: 22rpx;
      border: 1rpx solid $uni-color-primary;
      color: $uni-color-primary;
      max-width: 100%;
      border-radius: 4rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .hui{
        height: 100%;
        padding: 0 6rpx;
        display: inline-block;
        background-color: $uni-color-primary;
        color: #fff;
      }
      .text{
        // overflow: hidden;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        padding: 0 10rpx;
      }
    }

    .tuan-row{
      box-sizing: border-box;
      display: inline-block;
      height: 32rpx;
      line-height: 30rpx;
      margin-top: 16rpx;
      font-size: 22rpx;
      border: 1rpx solid #00caa9;
      color: #00caa9;
      max-width: 100%;
      border-radius: 4rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .tuan{
        height: 100%;
        padding: 0 6rpx;
        display: inline-block;
        background-color: #00caa9;
        color: #fff;
      }
      .text{
        // overflow: hidden;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        padding: 0 10rpx;
      }
    }

    .rank{
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 10rpx;
      font-size: 22rpx;
      .icon{
        width: 140rpx;
        height: 36rpx;
        position: relative;
        z-index: 1;
      }
      .text{
        height: 36rpx;
        line-height: 36rpx;
        padding-left: 38rpx;
        padding-right: 12rpx;
        position: relative;
        left: -28rpx;
        font-size: 22rpx;
        color: #565656;
        background: linear-gradient(90deg, #FCF6DB 0%, #FBD38B 100%);
      }
    }

    .push{
      margin-top: 10rpx;
      align-items: center;
      .tiaojian{
        font-size: 22rpx;
        color: #999;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .is_push{
      // background:linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
      color: #fff;
      // padding: 6rpx 10rpx;
      // margin-right: 10rpx;
      // line-height: 1;
      // font-size: 22rpx;
      display: inline-block;
      // min-width: 100rpx;
      margin-right: 10rpx;
      text-align: center;
      font-size: 22rpx;
      padding: 5rpx 10rpx;
      border-radius: 8rpx;
      min-width: 100rpx;
      // color: #FB656A;
      background:  linear-gradient(90deg, #F7918F 0%, #FB656A 100%);
      &.pushed{
        background-image: linear-gradient(90deg, #8CD3FC 0%, #4CC7F6 100%);
      }
      
    }

    .other-info{
      position: relative;
      padding-right: 48rpx;
      height: 52rpx;
      margin-top: 14rpx;
      overflow: hidden;
      &.open{
        height: auto;
      }
      .open{
        position: absolute;
        top: 10rpx;
        right: 0;
        height: 32rpx;
        width: 32rpx;
        text-align: center;
        border-radius: 16rpx;
        background-color: #ffeff0;
      }
      .close{
        position: absolute;
        bottom: 10rpx;
        right: 0;
        height: 32rpx;
        width: 32rpx;
        text-align: center;
        border-radius: 16rpx;
        background-color: $uni-color-primary;
      }
      .item{
        display: flex;
        align-items: center;
        padding: 10rpx 0;
        white-space: nowrap;
        overflow: hidden;
        font-size: 26rpx;
        color: #333;
        .text{
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .icon{
        color: #fff;
        border-radius: 4rpx;
        padding: 4rpx;
        font-size: 22rpx;
        line-height: 1;
        margin-right: 20rpx;
      }
      .hui{
        background-color: $uni-color-primary;
      }
      .tuan{
        background-color: #00caa9;
      }
      .zhi{
        background-color: #f9cf5e;
      }
    }
  }
}
.cache{
  width: 0;
  height: 0;
}
.rz_strip{
  display: flex;
  align-items: center;
  line-height: 1;
  padding: 10rpx 20rpx;
  background-image: linear-gradient(90deg, #F0D4B0 0%, #FBEFDB 100%);
  border-radius: 4rpx;
  border-radius: 4rpx;
  color: #373131;
  
  .rz_icon{
    width: 15px;
    height: 15px;
    margin-right: 6rpx;
  }
  .text_block{
    flex: 1;
    display: block;
    overflow : hidden;
    text-overflow: ellipsis;
    .text{
      line-height: 1;
      padding: 0 10rpx;
      font-size: 22rpx;
      position: relative;
      // &.label{
      //   &:after{
      //     content: "";
      //     position: absolute;
      //     left: 0;
      //     top: 8rpx;
      //     bottom: 6rpx;
      //     width: 4rpx;
      //     -webkit-transform: scaleX(.5);
      //     transform: scaleX(.5);
      //     background-color: #373131;
      //   }
      // }
    }
  }
}
.hongbao {
  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 20rpx;
  }
  .text {
    color: #FF5B5B;
  }
}
</style>
<script>
import myIcon from '../components/myIcon'
import { config} from '../common/index'
export default {
  components: {myIcon},
  data() {
    return {
      other_info_onclose:true,
      hongbaoIcon: config.imgDomain + '/hongbao/hongbao.png'
    };
  },
  props: {
    itemData: Object,
    type: {
      type: String,
      default: "ershou",
    },
    titleRow: {
      type: [Number, String],
      default: 1,
    },
    mode:{
      type:Number,
      default:1
    },
    showLine:{
      type:Boolean,
      default:true
    },
    from:{
      type: [String],
      default: '',
    }
  },
  computed:{
    other_info_num(){
      let num = 0
      if(this.itemData.discount) num++
      if(this.itemData.group_title) num++
      if(this.itemData.live_room&&this.itemData.live_desc) num++
      if(this.itemData.hb_is_open) num++
      return num
    },
    help_hb(){
      return config.imgDomain+"/hongbao/fangyuan_list_hongbao.png"
    }
  },
  methods: {},
};
</script>
