<template>
<view class="online_detail" :class="{sand_cate:sand_navs.length>1}">
    <tab-bar class ="container" v-if="sand_navs.length>1" ref="sand_list" :nowIndex="cuttent_sand_index" :tabs="sand_navs" @click="handelSandCate"></tab-bar>
    <view class="sand_card container">
        <template v-if="sand_info.width">
			<!-- #ifndef APP-PLUS -->
			<movable :map_img="sand_info.img" :map_info="sand_info" ref ="moveable" :mark_point="sand_point" :filterList="filterList" :now_point="id" height="55vw" @onMove="handleMove" @clickPoint="onClickPoint" ></movable>
			<!-- #endif -->
			<!-- #ifdef APP-PLUS -->
			<movable :map_img="sand_info.img" :map_info="sand_info" :mark_point="sand_point"  ref ="moveable"  :filterList="filterList"  :now_point="id" height="55vw" @onMove="handleMove" @clickPoint="onClickPoint" ></movable>
			<!-- #endif -->
        </template>
    </view>
    <view class="lodong_info-box">
        <view class="row-title">
            <text>楼栋详情</text>
        </view>
        <view class="lodong_info">
            <tab-bar class="container" v-if="navs.length>0" ref="ld_list" :tabs="navs" @click="handelCate" :nowIndex="nav_now_index" :fixedTop="false"></tab-bar>
            <view class="ld_info container">
                <view class="ld_num">{{ld_info.number}}</view>
                <view class="info_data flex-box">
                    <view class="info_item">单元 {{ld_info.unit_count}}个</view>
                    <view class="info_item">层数 {{ld_info.floor}}层</view>
                    <view class="info_item">户数 {{ld_info.house_count}}户</view>
                    <view class="info_item">电梯数 {{ld_info.elevator}}部</view>
                </view>
            </view>
            <view class="card ">
                <view class="uni_title bottom-line">{{ld_info.number}}{{ld_info.show_model==1?'房源':'户型'}}</view>
                <view class="house_list">
                    <view class="unit_item flex-box" v-for="(house, index) in house_list" :key="index">
                        <view class="name flex-1">{{house.name}}</view>
                        <view class="stw">{{house.info}}</view>
                        <view class="mj">{{house.jzmj}}</view>
                        <!-- <view class="btn" v-if="ld_info.show_model==1">{{house.sale_status}}</view> -->
                        <view class="btn" v-if="ld_info.show_model==2" @click="prevImg(house.pic)">查看户型</view>
                    </view>
                    <view class="tip" v-if="house_list.length===0&&nohouselist">暂无该楼栋的房源信息</view>
                </view>
            </view>
        </view>
    </view>
    <view class="btn-box" v-if="id">
        <button class="default" @click="toFangyuan()">{{is_login?'查看楼栋房源信息':'登录后查看楼栋房源信息'}}</button>
    </view>
    <chat-tip></chat-tip>
</view>
</template>

<script>
import {
  navigateTo,
  formatImg,
  checkUserStatus,
} from '../common/index.js'
import tabBar from "../components/tabBar.vue"
import movable from '../components/moveableScale.vue'
import {wxShare} from '../common/mixin'
export default {
    data() {
        return {
            sand_navs:[],
            cuttent_sand_index:0,
            sand_info:{},
            sand_point:[],
            house_list:[],
            id:0,
            navs:[],
            nav_now_index:0,
            ld_info:{
                unit_count:"",
                floor:"",
                house_count:"",
                elevator:"",
                number:""
            },
            nohouselist:false,
            is_login:false,
            filterList:[]
        }
    },
    mixins:[wxShare],
    components: {
        movable,
        tabBar
    },
    onLoad(options){
        if(uni.getStorageSync('token')){
            this.is_login = true
        }
        this.build_id = options.build_id || null
        this.sand_id = options.sand_id || null
        this.getSandCate()
        if(options.id){
            this.id = options.id
            this.getLoudong()
        }
        if(this.build_id&&this.sand_id){
            this.getSand()
        }
    },
    onShow(){
        if(uni.getStorageSync('token')){
            this.is_login = true
        }
    },
    methods:{
        handelSandCate(e){
            this.cuttent_sand_index = e.index
            this.id = null
            this.sand_id = e.id
            this.getSand()
        },
        handelCate(e){
            this.nav_now_index = e.index
            this.id = e.id
            this.getLoudong()
        }, 
        getSandCate(){
            this.$ajax.get('build/getBuildSandCate.html',{id:this.build_id},res=>{
                if(res.data.code === 1&&res.data.cate.length>0){
                    this.sand_navs = res.data.cate.map((item,index)=>{
                        if(item.id == this.sand_id){
                            this.cuttent_sand_index = index
                        }
                        return {id:item.id,name:item.title}
                    })
                }
            })
        },
        getSand(){
            this.share = {}
            this.$ajax.get('build/getBuildSand.php',{build_id:this.build_id,sand_id:this.sand_id},res=>{
                if(res.data.code === 1){
                    // let padding = uni.upx2px(20)
                    let padding = 0
                    let windowWidth = this.$store.state.systemInfo.windowWidth
                    let box_height = windowWidth*3/5
                    let box_width = windowWidth-padding*2
                    let top = "0px"
                    let left = "0px"
                    if(res.data.sand.height-box_height>res.data.sand.height/4){
                        top = 0-(res.data.sand.height/4)+'px'
                    }
                    if(res.data.sand.width-box_width>0){
                        left = 0-(res.data.sand.width/2-box_width/2)+'px'
                    }
                    this.sand_info = {
                        img:res.data.sand.pic, // 楼盘背景
                        width:res.data.sand.width,
                        height:res.data.sand.height,
                        top:top,
                        left:left,
                        margin_left:"0px"
                    }
                    if(res.data.sand.item&&res.data.sand.item.length>0){
                        res.data.sand.item.map(item=>{
                            item.activeColor= "rgba(255,101,107,0.8)"
                            item.arrowColor= " 8rpx solid " +item.color
                            return item
                        })
                        this.sand_point = res.data.sand.item
                        if(!this.id){
                            this.id = res.data.sand.item[0].id
                            this.getLoudong()
                        }
                        let nav_now_index
                        this.navs = res.data.sand.item.map((item, index)=>{
                            if(this.id == item.id){
                                nav_now_index = index
                            }
                            return {id:item.id,name:item.name}
                        })
                        this.$nextTick(()=>{
                            this.nav_now_index = nav_now_index
                        })
                    }
                    if (res.data.buildStatus) {
                        this.filterList=res.data.buildStatus
                    }
                    // this.$nextTick(()=>{
                    //     this.$refs.moveable.showMoveable()
                    // })
                    
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            })
        },
        getLoudong(){
            this.$ajax.get('build/getBuildNumberInfo.php',{number_id:this.id},res=>{
                // #ifdef H5 || MP-BAIDU
                if (res.data.seo) {
                    this.seo = res.data.seo
                }
                // #endif
                if(res.data.code === 1){
                    this.ld_info = {
                        number:res.data.number.number,
                        unit_count:res.data.number.unit_count,
                        house_count:res.data.number.house_count,
                        elevator:res.data.number.elevator,
                        floor:res.data.number.floor,
                        show_model:res.data.number.show_model,
                    }
                    if(res.data.houses.length>0){
                        this.nohouselist = false
                    }else{
                        this.nohouselist = true
                    }
                    this.house_list = res.data.houses
                    uni.setNavigationBarTitle({
                        title:res.data.number.build_title||''+'楼栋信息'
                    })
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
                if(res.data.share&&res.data.share.title){
                    this.share = res.data.share
                    this.getWxConfig()
                }
            })
        },
        toFangyuan(){
            checkUserStatus(()=>{
                navigateTo(`/online/house_status?id=${this.id}`)
            },true,false)
        },
        onClickPoint(e){
            this.nav_now_index = e
            // console.log(this.sand_point[e])
            this.id = this.sand_point[e].id
            this.getLoudong()
        },
        prevImg(img){
            if(!img){
                uni.showToast({
                    title:'该房源暂未上传户型图',
                    icon:'none'
                })
                return
            }
            uni.previewImage({
                urls: [formatImg(img,'w_6401')],
                current: 0
            })
        }
    }
}
</script>

<style scoped lang="scss">
.online_detail{
    background: #fff;
    &.sand_cate{
        padding-top: 90rpx;
    }
    .container {
      background: #fff;
      padding: 0 48rpx;
    }
    .sand_card{
        height: 55vw;
        margin: 0 0 20rpx;
        background: #fff;
        touch-action: none
    }
    .row-title{
        padding: 25rpx 20rpx;
        line-height: 1.2;
        font-size: 34rpx;
        font-weight: bold;
        background-color: #fff;
        // margin-bottom: 20rpx;
        text{
            padding-left: 25rpx;
            // border-left: 3px solid #f65354;
        }
    }
    .lodong_info-box{
        .lodong_info{
            border-top: 1rpx solid #f3f3f3;
            border-left: 1rpx solid #f3f3f3;
            font-size: 0;
            // background-color: #fff;
            .th{
                background-color: #fbfbfb;
                color: #000;
                font-size: 30rpx;
                // font-weight: bold;
            }
            .td,.th{
                padding: 15rpx;
                height: 60rpx;
                line-height: 60rpx;
                border-bottom: 1rpx solid #f3f3f3;
                border-right: 1rpx solid #f3f3f3;
            }
            .unit-list{
                flex: 1;
                overflow-y: hidden;
                white-space: nowrap;
                font-size: 0;
            }
            .td.unit{
                color: #000;
                font-size: 30rpx;
            }
        }
        .info_title{
            display: inline-block;
            width: 160rpx;
        }
        .info_detail{
            // flex: 1;
            display: inline-block;
            min-width: 28%;
            max-width: 35%;
            text-align: center;
        }
        .ld_info{
            padding: 20rpx 48rpx;
            background-color: #fff;
            .ld_num{
                font-size: 38rpx;
                font-weight: bold;
                padding-left: 10rpx;
                margin-bottom: 15rpx;
            }
        }
        .info_data{
            flex-wrap: wrap;
            .info_item{
                padding: 20rpx 10rpx;
                box-sizing: border-box;
                width: 50%;
                color: #555;
            }
        }
        .card{
            margin-top: 20rpx;
            // margin: 20rpx;
            padding: 30rpx 48rpx;
            // border-radius: 5px;
            overflow: hidden;
            background-color: #fff;
            // box-shadow: 0 0 10px #dedede;
            .uni_title{
                padding-bottom: 15rpx;
                margin-bottom: 10rpx;
                font-size: 16px;
                position: relative;
            }
            // .house_list{
            //     padding: 15rpx;
            // }
            .unit_item{
                justify-content: space-between;
                line-height: 2;
                align-items: center;
                .name{
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                .stw{
                    min-width: 25%;
                    margin: 0 10rpx;
                }
                .mj{
                    min-width: 20%;
                    margin-left: 15rpx;
                }
                .btn{
                    color: $uni-color-primary;
                    padding: 10rpx;
                }
            }
            .tip{
                text-align: center;
                padding-top: 15rpx;
                color: #666;
            }
        }
    }
    .unit_info{
        text-align: center;
        padding: 20rpx;
    }
    .btn-box uni-button.default{
      background-color: rgba(255, 101, 107, 0.05);
      color: #ff656b;
    }
}
</style>
