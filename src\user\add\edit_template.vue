<template>
  <view class="page">
    <view class="text-box">
      <view class="label">
        <text>{{detail.name}}</text>
        <text class="link">{{detail.utime}}</text>
      </view>
      <textarea v-model="detail.content" ows="5" maxlength="300" placeholder="请输入模板内容"></textarea>
      <view class="footer flex-box">
        <text class="select_btn"></text>
        <view>
          <text>{{detail.content.length}}/300</text>
        </view>
      </view>
    </view>
    <view class="btn_group">
      <view class="add_btn" @click="handleEdit">确定</view>
      <view class="del_btn" @click="handleDel">删除模板</view>
    </view>
  </view>
</template>

<script>
import {showModal} from "../../common/index"
export default {
  name: '',
  components: {},
  data () {
    return {
      detail: {
        content: ''
      }
    }
  },
  onLoad(options){
    if(options.id){
      this.id = options.id
      this.getDetail(this.id)
    }
  },
  methods: {
    getDetail(id){
      this.$ajax.get('release/releaseTemplateDetail', {id}, res=>{
        if(res.data.code === 1){
          this.detail = res.data.detail
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    handleEdit(){
      var params = {
        id: this.detail.id,
        cate_id: this.detail.cate_id,
        content: this.detail.content,
        name: this.detail.name,
      }
      this.$ajax.post('release/saveReleaseTemplate', params, res=>{
        if(res.data.code === 1){
          uni.showToast({
            title: '编辑成功'
          })
          setTimeout(()=>{
            this.$navigateBack()
          }, 2000)
          uni.$emit('recapture')
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    handleDel(){
      showModal({
        content: "确定删除此此模板吗？",
        confirm: ()=>{
          this.$ajax.get('release/delReleaseTemplate', {id: this.detail.id}, res=>{
            if(res.data.code === 1){
              uni.showToast({
                title: '删除成功'
              })
              setTimeout(()=>{
                this.$navigateBack()
              }, 2000)
              uni.$emit('recapture')
            }else{
              uni.showToast({
                title: res.data.msg,
                icon: 'none'
              })
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.page{
  min-height: calc(100vh - 44px);
  background-color: #fff;
}
.text-box{
  padding: 0 48rpx;
  padding-top: 24rpx;
  background-color: #fff;
  .label{
    padding: 24rpx 0;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
    .link{
      font-size: 24rpx;
      font-weight: initial;
      color: #999;
    }
  }
  textarea{
    width: 100%;
    height: 300rpx;
    padding: 20rpx;
    box-sizing: border-box;
    line-height: 1.5;
    border-radius: 4rpx;
    font-size: 28rpx;
    background-color: #f8f8f8;
  }
  .footer{
    justify-content: space-between;
    padding: 20rpx 0;
    color: #999;
    .select_btn{
      color: $uni-color-primary;
    }
    .clear{
      color: #999;
      padding: 0 48rpx;
    }
  }
}

.btn_group{
  padding: 48rpx;
  .add_btn{
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #fff;
    background: #FB656A;
    box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
  }
  .del_btn{
    margin-top: 24rpx;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    border-radius: 44rpx;
    font-size: 28rpx;
    color: #999;
  }
}
</style>