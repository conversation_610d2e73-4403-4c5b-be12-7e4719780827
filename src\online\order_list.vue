<template>
<view class="order_list_page">
    <tab-bar ref="tab_bar" :nowIndex="tabBarIndex" :tabs="order_types" :equispaced="false" @click="switchTab">
	</tab-bar>
    <view class="order_list">
        <view class="order-item" v-for="(order,index) in order_list" :key="index" @click="toOrderDetail(order.id)">
            <view class="order-header bottom-line flex-box">
                <text class="flex-1">订单编号：{{order.order_sn}}</text>
                <text class="status" v-if="order.shixiao==1">已失效</text>
                <text class="status" v-else-if="order.write_off==1">已核销</text>
                <text class="status" v-else>{{order.pay_status | orderStatus}}</text>
            </view>
            <view class="order-content flex-box">
                <view class="img-box">
                    <image :src="order.pic | imgUrl('w_240')" mode="aspectFill"></image>
                </view>
                <view class="flex-1">
                    <view class="order-desc">{{order.online_title}}</view>
                    <view class="name">{{order.house_name||''}} {{order.huxing||''}}</view>
                </view>
                <!-- <view class="order-desc flex-1">{{order.online_title}}</view> -->
            </view>
            <view class="order-info bottom-line flex-box">
                <text class="time">日期：{{order.ctime}}</text>
                <text class="price">{{order.pay_status==0?'应付':'实付'}}：￥{{order.money}}</text>
                <!-- <text class="price">订房单金额：￥{{order.total_money}}</text> -->
            </view>
            <view class="order-footer flex-box">
                <!-- <view v-if="order.shixiao==1" class="surplus_time">已失效</view> -->
                <!-- 没失效且未支付显示支付有效时间 -->
                <view v-if="order.shixiao==0&&order.pay_status==0" class="surplus_time">{{order.limit_time | timeFormat}}</view>
                <view v-else class="surplus_time"></view>
                <view class="flex-box">
                    <view class="flex-box btn_box">
                        <view v-if="order.pay_status===0&&order.shixiao==0" class="btn" @click.stop.prevent="toCancel(order.id)">取消订单</view>
                        <view v-if="order.pay_status===0&&order.shixiao==0" class="btn" @click.stop.prevent="toPay(order.id)">去支付</view>
                        <view class="btn" @click.stop.prevent="toOrderStatus(order.id)">订单状态</view>
                        <!-- <view class="btn" @click.stop.prevent="toOrderDetail(order.id)">查看订单</view> -->
                    </view>
                </view>
            </view>
        </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
</view>
</template>

<script>
import tabBar from "../components/tabBar.vue"
import {uniLoadMore} from '@dcloudio/uni-ui'
import {
    navigateTo,
    formatImg,
} from '../common/index.js'
export default {
    data() {
        return {
            order_types:[
                {
                    name:"全部",
                    id:'',
                },
                {
                    name:"待付款",
                    id:0,
                },
                {
                    name:"已付款",
                    id:1,
                },
                {
                    name:"处理中",
                    id:2,
                },
                {
                    name:"已退款",
                    id:3,
                },
                // { 后端说不用加已失效筛选
                //     name:"已失效",
                //     id:4,
                // },
            ],
            tabBarIndex:0,
            order_list:[],
            get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
            params:{
                page:1,
                pay_status:'',
                rows:20
            }
        }
    },
    onLoad(options){
        if(options.order_type){
            this.params.pay_status = parseInt(options.order_type)
        }
        this.getData()
        uni.$on('getOrderList',()=>{
            this.params.page = 1
            this.getData()
        })
    },
    onUnload(){
        uni.$off('getOrderList')
    },
    onReady(){
        let nowTabIndex = 0;
        this.order_types.forEach((item, index) => {
            if (item.id === this.params.pay_status) {
                nowTabIndex = index
            }
        })
        this.tabBarIndex = nowTabIndex
    },
    components: {
        tabBar,
        uniLoadMore
    },
    filters:{
        imgUrl(val,param){
            return formatImg(val,param)
        },
        orderStatus(val){
            let status
            switch (val){
                case 0:
                    status = "待付款"
                    break;
                case 1:
                    status = "已付款"
                    break;
                case 2:
                    status = "处理中"
                    break;
                case 3:
                    status = "已退款"
                    break;
                case 5:
                    status = "待使用"
                    break;
                case 6:
                    status = "已失效"
                    break;
                default:
                    status = ""
            }
            return status
        },
        timeFormat(val){
            if(!val){
                return ""
            }
            let now_hour = new Date().getHours()
            let hour = new Date(val*1000).getHours()
            let minute = new Date(val*1000).getMinutes()
            let second = new Date(val*1000).getSeconds()
            return `请在${hour<10?'0'+hour:hour}:${minute<10?'0'+minute:minute}前支付`
        }
    },
    methods:{
        getData(){
            if(this.params.page == 1){
                this.order_list=[]
            }
            this.get_status = "loading"
            this.$ajax.get('onlineMy/orderLists',this.params,res=>{
                if(res.data.code == 1){
                    this.order_list = this.order_list.concat(res.data.lists)
                    if(res.data.lists.length<this.params.rows){
                        this.get_status = "noMore"
                    }else{
                        this.get_status = "more"
                    }
                }else{
                    this.get_status = "noMore"
                }
            })
        },
        switchTab(e){
            this.tabBarIndex = e.index
            this.params.pay_status = e.id
            this.params.page = 1
            this.getData()
        },
        toOrderDetail(id){
            navigateTo(`/online/order_detail?id=${id}`)
        },
        toOrderStatus(id){
            navigateTo(`/online/order_status?id=${id}`)
        },
        toPay(id){
            navigateTo(`/online/pay?order_id=${id}`)
        },
        toCancel(id){
            uni.showModal({
                content: '确认取消订单吗？',
                success:  (res)=> {
                    if (res.confirm) {
                        console.log('用户点击确定');
                        this.$ajax.post('/wapi/onlineMy/cancelOrder',{id:id},res=>{
                            if(res.data.code === 1){
                                uni.showToast({
                                    title:res.data.msg||'操作成功'
                                })
                                this.params.page = 1 
                                this.getData()
                            }else{
                                uni.showToast({
                                    title:res.data.msg||'操作失败',
                                    icon: 'none'
                                })
                                this.getData()
                            }
                        })
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
            
        }
    },
    onReachBottom(){
        if(this.get_status === "noMore"){
            return
        }
        this.params.page++
        this.getData()
    }
}
</script>

<style scoped lang="scss">
.order_list_page{
    padding-top: 80rpx;
}
.order_list{
    padding: 20rpx 30rpx;
    .order-item{
        border-radius: 20rpx;
        margin-bottom: 20rpx;
        background-color: #fff;
        .order-header{
            padding: 20rpx;
            justify-content: space-between;
            .status{
                color: $uni-color-primary
            }
        }
        .order-content{
            padding: 20rpx;
            .img-box{
                width: 200rpx;
                height: 160rpx;
                margin-right: 20rpx;
                image{
                    width: 100%;
                    height: 100%;
                }
            }
            .order-desc{
                line-height: 1.4;
                font-size: 30rpx;
                height: 80rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                color: #333;
            }
            .name{
                margin-top: 40rpx;
                color: #666;
            }
        }
        .order-info{
            padding: 5rpx 20rpx 20rpx 20rpx;
            justify-content: space-between;
            align-items: center;
            .time{
                color: #666;
                font-size: 26rpx;
            }
            .price{
                color: #333
            }
        }
        .order-footer{
            // flex-direction: row-reverse;  
            justify-content: space-between;
            align-items: center;
            padding:20rpx;
            flex-wrap: wrap;
            .surplus_time{
                font-size: 24rpx;
                color: $uni-color-primary;
            }
            .btn_box{
                // margin-top: 20rpx;
                .btn{
                    height: 50rpx;
                    line-height: 50rpx;
                    font-size: 22rpx;
                    padding: 0 25rpx;
                    border-radius: 30rpx;
                    background-color: $uni-color-primary;
                    color: #fff;
                    ~.btn{
                        margin-left: 20rpx;
                    }
                }
            }
        }
    }
}
</style>
