<template>
  <view class="page">
   <yVideoSlide :data ="videos" v-if ="videos.length"></yVideoSlide>
    
  </view>
</template>

<script>
import yVideoSlide from './components/y-video-slide'
export default {
  components:{
    yVideoSlide
  },
  data(){
    return  {
      title:'',
      height:"100vh",
      videos:[
        
      ],
      pinglun_list:[
        {
          nickname:'q',
          avatar:'https://images.tengfangyun.com/images/icon/def_head.jpg?x-oss-process=style/w_80',
          content:"点赞",
          id:1,
          replies:1,
          commentChildren:[
            {
              nickname:'b',
              id:2,
              content:"回复q",
              avatar:'https://images.tengfangyun.com/images/icon/def_head.jpg?x-oss-process=style/w_80',
              commentChildren:[
                {
                  nickname:'b',
                  id:3,
                  avatar:'https://images.tengfangyun.com/images/icon/def_head.jpg?x-oss-process=style/w_80',
                  content:"回复b",
                },
              ]
            },
            

          ]
        }
      ],
      current:0,
      show_pinglun:false ,
      placeholder:"回复：",
    }
  },
  onLoad(){
    this.getList()
    this.onChange({target:{current:0}})
    console.log(uni.getSystemInfoSync());
  },
  methods:{
    setTitle(){
      setTimeout(() => {
          this.title='设置的title'
      }, 300);
    },
    onChange(e,c){
      let id=  'video_play'+e.target.current
      this.current = e.target.current
      this.videoContext = uni.createVideoContext(id,this);
        this.videos.map((item,index)=>{
          let temp = "video_play" + index;
          if(temp !=id){
             uni.createVideoContext(temp, this).pause();
          }
        })
        this.videoContext.play()
    },
    getList(){
      this.$ajax.get("video/videoList",this.params,(res=>{
        if (res.data.code ==1){
          this.videos = res.data.list.map((item,index)=>{
            item.id = index+''
            item.commentObj ={
              count:10,
              list:[
                {
                  userHead:"",
                  userNick:"zhansan",
                  content:"评论12312",
                  isFabulous:0,
                  fabulousCount:222,
                  children:[
                    {
                      userHead:"",
                      replyTo:"lisi",
                      userNick:"zhansan1",
                      content:"评论123121233wqw ",
                      isFabulous:0,
                      fabulousCount:222,
                    }
                  ]
                }
              ]
            }
            return item
          })
        }else {
          uni.showToast({
            title:res.data.msg,
            icon:"none"
          })
        }
      }))
    },
    waitingVideo(){
    },
    playVideo(index){
      this.videoContext  =null
      this.videoContext = uni.createVideoContext('video_play'+index);
    },

    pauseVideo(){

    },
    showPinglun(){
      // this.videoContext.requestFullScreen()
      this.show_pinglun =true
    },
  }
}
</script>

<style lang="scss" scoped>
.page {
  background: #000;
}
.flex-row{
  display: flex;
  flex-direction: row;
}
.video_item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  flex-direction: column;
  position: relative;
  &.video_play_scale{
    align-items: flex-start;
    justify-content: flex-start;
    .video_play{
      height: 300rpx;
    }
  }
}
video{
  z-index: 1;
}
.fix_right{
  position: absolute;
  top: 50vh;
  right: 48rpx;
  transform: translateY(-50%);
  z-index: 1000;
  .prelogo {
    width: 80rpx;
    height: 80rpx;
    margin-bottom: 60rpx;
    overflow: hidden;
    border-radius: 50%;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .right_oper {
    margin-bottom: 48rpx;
    text-align: center;
    .right_img {
      width: 48rpx;
      height: 48rpx;
      margin: 0 auto;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
    .right_con{
      margin-top: 10rpx;
      font-size: 24rpx;
      color: #F8F8F8;
    }
  }
}
.fix_bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 48rpx;
  .title {
    color: #FFFFFF;
    font-size:36rpx;
    font-weight: 600;
    padding: 18rpx 0;
  }
  .info {
    color: #FFD952;
    font-size: 28rpx;
  }
  .info_c{
    color: #fff;
    font-size: 24rpx;
    margin-top: 16rpx;
  }
}
.pinglun{
  border-radius: 20rpx 20rpx 0px 0px;
  background: #FFFFFF;
  width: 100%;
  // position: absolute;
  // bottom:0;
  // left: 0;
  // right: 0;
  position: relative;
  height: 0;
  overflow-y: auto;
  transition: 0.3s;
  &.active{
    height: 600rpx;
    transition: 0.3s;
  }
  .title{
    position: relative;
    padding: 20rpx 0;
    justify-content: center;
    .title_c {
      color: #222222;
      font-size: 24rpx;
      text-align: center;
    }
    .close{
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 40rpx;
      height: 40rpx;
      image {
        width: 100%;
      }

    }
  }
  .pinglun_list {
    padding: 28rpx 40rpx 80rpx;
    position: relative;
    .pinglun_item {
      margin-bottom: 40rpx;
      .pinglun_prelogo {
        width: 72rpx;
        height: 72rpx;
        min-width: 72rpx;
        margin-right: 24rpx;
        overflow: hidden;
        border-radius: 50%;
        image{
          width: 100%;
        }
      }
      .pinglun_con {
        .cname {
          color: #ABA6A6;
          font-size: 26rpx;
        }
        .p_content {
          color: #222222;
          font-size: 28rpx;
        }
        .p_oper {
          margin-top: 20rpx;

          .ctime {
            color: #ABA6A6;
            margin-right: 40rpx;
            font-size: 26rpx;
          }
          .reply{
            color: #737373;
            font-size: 26rpx;
          }
        }
      }
    }
  }
  .inp {
      position: absolute;
      background: #F3F3F5;
      height: 72rpx;
      line-height: 72rpx;
      bottom: 20rpx;
      left: 48rpx;
      right: 48rpx;
      border-radius: 48rpx;
    }
    // .video_play{
    //   &.video_play_scale{

    //   }

    // }


}
</style>