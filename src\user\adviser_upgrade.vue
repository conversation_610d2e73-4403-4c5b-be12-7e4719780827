<template>
<view class="recharge">
	<view class="select_type flex-row">
		<view class="select_option" :class="{active:showTab===1}" @click="switchTab(1)">优选置业顾问</view>
		<view class="select_option" :class="{active:showTab===2}" @click="switchTab(2)">置顶卡</view>
	</view>
	<!-- 优选置业顾问套餐 -->
	<view class="options-box flex-row" v-if="showTab===1">
		<view class="options" :class="{active:item.id === upgrade_id}" @click="selectSetMeal(item)" v-for="item in adviser_list" :key="item.id">
			<text class="title">{{item.name}}</text>
			<text class="tip">{{payType==='active'?item.active+'活跃度':item.money+'元'}}</text>
			<!-- <text class="tip" v-if="optimization_free">{{item.money}}元</text>
			<text class="tip" v-else>{{item.active}}活跃度</text> -->
		</view>
		<view class="options" :class="{selected:show_custom}" @click="(show_custom = !show_custom)&&(upgrade_id='')">
			<view class="more flex-row">
				<text class="text">自定义</text>
				<my-icon :type="show_custom?'ic_close':'ic_open'" :color="show_custom?'#fff':'#999'"></my-icon>
			</view>
		</view>
		<view class="options vacancy"></view>
	</view>
	<!-- 置顶卡套餐 -->
	<view class="options-box flex-row" v-if="showTab===2">
		<view class="options" v-if="topFree.days" :class="{active:is_free === 1}" @click="selectFreeTop()">
			<text class="title">{{topFree.name}}</text>
			<text class="tip">{{payType==='active'?topFree.active+'活跃度':topFree.money+'元'}}</text>
		</view>
		<view class="options" :class="{active:item.id === upgrade_id}" @click="selectSetMeal(item)" v-for="item in top_list" :key="item.id">
			<text class="title">{{item.name}}</text>
			<text class="tip">{{payType==='active'?item.active+'活跃度':item.money+'元'}}</text>
		</view>
		<!-- <view class="options" :class="{selected:show_custom}" @click="(show_custom = !show_custom)&&(upgrade_id='')&&(is_free=0)">
			<view class="more flex-row">
				<text class="text">自定义</text>
				<my-icon :type="show_custom?'ic_close':'ic_open'" :color="show_custom?'#fff':'#999'"></my-icon>
			</view>
		</view> -->
		<view class="options vacancy"></view>
		<view class="options vacancy"></view>
	</view>
	<view class="exchange-box">
		<view class="inp-box flex-row bottom-line" v-if="show_custom">
			<input type="number" @input="handelInput" :placeholder="`请输入${optimization_free&&showTab===1?'购买':'兑换'}天数`" placeholder-style="font-size:26rpx">
			<view class="computed">
				<text>需花费</text>
				<text class="highlight">{{getMoney(days)}}</text>
				<text>{{payType==='active'?'活跃度':'元'}}</text>
			</view>
		</view>
		<view class="tip" v-if="showTab==2||(showTab===1&&optimization_free===0)">
			注：当前账户活跃度{{active_count}}个
		</view>
		<view class="tip" v-if="showTab==1&&privilege_desc">
			<text>优选置业顾问权限：</text>
			<view v-html="privilege_desc"></view>
			<!-- <text>您可以查看中间号通话记录</text> -->
		</view>
		<view class="tip" v-if="showTab==2&&open_privilege_desc">
			<view v-html="open_privilege_desc"></view>
		</view>
		<!-- 选择支付方式 -->
		<view class="pay_type">
			<view class="title">支付方式</view>
			<radio-group @change="radioChange">
				<label class="pay_item flex-box">
					<view class="pay_name flex-row">
						<my-icon type="huoyuedu" color="#fb656a" size="56rpx"></my-icon>
						<text class="text">活跃度支付</text>
					</view>
					<radio value="active" :checked="payType==='active'" color="#FB656A"></radio>
				</label>
				<label class="pay_item flex-box">
					<view class="pay_name flex-row">
						<my-icon type="weixinzhifu" color="#28C445" size="56rpx"></my-icon>
						<text class="text">微信支付</text>
					</view>
					<radio value="wechatpay" :checked="payType==='wechatpay'" color="#FB656A"></radio>
				</label>
			</radio-group>
		</view>
		<!-- 充值按钮 -->
		<view class="button-box">
			<!-- <view class="button" v-if="showTab===1&&optimization_free===1" @click="subOrder()">立即开通</view> -->
			<view class="button" @click="subOrder()">立即开通</view>
		</view>
	</view>
</view>
</template>

<script>
	import myIcon from "../components/myIcon"
	import {showModal} from '../common/index'
	export default {
		data() {
			return {
				days:0,
				showTab:1,
        payType:"active",
        active_count:0,
        exchange_optimization_price:1,
        exchange_optimization_active:1,
        exchange_top_active:0,
				adviser_list:[],
				top_list:[],
				topFree:{},
				is_free:0,
        upgrade_id:'',
        optimization_free:null,
				show_custom:false,
				privilege_desc: "",
				open_privilege_desc: ""
			};
		},
		components:{
			myIcon
		},
		onLoad(options){
			if(options.type){
				this.showTab = parseInt(options.type)
			}
			this.getSetmeal()
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			this.getWxConfig(
				['chooseWXPay', 'hideOptionMenu'],
				wx => {
				console.log('执行回调')
				this.wx = wx
				},
				1
			)
		},
		methods:{
      getMoney(days){
        if(!days){
          return 0
        }
        if(this.showTab === 1){
					if(this.payType==='active'){
						return days*this.exchange_optimization_active
					}else{
						return days*this.exchange_optimization_price
					}
        }
        if(this.showTab===2){
          return days*this.exchange_top_active
        }
      },
			handelInput(e){
				this.days = parseFloat(e.detail.value)||0
			},
			radioChange(e){
				this.payType = e.detail.value
			},
			switchTab(type){
				if(type===2 && this.is_optimization ===0 && this.optimization_free===1){
					showModal({
						title: '提示',
						content: '只有优选置业顾问才能开启置顶卡, 请先开启优选置业顾问',
						cancel: () => {
							this.$navigateBack()
						}
					})
					return
				}
				this.showTab = type
				// 清除参数
				// this.payType = ''
				this.upgrade_id = ''
        this.show_custom=false
        this.days = ''
			},
			// 获取套餐列表
			getSetmeal(){
				this.$ajax.get('adviser/exchangeList',{},res=>{
					if(res.data.code === 1){
						this.adviser_list = res.data.adviserList
            this.top_list = res.data.tops
						this.topFree = res.data.topFree
						if(res.data.exchange_optimization_price){
							this.exchange_optimization_price = res.data.exchange_optimization_price
						}
						if(res.data.exchange_optimization_active){
							this.exchange_optimization_active = res.data.exchange_optimization_active
						}
            this.exchange_top_active = res.data.exchange_top_active
            this.optimization_free = parseInt(res.data.optimization_free) // 0：免费 1：收费（和字面意思是反过来的）
						this.active_count = res.data.active
						this.is_optimization = res.data.is_optimization
						this.privilege_desc = res.data.privilege_desc
						this.open_privilege_desc = res.data.open_privilege_desc
					}else{
						uni.showToast({
							title: res.data.msg,
							icon:'none'
						})
						setTimeout(()=>{
							this.$navigateBack()
						},1200)
					}
				})
			},
			// 选择优选置业顾问或置顶卡
			selectSetMeal(item){
				this.upgrade_id = item.id
				this.is_free = 0
				this.show_custom = false
				this.days = ""
			},
			// 选择免费的置顶卡
			selectFreeTop(){
				this.upgrade_id=''
				this.is_free = 1
				this.show_custom = false
			},
			subOrder(){
				if(!this.days&&!this.upgrade_id&&!this.is_free){
					uni.showToast({
						title:"请输入或选择开通天数",
						icon:"none"
					})
					return
				}
				// 如果是优选置业顾问
				if(this.showTab === 1){
					if(this.payType==='active'){
						this.activePay('optimization')
					}else{
						this.moneyPay('optimization')
					}
				}

				// 如果是置顶卡
				if(this.showTab === 2){
					if(this.is_optimization ===0 && this.optimization_free===1){
						showModal({
							title: '提示',
							content: '只有优选置业顾问才能开启置顶卡, 请先开启优选置业顾问',
							confirm: () => {
								this.switchTab(1)
							},
							cancel: () => {
								this.$navigateBack()
							}
						})
						return
					}
					if(this.payType==='active'||this.is_free){
						this.activePay('top')
					}else if(this.payType==='wechatpay'){
						this.moneyPay('top')
					}else{
						uni.showToast({
							title: '请选择支付方式',
							icon: 'none'
						})
					}
				}
			},
			// 兑换优选置业顾问或置顶卡
      activePay(type){
				const api = {
					optimization: 'adviser/exchange.html',
					top: 'adviser/exchangeTop.html'
				}
				let params = {exchange_id:this.upgrade_id,days:this.days}
        if(this.showTab === 2){
					// 如果使用的是免费的需要传is_free
					if(this.is_free){
						params.is_free = this.is_free
						params.days = this.topFree.days
					}
        }
        this.$ajax.get(api[type],params,res=>{
          if(res.data.code === 1){
            uni.showToast({
              title:res.data.msg||"兑换成功"
						})
						setTimeout(()=>{
							this.$navigateBack()
						},1500)
          }else{
             uni.showToast({
              title:res.data.msg,
              icon:'none'
            })
          }
        })
      },
      moneyPay(type){
				const api = {
					optimization: 'adviser/buyByWxPay.html',
					top: 'adviser/topBuyWxPay.html'
				}
				let params = {exchange_id:this.upgrade_id}
				if(this.days){
					params.days = this.days
				}
				this.$ajax.get(api[type],params,(res)=>{
					if(res.data.code == 1){
						this.handelPay(res.data.data)
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:'none'
						})
					}
				})
			},
			handelPay(pay_info){
				this.wx.chooseWXPay({
						// provider: 'wxpay',
						timestamp:pay_info.timeStamp,
						nonceStr:pay_info.nonceStr,
						package:pay_info.package,
						signType:pay_info.signType,
						paySign:pay_info.paySign,
						success: res => {
						console.log(res)
								uni.showToast({
										title: '支付成功'
								})
								setTimeout(()=>{
									this.$navigateBack()
								},1500)
						},
						fail: function(err) {
								console.log('支付失败：', err)
								uni.showToast({
										title: err.err_desc || err.errMsg,
										icon: 'none',
										duration: 5000
								})
						}
				})
			},
		}
	}
</script>

<style lang="scss">
page{
	background-color: #fff;
}
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row{
  flex-direction: row;
}

.highlight{
	color: $uni-color-primary;
}

.header {
  margin: 24rpx 48rpx;
	align-items: center;
  border-radius: 30rpx;
  overflow: hidden;
	height: 278rpx;
	background-image: linear-gradient(90deg, #ff676c 0%, #fe8e8d 100%);
  .info {
    flex: 1;
		text-align: center;
		display: block;
		&.left{
			border-right: 1rpx solid #FB656A ;
		}
		&.right{
			border-left: 1rpx solid #FF989B ;
		}
    .title {
      line-height: 1;
      margin-bottom: 10rpx;
      font-size: 26rpx;
      color: #fff;
    }
    .integral{
      font-size: 64rpx;
      color: #fff;
    }
		.btn{
			margin-top: 24rpx;
			display: inline-block;
			height: 48rpx;
			line-height: 46rpx;
			padding: 0 24rpx;
			font-size: 22rpx;
			border-radius: 24rpx;
			border: 1rpx solid #fff;
			color: #fff;
		}
  }
}

.select_type{
	justify-content: center;
	.select_option{
		font-size: 36rpx;
		font-weight: bold;
		margin: 24rpx;
		padding: 24rpx;
		position: relative;
		&.active{
			color: $uni-color-primary;
			&::after{
				content:'';
				position: absolute;
				height: 8rpx;
				border-radius: 4rpx;
				background-color: $uni-color-primary;
				left: 40%;
				right: 40%;
				bottom: 0;
			}
		}
	}
}

.exchange-box{
  padding: 24rpx 48rpx;
}

.inp-box{
  padding: 20rpx 0;
  justify-content: space-between;
  input{
    padding: 0 20rpx;
  }
  .computed{
    display: block;
  }
}
.tip{
  margin-top: 24rpx;
  font-size: 22rpx;
  color: #999999;
	white-space: break-spaces;
	>view{
		font-size: 22rpx;
		line-height: 1.8;
	}
	>text{
		margin-bottom: 8rpx;
	}
}

.pay_type{
	margin-top: 50rpx;
	.title{
		font-size: 40rpx;
		font-weight: bold;
		margin-bottom: 24rpx;
	}
	.pay_item{
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 0;
		.pay_name{
			.text{
				margin-left: 20rpx;
			}
		}
	}
}

.button-box {
  margin-top: 48rpx;
  padding: 24rpx 0;
  padding-bottom: 32rpx;
  background-color: #fff;
  .button {
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    background: #fb656a;
    box-shadow: 0 8rpx 32rpx 0 rgba(251, 101, 106, 0.4);
    border-radius: 44rpx;
    border-radius: 44rpx;
  }
}


.options-box{
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 0 48rpx;
	.options{
		width: 31.5%;
		height: 132rpx;
		margin-bottom: 18rpx;
		border: 1rpx solid #d8d8d8;
		border-radius: 8rpx;
		padding: 24rpx;
		position: relative;
		color: #666;
		text-align: center;
		overflow: hidden;
		&.vacancy{
			height: 0;
			padding: 0;
			border: 0;
		}
		&.active{
			background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
			border-color: $uni-color-primary;
			.title{
				color: #333;
			}
			.tip{
				background-color: $uni-color-primary;
			}
		}
		&.selected{
			background-color: $uni-color-primary;
			border-color: $uni-color-primary;
			color: #fff;
			.more{
				color: #fff;
			}
		}
		.title{
			text-align: center;
			margin-bottom: 10rpx;
			font-size: 32rpx;
			font-weight: bold;
			color: #666;
		}
		.more{
			justify-content: center;
			align-items: center;
			margin-top: 20rpx;
			color: #666;
		}
		.tip{
			font-size: 22rpx;
			position: absolute;
			bottom: 0;
			width: 100%;
			left: 0;
			padding: 6rpx 0;
			background-color: #d8d8d8;
			color: #fff;
		}
	}
}
</style>
