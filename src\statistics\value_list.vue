<template>
  <view class="value">
    <view class="head">
      <view class="info">
        <image class="jzb" src="@/static/icon/jzb.png" mode="widthFix" />
        <view class="tit">3日飙升｜高性价比房源｜业主降价急卖</view>
        <view class="tag"
          >* 基于房源真实信息和经纪人<br /><text>作业情况生产</text></view
        >
        <view class="tit"
          >{{date}}更新
          <!-- <my-icon
            class="zhankai"
            type="-zhankai"
            size="12px"
            color="#fff"></my-icon> -->
        </view>
      </view>
    </view>
    <view class="value_tab">
      <tab-bar class="tab flex-row"
        :tabs="tab"
        :bgColor="'none'"
        :fixedTop="false"
        :showLine="false"
        :nowIndex="tabId"
      >
        <view
          class="nav_item"
          :class="{ active: index == tabId }"
          v-for="(item, index) in tab"
          :key="index"
          @click="switchTab(index)"
          >{{ item.name }} <text></text
        ></view>
      </tab-bar>
    </view>
    <view class="list">
      <view class="detail" v-for="(item,index) in list" :key="index" @click="toErshou(item.id)">
        <view class="top">
          <image v-if="index<3" class="tip" :src="require(`@/static/icon/top${index+1}.png`)"  mode=""></image>
          <image class="pic" :src="item.img | imageFilter" mode="aspectFill"></image>
        </view>
        <view class="info">
          <view class="tit_info flex-row">
            <text class="tit">{{item.title}}</text>
            <text class="price">
              <text class="mianyi" v-if="item.fangjia == '面议' || item.fangjia == '0' ||!item.fangjia">面议</text>
              <text class="price" v-else>{{item.fangjia}}万</text>
            </text>
          </view>
           <view class="tag_info flex-row">
            <text class="tag">
              <text>{{item.shi || 0}}室{{item.ting || 0}}厅</text>
              <text>{{item.mianji}}㎡</text>
            </text>
            <text class="address">
              <text>{{item.areaname}}</text> 
              <text>{{item.community_name}}</text>
            </text>
          </view>
          <!-- 智能房源评测标签 -->
          <view class="tag_box flex-row" v-if="item.worth_labels&&item.worth_labels.length>0">
            <view class="tag flex-row" v-for="(item,index) in item.worth_labels" :key="index">
              <image class="tag_icon" v-if="item.name == 'change_price_count'" src="@/static/icon/jiang.png" />
              <image class="tag_icon" v-else-if="item.name == 'lower_community_price'" src="@/static/icon/dy.png" />
              <image class="tag_icon" v-else-if="item.name == 'lower_size_price'" src="@/static/icon/hx.png" />
              <image class="tag_icon" v-else-if="item.name == 'is_hot_hit'" src="@/static/icon/sheng.png" />
              <image class="tag_icon" v-else-if="item.name == 'school_count'" src="@/static/icon/school.png" />
              <image class="tag_icon" v-else-if="item.name == 'is_ranking'" src="@/static/icon/re.png" />
              <text>{{ item.title }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
  </view>
</template>
<script>
import myIcon from "@/components/myIcon";
import tabBar from "@/components/tabBar.vue";
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
  components: {
    myIcon,
    tabBar,
    uniLoadMore,
  },
  data() {
    return {
      tab: [{ name: "3日飙升榜" }, { name: "降价急卖" }, { name: "高性价比" }],
      tabId: 0,
      height: "",
      list: [],
      date:'',
      param: {
        page: 1,
        rows: 10,
        type: 1, //tab
      },
      get_status:"loading",
      content_text:{
          contentdown:"",
          contentrefresh:"正在加载...",
          contentnomore:"没有更多数据了"
      },
      share:{}
    };
  },
  onLoad(options) {
    if (options.type) {
      this.tabId = options.type - 1
      this.param.type = options.type
    }
    this.getData();
  },
  methods: {
    getData() {
      if (this.param.page === 1) {
          this.list=[]
      }
      this.get_status = "loading"
      this.$ajax.get(
        "house/infoWorthRanking.html",
        this.param,
        (res) => {
          if (res.data.code == 1) {
            this.list = this.list.concat(res.data.list);
            this.date = res.data.date
            if(res.data.list.length<this.param.rows){
                this.get_status = "noMore"
            }else{
                this.get_status = "more"
            }
            
          }else{
              this.get_status = "noMore"
          }
          if(res.data.setting){
            this.share.title = res.data.setting.share_title
            this.share.pic = res.data.setting.share_pic
            this.share.content = res.data.setting.share_content
          }
          var Link = window.location.href.split("?")[0]
          this.share.link = Link+'?type='+this.param.type
          this.getWxConfig()
				})
    },
    switchTab(index){
      this.tabId = index
      this.param.type = index+1
      this.param.page = 1
      this.getData()
    },
    toErshou(id){
			this.$navigateTo('/pages/ershou/detail?id='+id)
    },
    onReachBottom(){
        if(this.get_status === "noMore"){
            return
        }
        this.param.page++
        this.getData()
    }
  },
};
</script>
<style lang="scss" scoped>
.flex-row {
  display: flex;
}
.value {
  background: #1e1f25;
  min-height: 100vh;
  .list {
    margin: 0 24rpx;
    .detail {
      width: 100%;
      margin-bottom: 50rpx;
      .info {
        background: #fff;
        border-bottom-left-radius: 20rpx;
        border-bottom-right-radius: 20rpx;
        padding: 24rpx;
        .tag_info {
          font-size: 22rpx;
          margin: 24rpx 0;
          text{
            text{
              margin-right: 16rpx;
            }
          }
          .address {
            color: #999999;
          }
        }
        .tit_info {
          font-size: 32rpx;
          font-weight: 500;
          justify-content: space-between;
          .tit {
            color: #333;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
          }
          .price {
            color: #ff282f;
          }
        }
        //  智能房源评测标签
        .tag_box {
          flex-wrap: wrap;
          .tag {
            background: #fff1f5;
            color: #fb656a;
            font-size: 22rpx;
            align-items: center;
            height: 48rpx;
            padding: 0 16rpx 0 8rpx;
            margin-bottom: 24rpx;
            margin-right: 24rpx;
            border-radius: 4rpx;
            font-weight: bold;
            .tag_icon {
              width: 32rpx;
              height: 32rpx;
              margin-right: 4rpx;
            }
          }
        }
      }
      .top {
        position: relative;
        height: 360rpx;
        .tip {
          position: absolute;
          top: -12rpx;
          left: 12rpx;
          width: 88rpx;
          height: 88rpx;
          z-index: 1;
          object-fit: cover;
        }
        .pic {
          width: 100%;
          height: 100%;
          border-top-left-radius: 20rpx;
          border-top-right-radius: 20rpx;
        }
      }
    }
  }
  .value_tab {
    padding: 32rpx 50rpx 50rpx 50rpx;
    .nav_item {
      display: inline-block;
      width: 33%;
      text-align: center;
      color: #999999;
      border-bottom: 4rpx solid #3d3d3d;
      &.active {
        color: #fce6c0;
        border-bottom: 4rpx solid #fce6c0;
      }
    }
  }
  .head {
    position: relative;
    background: url(@/static/icon/bg.png) no-repeat;
    background-size: cover;
    width: 100%;
    .info {
      margin: 0 50rpx;
      padding: 176rpx 0 84rpx 0;
      .zhankai {
        margin-left: 10rpx;
        font-weight: 500;
      }
      .tit {
        color: #fff;
        font-size: 22rpx;
      }
      .tag {
        color: #666;
        font-size: 22rpx;
        margin: 24rpx 0;
        line-height: 36rpx;
        text {
          margin-left: 18rpx;
        }
      }
      .jzb {
        width: 380rpx;
        margin-bottom: 24rpx;
      }
    }
  }
}
</style>
