<template>
  <view class="share-item flex-row">
    <view class="share-left" v-if="show_date">
      <text class="month">{{ share.ctime | timeFilter('month') }}</text>
      <text class="time">{{ share.ctime | timeFilter }}</text>
    </view>
    <view class="share-right" :class="{'bottom-line': show_bottom_line}" @click='$emit("click",share.id)'>
      <view class="flex-row name-row" v-if="share.build_title&&share.ispraise!==undefined">
        <text class="share-build_name" @click.prevent.stop="toBuild">{{
          share.build_title
        }}</text>
        <view class="share-zan flex-row" @click.prevent.stop="$emit('clickpraise',{ispraise:share.ispraise, id:share.id})">
          <my-icon type="ic_zan" :color="share.ispraise?'#fb656a':'#dedede'" size="28rpx"></my-icon>
          <text class="zan_num">{{ share.praise_count || 0 }}</text>
        </view>
      </view>
      <view v-if="share.content" class="share-content" @click.prevent.stop="$emit('click', share.id)" >{{ share.content }}</view>
      <!-- 多图 -->
      <view class="share-img_list"  :class="{small: mode==='small'}" v-if="share.type<=2&&share.attached.length > 1">
        <view class="share-img_item" :class="{mb24: (share.attached.length-index)>3 }" v-for="(img, index) in share.attached" :key="index" @click.prevent.stop="prevImg(index, share.attached)">
          <image :src="img.path | imageFilter('w_220')" mode="aspectFill"></image>
          <view class="house_desc" v-if="img.type==3">{{img.desc}}</view>
        </view>
        <view class="share-img_item place"></view>
        <view class="share-img_item place"></view>
      </view>
      <!-- 单图 -->
      <view class="share-img_list" v-if="share.type<=2&&share.attached.length === 1">
        <view class="share-img_list_alone" v-for="(img, index) in share.attached" :key="index" @click.prevent.stop="prevImg(index, share.attached)">
          <!-- #ifdef H5 -->
          <img class="share-img_alone" :class="{small: mode==='small'}" :src="img.path | imageFilter('w_400')" alt="">
          <!-- #endif -->
          <!-- #ifndef H5 -->
          <image class="share-img_alone" :class="{small: mode==='small'}" :src="img.path | imageFilter('w_400')" :mode="mode==='small'?'aspectFill':'widthFix'"></image>
          <!-- #endif -->
          <view class="house_desc" v-if="share.imgs && share.imgs.length && share.imgs[0].type==3">{{share.imgs[0].desc}}</view>
        </view>
      </view>
      <!-- 视频 -->
      <view v-if="share.type===3&&share.attached.length==1" class="share-img_list share-video-list" :class="{small: mode==='small'}" @click.prevent.stop="toVideo">
          <image lazy-load  :src="share.attached[0].path | imageFilter('w_400')" :data-src="share.attached[0].path | imageFilter('w_400')" :mode="mode==='small'?'aspectFill':'widthFix'"></image>
          <image class="video-icon" src="/static/icon/video.png" mode="widthFix"></image>
      </view>
      <!-- 语音 -->
      <view v-if="share.type===4&&share.attached.length==1" class="share-img_list share-voice" @click.prevent.stop="">
        <!-- <slot name="voice" :shareData="share" :voice="share.attached[0]" /> -->
        <view class="voice felx-1" @click="playVoice(share.attached[0].path)">
          <image class="play_vioce_icon" :src="voice_playing?'/static/icon/voice/play_voice_black.gif':'/static/icon/voice/voice_icon_black.png'"></image>
          <text>{{share.attached[0].duration_format}}</text>
        </view>
      </view>
      <!-- 户型信息 -->
      <view class="house_type flex-row" @click.stop.prevent="toHouse" v-if="share.huxing&&share.house_id && share.type != 5">
        <image :src="share.huxing.path | imageFilter('w_240')" mode="aspectFill"></image>
        <view class="house_info flex-1">
          <view class="title">{{share.house_title}} 约{{share.huxing.mianji}}m²</view>
          <view class="desc flex-row">
            <text class="huxing_desc">{{share.huxing.desc}}</text>
            <text>{{share.build_title}}</text>
          </view>
        </view>
      </view>
      <!-- 发布房源的户型信息 -->
      <view class="house_type flex-row" @click.stop.prevent="toHuxing" v-if="share.type == 5 && share.house">
        <image :src="share.house.path | imageFilter('w_240')" mode="aspectFill"></image>
        <view class="house_info flex-1">
          <view class="title">{{share.house.shi}}室{{share.house.ting}}厅{{share.house.wei}}卫 约{{share.house.mianji}}m²</view>
          <view class="desc flex-row">
            <text class="huxing_desc">{{share.house.desc}}</text>
            <text>{{share.build_title}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from './myIcon.vue'
import { formatImg } from '../common/index.js'
export default {
  components: {
    myIcon
  },
  props: {
    share: {
      type: Object,
      default: () => {
        return {}
      }
    },
    voice_playing: {
      type: Boolean,
      default: false
    },
    show_date: {
      type: Boolean,
      default: true
    },
    show_bottom_line: {
      type: Boolean,
      default: true
    },
    mode:{
      type: String,
      default: "default"
    }
  },
  data() {
    return {
      innerAudioContext: uni.createInnerAudioContext()
    }
  },
  watch: {
    voice_playing(val){
      console.log('监听播放状态：', val)
      if(!val){
        this.stopPlay()
      }
    }
  },
  created(){
    this.innerAudioContext.onPlay(()=>{
      this.$emit('voicePlay')
      // this.play_voice_index = this.current_voice_index
    })
    this.innerAudioContext.onStop(()=>{
      this.$emit('voiceStop')
      // console.log("暂停了")
      // this.play_voice_index = -1
    })
    this.innerAudioContext.onEnded(()=>{
      this.$emit('voiceEnded')
      // this.play_voice_index = -1
    })
    // 监听语音播放失败事件
    this.innerAudioContext.onError(()=>{
      uni.showToast({
          title:'播放失败，请重试',
          icon:'none'
      })
      this.$emit('voiceError')
    })
  },
  filters: {
    // 获取时间的月和日
    timeFilter(val, type) {
      if (!val) {
        return ''
      }
      let time = new Date(val.replace(/-/g,'/'))
      let month = time.getMonth()
      let day = time.getDate()
      if (type === 'month') {
        return month + 1 + '月'
      }
      return (month + 1 < 10 ? '0' + (month + 1) : month + 1) + '-' + (day < 10 ? '0' + day : day)
    },
    imageFilter(img,param=""){
				return formatImg(img,param)
		}
  },
  methods: {
    prevImg(index,img_list){
      let img_arr = img_list.map(item=>formatImg(item.path,"w_800"))
      uni.previewImage({
        urls: img_arr,
        current: index
      })
    },
    playVoice(src){
      if(!src){
          uni.showToast({
              title: '暂无录音文件',
              icon: 'none'
          })
          return
      }
      this.$emit('clickvoice', src)
      if(this.voice_playing){
        this.stopPlay()
      }else{
        setTimeout(()=>{
          this.innerAudioContext.src = src
          this.innerAudioContext.play()
        }, 300)
      }
    },
    stopPlay(){
      this.innerAudioContext.stop()
    },
    toBuild(){
      if(!this.share.build_id) return
      this.$navigateTo('/pages/new_house/detail?id=' + this.share.build_id)
    },
    toHouse(){
      this.$navigateTo(`/pages/new_house/photo?bid=${this.share.build_id}&img_id=${this.share.huxing.pid}`)
    },
    toVideo(){
      this.$navigateTo(`/vr/pre_comm_video?video_id=${this.share.attached[0].id}&id=${this.share.id}`)
    },
    toHuxing(){
      this.$navigateTo(`/online/sale_detail?id=${this.share.house.id}&bid=${this.share.house.build_id}`)
    }
  },
  beforeDestroy(){
    if(this.voice_playing){
      this.stopPlay()
    }
  }
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.share-item {
  margin-bottom: 28rpx;
  .share-left {
    margin-right: 15rpx;
    min-width: 70rpx;
    .month {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
    }
    .time {
      font-size: 22rpx;
      color: #999;
    }
  }
  .share-right {
    flex: 1;
    overflow: hidden;
    &.bottom-line{
      padding-bottom: 48rpx;
    }
  }
  .name-row {
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    .share-build_name {
      font-size: 24rpx;
      color: $uni-color-primary;
    }
    .share-zan {
      align-items: center;
      font-size: 22rpx;
      color: #d8d8d8;
      .zan_num {
        margin-left: 6rpx;
        position: relative;
        top: -3rpx;
      }
    }
  }
  .share-content {
    margin-bottom: 15rpx;
    color: #333;
    white-space:normal;
    display:-webkit-box;
    -webkit-box-orient:vertical;
    -webkit-line-clamp:3;
    overflow:hidden;
  }
  .share-img_list {
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    position: relative;
    &.small{
      padding-right: 52rpx;
    }
    &.share-video-list{
      max-height: 600rpx;
      max-width: 490rpx;
      overflow: hidden;
      &.small{
        width: 176rpx;
        height: 176rpx;
        padding: 0;
        .video-icon{
          width: 60rpx;
          height: 60rpx;
        }
      }
    }
     &.share-voice{
      width: 490rpx;
    }
    
    .share-img_item {
      width: 176rpx;
      height: 176rpx;
      position: relative;
      &.mb24{
        margin-bottom: 24rpx;
      }
      &.place {
        height: 0;
        margin: 0;
      }
      image {
        width: 100%;
        height: 100%;
        
      }
    }
    .share-img_list_alone{
      width: 68%;
      max-height: 600upx;
      position: relative;
      overflow: hidden;
    }
    .share-img_alone {
      width: 100%;
      // height: 100%;
      // margin-bottom: 24rpx;
      overflow: unset;
      &.small{
        width: 176rpx;
        height: 176rpx;
        object-fit: cover;
        position: relative;
      }
    }
    .video-icon{
      width: 16vw;
      height: 0;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0);
    }
  }
}


.voice{
  flex-direction: row;
  align-items: center;
  padding: 20rpx 24rpx;
  border-radius: 12rpx;
  width: 490rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
  color: #333;
  .play_vioce_icon{
    margin-right: 16rpx;
    width: 40rpx;
    height: 40rpx;
  }
}

.house_desc{
    margin-top: 4rpx;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
}

.house_type{
  margin-top: 24rpx;
  padding: 24rpx;
  border-radius: 8rpx;
  background-color: #f8f8f8;
  image{
    width: 128rpx;
    height: 128rpx;
    margin-right: 24rpx;
    border-radius: 8rpx;
  }
  .house_info{
    .title{
      margin-bottom: 24rpx;
       font-size: 32rpx;
       color: #333;
    }
    .desc{
      font-size: 22rpx;
      color: #666;
      .huxing_desc{
        margin-right: 16rpx;
      }
    }
  }
}
</style>
