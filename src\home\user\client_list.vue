<template>
<view>
    <my-swiper :focus="focus" :autoplay="true" :interval="4000" :indicatorDots="true" :circular="true" indicatorActiveColor="#f65354" height="44vw"></my-swiper>
    <view class="item flex-box bottom-line" v-for="(item,index) in clientList" :key="item.id">
        <view class="logo-box">
            <image class="logo" :src="item.shoplogo | imgUrl" mode="aspectFill"></image>
        </view>
        <view class="info-box flex-1">
            <view class="name-line">
                <text class="name">{{item.shopname}}</text>
                <text class="is-top">客户预约</text>
                <text class="name">{{item.name}}</text>
            </view>
            <view class="title">联系电话{{item.tel}}</view>
            <view class="footer">
                <text>{{item.add_time}}</text>
                <view class="c-right" @click="showBar(index)">
                    <slot name="icon">
                        <my-icon type="dianhua" size="22" color="#91aad5"></my-icon>
                    </slot>
                </view>
                <view class="handle-bar" :class="{'show':show_bar_index==index}">
                    <view v-if="item.tel" class="bar-item right-line" @click="handleTel(item.tel)">
                        <my-icon type="dianhua" color="#ffffff"></my-icon>
                        <text>电话</text>
                    </view>
                    <view class="bar-item right-line" @click="handleSure(item.id,item.youxiao,1)">
                        <my-icon type="shikebiao" color="#ffffff"></my-icon>
                        <text>确认</text>
                    </view>
                    <view class="bar-item" @click="handleSure(item.id,item.youxiao,2)">
                        <my-icon type="huodongxiangqu" color="#ffffff"></my-icon>
                        <text>无效</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
</view>
</template>

<script>
import mySwiper from "../../components/mySwiper"
import myIcon from "../../components/icon"
import {showModal,formatImg} from '../../common/index'
import {
    uniLoadMore
} from '@dcloudio/uni-ui'
import {
    navigateTo
} from '../../common/index.js'
export default {
    data() {
        return {
            show_bar_index: -1,
            focus: [],
            is_vip:1,
            params:{
                page:1,
                rows:20
            },
            clientList:[],
            get_status: "loading",
            content_text: {
                contentdown: "",
                contentrefresh: "正在加载...",
                contentnomore: "没有更多数据了"
            }
        }
    },
    components: {
        mySwiper,
        myIcon,
        uniLoadMore
    },
    onLoad(){
        this.getFocus()
        this.getData()
    },
    filters: {
        imgUrl(val){
            return formatImg(val,'w_120')
        }
    },
    methods: {
        getData(){
             this.get_status = "loading"
            if (this.params.page == 1) {
                this.clientList = []
            }
            this.$ajax.get('memberShop/order',this.params,res=>{
                this.is_vip = res.data.shop_vip || 1
                if (res.data.code == 1) {
                    this.clientList = this.clientList.concat(res.data.list)
                    if (res.data.list.length < this.params.rows) {
                        this.get_status = "noMore"
                    } else {
                        this.get_status = "more"
                    }
                } else {
                    this.get_status = "noMore"
                    this.params.page > 1 ? this.params.page-- : this.params.page = 1
                }
            })
        },
        getFocus(){
            this.$ajax.get('memberShop/focus',{},res=>{
                if(res.data.code == 1){
                    this.focus = res.data.focus
                }
            })
        },
        showBar(index) {
            if(this.show_bar_index == index){
                this.show_bar_index = -1
                return
            }
            this.show_bar_index = index
        },
        handleTel(tel) {
            if(this.is_vip>1){
                uni.makePhoneCall({
                    phoneNumber: tel
                })
            }else{
                uni.showToast({
                    title:"升级vip才能查看手机号",
                    icon:"none"
                })
                // showModal({
                //     content:"升级VIP可见，是否升级？",
                //     confirm:()=>{
                        
                //     }
                // })
                return
            }
        },
        handleSure(id,status,operate){
            if(operate == 1&&status == operate){
                uni.showToast({
                    title:"您已确认过了",
                    icon:'none'
                })
                return
            }
            if(operate == 2&&status == operate){
                uni.showToast({
                    title:"当前已经是无效状态",
                    icon:'none'
                })
                return
            }
            this.$ajax.get('memberShop/orderOperate',{id:id,operate:operate},res=>{
                if(res.data.code == 1){
                    uni.showToast({
                        title:"操作成功"
                    })
                    this.params.page = 1;
                    this.getData()
                }else if(res.data.code == -8){
                    showModal({
                        title:"提示",
                        content:"此操作需要家装会员VIP权限，是否去升级?",
                        confirm:function(){
                            navigateTo('/home/<USER>/upgrade')
                        }
                    })
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                }
            },err=>{
                console.log(err)
                uni.showToast({
                    title:"升级vip才能查看微信号",
                    icon:"none"
                })
                return
            })
        }
    },
    onReachBottom() {
        this.params.page++
        this.getData()
    }
}
</script>

<style lang="scss">
.item {
    padding: 24upx 28upx;
    background-color: #fff;

    .logo-box {
        height: 90upx;
        width: 90upx;
        margin-right: 15upx;

        .logo {
            height: 100%;
            width: 100%;
            border-radius: 50%;
        }
    }

    .info-box {
        overflow: hidden;

        .name-line {
            padding: 10upx 0;
            margin-bottom: 10upx;
            display: flex;
            position: relative;
            align-items: center;

            .name {
                display: inline-block;
                max-width: 60%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #666;
            }

            .is-top {
                display: inline-block;
                padding: 2upx 10upx;
                margin: 0 10upx;
                border-radius: 6upx;
                font-size: 24upx;
                background-color: #ffda77;
                color: #ff6565;
            }

        }

        .title {
            font-size: $uni-font-size-blg;
            width: 100%;
            margin-bottom: 20upx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .footer {
            height: 70upx;
            line-height: 70upx;
            font-size: 26upx;
            color: #999;
            position: relative;

            .handle-bar {
                padding: 10upx 0;
                line-height: 50upx;
                position: absolute;
                top: 0;
                display: flex;
                right: 44upx;
                width: 0;
                transition: 0.3s;
                border-radius: 8upx;
                background-color: #4d5154;

                .bar-item {
                    flex: 1;
                    min-width: 33.333%;
                    overflow: hidden;
                    // text-overflow: ellipsis;
                    white-space: nowrap;
                    text-align: center;
                    color: #fff;
                    transform: 0.3s;
                }
            }

            .handle-bar.show {
                width: 480upx;
            }
        }

    }
}
</style>
