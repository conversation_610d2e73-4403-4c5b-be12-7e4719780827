<template>
	<view class="page">
		<cover-view class ="back flex-box" @click="goBack">
        <cover-image class ="back_icon" :src="'/ditu/ditu_back.png' | imageFilter('m_120')"></cover-image>
		</cover-view>
		<map id="map" :scale="mapData.scale" @regionchange ="regionchange"  :circles="cirles" :latitude="lat" :longitude="lng" layer-style=2  :markers="mapData.covers" @callouttap ="callouttap"  @markertap ="markertap">
		</map>
		<cover-view class="right_menu">
			<cover-image class="item" @click="opemLocation()" :src="'/build/v_3/map_property/nav.png' | imageFilter('m_320')">导航</cover-image>
			<cover-image v-if="type==1" class="item" @click="sendAddressToPhone()" :src="'/build/v_3/map_property/mobile.png' | imageFilter('m_320')">发送</cover-image>
			<cover-image class="item" @click="show_share_tip=true" :src="'/build/v_3/map_property/share.png' | imageFilter('m_320')">分享</cover-image>
		</cover-view>
		<cover-view class="cover_container" @touchend ="onTouchEnd" @touchmove ="onTouchMove" @touchstart ="onTouchStart">
			<cover-view class="options_btn">
					<cover-image class="icon" :class="{icon_reversal: icon_reversal}" :src="'/build/v_3/map_property/open.png' | imageFilter('m_320')"></cover-image>
				</cover-view>
			<cover-view class="shareInfo flex-row " v-if = "shareUserInfo&&shareUserInfo.mid">
				<cover-image class="share_prelogo" :src="shareUserInfo.prelogo | imageFilter('w_320')"></cover-image>
				<cover-view class="share_name flex-1" >{{shareUserInfo.cname}}</cover-view>
				<cover-view class="ask_button flex-row"  @click='getChatInfo'>
					<cover-image class ="ask_btn_image" :src="'/ditu/ask_icon.png' | imageFilter('m_120')"></cover-image>
					<cover-view class ="ask_btn_name">咨询</cover-view>
				</cover-view>
			</cover-view>

			<cover-view >
				
				<cover-view class="map-cate-list cate_list">
					<cover-view v-for="(cate, index) in cate_list" :key="index" class="cate-item" :class="{active: currentName === cate.name}" @click="changeCate(cate.name)">
						<cover-view class="text">{{cate.title}}</cover-view>
					</cover-view>
				</cover-view>
				<cover-view class="chil_cate"  v-if = 'current_chil_cate&&current_chil_cate.length'>
					<cover-view v-for="(item, idx) in current_chil_cate" :key="idx" class="chil_cate-item" :class="{active: chil_cate===item}" @click="getCovers(current, current_chil_cate, item)">{{item}}</cover-view>
				</cover-view>
			</cover-view>
			<cover-view class="res_list">
				<cover-view v-if="mapData.covers.length<5" class="nodata">暂无数据</cover-view>
				<cover-view class="all_list" v-else>
					<cover-view class= "pois_list"   v-if ="currentName =='all' &&poiList[0].title" >
						<cover-view v-for ="(item,index) in poiList" :key ="index">
							<cover-view class="poi_title" >
								{{item.title}}
							</cover-view>
							<cover-view class="poi_info">
								<cover-view class="poi_item flex-row" v-for ="(info,i) in item.list" :key ="i">
									<cover-view class="poi_item_name flex-1" >
										{{info.title}}
									</cover-view>
									<cover-view class="poi_item_num flex-row " >
										<cover-view class="poi_item_num flex-row red" >

										{{info.num}} 
									</cover-view>
									<cover-view class="poi_item_num flex-row " >

										{{info.unit}} 
									</cover-view>
									</cover-view>
								</cover-view>
								<!-- <cover-view class="poi_item flex-row " >
									<cover-view class="poi_item_name flex-1 " >
										划片施教范围内学校
									</cover-view>
									<cover-view class="poi_item_num " >
										3个
									</cover-view>
								</cover-view> -->
							</cover-view>
							
						</cover-view>
					</cover-view>
					<template v-for="(item, idx) in mapData.covers" >
						<cover-view class="list_" :key="idx"  v-show = "item.title">
							<!-- 房价分析 -->
							<cover-view class ="list_item" v-if ="from ==1"  @click ="moveTo(item.latitude,item.longitude)">
              	<price :item='item'></price>
              </cover-view> 
              <cover-view class ="list_item" v-if ="from ==2" @click ="moveTo(item.latitude,item.longitude)" >
              	<data-card :item='item' type="tudi"></data-card>
              </cover-view>
							<cover-view class ="list_item" v-if ="from ==3" :class ="{matop:idx ==0}" @click ="moveTo(item.latitude,item.longitude)" >
              	<data-card :item='item' type="yushou"></data-card>
              </cover-view>
							<cover-view class ="list_item marb0" v-if  ='from ==4'  :class ="{matop:idx ==0,}"    @click ="moveTo(item.latitude,item.longitude)"   v-show="item.title" >
              	<school :item='item'></school>
              </cover-view> 
							
								<!-- <cover-view class="list_title flex-row" v-if =" currentName=='all' &&item.name!=='all'">
									<cover-image class= "list_title_img" :src="'/ditu/'+ item.name +'_icon.png' | imageFilter('m_80')"></cover-image>
									<cover-view>{{item.title}}</cover-view>
								</cover-view> -->
                <!-- <cover-view  class="item" v-for="(item, index) in item.list "  :key="index" v-show="item.title" @click="moveTo(item.latitude, item.longitude,index)">
                  <cover-view class="left flex-1">
                    <cover-view class="title">{{item.title}}</cover-view>
                    <cover-view class="address">{{item.address}}</cover-view>
                  </cover-view>
                  <cover-view class="distance">{{item._distance | distanceFormat}}</cover-view>
                </cover-view> -->
						</cover-view>
						
					</template>

				</cover-view>
				
			</cover-view>
			
			</cover-view>
		<send-address-to-phone
      ref="sub_send_form"
      :sub_mode="sub_mode"
      @signUp ="handleSubForm"
      :login_status="login_status"
    ></send-address-to-phone>
		<poiRules
      ref="poi_rules"
      @onsubmit="closePoiRules"
    ></poiRules>
			<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>

	</view>
</template>

<script>
	import SendAddressToPhone from './components/subForm.vue'
	import poiRules from './components/poiRules.vue'
  import dataCard from './components/dataCard.vue'
	import school from './components/school.vue'
	import price from './components/price.vue'
	import copyText from '../../common/utils/copy_text'
	import getChatInfo from '../../common/get_chat_info'
	import shareTip from '../../components/shareTip.vue'
	import checkLogin from '../../common/utils/check_login'
	export default {
		components:{
			SendAddressToPhone,
			poiRules,
      dataCard,
			school,
			price,
			shareTip
		},
		data() {
			return {
				id:"",
				type:1,
				lat:"",
				lng:"",
        from:1, //1  房价分析（调用标注点及周边 范围内小区数据  均价、租金  挂牌套数）2版块价值（调用标注点及周边土拍成交数据） 3存量分析（调用标注点及周边预售数据的关联信息展示  入市住宅套数 、商业套数、获批时间）4 教育分析（调用标注点及周边关联楼盘的 施教范围内的学校、查学校增加关联楼盘快速获取范围内楼盘标注）
				current: '价值分析',
				currentName: 'all',
				chil_cate: '',
				current_chil_cate: [],
				currentType:'配套',
				type_list:[
					{
						name:'配套'
					},
					{
						name:'POI热度'
					}
				],
				cate_list: [

				],
				mapData:{
					scale:13,
					covers: [],
				},
				cover_container_translate: uni.upx2px(300),
				animationData: {},
				icon_reversal: true,
				cirles:[],
				map:null,
				moteNum:0,
				moteNum1:0,
				matches:[],
				list:[],
				totalNum:0,
				matchesAll:[],
				shareUserInfo:{},
				show_share_tip:false,
				poiList:[],				// typeList
				currentScale:13,
				build:{},
				toLogin:true,				// typeList
			};
		},
		computed: {
			login_status() {
				return this.$store.state.user_login_status
			},
			sub_mode() {
				return this.$store.state.sub_form_mode 
			},
			oneKm(){
				return this.getLonAndLat(this.lng,this.lat,0,1000)
			},
			twoKm(){
				return this.getLonAndLat(this.lng,this.lat,0,2000)
			},
			threeKm(){
				return this.getLonAndLat(this.lng,this.lat,0,3000)
			}
		},
		onLoad(options){
			this.lat = options.lat
			this.lng = options.lng
			this.title = options.title||''
      this.from = options.from ||1
			this.type = options.type ||1
			
			if (options.shareId) {
				this.shareId = options.shareId
				this.shareType = options.shareType
			}
			if(options.id){
				this.id = options.id
				uni.showLoading({
					title: '加载中...'
				})
				this.loginState()
				// this.getCovers(this.current)
			}
			this.map = uni.createMapContext("map",this);
			this.map.includePoints({
				padding:[20,20,20,20]
			})
			uni.$on("getDataAgain",()=>{
				this.getCovers(this.current)
			})
      
		},
		onUnload(){
			uni.$off("getDataAgain")
		},
		filters:{
			distanceFormat(val){
				if(!val){
					return ''
				}
				if(val<1000){
					return Math.ceil(val)+'m'
				}else{
					return (val/1000).toFixed(1)+'km'
				}
			}
		},
		onUnload(){
			uni.$off("getDataAgain")
		},
		methods:{
			loginState() {
				checkLogin({
					success: (res) => {
							this.getCovers(this.current)
					},
					fail: (res) => {
						if (this.toLogin == false) return
						this.toLogin = false
						if (this.$store.state.user_login_status == 1) {
							uni.setStorageSync('backUrl', window.location.href)
							this.$navigateTo('/user/login/login')
						}else if (this.$store.state.user_login_status == 2){
							this.$navigateTo('/user/bind_phone/bind_phone')
						}else{
							this.getCovers()
						}
					},
					complete: (res) => {
						this.$store.state.user_login_status = res.status
					},
				})
			},
			changeCate(name){
				this.currentName = name
				let covers = this.covers 
				switch (Number(this.from)) {
					case 1:
						if(this.current =='all'){
							this.getCovers()
						}else {
							this.mapData.covers.sort((b,a)=>a[name]-b[name])
						}
						
						
						break;
					case 2:
						if(this.current =='all'){
							this.getCovers()
						}else {
							
							if(name =="all" ){
								this.mapData.covers  =  this.covers 

							}else {
								this.mapData.covers = covers.filter ((item)=>
							item.yongtu_id  == name || !item.title)
							}
							
						}
						
						
						break;
				
					case 3:
						
						if(name =="all" ){
							this.mapData.covers  =  this.covers 

						}else {
							this.mapData.covers = covers.filter ((item)=>
								item.cate_id  == name || !item.title)
							}
						break;
					case 4:
						
						if(name =="all" ){
							this.mapData.covers  =  this.covers 

						}else {
							this.mapData.covers = covers.filter ((item)=>
								item.cid  == name || !item.title)
							}
						break;
					default:
						break;
				}
			},
			getChatInfo(){
				getChatInfo(this.shareUserInfo.mid, Number(this.type)+34, this.id)
			},
			getCovers(keywords=this.current){
				let api
				// 返回经纬度 全写是百度的 简写是腾讯的
        switch (Number(this.from)) {
          case 1 :  //房价分析 周围小区数据
           api = "map/fangjiaAnalyse"
					 this.cate_list = [
						 {
							 name:'all',
							 title:'价值分析'
						 },
						 {
							 name:'avg_price',
							 title:'按均价'
						 },
						 {
							 title:'按租金',
							 name:'cz_avg_price'
						 },
						 {
							 title:'按房源数量',
							 name:'info_count'
						 }
					 ]
            break;
          case 2 :  //版块价值 周围周边土拍数据
            api ='map/plateAnalyse'
            break;
           case 3 :  //存量分析（调用标注点及周边预售数据的关联信息展示  
            api='map/stockAnalyse'
            break;
           case 4:  //教育分析（调用标注点及周边关联楼盘的 施教范围内的学校
            api='map/eduAnalyse'
            break;
        
          default:
            break;
        }


				
				this.current = keywords
				if(this.current =="价值分析") this.current =''
				if(!this.map){
						this.map = uni.createMapContext("map",this);
						this.map.includePoints({
							padding:[20,20,20,20]
						})
						this.currentScale = this.mapData.scale
				}
				this.$ajax.get(api,{id:this.id,type:this.type,sid:this.shareId,sharetype:this.shareType},(res)=>{
					uni.hideLoading()
					
					if (res.data.share) {
						this.share = res.data.share
					}
					if(res.data.code != 1){
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						})
						return
					}
					if (res.data.shareUser) {
							this.currentUserInfo=res.data.shareUser 
          if (this.currentUserInfo.adviser_id>0){
              this.currentUserInfo.shareType=1
              this.currentUserInfo.sid=this.currentUserInfo.adviser_id
          }else if (this.currentUserInfo.agent_id>0) {
            this.currentUserInfo.shareType=2
            this.currentUserInfo.sid=this.currentUserInfo.agent_id
          }else {
            this.currentUserInfo={
              sid:'',
              shareType:''
            }
          }
					}
				if (res.data.share_user) {
						this.shareUserInfo=res.data.share_user
				}
				
					
					this.title = res.data.title ||''
						this.cirles = [
						{
							longitude:this.lng,
							latitude:this.lat,
							color:"#ff0000",
							radius:1000,
							strokeWidth:1,
						},
						{
							longitude:this.lng,
							latitude:this.lat,
							color:"#ff9c00",
							radius:2000,
							strokeWidth:1
						},
						{
							longitude:this.lng,
							latitude:this.lat,
							color:"#fee500",
							fillColor:"#00000026",
							radius:3000,
							strokeWidth:1
						}
					]
					let poiList = [{title:'',list:[]},{title:'',list:[]},{title:'',list:[]}] 
					switch (Number(this.from)) {
						case 1 : 
						// 房价分析数据
							uni.setNavigationBarTitle({
								title:`${res.data.title||''}房价分析`
							})
							this.share.title = `${res.data.title||''}房价分析`
						
							res.data.statistics.map((item,i)=>{
								poiList[i].title = item.distance +'KM内'
								poiList[i].list =[{
									title:'小区数量',
									num:item.coummunityCount,
									unit:"个"
								},
									{
										title:'小区均价',
										num:item.coummunityPrice,
										unit:"元/m²"
									}
								]
							})
							this.matchesAll = res.data.communities
							if (this.type ==1) {
								this.build = res.data.build
							}
							if(this.type==3){
								this.build = res.data.community
							}
							
							console.log(this.build,123);
							this.setHouseData(res.data.communities)
							break;
						case 2 : 
							uni.setNavigationBarTitle({
								title:`${res.data.title||''}版块价值`
							})
							this.share.title = `${res.data.title||''}版块价值分析`
							res.data.statistics.map((item,i)=>{
								poiList[i].title = item.distance +'KM内'
								poiList[i].list =[{
									title:'地价平均约',
									num:item.muAvgPrice,
									unit:"万元/亩"
								},
									{
										title:'楼面均价约',
										num:item.floorAvgPrice,
										unit:"元/m²"
									}
								]
							})
						this.cate_list = res.data.tupaiCates.map(item=>{
							item.name = item.id
							item.title = item.auc_name
							item.count = item.count
							return item
						})
						this.cate_list.unshift ({
							name:'all',
							title:'价值分析'
						})
						this.matchesAll = res.data.tupais
							this.setTupaiData(res.data.tupais)
						
							// this.setPOI(this.cate_list)
							break;
						case 3 : 
							uni.setNavigationBarTitle({
									title:`${res.data.title||''}存量分析`
								})

							this.share.title = `${res.data.title||''}存量分析`
						
							res.data.statistics.map((item,i)=>{
								poiList[i].title = item.distance +'KM内'
								poiList[i].list =[{
									title:'新房预售（入市量）',
									num:item.housesCount,
									unit:"套"
								}
								]
							})
						this.cate_list = res.data.ysxkCates.map(item=>{
							item.name = item.id
							item.title = item.cate_name
							return item
						})
						this.cate_list.unshift ({
							name:'all',
							title:'价值分析'
						})
						this.matchesAll = res.data.ysxks
							this.setYushouData(res.data.ysxks)
							break;
						case 4: 
							uni.setNavigationBarTitle({
								title:`${res.data.title||''}教育分析`
							})
							this.share.title = `${res.data.title||''}教育分析`
							res.data.statistics.map((item,i)=>{
								poiList[i].title = item.distance +'KM内'
								poiList[i].list =[{
									title:'学校',
									num:item.schoolCount,
									unit:"个"
								}
								]
							})
						this.cate_list = res.data.schoolCates.map(item=>{
							item.title = item.name
							item.name = item.id
							
							return item
						})
						this.cate_list.unshift ({
							name:'all',
							title:'价值分析'
						})
						this.matchesAll = res.data.schools
						this.setSchoolData(res.data.schools)
						break;
					
						default:
							break;
					}	
					this.getWxConfig()
						// this.getPois()	
					// this.matches = res.data.matches
					this.poiList = poiList
				
					if (!this.isGet) {
						this.map.moveToLocation({
							latitude: parseFloat(this.lat)-0.0015,
							longitude: parseFloat(this.lng)
						})
					}
					this.$nextTick(()=>{
						uni.pageScrollTo({
							scrollTop: uni.upx2px(200),
							duration: 200
						})
					})
					
				})
			},
			setSchoolData(data=this.matchesAll){
				let icon = '/static/icon/none.png',lat='', lng ='' ,title='学校',bgColor='#FF8c00',color="#fff",covers=[]
				data.map(item=>{
					// #ifndef MP-BAIDU
					 lat =item.lat
					 lng = item.lng
					// #endif
					// #ifdef MP-BAIDU
					 lat =item.bd_lat
					 lng = item.bd_lng
					// #endif
					let ob = {
						width: 30,
						height: 30,
						iconPath: icon,
						latitude: lat,
						longitude:lng,
						title: item.name,
						id:item.id+'',
						cid:item.cid,
						type:item.type,
						distance:Number(item.distance),
						callout: {
							content:this.currentScale<=13?title: item.name,
							padding:this.currentScale<=13?5:10,
							fontSize:12,
							boxShadow:'none',
							bgColor,
							color,
							borderRadius: 4,
							borderColor:bgColor,
							lineHeight:1.5,
							display:'ALWAYS',
							textAlign:'center'
						},
					}
					covers.push(ob)
					return item
				})
				setTimeout(() => {
					covers.unshift({
						latitude: this.lat,
						longitude: this.lng,
						id:this.id,
						width: 0,
						height: 0,
						callout: {content:this.title ||'本楼盘',padding:10,borderRadius:4,bgColor:"#f00",color:"#ffffff",display:'ALWAYS',zIndex:11},
						iconPath: '/static/icon/center.png'
					})
				}, 200);
				covers.push({
					latitude: this.oneKm.lat,
					id:"a"+1,
					longitude: this.oneKm.lon,
					width: -1,
					height:-1,
					label: {
						content:'1公里',
						padding:2,
						borderRadius:2,
						bgColor:"inherit",
						color:"#ff0000",
						display:'ALWAYS',
						fontSize:10,
						borderWidth:0,
						x:-15,
						y:5,
						anchorX:-15,
						anchorY:5,
						borderColor:'#ffffff'
					},
					iconPath: '/static/icon/none.png'
				})
				covers.push({
					latitude: this.twoKm.lat,
					longitude: this.twoKm.lon,
					width: -1,
					height: -1,
					id:"a"+2,
					label: {
						content:'2公里',
						padding:2,
						borderRadius:2,
						bgColor:"inherit",
						color:"#ff9c00",
						display:'ALWAYS',
						fontSize:10,
						borderWidth:0,
						x:-15,
						y:5,
						anchorX:-15,
						anchorY:5
					},
					iconPath: '/static/icon/none.png'
				})
				covers.push({
						latitude: this.threeKm.lat,
						longitude: this.threeKm.lon,
						width: -1,
						height: -1,
						id:"a"+3,
						label: {
							content:'3公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#fee500",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
							},
						iconPath: '/static/icon/none.png'
					})
			
				this.mapData.covers = covers
				this.covers = covers 
			},
			setYushouData(data = this.matchesAll){
				let icon = '/static/icon/none.png',lat='', lng ='' ,title='预售',bgColor='#D2b112',color="#fff",covers=[]
				

				data.map(item=>{
					// #ifdef MP-BAIDU
					 lat =item.lat
					 lng = item.lng
					// #endif
					// #ifndef MP-BAIDU
					 lat =item.yzhou
					 lng = item.xzhou
					// #endif
					let ob = {
						width: 30,
						height: 30,
						iconPath: icon,
						latitude: lat,
						longitude:lng,
						title: item.xkzh,
						zzts:item.zzts,
						zzmj:item.zzmj,
						yszts:item.yszts,
						xmmc:item.xmmc,
						xmdz:item.xmdz,
						xkzh:item.xkzh,
						tjrq:item.tjrq,
						jzmj:item.jzmj,
						hprq:item.hprq,
						gsmc:item.gsmc,
						fzzts:item.fzzts,
						fzzmj:item.fzzmj,
						distance:item.distance,
						fenlei:item.fenlei,
						cate_id:item.cate_id,
						buildid:item.buildid,
						beizhu:item.beizhu,
						areaid:item.areaid,
						buildname:item.buildname,
						id:item.id+'',
						callout: {
							content:this.currentScale<=13?title: (item.xkzh +'\n' + (item.jzmj>0?('约'+item.jzmj+'m²') :0)),
							padding:this.currentScale<=13?5:10,
							fontSize:12,
							boxShadow:'none',
							bgColor,
							color,
							borderRadius: 4,
							borderColor:bgColor,
							lineHeight:1.5,
							display:'ALWAYS',
							textAlign:'center'
						},
					}
					covers.push(ob)
					return item
				})

				setTimeout(() => {
					covers.unshift({
						latitude: this.lat,
						longitude: this.lng,
						id:this.id,
						width: 0,
						height: 0,
						callout: {content:this.title ||'本楼盘',padding:10,borderRadius:4,bgColor:"#f00",color:"#ffffff",display:'ALWAYS',zIndex:11},
						iconPath: '/static/icon/center.png'
					})
				}, 200);
				
				covers.push({
					latitude: this.oneKm.lat,
					id:"a"+1,
					longitude: this.oneKm.lon,
					width: -1,
					height:-1,
					label: {
						content:'1公里',
						padding:2,
						borderRadius:2,
						bgColor:"inherit",
						color:"#ff0000",
						display:'ALWAYS',
						fontSize:10,
						borderWidth:0,
						x:-15,
						y:5,
						anchorX:-15,
						anchorY:5,
						borderColor:'#ffffff'
					},
					iconPath: '/static/icon/none.png'
				})
				covers.push({
					latitude: this.twoKm.lat,
					longitude: this.twoKm.lon,
					width: -1,
					height: -1,
					id:"a"+2,
					label: {
						content:'2公里',
						padding:2,
						borderRadius:2,
						bgColor:"inherit",
						color:"#ff9c00",
						display:'ALWAYS',
						fontSize:10,
						borderWidth:0,
						x:-15,
						y:5,
						anchorX:-15,
						anchorY:5
					},
					iconPath: '/static/icon/none.png'
				})
				covers.push({
						latitude: this.threeKm.lat,
						longitude: this.threeKm.lon,
						width: -1,
						height: -1,
						id:"a"+3,
						label: {
							content:'3公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#fee500",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
							},
						iconPath: '/static/icon/none.png'
					})
			
				this.mapData.covers = covers
				this.covers = covers 
			},
			setHouseData(data = this.matchesAll){

				let icon = '/static/icon/none.png',lat='', lng ='' ,title='小区',bgColor='#61c6fe',color="#fff",covers=[]
				data.map(item=>{
					// #ifdef MP-BAIDU
					 lat =item.latitude
					 lng = item.longitude
					// #endif
					// #ifndef MP-BAIDU
					 lat =item.lat
					 lng = item.lng
					// #endif
					let distance = parseInt(item.distance)
					let ob = {
						width: 30,
						height: 30,
						iconPath: icon,
						// name:title,
						
						latitude: lat,
						longitude:lng,
						title: item.title,
						name:title,
						count:item.count,
						cz_count:item.cz_count,
						info_count:item.info_count, 
						avg_price:item.avg_price,
						cz_avg_price:item.cz_avg_price,
						// showtitle:this.currentScale<13?false:true,
						id:item.id+'',
						address: item.address,
						distance: distance,
						callout: {
							content:this.currentScale<=13?title: (item.title +'\n' + (item.avg_price>0?('￥'+item.avg_price) :'未更新')),
							padding:this.currentScale<=13?5:10,
							fontSize:12,
							boxShadow:'none',
							bgColor,
							color,
							borderRadius: 4,
							borderColor:bgColor,
							lineHeight:1.5,
							display:'ALWAYS',
							textAlign:'center'
						},
					}
					covers.push(ob)
					return item
				})
				let build_title
				if (this.type ==1) {
					build_title= (this.title ||'本楼盘')+'\n' +(this.build?(this.build.price_title+this.build.price_value+this.build.price_unit):'')
				}
				if (this.type==3){
					build_title= (this.title ||'本小区')+'\n' +(this.build?(this.build.avg_price +'元/m²'):'')
				}
				 
				setTimeout(() => {
					covers.unshift({
						latitude: this.lat,
						longitude: this.lng,
						id:this.id,
						width: 0,
						height: 0,
						callout: {content:build_title,padding:10,borderRadius:4,bgColor:"#f00",color:"#ffffff",display:'ALWAYS',zIndex:11},
						iconPath: '/static/icon/center.png'
					})
				}, 200);
				
				covers.push({
					latitude: this.oneKm.lat,
					id:"a"+1,
					longitude: this.oneKm.lon,
					width: -1,
					height:-1,
					label: {
						content:'1公里',
						padding:2,
						borderRadius:2,
						bgColor:"inherit",
						color:"#ff0000",
						display:'ALWAYS',
						fontSize:10,
						borderWidth:0,
						x:-15,
						y:5,
						anchorX:-15,
						anchorY:5,
						borderColor:'#ffffff'
					},
					iconPath: '/static/icon/none.png'
				})
				covers.push({
					latitude: this.twoKm.lat,
					longitude: this.twoKm.lon,
					width: -1,
					height: -1,
					id:"a"+2,
					label: {
						content:'2公里',
						padding:2,
						borderRadius:2,
						bgColor:"inherit",
						color:"#ff9c00",
						display:'ALWAYS',
						fontSize:10,
						borderWidth:0,
						x:-15,
						y:5,
						anchorX:-15,
						anchorY:5
					},
					iconPath: '/static/icon/none.png'
				})
				covers.push({
						latitude: this.threeKm.lat,
						longitude: this.threeKm.lon,
						width: -1,
						height: -1,
						id:"a"+3,
						label: {
							content:'3公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#fee500",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
							},
						iconPath: '/static/icon/none.png'
					})
			
				this.mapData.covers = covers
			},
			setTupaiData(data = this.matchesAll){
				let icon = '/static/icon/none.png',lat='', lng ='' ,title='土拍',bgColor='#279b29',color="#fff",covers=[]
				data.map(item=>{
					 lat =item.lat
					 lng = item.lng
					let ob = {
						width: 30,
						height: 30,
						iconPath: icon,
						latitude: lat,
						longitude:lng,
						title: item.tdbh,
						chengjiaojia:item.chengjiaojia,
						zt:item.zt,
						yongtu_id:item.yongtu_id,
						yongtu:item.yongtu,
						yjl:item.yjl,
						tjrq:item.tjrq,
						tdwz:item.tdwz,
						tdname:item.tdname,
						tdbh:item.tdbh,
						cjdw:item.cjdw,
						mushu:item.mushu,
						loumianjia:item.loumianjia,
						distance:item.distance,
						cjrq:item.cjrq,
						site_area:item.site_area,
						id:item.id+'',
						distance: item.distance,
						mu_avg_price:item.mu_avg_price,
						callout: {
							content:this.currentScale<=13?title: (item.tdbh +'\n' + (item.mu_avg_price>0?('约'+item.mu_avg_price+'万元/亩') :'未更新')),
							padding:this.currentScale<=13?5:10,
							fontSize:12,
							boxShadow:'none',
							bgColor,
							color,
							borderRadius: 4,
							borderColor:bgColor,
							lineHeight:1.5,
							display:'ALWAYS',
							textAlign:'center'
						},
					}
					covers.push(ob)
					return item
				})

				setTimeout(() => {
					covers.unshift({
						latitude: this.lat,
						longitude: this.lng,
						id:this.id,
						width: 0,
						height: 0,
						callout: {content:this.title ||'本楼盘',padding:10,borderRadius:4,bgColor:"#f00",color:"#ffffff",display:'ALWAYS',zIndex:11},
						iconPath: '/static/icon/center.png'
					})
				}, 200);
				
				covers.push({
					latitude: this.oneKm.lat,
					id:"a"+1,
					longitude: this.oneKm.lon,
					width: -1,
					height:-1,
					label: {
						content:'1公里',
						padding:2,
						borderRadius:2,
						bgColor:"inherit",
						color:"#ff0000",
						display:'ALWAYS',
						fontSize:10,
						borderWidth:0,
						x:-15,
						y:5,
						anchorX:-15,
						anchorY:5,
						borderColor:'#ffffff'
					},
					iconPath: '/static/icon/none.png'
				})
				covers.push({
					latitude: this.twoKm.lat,
					longitude: this.twoKm.lon,
					width: -1,
					height: -1,
					id:"a"+2,
					label: {
						content:'2公里',
						padding:2,
						borderRadius:2,
						bgColor:"inherit",
						color:"#ff9c00",
						display:'ALWAYS',
						fontSize:10,
						borderWidth:0,
						x:-15,
						y:5,
						anchorX:-15,
						anchorY:5
					},
					iconPath: '/static/icon/none.png'
				})
				covers.push({
						latitude: this.threeKm.lat,
						longitude: this.threeKm.lon,
						width: -1,
						height: -1,
						id:"a"+3,
						label: {
							content:'3公里',
							padding:2,
							borderRadius:2,
							bgColor:"inherit",
							color:"#fee500",
							display:'ALWAYS',
							fontSize:10,
							borderWidth:0,
							x:-15,
							y:5,
							anchorX:-15,
							anchorY:5
							},
						iconPath: '/static/icon/none.png'
					})
			
				this.mapData.covers = covers
				this.covers = covers 
			},

			showPoiRule(){
				this.$refs.poi_rules.showPopup()
			},

			/**
				02
				* 根据一个经纬度及距离角度，算出另外一个经纬度
				03
				* @param {*} lon 经度 113.3960698
				04
				* @param {*} lat 纬度 22.941386
				05
				* @param {*} brng 方位角 45 ---- 正北方：000°或360° 正东方：090° 正南方：180° 正西方：270°
				06
				* @param {*} dist 90000距离(米)
				07
			*/
			getLonAndLat (lon,lat,brng,dist){
						var a=6378137;

					var b=6356752.3142;

					var f=1/298.257223563;

					var lon1 = lon*1;

					var lat1 = lat*1;

					var s = dist;

					// var alpha1 = mapNumberUtil.rad(brng);
			// * (Math.PI/180)
					var alpha1 = brng *(Math.PI/180)
					var sinAlpha1 = Math.sin(alpha1);

					var cosAlpha1 = Math.cos(alpha1);

					// var tanU1 = (1-f) * Math.tan(mapNumberUtil.rad(lat1));
					var tanU1 = (1-f) * Math.tan(lat1*(Math.PI/180));
					var cosU1 = 1 / Math.sqrt((1 + tanU1*tanU1)), sinU1 = tanU1*cosU1;

					var sigma1 = Math.atan2(tanU1, cosAlpha1);

					var sinAlpha = cosU1 * sinAlpha1;

					var cosSqAlpha = 1 - sinAlpha*sinAlpha;

					var uSq = cosSqAlpha * (a*a - b*b) / (b*b);

					var A = 1 + uSq/16384*(4096+uSq*(-768+uSq*(320-175*uSq)));

					var B = uSq/1024 * (256+uSq*(-128+uSq*(74-47*uSq)));

					var sigma = s / (b*A), sigmaP = 2*Math.PI;

					while (Math.abs(sigma-sigmaP) > 1e-12) {

							var cos2SigmaM = Math.cos(2*sigma1 + sigma);

							var sinSigma = Math.sin(sigma);

							var cosSigma = Math.cos(sigma);

							var deltaSigma = B*sinSigma*(cos2SigmaM+B/4*(cosSigma*(-1+2*cos2SigmaM*cos2SigmaM)-

							B/6*cos2SigmaM*(-3+4*sinSigma*sinSigma)*(-3+4*cos2SigmaM*cos2SigmaM)));

							sigmaP = sigma;

							sigma = s / (b*A) + deltaSigma;

					}

					var tmp = sinU1*sinSigma - cosU1*cosSigma*cosAlpha1;

					var lat2 = Math.atan2(sinU1*cosSigma + cosU1*sinSigma*cosAlpha1,(1-f)*Math.sqrt(sinAlpha*sinAlpha + tmp*tmp));

					var lambda = Math.atan2(sinSigma*sinAlpha1, cosU1*cosSigma - sinU1*sinSigma*cosAlpha1);

					var C = f/16*cosSqAlpha*(4+f*(4-3*cosSqAlpha));

					var L = lambda - (1-C) * f * sinAlpha * (sigma + C*sinSigma*(cos2SigmaM+C*cosSigma*(-1+2*cos2SigmaM*cos2SigmaM)));

					var revAz = Math.atan2(sinAlpha, -tmp); // final bearing
			// * (180/Math.PI)
					// var lonLatObj = {lon:lon1+mapNumberUtil.deg(L),lat:mapNumberUtil.deg(lat2)}
			var lonLatObj = {lon:lon1+L * (180/Math.PI),lat:lat2 * (180/Math.PI)}
					return lonLatObj
			},

			onTouchStart(e){
				this.touch_start = e.touches[0].clientY
				this.current_translate = this.cover_container_translate
			},
			onTouchMove(e){
				let difference = this.current_translate+(e.touches[0].clientY - this.touch_start)
				if(difference<=0){
					difference = 0
				}
				if(difference>=uni.upx2px(500)){
					difference = uni.upx2px(500)
				}
				this.cover_container_translate = difference
			},
			onTouchEnd(e){
				if(this.cover_container_translate<130){
					this.handleAnimation(this.cover_container_translate, 0)
				}else{
					this.handleAnimation(this.cover_container_translate, uni.upx2px(500))
				}
			},
			regionchange(){
				this.map.getScale({
					success:(res)=>{
						if (res.scale == this.currentScale )  return 
						this.currentScale = res.scale
						switch (Number(this.from)) {
							case 1:
								this.setHouseData()
								break;
							case 2:
								if (this.currentType =='配套' ) {
									this.setTupaiData()
								}
								break;
							case 3:
								this.setYushouData()
								break;
							case 4:
								this.setSchoolData()
								break;
						
							default:
								break;
						}
					}
				})

			},
			callouttap(e){
				if (e.detail.markerId == this.id ) return 
				let covers = []
				this.mapData.covers.map(item=>{
					covers.push(item)
				})
				let item = 	this.mapData.covers.find(item=>item.id == e.detail.markerId )	
				let i = 	this.mapData.covers.findIndex(item=>item.id == e.detail.markerId )	
				// item.callout.content = item.title 
				
				let title = ''
				switch (Number(this.from)) {
					case 1:
						title =item.title +'\n' + (item.avg_price>0?('￥'+item.avg_price) :'未更新')
						
						break;
					case 2:
						title =item.tdbh +'\n' + (item.mu_avg_price>0?('约'+item.mu_avg_price+'万元/亩') :'未更新')
						
						
						break;
					case 3:
						title =item.xkzh +'\n' + (item.jzmj>0?('约'+item.jzmj+'m²') :0)
						
						break;
					case 4:
						title =item.title
						
						break;
				
					default:
						break;
				}
				item.callout.content  = title
				item.callout.padding  = 10;
				this.mapData.covers.splice(i,1)
				this.$nextTick(res=>{
					this.mapData.covers.splice(i,0,item)
				})
				
			
			
			},
			chooseType(name){
				this.currentType = name
				if (this.currentType=='配套'){
					switch (Number(this.from)) {
						case 1:
							this.setHouseData()
							
							break;
						case 2:
							this.setTupaiData()
							
							break;
						case 3:
							this.setYushouData()
							
							break;
						case 4:
							this.setSchoolData()
							
							break;
					
						default:
							break;
					}
				}else{
						this.$nextTick(()=>{
							uni.pageScrollTo({
								scrollTop: uni.upx2px(500),
								duration: 200
							})
						})
					this.setPOI()
				}
			},
			markertap (e){
				this.markerId = e.detail.markerId
				if (this.markerId == this.id ) return 
				let item = this.mapData.covers.find(item=>item.id == e.detail.markerId )
				let i = this.mapData.covers.findIndex(item=>item.id == e.detail.markerId )
				let title = ''
				switch (Number(this.from)) {
					case 1:
						title =item.title +'\n' + (item.avg_price>0?('￥'+item.avg_price) :'未更新')
						
						break;
					case 2:
						title =item.tdbh +'\n' + (item.mu_avg_price>0?('约'+item.mu_avg_price+'万元/亩') :'未更新')
						
						
						break;
					case 3:
						title =item.xkzh +'\n' + (item.jzmj>0?('约'+item.jzmj+'m²') :0)
						
						break;
					case 4:
						title =item.title
						
						break;
				
					default:
						break;
				}
				item.callout.content  = title
				item.callout.padding  = 10;
				this.mapData.covers.splice(i,1)
				this.$nextTick(res=>{
					this.mapData.covers.splice(i,0,item)
				})
				
			},
			handleAnimation(value, last_value){
				var timer = setInterval(()=>{
					if(last_value===0){
						this.cover_container_translate-=3
						if(this.cover_container_translate<=0){
							this.cover_container_translate = 0
							clearInterval(timer)
						}
					}
					if(last_value>value){
						this.cover_container_translate+=3
						if(this.cover_container_translate>=last_value){
							this.cover_container_translate = last_value
							clearInterval(timer)
						}
					}
				}, 6)
			},
			opemLocation(){
				uni.openLocation({
					latitude: parseFloat(this.lat),
					longitude: parseFloat(this.lng),
					name: this.title,
					address: this.address||''
				})
			},
			sendAddressToPhone(){
				this.$refs.sub_send_form.showPopup()
			},

			handleSubForm(e) {
				//提交报名
				e.from ='地图分析页面'
				e.bid = this.id
				e.type = this.sub_type || ''
				if(this.shareUserInfo&&this.shareUserInfo.adviser_id){ // 如果是置业顾问分享的
        e.share_uid = this.shareUserInfo.adviser_id
        e.is_adviser = 1
      }else if(this.shareUserInfo&&this.shareUserInfo.agent_id){ // 如果是经纪人分享的
        e.share_uid = this.shareUserInfo.agent_id
        e.is_adviser = 2
      }
				this.$ajax.post('build/signUp.html', e, res => {
					// 没开启引导登录模式或已经绑定手机号了
          if (this.sub_mode!==2||res.data.status === 3) {
            //提示报名成功
              uni.showToast({
                title: res.data.msg,
                icon: 'none'
              })
            this.$refs.sub_send_form.closeSub()
          } else {
            this.$refs.sub_send_form.getVerify()
          }
				})
			},
			handleShare(){
				let href =window.location.origin+ window.location.pathname+'?id='+this.id+'&lat='+this.lat+'&lng='+this.lng+"&type="+this.type
				if (this.currentUserInfo.sid){
					href += "&shareId="+this.currentUserInfo.sid + "&shareType="+ this.currentUserInfo.shareType
				}
				let content = `${this.title}分析:${href}`
				copyText(content, ()=>{
					uni.showToast({
						title: '复制成功，去发送给好友吧',
						icon: 'none'
					})
				})
			},
			moveTo(lat, lng,index){
				
				let item = this.mapData.covers.find(item=>item.latitude == lat )
				let i = this.mapData.covers.findIndex(item=>item.latitude == lat )
				let title = ''
				switch (Number(this.from)) {
					case 1:
						title =item.title +'\n' + (item.avg_price>0?('￥'+item.avg_price) :'未更新')
						
						break;
					case 2:
						title =item.tdbh +'\n' + (item.mu_avg_price>0?('约'+item.mu_avg_price+'万元/亩') :'未更新')
						
						
						break;
					case 3:
						title =item.xkzh +'\n' + (item.jzmj>0?('约'+item.jzmj+'m²') :0)
						
						break;
					case 4:
						title =item.title
						
						break;
				
					default:
						break;
				}
				item.callout.content  = title
				item.callout.padding  = 10;
				this.map.moveToLocation({
					latitude: parseFloat(lat)-0.0015,
					longitude: parseFloat(lng)
				})
				this.mapData.covers.splice(i,1)
				this.$nextTick(res=>{
					this.mapData.covers.splice(i,0,item)
				})
			},
			goBack (){
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					// #ifdef H5
					var ua = window.navigator.userAgent.toLowerCase();
					//通过正则表达式匹配ua中是否含有MicroMessenger字符串
					if(ua.match(/MicroMessenger/i) == 'micromessenger'){
						uni.switchTab({
							url: '/pages/index/index'
						})
					}else{
						window.history.go(-1)
					}
					// #endif
					// #ifndef H5
						uni.switchTab({
							url: '/pages/index/index'
						})
					// #endif
				}
			},
		},
		onPageScroll(e){
			if(e.scrollTop === 0){
				this.icon_reversal = true
			}else{
				this.icon_reversal = false
			}
		},
		onShareAppMessage(){
			return {
				title :`${this.title}地图周边`,
				path:"/propertyData/map/map?id=" + this.id + '&type=1&lat=' + this.lat + '&lng=' + this.lng
			}
		}
	}
</script>

<style lang="scss">
.page{
	height: calc(100vh - 44px);
	overflow: hidden;
}
	map{
		position: fixed;
		width: 100%;
		top: 0;
		bottom: 0;
		height: auto;
	}
	// #map {
	// 	::v-deep div{
	// 		text-align: center!important;
	// 		line-height: 1.5!important;
	// 	}
	// }
	.grid-box{
		position: absolute;
		width: 100%;
		bottom: 0;
	}
	.cate-icon{
		width: 48rpx;
		height: 48rpx;
	}
	.flex-row {
		display: flex;
	}

	.cover_container{
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #fff;
		transform: translateY(500rpx);
		.options_btn{
			display: flex;
			justify-content: center;
			padding-top: 24rpx;
			.icon{
				width: 48rpx;
				&.icon_reversal{
					transform: rotateX(180deg);
				}
			}
		}
	}
	.map-cate-list {
    padding: 16rpx 0;
    display: flex;
    align-items: center;
		justify-content: space-around;
    background-color: #fff;
		overflow-x:  auto;
		&.cate_list {
			justify-content: flex-start;
			padding: 8px 48rpx;
		}
    // box-shadow: 0 0 12rpx 0 rgba(0, 0, 0, 0.15);
    .cate-item {
      // flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
			min-width: 20%;
			padding: 8rpx;
      color: #999;
			position: relative;
      &.active{
        color: $uni-color-primary;
        &:after{
          content: '';
          position: absolute;
          bottom: 0;
          left: 12rpx;
          right: 12rpx;
          height: 4rpx;
          background-color: $uni-color-primary;
        }
      }
      .image {
        width: 48rpx;
        height: 48rpx;
        margin-right: 6rpx;
      }
      .text {
        font-size: 24rpx;
      }
    }
  }
	
	.chil_cate{
		padding: 24rpx 48rpx;
		display: flex;
		align-items: center;
		.chil_cate-item{
			height: 48rpx;
			line-height: 48rpx;
			padding: 0 24rpx;
			border-radius: 24rpx;
			background-color: #F8F8F8;
			~.chil_cate-item{
				margin-left: 24rpx;
			}
			&.active{
				background-color: #FFE3E3;
				color: #FF3939;
			}
		}
	}

	.res_list{
		padding: 0 48rpx;
		box-sizing: border-box;
		height: 500rpx;
		overflow-y: auto;
		width: 100%;
		.nodata{
			text-align: center;
			padding: 24rpx;
			color: #999;
		}
		.item{
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			align-items: flex-end;
			width: 100%;
			overflow: hidden;
			margin-bottom: 24rpx;
			font-size: 24rpx;
			.title{
				margin-bottom: 8rpx;
				flex-shrink: 0;
				font-size: 26rpx;
			}
			.address{
				// margin: 0 12rpx;
				flex: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				color: #999;
			}
			.distance{
				margin-left: 12rpx;
				flex-shrink: 0;
				color: #999;
			}
		}
	}
	
	.right_menu{
		position: absolute;
		right: 24rpx;
		bottom: 300rpx;
		.item{
			width: 120rpx;
			height: 120rpx;
			// margin-bottom: 24rpx;
			// display: flex;
			// align-items: center;
			// justify-content: center;
			// border-radius: 50%;
			// font-size: 20rpx;
			// background: #FFFFFF;
			// border: 0.5px solid #DADADA;
			// box-shadow: 0px 0px 10px 0px #00000019;
		}
	}
.list_ .list_item {
  margin-bottom: 18rpx;
	&.matop{
		margin-top: 20rpx;
	}
	&.marb0{
		margin-bottom: 0;
	}
}
.poi_list {
	margin-bottom: 20rpx;
}
.poi_total {
	// justify-content: space-between;
	align-items: center;
	padding:10rpx 0;
	color: #000;
	.poi_name {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
	}
	.poi_tip{
		font-size: 22rpx;
		color: #999999;
		margin-left: 24rpx;
		align-items: center;
		.wenhao {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 12px;
			height: 12px;
			font-size: 12px;
			transform: scale(0.8);
			margin-left: 5rpx;
			border: 1px solid #999;
			color: #999;
			border-radius: 50%;
		}
	}
	.poi_num {
		font-size:28rpx;
		color: #FB656A;
		margin-left: auto;
	}	
	// font-weight: 600;


}
.poi_item{
	// width: 100%;
	padding:15rpx 0;
	align-items: center;
	position: relative;
	.poi_right {
			min-width: 120rpx;
			font-family: PingFangSC-Regular;
			font-size: 28rpx;
			color: #333333;
			text-align: right;
	}
	.poi_name {
		// position: absolute;
		// top:25rpx;
		// left: 48rpx;
		padding-left: 24rpx;
		font-size: 28rpx;
		color: #fff;

	}
	.poi_zhanbi {
		width: 100%;
		// padding: 15rpx  0;
		background: #F8F8F8;;
		border-top-right-radius: 30rpx;
		border-bottom-right-radius:30rpx;
		
		// background: #fff;
		// margin-bottom: 10rpx;
		.precent{
			padding: 10rpx  0;
			background-image: linear-gradient(36deg, #FFA533 0%, #FE6C17 100%);
			border-top-right-radius: 30rpx;
			border-bottom-right-radius:30rpx;
			// text-align: right;
		}
	}

} 

.list_title{
	font-family: PingFangSC-Medium;
	font-size: 28rpx;
	padding: 8rpx 0;
	font-weight: 600;
	color: #333333;
	align-items: center;

	.list_title_img {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
		object-fit: cover;
	}
}
.shareInfo {
	// margin-bottom: 24rpx;
	align-items: center;
	padding: 15rpx 48rpx;
	.share_prelogo{
		width: 50rpx;
		height: 50rpx;
		border-radius: 100%;
		object-fit: cover;
	}
	.share_name{
		margin-left: 16rpx;
		font-size: 24rpx;
	}
	.ask_button {
		padding: 8rpx 15rpx;
		font-size: 24rpx;
		align-items: center;
		background: #FE6C17;
		color: #fff;
		border-radius: 40rpx;
		.ask_btn_image{
			width: 30rpx;
			height: 30rpx;
			object-fit: cover;
			margin-right: 8rpx;
		}
		.ask_btn_name {
			font-size: 24rpx;
		}
	}
}

.poi_title{
	margin: 8rpx 0;
	flex-shrink: 0;
	font-size: 28rpx;
	font-family: PingFangSC-Medium;
	font-weight: 600;
	color: #333333;
}
.poi_info {
	// margin-bottom: 15rpx;
	.poi_item {
		align-items: center;
		.poi_item_name{
			font-size: 22rpx;
			color: #999999;
		}
		.poi_item_num{
			margin-left: 6rpx;
			font-size: 22rpx;
			color: #333;
			.red{
				color: #FB656A;
			}
		}
	}
}
.back{
	position: fixed;
	top: 10rpx;
	left: 10rpx;
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(0,0,0,0.2);
	overflow: hidden;
	align-items: center;
	justify-content: center;
	z-index: 10;
	.back_icon {
		width: 18rpx;
		height: 36rpx;
		object-fit: cover;
	}

}
</style>
