<template>
    <view>
        <!-- #ifdef H5 || APP-PLUS -->
        <web-view :src="url" :webview-styles="webviewStyles"></web-view>
        <!-- 优先使用分享者信息 -->
        <view>
            <!-- <view class="flex-box bottom-bar user_box" v-if="position_info==3">
          <view class="flex-box user_info" @click="toShareUser()">
          <image class="user_header" :src="shareUserInfo.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
          <view class="name">
            <view class="user_name">{{shareUserInfo.cname}}</view>
            <view class="identity">{{params.sharetype==1?'置业顾问':'经纪人'}}</view>
          </view>
          </view>
          <view class="flex-box btn-group">
            <view class="tel_btn" @click="handleTel">拨打电话</view>
            <view class="chat_btn" @click="handleChat">在线咨询</view>
          </view>
        </view> -->
            <view class="contact">
                <view @click="toShareUser()">
                    <image class="user_header" :src="infodata.video.author_prelogo | imageFilter('w_80')" v-if="infodata" mode="aspectFill">
                    </image>
                    <view class="user_name">{{ shareUserInfo.cname }}</view>
                </view>
                <view @click="handleGive">
                    <image class="contact_icon" :src="imgurl"></image>
                    <view  :style="{color:fontcolor}">点赞</view>
                </view>
                <view>
                            <view @click="handleTel">

                                 <image class="contact_icon" :src="'/icon/vr/<EMAIL>' | img_icon"></image>
                    <view>电话</view>
                            </view>
                </view>
                <view @click="handleChat">
                    <image class="contact_icon" :src="'/icon/vr/<EMAIL>' | img_icon"></image>
                    <view>微聊</view>
                </view>
            </view>
        </view>
        <sub-form :groupCount="groupCount" :sub_type="sub_type" :sub_mode="sub_mode" ref="sub_form"
            @onsubmit="handleSubForm"></sub-form>
        <!-- <enturstBtn v-if="shareUserInfo.agent_id||shareUserInfo.adviser_id" :to_user="shareUserInfo" @click="$refs.enturst_popup.show()" />
          <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
              <enturstBox @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="$navigateTo('/user/login/login')" :to_user="shareUserInfo" />
          </my-popup> -->
        <!-- #endif -->
        <!-- #ifdef MP -->
        <web-view v-if="vrlink_ischeck" :src="url" :webview-styles="webviewStyles"></web-view>
        <view v-else class="tip-box" @longtap="handleCopy()">
            <view>{{ vrUrl }}</view>
            <view style="margi-top:10upx;">长按复制全景图地址在浏览器打开查看</view>
        </view>
        <!-- #endif -->
        <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
    </view>
</template>
  
<script>
import myIcon from '../components/myIcon.vue'
import { config } from '../common/index'
import allTel from '../common/all_tel.js'
import getChatInfo from '../common/get_chat_info'
import subForm from '../components/subForm'
// import enturstBtn from '@/components/enturstBtn'
// import enturstBox from '@/components/enturstBox'
import myPopup from "../components/myPopup.vue";
import loginPopup from '@/components/loginPopup'
export default {
    data() {
        return {
            url: '',
            webviewStyles: {
                progress: {
                    color: '#f65354'
                }
            },
            id: '',
            infoid: '',
            estateid: '',
            videoid: '',
            vrUrl: '',
            sub_type: 0,
            groupCount: '',
            build_info: {},
            adviser_info: {},
            float:'',
            memberObj:{},
            creat:{},
            position_info: 3,
            agent_info: {},
            currentUserInfo: {},
            shareUserInfo: {},
            params: {
                sid: '',
                sharetype: '',
            },
            cid: '',
            tel_res: {},
            show_tel_pop: false,
            infodata: '',
            imgurl: '../static/icon/dianzan.png',
            fload: '',
            is_praise: '',
            fontcolor:'',
            list:'',
        }
    },
    // #ifdef H5 || MP-BAIDU
    // #endif
    components: {
        myIcon,
        subForm,
        // enturstBtn,
        // enturstBox,
        // myPopup
    },
    computed: {
        vrlink_ischeck() {
            return this.$store.state.vrlink_ischeck
        },
        sub_mode() {
            return this.$store.state.sub_form_mode
        },
        is_open_chat() { //是否全局开启聊天
            return this.$store.state.im.ischat
        },
    },
    filters: {
        img_icon(val) {
            return config.imgDomain + val + '?x-oss-process=style/m_240'
        }
    },
    onLoad(options) {
        uni.setNavigationBarTitle({
        title: options.title,
    })
        this.videoid = options.id
        if (options.shareId) {
            this.params.sid = options.shareId,
                this.params.sharetype = options.shareType
            this.params.forward_time = options.f_time || ''
        }

        // 楼盘信息中的vr
        // if (options.buildid) {
        //     this.id = options.buildid
        //     this.params.id = this.id
        //     this.vrUrl = config.apiDomain + '/m/vr/detail?buildid=' + this.id
        //     this.api = "build/vrDetail.html"
        //     this.getVr()
        // }
        this.getVr()
    },
    methods: {
        handleCopy() {
            uni.setClipboardData({
                data: this.vrUrl,
                success: function () {
                    uni.showToast({
                        title: '复制成功，请到浏览器粘贴访问',
                        icon: 'none'
                    })
                }
            })
        },
        getVr() {
            // https://tfy.tengfun.com/wapi/video/vrDetail?id=5
            this.$ajax.get('video/vrDetail', { id: this.videoid }, res => {
                // #ifdef H5 || MP-BAIDU
                
                if (res.data.seo) {
                    this.seo = res.data.seo
                 }   
                // #endif            
                if(res.data.share) {
                    this.share =res.data.share
                }else {
                    this.share = {}
                }
                this.list = res.data.infoData
                this.share.link="https://"+window.location.host+"/h5/vr/detailto?id="+this.videoid+"&type="+this.float
                this.getWxConfig()
               
                if (res.data.code != 1) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                    return
                }
                this.is_praise = res.data.is_praise
                if (this.is_praise == 0) {
                    this.imgurl = '../static/icon/dianzan.png'
                    this.fontcolor ='white'
                } else {
                    this.imgurl = '../static/icon/reddianzan.png'
                    this.fontcolor ='#ff3e40'
                }
                this.url = res.data.video.path
                this.infodata = res.data
                this.creat = res.data
                console.log(res.data,"12314dddddddd")
                this.float = res.data.video.info_type
                if(this.float ==1){
                this.memberObj = res.data.infoData.mountMembers[0]
                }
                
            })
        },
        toHome() {
            uni.switchTab({
                url: '/pages/index/index'
            })
        },
        toSubForme(type) {
            this.sub_type = type
            this.$refs.sub_form.showPopup()
        },
        handleSubForm(e) {
            e.from = '楼盘页'
            e.bid = this.build_info.id
            e.type = this.sub_type || ''
            this.$ajax.post('build/signUp.html', e, res => {
                uni.hideLoading()
                if (res.data.code === 1) {
                    if (this.sub_mode !== 2 || res.data.status === 3) {
                        //提示报名成功
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none'
                        })
                        this.$refs.sub_form.closeSub()
                    } else {
                        this.$refs.sub_form.getVerify()
                    }
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
        handleTel() {
            console.log(this.creat,"2131241234123")
            console.log(this.float)
            this.tel_params = {
            type: '',
            callee_id: '',
            scene_type:'',
            scene_id: '',
            source:'video_detail',
            success: (res)=>{
            this.tel_res = res.data
            this.show_tel_pop = true
        }
      }
      if(this.creat.infoData.length==0){
                uni.showToast({
            title: '此楼盘没有绑定联系电话',
            icon: 'none'
            }) 
        return  
    }
    if (this.float==1){
        console.log(this.memberObj,"21333333333333333")
            if(this.creat.infoData.mountMembers.length>0){
                  if(this.creat.infoData.mountMembers[0].isAdviser==1&&this.creat.infoData.mountMembers[0].isAgent==1){
                    this.callMiddleNumber(2,this.creat.infoData.mountMembers[0].id,8,this.creat.video.id,'video_detail','')
                  }else if(this.creat.infoData.mountMembers[0].isAdviser==1&&this.creat.infoData.mountMembers[0].isAgent==0){
                    this.callMiddleNumber(2,this.creat.infoData.mountMembers[0].id,8,this.creat.video.id,'video_detail','')
                  }else if(this.creat.infoData.mountMembers[0].isAdviser==0&&this.creat.infoData.mountMembers[0].isAgent==1){
                    this.callMiddleNumber(3,this.creat.infoData.mountMembers[0].id,8,this.creat.video.id,'video_detail','')
                  }else{
                    this.callMiddleNumber(0,this.creat.infoData.mountMembers[0].id,8,this.creat.video.id,'video_detail','')
                  }
            }else{
              this.callMiddleNumber(1,this.creat.infoData.build.id,8,this.creat.video.id,'video_detail',this.creat.infoData.build.id) 
            }
      }
        if(this.float ==2){
            this.tel_params={
            type: 4,
            callee_id: this.creat.infoData.house.id,
            scene_type: 8,
            scene_id: this.creat.video.id,
          }
          allTel(this.tel_params)  
          return
        }

        if(this.float==3){
            this.tel_params.type = 6        
            this.tel_params.callee_id = this.creat.infoData.estate.id
            this.tel_params.scene_type = 8  
            this.tel_params.scene_id =  this.creat.video.id,
            allTel(this.tel_params)
            return
        }
        if(this.float==4) {
        if(this.creat.infoData.is_adviser==1&&this.creat.infoData.is_agent==1){
        this.callMiddleNumber(2,this.creat.infoData.member.adviser_id,8,this.creat.video.id,'video_detail','')
        }else if(this.creat.infoData.is_adviser==1&&this.creat.infoData.is_agent==0){
        this.callMiddleNumber(2,this.creat.infoData.member.adviser_id,8,this.creat.video.id,'video_detail','')
        }else if(this.creat.infoData.is_adviser==0&&this.creat.infoData.is_agent==1){
        this.callMiddleNumber(3,this.creat.infoData.member.id,8,this.creat.video.id,'video_detail','')
        }else {
        this.callMiddleNumber(0,this.creat.infoData.member.id,8,this.creat.video.id,'video_detail','')
        }
        return
        }
        uni.showToast({
          title: '此楼盘没有绑定联系电话',
          icon: 'none'
        })

    //   if(this.shareUserInfo.mid){//分享
    //     this.tel_params.type = this.params.sharetype==2?"3":"2"
    //     this.tel_params.callee_id = this.shareUserInfo.mid
    //     this.tel_params.scene_id = this.agent_info.id
    //     if (this.adviser_info.id) {
    //       this.tel_params.scene_type = 1
    //       this.tel_params.scene_id = this.build_info.id
    //     }
    //     if(this.agent_info.uid){
    //       if(this.estateid){ 
    //         this.tel_params.scene_type = 6
    //       }else{
    //         this.tel_params.scene_type = 4  
    //       }
    //     }
    //     allTel(this.tel_params)
    //     return
    //   }
    //   if(this.adviser_info.id){ //楼盘置业顾问
    //     this.tel_params.type = "2"
    //     this.tel_params.callee_id = this.adviser_info.id
    //     this.tel_params.scene_type = 1
    //     this.tel_params.scene_id = this.build_info.id
    //     // this.tel_params.bid = this.build_info.id
    //     allTel(this.tel_params)
    //     return
    //   }
    //   if(this.agent_info.uid){
    //     if(this.estateid){ //商业信息
    //       this.tel_params.type = 6
    //       this.tel_params.callee_id = this.agent_info.id
    //       this.tel_params.scene_type = 6
    //       this.tel_params.scene_id = this.agent_info.id
    //     }else{ //二手房租房
    //       this.tel_params.type = "3"
    //       this.tel_params.callee_id = this.agent_info.uid
    //       this.tel_params.scene_type = 4
    //       this.tel_params.scene_id = this.agent_info.id
    //     }
    //     allTel(this.tel_params)
    //     return
    //   }
    //   if ((this.build_info.phone && this.build_info.sellmobile_part) || this.build_info.tel) {
    //     this.tel_params.type = '1'
    //     this.tel_params.callee_id = this.build_info.id
    //     this.tel_params.scene_type = 1
    //     this.tel_params.scene_id = this.build_info.id
    //     allTel(this.tel_params)
    //   } else {
    //     uni.showToast({
    //       title: '此楼盘没有绑定联系电话',
    //       icon: 'none'
    //     })
    //   }
    },
     // 请求虚拟号接口
     callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
      this.tel_params = {
        type,
        callee_id,
        scene_type,
        scene_id,
        source,
        bid,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      // #ifdef MP-WEIXIN
      allTel(this.tel_params)
      // #endif
      // #ifndef MP-WEIXIN
      // 全局开启中间号且楼盘开启中间号需要检测登录
      if(this.is_open_middle_num == 1 && this.detail.use_middle_call > 0){
        this.tel_params.intercept_login = true
        this.tel_params.fail = (res)=>{
          if(res.data.code === -1){
            this.$store.state.user_login_status = 1
            this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
          }
          if(res.data.code === 2){
            this.$store.state.user_login_status = 2
            this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
          }
        }
        allTel(this.tel_params)
      }else{
        allTel(this.tel_params)
      }
      // #endif
    },
        handleChat() {
            if (!uni.getStorageSync('token')) {
                this.$navigateTo('/user/login/login')
                return
            }
            if (this.is_open_chat == 0) {
                this.toShareUser()
                return
            }
            // if (this.shareUserInfo.mid) {
            //     getChatInfo(this.shareUserInfo.mid, 28, this.build_info.id || '')
            //     return
            // }
            // if (this.adviser_info.mid) {
            //     getChatInfo(this.adviser_info.mid, 28, this.build_info.id || '')
            //     return
            // }
            if (this.list!='') {
                getChatInfo(this.list.agent.id, 28)
                return
            }

        },
        toAdviser() {
            this.$navigateTo('/pages/consultant/detail?id=' + this.adviser_info.id)
        },
        toAgent() {
            if (this.agent_info.zhongjie == 2) {
                this.$navigateTo('/pages/agent/detail?id=' + this.agent_info.uid)
            }
        },
        toShareUser() {
            if (this.params.sharetype == 1) { //置业顾问
                this.$navigateTo('/pages/consultant/detail?id=' + this.shareUserInfo.adviser_id)
            } else if (this.params.sharetype == 2) {
                this.$navigateTo('/pages/agent/detail?id=' + this.shareUserInfo.agent_id)
            }
        },
        //   点赞
        handleGive() {
            console.log("2222222222222")
            if (this.is_praise == 0) {
                this.$ajax.post('video/vrPraise', { id: parseInt(this.videoid) }, res => {
                    console.log(res)
                    if (res.data.code = 1) {
                        this.imgurl = '../static/icon/reddianzan.png'
                        this.fontcolor ='#ff3e40'
                        this.is_praise=1
                    }
                })
            } else {
                this.$ajax.post('video/cancelVrPraise', { id: parseInt(this.videoid) }, res => {
                    console.log(res)
                    if (res.data.code = 1) {
                        this.imgurl = '../static/icon/dianzan.png'
                        this.fontcolor ='white'
                        this.is_praise=0
                    } 
                })
            }
        }
    }
}
</script>
  
<style lang="scss" scoped>
// #ifdef MP
.tip-box {
    margin-top: 120upx;
    padding: 20upx;
    text-align: center;
    color: #666;
}

// #endif

// #ifdef H5 || APP-PLUS
.contact {
    position: fixed;
    bottom: 310rpx;
    z-index: 9;
    right: 0;
    color: #fff;
    text-align: center;
    padding: 10rpx 10rpx;
    border-radius: 10rpx;
    background: rgba(0, 0, 0, 0.3);

    .icon_txt {
        margin-top: 8rpx;
    }

    .user_header {
        width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
        object-fit: cover;
        border: 4rpx solid #fff;
        margin-bottom: 4rpx;
        box-sizing: border-box;
    }

    .contact_icon {
        width: 48rpx;
        height: 48rpx;
        margin-top: 30rpx;
    }
}

.contact_left {
    left: 22rpx;
}

.contact_right {
    right: 22rpx;
}

.bottom-bar {
    background-color: #fff;
    height: 88rpx;
    line-height: 88rpx;
    width: auto;
    left: 120rpx;
    right: 20rpx;
    bottom: 20rpx;
    border-radius: 26rpx;
    overflow: hidden;
    align-items: center;

    &.user_box {
        justify-content: space-between;
        box-sizing: border-box;
        border-radius: 20rpx;
        padding: 12rpx 18rpx;
        line-height: 1;
        background-color: rgba($color: #000000, $alpha: 0.32);
        color: #fff;

        .user_info {
            align-items: center;
            margin-right: 24rpx;
            max-width: 220rpx;

            .name {
                flex: 1;
                overflow: hidden;
            }
        }

        .user_header {
            width: 64rpx;
            height: 64rpx;
            margin-right: 16rpx;
            border-radius: 50%;
        }

        .user_name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-bottom: 8rpx;
            font-size: 28rpx;
        }

        .identity {
            display: inline-block;
            line-height: 28rpx;
            font-size: 20rpx;
            border-radius: 8rpx;
            padding: 0 12rpx;
            background-image: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
            box-shadow: 0 0 8rpx 0 rgba(255, 80, 0, 0.30);
        }

        .btn-group {
            width: 350rpx;
            justify-content: space-between;
        }

        .tel_btn {
            line-height: 64rpx;
            width: 160rpx;
            max-width: 160rpx;
            text-align: center;
            border-radius: 32rpx;
            font-size: 28rpx;
            color: #fff;
            background-image: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
            box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.30);
        }

        .chat_btn {
            line-height: 64rpx;
            width: 160rpx;
            max-width: 160rpx;
            text-align: center;
            border-radius: 32rpx;
            font-size: 28rpx;
            color: #fff;
            background: #65AEFB;
            box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.05);
        }
    }
}

.bottom-bar my-icon {
    line-height: 1;
    margin-right: 10upx;
}

.bottom-bar .text {
    margin-left: 6rpx;
}

.to-tel {
    align-items: center;
    justify-content: center;
    color: #fff;
    background-color: $uni-color-primary;
}

.to-collect {
    align-items: center;
    justify-content: center;
    color: #666666;
    background-color: #fff;
}

.to-share {
    align-items: center;
    justify-content: center;
    color: #666666;
    background-color: #fff;
}

// #endif</style>
  