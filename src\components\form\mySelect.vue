<template>
	<view class="select-box" :class="{'one_row':oneRow, 'bottom-line': bottomLine}">
		<label for="" v-if="label">{{label}}</label>
		<view class="select-row flex-row" :class="{small:small}" @click="pickerClick">
			<picker :disabled="disabled" @change="pickerChange" :value="index" :range="range" range-key="name">
				<view :class="index>=0?'':'novalue'">{{index>=0?range[index].name:placeholder}}</view>
			</picker>
			<text v-if="unit" class="unit">{{unit}}</text>
			<my-icon v-if="show_icon" type="ic_into" color="#999" size="32rpx"></my-icon>
		</view>
	</view>
</template>

<script>
	import myIcon from '../myIcon'
	export default {
		props:{
			label:String,
			name:String,
			value:[String,Number],
			range:Array,
			unit:String,
			small:{
				type:[Boolean],
				default:false
			},
			disabled:{
				type:[<PERSON>olean,Number,String],
				default:false
			},
			oneRow:{
				type:[Boolean],
				default:false
			},
			bottomLine:{
				type:[Boolean],
				default:false
			},
			show_icon:{
				type:[Boolean],
				default:true
			},
			placeholder: {
				type: String,
				default: '请选择'
			}
		},
		data() {
			return {
				nowValue:this.value
			};
		},
		watch:{
			value(val){
				this.nowValue = val
			}
		},
		computed:{
			index(){
				if(this.nowValue||this.nowValue==="0"||this.nowValue===0){
					for(let i = 0;i<this.range.length;i++){
						if(this.range[i].value === this.nowValue){
							return i
						}
					}
				}else{
					return -1
				}
			}
		},
		components:{
			myIcon
		},
		methods:{
			pickerChange(e){
				let index = parseInt(e.detail.value)||0
				if(index<0){
					index = 0
				}
				this.nowValue = this.range[index].value
				let res = {
					value:this.nowValue,
					_name:this.name
				}
				this.$emit('change', res)
			},
			pickerClick(){
				// #ifdef APP-PLUS
				    // uni.hideKeyboard()
				   plus.key.hideSoftKeybord()
				// #endif
				
			}
		}
	}
</script>

<style lang="scss" scoped>
	view{
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
	}
	.flex-row{
		flex-direction: row;
	}
	label{
		font-size: 22rpx;
		color: #666;
		margin-bottom: 24rpx;
	}
	.select-box{
		padding: 24rpx 0;
	}
	.one_row{
		width: 100%;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		padding: 0;
		label{
			margin-bottom: 0;
			font-size: 32rpx;
			color: #666;
			margin-right: 24rpx;
		}
		.select-row{
			flex: 1;
			picker{
				view{
					text-align: right;
					font-size: 32rpx;
					// color: #999;
				}
			}
			&.small{
				padding: $uni-spacing-col-base;
				view{
					font-size: 30rpx;
				}
			}
		}
	}
	.select-row{
		align-items: center;
		// line-height: 62upx;
		background-color: #fff;
		picker{
			flex: 1;
			// height: 62upx;
			margin-right: 10upx;
			view{
				font-size: 36rpx;
			}
		}
		.novalue{
			color: $uni-text-color-grey;
		}
		.unit{
			font-size: $uni-font-size-sm;
			color: $uni-text-color-grey;
		}
		uni-icon{
			line-height: 62upx;
			margin-left: 10upx;
		}
		&.small{
			padding: $uni-spacing-col-base;
			view{
				font-size: 30rpx;
			}
		}
	}
</style>
