<template>
  <view class="chooseImg">
    <view class ="w100 block">
      <view class="inp">
        <textarea  ref ='titleInput' :focus="isInputFocus" rows ="3" v-model ='params.title'  placeholder="请填写标题描述"></textarea>
      </view>
     
    </view>
    <view class="tags flex-row">
      <view class="tag" @click="setTitleTopic"> #添加话题 </view>
      <view class="tag" @click="setTitleAt"> @好友 </view>
    </view>
    <view class="img_type flex-row">
      <view
        class="type"
        @click="currentType = 'image'"
        :class="{ active: currentType == 'image' }"
      >
        图片
      </view>
      <view
        class="type"
        @click="currentType = 'video'"
        :class="{ active: currentType == 'video' }"
      >
        视频
      </view>
    </view>
    <view class="list flex-row" v-if = 'showC'>
      <template v-if="currentType == 'image'">
        <template v-if ='imgList.length'>
          <view class="img" v-for="(item, index) in imgList" :key="index" @click ="selectImg(item)">
            <image :src="item.url" ></image>
            <view
              class="icon"
              :style="{
                background: item.checked ? '#2D84FB' : '#fff',
                borderColor: item.checked ? '#2D84FB' : '#A0A0A0',
              }"
            >
              <my-icon type="wancheng" color="#fff" size="28rpx"></my-icon>
            </view>
          </view>
        </template>
        <template v-else >
          <view class="flex-center">暂无图片</view>
        </template>
      </template>
      <template v-if="currentType == 'video'">
        <template  v-if ='videoList.length'>
          <view class="img" v-for="(item, index) in videoList" :key="index" @click ="selectImg(item)">
            <video :src="item.url" mode="widthFix"></video>
            <view
              class="icon"
              :style="{
                background: item.checked ? '#2D84FB' : '#fff',
                borderColor: item.checked ? '#2D84FB' : '#A0A0A0',
                'border-radius':'50%'
              }"
            >
              <my-icon type="wancheng" color="#fff" size="28rpx"></my-icon>
            </view>
          </view>
        </template>
        <template v-else >
          <view class="flex-center">暂无视频</view>
        </template>
      </template>
    </view>
    <view class="btns flex-row">
      <view class="btn flex-1" @click ="shareDouyin">
        确定
      </view>
    </view>
  </view>
</template>

<script>
// import myInput from '@/components/form/newInput.vue'
import myIcon from "@/components/myIcon";

export default {
  components: {
    // myInput,
    myIcon
  },
  data () {
    return {
      imgList: [
      ],
      videoList:[],
      id: "",
      params: {
        id: '',
        title:""
      },
      currentType:"image",
      type:1,
      isInputFocus:false,
      showC:false
    }
  },
  onLoad (options) {
    if (options.id) {
      this.params.id = options.id
    }
    if (options.type) {
      this.type = options.type
    }
    uni.showLoading({
      title:"加载中...",
      mask:true
    })
    this.getList()
    this.handle('https://lf3-static.bytednsdoc.com/obj/eden-cn/fljpeh7hozbf/douyin_open/cdn/dy_open_util_v0.0.6.umd.js', 'douyin_share_script')
  },
  methods: {
    getList(){
      let url = 'release/housevideoAndPhoto'
       if (this.type == 2) {
          url = 'estateRelease/housevideoAndPhoto'
      }
      this.$ajax.get(url,{id:this.params.id},res=>{
        
        if (res.data.code ==1){
          this.imgList = res.data.images || [].map(item=>item.checked=false)
          this.videoList =res.data.videos ||[].map(item=>item.checked=false)
        }
        uni.hideLoading()
        setTimeout(() => {
          this.showC=true
        }, 200);
        
      },()=>{
        uni.hideLoading()
         this.showC=true
      })

    },
    handle(link, type = '') {
        document.getElementById(type) && document.getElementById(type).remove();
        var hm = document.createElement("script");
        hm.src = link;
        hm.id = type || (+new Date() + '')
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    },
    selectImg(item){
       if (this.currentType == 'image') {
        let  checked =this.imgList.filter(item=>item.checked)
        if (checked.length>5  && item.checked==false) {
          // this.$message.warning("最多选择6张图片")
          uni.showToast({
            title:"最多选择6张图片",
            icon:"none"
          })
          return
        }
        item.checked = !item.checked
      }
      if (this.currentType == 'video') {
        if (item.checked){
          item.checked=false
          this.$forceUpdate()
          return 
        }
        console.log(item);
        this.videoList.map(i => {
          i.checked = false
          if (i.url == item.url) {
              i.checked =true
          }
        })
      }
      console.log(item);
      this.$forceUpdate()
    },
    setTitleTopic(){
      this.isInputFocus =false
      this.setTitleAdd('#');
    },
    setTitleAt(){
      this.isInputFocus =false
      this.setTitleAdd('@');
    },
    setTitleAdd(str){
      this.params.title += ''+ str;
        this.$nextTick(()=>{
           this.isInputFocus = true
        })
      // setTimeout(() => {
        
        
      // }, 200);
      
      // console.log(this.$refs.titleInput.onfocus());
      // this.$refs.titleInput.onfocus();
    },
    shareDouyin() {
        this.image_list_path = []
        this.imgList.map(item => {
            if (item.checked) {
            this.image_list_path.push(item.url+'?x-oss-process=style/w_1920')
            }
        })
        let chekedVideo = this.videoList.find(item => item.checked)
        if (chekedVideo) {
            this.video_path = chekedVideo.url
        } else {
            this.video_path = ''
        }
        if (this.image_list_path == '' && this.video_path == '') {
          uni.showToast({
            icon: 'none',
            title: '请选择需要分享的图片或视频'
          });
          return
        }
        this.$ajax.get('douyinApi/shareH5Schema',{id:this.params.id},res => {
            if (res.data.code == 1) {
                res.data.schema.image_path = ''
                res.data.schema.image_list_path =encodeURIComponent(JSON.stringify(this.image_list_path)) 
                res.data.schema.video_path = this.video_path
                res.data.schema.title = encodeURIComponent(this.params.title)
                // snssdk1128://openplatform/share?share_type=h5&client_key=xx&key=value...
                let schema = 'snssdk1128://openplatform/share?'
                for (const key in res.data.schema) {
                  if ( res.data.schema[key]){
                    schema +=`${key}=${res.data.schema[key]}&`
                  }
                  
                }
                schema+= 'share_to_publish=0'

                try {
                  location.replace(schema)
                  //  location.replace(schema)
                } catch (error) {
                  uni.showToast({
                    title:"出现了错误",
                    icon:"none"
                  })
                }
               
            }else {
              this.$message.error(res.data.msg)
            }
        })
    },
  }
}
</script>

<style lang="scss">
// .w100 {
//   width: 100%;

// }
.chooseImg{
  padding-bottom: 100rpx;
  min-height: calc(100vh - 200rpx) ;
  background: #fff;
}
.block {
  margin :0 48rpx 24rpx;
  border-bottom: 1rpx solid  #999;
  // margin: 0 48rpx;
  .label {
    margin-bottom: 20rpx;
  }
}
.flex-row{
  display: flex;
  flex-direction: row;
}
.flex-center {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex: 1;
}
.label {
  font-size: 34rpx;
  margin-bottom: 10rpx;
}
.inp {
  textarea{
    width: 100%;
    height: 180rpx;
    background: #fff;
  }
}
.tags {
   padding-left: 48rpx;
   
  .tag {
    padding:5rpx 15rpx;
    background: #f4f4f4;
    font-size: 22rpx;
    color:#999 ;
    margin-right: 24rpx;
    border-radius: 8rpx;
    // margin-right: 10rpx;
  }
}
.img_type {
  padding: 5rpx 48rpx;
  margin-top: 24rpx;
  .type {
    padding: 5rpx 10rpx;
    color: #999;
    &.active{
      color: #2D84FB;
    }
  }
}
.list {
  padding: 24rpx 48rpx;
  flex-wrap: wrap;
  .img {
    width: 33%;
    height:200rpx;
    border-radius: 10rpx;
    overflow: hidden;
    margin-bottom: 10rpx;
    position: relative;
    image{
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    &:nth-child(2n) {
      margin-right: 0.5%;
      margin-left: 0.5%;
    }
    .icon {
      position: absolute;
      z-index: 100;
      right: 5rpx;
      top: 5rpx;
      width: 40rpx;
			height: 40rpx;
			border: 2rpx solid #f9f9f9;
			border-radius: 2rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			&.no_border {
				border-width: 0;
			}
    }
  }
}
.btns {
  justify-content: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  .btn{
    background: #2d84fb;
    padding: 16rpx 80rpx;
    border-radius: 4rpx;
    margin: 20rpx 48rpx;
    border-radius: 48rpx;
    text-align: center;
    font-size: 30rpx;
    color: #fff;
  }
}
</style>
