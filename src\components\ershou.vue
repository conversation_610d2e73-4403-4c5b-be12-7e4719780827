<template>
	<view>
		<block v-if="type==1">
			<block v-for="(item, index) in listsData" :key="index">
				<house-item :item-data="item" type="ershou" @click="toDetail" :from ="from"></house-item>
				<!-- <list-item :item-data="item" type="ershou" @click="toDetail"></list-item> -->
			</block>
		</block>
		<block v-if="type==2">
			<block v-for="(item, index) in listsData" :key="index">
				<house-item :item-data="item" type="renting" :from ="from" @click="toDetail"></house-item>
				<!-- <list-item :item-data="item" type="renting" @click="toDetail"></list-item> -->
			</block>
		</block>
		<!-- <list :listsData="listsData" @click="toDetail" v-if="type==1" titleRow="2">
			<template v-slot="{item}">
				<view class="label renting">
					<text :class="'attr'+item.zhongjie">{{item.zhongjie==2?"经纪人":"个人"}}</text>
					<text>{{item.catname}}</text>
					<text class="area">{{item.areaname}}</text>
				</view>
				<view class="info-price">
					<text class="average">{{item.begintime}}</text>
					<text class="average">{{item.shi}}室{{item.ting}}厅{{item.wei}}卫</text>
					<text class="acreage">{{item.mianji}}㎡</text>
					<text class="total">{{item.fangjia=='面议'||item.fangjia=='0'?'面议':item.fangjia+'万'}}</text>
				</view>
			</template>
		</list>
		<list :listsData="listsData" @click="toDetail" v-if="type==2" titleRow="2">
			<template v-slot="{item}">
				<view class="label renting">
					<text :class="'attr'+item.zhongjie">{{item.zhongjie==2?"经纪人":"个人"}}</text>
					<text>{{item.catname}}</text>
					<text class="area">{{item.areaname}}</text>
				</view>
				<view class="info-price">
					<text class="average">{{item.begintime}}</text>
					<text class="average">{{item.shi}}室{{item.ting}}厅{{item.wei}}卫</text>
					<text class="acreage">{{item.mianji}}㎡</text>
					<text class="total">{{item.zujin}}元/月</text>
				</view>
			</template>
		</list> -->
	</view>
</template>

<script>
	import houseItem from '../components/houseItem.vue'
	import {navigateTo} from '../common/index.js'
	export default {
		props:{
			listsData:Array,
			type:String,
			to_detail: {
				type: Boolean,
				default: true
			},
			from:{
				type:String,
				default:""
			}
		},
		components:{
			houseItem
		},
		data() {
			return {
				
			};
		},
		methods:{
			toDetail(e){
				this.$emit('click', e.detail)
				if(!this.to_detail){
					return false
				}
				// if(!e.detail.id){
				// 	return
				// }
				this.$store.state.tempData = e.detail
				if(this.type==1){
					navigateTo('/pages/ershou/detail?id='+e.detail.id)
				}else if(this.type==2){
					navigateTo('/pages/renting/detail?id='+e.detail.id)
				}
			}
		}
	}
</script>

