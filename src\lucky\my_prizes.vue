<template>
<view>
  <view class="tab_box flex-box">
    <view class="tab_item" :class="{active: list_type==='prize'}" @click="selectType('prize')">我的奖品</view>
    <view class="tab_item" :class="{active: list_type==='share'}" @click="selectType('share')">邀请记录</view>
  </view>
  <view class="prize_container" v-show="list_type==='prize'">
    <view class="top" :style="{backgroundImage: `url(${image_domain}/lucky/border_top2.png)`}"></view>
    <view class="prize_list" :style="{backgroundImage: `url(${image_domain}/lucky/container_center.png)`}">
      <view class="prize_item flex-box" v-for="(item, index) in prize_list" :key="item.id">
        <view class="pic_box">
          <image mode="aspectFill" :src="item.pic | imageFilter('w_240')"></image>
          <view class="label" :class="'label'+item.is_receive">{{item.is_receive | label}}</view>
        </view>
        <view class="info_box flex-1 bottom-line" :class="{overdue: item.is_receive===2}">
          <view class="prize_info flex-box">
            <view class="info flex-1">
              <view class="name">{{item.name}}</view>
              <view class="sponsor">{{item.sponsor||'　'}}</view>
            </view>
            <view class="info_right">
              <view class="time">{{item.ctime}}</view>
              <view class="button" v-if="item.is_receive===0" @click="onShowQrcode(item.id)">我要兑奖</view>
              <view class="button disable" v-else>我要兑奖</view>
            </view>
          </view>
          <view class="receive_time" v-if="item.time&&item.address">
            请于{{item.time}}前，到{{item.address}}处领取奖品
          </view>
        </view>
      </view>
      <view v-if="get_status==='noMore'&&params.page === 1" class="nomore">您还没有中奖记录</view>
      <uni-load-more v-else :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
    <view class="bottom" :style="{backgroundImage: `url(${image_domain}/lucky/border_bottom.png)`}"></view>
  </view>
  <view class="share_logs" v-show="list_type==='share'">
    <view class="share_item flex-box" v-for="(item, index) in share_list" :key="index">
      <view class="left flex-1">
        <view class="time">{{item.ctime}}</view>
        <view class="flex-box">
          <image class="avatar" :src="item.prelogo | imageFilter('w_80')"></image>
          <view class="uname">{{item.cname}}</view>
        </view>
      </view>
      <view class="right flex-box">
        <view class="number">+{{item.number}}</view>
        <view class="label">抽奖次数</view>
      </view>
    </view>
    <view v-if="get_status==='noMore'&&params.page === 1" class="nomore">您还没有邀请成功记录</view>
    <uni-load-more v-else :status="get_status" :content-text="content_text"></uni-load-more>
  </view>
  <my-popup ref="add_user_info" position="center" height="660rpx">
    <view class="add_user_info">
      <view class="header">
        <view class="header_title">请先完善领奖人信息</view>
      </view>
      <view class="form">
        <view class="form-item">
          <input type="text" v-model="user_info.cname" maxlength="4" placeholder="联系人">
        </view>
        <view class="form-item">
          <input type="number" v-model="user_info.tel" maxlength="11" placeholder="手机号">
          <view class="tip">手机号将作为领奖凭证</view>
        </view>
      </view>
      <view class="btn" @click="addUserInfo">提交</view>
      <view class="cancel" @click="$refs.add_user_info.hide()">取消</view>
    </view>
  </my-popup>
  <my-popup ref="qrcode_popup" position="top">
    <view class="qrcode-box">
      <view class="img-box">
        <image class="qrcode" :src="qrcode" mode="aspectFill"></image>
        <view>
          <view class="tip">请将兑奖码展示给兑奖人员扫码兑奖</view>
        </view>
      </view>
      <view class="icon-box" @click="$refs.qrcode_popup.hide()">
        <my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
      </view>
    </view>
  </my-popup>
</view>
</template>

<script>
import {config} from '../common/config'
import myPopup from '../components/myPopup'
import myIcon from '../components/myIcon'
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
  name: '',
  components: {
    myPopup,
    myIcon,
    uniLoadMore,
  },
  data () {
    return {
      prize_list:[],
      image_domain: config.imgDomain,
      user_info: {
        cname: "",
        tel: ""
      },
      qrcode: '',
      list_type: 'prize',
      share_list: [],
      nomore: false,
      get_status:"loading",
      content_text:{
        contentdown:"",
        contentrefresh:"正在加载...",
        contentnomore:"没有更多数据了"
      },
      params: {
        page: 1,
        rows: 20,
        id: ''
      }
    }
  },
  onLoad(options){
    if(options.id){
      this.id = options.id
      this.params.id = options.id
      if(options.list_type){
        this.list_type = options.list_type
      }
      this.user_info.id = options.id
      this.getData()
      uni.$on("getDataAgain", ()=>{
        this.getData()
      })
    }
  },
  filters:{
    label(val){
      switch (val){
        case 0:
          return '待领取'
        case 1:
          return '已领取'
        case 2:
          return '已过期'
        default:
          return ''
      }
    }
  },
  onUnload(){
    uni.$off("getDataAgain")
  },
  methods: {
    getData(){
      // this.nomore = false
      this.params.page === 1
      if(this.list_type=='prize'){
        this.getPrizes(this.id)
      }
      if(this.list_type=='share'){
        this.getShareLog(this.id)
      }
    },
    getPrizes(id){
      // uni.showLoading({
      //   title: '加载中...'
      // })
      if(this.params.page === 1){
        this.prize_list = []
      }
      this.get_status = "loading"
      this.$ajax.get('activity/myLuckDraw', this.params, res=>{
        // uni.hideLoading()
        if(res.data.code === 1){
          if(res.data.list.length<this.params.rows){
            this.get_status = "noMore"
          }else{
            this.get_status = "more"
          }
          var prize_list = res.data.list.map(item=>{
            if(new Date(item.time).getTime()<=(new Date().getTime()-86400000)&&item.is_receive==0){
              item.is_receive = 2
            }
            return item
          })
          this.prize_list = this.prize_list.concat(prize_list)
        }else{
          this.get_status = "noMore"
        }
      }, err=>{
        // uni.hideLoading()
      })
    },
    selectType(type){
      this.list_type = type
      this.getData()
    },
    addUserInfo(){
      this.$ajax.post('activity/addLuckDrawUserInfo.html', this.user_info, res=>{
        if(res.data.code === 1){
          this.$refs.add_user_info.hide()
          uni.showToast({
            title: res.data.msg
          })
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    onShowQrcode(id){
      this.$ajax.get('activity/createQrCode.html', {id}, res=>{
        if(res.data.is_perfect){
          // 需要完善信息
          this.$refs.add_user_info.show()
          return
        }
        if(res.data.code === 1){
          this.qrcode = res.data.img_path
          this.$refs.qrcode_popup.show()
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    getShareLog(id){
      // uni.showLoading({
      //   title: '加载中...'
      // })
      if(this.params.page === 1){
        this.share_list = []
      }
      this.get_status = "loading"
      this.$ajax.get('activity/shareLog.html', {id}, res=>{
        // uni.hideLoading()
        if(res.data.code === 1){
          if(res.data.list.length<this.params.rows){
            this.get_status = "noMore"
          }else{
            this.get_status = "more"
          }
          this.share_list = this.share_list.concat(res.data.list)
        }else{
          this.get_status = "noMore"
        }
      }, err=>{
        // uni.hideLoading()
      })
    }
  },
  onReachBottom(){
    if (this.get_status == "more"){
      this.params.page = this.params.page+1
      this.getData()
    }
    
  },
}
</script>

<style scoped lang="scss">
.tab_box{
  position: sticky;
  top: 0;
  width: 100%;
  justify-content: space-around;
  background-color: #fff;
  z-index: 10;
  .tab_item{
    text-align: center;
    width: 20%;
    padding: 24rpx;
    &.active{
      color: #e22f1a;
      border-bottom: 4rpx solid #e22f1a;
    }
  }
}
.share_logs{
  align-items: center;
  padding: 24rpx 0;
  .left{
    margin-right: 24rpx;
  }
  .share_item{
    align-items: center;
    padding: 24rpx 48rpx;
    margin-bottom: 24rpx;
    background-color: #fff;
    .time{
      font-size: 22rpx;
      color: #999;
      margin-bottom: 24rpx;
    }
    .avatar{
      width: 46rpx;
      height: 46rpx;
      border-radius: 50%;
      margin-right: 24rpx;
    }
    .uname{
      font-size: 32repx;
    }
  }
  .right{
    flex-direction: column;
    align-items: center;
    .number{
      font-weight: bold;
      margin-bottom: 12rpx;
      font-size: 32rpx;
      color: #e22f1a;
    }
    .label{
      font-size: 24rpx;
      color: #999;
    }
  }
}
.prize_container{
  padding: 48rpx;
  background-color: #e22f1a;
  min-height: calc(100vh - 44px);
  box-sizing: border-box;
  view{
    box-sizing: border-box;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .top{
    height: 50rpx;
    margin-bottom: -1rpx;
  }
  .bottom{
    margin-top: -1rpx;
    height: 48rpx;
  }
}
.prize_list{
  padding: 24rpx;
}
.prize_item{
  align-items: flex-start;
  padding: 12rpx 24rpx;
  .pic_box{
    width: 108rpx;
    height: 108rpx;
    margin-right: 20rpx;
    position: relative;
    image{
      width: 100%;
      height: 100%;
    }
    .label{
      text-align: center;
      line-height: 40rpx;
      padding: 0 12rpx;
      border-radius: 0 0 16rpx 0;
      position: absolute;
      top: 0;
      left: 0;
      font-size: 22rpx;
      color: #fff;
      background-color: #cccccc;
      &.label0{
        background-color: #e22f1a;
      }
      &.label1{
        background-color: #ffcd46;
      }
    }
  }
  .info_box{
    padding-bottom: 24rpx;
    &.overdue{
      color: #cccccc;
      view{
        color: #cccccc;
      }
    }
  }
  .prize_info{
    .name{
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 24rpx;
    }
    .sponsor{
      font-size: 24rpx;
      color: #666;
    }
    .info_right{
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }
    .time{
      margin-bottom: 20rpx;
      font-size: 22rpx;
      color: #999;
    }
    .button{
      width: 138rpx;
      text-align: center;
      height: 58rpx;
      line-height: 58rpx;
      font-size: 24rpx;
      background-image: linear-gradient(180deg, #FF8163 0%, #E22F1A 100%);
      box-shadow: 0 8rpx 16rpx 0 rgba(240,86,61,0.40);
      border-radius: 29rpx;
      border-radius: 29rpx;
      color: #fff;
      &.disable{
        background-image: none;
        background-color: #d8d8d8;
        color: #999;
        box-shadow: none
      }
    }
  }
  .receive_time{
    margin-top: 24rpx;
    font-size: 24rpx;
    color: #e22f1a;
  }
}


.qrcode-box{
  position: relative;
  margin-top: 15vh;
  .img-box{
    width: 584rpx;
    text-align: center;
    padding: 12rpx;
    margin: auto;
    background-color: #fff;
    border-radius: 8rpx;
    overflow: hidden;
    .title{
      font-size: 36rpx;
      font-weight: bold;
      text-align: center;
      color: #333;
    }
    .tip{
      padding: 0 24rpx;
      padding-bottom: 48rpx;
      font-size: 30rpx;
      text-align: center;
      color: #666;
    }
  }
  .qrcode{
    width: 560rpx;
    height: 560rpx;
  }
  .icon-box{
    position: absolute;
    bottom: -80rpx;
    width: 52rpx;
    height: 52rpx;
    left: 0;
    right: 0;
    margin: auto;
  }
}


.add_user_info{
  width: 600rpx;
  height: 660rpx;
  box-sizing: border-box;
  padding-bottom: 24rpx;
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 48rpx;
  background: #fff;
  .header{
    width: 100%;
    height: 132rpx;
    padding: 44rpx;
    box-sizing: border-box;
    background-image: linear-gradient(180deg, #FF8163 0%, #E22F1A 100%);
    border-radius: 48rpx 48rpx 0 0;
    color: #fff;
    .header_title{
      padding-left: 24rpx;
      border-left: 12rpx solid #fff;
      font-size: 32rpx;
    }
  }

  .form{
    margin: 48rpx 0;
    .form-item{
      margin-bottom: 24rpx;
      height: 88rpx;
      width: 504rpx;
      margin: 24rpx 0;
      input{
        padding: 24rpx;
        box-sizing: border-box;
        background-color: #f8f8f8;
        height: 100%;
      }
      .tip{
        margin-top: 12rpx;
        font-size: 24rpx;
        color: $uni-color-primary;
      }
    }
  }


  .btn{
    width: 504rpx;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    background-image: linear-gradient(180deg, #FF8163 0%, #E22F1A 100%);
    box-shadow: 0 8rpx 16rpx 0 rgba(240,86,61,0.40);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #fff;
  }

  .cancel{
    padding: 24rpx;
    text-align: center;
    color: #999;
  }
}

.nomore{
  text-align: center;
  padding: 24rpx;
  font-size: 28rpx;
  color: #999;
}
</style>