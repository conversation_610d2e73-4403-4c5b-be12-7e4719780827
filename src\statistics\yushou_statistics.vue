<template>
	<view class="page">
		<view class="header" :style="{backgroundImage:'url('+bgcolor+')'}">
			<view class="head-info">
				<view class="title">数据报表</view>
				<view class="update-tip">{{siteName}} {{currentMonth}}</view>
			</view>
			<view class="share_icon" @click="showSharePop">
				<my-icon type="ic_fenxiang" size="32rpx" color="#fff"></my-icon>
			</view>
		</view>

		<view class="yushou-data-info">
			<view class="middle-bar">
				<view class="middle-bar-info flex-box">
					<view class="mid-bar flex-box flex-row" :class="{'active':showType==1}" @click="clickTab(1)">
						<view class="mid-bar-info" >面积</view>
					</view>
					<view class="mid-bar flex-box flex-row" :class="{'active':showType==2}"  @click="clickTab(2)" v-if="isShowTaoshu">
						<!-- <image></image> -->
						<view class="mid-bar-info">住宅</view>
					</view>
				</view>
			</view>
			<block v-if="showType==1">
				<view class="qiun-columns">
					<view class="flex-box" >
						<view class="qiun-title-dot-light">预售总面积</view>
						<view class="dingyue flex-box" @click="showDingyuePop"><my-icon type="ic_jia" color="#333" size="24rpx"></my-icon><text class="text">订阅</text></view>
					</view>
					<view class="flex-box tips" >
						<view class="qiun-title">{{siteCity}}预售总面积统计</view>
						<view class="qiun-title-tips">{{siteName}}</view>
					</view>
					<view class="qiun-charts" >
						<canvas canvas-id="canvasLineA" id="canvasLineA" class="charts" @touchstart="touchLineA"></canvas>
					</view>
				</view>
				
				<view class="qiun-columns">
					<view class="flex-box mianji" >
						<view class="qiun-title-dot-light">住宅面积</view>
					</view>
					<view class="flex-box tips" >
						<view class="qiun-title">{{siteCity}}商品房住宅面积统计</view>
						<view class="qiun-title-tips">{{siteName}}</view>
					</view>
					<view class="qiun-charts" >
						<canvas canvas-id="canvasLineB" id="canvasLineB" class="charts" @touchstart="touchLineB"></canvas>
					</view>
				</view>
				<view class="qiun-columns">
					<view class="flex-box mianji" >
						<view class="qiun-title-dot-light">非住宅面积</view>
					</view>
					<view class="flex-box tips" >
						<view class="qiun-title">{{siteCity}}商品房非住宅面积统计</view>
						<view class="qiun-title-tips">{{siteName}}</view>
					</view>
					<view class="qiun-charts" >
						<canvas canvas-id="canvasLineC" id="canvasLineC" class="charts" @touchstart="touchLineC"></canvas>
					</view>
				</view>
				<view class="friend-tips">
					{{tips}}
				</view>
			</block>
			<block v-if="showType==2">
				<view class="qiun-columns">
					<view class="flex-box" >
						<view class="qiun-title-dot-light">预售总套数</view>
						<view class="dingyue flex-box" @click="showDingyuePop"><my-icon type="ic_jia" color="#333" size="24rpx"></my-icon><text class="text">订阅</text></view>
					</view>
					<view class="flex-box tips" >
						<view class="qiun-title">{{siteCity}}预售总套数统计</view>
						<view class="qiun-title-tips">{{siteName}}</view>
					</view>
					<view class="qiun-charts" >
						<canvas canvas-id="canvasLineJianzhuA" id="canvasLineJianzhuA" class="charts" @touchstart="touchLineJianzhuA"></canvas>
					</view>
				</view>
				
				<view class="qiun-columns">
					<view class="flex-box mianji" >
						<view class="qiun-title-dot-light">住宅套数</view>
					</view>
					<view class="flex-box tips" >
						<view class="qiun-title">{{siteCity}}商品房住宅套数统计</view>
						<view class="qiun-title-tips">{{siteName}}</view>
					</view>
					<view class="qiun-charts" >
						<canvas canvas-id="canvasLineJianzhuB" id="canvasLineJianzhuB" class="charts" @touchstart="touchLineJianzhuB"></canvas>
					</view>
				</view>
				<view class="qiun-columns">
					<view class="flex-box mianji" >
						<view class="qiun-title-dot-light">非住宅套数</view>
					</view>
					<view class="flex-box tips" >
						<view class="qiun-title">{{siteCity}}商品房非住宅套数统计</view>
						<view class="qiun-title-tips">{{siteName}}</view>
					</view>
					<view class="qiun-charts" >
						<canvas canvas-id="canvasLineJianzhuC" id="canvasLineJianzhuC" class="charts" @touchstart="touchLineJianzhuC"></canvas>
					</view>
				</view>
				<view class="friend-tips">
					{{tips}}
				</view>
			</block>
		</view>
		<share-pop ref="show_share_pop" @copyLink="copyLink" :showHaibao="false" @showCopywriting='showCopywriting'></share-pop>
		<dingyue ref="dingyue" @dingyue="dingyue" :type="type" @login="toLogin" @bindPhone="toBind" ></dingyue>
        <my-popup ref="qrcode_popup" position="top">
			<view class="qrcode-box">
				<!-- #ifdef H5 -->
				<view class="img-box">
					<view class="title titles">数据报告将通过服务号发送</view>
					<view class="tip red">请关注{{siteName}}公众号</view>
					<image @longtap="saveQrcode" class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="title">长按保存图片</view>
						<view class="tip">相册选取，识别关注</view>
					</view>
				</view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view class="img-box">
					<view class="title titles">数据报告将通过服务号发送</view>
					<view class="tip red">请关注{{siteName}}公众号</view>
					<image class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="tip">长按识别二维码关注公众号</view>
					</view>
				</view>
				<!-- #endif -->
				<view class="icon-box" @click="$refs.qrcode_popup.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
		<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
		<chat-tip></chat-tip>
	</view>
</template>

<script>
import { config } from "../common/config";
import myIcon from "../components/myIcon";
import sharePop from "../components/sharePop";
import shareTip from '../components/shareTip'
import uCharts from '@/components/u-charts/u-charts.js';
import myPopup from "../components/myPopup.vue"
import dingyue from "../components/dingyue.vue"
	var _self;
	var canvaLineA={},canvaLineB={},canvaLineC={},canvasLineJianzhuA={},canvasLineJianzhuB={},canvasLineJianzhuC={};
export default {
	data() {
		return {
			cWidth:'650rpx',
			cHeight:'500rpx',
			pixelRatio:1,
			siteCity:'',
			siteUrl:'',
			tips:'',
			currentMonth:'',
			qrcode:"",
			show_share_tip:false,
			type:"dingyue",
			showType:1,
			link:'',
		};
	},
	components: {
		myIcon,
		sharePop,
		myPopup,
		dingyue,
		shareTip
	},
	onLoad(options) {
		// this.bgcolor=config.imgDomain+'/images/new_icon/record/<EMAIL>'
		
		// this.getStatisData();
		_self = this;
		this.cWidth=uni.upx2px(750);
		this.cHeight=uni.upx2px(500);
		// this.getServerData();
		if(options.showType){
			this.showType=options.showType
		}
		this.getData();
		uni.$on('getDataAgain',()=>{
			this.getData()
		})
	},
	onShow(){
		if(this.$store.state.updatePageData){
				this.getData()
				this.$store.state.updatePageData = false
			}
	},
	onUnload(){
		uni.$off('getDataAgain')
	},
	computed: {
		siteName() {
			return this.$store.state.siteName;
        },
        bgcolor(){
            return config.imgDomain+'/images/new_icon/record/<EMAIL>' 
		},
		isShowTaoshu() { // 是否显示预售住宅非住宅套数
			return this.$store.state.isShowTaoshu
		},
	},
	methods: {
		getData() {
			this.type="dingyue"
			this.$ajax.get("build/bookingForm",{}, (res) => {
				if (res.data.code == 1) {
					let total=[],zzmj=[],fzzmj=[],totalData={month:[],data:[{name:'',data:[]}]},zzmjData={month:[],data:[{name:'',data:[]}]},fzzmjData={month:[],data:[{name:'',data:[]}]},totalJianzhuData={month:[],data:[{name:'',data:[]}]},zzmjJianzhuData={month:[],data:[{name:'',data:[]}]},fzzmjJianzhuData={month:[],data:[{name:'',data:[]}]};
					total=res.data.total
					zzmj=res.data.zzmj
					fzzmj=res.data.fzzmj
					this.currentMonth=res.data.currentMonth
					total.map(item=>{
						totalData.month.push(item.month)
						totalData.data[0].data.push(item.mj)
						totalData.data[0].name="总面积"
						totalJianzhuData.month.push(item.month)
						totalJianzhuData.data[0].data.push(item.ts)
						totalJianzhuData.data[0].name="总套数"
					})
					zzmj.map(item=>{
						zzmjData.month.push(item.month)
						zzmjData.data[0].data.push(item.mj)
						zzmjData.data[0].name="住宅面积"
						zzmjJianzhuData.month.push(item.month)
						zzmjJianzhuData.data[0].data.push(item.ts)
						zzmjJianzhuData.data[0].name="住宅套数"
					})
					fzzmj.map(item=>{
						fzzmjData.month.push(item.month)
						fzzmjData.data[0].data.push(item.mj)
						fzzmjData.data[0].name="非住宅面积"
						fzzmjJianzhuData.month.push(item.month)
						fzzmjJianzhuData.data[0].data.push(item.ts)
						fzzmjJianzhuData.data[0].name="非住宅套数"
					})
					_self.showLine("canvasLineA",totalData,'canvaLineA');
					_self.showLine("canvasLineB",zzmjData,'canvaLineB');
					_self.showLine("canvasLineC",fzzmjData,'canvaLineC');
					_self.showLine("canvasLineJianzhuA",totalJianzhuData,'canvasLineJianzhuA');
					_self.showLine("canvasLineJianzhuB",zzmjJianzhuData,'canvasLineJianzhuB');
					_self.showLine("canvasLineJianzhuC",fzzmjJianzhuData,'canvasLineJianzhuC');
					this.tips=res.data.declare
					// this.listData = this.listData.concat(res.data.list);
					// LineA.categories=res.data.data.LineA.categories;
					this.siteCity=res.data.siteCity
				// 获取登录状态
					this.$ajax.get('member/checkUserStatus', {}, res => {
						if (res.data.code !== 1) {
							this.$store.state.user_login_status = res.data.status
							if (this.$store.state.user_login_status==1){
								this.type="denglu"
								uni.setStorageSync('backUrl', window.location.href)
								this.$store.state.updatePageData=true
								this.showDingyuePop()
							}else if (this.$store.state.user_login_status==2){
								this.type='bangshouji'
								this.$store.state.updatePageData=true
								this.showDingyuePop()
							}
						}
					})
				}
				this.siteCity=res.data.siteCity||''
				if (res.data.share){
					this.share=res.data.share
				}
				this.share.title=`${this.siteCity}商品房预售许可证数据统计【最新报表】`
				this.getWxConfig()
			});
		},
			showLine(canvasId,chartData,lineName){
				let lineNames={}
				lineNames=new uCharts({
					$this:_self,
					canvasId: canvasId,
					type: 'area',
					padding:[15,60,4,0],
					fontSize:10,
					legend:{show:false},
					dataLabel:false,
					dataPointShape:true,
					background:'#FFFFFF',
					pixelRatio:_self.pixelRatio,
					categories: chartData.month,
					series: chartData.data,
					animation: true,
					xAxis: {
						type:'grid',
						gridColor:'#CCCCCC',
						gridType:'dash',
						dashLength:1,
						fontSize:10,
						// boundaryGap:"justify"
					},
					yAxis: {
						gridType:'dash',
						gridColor:'#CCCCCC',
						dashLength:8,
						splitNumber:5,
						// min:10,
						// max:180,
						format:(val)=>{
								if (_self.showType==1){
									return val.toFixed(0)+'m²'
								}else if(_self.showType==2){
									return val.toFixed(0)+'套'
								}
								
							}
							
					},
					width: _self.cWidth*_self.pixelRatio,
					height: _self.cHeight*_self.pixelRatio,
					extra: {
						area:{
							type: 'curve',
						},
					}
				});
				if (lineName=="canvaLineA"){
					canvaLineA=lineNames
				}else if (lineName=="canvaLineB"){
					canvaLineB=lineNames
				}else if(lineName=="canvaLineC"){
					canvaLineC=lineNames
				}else if (lineName=="canvasLineJianzhuA"){
					canvasLineJianzhuA=lineNames
				}else if (lineName=="canvasLineJianzhuB"){
					canvasLineJianzhuB=lineNames
				}else if(lineName=="canvasLineJianzhuC"){
					canvasLineJianzhuC=lineNames
				}
				
			},
			touchLineA(e) {
				this.$nextTick(()=>{
					canvaLineA.showToolTip(e, {
					format: function (item, category) {
						return category + ' ' + item.name + ':' + item.data+"m²"
					}
				});
				})
				
			},
			touchLineB(e) {
				canvaLineB.showToolTip(e, {
					format: function (item, category) {
						return category + ' ' + item.name + ':' + item.data+"m²" 
					}
				});
			},
			touchLineC(e) {
				canvaLineC.showToolTip(e, {
					format: function (item, category) {
						return category + ' ' + item.name + ':' + item.data+"m²" 
					}
				});
			},
			touchLineJianzhuA(e) {
				this.$nextTick(()=>{
					canvasLineJianzhuA.showToolTip(e, {
					format: function (item, category) {
						return category + ' ' + item.name + ':' + item.data+"套"
					}
				});
				})
				
			},
			touchLineJianzhuB(e) {
				canvasLineJianzhuB.showToolTip(e, {
					format: function (item, category) {
						return category + ' ' + item.name + ':' + item.data+"套" 
					}
				});
			},
			touchLineJianzhuC(e) {
				canvasLineJianzhuC.showToolTip(e, {
					format: function (item, category) {
						return category + ' ' + item.name + ':' + item.data+"套" 
					}
				});
			},
			showSharePop(){
				this.getShortLink()
				this.$refs.show_share_pop.show()
			},
			getShortLink(){
				this.link=window.location.origin+window.location.pathname+"?showType="+this.showType
				this.$ajax.get('build/shortUrl.html', {page_url:this.link }, res=>{
					if(res.data.code === 1){
					this.link = res.data.short_url
					}
				})
			},
			// 复制分享链接
		copyLink(){
			this.show_share_tip=true
			
		},
		// 复制分享内容
		showCopywriting(){
			let link=''
			// #ifdef H5
			link=window.location.origin+window.location.pathname+"?showType="+this.showType
			// #endif
			// #ifndef H5
			link=config.apiDomain+"/h5/statistics/youshou_statistics?showType="+this.showType
			// #endif
			const content = `【我正在看】${this.siteName}预售证统计报表\n【链   接】${this.link}`
			this.copyText(content, ()=>{
					uni.showToast({
					title: '复制成功,去发送给好友吧',
					icon: 'none'
					})
			})
		},
		// 复制内容
		copyText(cont, callback) {
			let oInput = document.createElement('textarea')
			oInput.value = cont
			document.body.appendChild(oInput)
			oInput.select() // 选择对象;
			oInput.setSelectionRange(0, oInput.value.length);
			document.execCommand('Copy') // 执行浏览器复制命令
			oInput.blur()
			oInput.remove()
			if(callback) callback()
		},
		toLogin(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/login/login")
		},
		toBind(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/bind_phone/bind_phone")
		},
		// 订阅
		showDingyuePop(){
			this.$refs.dingyue.showPopup()
		},
        dingyue(){
            this.$ajax.get("build/subscribeBooking",{type:1},res=>{
                if (res.data.code ==-1){
					uni.setStorageSync('backUrl', window.location.href)
					this.$navigateTo("/user/login/login")
                }else if (res.data.code ==2){
					this.type='bangshouji'
					this.showDingyuePop()
                }else if(res.data.code ==1){
                    uni.showToast({
                        title:res.data.msg,
                        icon:"success"
                    })
                    setTimeout(() => {
                        this.$refs.dingyue.hide()
                    },500)
                }else if (res.data.code ==0){  //订阅失败
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                    if (res.data.gzhewm){
                        this.qrcode=res.data.gzhewm
                        setTimeout(() => {
                            this.$refs.qrcode_popup.show()
                        }, 500);
					}
					this.$refs.dingyue.hide()

                }
            },err=>{},{disableAutoHandle:true})
		},
		clickTab(type){
			if (type ==this.showType) return
			this.showType=type
			this.getData()
		}
	},
	onShareAppMessage() {
		if (this.share) {
			return {
				title: this.share.title || "",
				content: this.share.content || "",
				imageUrl: this.share.pic ? formatImg(this.share.pic, "w_6401") : "",
			};
		}
	},
	onNavigationBarSearchInputConfirmed(e) {
		this.handleSearch(e.text);
	},
};
</script>

<style scoped lang="scss">
.page {
	background: #fff;
}
.header {
	width: 100%;
	height: 400rpx;
	background-image: linear-gradient(0deg, #f7918f 0%, #fb656a 100%);
	display: flex;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	padding: 48rpx 48rpx 352rpx;;
	box-sizing: border-box;
	position: relative;
	.share_icon{
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 24rpx;
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		background-color: rgba(0,0,0, 0.5);
		position: absolute;
		top: 48rpx;
		right: 48rpx;
}
	.head-info {
		width: 100%;
	}
	.title {
		font-size: 80rpx;
		color: #FFFFFF;
		margin:0 0 32rpx;
	}
	.update-tip {
		margin-top: 16upx;
		color: #fff;
	}
}
.yushou-data-info{
	position: relative;
	top: -20px;
	background: #fff;
	border-radius: 48rpx 48rpx 0 0;
	padding: 20px 48rpx;
	.qiun-title-dot-light{
		font-size: 40rpx;
		color: #333;
		font-weight: bold;
	}
	.mianji{
		margin-top: 48rpx;
	}
	.tips{
		justify-content: space-between;
		align-items: center;
		margin-top: 48rpx;
		margin-bottom: 24rpx;
		.qiun-title{
			font-size: 28rpx;
			color: #333333;
		}
		.qiun-title-tips{
			font-size: 22rpx;
			color: #999999;
		}
	}
}
.qiun-charts {
		width: 650upx;
		height: 500upx;
		background-color: #FFFFFF;
		// background-image: url("https://images.tengfangyun.com/images/new_icon/record/<EMAIL>");/
	}
	
	.charts {
		width: 650upx;
		height: 500upx;
		background-color: #FFFFFF;
		// background-image: url("https://images.tengfangyun.com/images/new_icon/record/<EMAIL>");
		// background-size: 50% 50%;
		// background-position: 50% 30%;
		// background-repeat: no-repeat;
	}

.friend-tips{
	margin-top: 60rpx;
	color: #999;
	line-height: 1.5;
}
.dingyue{
	margin-left: auto;
	align-items: center;
	border: 2rpx solid #999;
	border-radius: 8rpx;
	padding: 4rpx 8rpx;;

	.text{
		margin-left: 10rpx;
	}
}
//公众号二维码弹框
.qrcode-box{
	position: relative;
	margin-top: 15vh;
	.img-box{
		width: 584rpx;
		padding: 12rpx;
		margin: auto;
		background-color: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		.title{
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
			color: #333;
			&.titles{
				margin-top: 36rpx;
			}
		}
		.tip{
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			color: #666;
			&.red{
				padding-bottom: 8rpx;
				color: #f00;
			}
		}
	}
	.qrcode{
		width: 560rpx;
		height: 560rpx;
	}
	.icon-box{
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
}
.middle-bar {
	padding: 24rpx 48rpx ;
	.middle-bar-info{
		background: #f8f8f8;
		border-radius: 44rpx;

	}
	.mid-bar {
		flex: 1;
		justify-content: center;
		align-items: center;
		padding: 20rpx 0;
		&.active{
			background-image: linear-gradient(57deg, #FF5500 27%, #FFA402 80%);
			box-shadow: 0 2px 6px 0 rgba(255,145,1,0.50);
			border-radius: 22px;
			.mid-bar-info{
				color: #fff;
			}
		}
	}
}

</style>
