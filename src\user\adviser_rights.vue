<template>
  <view class="page">
    <view class="header">
      <image class="bg_img" mode="aspectFill" :src="'/images/new_icon/adviser/right_banner.png' | imageFilter('m_320')"></image>
      <view class="info">
        <text class="title">{{is_optimization===0?'当前等级：普通置业顾问':'当前等级：优选置业顾问'}}</text>
        <text class="tip" v-if="is_optimization">楼盘展位<text style="color:#0ab90a">上线中</text>，有效期至{{optimization_endtime}}</text>
        <text class="tip" v-else>楼盘展位已下线，可开启优选置业顾问提升楼盘页排位。</text>
        <view class="btn" @click="$navigateTo('/user/adviser_upgrade')">{{is_optimization===0?'去开启':'去续期'}}</view>
      </view>
    </view>
    <view class="grid-box flex-row">
      <view class="grid flex-row" v-for="(item, index) in grid_list" :key="index" @click="$navigateTo(item.url)">
        <image class="grid-img" :src="item.image | imageFilter()"></image>
        <text>{{ item.text }}</text>
      </view>
    </view>
    <view class="rights_box">
      <view class="label">所有特权</view>
      <view class="rights_list">
        <view class="rights_item flex-row" :class="{no_open:!item.is_open}" v-for="(item, index) in rights" :key="index">
          <image class="icon" :src="item.icon"></image>
          <view class="info flex-1">
            <text class="title">{{item.title}}</text>
            <text class="sub_title">{{item.desc}}</text>
          </view>
          <view class="btn" :class="{active:item.is_open}" @click="onClickItem(item)">{{item | btnText}}</view>
        </view>
      </view>
    </view>
    <view class="ranking_tip">{{$store.state.siteName}}置业顾问排名机制：
      排位置顶 > 优选置业顾问 > 刷新排名 > 普通置业顾问
    </view>
    <my-popup ref="optimization_popup" position="center" :height="popup1_height" :touch_hide="false">
      <view class="pop-content" id="popup-box1">
        <view class="header">
          <text class="title">友情提示</text>
          <image class="icon" mode="widthFix" :src="'/images/new_icon/adviser/adviser_tip.png' | imageFilter('m_320')"></image>
        </view>
        <view class="content-box">
          <view class="info">
            <text>亲爱的置业顾问</text>
          </view>
          <view class="tip">您当前展现量较低，将影响您的咨客接待数量。可开启优选置业顾问提升楼盘页排位。</view>
          <view class="btn-box">
            <view class="btn" @click="openOptimization()">去开启</view>
            <view class="can_btn" @click="$refs.optimization_popup.hide()">暂不开启</view>
          </view>
        </view>
      </view>
    </my-popup>
    <!-- <my-popup ref="popup" position="center" :height="popup_height" :touch_hide="false">
      <view class="pop-content" id="popup-box">
        <view class="header">
          <text class="title">友情提示</text>
          <image class="icon" mode="widthFix" :src="'/images/new_icon/adviser/adviser_tip.png' | imageFilter"></image>
        </view>
        <view class="content-box">
          <view class="info">
            <text>亲爱的置业顾问</text>
          </view>
          <view class="tip">您当前入驻的楼盘{{popup_tip}}可以通过<text>排位保护卡</text>或者<text>刷新排名</text>提高排位名次，增加曝光率</view>
          <view class="btn-box">
            <view class="btn" @click="openCard">去开启保护卡</view>
            <view class="can_btn" @click="$refs.popup.hide()">暂不开启</view>
          </view>
        </view>
      </view>
    </my-popup> -->
  </view>
</template>

<script>
import myPopup from '../components/myPopup'
export default {
  components: {
    myPopup
  },
  data() {
    return {
      grid_list: [
        // {
        //   text:'所有特权',
        //   image:'/images/new_icon/adviser/tequan.png',
        //   url:''
        // },
        {
          text: '购买记录',
          image: '/images/new_icon/adviser/duihuanjilu.png',
          url: '/user/adviser_pay_logs'
        },
        {
          text: '活跃度',
          image: '/images/new_icon/adviser/huoyuedu.png',
          url: '/user/adviser_activity'
        }
      ],
      rights: [],
      is_optimization: null,
      optimization_endtime: '',
      popup1_height: '',
      popup_height: '',
      popup_tip: ''
      // rights: {
      //   is_top: {},
      //   refresh: {}
      // }
    }
  },
  onLoad() {
    this.getData()
  },
  filters:{
    btnText(e){
      let name = ""
      switch (e.type){
        case 'is_top':
          name = e.is_open?'续期':"去开启"
          break;
        case 'refresh':
          name = "刷新"
          break;
        default:
          name = e.is_open?(e.is_free?'已开启':'续期'):"去开启"
      }
      return name
    }
  },
  methods: {
    getData() {
      this.$ajax.get('adviser/adviserMembership.html', {}, res => {
        if (res.data.code === 1) {
          this.rights = res.data.data
          this.is_optimization = res.data.is_optimization
          this.optimization_endtime = res.data.endtime||''
          if(!this.is_optimization){
            // 提示开通优选置业顾问
            const query = uni.createSelectorQuery().in(this)
            query.select('#popup-box1').fields({rect:true,scrollOffset:true,size:true},data => {
              this.popup1_height = data.height+'px'
            }).exec();
            this.$refs.optimization_popup.show()
            return
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    onClickItem(item){
      if(item.is_free){
        return
      }
      switch (item.type){
        case 'is_top':
          this.$navigateTo('/user/adviser_upgrade?type=2')
          break;
        case 'refresh':
          this.refresh()
          break;
        default:
          this.$navigateTo('/user/adviser_upgrade')
      }
    },
    refresh() {
      this.$ajax.get('adviser/refresh.html', {}, res => {
        uni.showToast({
          title: res.data.msg,
          icon: res.data.code === 1 ? 'success' : 'none'
        })
      })
    },
    openOptimization(){
      this.$navigateTo('/user/adviser_upgrade')
      this.$refs.optimization_popup.hide()
    },
    openCard(){
      this.$navigateTo('/user/adviser_upgrade?type=2')
      this.$refs.popup.hide()
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  background-color: #fff;
  min-height: calc(100vh - 44px);
}
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.header{
  width: 100%;
  height: 300rpx;
  position: relative;
  z-index: 2;
  .bg_img{
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: -1;
  }
  .info{
    width: 70%;
    padding: 48rpx;
    color: #E5BA72;
    .title{
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 24rpx;
      line-height: 1
    }
    .tip{
      font-size: 24rpx;
      margin-bottom: 20rpx;
      line-height: 1.6;
    }
    .btn{
      line-height: 48rpx;
      border-radius: 24rpx;
      width: 128rpx;
      text-align: center;
      font-size: 24rpx;
      color: #fff;
      background: rgba(229,186,114,0.39);
      border: 1rpx solid #E5BA72;
    }
  }
}

.pop-content{
  width: 600rpx;
  margin: 0 auto;
  padding-top: 48rpx;
  border-radius: 16rpx;
  .header{
    position: relative;
    height: 134rpx;
    padding: 48rpx;
    background-color: #252428;
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
    .title{
      line-height: 1;
      font-size: 40rpx;
      font-weight: bold;
      color: #E5BA72;
    }
    .icon{
      position: absolute;
      width: 154rpx;
      right: 48rpx;
      top: -48rpx;
    }
  }
  .content-box{
    background-color: #fff;
    padding: 48rpx;
    border-bottom-left-radius: 16rpx;
    border-bottom-right-radius: 16rpx;
    color: #333;
    .info{
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 24rpx;
    }
    .tip{
      display: block;
    }
    .btn-box{
      margin-top: 48rpx;
      padding: 0;
      .btn{
        line-height: 88rpx;
        border-radius: 44rpx;
        border-radius: 44rpx;
        background-image: linear-gradient(135deg, #E5BA72 0%, #F4DBB3 100%);
        font-size: 32rpx;
        font-weight: bold;
        text-align: center;
        color: #fff;
      }
      .can_btn{
        margin-top: 24rpx;
        color: #999;
        text-align: center;
      }
    }
  }
}

.grid-box {
  justify-content: center;
  padding: 24rpx 48rpx;
  .grid {
    flex: 1;
    justify-content: center;
    align-items: center;
    padding: 10rpx;
    border: 1rpx solid #E5BA72;
    border-radius: 8rpx;
    background: rgba(244,219,179,0.30);
    color: #333;
    ~.grid{
      margin-left: 24rpx;
    }
    .grid-img {
      width: 72rpx;
      height: 72rpx;
      margin-right: 24rpx;
    }
  }
}

.rights_box {
  padding: 24rpx 48rpx;
  .label {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 48rpx;
    color: #333;
  }
  .rights_item {
    padding: 48rpx;
    margin-bottom: 24rpx;
    align-items: center;
    line-height: 1;
    border: 1rpx solid #e5ba72;
    border-radius: 16rpx;
    &.no_open{
      border-color: #d8d8d8;
      .sub_title{
        color: #999;
      }
    }
    .icon {
      margin-right: 24rpx;
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background-color: #1e1f25;
    }
    .title {
      margin-bottom: 16rpx;
    }
    .sub_title {
      font-size: 22rpx;
      line-height: 1.5;
      color: #b3883f;
    }

    .btn {
      line-height: 48rpx;
      margin-left: 16rpx;
      padding: 0 30rpx;
      border-radius: 24rpx;
      font-size: 22rpx;
      border: 1rpx solid #e5ba72;
      color: #e5ba72;
      &.active {
        background-color: #e5ba72;
        color: #fff;
      }
    }
  }
}

.ranking_tip{
  font-size: 24rpx;
  margin-bottom: 24rpx;
  padding: 10rpx 48rpx;
  line-height: 1.6;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
  color: $uni-color-primary;
}
</style>
