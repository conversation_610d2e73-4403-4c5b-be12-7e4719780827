<template>
<view class="progress">
    <view class="header flex-box">
        <text class="btn cancel" @click="$emit('cancel')">取消</text>
        <text class="btn confirm" @click="confirm">确认</text>
    </view>
    <view class="block flex-row">
    <view class="label">选择日期<text class="necessary">*</text></view>
    <view class="progress_list flex-box">
        <view class="uni-list-cell-db">
            <picker mode="date" :value="newDate" @change="bindDateChange">
                <view class="uni-input">{{newDate||"请选择"}}</view>
            </picker>
        </view>
    </view>
    </view>
    <view class="block flex-row">
      <view class="label">选择时间<text class="necessary">*</text></view>
      <view class="progress_list flex-box">
            <view class="uni-list-cell-db">
                <picker mode="time" :value="newTime" @change="bindTimeChange">
                    <view class="uni-input">{{newTime||"请选择"}}</view>
                </picker>
            </view>
      </view>
    </view>
    <view class="tips">系统将根据设定时间提前发送跟进客户通知</view>
    <!-- <slot></slot> -->
  </view>
</template>

<script>
export default {
  data () {
      return {
        newDate:'',
        newTime:''
      }
  },
  props:{
    detail: {
        type:[Date,String],
        default:new Date()
    },
  },
  watch:{
    detail:{
        handler(newVal,oldVal) {
      　　// ...
        let temp =newVal ||new Date()
        let h = temp.getHours()
        let min =temp.getMinutes()+15
        let s =temp.getSeconds()
        let y = temp.getFullYear()
        let m =temp.getMonth()+1
        let d =temp.getDate()
        if (min>=60){
          min=min%60
          h=h+1
        }
        this.newDate =y+"-"+(m<10?"0"+m:m)+"-"+(d<10?"0"+d:d)
        this.newTime=(h<10?"0"+h:h)+":"+(min<10?"0"+min:min)+":"+(s<10?"0"+s:s)
    　　},
    　　immediate: true
    }
  },
  methods: {
    bindDateChange: function(e) {
        this.newDate = e.target.value
    },
    bindTimeChange: function(e) {
        this.newTime = e.target.value
    },
    confirm(){
      if(this.detail.time===''){
        uni.showToast({
          title: '请选择时间',
          icon: 'none'
        })
        return
      }
      if(this.detail.date===''){
        uni.showToast({
          title: '请选择日期',
          icon: 'none'
        })
        return
      }
      this.$emit('confirm',{
        time:this.newTime,
        date:this.newDate
      })
    }
  }
}
</script>

<style scoped lang="scss">
.flex-row{
    display: flex;
    flex-direction: row;
}
.progress{
  padding: 24rpx 48rpx;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  background-color: #fff;
  .header{
    justify-content: space-between;
    margin-bottom: 32rpx;
    .btn{
      display: inline-block;
      padding: 20rpx;
      font-size: 32rpx;
      &.cancel{
        color: #999;
      }
      &.confirm{
        color: $uni-color-primary;
      }
    }
  }
  .block{
    margin-bottom: 32rpx;
    justify-content: space-between;
    .label{
      margin-bottom: 32rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      .necessary{
        position: relative;
        left: 4rpx;
        top: 4rpx;
        color: $uni-color-primary;
      }
    }
    .progress_list{
      justify-content: space-between;
      flex-wrap: wrap;
      flex: 1;
      justify-content: flex-end;
    }
    .progress_item{
      min-width: 202rpx;
      max-width: 202rpx;
      height: 80rpx;
      line-height: 80rpx;
      box-sizing: border-box;
      margin-bottom: 24rpx;
      text-align: center;
      border-radius: 8rpx;
      background-color: #f8f8f8;
      color: #666;
      &.active{
        border: 1rpx solid $uni-color-primary;
        color: $uni-color-primary;
        background-color: #fff;
      }
    }

    .custom{
      align-items: center;
      color: #666;
      .progress_item{
        margin-bottom: 0;
      }
      input{
        height: 40rpx;
        text-align: left;
        padding: 20rpx 10rpx;
        margin-left: 10rpx;
        color: #666;
      }
    }

    textarea{
      height: 200rpx;
      padding: 24rpx;
      font-size: 28rpx;
      background-color: #f8f8f8;
    }
    input{
      height: 60rpx;
      padding: 0 24rpx;
      font-size: 28rpx;
      background-color: #f8f8f8;
    }
  }
}
.tips{
    color: #ff656b;
    margin: 20rpx auto 40rpx;
    text-align: center;
}
</style>
