<template>
    <scroll-view class="scroll" :class="{has_mark: current>-1}" scroll-x :scroll-into-view="'_'+(current>=2?current-2: current)">
      <view class="list">
        <view class="item" :id="'_'+index" :class="{current:current===index}" v-for="(item, index) in list" :key="index" :style="{backgroundImage: `url('${bg_img(index, 1)}')`}">
          <view v-if="index>0" class="left" :style="{backgroundImage: `url('${bg_img(index, 0)}')`}"></view>
          <view v-if="index<list.length-1" class="right" :style="{backgroundImage: `url('${bg_img(index, 2)}')`}"></view>
          <image v-if="current===index" class="mark" :src="'/build/v_3/progress/mark.png' | imageFilter('m_320')" />
          <text class="name">{{item.label}}</text>
          <text class="time">{{item.ktime}}</text>
        </view>
      </view>
    </scroll-view>
</template>

<script>
import { formatImg } from '../../../common/index.js'
export default {
  name: "salesProgress",
  props: {
    list: {
      type: Array,
      default: ()=>[]
    }
  },
  data(){
    return{
      
    }
  },
  computed:{
    current(){
      return this.list.findIndex(item=>item.is_ongoing===1)
    }
  },
  methods: {
    bg_img(index, postion){
      var num = 1
      if(index<this.current){
        num = 7
      }
      if(index===this.current){
        num = 4
      }
      if(index>this.current){
        num = 1
      }
      return formatImg(`/build/v_3/progress/0${num+postion}.png`)
     }
  },
};
</script>

<style scoped lang="scss">
.scroll{
  width: 100%;
  &.has_mark{
    margin-top: -24rpx;
    .list{
      padding-top: 24rpx;
    }
  }
}
.list{
  display: flex;
  height: 100rpx;
  flex-direction: row;
  flex-wrap: nowrap;
  background-size: 100%;
  background-repeat: no-repeat;
  margin-right: 48rpx;
  .item{
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    box-sizing: border-box;
    width: 160rpx;
    margin-right: 32rpx;
    padding-left: 12rpx;
    justify-content: center;
    font-size: 22rpx;
    color: #6f6f6f;
    background-color: #f2f2f2;
    position: relative;
    background-size: 100% 100%;
    .left{
      width: 20rpx;
      height: 100%;
      content: '';
      position: absolute;
      left: -20rpx;
      background-size: 100% 100%;
    }
    .right{
      width: 20rpx;
      height: 100%;
      content: '';
      position: absolute;
      right: -20rpx;
      background-size: 100% 100%;
    }
    &.current{
      color: #fff;
    }
    .mark{
      width: 100rpx;
      height: 48rpx;
      position: absolute;
      top: -24rpx;
      right: -4rpx;
    }
    .name{
      margin-top: 12rpx;
    }
    .time{
      font-weight: 22rpx;
    }
  }
}
</style>
