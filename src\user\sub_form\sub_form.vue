<template>
	<view class="content">
		<view class="he-box" v-if="type==1">
			<!-- <view class="ad-box">
				<image :src="img" mode="widthFix"></image>
			</view> -->
			<view class="coupon-box">
				<image class="coupon-img" :src="img | imgUrl" mode="widthFix"></image>
				<!-- <view class="coupon-title">红包</view>
				<view class="coupon-value">{{popup_desc}}</view> -->
			</view>
		</view>
		<form @submit="formSubmit" @reset="formReset">
			<view class="row label-row bottom-line">{{title}}{{leixing}}</view>
			<view class="row form-row flex-box">
				<view class="label">你的姓名</view>
				<input name="name" maxlength="10" placeholder="请输入姓名" />
			</view>
			<view class="row form-row flex-box">
				<view class="label">手机号</view>
				<input name="tel" type="number" maxlength="11" placeholder="请输入手机号" />
			</view>
			<view class="btn-box">
				<button formType="submit" class="default">确定提交</button>
			</view>
			<view class="text-center" style="font-size: 28upx;">已有<text style="color: #FF4444;">{{bm_num}}人</text>报名</view>
			<view class="text-center" style="font-size: 24upx;color: #999;">您的信息将被严格保密，请准确填写</view>
		</form>
		<view class="bullet-box" v-if ="bullet_list.length>0"  :style ="{height:height}">
            <swiper class="banner" :indicator-dots="false" :interval="2000" :circular="true" :vertical="true" :autoplay= "true" :duration="600" indicator-active-color="#f65354" :display-multiple-items="count"  :current="swiperCurrent">
                <swiper-item v-for="(item,index) in bullet_list" :key="index">
					<view class="con flex-box" id="con"><view class="con_">{{item}}</view></view>
                </swiper-item>
                <swiper-item v-if= "bullet_list.length<count">
                </swiper-item>
            </swiper>
            <!-- <bullet v-if="bullet_list.length>0&&show_bullet" :delay="bullet_delay" name_color="#333333" :bullet_list="bullet_list"></bullet> -->
        </view>
		<chat-tip></chat-tip>
	</view>
</template>

<script>
	import {formatImg} from '../../common/index'
	export default{
		data(){
			return{
				type:0,
				img:"",
				popup_desc:"",
				bm_num:"",
				title:"",
				params:{},
				leixing:0,
				count:5,
				swiperCurrent:0,
				height:'',
				bullet_list:[],
			}
		},
		onLoad(options){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
			// console.log(options)
			if(options.title){
				uni.setNavigationBarTitle({
					title: options.title
				})
				this.title = options.title
			}
			this.bid = options.bid || '' //获取传递过来的楼盘id 用来获取报名人数
			if(options.type){
				this.type = options.type
			}
			if(options.leixing){  //0  团购 1 开盘 2 降价 3  获取优惠  4 预约看房  5 动态变动
				this.leixing= options.leixing
				if (this.leixing==1){
					this.leixing = "开盘通知我"
				}else if(this.leixing==0){
						this.leixing = "团购报名"
				}
				else if(this.leixing==2){
						this.leixing = "降价通知我"
				}
				else if(this.leixing==3){
						this.leixing = "预约优惠报名"
				}

				else if(this.leixing==4){
						this.leixing = "预约看房报名"
				}
				else if(this.leixing==5){
						this.leixing = "动态变动通知我"
				}	else if(this.leixing==6){
						this.leixing = "看房团报名"
				}
				else{
						this.leixing ="团购报名"
				}
				
			}else {
				 this.leixing ="团购报名"
			}
			if(options.popup_desc){
				this.popup_desc = options.popup_desc
			}
			if(options.from){
				switch(options.from){
					case "1":
						this.params.from = "楼盘页"
						this.params.bid = options.bid //楼盘报名提交传的是bid（楼盘id）
						break
					case "2":
						this.params.from = "看房团页"
						this.params.group_id = options.group_id //看房团报名提交传的是group_id（看房团详情的id）
						break
					case "3":
						this.params.from = "资讯页"
						this.params.bid = options.bid //资讯页进来报名提交的是bid（资讯绑定的楼盘的id）
						break
					case "4":
						this.params.from = "活动页"
						this.params.bid = options.bid //资讯页进来报名提交的是bid（资讯绑定的楼盘的id）
						break
					default:
						this.params.from = "楼盘页"
						this.params.bid = options.bid
				}
			}else{
				this.params.from = "楼盘页"
				this.params.bid = options.bid
			}
			this.getNum()
		},
		filters:{
			imgUrl(val){
				if(!val){
					return ""
				}
				return formatImg(val,'w_8601')
			}
		},
		methods:{
			formSubmit(e){
				Object.assign(this.params,e.detail.value)
				// console.log(this.params)
				// return
				if(e.detail.value.tel.length<11||e.detail.value.tel[0]!=1){
					uni.showToast({
						title:"手机号格式不正确",
						icon:"none"
					})
					return
				}
				uni.showLoading({
						title:"正在提交",
						mask:true
				})
				this.$ajax.post('build/signUp.html',this.params,res=>{
					if(res.data.code == 1){
						setTimeout(()=>{
							uni.navigateBack()
						},1500)
					}
					uni.showToast({
						title:res.data.msg,
						icon:"none"
					})
				})
			},
			getNum(){
				let params = {bid:this.bid}
				if(this.type==1){
					params.is_popup = 1
				}
				this.$ajax.get('build/getBuildGroup.html',params,res=>{
					if(res.data.title){
						uni.setNavigationBarTitle({
							title: res.data.title
						})
						this.title = res.data.title
					}
					if(res.data.code == 1){
						this.bm_num = res.data.group
						if(res.data.img){
							this.img = res.data.img
						}
					}
					let bullet_list=[]
					if(res.data.bubbles&&res.data.bubbles.length>0){
						bullet_list = res.data.bubbles
						//   bullet_list= bullet_list.slice(0,5)  // 调试改变数据
						let len = bullet_list.length
						if(len>1&&len<=this.count){
							//动态改变高度
							this.height =uni.upx2px((len-1)*90)+"px"
							//比实际数量少显示一个
							this.count =len-1
						}else if (len==1){
							this.height = uni.upx2px((len)*90)+"px"
							this.count =1
						}else {
							this.height = uni.upx2px(this.count*90)+"px"
						}
						this.bullet_list =bullet_list
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.he-box{
		background: #d32526
	}
	.ad-box image{
		width: 100%;
	}
	.label-row{
		padding-left: 34upx;
		font-size: 26upx;
		color: #999999
	}
	.coupon-box{
		position: relative;
		 .coupon-img{
			width: 100%;
		}
		.coupon-title{
			height: 50upx;
			font-size: 36upx;
			width: 38%;
			text-align: center;
			color: #d32526;
			position: absolute;
			left: 0;
			top: 0;
			bottom: 0;
			margin: auto;
		}
		.coupon-value{
			position: absolute;
			right: 12%;
			top: 0;
			bottom: 0;
			margin: auto;
			width: 45%;
			height: 80upx;
			line-height: 80upx;
			text-align: center;
			color: #ff5e4d;
			font-size: 40upx;
			background: #fff;
			border-radius: 10upx;
		}
	}
	.bullet-box{
		width: 95%;
		margin: 100upx auto;
		max-height: 400upx;
		overflow: hidden;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		background: #fff;
	
	}
	.bullet-box swiper {
		height:100%;
		width: 100%;
		padding:15rpx;
	}
	.bullet-box .con{
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20upx;
		padding: 20upx 0;
		color: #666;
	}
	.bullet-box .con .con_{
		font-size: 26upx;
		flex: 1;
		white-space: nowrap;
		overflow : hidden;
		text-overflow: ellipsis;
	
	}
</style>
