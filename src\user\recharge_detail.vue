<template>
    <view class="p-top-80">
        <tab-bar :tabs="tabs" @click="switchTab" :nowIndex="tabIndex"></tab-bar>
        <view class="list">
			<block v-for="(item,index) in listData" :key="index">
				<uni-list-item v-if="tabIndex==0" :thumb="img.cz" :title="'支付金额'+item.money+'元'" :subTitle="item.paybz" :note="item.time" show-arrow="false"></uni-list-item>
				<uni-list-item v-if="tabIndex==1" :thumb="img.xf" :title="(item.status==1?'增加':'扣除')+item.money+'金币'" titleType="html" :subTitle="item.content" :note="item.time" show-arrow="false"></uni-list-item>
				<uni-list-item v-if="tabIndex==2" :thumb="img.xf" :title="(item.status==1?'增加':'扣除')+item.money+'积分'" titleType="html" :subTitle="item.content" :note="item.time" show-arrow="false"></uni-list-item>
			</block>
        </view>
        <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    </view>
</template>

<script>
    import tabBar from "../components/tabBar"
    import {uniListItem,uniLoadMore} from "@dcloudio/uni-ui"
    export default {
        components:{
            tabBar,
            uniListItem,
            uniLoadMore
        },
        data(){
            return {
                tabs:[
                    // {name:"全部",type:""},
                    {name:"支付记录",type:"1"},
                    {name:"消费记录",type:"2"},
                    {name:"积分记录",type:"3"}
                ],
                img:{
                    cz:'https://images.tengfangyun.com/images/icon/m6.png',
                    xf:'https://images.tengfangyun.com/images/icon/m7.png'
                },
                tabIndex:0,
                listData:[],
                page:1,
                get_status:"loading",
				content_text:{
					contentdown:"",
					contentrefresh:"正在加载...",
					contentnomore:"没有更多数据了"
				}
            }
        },
		filters:{
			formatHtml(val){
				if(!val){
					return ""
				}
				return val.replace('font','div')
			}
		},
        onLoad(){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
            this.getData()
        },
        methods:{
            getData(){
                switch (this.tabIndex){
                    case 0:
                        this.getRachargeList()
                        break;
                    case 1:
                        this.getRecordList(1)
                        break;
                    case 2:
                        this.getRecordList(2)
                        break;
                    default:
                        this.getRachargeList()
                }
            },
            getRachargeList(){
                this.get_status = "loading"
				if(this.page == 1){
					this.listData = []
				}
                this.$ajax.get('member/payRecord.html',{page:this.page},res=>{
                    if(res.data.code == 1){
                        this.listData = this.listData.concat(res.data.list)
                        this.get_status = "more"
                    }else{
                        this.get_status = "noMore"
                    }
                })
            },
            getRecordList(type=1){
                this.get_status = "loading"
				if(this.page == 1){
					this.listData = []
				}
                this.$ajax.get('member/userRecordLog',{page:this.page,type:type},res=>{
                    if(res.data.code == 1){
                        this.listData = this.listData.concat(res.data.list)
                        this.get_status = "more"
                    }else{
                        this.get_status = "noMore"
                    }
                })
            },
            switchTab(e){
                this.page = 1
                this.tabIndex = e.index
                this.getData()
            }
        },
        onReachBottom(){
			this.page++
			this.getData()
		}
    }
</script>

<style lang="scss">
    .list{
        background-color: #ffffff;
    }
</style>