<template>
    <view class="info_detail"  :style = "{backgroundImage:(pdetail.tp_bgimg)?'url('+pdetail.tp_bgimg+')':''}">
        <view class="con" :class='detail.info_showbutton==1?"bottom_130":""'>
          <!-- <view class="con_" v-html="detail.info_content"></view> -->
           <!-- #ifdef H5 -->
          <view class="article-content" v-html="detail.info_content"></view>
          <!-- #endif -->
          <!-- #ifndef H5 -->
        
          <u-parse :html="detail.info_content" @linkpress="navigate" :tag-style="tagStyle"></u-parse>
          <!-- #endif -->

        </view>
        <view class="group" v-if = "detail.info_showbutton == '1'">
          <view class="join" @click="toSubForme(detail.info_jumpid)">{{detail.info_jumpid|formatName}}</view> 
        </view>
        <view class="fixed" @click= "showFix=false" v-if ="showFix">
            <form @submit.prevent.stop="formSubmit" @reset.prevent.stop="formReset" @click.prevent.stop ="showFix=true">
                <view class="row label-row bottom-line">{{detail.info_jumpid|formatName}}</view>
                <view class="row form-row flex-box">
                  <view class="label">您的姓名</view>
                  <input name="name" maxlength="10" placeholder="请输入姓名" />
                </view>
                <view class="row form-row flex-box">
                  <view class="label">手机号</view>
                  <input name="tel" type="number" maxlength="11" placeholder="请输入手机号" />
                </view>
                <view class="btn-box">
                  <button formType="submit" class="default sub">确定提交</button>
                </view>
                <!-- <view class="text-center" style="font-size: 28upx;">已有<text style="color: #FF4444;">{{bm_num}}人</text>报名</view> -->
                <view class="text-center" style="font-size: 24upx;color: #999;">您的信息将被严格保密，请准确填写</view>
            </form>
        </view>
    </view>
</template>

<script>
import {
  formatImg,navigateTo
} from '../common/index.js'

import uParse from '../components/Parser/index'
import {wxShare} from '../common/mixin'
export default {
    data(){
      return {
        id:"",
        title:"",
        detail :[],
        pdetail :[],
        showFix:false,
        url:"",
        seo:{},
        tagStyle:{
            video:'max-width:100%'
        },
       
      }
    },
    mixins: [wxShare],
    components:{
      uParse,
  
    },
    onLoad(options){
      if (options.id){
        this.id =options.id
        this.title =options.title||""
      }
      	uni.setNavigationBarTitle({
					title: this.title
				})
      this.getData()
    },
   filters:{
			imgUrl(img,param="w_240"){
				if(!img){
					return ""
				}
				return formatImg(img,param)
      },
      formatName(val){
        switch (val) {  //后台返回字符串
          case '0':
          
            return "专题报名"
            
            break;
            case '1':
            
            return "楼盘报名"
            
            
            break;
            case '2':
             
            return "家装报名"
            
            break;
        
          default:
            return ""
            break;
        }
      }
		},
    methods:{
      getData(){
        this.$ajax.get('topic/view',{id:this.id} , (res) => {
          if (res.data.code ==1){
            let  detail =res.data.data
            this.detail = detail
            if (res.data.seo) {
              this.seo = res.data.seo
            }
            switch (this.detail.info_jumpid) {
              case "0":
                  this.url = ''
                break;
                case "1":
                  this.url ="/user/sub_form/sub_form"
                break;
                case "2":
                  this.url ="/home/<USER>/index"
                break;
            
              default:
                this.url=''
                break;
            }
            uni.setNavigationBarTitle({
              title:this.detail.info_title
            })
          
          }
          if (res.data.share&&res.data.share.title) {
            this.share = res.data.share
            this.getWxConfig()
          }
          
        })
      },
      navigate(href, e) {
				console.log(href)
				// navigateTo(href)
      },
      toSubForme(id) {
          switch (id) {
            case "0":
                this.showFix=true
                break;
            case "1":
                navigateTo(this.url+'?bid=' + this.detail.tp_bid+'&from=3'+"&leixing=0")
                break;
            case "2":
                navigateTo(this.url)
                break;
          
            default:
                this.showFix=true
                break;
          }
      },
      formSubmit(e){
        let params={
          up_tpid:this.detail.id,
          up_infoid:this.id,
          up_bid:this.detail.tp_bid,
          up_name:e.detail.value.name,
          up_tel:e.detail.value.tel,
        }
        if(e.detail.value.name==''){
					uni.showToast({
						title:"姓名不能为空",
						icon:"none"
					})
					return
				}
				 
				if(e.detail.value.tel.length<11||e.detail.value.tel[0]!=1){
					uni.showToast({
						title:"手机号格式不正确",
						icon:"none"
					})
					return
				}
				this.$ajax.post('topic/signUp.html',params,res=>{
					if(res.data.code == 1){
              setTimeout(()=>{
                  this.showFix=false
              },1500)
          }
					uni.showToast({
						title:res.data.msg,
						icon:"none"
					})
				})
			},
    },
    onShareAppMessage() {
      return {
        title: this.detail.info_sharetitle || this.detail.info_title,
        content: this.detail.info_sharedesc || '',
        imageUrl:this.detail.info_smallimg?formatImg(this.detail.info_smallimg,'w_240'):""
      }
    
  }
}
</script>

<style lang="scss">
.info_detail{
  background-repeat: no-repeat;
  background-size: cover;
  background: #fff;
  box-sizing: border-box;
  padding-top: 20upx;
  min-height: calc(100vh - 45px);
  // position: fixed;
  // left: 0;
  // right: 0;
  // top: 0;
  // bottom: 0;
  overflow-y: auto;
  .con{
    .title{
     text-align: center;
     font-size: 32upx;
     font-weight: 400;
    }
    
  }
   .group{
    position: fixed;
    bottom: 0upx;
    left: 0px;
    right: 0upx;

    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20upx 10upx;
    .join {
      text-align: center;
      background: #007AFF;
      color: #fff;
      font-size: 40upx;
      width: 80%;
      padding: 20upx;
      border-radius: 50upx;
    }
  }
}
.bottom_130{
  padding-bottom: 130upx;
}
.fixed{
  position: fixed;
  bottom: 0;
  right: 0;
  left: 0;
  top: 0;
  background:rgba(0,0,0,0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}
.fixed form {
  padding: 20upx;
  width: 80%;
  background: #fff;
}
.label-row{
		padding-left: 34upx;
		font-size: 26upx;
		color: #999999
	}
  .btn-box button.default.sub{
    background: #007AFF;
    border-radius: 50upx;
  }
  	.share-box{
		padding: 20upx 0;
		margin-bottom: 90upx;
		background-color: #fff;
		.tip{
			padding: 10px;
			width: 100%;
			font-weight: 700;
			box-sizing: border-box;
			text-align: center;
		}
		button{
			line-height: initial;
			padding: 10upx 20upx;
			background-color: #fff;
		}
		.wechat-img{
			width: 60vw;
			height: 60vw;
		}
		.item{
			text-align: center;
			padding: 10upx 20upx;
			line-height: inherit;
		}
	}
  .card-btn{
		position: fixed;
		z-index: 96;
		bottom:30vh;
		right: 0;
		padding: 15upx 30upx;
		border-top-left-radius: 30upx;
		border-bottom-left-radius: 30upx;
		background-color: #f65354;
		opacity: 0.8;
		box-shadow: 0 10upx 30upx #f65354;
		color: #fff
	}
  .card-img{
		width: 70%;
		margin: 0 15%;
		padding: 40upx 0;
	}
  	/* #ifdef H5 */
	#card{
		padding: 50upx 0 10upx 0;
		width: 100%;
		position: fixed;
		left: 100%;
		background-color: #fff;
	}
	.project-name,.nowdate{
		margin: 6upx 50upx;
		font-size: 28upx;
		color: #666;
	}
	.color-red{
		color: #f65354;
	}
	.card_img-box{
		width: 100%;
		margin-top: 40upx;
		padding: 10upx 50upx;
		box-sizing: border-box;
		height: 60vw;
		overflow: hidden;
	}
	.card_img-box image{
		width: 100%;
		height: 100%;
	}
	.card_info-box{
		margin: 50upx;
	}
	.card_info-box .title{
		font-size: 40upx;
		padding: 0;
	}
	.card-line-box{
		width: 100%;
		box-sizing: border-box;
		padding: 0 50upx;
	}
	.card-line{
		height: 4upx;
		width: 100%;
		background-color: #f65354;
	}
	.card-footer{
		margin: 50upx;
		.categoryname{
			font-size: 32upx;
			line-height: 60upx;
			color: #666;
		}
		.update_time{
			font-size: 26upx;
			line-height: 40upx;
			color: #666
		}
		.logo-box{
			width: 30vw;
			.logo{
				width: 100%
			}
		}
		.qrcode-box{
			.qrcode{
				width: 25vw;
				height: 25vw
			}
			.text-center{
				font-size: 24upx;
				color: #666;
			}
		}
		
	}
	/* #endif */
</style>