<template>
  <view class="content share">
    <view class="filter_list">
      <tab-bar :tabs="type_list" :fixedTop="false" :showLine="false" :nowIndex="current_type_index">
        <view class="filter_item" :class="{active:index===current_type_index}" :id="'i' + index" v-for="(item,index) in type_list" :key="index" @click="onSelectType(item.id, index)">{{item.typename}}({{item.count}})</view>
      </tab-bar>
    </view>
    <view class="lists-box" id="moments">
      <view v-for="(photo, index) in photo_list" :key="photo.id">
        <share-item :share="photo">
        </share-item>
      </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
  </view>
</template>

<script>
import tabBar from "../../components/tabBar.vue"
import shareItem from '../../components/shareItem.vue'
import { uniLoadMore } from '@dcloudio/uni-ui'
// #ifdef H5
import {formatImg } from '../../common/index.js'
// #endif
// #ifndef H5
import {formatImg, showModal } from '../../common/index.js'
// #endif
export default {
  data() {
    return {
      params: {
        id: '',
        page: 1,
        type: 0,
        rows: 20
      },
      type_list:[],
      photo_list: [],
      get_status: 'loading',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      },
    }
  },
  components: {
    tabBar,
    uniLoadMore,
    shareItem
  },
  computed: {
    imconsu() {
      return this.$store.state.im.adviser
    },
    imchat() {
      return this.$store.state.im.ischat
    },
    istelcall() {
      return this.$store.state.im.istelcall
    },
    current_type_index(){
      const index = this.type_list.findIndex(item => item.id === this.params.type);
      return index === -1 ? 0 : index
    }
  },
  onLoad(options) {
		this.params.id = options.id
    this.params.type = parseInt(options.type || 0)
    this.getData()
  },
  onReachBottom() {
    if(this.get_status!=='more'){
      return
    }
    //监听上拉触底事件
    this.params.page++
    this.getData()
  },
  onPullDownRefresh() {
    //监听下拉刷新动作
    this.params.page = 1
    this.getData()
  },
  methods: {
    onSelectType(type, index){
      if(this.params.type === type){
        return
      }
      this.params.type = type
      this.params.page = 1
      this.getData()
    },
    getData() {
      if (this.params.page == 1) {
        this.photo_list = []
      }
      this.get_status = 'loading'

      this.$ajax.get('build/buildPhotos', this.params, res => {
        if(this.params.page === 1){
          if (res.data.share && res.data.share.title) {
            this.share = res.data.share
            this.getWxConfig()
          }
          if (res.data.navigationBarTitle) {
            uni.setNavigationBarTitle({
              title: `${res.data.navigationBarTitle}`
            })
          } else {
            uni.setNavigationBarTitle({
              title: (res.data.buildTitle||'')+'楼盘相册'
            })
          }
        }
        if (res.data.code == 0||res.data.lists.length == 0) {
          this.get_status = 'noMore'
          if (this.params.page < 1) {
            this.params.page = 1
          }
          return
        }
        if(res.data.types&&res.data.types.length>0){
          var count = res.data.types.reduce((total, item)=>{
            return total + item.count
          }, 0)
          this.type_list = [{id: '', typename: '全部', count}]
          this.type_list = [...this.type_list, ...res.data.types]
        }
        uni.stopPullDownRefresh()
        var lists = res.data.lists.map(item=>{
          item.attached = item.imgs
          item.type = 1
          return item
        })
        this.photo_list = this.photo_list.concat(lists)
        this.get_status = 'more'
      })
    }
  },
  onShareAppMessage() {
    if (this.share) {
      return {
        title: this.share.title,
        // #ifdef MP-BAIDU
        content: this.share.content,
        // #endif
        imageUrl: this.share.pic ? formatImg(this.share.pic, 'w_6401') : ''
      }
    }
  }
}
</script>

<style lang="scss">
.lists-box {
  padding: 24rpx 48rpx;
  background-color: #fff;
}

.filter_list{
  padding-left: 48rpx;
  padding-top: 12rpx;
  background-color: #fff;
  position: sticky;
  z-index: 10;
  // #ifdef H5
  top: 44px;
  // #endif
  // #ifndef H5
  top: 0;
  // #endif
  .filter_item{
    display: inline-block;
    padding: 10rpx 20rpx;
    border-radius: 4rpx;
    line-height: 1;
    box-sizing: border-box;
    margin-right: 24rpx;
    font-size: 24rpx;
    background-color: #f5f5f5;
    border: 1rpx solid #f5f5f5;
    color: #999;
    &.active{
      background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
      color: $uni-color-primary;
      border: 1rpx solid $uni-color-primary;
    }
  }
}
</style>
