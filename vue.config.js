const path = require('path');

function resolve (dir) {
  console.log(path.resolve(__dirname, dir))
  return path.resolve(__dirname, dir)
}


const replaceManifestItems = [];
switch(process.env.npm_lifecycle_event){
    case 'build:m-oss':
        replaceManifestItems.push({name:'h5.publicPath', value: '"https://images.tengfangyun.com/publicPathM"'});
        break;
    case 'build:h5-oss':
        replaceManifestItems.push({name:'h5.publicPath', value: '"https://images.tengfangyun.com/publicPathH5"'});
        break;
    case 'build:m':  
    case 'build:h5':
        replaceManifestItems.push({name:'h5.publicPath', value: '"https://images.tengfangyun.com/publicPathH5"'});
        break;
}

const fs = require('fs');
const manifestPath = './src/manifest.json'
let Manifest = fs.readFileSync(manifestPath, { encoding: 'utf-8' })
function replaceManifest(path, value) {
  const arr = path.split('.')
  const len = arr.length
  const lastItem = arr[len - 1]

  let i = 0
  let ManifestArr = Manifest.split(/\n/)

  for (let index = 0; index < ManifestArr.length; index++) {
    const item = ManifestArr[index]
    if (new RegExp(`"${arr[i]}"`).test(item)) ++i;
    if (i === len) {
      const hasComma = /,/.test(item)
      ManifestArr[index] = item.replace(new RegExp(`"${lastItem}"[\\s\\S]*:[\\s\\S]*`), `"${lastItem}": ${value}${hasComma ? ',' : ''}`)
      break;
    }
  }

  Manifest = ManifestArr.join('\n')
}
if(replaceManifestItems.length) {
    replaceManifestItems.forEach(item => {
        replaceManifest(item.name, item.value);
    })
    fs.writeFileSync(manifestPath, Manifest, {
        "flag": "w"
    })
}


module.exports = {
  // 让图片不转为beas64
  chainWebpack: config => {
    config.module
      .rule('images')
      .use('url-loader')
      .loader('url-loader')
      .tap(options => Object.assign(options, { limit: false }))
  },
}