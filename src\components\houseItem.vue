<template>
  <view class="house " :class="{ 'bottom-line': showLine }" @click="$emit('click', { type: type, detail: itemData })">
    <view v-if="itemData.upgrade_type == 2 && endTime" class="zhiding">
      <text class="spantext">置顶中</text>
      <text class="spantext">有效期至</text>
      <text class="spantext">{{ itemData.upgrade_time }}</text>
    </view>
    <view class="house-box">
      <view class="img-box">
        <view class="level-box">
          <text class="level level2" v-if="itemData.info_level === 2">精选</text>
        </view>
        <image class="img" :src="itemData.img | imgUrl" lazy-load mode="aspectFill"></image>
        <image v-if="itemData.is_vr || itemData.vr" class="video-icon" src="/static/icon/vr.png"></image>
        <image v-else-if="itemData.is_video == 1" class="video-icon" src="/static/icon/video.png"></image>
      </view>
      <view class="info">
        <view class="title_box flex-row">
          <view class="title" :class="titleRow == 2 ? 'row2' : 'row1'">
            <text v-if="itemData.upgrade_type == 2 && !endTime" class="ding">顶</text>
            <view class="is_agent" v-if="itemData.levelid > 1 && from == 'find_house'">
              经纪人
            </view>
            <view class="is_personal" v-if="itemData.levelid == 1 && from == 'find_house'">
              个人
            </view>
            <text :class="{
              red: itemData.ifred,
              bold: itemData.ifbold,
            }">{{ itemData.title }}</text>
          </view>
        </view>
        <!-- 二手房和出租房 -->
        <view class="center-info">
          <!-- <text :class="'attr' + itemData.zhongjie">{{ itemData.zhongjie == 2 ? "经纪人" : "个人" }}</text> -->
          <text class="huxing">{{ itemData.shi }}室{{ itemData.ting }}厅{{ itemData.wei }}卫</text>
          <text class="jiange jiange-margin" v-if="itemData.mianji">|</text>
          <text class="mj" v-if="itemData.mianji">{{ itemData.mianji }}㎡</text>
          <text class="jiange" v-if="itemData.chaoxiang">|</text>
          <text class="cx" v-if="itemData.chaoxiang">{{ itemData.chaoxiang }}</text>
          <!-- <text class="area">{{ itemData.areaname || "" }}</text> -->
          <text class="type">{{ itemData.community_name || itemData.areaname }}</text>
        </view>
        <view class="labels" v-if="itemData.label && itemData.label.length > 0">
          <text class="label" :style="{ color: label.color, borderColor: label.color }"
            v-for="(label, index) in itemData.label" :key="index">{{ label.name }}</text>
        </view>
        <view class="bottom-info flex-box">
          <view class="bottom-left">
            <template v-if="type === 'ershou'">
              <text class="mianyi" v-if="itemData.fangjia == '面议' || itemData.fangjia == '0' || !itemData
                .fangjia">面议</text>
              <text class="price" v-else>{{ itemData.fangjia }}</text>
              <block v-if="itemData.fangjia !== '面议' && itemData.fangjia != '0' && itemData.fangjia">
                <text class="price-unit">万</text>
                <text class="average_price">{{ (itemData.fangjia * 10000 / itemData.mianji).toFixed(2) }}元/m²</text>
              </block>
              <!-- <text>{{ itemData.shi }}室{{ itemData.ting }}厅{{ itemData.wei }}卫</text>
            <text class="mj">{{ itemData.mianji }}㎡</text> -->
            </template>
            <template v-if="type === 'renting'">
              <text class="mianyi" v-if="itemData.zujin == '面议' || itemData.zujin == '0' || !itemData
                .zujin">面议</text>
              <text class="price" v-else>{{ itemData.zujin }}</text>
              <text class="price-unit"
                v-if="itemData.zujin !== '面议' && itemData.zujin != '0' && itemData.zujin">元/月</text>
              <!-- <text>{{ itemData.shi }}室{{ itemData.ting }}厅{{ itemData.wei }}卫</text>
            <text class="mj">{{ itemData.mianji }}㎡</text> -->
            </template>
          </view>

          <view class="bottom-right" v-if="showTime">
            <text class="u-time">{{ itemData.begintime }}</text>
          </view>
        </view>
        <!-- <view v-if="endTime" class="fy-right">
        <view v-if="itemData.endtime">
          <text class="fyend"> 房源有效期: </text>
          <text class="fyend-time"> {{ itemData.endtime }}</text>
        </view>
      </view> -->
        <!-- <view v-if="endTime" class="fy-right">
        <view v-if="itemData.upgrade_time">
          <text class="fyend"> 置顶到期时间: </text>
          <text class="fyend-time"> {{ itemData.upgrade_time }}</text>
        </view>
      </view> -->
        <view class="agent_info flex-row" v-if="itemData.levelid > 1">
          <image class="header_img" :src="itemData.prelogo | imageFilter('w_80')"></image>
          <text class="c_name">{{ itemData.cname }}</text>
          <text class="b_name flex-1">{{ itemData.tname }}</text>
        </view>
        <view class="push flex-row" v-if="from == 'find_house' && itemData.is_match">
          <view class="is_push pushed" v-if="itemData.is_push == 1">已推送</view>
          <view class="is_push" v-else>
            猜你喜欢
          </view>
          <view class="tiaojian">{{ itemData.is_match }}</view>
        </view>
      </view>
    </view>
  </view>
</template>
<style scoped lang="scss">
.flex-row {
  display: flex;
  flex-direction: row;
}

.zhiding {
  display: inline-block;
  width: 97%;
  padding: 2px 8px;
  align-items: center;
  border-radius: 4px;
  background-color: #FFF3E8;
  margin-bottom: 24rpx;

  .spantext {
    color: #F53F3F;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding-right: 10rpx;
  }
}

.house {
  display: flex;
  flex-direction: column;
  // padding: 40rpx 0;
  padding: 20rpx 0rpx;

  /* 默认是垂直方向 */
  .house-box {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap
  }

  .img-box {
    width: 204rpx;
    height: 172rpx;
    margin-right: 16rpx;
    position: relative;
    border-radius: 8rpx;
    overflow: hidden;

    .img {
      width: 100%;
      height: 100%;
    }

    .level-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;

      .level {
        display: block;
        margin-bottom: 5rpx;
        padding: 2rpx 10rpx;
        font-size: 22rpx;
        border-bottom-left-radius: 20rpx;
        color: #fff;

        &.level1 {
          background: linear-gradient(132deg, #F7918F 0%, #FB656A 100%);
        }

        &.level2 {
          background: linear-gradient(135deg, #69D4BB 0%, #00CAA7 100%);
        }
      }
    }

    .video-icon {
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      left: 20rpx;
      bottom: 20rpx;
    }
  }

  .info {
    flex: 1;
    overflow: hidden;

    .title {
      font-size: 32rpx;
      line-height: 1.5;
      flex: 1;
      margin-top: -6rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;

      .red {
        color: #fb656a;
      }

      .bold {
        font-weight: bold;
      }

      &.row1 {
        max-height: 90rpx;
        margin-bottom: 10rpx;
      }

      &.row2 {
        min-height: 45rpx;
      }
    }

    .title_box {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    .push {
      margin-top: 10rpx;
      align-items: center;

      .tiaojian {
        font-size: 22rpx;
        color: #999;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis
      }
    }

    .is_push {
      // background:linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
      color: #fff;
      // padding: 6rpx 10rpx;
      // margin-right: 10rpx;
      // line-height: 1;
      // font-size: 22rpx;
      display: inline-block;
      min-width: 100rpx;
      margin-right: 10rpx;
      text-align: center;
      font-size: 22rpx;
      padding: 5rpx 10rpx;
      border-radius: 8rpx;
      // color: #FB656A;
      background: linear-gradient(90deg, #F7918F 0%, #FB656A 100%);

      &.pushed {
        background-image: linear-gradient(90deg, #8CD3FC 0%, #4CC7F6 100%);
      }
    }

    .is_agent {
      display: inline-block;
      padding: 6rpx 10rpx;
      margin-right: 10rpx;
      line-height: 1;
      font-size: 22rpx;
      border-radius: 4rpx;
      background: #44331a;
      color: #fcecdb;
      // color: linear-gradient(to right, #fcecdb 0%, #f8dda9 100%);
    }

    .is_personal {
      display: inline-block;
      padding: 6rpx 10rpx;
      margin-right: 10rpx;
      line-height: 1;
      font-size: 22rpx;
      border-radius: 4rpx;
      background: linear-gradient(90deg, #69d4bb 0, #00caa7);
      color: #fff;

    }

    .ding {
      display: inline-block;
      padding: 6rpx 10rpx;
      margin-right: 10rpx;
      line-height: 1;
      font-size: 22rpx;
      border-radius: 4rpx;
      background: linear-gradient(to right, #F7918F 0%, #FB656A 100%);
      color: #fff;
    }

    .center-info {
      display: flex;
      align-items: center;
      margin-top: 5rpx;
      font-size: 22rpx;

      .jiange {
        margin: 0 4rpx;
        color: #999;

        &.jiange-margin {
          margin: 0 12rpx;
        }
      }

      &.need {
        .price_box {
          margin-left: 48rpx;
        }

        .label {
          font-size: 22rpx;
          color: #999;
        }

        .area {
          font-size: 22rpx;
          color: #333;
        }

        .in_price {
          font-size: 22rpx;
          color: $uni-color-primary;
        }
      }

      .area {
        margin-left: 16rpx;
        color: #999;
      }

      .type {
        overflow: hidden;
        white-space: nowrap;
        flex: 1;
        text-align: right;
        text-overflow: ellipsis;
        color: #333;
      }

      .cx {
        margin-right: 4rpx;
      }

      .mj {
        margin-right: 4rpx;
      }
    }

    .labels {
      margin-top: 16rpx;
      line-height: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      .label {
        display: inline-block;
        line-height: 1;
        font-size: 22rpx;
        padding: 4rpx 8rpx;
        border: 1rpx solid #d8d8d8;
        color: #999;
        border-radius: 4rpx;

        ~.label {
          margin-left: 16rpx;
        }
      }
    }

    .bottom-info {
      margin-top: 16rpx;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      flex-wrap: wrap;

      text {
        font-size: 22rpx;
      }

      .mianyi {
        font-size: 32rpx;
        font-weight: bold;
        margin-right: 10rpx;
        color: #fb656a;
      }

      .price {
        font-size: 34rpx;
        line-height: 1;
        font-weight: bold;
        color: #fb656a;
      }

      .price-unit {
        font-size: 26rpx;
        margin: 0 16rpx 0 8rpx;
      }

      .average_price {
        color: #999;
      }

      .bottom-right {
        flex-shrink: 0
      }

      .u-time {
        line-height: 1;
        position: relative;
        font-size: 22rpx;
        color: #999;
      }
    }
  }

  .agent_info {
    display: flex;
    margin-top: 16rpx;
    align-items: center;
    font-size: 22rpx;
    color: #999;

    .header_img {
      width: 36rpx;
      height: 36rpx;
      border-radius: 50%;
      background-color: #f5f5f5;
    }

    .c_name,
    .b_name {
      margin-left: 16rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .fy-right {
    text-align: right;

    .fyend {
      font-size: 22rpx;
      color: #999;
      margin-right: 5rpx;
    }

    .fyend-time {
      font-size: 22rpx;
      color: #fb656a;
    }
  }
}
</style>
<script>
import { formatImg } from "../common/index.js";
export default {
  components: {},
  data() {
    return {};
  },
  props: {
    itemData: Object,
    type: {
      type: String,
      default: "ershou",
    },
    titleRow: {
      type: [Number, String],
      default: 2,
    },
    from: {
      type: String,
      default: "",
    },
    showLine: {
      type: Boolean,
      default: true
    },
    showTime: {
      type: Boolean,
      default: true
    }, endTime: {
      type: Boolean,
      default: false
    }
  },
  filters: {
    imgUrl(val) {
      return formatImg(val, "w_240");
    },
  },
  methods: {},
};
</script>
