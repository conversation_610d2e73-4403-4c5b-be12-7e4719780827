<template>
	<view class="page">
		<title-bar custom>
			<view class="search-box flex-box" @click="$navigateTo('/pages/search/search?text=二手房')">
				<my-icon type="ic_sousuo" color="#999999" size="46rpx"></my-icon>
				<view class="inp">
					<text :class="{has_key: params.keyword}">{{params.keyword||'您想住哪里？'}}</text>
				</view>
			</view>
			<view slot="right" class="seach_btn flex-box" @click.prevent.stop="$navigateTo('/pages/add/detail?catid=1')">
				<my-icon type="ic_tianjia" color="#fb656a" size="46rpx"></my-icon>
				<text class="text">发布</text>
			</view> 
		</title-bar>
		<!-- 头部模块 -->
		<view class="model" v-if ="prices&&prices.is_show">
			<view class="model_top flex-row">
				<view class="model_top_title">
					房价行情
				</view>
				<view class="model_top_icon">
					<!-- <view class="model_top_icon_con"> -->
						<my-icon type="ic_into" size="24rpx" color="#fff"></my-icon>
					<!-- </view>	 -->
				</view>
			</view>
			<view class="model_bottom flex-row">
				<view class="model_bottom_item flex-1">
					<view class="model_bottom_item_num">
						<text class ="model_bottom_item_num_c">{{prices.avg_price.price}}</text>
						<text class ="model_bottom_item_num_unit">元/m²</text>
					</view>
					<view class="model_bottom_item_name">
							{{prices.avg_price.title}}
					</view>
				</view>
				<view class="model_bottom_item flex-1">
					<view class="model_bottom_item_num">
						<text class ="model_bottom_item_num_c">{{prices.month_rate.sign==1?"+":(prices.month_rate.sign==2?"-":'')}}{{prices.month_rate.rate}}</text>
						<text class ="model_bottom_item_num_unit">%</text>
					</view>
					<view class="model_bottom_item_name">
						{{prices.month_rate.title}}
					</view>
				</view>
				<view class="model_bottom_item flex-1">
					<view class="model_bottom_item_num">
						<text class ="model_bottom_item_num_c">{{prices.house_total.total}}</text>
						<text class ="model_bottom_item_num_unit">套</text>
					</view>
					<view class="model_bottom_item_name">
						{{prices.house_total.title}}
					</view>
				</view>
			</view>

		</view>
		<view class="top_custom" v-if ="(nav&&nav.is_show) ||(custom_labels&&custom_labels.is_show)">
			<view class="top_grid flex-row" v-if ="nav.is_show">
				<view class="top_grid_item flex-1" v-for ="(item,index) in nav.nav" :key ="index" @click ="$navigateTo(item.path)">
					<view class="top_grid_item_icon">
						<image :src="item.icon| imgUrl('w_80')" mode ="aspectFit"></image>
					</view>
					<view class="top_grid_item_title">
						{{item.title}}
					</view>
				</view>
			</view>
			<view class="top_grid_other flex-row" v-if ="custom_labels.is_show" >
				<view class="top_grid_other_item" v-for ="(item,index) in custom_labels.custom_labels" :key = "index" @click ="$navigateTo('/pages/ershou/ershou?label='+item.label)">
					<view class="top_grid_other_item_icon" > 
						<view class="top_grid_other_item_icon_c flex-row" :style="{background:item.color}">
							{{item.count}}
						</view>
						
					</view>
					<view class="top_grid_other_item_title">
						{{item.title}}
					</view>
				</view>
			</view>
		</view>
		<!-- 广告位 -->
		<view class="swiper-container" v-if ="adv.length>0">
			<view class="swiper-con">
				<view class="swiper">
						<my-swiper :focus="adv"  :autoplay="true" :interval="4000" :indicatorDots="adv.length>1" :circular="true" indicatorActiveColor="#ffffff" height="140rpx"></my-swiper>
				</view>
			</view>
		</view>
		<view class="screen-tab flex-box" id ="tab_top">
			<view class="screen-tab-item flex-1 text-center" @click="switchTab(1)">
				<text>{{areaName}}</text>
				<my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
			</view>
			<view class="screen-tab-item flex-1 text-center" @click="switchTab(2)">
				<text>{{priceName}}</text>
				<my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
			</view>
			<view class="screen-tab-item flex-1 text-center" @click="switchTab(3)">
				<text>{{distanceName}}</text>
				<my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
			</view>
			<view class="screen-tab-item flex-1 text-center" @click="switchTab(4)">
				<text>{{typeName}}</text>
				<my-icon type="ic_down" color="#d8d8d8" size="24rpx"></my-icon>
			</view>
			<scroll-view scroll-y class="screen-panel" :class="nowTab==1?'show':''" @touchmove.stop.prevent="stopMove"  v-if ="showTab">
				<addressd :addressd = "area" ref="showArea" @changes="changeArea"></addressd>
			<!-- <block v-for="item in area" :key="item.areaid">
				<uni-list-item :title="item.areaname" show-arrow="false" @click="selectArea(item.areaid, item.areaname)"></uni-list-item>
			</block> -->
			</scroll-view>
			<scroll-view scroll-y class="screen-panel" :class="nowTab==2?'show':''" @touchmove.stop.prevent="stopMove">
				<block v-for="(item,index) in price" :key="index">
					<uni-list-item :title="item.name" show-arrow="false" @click="selectPrice(item.value, item.name)"></uni-list-item>
				</block>
			</scroll-view>
			<scroll-view scroll-y class="screen-panel" :class="nowTab==3?'show':''" @touchmove.stop.prevent="stopMove">
				<block v-for="(item,index) in distance" :key="index">
					<uni-list-item :title="item.name" show-arrow="false" @click="selectDistance(item.value, item.name)"></uni-list-item>
				</block>
			</scroll-view>
			<scroll-view scroll-y class="screen-panel more-panel" :class="nowTab==4?'show':''" @touchmove.stop.prevent="stopMove">
				<view class="more-screen-item">
					<view class="title">面积(㎡)</view>
					<view class="options flex-box">
						<view class="options-item" @click="selectOption({space:item.value},'space')" :class="params.space==item.value?'active':''" v-for="(item,index) in space" :key="index">{{item.name}}</view>
					</view>
				</view>
				<view class="more-screen-item">
					<view class="title">房型</view>
					<view class="options flex-box">
						<view class="options-item" @click="selectOption({room:item.id},'room')" :class="params.room==item.id?'active':''" v-for="(item,index) in rooms" :key="index">{{item.name}}</view>
					</view>
				</view>
				<view class="more-screen-item">
					<view class="title">类型</view>
					<view class="options flex-box">
						<view class="options-item" @click="selectOption({type_id:item.catid},'type_id')" :class="params.type_id==item.catid?'active':''" v-for="(item,index) in types" :key="index">{{item.catname}}</view>
					</view>
				</view>
				<!-- <view class="more-screen-item">
					<view class="title">来源</view>
					<view class="options flex-box">
						<view class="options-item" @click="selectOption({intermediary:0},'intermediary')" :class="params.intermediary===0?'active':''">不限</view>
						<view class="options-item" @click="selectOption({intermediary:1},'intermediary')" :class="params.intermediary===1?'active':''">个人</view>
						<view class="options-item" @click="selectOption({intermediary:2},'intermediary')" :class="params.intermediary===2?'active':''">经纪人</view>
					</view>
				</view> -->
				<view class="flex-box padding-20">
					<button size="medium" type="default" @click="resetMore">重置</button>
					<button size="medium" type="primary" @click="selectMore()">确定</button>
				</view>
			</scroll-view>
		</view>

		<view class="filter_list" >
			<tab-bar :tabs="filter_list" :fixedTop="false" :showLine="false" :nowIndex="current_filter_index" style ="whiteSpace:nowrap">
				<view class="filter_item" :class="{active:index===current_filter_index}" :id="'i' + index" v-for="(item,index) in filter_list" :key="index" @click="onClickFilter(item, index)">{{item.name}}</view>
			</tab-bar>
		</view>

		<template v-if ="showList">
			<view class="house_list" :class ="{'house_little_list':likeData.length==0||!showLike}"  v-if ="listsData.length>0 &&show_type ==1">
				<ershou :listsData="listsData" type="1" ref="ershou"></ershou>
				<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
			</view>
			<view class="house_list waterfall" :class ="{'house_little_list':likeData.length==0||!showLike}" 	v-if ="(leftData.length>0 ||rightData.length>0) &&show_type ==2">
				<view class ='view_item' v-for ='item in leftData' :key = "item.id">
					<indexItem :itemData="item" @click="toDetail" type="ershoufang"></indexItem>
				</view>
				<view class ='view_item' v-for ='item in rightData' :key = "item.id">
					<indexItem :itemData="item" @click="toDetail" type="ershoufang"></indexItem>
				</view>	
			</view>
			<uni-load-more v-if ="(leftData.length>0 || rightData.length>0) &&show_type ==2" :status="get_status" :content-text="content_text"></uni-load-more>
			<view class="house_list" v-if ="listsData.length==0 && leftData.length==0 && rightData.length==0">
				<view class="no_data flex-row">
					<view class="icon flex-row">
						<myIcon type="tishifu" fontSize ="30"></myIcon>
					</view>
					<view class="no_data_text">
						没有找到符合条件的房源 换个条件试试吧
					</view>
				</view>
			</view>
			<view class="house_list " v-if ="likeData.length>0&&showLike">
				<view class="xihuan flex-row">
					<view class="line flex-1">
					</view>
					<view class="xihuan_text">
						猜你喜欢
					</view>
					<view class="line flex-1">

					</view>
				</view>
				<ershou v-if ='show_type==1' :listsData="likeData" type="1" ref="ershou"></ershou>
				<view class="house_list waterfall"  v-if ="show_type ==2">
					<view class ='view_item' v-for ='item in leftLikeData' :key = "item.id">
						<indexItem :itemData="item" @click="toDetail"  type="ershoufang"></indexItem>
					</view>
					<view class ='view_item' v-for ='item in rightLikeData' :key = "item.id">
						<indexItem :itemData="item" @click="toDetail"  type="ershoufang"></indexItem>
					</view>
				</view>
			</view>
		</template>

	

		<my-dialog ref="dialog" @cancelButton="getTmoto" :show="show_dialog" @close="show_dialog = false" title="温馨提示" openType="openSetting">
			<view class="set-nickname-box">
				<view class="row">只有获取位置权限才能获取附近的房源</view>
			</view>
		</my-dialog>
		<view class="mask" :class="nowTab>0?'show':''" @click="()=>{nowTab=0;showPannel=false}" @touchmove.stop.prevent="stopMove"></view>
		
		<chat-tip></chat-tip>
	</view>
</template>

<script>
	import titleBar from "../../components/titleBar.vue";
	import tabBar from "../../components/tabBar.vue"
	import ershou from "../../components/ershou.vue"
	import mySwiper from "../../components/mySwiper.vue"
	import myIcon from "../../components/myIcon"
	import {uniLoadMore,uniList,uniListItem} from '@dcloudio/uni-ui'
	import myDialog from "../../components/dialog.vue"
	import {checkAuth,isIos,formatImg} from "../../common/index.js"
	import wx from "weixin-js-sdk"
	import {statistics} from '../../common/statistics'
	import addressd from '../../components/jm-address/jm-address'
	import indexItem from '../../components/indexItem.vue'

	export default{
		components:{
			titleBar,
			tabBar,
			ershou,
			mySwiper,
			myIcon,
			uniLoadMore,
			uniList,
			myDialog,
			addressd,
			uniListItem,
			indexItem
		},
		data(){
			return{
				get_status:"loading",
				likeData:[],
				content_text:{
					contentdown:"",
					contentrefresh:"正在加载...",
					contentnomore:"没有更多数据了"
				},
				nowTab:0,
				// areaName:'区域',
				typeName:"更多",
				adv:[],
				area:[],
				price:[],
				rooms:[
					{id:0,name:"不限"},
					{id:1,name:"一室"},
					{id:2,name:"二室"},
					{id:3,name:"三室"},
					{id:4,name:"四室"},
					{id:5,name:"五室"}
				],
				distance:[],
				space:[],
				types:[],
				area_list:[],
				filter_list:[],
				current_filter_index: 0,
				params:{
					price:"",
					distance:"",
					areaid:"",
					cate_id:1,
					page:1,
					rows:20,
					keyword:'',
					label:'',
					info_level:'',
					zhongjie:'',
					store_id:0,
					is_cut:'', //降价好房
					is_video:''
				},
				
				qu:[],
				showTab:false,
				listsData:[],
				show_dialog:false,
				keyword:"",
				t_title:[
					{text:"全部",isActive:true},
					{text:"精选",isActive:false},
					{text:"个人",isActive:false},
					{text:"经纪人",isActive:false},
				],
				prices:{},
				custom_labels:{},
				nav:{},
				scrollTop:0,
				showPannel:false,
				scrollTopOffset:0,
				showList:false,
				showLike:false,
				show_type:1,
				leftData:[],
				rightData:[],
				leftLikeData:[],
				rightLikeData:[],
				
			}
		},
		computed:{
			distanceName(){
				let name = "距离";
				if(this.params.distance&&this.distance.length>0){
					let res = this.distance.find(item=>{
						return item.value == this.params.distance
					})
					if(res&&res.name){
						name = res.name
					}
				}
				return name
			},
			priceName(){
				let name = "价格";
				if(this.params.price&&this.price.length>0){
					let res = this.price.find(item=>{
						return item.value == this.params.price
					})
					if(res&&res.name){
						name = res.name
					}
				}
				return name
			},
			areaName(){
				let name = "区域";
				if(this.params.areaid&&this.area_list.length>0){
					let res = this.area_list.find(item=>{
						return item.areaid == this.params.areaid
					})
					if(res&&res.areaname){
						name = res.areaname
					}
				}
				return name
			},
			status_top(){
				return this.$store.state.systemInfo.statusBarHeight
			}
		},
		onLoad(options){
			console.log(options,"21321421")
			for (let key in options){
				this.params[key] = options[key]
			}
			if(options.keyword){
				this.params.keyword = decodeURIComponent(options.keyword)
				console.log(this.params.keyword)
			}
			if(options.cid){ //小区id
				this.params.cid = options.cid
			}
			if(options.id){ //门店id
				this.params.store_id = options.id
			}
			if(options.label){ 
				this.params.label = options.label
				this.current_filter_type ="label"
			}
			if(options.info_level){ 
				this.params.info_level = options.info_level
				this.current_filter_type ="info_level"
			}
			if (options.zhongjie) {
				this.current_filter_type ="zhongjie"
			}
			if(options.is_video){ 
				this.params.is_video = options.is_video
			}
			if(options.is_cut){ 
				this.params.is_cut = options.is_cut
			}
			if(!uni.getStorageSync('no_watch_search_key')){
				uni.$on('handleSearch',(e)=>{
					this.params.keyword = e
					this.params.page = 1
					this.getTmoto()
				})
			}

			uni.$on("getDataAgain",()=>{
				this.params.page = 1
				this.getTmoto()
			})
			this.getTmoto()
			this.getScreen()
			statistics()
		},
		filters:{
			imgUrl(val, param = "") {
				return formatImg(val, param)
			},
		},
		onShow(){
			setTimeout(()=>{
				if(this.$store.state.updatePageData){
					this.$store.state.updatePageData = false
					this.getLocation()
				}
			},150);
		},
		onUnload(){
			if(!uni.getStorageSync('no_watch_search_key')){
				uni.$off('handleSearch')
			}else{
				uni.removeStorageSync('no_watch_search_key')
			}
			uni.$off("getDataAgain")
		},
		onPageScroll(e){
			
			this.scrollTop = e.scrollTop
			const query = uni.createSelectorQuery().in(this);
			if (this.showPannel) {
				uni.pageScrollTo({
				scrollTop: this.scrollTopOffset,
				duration: 0
				});
			}
		},
		methods:{
			getLocation(){
				this.$store.state.getPosition(()=>{
					this.getTmoto()
				})
			},
			scroppTo(fun){
				const query = uni.createSelectorQuery().in(this);
				query.select('#tab_top').fields({rect:true,scrollOffset:true},data => {
					
				if (data){
					if(data.top<=44){
						fun&&fun()
						return
					}
					// #ifdef H5
					this.scrollTopOffset =(this.scrollTop||0) + data.top-uni.upx2px(80)
					 // #endif
					// #ifndef H5
					this.scrollTopOffset =(this.scrollTop||0) + data.top-(44+this.status_top)+uni.upx2px(100)
					 // #endif
        
				}
        uni.pageScrollTo({
          duration:120,
          // #ifdef H5
          scrollTop:	this.scrollTopOffset,
          // #endif
          // #ifndef H5
					scrollTop:	this.scrollTopOffset,
					
					// #endif
					success:()=>{
						if (fun){
							fun()
						}
					}
        })
      }).exec();
    },
			getScreen(){
				this.$ajax.get('house/houseCondition.html',{catid:this.params.cate_id},(res)=>{
					res.data.area.push({areaid:'',parentid:0 ,mapx:'',mapy:'',areaname:"全部"})
					let area =res.data.area
					this.area_list = res.data.area
					this.area = this.getJiedao(area,"areaid","parentid","city")
					this.price = res.data.price
					this.distance = res.data.distance
					this.space = res.data.space
					this.types = res.data.cates
					let label = [
						{
							name:'全部',
							type:'all',
							id:0,
							optionId:0
						},
						{
							name:'精选',
							type:'info_level',
							id:2,
							optionId:3
						},
						{
							name:'经纪人',
							type:'zhongjie',
							id:2,
							optionId:2
						}
					]
					if (res.data.is_show_personal_tab==1){
						label=[
							{
								name:'全部',
								type:'all',
								id:0,
								optionId:0
							},
							{
								name:'精选',
								type:'info_level',
								id:2,
								optionId:3
							},
							{
								name:'个人',
								type:'zhongjie',
								id:1,
								optionId:1
							},
							{
								name:'经纪人',
								type:'zhongjie',
								id:2,
								optionId:2
							}
						]
					}
					let label2 = res.data.label.map(item=>{
						item.type = 'label'
						return item
					})
					this.filter_list = label.concat(label2)
					if(this.params.label){
						this.$nextTick(()=>{
							this.current_filter_index = this.filter_list.findIndex(item=>item.id==this.params.label)
						})
					}
					if(this.params.zhongjie){
						this.$nextTick(()=>{
							this.current_filter_index = label.findIndex(item=>item.optionId==this.params.zhongjie)
						})
					}
					if(this.params.info_level){
						this.$nextTick(()=>{
							this.current_filter_index = label.findIndex(item=>item.optionId==3)
						})
					}
					this.showTab=true
				},(err)=>{
					console.log(err)
				})
			},			
			getTmoto(){
					this.getData()
				// this.$ajax.get('house/index.html',this.params,(res)=>{
				// 	console.log(res.data.house_list_style)
				// 	if(res.data.code==1){
				// 		// this.show_type=res.data.house_list_style
				// 		if(this.show_type==2){
				// 			this.getData()
				// 		}else{
				// 			this.getDatato()
				// 		}
				// 	}
				// })
			},
			toDetail(e){
				this.$navigateTo("/pages/ershou/detail?id="+e.detail.id+'&show_type='+this.show_type)
			},
			getDatato(){
				this.params.lat = this.$store.state.position.lat
				this.params.lng = this.$store.state.position.lng
				// if(this.params.page == 1){
				// 	this.listsData=[]
				// }
				this.showPannel=false
				this.get_status = "loading"
				this.$ajax.get('house/index.html',this.params,(res)=>{
					// #ifdef H5 || MP-BAIDU
					if(res.data.seo){
						let seo = res.data.seo
						if(res.data.share.pic){
							seo.image = formatImg(res.data.share.pic,'w_8001')
						}
						this.seo = seo
					}
					
					// #endif
					if(res.data.adv&&res.data.adv.length>0&&this.adv.length===0){
						this.adv = res.data.adv.map(item=>{
							item.url=item.wap_link
							return item
						})
					}
					if (res.data.prices){
						this.prices=res.data.prices
					}
					if (res.data.custom_labels){
						this.custom_labels =res.data.custom_labels
					}
					if (res.data.nav){
						this.nav =res.data.nav
					}
					if(res.data.code == 1){
						this.likeData = res.data.house2?res.data.house2:this.likeData
						if (this.params.page ==1){
							this.listsData =res.data.house
						}else {
							this.listsData = this.listsData.concat(res.data.house)
						}
						this.showList =true
						if(res.data.house.length<this.params.rows){
							this.get_status = "noMore"
							this.showLike =true
						}else{
							this.get_status = "more"
						}
						if(res.data.share){
							this.share = res.data.share
						}
					}else{
						this.listsData =[]
						this.showLike =true
						this.get_status = "noMore"
					}
					uni.stopPullDownRefresh();
				},(err)=>{
					this.listsData =[]
					console.log(err)
					uni.stopPullDownRefresh();
				})
			},
			getData(){
				this.params.lat = this.$store.state.position.lat
				this.params.lng = this.$store.state.position.lng
				// if(this.params.page == 1){
				// 	this.listsData=[]
				// }
				this.showPannel=false
				this.get_status = "loading"
				this.$ajax.get('house/index.html',this.params,(res)=>{
					if(res.data.adv&&res.data.adv.length>0&&this.adv.length===0){
						this.adv = res.data.adv.map(item=>{
							item.url=item.wap_link
							return item
						})
					}
					if (res.data.prices){
						this.prices=res.data.prices
					}
					if (res.data.custom_labels){
						this.custom_labels =res.data.custom_labels
					}
					if (res.data.nav){
						this.nav =res.data.nav
					}
					if(this.params.page==1){
						this.leftData =[]
						this.rightData =[]
						this.leftLikeData=[]
						this.rightLikeData =[]
					}
					//console.log(JSON.stringify(this.params));
					if(res.data.code == 1){
						if (this.params.page ==1){
							this.show_type = res.data.house_list_style || 1
						}
						if (this.show_type==1){
							this.likeData = res.data.house2?res.data.house2:this.likeData
							if (this.params.page ==1){
								this.listsData =res.data.house

							}else {
								this.listsData = this.listsData.concat(res.data.house)
							}
						}else {
							const left = res.data.house.filter((item, index) => index % 2 === 0);
							const right = res.data.house.filter((item, index) => index % 2 !== 0);
							this.leftData =  this.leftData.concat(left)
							this.rightData =  this.rightData.concat(right) 
							const leftLikeData = res.data.house2.filter((item, index) => index % 2 === 0);
							const rightLikeData = res.data.house2.filter((item, index) => index % 2 !== 0);
							this.leftLikeData =  this.leftLikeData.concat(leftLikeData)
							this.rightLikeData =  this.rightLikeData.concat(rightLikeData)
							console.log(this.leftData,this.rightData ,this.leftLikeData,this.leftLikeData); 
						}
						
						this.showList =true
						if(res.data.house.length<this.params.rows){
							this.get_status = "noMore"
							this.showLike =true
						}else{
							this.get_status = "more"
						}
						if(res.data.share){
							this.share = res.data.share
						}
						this.share.sharePath="/pages/ershou/ershou"
					}else{
						if (this.params.page ==1){
							this.listsData =[]
							this.leftData =[]
							this.rightData =[]
							this.showLike =true
						}
						
						this.get_status = "noMore"
					}
					if(res.data.share){
						this.share = res.data.share
					}else{
						this.share = {}
					}
					var  shareLink = window.location.href.split("?")[0]
					this.share.link =`${shareLink}`
					var opt ="?isShare=1&"
					for (const key in this.params) {
							const element = this.params[key];
							if (this.params[key]&&key!=="page"&&key!=="rows"){
								opt +=`${key}=${this.params[key]}&`
							}
					}
					var _opt = opt.substring(0, opt.length-1)
          this.share.link +=_opt
					this.getWxConfig()
					uni.stopPullDownRefresh();
				},(err)=>{
					this.listsData =[]
					this.leftData =[]
					this.rightData=[]
					console.log(err)
					uni.stopPullDownRefresh();
				})
			},
			stopMove(){

			},
			getJiedao (a,idStr,pIdStr,chindrenStr) {
				var r = [], hash = {}, id = idStr, pId = pIdStr, children = chindrenStr, i = 0, j = 0, len = a.length;  
				for(; i < len; i++){  
				a[i].label= a[i].name;	
				delete a[i].name; 
					hash[a[i][id]] = a[i];
				}  
				for(; j < len; j++){  
					var aVal = a[j], hashVP = hash[aVal[pId]];  

					if(hashVP){  
						!hashVP[children] && (hashVP[children] = []);  
				
						hashVP[children].unshift(aVal);  
					}else{  
						r.unshift(aVal);  
					}  
				}  
				return r;
   		},
			changeArea(e){
				this.current_filter_index = -1
				// this.areaName = e.district?e.district:(e.city?e.city:(e.province?e.province:""))
				this.params.areaid = e.district_id?e.district_id:(e.city_id?e.city_id:(e.province_id?e.province_id:""))
				this.nowTab = 0
				this.params.page = 1
				this.getTmoto()
			},
			switchTab(index){
				this.scroppTo(()=>{
					
					// this.scrollTop =200
					let timeout =setTimeout(() => {
						if(this.nowTab == index){
							this.nowTab = 0
							this.showPannel=false
							// this.$refs.ershou.allMove()
						}else{
							this.nowTab = index
							if (index==1){
								this.$refs.showArea.showAddress()
							}
							this.showPannel=true
							// this.$refs.ershou.stopMove()
						}
					}, 200);
					
				})

				
			},
			selectArea(id, name){
				this.current_filter_index = -1
				this.params.areaid = id
				// this.areaName = name
				this.nowTab = 0
				this.params.page = 1
				this.getTmoto()
			},
			selectPrice(value, name){
				this.current_filter_index = -1
				this.params.price = value
				// this.priceName = name
				this.nowTab = 0
				this.params.page = 1
				this.getTmoto()
			},
			selectDistance(value, name){
				this.current_filter_index = -1
				this.params.distance = value
				// this.distanceName = name
				this.nowTab = 0
				this.params.page = 1
				// #ifdef MP
				checkAuth('scope.userLocation',{
					authOk:()=>{
						this.getLocation()
					},
					success:()=>{
						this.getLocation()
					},
					fail:()=>{
						this.show_dialog = true
					}
				})
				// #endif
				// #ifdef H5 || APP-PLUS
				this.getLocation()
				// #endif
			},
			selectOption(obj,type){
				switch(type)
				{
				case "type_id":
					if(this.params.type_id === obj.type_id){
						obj.type_id = ""
					}
				case "space":
					if(this.params.space === obj.space){
						obj.space = ""
					}
				case "room":
					if(this.params.room === obj.room){
						obj.room = ""
					}
				case "intermediary":
					if(this.params.intermediary === obj.intermediary){
						obj.intermediary = ""
					}
				}
				this.current_filter_index = -1
				this.params = Object.assign({},this.params,obj)
			},
			selectMore(){
				this.current_filter_index = -1
				this.nowTab = 0
				this.params.page = 1
				this.getTmoto()
			},
			resetMore(){
				this.params.type_id = ""
				this.params.space = ""
				this.params.intermediary = ""
				this.params.room=""
			},
			handleInput(e){
				this.keyword = e.detail.value
			},
			handleSearch(e){
				this.params.keyword = e.detail.value
				this.params.page = 1
				this.getTmoto()
			},
			onClickFilter(item, index){
				this.current_filter_index = index
				this.current_filter_type = item.type
				this.params.zhongjie=''
				this.params.info_level=''
				this.params.type=''
				if(item.type === 'all'){
					// this.areaName='全部'
					// this.priceName='价格'
					// this.distanceName='距离'
					this.typeName="更多"
					this.resetMore()
					this.params.areaid = ''
					this.params.price = ''
					this.params.distance = ''
					this.params.label = ''
					this.params.lat = ''
					this.params.lng = ''
					this.params.page = 1
					this.getTmoto()
					return
				}
				this.params.zhongjie=''
				this.params.info_level=''
				this.params.label=''
				this.params[item.type]=item.id
				this.params.page = 1
				this.getTmoto()
			},
			back() {
				if (getCurrentPages().length > 1) {
					uni.navigateBack()
				} else {
					uni.switchTab({
						url: '/pages/index/index'
					})
				}
			}
		},
		onPullDownRefresh(){
			this.params.page = 1
			this.getTmoto()
		},
		onReachBottom(){
			if (this.get_status == "more"){
				this.params.page = this.params.page+1
				this.getTmoto()
			}
			
		},
		// #ifdef H5 || APP-PLUS
		onNavigationBarButtonTap(option){
			this.$navigateTo('/pages/add/detail?catid=1')
		},
		// #endif
	}
</script>
<style lang="scss" scoped>
	// view{
	// 	display: flex;
	// 	flex-direction: column;
	// 	box-sizing: border-box;
	// }
	.page{
		background: #fff;
	}
	// .no_show{
	// 	display: none;
	// }
	.page.fixed{
		position: fixed;
		left: 0;
		right: 0;
	}
	.flex-row{
		display: flex;
		flex-direction: row;
	}
	.p-top-180{
		padding-top: 80upx;
	}
/* #ifdef H5 */
	.screen-tab{
		position: sticky;
		top:44px;
		// margin-top: 44px;
		box-sizing: border-box;
		padding: 0 48rpx;
	}
	.screen-panel {
		top:0;
		margin-top: 80rpx;
		display: none;
	}
	.screen-panel.show {
		top:37px;
		left: 0;
		display: block;
	
	}
	/* #endif */
	/* #ifndef H5 */
	.screen-tab{
		top: var(--window-top);
		margin-top: 43px;
		box-sizing: border-box;
		padding: 0 48rpx;
	}
	.screen-panel {
		top: var(--window-top);
		margin-top: 162rpx;
	}
	/* #endif */
.swiper-container{
	background: #fff;
	.swiper-con{
		padding: 32rpx 48rpx 0;
	}
	.swiper{
		border-radius: 8rpx;
		overflow: hidden;
	}
}
.search-box {
  margin-left: 20rpx;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #f5f5f5;
	color: #999;
  border-radius: 8rpx;
  .search-left {
    margin-right: 20rpx;
  }
	.inp{
		margin-left: 20rpx;
		.has_key{
			color: #333;
		}
	}
}
.seach_btn{
	align-items: center;
	padding: 0 24rpx;
	.text{
		margin-left: 16rpx;
	}
}

.house_list{
		&.house_little_list{
			min-height: 100vh;
		}
		&.waterfall {
			  padding: 0 48rpx;
				// -webkit-column-count: 2;
				column-count: 2;
				// -webkit-column-gap: 9px;
				column-gap: 18rpx;
				display: block;
				background: #f7f7f7;
				.view_item {
					display: inline-block;
					width: 100%;
					margin-bottom: 20rpx;
				}
		}
		padding: 0 48rpx;
		background-color: #fff;
		.no_data{
			justify-content: center;
			align-items: center;
			padding: 40rpx 0;
			.icon{
				justify-content: center;
				align-items: center;
			}
			.no_data_text{
				font-size: 30rpx;
				color: #333;
				margin-left: 10rpx;

			}
		}
		.xihuan{
			justify-content: center;
			align-items: center;
			padding: 0 0 20rpx ;
			.xihuan_text{
				margin: 0 20rpx;
				color: #968E9F;
				font-size: 30rpx;

			}
			.line{
				height: 2rpx;
				background:#E1E3EE;
			}
		}
	}
	.filter_list{
		padding-left: 48rpx;
		padding-top: 24rpx;
		background-color: #fff;
		white-space: nowrap;
		.filter_item{
			display: inline-block;
			padding: 10rpx 20rpx;
			border-radius: 4rpx;
			line-height: 1;
			box-sizing: border-box;
			margin-right: 24rpx;
			font-size: 24rpx;
			background-color: #f5f5f5;
			border: 1rpx solid #f5f5f5;
			color: #999;
			&.active{
				background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
				color: $uni-color-primary;
				border: 1rpx solid $uni-color-primary;
			}
		}
	}
	.model{
		height: 236rpx;
		padding: 32rpx 48rpx;
		background-image: linear-gradient(0deg, #F9B282 0%, #F96456 100%);
		.model_top{
			font-size: 40rpx;
			color: #FFFFFF;
			letter-spacing: 0;
			text-align: center;
			justify-content: center;
			align-items: center;
			margin-top: 16rpx;
			margin-bottom: 32rpx;
			.model_top_title{
				font-size: 40rpx;
			}

			.model_top_icon{
				width: 30rpx;
				height: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				margin-left: 10rpx;
				padding-top: 4rpx;
				background:  rgba(255,255,255,0.30);
			}
		}
		.model_bottom{
			margin-top: 20rpx;
			&_item{
				justify-content: center;
				align-items: center;
				&_num{
					font-size: 22rpx;
					color: #FFFFFF;
					text-align: center;
					&_c{
						font-size: 40rpx;
					}

				}
				&_name{
					margin-top: 16rpx;
					font-size: 22rpx;
					text-align: center;
					color: #fff;
				}
			}
		}
	}
	.top_custom	{
		margin-top: 20rpx;
	}
	.top_grid{
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0 10rpx;
		background: #FFFFFF;
		&_item{
			text-align: center;
			max-width: 25%;
			&_icon{
				width: 80rpx;
				height: 80rpx;
				margin: 0 auto;
				overflow: hidden;
				image{
					width: 100%;
					height: 100%;
				}
			}
			&_title{
				margin-top: 15rpx;
				font-size: 24rpx;
				color: #333333;
				letter-spacing: 0;
				text-align: center;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
	.top_grid_other{
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 26rpx;
		background: #FFFFFF;
		&_item{
			text-align: center;
			width: 20%;
			&_icon{
				
				&_c{
					align-items: center;
					justify-content: center;
					overflow: hidden;
					width: 80rpx;
					background: #F96456;
					height: 80rpx;
					color: #fff;
					margin: 0 auto;
					border-radius: 50%;
				}
			}
			&_title{
				margin-top: 15rpx;
				font-size: 24rpx;
				color: #333333;
				letter-spacing: 0;
				text-align: center;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}

	.more-screen-item .options .options-item {
		line-height: 1;
	}
</style>