<template>
<view class="page">
    <view class="top_nav flex-row bottom-line" :style="{opacity:opacity}">
      <view class="nav_item" v-for="(item, index) in top_navs" :key="index" :class="{active:current_nav_index===index}" @click="scroppTo(item.id, item.type, index)">{{item.name}}</view>
    </view>
    <view class="top-20 bottom-line block" id="point1">
        <view class="title bottom-line">基本信息</view>
        <view class="item bottom-line flex-row">
            <view class="label">楼盘名称</view>
            <view class="info">{{build.title}}</view>
        </view>
        <view class="item bottom-line flex-row">
            <view class="label">{{build.price_type}}</view>
            <view class="info flex-box">
                <view class="build_price flex-row">
                    <text class="highlight">{{build.build_price}}</text>
                    <text>{{build.price_unit}}</text>
                    <text style="margin-left:20rpx">{{build.avg_price_desc}}</text>
                </view>
                <text class="highlight">{{build.t2}}</text>
                <text class="highlight">{{build.t3}}</text>
            </view>
        </view>
        <view class="item bottom-line flex-row" v-if  ='build.kfs'>
            <view class="label">开发商</view>
            <view class="info">{{build.kfs}}</view>
        </view>
        <view class="item bottom-line flex-row" v-if ='build.selladdress'>
            <view class="label">售楼处地址</view>
            <view class="info">{{build.selladdress}}</view>
        </view>
        <view class="item bottom-line flex-row" v-if ='build.address'>
            <view class="label">楼盘地址</view>
            <view class="info" @click="viewMap()">{{build.address}}<text>[查看地图]</text></view>
        </view>
    </view>
    <view class="top-20 bottom-line block" id="point2">
        <view class="title bottom-line">小区情况</view>
        <view class="item bottom-line flex-row" v-if ='build.typename'>
            <view class="label">建筑类型</view>
            <view class="info">{{build.typename}}</view>
        </view>
        <view class="item bottom-line flex-row" v-if ='build.other'>
            <view class="label">物业类型</view>
            <view class="info">{{build.other}}</view>
        </view>
        <view class="item bottom-line flex-row" v-if ='build.jindu'>
            <view class="label">工程进度</view>
            <view class="info">{{build.jindu}}</view>
        </view>
        <view class="item bottom-line flex-row" v-if ='build.jflx'>
            <view class="label">交房标准</view>
            <view class="info">{{build.jflx}}</view>
        </view>
        
        <view class="item bottom-line flex-row" v-if ='build.tzs'>
            <view class="label">投资商</view>
            <view class="info">{{build.tzs}}</view>
        </view>
        <!-- <view class="item bottom-line flex-row">
            <view class="label">交通状况</view>
            <view class="info">{{build.gjxl}}</view>
        </view> -->
        <view class="item bottom-line flex-row" v-if ='build.matches && build.matches.length'>
            <view class="label">周边配套</view>
            <view class="info">
                <view v-for="(item, index) in build.matches" :key="index">{{item.title}}：{{item.desc}}；</view>
            </view>
        </view>
        <view class="item bottom-line flex-row" v-if ='build.xsch'>
            <view class="label">营销公司</view>
            <view class="info">{{build.xsch}}</view>
        </view>
    </view>
    <view class="top-20 bottom-line block" id="point3">
        <view class="title bottom-line">小区规划</view>
        <view class="item bottom-line flex-row">
            <view class="label">容积率</view>
            <view class="info">{{build.dfl||'待更新'}}</view>
        </view>
        <view class="item bottom-line flex-row" >
            <view class="label">绿化率</view>
            <view class="info">{{build.lhl||'待更新'}}</view>
        </view>
        <view class="item bottom-line flex-row">
            <view class="label">建筑面积</view>
            <view class="info">{{build.jzsize?build.jzsize+'m²':'待更新'}}</view>
        </view>
        <view class="item bottom-line flex-row">
            <view class="label">占地面积</view>
            <view class="info">{{build.zdsize?build.zdsize+(build.zdsize_unit==0?"m²":'亩'):'待更新'}}</view>
        </view>
        <view class="item bottom-line flex-row">
            <view class="label">停车位</view>
            <view class="info">{{build.tcw||'待更新'}}</view>
        </view>
        <view class="item bottom-line flex-row">
            <view class="label">总户数</view>
            <view class="info">{{build.total_hushu||'待更新'}}</view>
        </view>
        <view class="item bottom-line flex-row">
            <view class="label">物业公司</view>
            <view class="info">{{build.wygs||'待更新'}}</view>
        </view>
        <view class="item bottom-line flex-row">
            <view class="label">物业费</view>
            <view class="info">{{build.wyf||'待更新'}}</view>
        </view>
        <view class="item bottom-line flex-row">
            <view class="label">物业描述</view>
            <view class="info">{{build.wyf_desc||'待更新'}}</view>
        </view>
        <view class="item bottom-line flex-row">
            <view class="label">楼层状况</view>
            <view class="info">{{build.lczk||'待更新'}}</view>
        </view>
    </view>
    <view class="top-20 bottom-line block" id="point4">
        <view class="title bottom-line">销售信息</view>
        <view class="item bottom-line flex-row">
            <view class="label">预售许可证</view>
            <!-- <view class="info">{{build.t4}}</view> -->
            <view class="info">
                <text>{{build.xkzh&&build.xkzh.length>0?build.xkzh[0]:'待更新'}}</text>
                <text class="highlight" @click="toYushou()" style="margin-left:10rpx">查看更多</text>
            </view>
        </view>
        <view class="item bottom-line flex-row">
            <view class="label">最新开盘时间</view>
            <view class="info">{{build.kptime||'待更新'}}</view>
        </view>
        <view class="item bottom-line flex-row">
            <view class="label">交房时间</view>
            <view class="info">{{build.rztime||'待更新'}}</view>
        </view>
        <view class="item bottom-line flex-row" v-if ='build.areaname'>
            <view class="label">所属区域</view>
            <view class="info">{{build.areaname}}</view>
        </view>
    </view>
    <view class="top-20 bottom-line block" id="point5">
        <view class="title bottom-line">项目介绍</view>
        <view class="item bottom-line buildinfos" v-if ='build.content1'>
            <view class="info">{{build.content1}}</view>
            <view class="btns" @click="goInfo">查看更多</view>
        </view>
    </view>
    <!-- 底部操作菜单 -->
    <view class="bottom-bar flex-row">
        <view class="bar-left flex-row flex-1">
            <!-- <view v-if="show_build_tel && cusArr && cusArr.mid && is_open_adviser && detail.open_adviser" class="icon-btn" @click="consuDetail(cusArr.id)">
            <image :src="cusArr.prelogo | imageFilter('w_120')" class="header_img"></image>
            <text>{{cusArr.cname || cusArr.typename}}</text>
            </view> -->
            <view class="icon-btn" @click="handleFollow">
            <my-icon :type="is_follow?'ic_guanzhu_red':'ic_guanzhu'" :color="is_follow?'#ff656b':'#666'" size="50rpx"></my-icon>
            <text>关注</text>
            </view>
            <view class="icon-btn" @click="toSubForme(4)">
            <my-icon type="yuyue" color="#666" size="50rpx"></my-icon>
            <text>预约</text>
            </view>
        </view>
        <view class="bar-btn btn1 flex-1" @click="$navigateTo('/pages/consultant/consuList?id=' + id)">置业顾问</view>
        <view class="flex-1" @click="handleTel()">
            <view class="bar-btn btn2">致电售楼处</view>
        </view>
    </view>
    <sub-form :groupCount="build.groupCount" :sub_type="sub_type" :sub_mode="sub_mode" ref='sub_form' @onsubmit="handleSubForm"></sub-form>
    <chat-tip></chat-tip>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
</view>
</template>

<script>
import myIcon from '../../components/myIcon.vue'
import {showModal} from '../../common/index'
import allTel from '../../common/all_tel.js'
import {wxShare} from '../../common/mixin'
import {mapState} from 'vuex'
import subForm from '../../components/subForm'
export default {
    data() {
        return {
            build:{},
            is_follow:0,
            id:"",
            sub_type: 0,
            sub_title:"",
            sub_content:"",
            opacity:0,
            current_nav_index:1,
            top_navs:[
                {
                name: '楼盘首页',
                id: '',
                type:'detail'
                },
                {
                name: '基本信息',
                id: 'point1'
                },
                {
                name: '小区规划',
                id: 'point3'
                },
                {
                name: '楼盘动态',
                id: '',
                type:'news'
                },
                {
                name: '户型图',
                id: '',
                type:'house_type'
                }
            ],
            tel_res: {},
            show_tel_pop: false
        }
    },
    mixins:[wxShare],
    components:{
        myIcon,
        subForm
    },
    onLoad(options){
        if(options.id){
            this.id = options.id
        }
        this.getData()
    },
    computed:{
        ...mapState(['tel400jing']),
        glabol_middle_tel(){
            return this.$store.state.im.istelcall
        },
        sub_mode() {
            return this.$store.state.sub_form_mode 
        }
    },
    methods:{
        scroppTo(id, type, index){
            if(!id){
                switch (type){
                    case 'detail':
                        var pages = getCurrentPages()
                        if(pages.length>=2&&pages[pages.length-2].route === 'pages/new_house/detail'){
                            this.$navigateBack()
                        }else{
                            this.$navigateTo(`/pages/new_house/detail?id=${this.id}`)
                        }
                        break;
                    case "news":
                        this.$navigateTo(`/pages/new_house/buildNews?bid=${this.id}`)
                        break;
                    case "house_type":
                        this.$navigateTo(`/pages/new_house/house_type_list?bid=${this.id}`)
                }
                return
            }
            this.current_nav_index = index
            this.active_tab = true
            setTimeout(()=>{
                this.active_tab = false
            },280)
            const query = uni.createSelectorQuery().in(this);
            query.select('#'+id).fields({rect:true,scrollOffset:true},data => {
                uni.pageScrollTo({
                duration:120,
                // #ifdef H5
                scrollTop:(this.scrollTop||0)+data.top -44,
                // #endif
                // #ifndef H5
                scrollTop:(this.scrollTop||0)+data.top - 0
                // #endif
                })
            }).exec();
        },
        getData(){
            this.$ajax.get('build/buildInfo.html',{id:this.id},res=>{
                if(res.data.code == 1){
                    this.build = res.data.build
                    this.is_follow = res.data.is_follow
                    uni.setNavigationBarTitle({
                        title:`${this.build.title}楼盘信息`
                    })
                }else {
                    uni.setNavigationBarTitle({
                        title:`楼盘信息`
                    })
                }
                if(res.data.share&&res.data.share.title){
                    this.share = res.data.share
                    this.getWxConfig()
                }
            },
            err=>{
                uni.setNavigationBarTitle({
                        title:`楼盘信息`
                })
            })
        },
        viewMap(){
            if(this.build.yzhou>0&&this.build.xzhou>0){
                this.$navigateTo("/propertyData/map/map?id="+this.id+'&type=1&lat='+this.build.yzhou+'&lng='+this.build.xzhou)
            }else{
                uni.showToast({
                    title:"未标记地图位置",
                    icon:"none"
                })
            }
        },
        toSubForme(type){
            this.sub_type = type
            this.$refs.sub_form.showPopup()
        },
        handleSubForm(e) {
            e.from = "楼盘页"
            e.bid = this.id
            e.type = this.sub_type || ''
            this.$ajax.post('build/signUp.html', e, res => {
                uni.hideLoading()
                if (res.data.code === 1) {
                    if (this.sub_mode!==2||res.data.status === 3) { //提示报名成功
                        uni.showToast({
                            title: res.data.msg,
                            icon: "none"
                        })
                        this.$refs.sub_form.closeSub()
                    } else if(res.data.status === 1){
                        uni.removeStorageSync('token')
                        navigateTo('/user/login/login')
                    }else if(res.data.status === 2){
                        this.$refs.sub_form.getVerify()
                    }
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none"
                    })
                }
            })
        },
        goInfo(){
            this.$navigateTo('/online/loupan_info?id='+this.id)
        },
        handleTel(){
            this.tel_params = {
                type: 1,
                callee_id: this.build.id,
                scene_type: 1,
                scene_id: this.build.id,
                success: (res)=>{
                this.tel_res = res.data
                this.show_tel_pop = true
                }
            }
            allTel(this.tel_params)
            
        },
        retrieveTel(){
            allTel(this.tel_params)
        },
        // 处理关注或取消关注楼盘
        handleFollow(){
        if(this.is_follow){
            this.cancelFollow()
        }else{
            this.follow()
        }
        },
        // 关注楼盘
        follow(){
        this.$ajax.get('build/followBuild.html',{bid:this.build.id},res=>{
            if(res.data.code === 1){
            uni.showToast({
                title:res.data.msg
            })
            this.is_follow = 1
            }else{
            uni.showToast({
                title:res.data.msg,
                icon:'none'
            })
            }
        })
        },
        // 取消关注楼盘
        cancelFollow(){
            this.$ajax.get('build/cancelFollowBuild.html',{bid:this.build.id},res=>{
                if(res.data.code === 1){
                uni.showToast({
                    title:res.data.msg
                })
                this.is_follow = 0
                }else{
                uni.showToast({
                    title:res.data.msg,
                    icon:'none'
                })
                }
            })
        },
        toYushou(){
            this.$navigateTo(`/pages/yushou/index?bid=${this.id}`)
        }
    },
    onPageScroll(e){
        let top = 0
        // #ifdef H5
        top = 44
        // #endif
        this.scrollTop = e.scrollTop
        let opacity = 0
        if(this.scrollTop>top){
        opacity = (this.scrollTop-top)/44/4
        }
        if(opacity>1)opacity=1
        this.opacity = opacity
        if(this.active_tab){
        return
        }
        if(this.time&&new Date().getTime()-this.time<220){
        return
        }else{
        this.time = new Date().getTime()
        }
        const query = uni.createSelectorQuery().in(this);
        query.select('#point1').fields({rect:true,scrollOffset:true,size:true},data => {
        if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
            this.current_nav_index = 1
        }
        }).exec();
        query.select('#point3').fields({rect:true,scrollOffset:true,size:true},data => {
        if(data&&data.top>=(-top-data.height)&&data.top<=(top+4)){
            this.current_nav_index = 2
        }
        }).exec();
    },
    onShareAppMessage() {
     return {
         title:this.title||"",
         path:"/pages/new_house/info?id="+this.id
     }

     }
}
</script>

<style scoped lang="scss">
.page{
    padding-bottom: 120rpx;
}

view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.top_nav{
  position: fixed;
  // #ifdef H5
  top: 44px;
  // #endif
  // #ifndef H5
  top: 0;
  // #endif
  z-index: 999;
  width: 100%;
  height: 90rpx;
  background-color: #fff;
  align-items: center;
  justify-content: space-between;
  .nav_item{
    flex: 1;
    height: 84rpx;
    // margin:0 20rpx;
    line-height: 84rpx;
    border-bottom: 4rpx solid #fff;
    text-align: center;
    transition: 0.26s;
    &.active{
      color: $uni-color-primary;
      border-color: $uni-color-primary;
    }
  }
}

.block{
    background-color: #ffffff;
    .title{
        padding: 24upx;
        font-size: 32upx;
        font-weight: bold;
    }
    .item{
        padding: 20upx 24upx;
        .label{
            padding: 10upx 10upx;
            margin-right: 20upx;
            width: 150upx;
            min-width: 170upx;
            // float: left;
            display: inline-block
        }
        .info{
            display: inline-block;
            padding: 10upx 10upx;
            line-height: 1.5;
            .build_price{
                display: inline-block;
                flex-wrap: wrap;
            }
            &.flex-box{
                display: flex;
            }
        }
        .btn{
            display: inline-block;
            margin-left: 20upx;
            font-size: 24upx;
            padding: 8upx 15upx;
            border-radius: 6upx;
            background-color: $uni-color-primary;
            color: #ffffff;
        }
        .highlight{
            color: $uni-color-primary
        }
    }
}
.top-20{
    margin-top: 20upx;
}
.page .block .item.buildinfos .info {
    width: 100%;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 128upx; 
    line-height: 40upx;
}
.buildinfos .btns{
    text-align: center;
    margin-top: 10upx; 
    padding: 10upx;

}
    
// 底部菜单
.bottom-bar {
    background-color: #fff;
    height: 110rpx;
    padding: 15rpx 48rpx;
    left: 0;
    z-index: 10;
    .bar-left{
        padding-right: 10rpx;
        justify-content: flex-start;
    }
    .icon-btn {
        // width: 100rpx;
        align-items: center;
        padding: 0;
        margin: 0;
        background-color: #fff;
        line-height: initial;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        // flex: 1;
        padding-right: 32rpx;
        overflow: hidden;
        position: relative;
        // & ~ .icon-btn {
        //   margin-left: 24rpx;
        // }
        .header_img{
            width: 50rpx;
            height: 50rpx;
            border-radius: 50%;
        }
        text {
            line-height: 1;
            font-size: 22rpx;
            color: #999;
            display: inline-block;
            width: 100%;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
    .bar-btn {
        // width: 220rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        font-size: 30rpx;
        padding: 0;
        margin: 0;
        border-radius: 0;
        color: #fff;
        &.btn1 {
            background: #FBAC65;
            box-shadow: 0 0 4px 0 rgba(0,0,0,0.05);
            border-top-left-radius: 40rpx;
            border-bottom-left-radius: 40rpx;
        }
        &.btn2 {
            background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
            box-shadow: 0 0 4px 0 rgba(255,80,0,0.30);
            border-top-right-radius: 40rpx;
            border-bottom-right-radius: 40rpx;
        }
    }
}
</style>
