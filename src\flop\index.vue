<template>
  <view class="page"   @touchstart="firstPlayingAudio">
    
    <view class="top_img" :class ="{opcity0:!is_show}" v-if ="detail.bg_top_pic">
      <image mode ="widthFix" :src= "detail.bg_top_pic | imageFilter('w_8601')">

      </image>
    </view>
    <view class="cont" :style="{background:detail.bg_color}">
      

      <!-- 抽奖 -->
      <view class="con" >
        <view class="border_con">
          <!--  :style="styleObj" <svg class="svg_border" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <path :d="svg_path" stroke="#f00" stroke-width="1" />
           
          </svg> -->
          <!--  -->
          <view class="con_title" :style="styleObj1"  >
            {{detail.subject_line}}
          </view>
          <view class="time flex-row items-center">
            <template v-if ="active_status!=3">
                <template v-if ="active_status==1">
                <text>
                  距离活动结束
                </text>
              </template>
              <template v-if ="active_status==2">
                <text>
                  距离下场开始
                </text>
              </template>
              <template v-if ="active_status==0">
                <text>
                  活动已结束
                </text>
              </template>
              <template v-if ="active_status==4 ">
                <text>
                活动未开始
                </text>
              </template>
              <template v-if='active_status>0'>
              <!-- <template v-if='active_status==1 &&detail.open_day_times==0'> -->

              
                  <text class="time_bg" v-if ="day>0">
                  {{day}}
                </text>
                <text v-if ="day>0">天</text>
                <text v-if ="hour>0" class="time_bg">
                  {{hour}}
                </text>
                <text v-if ="hour>0">时</text>
                <!-- </template> -->
                <text class="time_bg">
                  {{minute}}
                </text>
                <text>分</text>
                <text class="time_bg">
                  {{second}}
                </text>
                <text>秒</text>

            </template>
              
            </template>
            <template  v-if ="active_status==3">
              <text>
                  今天限时参与已结束，明天{{new Date(tomorrowStart*1000).getHours()}}点-{{new Date(tomorrowEnd*1000).getHours()}}点准时开始。
                </text>
            </template>
            
            
          </view>
          <view class="time  flex-row items-center">
            <text class="time_bg visit">
              {{detail.participate || 0}}
            </text>
            <text>人参与</text>
            <text>/</text>
            <text class="time_bg visit">
              {{detail.visit || 0}}
            </text>
            <text>人浏览</text>
          </view>
          <view class="pic_list" v-if ="cardsList.length">
            <view class="pic flex-row items-center">
              <view class="pic_item " :style="{width:picWidth}" v-for ="item in cardsList" :key ="item.id" >
                <view class="pic_img">
                  <image v-if ="item.count>0" mode="widthFix" :src="item.selected_style | imageFilter"></image>
                   <image v-else  mode="widthFix" :src="item.no_selected_style | imageFilter"></image>
                   <view class="has_card_num" :style ='numberStyleBg' v-if ='item.count>0' >{{item.count}}</view>
                </view>
                
                <view class="pic_oper" v-if ="detail.open_gift" :class ="{suoyao:item.count==0,has:item.count==1,more:item.count>1}" @click.prevent.stop ="operCard(item)">
                  {{item.count>0?'送给好友':"索要卡片"}}
                </view>
              </view>
            </view>
          </view>
          <view class="step flex-row items-center">
            <view class="step_l flex-1">
              <view class="act" :style="{width:stepW}"></view>
            </view>
            <view class="step_r ">
              已集齐{{detail.word_is_all}}人/还剩{{detail.award_store}}份
            </view>
          </view>
          <view class="oper_btns flex-row items-center">
            <view class="oper_btn chouka flex-1 mr50" @click ="chouka()">
              立即抽卡 x{{detail.prize_draw_count}}
            </view>
            <view class="oper_btn  flex-1" @click ="showTask">
              获得更多卡片
            </view>
          </view>

        </view>
      </view>
      <!-- 加群 -->
      <view class="con" v-if ="leads.length">
        <view class="border_con">
         
          <view class="con_title" :style="styleObj1"  >
            更多福利 尽在群聊
          </view>
          <view class="group_list">
            <view class="group_item flex-row items-center" :class="{'bottom-line':index!=leads.length-1}" v-for ="(item,index) in leads" :key ="item.id" @click ="joinGroup(item)">
              <view class="group_img">
                <image mode="widthFix" :src ="item.pic |imageFilter">

                </image>
              </view>
              <view class="group_con flex-1" >
                <view class="group_title">
                    {{item.title}}
                </view>
                <view class="group_sub_title">
                    {{item.descp}}
                </view>
              </view>
              <view class="join_group">
                {{item.button_name}}
              </view>

            </view>
          </view>

        </view>
      </view>
      <!-- 楼盘 -->
      <view class="con" v-if ="buildList.length" >
        <view class="border_con">
         
          <view class="con_title" :style="styleObj1"  >
            热门楼盘
          </view>
          <view class="build_list">
            <view class="build_item " v-for ="item in buildList" :key ="item.id" @click ="toBuild(item)">
              <view class="build_item_top flex-row ">
                <view class="build_img">
                  <image mode="widthFix" :src ="item.pic | imageFilter"></image>
                </view>
                <view class="build_con flex-1" >
                  <view class="build_title">
                      {{item.build_name}}
                  </view>
                  <view class="build_info flex-row items-center">
                    <text class ="area">
                      {{item.area_name}}
                    </text>
                    <text class ="jimian" v-if ="item.jian_mj && item.jian_mj.length">
                      {{item.jian_mj[0]}}- {{item.jian_mj[1]}}
                    </text>
                      
                  </view>
                  <view class="build_label flex-row items-center">
                      <text class ="build_label_item" v-for = "(l,index) in item.label" :key ="index" >{{l}} </text>
                  </view>
                </view>
              </view>
              <view class="build_item_b flex-row items-center">
                <view class="price flex-1">
                    {{item.price}}
                </view>
                <view class="daohang" @click.prevent.stop ="toLocation(item)">
                  地图导航
                </view>
                <view class="zixun" @click.prevent.stop ="makePhone(item)" >
                  立即咨询
                </view>
              </view>
            </view>
          </view>

        </view>
      </view>
      <!-- 详情-->
      <view class="con"  v-if ="detail.desc_type==2">
        <view class="border_con">
         
          <view class="con_title" :style="styleObj1"  >
            活动详情
          </view>
          <view class="activ_info">
            <view class="act_con" v-html ="detail.content"></view>
          </view>

        </view>
      </view>
      <view class="con con_nobg_img"  v-if ="detail.desc_type==1" v-html ="detail.content">
        <!-- <view class="con_img" > -->
          <!-- <image :src ="detail.desc_pic | imageFilter('w_400')" mode="widthFix"></image> -->
        <!-- </view> -->
      </view>
    </view>
    <view class="right_icon">
      <view class="right_icon_t" @click='showRule'>
        活动规则
      </view>
      <view class="right_icon_t" @click="toMyPrize">
        我的奖品
      </view>
    </view>
    <view class="right_share">
      <view class="right_share_t share" @click='toShare'>
        
          <image mode="widthFix" :src ="`/yidongduan/flop/<EMAIL>` |imageFilter">
          </image>
     
      </view>
      <view class="right_share_t" @click="refresh">
          <image  mode="widthFix" :src ="`/yidongduan/flop/refresh.png` |imageFilter">

          </image>
      </view>
      <view class="audio-box" :class="{rotate:playing}" @click="switchAudio()" v-if="audio">
        <my-icon type="yinyue" size="20" color="#88470d"></my-icon>
    </view>
    </view>

    <my-popup ref="show_rule" position="center">
			<view class="qrcode-box ">
				<view class="img-box">
          <view class="qrcode-box_title" :style="titleBgStyle">
            <view class="qrcode-box_title_con">
              活动规则
            </view>
          </view>
					<view class="rule_con " v-html="rules">

          </view>
          <view class="login_btn flex-row items-center j-center">
            
            <view class="rule_kown" @click ="$refs.show_rule.hide()">
              我知道了
            </view>
          </view>
				</view>
				<!-- <view class="icon-box" @click="$refs.show_rule.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view> -->
			</view>
		</my-popup>
    <my-popup ref="show_login" position="center">
			<view class="qrcode-box show_login center">
        <view class="img-box">
          <view class="login_img">
            <image mode="widthFix" :src ="`/yidongduan/flop/<EMAIL>` | imageFilter('w_320')"></image>
          </view>
          <view class="tip">我们需要你的授权，以继续操作</view>
          <view class="login_btn flex-row items-center j-center">
            <!-- <view class="icon"> -->
              <myIcon type="weixin" size="32rpx" color="#Fff"></myIcon>
            <!-- </view> -->
            <view class="w_login" @click ="shouquanLogin">
              微信授权登录
            </view>
          </view>
        </view>
				<view class="icon-box" @click="$refs.show_login.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
    <my-popup ref="show_card" position="center" >
			<view class="qrcode-box center" @click ="$refs.show_card.hide()" :class ="{opcity0:is_again}">
				<view class="img-box card">
          <view class="qrcode-box_send_info flex-row items-center j-center " >
            <!-- <view class="qrcode-box_title_con"> -->
              <view class="prelogo">
                <image mode="widthFix" :src='getCardInfo.prelogo |imageFilter("w_80")'></image>
              </view>
              <view class="send_name" v-if ="getCardInfo.user_name">
                {{getCardInfo.user_name}}{{getCardInfo.is_suoyao?'索要':'赠送'}}的卡
              </view>
            <!-- </view> -->
          </view>
          <view class="qrcode-box_title " >
            <!-- <view class="qrcode-box_title_con"> -->
              <template  v-if ="getCardInfo.id">
                    恭喜您获得此卡
              </template>
              <template  v-else>
                   {{getCardInfo.msg ||''}}
              </template>
              
              
            <!-- </view> -->
          </view>
					<view class="card_con" :style ="[getCardInfo.id?cardJinStyle:'']">
            <view class="card_img" v-if ="getCardInfo.id || getCardInfo.selected_style">
              <image v-if ="getCardInfo.pic" mode="widthFix" :src='getCardInfo.pic |imageFilter("w_320")'></image>
              <image v-if ="getCardInfo.selected_style" mode="widthFix" :src='getCardInfo.selected_style |imageFilter("w_320")'></image>
            </view>
            
          </view>
          <view class="login_btn flex-row items-center j-center">
            
            <view class="card_kown" :style="cardKnowStyle" @click.prevent.stop ="choukaAgain">
            <template v-if ="getCardInfo.is_send">开心收下</template>
            <template v-else-if ="getCardInfo.is_suoyao">确认赠送</template>
            <template v-else>继续抽卡 x{{detail.prize_draw_count}}</template>
              
            </view>
          </view>
				</view>
				<view class="icon-box" @click.prevent.stop="hide('show_card')">
          <image mode ="widthFix" :src='`/yidongduan/flop/<EMAIL>` |imageFilter("w_80")'></image>
					<!-- <my-icon type="guanbi" color="#fff" size="62rpx"></my-icon> -->
				</view>
			</view>
		</my-popup>
    <my-popup ref="show_task" position="bottom" :touch_hide="true">
			<view class="qrcode-box mt0  w_f">
				<view class="img-box task">
          <view class="qrcode-box_title center" :style="styleObj1" >
            <!-- <view class="qrcode-box_title_con"> -->
              做任务 得抽卡机会
            <!-- </view> -->
          </view>
					<view class="task1">
            <template  v-for ="(item,value) in taskList">
              <view class="task_item flex-row items-center" v-if='item.is_open'  :key="value" >
                <view class="task_item_icon">
                  <image mode="widthFix" :src ="item.icon | imageFilter"></image>
                </view>
                <view class="task_item_middle flex-1">
                  <view class="task_item_middle_title flex-row items-center">
                    <text>
                    {{item.title}}
                    </text> 
                    <text v-if ="value !=='follow_task' && value !=='gift_task'&& value !=='group_task'" class ="number" :style="numberStyleBg">
                      +{{item.add}}
                    </text>
                  </view>
                  <view class="task_item_middle_info">
                    {{item.desc}}
                
                  </view>
                </view>
              
                <view class="task_item_oper" :class ="{finished:item.is_finish}" @click ="doTask(item,value)">
                  <template v-if ="value !=='gift_task'&& value !=='group_task'">
                     {{item.is_finish?"已完成":'未完成'}}
                  </template>
                  <template v-else >
                     {{item.is_finish?"已完成":'去完成'}}
                  </template>
                 
                </view>
                
              </view>
            </template>
          </view>
          
				</view>

			</view>
		</my-popup>
     <my-popup ref="show_zhuli" position="center">
			<view class="qrcode-box zhuli center" >
        <view class="zhuli_content" :style ="zhuliStyle">
          <view class="img-box">
            
            <view class="zhuli_title">
              {{zhuli_info.title}}
            </view>
            <view class="zhuli_con">
             {{zhuli_info.msg}}
            </view>
          <view class="login_btn flex-row items-center j-center">
              <view class="zhuli_kown"  @click ="$refs.show_zhuli.hide()">
              我知道了
              </view>
            </view>
            
            
          </view>
        </view>
				<!-- <view class="icon-box" @click="$refs.show_rule.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view> -->
			</view>
		</my-popup>
    <my-popup ref="show_zhuli_success" position="center">
			<view class="qrcode-box zhuli center" >
        <view class="zhuli_content" :style ="zhuliStyle2">
          <view class="img-box">
            
            <view class="zhuli_title">
              {{zhuli_info.title}}
            </view>
            <view class="zhuli_con">
             {{zhuli_info.msg}}
            </view>
          <view class="login_btn flex-row items-center j-center">
              <view class="zhuli_kown"  @click ="$refs.show_zhuli_success.hide()">
              我知道了
              </view>
            </view>
            
            
          </view>
        </view>
				<!-- <view class="icon-box" @click="$refs.show_rule.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view> -->
			</view>
		</my-popup>
    <my-popup ref="show_confirm_zhuli" position="center">
			<view class="qrcode-box zhuli center" >
        <view class="zhuli_content" :style ="zhuliStyle2">
          <view class="img-box">
            
            <view class="zhuli_title">
              邀请您为TA助力
            </view>
            <view class="zhuli_con">
              <!-- TA的助力次数今日已达上限～ -->
            </view>
          <view class="login_btn flex-row items-center j-center">
              <view class="zhuli_kown"  @click ="confirmHelp">
              帮他助力
              </view>
            </view>
            
            
          </view>
        </view>
				<view class="icon-box" @click="$refs.show_confirm_zhuli.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
    <my-popup ref="qrcode_popup" position="top">
			<view class="qrcode-box">
        
				<!-- #ifdef H5 -->
				<view class="img-box">
          <view class="qrcode-box_title" :style="titleBgStyle" >
            关注微信公众号
          </view>
          <view class="qrcode_img">
            	<image @longtap="saveQrcode" class="qrcode" :src="detail.wx_qr_code" mode="aspectFill"></image>
          </view>
				
					<view>
						<view class="title">长按保存图片</view>
						<view class="tip">相册选取，识别关注</view>
					</view>
          
				</view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view class="img-box">
          <view class="qrcode-box_title" :style="{backgroundImage:`url(${rule_bg})`}">
            关注微信公众号
          </view>
					<image class="qrcode" :src="detail.wx_qr_code" mode="aspectFill"></image>
					<view>
						<view class="tip">长按识别二维码关注公众号</view>
					</view>
          
				</view>
				<!-- #endif -->
         
				<view class="icon-box" @click="$refs.qrcode_popup.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
    <my-popup ref="group_popup" position="top">
			<view class="qrcode-box">
        
				
				<view class="img-box group">
          <view class="qrcode-box_title" :style="titleBgStyle" >
            {{group_info.title}}
          </view>
					<image @longtap="saveQrcode" class="qrcode11" :src="group_info.qr_code" mode="aspectFill"></image>
					<view>
						<view class="title">{{group_info.descp}}</view>
						<view class="tip">微信号：{{group_info.account}}</view>
					</view>
          <view class="login_btn flex-row items-center j-center">
            <view class="card_kown" @click ="copyWechats($event,group_info)">
              复制微信号
            </view>
          </view>
				</view>
        <!-- #ifdef H5 -->
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<!-- <view class="img-box group">
          <view class="qrcode-box_title" :style="{backgroundImage:`url(${rule_bg})`}">
              {{group_info.title}}
          </view>
					<image class="qrcode11" :src="qrcode" mode="aspectFill"></image>
					<view>
							<view class="title">{{group_info.desc}}</view>
						<view class="tip">微信号：qwe123</view>
					</view>
          <view class="login_btn flex-row items-center j-center">
            <view class="card_kown" @click ="copyWechat">
              复制群号
            </view>
          </view>
				</view> -->
				<!-- #endif -->
        
				<view class="icon-box" @click="$refs.group_popup.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
     <my-popup ref="show_prize" position="center" >
			<view class="qrcode-box center" :style="zhongjiangStyle" >
				<view class="img-box prize_info">
          <!-- <view class="jiangpin_img">
            <image mode="widthFix" :src="`/yidongduan/blindBox/<EMAIL>` |imageFilter " alt="">
          </view> -->
					<view class="jiangpin_title">
            {{prize_info.award_name}}
            
          </view>
          <!-- 中奖图片 -->
          
          <!-- <view class="jiangpin_sub_title">
           {{prizeInfo.reason}}
           
          </view> -->
          <view class="jiangpin_btn flex-row flex-1" @click='toHexiao'>
             去核销
          </view>
				</view>
				<view class="icon-box"  @click='$refs.show_prize.hide()'>
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
    <my-popup ref="show_tip" position="center" >
			<view class="qrcode-box center" :style="zhongjiangStyle" >
				<view class="img-box prize_info">
          <!-- <view class="jiangpin_img">
            <image mode="widthFix" :src="`/yidongduan/blindBox/<EMAIL>` |imageFilter " alt="">
          </view> -->
					<view class="jiangpin_title">
            {{show_tip_info.title}}
            
          </view>
          
          <view class="jiangpin_btn flex-row flex-1"  @click='closeJinghouKaijiang'>
             静候开奖
          </view>
				</view>
				<view class="icon-box"  @click='closeJinghouKaijiang'>
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
    <my-popup ref="show_confirm_prize" position="center" >
			<view class="qrcode-box center" :style="zhongjiangStyle" >
				<view class="img-box prize_info">
          
					<view class="jiangpin_title">
            卡片已集齐 现在抽奖？
          </view>
          
          <view class="jiangpin_btn flex-row flex-1"  @click='closeConFirmPrize(1)'>
             马上开奖
          </view>
				</view>
				<view class="icon-box"  @click='closeConFirmPrize(0)'>
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
    <!--  -->
      <view class="tanmu" >
      <view class="tanmu_c" id ="tanmu" :style="{transform:`translateX(${tanmu_tranformX*-1}px)`}" >
        <template v-for ="(item) in tanmuList">
          <!-- <template v-if ="index<tanmuList.length/2">    :class="{show:item.show}"   :style="{top:tanmu_tranformX+'rpx'}"-->
            <view class="tanmu_item"  :key ="item.id">
              <view class="prelogo">
                <image mode="widthFix" :src="item.headimage"  alt="">
              </view>
              <view class="desc">{{item.desc}}</view> 
            </view>
          <!-- </template> -->
          <!-- <template v-if ="index>=tanmuList.length/2">
            <view class="tanmu_item"  :key ="item.id">
              <view class="prelogo">
                <image :src="item.prelogo"  alt="">
              </view>
              <view class="desc">{{item.name}}{{item.desc}}</view> 
            </view>
          </template> -->
        </template>

        
        
      </view>
    </view>
    <!-- <view class="tanmu tanmu1" :style="{transform:`translateX(${tanmu1_tranformX*-1}px)`}"  >
      <view class="tanmu_c" id ="tanmu1">
        <template v-for ="(item,index) in tanmuList">
          <template v-if ="index>=tanmuList.length/2">
            <view class="tanmu_item"  :key ="item.id">
              <view class="prelogo">
                <image :src="item.headimage"  alt="">
              </view>
              <view class="desc">{{item.cname}}  {{item.award_name}}</view> 
            </view>
          </template>
        </template>
      </view>
    </view> -->


    <!-- #ifdef H5 -->
      <!-- H5海报 -->
      <view id="card">
        <view class="card_img-box">
          <image :src="detail.poster | imageFilter('w_8001')" mode="widthFix"></image>
          <view class="chat_card">
            <image mode ="widthFix" :src="qrcode1"></image>
          </view>
        </view>
        
        
      </view>
      <!-- #endif -->
      <my-popup ref="show_invite"  position="bottom" >
			<view class="qrcode-box  mt0  w_f">
         <view class="postor-box"  v-if="cardImg">
            <image class="card-img" :src="cardImg" mode="widthFix" @click.stop.prevent="$refs.show_invite.hide()"></image>
          </view>
				<view class="img-box bottom_bar">
         
          
          <view class="invite_type flex-row items-center">
            <view class="invite_type_item" @click ="showTip">
              <view class="invite_type_image">
                <image :src="`/yidongduan/blindBox/<EMAIL>`| imageFilter('m_80')" mode ="widthFix"></image>
              </view>
              <view class="invite_type_name">
                微信转发
              </view>
            </view>
            <view class="invite_type_item " @click ="saveCard">
              <view class="invite_type_image">
                <image :src="`/yidongduan/blindBox/<EMAIL>`| imageFilter('m_80')" mode ="widthFix"></image>
              </view>
              <view class="invite_type_name">
                保存海报
              </view>
            </view>
            
          </view>
          <!-- <view class="jiangpin_btn flex-row flex-1" @click='$refs.show_invite.hide()'>
             取消
            
          </view> -->
				</view>
			</view>
		</my-popup>
    <!-- <my-popup ref="show_swiper"  position="center" >
      <view class="swiper1">
          <swiper  class="banner"   :indicator-dots="false"
            :circular="true"
            :duration="300"
            indicator-active-color="#f65354"
            @change="swiperChange"
            :current="swiperCurrent" >
            <swiper-item class="items-center sw_i" >
              <view class ="sw_item" v-for ="item in cardsList " :key ="item.id">
                <view class="sw_img">
                  <image :src="item.selected_style | imageFilter" mode="widthFix"></image>
                </view>
                <view class="ws_img_count">
                  123
                </view>
              </view>
                
            </swiper-item>
            
          </swiper>
      </view>
			
		</my-popup> -->
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <my-popup ref="sub_form_popup" position="center" height="800rpx" :touch_hide="false">
        <view class="sub_box" id="sub_box">
            <view class="sub_header">
                <view class="sub_title">完善兑奖信息</view>
                <view class="icon">
                <image  mode="widthFix"  :src="'/images/new_icon/baoming_tg.png' | imageFilter('m_320')"></image>
                </view>
            </view>
            <view class="form_box">
                <view class="sub_content">所填信息不会公开 仅用于活动兑奖</view>
                <view class="sub_form">
                    <input v-model="name" class="sub_tel" maxlength="10" type="text" placeholder="称呼" @input="inputName" />
                    <input v-model="tel"  class="sub_tel" maxlength="11" type="number" placeholder="手机号" @input="inputPhone" />
                    <view class="btn-box">
                        <button class="default" @click="subData()">提交</button>
                        <view class="close_btn" @click="$refs.sub_form_popup.hide()">取消</view>
                    </view>
                </view>
            </view>
        </view> 
    </my-popup>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
     <!-- 登录弹窗 -->
     <!-- #ifndef MP-WEIXIN-->
    <login-popup ref="login_popup" @onclose="toLoginPage" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
    <!-- #endif -->
  </view>
</template>

<script>
import {config} from "@/common/index";
import myPopup from "@/components/myPopup"
import myIcon from "@/components/myIcon.vue"
import shareTip from "@/components/shareTip.vue"
import wx from "weixin-js-sdk"
import {
  formatImg,
} from '@/common/index.js'
// #ifdef H5
import html2canvas from '@/common/html2canvas.min.js'
// #endif
import allTel from '@/common/all_tel.js'
// #ifndef MP-WEIXIN
import loginPopup from '@/components/loginPopup'
// #endif
export default {
  components:{
    myPopup,
    myIcon,
    shareTip,
    // #ifndef MP-WEIXIN
    loginPopup,
    // #endif
  },
  data(){
    return {
      styleObj:{

      },
      cardJinStyle:{},
      numberStyleBg:{},
      detail:{
        count1:0,
        count2:0,
        top_bg:""
      },
      styleObj1:{},
      con:"",
      rule:"",
      blindBox:{},
      qrcode:"",
      cardsList:[],
      userTaskData:[],
      leads:[],
      buildList:[],
      rules:'',
      tanmuList:[],
      tanmu_tranformX:0,
      tanmu1_tranformX:20,
      group_info:{},
      taskList:{}, //任务列表
      active_status:1, //1 已开始 2 本场结束等待下一场 3 全天结束 0 已结束 4 未开启
      active_time:0,  //活动时间
      getCardInfo:{}, // 抽取卡片信息
      prizeInfo:{},
      prize_info:{},
      show_tip_info:{},
      zhuli_info:{},
      is_show:false,
      show_share_tip:false,
      cardImg:"",
      qrcode1:"",
      share_poster:"",
      swiperCurrent:0,
      is_again:false,
      audio:"",
      playing:false,
      tel:"",
      name:"",
      innerAudioContext:null,
      zhuliStyle2:{},
      details:{},
      tel_params:{},
      tel_res:{},
      show_tel_pop:false,
      login_tip: '',
      rule_bg:""

    }
  },
  computed:{
    stepW(){
      let percent = (this.detail.word_is_all/this.detail.award_store)*100
      if (percent>=100){
        percent =100 
      }
      return percent+"%"
    },
      day(){
        console.log(this.active_time);
        if (!this.active_time) return 0
        return (Math.floor((this.active_time/(60*60*24))%60)+'').padStart(2,"0") 
      },
      hour(){
        if (!this.active_time) return 0
        return (Math.floor((this.active_time/3600)%60)+'').padStart(2,"0")
      },
      minute(){
        if (!this.active_time) return 0
        return (Math.floor((this.active_time/60)%60)+'').padStart(2,"0") 
      },
      second(){
        if (!this.active_time) return 0
        return (Math.floor(this.active_time%60)+'').padStart(2,"0")
      },
      picWidth(){
        if (!this.detail.rows_number){
          return 20+'%'
        }else {
          return (100/this.detail.rows_number)+"%"
        }
      },
      is_open_middle_num(){
        return this.$store.state.im.istelcall
      }
  },
   onHide(){
        this.innerAudioContext&&this.innerAudioContext.pause()
    },
    onShow(){
      if(this.playing){
          this.innerAudioContext.play()
    }},
  onLoad(options){
    this.styleObj={
      backgroundImage:`url(${config.imgDomain}/yidongduan/flop/<EMAIL>)`
    }
    this.detail.bg_top_pic=`${config.imgDomain}/yidongduan/flop/<EMAIL>`
    this.numberStyleBg= {
      backgroundImage:`url(${config.imgDomain}/yidongduan/flop/<EMAIL>)`,
      backgroundSize:"100% 100%",
      backgroundRepeat:"no-repeat"
    }
    this.zhuliStyle={
      backgroundImage:`url(${config.imgDomain}/yidongduan/flop/<EMAIL>)`,
      backgroundSize:"100% 100%",
      backgroundRepeat:"no-repeat"
    }
    this.zhuliStyle2={
      backgroundImage:`url(${config.imgDomain}/yidongduan/flop/<EMAIL>)`,
      backgroundSize:"100% 100%",
      backgroundRepeat:"no-repeat"
    }
    this.titleBgStyle ={
      backgroundImage:`url(${config.imgDomain}/yidongduan/flop/<EMAIL>)`,
      backgroundSize:"100% 100%",
      backgroundRepeat:"no-repeat"
    }
    this.zhongjiangStyle={
      backgroundImage:`url(${config.imgDomain}/yidongduan/flop/zhongjiang.png)`,
      backgroundSize:"100% 100%",
      backgroundRepeat:"no-repeat"
    }
    this.styleObj1={
      backgroundImage:`url(${config.imgDomain}/yidongduan/flop/<EMAIL>)`
    }
    this.cardKnowStyle={
      backgroundImage:`url(${config.imgDomain}/yidongduan/flop/<EMAIL>)`,
      backgroundSize:"100% 100%",
      backgroundRepeat:"no-repeat"

    }
    this.has_card_num_bg={
      backgroundImage:`url(${config.imgDomain}/yidongduan/flop/<EMAIL>)`,
      backgroundSize:"100% 100%",
      backgroundRepeat:"no-repeat"

    }
    this.cardJinStyle={
      "background-image": `url(${config.imgDomain}/yidongduan/flop/<EMAIL>?x-oss-process=style/w_320)`,
      "background-size": "100% 100%",
      "background-repeat": "no-repeat"
    }
    if (options.suoyao_id){
      this.suoyao_id = options.suoyao_id
    }
    if (options.send_id){
      this.send_id = options.send_id
    }
    if (options.share_id){
      this.share_id = options.share_id
    }
    if (options.sharer){
      this.sharer = options.sharer
    }
    if(options.id){
      this.id = options.id
    }
    if (this.videoUrl){
            this.videoContext = uni.createVideoContext('video')
        }
    
    // #ifdef h5
      var ua = window.navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i) == 'micromessenger') {
          this.isWechat =1
        }else {
          this.isWechat =0
        }
      if(this.isWechat){
        // 公众号
        if(wxApi.getCode().code!=undefined){ //如果获取到了微信code
          console.log(wxApi.getCode().code)
          this.getToken(wxApi.getCode().code)
          return
        }
      }
      // #endif
    
    this.getDetail()
    
  },
  mounted(){
    // this.$refs.sub_form_popup.show()
    // this.$refs.show_swiper.show()
  },
  methods:{
    inputName(e) {
          this.name = e.detail.value
      },
      inputPhone(e) {
          this.tel = e.detail.value
      },
      subData(){
        if (!this.name){
          uni.showToast({
            title:"请输入用户名",
            icon:"none"
          })
          return 
        }
        if (!this.tel){
          uni.showToast({
            title:"请输入手机号",
            icon:"none"
          })
          return 
        }
        uni.showLoading()
        
       
        this.$ajax.post("wordCollecting/perfectActivityUser",{
          user_tel:this.tel,
          user_name:this.name,
          activity_id:this.id
        },res=>{
          if (res.data.code ==1){
            uni.showToast({
              title:res.data.msg,
              icon:'none'
            })
            this.$refs.sub_form_popup.hide()
            this[this.currentFun]&&this[this.currentFun]()
          }else {
            uni.showToast({
              title:res.data.msg,
              icon:'none'
            })
          }
          this.clicking = false
           uni.hideLoading()
        },()=>{
          this.clicking = false
          uni.hideLoading()
        })
    },
    swiperChange(){},
    getDetail(){
      uni.showLoading({
        title:"加载中...",
        mask:true
      })
      let params = {
        activity_id:this.id
      }
      if (this.share_id){
        params.sharer = this.share_id
      }
      this[`tanmu_timer`]&&clearInterval(this[`tanmu_timer`])
      this.timer &&clearInterval(this.timer)
      this.$ajax.get("WordCollecting/activityDetail",params,res=>{
       let data =res.data
        if(res.data.code ==1){
          this.detail =data.activity
          // 弹幕列表
          this.tanmuList=data.activity.user_award_log 
          this.buildList= data.activity.builds
          this.leads = data.activity.leads
          this.rules = data.activity.rules
          this.cardsList = data.activity.words
         
          let current_time = +new Date()/1000
          // 首先判断当前时间和活动开始时间如果当前时间小于活动开始时间  活动未开启 
          //  如果活动未开始  this.active_status =-1 提示距离活动开始时间
          // if 当前时间大于活动开始时间 判断  open_day_times 1 为开启场次  是否开启场次 
          //如果开启场次
          
//1 已开始 2 本场结束等待下一场 3 全天结束 0 已结束 4未开启
          if(current_time< this.detail.activity_start_time) {
               this.active_status =4
               this.active_time = this.detail.activity_start_time-current_time
          }else if (current_time>this.detail.activity_end_time){
            // 结束
            this.active_status = 0
          }else {
            if (this.detail.open_day_times==1) {
              if(this.detail.day_times &&!Array.isArray(this.detail.day_times)) {
                this.active_time = this.detail.day_times.day_end_time-current_time
                this.active_status =1
              }else if (this.detail.day_next_times&&!Array.isArray(this.detail.day_next_times) ) {
                this.active_time = this.detail.day_next_times.day_start_time-current_time
                this.active_status =2
              }else {
                // 全天结束   

                if (this.detail.tomorrow ){
                  console.log(123323);
                  this.tomorrowStart = this.detail.tomorrow.day_start_time
                   this.tomorrowEnd = this.detail.tomorrow.day_end_time
                  this.active_status =3
                }else {
                  this.active_status = 0
                }
                
              }
            }else {
              console.log("没开场次");
              // 没开启场次当前时间和活动结束时间比较 如果当前时间小于结束时间正在进行
              if(current_time< this.detail.activity_end_time){
                this.active_time = this.detail.activity_end_time-current_time
                this.active_status =1
              }else {
                // 结束
                this.active_status = 0
              }
            }
          }
          
          this.share ={
            title:this.detail.share_title,
            content:this.detail.share_desc,
            pic:this.detail.share_pic
          }
          this.user_id = data.user_id
          this.user_name = data.cname
          let is_send = sessionStorage.getItem("is_send")
          let is_suoyao = sessionStorage.getItem("is_suoyao")
          let is_help = sessionStorage.getItem("is_help")
          let is_visit = sessionStorage.getItem("is_visit")
          this.share.link =window.location.origin+'/h5/flop/index?id='+this.id+'&share_id='+(this.user_id||'')
          this.getWxConfig()
          
          uni.setNavigationBarTitle({
            title:this.detail.activity_name
          })
          
          
          // if (this.suoyao_id && this.sharer != this.user_id&& !is_suoyao){
          //   sessionStorage.setItem("is_suoyao","1")
          //   this.getSuoyaoInfo()
          // }
          
          // if (this.share_id && this.share_id != this.user_id &&!is_help){
          //   sessionStorage.setItem("is_help","1")
          //   this.showHelp()
          // }
          if (this.$store.state.user_login_status>1 && !is_visit){
            sessionStorage.setItem("is_visit","1")
            this.doVisitTask()
          }
          wx.ready(() => {   //需在用户可能点击分享按钮前就先调用
              console.log(12312);
              if(data.activity.bg_music){
                  this.audio = data.activity.bg_music
                  this.playAudio(this.audio)
                  // this.playOnceAudio()
              }
          })
          // this.audio = 
          this.is_show=true
          this.checkShow1().then((res)=>{
            if (res.code ==1){
              if (this.suoyao_id && this.sharer != this.user_id&& !is_suoyao){
                sessionStorage.setItem("is_suoyao","1")
                this.getSuoyaoInfo()
              }
              if (this.share_id && this.share_id != this.user_id &&!is_help){
                sessionStorage.setItem("is_help","1")
                this.showHelp()
              }
              if (this.send_id&& this.sharer != this.user_id  &&!is_send){
                this.getSendInfo()
                sessionStorage.setItem("is_send","1")
              }
            }else{
              if (this.suoyao_id && this.sharer != this.user_id&& !is_suoyao){
                sessionStorage.setItem("is_suoyao","1")
               
              }
              if (this.share_id && this.share_id != this.user_id &&!is_help){
                sessionStorage.setItem("is_help","1")
                
              }
              if (this.send_id&& this.sharer != this.user_id  &&!is_send){
                
                sessionStorage.setItem("is_send","1")
              }
            }
            
          })
          // "end_type": 0,//,1:奖品库存为0，活动自动结束,2:活动时间已经结束，无法在抽取,3;活动场次已经结束，无法在抽取,0:进行中
          if (this.active_status>0 && this.active_status!==3) {
            this.timer = setInterval(() => {
              if (this.active_time<=0) {
                if ( this.active_status =1 &&this.detail.day_next_times &&!Array.isArray(this.detail.day_next_times) ){
                  let c_time = +new Date()/1000
                  this.active_status =2
                  this.active_time = this.detail.day_next_times.day_start_time-c_time
                }else {
                  clearInterval(this.timer)
                  return 
                }
                
              }
               this.active_time--
            }, 1000);
          }
          uni.hideLoading()
          // this.runTanmu("tanmu")
          // setTimeout(() => {
          //   this.runTanmu("tanmu1")
          // }, 2000);
        }else {
          uni.hideLoading()
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    },
    showHelp(){
      // this.zhuliStyle = Object.assign({},this.zhuliStyle)
      // console.log(this.zhuliStyle);
      // this.$forceUpdate()
      this.$nextTick(()=>{
        this.$refs.show_confirm_zhuli.show()
      })
      
    },
    toLocation(item){
      uni.openLocation({
         latitude: parseFloat(item.lat),
          longitude: parseFloat(item.lng),
          name: item.build_name,
          address:item.address,
          success: function () {
            console.log('success')
          },
      })
    },
    callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
      this.tel_params = {
        type,
        callee_id,
        scene_type,
        scene_id,
        source,
        bid,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      // #ifdef MP-WEIXIN
      allTel(this.tel_params)
      // #endif
      // #ifndef MP-WEIXIN
      // 全局开启中间号且楼盘开启中间号需要检测登录
      if(this.is_open_middle_num == 1 && this.details.use_middle_call > 0 ){
        this.tel_params.intercept_login = true
        this.tel_params.fail = (res)=>{
          if(res.data.code === -1){
            this.$store.state.user_login_status = 1
            this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
          }
          if(res.data.code === 2){
            this.$store.state.user_login_status = 2
            this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
          }
        }
        allTel(this.tel_params)
      }else{
        allTel(this.tel_params)
      }
      // #endif
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    showLoginPopup(tip){
      this.login_tip = tip
      this.$refs.login_popup.showPopup()
    },
    onLoginSuccess(res){
      this.$store.state.user_login_status = 3
      this.retrieveTel()
    },
    makePhone(item){
      if (item.system_id>0) {
        this.details =  item
        this.callMiddleNumber(1,item.system_id,1,item.system_id,'build_detail',item.system_id)
        return 
      }
      this.details =  {}
      if(item.phone){
        uni.makePhoneCall({
          phoneNumber:item.phone
        })
      }else {
        uni.showToast({
          title:"该楼盘暂无电话",
          icon:"none"
        })
      }
    },
    // 做访问任务
    doVisitTask(){
      this.$ajax.get("WordCollecting/addDrawByBrowse",{activity_id:this.id},res=>{
        console.log(res);
      })
    },
    // 获取增赠送的卡片的信息
    getSendInfo(){
      uni.showLoading()
      this.$ajax.get("wordCollecting/giftWordDetail",{ask_id:this.send_id,activity_id:this.id},res=>{
        if(res.data.code ==1){
          this.getCardInfo= res.data.word
          this.getCardInfo.is_send =1
          if ( this.getCardInfo.is_gift==0){
             this.$refs.show_card.show()
          }
        }else {
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
        uni.hideLoading()
      },()=>{
        uni.hideLoading()
      })
    },
    // 确认帮助
    confirmHelp(){
      // /WordCollecting/helpingUser?activity_id=1&sharer=5680(分享者id)
      
      if (this.clicking) return 
      this.clicking = true
      uni.showLoading()
      this.$ajax.get("WordCollecting/helpingUser",{sharer:this.share_id,activity_id:this.id},res=>{
        if(res.data.code ==1){
        //  this.zhuliStyle.backgroundImage =  ``
        this.$set(this.zhuliStyle,'backgroundImage',`url(${config.imgDomain}/yidongduan/flop/<EMAIL>)`)
         this.zhuli_info={
           title:'助力成功',
           msg:res.data.msg
         }
         uni.hideLoading()
         this.$refs.show_confirm_zhuli.hide()
        this.$refs.show_zhuli_success.show()
        }else {
          this.zhuliStyle.backgroundImage = `url(${config.imgDomain}/yidongduan/flop/<EMAIL>)`
          this.zhuli_info={
           title:'助力失败',
           msg:res.data.msg
         }
         uni.hideLoading()
         this.$refs.show_confirm_zhuli.hide()
        this.$refs.show_zhuli.show()
        }
       
        this.clicking =false
      },()=>{
        this.clicking =false
        uni.hideLoading()
      })
    },
    // 获取索要的信息
    getSuoyaoInfo(){
      uni.showLoading()
      this.$ajax.get("wordCollecting/askWordDetail",{ask_id:this.suoyao_id,activity_id:this.id},res=>{
        if(res.data.code ==1){
          if(res.data.un_perfect){
            uni.hideLoading()
            this.$refs.sub_form_popup.show()
            return 
          }
          this.getCardInfo= res.data.word
          this.getCardInfo.is_suoyao =1
          if ( this.getCardInfo.is_gift==0){
             this.$refs.show_card.show()
          }
          
          // this.$refs.show_card.show()
        }else {
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
        uni.hideLoading()
      },()=>{
        uni.hideLoading()
      })
    },
    shouquanLogin(){
     // #ifdef H5
      //  公众号授权
      const redirect_uri = window.location.origin + "/h5/flop/index?id="+this.id;
				this.$ajax.get('/wap/index/wxAppId',{},res=>{
					if(res.data.appid){
						wxApi.author(redirect_uri,res.data.appid)
					}else{
						uni.showToast({
							title:res.data.msg||'公众号参数配置错误',
							icon:"none"
						})
					}
				})
      // #endif
    },
    toLoginPage() {
      if (this.$store.state.user_login_status === 1) {
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if (this.$store.state.user_login_status === 2) {
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    // 公众号授权登录 获取token
    getToken(code){
        // #ifdef H5
				this.$ajax.get('/wechat/Index/getMemberInfo.html',{code:code,goufangjin_sid:uni.getStorageSync('goufang_sid')},res=>{
					if(res.data.code == 1){
						this.getUserInfo(res.data.user)
						// 存储token
						uni.setStorageSync('token',res.data.token)
						// uni.showToast({
						// 	title:"登录成功"
						// })
						this.$store.state.user_login_status = 2
						if(res.data.user.tel){
							this.$store.state.user_login_status = 3
						}
						// this.getDetail()
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
				})
        // #endif
			},
    setTanmu (el) {
      return new Promise ((res,rej)=>{
      this.$nextTick(() => {
          uni.createSelectorQuery().in(this).select(`#${el}`).boundingClientRect(data => {
              res(data)
          }).exec()
        })
        
      })
    },
    async runTanmu2 (el,step = 1000) {
      let index=0,timer=null
      this.$set(this.tanmuList[index],"show",true)
      this[`${el}_timer`] = setInterval(() => {
        if(index>this.tanmuList.length-1){
          index=0
        }
        
        timer&&clearTimeout(timer)
        // setTimeout(() => {
          console.log(3);
        this.$set(this.tanmuList[index],"show",true)
        index++
        // }, 1000);
        this.tanmu_tranformX = Math.random()*(300+1)+100
        // this.$set( this.tanmuList[index],"show",true)
        // Math.random() * (b - a + 1) + a)
        console.log(index,2);
        let timer = setTimeout(() => {
          console.log(1);
           this.tanmuList.map(item=>item.show=false)
        }, 2000);
        // setTimeout(() => {
           
        // }, 3000);
      }, 3000);
    },
    async runTanmu (el,step = 1000) {
      let  {width} = await  this.setTanmu(el)
      this[`${el}_timer`] = setInterval(() => {
        this[`${el}_tranformX`] += step / 1000
        if (this[`${el}_tranformX`]+ uni.getSystemInfoSync().screenWidth > width + 50) {
          clearInterval(this[`${el}_timer`])
          // setTimeout(() => {
            this[`${el}_tranformX`]= 0
          // }, 1000);
          // setTimeout(() => {
            this.runTanmu(el)
          // }, 1000);
        }
      }, 10);
    },
    //活动规则弹框
    showRule(){
      this.$refs.show_rule.show()
    },
    showTask(){
      // wordCollecting/activityTask?activity_id=1
      // if (this.active_status!=1){
      //   uni.showToast({
      //     title:"当前不在活动时间范围内",
      //     icon:"none"
      //   })
      //   return 
      // }
      uni.showLoading()
      this.$ajax.get('wordCollecting/activityTask',{activity_id:this.id},res=>{
        console.log(res);
        if(res.data.code ==1){
          let taskList = res.data.list
          this.taskList = taskList
          setTimeout(() => {
            this.$refs.show_task.show()
          }, 300);
        }else {
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
        uni.hideLoading()
      },()=>{
        uni.hideLoading()
      })
    },
    // getPrizeInfo(){
    //   let user = await this.getCardCount()
    //   let is_open_prize = sessionStorage.getItem("is_open_prize") 
    //   if (user.is_all && !user.prize_draw && !is_open_prize){
    //     uni.showModal({

    //     })
    //   }
    // },
    async chouka(){
      this.getCardInfo = {}
       if (this.$store.state.user_login_status ==1){
        if (this.isWechat){
          this.showShouquan()
        }else {
          this.$navigateTo("/user/login/login")
        }
        this.is_again=false
        return 
      }
      if (this.detail.prize_draw_count==0){
        // TODO 剩余抽卡次数已用完
        uni.showToast({
          title:"您的抽卡次数已用完",
          icon:"none"
        })
        this.is_again=false
        this.$refs.show_card.hide()
        return
      }
      // if (this.active_status!=1){
      //   uni.showToast({
      //     title:"当前不在活动时间范围内",
      //     icon:"none"
      //   })
      //   return 
      // }
      if (this.clicking) return 
       this.clicking = true
       uni.showLoading()
      this.$ajax.get("WordCollecting/cardDraw",{activity_id:this.id},res=>{
        console.log(res);
        if(res.data.code ==1){
          if(res.data.un_perfect){
            this.currentFun = "chouka"
            this.clicking = false
            uni.hideLoading()
            this.$refs.sub_form_popup.show()
            
            return 
          }
          if(Array.isArray(res.data.word)){
            res.data.word={}
          }
          this.getCardInfo = res.data.word
          if ( this.getCardInfo.id){
            let index = this.cardsList.findIndex(item=>item.id == this.getCardInfo.id)
             this.getCardInfo.pic = this.cardsList[index].selected_style
            this.$set( this.cardsList[index],"count",++this.cardsList[index].count)
          }else {
            this.getCardInfo.msg = res.data.msg
          }
          this.is_again=false
          this.$set(this.detail,"prize_draw_count",--this.detail.prize_draw_count)
          this.$forceUpdate()
          this.$refs.show_card.show()
        //  this.getPrizeInfo()
          // let date=this.detail.prize_start_time-(+new Date()/1000) 
          
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
        uni.hideLoading()
        setTimeout(() => {
          this.clicking =false
        }, 300);
      },()=>{
        uni.hideLoading()
        this.clicking =false
      })
      // this.$refs.show_card.show()
    },
    // 卡片管理 赠送索要 
    operCard(item){
      if(item.count>1){
        // 赠送
        this.sendCard(item)
      }else if(item.count<1) {
        this.suoyaoCard(item)
      }
    },
    closeJinghouKaijiang(){
      this.is_again=0
      this.$refs.show_card.hide()
      this.$refs.show_tip.hide()
      if (sessionStorage.getItem("is_help")) {
        setTimeout(() => {
          this.showHelp()
        }, 300);
      }
      if (sessionStorage.getItem("is_send")) {
        setTimeout(() => {
          this.getSendInfo()
        }, 300);
      }
      if (sessionStorage.getItem("is_suoyao")) {
        setTimeout(() => {
          this.getSuoyaoInfo()
        }, 300);
      }
    },
    sendCard(item = (this.sendItem||{})){
      if (this.clicking) return 
      this.clicking = true
      uni.showLoading()
      this.$ajax.get("WordCollecting/giftWordCard",{activity_id:this.id,word_id:item.id},res=>{
        if(res.data.code ==1){
          if(res.data.un_perfect){
             this.sendItem = item
              this.currentFun= "sendCard"
              this.clicking = false
              uni.hideLoading()
            this.$refs.sub_form_popup.show()
            return 
          }
          this.share.content = (this.user_name||'')+"赠送给您一张卡片"
          this.share.link =window.location.origin+'/h5/flop/index?id='+this.id
          if (res.data.ask_id){
             this.share.link+= "&send_id="+res.data.ask_id+'&sharer='+(this.user_id||'')
            //  this.copyWechat(this.share.link)
          }else {
            this.share.link+='&share_id='+(this.user_id||'')
          }
          this.getWxConfig()
          this.show_share_tip =true
        }else {
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
        uni.hideLoading()
        setTimeout(() => {
          this.clicking =false
        }, 300);
      },()=>{
        uni.hideLoading()
        this.clicking =false
      })
    },
    suoyaoCard(item=(this.suoyaoItem||{})){
      if (this.clicking) return 
      this.clicking = true
      uni.showLoading()
      this.$ajax.get("WordCollecting/askWordCard",{activity_id:this.id,word_id:item.id},res=>{
        if(res.data.code ==1){
          this.suoyaoItem = item
          this.currentFun= "suoyaoCard"
          if(res.data.un_perfect){
            uni.hideLoading()
            this.clicking = false
            this.$refs.sub_form_popup.show()
            return 
          }
          this.share.content = (this.user_name||'')+"向你索要一张卡片"
          this.share.link =window.location.origin+'/h5/flop/index?id='+this.id
          if (res.data.ask_id){
             this.share.link+= "&suoyao_id="+res.data.ask_id+'&sharer='+(this.user_id||'')
              this.getWxConfig()
              this.show_share_tip =true
          }else {
            this.share.link+='&share_id='+(this.user_id||'')
          }
        }else {
          unis.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
        uni.hideLoading()
        setTimeout(() => {
          this.clicking =false
        }, 300);
      },()=>{
        this.clicking =false
        uni.hideLoading()
      })
    },
    doTask(item,value){
      if (this.$store.state.user_login_status ==1){
        if (this.isWechat){
          uni.setStorageSync('backUrl', window.location.href)
          this.shouquanLogin()
        }else {
          this.$navigateTo("/user/login/login")
        }
        return 
      }
      switch (value) {
        case 'share_task':
          //邀请好友
          this.share.content = (this.user_name||'')+"邀请您加入"+this.detail.activity_name+"活动"
          this.share.link = `${window.location.origin}/h5/flop/index?id=${this.id}&share_id=${this.user_id||''}`
          this.$refs.show_task.hide()
          this.getWxConfig()
          this.show_share_tip = true
          // this.s
          // this.handleCreat()
          // this.copyWechat(this.share.link)
          
          break;
        case 'follow_task':

          if (!item.is_finish){
            this.$refs.show_task.hide()
            setTimeout(() => {
              this.showQrcode()
            }, 300);
          }

         
          
          break;
        case 'group_task' :
          // if (item.is_finish){
            this.$refs.show_task.hide()
            this.group_info= item.customer
            setTimeout(() => {
              this.showGroup()
            }, 300);
          // }
          break;
           
        case 'gift_task':
          console.log(item);

          // this.handleCreat()
          // this.share_poster = 
          // 好友交换
          uni.showToast({
            title:"请选择卡片进行索要或者赠送",
            icon:'none'
          })
        
          // if (item.is_finish){
            // this.$refs.show_task.hide()
            // this.share.link = `${window.location.origin}/flop/index?id=${this.id}&share_id=${this.user_id||''}`
            // this.copyWechat(this.share.link)
            // this.group_info= item.customer
            // setTimeout(() => {
            //   this.showGroup()
            // }, 300);
          // }
          break;
        default:
          break;
      }
    },
    toHexiao(){
      
      this.$refs.show_prize.hide()
      this.toPath("/flop/my_prize?id="+this.id)
    },
    choukaAgain(){
      
      if (this.getCardInfo.is_send){
        this.confirmJieshou()
        return 
      }
      if (this.getCardInfo.is_suoyao){
        this.confirmSend()
        return 
      }
      this.getCardInfo={}
      this.is_again =true
      this.checkShow(this.chouka)
    },
    firstPlayingAudio(){
      if (this.isClicked) return 
      if (!this.playing&&this.innerAudioContext){
          this.innerAudioContext.play()
          this.isClicked =true
      }else{
          this.isClicked =true
      }
    },
    confirmJieshou(){
      
      if (this.clicking) return 
      this.clicking = true
      uni.showLoading()
      // /WordCollecting/confirmGiftWord?activity_id=1&ask_id=5
      this.$ajax.get('WordCollecting/confirmGiftWord',{activity_id:this.id,ask_id:this.getCardInfo.ask_id},res=>{
        if(res.data.code==1){
          let index = this.cardsList.findIndex(item=>item.id== res.data.word_id)
          if(index>-1){
            this.$set( this.cardsList[index],"count",this.cardsList[index].count++)
          }
          uni.showToast({
            title:res.data.msg,
            icon:"none",
          })
          this.$refs.show_card.hide()
        }else {
           uni.showToast({
            title:res.data.msg,
            icon:"none",
          })
        }
        uni.hideLoading()
        setTimeout(() => {
          this.clicking =false
        }, 300);
      },()=>{
        this.clicking =false
        uni.hideLoading()
      })
    },
    // 确认赠送
    confirmSend(){
      
      if (this.clicking) return 
      this.clicking = true
      uni.showLoading()
      this.$ajax.get('WordCollecting/confirmAskWordCard',{activity_id:this.id,ask_id:this.getCardInfo.ask_id},res=>{
        if(res.data.code==1){
           
          let index = this.cardsList.findIndex(item=>item.id== res.data.word_id)
          if(index>-1){
            this.$set( this.cardsList[index],"count",this.cardsList[index].count--)
          }
          this.$refs.show_card&&this.$refs.show_card.hide()
          uni.showToast({
            title:res.data.msg,
            icon:"none",
          })
          
        }else {
           uni.showToast({
            title:res.data.msg,
            icon:"none",
          })
        }
        uni.hideLoading()
        setTimeout(() => {
          this.clicking=false
        }, 300);
      },()=>{
        this.clicking=false
      })
    },
    showShouquan(){
      this.$refs.show_login.show()
    },
    showQrcode (){
      this.$refs.qrcode_popup.show()
    },
    showGroup(){
      this.$refs.group_popup.show()
    },
    copyWechat(cont){
      if(!cont){
                uni.showToast({
                    title:"微信号为空",
                    icon:"none"
                })
                return
            }
            // #ifndef H5
            uni.setClipboardData({
                data:cont,
                success:res=>{
                    uni.showToast({
                        title:"复制成功",
                        icon:"none"
                    })
                }
            })
            // #endif
            // #ifdef H5
            let oInput = document.createElement('input');
            oInput.value = cont;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            document.execCommand("Copy"); // 执行浏览器复制命令
            uni.showToast({
                title:"复制成功",
                icon:"none"
            })
            oInput.remove()
            // #endif
            this.$refs.group_popup.hide()
    },
    toPath(url){
      this.$navigateTo(url)
    },
    toBuild(item){
      if (item.system_id==0){
        this.toPath("/flop/build_detail?id="+item.id +"&active_id="+this.id)
      }else {
        this.toPath("/pages/new_house/detail?id="+item.system_id)
      }
    },
    copyWechats(e,cont){
      this.copyWechat(cont.account)
    },
    toMyPrize(){
      this.toPath("/flop/my_prize?id="+this.id)
    },
    refresh(){
      if(this.clicking1) return
      console.log(********); 
      this.clicking1 = true
      setTimeout(() => {
         this.clicking1 =false
         this.getDetail()
      },500);
      
      
    },
    async  hide(el){
        this.$refs[el]&this.$refs[el].hide()
    },
   async checkShow(callback){
      let user = await this.getCardCount()
      if (user.is_all&& !user.prize_draw){
         let date=this.detail.prize_start_time -(+new Date()/1000) 
         let is_open_prize = sessionStorage.getItem("is_open_prize")
        if(this.is_again){
          this.$refs.show_card&&this.$refs.show_card.hide()
        }
        if (date>0){
          if(is_open_prize) {
            callback&&callback()
              return
            }
            sessionStorage.setItem("is_open_prize",1)
            // 开奖未开始
          let  day=(Math.floor((date/(60*60*24))%60)+'').padStart(2,"0") 
          let hour=(Math.floor((date/3600)%60)+'').padStart(2,"0")
          let minute= (Math.floor((date/60)%60)+'').padStart(2,"0")
          this.show_tip_info={
            title:`${Number(day)>0?(day+'天'):""}${Number(hour)>0?(hour+'小时'):""}${Number(minute)>0?(minute+'分钟'):""}后开奖`,
          }
          this.$refs.show_card=false
          this.$refs.show_tip.show()
        }else if((+new Date()/1000)>this.detail.prize_end_time){
          if(is_open_prize) {
            callback&&callback()
            return
          }
          sessionStorage.setItem("is_open_prize",1)
          this.$store.state.is_open_prize = 1
          // 开奖时间已结束
          this.show_tip_info={
            title:`活动已结束`,
          }
          this.$refs.show_card=false
          this.$refs.show_tip.show()
        }else{
          // 处理开一次网页只弹出一次开奖确认
        //  let is_open_prize = this.$store.state.is_open_prize &&sessionStorage.getItem("is_open_prize")
        //   if(!is_open_prize) {
        //     this.$refs.show_card.hide()
        //     this.$refs.show_confirm_prize.show()
        //     this.$store.state.is_open_prize = 1
        //     sessionStorage.setItem("is_open_prize",1)
        //   }else {
            callback&&callback()
          // }

        }
      }else {

        callback&&callback()
      }
    },
    async checkShow1(){
      let user = await this.getCardCount()
      return new Promise((res,rej)=>{
         if (user.is_all&& !user.prize_draw){
         let date=this.detail.prize_start_time-(+new Date()/1000) 
         let is_open_prize = sessionStorage.getItem("is_open_prize") 
        if (date>0){
            // 开奖未开始
            if(is_open_prize) {
               res({code:1})
              return
            }
            this.$store.state.is_open_prize = 1
            sessionStorage.setItem("is_open_prize",1)
          let  day=(Math.floor((date/(60*60*24))%60)+'').padStart(2,"0") 

            let hour=(Math.floor((date/3600)%60)+'').padStart(2,"0")
            let minute= (Math.floor((date/60)%60)+'').padStart(2,"0")
            let second = (Math.floor(date%60)+'').padStart(2,"0")
          this.show_tip_info={
            title:`${Number(day)>0?(day+'天'):""}${Number(hour)>0?(hour+'小时'):""}${Number(minute)>0?(minute+'分钟'):""}后开奖`,
          }
          this.$refs.show_tip.show()
          res({code:0})
          return 
        }else if((+new Date()/1000)>this.detail.prize_end_time){
          // 开奖时间已结束
          if(is_open_prize) {
            res({code:1})
            return
          }
          this.$store.state.is_open_prize = 1
          sessionStorage.setItem("is_open_prize",1)
          this.show_tip_info={
            title:`活动已结束`,
          }
          this.$refs.show_tip.show()
          res({code:0})
          return 
        }else{
          // 处理开一次网页只弹出一次开奖确认
          let is_open_prize =sessionStorage.getItem("is_open_prize")
          if(is_open_prize) {
            res({code:1})
              return
            
          }else {
            this.$refs.show_confirm_prize.show()
            res({code:0})
            return 
            // callback&&callback()
         }

        }
      }else {
        res({code:1})
        return 
        // callback&&callback()
      }
      })
     
    },
    closeConFirmPrize(type){
      if (type==1){
        this.getPrize()
      }
      this.$refs.show_confirm_prize&&this.$refs.show_confirm_prize.hide()
    },

   async getPrize(){
     
        this.$ajax.get("WordCollecting/drawPrize",{activity_id:this.id},res=>{
          if (res.data.code ==1){
            this.prize_info = res.data.award
            this.$refs.show_confirm_prize.hide()
            setTimeout(() => {
             this.$refs.show_prize.show()
            }, 300);
          }else {
            uni.showToast({
              title:res.data.msg,
              icon:"none"
            })
          }
        })
    },
    getCardCount(){
      return new Promise((resolve)=>{
        this.$ajax.get("WordCollecting/activityUserDetail",{activity_id:this.id},res=>{
            if (res.data.code ==1){
              resolve(res.data.user)
              return res.data.user
            }else {
            resolve({})
              return{}
            }
          })
      })
    },
    joinGroup(item){
      this.$ajax.get("WordCollecting/wordLeadDetail",{id:item.id,activity_id:this.id},res=>{
        console.log(res);
        if(res.data.code ==1){
          this.group_info = res.data.lead
          this.$refs.group_popup.show()
        }else {
          this.group_info ={}
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    },
    toShare(){
      if (this.detail.poster){
        this.handleCreat()
      }else {
        this.share.content = (this.user_name||'')+"邀请您加入"+this.detail.activity_name+"活动"
        this.share.link = `${window.location.origin}/h5/flop/index?id=${this.id}&share_id=${this.user_id||''}`
         this.getWxConfig()
         this.show_share_tip =true
      }
    },
    showTip(){
      this.$refs.show_invite.hide()
      this.show_share_tip =true
    },
    handleCreat() {
      // #ifdef APP-PLUS
      // this.showPopup = true
      // this.$refs.share_popup.show()
        return;
      // #endif
      // this.$refs.share_popup.hide()
      if (this.cardImg) {
        this.$refs.show_invite.show()
        return;
      }
      uni.showLoading({
        title: "正在生成海报",
        mask: true
      });
      // #ifdef H5
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      })
      let qrcode = this.detail.wx_qr_code
      // #ifdef H5
       qrcode = this.detail.wap_qr_code
      // #endif 
        this.qrcode1 = formatImg(qrcode)
        setTimeout(() => {
          this.$nextTick(() => {
            let widthTemp = document.querySelector(`#card`).clientWidth;
            let heightTemp = document.querySelector(`#card`).clientHeight;
              html2canvas(document.querySelector("#card"), {
              useCORS: true,
              logging: true,
              scrollY:0,
              scrollX:0,
              // width:widthTemp,
              height:heightTemp-2,
              backgroundColor:"none"
            }).then(canvas => {
              uni.hideLoading()
              console.log(canvas.toDataURL());
                  this.cardImg = canvas.toDataURL()
                  this.$refs.show_invite.show()
            });
        })
        }, 1000);
        
      // #endif
      // #ifdef MP
      drawCard.canvasImg(formatImg(this.detail.postor, 'w_8001')).then((imgInfo) => {
        // imgInfo：下一步绘制主图时的一些参数
        this.creatCard(imgInfo)
      }, (res) => {
        console.log(err)
        uni.showToast({
          title: "海报保存失败，请重试",
          icon: "none"
        })
      })
      // #endif
    },
    saveCard(){
      
        uni.showToast({
          title:"请长按保存",
          icon:"none"
        })
				// uni.saveFile({
				// 	filePath: this.cardImg,
        //   url: this.cardImg,
				// 	success: (result) => {
				// 		uni.showToast({
				// 		title: "保存成功，从相册中分享给好友吧",
				// 		icon: "none",
				// 		duration: 4000
				// 		})
				// 		},
				// 		fail: (err) => {
				// 		console.log(err)
				// 		uni.showToast({
				// 			title: "海报保存失败，请重试",
				// 			icon: "none"
				// 		})
				// 	}
				// })
			},
    async playAudio(src){
        this.innerAudioContext = uni.createInnerAudioContext();
        // this.innerAudioContext.autoplay = true;
        this.innerAudioContext.loop = true;
        this.innerAudioContext.src = src;
        // this.innerAudioContext.pause()
        this.innerAudioContext.onPlay(() => {
            console.log('开始播放');
            this.playing = true
        });
        this.innerAudioContext.onError((res) => {
            console.log("播放失败")
            console.log(res.errMsg);
            console.log(res.errCode);
        });
        // let i = 0
        // while (!this.playing&&i<30) {
        //     i++
            this.innerAudioContext.play()
        //     await this.delay(500)
        // }
    },
      playVideo(e){
          this.innerAudioContext&&this.innerAudioContext.pause()
          this.playing=false
      },
      switchAudio(){
          if(this.playing){
              this.innerAudioContext.pause()
          }else{
              this.innerAudioContext.play()
              if(this.videoContext){
                  this.videoContext.pause()
              }
          }
          this.playing = !this.playing
      },
      
  },
  onUnload(){
      clearInterval(this.tanmu1_timer)
      clearInterval(this.tanmu_timer)
      if (this.innerAudioContext){
          this.innerAudioContext.destroy()
      }
  }
}
</script>

<style lang="scss" scoped>
.top_img{
  width: 100%;
  &.opcity0{
    opacity: 0;
  }
  image {
    width: 100%;
    height:1rpx;
    display: block;
  }
}
.flex-row{
  display: flex;
  flex-direction: row;
  
}
.items-center{
  align-items: center;
}
.j-center {
  justify-content: center;
}
.cont {
  padding: 40rpx 0;
  background: #F23925;
  .border_con {
    position: relative;
    background-size: cover;
    background-repeat: no-repeat;
    padding:0 24rpx  40rpx;
    border-radius: 10rpx;
    border: 2rpx solid #FFAD61;
    .svg_border {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      right: 0;
      bottom: 0;
      right: 0;
    }
  }
  ::v-deep .con_nobg_img  img {
       max-width: 100%;
  }
  .con {
    margin: 0 32rpx 44rpx;
    padding: 12rpx;
    border-radius: 10rpx;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background: #FFEFE1;
    &.con_nobg_img{
      background:inherit;
      padding: 0;
    }
    .con_img{
      width: 100%;
      height:auto;
      img{
        max-width: 100%;
      }
      image {
        width: 100%;
      }
    }
    .con_title {
      width: 60%;
      margin: 0  auto;
      text-align: center;
      color: #F3EAC6;
      padding: 20rpx 0;
      text-shadow: 0px 0px 10px 0px #BB0001;
      font-size: 32rpx;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .time {
      color: #904F02;
      font-size:28rpx;
      margin: 28rpx;
      padding: 14rpx 0;
      border-radius: 10rpx;
      background: #FBE5CD;
      text-align: center;
      font-weight: bold;
      justify-content: center;

      .time_bg {
        border-radius: 4rpx;
        background: #FFAD61;
        color: #FFF4DE;
        font-size: 26rpx;
        text-align: center;
        margin: 0 8rpx;
        &.visit{
          background: none;
          color: #904F02;
        }

      }
    }
    
  }
}
.pic_list {
  padding: 0 32rpx;
  .pic{
    flex-wrap: wrap;
    padding: 24rpx 0;
    min-width: 20%;
    justify-content: center;
    .pic_item{
      padding:0 10rpx;
      box-sizing: border-box;
      .pic_img{
        width: 88rpx;
        height: 88rpx;
        margin: 0 auto 20rpx;
        border-radius: 10rpx;
        position: relative;
        .has_card_num {
          position: absolute;
          display: flex;
          justify-content: center;
          align-items: center;
          top: -20rpx;
          right: -20rpx;
          padding-bottom: 4rpx;
          width: 32rpx;
          height: 32rpx;
          color: #F23925;
          font-weight: 600;
          font-size: 24rpx;
          // background: #feec70;
          border-radius: 50%;
        }
        // background-repeat: no-repeat;
        // background-size: 100% 100%;
        image{
          width: 100%;
        }
      }
      .pic_oper {
       
        margin: 0 5%  20rpx;
        color: #FFFFFF;
        font-size: 20rpx;
        padding: 5rpx 0;
        text-align: center;
        border-radius: 16rpx;
        background: #B1B1B1;
        &.has{
          background: #B1B1B1;
        }
        &.more{
          background: linear-gradient(180deg, #FFA800 0%, #FF7A00 100%);

        }


      }
    }
  }
}
.step {
  margin: 0 32rpx;
  .step_l {
    position: relative;
    margin-right: 28rpx;
    height: 12rpx;
    border-radius: 10rpx;
    background: #FFFFFF;
    .act{
      height: 100%;
      border-radius: 10rpx;
      background: #FF8E00;
    }
  }
  .step_r {
    color: #904F02;
    font-size: 20rpx;
    text-align: center;

  }
}
.oper_btns{
  padding: 0 60rpx;
  margin-top: 40rpx;
  justify-content: space-between;
  .mr50{
    margin-right: 50rpx;
  }
  .oper_btn{
    color: #FFFFFF;
    font-size: 32rpx;
    border-radius:30rpx;
    background: #FF8E00;
    padding: 12rpx 0;
    text-align: center;
    font-weight: 600;

    &.chouka {
      border-radius: 15px;
      background: #F23925;
      color: #FEEE8E;
      font-size: 32rpx;
      text-align: center;

    }
  }
}
.group_list{
  padding: 24rpx 48rpx;
  margin-top: 22rpx;
  background: #fff;
  // padding: 24rpx;
    
    border-radius: 10rpx;
  .group_item{
    padding: 20rpx 0;
    .group_img{
      width: 90rpx;
      height: 90rpx;
      margin-right: 20rpx;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
    .group_con {
      padding: 6rpx 0;
      overflow: hidden;
      .group_title {
        color: #904F02;
        font-size: 28rpx;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

      }
      .group_sub_title {
        
        color: #904F02;
        font-size: 22rpx;
        margin-top: 8rpx;
      }
    }
    .join_group {
      border-radius: 10px;
      background: linear-gradient(107.35deg, #FF4F4C 0%, #FF120E 100%);
      color: #FFFFFF;
      font-size:28rpx;
      padding: 16rpx 24rpx;
      text-align: center;

    }
  }
}
.build_list {
  padding: 20rpx 48rpx;
  margin-top: 22rpx;
  background: #fff;
    
  border-radius: 10rpx;
  .build_item {
    padding: 14rpx 0;
    border-bottom: 2rpx solid #DCDCDC;
    .build_item_top {
      .build_img {
        width: 220rpx;
        height: 160rpx;
        min-width:220rpx;
        margin-right: 24rpx;
        overflow: hidden;
        image {
          width: 100%;
        }

      }
      .build_con {
        width:calc(100% - 248rpx)
      }
      .build_label{
        margin-top: 20rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        .build_label_item{
          border-radius: 4rpx;
          padding: 2rpx 6rpx;
          background: #FFEFE1;
          color: #904F02;
          font-size: 22rpx;
          ~.build_label_item {
            margin-left: 12rpx;
          }
        }
      }
      .build_title{
        color: #191C2F;
        font-weight: 600;
        font-size: 30rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

      }
      .build_info{
        margin-top: 8rpx;
        color: #191C2F;
        font-size: 22rpx;
        .area {
          margin-right: 24rpx;

        }

      }
      
    }
    .build_item_b {
      margin-top: 20rpx;
      // padding: 0 48rpx;
      .price {
        color: #FF3939;
        font-size: 28rpx;
      }
      .daohang {
        color: #904F02;
        font-size: 24rpx;
        margin-right: 24rpx;
        padding: 8rpx 20rpx;
        border-radius: 24rpx;
        background: #FFEFE1;
      }
      .zixun {
        border-radius: 24rpx;
        background: #F23925;
        padding: 8rpx 20rpx;
        color: #FFFFFF;
        font-size: 24rpx;


      }
    }
  }
}
.activ_info{
  padding: 24rpx 0;
  margin-top: 22rpx;
    
  border-radius: 10rpx;
}
.right_icon{
  position: fixed;
  right: 0;
  top: 160rpx;
  width: 50rpx;

  .right_icon_t{
    color: #FFE999;
    font-size: 24rpx;
    font-weight:500;
    padding: 16rpx 12rpx;
    white-space: normal;
    border-radius: 20rpx 0 0 20rpx;
    background: #E64C44;
    ~.right_icon_t{
      margin-top: 20rpx;
    }
  }
  
}

.qrcode-box {
  position: relative;
  margin-top: 15vh;
  transition: all 0.5;
  &.opcity0{
    opacity: 0;
  }
  &.mt0{
    margin-top: 0;
  }
  &.center {
    margin-top: 50vh;
    transform: translateY(-50%);
    
    .card {
     
      .qrcode-box_title{
        text-align: center;
        font-size: 48rpx;
        margin: 42rpx auto;
      }
      .qrcode-box_send_info{
        .send_name{
          color: #fff7d6;
          font-size: 32rpx;
        }
        .prelogo{
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          overflow: hidden;
          image {
            width: 100%;
          }
        }
      }
      .login_btn{
        background: inherit;
       margin-top: 68rpx;
        .card_kown {
          padding: 16rpx 76rpx;
          color: #F23925;
          font-size: 16px;
          text-align: center;

        }
      }
      
    }
    .jiangpin_img{
      position: absolute;
      top: -120rpx;
      z-index: 10;
      left:50%;
      transform: translateX(-50%);
      width: 240rpx;
      height: 240rpx;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
    .jiangpin_sub_title {
      font-size: 11px;
      text-align: center;
      color: #7F502C;
      margin-top: 24rpx;

    }
    .jiangpin_logo {
      width: 240rpx;
      height: 240rpx;
      overflow: hidden;
      margin: 24rpx auto 0 ;
      image {
        width: 100%;
      }
    }
    .jiangpin_btn {
      margin: 85rpx 40rpx 40rpx;
      font-size: 32rpx;
      padding: 24rpx 0;
      text-align: center;
      justify-content: center;
      color: #FFFFFF;
      background-image: linear-gradient(90deg, #FA7427 0%, #F24011 100%);
      box-shadow: 0 10rpx 20rpx -8rpx rgba(244,73,21,0.60), inset 0 0 6rpx 0 rgba(255,255,255,0.42);
      border-radius: 8rpx;

    }
    .jiangpin_title {
      margin-top: 140rpx;
      text-align: center;
      font-size: 32rpx;
      color: #7F502C;
    }
    // overflow: hidden;
    .title {
      font-size: 36rpx;
      font-weight: bold;
      text-align: center;
      color: #7f502c;
    }
    .tip {
      padding: 24rpx;
      padding-bottom: 48rpx;
      text-align: center;
      font-size: 28rpx;
      color: #7f502c;
    }
  }
  &.w_f {
    
    .img-box{
      // border-top-right-radius: 40rpx;
      // border-top-left-radius: 40rpx;
      // border-bottom-right-radius: 0;
      // border-bottom-left-radius: 0;
      // padding: 0;
      border-radius: 0;
      // background: linear-gradient(180deg, #F75D2B 0%, #F78C2C 100%);
      // padding-top: 60rpx;
      padding: 40rpx 40rpx 20rpx;
      width: 100vw;
      box-sizing: border-box;
    }
  }
  .qrcode-box_title {
    padding: 40rpx 52rpx;
    // width: 70%;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-align: center;
    color: #FFFFFF;
    font-size: 40rpx;
    
    &.center {
      text-align: center;
    }
    .qrcode-box_title_con {
        position: relative;
        display: inline-block;
    
        &:before {
          content: "";
          position: absolute;
          left: 0;
          right: 0;
          bottom: -10rpx;
          height: 4rpx;
          // width: 8rpx;
          background: #F86F2F;

        }
    }
  }
  .img-box {
    width: 584rpx;
    // padding: 12rpx;
    margin: auto;
    position: relative;
    background-color: #fff;
    border-radius: 20rpx;
    &.card {
      background: inherit;
    }
    &.group {
      .title{
        margin-top: 28rpx;
      }
    }
    &.prize_info{
      padding: 400rpx 0 40rpx;
      background: none;
    }
    &.bottom_bar {
      // position: fixed;
      // left: 0;
      // right: 0;
      // height: 100rpx;
      background: none;
    }
    .jiangpin_img{
      position: absolute;
      top: -120rpx;
      z-index: 10;
      left:50%;
      transform: translateX(-50%);
      width: 240rpx;
      height: 240rpx;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
    .jiangpin_sub_title {
      font-size: 11px;
      text-align: center;
      color: #7F502C;
      margin-top: 24rpx;

    }
    .jiangpin_logo {
      width: 240rpx;
      height: 240rpx;
      overflow: hidden;
      margin: 24rpx auto 0 ;
      image {
        width: 100%;
      }
    }
    .jiangpin_btn {
      margin: 85rpx 40rpx 40rpx;
      font-size: 32rpx;
      padding: 24rpx 0;
      text-align: center;
      justify-content: center;
      color: #FFFFFF;
      background-image: linear-gradient(90deg, #FA7427 0%, #F24011 100%);
      box-shadow: 0 10rpx 20rpx -8rpx rgba(244,73,21,0.60), inset 0 0 6rpx 0 rgba(255,255,255,0.42);
      border-radius: 8rpx;

    }
    .jiangpin_title {
      margin-top: 140rpx;
      text-align: center;
      font-size: 32rpx;
      color: #7F502C;
    }
    // overflow: hidden;
    .title {
      font-size: 36rpx;
      font-weight: bold;
      text-align: center;
      color: #7f502c;
    }
    .tip {
      padding: 24rpx;
      padding-bottom: 48rpx;
      text-align: center;
      font-size: 28rpx;
      color: #7f502c;
    }
  }
  
  .qrcode {
    width: 320rpx;
    margin: 0 auto;
    height: 320rpx;
    display: block;
  }
  .icon-box {
    position: absolute;
    bottom: -80rpx;
    width: 52rpx;
    height: 52rpx;
    left: 0;
    right: 0;
    margin: auto;
    image {
      width: 100%;
    }
  }
  .login_img {
    width: 100%;
    // height: 290rpx;
    // background: #7f502c;
    margin: 20rpx auto 0;
    image{
      width: 100%;
    }
  }

  .login_btn {
    border-radius: 0 0 20rpx 20rpx;
    background: #FCECD5;
    color: #904F02;
    padding: 20rpx 0;
    text-align: center;
    justify-content: center;
    font-size: 30rpx;
    // margin: 0 -10rpx;
    .w_login {
      margin-left: 15rpx;
    }
    .card_kown{
      display: inline-block;
    }
  }
  .rule_con {
    max-height: 630rpx;
    min-height: 630rpx;
    overflow-y: auto;
    padding: 0 48rpx 48rpx;
    color: #7f502c;
  }
  .card_con {
    width: 100%;
    .card_img {
      
      width: 320rpx;
      height: 416rpx;
      margin: 0 auto;
      image{
        width: 100%;

      }
    }

  }
}
.show_login.qrcode-box .img-box .tip{
    color: #000000;
  }
  .qrcode-box.show_login .login_btn,.img-box.group .login_btn {
    padding-bottom: 40rpx;
  }
.show_login.qrcode-box .img-box .login_btn {
  background: none;
  .w_login {
    padding: 20rpx 60rpx;
    border-radius: 36rpx;
    background: linear-gradient(180deg, #FF4F3B 0%, #FF7154 100%);
    color: #FFFFFF;
    font-size: 24rpx;

  }
}
.qrcode-box .img-box.group .login_btn  {
  background: none;
  .card_kown {
    padding: 20rpx 60rpx;
    border-radius: 18px;
    background: linear-gradient(180deg, #FF4F3B 0%, #FF7154 100%);
    color: #FFFFFF;
    font-size: 24rpx;

  }
}
.task1{
    background: #FFFFFF;
    border-radius: 48rpx;
    margin: 40rpx 30rpx;
    position: relative;
    padding:12rpx 20rpx;
    .task_item {
      padding: 28rpx 0;
      .task_item_icon {
        height: 64rpx;
        width: 64rpx;
        margin-right: 24rpx;
        image {
          width: 100%;
        }

      }
      .task_item_middle{
        overflow: hidden;
        .task_item_middle_title {
          color: #904F02;
          font-size: 32rpx;
          font-weight: 600;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          .number {
            margin-left: 20rpx;
            padding: 6rpx 12rpx;
            color: #FF0000;
            font-size: 24rpx;

          }
        }
        .task_item_middle_info{
          font-size: 22rpx;
          margin-top: 12rpx;
          color: #EB3C0E;
          white-space: normal;
        }
      }
      .task_item_oper{
        // .task_item_oper_c {
          font-size: 24rpx;
          color:#fff;
          margin-left: 10rpx;
          // color: #7F502C;
          padding: 14rpx 24rpx;
          border-radius: 30rpx;
          background: linear-gradient(180deg, #FFA800 0%, #FF7A00 100%);
        // }
        &.finished{
            color: #904F02;
            background: #F0F0F0;
            background: linear-gradient(90deg, #FCF6DB 0%, #F7D499 100%);
            border: 2rpx solid #F5D395;
        }
        
      }
      .task_item_b{
        margin-top: 24rpx;
        .task_item_b_prelogo {
          .task_item_b_prelogo_img {
            width: 64rpx;
            height: 64rpx;
            overflow: hidden;
            margin-right: 24rpx;
            .logo_item {
              width: 100%;
            }
          }
          
        }
      }
    }
  }
.zhuli_content {
  padding-top: 180rpx;
  width: 80vw;
  margin: 0 auto;
  text-align: center;
  .img-box {
    background: none;

    .zhuli_title {
      margin-top: 60rpx;
      font-weight: 600;
      color: #FB3333;
      font-size: 48rpx;

    }
    .zhuli_con {
      color: #A6A1A1;
      font-size: 32rpx;
    }
    .login_btn {
      margin-top: 100rpx;
      padding-bottom: 40rpx;
      background: inherit;
      .zhuli_kown {
        color: #FFFCFC;
        font-size: 32rpx;
        border-radius: 48rpx;
        padding: 20rpx 128rpx;
        background: linear-gradient(180deg, #FF4F3B 0%, #FF8F78 100%);

      }
    }
  }
}
.qrcode11 {
  width: 256rpx;
  height: 256rpx;
  margin: 60rpx auto 0;
  display: block;

}
 @keyframes dmAnimation {
    0% {
      transform: translateX(0);
    }
    100% {
      // transform: translateX(calc((-100vw + 100rpx)));
      transform: translateX(-100vw);
    }
  }
.tanmu {
  position: fixed;
  top: 110rpx;
  left: 30rpx;
  &.tanmu1{
    top: 200rpx;
  }
  .tanmu_c {
    display: flex;
    align-items: center;
    animation: 10s dmAnimation linear infinite normal;
    .tanmu_item {
      // position: absolute;
      // top:0;
      // left:20rpx;
      margin-right: 28rpx;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 32rpx;
      padding: 12rpx 16rpx;
      display: flex;
      align-items: center;
      white-space: nowrap;
      // opacity: 0;
      // transition: opacity 1s linear ;
      &.show{
        opacity: 1;
        transition: opacity 1s linear ;
      }
      .prelogo {
        width: 32rpx;
        height: 32rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 16rpx;
        image {
          width: 100%;
        }
      }
      .desc {
        color: #fff;
        font-size: 28rpx;
      }
    }
  }
}
.invite_type{
  .invite_type_item{
    text-align: center;
    flex: 1;
    .invite_type_image {
      width: 96rpx;
      height: 96rpx;
      margin: 0 auto;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
    .invite_type_name  {
      font-size: 28rpx;
      color: #fff;
      // color: #7F502C;
      margin-top: 24rpx;
    }
  }
}
/* #ifdef H5 */
// H5海报
#card {
  // padding-bottom: 15px;
  width: 100%;
  position: fixed;
  left: -110vw;
  background: none;
  .card_img-box {
    width: 100%;
    height: 100%;
    position: relative;
    .chat_card {
      position: absolute;
      z-index:10;
      right: 40rpx;
      bottom: 40rpx;
      width: 220rpx;
      height: 220rpx;

    }

    overflow: hidden;
  }

  .card_img-box image {
    width: 100%;
    height: 100%;
    display:block
  }

  .card-footer {
    margin: 40upx;
    font-size: 34px;
    line-height: 50upx;
    color: #333;

    .text {
      padding: 20upx;
    }

    .tip {
      font-size: 26upx;
      color: #666
    }

    .qrcode {
      width: 30vw;
      height: 30vw
    }
  }
}
.postor-box {
  .card-img {
    width: 80%;
    margin: 0 10%;
    padding: 40upx 0;
  }
}
/* #endif */

.right_share {
  position: fixed;
  top: 55vh;
  // margin-top: -50%;
  right: 40rpx;
  .right_share_t{
    width: 80rpx;
    height: 80rpx;
    &.share{
      margin-bottom: 40rpx;
    }
    image{
      width:100%;
    }
  }
}
.act_con ::v-deep{
  img{
    max-width: 100%;
  }
}
.rule_con ::v-deep{
  img{
    max-width: 100%;
  }
}
.audio-box{
    // position: fixed;
    // right: 20rpx;
    // top: 60rpx;
    margin-top: 20rpx;
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #e3bb73;
    z-index: 11;
    color: #fff;
    text-align: center;
    line-height: 70rpx;
    &.rotate{
        animation:rotate 3s linear infinite
    }
    .icon-yinyue{
        line-height: 70rpx;
    }
}
@keyframes rotate{
    0%{-webkit-transform:rotate(0deg);}
    25%{-webkit-transform:rotate(90deg);}
    50%{-webkit-transform:rotate(180deg);}
    75%{-webkit-transform:rotate(270deg);}
    100%{-webkit-transform:rotate(360deg);}
}
@-webkit-keyframes rotate{
    0%{-webkit-transform:rotate(0deg);}
    25%{-webkit-transform:rotate(90deg);}
    50%{-webkit-transform:rotate(180deg);}
    75%{-webkit-transform:rotate(270deg);}
    100%{-webkit-transform:rotate(360deg);}
}

// .swiper1{
//    height: 60vh;
//    position: relative;
//   .banner{
//     height: 60vh;
    
//     .sw_i {
//       display: inline-flex;
//     }
//     .sw_item{
//       width: 100%;
//       padding: 20rpx 50rpx;
//       background: #904F02;
//       display: inline-block;
      
//       .sw_img{
//         width: 400rpx;
//         margin: 0 auto;
//         height: 400rpx;
//         image{
//           width: 100%;
//         }
//       }
//       .ws_img_tip{
//           color: #f00;
//       }
//     }
//   }
// }

.sub_box{
  background-color: #fff;
  margin: 0 40rpx;
  // height: 500rpx;
  border-radius: 16rpx;
  position: relative;
//   overflow-y: hidden;
  margin-top: 32rpx;
  .sub_header{
      padding: 24rpx 48rpx;
      color: #fff;
      background-image: linear-gradient(-41deg, #F7918F 0%, #FB656A 100%);
      position: relative;
      border-top-left-radius: 16rpx;
      border-top-right-radius: 16rpx;
      .sub_title{
        margin-bottom: 16rpx;
        font-size: 40rpx;
        font-weight: bold;
    }
    .sub_tip{
        font-size: 24rpx;
    }
    .icon{
        width: 188rpx;
        height: 188rpx;
        position: absolute;
        top: -32rpx;
        right: 48rpx;
        image {
            width: 100%;
            height: 100%;
        }
    }
  }
  .form_box{
      padding: 30rpx 48rpx;
  }
  .sub_content{
    font-size: 32rpx;
    line-height: 1.5;
    color: #333;
  }
  .sub_form{
    margin-top: 25rpx;
    .sms_code_inp{
        align-items: center;
        margin-bottom: 20upx;
    }
    .sub_tel{
        margin-bottom: 20rpx;
    }
    .entrustSelect{
        height: 80upx;
        background: #f5f5f5;
        margin-bottom: 20upx;
        display: flex;
        padding: 0 20rpx;
        color: #888;
        align-items: center;
        justify-content: space-between;
    }
    input{
      padding: 20rpx;
      font-size: 28rpx;
      background-color: #f5f5f5;
    }
    .send-code{
      margin-left: 10rpx;
      color: $uni-color-primary;
      &.disable{
        color: #888;
      }
    }
    .btn-box{
      padding: 10px 0 0 0;
      button{
          font-size: 34rpx;
          font-weight: bold;
          height: 88rpx;    
          line-height: 88rpx;    
        background: #FB656A;
        box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
        border-radius: 44rpx;
      }
      .close_btn{
          padding: 24rpx;
          text-align: center;
          color: #999;
      }
    }
  }
  .verify_block{
    position: absolute;
    left: 0;
    right: 0;
    top: 150rpx;
    bottom: 40rpx;
    background-color: #fff;
    z-index: 2;
  }
}
</style>