<template>
	<view class="find_house-box">
		<view class="top_box1">
			<view class="top_img">
				<!-- #ifndef H5 -->
				<image mode="widthFix" :src="bgOption.image |imageFilter('w_400')"></image>
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<image  :src="bgOption.image |imageFilter('w_400')"></image>
				<!-- #endif -->
				<view class="bg_text">
					<view class="top_text">
						{{bgOption.first_text}}
					</view>
					<view class="top_sub_text">
						{{bgOption.second_text}}
					</view>
				</view>
			</view>
			<view class="top_con">
				<view class="top_con_item flex-row" @click="toAISearch">
					<view class="top_con_item_img">
							<!-- #ifndef H5 -->
						<image :src="middleOption.image" mode="widthFix"></image>
							<!-- #endif -->
							<!-- #ifdef H5 -->
						<image :src="middleOption.image"></image>
							<!-- #endif -->

					</view>
					<view class="top_con_item_con flex-1">
						<view class="top_con_item_con_title">
							<span class="top_con_item_con_title_t">{{middleOption.first_text}}</span>
						</view>
						<view class="top_con_item_con_tags">
							{{middleOption.second_text}}
						</view>
					</view>
					<view class="top_con_item_right flex-row">
						<view class="top_con_item_right_con" :class ="{'weidingyue':dingyueStatus==0 }" v-if ="showStatus">
							{{ isDingyue }}
						</view>
						<view class="top_con_item_right_icon">
							<my-icon type="ic_into" color="#A0A0A0"></my-icon>
						</view>
					</view>
				</view>
				<view class="top_con_item flex-row" @click="toHouseSearch">
					<view class="top_con_item_img">
							<!-- #ifndef H5 -->
						<image :src="bottomOption.image" mode="widthFix"></image>
							<!-- #endif -->
							<!-- #ifdef H5 -->
						<image :src="bottomOption.image" ></image>
							<!-- #endif -->
					</view>
					<view class="top_con_item_con flex-1">
						<view class="top_con_item_con_title">
							<span class="top_con_item_con_title_t">{{bottomOption.first_text}}</span>
						</view>
						<view class="top_con_item_con_tags">
							{{bottomOption.second_text}}
						</view>
					</view>
					<view class="top_con_item_right flex-row">
						<view class="top_con_item_right_icon">
							<my-icon type="ic_into" color="#A0A0A0"></my-icon>
						</view>
					</view>
				</view>
			</view>
		</view>
		<template  v-if ="showList">
			<view class="house_list"  v-if ='listsData.length>0'>
				<template v-for="(item,index) in listsData">
					<view
						v-if="item.parentid && item.parentid == 1 && !item.is_build"
						:key="index"
					>
						<house-item
							:item-data="item"
							type="ershou"
							:showLine="likeList.length==0||(index!=(listsData.length-1)&&likeList.length>0)"
							from="find_house"
							@click="toDetail(item.parentid,item.id)"
						></house-item>
					</view>
					<view
						v-if="item.parentid && item.parentid == 2 && !item.is_build"
						:key="index"
					>
						<house-item
							:item-data="item"
							type="renting"
							:showLine="likeList.length==0||(index!=(listsData.length-1)&&likeList.length>0)"
							from="find_house"
							@click="toDetail(item.parentid,item.id)"
						></house-item>
					</view>
					<view v-if="item.is_build" :key="index">
						<new-house-item
							:item-data="item"
							:showLine="likeList.length==0||(index!=(listsData.length-1)&&likeList.length>0)"
							type="new_house"
							from="find_house"
							@click="toDetail(3,item.id)"
						></new-house-item>
					</view>
				</template>
				<uni-load-more
					v-if ="likeList.length==0"
					:status="get_status"
					:content-text="content_text"
				></uni-load-more>
			</view>
			
		
			<!-- <view class="house_list" v-else >
				<view class="nodata flex-row" @click='toAISearch'>
					<view class="icon flex-row">
						<myIcon type= "tishifu" color="#666" fontSize="30"></myIcon>
					</view>
					<view class="text" v-if="isDingyue=='已订阅'">没有符合条件的房源，换个条件试试</view>
					<view class="text" v-if="isDingyue=='未订阅'">暂未订阅，请先订阅</view>
				</view>
			</view> -->

			<view class="house_list" v-if ="likeList.length>0&&showLike">
				<!-- 猜你喜欢 -->
				<view class="xihuan flex-row" :class ="{'no_match':listsData.length==0}">
					<view class="line flex-1">

					</view>
					<view class="xihuan_text">
						猜你喜欢
					</view>
					<view class="line flex-1"></view>
				</view>
				<template v-for="(item,index) in likeList" >
					<view
						v-if="item.parentid && item.parentid == 1 && !item.is_build"
						:key="index"
					>
						<house-item
							:item-data="item"
							type="ershou"
							from="find_house"
							@click="toDetail(item.parentid,item.id)"
						></house-item>
					</view>
					<view
						v-if="item.parentid && item.parentid == 2 && !item.is_build"
						:key="index"
					>
						<house-item
							:item-data="item"
							type="renting"
							from="find_house"
							@click="toDetail(item.parentid,item.id)"
						></house-item>
					</view>
					<view v-if="item.is_build" :key="index">
						<new-house-item
							:item-data="item"
							type="new_house"
							from="find_house"
							@click="toDetail(3,item.id)"
						></new-house-item>
					</view>
				</template>
			</view>
			<uni-load-more
					v-if ="showLike"
				:status="get_status1"
				:content-text="content_text"
			></uni-load-more>
		</template>
	</view>
</template>

<script>
import myIcon from "@/components/myIcon";
import houseItem from "../../components/houseItem";
import newHouseItem from "../../components/newHouseItem";
import { formatImg, config } from "../../common/index.js";
import { uniLoadMore, uniList, uniListItem } from "@dcloudio/uni-ui";
import myDialog from "../../components/dialog.vue";
import { checkAuth } from "../../common/index.js";
import addressd from "../../components/jm-address/jm-address";
	import {
		mapState,
		mapMutations
	} from 'vuex'
export default {
	components: {
		myIcon,
		uniLoadMore,
		uniList,
		myDialog,
		addressd,
		houseItem,
		newHouseItem,
		uniListItem,
	},
	data() {
		return {
			get_status: "loading",
			get_status1: "loading",
			content_text: {
				contentdown: "",
				contentrefresh: "正在加载...",
				contentnomore: "没有更多数据了",
			},
			nowTab: 0,
			rooms: [
				{ id: 0, name: "不限" },
				{ id: 1, name: "一室" },
				{ id: 2, name: "二室" },
				{ id: 3, name: "三室" },
				{ id: 4, name: "四室" },
				{ id: 5, name: "五室" },
			],
			params: {
				page: 1,
				rows: 20,
			},
			bgOption:{
				image:"",
				first_text:'',
				second_text:''
			},
			middleOption:{
				image:"",
				first_text:'',
				second_text:''
			},
			bottomOption:{
				image:"",
				first_text:'',
				second_text:''
			},
			listsData: [],
			likeList:[],
			show_dialog: false,
			isDingyue: "订阅",
			showStatus:false,
			dingyueType:1,
			dingyueStatus:0,
			share:{},
			showList:false,
			showLike:false
		};
	},
	onLoad(options) {
		for (let key in options) {
			this.params[key] = options[key];
		}
		this.getOptions()
		this.getData();
		uni.$on("getListAgain", () => {
			this.params.page = 1;
			this.getData();
		});
		uni.$on("getDataAgain", () => {
			this.params.page = 1;
			this.getData();
		});
	},
	onUnload() {
		uni.$off("getListAgain");
		uni.$off("getDataAgain");
	},
	computed: {
		...mapState(['allowOpen']),
		topBg() {
			return config.imgDomain + "/zhaofang/banner.png?x-oss-process=style/w_400"
		},
		zhinengSrc() {
			return config.imgDomain + "/zhaofang/zhinengzhaofangicon.png";
		},
		weituoSrc() {
			return config.imgDomain + "/zhaofang/weituozhaofangicon.png";
		},
		status_top() {
			return this.$store.state.systemInfo.statusBarHeight;
		},
	},
	filters: {
		imgUrl(val, param = "") {
			return formatImg(val, param);
		},
	},
	onHide() {
		this.setAllowOpen(true)
		this.$store.state.preventHandleShow = false
	},

	onShow() {
		setTimeout(() => {
			if (this.$store.state.updatePageData) {
				this.$store.state.updatePageData = false;
				this.getLocation();
			}
		}, 150);
			if(this.$store.state.preventHandleShow){
				return
			}
	},
	onPageScroll(e) {},
	methods: {
		...mapMutations(['setAllowOpen']),
		getData() {
			this.get_status="loading"
			this.params.lat = this.$store.state.position.lat;
			this.params.lng = this.$store.state.position.lng;
			this.$ajax.get(
				"House/subscribeList",
				this.params,
				(res) => {
					if (res.data.share){
						this.share =res.data.share
					}
					this.getWxConfig()
					if (res.data.code == 1 ) {
						if(this.params.page ==1){
							this.listsData  =res.data.data 
						}else {
								this.listsData = this.listsData.concat(res.data.data);
						}
						this.showList = true
						if (res.data.data.length < this.params.rows) {
							this.get_status = "noMore";
							this.showLike =true
							this.params.page =1
							this.getLikeList()
						} else {
							this.get_status = "more";
						}
						
					} else {
						this.showLike =true
						this.params.page =1
						this.getLikeList()
						this.get_status = "noMore";
					}
					this.getIsDingyue()
				},
				(err) => {
					console.log(err);
				}
			);
		},
		getLikeList() {
			this.get_status1="loading"
			this.$ajax.get(
				"House/subscribeList",
				this.params,
				(res) => {
					if (res.data.code == 1 ) {
						if(this.params.page ==1){
								this.likeList = res.data.list
						}else {
							
							this.likeList =this.likeList.concat(res.data.list) 
						}

						if (res.data.list.length < this.params.rows) {
							this.get_status1 = "noMore";
						} else {
							this.get_status1 = "more";
						}
					} else {
						this.get_status1 = "noMore";
					}
				},
				(err) => {
					console.log(err);
				}
			);
		},
		getOptions(){
			this.$ajax.get("House/wordImage", {}, (res) => {
				if (res.data.code ==1){
					let data =res.data.data
					this.middleOption ={
							image:data.middle_image,
							first_text:data.middle_first,
							second_text:data.middle_second
					}
					this.bottomOption ={
							image:data.bottom_image,
							first_text:data.bottom_title_first,
							second_text:data.bottom_title_second
					}
					this.bgOption ={
							image:data.top_image,
							first_text:data.top_title_first,
							second_text:data.top_title_second
					}
				}
			})
		},
		getIsDingyue() {
			this.$ajax.get("House/isSubscription", {}, (res) => {

				if (res.data.code == 1) {
					this.isDingyue = res.data.is_subscription == 1 ? "已订阅" : "订阅";
					this.dingyueStatus=res.data.is_subscription
					this.showStatus =true
					if (res.data.is_subscription == 1 &&res.data.subscription_builds&&res.data.subscription_builds ==1&&res.data.subscription_houses!=1){
						this.dingyueType=2
					}
				}else {
					// this.showStatus =true
					uni.showToast({
						title:res.data.msg,
						icon:"none"
					})
				}
			});
    },
    toDetail(type,id){
      if (type==1){
        this.$navigateTo("/pages/ershou/detail?id="+id)
      }
      if (type==2){
        this.$navigateTo("/pages/renting/detail?id="+id)
      } 
      if (type==3){
        this.$navigateTo("/pages/new_house/detail?id="+id)
      }
    },
    stopMove() {},
		toAISearch() {
			if (this.$store.state.user_login_status==1){
				this.$navigateTo("/user/login/login");
			}else if(this.$store.state.user_login_status==2) {
				this.$navigateTo("/user/bind_phone/bind_phone");
			}else {
				this.$navigateTo("/findHouse/ai_house_search?dingyueType="+this.dingyueType);
			}

			
		},
		toHouseSearch() {
			this.$navigateTo("/findHouse/find_house");
		},
	},
	onTabItemTap(e) {
		uni.$emit("onTabItemTap", e);
	},
	onReachBottom() {
		if (this.get_status == "more") {
			this.params.page = this.params.page + 1;
			this.getData();
		}
		if (this.get_status1 == "more") {
			this.params.page = this.params.page + 1;
			this.getLikeList();
		}
	},
};
</script>

<style scoped lang="scss">
page {
	background-color: #fff;
}
.flex-row {
	display: flex;
	flex-direction: row;
}
.top_box1 {
	height: 580rpx;
	width: 100vw;
	.top_img {
		width: 100%;
		height: 348rpx;
		position: relative;
		overflow: hidden;
		.bg_text{
			position: absolute;
			bottom: 120rpx;
			left: 0;
			right: 0;
			padding: 0 48rpx;
			.top_text{
				font-size: 34rpx;
				color: #fff;
				font-weight: 600;
				margin-bottom: 10rpx;
			}
			.top_sub_text{
				font-size: 24rpx;
				letter-spacing: 2rpx;
				color: #fff;
			}
		}
		// #ifdef H5
		image {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
		//#endif 
		// #ifndef H5
		image {
			width: 100%;
		}
		//#endif 
	}
	.top_con {
		position: relative;
		margin-top: -66rpx;
		&_item {
			margin: 0 32rpx 18rpx;
			background: #fff;
			border-radius: 10rpx;
			justify-content: space-between;
			box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
			border-radius: 5px;
			align-items: center;
			padding: 24rpx 32rpx;
			&_img {
				width: 56rpx;
				height: 56rpx;
				margin-right: 20rpx;
				overflow: hidden;
			// #ifdef H5
			image {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
			//#endif 
			// #ifndef H5
			image {
				width: 100%;
			}
			//#endif 
			}
			&_con {
				&_title {
					&_t {
						font-weight: 600;
						font-size: 28rpx;
						color: #3a3f42;
						margin-right: 4rpx;
					}
					&_info {
						font-size: 18rpx;
						color: #eb2626;
						padding: 2rpx 6rpx;
						margin-right: 4rpx;
						background: #ffc6c6;
						border-radius: 4rpx;
					}
					&_info1 {
						font-size: 18rpx;
						padding: 2rpx 6rpx;
						border-radius: 4rpx;
						color: #ff5d2a;
						background: #ffd3b4;
					}
				}
				&_tags {
					margin-top: 12rpx;
					color: #8c8c8c;
				}
			}
			&_right {
				&_con {
					color: #a4a4a4;
					font-size: 28rpx;
					&.weidingyue{
						padding: 5rpx 8rpx;
						border-radius: 4rpx;
						font-size: 22rpx;
						border: 2rpx solid #fb656a;
						color: #fb656a;
					}
				}
			}
		}
	}
}
/* #ifdef H5 */
.screen-tab {
	position: sticky;
	top: 0;
	// margin-top: 44px;
	box-sizing: border-box;
	padding: 0 48rpx;
}
.screen-panel {
	top: 0;
	margin-top: 80rpx;
	display: none;
}
.screen-panel.show {
	left: 0;
	display: block;
	line-height: 1;
}
/* #endif */
/* #ifndef H5 */
.screen-tab {
	top: var(--window-top);
	margin-top: 43px;
	box-sizing: border-box;
	padding: 0 48rpx;
}
.screen-panel {
	top: var(--window-top);
	margin-top: 162rpx;
}
/* #endif */
.house_list {
	// min-height: 100vh;
	padding: 0 48rpx;
	background-color: #fff;
	.nodata{
		padding: 20rpx 48rpx;
		margin-top: 20rpx;
		justify-content: center;
		align-items: center;
		.icon {
			align-items: center;
			justify-content: center;
			margin-right: 10rpx;
		}
		.text{
			font-size: 30rpx;
		}
	}
	.xihuan{
		justify-content: center;
		align-items: center;
		&.no_match{
			margin-top: 20rpx;
		}
		.xihuan_text{
			margin: 20rpx 40rpx;
			font-size: 30rpx;
			color: #9A9FAB;
		}
		.line {
			height: 2rpx;
			position: relative;
			&:after {
				content: "";
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				height: 2rpx;
				// -webkit-transform: scaleY(0.5);
				// transform: scaleY(0.5);
				background-color: #d8d8d8;
			}
		}
	}
}
.filter_list {
	padding-left: 48rpx;
	padding-top: 24rpx;
	background-color: #fff;
	.filter_item {
		display: inline-block;
		padding: 10rpx 20rpx;
		border-radius: 4rpx;
		line-height: 1;
		box-sizing: border-box;
		margin-right: 24rpx;
		font-size: 24rpx;
		background-color: #f5f5f5;
		border: 1rpx solid #f5f5f5;
		color: #999;
		&.active {
			background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
			color: $uni-color-primary;
			border: 1rpx solid $uni-color-primary;
		}
	}
}
</style>
