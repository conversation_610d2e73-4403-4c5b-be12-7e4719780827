<template>
  <view class="page">
      <!-- 搜索框 -->
      <titleBar custom>
        <view class="search-box flex-box">
          <view class="search-left">
            <my-icon type="sousuo" size="22"></my-icon>
          </view>
          <input
            type="text"
            confirm-type="search"
            placeholder-style="font-size:28rpx;color:#999;"
            placeholder="找到你的学校？"
            v-model="params.keywords"
            @confirm="onSearch"
          />
        </view>
        <template v-slot:right>
          <view class="shou" @click="active = 0">收起</view>
        </template>
      </titleBar>
      <view class="filter-tab">
        <view
          v-for="filter_type in filter_types"
          :key="filter_type.type"
          class="filter-tab-item text-center"
          :class="{ active: active === filter_type.type }"
          @click="switchTab(filter_type.type)"
        >
          <text>{{ filter_type.name }}</text>
          <my-icon
            :type="active === filter_type.type ? 'ic_down' : 'ic_down'"
            :color="active === filter_type.type ? '#f65354' : '#d8d8d8'"
            size="32rpx"
          ></my-icon>
        </view>
      </view>
    <view
      class="filter"
      :class="{
        show: active === 1
      }"
      id="filter1"
    >
      <view class="filter-list">
        <view
          class="filter-item"
          v-for="item in area_list"
          :key="item.id"
          @click="onClickArea(item)"
        >
          <my-tag :type="item.id === params.area_id ? '' : 'info'">{{
            item.name
          }}</my-tag>
        </view>
      </view>
    </view>
    <view
      class="filter"
      :class="{
        show: active === 2
      }"
      id="filter2"
    >
      <view class="filter-list">
        <view
          class="filter-item"
          v-for="item in cate_list"
          :key="item.id"
          @click="onClickCate(item)"
        >
          <my-tag :type="item.id === params.cate_id ? '' : 'info'">{{
            item.name
          }}</my-tag>
        </view>
      </view>
    </view>
    <view
      class="filter"
      :class="{
        show: active === 3
      }"
      id="filter3"
    >
      <view class="filter-list">
        <view
          class="filter-item"
          v-for="item in type_list"
          :key="item.id"
          @click="onClickType(item)"
        >
          <my-tag :type="item.id === params.type ? '' : 'info'">{{
            item.name
          }}</my-tag>
        </view>
      </view>
    </view>
    <view
      class="filter"
      :class="{
        show: active === 4
      }"
      id="filter4"
    >
      <view class="filter-list">
        <view
          class="filter-item"
          v-for="item in status_list"
          :key="item.id"
          @click="onClickStatus(item)"
        >
          <my-tag :type="item.id === params.school_status ? '' : 'info'">{{
            item.name
          }}</my-tag>
        </view>
      </view>
    </view>
    <view class="map-box">
      <!-- #ifndef H5 -->
      <map
        class="qqmap"
        id="map"
        :scale="scale"
        :longitude="mapcenter.longitude"
        :latitude="mapcenter.latitude"
        :markers="school_point"
        :polygons="polygons"
        @markertap="onClickMarker"
        @regionchange="onRegionchange"
      ></map>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <qqMap
        :mapkey="qqmapkey"
        :scale="scale"
        :longitude="mapcenter.longitude"
        :latitude="mapcenter.latitude"
        :markers="school_point"
        :polygons="polygons"
        :circles="circles"
        @markertap="onClickMarker"
        @regionchange="onRegionchange"
      ></qqMap>
      <view class="school-box" v-if="currentSchool.id">
        <list-item
          :title_row="1"
          :image="currentSchool.master_pic"
          :title="currentSchool.name"
          :address="currentSchool.address"
          :distance="currentSchool.distance"
          :tags="[
            { name: currentSchool.cname },
            { name: currentSchool.type_name }
          ]"
          @click="navigateTo(`/school/detail?id=${currentSchool.id}`)"
        >
          <template v-slot:prominent>
            <view class="school_tip">
              <text class="label">{{ currentSchool.school_status }}</text>
              <!-- <text class="tip">预计2020.05.01开始</text> -->
            </view>
          </template>
        </list-item>
      </view>
      <!-- #endif -->
      <view class="cate_box">
        <view
          v-for="cate in cate_list_simple"
          :key="cate.id"
          class="cate"
          :class="{ active: params.cate_id == cate.id }"
          @click="onClickCate(cate)"
          >{{ cate.name }}</view
        >
      </view>
    </view>
    <!-- #ifndef MP-WEIXIN -->
    <login-popup ref="login_popup" @onclose="handleCloseLogin" sub_content="当前服务需要会员登录后查看" :login_success_tip="false" @success="onLoginSuccess()"></login-popup>
    <!-- #endif -->
    <!-- 查询次数 邀请好友-->
    <myPopup :show="show_invit" position="top" @hide="show_invit = false">
      <invitePop invite @close="toHome" title="温馨提示" :tip="user_status_tip">
        <button class="up_vip" v-if="userLoginStatus===0" @click="toLogin()">去登录</button>
        <button class="up_vip" v-else @click="toUpVip()">去开通</button>
      </invitePop>
    </myPopup>
  </view>
</template>

<script>
import titleBar from "@/components/titleBar";
import mySearch from '@/components/mySearch'
import myTag from '@/components/myTag'
import myIcon from '@/components/icon'
// #ifdef H5
import qqMap from '@/components/qqMap'
import listItem from './components/listItem'
// #endif
// #ifndef MP-WEIXIN
import loginPopup from '../components/loginPopup'
// #endif
import myPopup from './components/myPopup'
import invitePop from './components/invitePop'
import { mapActions } from 'vuex'
import { isArray,navigateTo } from '../common/index'
import wxApi from '../common/mixin/wx_api';
export default {
  components: {
    titleBar,
    mySearch,
    myTag,
    myIcon,
    // #ifdef H5
    qqMap,
    listItem,
    // #endif
    // #ifndef MP-WEIXIN
    loginPopup,
    // #endif
    myPopup,
    invitePop
  },
  mixins:[wxApi],
  data() {
    return {
      active: 0,
      show_invit:false,
      user_status_tip:"当前服务需要开通VIP后查看",
      filter_types: [
        {
          name: '区域',
          type: 1
        },
        {
          name: '学段',
          type: 2
        },
        {
          name: '性质',
          type: 3
        },
        {
          name: '状态',
          type: 4
        }
      ],
      cate_list_simple: [
        {
          id: 4,
          name: '幼儿园'
        },
        {
          id: 2,
          name: '小学'
        },
        {
          id: 5,
          name: '初中'
        }
      ],
      area_list: [],
      cate_list: [],
      type_list: [],
      status_list: [],
      school_point: [],
      polygons: [],
      circles: [],
      currentSchool: {},
      scale:15,
      qqmapkey:__uniConfig.qqMapKey,
      mapcenter:{
        latitude:"",
        longitude:""
      },
      params: {
        rows: 20,
        area_id: 0,
        cate_id: 2,
        type: 0,
        school_status: 0,
      isMap: 1,
        lng: '',
        lat: '',
        keywords: ''
      },
      userLoginStatus:null,
       user_location: {
        lat: '',
        lng: ''
      }
    }
  },
  onLoad(options) {
    if (options.cate_id){
      this.params.cate_id =options.cate_id
    }
    this.init()
  },
  onShow(){
    if(this.isToUpVip){
      this.isToUpVip = false
      this.getUserStatus()
    }
  },
  // watch: {
  //   mapcenter(val, old_val) {
  //     if (
  //       val.latitude &&
  //       val.longitude &&
  //       (!old_val.latitude || !old_val.longitude)
  //     ) {
  //       this.params.lat = val.latitude
  //       this.params.lng = val.longitude
  //       this.getData()
  //     }
  //   }
  // },
  methods: {
    ...mapActions(['getLocation']),
    init() {
      this.getWxConfig(
        ['getLocation', 'updateAppMessageShareData', 'updateTimelineShareData'],
        wx => {
          this.getLocation({
            wx,
            success: res => {
              if (res) {
                this.params.lat = res.latitude
                this.params.lng = res.longitude
                this.mapcenter.latitude = res.latitude
                this.mapcenter.longitude = res.longitude
                this.user_location = {
                  lat: res.latitude,
                  lng: res.longitude
                }
              }
              this.getData()
            },
            fail: err => {
              console.log(err)
              this.getMapCenter((res)=>{
                let point = res.cfg_mappoint.split(",")
                this.params.lat = point[0]
                this.params.lng = point[1]
                this.mapcenter.latitude = point[0]
                this.mapcenter.longitude = point[1]
                this.user_location = {
                  lat: point[0],
                  lng: point[1]
                }
                this.getData()
              })
            }
          })
        })
      this.getFilter()
    },
    getMapCenter(callback){
      this.$ajax.get('index/mappoint',{},res=>{
        if(res.data.code == 1){
          callback(res.data)
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:none
          })
        }
      })
    },
    switchTab(index) {
      if (index === this.active || index === 0) {
        this.active = 0
      } else {
        this.active = index
      }
    },
    onSearch(){
      this.active = 0
      this.currentSchool = {}
      this.getData()
    },
    getData() {
      if (!this.mapcenter.latitude || !this.mapcenter.longitude) {
        return
      }
      uni.showLoading({
        title:'正在获取学校...'
      })
      this.$ajax.get('school/schoolList.html', this.params, res => {
        if (res.data.code === 1) {
          const pointIcons = {
            you: 'https://images.tengfangyun.com/images/icon/you.png',
            xiao: 'https://images.tengfangyun.com/images/icon/xiao.png',
            chu: 'https://images.tengfangyun.com/images/icon/chu.png',
            default: 'https://images.tengfangyun.com/images/icon/xiaoxuequ.png'
          }
          if(res.data.scale){
            this.scale = res.data.scale
          }
          this.school_list = res.data.list
          // 生成地图标记点
          this.school_point = res.data.list.map((school, index) => {
            let pointIcon
            switch (school.cid) {
              case 4: // 幼儿园
                pointIcon = pointIcons.you
                break
              case 2: // 小学
                pointIcon = pointIcons.xiao
                break
              case 5: // 初中
                pointIcon = pointIcons.chu
                break
              default:
                pointIcon = pointIcons.default
            }
            return {
              id: index + 1,
              school_id: school.id,
              iconPath: pointIcon,
              latitude: school.lat,
              longitude: school.lng,
              polygon: school.teach_range,
              width: 40,
              height: 40
            }
          })
          if (!this.currentSchool.id) {
            this.currentSchool = this.school_list[0]
            this.allowRegionchangeGetData = false
            setTimeout(()=>{
              this.allowRegionchangeGetData = true
            },500)
            this.mapcenter = {
              latitude:this.currentSchool.lat,
              longitude:this.currentSchool.lng
            }
            const polygonData = this.school_point[0].polygon
            this.setPolygons(polygonData)
          }
        } else {
          this.school_point = []
          this.polygons = []
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
        if (!this.share) {
          this.share = res.data.share || {}
          this.initShare()
        }
        uni.hideLoading()
        setTimeout(()=>{
          this.allowRegionchangeGetData = true
        },500)
      },err=>{
        uni.hideLoading()
        console.log(err)
        setTimeout(()=>{
          this.allowRegionchangeGetData = true
        },500)
      })
    },
    getFilter() {
      this.$ajax.get('school/schoolCondition.html', {}, res => {
        if (res.data.code === 1) {
          this.area_list = [{ id: 0, name: '不限' }, ...res.data.data.areaList]
          // 高亮选中当前已选中的区域
          if (this.params.area_id) {
            this.filter_types[0].name = this.area_list.filter(item => {
              return item.id === this.params.area_id
            })[0].name
          }

          this.cate_list = [
            { id: 0, name: '不限' },
            ...res.data.data.schoolCateList
          ]
          // 高亮选中当前已选中的学校分类
          if (this.params.cate_id) {
            console.log(12312);
            this.filter_types[1].name = this.cate_list.filter(item => {
              return item.id == this.params.cate_id
            })[0].name
            console.log(this.filter_types[1].name );
          }

          this.type_list = [{ id: 0, name: '不限' }, ...res.data.data.types]
          // 高亮选中当前已选中的学校性质
          if (this.params.type) {
            this.filter_types[2].name = this.type_list.filter(item => {
              return item.id === this.params.type
            })[0].name
          }

          this.status_list = [
            { id: 0, name: '不限' },
            ...res.data.data.schoolStatus
          ]
          // 高亮选中当前已选中的学校性质
          if (this.params.school_status) {
            this.filter_types[3].name = this.status_list.filter(item => {
              return item.id === this.params.school_status
            })[0].name
          }
        }
        if(!this.getOnce){
          setTimeout(()=>{
            this.getUserStatus()
            this.getOnce = true
          },500)
        }
      })
    },
    navigateTo(url){
      navigateTo(url)
    },
    onClickArea(area) {
      this.params.area_id = area.id
      this.allowRegionchangeGetData = false
      setTimeout(() => {
        this.allowRegionchangeGetData = true
      }, 500)
      if (area.lat && area.lng) {
        this.params.lat = area.lat
        this.params.lng = area.lng
        this.mapcenter.latitude = area.lat
        this.mapcenter.longitude = area.lng
      } else if (this.user_location.lat && this.user_location.lng) {
        this.params.lat = this.user_location.lat
        this.params.lng = this.user_location.lng
        this.mapcenter.latitude = this.user_location.lat
        this.mapcenter.longitude = this.user_location.lng
      }
      this.filter_types[0].name = area.name
      this.active = 0
      this.currentSchool = {}
      this.getData()
    },
    onClickCate(cate) {
      this.params.cate_id = cate.id
      this.filter_types[1].name = cate.name
      this.active = 0
      this.currentSchool = {}
      this.getData()
    },
    onClickType(type) {
      this.params.type = type.id
      this.filter_types[2].name = type.name
      this.active = 0
      this.currentSchool = {}
      this.getData()
    },
    onClickStatus(status) {
      this.params.school_status = status.id
      this.filter_types[3].name = status.name
      this.active = 0
      this.currentSchool = {}
      this.getData()
    },
    onClickMarker(e) {
      this.currentSchool = this.school_list[e.detail.markerId - 1]
      const currentSchool = this.school_point[e.detail.markerId - 1]
      const polygonData = currentSchool.polygon
      this.setPolygons(polygonData)
    },
    setPolygons(polygonData) {
      if (!isArray(polygonData)) {
        this.polygons = []
        return
      }
      const color = '#f65354'
      this.polygons = polygonData.map(items => {
        const points = items.paths.map(item => {
          return {
            latitude: parseFloat(item.latitude),
            longitude: parseFloat(item.longitude)
          }
        })
        return {
          id: items.id,
          points,
          strokeWidth: 1,
          strokeColor: color,
          fillColor: `${color}33`
        }
      })
    },
    onRegionchange(e) {
      if(!this.allowRegionchangeGetData){
        return
      }
      const handle = function() {
        this.params.lat = e.lat
        this.params.lng = e.lng
        this.getData()
      }
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        handle.call(this)
      }, 300)
    },
    // 获取用户账号状态
    getUserStatus(){
      this.$ajax.get('school/checkUserInfo',{},res=>{
        this.userLoginStatus = res.data.loginStatus
        if(this.userLoginStatus===0){
          this.$refs.login_popup.closeSub()
          this.user_status_tip = "当前服务需要会员登录后查看"
          this.show_invit = true
        }
        if(this.userLoginStatus === 1){
          this.$refs.login_popup.closeSub()
          this.user_status_tip = "您的账号已被封禁，请开通个人VIP后查看"
          this.show_invit = true
        }
        if(this.userLoginStatus === 2){
          this.$refs.login_popup.closeSub()
          this.user_status_tip = "当前服务需要开通个人VIP后查看"
          this.show_invit = true
        }
        if(this.userLoginStatus===3){
          this.$refs.login_popup.closeSub()
          this.show_invit = false
        }
      })
    },
    // 登录成功的回调事件
    onLoginSuccess(){
      // 重新获取用户账号状态
      this.getUserStatus()
    },
    // 关闭登录窗口的事件
    handleCloseLogin(){
      // 如果是登录状态怎不做任何处理
      // if(this.userLoginStatus !== 0){
      //   return
      // }
      // let pages = getCurrentPages() 
      // if(pages.length>1){
      //   uni.navigateBack()
      // }else{
      //   uni.switchTab({
      //     url:'/'
      //   })
      // }
    },
    toUpVip(){
      this.isToUpVip = true
      navigateTo('/user/member_upgrade?is_personal=1')
    },
    toLogin(){
      uni.removeStorageSync('token')
      navigateTo('/user/login/login')
    }
  }
}
</script>

<style scoped lang="scss">
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.fixed-top {
  width: 100%;
  position: fixed;
  top: var(--window-top);
  z-index: 100;
}
.search-box {
  margin-left: 20rpx;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #eeeeee;
  border-radius: 8rpx;
  flex-direction: row;
  .search-left {
    margin-right: 20rpx;
  }
}

.shou {
  padding: 20rpx 24rpx;
  color: #666;
}

.filter-tab {
  flex-direction: row;
  width: 100%;
  height: 80rpx;
  background-color: #fff;
  position: fixed;
  top: 44px;
  z-index: 99;
  .filter-tab-item {
    flex: 1;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    &.active{
      color: #f65354;
    }
    text {
      margin-right: 10rpx;
      transition: 0.3s;
    }
  }
}

.filter {
  padding: 20rpx 10rpx;
  width: 100%;
  background-color: #fff;
  position: fixed;
  top: 160rpx;
  transform: translateY(-101%);
  transition: 0.3s;
  z-index: 8;
  &.show {
    transform: translateY(0);
  }
  .filter-list {
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  .filter-item {
    margin: 15rpx;
  }
}

.map-box {
  padding-top: 160rpx;
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  transition: 0.3s;
  .qqmap {
    width: 100%;
    height: 100%;
  }
  .cate_box {
    width: 64rpx;
    position: fixed;
    right: 30rpx;
    bottom: 320rpx;
    border-radius: 32rpx;
    overflow: hidden;
    box-shadow: 0 5rpx 8rpx #ccc;
    z-index: 8;
    background-color: #fff;
    .cate {
      width: 58rpx;
      padding: 10rpx 15rpx;
      margin: 3rpx;
      line-height: 1.1;
      font-size: 28rpx;
      border-radius: 29rpx;
      align-items: center;
      justify-content: space-around;
      color: #666;
      &.active {
        color: #fff;
        background: linear-gradient(to bottom, #ff6834, #ff8e09);
      }
    }
  }
}

.school-box {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: 8;
  background-color: #fff;
  .school_tip {
    flex-direction: row;
    align-items: flex-end;
    margin-bottom: 20rpx;
    font-size: 24rpx;
    .label {
      color: $uni-color-primary;
    }
    .tip {
      margin-left: 10rpx;
    }
  }
}

.up_vip{
  width: 508rpx;
  height: 72rpx;
  line-height: 72rpx;
  background: #ff6735;
  color: #fff;
  border-radius: 50rpx;
  margin: 60rpx auto;
  font-size: 32rpx;
  background-image: linear-gradient(to left, #fb8a65, #ff6735);
}
</style>
