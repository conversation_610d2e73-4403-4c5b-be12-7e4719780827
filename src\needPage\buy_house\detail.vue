W<template>
	<view class="content">
		<view class="info">
			<view class="title-row">
				<text class="title">{{detail.title}}</text>
			</view>
			<view class="info_detail flex-row bottom-line">
				<image class="header_img" :src="agent.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
				<view class="center flex-1">
					<view class="user_name">{{agent.cname}}</view>
					<view class="detail_bottom flex-row">
						<text class="time">{{detail.begintime}}</text>
						<text class="area" v-if="detail.areaname">{{detail.areaname}}</text>
						<text>{{detail.catname}}</text>
					</view>
				</view>
				<view class="info_price">{{detail.qgprice?detail.qgprice+'万元':'面议'}}</view>
			</view>
		</view>
		<view class="map_box" v-if="detail.lat&&detail.lng">
			<view class="label">目标区域</view>
			<map class="map" :scale="14" :longitude="detail.lng" :latitude="detail.lat" :markers="[{id:1,latitude:detail.lat,longitude:detail.lng}]"></map>
		</view>
		<view class="desc_box">
			<view class="label">求购描述</view>
			<view class="desc">
				<text>{{detail.content}}</text>
        <view class="tip">联系我时，请说在{{site_name}}看到的信息，谢谢</view>
			</view>
			<view class="xiajia_icon" v-if="detail.is_show === 0">
					<my-icon type='yixiajia' size="180rpx" color='#e94e50'></my-icon>
				</view>
		</view>
		<!-- 免责声明 -->
		<view class="shengming">
			<view class="shengming_title flex-row">
				<text>免责声明</text>
				<text class="label" @click="toJubao">举报</text>
			</view>
			<view class="shengming_content" v-html="disclaimer"></view>
		</view>
		<!-- 底部操作菜单 -->
		<view class="bottom-bar flex-row top-line">
			<view class="bar-left flex-row flex-2">
				<view class="icon-btn" @click="handleCollect()">
					<my-icon v-if="is_collect" type="ic_shoucang_red" size="50rpx" color="#fb656a"></my-icon>
					<my-icon v-else type="ic_shoucang" size="50rpx"></my-icon>
					<text>收藏</text>
				</view>
				<!-- #ifndef H5 -->
				<button open-type="share" class="icon-btn">
					<my-icon type="ic_fenxiang" size="50rpx"></my-icon>
					<text>分享</text>
				</button>
				<!-- #endif -->
			</view>
			<view class="bar-right flex-row flex-3">
				<view class="bar-btn btn1 flex-1" v-if="is_open_im" @click="showWechat()">在线咨询</view>
				<view class="bar-btn btn2 flex-1" @click="handleTel()">电话咨询</view>
			</view>
		</view>
		<chat-tip></chat-tip>
		<tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
	</view>
</template>

<script>
	import {showModal, getSceneParams, config} from '../../common/index.js'
	import {wxShare} from '../../common/mixin'
	import myIcon from '../../components/myIcon.vue'
	import getChatInfo from '../../common/get_chat_info'
	import encryptionTel from '../../common/encryption_tel.js'
	export default{
		components:{
			myIcon
		},
		data(){
			return{
				id:"",
				detail:{},
				agent:{},
				is_collect:0,
				disclaimer: '',
				tel_res: {},
				show_tel_pop:false
			}
		},
		mixins:[wxShare],
		onLoad(options) {
			// #ifdef MP
			if (options.scene) {
				const params = getSceneParams(decodeURIComponent(options.scene))
				if (params.id) {
					this.id = params.id
					this.getData(this.id)
				}
				return
			}
			// #endif
			if(JSON.stringify(this.$store.state.tempData)!="{}"){
				Object.assign(this.detail,this.$store.state.tempData)
				document.title = this.detail.title
				// uni.setNavigationBarTitle({
				// 	title: this.detail.title
				// })
			}else if(options.title){
				this.detail.title = decodeURIComponent(options.title)
				document.title = this.detail.title
				// uni.setNavigationBarTitle({
				// 	title: this.detail.title
				// })
			}
			if(options.id){
				this.id = options.id
				this.getData(options.id)
			}
		},
		onShow(){
			if(this.reload){
				this.reload = false
				this.$store.state.allowOpen = true
				this.getData(this.id)
			}
		},
		onUnload(){
			this.$store.state.tempData={}
		},
		computed:{
			is_open_im() {
					return this.$store.state.im.ischat
			},
			site_name() {
				return this.$store.state.siteName
			}
		},
		methods:{
			getData(id){
				this.$ajax.get('house/houseDetail.html',{id},(res)=>{
					if(res.data.seo){
						this.seo = res.data.seo
					}
					if(res.data.code==0){
						uni.showToast({
							title:res.data.msg,
							icon:'none'
						})
						return
					}
					this.parameter = res.data.parameter
					this.disclaimer = res.data.disclaimer
					this.detail = res.data.house
					this.agent = res.data.agent
					this.is_collect = res.data.is_collect
					this.share = {
							title:res.data.house.title,
							content:'意向价格：'+(res.data.house.fangjia?res.data.house.fangjia+'万元':'面议'),
							pic:res.data.house.img[0]||""
						}
					this.getWxConfig()
				})
			},
			toHome(){
				uni.switchTab({
					url:'/pages/index/index'
				})
			},
			toHome(){
				uni.switchTab({
					url:'/pages/index/index'
				})
			},
			handleCollect(){
				this.$store.state.allowOpen = true
				if(!this.is_collect){
					this.collect()
				}else{
					this.noCollect()
				}
			},
			collect(){
				this.$ajax.get('house/infoCollect.html',{id:this.id},res=>{
					if(res.data.code == 1){
						this.is_collect=1
						uni.showToast({
							title:res.data.msg,
							duration:2000
						})
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:"none",
							duration:2000
						})
					}
				})
			},
			noCollect(){
				this.$ajax.get('house/cancelCollect.html',{id:this.id},res=>{
					if(res.data.code == 1){
						this.is_collect=0
						uni.showToast({
							title:res.data.msg,
							duration:2000
						})
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:"none",
							duration:2000
						})
					}
				})
			},
			showWechat(){
        if (this.detail.shixiao === 1) {
          uni.showToast({
            title: "此信息已失效",
            icon: 'none'
          })
          return false
        }
				if(!uni.getStorageSync('token')){
					this.$navigateTo('/user/login/login')
					this.reload = true
					return
        }
        // let thumb
				// if(this.imgs.length>0){
				// 	thumb = this.imgs[0]
				// }else if(this.focus.length>0){
				// 	thumb = this.focus[0].url
				// }
        // this.$store.state.buildInfo = {
        //   id: this.id,
        //   title: this.detail.title,
        //   type: 'ershou',
        //   image: thumb
        // }
				getChatInfo(this.agent.id, 6)
			},
			handleTel(){
				// if(this.detail.shixiao===1){
				// 	uni.showToast({
				// 		title:"此信息已失效",
				// 		icon:'none'
				// 	})
				// 	return false
				// }
				this.tel_params = {
					id: this.detail.uid,
					mid: this.detail.uid,
					tel: this.detail.tel,
					type: 'agent',
					from: 15,
					source: '',
					info_id: this.detail.id,
					bid: '',
					intercept_login: true,
					// success: (res)=>{
					// 	this.tel_res = res.data
					// 	this.show_tel_pop = true
					// },
					// fail: (res)=>{
					// 	switch(res.data.code){
					// 		case -1:
					// 			this.reload = true
					// 			this.$navigateTo('/user/login/login')
					// 			break
					// 		case 2:
					// 			this.reload = true
					// 			// #ifdef H5 || APP-PLUS || MP-BAIDU
					// 			this.$navigateTo('/user/login/login')
					// 			// #endif
					// 			// #ifdef MP-WEIXIN
					// 			this.$navigateTo('/user/bind_phone/bind_phone')
					// 			// #endif
					// 			break
					// 		case -5:
					// 			showModal({
					// 				title: "安全验证，防恶意骚扰已开启",
					// 				content: "验证后可免费发布查看信息。",
					// 				confirm: () => {
					// 					if (res.data.is_agent){
					// 						this.$navigateTo('/user/member_upgrade')
					// 					}else{
					// 						this.$navigateTo('/user/member_upgrade?is_personal=1')
					// 					}
					// 				}
					// 			})
					// 			break
					// 		case -10:
					// 			console.log("账号被封禁")
					// 			uni.showToast({
					// 				title: res.data.msg,
					// 				icon: 'none'
					// 			})
					// 			break
					// 		default:
					// 			uni.showToast({
					// 				title: res.data.msg,
					// 				icon: 'none'
					// 			})
					// 	}
					// }
				}
				// encryptionTel(this.tel_params)
				this.checkTelUseMoney()
			},
			checkTelUseMoney(){
				uni.showLoading({
					title: '',
					mask: true
				})
				this.$ajax.get('information/checkcornbylooktel.html', {info_id: this.detail.id}, res=>{
					uni.hideLoading()
					var deduction_cron = parseInt(res.data.deduction_cron)
					if(res.data.code === 1){
						if(deduction_cron>0){
							showModal({
								title: "温馨提示",
								content: res.data.msg,
								confirm: () => {
									this.getTel()
								}
							})
						}
						if(deduction_cron===0){
							showModal({
								title: '友情提示',
								content: "接通后，提及在" + (this.$store.state.siteName || config.projectName) + "看到的信息，获得更好的服务！",
								confirm: () => {
									this.getTel()
								}
							})
						}
					}else{
						if(deduction_cron===-1){
							showModal({
								title: '温馨提示',
								content: res.data.msg,
								confirmText: '去充值',
								confirm: () => {
									this.$navigateTo('/user/recharge')
								}
							})
						}else{
							uni.showToast({
								title: res.data.msg,
								icon:'none'
							})
						}
					}
				})
			},
			getTel(){
				this.$ajax.get('information/getInfoTel.html', {info_id: this.detail.id}, res=>{
					switch(res.data.code){
						case 1:
							uni.makePhoneCall({
								phoneNumber: res.data.tel
							})
							this.statisticsTel()
							break;
						case -5:
							showModal({
								title: "安全验证，防恶意骚扰已开启",
								content: "验证后可免费发布查看信息。",
								confirm: () => {
									if (res.data.is_agent){
										this.$navigateTo('/user/member_upgrade')
									}else{
										this.$navigateTo('/user/member_upgrade?is_personal=1')
									}
								}
							})
							break
						case -10:
							console.log("账号被封禁")
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							break
						default:
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
					}
				})
			},
			statisticsTel(){
				this.$ajax.get("im/callUpStatistics", {
					id: this.tel_params.mid,
					type: this.tel_params.from,
					info_id: this.tel_params.info_id
				}, res => {
					console.log(res.data);
				}, err => {
					console.log(err)
				}, { disableAutoHandle: true })
			},
			retrieveTel(){
				encryptionTel(this.tel_params)
			},
			toJubao(){
				this.$navigateTo('/user/inform/inform?id='+this.id+'&type=1')
			}
		},
		onShareAppMessage(res) {
			return {
			  title: this.detail.title||""
			}
		}
	}
</script>

<style lang="scss">
view{
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}
.flex-row{
	flex-direction: row;
}
	.content{
		padding: 0 48rpx 120rpx 48rpx;
		background-color: #fff;
	}
	.info{
		background-color: #fff;
	}
	.title-row{
		padding: 24rpx 0;
		margin-bottom: 24rpx;
		.title{
			font-size: 36upx;
			line-height: 1.3;
			font-weight: bold;
			margin-right: $uni-spacing-row-base;
		}
	}
	.info_detail{
		align-items: flex-end;
		line-height: 1;
		padding: 24rpx 0;
		.header_img{
			width: 64rpx;
			height: 64rpx;
			margin-right: 16rpx;
			border-radius: 50%;
			background-color: #f5f5f5;
		}
		.center{
			font-size: 22rpx;
			.user_name{
				margin-bottom: 16rpx;
			}
			.detail_bottom{
				align-items: center;
				color: #999;
				>text{
					padding: 0 16rpx;
					&.time{
						padding-left: 0;
					}
					&.area{
						border-right: 1rpx solid #999;
					}
				}
			}
		}

		.info_price{
			font-size: 32rpx;
			font-weight: bold;
			color: $uni-color-primary;
		}
	}

	.map_box{
		margin-top: 48rpx;
		.label{
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 16rpx;
		}
		.map{
			height: 340rpx;
			width: 100%;
		}
	}

	.desc_box{
		margin-top: 48rpx;
		position: relative;
		.xiajia_icon{
			position: absolute;
			width: 180rpx;
			height: 180rpx;
			left: 0;
			right: 0;
			top: -32rpx;
			margin: auto;
		}
		.label{
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 16rpx;
		}
		.desc{
			line-height: 1.8;
			min-height: 160rpx;
		}
	}


// 底部操作菜单
	.bottom-bar {
		background-color: #fff;
		height: 110rpx;
		padding: 15rpx 48rpx;
		left: 0;
		z-index: 10;
		button{
			padding: 0;
		}
		.bar-left{
			justify-content: space-between;
			padding-right: 48rpx;
		}
		.icon-btn {
			width: 90rpx;
			align-items: center;
			padding-right: 38rpx;
			margin: 0;
			background-color: #fff;
			line-height: initial;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			// flex: 1;
			overflow: hidden;
			.header_img{
				width: 50rpx;
				height: 50rpx;
				border-radius: 50%;
			}
			text {
				line-height: 1;
				font-size: 22rpx;
				color: #333;
				display: inline-block;
				width: 100%;
				text-align: center;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
		.bar-btn {
			// width: 220rpx;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 30rpx;
			padding: 0;
			margin: 0;
			border-radius: 0;
			background-color: $uni-color-primary;
			color: #fff;
			&.btn1 {
				margin-right: 24rpx;
			}
			&.btn2 {
				background-color: #2fc6f3;
			}
		}
}




// 免责声明
.shengming {
  margin-top: 32rpx;
  color: #999;
  .shengming_title {
    font-size: 30rpx;
    margin-bottom: 16rpx;
    justify-content: space-between;
    align-items: center;
    .label{
      line-height: 1;
      padding: 4rpx 8rpx;
      font-size: 22rpx;
      border: 1rpx solid #d8d8d8;
    }
  }
  .shengming_content {
    font-size: 26rpx;
    line-height: 1.8;
  }
}
</style>
