<template>
  <view>
    <hongbao ref="hongbao" :money="money" @openHb="checkHb"></hongbao>
    <sub-form ref="sub_form" :defaultTel="user_tel" :disabledTel="!!user_tel" @onsubmit="handleSubForm" sub_submit="提交" sub_title="领取联系人" @close="sub_close"></sub-form>
  </view>
</template>

<script>
import getLocation from './get_location'
import hongbao from '@/components/hongbao'
import subForm from '@/components/subForm'
export default {
  components: { hongbao, subForm },
  data() {
    return {
      id: '',
      money: 0,
      hb_info: {},
      current_city: '',
      map_key: '',
      lat: '',
      lng: '',
      user_tel: '',
      resend_id: '',
      code: '',
    }
  },
  onLoad(options) {
    this.id = options.id
    if (options.resend_id) {
      this.resend_id = options.resend_id
    }
    if (options.code) {
      this.code = options.code
    }
    if (this.code) {
      this.login()
    } else {
      this.getData()
    }
    uni.$on('getDataAgain', () => {
      this.getData()
    })
  },
  onUnload() {
    uni.$off('getDataAgain', () => {})
  },
  methods: {
    getData() {
      this.$ajax.get('WxMoney/checkInviteHb', {id: this.id, resend_id: this.resend_id}, (res) => {
        if (res.data.code == 1) {
          this.money = res.data.money
          this.hb_info = res.data.hb_info
          this.user_tel = res.data.user_info.tel
          this.map_key = res.data.txmapwapkey
          this.$nextTick(()=> {
            this.$refs.hongbao.showPopup()
          })
          if (this.hb_info.limit_area) {
            this.getWxConfig(['getLocation','updateAppMessageShareData','updateTimelineShareData'], (wx)=>{
              this.wx = wx
              this.getCity()
            })
          } else {
            this.getWxConfig()
          }
        } else if (res.data.code === 101) {
          var redirect_uri = location.href
          var url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + res.data.appid + "&redirect_uri=" + redirect_uri + "&response_type=code"  + "&scope=snsapi_base" + "&state=STATE#wechat_redirect";
          // window.location.href = url;
          window.location.replace(url)
        }else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    login() {
      this.$ajax.get('WxMoney/authLogin', {code: this.code}, (res) => {
        if (res.data.code != -1) {
          this.getData()
        }
      })
    },
    openHb() {
      if (this.hb_info.limit_area && !this.current_city) {
        this.getCity({
          success: () => {
            this.getHb()
          }, fail: (err) => {
            console.log(err)
            this.getHb()
          }
        })
      } else {
        this.getHb()
      }
    },
    getHb() {
      let form = {
        id: this.id,
        resend_id: this.resend_id,
        lat: this.lat,
        lng: this.lng,
        area: this.current_city
      }
      this.$ajax.post('wxMoney/getInviteHb', form, (res) => {
        if (res.data.code == 1) {
          this.$navigateTo(`/redEnvelopes/money?money=${this.money}&not_back=1`)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    getCity(options={}) {
      this.$store.state.getPosition(this.wx, (res)=>{
        this.lat = res.lat
        this.lng = res.lng
        getLocation({
          latitude: res.lat,
          longitude: res.lng,
          map_key: this.map_key||'',
          success: cityRes=>{
            this.current_city = cityRes.city
            options.success && options.success(cityRes)
          },
          fail: err=>{
            options.fail && options.fail(err)
          }
        })
      })
    },
    checkHb() {
      if (this.hb_info.is_need_fill_tel == 1) {
        this.$refs.hongbao.hidenPopup()
        this.$refs.sub_form.showPopup()
      } else {
        this.getHb()
      }
    },
    handleSubForm(e) {
      let form = {
        id: this.id,
        resend_id: this.resend_id,
        lat: this.lat,
        lng: this.lng,
        area: this.current_city,
        realname: e.name,
        tel: e.tel,
      }
      this.$ajax.post('wxMoney/getInviteHb', form, (res) => {
        if (res.data.code == 1) {
          this.$navigateTo(`/redEnvelopes/money?money=${this.money}&not_back=1`)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    sub_close() {
      this.$refs.hongbao.showPopup()
    }
  }
}
</script>

<style lang="scss" scoped>

</style>