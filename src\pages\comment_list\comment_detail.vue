<template>
    <view class="content" :class="{'overhide':isComment}">
        <view class="lists-box" id="moments">
				<view class="adviser-box flex-row" @click="consuDetail(post.AdvId)">
					<image class="prelogo" :src="post.prelogo | imgUrl('w_80')"></image>
					<view class="adviser-info">
						<view class="name flex-row">
						<text class="text">{{post.AdvName||post.nickname}}</text>
						<!-- <image class="level_icon" :src="post.levelid | levelIcon"></image> -->
						</view>
						<view class="level" @click.prevent.stop='goBack(post.bid)'>{{post.name}}</view>
					</view>
					<view class="btn-list flex-row">
						<view class="btn chat" v-if="post.AdvId>0" @click.prevent.stop="ask(post.mid,post.AdvId)">在线咨询</view>
						<view class="btn tel" v-if="post.AdvId>0" @click.prevent.stop="handleTel(post.AdvId,post.bid)">拨打电话</view>
					</view>
					</view>
				<!-- <block v-if="post.reward_status>0">
					<view class="xs_money">
						<my-icon type="hongbao" color="#f65354"></my-icon>
						<text style="font-size:20upx;">￥</text><text>{{post.reward_money}}</text>
					</view>
					<view class="xs_status" :class="post.reward_status==1?'red':'gray'"></view>
					<text class="xs_status_text">{{post.reward_status==1?'悬赏':(post.reward_status==2?'结束':'结束')}}</text>
				</block> -->
			<!-- </view> -->
			<view class="moments__post">
				<view style="width: 100%;">
					<view class="paragraph">{{post.content}}</view>
					<!-- 相册 -->
					<view class="thumbnails">
						<view :class="post.img.length === 1?'my-gallery':'thumbnail'" v-for="(image, index_images) in post.img" :key="index_images">
							<image class="gallery_img" lazy-load :mode="post.img.length > 1?'aspectFill':'widthFix'" :src="image | imgUrl('w_320')" :data-src="image | imgUrl('w_320')" @tap="previewImage(post.img,index_images)"></image>
						</view>
						<view class="my-gallery-video" v-if="post.videos.length>0" @click.stop.prevent="previewVideo(post.videos[0])">
							<image class="gallery_img" lazy-load mode="widthFix" :src="post.videos[0] | imgUrl" :data-src="post.videos[0] | imgUrl"></image>
							<image class="video-icon" src="/static/icon/video.png" mode="widthFix"></image>
						</view>
					</view>
					<!-- 资料条 -->
					<view class="toolbar">
						<!-- <view class="looknum">{{post.click_count}}浏览，</view> -->
						<view class="timestamp" v-if="post.leader_name">{{post.updtime}}</view>
						<!-- <view class="tag_map" @click="goBack(post.bid)"> <my-icon type="biaozhu-blue" color="#2ac8ea" size="12" style="margin-right:10upx;"></my-icon>{{post.name}}</view> -->
						<view v-if="post.is_my&&post.isleader!=7" class="del" @click.stop.prevent="delPost(post.id)">删除</view>
						<view class="handle" v-if="post.isleader==7">
							<button type="primary" class="small" @click="handlePost">操作</button>
						</view>
						
						<view class="like" @click.stop.prevent="like(post.id)">
							<my-icon v-if="post.is_praise" type="zan" size="20" color="#f65354"></my-icon>
							<my-icon v-else type="zan1" size="20"></my-icon>
						</view>
						<view class="comment" @click.stop.prevent="comment()">
							<my-icon type="pinglun" size="20"></my-icon>
						</view>
					</view>
					
					<!-- 赞 -->
					<view class="post-footer" v-if="post.praise.length>0">
						<view class="footer_content like-list bottom-line">
							<my-icon type="zan" size="20" color="#f65354"></my-icon>
							<image class="nickimg" v-for="(user,index_praise) in post.praise" :key="index_praise" :src="user.prelogo | imgUrl('w_80')"></image>
							<!-- <text class="nickname" v-for="(user,index_praise) in post.praise" :key="index_praise">{{index_praise>0?'，':''}}{{user.nickname}}</text> -->
						</view>
					</view>
					<!-- 结束 post -->
				</view>
			</view>
		</view>
		<view class="jubao label bottom-line top-20 flex-box" @click="toJubao">
			<view class="left">【我要举报】</view>
			<view class="right">
				<my-icon type="jiaoxing" size="20" color="#179B16"></my-icon>
			</view>
		</view>
		<!-- 评论区 -->
		<view class="label bottom-line top-20 flex-box">
			<view class="left" style="line-height:52upx;">评论</view>
			<view class="right t-comment" @tap="comment()">
				<text>我要评论</text>
				<uni-icons type="arrowright"></uni-icons>
			</view>
		</view>
		<view class="comment-list-box">
			<comment-list :listData="commentDatas" :showMore="false" @clickReply="reply" @delComment="delComment" @adoption="adoption" :answer="post.is_adopt==1?true:false" :adoptId="post.adopt_id" :money="post.reward_money" :isleader="post.isleader"></comment-list>
			<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
		</view>
        <!-- <view class="foot" v-show="showInput">
			<chat-input @send-message="send_comment" @blur="blur" :focus="focus" :placeholder="input_placeholder"></chat-input>
		</view> -->
		<view class="foot">
			<view class="comment-box top-line" v-show="showInput">
				<view class="flex-1 textarea-box">
					<textarea  v-model="inputValue" fixed  @confirm="send_comment({content:inputValue})" :focus="focus" @focus="handleFocus()" @blur="handleBlur()" :show-confirm-bar="false" :placeholder="input_placeholder" :cursor-spacing="15" />
				</view>
				<view class="btn-box">
					<button class="small plain" @tap="send_comment({content:inputValue})">发表评论</button>
				</view>
			</view>
		</view>
		<login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip"></login-popup>
		<chat-tip></chat-tip>
		<tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
    </view>
</template>

<script>
	import {wxShare} from '../../common/mixin'
	import allTel from '../../common/all_tel.js'
    import {formatImg,navigateTo,isArray,showModal,isIos} from "../../common/index.js"
    import myIcon from "../../components/icon.vue"
	import {uniIcons,uniLoadMore} from "@dcloudio/uni-ui"
	import commentList from '../../components/commentList.vue'
	import chatInput from '../../components/im-chat/chatinput.vue'; //input框
	import getChatInfo from '../../common/get_chat_info'
	// #ifndef MP-WEIXIN
	import loginPopup from '../../components/loginPopup'
	// #endif
    export default {
        components:{
            myIcon,
			uniIcons,
			commentList,
			uniLoadMore,
			// #ifndef MP-WEIXIN
			loginPopup
			// #endif
		},
        data(){
            return{
                post:{
                    nickname:"",
					name:"",
                    content:"",
					click_count:"",
					img:[],
					videos:[],
					adopt_id:0,
					is_adopt:0,
					is_follow:0,
					praise:[],
					is_praise:0,
				},
				login_tip:'',
				showInput:"",
				page:1,
				get_status:"loading",
				content_text:{
					contentdown:"",
					contentrefresh:"正在加载...",
					contentnomore:"没有更多数据了"
				},
				isPostMsg:0,  //是否发送消息  默认否
                isActive:0,   //是否减少活跃度 默认否
				commentDatas:[],
				isComment:false,
                input_placeholder: '请输入评论', //占位内容
				focus: false, //是否自动聚焦输入框
				is_reply: false, //回复还是评论
				inputValue: "", //评论输入框内容
				scrollTop:0,
				showInput: false, //评论输入框
				shareType:'',
				sid:'',
				currentUserInfo:{},
				toLogin:true,
				tel_res: {},
				show_tel_pop: false,
            }
		},
		mixins:[wxShare],
        onLoad(options){
			// console.log(this.$store.state.tempData)
			if(JSON.stringify(this.$store.state.tempData)!='{}'){
				Object.assign(this.post,this.$store.state.tempData)
			}
			if (options.shareId){
				this.sid=options.shareId
				this.shareType=options.shareType
				this.share_time =options.f_time||''
			}
            if(options.id){
                this.id = options.id
                this.getData()
				this.getComment()
			}
        },
		onUnload(){
			this.$store.state.tempData = {}
		},
		filters:{
			imgUrl(img,param=""){
				return formatImg(img,param)
			}
		},
		computed: {
			imchat(){
				return this.$store.state.im.ischat
			},

		},
        methods:{
            getData(){
                this.$ajax.get('news/communityDetail.html',{id:this.id,sid:this.sid,
        			sharetype:this.shareType, forward_time:this.share_time||''},(res)=>{
					if(res.data.code == 1){
						Object.assign(this.post,res.data.detail)
						this.currentUserInfo=res.data.shareUser
						if (this.currentUserInfo.adviser_id>0){
							this.sid=this.currentUserInfo.adviser_id
							this.shareType=1
						}else if (this.currentUserInfo.agent_id>0){
							this.sid=this.currentUserInfo.agent_id
							this.shareType=2
						}
						if(res.data.share){
							this.share = res.data.share
						}else{
							this.share = {
								title:res.data.detail.title,
								content:res.data.detail.content,
								pic:res.data.detail.img[0]||''
							}
						}
						let time =parseInt(+new Date()/1000)
						this.share.link=window.location.origin+window.location.pathname+'?id=' + this.id + '&shareId=' +this.sid+'&shareType='+this.shareType+"&f_time="+time,
						this.getWxConfig()
						// this.post = res.data.detail
						if (this.sid){
							// 获取登陆状态
							this.$ajax.get('member/checkUserStatus', {}, res => {
								if (res.data.code === 1) {
								} else {
								if (this.toLogin==false) return 
								this.toLogin=false
								
								this.$store.state.user_login_status = res.data.status
								if(this.$store.state.user_login_status==1){
									uni.setStorageSync('backUrl', window.location.href)
									this.$navigateTo("/user/login/login")
								}
								}
							})
						}
						
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
                })
			},
			handleCloseLogin(){
				if(this.$store.state.user_login_status===1){
					uni.removeStorageSync('token')
					navigateTo('/user/login/login')
				}
				if(this.$store.state.user_login_status===2){
					navigateTo('/user/bind_phone/bind_phone')
				}
			},
			getComment(){
				if(this.page == 1){
					this.commentDatas = []
				}
				if(this.page<1){
					this.page == 1
				}
				this.get_status = "loading"
				this.$ajax.get('news/getReplyList.html',{id:this.id,page:this.page},res=>{
					if(res.data.code == 1){
						this.commentDatas = this.commentDatas.concat(res.data.list)
						if(res.data.list.length<10){
							this.get_status = "noMore"
						}else{
							this.get_status = "more"
						}
					}else{
						if(this.commentDatas.length==0){
							this.content_text.contentnomore="暂时还没有评论哦~~"
						}
						this.get_status = "noMore"
						this.page--
					}
				})
			},
            previewImage(imageList, image_index=0) {
				let urls
				// 判断是不是数组并进行处理
				if(!isArray(imageList)){
					urls = [formatImg(imageList,'w_800')]
				}else{
					urls = imageList.map((item)=>{
						return formatImg(item,'w_800')
					})
				}
				var current = urls[image_index];
				uni.previewImage({
					current: current,
					urls: urls
				});
            },
			previewVideo(url){
				navigateTo('/vr/preview_video?url='+url)
			},
			like(id) {
				this.$ajax.get('adviser/praise.html',{id},res=>{
					if(res.data.code == 1){
						if (this.post.is_praise === 0) {
							this.post.is_praise = 1;
						} else {
							this.post.is_praise = 0;
						}
						this.post.praise = res.data.praise
					}
					uni.showToast({
						title:res.data.msg,
						icon:'none'
					})
				})
			},
			comment() {
				this.showInput = true; //调起input框
				this.is_reply = false; //回复中
				if(isIos()){
					this.isComment = true
				}
				this.$nextTick(()=>{
					this.focus = true;
				})
            },
            reply(e) {
				this.is_reply = true; //回复中
				this.replyParentIndex = e.parentIndex
				this.replyIndex = e.index
				this.be_commentid = e.be_commentid
				this.showInput = true; //调起input框
				this.input_placeholder = '回复：' + e.be_reply;
				if(isIos()){
					this.isComment = true
				}
				this.$nextTick(()=>{
					this.focus = true;
				})
            },
			toJubao(){
				navigateTo('/user/inform/inform?id='+this.id+'&type=3')
			},
            handleBlur: function() {
				setTimeout(()=>{
					this.init_input();
					if(isIos){
						uni.pageScrollTo({
							scrollTop: this.scrollTop,
							duration: 0
						});
					}
				},30)
				this.focus=false
				this.isComment = false
			},
			handleFocus(){
				this.focus=true
				if(isIos()){
					this.isComment = true
				}
			},
			send_comment: function(message) {
				if(!this.inputValue){
					uni.showToast({
						title:"请输入评论内容",
						icon:"none"
					})
					return
				}
				if(this.is_reply){
					this.$ajax.post('adviser/commentReply.html',{id:this.be_commentid,content:message.content},res=>{
						if(res.data.code == 1){
							res.data.comment.is_my = 1;
							this.commentDatas[this.replyParentIndex].reply.unshift(res.data.comment)
							this.inputValue = ""
						}
						uni.showToast({
							title:res.data.msg,
							icon:res.data.code==1?'success':'none',
							mask:true
						})
					})
				}else{
					this.$ajax.post('adviser/comment.html',{id:this.id,content:message.content},res=>{
						if(res.data.code == 1){
							res.data.comment.is_my = 1;
							this.commentDatas.unshift(res.data.comment)
							this.inputValue = ""
							uni.$emit('addreply',res.data.comment)
						}
						uni.showToast({
							title:res.data.msg,
							icon:res.data.code==1?'success':'none',
							mask:true
						})
					})
				}
				this.init_input();
			},
			init_input() {
				this.showInput = false;
				this.focus = false;
				this.input_placeholder = '请输入评论';
				// this.is_reply = false;
			},
			showPostMsg(id){
				let actionArr = ["不发送消息通知","发送消息通知"]
				uni.showActionSheet({
					itemList: actionArr,
					success: res=> {
						this.isPostMsg=res.tapIndex
							// this.isPostMsg(res.tapIndex)
						this.del(id)
					}
				})
			},
			showActive(id){
				let actionArr = ["不扣除活跃度","扣除活跃度"]
				uni.showActionSheet({
					itemList: actionArr,
					success: res=> {
						this.isActive= res.tapIndex
						// this.isACtive(res.tapIndex)
						this.showPostMsg(id)
					}
				})
			},
			del(id){
				this.$ajax.get('adviser/deletedCommunityReply',{id,IsDec:this.isActive,IsMsg:this.isPostMsg,act :0},res=>{
					if(res.data.code == 1){
						this.$store.state.updatePageData = true
						setTimeout(()=>{
							uni.navigateBack()
						},1500)
					}
					uni.showToast({
						title:res.data.msg,
						icon:'none'
					})
				})
			},
			delPost(id){ //删除帖子
				showModal({
					title: '提示',
					content: "确定要删除吗?",
					confirm:()=>{
						// 扣除活跃度  是否发送消息通知
						if (this.post.isleader==7){
							this.showActive(id)
						}else{
							this.del(id)
						}
						
					}
				})
			},
			delComment(e){ //删除评论
				let {parentIndex,index} = e
				console.log(parentIndex,index)
				// return
				let id
				if(index==undefined){
					id = this.commentDatas[parentIndex].id
				}else{
					id = this.commentDatas[parentIndex].reply[index].id
				}
				this.$ajax.get('adviser/deletedComment',{id},res=>{
					if(res.data.code == 1){
						if(index==undefined){
							this.commentDatas.splice(parentIndex,1)
						}else{
							this.commentDatas[parentIndex].reply.splice(index,1)
						}
					}
					uni.showToast({
						title:res.data.msg,
						icon:res.data.code==1?'success':'none'
					})
				})
			},
			handleFlow(){
				this.$ajax.get('adviser/follow.html',{cid:this.post.cid},res=>{
					if(res.data.code == 1){
						uni.showToast({
							title:res.data.msg,
							icon:'none'
						})
						if(this.post.is_follow==1){
							this.post.is_follow = 0
						}else{
							this.post.is_follow = 1
						}
					}
				})
			},
			setPromote(id){ //设置是否推荐
				this.$ajax.get('news/isPromote.html',{id},res=>{
					if(res.data.code == 1){
						this.post.ispromote==0?this.post.ispromote=1:this.post.ispromote=0
					}
					uni.showToast({
						title:res.data.msg,
						icon:'none'
					})
				})
			},
			handleTel(ids,bid){
				this.tel_params = {
					type: 2,
					callee_id: ids,
					scene_type: 1,
					scene_id: bid,
					success: (res)=>{
						this.tel_res = res.data
						this.show_tel_pop = true
					},
					fail: (res)=>{
						if(res.data.code === -1){
							this.$store.state.user_login_status = 1
							uni.removeStorageSync('token')
							this.$navigateTo('/user/login/login')
						}
						if(res.data.code === 2){
							this.$store.state.user_login_status = 2
							this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
							this.$refs.login_popup.showPopup()
						}
					}
				}
				allTel(this.tel_params)
			},
			retrieveTel(){
				allTel(this.tel_params)
			},
			consuDetail(id) {
				if (id == 0) return 
					navigateTo('/pages/consultant/detail?id=' + id)
			
			},
			//聊天
            ask(id,ids) {  //uid advId
                if(this.imchat == 1){//开聊天  
						// getChatInfo(id, 3)
						 // #ifdef MP-WEIXIN
						getChatInfo(id, 3)
						// #endif
						// #ifndef MP-WEIXIN
						this.$ajax.get('/wap/member/checkUserStatus',{},res=>{
							if(res.data.code === 1){
								getChatInfo(id, 3)
							}else if(res.data.status == 1){
								uni.removeStorageSync('token')
								navigateTo('/user/login/login')
							}else if(res.data.status == 2){
								this.$store.state.user_login_status = res.data.status
								this.login_tip = '为方便您及时接收消息通知，请输入手机号码'
								this.$refs.login_popup.showPopup()
							}
						})
						// #endif
                }else if (this.imchat == 0) {   //不开聊天  
                    this.consuDetail(ids)
                }
			},
			setGoods(id){ //设置是否精选
				this.$ajax.get('news/isGood.html',{id},res=>{
					if(res.data.code == 1){
						console.log(this.post.isgood);
						
						this.post.isgood == 0 ? this.post.isgood = 1 : this.post.isgood = 0
						this.$forceUpdate()
					}
					uni.showToast({
						title:res.data.msg,
						icon:'none'
					})
				})
			},
			setTop(id){ //设置是否置顶
				this.$ajax.get('news/isTop.html',{id},res=>{
					if(res.data.code == 1){
						this.post.istop == 0 ? this.post.istop = 1 : this.post.istop = 0
						this.$forceUpdate()
					}
					uni.showToast({
						title:res.data.msg,
						icon:'none'
					})
				})
			},
			jinYan(id){
				console.log("uid:"+id,"执行禁言")
				this.$ajax.get('news/forbiddenWords.html',{uid:id},res=>{
					// if(res.data.code == 1){
					// 	this.post.istop==0?this.post.istop=1:this.post.istop=0
					// }
					uni.showToast({
						title:res.data.msg,
						icon:'none'
					})
				})
			},
			jinPing(id){
				console.log("news_id:"+id,"执行禁评")
				this.$ajax.get('news/forbiddenComment.html',{news_id:id},res=>{
					// if(res.data.code == 1){
					// 	this.post.istop==0?this.post.istop=1:this.post.istop=0
					// }
					uni.showToast({
						title:res.data.msg,
						icon:'none'
					})
				})
			},
			handlePost(){
				let actionArr = ['删除',this.post.ispromote?'取消推荐':'推荐至首页',this.post.isgood?'取消精华':'设为精华帖',this.post.istop?'取消置顶':'置顶该贴','设置禁言','禁止评论']
				uni.showActionSheet({
					itemList: actionArr,
					success: res=> {
						switch(res.tapIndex){
							case 0:
								this.delPost(this.post.id)
								break
							case 1:
								this.setPromote(this.post.id)
								break
							case 2:
								this.setGoods(this.post.id)
								break
							case 3:
								this.setTop(this.post.id)
								break
							case 4:
								this.jinYan(this.post.mid)
								break
							case 5:
								this.jinPing(this.post.id)
						}
					},
					fail: function (res) {
						console.log(res.errMsg);
					}
				});
			},
			adoption(e){ //采纳
				showModal({
					title: '提示',
					content: "确定要采纳此回答吗?",
					confirm:()=>{
						this.$ajax.get('news/adopt.html',{id:this.id,reply_id:e.id},res=>{
							if(res.data.code == 1){
								this.post.adopt_id = e.id
								this.post.is_adopt = 0
							}
							uni.showToast({
								title:res.data.msg,
								icon:'none'
							})
						})
					}
				})
			},
			goBack(id){
				// uni.navigateBack()
				navigateTo("/pages/new_house/detail?id="+id)
			}
		},
		onPageScroll(e){
			if(isIos&&!this.isComment){
				this.scrollTop = e.scrollTop
			}
		},
		onReachBottom() { //监听上拉触底事件
			this.page++
			this.getComment()
		},
		onShareAppMessage(){
			let time =parseInt(+new Date()/1000)
			if(this.share){
				return {
					title: this.share.title,
					path: '/pages/consultant/shareDetail?id=' + this.id + '&shareId=' +this.sid+'&shareType='+this.shareType+"&f_time="+time,
					// #ifdef MP-BAIDU
					content:this.share.content,
					// #endif
					imageUrl: this.share.pic? formatImg(this.share.pic,'w_6401'):""
				}
			}
		}
    }
</script>

<style lang="scss">
    @import url("../../static/css/community.css");
	#moments .moments__post.title-box{
		padding: 10upx 20upx;
		align-items: center;
	}
	#moments{
		padding: 10upx 48upx;
		align-items: center;
	}
	#moments .moments__post .post_right{
		flex: 3;
		width: auto;
		max-width: 65%;
	}
	#moments .moments__post .toolbar{
		height: 56rpx;
	}
	// .user-left{
	// 	flex: 3;
	// }
	.user-le{
		display: flex;
		justify-content: flex-start;
		align-items: center;
	}
	.flex-row{
		display: flex;
		flex-direction: row;
	}
	.adviser-box{
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      margin-bottom: 24rpx;
      .prelogo{
        margin-right: 24rpx;
        width: 54rpx;
        height: 54rpx;
        border-radius: 27rpx;
        background-color: #f5f5f5;
      }
      .adviser-info{
        flex: 1;
        overflow: hidden;
        line-height: 1;
        .name{
          align-items: center;
          font-size: 26rpx;
          margin-bottom: 16rpx;
          .text{
            margin-right: 16rpx;
			display: inline-block;
			width: 100%;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
          }
        }
        .level_icon{
          width: 28rpx;
          height: 28rpx;
        }
        .level{
          font-size: 24rpx;
          color: #999;
        }
      }
      .btn-list{
        line-height: 1;
        .btn{
          margin-left: 24rpx;
          padding: 16rpx 16rpx;
          min-width: 120rpx;
          text-align: center;
          color: $uni-color-primary;
          border: 1rpx solid $uni-color-primary;
          border-radius: 8rpx;
		  box-shadow: 0 2px 4px 0 rgba(251,101,106,0.10);
        }
      }
    }

	.moments__post  .concat {
        flex: 1;
        justify-content: space-between;
        align-items: center;
    }
    .moments__post .concat .tel,.moments__post .concat .zixun {
        background: #EAF3FD;
        padding: 10upx;
        border-radius: 50%;
    } 
	.user-le image{
		width: 38upx;
		height: 38upx;
		margin-left: 10upx;
	}
	.sub_name{
		margin-top: 6upx;
		font-size: $uni-font-size-sm;
		color: #999
	}
	.xs_money{
		margin-right: 60upx;
		color: #f65354
	}
	.xs_status{
		position: absolute;
		right: 0;
		height: 0;
		width: 0;
		border-left: 100upx solid transparent;
	}
	.xs_status.red{
		border-top: 100upx solid #f65354;
	}
	.xs_status.gray{
		border-top: 100upx solid #999;
	}
	.xs_status_text{
		position: absolute;
		right: 0;
		top: 24upx;
		font-size: 22upx;
		letter-spacing:8upx;
		transform:rotate(45deg);
		color: #ffffff;
	}
	.xiaoqu{
		padding: 24upx;
		align-items: center;
		.img-box{
			width: 160upx;
			height: 160upx;
			margin-right: 10upx;
			image{
				width: 100%;
				height: 100%;
			}
		}
		.title{
			margin-bottom: 10upx;
			font-weight: bold;
			font-size: $uni-font-size-blg;
		}
	}
	.handle{
		width: 180upx;
		display: flex;
		justify-content: center;
		position: absolute;
		right: 100upx;

	}
	.handle button{
		margin: 0;
		padding-left: 15upx;
		padding-right: 15upx;
		font-size: $uni-font-size-base;
		background-color: $uni-color-primary;
		line-height: 1.8
	}
	.button-hover[type=primary] {
		color:rgba(255, 255, 255, 0.6);
		background-color:$uni-color-primary;
	}

	.looknum{
		color:#757575;
		font-size:22rpx;
	}
	.tag{
		margin: 0 6upx;
		color:#999999;
		font-size:22rpx;
		line-height: 1;
		padding: 8upx;
		border: 1upx solid #999999;
		border-radius: 8upx;
	}
	.ding{
		font-size:$uni-font-size-sm;
		border-radius: 4upx;
		margin-left: 10upx;
		padding: 1upx 10upx;
		color: #f40;
		background-color: #ffda77
	}
	.comment-list-box{
		padding: 10upx 24upx;
		background-color: #fff;
	}
	.label{
		justify-content: space-between;
		padding: 20upx 48upx;
		font-size: $uni-font-size-base;
		background-color: #fff;
	}
	.label .right{
		font-size: $uni-font-size-sm;
		color: #666;
	}
	.label .t-comment{
		padding: 10upx;
	}
	.jubao.label{
		padding: 24upx 48upx
	}
	.comment{
		padding: 10upx 0;
	}
	.overhide{
		position: absolute;
		width: 100%;
		top: 0;
		bottom: 0;
		overflow: hidden;
	}
	.comment-box{
		position: fixed;
		display: flex;
		align-items: flex-end;
		width: 100%;
		bottom: 0;
		background-color: #fff;
		.btn-box{
			display: flex;
			justify-content: flex-end;
			margin-top: 0;
			.small{
				margin: initial
			}
		}
	}
	.textarea-box{
		padding: 24upx;
		textarea{
			width:100%;
			height:54upx;
			line-height: 1.5;
			background-color: #fff
		}
	}
	.concat{
		justify-items: flex-end;
	}
	.tag_map{
		margin: 0 3px;
		color: #999999;
		font-size: 12px;
		line-height: 1;
		padding: 4px;
	}
	.nickimg{
		width: 50upx;
		height: 50upx;
		border-radius: 50%;
		margin-left: -16upx;

	}
	.nickimg:nth-of-type(1){
		margin-left: 0upx;
	}
	#moments .moments__post .thumbnails{
		margin-bottom: 16upx;
	}
</style>