/** 
 * javascript comment 
 * @Author: 拓荒 QQ：1017771330 
 * @Date: 2019-04-16 16:55:32 
 * @Desc: 公共配置以及方法 
 */
// #ifndef APP-PLUS
import {version} from '../../package.json'
// #endif
import store from '../store/index'
import {config} from './config'
const now = Date.now || function() {
	return new Date().getTime();
};
const isArray = Array.isArray || function(obj) {
	return obj instanceof Array;
};
// 判断资源是不是远程资源
const isHttp = function(val){
	let httpArr = ['http','https']
	return httpArr.includes(val.split('://')[0])
}
// 判断是否是视频
const isVideo = function(url){
	let videoArr = ['mp4','avi']
	let varr = url.split('.')
	let name = varr[varr.length-1]
	return videoArr.includes(name)
}
// 判断是否是苹果手机
const isIos = function(){
	const u = navigator.userAgent;
	return u.indexOf("iPhone") > -1 || u.indexOf("Mac OS") > -1;
}

// 图片格式化
const formatImg = function (url,param="w_8601"){
	if (!url || typeof (url) !== 'string'){
		// #ifdef APP-PLUS
		return"/static/icon/none.png"
		// #endif
		// #ifndef APP-PLUS
		return require("../static/icon/none.png")
		// #endif
		
		// return"/m/static/icon/none.png"
	}
	if(isHttp(url)){
		let reg = new RegExp(/\?.+\=/)
		if (reg.test(url)) { //如果链接中有参数则直接返回不需要再加参数
			return url
		}
		if(isVideo(url)){
			return url+config.ossSnapshot
		}
		if(param){
			if (url.split('.')[url.split('.').length - 1] === 'gif'){
				return url + config.thumbParam + 'w_1200'
			}else{
				return url + config.thumbParam + param
			}
		}
		return url
	}else{
		if(isVideo(url)){
			return config.imgDomain+url+config.ossSnapshot
		}
		if(param){
			if (url.split('.')[url.split('.').length-1] === 'gif') {
				return config.imgDomain + url + config.thumbParam + 'w_1200'
			} else {
				return config.imgDomain + url + config.thumbParam + param
			}
		}
		return config.imgDomain+url
	}
}

// const timeFormater = function(val){
// 	if(!val){
// 		return ""
// 	}
// 	let time = new Date(val*1000)
// 	let message_year = time.getFullYear()
// 	let message_month = time.getMonth() + 1 > 9 ? time.getMonth() + 1 : '0' + (time.getMonth() + 1)
// 	let message_day = time.getDate()
// 	let message_hours = time.getHours()
// 	let message_minutes = time.getMinutes()
// 	let now_time = new Date()
// 	let now_year = now_time.getFullYear()
// 	let now_month = now_time.getMonth() + 1 > 9 ? now_time.getMonth() + 1 : '0' + (now_time.getMonth() + 1)
// 	let now_day = now_time.getDate()
// 	let time_difference = Date.parse(new Date()) / 1000 - val,
// 		time_str;
// 	switch (true) {
// 		case now_year - message_year == 0 && now_month - message_month == 0 && now_day - message_day == 0:
// 			time_str = (message_hours > 9 ? message_hours : '0' + message_hours) + ':' + (message_minutes > 9 ? message_minutes : '0' + message_minutes)
// 			break;
// 		case now_year - message_year == 0 && now_month - message_month == 0 && now_day - message_day == 1:
// 			time_str = '昨天 '+(message_hours > 9 ? message_hours : '0' + message_hours) + ':' + (message_minutes > 9 ? message_minutes : '0' + message_minutes)
// 			break;
// 		case now_year - message_year == 0 && now_month - message_month == 0 && now_day - message_day == 2:
// 			time_str = '前天 '+(message_hours > 9 ? message_hours : '0' + message_hours) + ':' + (message_minutes > 9 ? message_minutes : '0' + message_minutes)
// 			break;
// 		case time_difference <= 60:
// 			// time_str = "刚刚"
// 			time_str = (message_hours > 9 ? message_hours : '0' + message_hours) + ':' + (message_minutes > 9 ? message_minutes : '0' + message_minutes)
// 			break;
// 		case time_difference > 60 && time_difference <= 3600:
// 			// time_str = parseInt(time_difference / 60) + '分钟前'
// 			time_str = (message_hours > 9 ? message_hours : '0' + message_hours)+':'+(message_minutes > 9 ? message_minutes : '0' + message_minutes)
// 			break;
// 		case time_difference > 3600 && time_difference <= 86400:
// 			// time_str = parseInt(time_difference / 3600) + '小时前'
// 			time_str = (message_hours > 9 ? message_hours : '0' + message_hours) + ':' + (message_minutes > 9 ? message_minutes : '0' + message_minutes)
// 			break;
// 		// case time_difference > 86400 && time_difference <= 86400 * 7:
// 		// 	time_str = parseInt(time_difference / 86400) + '天前'
// 		// 	break;
// 		default:
// 			// let seconds = nowTime.getSeconds()
// 			time_str = `${message_year}/${message_month}/${message_day} ${message_hours}:${message_minutes}`
// 	}
// 	return time_str
// }

// 获取小程序码中的参数
const getSceneParams = function(scene){
	var sceneParams = {};
	var params = scene.split("&");
	for(var i = 0; i < params.length; i ++) {
		sceneParams[params[i].split("=")[0]]=unescape(params[i].split("=")[1]);
	}
	return sceneParams
}

// 判断用户状态
const checkUserStatus = function (callback, check_login = true, check_phone = true){
	if (check_login && !uni.getStorageSync('token')) {
		store.state.toLogin = true
		navigateTo('/user/login/login')
		return false
	}
	if (check_phone && !store.state.user_info.tel) {
		navigateTo('/user/bind_phone/bind_phone')
		return false
	}
	if(callback){
		callback()
	}
	return true
}
// 路由跳转封装
const navigateTo = function(e, animType = "pop-in") {
	if(!e){
		return
	}
	// 如果是超链接则打开webView页面
	if (isHttp(e)) {
		// #ifdef H5
		window.location.href = e
		// #endif
		// #ifndef H5
		navigateTo('/pages/web_view/web_view?url=' + encodeURIComponent(e))
		// #endif
		return
	}
	var domain_reg = /^[\w-]+(\.[\w-]+)+([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?$/
	if(domain_reg.test(e)){
		navigateTo('http://' + e)
	}
	let regPath = ['pages/ershou/detail', 'pages/renting/detail']
	console.log(e, 111);
	//获取路由参数

	if ((e.indexOf(regPath[0]) >= 0 || e.indexOf(regPath[1]) >= 0) && (e.indexOf("?id=undefined") >= 0 || e.indexOf("&id=undefined") >= 0)) {
		store.state.allowOpen = true
		setTimeout(() => {
			store.state.allowOpen = false
		}, 800);
		ajax.get("member/checkUser", {}, res => {
				console.log(res);
			})
		return 
	}
	uni.navigateTo({
		url: e,
		animationType: animType,
		animationDuration: 320,
	})
	// #ifdef H5
	setTimeout(()=>{
		const { aplus_queue, location } = window;
		var reg = /(my)|(user)\//
		if (aplus_queue && !reg.test(location.pathname)) {
			aplus_queue.push({
				action: 'aplus.sendPV',
				arguments: [{ is_auto: false }, {}]
			});
		}
	}, 200)
	// #endif
}

// 提示框
const showModal = function(obj){
	uni.showModal({
		title:obj.title||'提示',
		content:obj.content||'',
		showCancel:obj.showCancel||true,
		cancelText:obj.cancelText||'取消',
		cancelColor:obj.cancelColor||'#333333',
		confirmText:obj.confirmText||'确定',
		confirmColor:obj.confirmColor||config.modalBtnColor,
		success:res=>{
			 if (res.confirm) {
				obj.confirm()
			 }else{
				 if(obj.cancel){
					 obj.cancel()
				 }
				 console.log('用户点击取消')
			 }
		}
		
	})
}

// 防抖
function debounce(fun, delay) {
    return function (args) {
        let that = this
        let _args = args
        clearTimeout(fun.id)
        fun.id = setTimeout(function () {
            fun.call(that, _args)
        }, delay)
    }
}

// ajax请求封装
// code:-1 需要登录；2:需要绑定手机号；-3:信息失效；-4:金币不足,提示充值；-5:需要续费会员；-6:需要设置社区昵称；-7:社区金币不足,充值跳转；-8：家装会员到期；-9: 登录已过期或用户不存在；-10:用户被封禁; -11:聊天已存在; -12:添加房源，房源已经存在 -13:发布信息提示确认扣除金币

const ajax = {
	get: function(url, params, doSuccess, doFail ,options={}) {
		let initial_url = url
		if (config.anti_shake_api.list.some(item=>item===initial_url) && store.state.requesting_list.some(item=>item===initial_url)){
			console.log('当前接口请求中，拒绝请求')
			doFail && doFail({ msg: '当前接口请求中'})
			return
		}
		store.state.requesting_list.push(initial_url)
		let header = {
			"content-type": "application/json;charset=UTF-8",
			"Cache-Control":"no-cache"
		}
		// #ifndef APP-PLUS
		header.Tfyplatform = store.state.systemInfo.platform || ''
		header.CLIENTVERSION = version
		// #endif
		// if(uni.getSystemInfoSync().platform=="ios"){
		// 	params.
		// }
		if(uni.getStorageSync('token')){
			// params.token = uni.getStorageSync('token') //后台需要在请求参数上传递token
			header.Authorization = uni.getStorageSync('token')
		}
		/** 
		  * header.From 1：微信小程序，2：微信公众号，3：h5，4：PC，5：百度小程序，8：安卓 9 ios
		 */
		// #ifdef MP-WEIXIN
		header.From = 1
		// #endif
		// #ifdef H5
		var ua = navigator.userAgent.toLowerCase();
		if (ua.match(/MicroMessenger/i) == "micromessenger") { //如果是微信
			header.From = 2
		}else{
			header.From = 3
		}
		header.From = 2
		// #endif
		// #ifdef MP-BAIDU
		header.From = 5
		// #endif
 
		// #ifdef APP-PLUS
		 if (uni.getSystemInfoSync().platform =="ios"){
			 header.Clientfrom = 9
			 header.APPVERSION = plus.runtime.version
			 header.CLIENTVERSION = plus.runtime.version
		 }else {
			 header.Clientfrom = 8
			 header.CLIENTVERSION = plus.runtime.version
		 }
		
		// #endif
		if(url[0]==='/'){
			url = config.apiDomain + url
		}else{
			url = config.apiDomain + config.apiBase + url
		}

		if (uni.getStorageSync('headerFrom')){
			header.From = uni.getStorageSync('headerFrom')
		}
		if (uni.getStorageSync('newLogin')) return
		var requestTask = uni.request({
			url: url,
			data: params,
			header,
			method: 'GET',
			success: (res) => {
				store.state.on_click = false
				if (res.statusCode==500){
					uni.showToast({
						title: "服务器内部错误",
						icon: "none",
						duration: 2000
					})
					doFail&&doFail(res)
					return
				}
				if (res.data.code === undefined ){
					uni.showToast({
						title: "数据格式错误",
						icon: "none",
						duration: 2000
					})
					doFail && doFail(res)
					return
				}
				if (res.data.code === -1 || res.data.code === -9) {
					uni.removeStorageSync('token')
					store.state.user_login_status = 1
				} else if (res.data.code === 2) {
					store.state.user_login_status = 2
				}
				if(options.disableAutoHandle){ //如果禁用自动处理code则直接执行请求成功回调
					doSuccess(res)
					return
				}
				if(res.data.code == 2 && store.state.allowOpen){
					uni.hideLoading()
					navigateTo('/user/bind_phone/bind_phone')
					return
				}
				if(res.data.code == -1 && store.state.allowOpen){
					uni.hideLoading()
					uni.removeStorageSync('token')
					store.state.toLogin = true
					uni.setStorageSync('backUrl', window.location.href)
					navigateTo('/user/login/login')
					// showModal({
					// 	title: '提示',
					// 	content: '当前操作需要登录，是否去登录？',
					// 	confirmText: '去登录',
					// 	confirm: () => {
					// 		navigateTo('/user/login/login')
					// 	},
					// 	cancel:()=>{
					// 		if (getCurrentPages().length > 1) {
					// 			uni.navigateBack()
					// 		} else {
					// 			uni.switchTab({
					// 				url: '/pages/index/index'
					// 			})
					// 		}
					// 	}
					// });
					return
				}
				if(res.data.code == -9){
					uni.removeStorageSync('token')
					uni.showToast({
						title:res.data.msg,
						icon:"none"
					})
					setTimeout(()=>{
						uni.setStorageSync('backUrl', window.location.href)
						navigateTo('/user/login/login')
					},1500)
					return
				}
				if(res.data.code == -3){
					uni.showToast({
						title:res.data.msg,
						icon:"none"
					})
					setTimeout(()=>{
						uni.navigateBack()
					},2000)
					return
				}
				if(res.data.code == -4){
					showModal({
						title: '提示',
						content: res.data.msg,
						confirm: ()=> {
							navigateTo('/user/recharge')
						}
					});
					return
				}
				if(res.data.code == -7){
					showModal({
						title: '提示',
						content: res.data.msg,
						confirm: ()=> {
							navigateTo('/user/recharge?type=2')
						}
					});
					return
				}
				if(res.data.code == -5 && store.state.allowOpen){
					uni.showToast({
						title:res.data.msg,
						duration:2000,
						icon:"none"
					})
					setTimeout(()=>{
						if (res.data.is_agent){
							navigateTo('/user/member_upgrade')
						}else{
							navigateTo('/user/member_upgrade?is_personal=1')
						}
					},2000)
					return
				}
				if (res.data.code == -8) { //家装会员已到期
					uni.showToast({
						title: res.data.msg,
						icon: 'none'
					})
					return
				}
				if (res.data.code == -10) { //当前用户被封禁
					uni.showToast({
						title: res.data.msg,
						icon: 'none'
					})
					return
				}
				if (res.data.code == -11) { //当前用户被关闭聊天功能
					uni.showToast({
						title: res.data.msg,
						icon: 'none'
					})
					return
				}
				if (res.data.code == 0) {
					if(process.env.NODE_ENV === 'development'){
						console.log(res.data.msg || '')
					}
				}
				doSuccess(res)
			},
			fail: (err) => {
				if (doFail) {
					doFail(err)
				}
				let err_content = ''
				if (typeof err === 'string') {
					err_content = err
				} else {
					err_content = JSON.stringify(err)
				}
				console.log(err_content)
				uni.showToast({
					title: "网络请求失败：" + err_content,
					icon: "none",
					duration: 2000
				})
			},
			complete:()=>{
				setTimeout(() => {
					store.state.requesting_list = store.state.requesting_list.filter(item => item !== initial_url)
				}, config.anti_shake_api.delay)
			}
		});
		if (requestTask && uni.getStorageSync('newLogin')){
			console.log('取消请求', url)
			requestTask.abort();
		}
	},
	post: function(url, params, doSuccess, doFail, options={}) {
		let initial_url = url
		if (config.anti_shake_api.list.some(item => item === initial_url) && store.state.requesting_list.some(item => item === initial_url)) {
			console.log('当前接口请求中，拒绝请求')
			doFail && doFail({ msg: '当前接口请求中' })
			return
		}
		store.state.requesting_list.push(initial_url)
		let header = {
			"content-type": "application/json"
		}
		// #ifndef APP-PLUS
		header.Tfyplatform = store.state.systemInfo.platform || ''
		header.CLIENTVERSION = version
		// #endif
		if(uni.getStorageSync('token')){
			//params.token = uni.getStorageSync('token') //后台需要在请求参数上传递token
			header.Authorization = uni.getStorageSync('token')
		}
		/** 
		 * header.From 1：微信小程序，2：微信公众号，3：h5，4：PC，5：百度小程序，6：支付宝小程序，8：安卓 9 ios
		 */
		// #ifdef MP-WEIXIN
		header.From = 1
		// #endif
		// #ifdef H5
		var ua = navigator.userAgent.toLowerCase();
		if (ua.match(/MicroMessenger/i) == "micromessenger") { //如果是微信
			header.From = 2
		}else{
			header.From = 3
		}
		// #endif
		// #ifdef MP-BAIDU
		header.From = 5
		// #endif
		if(url[0]==='/'){
			url = config.apiDomain + url
		}else{
			url = config.apiDomain + config.apiBase + url
		}
		if (uni.getStorageSync('headerFrom')) {
			header.From = uni.getStorageSync('headerFrom')
		}
		if (uni.getStorageSync('newLogin')) return
		var requestTask = uni.request({
			url: url,
			data: params,
			header,
			method: 'POST',
			timeout: 60000,
			success: (res) => {
				store.state.on_click = false
				if (res.statusCode == 500) {
					uni.showToast({
						title: "服务器内部错误",
						icon: "none",
						duration: 2000
					})
					doFail && doFail(res)
					return
				}
				if (res.data.code === undefined) {
					uni.showToast({
						title: "数据格式错误",
						icon: "none",
						duration: 2000
					})
					doFail && doFail(res)
					return
				}
				if (res.data.code === -1 || res.data.code === -9){
					uni.removeStorageSync('token')
					store.state.user_login_status = 1
				}else if(res.data.code === 2){
					store.state.user_login_status = 2
				}
				if(options.disableAutoHandle){
					doSuccess(res)
					return
				}
				if(res.data.code == 2 && store.state.allowOpen){
					uni.hideLoading()
					navigateTo('/user/bind_phone/bind_phone')
					return
				}
				if(res.data.code == -1 && store.state.allowOpen){
					uni.hideLoading()
					uni.removeStorageSync('token')
					store.state.toLogin = true
					uni.setStorageSync('backUrl', window.location.href)
					navigateTo('/user/login/login')
					// showModal({
					// 	title: '提示',
					// 	content: '当前操作需要登录，是否去登录？',
					// 	confirmText: '去登录',
					// 	confirm: () => {
					// 		navigateTo('/user/login/login')
					// 	},
					// 	cancel: () => {
					// 		if (getCurrentPages().length > 1) {
					// 			uni.navigateBack()
					// 		} else {
					// 			uni.switchTab({
					// 				url: '/pages/index/index'
					// 			})
					// 		}
					// 	}
					// });
					return
				}
				if(res.data.code == -9){
					uni.removeStorageSync('token')
					uni.showToast({
						title:res.data.msg,
						icon:"none"
					})
					setTimeout(()=>{
						uni.setStorageSync('backUrl', window.location.href)
						navigateTo('/user/login/login')
					},1500)
					return
				}
				if(res.data.code == -3){
					uni.showToast({
						title:res.data.msg,
						icon:"none"
					})
					setTimeout(()=>{
						uni.navigateBack()
					},2000)
					return
				}
				if(res.data.code == -4){
					uni.hideLoading()
					showModal({
						title: '提示',
						content: res.data.msg,
						confirm: ()=> {
							navigateTo('/user/recharge')
						}
					});
					return
				}
				if(res.data.code == -7){
					uni.hideLoading()
					showModal({
						title: '提示',
						content: res.data.msg,
						confirm: ()=> {
							navigateTo('/user/recharge?type=2')
						}
					});
					return
				}
				if(res.data.code == -5 && store.state.allowOpen){
					uni.showToast({
						title:res.data.msg,
						duration:2000,
						icon:"none"
					})
					setTimeout(()=>{
						if (res.data.is_agent) {
							navigateTo('/user/member_upgrade')
						} else {
							navigateTo('/user/member_upgrade?is_personal=1')
						}
					},2000)
					return
				}
				if (res.data.code == -8) { //家装会员已到期
					uni.showToast({
						title: res.data.msg,
						icon: 'none'
					})
					return
				}
				if (res.data.code == -10) { //当前用户被封禁
					uni.showToast({
						title: res.data.msg,
						icon: 'none'
					})
					return
				}
				if (res.data.code == -11) { //当前用户被关闭聊天功能
					uni.showToast({
						title: res.data.msg,
						icon: 'none'
					})
					return
				}
				doSuccess(res)
			},
			fail: (err) => {
				if (doFail) {
					doFail(err)
					return
				}
				// console.log(JSON.stringify(err))
				uni.showToast({
					title: "网络链接失败，请重试",
					icon: "none",
					duration: 2000
				})
			},
			complete: () => {
				setTimeout(()=>{
					store.state.requesting_list = store.state.requesting_list.filter(item => item !== initial_url)
				}, config.anti_shake_api.delay)
			}
		});
		if (requestTask && uni.getStorageSync('newLogin')) {
			requestTask.abort();
		}
	}
}
// 文件上传
	let upHeader = {}
	// #ifndef APP-PLUS
	upHeader.CLIENTVERSION = version
	// #endif
	// #ifdef MP-WEIXIN
	upHeader.From = 1
	// #endif
	// #ifdef H5
	var ua = navigator.userAgent.toLowerCase();
	if (ua.match(/MicroMessenger/i) == "micromessenger") { //如果是微信
		upHeader.From = 2
	} else {
		upHeader.From = 3
	}
	// #endif
	// #ifdef MP-BAIDU
	upHeader.From = 5
	// #endif
	// #ifdef APP-PLUS
	if (uni.getSystemInfoSync().platform == "ios") {
		upHeader.Clientfrom = 9
		upHeader.APPVERSION = plus.runtime.version
		upHeader.CLIENTVERSION = plus.runtime.version
	} else {
		upHeader.Clientfrom = 8
		upHeader.CLIENTVERSION = plus.runtime.version
	}
	// #endif
const uploadFile = function(action=config.uploadApi,filePath, formData = {}, doSuccess, doFail) {
	if (uni.getStorageSync('token')) {
		upHeader.Authorization = uni.getStorageSync('token');
	}
	if(action[0]==='/'){
		action = config.apiDomain + action
	}else{
		action = config.apiDomain + config.apiBase + action
	}
	uni.uploadFile({
		url: action,
		filePath: filePath,
		name: 'file',
		header: upHeader,
		formData: formData,
		timeout: 60000,
		success: (res) => {
			try{
				// #ifdef MP-BAIDU
				res.data
				// #endif
				// #ifndef MP-BAIDU
				JSON.parse(res.data)
				// #endif
			}catch(err){
				console.log(err)
				uni.showToast({
					title:"上传失败",
					icon:"none"
				})
				return
			}
			if (doSuccess) {
				doSuccess(res)
			}
		},
		fail: (err) => {
			console.log(err)
			uni.showToast({
				title:"上传失败，请重试",
				icon:"none"
			})
			if (doFail) {
				doFail(err)
			}
		}
	});
}
// 权限授权检测
const checkAuth = function(scope,obj){
	uni.getSetting({
		success:(res)=> {
			//console.log(res.authSetting);
			if (!res.authSetting[scope]) {
				uni.authorize({
					scope: scope,
					success:()=> {
						obj.success()
					},
					fail:(err)=> {
						console.log(err)
						obj.fail()
					}
				})
			}else{
				obj.authOk()
				// console.log(res)
			}
		},
		fail(err){
			console.log(err)
		}
	})
}

const compareVersion = function (v2) {
	let v1 = uni.getSystemInfoSync().SDKVersion
	v1 = v1.split('.')
	v2 = v2.split('.')
	const len = Math.max(v1.length, v2.length)

	while (v1.length < len) {
		v1.push('0')
	}
	while (v2.length < len) {
		v2.push('0')
	}

	for (let i = 0; i < len; i++) {
		const num1 = parseInt(v1[i])
		const num2 = parseInt(v2[i])

		if (num1 > num2) {
			return 1
		} else if (num1 < num2) {
			return -1
		}
	}

	return 0
}

/**
 * 格式化金额显示
 * @param {number} money 金额数值
 * @returns {string} 格式化后的金额字符串
 * 1.00 => 1
 * 1.20 => 1.2  
 * 1.23 => 1.23
 */
export const formatMoney = (money) => {
  if(!money && money !== 0) return '';
  // 先保留2位小数
  let value = Math.round(Number(money) * 100) / 100;
  // 转字符串
  let str = value.toString();
  // 如果是整数,直接返回
  if(str.indexOf('.') < 0) {
    return str;
  }
  // 去掉末尾的0
  return str.replace(/\.?0+$/, '');
}

module.exports = {
	config,
	debounce,
	checkUserStatus,
	ajax,
	uploadFile,
	now,
	isArray,
	isVideo,
	isHttp,
	isIos,
	formatImg,
	navigateTo,
	showModal,
	checkAuth,
	getSceneParams,
	compareVersion,
	formatMoney
}
