<template>
    <view class="newsInfo">
                <view class="first flex-box bottom-line" v-for = "item in listsData" :key= "item.id" @click= "toDetail(item.info_url,item.id)">
                    <view class= 'left flex-box '>
                        <view class="title">{{item.info_title}}</view>
                        <view class="time">{{item.info_utime}}</view>
                    </view>
                    
                    <view class="img">
                        <image :src="item.info_smallimg|imgUrl('w_240')" mode = "widthFix"></image>
                    </view>
                </view>
    </view>
</template>

<script>

import {
    formatImg,
    navigateTo
} from "../../common/index.js"
export default {
    props:{
        listsData:{
            type:Array,
            default:[]
        },
        
    },
    // components:{
    //     uniList,
    //     uniListItem
    // },
    data(){
        return {
            
        }
    },
    filters:{
        imgUrl(val, param = "") {
            return formatImg(val, param)
        }
    },
    methods: {
        toDetail(type,id){
            if(type){
                navigateTo('/pages/news/detail?id='+type)
            }else {
                 // #ifdef H5
                    navigateTo('/topic/info_detail?id='+id)
                    // #endif 
                    // #ifdef MP
                    let url =encodeURIComponent(config.apiDomain+'/m/topic/info_detail?id='+id) 
                    navigateTo('/pages/web_view/web_view?url='+url)
                    // #endif
                // navigateTo('/topic/info_detail?id='+id)
            }
            
        }
    },
   
}
</script>

<style lang="scss">
.newsInfo{
        padding: 0upx 50upx;
        border: 1px solid #f0f0f0;
        box-shadow: 0upx 10upx 10upx 10upx #f0f0f0;
        border-radius: 10upx;
        .first{
            padding: 20upx 0upx;
            align-items: center;
            justify-content: space-between;
            .left{
                flex-direction: column;
                justify-content: space-around;
                max-width: calc(100% - 240upx);
                min-height: 140upx;

                padding: 10upx 0upx;
                .title{
                    
                    font-size: 34upx;
                    font-weight: bolder;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp:2;
                    overflow: hidden;
                    text-overflow: hidden;  
                    line-height: 1.5;
                    letter-spacing: 2upx;
                }
                .time{
                    font-size: 25upx;
                }
            }
            .img{
                width: 200upx;
                max-height: 200upx;
                overflow: hidden;
                image{
                    border-radius: 10upx;
                    width: 100%;
                    max-height: 200upx;
                }
            }
        }
       
        
        
    }

</style>