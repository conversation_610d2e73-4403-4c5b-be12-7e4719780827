<template>
  <view
    class="house bottom-line"
    @click="$emit('click', { type: type, detail: itemData })"
  >
    <view class="tip">
      <view>
        <text v-if="itemData.is_show == 1" class="el-tag el-success"
          >已上架</text
        >
        <text v-else class="el-tag el-danger">已下架</text>
        <text v-if="itemData.info_level == 0" class="el-tag el-warning"
          >待审</text
        >
        <text v-else-if="itemData.info_level == 1" class="el-tag el-success"
          >正常</text
        >
        <text v-else class="el-tag el-danger">精选</text>
        <text v-if="itemData.upgrade_type == 2" class="el-tag el-danger"
          >置顶</text
        >
        <text v-if="itemData.is_fee == 1" class="el-tag el-primary">付费</text>
        <text v-if="itemData.is_deleted == 1" class="el-tag el-danger"
          >已删除</text
        >
        <text
          v-if="itemData.release_type == 'check_release'"
          class="el-tag el-warning"
          >委托发布</text
        >
      </view>
      <view class="used el-tag el-danger" @click.prevent.stop="used"
        >历史{{ itemData.history_count }}条记录</view
      >
    </view>
    <view class="info_box flex-row">
      <view
        class="img-box"
        :class="{
          cut_price: itemData.is_cut_price == 1 && itemData.cut_price > 0,
        }"
      >
        <!-- <view class="level-box">
        <view class="level level2" v-if="itemData.info_level === 2">精选</view>
      </view> -->
        <view class="img_con">
          <image
            class="img"
            :src="itemData.img | imgUrl"
            lazy-load
            mode="aspectFill"
          ></image>
          <image
            v-if="itemData.is_vr || itemData.vr"
            class="video-icon"
            src="/static/icon/vr.png"
          ></image>
          <image
            v-else-if="itemData.is_video == 1"
            class="video-icon"
            src="/static/icon/video.png"
          ></image>
          <view v-if="itemData.parentid == 1" class="img-label img-label-sale"
            >出售</view
          >
          <view v-if="itemData.parentid == 2" class="img-label img-label-rent"
            >出租</view
          >
          <view
            v-if="itemData.parentid == 3"
            class="img-label img-label-transfer"
            >转让</view
          >
          <view
            v-if="itemData.is_cut_price == 1 && itemData.cut_price > 0"
            class="cut_price_info"
            >直降{{ itemData.cut_price }}{{ itemData.cut_price_unit }}</view
          >
        </view>
        <!-- 当前会员认领的+委托发布的+下架或者待审的，加一个上架按钮,已删除的不显示上架 -->
        <!-- <text v-if="itemData.is_claim == 1&&itemData.release_type == 'check_release'&&itemData.is_deleted != 1&&(itemData.is_show != 1 || itemData.info_level == 0)" class="foot-status" @click.prevent.stop="update">上架</text> -->
        </view>
      <view class="info">
        <view class="title" :class="titleRow == 2 ? 'row2' : 'row1'">
          <text v-if="itemData.upgrade_type == 2" class="ding">顶</text>
          <text v-if="itemData.info_level === 2" class="jing">精</text>
          <text
            :class="{
              red: itemData.ifred,
              bold: itemData.ifbold,
            }"
            >{{ itemData.title }}</text
          >
        </view>
        <view class="center-info">
          <text class="mj" v-if="itemData.mianji"
            >{{ itemData.mianji }}{{ itemData.mianji_unit }}</text
          >
          <text class="type">{{
            itemData.community_name || itemData.areaname
          }}</text>
        </view>
        <view class="labels">
          <!-- <text v-if="itemData.is_cut_price==1&&itemData.cut_price >0" class="cut_price_info">直降{{itemData.cut_price}}{{itemData.cut_price_unit}}</text> -->
          <template v-if="itemData.label && itemData.label.length > 0">
            <text
              class="label"
              :style="{ color: label.color, borderColor: label.color }"
              v-for="(label, index) in itemData.label"
              :key="index"
              >{{ label.name }}
            </text>
          </template>
        </view>
        <view class="bottom-info flex-box">
          <view class="bottom-left">
            <template v-if="type == 'sale'">
              <text
                class="mianyi"
                v-if="
                  itemData.price == '面议' ||
                    itemData.price == '0' ||
                    !itemData.price
                "
                >面议</text
              >
              <text class="price" v-else>{{ itemData.price }}</text>
              <block
                v-if="
                  itemData.price !== '面议' &&
                    itemData.price != '0' &&
                    itemData.price
                "
              >
                <text class="price-unit">{{ itemData.price_unit }}</text>
                <!-- <text class="average_price">{{ itemData.danjia }}{{itemData.catid == 5 ? '元/亩' : '元/m²'}}</text> -->
                <text class="average_price"
                  >{{ itemData.danjia }}{{ itemData.danjia_unit }}</text
                >
              </block>
            </template>
            <template v-if="type === 'rent' || type === 'transfer'">
              <text
                class="mianyi"
                v-if="
                  itemData.price == '面议' ||
                    itemData.price == '0' ||
                    !itemData.price
                "
                >面议</text
              >
              <text class="price" v-else>{{ itemData.price }}</text>
              <text
                class="price-unit"
                v-if="
                  itemData.price !== '面议' &&
                    itemData.price != '0' &&
                    itemData.price
                "
                >{{ itemData.price_unit }}</text
              >
              <text
                class="average_price"
                v-if="
                  itemData.catid == 2 &&
                    itemData.zujin_type == 2 &&
                    itemData.zujin_month > 0
                "
                >{{ itemData.zujin_month }}元/月</text
              >
            </template>
          </view>
          <view class="bottom-right" v-if="showTime">
            <text class="u-time">{{ itemData.begintime }}</text>
          </view>
        </view>

        <template v-if="itemData.is_claim == 1 && itemData.claim_is_protected == 0">
          <view class="footer-info">
            <view class="footer-right">
              <image
                v-if="itemData.prelogo != ''"
                class="ic_dianhua prelogo"
                :src="itemData.prelogo"
              ></image>
              <text class="contact-name" v-if="itemData.contact_who != ''"
                >{{ itemData.contact_who }}</text
              >
              <text v-show="itemData.tel != ''">{{ itemData.tel }}</text>
              <image
                v-show="itemData.tel != ''"
                class="ic_dianhua"
                @click.prevent.stop="handleTel"
                src="https://images.tengfangyun.com/images/icon/ic_dianhua.png"
              ></image>
            </view>
          </view>
        </template>
        <!-- <template v-if="itemData.is_claim == 1"> -->
        <view class="foot-info">
          <text
            class="foot-status"
            v-if="itemData.is_claim == 1"
            >已认领</text
          >
          <text
            class="foot-status"
            v-if="itemData.is_claim == 0"
            @click.prevent.stop="renling"
            >点击认领</text
          >
          <!-- 信息被当前会员认领 -->
          <template v-if="itemData.is_claim == 1">
            <text class="blue" @click.prevent.stop="browse">浏览</text>
            <text class="orange" @click.prevent.stop="follow">跟进</text>
            <text class="green" @click="showCopywriting()">复制</text>
          </template>
        </view>
        <!-- </template> -->
        <view v-if="itemData.is_claim == 1" class="foot-info foot-text">
          {{itemData.claim_cname}}已认领
        </view>
        <!-- <view
          class="agent_info flex-row"
          v-if="showBottom && itemData.levelid > 1"
        >
          <image
            class="header_img"
            :src="itemData.prelogo | imageFilter('w_80')"
          ></image>
          <text class="c_name">{{ itemData.cname }}</text>
          <text class="b_name flex-1">{{ itemData.tname }}</text>
        </view>
        <view class="hongbao" v-if="itemData.hb_is_open">
          <image src="/static/icon/hongbao.png" mode="aspectFit"></image>
          <text class="text">红包</text>
        </view> -->
      </view>
    </view>
    <!-- 复制分享文案 -->
    <!-- 出售 -->
    <my-popup
      ref="text_popup"
      position="center"
      :height="text_popup_height"
      v-if="itemData.parentid == 1"
    >
      <view class="copy-text-box" id="copy-text">
        <view class="title">{{ itemData.title }}</view>
        <view class="info-box">
          <view class="info-row flex-row" v-if="itemData.name">
            <text class="label">小区：</text>
            <text class="value">{{ itemData.name }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">面积：</text>
            <text class="value">{{
              `${itemData.mianji}${itemData.mianji_unit}`
            }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">售价：</text>
            <text class="value">{{
              itemData.price ? itemData.price + itemData.price_unit : "面议"
            }}</text>
            <text class="value" v-if="itemData.danjia && itemData.catid != 5"
              >单价{{ itemData.danjia }}{{ itemData.danjia_unit }}</text
            >
          </view>
          <view class="info-row flex-row">
            <text class="label">{{
              itemData.catid == 5 ? "土地用途：" : "类型："
            }}</text>
            <text class="value">{{ itemData.type_title }}</text>
          </view>
          <view class="info-row flex-row" v-if="itemData.catid < 3">
            <text class="label">楼层：</text>
            <text class="value" v-if="itemData.szlc"
              >{{ itemData.floor_title }}{{ itemData.szlc }}层{{
                itemData.szlc2 !== 0 ? `至${itemData.szlc2}层` : ""
              }}/共{{ itemData.louceng || "" }}层</text
            >
            <text class="value" v-else
              >{{ itemData.floor_title }}共{{ itemData.louceng || "" }}层</text
            >
          </view>
          <template v-if="itemData.catid == 1">
            <view class="info-row flex-row">
              <text class="label">经营状态：</text>
              <text class="value">{{
                itemData.business_status == 1 ? "经营中" : "空置中"
              }}</text>
            </view>
            <view class="info-row flex-row" v-if="itemData.trade_title">
              <text class="label">经营行业：</text>
              <text class="value"
                >{{ itemData.trade_ptitle }}-{{ itemData.trade_title }}</text
              >
            </view>
            <view class="info-row flex-row" v-if="itemData.consumer_ids">
              <text class="label">客流人群：</text>
              <text class="value">{{ itemData.consumer_ids }}</text>
            </view>
          </template>
          <template v-if="itemData.catid == 2">
            <view class="info-row flex-row">
              <text class="label">可注册：</text>
              <text class="value">{{
                itemData.can_register == 1 ? "是" : "否"
              }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">可拆分：</text>
              <text class="value">{{
                itemData.can_divisible == 1 ? "是" : "否"
              }}</text>
            </view>
          </template>
          <template v-if="itemData.catid == 5">
            <view class="info-row flex-row">
              <text class="label">土地来源：</text>
              <text class="value">{{ itemData.land_source_title }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">流转年限：</text>
              <text class="value">{{ itemData.useful_years }}年</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">土地使用证：</text>
              <text class="value">{{
                itemData.land_certificate == 1 ? "有" : "无"
              }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">所有权证：</text>
              <text class="value">{{
                itemData.owner_certificate == 1 ? "有" : "无"
              }}</text>
            </view>
          </template>
          <view
            class="info-row flex-row"
            v-if="itemData.label && itemData.label.length > 0"
          >
            <text class="label">卖点：</text>
            <text class="value">{{
              itemData.label.map((item) => item.name).join(" ")
            }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">电话：</text>
            <text class="value">{{ itemData.tel }}</text>
          </view>
          <view class="button disabled-btn flex-row" v-if="copy_success">
            <my-icon type="check-circle" size="30rpx" color="#999"></my-icon>
            <text class="text">文本已复制</text>
          </view>
          <view class="button" v-else @click="copywriting1">复制文本</view>
        </view>
      </view>
    </my-popup>
    <!-- 出租 -->
    <my-popup
      ref="text_popup"
      position="center"
      :height="text_popup_height"
      v-if="itemData.parentid == 2"
    >
      <view class="copy-text-box" id="copy-text">
        <view class="title">{{ itemData.title }}</view>
        <view class="info-box">
          <view class="info-row flex-row" v-if="itemData.name">
            <text class="label">小区：</text>
            <text class="value">{{ itemData.name }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">面积：</text>
            <text class="value">{{
              `${itemData.mianji}${itemData.mianji_unit}`
            }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">租金：</text>
            <text class="value">{{
              itemData.price ? itemData.price + itemData.price_unit : "面议"
            }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">{{
              itemData.catid == 5 ? "土地用途：" : "类型："
            }}</text>
            <text class="value">{{ itemData.type_title }}</text>
          </view>
          <view class="info-row flex-row" v-if="itemData.catid < 3">
            <text class="label">楼层：</text>
            <text class="value" v-if="itemData.szlc"
              >{{ itemData.floor_title }}{{ itemData.szlc }}层{{
                itemData.szlc2 !== 0 ? `至${itemData.szlc2}层` : ""
              }}/共{{ itemData.louceng || "" }}层</text
            >
            <text class="value" v-else
              >{{ itemData.floor_title }}共{{ itemData.louceng || "" }}层</text
            >
          </view>
          <template v-if="itemData.catid == 1">
            <view class="info-row flex-row">
              <text class="label">经营状态：</text>
              <text class="value">{{
                itemData.business_status == 1 ? "经营中" : "空置中"
              }}</text>
            </view>
            <view class="info-row flex-row" v-if="itemData.trade_title">
              <text class="label">经营行业：</text>
              <text class="value"
                >{{ itemData.trade_ptitle }}-{{ itemData.trade_title }}</text
              >
            </view>
            <view class="info-row flex-row">
              <text class="label">规格：</text>
              <text class="value"
                >面宽{{ itemData.sizes_width }}m、层高{{
                  itemData.sizes_height
                }}m、进深{{ itemData.sizes_depth }}m</text
              >
            </view>
            <view class="info-row flex-row">
              <text class="label">客流人群：</text>
              <text class="value">{{ itemData.consumer_ids }}</text>
            </view>
          </template>
          <template v-if="itemData.catid == 2">
            <view class="info-row flex-row">
              <text class="label">可注册：</text>
              <text class="value">{{
                itemData.can_register == 1 ? "是" : "否"
              }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">可拆分：</text>
              <text class="value">{{
                itemData.can_divisible == 1 ? "是" : "否"
              }}</text>
            </view>
          </template>
          <template v-if="itemData.catid == 5">
            <view class="info-row flex-row">
              <text class="label">土地来源：</text>
              <text class="value">{{ itemData.land_source_title }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">流转年限：</text>
              <text class="value">{{ itemData.useful_years }}年</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">土地使用证：</text>
              <text class="value">{{
                itemData.land_certificate == 1 ? "有" : "无"
              }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">所有权证：</text>
              <text class="value">{{
                itemData.owner_certificate == 1 ? "有" : "无"
              }}</text>
            </view>
          </template>
          <view
            class="info-row flex-row"
            v-if="itemData.label && itemData.label.length > 0"
          >
            <text class="label">卖点：</text>
            <text class="value">{{
              itemData.label.map((item) => item.name).join(" ")
            }}</text>
          </view>
          <view class="info-row flex-row" v-if="itemData.tel">
            <text class="label">电话：</text>
            <text class="value">{{
              itemData.tel ? itemData.tel : "暂无"
            }}</text>
          </view>
          <view class="button disabled-btn flex-row" v-if="copy_success">
            <my-icon type="check-circle" size="30rpx" color="#999"></my-icon>
            <text class="text">文本已复制</text>
          </view>
          <view class="button" v-else @click="copywriting2">复制文本</view>
        </view>
      </view>
    </my-popup>
    <!-- 转让 -->
    <my-popup
      ref="text_popup"
      position="center"
      :height="text_popup_height"
      v-if="itemData.parentid == 3"
    >
      <view class="copy-text-box" id="copy-text">
        <view class="title">{{ itemData.title }}</view>
        <view class="info-box">
          <view class="info-row flex-row" v-if="itemData.name">
            <text class="label">小区：</text>
            <text class="value">{{ itemData.name }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">面积：</text>
            <text class="value">{{
              `${itemData.mianji}${itemData.mianji_unit}`
            }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">租金：</text>
            <text class="value">{{
              itemData.price ? itemData.price + itemData.price_unit : "面议"
            }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">转让费：</text>
            <text class="value">{{
              itemData.transfer_fee > 0
                ? itemData.transfer_fee + "万元"
                : itemData.transfer_fee
            }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">剩余租期：</text>
            <text class="value">{{
              itemData.remain_lease > 0
                ? itemData.remain_lease + "月"
                : itemData.remain_lease
            }}</text>
          </view>
          <view class="info-row flex-row">
            <text class="label">{{
              itemData.catid == 5 ? "土地用途：" : "类型："
            }}</text>
            <text class="value">{{ itemData.type_title }}</text>
          </view>
          <view class="info-row flex-row" v-if="itemData.catid < 3">
            <text class="label">楼层：</text>
            <text class="value" v-if="itemData.szlc"
              >{{ itemData.floor_title }}{{ itemData.szlc }}层{{
                itemData.szlc2 !== 0 ? `至${itemData.szlc2}层` : ""
              }}/共{{ itemData.louceng || "" }}层</text
            >
            <text class="value" v-else
              >{{ itemData.floor_title }}共{{ itemData.louceng || "" }}层</text
            >
          </view>
          <template v-if="itemData.catid == 1">
            <view class="info-row flex-row">
              <text class="label">经营状态：</text>
              <text class="value">{{
                itemData.business_status == 1 ? "经营中" : "空置中"
              }}</text>
            </view>
            <view class="info-row flex-row" v-if="itemData.trade_title">
              <text class="label">经营行业：</text>
              <text class="value"
                >{{ itemData.trade_ptitle }}-{{ itemData.trade_title }}</text
              >
            </view>
            <view class="info-row flex-row">
              <text class="label">规格：</text>
              <text class="value"
                >面宽{{ itemData.sizes_width }}m、层高{{
                  itemData.sizes_height
                }}m、进深{{ itemData.sizes_depth }}m</text
              >
            </view>
            <view class="info-row flex-row" v-if="itemData.consumer_ids">
              <text class="label">客流人群：</text>
              <text class="value">{{ itemData.consumer_ids }}</text>
            </view>
          </template>
          <template v-if="itemData.catid == 2">
            <view class="info-row flex-row">
              <text class="label">可注册：</text>
              <text class="value">{{
                itemData.can_register == 1 ? "是" : "否"
              }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">可拆分：</text>
              <text class="value">{{
                itemData.can_divisible == 1 ? "是" : "否"
              }}</text>
            </view>
          </template>
          <template v-if="itemData.catid == 5">
            <view class="info-row flex-row">
              <text class="label">土地来源：</text>
              <text class="value">{{ itemData.land_source_title }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">流转年限：</text>
              <text class="value">{{ itemData.useful_years }}年</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">土地使用证：</text>
              <text class="value">{{
                itemData.land_certificate == 1 ? "有" : "无"
              }}</text>
            </view>
            <view class="info-row flex-row">
              <text class="label">所有权证：</text>
              <text class="value">{{
                itemData.owner_certificate == 1 ? "有" : "无"
              }}</text>
            </view>
          </template>
          <view
            class="info-row flex-row"
            v-if="itemData.label && itemData.label.length > 0"
          >
            <text class="label">卖点：</text>
            <text class="value">{{
              itemData.label.map((item) => item.name).join(" ")
            }}</text>
          </view>
          <view class="info-row flex-row" v-if="itemData.tel">
            <text class="label">电话：</text>
            <text class="value" v-if="itemData.tel">{{ itemData.tel }}</text>
          </view>
          <view class="button disabled-btn flex-row" v-if="copy_success">
            <my-icon type="check-circle" size="30rpx" color="#999"></my-icon>
            <text class="text">文本已复制</text>
          </view>
          <view class="button" v-else @click="copywriting3">复制文本</view>
        </view>
      </view>
    </my-popup>
  </view>
</template>
<script>
import { formatImg } from "@/common/index.js";
import myPopup from "@/components/myPopup.vue";
import myIcon from "../../components/myIcon.vue";
export default {
  components: {
    myIcon,
    myPopup,
  },
  data() {
    return {
      text_popup_height: "",
      copy_success: false,
    };
  },
  props: {
    itemData: Object,
    type: {
      type: String,
      default: "ershou",
    },
    titleRow: {
      type: [Number, String],
      default: 2,
    },
    showTime: {
      type: Boolean,
      default: true,
    },
    showBottom: {
      type: Boolean,
      default: true,
    },
  },
  filters: {
    imgUrl(val) {
      return formatImg(val, "w_240");
    },
  },
  methods: {
    renling() {
      this.$emit("renling", this.itemData);
    },
    browse() {
      this.$emit("browse", this.itemData);
    },
    follow() {
      this.$emit("follow", this.itemData);
    },
    handleTel() {
      this.$emit("handleTel", this.itemData);
    },
    used() {
      this.$emit("used", this.itemData);
    },
    update(){
      this.$emit("update", this.itemData);
    },
    showCopywriting() {
      console.log(this.itemData);
      this.$ajax.get(
        "infoServicer/infoCopyCheck",
        {
          info_id: this.itemData.id,
          info_type: 2,
        },
        (res) => {
          if (res.data.code == 1) {
            const query = uni.createSelectorQuery().in(this);
            query
              .select("#copy-text")
              .fields(
                { rect: true, scrollOffset: true, size: true },
                (data) => {
                  this.text_popup_height = data.height + "px";
                }
              )
              .exec();
            this.copy_success = false;
            this.$refs.text_popup.show();
            // this.$refs.ershou.hide()
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        }
      );
    },
    copywriting1() {
      let tel = "";
      if (this.itemData.tel) {
        tel = "【电话】" + this.itemData.tel + "\n";
      }
      let louceng = "";
      if (this.itemData.catid < 3) {
        louceng = `【楼层】${this.itemData.floor_title}${
          this.itemData.szlc
            ? this.itemData.szlc +
              "层" +
              (this.itemData.szlc2 !== 0
                ? "至" + this.itemData.szlc2 + "层"
                : "")
            : ""
        }${"共" + this.itemData.louceng + "层"}\n`;
      }
      let info = "";
      if (this.itemData.catid == 1) {
        info = `【经营状态】${
          this.itemData.business_status == 1 ? "经营中" : "空置中"
        }
${
  this.itemData.trade_title
    ? "【经营行业】" +
      this.itemData.trade_ptitle +
      "-" +
      this.itemData.trade_title +
      "\n"
    : ""
}【规格】面宽${this.itemData.sizes_width}m、层高${
          this.itemData.sizes_height
        }m、进深${this.itemData.sizes_depth}m\n`;
      }
      if (this.itemData.catid == 2) {
        info = `【可注册】${
          this.itemData.can_register == 1 ? "是" : "否"
        }\n【可拆分】${this.itemData.can_divisible == 1 ? "是" : "否"}\n`;
      }
      if (this.itemData.catid == 5) {
        info = `【土地来源】${this.itemData.land_source_title}\n【流转年限】${
          this.itemData.useful_years
        }年
【土地使用证】${
          this.itemData.land_certificate == 1 ? "有" : "无"
        }\n【所有权证】${this.itemData.owner_certificate == 1 ? "有" : "无"}\n`;
      }
      this.$ajax.post(
        "infoServicer/infoCopy",
        {
          info_id: this.itemData.id,
          info_type: 1,
        },
        (res) => {
          const text = `${this.itemData.title}
${this.itemData.name ? "【小区】" + this.itemData.name + "\n" : ""}【面积】${
            this.itemData.mianji
          }${this.itemData.mianji_unit}
【售价】${
            this.itemData.price
              ? this.itemData.price + this.itemData.price_unit
              : "面议"
          }${
            this.itemData.danjia && this.itemData.catid != 5
              ? " 单价" + this.itemData.danjia + this.itemData.danjia_unit
              : ""
          }
${this.itemData.catid == 5 ? "【土地用途】" : "【类型】"}${
            this.itemData.type_title
          }
${louceng}${info}${
            this.itemData.label.length > 0
              ? "【卖点】" +
                this.itemData.label.map((item) => item.name).join(" ") +
                "\n"
              : ""
          }${tel}`;
          this.copyContent(text, () => {
            this.copy_success = true;
          });
        }
      );
    },
    copywriting2() {
      let tel = "";
      if (this.itemData.tel) {
        tel = "【电话】" + this.itemData.tel + "\n";
      }
      let louceng = "";
      if (this.itemData.catid < 3) {
        louceng = `【楼层】${this.itemData.floor_title}${
          this.itemData.szlc
            ? this.itemData.szlc +
              "层" +
              (this.itemData.szlc2 !== 0
                ? "至" + this.itemData.szlc2 + "层"
                : "")
            : ""
        }${"共" + this.itemData.louceng + "层"}\n`;
      }
      let info = "";
      if (this.itemData.catid == 1) {
        info = `【经营状态】${
          this.itemData.business_status == 1 ? "经营中" : "空置中"
        }
${
  this.itemData.trade_title
    ? "【经营行业】" +
      this.itemData.trade_ptitle +
      "-" +
      this.itemData.trade_title +
      "\n"
    : ""
}【规格】面宽${this.itemData.sizes_width}m、层高${
          this.itemData.sizes_height
        }m、进深${this.itemData.sizes_depth}m\n`;
      }
      if (this.itemData.catid == 2) {
        info = `【可注册】${
          this.itemData.can_register == 1 ? "是" : "否"
        }\n【可拆分】${this.itemData.can_divisible == 1 ? "是" : "否"}\n`;
      }
      if (this.itemData.catid == 5) {
        info = `【土地来源】${this.itemData.land_source_title}\n【流转年限】${
          this.itemData.useful_years
        }年
【土地使用证】${
          this.itemData.land_certificate == 1 ? "有" : "无"
        }\n【所有权证】${this.itemData.owner_certificate == 1 ? "有" : "无"}\n`;
      }
      this.$ajax.post(
        "infoServicer/infoCopy",
        {
          info_id: this.itemData.id,
          info_type: 1,
        },
        (res) => {
          const text = `${this.itemData.title}
${this.itemData.name ? "【小区】" + this.itemData.name + "\n" : ""}【面积】${
            this.itemData.mianji
          }${this.itemData.mianji_unit}
【租金】${
            this.itemData.price
              ? this.itemData.price + this.itemData.price_unit
              : "面议"
          }
${this.itemData.catid == 5 ? "【土地用途】" : "【类型】"}${
            this.itemData.type_title
          }
${louceng}${info}${
            this.itemData.label.length > 0
              ? "【卖点】" +
                this.itemData.label.map((item) => item.name).join(" ") +
                "\n"
              : ""
          }${tel}【查看】`;

          this.copyContent(text, () => {
            this.copy_success = true;
          });
        }
      );
    },
    copywriting3() {
      let tel = "";
      if (this.itemData.tel) {
        tel = "【电话】" + this.itemData.tel + "\n";
      }
      let louceng = "";
      if (this.itemData.catid < 3) {
        louceng = `【楼层】${this.itemData.floor_title}${
          this.itemData.szlc
            ? this.itemData.szlc +
              "层" +
              (this.itemData.szlc2 !== 0
                ? "至" + this.itemData.szlc2 + "层"
                : "")
            : ""
        }${"共" + this.itemData.louceng + "层"}\n`;
      }
      let info = "";
      if (this.itemData.catid == 1) {
        info = `【经营状态】${
          this.itemData.business_status == 1 ? "经营中" : "空置中"
        }
${
  this.itemData.trade_title
    ? "【经营行业】" +
      this.itemData.trade_ptitle +
      "-" +
      this.itemData.trade_title +
      "\n"
    : ""
}【规格】面宽${this.itemData.sizes_width}m、层高${
          this.itemData.sizes_height
        }m、进深${this.itemData.sizes_depth}m\n`;
      }
      if (this.itemData.catid == 2) {
        info = `【可注册】${
          this.itemData.can_register == 1 ? "是" : "否"
        }\n【可拆分】${this.itemData.can_divisible == 1 ? "是" : "否"}\n`;
      }
      if (this.itemData.catid == 5) {
        info = `【土地来源】${this.itemData.land_source_title}\n【流转年限】${
          this.itemData.useful_years
        }年
【土地使用证】${
          this.itemData.land_certificate == 1 ? "有" : "无"
        }\n【所有权证】${this.itemData.owner_certificate == 1 ? "有" : "无"}\n`;
      }
      this.$ajax.post(
        "infoServicer/infoCopy",
        {
          info_id: this.itemData.id,
          info_type: 1,
        },
        (res) => {
          const text = `${this.itemData.title}
${this.itemData.name ? "【小区】" + this.itemData.name + "\n" : ""}【面积】${
            this.itemData.mianji
          }${this.itemData.mianji_unit}
【租金】${
            this.itemData.price
              ? this.itemData.price + this.itemData.price_unit
              : "面议"
          }
【转让费】${
            this.itemData.transfer_fee > 0
              ? this.itemData.transfer_fee + "万元"
              : this.itemData.transfer_fee
          }
【剩余租期】${
            this.itemData.remain_lease > 0
              ? this.itemData.remain_lease + "月"
              : this.itemData.remain_lease
          }
${this.itemData.catid == 5 ? "【土地用途】" : "【类型】"}${
            this.itemData.type_title
          }
${louceng}${info}${
            this.itemData.label.length > 0
              ? "【卖点】" +
                this.itemData.label.map((item) => item.name).join(" ") +
                "\n"
              : ""
          }${tel}`;
          this.copyContent(text, () => {
            this.copy_success = true;
          });
        }
      );
    },
    // 复制内容
    copyContent(content, callback) {
      // #ifndef H5
      uni.setClipboardData({
        data: content,
        success: (res) => {
          if (callback) callback();
        },
      });
      // #endif
      // #ifdef H5
      let oInput = document.createElement("textarea");
      oInput.value = content;
      document.body.appendChild(oInput);
      oInput.style.opacity = 0;
      oInput.select(); // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand("Copy"); // 执行浏览器复制命令
      uni.showToast({
        title: "复制成功",
        icon: "none",
      });
      oInput.blur();
      oInput.remove();
      if (callback) callback();
      // #endif
    },
  },
};
</script>

<style scoped lang="scss">
.el-tag {
  height: 40rpx;
  padding: 0 10rpx;
  line-height: 36rpx;
  display: inline-block;
  font-size: 24rpx;
  border-width: 2rpx;
  border-style: solid;
  border-radius: 4rpx;
  box-sizing: border-box;
  white-space: nowrap;
  margin-right: 10rpx;
}

.el-primary {
  color: #409eff;
  background-color: #ecf5ff;
  border-color: #d9ecff;
}

.el-warning {
  background-color: #fdf6ec;
  border-color: #faecd8;
  color: #e6a23c;
}

.el-success {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}

.el-danger {
  background-color: #fef0f0;
  border-color: #fde2e2;
  color: #f56c6c;
}
// 复制文案
.copy-text-box {
  padding: 24rpx 32rpx;
  background-color: #fff;
  width: 600rpx;
  // height: 100%;
  // margin-left: 75rpx;
  margin: 0 auto;
  border-radius: 16rpx;
  .title {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }
  .info-row {
    line-height: 1.6;
    color: #333;
    .label {
      color: #999;
    }
    .value {
      flex: 1;
      &.highlight {
        color: $uni-color-primary;
      }
    }
  }
  .button {
    align-items: center;
    justify-content: center;
    line-height: 64rpx;
    width: 236rpx;
    text-align: center;
    border-radius: 32rpx;
    margin: 32rpx auto;
    background: #fb656a;
    box-shadow: 0 2px 8px 0 rgba(251, 101, 106, 0.4);
    color: #fff;
  }
  .disabled-btn {
    background-color: #f5f5f5;
    box-shadow: none;
    color: #999;
    > .text {
      margin-left: 12rpx;
    }
  }
}
.footer-info {
  margin: 20rpx 0;
  color: #333;
  .footer-right {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 28rpx;
    .contact-name{
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    text {
      margin-right: 10rpx;
      display: inline-block;
    }
  }
  .ic_dianhua {
    width: 50rpx;
    height: 50rpx;
  }
  .prelogo {
    border-radius: 50%;
    margin-right: 10rpx;
    object-fit: cover;
  }
}
.foot-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16rpx;
  .foot-status {
    background: #fb656a;
  }
  .blue {
    background: #498fe2;
  }
  .orange {
    background: #fbac65;
  }
  .green {
    background: #67c23a;
  }
  text {
    background: #fb656a;
    color: #fff;
    border-radius: 22rpx;
    border: none;
    padding: 6rpx 20rpx;
    font-size: 24rpx;
    // width: 90rpx;
    text-align: center;
  }
}
.foot-text{
  color: #333;
}
.house {
  padding: 40rpx 0;
  .tip {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    .used {
      background: #fff;
      border-color: #f56c6c;
    }
  }
  .info_box {
    display: flex;
    .img-box {
      width: 204rpx;
      height: 172rpx;
      margin-right: 16rpx;
      position: relative;
      border-radius: 8rpx;
      .img_con {
        position: relative;
        width: 204rpx;
        height: 172rpx;
        border-radius: 8rpx;
        overflow: hidden;
        background: #fff;
        z-index: 1;
      }
      .foot-status{
        background: #fb656a;
        color: #fff;
        display: block;
        text-align: center;
        font-size: 24rpx;
        padding: 4rpx 0;
        border-radius: 4rpx;
        margin-top: 14rpx;
      }
      &.cut_price {
        padding: 4rpx;
        .cut_price_info {
          position: absolute;
          left: 0;
          bottom: 0;
          z-index: 3;
          display: inline-block;
          padding: 8rpx 12rpx;
          font-size: 22rpx;
          line-height: 1;
          border-top-right-radius: 8rpx;
          background: #ff6069;
          background-size: 100% 100%;
          color: #fff;
        }
        &:after {
          content: "";
          position: absolute;
          top: -3px;
          bottom: -3px;
          left: -3px;
          right: -3px;
          background: linear-gradient(to bottom, #ff6069 0%, #ffa857 100%);
          border-radius: 10rpx;
          z-index: 0;
        }
      }

      .img {
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 1;
      }
      .level-box {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 2;
        .level {
          display: block;
          margin-bottom: 5rpx;
          padding: 2rpx 10rpx;
          font-size: 22rpx;
          border-bottom-left-radius: 20rpx;
          color: #fff;
          &.level1 {
            background: linear-gradient(132deg, #f7918f 0%, #fb656a 100%);
          }
          &.level2 {
            background: linear-gradient(135deg, #69d4bb 0%, #00caa7 100%);
          }
        }
      }
      .video-icon {
        width: 40rpx;
        height: 40rpx;
        position: absolute;
        left: 50%;
        bottom: 50%;
        z-index: 2;
        transform: translate(-50%, 50%);
      }
      .img-label {
        position: absolute;
        top: 0;
        right: 0;
        width: 68rpx;
        height: 36rpx;
        font-size: 22rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        border-radius: 0 0 0 8rpx;
        z-index: 2;
        &.img-label-sale {
          background: linear-gradient(180deg, #ff4f3bff 0%, #ff7154ff 100%);
        }
        &.img-label-rent {
          background: linear-gradient(-152.68deg, #00b374ff 0%, #22e29bff 100%);
        }
        &.img-label-transfer {
          background: linear-gradient(180deg, #0a81f3ff 0%, #4dbcfdff 100%);
        }
      }
    }
    .info {
      flex: 1;
      overflow: hidden;
      .title {
        font-size: 32rpx;
        line-height: 1.5;
        margin-top: -6rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        .red {
          color: #fb656a;
        }
        .bold {
          font-weight: bold;
        }
        &.row1 {
          max-height: 90rpx;
          margin-bottom: 10rpx;
        }
        &.row2 {
          min-height: 90rpx;
        }
      }
      .ding,
      .jing {
        display: inline-block;
        padding: 6rpx 10rpx;
        margin-right: 10rpx;
        line-height: 1;
        font-size: 22rpx;
        border-radius: 4rpx;
        color: #fff;
      }
      .ding {
        background: linear-gradient(to right, #f7918f 0%, #fb656a 100%);
      }
      .jing {
        background: linear-gradient(135deg, #69d4bb 0%, #00caa7 100%);
      }
      .center-info {
        display: flex;
        align-items: center;
        margin-top: 5rpx;
        font-size: 22rpx;

        .jiange {
          margin: 0 4rpx;
          color: #999;
          &.jiange-margin {
            margin: 0 12rpx;
          }
        }
        &.need {
          .price_box {
            margin-left: 48rpx;
          }
          .label {
            font-size: 22rpx;
            color: #999;
          }
          .area {
            font-size: 22rpx;
            color: #333;
          }
          .in_price {
            font-size: 22rpx;
            color: $uni-color-primary;
          }
        }
        .area {
          margin-left: 16rpx;
          color: #999;
        }
        .type {
          overflow: hidden;
          white-space: nowrap;
          flex: 1;
          text-align: right;
          text-overflow: ellipsis;
          color: #333;
        }
        .cx {
          margin-right: 4rpx;
        }
        .mj {
          margin-right: 4rpx;
        }
      }

      .labels {
        margin-top: 16rpx;
        line-height: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        .cut_price_info {
          display: inline-block;
          padding: 8rpx 12rpx;
          font-size: 22rpx;
          line-height: 1;
          border-radius: 4rpx;
          background: #ff6069;
          margin-right: 4rpx;
          color: #fff;
        }
        .label {
          display: inline-block;
          line-height: 1;
          font-size: 22rpx;
          padding: 4rpx 8rpx;
          border: 1rpx solid #d8d8d8;
          color: #999;
          border-radius: 4rpx;
          ~ .label {
            margin-left: 16rpx;
          }
        }
      }
      .bottom-info {
        margin-top: 16rpx;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        flex-wrap: wrap;
        text {
          font-size: 22rpx;
        }
        .mianyi {
          font-size: 32rpx;
          font-weight: bold;
          margin-right: 10rpx;
          color: #fb656a;
        }
        .price {
          font-size: 34rpx;
          line-height: 1;
          font-weight: bold;
          color: #fb656a;
        }
        .price-unit {
          font-size: 26rpx;
          margin: 0 16rpx 0 8rpx;
        }
        .average_price {
          color: #999;
        }
        .bottom-right {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          text-align: right;
          margin-left: 16rpx;
        }
        .u-time {
          line-height: 1;
          position: relative;
          font-size: 22rpx;
          color: #999;
        }
      }
    }
    .agent_info {
      display: flex;
      margin-top: 16rpx;
      align-items: center;
      font-size: 22rpx;
      color: #999;
      .header_img {
        width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
        background-color: #f5f5f5;
      }
      .c_name,
      .b_name {
        margin-left: 16rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
.hongbao {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 20rpx;
  }
  .text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #ff5b5b;
  }
}
</style>
