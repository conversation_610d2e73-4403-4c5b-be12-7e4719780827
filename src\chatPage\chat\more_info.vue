<template>
<view class="page">
    <view class="list-item flex-box bottom-line" @click="toInvited()">
        <view class="icon">
            <my-icon type="fabu1" color="#f05021" size="48rpx"></my-icon>
        </view>
        <view class="info flex-1">
            <text class="title">邀请码</text>
            <text class="note">邀请同事加入</text>
        </view>
        <view class="right-tip">邀请有礼</view>
        <my-icon type="ic_into"></my-icon>
    </view>
    <view class="list-item flex-box bottom-line" @click="$navigateTo('/chatPage/chat/auto_reply')">
        <view class="icon">
            <my-icon type="ic_xiugai" color="#f05021" size="52rpx"></my-icon>
        </view>
        <view class="info flex-1">
            <text class="title">设置自动回复</text>
        </view>
        <my-icon type="ic_into"></my-icon>
    </view>
</view>
</template>

<script>
import myIcon from '../../components/myIcon'
export default {
    data() {
        return {
            lineData:[]
        }
    },
    components: {
        myIcon
    },
    onLoad(options){
        if(options.chat_id){
            this.chat_id = options.chat_id
            this.getData(options.chat_id)
        }
        this.adviser_id = options.adviser_id||null
        this.adviser = options.adviser||null
        this.user_id = options.user_id||null
    },
    methods: {
        getData(chat_id){
            this.$ajax.get('im/visitorTrajectoryLog.html',{chat_id}, res=>{
                if(res.data.code === 1){
                    this.lineData = res.data.logs
                }
            })
        },
        toInvited(){
            if(this.adviser_id&&this.adviser_id!='0'){
                this.$navigateTo('/chatPage/chat/invited')
            }else{
                uni.showToast({
                    title:"您还不是置业顾问,不能邀请",
                    icon:'none'
                })
            }
        }
    }
}
</script>

<style lang="scss">
.list-item{
    align-items: center;
    .icon{
        margin-right: 20upx;
    }
    .info{
        margin-right: 16upx;
        .title{
            font-size: 34upx;
            font-weight: bold;
            margin-right: 16upx;
        }
        .note{
            font-size: 24upx;
        }

    }
    .right-tip{
        margin-right: 16upx;
        font-size: 34upx;
        font-weight: bold;
        color: #ff144f
    }
}
</style>
