<template>
  <view class="publication">
    <view v-if="show_select" class="info-row flex-box" @click="chooseXiaoqu">
      <view class="label">{{xiaoqu_name||'选择小区'}}</view>
      <view class="icon-box">
        <my-icon type="ic_into" color="#bbb" size="32rpx"></my-icon>
      </view>
    </view>
    <textarea
      auto-height
      v-model="params.content"
      placeholder="发布小区实勘动态 成为小区专家"
    ></textarea>
    <view class="pic_list">
      <myUpload
        @uploadDon="uploadDon"
        :imgs="imgList"
         :videos="videoList"
        :chooseType="2"
        :maxCount="maxCount"
        :showSlot="false"
      >
      </myUpload>
    </view>
    <!-- 位置 -->
    <!-- <view class="my_position row" @click="goMap">
      <view class="left row">
        <myIcon type="juli" size="44rpx"></myIcon>
        <text class="site_name" v-if="site">{{ site }}</text>
        <text class="site_name" v-else>请选择你的位置</text>
      </view>
      <view class="right">
        <myIcon type="you" color="#999" size="22rpx"></myIcon>
      </view>
    </view> -->
    <my-popup ref="popup" position="bottom">
      <view class="community_box">
        <view class="search_box flex-box">
          <view class="flex-1">
            <my-search v-model="keyword" @confirm="handelSearch">
          </my-search>
          </view>
          <view class="search_btn flex-box" @click="handelSearch">
            <text class="text">搜索</text>
            <!-- <my-icon type="ic_sousuo" color="#fff" size="36rpx"></my-icon> -->
          </view>
        </view>
        <view class="community_list">
          <view class="community_item" @click="selectXiaoqu(item)" v-for="(item, index) in search_list" :key="index">
            {{item.title}}
          </view>
        </view>
      </view>
    </my-popup>
    <view class="tip">
      <myIcon type="tishifu" color="#888888" size="40rpx"></myIcon>
      <text>本页面可提交本小区实景图片、视频。请勿涵盖任何形式水印，一经审核通过优质内容将获得更多推荐和展示。（禁止发布房源、联系方式等内容）</text>
    </view>
    <view class="btn-box">
      <button class="default" @click="handelPush()">立即发布</button>
    </view>
    <!-- 返回叉号 -->
    <view class="go_back" @click="back">
      <my-icon type="close" color="#333" size="28"></my-icon>
    </view>
  </view>
</template>

<script>
import myIcon from '../../components/myIcon'
import myPopup from '../../components/myPopup'
import mySearch from '../../components/mySearch'
import myUpload from '../../components/form/myUpload'
export default {
  components: {
    myUpload,
    myIcon,
    myPopup,
    mySearch
  },
  data() {
    return {
      imgList: [],
      videoList:[],
      maxCount: 6,
      site: '',
      params:{
        content:''
      },
      keyword:"",
      xiaoqu_name:"",
      search_list:[],
      show_select: false
    }
  },
  onLoad(options) {
    if(options.cid){
      this.params.cid = options.cid
      this.show_select = false
    }else{
      this.show_select = true
    }
  },
  computed: {},
  methods: {
    chooseXiaoqu(){
      console.log("显示选择小区")
      this.$refs.popup.show()
    },
    handelSearch(e){
      this.$ajax.get('index/searchCommunity.html',{keywords:this.keyword}, res=>{
        if(res.data.code === 1){
          console.log(res.data.list)
          this.search_list = res.data.list
        }else{
          this.search_list=[]
        }
      }, err=>{
        this.search_list=[]
        this.builds=[]
        this.get_status ="noMore"
      })
    },
    selectXiaoqu(e){
      this.xiaoqu_name = e.title
      this.params.cid = e.id
      this.$refs.popup.hide()
    },
    uploadDon(e) {
      if(e.type == 'video'){
        this.videoList = e.files
        this.params.isvideo = 1
        this.params.medias = e.files.join(',')
      }else if(e.type == 'image'){
        this.imgList = e.files
        this.params.isvideo = 0
        this.params.medias = e.files.join(',')
      }
    },
    back(){
      if (getCurrentPages().length > 1) {
        uni.navigateBack()
      } else {
        uni.switchTab({
          url: '/'
        })
      }
    },
    goMap() {
      uni.chooseLocation({
        success: res => {
          var lat = res.latitude
          var lng = res.longitude
          this.site = res.name
        }
      })
    },
    handelPush(){
      if(!this.params.cid){
        uni.showToast({
          title: '请选择小区',
          icon:'none'
        })
        return
      }
      if(!this.params.medias){
        uni.showToast({
          title: '请上传图片或视频',
          icon:'none'
        })
        return
      }
      uni.showLoading({
        title:"发布中...",
        mask:true
      })
      this.$ajax.post("member/communityPub.html",this.params,res=>{
        if(res.data.code ===1){
          uni.showToast({
            title:res.data.msg,
            mask:true
          })
          setTimeout(() => {
            uni.$emit('getCommunityPhoto')
            this.$navigateBack()
          }, 1500);
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none',
            mask:true
          })
        }
      },err=>{
        uni.showToast({
          title:"发布失败，请重试",
          icon:'none',
          mask:true
        })
      })
    }
  }
}
</script>

<style lang="scss">
page{
  background-color: #fff ;
}
.publication {
  .info-row{
    padding: 24rpx 48rpx;
    width: 100%;
    box-sizing: border-box;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    .label{
      font-size: 30rpx;
      color: #666;
    }
    &.avatar-row{
      padding: 24rpx 0;
      .upload-btn{
        width: 96rpx;
        height: 96rpx;
      }
      .img{
        width: 96rpx;
        height: 96rpx;
      }
    }
    input{
      text-align: right;
    }
  }
  // padding: 0 10rpx;
  textarea {
    min-height: 100rpx;
    max-height: 200rpx;
    width: 100%;
    font-size: 32rpx;
    margin: 50rpx 48rpx 10px 48rpx;
    box-sizing: border-box;
  }
  .go_back {
    font-size: 66rpx;
    position: fixed;
    right: 8.3%;
    bottom: 5.1%;
  }
}
.pic_list{
  padding: 20rpx 48rpx;
}
.tip{
  padding: 10rpx 28rpx;
  color: #666;
  text{
    margin-left: 10rpx;
    font-size: 24rpx;
  }
}

.community_box{
  padding: 24rpx;
  background-color: #fff ;
  .search_box{
    align-items: center;
  }
  .search_btn{
    margin-left: 20rpx;
    align-items: center;
    height: 50rpx;
    padding: 6rpx 24rpx;
    border-radius: 10rpx;
    font-size: 28rpx;
    background-color: $uni-color-primary;
    color: #fff;
    .text{
      margin-right: 6rpx;
    }
  }
}
.community_list{
  margin-top: 24rpx;
  min-height: 30vh;
  max-height: 50vh;
  overflow-x: hidden;
  .community_item{
    padding: 20rpx 10rpx;
  }
}
</style>
