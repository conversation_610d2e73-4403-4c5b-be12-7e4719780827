<template>
<view class="apply-box">
    <view class="row bottom-line">选择升级类型</view>
    <view class="upgrade-list flex-box">
        <view class="upgrade-item flex-1" :class="{'active':item.value===type}" v-for="item in releaseList" :key="item.value" @click="selectRelease(item.value)">
            <view>{{item.name}}</view>
            <view>{{item.money}}金币/年</view>
        </view>
        <view class="upgrade-item flex-1" style="height:0;background:#fff;border-width:0"></view>
    </view>
    <view class="btn-box">
        <button class="btn" @click="handleUpgrade()">确定升级</button>
    </view>
</view>
</template>

<script>
export default {
    data(){
        return{
            type:"",
            releaseList:[]
        }
    },
    created(){
        this.getData()
    },
    methods:{
        getData(){
            this.$ajax.get('memberShop/upgrade',{},res=>{
                if(res.data.code == 1){
                    this.releaseList = res.data.group
                }
            })
        },
        selectRelease(type){
            this.type = type
        },
        handleUpgrade(){
            if(!this.type){
                uni.showToast({
                    title:"请选择会员类型",
                    icon:"none"
                })
                return
            }
            this.$ajax.post('memberShop/upgrade',{vip:this.type},res=>{
                if(res.data.code == 1){
                    uni.showToast({
                        title:"升级成功"
                    })
                    setTimeout(()=>{
                        uni.navigateBack()
                    },1500)
                }
            })
        }
    }
}
</script>

<style lang="scss">
    .apply-box{
        height:100vh;
        width:100%;
        background-color:#fff;
        .row{
            padding:24upx 30upx;
        }
        .list-item{
            display: flex;
            align-items: center;
            padding: $uni-spacing-col-lg $uni-font-size-lg;
            radio{
                padding: 20upx 30upx;
                margin-left: -30upx;
            }
            .list-title{
                font-size: $uni-font-size-lg;
                text-overflow:ellipsis;
                white-space:nowrap;
                line-height:1.5;
                overflow:hidden;
            }
        }
        .upgrade-list{
            padding: 10upx;
            flex-wrap: wrap;
        }
        .upgrade-item{
            min-width: 40%;
            width:45%;
            padding: 20upx;
            margin: 10upx;
            text-align: center;
            border-radius: 10upx;
            border: 1px solid #00c07b;
            color: #00c07b;
        }
        .upgrade-item.active{
            background-color: #00c07b;
            color: #fff;
        }
        .btn{
            background-color: #00c07b;
            color: #ffffff;
        }
    }
</style>