<template>
	<view class="page">
    <tab-bar class="nav" :tabs="parent_list" :fixedTop="true" @click="switchTab" :nowIndex="current_index"></tab-bar>
		<view class="house_list" v-if="serve==1">
        	<template v-if ="param.parentid==1|| param.parentid==2">
					<ershou :listsData="listsData" :type="param.parentid+''" from="mendian" ref="ershou" ></ershou>
        	</template>
        	<template v-if ="param.parentid==3|| param.parentid==4">
					<view class="house-item" v-for="(item, index) in listsData" :key="index">
        	  <demand-item :item-data="item" :type="param.parentid==3?'rest_house':'buy_house' " from="mendian" @click="toDetail($event,param.parentid)" :showTel="false"></demand-item>
        	</view>
        	</template>
		</view>
		<view class="house_list" v-else>
        	<template v-if ="params.parentid==1|| params.parentid==2">
					<ershou :listsData="listsData" :type="params.parentid+''" ref="ershou"></ershou>
        	</template>
        	<template v-if ="params.parentid==3|| params.parentid==4">
					<view class="house-item" v-for="(item, index) in listsData" :key="index">
        	  <demand-item :item-data="item" :type="params.parentid==3?'rest_house':'buy_house' " @click="toDetail($event,params.parentid)" :showTel="false"></demand-item>
        	</view>
        	</template>
		</view>
				<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
		
	</view>
</template>

<script>
	import ershou from "../../components/ershou.vue"
	import tabBar from "../../components/tabBar.vue"
	import demandItem from "../../components/demandItem.vue"
	import {uniLoadMore} from '@dcloudio/uni-ui'
	export default{
		components:{
			ershou,
			uniLoadMore,
      tabBar,
      demandItem
		},
		data(){
			return{
				get_status:"loading",
				content_text:{
					contentdown:"",
					contentrefresh:"正在加载...",
					contentnomore:"没有更多数据了"
				},
        params:{
          page:1,
          rows:20,
          id:'',
          uid:'',
          parentid:1
        },
		param:{
      	  page: 1,
      	  rows: 20,
      	  info_type: 1,
      	  info_id:'',
      	  uid:'',
          parentid:1
      	},
		serve:'',
				listsData:[],
        current_index:0
			}
		},
		computed:{
      parent_list(){
        return [
        {
          name:'出售',
          type:1
        },
        {
          name:'出租',
          type:2
        },
        {
          name:'求租',
          type:3
        },
        {
          name:'求购',
          type:4
        }
      ]
      }
		},
		onLoad(options){
      if (options.uid){
        this.params.uid = options.uid
        this.param.uid = options.uid
      }
		this.param.info_type = options.info_type
		this.param.info_id = options.info_id
		this.serve = options.serve
      if (options.id){
        this.params.id = options.id
      }
			this.getData()
		},
		filters:{
		},
		onShow(){
		},
		onUnload(){
		},

		methods:{
      switchTab(e){
        console.log(e);
        this.current_index =e.index
        this.params.parentid = e.type
        this.param.parentid = e.type
        this.params.page = 1
        this.param.page = 1
        this.getData()
      },
      handleTel(e){
        console.log(e);

      },
      toDetail(e){
        console.log(e);
        if (e.type =='buy_house'){
          this.$navigateTo("/needPage/buy_house/detail?id="+e.detail.id)
        }
        if (e.type =='rest_house'){
          this.$navigateTo("/needPage/rest_house/detail?id="+e.detail.id)
        }
      },
			getData(){
				this.get_status = "loading"
				var url;
				var from;
				// if (this.param.parentid==1) {
				// 	this.param.parentid=''
				// }
				if (this.serve==1) {
      			  url = 'infoServicer/historyInfoList'
      			  from = this.param
      			}else{
      			  url = 'infoAudit/infoList'
      			  from = this.params
      			}
				this.$ajax.get(url,from,(res)=>{
					//console.log(JSON.stringify(this.params));
					if(res.data.code == 1){
						console.log(from.page);
						if (from.page ==1){
							this.listsData =res.data.list
						}else {
							this.listsData = this.listsData.concat(res.data.list)
						}
						if(res.data.list.length<this.params.rows){
							this.get_status = "noMore"
						}else{
							this.get_status = "more"
						}
						
					}else{
						this.get_status = "noMore"
					}
				},(err)=>{
					console.log(err)
				})
			},
			stopMove(){

			},
		},
		onReachBottom(){
			if (this.get_status == "more"){
				this.params.page = this.params.page+1
				this.param.page = this.param.page+1
				this.getData()
			}
		},
		// #ifdef H5 || APP-PLUS
		onNavigationBarButtonTap(option){
			this.$navigateTo('/pages/add/detail?catid=1')
		},
		// #endif
	}
</script>
<style lang="scss" scoped>
	.page{
		background: #fff;
		.nav{
			top:0;
		}
	}
	.house_list{
		padding: 90rpx 48rpx 0;
		background-color: #fff;
	}
</style>
