<template>
  <view class="house flex-row" @click="$emit('click',{type:'new_house',detail:itemData})">
    <view class="img-box">
      <view class="level-box">
        <!-- <text class="level level1" v-if="itemData.isvip">精选</text> -->
        <!-- <text class="level level2" v-if="itemData.isgood">优惠</text> -->
      </view>
      <image class="img" :src="itemData.img | imageFilter('w_240')" lazy-load mode="aspectFill"></image>
      <image v-if="itemData.is_vr==1||itemData.quanjing" class="video-icon" :class="{center:mode===2}" src="/static/icon/vr.png"></image>
      <image v-else-if="itemData.is_video==1" class="video-icon" :class="{center:mode===2}" src="/static/icon/video.png"></image>
      <text class="area_name" v-if="mode===2">{{ itemData.areaname || "" }}</text>
    </view>
    <view class="info">
      <view class="title-box">
        <view class="title">
          <text>{{ itemData.title }}</text>
        </view>
        <template v-if ="index>=0&&index<10">
            <view class ="idx" :class="index<=2?('idx'+(index+1)):''">{{index+1}}</view>
        </template>
      </view>
      <view class="bottom-info flex-box" v-if="mode===1">
        <view class="bottom-left">
          <text class="price small">{{itemData.build_price}}</text>
          <text class="price-unit">{{itemData.price_unit}}</text>
        </view>
        <!-- <view class="bottom-right">
          <text class="u-time">{{ itemData.visit_desc }}</text>
        </view> -->
      </view>
      <view class="center-info" :class="{colum:mode===1}">
        <view class="area flex-row">
          <text class="area_name" v-if="mode===1">{{ itemData.areaname || "" }}</text>
          <text v-if="mode===1&&itemData.mj">建面：{{itemData.mj}}</text>
        </view>
        <view class="label-row">
        <text :class="'attr'+itemData.leixing" class ="attr">{{itemData.status_name}}</text>
          <text class="label" v-for = "(label,idx) in itemData.build_type" :key= "idx" >{{label}}</text>
        </view>
        <!-- <text :class="'status'+itemData.leixing">{{itemData.status_name}}</text> -->
      </view>
      <template v-if ="index>=0">
      <view class="paiming">
            <view class="paiming_box" :class="'paiming_box'+(index<=2?(index+1):3)">
                 <image :src="'/images/property/<EMAIL>'|imgUrl" alt="" mode ="widthFix"></image>
                 <text>本月热门榜第{{mingci[index]}}名</text>
            </view>
      </view>
      </template>

    </view>
  </view>
</template>
<style scoped lang="scss">
view{
  line-height: 1;
}
.house {
  display: flex;
  padding: 24rpx 0;
  .img-box {
    width: 204rpx;
    height: 172rpx;
    margin-right: 16rpx;
    position: relative;
    border-radius: 8rpx;
    overflow: hidden;
    .img {
      width: 100%;
      height: 100%;
    }
    .level-box{
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;
      .level{
        display: block;
        margin-bottom: 8rpx;
        padding: 4rpx 10rpx;
        font-size: 22rpx;
        border-bottom-left-radius: 20rpx;
        color: #fff;
        &.level1{
          background: linear-gradient(135deg, #69D4BB 0%, #00CAA7 100%);
        }
        &.level2{
          background: linear-gradient(132deg, #F7918F 0%, #FB656A 100%);
        }
      }
    }
    .video-icon{
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      left: 20rpx;
      bottom: 20rpx;
      &.center{
        left: initial;
        right: 12rpx;
        bottom: 12rpx;
        top: initial;
        margin: auto;
      }
    }
    .area_name{
      position: absolute;
      left: 12rpx;
      bottom: 12rpx;
      font-size: 22rpx;
      color: #fff;
    }
  }
  .info {
    flex: 1;
    overflow: hidden;
    .attr{
        margin-right: 5rpx;
    }
    .attr1 {
        font-size: 22rpx;
        padding: 6rpx 10rpx;
        border-radius: 4rpx;
        // margin-right: 10rpx;
        background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
        color: #fff;
      }
      .attr2 {
        font-size: 22rpx;
        padding: 6rpx 10rpx;
        border-radius: 4rpx;
        // margin-right: 10rpx;
        background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
        color: #fff;
      }
      .attr3 {
        font-size: 22rpx;
        padding: 6rpx 10rpx;
        border-radius: 4rpx;
        // margin-right: 10rpx;
        background: linear-gradient(to right, #ff8533 0%, #ff7213 100%);
        color: #f3f3f3;
      }
      .attr4 {
        font-size: 22rpx;
        padding: 6rpx 10rpx;
        border-radius: 4rpx;
        // margin-right: 10rpx;
        background: linear-gradient(to right, #ccc 0%, #ccc 100%);
        color: #fff;
      }
    .title-box{
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      .idx{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        background: #D8D8D8;
        border-radius: 20rpx;
        width: 36rpx;
        min-width: 36rpx;
        height: 36rpx;
        font-size: 28rpx;
        color: #FFFFFF;
        letter-spacing: 0;
        text-align: center;
        &.idx1{
            background: #FB656A;
            box-shadow: 0 4rpx 8rpx 0 rgba(251,101,106,0.40);
        }
        &.idx2{
            background: #FB8968;
            box-shadow: 0 2px 4px 0 rgba(251,137,104,0.40);
        }
        &.idx3{
            background: #FBC365;
            box-shadow: 0 2px 4px 0 rgba(251,195,101,0.40);
        }
      }
    }
    .title {
      flex: 1;
      font-size: 32rpx;
      line-height: 1.3;
      margin-top: -4rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      .ding {
        font-size: 22rpx;
        border-radius: 4upx;
        margin-right: 10upx;
        padding: 2upx 10upx;
        color: #fff;
        background-color: #f40;
      }
      .jing {
        font-size: $uni-font-size-sm;
        border-radius: 4upx;
        margin-right: 10upx;
        padding: 2upx 10upx;
        color: #fff;
        background-color: #f40;
      }
    }
    .center-info {
      display: flex;
      align-items: center;
      margin-top: 16rpx;
      font-size: 22rpx;
      color: #999;
      &.colum{
        align-items: flex-start;
        flex-direction: column;
        .area{
          margin-bottom: 16rpx;
          .area_name{
            margin-right: 16rpx;
          }
        }
      }
      .area {
        font-size: 22rpx;
        margin-right: 10rpx;
      }
      .status1{
        color: #4cc7f6;
      }
      .status2{
        color: #00caa7;
      }
      .status3{
        color: #666;
      }
      .status4{
        color: #ff7213;
      }
    }

    .label-row{
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }
    .label{
      display: inline-block;
      font-size: 22rpx;
      padding: 4rpx 6rpx;
      color: #999;
      border: 0.5px solid #d8d8d8;
      border-radius: 4rpx;
      ~.label{
        margin-left: 10rpx;
      }
    }

    .bottom-info {
      margin-top: 16rpx;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      flex-wrap: wrap;
      text {
        font-size: 22rpx;
      }
      .price {
        font-size: 32rpx;
        font-weight: bold;
        color: #fb656a;
        &.small{
          font-size: 32rpx;
        }
      }
      .price-unit {
        font-size: 22rpx;
        color: #fb656a;
        margin: 0 10rpx 0 10rpx;
      }
      .mj {
        margin-left: 10rpx;
      }
      .bottom-right{
        text-align: right;
        // flex-shrink:0;
        flex: 1;
        overflow: hidden;
        font-size: 0;
      }
      .u-time {
        display: inline-block;
        max-width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        position: relative;
        font-size: 22rpx;
        color: #999;
      }
    }

    .hui-row{
      box-sizing: border-box;
      display: inline-block;
      height: 32rpx;
      line-height: 30rpx;
      margin-top: 16rpx;
      font-size: 22rpx;
      border: 1rpx solid $uni-color-primary;
      color: $uni-color-primary;
      max-width: 100%;
      border-radius: 4rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .hui{
        height: 100%;
        padding: 0 6rpx;
        display: inline-block;
        background-color: $uni-color-primary;
        color: #fff;
      }
      .text{
        // overflow: hidden;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        padding: 0 10rpx;
      }
    }

    .tuan-row{
      box-sizing: border-box;
      display: inline-block;
      height: 32rpx;
      line-height: 30rpx;
      margin-top: 16rpx;
      font-size: 22rpx;
      border: 1rpx solid #00caa9;
      color: #00caa9;
      max-width: 100%;
      border-radius: 4rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .tuan{
        height: 100%;
        padding: 0 6rpx;
        display: inline-block;
        background-color: #00caa9;
        color: #fff;
      }
      .text{
        // overflow: hidden;
        // text-overflow: ellipsis;
        // white-space: nowrap;
        padding: 0 10rpx;
      }
    }

    .other-info{
      position: relative;
      padding-right: 48rpx;
      height: 52rpx;
      margin-top: 14rpx;
      overflow: hidden;
      &.open{
        height: auto;
      }
      .open{
        position: absolute;
        top: 10rpx;
        right: 0;
        height: 32rpx;
        width: 32rpx;
        text-align: center;
        border-radius: 16rpx;
        background-color: #ffeff0;
      }
      .close{
        position: absolute;
        bottom: 10rpx;
        right: 0;
        height: 32rpx;
        width: 32rpx;
        text-align: center;
        border-radius: 16rpx;
        background-color: $uni-color-primary;
      }
      .item{
        display: flex;
        align-items: center;
        padding: 10rpx 0;
        white-space: nowrap;
        overflow: hidden;
        font-size: 26rpx;
        color: #333;
        .text{
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .icon{
        color: #fff;
        border-radius: 4rpx;
        padding: 4rpx;
        font-size: 22rpx;
        line-height: 1;
        margin-right: 20rpx;
      }
      .hui{
        background-color: $uni-color-primary;
      }
      .tuan{
        background-color: #00caa9;
      }
      .zhi{
        background-color: #f9cf5e;
      }
    }
    .paiming{
        margin-top: 24rpx;
        // color: #FB8968;
        .paiming_box{
            padding: 6rpx 26rpx;
            font-size: 22rpx;
            border-radius: 20rpx;
            display: inline-block;
            &.paiming_box1{
                background: rgba(255,58,58,0.15);
                color: #FB656A;
            }
            &.paiming_box2{
                background: rgba(255,120,26,0.21);
                color:#FB8968;
            }
            &.paiming_box3{
                background: rgba(251,195,101,0.20);
                color:#FBC365;
            }
            image{
                width: 20rpx;
                
            }
            text{
                margin-left: 6rpx;
            }
        }
    }
  }
}
</style>
<script>
import myIcon from '../components/myIcon'
import {config} from '../common/config.js'
export default {
  components: {myIcon},
  data() {
    return {
      other_info_onclose:true,
      mingci:['一','二','三','四','五','六','七','八','九','十']
    };
  },
  props: {
    itemData: Object,
    type: {
      type: String,
      default: "ershou",
    },
    titleRow: {
      type: [Number, String],
      default: 1,
    },
    mode:{
      type:Number,
      default:1
    },
    index:{
        type:Number,
        default:-1
    }
  },
  computed:{
    other_info_num(){
      let num = 0
      if(this.itemData.discount) num++
      if(this.itemData.group_title) num++
      if(this.itemData.live_room&&this.itemData.live_desc) num++
      return num
    }
  },
  filters:{
      imgUrl(val){
            return config.imgDomain+val+'?x-oss-process=style/m_240'  
        }
  },
  methods: {},
};
</script>

