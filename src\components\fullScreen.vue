<template>
<scroll-view class="fullscreen" :style="{height: systemInfo.windowHeight+'px', opacity: full_opcity, transition: css_transition?'opacity 0.5s': 'opacity 0s'}" v-show="show_full" :scroll-top="scroll_top" :scroll-with-animation="animation" scroll-y @scroll="onScroll">
  <view class="scroll_content" :style="{height: systemInfo.windowHeight*2+'px'}" @touchmove.stop.prevent="onTouchMove" @touchend="onTouchend" @touchstart="onTouchendStart">
    <image :src="bg_img | imageFilter('w_1200')" mode="aspectFill"></image>
    <slot></slot>
  </view>
</scroll-view>
</template>

<script>
import {mapState} from 'vuex'
export default {
  name: '',
  components: {},
  data () {
    return {
      full_opcity: 1,
      scroll_top: 0,
      animation: false,
      css_transition: false
    }
  },
  props: {
    show_full: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    bg_img: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState(["systemInfo"])
  },
  methods: {
    onScroll(e){
      if(e.detail.scrollTop === 0){
        this.full_opcity = 1
      }
      if(e.detail.scrollTop>=this.systemInfo.windowHeight-1){
        setTimeout(()=>{
          this.$emit('hide')
        }, 100)
        return
      }
      if(!this.full_time||Date.now()-this.full_time>60){
        this.full_opcity = 1-e.detail.scrollTop/this.systemInfo.windowHeight
        this.full_time = Date.now()
      }
    },
    onTouchendStart(e){
      this.touch_start_y = e.changedTouches[0].clientY
      this.touch_start_time = Date.now()
      this.animation = false
      this.css_transition = false
      console.log(this.css_transition)
    },
    onTouchend(e){
      this.touch_end_time = Date.now()
      this.touch_end_y = e.changedTouches[0].clientY
      var time_disparity = this.touch_end_time-this.touch_start_time
      var move_disparity = this.touch_start_y-this.touch_end_y
      var speed = time_disparity/move_disparity
      // console.log(speed)
      this.animation = true
      if(speed<=2&&speed>=0){
        this.scroll_top = this.systemInfo.windowHeight
        setTimeout(()=>{
          this.$emit('hide')
        }, 500)
      }else{
        this.css_transition = true
        this.full_opcity = 1
        this.scroll_top = 0
      }
    },
    onTouchMove(e){
      this.scroll_top = this.touch_start_y - e.changedTouches[0].clientY
    }
  }
}
</script>

<style scoped lang="scss">
.fullscreen{
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 996;
  .scroll_content{
    image{
      width: 100%;
      height: 50%;
      background-color: #000;
    }
  }
  .swiper{
    height: 100%;
  }
  .item{
    height: 100%;
    position: relative;
    image{
      width: 100%;
      height: 100%;
    }
  }
}
</style>