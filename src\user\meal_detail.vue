<template>
  <view>
    <view class="list">
      <view class="item" v-for="item in data_list" :key="item.id">
        <view class="name">{{item.title}}</view>
        <view class="time">购买时间：{{item.ctime}}</view>
        <view class="gave_list">
          <view class="gave_item l">
            <view class="flex-box" v-if="item.info_top">
              <text>剩余置顶：</text>
              <text class="surplus">{{item.info_top.surplus_info_top}}/{{item.info_top.info_top}}次</text>
            </view>
            <view class="more_link" @click="showLog(item.info_top.logs, 'top')">
              <text>使用记录</text>
              <my-icon type="ic_into" color="#0076d2" size="32rpx"></my-icon>
            </view>
            </view>
          <view class="gave_item" v-if="item.info_selected">
            <view class="flex-box">
              <text>剩余精选：</text>
              <text class="surplus">{{item.info_selected.surplus_info_selected}}/{{item.info_selected.info_selected}}次</text>
            </view>
            <view class="more_link" @click="showLog(item.info_selected.logs, 'jing')">
              <text>使用记录</text>
              <my-icon type="ic_into" color="#0076d2" size="32rpx"></my-icon>
            </view>
            </view>
        </view>
        <view class="time expire">到期时间：{{item.expire_time}}</view>
      </view>
      <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
      <my-popup ref="logs" position="center" height="760rpx">
        <view>
          <view class="logs">
            <view class="title">套餐使用记录</view>
            <scroll-view scroll-y class="info gave_list">
              <!-- <view class="time">套餐有效期剩余--</view> -->
              <view class="gave_item" v-for="(item, index) in logs" :key="index">
                <text class="time">{{item.ctime}}</text>
                <text>{{item.descp}}</text>
              </view>
            </scroll-view>
          </view>
          <view class="close" @click="$refs.logs.hide()">
            <my-icon type="guanbi" color="#fff" size="60rpx"></my-icon>
          </view>
        </view>
      </my-popup>
    </view>
  </view>
</template>

<script>
import {uniLoadMore} from '@dcloudio/uni-ui'
import myIcon from '../components/myIcon'
import myPopup from "../components/myPopup"
export default {
  components: {
    myIcon,
    uniLoadMore,
    myPopup
  },
  data () {
   return {
     get_status:"",
      content_text:{
        contentdown:"",
        contentrefresh:"正在加载...",
        contentnomore:"没有更多数据了"
      },
      page: 1,
      data_list:[],
      logs: []
    }
  },
  onLoad(){
    this.getData()
  },
  methods: {
    getData(){
      this.get_status = 'loading'
      if(this.page === 1){
        this.data_list = []
      }
      this.$ajax.get('upgrade/surplusPackage', {page: this.page}, res=>{
        if(res.data.code !==1){
          this.get_status = 'noMore'
          return
        }
        this.data_list = this.data_list.concat(res.data.list)
        this.get_status = 'more'
      }, err=>{
        this.get_status = 'load_end'
      })
    },
    showLog(logs, type){
      if(logs.length===0){
        uni.showToast({
          title: '还没有使用记录',
          icon: 'none'
        })
        return
      }
      this.logs = logs
      this.$refs.logs.show()
    }
  },
   onReachBottom(){
     if(this.get_status!=='more'){
       return
     }
     this.page ++
     this.getData()
   }
}
</script>

<style scoped lang="scss">
.list{
  padding-top: 24rpx;
  .item{
    padding: 48rpx;
    margin-bottom: 24rpx;
    background-color: #fff;
    .name{
      margin-bottom: 24rpx;
      font-size: 32rpx;
    }
    .time{
      font-size: 24rpx;
      color: #999;
      &.expire{
        color: $uni-color-primary;
      }
    }
  }
}
.gave_list{
  margin: 16rpx 0;
  .gave_item{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 16rpx 24rpx;
    color: #666;
    background-color: #f9f9f9;
    &.l{
      background-color: #fff3ec;
    }
    .surplus{
      margin-left: 16rpx;
    }
    .more_link{
      margin-left: 24rpx;
      font-size: 24rpx;
      display: flex;
      align-items: center;
      color: #0076d2;
    }
  }
}

.logs{
  width: 600rpx;
  margin: auto;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #fff;
  .title{
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    font-size: 36rpx;
    color: #fff;
    background-image: linear-gradient(-45deg, #FFC072 0%, #FF8D3E 100%);;
  }
  .info{
    width: 100%;
    box-sizing: border-box;
    padding: 24rpx;
    height: 500rpx;
    .gave_item{
      flex-direction: column;
      margin-bottom: 20rpx;
      .time{
        font-size: 26rpx;
      }
    }
  }
  .time{
    margin-bottom: 10rpx;
    padding: 0;
    line-height: 1;
    padding-left: 10rpx;
    border-left: 4rpx solid #FF9344;
  }
}
.close{
    width: 60rpx;
    height: 60rpx;
    margin: 32rpx auto;
    align-items: center;
    justify-content: center;
  }
</style>