<template>
	<view class="content">
		<view class="top_nav flex-row bottom-line">
			<view class="nav_item" v-for="(item, index) in top_navs" :key="index" :class="{active:current_nav_index===index}" @click="scroppTo(item.id,index)">{{item.name}}</view>
		</view>
		 <view class="lists-box" id="moments">
			<comment-list-new :listData="posts" :showMore="false"  @clickReply="toReply" @praise='like' @delComment="delPost" @toDetail="toDetail" type="1"></comment-list-new>
            
			<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
		</view>
		<view class="add-post" @click="addPost">
			<my-icon type="jiahao" size="72rpx" color="#fff"></my-icon>
		</view>
		<view class="foot" v-show="showInput">
			<chat-input @send-message="send_comment" @blur="blur" :focus="focus" :placeholder="input_placeholder"></chat-input>
		</view>
		
		<chat-tip></chat-tip>
	</view>
</template>

<script>
	import myIcon from "../../components/myIcon.vue"
	import commentListNew from '../../components/commentListNew'
	import chatInput from '../../components/im-chat/chatinput.vue'; //input框
	import {uniLoadMore,uniIcons} from '@dcloudio/uni-ui'
  	import getChatInfo from '../../common/get_chat_info'

	import {isArray,formatImg,showModal} from "../../common/index.js"
	export default {
		data() {
			return {
				params:{
					page:1,
					bid:""
				},
				get_status:"loading",
				content_text:{
					contentdown:"",
					contentrefresh:"正在加载...",
					contentnomore:"没有更多数据了"
				},
				posts: [],
				top_navs:[
					
					{
						name: '楼盘',
						id: 'build'
					},
					{
						name: '动态',
						id: 'news'
					},
					{
						name: '点评',
						id: 'comment'
					},
					
					{
						name: '户型',
						id: 'huxing'
					},
					
				],
				current_nav_index:2,
				nickname: '',
				index: '',
				comment_index: '',
				input_placeholder: '评论', //占位内容
				focus: false, //是否自动聚焦输入框
				is_reply: false, //回复还是评论
				showInput: false, //评论输入框
				screenHeight: '', //屏幕高度(系统)
				platform: '',
				windowHeight: '' ,//可用窗口高度(不计入软键盘)
				consu:0,
			};
		},
		components:{
			chatInput,
			uniLoadMore,
			myIcon,
			uniIcons,
			commentListNew
		},
		mounted() {
			uni.getStorage({
				key: 'posts',
				success: function (res) {
					this.posts = res.data;
				}
			});
		},
		onLoad(options){
			if(options.bid){
				this.params.bid = options.bid
			}
			if (options.open){
				this.consu =options.open
				console.log(this.consu);
				
			}
			this.getData()
		},
		onShow() {
			if(this.$store.state.updatePageData){
				this.params.page==1
				this.getData()
				this.$store.state.updatePageData = false
			}
			// uni.onWindowResize((res) => { //监听窗口尺寸变化,窗口尺寸不包括底部导航栏
			// 	if(this.platform === 'ios'){
			// 		this.windowHeight = res.size.windowHeight;
			// 		this.adjust();
			// 	}else{
			// 		if (this.screenHeight - res.size.windowHeight > 60 && this.windowHeight <= res.size.windowHeight) {
			// 			this.windowHeight = res.size.windowHeight;
			// 			this.adjust();
			// 		}
			// 	}
			// });
		},
		// onHide() {
		// 	uni.offWindowResize(); //取消监听窗口尺寸变化
		// },
		// onUnload() {
		// 	this.max = 0,
		// 		this.data = [];
		// },
		onReachBottom() { //监听上拉触底事件
			if(this.posts.length<1){
				return
			}
			this.params.page++
			this.getData()
		},
		onPullDownRefresh() { //监听下拉刷新动作
			this.params.page = 1
			this.getData()
		},
		computed: {
			imconsu(){
			 return this.$store.state.im.adviser
			},
			imchat(){
			 return this.$store.state.im.ischat
			}
		},
		filters:{
			imgUrl(img,param=""){
				return formatImg(img,param)
			}
		},
		methods:{
			scroppTo(id, index){
				
				if(id==='build'){
					this.$navigateTo(`/pages/new_house/detail?id=${this.params.bid}`)
					return 
				}
				if(id==='news'){
					this.$navigateTo(`/pages/new_house/buildNews?bid=${this.params.bid}`)
					return 
				}
				if(id==='huxing'){
					this.$navigateTo(`/pages/new_house/house_type_list?bid=${this.params.bid}`)
					return 
				}
				if(id==='comment'){
					this.current_nav_index===index
					return 
				}
				
			},
			toReply(option){
				let {parentIndex,index,be_reply,be_commentid} = option
				// return
				this.is_reply = true; //回复中
				this.showInput = true; //调起input框
				let  replyTo=''
				if (index){
					replyTo = this.posts[parentIndex].reply[index].nickname;
				}else {
					replyTo = this.posts[parentIndex].nickname;
				}
				
				this.input_placeholder = '回复' + replyTo;
				this.index = parentIndex; //post索引
				this.comment_index = index; //评论索引
				this.focus = true;

			},
			getData(){
				// if(this.params.page == 1){
				// 	this.posts = []
				// }
				this.get_status = "loading"
				this.$ajax.get("build/buildCommentList.html",this.params,(res)=>{
                    if(res.data.title){
                        uni.setNavigationBarTitle({
                            title: res.data.title+'楼盘点评'
                        })
                    }
					if(res.data.share){
						this.share = res.data.share
						
					}
					this.getWxConfig()
					if(res.data.code == 0){
						this.get_status = "noMore"
						this.params.page--
						if(this.params.page<1){
							this.params.page=1
						}
						return;
					}
                    uni.stopPullDownRefresh();
                    if(this.params.page == 1){
                        this.posts = res.data.list
                    }else{
                        this.posts = this.posts.concat(res.data.list)
                    }
					this.get_status = "more"
				})
			},
			handelTab(e){
				this.params.page = 1
				this.params.cid = e.id
				this.getData()
			},
			 ask(id,ids) {
      // this.$store.state.buildInfo = {
      //   id: this.id,
      //   title: this.detail.title,
      //   type: 'build',
      //   image: this.img[0]
      // }
      if(this.imchat == 1){//开聊天  
      
            getChatInfo(id, 3)
     
      }else if (this.imchat == 0) {   //不开聊天  
        this.consuDetail(ids)
      }
    },
				//转到顾问详情
			consuDetail(id) {
				if (id == 0) return 
				if(this.imconsu==1&&this.consu==1){
					this.$navigateTo('/pages/consultant/detail?id=' + id)
				}else {
						uni.showToast({
							title :"当前楼盘没有开通置业顾问功能 请先开通",
							icon:"none"
						})
				}
			
			},
			toDetail(option){
				// 如果type是2则跳转到新闻资讯详情
				let {id,index}=option
				if(this.posts[index].type==2){
					this.$navigateTo('/pages/news/detail?id='+id)
				}else{
					this.$store.state.tempData = this.posts[index]
					this.$navigateTo('/pages/comment_list/comment_detail?id='+id)
				}
			},
			like(option) {
				let {id,index}=option
				this.$ajax.get('news/praise.html',{id},res=>{
					if(res.data.code == 1){
						if (this.posts[index].is_praise === 0) {
							this.posts[index].is_praise = 1;
							this.posts[index].praise_count +=1
						} else {
							this.posts[index].is_praise = 0;
							this.posts[index].praise_count -=1
						}
						// this.posts[index].praise = res.data.praise
					}
					uni.showToast({
						title:res.data.msg,
						icon:'none'
					})
				})
			},
			comment(index) {
				this.showInput = true; //调起input框
				this.index = index;
				setTimeout(()=>{
					this.focus = true;
				},30)
			},
			reply(index, comment_index) {
				console.log(index,comment_index)
				return
				this.is_reply = true; //回复中
				this.showInput = true; //调起input框
				let replyTo = this.posts[index].reply[comment_index].nickname;
				this.input_placeholder = '回复' + replyTo;
				this.index = index; //post索引
				this.comment_index = comment_index; //评论索引
				this.focus = true;
			},
			blur: function() {
				setTimeout(()=>{
					this.init_input();
				},30)
			},
			send_comment: function(message) {
				console.log(this.posts[this.index])
				if(!message.content){
					uni.showToast({
						title:"请输入评论内容",
						icon:"none"
					})
					return
				}
				this.$ajax.post('news/comment.html',{id:this.posts[this.index].id,content:message.content},res=>{
					if(res.data.code == 1){
						this.posts[this.index].reply.unshift(res.data.comment)
					}
					uni.showToast({
						title:res.data.msg,
						icon:res.data.code==1?'success':'none',
						mask:true
					})
				})
				this.init_input();
			},
			init_input() {
				this.showInput = false;
				this.focus = false;
				this.input_placeholder = '评论';
				this.is_reply = false;
			},
			previewImage(imageList, image_index=0) {
				let urls
				// 判断是不是数组并进行处理
				if(!isArray(imageList)){
					urls = [formatImg(imageList,'w_8601')]
				}else{
					urls = imageList.map((item)=>{
						return formatImg(item,'w_8601')
					})
				}
				var current = urls[image_index];
				uni.previewImage({
					current: current,
					urls: urls
				});
			},
			previewVideo(url){
				this.$navigateTo('/vr/preview_video?url='+url)
			},
			delPost(id,index){ //删除帖子
				showModal({
					title:"提示",
					content:"确定要删除吗",
					confirm:()=>{
						console.log(id,index)
						this.$ajax.get('news/deletedCommunityReply',{id},res=>{
							console.log(res)
							if(res.data.code == 1){
								this.posts.splice(index,1)
							}
							uni.showToast({
								title:res.data.msg,
								icon:'none'
							})
						})
					}
				})
			},
			addPost(){
				this.$navigateTo('/user/community/add_post?id='+this.params.bid)
			}
		},
		onShareAppMessage(){
			if(this.share){
					return {
						title: this.share.title||"",
						content:this.share.content||"",
						imageUrl:this.share.pic?formatImg (this.share.pic,"w_6401"):""
					}
			}
		}
	}
</script>

<style lang="scss">
	@import url("../../static/css/community.css");
	.post_right .uname{
		margin-right: 4upx;
    font-weight: normal;
	}
	.looknum{
		color:#757575;
		font-size:22rpx;
	}
	.ding{
		font-size:$uni-font-size-sm;
		border-radius: 4upx;
		margin-left: 10upx;
		padding: 1upx 10upx;
		color: #f40;
		background-color: #ffda77
	}
	.add-post{
		position: fixed;
		right: 36upx;
		bottom: 180upx;
		height: 90upx;
		width: 90upx;
		border-radius: 50%;
		display:flex;
		align-items:center;
		justify-content:center;
		background-color: rgba($color: $uni-color-primary, $alpha: 0.9);
		z-index: 96;
	}
	.title-box{
		align-items: center;
		justify-content: space-between;
	}
	.title-box image{
		width: 38upx;
		height: 38upx;
		margin-left: 10upx;
	}
	.title-box .nickname{
		align-items: center;
	}
	.xs_money{
		margin-right: 60upx;
		color: #f65354
	}
	.xs_status{
		position: absolute;
		top: 0;
		right: 0;
		height: 0;
		width: 0;
		border-left: 100upx solid transparent;
	}
	.xs_status.red{
		border-top: 100upx solid #f65354;
	}
	.xs_status.gray{
		border-top: 100upx solid #999;
	}
	.xs_status_text{
		position: absolute;
		right: 0;
		top: 24upx;
		font-size: 22upx;
		letter-spacing:8upx;
		transform:rotate(45deg);
		color: #ffffff;
	}
	.lists-box{
		padding: 100upx 48upx 0;
	}
	#moments .moments__post .footer_content .adv{
		// padding: 0 15rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 3;
		overflow: hidden;

	}
	#moments .moments__post .footer_content .adv view{
		display: inline-block;
	}
	#moments .moments__post .footer_content{
		padding: 10upx;
	}
  #moments .moments__post .footer_content~.footer_content{
		padding-top: 0upx;
	}
	.nickimg{
		width: 50upx;
		height: 50upx;
		border-radius: 50%;
		margin-left: -16upx;

	}
	.nickimg:nth-of-type(1){
		margin-left: 0upx;
	}
	.flex-row{
		display: flex;
		flex-direction: row;
	}
	.top_nav{
		position: fixed;
		// #ifdef H5
		top: 44px;
		// #endif
		// #ifndef H5
		top: 0;
		// #endif
		z-index: 999;
		width: 100%;
		height: 90rpx;
		background-color: #fff;
		align-items: center;
		justify-content: space-between;
		.nav_item{
			flex: 1;
			height: 84rpx;
			margin:0 20rpx;
			line-height: 84rpx;
			border-bottom: 4rpx solid #fff;
			text-align: center;
			transition: 0.26s;
			&.active{
				color: $uni-color-primary;
				border-color: $uni-color-primary;
			}
		}
	}

</style>
