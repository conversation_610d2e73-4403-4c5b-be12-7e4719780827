<template>
	<view class="my-input flex-row" :class="{small:small, 'bottom-line': bottomLine}">
		<view class="input-row flex-1">
			<label v-if="label" for="" :style="{width:label_width}" @click="$emit('clickLabel')">
				<text class="label_text">{{label}}</text>
				<my-icon v-if="showLabelIcon" type="tishifu" color="#ff656b" size="28rpx"></my-icon>
				<slot name="label_right"></slot>
			</label>
			<view class="input flex-row">
				<input class="flex-1" :class="isColor==2?'input-hui':''"   :type="type" :maxlength="maxlength" :value="value" @input="$emit('input',$event.detail.value)" :placeholder="placeholder?placeholder:'请输入'+label" placeholder-style="color:#999999" :disabled="disabled" :password="type==='password'?true:false" @blur="blur" >
				<text v-if="unit" class="unit">{{unit}}</text>
			</view>
		</view>
		<my-icon v-if="show_arrow" type="ic_into" size="32rpx" color="#999"></my-icon>
		<!-- <slot></slot> -->
	</view>
</template>

<script>
	import myIcon from '../myIcon'
	export default {
		components:{
			myIcon
		},
		props:{
			isColor:Number,
			isColorto:Number,
			label:String,
			label_width:{
				type:[String],
				default:"auto"
			},
			disabled:{
				type:[Boolean,String],
				default:false
			},
			name:String,
			value:[String,Number],
			maxlength:{
				type:[String,Number],
				default:"140"
			},
			unit:String,
			show_arrow:Boolean,
			placeholder:String,
			type:{
				type:String,
				default:'text'
			},
			small:{
				type:[Boolean],
				default:false
			},
			bottomLine:{
				type:[Boolean],
				default:false
			},
			showLabelIcon:{
				type:[Boolean],
				default:false
			}
		},
		data() {
			return {
			
			};
		},
		methods:{
			blur(){
				uni.hideKeyboard()
			}
		}
	}
</script>

<style lang="scss">
view{
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}
.flex-row{
	flex-direction: row;
}
.my-input{
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 0;
	background-color: #fff;
}
	.small{
		.input-row{
			input{
				font-size: 30rpx;
			}
		}
	}
	.input-row{
		// display: flex;
		label{
			font-size: 22rpx;
			margin-bottom: 24rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			color: #666;
			.label_text{
				margin-right: 10rpx;
			}
		}
		input{
			font-size: 36rpx;
			margin-right: 10upx;
		}
		.unit{
			font-size: $uni-font-size-base;
			color: #999;
		}
		uni-icon{
			line-height: 62upx;
			margin-left: 10upx;
		}
	}
	.input-hui{
		background-color: #f5f5f5;
	}
</style>
