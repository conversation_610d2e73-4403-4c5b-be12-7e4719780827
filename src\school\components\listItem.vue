<template>
  <view
    class="item-box bottom-line"
    hover-class="item-hover"
    :hover-start-time="60"
    :hover-stay-time="120"
    @click="onClick()"
  >
    <view class="image-box">
      <image :src="image | imageFilter('w_240')" mode="aspectFill"></image>
    </view>
    <view class="info-box">
      <view class="title">
        <view class="text-box">
          <text class="text" :class="{ 'line-clamp_2': title_row === 2 }">{{
            title
          }}</text>
          <text class="text2">{{ sub_title }}</text>
        </view>
        <slot name="title_right" />
      </view>
      <view class="content">
        <view class="info">
          <view class="tags">
            <my-tag
              class="tag"
              v-for="(tag, index) in tags"
              :key="index"
              size="small"
              :type="tag.type"
            >
              {{ tag.name }}
            </my-tag>
          </view>
          <view class="desc" v-if="desc">{{ desc }}</view>
          <slot name="prominent" />
        </view>
        <view class="arrow">
          <my-icon type="jinru" color="#333" size="36rpx"></my-icon>
        </view>
      </view>
      <slot name="time" />
      <view class="address">
        <text class="text">{{ address }}</text>
        <my-icon
          v-if="distance"
          type="ic_map"
          color="#999"
          size="34rpx"
        ></my-icon>
        <text class="distance" v-if="distance">{{ distance }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from '@/components/icon'
import myTag from '@/components/myTag'
import {
  formatImg
} from '../../common/index.js'
export default {
  components: { myIcon, myTag },
  props: {
    image: {
      type: String,
      default: ''
    },
    title_row: {
      type: Number,
      default: 1
    },
    title: String,
    sub_title: String,
    tags: Array,
    desc: String,
    address: {
      type: String,
      default: ''
    },
    distance: {
      type: [String,Number],
      default: ''
    }
  },
  data() {
    return {}
  },
  filters: {
    imageFilter(val,param) {
      if (!val) {
        return ""
      }
      return formatImg(val, param)
    }
  },
  methods: {
    onClick() {
      this.$emit('click')
    }
  }
}
</script>

<style scoped lang="scss">
view{
  display: flex;
  flex-direction: column;
}
.item-box {
  flex-direction: row;
  overflow: hidden;
  padding: 30rpx;
  &.item-hover {
    background-color: $uni-bg-color-hover;
  }
  .image-box {
    width: 240rpx;
    height: 220rpx;
    border-radius: 10rpx;
    overflow: hidden;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .info-box {
    margin-left: 32rpx;
    flex: 1;
    overflow: hidden;
    .content {
      flex-direction: row;
      align-items: center;
      background-color: initial;
      .info {
        flex: 1;
        margin-right: 20rpx;
      }
    }
    .title {
      flex-direction: row;
      align-content: flex-start;
      justify-content: space-between;
      .text-box {
        flex-direction: row;
        align-items: flex-end;
        margin-bottom: 20rpx;
        .text {
          flex: 1;
          line-height: 1.5;
          font-size: $uni-font-size-lg;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          &.line-clamp_2 {
            -webkit-line-clamp: 2;
          }
        }
        .text2 {
          margin-bottom: 10rpx;
          margin-left: 10rpx;
          font-size: $uni-font-size-sm;
          color: #999;
        }
      }
    }
    .tags {
      display: inline-block;
      margin-bottom: 36rpx;
      .tag {
        margin-right: 8rpx;
      }
    }
    .desc {
      margin-bottom: 24rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      font-size: $uni-font-size-base;
      color: #666;
    }
    .address {
      flex-direction: row;
      align-items: center;
      overflow: hidden;
      font-size: $uni-font-size-base;
      color: #666;
      .text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .distance {
        margin-left: 5rpx;
      }
    }
  }
}
</style>
