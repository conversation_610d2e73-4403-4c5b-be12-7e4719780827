<template>
  <view>
    <view class="block">
      <view class="title flex-row">
        <template v-if ="levelid==1">
          <view class="flex-row" >
            <text class="label">上传图片/视频</text>
            <text class="link" @click="showCommunityPhoto">小区图库</text>
          </view>
        </template>
        <template v-else >
          <text>室内图/视频</text>
        </template>
        <text class="tip">最多上传10张{{photos.indoor_images.length+photos.indoor_videos.length}}/10</text>
      </view>
      <view class="upload-box">
        <my-upload @uploadDon="uploadDon1" :chooseType="3" :maxCount="10" :imgs="photos.indoor_images" :videos="photos.indoor_videos" :disabled ="cont.indoor_images && cont.indoor_images.length>=0  && cont.disabled?true :false"    :allowDel='cont.indoor_images && cont.indoor_images.length  && cont.disabled?false :true' ></my-upload>
      </view>
    </view>
    <template v-if ="levelid>1">
    <view class="block">
      <view class="title flex-row">
        <text>户型图</text>
        <text class="tip">最多上传3张{{photos.house_type.length}}/3</text>
      </view>
      <view class="upload-box">
        <my-upload @uploadDon="uploadDon2" :disabled ="cont.house_type && cont.house_type.length>=0  && cont.disabled?true :false" :allowDel='cont.house_type && cont.house_type.length  && cont.disabled?false :true' :chooseType="1" :maxCount="3" :imgs="photos.house_type"></my-upload>
      </view>
    </view>
    <view class="block">
      <view class="title flex-row">
        <view class="flex-row">
          <text class="label">室外图</text>
          <text class="link" @click="showCommunityPhoto">小区图库</text>
        </view>
        <text class="tip">最多上传10张{{photos.outdoor.length}}/10</text>
      </view>
      <view class="upload-box">
        <my-upload @uploadDon="uploadDon3" :disabled ="cont.outdoor && cont.outdoor.length>=0 && cont.disabled?true :false" :allowDel='cont.outdoor && cont.outdoor.length  && cont.disabled?false :true' :chooseType="1" :maxCount="10" :imgs="photos.outdoor"></my-upload>
      </view>
    </view>
    <view class="block">
      <view class="title flex-row">
        <text>设置封面图</text>
        <text class="tip">请点击选择封面图</text>
      </view>
      <view class="upload-box">
        <view class="add_box" @click="setCover">
           <image class="cover" mode="aspectFill" v-if="photos.cover_path" :src="photos.cover_path | imageFilter('w_320')"></image>
          <view class="icon-box" v-else>
            <my-icon type="ic_jia" size="120rpx" color="#DEDEDE" ></my-icon>
          </view>
        </view>
      </view>
    </view>
    </template>
    <my-popup ref="photo_list" position="bottom">
      <view class="photo_box">
        <view class="title">请点击选择封面图</view>
        <view class="photo_list flex-row">
          <image class="img_item" v-for="item in photos.indoor_images" :key="item" :src="item | imageFilter('w_320')" @click="selectCover(item)"></image>
          <image class="img_item" v-for="item in photos.house_type" :key="item" :src="item | imageFilter('w_320')" @click="selectCover(item)"></image>
          <image class="img_item" v-for="item in photos.outdoor" :key="item" :src="item | imageFilter('w_320')" @click="selectCover(item)"></image>
          <view class="img_item rech"></view>
        </view>
      </view>
    </my-popup>
    <my-popup ref="community_list" position="bottom">
      <view class="community_box">
        <view class="header flex-row">
          <text class="cancel" @click="hideCommunity()">取消</text>
          <text class="ok" @click="onSelectCommunityImg()">确定</text>
        </view>
        <scroll-view scroll-y class="community_list" @scrolltolower="onScrollBottom">
          <time-line custom :lineData="community_list">
            <template v-slot:default="{slotItem, slotIndex}">
              <view class="time">{{slotItem.ctime}}</view>
              <view class="img_list">
                <view class="img" v-for="(img, index) in slotItem.images" :key="index" @click="onClickCommunityImg(img)">
                  <image mode="aspectFill" :src="img | imageFilter('w_120')"></image>
                  <view class="icon" v-if="selected_imgs.includes(img)">
                    <my-icon type="wancheng" color="#fff" size="24rpx"></my-icon>
                  </view>
                </view>
              </view>
              <view class="desc flex-row">由<text class="highlight">{{slotItem.cname}}</text>上传至 {{siteName}} {{slotItem.community_name}}</text></view>
            </template>
          </time-line>
          <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
        </scroll-view>
      </view>
    </my-popup>
    <view class="btn-box">
      <view class="btn" @click="subData">完成</view>
    </view>
  </view>
</template>

<script>
import myUpload from '../components/form/myUpload'
import myIcon from '../components/myIcon'
import myPopup from '../components/myPopup'
import timeLine from '../components/timeLine'
import {uniLoadMore} from '@dcloudio/uni-ui'
import {mapState} from 'vuex'
export default {
  components: {
    myUpload,
    myIcon,
    myPopup,
    timeLine,
    uniLoadMore
  },
  data () {
    return {
      photos: {
        indoor_images:[],
        indoor_videos:[],
        house_type:[],
        outdoor:[],
        cover_path:""
      },
      community_params: {
        cid: "",
        page: 1,
        rows: 10
      },
      get_status:"loading",
      content_text:{
        contentdown:"",
        contentrefresh:"正在加载...",
        contentnomore:"没有更多数据了"
      },
      community_list: [],
      selected_imgs: [],
      levelid:0,
      cont:{},
    }
  },
  computed: {
    ...mapState(['siteName'])
  },
  onLoad(options){
    this.community_params.cid = options.cid||''
    this.levelid= options.levelid ||0
    if(this.$store.state.photos){
      for (let key in this.$store.state.photos){
        this.photos[key] = this.$store.state.photos[key]
        this.cont[key] =JSON.parse(JSON.stringify(this.$store.state.photos[key])) 
      }
    }
  },
  methods: {
    setCover(){
      if (this.cont.cover_path && this.cont.disabled) {
        uni.showToast({
          title:"不可修改",
          icon:"none"
        })
        return 
      }
      this.$refs.photo_list.show()
    },
    selectCover(item){
      this.photos.cover_path = item
      this.$refs.photo_list.hide()
    },
    showCommunityPhoto(){
      if(this.levelid==1 && this.cont.disabled){
          uni.showToast({
            title: "不可修改",
            icon: 'none'
          })
          return
      }
      if(this.levelid>1 && this.cont.disabled){
          uni.showToast({
            title: "不可修改",
            icon: 'none'
          })
          return
      }
      if(!this.community_params.cid){
        uni.showToast({
          title: "请先选择小区",
          icon: 'none'
        })
        return
      }
      this.community_params.page = 1
      this.getCommunityPhoto()
      this.$refs.community_list.show()
    },
    getCommunityPhoto(){
      if(this.community_params.page === 1){
        this.community_list= []
      }
      this.get_status = "loading"
      this.$ajax.get('release/communityImagesAgent', this.community_params, res=>{
        if(res.data.code === 1){
          this.community_list = this.community_list.concat(res.data.list)
          if(res.data.list.length<this.community_params.rows){
            this.get_status = "noMore"
          }else{
            this.get_status = "more"
          }
        }else{
          if(this.community_params.page === 1){
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
          }
          this.get_status = "noMore"
        }
      })
    },
    onScrollBottom(){
      if(this.get_status === "more"){
        this.community_params.page++
        this.getCommunityPhoto()
      }
    },
    onClickCommunityImg(img){
      var _index = this.selected_imgs.findIndex(item=>item === img)
      if(_index<0){
        this.selected_imgs.push(img)
      }else{
        this.selected_imgs.splice(_index, 1)
      }
      console.log(this.selected_imgs)
    },
    hideCommunity(){
      this.$refs.community_list.hide()
    },
    onSelectCommunityImg(){
      if(this.selected_imgs.length===0){
        uni.showToast({
          title: "请至少选择一张图片",
          icon: 'none'
        })
        return
      }
      this.selected_imgs.forEach(item=>{
        if(this.levelid>1){
          if(!this.photos.outdoor.includes(item)){
              this.photos.outdoor.push(item)
          }
        }else {
          if(!this.photos.indoor_images.includes(item)){
              this.photos.indoor_images.push(item)
          }
        }
          
      })
      this.$refs.community_list.hide()
    },
    subData(){
      if(this.photos.indoor_images.length+this.photos.indoor_videos.length===0){
        uni.showToast({
          title:"请至少上传一张室内图或视频",
          icon:'none'
        })
        return
      }
      // if(this.photos.house_type.length===0){
      //   uni.showToast({
      //     title:"请至少上传一张户型图",
      //     icon:'none'
      //   })
      //   return
      // }
      uni.$emit('transmit', this.photos)
      uni.navigateBack()
    },
    uploadDon1(e){
      if(e.type == 'video'){
        this.photos.indoor_videos = e.files
      }else if(e.type == 'image'){
        this.photos.indoor_images = e.files
      }
    },
    uploadDon2(e){
      this.photos.house_type = e.files
      console.log("2222")
    },
    uploadDon3(e){
      this.photos.outdoor = e.files
    }
  }
}
</script>

<style scoped lang="scss">
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row{
  flex-direction: row;
}
.block{
  padding: 10rpx 48rpx;
  background-color: #fff;
  .title{
    justify-content: space-between;
    align-items: center;
    font-size: 36rpx;
    margin-bottom: 24rpx;
    .label{
      font-size: 36rpx;
    }
    .link{
      position: relative;
      top: 6rpx;
      left: 12rpx;
      color: $uni-color-primary;
    }
  }
  .tip{
    font-size: 22rpx;
    color: #999;
  }
}

.add_box{
    width: 30%;
    margin-bottom: 5%;
    height: 0;
    padding-bottom: 30%;
    text-align: center;
    box-sizing: border-box;
    background-color: #f3f3f3;
    position: relative;
    .icon-box{
      width: 120rpx;
      height: 120rpx;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      margin: auto;
    }
    .cover{
      height: 100%;
      width: 100%;
      position: absolute;
    }
}

.btn-box{
  padding: 48rpx;
  .btn{
    height: 88rpx;
    line-height: 88rpx;
    background: #FB656A;
    box-shadow: 0 8rpx 32rpx 0 rgba(251,101,106,0.40);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    text-align: center;
  }
}

.photo_box{
  padding: 24rpx 48rpx;
  background-color: #fff;
  .title{
    margin-bottom: 24rpx;
  }
}
.photo_list{
  justify-content: space-between;
  flex-wrap: wrap;
  .img_item{
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 28rpx;
    &.rech{
      padding: 0;
      height: 0;
    }
  }
}
.community_box{
  padding: 24rpx 0 24rpx 24rpx;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  background-color: #fff;
  .header{
    padding: 12rpx 48rpx 48rpx 24rpx;
    justify-content: space-between;
    .cancel{
      color: #999;
    }
    .ok{
      color: $uni-color-primary;
    }
  }
}
.community_list{
  max-height: 70vh;
  .time{
    margin-bottom: 12rpx;
    font-size: 24rpx;
    color: #999;
  }
  .img_list{
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    .img{
      width: 140rpx;
      height: 140rpx;
      margin-right: 12rpx;
      border-radius: 6rpx;
      overflow: hidden;
      position: relative;
      overflow: hidden;
      .icon{
        position: absolute;
        right: -26rpx;
        top: -6rpx;
        transform: rotate(45deg);
        width: 80rpx;
        text-align: center;
        height: 36rpx;
        background-color: #3399ff;
        color: #fff;
         ::v-deep .icon-wancheng{
          line-height: 40rpx !important;
          transform: rotate(-45deg);
        }
      }
    }
    image{
      width: 100%;
      height: 100%;
    }
  }
  .desc{
    margin-top: 24rpx;
  }
  .highlight{
    color: $uni-color-primary;
  }
}
</style>