<template>
 
      <view class="topic_detail" :style="{backgroundColor:detail.bg_color}">
        <image mode="widthFix" class="head" v-if="detail.header_bg_img" :src="detail.header_bg_img" alt="">
          <!-- #ifdef H5 -->
          <view class="article-content" v-html="detail.content"></view>
          <!-- #endif -->
          <!-- #ifndef H5 -->
          <u-parse :html="detail.content" @linkpress="navigate" :tag-style="tagStyle"></u-parse>
          <!-- #endif -->
        <view class="group" v-if = "detail.appoint_show==1&&detail.appoint_placement==0">

          <view class="join" @click="toSubForme(0)">立即报名</view> 
        </view>
        <view class="groups" v-if = "detail.appoint_show==1&&detail.appoint_placement==1">

            <view class="fix-sign" @click="toSubForme(0)">
              <my-icon type="huodongxiangqu" size="40upx" color='#fff'></my-icon>
              <view class="sign-up">报名</view>
            </view>
            
        </view>
        <!-- 报名弹窗 -->
            <sub-form
              :sub_type="sub_type"
              sub_title="报名"
              show_policy
              :sub_mode="sub_mode"
              ref="sub_form"
              @onsubmit="handleSubForm"
              sub_submit="提交报名"
              :login_status="login_status"
            ></sub-form>
      </view>


</template>

<script>
import {formatImg,navigateTo} from '../common/index.js'
import {config} from '../common/config.js'
import uParse from '../components/Parser/index'
import {wxShare} from '../common/mixin'
import subForm from '../components/subForm'
import myIcon from '../components/myIcon'
export default {
    data(){
        return {
          id:"",
          title:"",
          detail :[],
          name:"",
          showFix:false,
          url:"",
          seo:{},
          share:{},
          tagStyle:{
            video:'max-width:100%'
          },
          sub_type:0,
          src:'/activity/timg.png'

          
        }
    },
    mixins: [wxShare],
    components:{
      uParse,
      subForm,
      myIcon
    },
    computed:{
        sub_mode() {
          return this.$store.state.sub_form_mode 
        },
        login_status() {
          return this.$store.state.login_status 
        },
    },
    onLoad(options){
      
        if (options.id){
          this.id =options.id
          this.title =options.title||""
        }
          uni.setNavigationBarTitle({
            title: this.title
          })
        this.getData()
    },
    filters: {
      imgUrl(val) {
        return formatImg(val, 'w_240')
      },
      imageFormat(val){
        return config.imgDomain+val
      },
      
    },
    methods:{
      getData(){
        this.$ajax.get('topic/detail.html',{id:this.id} , (res) => {
          // if(res.data.share&&ers.data.share.title){
          //   this.share = res.data.share
          //   this.getWxConfig()
          // }
          if (res.data.code ==1){
            let  detail =res.data.detail
            this.detail = detail
            // #ifdef H5 || MP-BAIDU
            // if (res.data.seo){
              this.seo ={
                title:res.data.detail.seo_title,
                keywords:res.data.detail.seo_keyword,
                description:res.data.detail.seo_desc,
              }
            // }
            // #endif
            uni.setNavigationBarTitle({
              title:this.detail.title
            })
            this.share={
              title:this.detail.share_title,
              content:this.detail.share_desc,
              pic:this.detail.share_img
            }
            this.getWxConfig()
            // switch (this.detail.tp_jumpid) {
            //     case '0':
            //       this.url=''
                  
            //       break;
            //     case "1":
            //         this.url ="/pages/sub_form/sub_form"
            //     break;
            //     case "2":
            //         this.url ="/home/<USER>/index"
            //     break;
              
            //     default:
            //       this.url =''
            //       break;
            // }
          }
        })
      },
      
      toSubForme(id) {
          switch (id) {
            case 0:
                this.sub_type=8
                this.$refs.sub_form.showPopup()
                console.log(this.sub_type);
                break;
            case "1":
                navigateTo(this.url+'?bid=' + this.detail.tp_bid+'&from=3'+"&leixing=0")
                break;
            case "2":
                navigateTo(this.url)
                break;
          
            default:
                this.showFix=true
                break;
          }
          
       
      },
      navigate(href, e) {
				console.log(href)
				// navigateTo(href)
			},
       handleSubForm(e) {
      //提交报名
      e.from = '专题页'
      e.type = this.sub_type || 0
      e.single_page = this.id
      this.$ajax.post('build/signUp.html', e, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          // 没开启引导登录模式或已经绑定手机号了
          if (this.sub_mode!==2||res.data.status === 3) {
            //提示报名成功
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            this.$refs.sub_form.closeSub()
          } else {
            this.$refs.sub_form.getVerify()
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    },
    onShareAppMessage() {
      return {
        title: this.share.title ||'',
        content: this.share.content || '',
        imageUrl:this.share.pic?formatImg(this.share.pic,'w_240'):""
      }
    
  }
}
</script>

<style lang="scss">
.topic_detail{
    background-repeat: no-repeat;
    background-size: cover;
    overflow-y: auto;
    background: #fff;
    box-sizing: border-box;
    padding: 20rpx 40rpx 80rpx;
    min-height:calc(100vh - 44px);
    .head{
      width: 100%;
    }
  .tp_bigimg{
      width:100%;
      height:60vw;
      margin-bottom: 20upx;
    
    image{
      width: 100%;
      height: 100%;
    }
  }
  .con{
      width: 100%;
      overflow: hidden;
      // height: 100vh;
      .title{
      text-align: center;
      font-size: 32upx;
      font-weight: 400;
      }
      
  }
  .group{
      position: fixed;
      bottom: 0upx;
      left: 0px;
      right: 0upx;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20upx 10upx;
      .join {
          text-align: center;
          background: #007AFF;
          color: #fff;
          font-size: 40upx;
          width: 80%;
          padding: 20upx;
          border-radius: 50upx;
      }
  }
}
.bottom_130{
    padding-bottom: 130upx;
}
.fixed{
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    top: 0;
    background:rgba(0,0,0,0.1);
    display: flex;
    justify-content: center;
    align-items: center;
}
.fixed form {
    padding: 20upx;
    width: 80%;
    background: #fff;
}
.label-row{
		padding-left: 34upx;
		font-size: 26upx;
		color: #999999
	}
.btn-box button.default.sub{
    background: #007AFF;
    border-radius: 50upx;
  }
.fix-sign{
    position: fixed;
    bottom: 200px;
    right: 0;
    width: 40px;
    padding: 10rpx;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    background-image: linear-gradient(294deg,#ff7e24,#f84c32 40%,#ed0246);
    border-radius: 50%;
    .sign-up{
      color: #fff;
    }
}
</style>