<template>
<view class="content">
  <view class="swiper-box">
    <swiper
    class="img-swiper"
    :indicator-dots="false"
    :circular="true"
    :duration="300"
    indicator-active-color="#f65354"
    @change="swiperChange"
    :current="swiperCurrent"
    >
      <swiper-item v-for="(vr, index) in vr_list" :key="index">
        <view class="img-box" @click="toVr(vr)">
          <image mode="aspectFill" :src="(vr.cover||detail.path) | imageFilter('w_8001')"></image>
          <image class="vr-icon" src="/static/icon/vr_b.png" mode="widthFix"></image>
        </view>
      </swiper-item>
      <swiper-item>
        <view class="img-box">
          <image mode="aspectFit" :src="detail.path | imageFilter('w_8001')" @click="preImg"></image>
        </view>
      </swiper-item>
    </swiper>
    <view class="cate-box" v-if="vr_list.length>0">
      <view class="cate-list flex-row">
        <view
          class="cate"
          @click="switchFocus('vr')"
          :class="cateActive == 'vr' ? 'active' : ''"
          >VR</view
        >
        <view
          class="cate"
          @click="switchFocus('img')"
          :class="cateActive == 'img' ? 'active' : ''"
          >图片</view
        >
      </view>
    </view>
  </view>
  <view class="title-box flex-row">
    <view class="title flex-row">
      <text class="status" :class="'status'+detail.status_id||''">{{detail.status}}</text>
      <text>{{detail.desc}}</text>
    </view>
    <view class="contrast" @click="addContrast(img_id)">
      <my-icon type="duibi" color="#ff656b" size="32rpx"></my-icon>
      <text>加入对比</text>
    </view>
  </view>
  <!-- 基础信息 -->
  <view class="block container">
    <view class="info-list flex-row">
      <view class="info-list-item">
        <view class="label">参考均价</view>
        <view class="data highlight">{{detail.jiage?detail.jiage+'元/㎡':'待定'}}</view>
      </view>
      <view class="info-list-item">
        <view class="label">建筑面积</view>
        <view class="data">约{{detail.mianji}}m²</view>
      </view>
      <!-- <view class="info-list-item">
        <view class="label">套内面积</view>
        <view class="data">{{detail.zhuangxiu||'不详'}}</view>
      </view> -->
      <view class="info-list-item">
        <view class="label">户型</view>
        <view class="data">{{detail.shi}}室{{detail.ting}}厅{{detail.wei}}卫</view>
      </view>
      <view class="info-list-item" v-if="detail.chaoxiang">
        <view class="label">户型朝向</view>
        <view class="data">{{detail.chaoxiang}}</view>
      </view>
      <view class="info-list-item" v-if="detail.jiage">
        <view class="label">月供</view>
        <view class="data flex-row" @click="$navigateTo(`/pages/calculator/calculator?total=${detail.jiage*detail.mianji*1/10000}&shoufubili=${build.down_pay_pct}`)">
          <text class="mgr10">房贷计算</text>
          <my-icon type="fangdaijisuan" size="32rpx" color="#ff656b"></my-icon>
        </view>
      </view>
      <view class="info-list-item">
        <view class="label" v-if="detail.brows">浏览</view>
        <view class="data">{{detail.brows||''}}</view>
      </view>
    </view>
  </view>
  <!-- 聊天咨询按钮 -->
  <view class="btn_list-box flex-row container" v-if="is_open_im&&build.open_adviser">
    <view class="btn-item">
      <chatBtn :login_status="login_status" @ok="getSendMsg($event, 1)">
        <view class="flex-row">
          <my-icon type="jiage" color="#ff656b" size="42rpx"></my-icon>
          <text>咨询房价</text>
        </view>
      </chatBtn>
    </view>
    <view class="btn-item">
      <chatBtn :login_status="login_status" @ok="getSendMsg($event, 2)">
        <view class="flex-row">
          <my-icon type="huxing" color="#ff656b" size="42rpx"></my-icon>
          <text>咨询户型</text>
        </view>
      </chatBtn>
    </view>
  </view>
  <!-- 获取优惠 -->
  <view class="coupon-box container" v-if="build_discount&&build_discount.content">
    <image class="bg_img" :src="'/images/new_icon/quan.png' | imageFilter('w_8601')"></image>
    <view class="coupon_container flex-row">
      <text class="coupon_name flex-1">{{build_discount.content}}</text>
      <view class="desc">
        <text class="btn" v-if="build_discount.group_id" @click="$navigateTo(`/pages/groups/detail?id=${build_discount.group_id}`)">获取优惠</text>
        <text class="btn" v-else @click="toSubForme(3, 5)">获取优惠</text>
        <text class="coupon_content" v-if="build_discount.endtime">结束时间{{build_discount.endtime}}</text>
      </view>
    </view>
  </view>
  <!-- 参考月贷 -->
  <view class="block container" v-if="detail.jiage&&detail.mianji">
    <view class="label">
      <text>参考月贷</text>
      <view class="icon-box" @click="showTip">
        <my-icon type="tishifu" color="#d8d8d8" size="28rpx"></my-icon>
      </view>
    </view>
    <view class="card">
      <view class="tab-box flex-row" :class="{right:loan_type === 1}">
        <view class="tab" :class="{ active: loan_type === 0 }" @click="loan_type = 0">等额本息</view>
        <view class="tab" :class="{ active: loan_type === 1 }" @click="loan_type = 1">等额本金</view>
      </view>
      <loan-details
        :tab="loan_type"
        :lilv="(borrow.shangdaililv||4.9)/100/12"
        :shoufubili="(build.down_pay_pct/10)||borrow.jiceng||3"
        :total="detail.jiage*detail.mianji*1/10000"
        :downPayments="(detail.jiage*detail.mianji * ((build.down_pay_pct/10)||borrow.jiceng||3)/10/10000).toFixed(0)"
      ></loan-details>
    </view>
  </view>
  <!-- 置业顾问 -->
  <view class="block container" v-if="adviser_list&&adviser_list.length>0">
    <view class="label">{{mountTitle}}</view>
    <view class="advier-list">
      <view
        class="adviser-item flex-row"
        v-for="item in adviser_list"
        :key="item.id"
        @click="consuDetail(item.adviser_id)"
      >
        <view class="header_img">
          <image mode="widthFix" :src="item.prelogo | imageFilter('w_120')"></image>
        </view>
        <view class="info">
          <view class="name flex-row">
            <text class="text">{{ item.cname || item.typename }}</text>
          </view>
          <view class="data">
            <text>{{ item.traffic_volume }}人咨询过他</text>
          </view>
        </view>
        <view class="adviser-right">
          <view class="btn-list flex-row">
            <view class="btn">
              <chat-btn
                :user_login_status="login_status"
                :user_id="item.mid||item.uid||item.id"
                :identity_id="item.adviser_id||item.uid||item.id"
                :from_type="4"
                @ok="advAsk"
              >
                <view class="icon-box">
                  <my-icon type="ic_zixun1" size="45rpx" color="#ff656c"></my-icon>
                </view>
              </chat-btn>
            </view>
            <view class="btn" v-if='(item.adviser_id&& switch_adviser_tel) || !item.adviser_id'>
              <tel-btn :user_id="item.mid||item.uid||item.id" :identity_id="item.adviser_id||item.uid||item.id" :tel="item.tel" @ok="handleTel($event, item)">
                <view class="icon-box">
                  <my-icon type="ic_dianhua1" size="45rpx" color="#ff656c"></my-icon>
                </view>
              </tel-btn>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 户型图 -->
  <view class="house_img-box block container" id="huxing" v-if="house_type_list.length>0">
    <view class="label">
      <text>本楼盘其他户型({{house_type_list.length}})</text>
    </view>
    <swiper class="house_img-list" :duration="300" :display-multiple-items="2" next-margin="88rpx">
      <swiper-item v-for="(item, idx) in house_type_list" :key="idx">
        <view class="swiper-item" @click="$navigateTo(`/pages/new_house/photo?bid=${bid}&img_id=${item.id}`)">
          <view class="img-box">
            <image :src="item.path  | imageFilter('w_320')" mode="aspectFill"></image>
            <image v-if="item.vr" class="vr-icon" src="/static/icon/vr_b.png" mode="widthFix"></image>
          </view>
          <view class="house_type">{{ item.desc }}</view>
          <view class="aligin-end flex-row">
            <text class="stw">{{ item.shi }}室{{ item.ting }}厅{{ item.wei }}卫</text>
            <text class="status" :class="item.status_class" v-if="item.sale_status">{{
              item.sale_status_text
            }}</text>
          </view>
          <view class="mj-box flex-row">
            <view class="flex-row">
              <text>约</text>
              <text class="mianji">{{ item.mianji }}㎡</text>
            </view>
            <view class="db_btn" @click.prevent.stop="addContrast(item.id)">+对比</view>
          </view>
        </view>
      </swiper-item>
      <swiper-item v-if="house_type_list.length < 2"></swiper-item>
    </swiper>
  </view>
  <!-- 底部操作菜单 -->
  <view class="bottom-bar flex-row">
    <view class="bar-left flex-row flex-1">
      <view v-if="current_adviser && current_adviser.mid && is_open_adviser && build.open_adviser" class="icon-btn" @click="consuDetail(current_adviser.id)">
        <image :src="current_adviser.prelogo | imageFilter('w_120')" class="header_img"></image>
        <text>{{current_adviser.cname || current_adviser.typename}}</text>
      </view>
      <view class="icon-btn" v-if="navs[0].is_show&&(navs[0].operation===1||navs[0].operation===2)" @click="toYuyue(3, navs[0])">
        <my-icon type="yuyue" color="#666" size="50rpx"></my-icon>
        <text>{{navs[0].name}}</text>
      </view>
      <view class="icon-btn" v-if="navs[0].is_show&&(navs[0].operation===3||navs[0].operation===4)" @click="cusList( navs[0].operation)">
        <my-icon type="ic_zixun" color="#666" size="50rpx"></my-icon>
        <text>{{navs[0].name}}</text>
      </view>
      <view class="icon-btn" v-if="navs[1].is_show===1" @click="toContrast()">
        <text class="badge" v-if="login_status>1&&contrastCount>0">{{contrastCount>99?'99+':contrastCount}}</text>
        <text class="badge" v-if="login_status<=1&&$store.state.temp_huxing_contrast_ids.length>0">{{$store.state.temp_huxing_contrast_ids.length>99?'99+':$store.state.temp_huxing_contrast_ids.length}}</text>
        <my-icon type="pk" color="#666" size="50rpx"></my-icon>
        <text>{{navs[1].name}}</text>
      </view>
    </view>
    <!-- 置业顾问按钮 -->
    <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show===1&&(navs[2].operation===1||navs[2].operation===2)" @click="toYuyue(3, navs[2])">{{navs[2].name}}</view>
    <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show===1&&(navs[2].operation===3||navs[2].operation===4)" @click="cusList(navs[2].operation)">{{navs[2].name}}</view>
    <!-- 咨询售楼处按钮 -->
    <view class="flex-1" :class="{alone:navs[2].is_show===0}" v-if="navs[3].is_show===1">
      <tel-btn :user_login_status="login_status" @ok="handleTel">
      <view class="bar-btn btn2">{{navs[3].name}}</view>
    </tel-btn>
    </view>
  </view>
  <my-popup ref="tip_popup" position="center" height="396rpx">
    <view class="tip_container">
      <view class="title">温馨提示</view>
      <view class="desc">由楼盘均价乘以面积计算得出，实际总价请咨询售楼处</view>
      <view class="btn" @click="$refs.tip_popup.hide()">好的，知道了</view>
    </view>
  </my-popup>
  <chat-tip></chat-tip>
  <sub-form :sub_type="sub_type" :sub_mode="sub_mode" ref='sub_form' @onsubmit="handleSubForm"></sub-form>
  <!-- #ifndef MP-WEIXIN -->
  <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip"></login-popup>
  <!-- #endif -->
  <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
</view>
</template>

<script>
import {
  formatImg,
} from '../../common/index.js'
import myIcon from '../../components/myIcon.vue'
import allTel from '../../common/all_tel.js'
import getChatInfo from '../../common/get_chat_info'
import chatBtn from '../../components/open-button/chatBtn'
import telBtn from '../../components/open-button/telBtn'
import loanDetails from "../../components/loanDetail.vue";
import myPopup from '../../components/myPopup.vue';
import subForm from '../../components/subForm'
// #ifndef MP-WEIXIN
import loginPopup from '../../components/loginPopup'
// #endif
import { config } from '../../common/index.js'
export default {
  data() {
    return {
      detail:{

      },
      borrow:{},
      build:{},
      build_discount:{},
      loan_type:0,
      mountTitle: "置业顾问",
      adviser_list:[],
      house_type_list:[],
      login_tip:'',
      sub_type: 0,
      contrastCount:0,
      current_adviser:{},
      current_adviser_id:'',
      bid: '',
      navs: [
        {},
        {},
        {},
        {}
      ],
      vr_list:[],
      swiperCurrent: 0,
      cateActive: 'img',
      tel_res: {},
      show_tel_pop: false
    }
  },
  components: {
      myIcon,
      chatBtn,
      telBtn,
      loanDetails,
      myPopup,
      subForm,
      // #ifndef MP-WEIXIN
      loginPopup
      // #endif
  },
  onLoad(options) {
    // app端没安装微信处理
    // #ifdef APP-PLUS
    if(!this.hasWechat){
      let webView =this.$mp.page.$getAppWebview()
      webView.setTitleNViewButtonStyle(0,{
        type:"none",
        width:0
      })
    }
    // #endif

    if (options.bid) this.bid = options.bid; 
    if (options.img_id) this.img_id = options.img_id; 
    if (options.sharer_advserid) this.sharer_advserid = options.sharer_advserid
    this.getData()
    this.getNav()
  },
   computed: {
    tel400jing(){
      return this.$store.state.tel400jing
    },
    is_open_im() {
        return this.$store.state.im.ischat 
    },
    is_open_middle_num() {
        return this.$store.state.im.istelcall 
    },
    hasWechat(){
      return this.$store.state.hasWechat
    },
    sub_mode() {
        return this.$store.state.sub_form_mode 
    },
    login_status() {
      return this.$store.state.user_login_status
    },
    switch_adviser_tel(){
      return this.$store.state.switch_adviser_tel
    }
  },
  methods: {
    getData() {
      this.$ajax.get('build/buildPicTypeDetail.html', {
        bid: this.bid,img_id:this.img_id
      }, (res) => {
        if(res.data.share){
          this.share = res.data.share
          this.getWxConfig()
        }
        if (res.data.code == 1) {
          if(res.data.vrs&&res.data.vrs.length>0){
            this.vr_list = res.data.vrs
            this.cateActive = 'vr'
          }
          this.detail = res.data.huxing
          this.build = res.data.build
          this.borrow=res.data.borrow
          for (const key in  this.borrow) {
            if (this.borrow.hasOwnProperty(key)) {
              const element = this.borrow[key];
                this.borrow[key]=Number(this.borrow[key])
            }
          }
          uni.setNavigationBarTitle({
            title:`${this.build.title||''}户型图`
          })
          this.build_discount = res.data.build_discount
          this.house_type_list = res.data.others
          this.contrastCount = res.data.contrastCount||0
          // 判断是不是置业顾问 且是不是当前置业顾问绑定的楼盘
          if (res.data.advinfo && res.data.advinfo.build_ids && res.data.advinfo.id) {
            //是置业顾问
            let builds = res.data.advinfo.build_ids.split(',')
            if (builds && builds.indexOf(this.bid.toString()) >= 0) {
              //是当前置业顾问绑定的楼盘
              this.isshare = 1
              this.now_user_is_adviser = 1
              this.current_adviser_id = res.data.advinfo.id
            } else {
              this.isshare = 0
              this.now_user_is_adviser = 0
              this.current_adviser_id = ''
            }
          } else {
            //不是置业顾问
            this.isshare = 0
          }

          // 处理置业顾问显示
          if (res.data.mountMembers.length>0) {
            this.mountTitle = res.data.mountTitle
            let list = res.data.mountMembers
            // let num = res.data.showCount //显示的置业顾问个数
            if (this.sharer_advserid != '') {
              //分享者信息处理
              const current_index = list.findIndex(item=>item.adviser_id === parseInt(this.sharer_advserid))
              if(current_index>=0){
                this.current_adviser = list.splice(current_index,1)[0]
                list.unshift(this.current_adviser)
              }
            } else if (this.now_user_is_adviser == 1) {
              //当前置业顾问绑定的楼盘 放在第一位
              const current_index = list.findIndex(item=>item.id === this.current_adviser_id)
              if(current_index>=0){
                this.current_adviser = list.splice(current_index,1)[0]
                list.unshift(this.current_adviser)
              }
            }else {
              this.current_adviser = {}
            }
            // this.adviser_list = list.splice(0, num) //显示的数组
            this.adviser_list = list
          } else {
            this.current_adviser = {}
          }
        } else {
          this.show =true
          uni.showToast({
            title: "未请求到数据",
            icon: "none"
          })
        }
        if(res.data.share){
          this.share = res.data.share
          this.getWxConfig()
        }
      }, (err) => {
        console.log(err)
        this.show =true
      })
    },
    swiperChange(e){
      this.swiperCurrent = e.detail.current
      if(this.swiperCurrent<this.vr_list.length){
        this.cateActive = "vr"
      }else{
        this.cateActive = 'img'
      }
    },
    switchFocus(type){
      this.cateActive = type
      if(type==='vr'){
        this.swiperCurrent = 0
      }else{
        this.swiperCurrent = this.vr_list.length
      }
    },
    toVr(e) {
      this.$navigateTo('/vr/detail?build_vrid=' + e.id)
    },
    getNav(){
      this.$ajax.get('build/buildNav.html',{bid:this.bid},res=>{
        if(res.data.code === 1){
          this.navs = res.data.navs
        }
      })
    },
    checkLogin(tip, callback) {
      this.$ajax.get('member/checkUserStatus', {}, res => {
        if (res.data.code === 1) {
          callback&&callback()
        }else if(res.data.status == 1){
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
        }else if(res.data.status == 2){
          this.$store.state.user_login_status = res.data.status
          this.login_tip = tip
          this.$refs.login_popup.showPopup()
        }
      })
    },
    // 和置业顾问发起聊天
    advAsk(e) {
      if (this.is_open_im == 0) {
        // 判断id是不是置业顾问id
        // 根据id查询出挂载列表中的置业顾问
        var current_adviser = this.adviser_list.find(item=>item.adviser_id===e.identity_id)
        if(e.identity_id == this.current_adviser.adviser_id||(current_adviser&&!current_adviser.isGuwen)){
          this.$navigateTo('/pages/consultant/detail?id=' + e.identity_id)
        }else{
          console.log("没开启聊天且不是置业顾问,不跳转详情")
        }
        return
      }
      // #ifdef MP-WEIXIN
      getChatInfo(e.user_id, 3, this.bid)
      //  #endif
      // #ifndef MP-WEIXIN
      this.checkLogin('为方便您及时接收消息通知，请输入手机号码', ()=>{
        getChatInfo(e.user_id, 4, this.bid)
      })
      //  #endif
    },
    // 拨打电话
    handleTel(e, options={}) {
      // 如果有身份id则拨打置业顾问电话
      if(e&&e.identity_id){
        if(options.isAgent){
          e.isAgent = 1
        }
        if(options.isAdviser){
          e.isAdviser = 1
        }
        this.callAdviserMiddleNumber(e)
        return
      }
      // 如果关闭显示楼盘电话且有置业顾问则拨打置业顾问电话，否则拨打楼盘电话
      if(this.build.use_middle_number===0&&this.build.open_adviser == 1 &&
      this.adviser_list.length > 0){
        this.callAdviserMiddleNumber(e)
      }else{
        this.callBuildMiddleNumber()
      }
    },
    // 拨打置业顾问虚拟号码
    callAdviserMiddleNumber(e) {
      console.log('拨打置业顾问虚拟号码')
      var call_adviser = {}
      if(e.identity_id){
        call_adviser = e
      }else if(this.current_adviser&&this.current_adviser.id){
        call_adviser = this.current_adviser
      }else if(this.adviser_list.length>0){
        call_adviser = this.adviser_list[0]
      }
      var user_id = call_adviser.user_id||call_adviser.mid||call_adviser.uid||call_adviser.id
      var identity_id = call_adviser.identity_id||call_adviser.adviser_id||call_adviser.uid||call_adviser.id
      var tel_type = ""
      if(call_adviser.isAgent){
        tel_type = 3
      }
      if(call_adviser.isAdviser){
        tel_type = 2
      }
      if (!call_adviser.isAgent &&!call_adviser.isAdviser) {
        tel_type = 0
      }
      this.callMiddleNumber(tel_type,identity_id,1,this.bid)
    },
    // 拨打楼盘虚拟号码
    callBuildMiddleNumber() {
      console.log('拨打楼盘虚拟号码')
      let phoneNumber=""
      if (this.build.tel) {
        phoneNumber = this.build.tel
      } else if (this.build.phone && this.build.sellmobile_part) {
        phoneNumber = this.build.phone + ',' + this.build.sellmobile_part.trim()
        if (this.tel400jing) {
          phoneNumber += '#'
        }
      }
      this.callMiddleNumber(1,this.bid,1,this.bid)
    },
    // 请求虚拟号接口
    callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
      this.tel_params = {
        type,
        callee_id,
        scene_type,
        scene_id,
        source,
        bid,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      // #ifdef MP-WEIXIN
      allTel(tel_params)
      // #endif
      // #ifndef MP-WEIXIN
      this.tel_params.intercept_login = true
      this.tel_params.fail = (res)=>{
        if(res.data.code === -1){
            this.$store.state.user_login_status = 1
            uni.removeStorageSync('token')
            this.$navigateTo('/user/login/login')
          }
          if(res.data.code === 2){
            this.$store.state.user_login_status = 2
            this.login_tip = '当前操作需要绑定手机号，请输入您的手机号'
            this.$refs.login_popup.showPopup()
          }
      }
      allTel(this.tel_params)
      // this.checkLogin('当前操作需要绑定手机号，请输入您的手机号', ()=>{
      //   allTel(id, mid, tel, type, from, source, info_id, isstatis, this.bid)
      // })
      // #endif
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    toYuyue(type, nav){
      if(nav.operation===2&&nav.group_id){
        // 跳转团购报名
        this.$navigateTo(`/pages/groups/detail?id=${nav.group_id}`)
      }else{
        this.toSubForme(type)
      }
    },
    
    toSubForme(type, operation) {
      this.sub_operation = operation || ''
      this.sub_type = type
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e) {
      //提交报名
      e.from = '楼盘页'
      e.bid = this.bid
      e.type = this.sub_type || ''
      if(this.sub_operation) e.operation = this.sub_operation
      this.$ajax.post('build/signUp.html', e, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          // 没开启引导登录模式或已经绑定手机号了
          if (this.sub_mode!==2||res.data.status === 3) {
            //提示报名成功
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            this.$refs.sub_form.closeSub()
          } else {
            this.$refs.sub_form.getVerify()
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    handleCloseLogin(){
      if(this.login_status===1){
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if(this.login_status===2){
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
    statistics(){
      this.$ajax.get("im/callUpStatistics", {
        id: this.current_adviser.mid,
        tel: this.current_adviser.tel,
        type: 4,
        info_id:this.bid 
      }, res => {
        console.log(res);
      })
    },
    consuDetail(id) {
      if (!id) return
      this.$navigateTo('/pages/consultant/detail?id=' + id)
    },
    //转到顾问列表
    cusList(operation) {
      if(operation===4){
        console.log("和置业顾问发起聊天")
        if(this.current_adviser&&this.current_adviser.id){
          var user_id = this.current_adviser.mid||this.current_adviser.uid||this.current_adviser.id
          var identity_id = this.current_adviser.adviser_id||this.current_adviser.uid||this.current_adviser.id
          this.advAsk({user_id:user_id,identity_id:identity_id})
        }else if(this.adviser_list.length>0){
          var user_id = this.adviser_list[0].mid||this.adviser_list[0].uid||this.adviser_list[0].id
          var identity_id = this.adviser_list[0].adviser_id||this.adviser_list[0].uid||this.adviser_list[0].id
          this.advAsk({user_id:user_id,identity_id:identity_id})
        }else{
          uni.showToast({
            title: "该楼盘还没有置业顾问",
            icon: 'none'
          })
        }
        return
      }
      if (!uni.getStorageSync('token')) {
        this.$navigateTo('/user/login/login')
        // this.reload = true
        return
      }
      this.$navigateTo('/pages/consultant/consuList?id=' + this.bid)
    },
    preImg() {
      uni.previewImage({
        urls: [formatImg(this.detail.path,'w_860')],
        current: 0
      })
    },
    getSendMsg(e, type) {
      // #ifdef MP-WEIXIN
      this.$ajax.get('im/getUserReply.html',{page_from:type,bid:this.bid},res=>{
        if(res.data.mid){
          this.$store.state.autoSendMsg = res.data.content||''
          console.log(this.$store.state.autoSendMsg)
          getChatInfo(res.data.mid, 3, this.bid)
        }
      })
      // #endif
      // #ifndef MP-WEIXIN
      this.checkLogin('当前操作需要绑定手机号，请输入您的手机号', ()=>{
        this.$ajax.get('im/getUserReply.html',{page_from:type,bid:this.bid},res=>{
            if(res.data.mid){
              this.$store.state.autoSendMsg = res.data.content||''
              getChatInfo(res.data.mid, 3, this.bid)
            }
          })
      })
      // #endif
    },
    // 加入对比
    addContrast(img_id){
      this.$ajax.get('build/addContrast.html',{img_id},res=>{
        if(res.data.code === -1){
          this.$store.state.user_login_status = 1
          // 检测是否已添加
          if(this.$store.state.temp_huxing_contrast_ids.includes(img_id)){
            uni.showToast({
              title:"该户型已经添加",
              icon:'none'
            })
            return
          }
          this.$store.state.temp_huxing_contrast_ids.push(img_id)
          return
        }
        if(res.data.code === 1){
          uni.showToast({
            title:res.data.msg
          })
          this.contrastCount ++
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      },err=>{

      },{disableAutoHandle:true})
    },
    toContrast(){
      if(this.login_status>1){
        this.$navigateTo('/contrast/house_list')
      }else{
        this.$navigateTo(`/contrast/house_list?no_login=1`)
      }
    },
    // #ifdef APP-PLUS
    appShare(type = 'WXSceneSession') {
      let _this =this
      uni.share({
        provider:'weixin',
        type: 0,
        title:_this.build.title+"户型图",
        scene: type,
        imageUrl: formatImg(_this.detail.path, 'w_220'),
        summary:_this.build.title+"户型图",
        href:`${config.apiDomain}/h5/pages/new_house/photo?bid=${this.bid}&img_id=${this.img_id}&sharer_advserid=${this.current_adviser_id}`,
        success: function (res) {
          // console.log("success:" + JSON.stringify(res));
          uni.showToast({
            title: '分享成功',
            icon: 'none'
          })
        },
        fail: function (err) {
          uni.showToast({
            title: '分享失败',
            icon: 'none'
          })
          console.log("fail:" + JSON.stringify(err));
        }
      })
    },
    // #endif
    showTip(){
      this.$refs.tip_popup.show()
    }
  },
  onShareAppMessage: function (res) {
    let that = this;
    if (res.from === 'button') {}
    return {
      title: this.build.title + this.detail.desc,
      path:`/pages/new_house/photo?bid=${this.bid}&img_id=${this.img_id}&sharer_advserid=${this.current_adviser_id}`,
      success: function (res) {
        console.log('成功', res)
      }
    }
  }
}
</script>

<style lang="scss">
.content{
  padding-bottom: 160rpx;
  color: #333;
  background-color:#fff;
}
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.container{
  margin: 0 48rpx;
}
.block{
  margin-top: 24rpx;
  >.label{
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    line-height: 1;
    padding: 24rpx 0;
    margin-bottom: 24rpx;
    font-size: 40rpx;
    font-weight: bold;
    .icon-box{
      padding: 6rpx 12rpx;
    }
  }
}

.swiper-box{
  height: 76vw;
  position: relative;
  .cate-box {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 24upx;
    display: block;
    text-align: center;
    font-size: 0;
    .cate-list {
      display: inline-block;
      border-radius: 6rpx;
      overflow: hidden;
      .cate {
        display: inline-block;
        padding: 8upx 20upx;
        font-size: 22rpx;
        background-color: #fff;
        &.active {
          background: linear-gradient(45deg, #fd9ea3, #fb656a);
          color: #fff;
        }
      }
    }
  }
}
.img-swiper{
  height: 100%;
  .img-box{
    height: 100%;
    position: relative;
    image{
      width: 100%;
      height: 100%;
    }
    .vr-icon{
      width: 16vw;
      width: 16vw;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
    }
  }
}


// 标题部分
.title-box{
  justify-content:space-between;
  align-items:flex-start;
  padding: 24rpx 48rpx;
  .title{
    flex: 1;
    display: block;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    font-size: 40rpx;
  }
  .contrast{
    position: relative;
    top: 10rpx;
    flex-direction: row;
    align-items: center;
    color: $uni-color-primary;
    >text{
      font-size: 22rpx;
      margin-left: 8rpx;
    }
  }
}

// 基础信息
.info-list{
  flex-wrap: wrap;
  .info-list-item{
    line-height: 1;
    margin-bottom: 48rpx;
    // padding: 10rpx 0;
    min-width: 30%;
    flex: 1;
    &.mgb0{
      margin-bottom: 0;
    }
    .label{
      font-size: 22rpx;
      margin-bottom: 20rpx;
      color: #999;
    }
    .highlight{
      color: $uni-color-primary;
    }
    .mgr10{
      margin-right: 10rpx;
    }
    .flex-row{
      align-items: center;
    }
  }
}

.status {
  line-height: 40rpx;
  padding: 0 8upx;
  position: relative;
  top: -5rpx;
  font-size: 28upx;
  border-radius: 4upx;
  background-color: #e3faf4;
  color: #fff;
  margin-right: 10rpx;
  display: inline-block;
}
.status1{
  background-color: #17bfff;
}
.status2{
  background-color: #53d2ab;
}
.status3{
  background-color: ff7213;
}
.status4{
  background-color: #666;
}



// 聊天按钮
.btn_list-box {
  margin-bottom: 24rpx;
  display: flex;
  .btn-item {
    padding: 20rpx 5rpx;
    flex: 1;
    align-items: center;
    justify-content: center;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.05);
    color: $uni-color-primary;
    ~ .btn-item {
      margin-left: 14rpx;
    }
    text {
      font-size: 32rpx;
      margin-left: 16rpx;
    }
  }
}


// 获取优惠
.coupon-box{
  height: 140rpx;
  line-height: 1;
  color: #fff;
  position: relative;
  .bg_img{
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
  .coupon_container{
    padding: 0 48rpx;
    align-items: center;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
  .coupon_name{
    flex: 1;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    margin-right: 16rpx;
  }
  .coupon_content{
    margin-top: 16rpx;
    font-size: 22rpx;
    flex: 1;
  }
  .btn{
    padding: 0 24rpx;
    height: 48rpx;
    line-height: 48rpx;
    border-radius: 24rpx;
    background-color: #fff;
    color: $uni-color-primary;
  }
}

// 参考月贷
.card {
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx 0 rgba(0, 0, 0, 0.15);
  margin-bottom: 28rpx;
  box-sizing: border-box;
  overflow: hidden;
  .tab-box {
    justify-content: space-between;
    margin-bottom: 10rpx;
    background-image: url('https://images.tengfangyun.com/images/new_icon/fd_tab.png');
    background-size: 100%;
    background-position-y: -24rpx;
    &.right{
      background-image: url('https://images.tengfangyun.com/images/new_icon/fd_tab2.png');
    }
    .tab {
      flex: 1;
      text-align: center;
      padding: 20rpx;
      color: #999;
      &.active {
        // background-color: #fff3f3;
        color: #333;
      }
    }
  }
}

// 置业顾问列表
.advier-list {
  background-color: #fff;
  .adviser-item {
    justify-content: space-between;
    align-items: flex-start;
    padding: 30rpx 0;
    .header_img {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 15rpx;
      overflow: hidden;
      background-color: #f3f3f3;
    }
    image {
      width: 100%;
    }
    .info {
      flex: 1;
      overflow: hidden;
      .name {
        display: flex;
        align-items: center;
        margin-bottom: 6rpx;
        .text {
          // flex: 1;
          font-size: 32rpx;
        }
      }
      .mgl-20 {
        margin-left: 20rpx;
      }
      .data {
        display: inline-block;
        margin-bottom: 6rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 22rpx;
        color: #999;
      }
    }

    .adviser-right {
      align-items: flex-end;
    }

    .btn-list {
      align-items: center;
      text {
        color: #999;
      }
      .btn {
        width: 64rpx;
        height: 64rpx;
        ~ .btn {
          margin-left: 30rpx;
        }
        .icon-box {
          width: 64rpx;
          height: 64rpx;
          justify-content: center;
          text-align: center;
          border-radius: 50%;
          background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
        }
        .icon {
          width: 64rpx;
          height: 64rpx;
        }
      }
    }
  }
}

// 户型图
.house_img-box {
  margin-top: 24rpx;
  margin-right: 0;
  .label{
    padding-right: 48rpx;
  }
  .house_img-list {
    height: 50vw;
    .swiper-item {
      margin-right: 24rpx;
      .img-box {
        height: 200rpx;
        position: relative;
      }
      image {
        width: 100%;
        height: 100%;
      }
      .vr-icon{
        width: 60rpx;
        width: 60rpx;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
      }
      .house_type {
        display: block;
        margin-top: 20rpx;
        font-size: 32rpx;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .aligin-end {
        align-items: flex-end;
      }
      .stw {
        align-items: center;
        margin-top: 10rpx;
        margin-right: 10rpx;
        font-size: 28rpx;
        color: #999;
      }
      .status{
        font-size: 22rpx;
        padding: 4rpx 10rpx;
      }
      .mj-box {
        align-items: center;
        justify-content:space-between;
      }
      .db_btn{
        line-height: 30rpx;
        padding: 0 8rpx;
        border-radius: 15rpx;
        font-size: 22rpx;
        border: 1rpx solid $uni-color-primary;
        color: $uni-color-primary;
      }
      .mianji {
        margin-left: 10rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: $uni-color-primary;
      }
    }
  }
}

// 底部菜单
.bottom-bar {
  background-color: #fff;
  height: 110rpx;
  padding: 15rpx 48rpx;
  left: 0;
  z-index: 10;
  .bar-left{
    padding-right: 48rpx;
    justify-content: flex-start;
  }
  .icon-btn {
    // width: 100rpx;
    align-items: center;
    padding: 0;
    margin: 0;
    background-color: #fff;
    line-height: initial;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // flex: 1;
    padding-right: 48rpx;
    overflow: hidden;
    position: relative;
    // & ~ .icon-btn {
    //   margin-left: 24rpx;
    // }
    .header_img{
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
    }
    text {
      line-height: 1;
      font-size: 22rpx;
      color: #999;
      display: inline-block;
      width: 100%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .badge{
      display: inline-block;
      box-sizing: border-box;
      width: auto;
      position: absolute;
      top: 0;
      left: 32rpx;
      // right: 38rpx;
      height: 28rpx;
      padding: 0 8rpx;
      min-width: 28rpx;
      border-radius: 14rpx;
      font-size: 22rpx;
      background-color: $uni-color-primary;
      color: #fff;
    }
  }
  .bar-btn {
    // width: 220rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    padding: 0;
    margin: 0;
    border-radius: 0;
    color: #fff;
    &.btn1 {
      background: #FBAC65;
      box-shadow: 0 0 4px 0 rgba(0,0,0,0.05);
      border-top-left-radius: 40rpx;
      border-bottom-left-radius: 40rpx;
    }
    &.btn2 {
      background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);;
      box-shadow: 0 0 4px 0 rgba(255,80,0,0.30);
      border-top-right-radius: 40rpx;
      border-bottom-right-radius: 40rpx;
    }
  }
}
.tip_container{
  width: 80%;
  height: 396rpx;
  box-sizing: border-box;
  padding: 24rpx 48rpx;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  border-radius: 16rpx;
  background-color: #fff;
  .title{
    text-align: center;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  .desc{
    font-size: 28rpx;
    color: #666;
  }
  .btn{
    padding: 24rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: $uni-color-primary;
  }
}
</style>
