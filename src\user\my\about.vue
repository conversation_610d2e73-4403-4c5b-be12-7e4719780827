<template>
  <view class="regInfos" v-html="content" >
      
  </view>
</template>

<script>
export default {
  data(){
    return {
      content:""
    }
  },
  onLoad(){
    this.getInfo()
  },
  methods: {
    getInfo(){
      this.$ajax.get('member/about.html', {}, (res) => {
        if (res.data.code ==1){
          const regex = new RegExp('<img', 'gi');
          const regex2 = new RegExp('style=""', 'gi');
          // 正则匹配处理富文本图片过大显示问题
          res.data.content = res.data.content.replace(regex2,"").replace(regex, `<img style="max-width: 100%;"`);
          this.content = res.data.content
        }
      })
    }
  },
}
</script>

<style>
  .regInfos{
    padding: 48rpx;
    line-height: 1.5;
    font-size: 32rpx;
    background: #fff;
  }
</style>