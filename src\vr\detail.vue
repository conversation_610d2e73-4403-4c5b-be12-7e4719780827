<template>
  <view>
    <!-- #ifdef H5 || APP-PLUS -->
    <web-view :src="url" :webview-styles="webviewStyles"></web-view>
    <!-- 优先使用分享者信息 -->
    <view v-if="(shareUserInfo.mid&&infoid)||(shareUserInfo.mid&&!infoid&&build_info&&build_info.open_vr==1) ||(shareUserInfo.mid&&cid) || (shareUserInfo.mid&&estateid)">
      <view class="flex-box bottom-bar user_box" v-if="position_info==3">
        <view class="flex-box user_info" @click="toShareUser()">
          <image class="user_header" :src="shareUserInfo.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
          <view class="name">
            <view class="user_name">{{shareUserInfo.cname}}</view>
            <view class="identity">{{params.sharetype==1?'置业顾问':'经纪人'}}</view>
          </view>
        </view>
        <view class="flex-box btn-group">
          <view class="tel_btn" @click="handleTel">拨打电话</view>
          <view class="chat_btn" @click="handleChat">在线咨询</view>
        </view>
        </view>
      <view class="contact" v-if="position_info!=3" :class="position_info=='1'?'contact_left':'contact_right'">
        <view @click="toShareUser()">
          <image class="user_header" :src="shareUserInfo.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
          <view class="user_name">{{shareUserInfo.cname}}</view>
        </view>
        <view @click="handleTel">
          <image class="contact_icon" :src="'/icon/vr/<EMAIL>'| img_icon"></image>
          <view>电话</view>
        </view>
        <view @click="handleChat">
          <image class="contact_icon" :src="'/icon/vr/<EMAIL>'| img_icon"></image>
          <view>微聊</view>
        </view>
      </view>
    </view>
    <!-- 如果是新房且有置业顾问 -->
    <view v-else-if="adviser_info.id&&!infoid&&build_info&&build_info.open_vr==1">
      <view class="flex-box bottom-bar user_box" v-if="position_info==3">
        <view class="flex-box user_info" @click="toAdviser()">
          <image class="user_header" :src="adviser_info.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
          <view class="name">
            <view class="user_name">{{adviser_info.cname||''}}</view>
            <view class="identity">置业顾问</view>
          </view>
        </view>
        <view class="flex-box btn-group">
          <view class="tel_btn" @click="handleTel">拨打电话</view>
          <view class="chat_btn" @click="handleChat">在线咨询</view>
        </view>
      </view>
      <view class="contact" v-if="position_info!=3" :class="position_info=='1'?'contact_left':'contact_right'">
        <view @click="toAdviser()">
          <image class="user_header" :src="adviser_info.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
          <view class="user_name">{{adviser_info.cname||''}}</view>
        </view>
        <view @click="handleTel">
          <image class="contact_icon" :src="'/icon/vr/<EMAIL>'| img_icon"></image>
          <view>电话</view>
        </view>
        <view @click="handleChat">
          <image class="contact_icon" :src="'/icon/vr/<EMAIL>'| img_icon"></image>
          <view>微聊</view>
        </view>
      </view>
    </view>

    <!-- 如果是二手房或出租房 -->
    <view v-else-if="agent_info.uid">
      <view class="flex-box bottom-bar user_box" v-if="position_info==3">
        <view class="flex-box user_info" @click="toAgent()">
          <image class="user_header" :src="agent_info.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
          <view class="name">
            <view class="user_name">{{agent_info.cname||''}}</view>
            <view class="identity" v-if="agent_info.zhongjie==2">经纪人</view>
          </view>
        </view>
        <view class="flex-box btn-group">
          <view class="tel_btn" @click="handleTel">拨打电话</view>
          <view class="chat_btn" @click="handleChat">在线咨询</view>
        </view>
      </view>
      <view class="contact" v-if="position_info!=3" :class="position_info=='1'?'contact_left':'contact_right'">
        <view @click="toAgent()">
          <image class="user_header" :src="agent_info.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
          <view class="user_name">{{agent_info.cname||''}}</view>
        </view>
        <view @click="handleTel">
          <image class="contact_icon" :src="'/icon/vr/<EMAIL>'| img_icon"></image>
          <view>电话</view>
        </view>
        <view @click="handleChat">
          <image class="contact_icon" :src="'/icon/vr/<EMAIL>'| img_icon"></image>
          <view>微聊</view>
        </view>
      </view>
    </view>
    <view  v-else-if="!infoid&&build_info&&build_info.id&&build_info.open_vr==1">
      <view class="flex-box bottom-bar" v-if="position_info==3">
        <view class="flex-1 flex-box text-center to-collect top-line right-line" @click="toHome()">
          <my-icon type="ic_shoye" color="#888" size="38rpx"></my-icon>
          <view class="text">首页</view>
        </view>
        <view class="flex-1 flex-box text-center to-share top-line" @click="handleTel">
          <my-icon type="ic_tel" color="#888" size="38rpx"></my-icon>
          <view class="text">电话</view>
        </view>
        <view class="flex-2 flex-box text-center to-tel" @click="toSubForme(3)">
          <my-icon type="ic_jiangjia" color="#ffffff" size="38rpx"></my-icon>
          <view class="text">获取优惠</view>
        </view>
      </view>
      <view class="contact" v-if="position_info!=3" :class="position_info=='1'?'contact_left':'contact_right'">
        <view @click="toHome()">
          <my-icon type="ic_shoye" color="#fff" size="50rpx"></my-icon>
          <view class="user_name icon_txt">首页</view>
        </view>
        <view @click="handleTel">
          <image class="contact_icon" :src="'/icon/vr/<EMAIL>'| img_icon"></image>
          <view>电话</view>
        </view>
        <view @click="toSubForme(3)">
          <image class="contact_icon" :src="'/icon/vr/<EMAIL>'| img_icon"></image>
          <view>获取优惠</view>
        </view>
      </view>
    </view>
    <view v-else-if="cid">
      <view class="flex-box bottom-bar" v-if="position_info==3">
      <view class="flex-1 flex-box text-center to-collect top-line right-line" @click="toHome()">
        <my-icon type="ic_shoye" color="#888" size="38rpx"></my-icon>
        <view class="text">首页</view>
      </view>
      <view class="flex-1 flex-box text-center to-share top-line" @click="handleTel">
        <my-icon type="ic_tel" color="#888" size="38rpx"></my-icon>
        <view class="text">电话</view>
      </view>
      <view class="flex-2 flex-box text-center to-tel" @click="toSubForme(3)">
        <my-icon type="ic_jiangjia" color="#ffffff" size="38rpx"></my-icon>
        <view class="text">获取优惠</view>
      </view>
      </view>
      <view class="contact" v-if="position_info!=3" :class="position_info=='1'?'contact_left':'contact_right'">
        <view @click="toHome()">
          <my-icon type="ic_shoye" color="#fff" size="50rpx"></my-icon>
          <view class="user_name icon_txt">首页</view>
        </view>
        <view @click="handleTel">
          <image class="contact_icon" :src="'/icon/vr/<EMAIL>'| img_icon"></image>
          <view>电话</view>
        </view>
        <view @click="toSubForme(3)">
          <image class="contact_icon" :src="'/icon/vr/<EMAIL>'| img_icon"></image>
          <view>获取优惠</view>
        </view>
      </view>
    </view>
    <sub-form
      :groupCount="groupCount"
      :sub_type="sub_type"
      :sub_mode="sub_mode"
      ref="sub_form"
      @onsubmit="handleSubForm"
    ></sub-form>
    <!-- <enturstBtn v-if="shareUserInfo.agent_id||shareUserInfo.adviser_id" :to_user="shareUserInfo" @click="$refs.enturst_popup.show()" />
		<my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
			<enturstBox @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="toLogin" :to_user="shareUserInfo" />
		</my-popup> -->
    <!-- 登录弹窗 -->
        <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
    <!-- #endif -->
    <!-- #ifdef MP -->
    <web-view v-if="vrlink_ischeck" :src="url" :webview-styles="webviewStyles"></web-view>
    <view v-else class="tip-box" @longtap="handleCopy()">
      <view>{{ vrUrl }}</view>
      <view style="margi-top:10upx;">长按复制全景图地址在浏览器打开查看</view>
    </view>
    <!-- #endif -->
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
  </view>
</template>

<script>
import myIcon from '../components/myIcon.vue'
import { config } from '../common/index'
import allTel from '../common/all_tel.js'
import getChatInfo from '../common/get_chat_info'
import subForm from '../components/subForm'
// import enturstBtn from '@/components/enturstBtn'
// import enturstBox from '@/components/enturstBox'
import myPopup from "../components/myPopup.vue";
import loginPopup from '@/components/loginPopup'
export default {
  data() {
    return {
      url: '',
      webviewStyles: {
        progress: {
          color: '#f65354'
        }
      },
      infoid:'',
      estateid: '',
      vrUrl: '',
      sub_type: 0,
      groupCount: '',
      build_info: {},
      adviser_info: {},
      position_info:3,
      agent_info: {},
      currentUserInfo: {},
      shareUserInfo: {},
      login_tip: '',
      params:{
        sid:'',
        sharetype:'',
      },
      cid:'',
      tel_res: {},
      show_tel_pop: false,
    }
  },
  components: {
    myIcon,
    subForm,
    // enturstBtn,
    // enturstBox,
    loginPopup,
    myPopup
  },
  computed: {
    vrlink_ischeck() {
      return this.$store.state.vrlink_ischeck
    },
    sub_mode() {
      return this.$store.state.sub_form_mode
    },
    is_open_chat() { //是否全局开启聊天
      return this.$store.state.im.ischat
    },
  },
  filters:{
    img_icon(val){
      return config.imgDomain+val+'?x-oss-process=style/m_240'
    }
  },
  onLoad(options) {
    if (options.shareId){
      this.params.sid=options.shareId,
      this.params.sharetype=options.shareType
      this.params.forward_time=options.f_time||''
    }

    // 楼盘信息中的vr
    if (options.buildid) {
      this.id = options.buildid
      this.params.id = this.id
      this.vrUrl = config.apiDomain + '/m/vr/detail?buildid=' + this.id
      this.api = "build/vrDetail.html"
      this.getVr()
    }
    // 楼盘绑定的vr
    if (options.build_vrid) {
      this.build_vrid = options.build_vrid
      this.params.id = this.build_vrid
      this.vrUrl = config.apiDomain + '/m/vr/detail?build_vrid=' + this.build_vrid
      this.api = "build/vrDetailNew.html"
      this.getVr()
    }
    // 二手房或租房
    if (options.infoid) {
      this.infoid = options.infoid
      this.params.id = this.infoid
      this.vrUrl = config.apiDomain + '/m/vr/detail?infoid=' + this.infoid
      this.api = "house/vrDetail.html"
      this.getVr()
    }
    // 商业地产
    if (options.estateid) {
      this.estateid = options.estateid
      this.params.id = this.estateid
      this.vrUrl = config.apiDomain + '/m/vr/detail?id=' + this.estateid
      this.api = "estate/vrDetail"
      this.getVr()
    }
    // 小区
    if (options.cid) {
      this.cid = options.cid
      this.params.id = this.cid
      this.vrUrl = config.apiDomain + '/m/vr/detail?id=' + this.cid
      this.api = "community/vrDetail"
      this.getVr()
    }
  },
  methods: {
    handleCopy() {
      uni.setClipboardData({
        data: this.vrUrl,
        success: function() {
          uni.showToast({
            title: '复制成功，请到浏览器粘贴访问',
            icon: 'none'
          })
        }
      })
    },
    getVr() {
      this.$ajax.get(this.api, this.params, res => {
        if (res.data.code != 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
          return
        }
        this.currentUserInfo=res.data.shareUser
        if (this.currentUserInfo.adviser_id>0){
            this.currentUserInfo.shareType=1
            this.currentUserInfo.sid=this.currentUserInfo.adviser_id
        }else if (this.currentUserInfo.agent_id>0) {
          this.currentUserInfo.shareType=2
          this.currentUserInfo.sid=this.currentUserInfo.agent_id
        }else {
          this.currentUserInfo={
            sid:this.sid,
            shareType:this.shareType
          }
        }
        this.shareUserInfo=res.data.share_user
        if(this.infoid || this.estateid){
          uni.setNavigationBarTitle({
            title: `${res.data.info.title}-VR看房`
          })
          this.url = res.data.info.vr
          this.agent_info = res.data.info
          if(res.data.share){
            this.share = res.data.share
          }else{
            this.share = {
              title: res.data.info.title,
              content:`【${res.data.info.title}】720°VR全景看房`,
              pic: res.data.info.img_path
            }
          }
          let time =parseInt(+new Date()/1000)
          if(this.currentUserInfo.sid){
            let link = `${window.location.origin}/h5/vr/detail?infoid=${this.infoid}&shareType=${this.currentUserInfo.shareType}&shareId=${this.currentUserInfo.sid}&f_time=${time}`
            this.share.link = link
          }
        }else if (this.cid){
          this.url = res.data.vr.path
          uni.setNavigationBarTitle({
            title: `${res.data.community.name}-VR看房`
          })
          if(res.data.share){
            this.share = res.data.share
          }else{
            this.share = {
              title: res.data.community.name,
              content:`【${res.data.community.name}】720°VR全景看房`,
              pic: res.data.community.img_path ||''
            }
          }
        }else{
          uni.setNavigationBarTitle({
            title: res.data.vrs.title || `${res.data.build.title}VR看房`
          })
          this.build_info = res.data.build
          if(res.data.buildAdviser){
            this.adviser_info = res.data.buildAdviser
          }
          this.groupCount = res.data.build.groupCount
          if(!res.data.buildVr){
            res.data.buildVr = {}
          }
          this.url = res.data.vrs.path||res.data.buildVr.path||res.data.vrs[0]
          if(res.data.share){
            this.share = res.data.share
          }else{
            this.share = {
              title:res.data.vrs.title||res.data.buildVr.title||`【${res.data.build.title}】720°VR全景看房 安家置业不打烊`,
              content:res.data.vrs.descp||res.data.buildVr.descp||"换一个角度看"+res.data.build.title,
              pic:res.data.vrs.cover||res.data.buildVr.cover||res.data.build.img
            }
          }
          let time =parseInt(+new Date()/1000)
          if(this.currentUserInfo.sid){
            let link = `${window.location.origin}/h5/vr/detail?${this.id?'buildid':(this.build_vrid?'build_vrid':'')}=${this.id||this.build_vrid}&shareType=${this.currentUserInfo.shareType}&shareId=${this.currentUserInfo.sid}&f_time=${time}`
            this.share.link = link
          }
        }
        this.position_info=res.data.vr_contacts_position
        this.getWxConfig()
      })
    },
    toHome() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    },
    toSubForme(type) {
      this.sub_type = type
      this.$refs.sub_form.showPopup()
    },
    handleSubForm(e) {
      e.from = '楼盘页'
      e.bid = this.build_info.id
      e.type = this.sub_type || ''
      this.$ajax.post('build/signUp.html', e, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          if (this.sub_mode !== 2 || res.data.status === 3) {
            //提示报名成功
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            this.$refs.sub_form.closeSub()
          } else {
            this.$refs.sub_form.getVerify()
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    toLogin(){
      if (this.$store.state.user_login_status==1){
        uni.removeStorageSync('token')
        uni.setStorageSync('backUrl', window.location.href)
        this.$navigateTo("/user/login/login")
      }else if (this.$store.state.user_login_status==2){
        this.showLoginPopup('为方便您及时接收消息通知，请输入手机号码')
      }
    },

    showLoginPopup(tip){
        this.login_tip = tip
        this.$refs.login_popup.showPopup()
    },
    handleCloseLogin() {
        if (this.$store.state.user_login_status === 1) {
            uni.removeStorageSync('token')
            this.$navigateTo('/user/login/login')
        }
        if(this.$store.state.user_login_status===2){
            this.$navigateTo('/user/bind_phone/bind_phone')
        }
    },
    onLoginSuccess(res){
        this.$store.state.user_login_status = 3
        if(this.weituo_is_show){
            console.log("登录成功后继续执行委托接口")
            this.$refs.enturst_box.handleEnturst()
        }
    },

    handleTel() {
      this.tel_params = {
        type: '',
        callee_id: '',
        scene_type:'',
        scene_id: '',
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      if(this.shareUserInfo.mid){//分享
        this.tel_params.type = this.params.sharetype==2?"3":"2"
        this.tel_params.callee_id = this.shareUserInfo.mid
        this.tel_params.scene_id = this.agent_info.id
        if (this.adviser_info.id) {
          this.tel_params.scene_type = 1
          this.tel_params.scene_id = this.build_info.id
        }
        if(this.agent_info.uid){
          if(this.estateid){ 
            this.tel_params.scene_type = 6
          }else{
            this.tel_params.scene_type = 4  
          }
        }
        allTel(this.tel_params)
        return
      }
      if(this.adviser_info.id){ //楼盘置业顾问
        this.tel_params.type = "2"
        this.tel_params.callee_id = this.adviser_info.id
        this.tel_params.scene_type = 1
        this.tel_params.scene_id = this.build_info.id
        // this.tel_params.bid = this.build_info.id
        allTel(this.tel_params)
        return
      }
      if(this.agent_info.uid){
        if(this.estateid){ //商业信息
          this.tel_params.type = 6
          this.tel_params.callee_id = this.agent_info.id
          this.tel_params.scene_type = 6
          this.tel_params.scene_id = this.agent_info.id
        }else{ //二手房租房
          this.tel_params.type = "3"
          this.tel_params.callee_id = this.agent_info.uid
          this.tel_params.scene_type = 4
          this.tel_params.scene_id = this.agent_info.id
        }
        allTel(this.tel_params)
        return
      }
      if ((this.build_info.phone && this.build_info.sellmobile_part) || this.build_info.tel) {
        this.tel_params.type = '1'
        this.tel_params.callee_id = this.build_info.id
        this.tel_params.scene_type = 1
        this.tel_params.scene_id = this.build_info.id
        allTel(this.tel_params)
      } else {
        uni.showToast({
          title: '此楼盘没有绑定联系电话',
          icon: 'none'
        })
      }
    },
    handleChat(){
      if (!uni.getStorageSync('token')) {
          this.$navigateTo('/user/login/login')
          return
      }
      if (this.is_open_chat == 0) {
        this.toShareUser()
        return
      }
      if (this.shareUserInfo.mid){
        getChatInfo(this.shareUserInfo.mid, 28, this.build_info.id||'')
        return 
      }
      if(this.adviser_info.mid){
        getChatInfo(this.adviser_info.mid, 28, this.build_info.id||'')
        return
      }
      if(this.agent_info.uid){
        getChatInfo(this.agent_info.uid, 28)
        return
      }
      
    },
    toAdviser(){
      this.$navigateTo('/pages/consultant/detail?id=' + this.adviser_info.id)
    },
    toAgent(){
      if(this.agent_info.zhongjie==2){
        this.$navigateTo('/pages/agent/detail?id=' + this.agent_info.uid)
      }
    },
    toShareUser(){
      if (this.params.sharetype == 1) { //置业顾问
        this.$navigateTo('/pages/consultant/detail?id=' + this.shareUserInfo.adviser_id)
      } else if (this.params.sharetype == 2) {
        this.$navigateTo('/pages/agent/detail?id=' + this.shareUserInfo.agent_id)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// #ifdef MP
.tip-box {
  margin-top: 120upx;
  padding: 20upx;
  text-align: center;
  color: #666;
}
// #endif

// #ifdef H5 || APP-PLUS
.contact{
    position: fixed;
    bottom: 310rpx;
    z-index: 9;
    color:#fff;
    text-align: center;
    padding: 10rpx 10rpx;
    border-radius: 10rpx;
    background: rgba(0,0,0,0.3);
    .icon_txt{
      margin-top: 8rpx;
    }
    .user_header{
      width: 96rpx;
      height: 96rpx;
      border-radius: 50%;
      object-fit: cover;
      border: 4rpx solid #fff;
      margin-bottom: 4rpx;
      box-sizing: border-box;
    }
    .contact_icon{
      width: 48rpx;
      height: 48rpx;
      margin-top: 30rpx;
    }
}
.contact_left{
    left: 22rpx;
}
.contact_right{
    right: 22rpx;
}
.bottom-bar {
  background-color: #fff;
  height: 88rpx;
  line-height: 88rpx;
  width: auto;
  left: 120rpx;
  right: 20rpx;
  bottom: 20rpx;
  border-radius:26rpx;
  overflow: hidden;
  align-items: center;
  &.user_box{
    justify-content: space-between;
    box-sizing: border-box;
    border-radius: 20rpx;
    padding: 12rpx 18rpx;
    line-height: 1;
    background-color: rgba($color: #000000, $alpha: 0.32);
    color: #fff;
    .user_info{
      align-items: center;
      margin-right: 24rpx;
      max-width: 220rpx;
      .name{
        flex: 1;
        overflow: hidden;
      }
    }
    .user_header{
      width: 64rpx;
      height: 64rpx;
      margin-right: 16rpx;
      border-radius: 50%;
    }
    .user_name{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 8rpx;
      font-size: 28rpx;
    }
    .identity{
      display: inline-block;
      line-height: 28rpx;
      font-size: 20rpx;
      border-radius: 8rpx;
      padding: 0 12rpx;
      background-image: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
      box-shadow: 0 0 8rpx 0 rgba(255,80,0,0.30);
    }
    .btn-group{
      width: 350rpx;
      justify-content: space-between;
    }
    .tel_btn{
      line-height: 64rpx;
      width: 160rpx;
      max-width: 160rpx;
      text-align: center;
      border-radius: 32rpx;
      font-size: 28rpx;
      color: #fff;
      background-image: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
      box-shadow: 0 0 4px 0 rgba(255,80,0,0.30);
    }
    .chat_btn{
      line-height: 64rpx;
      width: 160rpx;
      max-width: 160rpx;
      text-align: center;
      border-radius: 32rpx;
      font-size: 28rpx;
      color: #fff;
      background:  #65AEFB;
      box-shadow: 0 0 4px 0 rgba(0,0,0,0.05);
    }
  }
}
.bottom-bar my-icon {
  line-height: 1;
  margin-right: 10upx;
}
.bottom-bar .text{
  margin-left: 6rpx;
}
.to-tel {
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: $uni-color-primary;
}
.to-collect {
  align-items: center;
  justify-content: center;
  color: #666666;
  background-color: #fff;
}
.to-share {
  align-items: center;
  justify-content: center;
  color: #666666;
  background-color: #fff;
}
// #endif
</style>
