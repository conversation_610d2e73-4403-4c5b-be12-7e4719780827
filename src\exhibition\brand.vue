<template>
  <div class="introduce" :class="{ in_pc: !$store.state.in_mobile }">
    <div v-if="brand_type === 1" class="article-content" v-html="detail"></div>
    <div v-if="brand_type === 2" class="brand-content">
      <BrandList :list="brand_list" />
      <!-- <component :is="brand_component" :list="brand_list"></component> -->
    </div>
    <BottomBar pagePath="/exhibition/brand" :query="query" :id="id"/>
  </div>
</template>

<script>
import BottomBar from "./components/BottomBar.vue";
import BrandList from "./components/BrandList.vue";
export default {
  name: "",
  components: {
    BottomBar,
    BrandList,
  },
  data() {
    return {
      detail: "",
      query: "",
      brand_type: 1,
      brand_list: []
    };
  },
  // computed:{
  //   brand_component(){
  //     if(this.brand_type === 2){
  //       return () => ({
  //         component: import(/* webpackChunkName: "brandList" */ "./components/BrandList.vue"),
  //         timeout: 30000
  //       })
  //     }else{
  //       return ''
  //     }
  //   }
  // },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.query = "?id=" + this.id;
      this.getData(this.id);
    }
  },
  methods: {
    getData(id) {
      this.$ajax.get("buildShow/brandHouse", { id }, (res) => {
        if (res.data.code === 1) {
          this.brand_type = res.data.brand_house_type
          if(res.data.brand_house_type === 2){
            this.brand_list = res.data.content
            return
          }
          this.detail = res.data.content;
          this.$nextTick(() => {
            let imgs = document.querySelectorAll(".article-content img");
            let imgArr = [];
            let _this = this;
            for (let i = 0; i < imgs.length; i++) {
              imgArr.push(imgs[i].src);
              imgs[i].addEventListener("click", function () {
                _this.preImg(this.src, imgArr);
              });
            }
          });
        } else {
          uni.showToast({
            title: res.data.msg,
          });
        }
      });
    },
    preImg(nowImg, imgArr) {
      uni.previewImage({
        current: nowImg,
        indicator: "number",
        urls: imgArr,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.introduce {
  min-height: 100vh;
  &.in_pc {
    max-width: 414px;
    margin: auto;
  }
}
.brand-content{
  background-color: #fff;
  min-height: calc(100vh - 50px);
  box-sizing: border-box;
  padding: 48rpx 0;
}
.article-content {
  background-color: #fff;
  min-height: calc(100vh - 50px);
  box-sizing: border-box;
  padding: 48rpx;
  font-size: 28rpx;
}
.article-content  ::v-deep p {
  margin-bottom: 30upx !important;
}
.article-content  ::v-deep video {
  max-width: 100%;
  margin-bottom: 20upx;
}
</style>
