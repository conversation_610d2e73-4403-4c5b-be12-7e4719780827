<template>
  <view>
    <view
      class="list-item flex-box"
      :class="{ 'round-box': type === 'round','bottom-line':showBottomLine}"
      @click="goTo()"
    >
      <view class="img-box list-img">
        <image :src="itemData.img | imgUrl" lazy-load mode="aspectFill"></image>
        <image v-if="itemData.has_vr"  class="video-icon" :src="'/static/icon/vr.png'" mode="aspectFill"></image>
      </view>
      <view class="list-info flex-1">
        <view class="info-title" v-if="type === 'square'">
          <text class="title_text">{{ itemData.title }}</text>
					<text class="area_name">{{ itemData.areaname || "" }}</text>
        </view>
        <view class="info-content" v-if="type === 'square'">
          <text>售{{ itemData.count || 0 }}套</text>
          <text style="margin-left:15rpx"
            >租{{ itemData.cz_count || 0 }}套</text
          >
        </view>
        <view class="info-content" v-if="type === 'round'"
          >售{{ itemData.count || 0 }}套 租{{ itemData.cz_count || 0 }}套</view
        >
        <view class="price-data" @click.stop.prevent="toTrend(itemData.id)">
          <text class="small_text">比上月</text>
          <block v-if="itemData.house_status == 1">
            <my-icon type="ic_sanjiao_up_p" size="11" color="#f65354"></my-icon>
            <text class="up">{{ itemData.house_value }}%</text>
          </block>
          <block v-else-if="itemData.house_status == 2">
            <my-icon type="ic_sanjiao_d_n" size="11" color="#179B16"></my-icon>
            <text class="down">{{ itemData.house_value }}%</text>
          </block>
          <block v-else>
            <text class="small_text" style="margin-left:10rpx">持平</text>
          </block>
        </view>
        <view class="flex-box info-price hourse-price">
          <text class="small_text">{{ itemData.hot_points || 0 }}人看过</text>
          <view class="flex-box price">
            <text class="small_text" v-if="type === 'round'">均价约</text>
            <text class="total" v-if="cate == '1'">{{
              itemData.avg_price | format
            }}</text>
            <text class="total" v-if="cate == '2'">{{
              itemData.cz_avg_price | format
            }}</text>
            <text class="unit" style="margin-left: 0;">{{
              cate == 1 ? "元/m²" : "元/月"
            }}</text>
          </view>
          <text
            class="small_text"
            v-if="itemData.distance && type == 'square' && showDistance"
            >{{ itemData.distance }}km</text
          >
        </view>
      </view>
      <uni-icons v-if="type === 'round'" type="arrowright"></uni-icons>
    </view>
  </view>
</template>

<script>
import { navigateTo, formatImg } from "../common/index.js";
import { uniList, uniListItem, uniIcons } from "@dcloudio/uni-ui";
import myIcon from "./icon.vue";
export default {
  props: {
    itemData: {
      type: Object,
      default: {},
    },
    allowToTrend: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "square",
    },
    cate: {
      //1：二手房，2：出租房
      type: [String, Number],
      default: "1",
    },
    showDistance: {
      type: Boolean,
      default: true,
    },
    showBottomLine:{
      type: Boolean,
      default: true,
    }
  },
  components: {
    uniList,
    uniListItem,
    myIcon,
    uniIcons,
  },
  data() {
    return {};
  },
  filters: {
    format(val) {
      let newVal = parseInt(val);
      if (isNaN(newVal)) {
        return val || "不详";
      }
      return newVal;
    },
    imgUrl(val) {
      return formatImg(val, "w_240");
    },
  },
  methods: {
    goTo() {
      if (!this.itemData.id) {
        return;
      }
      navigateTo("/pages/house_price/detail?id=" + this.itemData.id);
    },
    toTrend(id) {
      if (this.allowToTrend) {
        navigateTo("/propertyData/price_trend/price_trend?id=" + id + "&type=2");
      } else {
        this.goTo();
      }
    },
  },
};
</script>

<style lang="scss">
.list-item{
	padding: 30rpx 48rpx;
}
.list-info {
	overflow: hidden;
  .info-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: #1e1f20;
		.title_text{
			flex: 1;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.area_name{
			font-size: 22rpx;
			color: #999;
		}
  }
  .small_text {
    font-size: 20rpx;
    color: #999;
  }
}
.hourse-price {
  justify-content: space-between;
  align-items: flex-end;
  .total {
    font-size: 48rpx;
    font-weight: initial;
  }
  .price {
    line-height: 1;
    align-items: flex-end;
  }
  .unit {
		position: relative;
		bottom: 5rpx;
    font-size: 26rpx;
    color: #333;
  }
}
.list-info .info-content {
  margin-top: 8rpx;
  margin-bottom: 0;
  color: #999;
}
.up {
  font-size: 22upx;
  color: #f65354;
}
.down {
  font-size: 22upx;
  color: #179b16;
}
.price-data {
  margin-top: 8rpx;
  line-height: 1;
  color: #999;
}
.list-img {
  width: 204rpx;
  height: 172rpx;
}
.round-box {
  align-items: center;
  .list-img {
    width: 150rpx;
    height: 150rpx;
    border-radius: 5%;
    overflow: hidden;
  }
  .list-info {
    margin-right: 24upx;
  }
  .list-info .info-content {
    font-size: $uni-font-size-base;
    line-height: 1.5;
  }
  .hourse-price .small_text {
    font-size: $uni-font-size-base;
    line-height: 1.5;
  }
}
</style>
