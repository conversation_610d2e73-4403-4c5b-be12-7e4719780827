<template>
	<view class="desc-box">
		<view class="chart">
		<view class="desc-title">价格走势</view>
		<view class="qiun-charts">
			<!--#ifdef H5-->
			<canvas canvasId="canvasLine" class="charts" :style="{'width':cWidth*pixelRatio+'px','height':cHeight*pixelRatio+'px', 'transform': 'scale('+(1/pixelRatio)+')','margin-left':-cWidth*(pixelRatio-1)/2+'px','margin-top':-cHeight*(pixelRatio-1)/2+'px'}" @touchstart="touchLine"></canvas>
			<!--#endif-->
			<!--#ifndef H5-->
			<canvas canvasId="canvasLine" class="charts" @touchstart="touchLine"></canvas>
			<!--#endif-->
		</view>

		</view>
		<view class='desc_table' v-if = 'tableDate.length>0'>
			<view class="desc-title">价格详情</view>
			<table  cellspacing="1" cellpadding="0">
        <thead>
						<th class="month">月份</th>
						<th class="price">价格</th>
						<th class="desc">描述</th>
						<!-- <tr v-for="(item,index) in state" :key="index">
						<th>{{item}}</th>
						</tr> -->
        </thead>
        <tbody>
					<tr v-for="(item,index) in (tableDate)" :key="index">
						<td>{{item.date}}</td>
						<td>{{item.self}}</td>
						<td>{{item.desc}}</td>
						<!-- <td v-for="(item,index) in (lineData.categories)" :key="index">{{item}}--{{index}}</td> -->
					</tr>
        </tbody>
			</table>
		</view>
	</view>
</template>

<script>
// #ifndef MP
import uCharts from '../../components/u-charts/u-charts.js';
// #endif
// #ifdef MP
import uCharts from '../../components/wx-charts/wxcharts.js';
// #endif
	export default {
		data() {
			return {
				api:"",
				cWidth:'',
				cHeight:'',
				pixelRatio:1,
				lineData:{},
				tableDate:[],
				title:"",
				title1:"",
				type:"",
				id:"",
			}
		},
		onLoad(options){
			// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			// uni.hideShareMenu()
			// #endif
			let api
			this.type =options.type;
			this.id =options.id;

			switch(options.type){
				case '1':
				api = "build/buildPriceTrend.html"
				break
				case '2':
				api = "build/communityPriceTrend.html"
				break
				default:
				api = "build/buildPriceTrend.html"
			}
			this.getLineData(options.id,api)
			//#ifdef H5
			uni.getSystemInfo({
				success: (res)=> {
					if(res.pixelRatio>1){
						//正常这里给2就行，如果pixelRatio=3性能会降低一点
						//_self.pixelRatio =res.pixelRatio;
						this.pixelRatio =2;
					}
				}
			});
			//#endif
			this.cWidth=uni.upx2px(750);
			this.cHeight=uni.upx2px(500);
		},
		onReady() {
			if(JSON.stringify(this.lineData)!=="{}"){
				this.showLine("canvasLine",this.lineData);
			}
			this.is_ready = true
		},
		methods: {
			getLineData(id,api){
				this.$ajax.get(api,{id},res=>{
					if(res.data.code == 1){
						this.lineData.categories = res.data.months
						if(res.data.area_house){
							this.lineData.series = [res.data.self].concat([res.data.house]).concat([res.data.area_house])
						}else{
							this.lineData.series = [res.data.self].concat([res.data.house])
						}
						if(this.is_ready){
							this.showLine("canvasLine",this.lineData);
						}
						this.title=res.data.self.name;
						uni.setNavigationBarTitle({
							title:`${this.title}房价走势`
						})
						res.data.months.map((item,index)=>{
							let obj ={
								date:item
							}
							this.tableDate.push(obj)
						})
							res.data.self.desc.map((item,index)=>{						
							this.tableDate[index].desc=item	
						})
							res.data.self.data.map((item,index)=>{
								let obj ={
									self:item
								}
								this.tableDate[index].self=item	
						  })
					
					}else {
						uni.setNavigationBarTitle({
							title:`房价走势`
						})
					}
				},err=>{
					uni.setNavigationBarTitle({
							title:`房价走势`
						})
				})
			},
			showLine(canvasId,chartData){
				let _self = this
				this.canvaLine=new uCharts({
					$this: _self,
					canvasId: canvasId,
					type: 'line',
					fontSize:11,
					legend:true,
					background:'#FFFFFF',
					pixelRatio:_self.pixelRatio,
					categories: chartData.categories,
					animation: true,
					series: chartData.series,
					xAxis: {
						disableGrid:true,
					},
					yAxis: {
						format:function(val){
							return val+"元"
						}
						//disabled:true
					},
					width: _self.cWidth*_self.pixelRatio,
					height: _self.cHeight*_self.pixelRatio,
					dataLabel: false,
					enableScroll: false,
					dataPointShape: true,
					extra: {
						// lineStyle: 'curve'
					},
				});
			},
			touchLine(e){
				this.canvaLine.showToolTip(e, {
					format: function (item, category) {
						return category + ' ' + item.name + ':' + item.data 
					}
				});
			}
		},
		onShareAppMessage(res) {
			return {
				title: this.title+"价格走势图",
				// content: this.title,
				path: "/propertyData/price_trend/price_trend?id="+this.id+"&type="+this.type,
				success: function () {
					console.log('成功');
				// 转发成功
			    },
			
			}
        },
	}
</script>

<style lang="scss">
	.desc-box{
		padding: $uni-spacing-row-base 0;
		background-color: #fff;
	}
	.desc-box .desc-title{
		font-size: 32upx;
		padding: 0 20upx;
		margin: 0 $uni-spacing-col-base;
		margin-bottom: 20upx;
		border-left: 6upx solid #179B16
	}
	.qiun-charts {
		width:100%;
		// #ifndef H5
		height:500upx;
		//  #endif
		// #ifdef H5
		height:760upx;
		//  #endif
		background-color:#FFFFFF;
	}
	.charts{
		width: 100%;
		height:500upx;
		background-color: #FFFFFF;
	}

   .desc-box .chart {
		border-bottom: 2upx solid #eee;
	 };
	.desc_table{
		.desc-title{
			margin-top: 20upx;
		}
		table{
			text-align: center;
			border-collapse: collapse;
			color: #787878;
			padding: 20upx 40upx;
				// #ifdef H5
			width: 95%;
			margin:0 auto;
			//  #endif
			thead{
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-collapse: collapse;
				// #ifndef H5
				border-top: 2upx solid #e5e5e5;
					//#endif
				border-left: 2upx solid #e5e5e5;
				border-right: 2upx solid #e5e5e5;
				width: 100%;
					// #ifndef H5
				box-sizing: border-box;
				//#endif
				background: #eee;
				tr{
					// border-right: 2upx solid #666;
					flex: 1;
					width: 100%;
					border-collapse: collapse;
					// padding: 10upx;
					th{
						border-left: 2upx solid #e5e5e5;
						border-top: 2upx solid #e5e5e5;
						
					}
				
				}
					.month{
						width: 25%;
						 padding: 10upx;
						border-right: 2upx solid #e5e5e5;
					}
					.price{
						width: 25%;
						border-right: 2upx solid #e5e5e5;
						 padding: 10upx;

					}
					.desc{
						width: 50%;
						 padding: 10upx;

					}
			}
			tbody{
			 border-collapse: collapse;
 			//  border: 2upx solid #666; 
			 border-top: 2upx solid #e5e5e5;
				border-left: 2upx solid #e5e5e5;
				border-right: 2upx solid #e5e5e5;

				tr{
					border-collapse: collapse;
					display: flex;
					border-bottom: 2upx solid #e5e5e5;
					width: 100%;
					align-items:stretch;
					td{
						border-right: 2upx solid #e5e5e5;
						padding: 10upx;
						border-right: 2upx solid #e5e5e5;
						display: flex;
						align-items: center;
						justify-content: center;
					}
					td:first-child{
						width: 25%;
					}
					td:nth-child(2){
						width: 25%;
					}
					td:nth-child(3){
						width: 50%;
						border-right: 0upx ;
						overflow: hidden;
						text-overflow: ellipsis;
						// white-space: nowrap;
					}
				}
				
		
			}

		}
       
	}
</style>
