<template>
  <view class="page">
    <view class="cate_list bottom-line">
      <view class="cate_item" :class="{active: current_cate_id===cate.id}" v-for="(cate, index) in cate_list" :key="index" @click="switchCate(cate)">{{cate.cate_name}}({{cate.templateCount}}/10)</view>
    </view>
    <view class="template_list" v-show="template_list.length>0">
      <view class="template_item" v-for="(item, index) in template_list" :key="index" @click="toDetail(item)">
        <view class="name">
          <text>{{item.name}}</text>
          <text class="check"></text>
        </view>
        <view class="desc">{{item.content}}</view>
      </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <view class="add_btn" @click="toAdd">添加模板</view>
  </view>
</template>

<script>
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
  name: '',
  components: {
    uniLoadMore
  },
  data () {
    return {
      current_cate_id: 0,
      cate_list: [],
      get_status:"loading",
      content_text:{
        contentdown:"",
        contentrefresh:"正在加载...",
        contentnomore:"没有更多数据了"
      },
      template_list: []
    }
  },
  onLoad(){
    this.getCates()
    uni.$on('recapture', ()=>{
      this.getCates()
    })
  },
  methods: {
    getCates(){
      this.$ajax.get('release/releaseTemplateByCate', {}, res=>{
        if(res.data.code ===1){
          this.cate_list = res.data.list
          if(!this.current_cate_id){
            this.current_cate_id = this.cate_list[0].id;
          }
          this.getTemplateList(this.current_cate_id);
        }
      })
    },
    switchCate(cate){
      this.current_cate_id = cate.id
      this.template_list = []
      this.getTemplateList(cate.id)
    },
    getTemplateList(cate_id){
      this.get_status = "loading"
      this.$ajax.get('release/getReleaseTemplate', {cate_id}, res=>{
        if (res.data.code === 1) {
          this.template_list = res.data.list;
        }else{
          this.template_list = []
        }
        this.get_status = "noMore"
      })
    },
    toDetail(item){
      this.$navigateTo(`/user/add/edit_template?id=${item.id}`)
    },
    toAdd(){
      this.$navigateTo(`/user/add/add_template?cate_id=${this.current_cate_id}`)
    }
  },
  onUnload(){
    uni.$off('recapture')
  }
}
</script>

<style scoped lang="scss">
.page{
  padding-bottom: 160rpx;
}
.cate_list{
  display: flex;
  justify-content: center;
  background-color: #fff;
  position: sticky;
  // #ifdef H5
  top: 44px;
  // #endif
  // #ifndef H5
  top: 0;
  // #endif
  .cate_item{
    padding: 24rpx;
    // margin: 0 24rpx;
    text-align: center;
    font-size: 28rpx;
    color: #999;
    position: relative;
    transition: 0.26s;
    &.active{
      font-weight: bold;
      color: #333;
      &::after{
        content: "";
        position: absolute;
        bottom: 0;
        left: 30%;
        right: 30%;
        height: 8rpx;
        border-radius: 4rpx;
        background-color: $uni-color-primary;
      }
    }
  }
}

.template_list{
  box-sizing: border-box;
  padding: 24rpx 48rpx;
  background-color: #fff;
  .template_item{
    display: block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    // white-space: nowrap;
    padding: 20rpx 0;
    margin-bottom: 24rpx;
    .name{
      display: flex;
      justify-content: space-between;
      margin-bottom: 24rpx;
      font-size: 22rpx;
      color: #666;
    }
    .desc{
      white-space: pre-line;
      font-size: 28rpx;
      color: #333;
    }
  }
}

.add_btn{
  position: fixed;
  left: 48rpx;
  right: 48rpx;
  bottom: 68rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  color: #fff;
  background: #FB656A;
  box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
}
</style>