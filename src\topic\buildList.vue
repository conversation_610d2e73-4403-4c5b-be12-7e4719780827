<template>
  <view class="my_prize">
    <!-- <scroll-view class="prize_list" scroll-y  :show-scrollbar="false"> -->
    <view class="prize_list" >
      <view class="prize_item " v-for ="(item,index) in list " :key ="index" :class="{'bottom-line':index == list.length}"  @click ="toDetail(item)">
        <view class="item_top flex-row ">
          <view class="prize_img">
            <image mode="widthFix" :src ="item.pic |imageFilter('w_240')"></image>
          </view>
          <view class="prize_info flex-1">
            <view class="prize_name">{{item.title}}</view>
            <view class="prize_num">{{item.area}}</view>
            <view class="prize_num">{{item.address}}</view>
            <!-- <div class="prize_num flex-row items-center">{{item.build_type}}</div> -->
          </view>
          <view class="oper">
            <view class="to_map" @click.prevent.stop="toMap(item)">
              <image mode ="widthFix" :src ="`/yidongduan/blindBox/<EMAIL>` | imageFilter('m_320')">
              </image>
            </view>
          </view>
        </view>
        <view class="item_b flex-row items-center">
          <view class="item_price flex-row items-center flex-1">
            <!-- <text>均价</text> -->
            <text class="red"> {{item.price}}</text>
          </view>
          <view class="zxzx" @click.stop.prevent ="maketel(item)">
            在线咨询
          </view>
          <view class="yhzx" @click.stop.prevent ="ask(item)">
            优惠咨询
          </view>
        </view>
      </view>
    </view>
    <view class="jindu" v-if ="show_step">
      浏览任务{{current_num}}/{{count}}
    </view>
    <sub-form
      groupCount=""
      sub_title="在线报名，获取优惠"
      sub_content="为方便通知到您最新的信息，请输入您的手机号码"
      :sub_type="3"
      :sub_mode="0"
      ref="sub_form"
      @onsubmit="handleSubForm"
      :login_status="login_status"
    ></sub-form>
     <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
     <!-- 登录弹窗 -->
     <!-- #ifndef MP-WEIXIN-->
    <login-popup ref="login_popup" @onclose="toLoginPage" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
    <!-- #endif -->
  </view>
</template>

<script>
import myPopup from "@/components/myPopup.vue"
import myIcon from "@/components/myIcon.vue"
import subForm from '../components/subForm'
import allTel from '@/common/all_tel.js'
// #ifndef MP-WEIXIN
import loginPopup from '@/components/loginPopup'
// #endif
export default {
  components: {
    myPopup,
    myIcon,
    subForm,
    // #ifndef MP-WEIXIN
    loginPopup,
    // #endif
  },
  data () {
    return {
      list:[],
      count:0,
      current:'',
      current_num:0,
      show_step:false,
      detail:{},
      tel_params:{},
      tel_res:{},
      show_tel_pop:false,
      login_tip: '',
    }
  },
  computed: {
    login_status() {
      return this.$store.state.user_login_status
    },
    sub_mode() {
      return this.$store.state.sub_form_mode
    },
    is_open_middle_num(){
       return this.$store.state.im.istelcall
    }
  },
  onLoad (options) {
    if (options.from) {
      this.from =options.from
    }
    if (options.id){
      this.id = options.id
      this.getList()
    }
    if (this.from ){
       let blind_info = uni.getStorageSync("blind_info"+this.id)
        if(blind_info ) {
           blind_info= JSON.parse(blind_info)
           let oDate =+ new Date(blind_info.date)/1000
           if (this.transDate(oDate) != this.transDate(+new Date()/1000)){
              blind_info ={
                date:this.transDate(+new Date()/1000),
                read_arr:[],
              }
            }
        }else {
           blind_info ={
            date:this.transDate(+new Date()/1000),
            read_arr:[],
          }
        }
        this.blind_info =blind_info
    }
  },
  mounted () {
    //  this.showQrcode()
  },

  methods: {
    getList(){
      this.$ajax.get("blind_box/blindBoxBuilds",{id:this.id},(res)=>{
        console.log(res);
        if (res.data.code ==1){
          this.list =res.data.builds
          if(this.from){
            this.blind_info.count = this.list.length
            let c_count = this.blind_info.read_arr.length
            let build_count =this.list.length 
            if (c_count!=build_count){
              this.show_step =true
              this.current_num= c_count
              this.count = build_count
            }
          }
        }else {
          uni.showToast({
            title:res.msg,
            icon:'none'
          })
        }
      })
    },
    callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
      this.tel_params = {
        type,
        callee_id,
        scene_type,
        scene_id,
        source,
        bid,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      // #ifdef MP-WEIXIN
      allTel(this.tel_params)
      // #endif
      // #ifndef MP-WEIXIN
      // 全局开启中间号且楼盘开启中间号需要检测登录
      if(this.is_open_middle_num == 1&& this.detail.use_middle_call > 0 ){
        this.tel_params.intercept_login = true
        this.tel_params.fail = (res)=>{
          if(res.data.code === -1){
            this.$store.state.user_login_status = 1
            this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
          }
          if(res.data.code === 2){
            this.$store.state.user_login_status = 2
            this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
          }
        }
        allTel(this.tel_params)
      }else{
        allTel(this.tel_params)
      }
      // #endif
    },
    retrieveTel(){
      allTel(this.tel_params)
    },
    showLoginPopup(tip){
      this.login_tip = tip
      this.$refs.login_popup.showPopup()
    },
    onLoginSuccess(res){
      this.$store.state.user_login_status = 3
      this.retrieveTel()
    },
    maketel(item){
      if (item.build_id){
        this.detail =  item
        this.callMiddleNumber(1,item.build_id,1,item.build_id,'build_detail',item.build_id)
        return 
      }
      this.detail =  {}
      if(!item.tel){
        uni.showToast({
          title:"暂无电话",
          icon:'none'
        })
        return 
      }
      uni.makePhoneCall({
        phoneNumber:item.tel
      })
    },
    ask(item){
      this.current = item
      this.$refs.sub_form.showPopup()
    },

    handleSubForm(e){
      var params = {
        blind_id: parseInt(this.current.blind_box_id),
        info_id: this.current.id||this.current.build_id ||'',
        cname: e.name,
        tel: e.tel
      }
      this.$ajax.post('build/signUp', params, res=>{
        uni.hideLoading()
        if(res.data.code === 1){
          uni.showToast({
            title: res.data.msg||'报名成功',
            mask: true
          })
          this.$refs.sub_form.hide()
        }else{
          uni.showToast({
            title: res.data.msg||'报名失败',
            icon: 'none',
            mask: true
          })
        }
      })
    },
    toMap(item){
      if(!item.lat|| !item.lng){
        uni.showToast({
          title:"楼盘经纬度不正确",
          icon:'none'
        })
        return 
      }
      uni.openLocation({
          latitude: parseFloat(item.lat),
          longitude: parseFloat(item.lng),
          name: item.title,
          address: item.address
        })
      
    },
    transDate(time){
        let data  = new Date(time*1000)
        let y  = data.getFullYear()
        let m = ((data.getMonth()+1)+'').padStart(2,"0")
        let d  = (data.getDate()+'').padStart(2,"0")
        return y+"-"+m+"-"+d
    },
    toDetail(item){
      if (this.from ){
        if (item.build_id){
          if(this.blind_info &&!this.blind_info.read_arr.includes(item.build_id+'') &&  this.from ){
            this.blind_info.read_arr.push(item.build_id+'' )
          }
        }else {
          console.log(this.blind_info.read_arr.includes(item.id),11111);
          if(this.blind_info &&!this.blind_info.read_arr.includes(item.id+'') &&  this.from ){
            this.blind_info.read_arr.push(item.id+'')
          }
        }
        this.current_num= this.blind_info.read_arr.length
        uni.setStorageSync('blind_info'+this.id,JSON.stringify(this.blind_info))
        if (this.current_num>0 && this.current_num==this.count ){
          uni.$emit("finished",{
            status:1,
            task_name:'view_build'
          })
        }
      }
      if (item.build_id ){
        this.$navigateTo("/pages/new_house/detail?id="+item.build_id+"&is_task="+(this.from||'')+'&active_id='+item.blind_box_id)
      }else {
         this.$navigateTo("/topic/build_detail?id="+item.id+"&is_task="+(this.from||'')+'&active_id='+item.blind_box_id)
      }
    },
    toLoginPage() {
      if (this.$store.state.user_login_status === 1) {
        uni.removeStorageSync('token')
        this.$navigateTo('/user/login/login')
      }
      if (this.$store.state.user_login_status === 2) {
        this.$navigateTo('/user/bind_phone/bind_phone')
      }
    },
  },
  onReachBottom(){
    this.isFinish=true
  }
}
</script>

<style lang="scss" scoped>
.my_prize {
  // position: absolute;
  // top: 0;
  // left: 0;
  // right: 0;
  // bottom: 0;
  padding-top: 80rpx;
  padding-bottom: 80rpx;
  background: #fe9f48;
  box-shadow: inset 0 0 20px 0 rgba(255, 255, 255, 0.5);
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.items-center {
  align-items: center;
}
.j-center {
  justify-content: center;
}
.flex-1 {
  flex: 1;
}
.prize_list {
  margin: 0 48rpx;
  // height: calc(100% - 100rpx);
  // height:calc(100% - 48px);
  // width: calc(100vw - 192rpx);
  background: #fff;
  border-radius: 20rpx;
  padding: 48rpx 48rpx;
  // overflow-y: scroll;
  // ​-webkit-overflow-scrolling: touch;
  
  .prize_item {
    padding: 24rpx 0;
    border-bottom: 1px solid #d8d8d8;
    .prize_img {
      width: 200rpx;
      min-width: 200rpx;
      height: 200rpx;
      margin-right: 24rpx;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
    .prize_info {
      overflow: hidden;
      .prize_name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 32rpx;
        color: #7f502c;
      }
      .prize_num {
        font-size: 28rpx;
        color: #7f502c;
        margin-top: 16rpx;
      }
    }
    .oper {
      .to_map {
        width: 64rpx;
        height: 64rpx;
        overflow: hidden;
        image{
          width: 100%;
        }
      }
    }
    .item_b{
      .zxzx{
        background: #FFC693;
        border: 1px solid #FEA350;
        box-shadow: inset 0 0 6rpx 0 rgba(255,255,255,0.42);
        border-radius: 32rpx;
        font-size: 22rpx;
        padding: 16rpx 24rpx;
        color: #7F502C;
        margin-right: 24rpx;
      }
      .yhzx{
        background-image: linear-gradient(90deg, #FA7427 0%, #F24011 100%);
        box-shadow: 0 10rpx 20rpx -8rpx rgba(244,73,21,0.60), inset 0 0 6rpx 0 rgba(255,255,255,0.42);
        border-radius: 32rpx;
        padding: 16rpx 24rpx;
        font-family: AlibabaPuHuiTi-Medium;
        font-size: 22rpx;
        color: #FFFFFF;
      }
    }
  }
}
.empty {
  text-align: center;
  width: 80vw;
  margin: 40rpx auto;
  height: 80vh;
  padding-top: 120rpx;
  background: #fff;
  border-radius: 20rpx;
  .empty_img {
    width: 240rpx;
    height: 240rpx;
    margin: 0 auto;
    overflow: hidden;
    image {
      width: 100%;
    }
  }
  .empty_name {
    margin-top: 48rpx;
    font-size: 32rpx;
    color: #7f502c;
  }
  .empty_btns {
    justify-content: center;
    margin-top: 86rpx;
    .empty_btn {
      font-size: 32rpx;
      color: #ffffff;
      padding: 22rpx 106rpx;

      background-image: linear-gradient(90deg, #fa7427 0%, #f24011 100%);
      box-shadow: 0 10rpx 20rpx -8rpx rgba(244, 73, 21, 0.6),
        inset 0 0 6rpx 0 rgba(255, 255, 255, 0.42);
      border-radius: 44rpx;
    }
  }
}

.qrcode-box {
  position: relative;
  margin-top: 15vh;

  .img-box {
    padding: 12rpx;
    padding-top: 48rpx;
    margin: auto;
    position: relative;
    background-color: #fff;
    border-radius: 15rpx;
    overflow: hidden;
    .title {
      font-size: 36rpx;
      font-weight: bold;
      text-align: center;
      color: #7f502c;
      &.title_info {
        text-align: left;
        margin-top: 48rpx;
        padding: 0 48rpx;
      }
    }
    .duihuan_info {
      margin: 24rpx 0;
      padding: 0 48rpx;
      font-size: 22rpx;
      color: #7f502c;
      .duihuan_info_name {
        white-space: nowrap;
        margin-right: 20rpx;
      }
    }
    .empty {
      height: 300rpx;
      width: 100vw;
    }

    .tip {
      padding: 24rpx;
      padding-bottom: 48rpx;
      text-align: center;
      font-size: 28rpx;
      color: #7f502c;
    }
    .line {
      width: calc(100vw - 48rpx);
      height: 2rpx;
      margin: 0 auto;
      background: #d8d8d8;
    }
  }
  .qrcode {
    width: 320rpx;
    margin: 0 auto;
    height: 320rpx;
    display: block;
  }
  .icon-box {
    position: absolute;
    bottom: -80rpx;
    width: 52rpx;
    height: 52rpx;
    left: 0;
    right: 0;
    margin: auto;
  }
}
.jindu {
  position: fixed;
  bottom:200rpx;
  border-radius: 8rpx;
  margin-left: 50%;
  left: 0;
  right: 0;
  text-align: center;
  transform: translateX(-50%);
  padding: 10rpx 20rpx;
  border: 2rxp solid #45A2FF;
  background: #45A2FF;
  color: #fff;
}
</style>