<template>
<view>
<block v-if="showSlot">
	<view @click="handelChoose">
		<slot></slot>
	</view>
</block>
<block v-else>
	<view class="upload-block flex-box">
		<view :class="chooseType===2||chooseType===4?'video-box':'img-box'" v-for="(video,idx) in videos" :key="idx"  @click="previewVideo(idx)">
			<view class="del-img" @click.stop.prevent="delVideo(index)">
				<my-icon type="qingchu" size="42rpx" color="#333333"></my-icon>
			</view>
			<image class="img" :src="video | imgUrl" mode="aspectFit"></image>
			<image class="video-icon" src="/static/icon/video.png" :mode="chooseType===2||chooseType===4?'widthFix':'aspectFill'"></image>
		</view>
		<view class="img-box" v-for="(img,index) in imgs" :key="index">
			<view class="del-img" @click="delImg(index)">
				<my-icon type="qingchu" size="42rpx" color="#333333"></my-icon>
			</view>
			<image class="img" :src="img | imgUrl" mode="aspectFill" @click="previewImage(index)"></image>
		</view>
		<view v-if="(imgs.length<maxCount&&chooseType==1)||(imgs.length<maxCount&&chooseType==3)||((imgs.length<maxCount&&videos.length<1&&chooseType==2))||(chooseType===4&&videos.length<maxCount)" class="upload-btn" @click="handelChoose()">
			<view class="icon-box">
				<my-icon type="ic_jia" size="120rpx" color="#DEDEDE" className="add"></my-icon>
			</view>
		</view>
		<view class="upload-btn perch"></view>
	</view>
</block>
</view>
</template>

<script>
	import myIcon from "../../components/myIcon"
	import {formatImg,isIos} from "../../common/index.js"
	import wx from "weixin-js-sdk"
	export default{
		props:{
			showSlot:{
				type:Boolean,
				default:false
			},
			imgs:{
				type:Array,
				default:function(){
					return []
				}
			},
			videos:{
				type:Array,
				default:function(){
					return []
				}
			},
			chooseType:{
				type:Number,
				default:3 //1:只允许船图片，2：视频图片只能传1种，3：视频图片可以混传，4：只能传视频
			},
			maxCount:{
				type:Number,
				default:6
			},
			clearImg:{
				type:Boolean,
				default:true
			},
			action:{
				type: String,
				default: "house/uploadFileByWx"
			},
			set_wx_config:{
				type:Boolean,
				default:true
			},
			disabled:{
				type:[Boolean,String],
				default:false
			},
			allowDel:{
				type:Boolean,
				default:true
			},
			message:{
				type:String,
				default:"不允许删除"
			},
		},
		data(){
			return{

			}
		},
		components:{
			myIcon
		},
		filters:{
			imgUrl(val){
				return formatImg(val,'w_400')
			}
		},
		created(){
			if(!this.set_wx_config){
				return
			}
			let url;
			if(isIos()){
				url = this.$store.state.firstUrl
			}else{
				url = window.location.href
			}
			this.$ajax.get('/wechat/index/signature.html',{url:url},res=>{
				if(res.data.code == 1){
					res.data.config.jsApiList = ['chooseImage','uploadImage','getLocalImgData','hideOptionMenu']
					// res.data.config.debug=true
					wx.config(res.data.config)
					wx.ready(()=>{
						wx.hideOptionMenu()
					})
				}
			})
		},
		methods:{
			handelChoose(){
				if (this.disabled){
					uni.showToast({
						title:'不可上传',
						icon:"none"
					})
					return 
				}
				// 如果有上传的视频且是图片视频可以混合模式或者只允许上传图片模式
				if((this.videos.length>0&&this.chooseType==3)||this.chooseType==1){
					this.chooseImg()
					return
				}
				// 如果是视频和图片只能上传一个，且已经有上传的图片
				if(this.chooseType==2&&this.imgs.length>0){
					this.chooseImg()
					return
				}
				if(this.chooseType==4&&this.videos.length<this.maxCount){
					this.chooseVideo()
					return
				}
				uni.showActionSheet({
					 itemList: ['上传照片', '上传视频'],
					 success: (res)=> {
						switch(res.tapIndex)
						{
							case 0:
							this.chooseImg()
							break;
							case 1:
							this.chooseVideo()
							break;
						}
					},
					fail: function (res) {
						console.log(res.errMsg);
					}
				})
			},
			chooseImg(){ //选择图片
				let _this = this
				let chooseCount = this.maxCount-this.imgs.length
				wx.chooseImage({
					count: chooseCount>9?9:chooseCount, // 默认9
					sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
					success: function (res) {
						var tempFilePaths = res.localIds; // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
						if(tempFilePaths.length>(_this.maxCount-_this.imgs.length)){
							uni.showToast({
								title:("最多只能上传"+_this.maxCount+"张图片"),
								icon:'none'
							})
							return false
						}
						let i = 0;
						uni.showLoading({
							title:"正在上传",
							mask:true
						})
						if(_this.showSlot){
							_this.imgList = []
						}else{
							_this.imgList = _this.imgs
						}
						_this.$emit("chooseDon",tempFilePaths)
						function upload(){
							wx.getLocalImgData({
								localId: tempFilePaths[i], // 图片的localID
								success: function (res) {
									var localData = res.localData; // localData是图片的base64数据，可以用img标签显示
									_this.$ajax.post(_this.action,{base64:localData},res=>{
										console.log(res.data.url)
										if(res.data.code == 1){
											_this.imgList.push(res.data.url)
										}else{
											uni.showToast({
												title:res.data.msg||"上传失败",
												icon:"none"
											})
										}
										i++;
										if (i < tempFilePaths.length) {
											upload();
										}else{
											_this.$emit('uploadDon',{files:_this.imgList,type:"image"})
											uni.hideLoading()
										}
									},
									)
								}
							});
						}
						upload()
					}
				})
			},
			chooseVideo(){ //选择视频
				uni.chooseVideo({
					count: 1,
					sourceType: ['camera', 'album'],
					success: (res)=> {
						const videoSrc = res.tempFilePath;
						console.log(videoSrc)
						uni.showLoading({
							title:"正在上传",
							mask:true
						})
						this.videoList = this.videos
						this.$uploadFile(this.action, videoSrc,{type:2},(res)=>{
							let result = JSON.parse(res.data)
							if(!result.url){
								uni.showToast({
									title:result.msg||"上传失败",
									icon:"none"
								})
								return
							}
							this.videoList.push(result.url)
							this.$emit('uploadDon',{files:this.videoList,type:"video"})
							uni.hideLoading()
						})
					},
					fail: err=>{
						console.log(err)
					}
				});
			},
			delImg(index){
				if (!this.allowDel){
						uni.showToast({
							title:this.message,
							icon :"none"
						})
						return 
				}
				this.imgList = this.imgs
				this.imgList.splice(index,1)
				this.$emit('uploadDon',{files:this.imgList,type:"image"})
			},
			delVideo(index){
			if (this.allowDel){
						uni.showToast({
							title:this.message,
							icon :"none"
						})
						return 
				}
				this.videoList = this.videos
				this.videoList.splice(index,1)
				this.$emit('uploadDon',{files:this.videoList,type:"video"})
			},
			previewImage(index){
				let urls = this.imgs.map(item=>{
					return formatImg(item, 'w_8601')
				}) 
				uni.previewImage({
					urls:urls,
					indicator:"number",
					current:urls[index]
				})
			},
			previewVideo(index){
				this.$navigateTo('/vr/preview_video?url='+this.videos[index])
			}
		}
	}
</script>

<style lang='scss'>
	.upload-block{
		flex-wrap: wrap;
		justify-content: space-between;
	}
	.upload-btn,.img-box{
		width: 30%;
		margin-bottom: 5%;
		height: 0;
		padding-bottom: 30%;
		text-align: center;
		box-sizing: border-box;
		background-color: #f3f3f3;
		position: relative;
		display: flex;
		&.perch{
			height: 0;
			padding: 0;
			border: 0;
		}
	}
	.video-box{
		width: 70%;
		margin-bottom: 5%;
		height: 0;
		padding-bottom: 50%;
		text-align: center;
		box-sizing: border-box;
		background-color: #f3f3f3;
		position: relative;
		display: flex;
	}
	.upload-btn .icon-box{
      width: 120rpx;
      height: 120rpx;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      margin: auto;
    }
	.upload-block .img{
		height: 100%;
		width: 100%;
		position: absolute;
	}
	.upload-block image.video-icon{
		width:70rpx;
		height:70rpx;
		position:absolute;
		top:0;
		bottom:0;
		left:0;
		right:0;
		margin:auto;
		border-radius:50%;
		background-color:rgba(0, 0, 0, 0.4);
		&.alone{
			width: 15vw;
			height: 15vw;
		}
	}
	.del-img{
		position: absolute;
		top: 0;
		right: 0;
		z-index: 9;
	}
</style>
