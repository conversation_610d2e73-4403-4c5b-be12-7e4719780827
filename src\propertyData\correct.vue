
<template>
  <view class ="correct">
    <!-- 我要补充 -->
    <block v-if="type == 0">
      <view class="block">
      <my-input
        label="楼栋总数"
        :bottomLine="false"
        textAlign="right"
        name="loudong"
        @input="handleInputInfo"
        v-model="params.loudong"
        :disabled="dis_params.loudong ? true : false"
        placeholder="请补充"
      ></my-input>
      <my-input
        label="房屋总数"
        name="house"
        @input="handleInputInfo"
         :bottomLine="false"
        v-model="params.house"
        :disabled="dis_params.house ? true : false"
        textAlign="right"
        placeholder="请输入"
      ></my-input>
      <my-input
        label="容积率"
        name="volume"
        v-model="params.volume"
        @input="handleInputInfo"
        :disabled="dis_params.volume ? true : false"
        :bottomLine="false"
        textAlign="right"
        placeholder="请输入"
      ></my-input>
      </view>
      <view class="block">
        <my-input
          label="绿化率"
          name="green_rate"
          v-model="params.green_rate"
          :disabled="dis_params.green_rate ? true : false"
          @input="handleInputInfo"
          :bottomLine="false"
          textAlign="right"
          placeholder="请输入"
        ></my-input>
        <my-input
          label="停车位"
          name="stall"
          v-model="params.stall"
          :disabled="dis_params.stall ? true : false"
          @input="handleInputInfo"
          :bottomLine="false"
          textAlign="right"
          placeholder="请输入"
        ></my-input>
        <my-input
          label="建筑面积"
          name="total_area"
          v-model="params.total_area"
          :disabled="dis_params.total_area  ? true : false"
          @input="handleInputInfo"
          :bottomLine="false"
          textAlign="right"
          placeholder="请输入"
        ></my-input>
      </view>
      <view class="block">
        <my-input
          label="开发商"
          name="company"
          v-model="params.company"
          :disabled="dis_params.company ? true : false"
          @input="handleInputInfo"
          :bottomLine="false"
          textAlign="right"
          placeholder="请输入"
        ></my-input>
        <my-input
          label="建成年代"
          name="completed_time"
          v-model="params.completed_time"
          :disabled="dis_params.completed_time ? true : false"
          @input="handleInputInfo"
          :bottomLine="false"
          textAlign="right"
          placeholder="请输入"
        ></my-input>
        <my-input
          label="物业公司"
          name="management_company"
          v-model="params.management_company"
          :disabled="dis_params.management_company ? true : false"
          @input="handleInputInfo"
          :bottomLine="false"
          textAlign="right"
          placeholder="请输入"
        ></my-input>
      </view>
      <view class="block">
        <my-input
          label="物业费"
          name="property_price"
          v-model="params.property_price"
          :disabled="dis_params.property_price ? true : false"
          @input="handleInputInfo"
          :bottomLine="false"
          textAlign="right"
          placeholder="请输入"
        ></my-input>
        <my-input
          label="建筑类型"
          name="architecture_type"
          v-model="params.architecture_type"
          :disabled="dis_params.architecture_type ? true : false"
          @input="handleInputInfo"
          :bottomLine="false"
          textAlign="right"
          placeholder="请输入"
        ></my-input>
        <my-input
          label="小区地址"
          name="address"
          v-model="params.address"
          :disabled="dis_params.address ? true : false"
          @input="handleInputInfo"
          :bottomLine="false"
          textAlign="right"
          placeholder="请输入"
        ></my-input>
      </view>
      <view class="btn-box">
        <view class="btn" @click="subData">立即提交</view>
      </view>
    </block>
    <!-- 纠错 -->
    <block v-if="type == 1">
      <view class="jc-box">
        <view class="jc-title flex-row">
          <view class="jc-t flex-row">
            纠错类型
            <text class="jc-tips">（最多一项）</text>
          </view>
          <!-- <view class="jc-tips" v-if="change_box.length > 9" @click="is_height==='auto' ? is_height = '330rpx' : is_height = 'auto'">全部类型</view> -->
        </view>
        <view class="change-box flex-row" >
          <view class="change-item" :style="{color:!item.value ? '#c0c4cc' : '#666'}" :class="{'active': isChange == item.name}" v-for="item in change_box" :key="item.name" @click="onChangeType(item)">
            {{item.title}}
          </view>
        </view>
      </view>
      <view class="jc-box">
        <view class="jc-t">问题描述</view>
        <textarea v-model="is_corrent_desc" style="margin-bottom:0" class="input" name="" id="" cols="30" rows="10" maxlength="300" :placeholder="placeholder || '请描述您的问题（300字内）'"></textarea>
      </view>
      <view class="jc-box">
        <view class="jc-t">联系方式</view>
        <input class="input" v-model="params.correct_tel" type="number" maxlength="11" placeholder="请填写手机号码">
        <view class="jc-tips">请保持手机畅通，我们将尽快与您取得联系</view>
      </view>
      <view class="jc-box">
        <view class="btn-box">
          <view class="btn" @click="subData">立即提交</view>
        </view>
      </view>
    </block>
  </view>
</template>

<script>
import myInput from "../components/form/myInput";
export default {
  components:{
    myInput
  },
  data(){
    return {
      params:{
        loudong:'',
        house:"",
        volume:"",
        green_rate:"",
        stall:"",
        total_area:"",
        company:"",
        completed_time:"",
        management_company:"",
        property_price:"",
        architecture_type:"",
        address:"",
      },
      change_box:[],
      isChange:"",
      is_height:"330rpx",
      is_corrent_desc:"", // 纠错问题描述字段
      placeholder:"",
      dis_params:{}
    }
  },
  onLoad(options){
    if (options.type ){
      this.type=options.type
    }
    uni.setNavigationBarTitle({
      title: this.type==1?"我要纠错" :'我要补充',
    });
    if (options.id){
      this.id =options.id
      this.getInfo()
      if(this.type == 1){
        this.getCorrectData()
      }
    }
       
  },
  methods: {
    handleInputInfo(e){
      this.params[e._name] = e.detail.value
    },
    getCorrectData(){
      this.$ajax.get('community/memberAdditionalFields',
      {id:this.id},
        (res)=>{
          if(res.data.code == 0){
            uni.showToast({
              title:res.data.msg,
              icon:'none'
            })
            return
          }
          this.change_box = res.data.fields
        },
        (err)=>{
          console.log(err);
        }
      )
    },
    getInfo(){
      this.$ajax.get(
        "house/communityDetailNew.html",
        { id:this.id },
        (res) => {
          if (res.data.code == 0) {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
            });
            return;
          }
          if(this.type == 0){
            this.params.loudong = res.data.community.loudong
            this.params.house = res.data.community.house
            this.params.volume = res.data.community.volume
            this.params.green_rate = res.data.community.green_rate
            this.params.stall = res.data.community.stall
            this.params.total_area = res.data.community.total_area
            this.params.company = res.data.community.company
            this.params.completed_time = res.data.community.completed_time
            this.params.management_company = res.data.community.management_company
            this.params.property_price = res.data.community.property_price
            this.params.architecture_type = res.data.community.architecture_type
            this.params.address = res.data.community.address
            this.dis_params = Object.assign({},this.params)  // 拷贝用于输入判断
            this.dis_params.total_area = parseInt(this.dis_params.total_area)
            this.dis_params.volume = parseInt(this.dis_params.volume)
          }else{
            this.params.is_correct = 1  // 纠错
          }

        },
        (err) => {
          console.log(err);
        }
      );
    },
    onChangeType(item){
      if(!item.value){
        return
      }
      this.placeholder = item.title + '：' + item.value
      // 单选
      if(item.name!=this.isChange){
        this.isChange = item.name;
      }else{
        // this.isChange = -1;    //不注释则可以点击取消，注释之后点击就不再取消
      }
    },
    removePropertyOfNull(obj){  
        Object.keys(obj).forEach(item=>{
            if(!obj[item])  delete obj[item]
        })
        return obj;
    },
    subData(){
      this.params.id = this.id
      if(this.params.is_correct == 1){
        this.params[this.isChange] = this.is_corrent_desc
        this.params.correct_field = this.isChange
        this.removePropertyOfNull(this.params)  // 删除对象空内容
      }
      this.$ajax.post(
        "community/memberAdditional",
        this.params,
        (res) => {
          if (res.data.code == 0) {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
            });
            return;
          }
          uni.showToast({
            title: res.data.msg,
            icon: "none",
          });
          setTimeout(() => {
            this.$navigateBack()
          }, 2000);
        },
        (err) => {
          console.log(err);
        }
      );
    },
  },
}
</script>

<style lang="scss" scoped>
.flex-row {
  flex-direction: row;
}
.block {
  margin-bottom: 20rpx;
  padding: 0 48rpx;
  background-color: #fff;
  input {
    color: #333;
  }
}
.btn-box {
  padding: 48rpx;
  .btn {
    height: 88rpx;
    line-height: 88rpx;
    background: #fb656a;
    box-shadow: 0 8rpx 32rpx 0 rgba(251, 101, 106, 0.4);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    text-align: center;
  }
}
// 纠错样式
.jc-box{
  background: #fff;
  padding: 48rpx;
  margin-bottom: 16rpx;
  .jc-t{
      font-size: 40rpx;
      align-items: center;
      color: #333;
      display: flex;
      font-weight: 500;
      margin-right: 24rpx;
  }
  .change-box{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
    &::after{
      content: "";
      width: 31%;
      height: 0px;
      visibility: hidden;
    }
    .change-item{
      text-align: center;
      margin-top: 24rpx;
      padding: 22rpx;
      width: 24%;
      font-size: 28rpx;
      color: #666;
      background: #F8F8F8;
      border: 1rpx solid rgba(216,216,216,1);
      border-radius: 8rpx;
      &.active{
        background: #FFF3F4;
        border: 1rpx solid rgba(251,101,106,1);
        color: #FB656A;
      }
    }
  }
  .jc-title{
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .input{
    margin: 24px 0;
    background: #f8f8f8;
    border:1rpx solid rgba(216,216,216,1);
    border-radius: 8rpx;
    padding: 24rpx;
  }
  textarea{
    width: auto;
  }
  .jc-tips{
      font-size: 22rpx;
      color: #999;
  }
  &:last-child{
    margin-bottom: 0;
    padding: 0;
  }
}
</style>
