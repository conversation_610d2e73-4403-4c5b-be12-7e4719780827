<template>
  <view class="page">
      <view class="top_bg" :style="{backgroundImage: 'url('+bgImage+')', backgroundRepeat:'no-repeat', backgroundPosition:'center center', backgroundSize: 'contain'}">
        <view class="search">
          <view class="search-icon"><my-icon size="48rpx" type="ic_sousuo" color="#999999"></my-icon></view>
          <input
            type="text"
            confirm-type="search"
            placeholder-style="font-size:28rpx;color:#999;"
            v-model="params.keywords"
            @confirm="onSearch"
            placeholder="搜索心仪的学校"
          />
        </view>
        <view class="title">
          {{word1}}
        </view>
        <view class="sub_title">
          {{word2}}
        </view>
      </view>
      <view class="shaixuan flex-row">
        
        <view class="shaixuan_item flex-1 flex-row"  v-for ="(item,index) in shaixuanList" :key ="index" :class ="{'first': index ==0}" @click ="$navigateTo('/school/map?cate_id='+item.cate_id)">
          <view class="shaixuan_item_left"> 
            <view class="shaixuan_item_left_top flex-row">
              <view class="shaixuan_item_left_top_line">

              </view>
              <view class="shaixuan_item_left_top_title">
                {{item.title}}
              </view>
            </view>
            <view class="shaixuan_item_left_bottom">
              施教范围分布图
            </view>
          </view>
          <view class="shaixuan_item_right flex-row">
            <view class="img">
              <image :src ="item.icon |formatOssImg " mode ="widthFix" ></image>
            </view>
          </view>
        </view>
      </view>
    <view class="filter-box flex-box"  id ="tab_top">

      <scroll-view
      scroll-y
      class="screen-panel"
      :class="show_filter_type == 1 ? 'show' : ''"
      @touchmove.stop.prevent="stopMove"
      >
        <block v-for="item in area_list" :key="item.id">
          <uni-list-item
            :title="item.name"
            show-arrow="false"
            @click="switchTab(item.id, item.name)"
          ></uni-list-item>
        </block>
      </scroll-view>
      <scroll-view
        scroll-y
        class="screen-panel"
        :class="show_filter_type == 2 ? 'show' : ''"
        @touchmove.stop.prevent="stopMove"
      >
        <block v-for="item in cate_list" :key="item.id">
          <uni-list-item
            :title="item.name"
            show-arrow="false"
            @click="switchTab(item.id, item.name)"
          ></uni-list-item>
        </block>
      </scroll-view>
      <scroll-view
        scroll-y
        class="screen-panel"
        :class="show_filter_type == 3 ? 'show' : ''"
        @touchmove.stop.prevent="stopMove"
      >
        <block v-for="item in type_list" :key="item.id">
          <uni-list-item
            :title="item.name"
            show-arrow="false"
            @click="switchTab(item.id, item.name)"
          ></uni-list-item>
        </block>
      </scroll-view>
      <scroll-view
        scroll-y
        class="screen-panel"
        :class="show_filter_type == 4 ? 'show' : ''"
        @touchmove.stop.prevent="stopMove"
      >
        <block v-for="item in status_list" :key="item.id">
          <uni-list-item
            :title="item.name"
            show-arrow="false"
            @click="switchTab(item.id, item.name)"
          ></uni-list-item>
        </block>
      </scroll-view> 

      <view
        class="filter-item"
        v-for="item in filter_list"
        :key="item.type"
        @click="onClickFilter(item)"
      >
        <text>{{ item.name }}</text>
        <my-icon type="ic_down" size="12" color="#d8d8d8"></my-icon>
      </view>
      <view class="icon-box" v-show="params.sort === 1" @click="handleSort(2)">
        <image class="map-icon" src="https://images.tengfangyun.com/images/icon/ic_paixu1.png"></image>
      </view>
      <view class="icon-box" v-show="params.sort === 2" @click="handleSort(1)">
        <image class="map-icon" src="https://images.tengfangyun.com/images/icon/ic_paixu2.png"></image>
      </view>

      
    </view>
    
    <!-- 数据列表 -->
    <view class="data-list" :style="{ 'margin-top': filter_height + 'px' }">
      <my-swiper v-if="focus.length>0" :focus="focus" :autoplay="true" :interval="4000" :indicatorDots="true" :circular="true" indicatorActiveColor="#f65354" height="37vw"></my-swiper>
      <school-item
        v-for="item in lists"
        :key="item.id"
        mode="image"
        :image="item.master_pic | imageFilter('w_240')"
        :title="item.name"
        title_size="32rpx"
        :desc="item.address"
        :distance="item.distance"
        :type_name="item.type_name"
        :type="item.type"
        :tags="[{ name: item.cname }, { name: item.type_name }]"
        @click="navigateTo(`/school/detail?id=${item.id}`)"
      >
        <!-- <template>
          <text v-slot:title_right :class="'attr'+item.type">{{ item.type_name}}</text>
        </template> -->
      </school-item>
      <uni-load-more :status="load_status"></uni-load-more>
    </view>
    <view
      class="mask"
      :class="show_filter_type>0 ? 'show' : ''"
      @click="clickMask"
      @touchmove.stop.prevent="stopMove"
    ></view>
    <chat-tip></chat-tip>
    <my-dialog
      ref="dialog"
      @cancelButton="getData"
      :show="show_dialog"
      @close="show_dialog = false"
      title="温馨提示"
      openType="openSetting"
    >
      <view class="set-nickname-box">
        <view class="row">只有获取位置权限才能获取附近的房源</view>
      </view>
    </my-dialog>
   
  </view>
</template>

<script>
import mySearch from "@/components/mySearch";
import mySwiper from '@/components/mySwiper.vue';
import schoolItem from "./components/schoolItem";
import myTag from "@/components/myTag";
import {
  navigateTo,
  formatImg,
  config
} from '../common/index.js'
import wxApi from '../common/mixin/wx_api';
import myIcon from "../components/myIcon.vue";
import myDialog from "../components/dialog.vue";
import { uniListItem, uniLoadMore, uniIcons } from "@dcloudio/uni-ui";
import { mapState, mapActions } from "vuex";
export default {
  components: {
    mySearch,
    mySwiper,
    schoolItem,
    myTag,
    myIcon,
    myDialog,
    uniListItem,
    uniLoadMore,
    uniIcons
  },
  mixins:[wxApi],
  data() {
    return {
      active: 0,
      filter_list: [
        {
          name: "区域",
          type: 1,
        },
        {
          name: "学段",
          type: 2,
        },
        {
          name: "性质",
          type: 3,
        },
        // {
        //   name: "状态",
        //   type: 4,
        // },
      ],
      focus:[],
      show_filter_type: 0,
      filter_height: 0,
      load_status: "",
      load_text: {},
      params: {
        page: 1,
        rows: 20,
        area_id: 0,
        cate_id: 0,
        type: 0,
        school_status: 0,
        keywords: "",
        sort:1
      },
      lists: [],
      area_list: [],
      cate_list: [],
      type_list: [],
      status_list: [],
      show_dialog:false,
      bgImage:'',
      word1:'学校',
      word2:'划片施教范围查询',
      shaixuanList:[
        {
          title:"小学",
          cate_id:2,
          icon:'/images/school/<EMAIL>'
        },
        {
          title:"初中",
          cate_id:5,
          icon:'/images/school/<EMAIL>'
        }
      ],
      scrollTop:0,
      showPannel:false,
      scrollTopOffset:0,
    }
  },
  onLoad(options) {
    if (options.cate_id) {
      this.params.cate_id = parseInt(options.cate_id);
    }
    if (options.area_id) {
      this.params.area_id = parseInt(options.area_id);
    }
    if (options.type) {
      this.params.type = parseInt(options.type);
    }
    if (options.status) {
      this.params.school_status = parseInt(options.status);
    }
    if (options.cid) {
      this.params.cid = parseInt(options.cid);
    }
    if (options.keywords) {
      this.params.keywords = options.keywords;
    }
    this.init();
  },
  onPageScroll(e){
      this.scrollTop = e.scrollTop
      console.log(e.scrollTop);
      let top = this.scrollTop
      this.scrollTopOld= top
      setTimeout(() => {
        if (this.showPannel) {
          uni.pageScrollTo({
          scrollTop:this.scrollTopOffset,
          duration: 0
          });
        }
      }, 200);
			
		},
  computed: {
    ...mapState(["location"]),
    status_top(){
      return this.$store.state.systemInfo.statusBarHeight
    }
    // bgImage(){
    //   return  require(config.imgDomain+"/images/school/<EMAIL>")
    // }
  },
  filters: {
    imageFilter(val,param) {
      if (!val) {
        return ""
      }
      return formatImg(val, param)
    },
    formatOssImg(val){
      return config.imgDomain+val+'?x-oss-process=style/m_240'
    }
  },
  methods: {
    ...mapActions(["getLocation"]),
    init() {
      this.load_status = "loading";
      this.getWxConfig(
        ['getLocation', 'updateAppMessageShareData', 'updateTimelineShareData'],
        wx => {
          this.getLocation({
            wx,
            success: res => {
              this.getData(res)
            },
            fail: err => {
              console.log(err)
              this.getData()
            }
          })
        }
      )
      this.getFilter();
    },
    clickMask (){
      this.show_filter_type=0
      this.showPannel =false
    },
    scroppTo(fun){
      const query = uni.createSelectorQuery().in(this);
      query.select('#tab_top').fields({rect:true,scrollOffset:true},data => {
        // #ifdef H5
        this.scrollTopOffset=(this.scrollTop||0) + data.top-this.status_top,
        // #endif
        // #ifndef H5
        this.scrollTopOffset=(this.scrollTop||0) + data.top-44,
        // #endif
        uni.pageScrollTo({
          duration:120,
          // #ifdef H5
          scrollTop:(this.scrollTop||0) + data.top-this.status_top,
          // #endif
          // #ifndef H5
					scrollTop:(this.scrollTop||0) + data.top-44,
					
					// #endif
					success:()=>{
						if (fun){
							fun()
						}
					}
        })
      }).exec();
    },
    getData(location) {
      if (location) {
        this.params.lat = location.latitude;
        this.params.lng = location.longitude;
      }
      if(!this.params.lat||!this.params.lng){
        this.params.sort = ''
      }
      if (this.params.page === 1) {
        this.lists = [];
      }
      this.load_status = "loading";
      this.showPannel =false
      this.$ajax.get("school/schoolList.html", this.params, (res) => {
        this.load_status = "loadend";
        if(res.data.focus){
          this.focus = res.data.focus
        }
        if (res.data.code === 1 && res.data.list.length > 0) {
          this.lists = this.lists.concat(res.data.list);
          if (res.data.list.length < this.params.rows) {
            this.load_status = "nomore";
          }
        } else {
          this.load_status = "nomore";
        }
        if (!this.share) {
          this.share = res.data.share || {}
          this.initShare()
        }
      });
    },
    getFilter() {
      this.$ajax.get("school/schoolCondition.html", {}, (res) => {
        if (res&&res.data&&res.data.data){
          if(res.data.data.bg){
            this.bgImage =res.data.data.bg
          }else {
            this.bgImage =config.imgDomain+"/images/school/<EMAIL>"
          }
          if(res.data.data.word1){
            this.word1 =res.data.data.word1
          }
          if(res.data.data.word2){
            this.word2 =res.data.data.word2
          }
          
          
        }
        
        if (res.data.code === 1) {
          this.area_list = [{ id: 0, name: "不限" }, ...res.data.data.areaList];
          // 高亮选中当前已选中的区域
          if (this.params.area_id) {
            this.filter_list[0].name = this.area_list.filter((item) => {
              return item.id === this.params.area_id;
            })[0].name;
          }

          this.cate_list = [
            { id: 0, name: "不限" },
            ...res.data.data.schoolCateList,
          ];
          // 高亮选中当前已选中的学校分类
          if (this.params.cate_id) {
            this.filter_list[1].name = this.cate_list.filter((item) => {
              return item.id === this.params.cate_id;
            })[0].name;
          }

          this.type_list = [{ id: 0, name: "不限" }, ...res.data.data.types];
          // 高亮选中当前已选中的学校性质
          if (this.params.type) {
            this.filter_list[2].name = this.type_list.filter((item) => {
              console.log(item);
              return item.id === this.params.type;
            })[0].name;
          }

          this.status_list = [
            { id: 0, name: "不限" },
            ...res.data.data.schoolStatus,
          ];
          // 高亮选中当前已选中的学校状态
          if (this.params.school_status) {
            this.filter_list[3].name = this.status_list.filter((item) => {
              return item.id === this.params.school_status;
            })[0].name;
          }
        }
      });
    },
    onSearch() {
      this.params.page = 1;
      this.getData();
    },
    onClickFilter(item){
      
      this.show_filter_type === item.type
              ? (this.show_filter_type = 0)
              : (this.show_filter_type = item.type)
      this.show_filter_type === item.type? (this.showPannel=true):(this.showPannel=false)
      this.scroppTo(()=>{

      })
    },
    switchTab(id,name) {
      this.params.page = 1
      switch (this.show_filter_type){
        case 1:
          this.params.area_id = id
          break
        case 2:
          this.params.cate_id = id
          break
        case 3:
          this.params.type = id
          break
        case 4:
          this.params.school_status = id
          break
      }
      this.filter_list.forEach((item,index)=>{
        if(item.type === this.show_filter_type){
          this.filter_list[index].name = name
        }
      })
      this.show_filter_type = 0;
      this.getData()
    },
    handleSort(sort){
      this.scroppTo(()=>{
          let timeout =setTimeout(() => {
            this.params.page = 1
            this.params.sort = sort
            this.show_filter_type = 0;
            this.getData()
          },300)
      })
      
    },
    navigateTo(url){
      navigateTo(url)
    },
    stopMove(){
      
    }
  },
  // onPageScroll(e) {
  //   console.log(e)
  // },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getData();
  }
};
</script>

<style scoped lang="scss">
.page {
  // padding-top: 44px;
  // background-color: #fff;
}
view{
  box-sizing: border-box;
}
.fixed-top {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 100;
}
.search-box {
  margin-left: 20rpx;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #eeeeee;
  border-radius: 8rpx;
  .search-left {
    margin-right: 20rpx;
  }
}
.map-icon{
  width: 40rpx;
  height: 40rpx;
}
.search-box-right {
  // width: 210rpx;
  padding-left: 20rpx;
  padding-right: 30rpx;
  display: flex;
  align-items: center;
  text{
    margin-left: 10rpx;
    font-size: 22rpx;
  }
}
.top_bg {
  height: 400rpx;
  width: 100%;

  position: relative;
  .search{
    position: absolute;
    left: 50%;
    top: 40rpx;
    transform: translateX(-50%);
    width: 654rpx;
    height: 80rpx;
    padding: 24rpx 0 24rpx 120rpx;
    background: #ffffff;
    border: 0 solid #d8d8d8;
    box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.05);
    border-radius: 56rpx;
    box-sizing: border-box;
    input {
      font-size: 28rpx;
    }
    .search-icon {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 51rpx;
    }
  }
  .title{
    font-size: 48rpx;
    padding-top: 160rpx;
    padding-left: 48rpx;
    color: #FFFFFF;
  }
  .sub_title{
    font-size: 48rpx;
    margin-top: 8rpx;
    padding-left: 48rpx;
    color: #FFFFFF;
  }
}
.filter-box {
  position: sticky;
  top: 0;
  z-index:99;
  justify-content: space-around;
  align-items: center;
  border-bottom: 1rpx solid #dedede;
  background-color: #fff;
  .filter-item {
    padding: 20rpx;
    color: #333;
  }
  .icon-box{
    font-size: 0;
  }
}
.screen-panel {
  position: absolute;
  top: 40px ;
  display: none;
  margin-top: 0;
  display: none;
  // top: 40px;
  // margin-top: 80upx;
}
.screen-panel.show{
  top: 0;
  left: 0;
  display: block;
  transform: translateY(70rpx);
}

.data-list {
  // padding-top: 80rpx;
  background-color: #fff;
  transition: 0.3s;
  min-height: 100vh;
  .school_tip {
    flex-direction: row;
    align-items: flex-end;
    margin-bottom: 26rpx;
    font-size: 24rpx;
    .label {
      color: $uni-color-primary;
    }
    .tip {
      margin-left: 10rpx;
    }
  }
}
.attr1 {
  margin: 0 20rpx;
  font-size: 22rpx;
  padding: 6rpx 10rpx;
  line-height: 1;
  background: linear-gradient(to right, #F7918F 0%, #FB656A 100%);
  color: #fff;
}
.attr2 {
  margin: 0 20rpx;
  font-size: 22rpx;
  padding: 6rpx 10rpx;
  line-height: 1;
  background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
  color: #fff;
}

.shaixuan {
  display: flex;
  padding: 24rpx 48rpx;
  background: #ffffff;
  justify-content: flex-start;
  align-items: center;
  margin-top: -90rpx;
  &_item{
    &.first{
      border-right: 1px solid #f5f5f5;
      border-top-left-radius: 8rpx;
      border-bottom-left-radius: 8rpx;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    display: flex;
    padding: 12rpx 24rpx;
    // border: 2rpx solid #D8D8D8;
    box-shadow: 0 0 8rpx 0 rgba(0,0,0,0.04);
    border-top-right-radius: 8rpx;
    border-bottom-right-radius: 8rpx;
    justify-content: space-between;
    z-index: 3;
    background: #fff;
    &_left{
      display: flex;
      flex-direction: column;
      justify-content: center;
      &_top{
        display: flex;
        align-items: center;
        &_line{
          width: 8rpx;
          height:28rpx;
          background: #F2C560;
        }
        &_title{
          font-size: 28rpx;
          color: #333333; 
          font-weight: 600;
          margin-left: 8rpx;
        }
      }
      &_bottom{
        margin-top: 16rpx;
        font-size: 22rpx;
        color: #999999;
      }
    }
    &_right{
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .img{
        width: 96rpx;
        height: 96rpx;
        overflow: hidden;
        image{
          width:100%;
          height: 100%;
        }
      }
    }
  }
}

</style>
