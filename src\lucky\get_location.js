import { jsonp } from 'vue-jsonp'
const getLocation = function (options = {}) {
  jsonp('https://apis.map.qq.com/ws/geocoder/v1/', {
    location: `${options.latitude},${options.longitude}`,
    key: options.map_key || 'RRCBZ-3EZWO-RAYWB-SQGVU-ABATH-CYB2H',
    output: 'jsonp'
  })
    .then(res => {
      switch (res.status) {
        case 0:
          options.success && options.success(res.result.address_component)
          return
        case 110:
          uni.showToast({
            title: '请求来源未被授权',
            icon: 'none'
          })
          break
        default:
          uni.showToast({
            title: res.message,
            icon: 'none'
          })
      }
      options.fail && options.fail(res)
    })
    .catch(err => {
      options.fail && options.fail(err)
    })
}

module.exports = getLocation
