<template>
    <view class="page">
        <view class="search-box" @click="handelaTap()">
            <view class="bold">小区</view>
            <input type="text" confirm-type="search" v-model="name" style="margin-right: 30rpx;" />
            <!-- <view style="margin-right: 290rpx;">{{ title.onew }}</view> -->
            <uni-icons type="forward" size="10" color="#979797"></uni-icons>
        </view>
        
        <view class="search-box">
            <view class="bold" style="margin-right: 10rpx;">面积</view>
            <input type="number" confirm-type="search" v-model="title.two" @confirm="handleSearch()" />
            <view style="color: #979797;">平米</view>
        </view>
        <picker mode="multiSelector" @columnchange="bindMultiPickerColumnChange" :value="multiIndex" :range="multiArray">
            <view class="search-box">
            <view class="bold">户型</view>
            <view  style="margin-right: 277rpx;font-size: 34rpx;">{{ title.three }}</view>
            <uni-icons type="forward" size="10" color="#979797"></uni-icons>
        </view>
        </picker>
        <view class="search-box" @click="showAreaPopup()">
            <view class="bold">朝向</view>
            <view style="margin-right:360rpx;font-size: 34rpx">{{ title.four }}</view>
            <uni-icons type="forward" size="10" color="#979797"></uni-icons>
        </view>
        <view class="search-box search-box-title" style="position: relative;">
            <view class="bold bolder" style="white-space: nowrap;">楼层</view>
            <view class="one" >
                <input type="number" style="position: relative;top: 0;left: -50rpx;" v-model="title.five" confirm-type="search" @confirm="handleSearch()" />
            </view>
            <view class="two">层</view>
            <view class="one">
                <input type="number" confirm-type="search" style="position: relative;top: 0;left: -50rpx;"  v-model="title.six" @confirm="handleSearch()" />
            </view>
            <view class="two" style="white-space: nowrap;">总层数</view>
        </view>
        <view class="btn">
            <button type="primary" @click="handelaTmoto()">立即评估</button>
        </view>

        <view>
            <my-popup ref="area_popup">
                 <addressPicker :data_list="area_list" @onselect="onAreaChange"></addressPicker>
             </my-popup>
        </view>
        <!-- <view>
            <my-picker disabledText="不可更改请联系客服" :disabled ='params.qf_uuid' :options="form.huxing.options" @change="onPickerChange"></my-picker>
        </view> -->
        <view>
    </view>
        
    </view>
</template>

<script>
import myPicker from '../../components/myPicker.vue'
import { uniIcons } from "@dcloudio/uni-ui";
import myIcon from "@/components/myIcon";
import myPopup from '../../components/myPopup.vue'
import addressPicker from '../../components/addressPicker.vue';
export default {
    components: {
        myIcon,
        uniIcons,
        myPicker,
        addressPicker,
        myPopup,
        myPicker,
    },
    data(){
        return {
            title:{
                one:"",
                two:"",
                three:"",
                four:"",
                five:"",
                six:"",
                seven:"",
                shi:"1",
                ting:"1",
                wei:"1",
            },
            multiArray: [],
            multiIndex: [0, 0, 0],
            name:"",
            id:0,
            area_list:[],
            data_list:[],
            huxing: {
            type: 'picker',
            value: [],
            options: [
                {
                label: '户型',
                value: [],
                required: true,
                range: [
                    {
                    title: '室',
                    identifier: 'shi',
                    rules: []
                    },
                    {
                    title: '厅',
                    identifier: 'ting',
                    rules: []
                    },
                    {
                    title: '卫',
                    identifier: 'wei',
                    rules: []
                    }
                ]
                },
                {
                label: '朝向',
                value: [],
                required: true,
                range: [
                    {
                    title: '',
                    identifier: 'chaoxiang',
                    rules: []
                    }
                ]
                },
                {
                label: '装修',
                value: [],
                required: true,
                range: [
                    {
                    title: '',
                    identifier: 'zhuangxiu',
                    rules: []
                    }
                ]
                }          
            ]
            },
        }
    },
    onLoad(e){
        console.log(e)
        this.name=e.cate
        this.id = parseFloat(e.query)
        this.load = e.cate
        this.handelelist()
    },
    methods: {
        bindMultiPickerColumnChange: function(e) {
            if(e.detail.column===0){
                this.title.shi = e.detail.value+1
            }
            if(e.detail.column===1){
                this.title.ting = e.detail.value+1
            }
            if(e.detail.column===2){
                this.title.wei = e.detail.value
            }
            this.multiIndex[e.detail.column] = e.detail.value;
            this.title.three = this.title.shi+"室"+this.title.ting+"厅"+this.title.wei+"卫"
            this.$forceUpdate();
        },
        showAreaPopup(){
            this.$refs.area_popup.show()
        },
        //  朝向
        onAreaChange(e){
            console.log(e)
            this.title.four= e[e.length-1].name
            this.$refs.area_popup.hide()
            this.title.seven = e[e.length-1].value
    },
         //  房型
        onPickerChange(e) {
         e.forEach(item => {
        item.forEach(el => {
          this.params[el.identifier] = el.value
        })
      })
      // this.setTitle()
    },
        handelaTap() {           
            this.$navigateTo('/room/assess/sellsersh')
        }, 
        handelelist(){
            this.$ajax.get('fangjia/fangjiaEstimateForm ',{},res=>{
                if(res.data.code==1){  
                        console.log(res)
                        let arr= []
                        for(let key in res.data.estimateForm.shi){
                            arr.push(key+"室")
                        }
                        this.multiArray[0] = arr  
                        let orr =[]
                        for(let key in res.data.estimateForm.ting){
                            orr.push(key+"厅")
                        } 
                        this.multiArray[1] = orr   
                        let nrr =[]
                        for(let key in res.data.estimateForm.ting){
                            nrr.push(key-1+"卫")
                        } 
                        this.multiArray[2] = nrr     
                    this.area_list =Object.values(res.data.estimateForm.chaoxiang).map((item,index)=>{
                        return {'areaname':item,areaid:index+1}
                    })
                }
            })
        },    
        // 立即评估
        handelaTmoto(){
            if(this.name!=this.load){
                uni.showToast({
                    title:'请选择小区',
                    icon:"error"
            })
            }else if(this.title.two==""){
                uni.showToast({
                    title:'请填写面积',
                    icon:"error"
            })
            }
            else if(this.title.three==""){
                uni.showToast({
                    title:'请填写户型',
                    icon:"error"
            })
            }else if(this.title.four==""){
                uni.showToast({
                    title:'请填写朝向',
                    icon:"error"
            })
            }else if(this.title.five==""){
                uni.showToast({
                    title:'请填写楼层',
                    icon:"error"
            })
            }else if(this.title.six==""){
                uni.showToast({
                    title:'请填写楼层',
                    icon:"error"
            })
            }else if(this.title.six<this.title.five){
                uni.showToast({
                    title:'所在楼层不能大于总层数',
                    icon:"error"
                })
            }
            else{
                this.$ajax.post( `fangjia/postFangjiaEstimate?community_id=${this.id}&size=${this.title.two}&shi=${this.title.shi}&ting=${this.title.ting}&wei=${this.title.wei}&chaoxiang=${this.title.seven}&floor=${this.title.five}&total_floor=${this.title.six}`,{} ,res=>{
                    console.log(res)
                    if(res.data.code===1){

                        this.$navigateTo("/room/assess/relestate?query="+res.data.entrust_id)
                    }
                })
            }
        }
    }
}
</script>

<style scoped lang="scss">
body {
    background-color: white !important;
}

.page {
    min-height: calc(100vh - 110rpx);
    background: white;
    border-top: 1rpx solid rgba(240, 240, 240, 1);
    box-sizing: border-box;
    .btn uni-button {
        background-color: #446EEF !important;
        width: 654rpx !important;
        height: 96rpx;
    }

    .btn {
        margin-top: 124rpx;
    }
}

.search-box {
    width: 654rpx;
    height: 116rpx;
    // background: red;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid rgba(240, 240, 240, 1);

    .one {
        font-size: 32rpx;
        color: #3a3a3a;
    }

    .two {
        font-size: 28rpx;
        color: #979797;
    }
}

view:nth-child(1) {
    color: #232323;
    font-size: 32rpx;
}

.uni-icons {
    color: #979797;
    width: 24rpx;
    height: 24rpx;
}

.bold {
    font-family: PingFang SC;
    font-weight: medium;
}

.search-box-title .uni-input-form {
    width: 10rpx !important;
}

.search-box-title .uni-input-input {
    width: 10rpx !important;
}

.bolder {
    width: 80rpx !important;
    display: flex;
}

.one {
    margin-left: 30rpx;
}

.one input {
    margin-left: 100rpx;

}
.bottom-line{
    color: black!important
}

</style>