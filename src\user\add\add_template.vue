<template>
  <view class="page">
    <view class="text-box">
      <view class="select_row">
        <my-select
            :value="detail.cate_id"
            @change="pickerChange"
            :range="cate_list"
            placeholder="请选择分类"
          ></my-select>
      </view>
      <view class="label">
        <input type="text" placeholder="模板名称" v-model="detail.name">
      </view>
      <textarea v-model="detail.content" ows="5" maxlength="300" placeholder="请输入模板内容"></textarea>
      <view class="footer flex-box">
        <text class="select_btn"></text>
        <view>
          <text>{{detail.content.length}}/300</text>
        </view>
      </view>
    </view>
    <view class="note" v-if="detail.cate_id>0">
      <text>{{default_content[detail.cate_id-1]}}</text>
      <view class="copy_btn" @click="handleCopy">快速复制</view>
    </view>
    <view class="btn_group">
      <view class="add_btn" @click="handleAdd">确定</view>
    </view>
  </view>
</template>

<script>
import mySelect from '../../components/form/mySelect'
export default {
  name: '',
  components: {
    mySelect
  },
  data () {
    return {
      cate_list: [],
      detail: {
        cate_id: 1,
        content: ''
      },
      default_content: [
        `1.产权优势：本房产权无纠纷
2.价格优势：价格低在同面积同楼层价格相比性价比较高。
3.楼层优势：得房率高，适合人群广。
4.配合优势：业主比较配合，看房可提前联系
5.配套优势：小区配套齐全成熟商圈
6.交通优势：出行方面，较为方便`,
`业主诚心出售此房，积极配合看房和达成协议后的各项手续。产权清晰无纠纷，看房方便，房主诚意出售`,
`我从事房产工作已有多年，经验丰富。
1:能够精准的根据每一位客户的详细需求匹配到性价比合适的
2:和业主议价是我的强项，让每位客户真正的省钱
3:我们的服务能让您物超所值
4:您的买房安家之旅，让您更加 安心 放心 省心
希望我的专业负责可以帮助到您！选择我为您服务让您无怨无悔！欢迎进店咨询！`
      ]
    }
  },
  onLoad(options){
    this.detail.cate_id = +options.cate_id||1
    this.getCates()
  },
  filters: {
    tipContent(value){
      if(!value){
        return ""
      }
      var content = ""
      switch (value){
        case 1:
          content = `1.产权优势：本房产权无纠纷
2.价格优势：价格低在同面积同楼层价格相比性价比较高。
3.楼层优势：得房率高，适合人群广。
4.配合优势：业主比较配合，看房可提前联系
5.配套优势：小区配套齐全成熟商圈
6.交通优势：出行方面，较为方便`
          break
        case 2:
          content = `业主诚心出售此房，积极配合看房和达成协议后的各项手续。产权清晰无纠纷，看房方便，房主诚意出售`
          break
        case 3:
          content = `我从事房产工作已有多年，经验丰富。
1:能够精准的根据每一位客户的详细需求匹配到性价比合适的
2:和业主议价是我的强项，让每位客户真正的省钱
3:我们的服务能让您物超所值
4:您的买房安家之旅，让您更加 安心 放心 省心
希望我的专业负责可以帮助到您！选择我为您服务让您无怨无悔！欢迎进店咨询！`
          break
        default: 
        content = ""
      }
      return content
    }
  },
  methods: {
    getCates(){
      this.$ajax.get('release/releaseTemplateByCate', {}, res=>{
        if(res.data.code ===1){
          this.cate_list = res.data.list.map(item=>{
            var _item = {
              name: item.cate_name,
              value: item.id
            }
            return _item
          })
          if(!this.detail.cate_id){
            this.detail.cate_id = this.cate_list[0].value
          }
        }
      })
    },
    pickerChange(e){
      this.detail.cate_id = e.value
    },
    handleCopy(){
      if(this.detail.cate_id>0){
        this.detail.content = this.default_content[this.detail.cate_id-1]
      }else{
        uni.showToast({
          title: "请选择模板分类",
          icon: 'none'
        })
      }
    },
    handleAdd(){
      this.$ajax.post('release/saveReleaseTemplate', this.detail, res=>{
        if(res.data.code === 1){
          uni.showToast({
            title: '添加成功'
          })
          setTimeout(()=>{
            this.$navigateBack()
          }, 2000)
          uni.$emit('recapture')
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.page{
  min-height: calc(100vh - 44px);
  background-color: #fff;
}
.text-box{
  padding: 0 48rpx;
  padding-top: 24rpx;
  background-color: #fff;
  .select_row{
     ::v-deep .select-box{
      padding: 24rpx 20rpx;
      border-radius: 4rpx;
      background-color: #f7f7f7;
      .select-row{
        background-color: #f7f7f7;
        picker view{
          font-size: 28rpx;
        }
      }
    }
     ::v-deep .novalue{
      font-size: 28rpx;
    }
  }
  .label{
    padding: 24rpx 0;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 40rpx;
    color: #333;
  }
  input{
    width: 100%;
    height: 44px;
    box-sizing: border-box;
    padding: 20rpx;
    border-radius: 4rpx;
    font-size: 28rpx;
    background-color: #f8f8f8;
  }
  textarea{
    width: 100%;
    height: 300rpx;
    box-sizing: border-box;
    padding: 20rpx;
    line-height: 1.5;
    border-radius: 4rpx;
    font-size: 28rpx;
    background-color: #f8f8f8;
  }
  .footer{
    justify-content: space-between;
    padding: 20rpx 0;
    color: #999;
    .select_btn{
      color: $uni-color-primary;
    }
    .clear{
      color: #999;
      padding: 0 48rpx;
    }
  }
}

.btn_group{
  padding: 48rpx;
  .add_btn{
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #fff;
    background: #FB656A;
    box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
  }
}
.note{
  margin: 24rpx 48rpx;
  padding: 12px;
  border-radius: 4px;
  overflow: hidden;
  line-height: 1.8;
  background-color: rgba(255, 101, 107, 0.15);
  border-left: 3px solid $uni-color-primary;
  color: #333;
  position: relative;
  padding-bottom: 72rpx;
  .copy_btn{
    padding: 2rpx 10rpx;
    width: 120rpx;
    text-align: center;
    font-size: 24rpx;
    border-radius: 6rpx;
    color: $uni-color-primary;
    border: 1rpx solid $uni-color-primary;
    position: absolute;
    right: 24rpx;
  }
}
</style>