<template>
<view class="page">
    <my-swiper :focus="focus" :autoplay="true" :interval="4000" :indicatorDots="true" :circular="true" indicatorActiveColor="#f65354" height="44vw"></my-swiper>
    <tab-bar :tabs="tabs" :nowIndex="nowTabIndex" :fixedTop="false" ref="tab_bar" @click="switchTab"></tab-bar>
    <home-list-item v-for="item in dataList" :key="item.id" :item="item" from="list"></home-list-item>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
</view>
</template>

<script>
import mySwiper from '../../components/mySwiper'
import {
    navigateTo,
    formatImg
} from '../../common/index.js'
import homeListItem from '../../components/homeListItem'
import tabBar from '../../components/tabBar'
import {
    uniLoadMore
} from '@dcloudio/uni-ui'
import {wxShare} from '../../common/mixin'
export default {
    data() {
        return {
            focus: [],
            tabs: [{
                name: "效果图",
                type: 1
            }, {
                name: "看工地",
                type: 2
            }, {
                name: "优惠活动",
                type: 3
            }],
            nowTabIndex:0,
            dataList: [],
            get_status: "loading",
            content_text: {
                contentdown: "",
                contentrefresh: "正在加载...",
                contentnomore: "没有更多数据了"
            }
        }
    },
    mixins:[wxShare],
    components: {
        mySwiper,
        homeListItem,
        tabBar,
        uniLoadMore
    },
    onLoad(options) {
        this.params = {
            page: 1,
            rows: 20,
            type: options.type || 1
        }
        this.getFocus()
        this.getData()
    },
    onReady() {
        this.tabs.forEach((item, index) => {
            if (item.type == this.params.type) {
                this.nowTabIndex = index
            }
        })
    },
    methods: {
        getData() {
            this.get_status = "loading"
            if (this.params.page == 1) {
                this.dataList = []
            }
            this.$ajax.get('memberShop/caseList.html', this.params, res => {
                if (res.data.code == 1) {
                    this.dataList = this.dataList.concat(res.data.list)
                    if (res.data.list.length < this.params.rows) {
                        this.get_status = "noMore"
                    } else {
                        this.get_status = "more"
                    }
                } else {
                    this.get_status = "noMore"
                    this.params.page > 1 ? this.params.page-- : this.params.page = 1
                }
                if(res.data.share){
                    this.share = res.data.share
                    this.getWxConfig()
                }else{
                    this.share = {}
                }
            }, err => {
                console.log(err)
            })
        },
        getFocus(){
            this.$ajax.get('memberShop/focus',{},res=>{
                if(res.data.code == 1){
                    this.focus = res.data.focus
                }
            })
        },
        switchTab(e) {
            this.nowTabIndex = e.index
            this.params.page = 1
            this.params.type = e.type
            this.getData()
        }
    },
    onReachBottom() {
        this.params.page++
        this.getData()
    },
    onShareAppMessage(){
        if(this.seo){
             return {
                title:this.seo.title||"",
                image:this.seo.image||""
            }
        }else {
             return {
                title:"家装"
            }
        }
       
    }
}
</script>
