<template>
    <view class="chat-content" :class="{ 'show-plus': show_plus, 'show-statement': show_statement || show_face }">
        <view class="card-box">
            <!-- 对方不是普通用户 -->
            <template v-if="friend_info.levelid > 1 || friend_info.adviser > 0">
                <agent-card :infoData="friend_info" :open="show_all">
                    <template v-slot:options="{ infoData }">
                        <view class="operation-box flex-box">
                            <view class="operation" @click="toTel(friend_info)"
                                v-if="(friend_info.adviser > 0 && switch_adviser_tel) || (friend_info.levelid > 1 && (!friend_info.adviser > 0))">
                                <my-icon type="ic_tel" size="42rpx" color="#ff656b"></my-icon>
                                <text class="text">电话咨询</text>
                            </view>
                            <view class="operation" @click="applyWechat()">
                                <my-icon type="weixin" size="42rpx" color="#09de74"></my-icon>
                                <text class="text">微信号</text>
                            </view>
                            <view class="operation" @click="toMore()">
                                <my-icon type="ic_fabu" size="24" color="#333333"></my-icon>
                                <text class="text">更多</text>
                            </view>
                        </view>
                    </template>
                    <template>
                        <view class="btn top-line" v-if="show_all" @click="show_all = false">
                            <text>收起</text>
                            <!-- <my-icon type="jinru" color="#ff706f"></my-icon> -->
                        </view>
                        <view class="btn" v-else @click="show_all = true">
                            <text>展开</text>
                            <!-- <my-icon type="jinru" color="#ff706f"></my-icon> -->
                        </view>
                    </template>
                </agent-card>
            </template>
            <!-- 对方是普通用户 -->
            <template v-else>
                <agent-card :infoData="friend_info" :open="show_all" :staInfo="false">
                    <template v-slot:options="{ infoData }">
                        <view class="operation-box flex-box">
                            <view class="operation" @click="applyTel()">
                                <my-icon type="ic_tel" size="48rpx" color="#ff656b"></my-icon>
                                <text class="text">邀请致电</text>
                            </view>
                            <view class="operation" @click="inviteEvaluate(infoData)">
                                <my-icon type="ic_huifu" size="48rpx" color="#09de74"></my-icon>
                                <text class="text">邀请评价</text>
                            </view>
                            <view class="operation" @click="toMore()">
                                <my-icon type="ic_fabu" size="48rpx" color="#333333"></my-icon>
                                <text class="text">更多</text>
                            </view>
                        </view>
                    </template>
                    <template>
                        <view class="btn top-line" v-if="show_all" @click="show_all = false">
                            <text>收起</text>
                            <!-- <my-icon type="jinru" color="#ff706f"></my-icon> -->
                        </view>
                        <view class="btn" v-else @click="show_all = true">
                            <text>展开</text>
                            <!-- <my-icon type="jinru" color="#ff706f"></my-icon> -->
                        </view>
                    </template>
                </agent-card>
            </template>
        </view>
        <scroll-view class="scroll" :class="{ 'has-info': !show_all, 'show-info': show_all }" scroll-y :upper-threshold="20"
            :scroll-top="scrollTop" :scroll-into-view="el" :scroll-with-animation="scrollAnimation"
            @click="onClickScroll()" @touchmove="show_from = false">
            <view id="chat-lists">
                <view id="loadmore" @click="getMore()">
                    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
                </view>
                <view v-for="(item, index) in im.nowChat.chatList" :key="index" :id="'e' + item.id">
                    <view class="time"
                        v-if="(index === 0 || (index > 1 && (item.ctime - im.nowChat.chatList[index - 1].ctime > 60))) && !(item.is_my === 0 && item.type === 'wechat')">
                        {{ item.time }}</view>
                    <chat-item :item="item" :index="index" :paly_voice="index === play_voice_index"
                        :myHeader="im.myChatInfo.headimage" :friendHeader="im.nowChat.headimage" @agree="handleAgree"
                        @viewimg="viewImg" @playvoice="playVoice($event, index)"
                        @onImgLoadErr="handleImgLoadErr"></chat-item>
                </view>
            </view>
        </scroll-view>
        <!-- 发送楼盘或房源信息 -->
        <view class="from-info flex-box" v-if="show_from">
            <view class="img-box">
                <image :src="info_data.image | imageFilter('w_240')"></image>
            </view>
            <view class="title flex-1">{{ info_data.title }}</view>
            <view class="btn" @click="sendBuild()">发送信息</view>
        </view>
        <view class="input-box top-line" :class="{ 'show-plus': show_plus, 'show-statement': show_statement || show_face }">
            <view class="quick_btn-list flex-box">
                <view class="item" @click="toogleStatement()">常用语</view>
                <view class="item"
                    v-if="(friend_info.adviser > 0 && switch_adviser_tel) || (friend_info.levelid > 1 && (!friend_info.adviser > 0))"
                    @click="toTel(friend_info)">电话咨询</view>
                <view class="item" v-if="!(friend_info.levelid > 1 || friend_info.adviser > 0)" @click="applyTel()">邀请致电
                </view>
                <view class="item" v-if="friend_info.levelid > 1 || friend_info.adviser > 0" @click="applyWechat()">查看微信号
                </view>
                <view class="item" v-else @click="inviteEvaluate(friend_info)">邀请评价</view>
                <view class="item" @click="chooseLocation()">发送位置</view>
            </view>
            <view class="send-box flex-box">
                <!-- <input type="text" confirm-type="send" :cursor-spacing="10" v-model="send_content" placeholder="请输入内容" class="flex-1" /> -->
                <view class="statement-btn" hover-class="btn-hover" @click="switchVoiceText()">
                    <my-icon :type="show_voice_btn ? 'jianpan' : 'ic_yuyin'" color="#333" size="60rpx"></my-icon>
                </view>
                <view class="type-box flex-1">
                    <!-- <textarea type="text" fixed :show-confirm-bar="false" :cursor-spacing="20" v-model="send_content" :class="{'in-ios':isiOS}"></textarea> -->
                    <!-- <text class="palceholder_text" v-show="send_content===''">想说点什么?</text> -->
                    <view class="voice_btn" v-if="show_voice_btn" @longtap="onLongTapVoiceBtn"
                        @touchmove="onTouchMoveVoicBtn" @touchend="onTouchEndVoiceBtn">长按发送语音</view>
                    <input v-else class="msg_input" type="text" :cursor-spacing="15" confirm-type="send"
                        @confirm="handleSend(send_content)" v-model="send_content" placeholder=" " />
                </view>
                <view class="plus-btn" hover-class="btn-hover" @click="toogleFace()">
                    <my-icon type="ic_biaoqing" color="#333" size="60rpx"></my-icon>
                </view>
                <button class="btn" v-if="send_content" @click="handleSend(send_content)"
                    hover-class="btn-hover">发送</button>
                <view v-else class="plus-btn" hover-class="btn-hover">
                    <view v-if="!allow_send_img" @click="tipNoPermission()">
                        <my-icon type="ic_tianjia" color="#333" size="60rpx"></my-icon>
                    </view>
                    <my-upload v-else showSlot @uploadDon="uploadDon" @chooseDon="chooseDon" :chooseType="1">
                        <my-icon type="ic_tianjia" color="#333" size="60rpx"></my-icon>
                    </my-upload>
                </view>
                <!-- <view v-else class="plus-btn" hover-class="btn-hover" @click="tooglePlus()">
                    <my-icon type="ic_tianjia" color="#333" size="60rpx"></my-icon>
                </view> -->
            </view>
            <!-- 发送图片和位置 -->
            <view v-show="show_plus" class="plus-box flex-box">

                <view class="plus-item text-center" v-if="!allow_send_img" @click="tipNoPermission()">
                    <view>
                        <my-icon type="tupian" size="30"></my-icon>
                        <view>图片</view>
                    </view>
                </view>
                <view class="plus-item text-center" v-else>
                    <my-upload showSlot @uploadDon="uploadDon" @chooseDon="chooseDon" :chooseType="1">
                        <my-icon type="tupian" size="30"></my-icon>
                        <view>图片</view>
                    </my-upload>
                </view>
                <view class="plus-item text-center" @click="chooseLocation()">
                    <my-icon type="ditu" size="30"></my-icon>
                    <view>位置</view>
                </view>
            </view>
            <!-- 快捷回复 -->
            <view v-show="show_statement" class="statement-box">
                <view class="statement-list">
                    <view v-for="(item, index) in statementList" :key="index"
                        class="statement-item flex-box bottom-line">
                        <text @click="handleSend(item.content)">{{ item.content }}</text>
                        <view class="options flex-box" v-if="manage_statement">
                            <view class="icon-box" @click="$navigateTo('/chatPage/chat/edit_statement?id=' + item.id)">
                                <my-icon type="ic_luru" color="#666"></my-icon>
                            </view>
                            <view class="icon-box" @click="handelDelStatement(item.id)">
                                <my-icon type="yichu" color="#ff656b"></my-icon>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="add-statement flex-box">
                    <text class="flex-1 text-center" v-if="manage_statement" @click="manage_statement = false">完成</text>
                    <template v-else>
                        <text class="flex-1 text-center"
                            @click="$navigateTo('/chatPage/chat/add_statement')">+添加常用语</text>
                        <text class="manage_btn" @click="manage_statement = true">管理</text>
                    </template>
                </view>
            </view>
            <!-- 表情 -->
            <view v-show="show_face" class="statement-box">
                <view class="face-list">
                    <view class="face" v-for="(face, index) in face_list" :key="index" @click="selectFace(face)">
                        <image :src="'https://images.tengfangyun.com/images/face/' + index + '.png'" mode="widthFix">
                        </image>
                    </view>
                    <view class="face_seat" v-for="item in ['_1', '_2', '_3', '_4', '_5', '_6']" :key="item"></view>
                </view>
            </view>
        </view>
        <!-- 录音中提示 -->
        <view class="voiced" :class="{ show: show_voice }">
            <image class="voice_img" mode="widthFix"
                :src="show_voice ? '/static/icon/voice/recorde.gif' : '/static/icon/none.png'">
            </image>
            <view class="tip">手指上滑取消发送</view>
        </view>
        <view class="voiced" :class="{ show: show_cancel_voice }">
            <image class="voice_img" mode="widthFix" :src="'/static/icon/voice/quxiao.png'"></image>
            <view class="tip">松开手指取消发送</view>
        </view>
        <!-- 评价弹窗 -->
        <my-dialog ref="dialog" @confirmButton="subEvaluate()" :show="show_dialog" @close="show_dialog = false"
            title="评价本次服务" confirmText="提交评价">
            <view class="evaluate-box">
                <view class="start-list flex-box">
                    <view classs="tart flex-1" v-for="(item, index) in [1, 2, 3, 4, 5]" :key="index"
                        @click="clickStart(index)">
                        <my-icon :type="evaluate >= index + 1 ? 'ic_shoucang_red' : 'ic_shoucang'"
                            :color="evaluate >= index + 1 ? '#fda613' : '#666666'" size="30"></my-icon>
                    </view>
                </view>
                <view class="evaluate-tip">{{ evaluate_tip[evaluate - 1] }}</view>
            </view>
        </my-dialog>
        <!-- 消息通知 -->
        <my-popup ref="popup" :showMask="false" position="top">
            <view class="notice" @click="toFriends()">
                {{ notice_msg | formatMsg }}
            </view>
        </my-popup>
        <!-- 断线提示 -->
        <my-popup ref="socket_colse" :showMask="false" position="top">
            <view class="flex-box err-tip">
                <view class="tip-text">聊天连接已断开</view>
                <view class="tip-btn" @click="connectChatAgain()">点击重连</view>
            </view>
        </my-popup>
        <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
    </view>
</template>

<script>
import { formatImg, showModal } from '../../common/index'
import myIcon from '../../components/myIcon'
import chatItem from '../components/chatItem'
import agentCard from '../../components/agentCard'
import chat from '../../common/chat_mixin'
import myUpload from '../../components/form/myUpload'
import myPopup from '../../components/myPopup'
import myDialog from '../../components/dialog'
import { uniLoadMore } from '@dcloudio/uni-ui'
import allTel from '../../common/all_tel.js'
import getChatInfo from '../../common/get_chat_info'
import {
    mapState
} from 'vuex'
// import getChatInfo from '../../common/get_chat_info'
// const recorderManager = uni.getRecorderManager();
const innerAudioContext = uni.createInnerAudioContext();
import Recorder from 'js-audio-recorder';
let recorder = new Recorder(
    {
        sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
        sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
        numChannels: 1, // 声道，支持 1 或 2， 默认是1
    }
);
export default {
    data() {
        return {
            isiOS: false,
            scrollTop: 10000,
            el: "",
            scrollAnimation: false,
            send_content: "",
            show_from: false,
            friend_info: {
                levelid: 1,
                adviser: 0
            },
            badwords: [],
            show_all: true,
            list: [],
            show_plus: false,
            show_statement: false,
            show_face: false,
            statementList: [],
            manage_statement: false,
            notice_msg: {},
            get_status: "more",
            content_text: {
                contentdown: "点击加载更多",
                contentrefresh: "正在加载...",
                contentnomore: "没有更多数据了"
            },
            page: 1,
            info_data: {},
            face_list: ['微笑', '大笑', '笑哭', '开心', '呲牙', '坏笑', '欣慰', '鄙视', '白眼', '飞吻', '鬼脸', '酷', '爱财', '调皮', '惊讶', '无表情', '思考', '亲亲', '喜欢', '低沉', '怒', '生气', '超爱', '大哭', '小声', '惊恐', '爱心', '心碎', '偷看', 'OK', '耶', '大拇指', '握拳', '强壮'],
            open_voice: true, // 是否开启语音功能
            allow_send_voice: 1, //是否允许用户发送语音
            allow_send_img: 1, //是否允许用户发送图片
            recorder_max_time: 60000, // 录音最大时长
            show_dialog: false,
            evaluate: 5,
            evaluate_tip: ['差评', '失望', '一般', '好评', '非常好'],
            voice_index: 0,
            show_voice_btn: false,
            show_voice: false,
            show_cancel_voice: false,
            play_voice_index: -1, //播放音频的消息索引值
            is_auto_replay: 0, //是否已经自动回复
            use_wx_voice: true, //是否使用微信jssdk的录音功能
            tel_res: {},
            show_tel_pop: false
        }
    },
    components: {
        myIcon,
        agentCard,
        chatItem,
        myUpload,
        myPopup,
        myDialog,
        uniLoadMore
    },
    mixins: [chat],
    computed: {
        ...mapState(['im', 'user_info', 'switch_adviser_tel']),
        friend_userid() {
            if (this.friend_info.id || this.$store.state.im.nowChat.user_id) {
                return this.friend_info.id || this.$store.state.im.nowChat.user_id
            } else {
                return ''
            }
        }
    },
    onLoad(options) {
        this.getWxConfig(['startRecord', 'stopRecord', 'onVoiceRecordEnd', 'playVoice', 'pauseVoice', 'stopVoice', 'onVoicePlayEnd', 'uploadVoice', 'downloadVoice'], (wx) => {
            console.log('初始化wxjssdk成功')
            this.wx = wx
        })
        uni.setNavigationBarTitle({
            title: options.title || '暂无昵称'
        })
        this.getSendAuth()
        this.userToken = uni.getStorageSync('token')
        let page_arr = getCurrentPages().map(item => {
            return item.route
        })
        // 获取上一个页面信息 新房、二手房、出租房等
        // this.info_data = this.$store.state.buildInfo
        // if(this.info_data.title&&this.info_data.id&&this.info_data.type){
        //     this.show_from = true
        // }
        // 触发页面滚动
        uni.$on('handleScroll', this.setScrollTop)
        // 接收到其他好友的消息
        uni.$on('showNotice', (data) => {
            this.showNotice(data)
        })
        uni.$once('onInviteEvaluate', (e) => {
            this.show_dialog = true
        })
        // 编辑或添加新的快捷回复后重新获取快捷回复语
        uni.$on('getStatementAgain', () => {
            this.getStatement()
        })
        this.isiOS = this.$store.state.systemInfo.system.includes('iOS')
        // #ifdef MP-WEIXIN
        uni.hideShareMenu()
        // #endif
        this.to_id = options.to_id || this.im.nowChat.platform_id
        if (options.user_id) {
            this.user_id = options.user_id
            this.getFriendInfo()
        }
        if (options.chat_type) {
            this.chat_type = options.chat_type
        }
        if (options.bid) {
            this.bid = options.bid
        }
        this.initChat()
        // uni.onKeyboardHeightChange(res => {
        //     console.log(res.height)
        // })
        // 监听语音播放
        innerAudioContext.onPlay(() => {
            this.play_voice_index = this.current_voice_index
        })
        // 监听语音播放停止
        innerAudioContext.onStop(() => {
            this.play_voice_index = -1
        })
        // 监听语音自然播放结束事件
        innerAudioContext.onEnded(() => {
            this.play_voice_index = -1
        })
        // 监听语音播放失败事件
        innerAudioContext.onError(() => {
            uni.showToast({
                title: '播放失败，请稍后重试',
                icon: 'none'
            })
            this.play_voice_index = -1
        })
    },
    onReady() {
        this.page_read = true
        // setTimeout(()=>{
        //     this.setScrollTop(true)
        // },100)
    },
    filters: {
        formatMsg(val) {
            let from_nickname = val.from_nickname || ''
            if (val.type === "image") {
                return from_nickname + ":[图片]"
            }
            if (val.type === "text") {
                return from_nickname + ":" + val.content
            }
            if (val.type === "map") {
                return from_nickname + ":[位置]" + (val.content.address || "")
            }
            if (val.type === "build") {
                return from_nickname + ":[楼盘]" + (val.content.address || "")
            }
            return `来自${from_nickname}的消息：${val.content}`
        },
    },
    methods: {
        getSendAuth() {
            this.$ajax.get('im/checkImAuth', {}, res => {
                if (res.data.code === 1) {
                    this.allow_send_voice = res.data.allow_audio
                    this.prohibit_send_voice_tip = res.data.allow_audio_msg
                    this.allow_send_img = res.data.allow_pic
                    this.prohibit_send_img_tip = res.data.allow_pic_msg
                }
            })
        },
        getFriendInfo() {
            uni.$on("updateStatement", (statementList) => {
                this.statementList = statementList
            })
            this.$ajax.get('im/contactDetails.html', { user_id: this.user_id }, res => {
                if (res.data.badwords) {
                    this.badwords = res.data.badwords.split(',')
                } else {
                    this.badwords = []
                }
                if (res.data.code === 1) {
                    this.friend_info = res.data.member
                    if (res.data.member.nickname) {
                        uni.setNavigationBarTitle({
                            title: res.data.member.nickname
                        })
                    }
                }
            }, err => {

            })
        },
        // 初始化聊天
        initChat() {
            // console.log(this.im.nowChat.chat_id)
            if (this.im.nowChat.chat_id) {
                this.chat_id = this.im.nowChat.chat_id
                this.getChatLogs()
                if (!this.im.socketOpen) {
                    console.log("执行聊天初始化")
                    this.initMsg = { flag: 'init', from_id: this.im.myChatInfo.platform_id }
                    this.connectChat()
                    // this.onMessage()
                    this.onClose()
                    this.onSocketError()
                } else {
                    console.log("聊天已经是连接状态")
                }
            } else {
                if (this.chat_type && this.user_id) {
                    uni.showLoading({
                        title: "正在连接中...",
                        mask: true
                    })
                    getChatInfo(this.user_id, this.chat_type, this.bid || '', false, () => {
                        uni.hideLoading()
                        this.initChat()
                    })
                } else {
                    uni.showToast({
                        title: "请手动动发起聊天",
                        icon: 'none'
                    })
                }
                // if(this.user_id){
                //     getChatInfo(this.user_id)
                // }
                console.log("无this.im.nowChat.chat_id", this.im.nowChat)
            }
            if (this.im.nowChat.uncount) {
                this.im.nowChat.uncount = 0
            }
        },
        // socket链接成功的回调
        socketOnOpen() {
            // 检测是否有需要自动发送的楼盘或信息
            this.info_data = this.$store.state.buildInfo
            if (this.info_data.title && this.info_data.id && this.info_data.type) {
                this.sendBuild()
            }
            // 检测是否有需要自动发送的消息
            if (this.$store.state.autoSendMsg) {
                this.sendMessage(this.$store.state.autoSendMsg, 'text')
                this.$store.state.autoSendMsg = ''
            }
        },
        getStatement() {
            this.$ajax.get('im/expressLanguage.html', { to_id: this.friend_userid }, res => {
                if (res.data.code === 1) {
                    this.statementList = res.data.express_language
                }
            })
        },
        clearUncount() {
            this.$ajax.get('im/clearUnread.html', { chat_id: this.chat_id }, (res) => {
                if (res.data.uncount) {
                    uni.setTabBarBadge({
                        index: 3,
                        text: res.data.uncount > 99 ? '99+' : res.data.uncount + ''
                    })
                } else {
                    uni.removeTabBarBadge({
                        index: 3
                    })
                }
            })
        },
        toTel(infoData) {
            let tel
            if (!infoData) {
                tel = this.info_data.tel
            } else {
                tel = infoData.tel
            }
            this.tel_params = {
                type: '',
                callee_id: this.friend_userid,
                scene_type: '',
                scene_id: this.friend_userid,
                success: (res) => {
                    this.tel_res = res.data
                    this.show_tel_pop = true
                }
            }
            if (this.im.istelcall == 1) {
                if (this.friend_info.adviser > 0) {
                    this.tel_params.callee_id = this.friend_info.adviser
                    this.tel_params.scene_id = this.friend_info.adviser
                    this.tel_params.type = 2
                    this.tel_params.scene_type = 2
                } else if (this.friend_info.levelid > 1) {
                    this.tel_params.callee_id = this.friend_userid
                    this.tel_params.scene_id = this.friend_userid
                    this.tel_params.type = 3
                    this.tel_params.scene_type = 3
                }
                allTel(this.tel_params)
            } else {
                uni.makePhoneCall({
                    phoneNumber: tel
                });
                this.$ajax.get("im/callUpStatistics", { id: this.friend_userid, tel, type: 7 }, () => { })
            }
        },
        retrieveTel() {
            allTel(this.tel_params)
        },
        handelDelStatement(id) {
            showModal({
                title: '提示',
                content: '确定删除吗？',
                confirm: () => {
                    this.$ajax.get('im/delExpressLanguage.html', { id }, res => {
                        if (res.data.code === 1) {
                            uni.showToast({
                                title: res.data.msg || '删除成功'
                            })
                            this.getStatement()
                        } else {
                            uni.showToast({
                                title: res.data.msg,
                                icon: 'none'
                            })
                        }
                    })
                }
            })
        },
        toEditStatement() {
            this.$navigateTo('/chatPage/chat/statement_list?to_id=' + this.to_id)
        },
        /**
         * 申请查看微信号
         */
        applyWechat() {
            let now_time = Date.parse(new Date())
            if (this.apply_time && now_time - this.apply_time < 30000) {
                uni.showToast({
                    title: "您的操作过于频繁",
                    icon: 'none'
                })
                return
            }
            // 如果对方是优选置业顾问可以直接查看其微信号
            // if(this.friend_info.is_optimization===1){
            //     console.log("可以直接查看")
            //     let content = {
            //         name:this.friend_info.nickname,
            //         qrcode:this.friend_info.wechat_img,
            //         wechat:this.friend_info.wechat || ""
            //     }
            //     console.log(content)
            //     this.checkMsg({
            //         chat_id: this.chat_id,
            //         content: content,
            //         from_headimage: this.friend_info.headimage,
            //         from_nickname: this.friend_info.nickname,
            //         from_id: this.friend_info.platform_id,
            //         type: 'wechat',
            //     })
            //     this.pushMyMsg(this.chat_id, 'wechat', JSON.stringify(content), 1, 1)
            //     return
            // }
            this.apply_time = now_time
            this.getQRcodeState(1)
            // this.sendMessage('apply_wechat', 'apply_wx')
            // this.statisticsApply(2)
        },
        applyWechatQRcode() {
            this.sendMessage('apply_wechat', 'apply_wx')
            this.statisticsApply(2)
        },
        getQRcodeState(index) {
            this.$ajax.get('index/setting', {}, res => {
                if (res.data.code == 1) {
                    if (index == 1) {
                        // this.lookWechat()
                        return res.data.chat_seek_wechat == 1 ? this.lookWechat() : this.applyWechatQRcode()
                    } else {
                        // this.lookTel()
                        return res.data.chat_seek_wechat == 1 ? this.lookTel() : this.applylookTel()
                    }
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })

        },
        lookWechat() {
            //  可以直接查看其微信号
            let content = {
                name: this.friend_info.nickname,
                qrcode: this.friend_info.wechat_img,
                wechat: this.friend_info.wechat || ""
            }
            console.log(content)
            this.checkMsg({
                chat_id: this.chat_id,
                content: content,
                from_headimage: this.friend_info.headimage,
                from_nickname: this.friend_info.nickname,
                from_id: this.friend_info.platform_id,
                type: 'wechat',
            })
            this.pushMyMsg(this.chat_id, 'wechat', JSON.stringify(content), 1, 1)
        },
        /**
         * 发送微信号
         */
        sendWechat() {
            if (!this.im.myChatInfo.wechat_img && !this.im.myChatInfo.wechat) {
                showModal({
                    title: '提示',
                    content: '您还没有补全微信号或微信二维码，是否去补全？',
                    confirm: () => {
                        if (this.im.myChatInfo.adviser > 0) {
                            this.$navigateTo('/user/adviser_info')
                        } else if (this.im.myChatInfo.levelid > 1) {
                            this.$navigateTo('/shops/agent_info')
                        }
                        // 监听微信号或微信二维码变化事件
                        uni.$once('onChangeWechat', (e = {}) => {
                            console.log(e)
                            if (e.wechat_img) {
                                this.im.myChatInfo.wechat_img = e.wechat_img
                            }
                            if (e.wechat) {
                                this.im.myChatInfo.wechat = e.wechat
                            }
                        })
                    }
                })
                return
            }
            if (!this.friend_userid) {
                uni.showToast({
                    title: '发送失败，请稍后重试',
                    icon: 'none'
                })
                return
            }
            this.$ajax.get('im/confirmWechat.html', { apply_id: this.friend_userid }, res => {
                if (res.data.code === 1) {
                    let content = {
                        name: this.im.myChatInfo.nickname,
                        qrcode: this.im.myChatInfo.wechat_img || "",
                        wechat: this.im.myChatInfo.wechat || ""
                    }
                    this.sendMessage(content, 'wechat')
                    this.staticAgree(2)
                    // this.addSystemMessage(content,'wechat')
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none'
                    })
                }
            })
        },
        /**
         * 申请查看手机号
         */
        applyTel() {
            if (!this.friend_userid) {
                uni.showToast({
                    title: '发送失败，请稍后重试',
                    icon: 'none'
                })
                return
            }
            let now_time = Date.parse(new Date())
            if (this.apply_tel_time && now_time - this.apply_tel_time < 30000) {
                uni.showToast({
                    title: "您的操作过于频繁",
                    icon: 'none'
                })
                return
            }
            this.apply_tel_time = now_time
            this.getQRcodeState(2)
            // this.$ajax.get('im/confirmTel', {apply_id: this.friend_userid}, res=>{
            //     if(res.data.code === 1){
            //         uni.makePhoneCall({
            //             phoneNumber: this.friend_info.tel
            //         });
            //     }else{
            //         this.sendMessage('apply_tel', 'apply_tel')
            //         this.statisticsApply(1)
            //     }
            // })
            // if(this.im.myChatInfo.is_optimization===1){
            //     if(!this.friend_info||!this.friend_info.tel){
            //         uni.showToast({
            //             title:"对方还没有绑定手机号",
            //             icon:none
            //         })
            //         return
            //     }
            //     uni.makePhoneCall({
            //         phoneNumber: this.friend_info.tel
            //     });
            //     return
            // }
            // let now_time = Date.parse(new Date())
            // if(this.apply_tel_time&& now_time-this.apply_tel_time<30000){
            //     uni.showToast({
            //         title:"您的操作过于频繁",
            //         icon:'none'
            //     })
            //     return
            // }
            // this.apply_tel_time = now_time
            // this.sendMessage('apply_tel', 'apply_tel')
            // this.statisticsApply(1)
        },
        applylookTel() {
            this.$ajax.get('im/confirmTel', { apply_id: this.friend_userid }, res => {
                if (res.data.code === 1) {
                    uni.makePhoneCall({
                        phoneNumber: this.friend_info.tel
                    });
                } else {
                    this.sendMessage('apply_tel', 'apply_tel')
                    this.statisticsApply(1)
                }
            })
        },
        lookTel() {
            //  可以直接查看其手机号
            console.log('1111111111');
            let content = {
                name: this.friend_info.nickname,
                tel: this.friend_info.tel
            }
            this.checkMsg({
                chat_id: this.chat_id,
                content: content,
                from_headimage: this.friend_info.headimage,
                from_nickname: this.friend_info.nickname,
                from_id: this.friend_info.platform_id,
                type: 'tel',
            })
            this.pushMyMsg(this.chat_id, 'tel', JSON.stringify(content), 1, 1)
        },
        /**
         * 发送手机号
         */
        sendTel() {
            this.sendMessage({ name: this.im.myChatInfo.nickname, tel: this.im.myChatInfo.mobile }, 'tel')
            this.staticAgree(1)
            // this.addSystemMessage(content,'wechat')
        },
        // 申请后发送通知
        statisticsApply(type) {
            this.$ajax.get('im/apply', { type, id: this.friend_userid }, res => { })
        },
        // 同意后发送通知
        staticAgree(type) {
            this.$ajax.get('im/agreeApply', { type, id: this.friend_userid }, res => { })
        },
        handleAgree(type) {
            if (type === 'tel') {
                this.sendTel()
            }
            if (type === 'wechat') {
                this.sendWechat()
            }
        },
        /**
         * 发送对方给予评价的邀请消息
        */
        inviteEvaluate() {
            let msg = { flag: 'inviteEvaluate', to_id: this.to_id, from_id: this.im.myChatInfo.platform_id }
            this.im.socketTask.send({
                data: JSON.stringify(msg),
                success: (res) => {
                    uni.showToast({
                        title: "评价邀请已发出"
                    })
                },
                fail: err => {
                    console.log(err)
                    uni.showToast({
                        title: '操作失败',
                        icon: 'none',
                        mask: true
                    })
                }
            });
        },
        toMore() {
            this.$navigateTo('/chatPage/chat/friend_info?chat_id=' + this.chat_id + '&to_id=' + this.to_id + '&from_id=' + this.im.myChatInfo.platform_id + '&user_id=' + this.user_id)
        },
        sendBuild() {
            this.show_from = false
            this.$store.state.buildInfo = {}
            if (this.info_data.title && this.info_data.id && this.info_data.type) {
                this.sendMessage(this.info_data, this.info_data.type)
            }
        },
        connectChat() {
            this.connectPage = 'chat'
            // this.onOpen()
            this.handleConnectSocket()
        },
        connectChatAgain() {
            this.$ajax.get('im/chatInfo.html', { agent_id: this.user_id }, res => {
                this.im.imToken = res.data.imToken || ''
                if (!this.im.socketOpen) {
                    this.connectChat()
                    return
                }
            })
        },
        pushIsSubscribe() {
            this.$ajax.post('im/subscribe', {}, res => {
                console.log(res.data)
            })
        },
        handleSend(content) {
            if (content.trim('') === '') {
                uni.showToast({
                    title: "请输入内容",
                    icon: 'none'
                })
                return
            }
            // #ifdef MP-WEIXIN
            if (!this.$store.state.is_subscribe) { //如果没有订阅小程序订阅消息
                let tmplIds = this.$store.state.unread_templateid_xcx
                uni.getSetting({
                    withSubscriptions: true,
                    success: (e) => {
                        // 如果没有授权或者没有授权的模板id
                        if (!e.subscriptionsSetting || e.subscriptionsSetting[tmplIds[0]] !== 'accept') {
                            uni.requestSubscribeMessage({
                                tmplIds: tmplIds,
                                success: (res) => {
                                    // console.log(res)
                                    if (res[tmplIds[0]] === 'accept') {
                                        this.$store.state.is_subscribe = 1
                                        this.pushIsSubscribe()
                                    }
                                },
                                fail: (err) => {
                                    console.log(err)
                                }
                            })
                        }
                    },
                    fail(err) {
                        console.log(err)
                    }
                })
            }
            // #endif
            this.show_plus = false
            this.show_statement = false
            this.show_from = false
            this.show_face = false
            this.sendMessage(content, 'text')
        },
        onClickScroll() {
            this.show_plus = false
            this.show_statement = false
            this.show_from = false
            this.show_face = false
        },
        tipNoPermission() {
            uni.showToast({
                title: this.prohibit_send_img_tip || '您已被禁止发送图片',
                icon: 'none'
            })
        },
        // 选择图片后触发,返回本地临时路径
        chooseDon(e) {
            this.show_plus = false
            // console.log(e)
        },
        /**
         * 图片上传完成触发
         */
        uploadDon(e) {
            e.files.forEach(img => {
                uni.getImageInfo({
                    src: img,
                    success: (res) => {
                        this.sendMessage({ img, width: res.width, height: res.height }, 'image')
                    }
                });
            });
        },
        /**
         * 预览图片
         * @param {String} img 当前图片
         */
        viewImg(img) {
            // let imgs = this.im.nowChat.chat.filter(item=>item.type==='image').map(item=>formatImg(item.content,'w_8601'))
            let imgs = this.im.nowChat.chatList.reduce((res, item) => {
                item.type === 'image' && res.push(formatImg((item.content.img || item.content), 'w_8601'))
                return res
            }, [])
            uni.previewImage({
                current: formatImg(img, 'w_8601'),
                indicator: "number",
                urls: imgs
            })
        },
        // 播放语音
        playVoice(e, index) {
            this.current_voice_index = index
            if (index === this.play_voice_index) {
                innerAudioContext.stop()
                return
            }
            // innerAudioContext.destroy()
            innerAudioContext.src = e.content.voice
            innerAudioContext.obeyMuteSwitch = false; //为false时即使用户打开了静音开关，也能继续发出声音
            innerAudioContext.play()
        },
        toogleStatement() {
            if (this.statementList.length === 0) {
                this.getStatement()
            }
            this.show_statement = !this.show_statement
            this.show_plus = false
            this.show_from = false
            this.show_face = false
        },
        tooglePlus() {
            this.show_plus = !this.show_plus
            this.show_statement = false
            this.show_from = false
            this.show_face = false
        },
        toogleFace() {
            this.show_face = !this.show_face
            this.show_plus = false
            this.show_statement = false
            this.show_from = false
        },
        selectFace(face) {
            this.send_content += '[' + face + ']'
        },
        /**
         * 选择位置
         */
        chooseLocation() {
            this.show_plus = false
            // #ifdef APP-PLUS
            uni.chooseLocation({
                keyword: "",
                success: (res) => {
                    console.log(res)
                    let msg = {
                        name: res.name,
                        address: res.address,
                        lat: parseFloat(res.latitude),
                        lng: parseFloat(res.longitude)
                    }
                    this.sendMessage(msg, 'map')
                }
            })
            // uni.getLocation({
            // 	type: 'gcj02',
            // 	geocode: true,
            // 	success: (res)=> {
            // 		console.log(res)
            // 		uni.chooseLocation({
            // 			keyword: res.address.district+res.address.street,
            // 			success: (res)=> {
            // 				let msg = {
            //                     name:res.name,
            //                     address:res.address,
            //                     lat:parseFloat(res.latitude),
            //                     lng:parseFloat(res.longitude)
            //                 }
            //                 this.sendMessage(msg,'map')
            // 			}
            // 		})
            // 	},
            // 	fail:()=>{
            // 		console.log("定位失败")
            // 		uni.chooseLocation({
            // 			keyword:"",
            // 			success: (res)=> {
            // 				let msg = {
            //                     name:res.name,
            //                     address:res.address,
            //                     lat:parseFloat(res.latitude),
            //                     lng:parseFloat(res.longitude)
            //                 }
            //                 this.sendMessage(msg,'map')
            // 			}
            // 		})
            // 	}
            // });
            // #endif
            // #ifndef APP-PLUS
            uni.chooseLocation({
                keyword: "",
                success: (res) => {
                    let msg = {
                        name: res.name,
                        address: res.address,
                        lat: res.latitude,
                        lng: res.longitude
                    }
                    this.sendMessage(msg, 'map')
                }
            })
            // #endif
        },
        /**
         * 获取聊天记录
         */
        getChatLogs() {
            this.get_status = "loading"
            this.$ajax.get('im/chatLog.html', { chat_id: this.chat_id, page: this.page }, res => {
                if (res.data.code === 1) {
                    let chat_list = res.data.list.map(item => {
                        let content;
                        if (typeof item.content === 'string' && typeof (item.content - 0)) {
                            try {
                                content = JSON.parse(item.content);
                                if (typeof content === 'number') { //如果content是数字则转成字符串
                                    content = content.toString()
                                }
                            } catch (e) {
                                content = item.content
                            }
                        }
                        let msg = { id: item.id, is_my: item.from_id === this.im.myChatInfo.platform_id ? 1 : 0, time: item.time, ctime: item.ctime, content, type: item.type }
                        return msg
                    })
                    if (this.page > 1) {
                        this.scrollAnimation = false
                        // #ifndef MP-BAIDU
                        // this.el = 'e'+this.im.nowChat.chatList[0].id
                        // #endif
                        this.concatChat(chat_list)
                    } else {
                        // this.im.nowChat.chatList = chat_list
                        this.concatChat(chat_list)
                        // // #ifdef MP-BAIDU
                        // this.tempChatList = chat_list
                        // // #endif
                        this.$nextTick(() => {
                            let time_out = 100
                            // #ifdef MP-BAIDU
                            time_out = 200
                            // #endif
                            setTimeout(() => {
                                this.setScrollTop(true)
                            }, time_out)
                        })
                    }
                    this.get_status = "more"

                    // 清除当前聊天的未读消息
                    if (this.page == 1) {
                        this.clearUncount()
                    }
                } else {
                    this.get_status = 'nomore'
                }
            })
        },
        /**
         * 合并获取到的在线聊天记录
         * @param {Array} list 获取到服务器聊天记录
         */
        concatChat(chat_list) {
            this.scrollAnimation = false
            let chat_id = chat_list[chat_list.length - 1].id
            // #ifdef MP-BAIDU
            // 百度小程序列表渲染，如果是在数组前面追加数据会报错
            this.im.nowChat.chatList = []
            this.$nextTick(() => {
                this.tempChatList = chat_list.concat(this.tempChatList)
                this.im.nowChat.chatList = this.tempChatList
                setTimeout(() => {
                    this.el = 'e' + chat_id
                }, 100)
            })
            // #endif
            // #ifndef MP-BAIDU
            this.im.nowChat.chatList = chat_list.concat(this.im.nowChat.chatList)
            // this.im.nowChat.chatList = [...chat_list,...this.im.nowChat.chatList]
            this.$nextTick(() => {
                this.el = 'e' + chat_id
            })
            // #endif
        },
        getMore() {
            if (this.get_status === 'nomore' || this.get_status === 'loading') {
                return
            }
            this.page++
            this.getChatLogs()
        },
        /**
         * 使页面滚动
         */
        setScrollTop(scrollAnimation = true) {
            if (this.page_read) {
                this.scrollAnimation = scrollAnimation
                // #ifdef H5
                setTimeout(() => {
                    let query = wx.createSelectorQuery()
                    query.select('#chat-lists').boundingClientRect(rect => {
                        this.scrollTop = rect.height
                    }).exec();
                }, 300)
                // #endif
                // #ifndef H5
                let query = wx.createSelectorQuery()
                query.select('#chat-lists').boundingClientRect(rect => {
                    this.scrollTop = rect.height
                }).exec();
                // #endif
            } else {
                setTimeout(() => {
                    this.setScrollTop()
                }, 300)
            }
        },
        /**
         * 提示通知
         */
        showNotice(data) {
            this.notice_msg = data
            // console.log(this.$refs.popup)
            this.$refs.popup.show()
            setTimeout(() => {
                this.$refs.popup.hide()
            }, 5000)
        },
        toFriends() {
            if (this.connectPage === 'chat') {
                uni.redirectTo({
                    url: '/chatPage/chat/list'
                });
            } else {
                uni.navigateBack()
            }
        },
        clickStart(index) {
            this.evaluate = index + 1
        },
        subEvaluate() {
            this.$ajax.get('im/chatComment.html', { to_id: this.user_id, star: this.evaluate, content: this.content || '' }, res => {
                if (res.data.code === 1) {
                    uni.showToast({
                        title: res.data.msg || '提交成功'
                    })
                } else {
                    uni.showToast({
                        title: res.data.msg || '提交失败，请重试',
                        icon: 'none'
                    })
                }
            })
        },
        handleImgLoadErr(index) {
            console.log(index)
        },
        // 切换语音和文字
        switchVoiceText() {
            if (!this.show_voice_btn && !this.allow_send_voice) {
                uni.showToast({
                    title: this.prohibit_send_voice_tip || '您已被禁止发送语音',
                    icon: 'none'
                })
                return
            }
            // #ifdef H5
            if (!this.show_voice_btn && !this.recorderPermission && !this.use_wx_voice) {
                Recorder.getPermission().then(() => {
                    this.show_voice_btn = !this.show_voice_btn
                    this.recorderPermission = true
                }, (error) => {
                    uni.showToast({
                        title: '没有录音权限',
                        icon: 'none'
                    })
                    console.log(`${error.name} : ${error.message}`);
                });
            } else {
                this.show_voice_btn = !this.show_voice_btn
            }
            // #endif
            // #ifndef H5
            this.show_voice_btn = !this.show_voice_btn
            // #endif
        },
        /** 
         * @date 2020-09-17 14:50:41 
         * @desc 长按发送语音按钮 
         */
        onLongTapVoiceBtn(e) {
            // 如果是正在录音则不进行任何操作
            if (this.recordering) {
                return
            }
            if (!this.open_voice) {
                uni.showToast({
                    title: '语音功能暂未开放',
                    icon: 'none'
                })
                return
            }
            if (!this.show_voice_btn && !this.allow_send_voice) {
                uni.showToast({
                    title: this.prohibit_send_voice_tip || '您已被禁止发送语音',
                    icon: 'none'
                })
                return
            }
            this.touch_voice_start_position_y = e.changedTouches[0].clientY
            // 执行开始录音
            if (this.use_wx_voice) {
                this.wx.startRecord()
                this.recorderCountDown()
                this.onRecordering()
                this.voice_start_time = Date.parse(new Date())
                this.onRecorderStop()
            } else {
                Recorder.getPermission().then(() => {
                    this.onRecordering()
                    recorder.start()
                    this.recorderCountDown()
                }, (error) => {
                    uni.showToast({
                        title: '没有录音权限',
                        icon: 'none'
                    })
                    console.log(`${error.name} : ${error.message}`);
                });
            }
        },
        /** 
         * @date 2020-09-17 14:50:35 
         * @desc 录音倒计时 
         */
        recorderCountDown() {
            if (this.recorder_timer) clearInterval(this.recorder_timer)
            let max_time = this.recorder_max_time / 1000
            this.recorder_timer = setInterval(() => {
                max_time--
                if (max_time >= 1 && max_time <= 3) {
                    uni.showToast({
                        title: `${max_time}秒后停止录音`,
                        icon: 'none'
                    })
                }
                if (max_time <= 0) {
                    this.onTouchEndVoiceBtn()
                    this.stopRecorder()
                }
            }, 1000)
        },
        /** 
         * @desc 录音中的状态
         */
        onRecordering() {
            this.recordering = true
            this.recorderCountDown()
            this.show_voice = true
            this.voice_index = 0
            this.timer = setInterval(() => {
                this.voice_index++
                if (this.voice_index > 5) {
                    this.voice_index = 0
                }
            }, 260)
        },
        /** 
         * @desc 录音结束后的状态
         */
        onRecordered() {
            this.recordering = false
            if (this.recorder_timer) clearInterval(this.recorder_timer)
            this.show_voice = false
            this.show_cancel_voice = false
            if (this.timer) clearInterval(this.timer)
        },
        /** 
         * @desc 滑动发送语音按钮 
         */
        onTouchMoveVoicBtn(e) {
            if (!this.open_voice || !this.touch_voice_start_position_y || !this.recordering) {
                return
            }
            // 判断垂直滑动距离达到某个值则提示松开取消发送录音
            if (this.touch_voice_start_position_y && this.touch_voice_start_position_y - e.changedTouches[0].clientY >= 70) {
                this.show_cancel_voice = true
                this.show_voice = false
            } else {
                this.show_cancel_voice = false
                this.show_voice = true
            }
        },
        /** 
         * @desc 松开发送语音按钮 
         */
        onTouchEndVoiceBtn(e) {
            if (!this.open_voice || !this.touch_voice_start_position_y || !this.recordering) {
                return
            }
            // 记录结束触摸的位置
            this.touch_voic_end_position_y = e.changedTouches[0].clientY
            // 执行停止录音
            // recorderManager.stop&&recorderManager.stop()
            this.stopRecorder()
        },
        /** 
         * @desc 执行停止录音 
         */
        stopRecorder() {
            if (this.recorder_timer) clearInterval(this.recorder_timer)
            if (this.use_wx_voice) {
                this.wx.stopRecord({
                    success: (res) => {
                        this.onRecordered()
                        this.voice_end_time = Date.parse(new Date())
                        // 如果大于等于70则是取消录音,否则获取录音文件
                        if (this.touch_voice_start_position_y - this.touch_voic_end_position_y < 70) {
                            this.uploadVoice(res.localId, this.voice_end_time - this.voice_start_time)
                        }
                        this.touch_voice_start_position_y = null
                    }
                });
            } else {
                recorder.stop()
                this.onRecordered()
                if (this.touch_voice_start_position_y - this.touch_voic_end_position_y < 70) {
                    let blob = recorder.getWAVBlob()
                    let duration = Math.ceil(recorder.duration * 1000)
                    recorder.destroy()
                    var reader = new FileReader();
                    reader.readAsDataURL(blob);
                    reader.onloadend = () => {
                        var base64data = reader.result;
                        this.uploadVoice(base64data, duration)
                    }
                } else {
                    recorder.destroy()
                }
                this.touch_voice_start_position_y = null
            }
        },
        // 监听到录音结束事件
        onRecorderStop() {
            this.wx.onVoiceRecordEnd({
                // 录音时间超过一分钟没有停止的时候会执行 complete 回调
                complete: (res) => {
                    // 如果是自动停止的
                    if (this.recorder_timer) {
                        uni.showToast({
                            title: '录音已自动终止',
                            icon: 'none'
                        })
                    }
                    this.onRecordered()
                    this.voice_end_time = Date.parse(new Date())
                    this.uploadVoice(res.localId, this.voice_end_time - this.voice_start_time)
                }
            });
        },
        // 上传录音文件并发送
        uploadVoice(file, duration) {
            // 获取上传后的录音文件路径，然后执行发送消息
            if (duration < 1000) {
                uni.showToast({
                    title: '录制时间太短',
                    icon: 'none'
                })
                this.touch_voice_start_position_y = null
                // recorder.destroy()
                return
            }
            if (this.use_wx_voice) {
                this.wx.uploadVoice({
                    localId: file, // 需要上传的音频的本地ID，由stopRecord接口获得
                    isShowProgressTips: 1, // 默认为1，显示进度提示
                    success: (res) => {
                        var serverId = res.serverId; // 返回音频的服务器端ID
                        console.log(serverId)
                        uploadServerId(serverId, duration)
                        // this.sendMessage({voice: serverId, duration}, 'voice')
                    }
                });
                // 将serverId传递给后台，后台进行语音的下载和转码
                var uploadServerId = (serverId, duration) => {
                    uni.showLoading({
                        title: "正在发送"
                    })
                    this.$ajax.get('UploadQiniu/downLoadVoice.html', { media_id: serverId, duration: Math.ceil(duration / 1000) }, res => {
                        if (res.data.code === 1) {
                            setTimeout(() => {
                                uni.hideLoading()
                                this.sendMessage({ voice: res.data.url, duration }, 'voice')
                            }, 500)
                        } else {
                            uni.hideLoading()
                            uni.showToast({
                                title: res.data.msg,
                                icon: 'none'
                            })
                        }
                    }, err => {
                        uni.hideLoading()
                    })
                }
                return
            }
            uni.showLoading({
                title: "正在发送"
            })
            this.$uploadFile('UploadQiniu/uploadVoice.html', file, { duration: Math.ceil(duration / 1000) }, (res) => {
                uni.hideLoading()
                let result
                // #ifdef MP-BAIDU
                result = res.data
                // #endif
                // #ifndef MP-BAIDU
                result = JSON.parse(res.data)
                // #endif
                if (!result.url) {
                    uni.showToast({
                        title: result.msg || "上传失败",
                        icon: "none"
                    })
                    return
                }
                this.sendMessage({ voice: result.url, duration }, 'voice')
            }, err => {
                uni.hideLoading()
                // recorder.destroy()
            })
        }
    },
    onUnload() {
        recorder.destroy(() => {
            recorder = null
        })
        innerAudioContext.stop()
        if (this.recorder_timer) clearInterval(this.recorder_timer)
        if (this.timer) clearInterval(this.timer)
        this.im.nowChat.chatList = []
        // 如果是当前页创建的连接则返回时断开连接
        if (this.connectPage === 'chat') {
            this.closeSocket()
        }
        if (this.timer) {
            clearInterval(this.timer)
        }
    },
    // #ifdef APP-PLUS
    onBackPress() {
        if (this.show_plus) {
            this.show_plus = false
            return true
        }
        if (this.show_statement) {
            this.show_statement = false
            return true
        }
        if (this.show_face) {
            this.show_face = false
            return true
        }
    },
    // #endif
    // #ifdef APP-PLUS || H5
    onNavigationBarButtonTap(e) {
        var userType = this.friend_info.adviser > 0 ? "2" : (this.friend_info.levelid > 1 ? "1" : 0)
        this.$navigateTo("/user/inform/inform?type=6&informUserId=" + this.user_id + "&userType=" + userType)
    },
    // #endif
}
</script>

<style lang="scss" scoped>
.chat-content {
    // height: 100vh;
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    flex-direction: column;
    box-sizing: border-box;
    padding-bottom: 188upx;
    background-color: #f3f3f3;
    transition: 0.2s;

    &.show-plus {
        padding-bottom: 300upx;
    }

    &.show-statement {
        padding-bottom: 658upx;
    }

    .scroll {
        height: 100%;
        width: 100%;
        background-color: #f5f5f5;
        box-sizing: border-box;

        &.has-info {
            padding-top: 200upx;
        }

        &.show-info {
            padding-top: 300upx;
        }
    }

    .card-box {
        position: absolute;
        top: 20upx;
        left: 48rpx;
        right: 48rpx;
        border-radius: 8rpx;
        overflow: hidden;
        z-index: 9;

        .operation-box {
            margin-bottom: 24rpx;
            justify-content: space-between;
            align-items: center;

            .operation {
                display: flex;
                align-items: center;
                font-size: 24upx;
                color: #666;

                .text {
                    margin-left: 10upx;
                }
            }
        }

        .btn {
            text-align: center;
            padding: 0 10upx;
            padding-top: 24rpx;
            font-size: 22rpx;
            color: #ff706f;
        }
    }

    .from-info {
        position: absolute;
        left: 24upx;
        right: 24upx;
        padding: 20upx;
        background-color: #fff;
        border-radius: 12upx;
        bottom: 190upx;

        .img-box {
            width: 200upx;
            height: 140upx;
            border-radius: 16upx;
            overflow: hidden;
            margin-right: 20upx;

            image {
                width: 100%;
                height: 100%;
            }
        }

        .title {
            margin-top: 20upx;
            font-size: 32upx;
        }

        .btn {
            height: 62upx;
            padding: 10upx 20upx;
            box-sizing: border-box;
            line-height: 42upx;
            font-size: 26uxp;
            border-radius: 12upx;
            margin-top: 59upx;
            color: #ff706f;
            background-color: #ffe0e0;
        }
    }

    .time {
        text-align: center;
        line-height: 40upx;
        padding: 20upx 0;
        font-size: 24upx;
        color: #999;
    }

    .input-box {
        width: 100%;
        min-height: 188upx;
        max-height: 188upx;
        overflow: hidden;
        box-sizing: border-box;
        position: relative;
        // align-items: center;
        background-color: #f5f5f5;
        transition: 0.2s;

        &.show-plus {
            min-height: 300upx;
            max-height: 300upx;
        }

        &.show-statement {
            min-height: 658upx;
            max-height: 658upx;
        }

        .quick_btn-list {
            justify-content: space-between;
            padding: 20rpx 48rpx;
            background-color: #fff;

            .item {
                height: 48rpx;
                line-height: 48rpx;
                padding: 0 24rpx;
                border: 1rpx solid #d8d8d8;
                border-radius: 24rpx;
                font-size: 24rpx;
                color: #666;
            }
        }

        .send-box {
            align-items: flex-end;
            padding: 15upx 20upx 20rpx 20upx;
            background-color: #fff;
        }

        .plus-btn {
            width: 60upx;
            height: 60upx;
            margin-left: 10upx;
            padding: 0;
            font-size: 28upx;
            line-height: 60upx;
            text-align: center;
            color: #d2d2d2;
        }

        .statement-btn {
            width: 60upx;
            height: 60upx;
            margin-right: 20upx;
            padding: 0;
            font-size: 28upx;
            line-height: 60upx;
            text-align: center;
            color: #d2d2d2;
        }

        .btn {
            width: 110upx;
            height: 56upx;
            margin-left: 20upx;
            margin-bottom: 5upx;
            padding: 0;
            font-size: 28upx;
            line-height: 56upx;
            text-align: center;
            border-radius: 10upx;
            background-color: #ff706f;
            color: #fff;

            &.left {
                margin-left: 0;
                margin-right: 20upx;
            }
        }

        .btn.btn-hover {
            background-color: #ff8686;
            color: #f3f3f3;
        }

        .plus-box {
            margin-top: 20upx;
            padding: 0 20upx 15upx 20upx;
        }

        .plus-item {
            width: 25%;
            text-align: center;
            padding: 46upx 20upx;
            box-sizing: border-box;
        }

        .statement-box {
            margin-top: 20upx;
            height: 460upx;
            box-sizing: border-box;
            padding: 10upx 20upx 15upx 20upx;
            background-color: #fff;

            .statement-list {
                height: 360upx;
                overflow-x: hidden;
            }

            .statement-item {
                justify-content: space-between;
                align-items: center;

                >text {
                    padding: 24upx 20upx;
                }

                .icon-box {
                    padding: 10rpx;
                }
            }

            .add-statement {
                align-items: center;
                color: #666;

                >text {
                    padding: 24upx 20upx;
                }

                .text-center {
                    color: $uni-color-primary;
                    text-align: center;
                }

                .manage_btn {
                    // color: $uni-color-primary;
                }
            }
        }
    }

    .face-list {
        padding: 20upx 0;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        height: 100%;
        box-sizing: border-box;
        overflow-x: hidden;

        .face {
            width: 50upx;
            min-width: 50upx;
            height: 50upx;
            margin: 20upx;

            image {
                width: 100%;
                height: 100%;
            }
        }

        .face_seat {
            width: 50upx;
            min-width: 50upx;
            height: 0;
            margin: 0 20upx;
        }
    }
}

.notice {
    text-align: left;
    padding: 28upx 24upx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    /* #ifdef H5 */
    margin-top: 44px;
    /* #endif */
    /* #ifndef H5 */
    margin-top: var(--window-top);
    /* #endif */
    margin-bottom: 10upx;
    background-color: #f7f7f7;
    border-bottom: 1upx solid #eee;
    box-shadow: 0 0 18rpx #dedede;
}

.err-tip {
    justify-content: space-between;
    padding: 10upx 24upx;
    background-color: #f44;
    color: #fff;

    .tip-btn {
        padding: 6upx 12upx;
        border: 1upx solid rgb(255, 103, 103);
        border-radius: 6upx;
        font-size: 26upx;
    }
}

.type-box {
    position: relative;
    z-index: 2;

    .msg_input {
        height: 70upx;
        padding: 10upx;
        box-sizing: border-box;
        border: 1rpx solid #dedede;
        background-color: #fff;
        border-radius: 8rpx;
    }

    textarea {
        // min-height: 70upx;
        width: auto;
        padding: 18upx 15upx !important;
        line-height: 1.2;
        margin-right: 20upx;
        // box-sizing: border-box;
        border-radius: 8upx;
        // max-height: 160upx;
        height: 36upx;

        &.in-ios {
            padding: 0 15upx;
        }
    }

    .palceholder_text {
        position: absolute;
        z-index: -1;
        top: 15upx;
        left: 20upx;
        color: #999
    }
}

.start-list {
    padding: 24upx 40upx;
    justify-content: space-between;
}

.evaluate-tip {
    padding: 24upx 40upx;
    text-align: left;
    font-size: 30upx;
    color: #666;
}



.voice_btn {
    height: 70rpx;
    padding: 10rpx;
    line-height: 50rpx;
    box-sizing: border-box;
    text-align: center;
    background-color: #fff;
    border: 1rpx solid #dedede;
    color: #666;
    user-select: none;
}

// 录音中
.voiced {
    width: 240rpx;
    padding: 24rpx;
    text-align: center;
    box-sizing: border-box;
    height: 240rpx;
    position: fixed;
    z-index: -1;
    left: 0;
    right: 0;
    bottom: 360rpx;
    margin: auto;
    border-radius: 20rpx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    opacity: 0;
    transition: 0.26s;

    &.show {
        opacity: 1;
        z-index: 10;
    }

    .tip {
        margin-top: 20 rpx;
        height: 30rpx;
        line-height: 30rpx;
        text-align: center;
        font-size: 22rpx;
        color: #fff;
    }
}

.voice_img {
    width: 140rpx;
}
</style>