<template>
<view class="page">
    <view class="head">
        <image class="company-logo" :src="areaInfo.pre_img_path | imgUrl"></image>
        <view class="company-name">{{areaInfo.name || ''}}</view>
    </view>
    <tab-bar :tabs="tabs" :fixedTop="false" @click="switchTab"></tab-bar>
    <home-list-item v-for="item in caseList" :key="item.id" :item="item" from="list"></home-list-item>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <my-popup ref="popup">
        <view class="popup-content">
            <view class="head">
                <image class="company-logo" :src="areaInfo.pre_img_path | imgUrl"></image>
                <view class="company-name">{{areaInfo.name || ''}}</view>
            </view>
            <view class="row bottom-line">在线预约【免费设计/获取报价】</view>
            <my-input label="手机号码" maxlength="11" type="number" placeholder="设计师怎么联系您呢？" name="tel" @input="handleInput"></my-input>
            <my-input label="您的称呼" maxlength="10" type="text" placeholder="Hi-您怎么称呼？" name="name" @input="handleInput"></my-input>
            <view class="btn-box">
                <button class="default" hover-class="btn-hover" @click="subData()">立即预约</button>
            </view>
            <view class="btn-box bottom">
                <button class="" hover-class="btn-hover" @click="closePopup()">取消</button>
            </view>
        </view>
    </my-popup>
</view>
</template>

<script>
import tabBar from '../../components/tabBar'
import myPopup from '../../components/myPopup'
import myInput from '../../components/form/myInput'
import homeListItem from '../../components/homeListItem'
import {formatImg} from '../../common/index'
import {
    uniIcons,
    uniLoadMore
} from '@dcloudio/uni-ui'
export default {
    data() {
        return {
            areaInfo: {
                name:""
            },
            tabs: [{
                name: '案例()',
                type: 1
            }, {
                name: "工地()",
                type: 2
            }, {
                name: "团装预约",
                type: ''
            }],
            caseList: [],
            get_status: "loading",
            content_text: {
                contentdown: "",
                contentrefresh: "正在加载...",
                contentnomore: "没有更多数据了"
            },
            formData:{
                type:1
            }
        }
    },
    components: {
        uniIcons,
        tabBar,
        myPopup,
        myInput,
        homeListItem,
        uniLoadMore
    },
    onLoad(options) {
        this.params = {
            page: 1,
            rows: 20,
            cid: options.id || '',
            type: options.type || 1
        }
        if (JSON.stringify(this.$store.state.tempData) != "{}") {
            Object.assign(this.areaInfo, this.$store.state.tempData)
            uni.setNavigationBarTitle({
                title: this.areaInfo.name
            })
        }
        this.getData()
    },
    filters:{
        imgUrl(val){
            return formatImg(val,'w_120')
        }
    },
    methods: {
        getData() {
            this.params.page == 1 && (this.caseList = []) && (this.get_status = "loading")
            this.$ajax.get('memberShop/myHomeDetail', this.params, res => {
                this.areaInfo.pre_img_path = res.data.community.pre_img_path||''
                this.areaInfo.name = res.data.community.name||''
                uni.setNavigationBarTitle({
                    title: this.areaInfo.name
                })
                if (res.data.code == 1) {
                    this.caseList = this.caseList.concat(res.data.caselist)
                    if (res.data.caselist.length < this.params.rows) {
                        this.get_status = "noMore"
                    } else {
                        this.get_status = "more"
                    }
                } else {
                    this.get_status = "noMore"
                    this.params.page > 1 && this.params.page--
                }
                this.tabs = [{
                    name: '案例(' + res.data.alcount + ')',
                    type: 1
                }, {
                    name: "工地(" + res.data.gdcount + ')',
                    type: 2
                }, {
                    name: "团装预约",
                    type: ''
                }]
            })
        },
        switchTab(e) {
            if (!e.type) {
                this.handleYuyue()
                return
            }
            this.params.page = 1
            this.params.type = e.type
            this.getData()
        },
        handleYuyue() {
            this.$refs.popup.show()
        },
        closePopup() {
            this.$refs.popup.hide()
        },
        handleInput(e) {
            this.formData[e._name] = e.detail.value
        },
        subData() {
            this.$ajax.get('memberShop/signUp', this.formData, res => {
                if (res.data.code == 1) {
                    uni.showToast({
                        title: res.data.msg
                    })
                    setTimeout(() => {
                        this.closePopup()
                    }, 2000)
                } else {
                    uni.showToast({
                        title: res.data.msg,
                        icon: "none",
                        duration: 3000
                    })
                }
            })
        }
    }
}
</script>

<style lang="scss">
.top {
    width: 100%;
    padding: 15upx;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    z-index: 9;
    border-bottom: 1upx solid rgba(255, 255, 255, 0);
    transition: 0.3s;

    .uni-icon {
        height: 60upx;
        width: 60upx;
        line-height: 60upx;
        position: absolute;
        z-index: 2;
        margin-left: 10upx;
    }
}

.bgwhite {
    background-color: #fff;
    border-bottom: 1upx solid $uni-border-color;
    box-shadow: 0 0 26upx #dedede;
}

.search-box {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.7);
    border: 1upx solid rgba(255, 255, 255, 0.5);
    transition: 0.3s;

    input {
        height: 60upx;
        padding-left: 70upx;
        font-size: $uni-font-size-sm;
        border-radius: 6upx;
    }
}

.popup-content {
    height: 100vh;
    box-sizing: border-box;
    padding-bottom: 90upx;
    background-color: #fff;

    .row {
        color: #999;
    }

    .bottom {
        width: 100%;
        box-sizing: border-box;
        position: absolute;
        bottom: 90upx;
    }
}

.bgwhite .search-box {
    border: 1upx solid $uni-border-color;
}
.head {
    width: 100%;
    height: 50vw;
    padding: 28upx;
    box-sizing: border-box;
    background-color: #26ad37;
    text-align: center;

    .company-logo {
        width: 120upx;
        height: 120upx;
        margin-top: 8vw;
        border-radius: 50%;
    }

    .company-name {
        margin-top: 20upx;
        font-size: 46upx;
        font-weight: 500;
        color: #fff;
    }
}
</style>
