<template>
	<view class="content groups">
		<view v-if="detail.is_discount===0">
			<view class="list-items">
				<view class="list-con">
					<view class="img-box list-img">
						<image :src="detail.img | imageFilter('w_8601')" lazy-load mode="widthFix"></image>
						
					</view>
					<view class="status" :class='{"ended":detail.status==2}'>{{detail.status==2?'已结束':'正在报名'}}/已有{{detail.count}}人报名</view>
				</view>
				<view class="list-info">
					<view class="info-title">{{detail.title}}</view>
					<view class="top-10 infos">
						<text class="time" v-if='detail.during'>活动时间：{{detail.during}}</text>
						<!-- <view class="inline c-right"><text class="em">{{detail.count}}</text><text class="author">人已报名</text></view> -->
					</view>
					<view class="top-10 infos" v-if ="detail.address">
						<text class="time" >活动地点:</text>
						<view class="inline">{{detail.address}}</view>
					</view>
				</view>
			</view>
			<view class="builds-box">
			<!-- <view class="label">入驻楼盘</view> -->
				<scroll-view scroll-x style="width: 100%" class="build-list flex-box">
					<view class="build-items">
					<view
					class="build-item"
					v-for="item in build"
					:key="item.id"
					@click="$navigateTo(`/pages/new_house/detail?id=${item.id}`)"
					>
						<view class="build-title-box">
							<view class="build-title-left">
								<view class="line"></view>
							</view>
							<view class="build-title-name-before"></view>
							<view class="build-title-name"><text>{{ item.title }}</text></view>
							<view class="build-title-right"><view class="line"></view></view>
						</view>
						<view class="build-infos">
							<image class="build_img" :src="item.img | imageFilter('w_400')" mode="aspectFill"></image>
							<view class="build-name flex-row">
								<view class="build-name-left">{{item.title}}</view>
								<view class="build-name-status" :class="'status'+item.leixing">{{item.sale_status}}</view>
							</view>
							<view class="build-labels flex-row" v-if ="item.label.length>0">
								<view class="build-label-item" v-for="(label,index) in item.label" :key="index">{{label}}</view>
							</view>
							<view class="build-price"><text class="price-type">{{item.price_type}}</text><text class="price-price">{{ item.build_price}}</text><text class="price-unit">{{item.price_unit}}</text></view>
							<!-- <view class="build-address">{{ item.address }}</view> -->
						</view>
					</view>
					</view>
				</scroll-view>
			</view>
			<view class="bullet-box" v-if ="bulletList.length>0">
				<view class="build-label">报名记录</view> 
							<swiper class="banners" :indicator-dots="false" :interval="2000" :circular="true" :vertical="true" :autoplay= "true" :duration="600" indicator-active-color="#f65354" :display-multiple-items="3"  :current="swiperCurrent">
									<swiper-item v-for="(item,index) in bulletList" :key="index">
						<view class="con flex-box" id="con">
							<view class="con_prelogo"><image :src="item.prelogo| imageFilter('w_80')" mode="widthFix"></image></view>
							<view class="con_name">{{item.name}}</view>
							<view class="con_tel">{{item.tel}}</view>
							<view class="con_time">{{item.up_time}}</view>
						</view>
									</swiper-item>
							</swiper>
					</view>
			<view class="top-20 user-box" v-if="detail.content">
				<view class="build-label">活动详情</view> 
				<!-- #ifdef H5 -->
				<view class="group-content" v-html="detail.content"></view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<u-parse :html="detail.content"></u-parse>
				<!-- #endif -->
			</view>
			<view class="flex-box bottom-bar">
				<view class="bottom-bar-left flex-box">
				<!-- <view class="icon-btn text-center" @click="toHome()">
					<my-icon type="home" color="#333333" size="24"></my-icon>
					<view>首页</view>
				</view> -->
				<!-- <view class="icon-btn text-center" @click="handelTel()">
					<my-icon type="dianhua" color="#333333" size="24"></my-icon>
					<view>电话</view>
				</view> -->
				<view class="icon-btn text-center" @click="showSharePop">
					<my-icon type="ic_fenxiang" color="#333333" size="48rpx"></my-icon>
					<view>分享</view>
				</view>
			
				</view>
				<!-- <view class="flex-box text-center bottom-bar-right"> -->
					<!-- <my-icon type="release" size="22" color="#ffffff"></my-icon> -->
					<view class="text-center flex-1 to-baoming tel-phone" @click="handelTel()">
						拨打电话
					</view>
					<view class="to-baoming flex-1 text-center submit-baoming"  @click="toSubForme()">{{detail.is_free==1?(detail.price+"元报名"):'立即报名'}}</view>
				<!-- </view> -->
			</view>
		</view>
		<view class="discount_container" v-if="detail.is_discount===1">
			<view class="top">
				<image v-if="detail.img" class="top-bg" :src="detail.img | imageFilter('w_8601')" mode="widthFix"></image>
				<image
					v-if="!detail.img && startDraw"
					class="top-bg"
					:src="topSrc | imageFilter('w_8601')"
					mode="widthFix"
				></image>
				<view class="top-card" v-if="!detail.img && startDraw">
					<view class="ellipsis card-title">{{ detail.title }}</view>
					<view class="ellipsis card-content">{{ detail.discount }}</view>
					<view class="card-date">● {{ detail.during }} ●</view>
				</view>
			</view>
			<view class="content">
				<view class="house" v-if="build.length > 0">
					<view
						class="house-left"
						v-for="(item, index) in build"
						:key="index"
						@click="$navigateTo(`/pages/new_house/detail?id=${item.id}`)"
					>
						<image class="house-image" :src="item.img" mode="aspectFill"></image>
						<view class="house-info">
							<view class="house-title">
								<view class="house-name">{{ item.title }}</view>
								<view class="house-right">
									<view class="house-detail">楼盘详情</view>
									<my-icon type="ic_into" color="#999999" size="24rpx"></my-icon>
								</view>
							</view>
							<view class="house-labels">
								<view class="labels-box" v-for="(label, index) in item.label" :key="index">{{ label }}</view>
							</view>
						</view>
					</view>
				</view>
				<view class="detail">
					<view class="detail-icon">
						<image :src="title_bg" mode="widthFix"></image>
						<view class="icon-text">优惠详情</view>
					</view>
					<view class="detail-title" v-if="detail.img">{{ detail.title }}，{{ detail.discount }}</view>
					<view class="detail-title" v-else>优惠信息</view>
					<view class="detail-date">活动时间：{{ detail.during }}</view>
					<view class="detail-content">
						<!-- #ifdef H5 -->
						<view class="group-content" v-html="detail.content"></view>
						<!-- #endif -->
						<!-- #ifndef H5 -->
						<u-parse :html="detail.content"></u-parse>
						<!-- #endif -->
					</view>
				</view>
			</view>
			<view class="bottom">
				<view class="line"></view>
				<view class="bottom-content">
					<view class="bottom-left" @click="showSharePop">
						<my-icon type="ic_fenxiang" color="#222222" size="48rpx"></my-icon>
						<view class="fenxiang">分享</view>
					</view>
					<view class="bottom-right">
						<view class="bottom-num">已有{{ detail.count }}人领取</view>
						<view class="to-baoming flex-1 text-center submit-baoming bottom-btn" @click="toSubForme()">{{
							detail.is_free == 1 ? detail.price + '元报名' : '立即报名'
						}}</view>
					</view>
				</view>
			</view>
		</view>
		<sub-form :groupCount="detail.count" :sub_type="sub_type" :sub_mode="sub_mode" :sub_title="sub_title"  :sub_content="sub_content" :sub_submit="sub_submit" ref='sub_form' :after_login_auto_handle="false" @onsubmit="handleSubForm" @after_login="handleSubForm"></sub-form>
		<share-pop ref="show_share_pop" @copyLink="copyLink" @appShare="appShare" @handleCreat='handleCreat' @showCopywriting='showCopywriting'></share-pop>
		<chat-tip></chat-tip>
		<shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
	</view>
</template>

<script>
	import {formatImg,showModal, config} from "../../common/index.js"
	import myPopup from '../../components/myPopup.vue'
	import myIcon from '../../components/myIcon.vue'
	import subForm from '../../components/subForm'
	import sharePop from '../../components/sharePop'
	import shareTip from '../../components/shareTip'
	export default{
		data(){
			return{
				topSrc: config.imgDomain + '/group/discount.png',
				title_bg: config.imgDomain + '/group/title_bg.png',
				id:"",
				detail:{
					
				},
				sub_submit:"",
				bulletList:[],
				build:{},
				sub_type:6,
				sub_title:'',
				sub_content:'',
				swiperCurrent:0,
				show_share_tip:false,
				link:'',
				group_id:0,  //用户提交报名以后支付需要用
				pay_params:{},
				startDraw: false,
			}
		},
		components:{
			myIcon,
			myPopup,
			subForm,
			sharePop,
			shareTip
		},
		onLoad(options){
			// #ifdef MP
			// 如果是扫码进来的
			if(options.scene){
				const params = getSceneParams(decodeURIComponent(options.scene))
				if(params.id){
					this.id = params.id
					this.getData()
				}
				return
			}
			// #endif

			if(options.id){
				this.id = options.id
				this.getData()
			}
			if(this.$store.state.tempData){
				this.detail = this.$store.state.tempData
				if(this.$store.state.tempData.title){
					uni.setNavigationBarTitle({
						title: this.detail.title
					})
				}
			}else if(options.title){
				this.detail.title = decodeURIComponent(options.title)
				uni.setNavigationBarTitle({
					title: this.detail.title
				})
			}
		},
		onUnload(){
			this.$store.state.tempData = {}
		},
		computed: {
			sub_mode() {
				return this.$store.state.sub_form_mode 
			}
		},
		methods:{
			getData(){
				this.$ajax.get("build/groupDetail.html",{id:this.id},(res)=>{
					if(res.data.code == 1){
						//const regex = new RegExp('<img', 'gi');
						// 正则匹配处理富文本图片过大显示问题
						//res.data.detail.content = res.data.detail.content.replace(regex, `<img style="max-width: 100%;"`);
						this.detail = res.data.detail
						this.bulletList=res.data.detail.sign_list
						this.build = res.data.build
						uni.setNavigationBarTitle({
							title: this.detail.title
						})
						this.startDraw = true
						if(res.data.share){
							this.share = res.data.share
						}else{
							this.share = {
								title:this.detail.title,
								content: this.detail.discount,
								pic: this.detail.img
							}
						}
						this.getWxConfig(['chooseWXPay','updateAppMessageShareData','updateTimelineShareData'],
							wx => {
							console.log('执行回调')
							this.wx = wx
						})
					}
				})
			},
			toHome(){
				uni.switchTab({
					url:'/pages/index/index'
				})
			},
			showSharePop(){
				this.getShortLink()
				this.$refs.show_share_pop.show()
			},
			getShortLink(){
				// #ifdef H5
				this.link = window.location.href
				// #endif
				// #ifndef H5
				this.link = config.apiDomain +'/h5/pages/groups/detail?id=' + this.id 
				// #endif
				this.$ajax.get("build/shortUrl.html",{page_url:this.link},(res)=>{
					if(res.data.code ==1){
						this.link=res.data.short_url
					}
				})
			},
			showCopywriting(){
					const address=this.detail.address?`【活动地点】${this.detail.address}`:''
					const text = `【看房团名称】${this.detail.title}
【活动时间】${this.detail.during}
${address}
【报名人数】已有${this.detail.count}人报名
【访问链接】${this.link}`
					this.copyWechatNum(text, ()=>{
						this.copy_success = true
					})
			},
			// #ifdef H5
			copyLink(){
				this.show_share_tip=true
				return;
				let link = window.location.href
				this.copyWechatNum(link, ()=>{
					uni.showToast({
					title: '复制成功,去发送给好友吧',
					icon: 'none'
					})
				})
			},
			// #endif
			 // #ifndef H5
			copyWechatNum(cont) {
				uni.setClipboardData({
					data: cont,
					success: res => {
					// uni.showToast({
					//   title: "复制成功",
					//   icon: "none"
					// })
					}
				})
			},
			// #endif
			// #ifdef H5
			copyWechatNum(cont, callback) {
				let oInput = document.createElement('textarea')
				oInput.value = cont
				document.body.appendChild(oInput)
				oInput.style.opacity = 0
				oInput.select() // 选择对象;
				oInput.setSelectionRange(0, oInput.value.length);
				document.execCommand('Copy') // 执行浏览器复制命令
				uni.showToast({
					title: '复制成功',
					icon: 'none'
				})
				oInput.blur()
				oInput.remove()
				if(callback) callback()
			},
			// #endif
			toHome(){
				uni.switchTab({
					url:'/pages/index/index'
				})
			},
			handelTel(){
				uni.makePhoneCall({
					phoneNumber: this.detail.tel
				});
			},
			toMap(){
				if(this.build.lat>0&&this.build.lng>0){
					uni.openLocation({
						latitude: parseFloat(this.build.lat),
						longitude: parseFloat(this.build.lng),
						success: function () {
							console.log('success');
						}
					});
				}
			},
			toSubForme(){
				if(this.detail.status==2){
					uni.showToast({
						title:"此活动报名已结束",
						icon:"none"
					})
					return
				}
				if(this.detail.is_free==1 && this.$store.state.user_login_status == 1){
					uni.removeStorageSync('token')
					this.$navigateTo('/user/login/login')
					return
				}
				this.sub_title = "预约报名"
				this.sub_content = "为方便通知到您最新的信息，请输入您的手机号码"
				this.sub_submit =this.detail.is_free==1?this.detail.price+"元预约": "提交预约"
				this.$refs.sub_form.showPopup()
			},
			handleSubForm(e){  //提交报名
				e.group_id = this.id
				e.from = "看房团页"
				e.type = this.sub_type
				if(this.detail.is_free==1){
					this.$ajax.get("buildGroupPay/checkBuildGroupPay.html",{group_id: this.detail.id}, res=>{
						uni.hideLoading()
						if(res.data.status === 1){
							uni.removeStorageSync('token')
							this.$navigateTo('/user/login/login')
							return
						}
						if(res.data.status === 2){
							this.$refs.sub_form.getVerify()
							return
						}
						if(res.data.code!==1){
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							})
							return
						}
						showModal({
							title: '提示',
							content: "需要支付"+this.detail.price+'元报名'||'',
							cancelText: '暂不支付',
							confirmText: '立即支付',
							confirm: () => {
								this.group_id = this.detail.id
								this.pay_params = {
									cname: e.name,
									tel: e.tel,
									group_id: this.detail.id
								}
								this.handlePay()
							},
							fail: () => {},
							complete: () => {}
						});
					})
					return
				}
				this.$ajax.post('build/signUp.html',e,res=>{
					uni.hideLoading()
					if(res.data.code === 1){
						if(this.sub_mode!==2||res.data.status === 3){ //提示报名成功
							uni.showToast({
								title:res.data.msg,
								icon:"none"
							})
							this.$refs.sub_form.closeSub()
						}else if(res.data.status === 1){
							uni.removeStorageSync('token')
							this.$navigateTo('/user/login/login')
						}else if(res.data.status === 2){
							this.$refs.sub_form.getVerify()
						}
					}else{
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
				})
			},
			handlePay(){
				uni.showLoading({
					title:"正在处理...",
					mask: true
				})
				this.mpWxPay()
			},
			mpWxPay(){
				this.$ajax.get('buildGroupPay/wxPay.html',this.pay_params,res=>{
					uni.hideLoading()
					if(res.data.code === 1){
							let pay_info = res.data.data
							this.wx.chooseWXPay({
									// provider: 'wxpay',
									timestamp:pay_info.timeStamp,
									nonceStr:pay_info.nonceStr,
									package:pay_info.package,
									signType:pay_info.signType,
									paySign:pay_info.paySign,
									success: (res)=> {
											uni.showToast({
													title:"支付成功"
											})
											this.$refs.sub_form.closeSub()
											setTimeout(()=>{
												this.$navigateTo('/user/order_list')
											}, 1500)
											// this.$refs.upgrade_success.show()
									},
									fail: function (err) {
											console.log(err);
											uni.showToast({
													title:err.err_desc||(err.errMsg=='requestPayment:fail cancel'?'已取消支付':err.errMsg),
													icon:"none",
													duration:5000
											})
									}
							})
					}else {
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
				})
      },
			handleCreat(){
				this.$navigateTo(`${location.origin}/wapi/poster/branch?type=7&id=${this.id}&header_from=2`)
			},
			lookAll(){
				console.log("查看全部")
			}
		},
		onShareAppMessage(){
			return {
				title: this.detail.title||"",
				content:this.detail.discount||"",
				imageUrl:this.detail.img?formatImg(this.detail.img,'w_6401'):""
			}
		}
	}
</script>

<style lang="scss" scoped>
	// @import url("../../components/wx-parse/parse.css");
		// #ifdef MP
	.groups .wxParse .img{
	   display: inline-block !important;  	//解决小程序端富文本文件 图片不居中显示的问题
	}
		// #endif
	.content{
		padding-bottom: 90upx;
	}
	.flex-row{
		display: flex;
		flex-direction: row;
	}
	.groups{
		box-sizing:border-box;
		background: #fff;
	}
	.list-con{
		padding-bottom: 40upx;
		overflow: hidden;
		position: relative;
	}
	.list-img{
		width: 100%;
		height: auto;

		box-sizing: border-box;
		// overflow: hidden;
		position: relative;
	}
	.list-img image{
		width: 100%;
		// height: 300upx!important;
	}
	.inline{
		display: inline;
	}
	.status{
		position: absolute;
		
		left: 50%;
		transform: translateX(-50%);
		bottom: 20upx;
		padding: 10upx 30upx;
		border-radius: 30upx;
		background-image: linear-gradient(to right, rgba(251, 172, 101, 1),rgba(251, 101, 106, 1));
		// border-top-left-radius: 10upx;
		// background-color: rgba($color: #000000, $alpha: 0.5);
		color: #fff
	}
	.status.ended{
		background-image: linear-gradient(to right, rgba(160, 171, 218, 1),rgba(122, 152, 231, 1));
	}
	.list-info{
		padding: 0 48upx;
	}
	.info-title{
		font-size: 40upx;
		line-height: 40upx;
		font-weight: bold;
		line-height: 1.5;
		margin: 10upx 0 24upx;
		color: #FA6569;
		font-weight: bold;
	}
	.preferential{
		padding: 10upx 20upx;
		font-size: $uni-font-size-blg;
		font-weight: bold;
		background-color: #f65354;
		color: #fff
	}
	.author,.time{
		margin-right: 10upx;
		font-size: 28upx;
		color: #666;
	}
	.em{
		color: #f65354
	}
	.top-10{
		margin-top: 10upx
	}
	.user-box{
		padding: 24upx 48upx;
		background-color: #fff;
		min-height: 400upx;
	}
	.user-list{
		justify-content: center
	}
	.user-list .user{
		width: 60upx;
		height: 60upx;
		margin: 6upx;
	}
	.user-list .user image{
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}
	.builds-box {
		margin-top: 48rpx;
		// padding: 0 48rpx;
		padding-left: 48rpx;
		background: #fff;
		.label {
			font-size: 40rpx;
			font-weight: bold;
			margin-bottom: 24rpx;
			color: #333;
		}
		.build-list {
			white-space: nowrap;

		}
		.build-items{
			display: flex;
			align-items: flex-start;
		}
		.build-item {
			// display: inline-block;
			// flex: 1;
			// font-size: 0;
			// margin-right: 24rpx;
			width: 400rpx;
			.build-title-box{
				display: flex;
				align-items: center;
				height: 50px;
				width: 400upx;
				
				.build-title-left{
					flex: 1;
				}
				.build-title-name-before{
					width: 8upx;
					height: 8upx;
					border-radius: 50%;
					background: #f65354;
					margin-left: 5upx;
				}
				.build-title-name{
					font-size: 28upx;
					max-width:40vw;
					position: relative;
					padding: 0 10upx;
					overflow: hidden;
					text{
						overflow: hidden;
						text-overflow: ellipsis;
						width: 100%;
						display: inline-block;
						white-space: nowrap;
					}

				}
				.build-title-right{
					flex: 1;
				}
				.line{
					width: 100%;
					height: 2upx;
					background: #333;
				}
			}
			.build-infos{
				margin-right: 24upx;
				.build_img {
					background: #f3f3f3;
					width: 100%;
					height: 220rpx;
					border-radius: 10upx;
				}
				.build-name{
					.build-name-left{
						max-width: 75%;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						color: #333;
						font-size: 32upx;
						font-weight: 600;
					}
					.build-name-status{
						margin-left: auto;
						padding: 0 10upx;
						border-radius: 4upx;
						&.status1 {
							color: #fff;
							background: linear-gradient(to right, #8cd3fc 0%, #4cc7f6 100%);
						}
						&.status2 {
							color: #fff;
							background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
						}
						&.status3 {
							color: #fff;
							background: linear-gradient(to right, #ff8533 0%, #ff7213 100%);
						}
						&.status4 {
							color: #fff;
							background: linear-gradient(to right, #ccc 0%, #ccc 100%);
						}
					}
				}
				.build-labels{
					padding: 12upx 0;
					overflow: hidden;
					.build-label-item{
						font-size: 22upx;
						padding: 4upx ;
						border: 2upx solid #999999;
						color: #999999;
						margin-right: 8upx;
						
					}
				}
				.build-price{
					color: #FA6569;
					font-size: 28upx;
					margin: 13upx 0;
					.price-type{
						color: #333;
					}
					.price-price{
						font-size:36upx;
						margin: 0 5upx;
					}
					.price-unit{
						color: #333;
					}
				}
				.build-address{
					color: #333;
					font-size: 28upx;
					white-space: normal;
					display: flex;
					
				}
			}
			
			// .build_name {
			// 	line-height: 1;
			// 	margin-top: 24rpx;
			// 	font-size: 28rpx;
			// 	color: #333;
			// }
		}
	}
	.highlight{
		color:#f65354;
	}
	.baoming-info{
		padding: 10upx;
	}
	.bottom-bar{
		background-color: #fff;
		height: 110upx;
		padding:15upx 48upx;
		justify-content: space-between;
		box-sizing: border-box;
		.icon-btn{
			line-height: initial;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			// flex: 1;
			padding-right: 32rpx;
			overflow: hidden;
			position: relative;
		}
	}
	.bottom-bar-right{
		flex: 1;
		align-items: center;
		justify-content: space-between;
	}
	.bottom-bar-left{
		flex: 1;
		// justify-content: space-between;
		// padding-right: 40upx;
	}
	.to-tel{
		align-items: center;
		justify-content: center;
		color: #333;
	}
	.to-share{
		align-items: center;
		justify-content: center;
		color: #333;
		padding: 0;
		background-color: #fff;
		border-radius: 0
	}
	.to-baoming{
		align-items: center;
		justify-content: center;
		color: #fff;
		line-height: 80upx;
		background-color: $uni-color-primary;
		flex: 1;
		font-size: 28upx;
	}
	.tel-phone{
		background: #FBAC65;
		box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.05);
		border-top-left-radius: 19px;
		border-bottom-left-radius: 19px;
	}
	.submit-baoming{
		background: linear-gradient(90deg, #FB656A 0%, #FBAC65 100%);
		box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.3);
		border-top-right-radius: 19px;
		border-bottom-right-radius: 19px;
	}
	.build-label{
		font-size: 32upx;
		color: #333;
		font-weight: bold;
		height: 80upx;
		line-height: 80upx;
	}
	.bottom-bar my-icon{
		line-height: 1;
		margin-right: 10upx;
	}
	.bullet-box {
		padding: 0upx 48upx 20upx;
		// margin:10px 5px;
		.build-label{
			margin-bottom: 13upx;
		}
	}
	.bullet-box .banners{
		height:300upx;
	}
	.bullet-box .banners .con{
		// padding:0 40upx;
		display: flex;
		flex-direction: row;
		align-items: center;
		color: #666;
		justify-content: space-between;
		.con_prelogo{
			width: 80upx;
			height: 80upx;
			border-radius: 50%;
			overflow: hidden;
			margin-right: 10upx;
			image{
				width: 100%;
			}

		}
		.con_name{
			width:100upx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

	}


// 优惠模式
.discount_container{
	flex-direction: column;
	background: #ffffff;
	min-height: calc(100vh - 46px);
	.top-img {
		height: fit-content;
		.top-image {
			width: 750rpx;
		}
	}
	.ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.top {
		height: fit-content;
		text-align: center;
		.top-bg {
			width: 100vw;
		}
		.top-card {
			position: absolute;
			top: 64rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 654rpx;
			height: 357rpx;
			box-sizing: border-box;
			padding-top: 48rpx;
			padding: 48rpx 20rpx 0 20rpx;
			text-align: center;
			.card-title {
				font-size: 36rpx;
				color: #b96100;
				letter-spacing: 2.24rpx;
			}
			.card-content {
				padding-top: 24rpx;
				padding-bottom: 24rpx;
				font-size: 64rpx;
				color: #b96100;
				letter-spacing: 4rpx;
			}
			.card-date {
				width: 360rpx;
				height: 52rpx;
				line-height: 52rpx;
				margin: 0 auto;
				background: #ffffff;
				border-radius: 26rpx;
				font-size: 28rpx;
				color: #b96100;
			}
		}
	}
	.content {
		position: relative;
		width: 100vw;
		padding-top: 48rpx;
		background: #fff;
		border-radius: 30rpx 30rpx 0 0;
		margin-top: -32rpx;
	}
	.house {
		margin: 0 auto;
		box-sizing: border-box;
		width: 654rpx;
		// height: 176rpx;
		border: 0 solid #f3f3f3;
		box-shadow: 0 0 24rpx 0 rgba(0, 0, 0, 0.06);
		border-radius: 16rpx;
		// align-items: center;
		padding: 40rpx 24rpx 40rpx 48rpx;
		.house-left {
			display: flex;
			~.house-left{
				margin-top: 24rpx;
			}
			.house-image {
				width: 96rpx;
				height: 96rpx;
				border-radius: 10rpx;
				background: #ff5c00;
			}
			.house-info {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				padding-left: 24rpx;
				width: 462rpx;
				.house-title {
					display: flex;
					justify-content: space-between;
				}
				.house-name {
					flex: 1;
					font-size: 32rpx;
					color: #333333;
					font-weight: bold;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				.house-labels {
					display: flex;
					overflow: hidden;
					.labels-box {
						flex-shrink: 0;
						height: 32rpx;
						line-height: 32rpx;
						padding: 0 8rpx;
						border: 1rpx solid #d8d8d8;
						border-radius: 4rpx;
						margin-right: 16rpx;
						font-size: 22rpx;
						color: #999999;
					}
				}
			}
		}
		.house-right {
			display: flex;
			align-items: center;
			align-self: flex-start;
			.house-detail {
				color: #999999;
				font-size: 22rpx;
				padding-right: 31rpx;
			}
		}
	}
	.detail {
		width: 654rpx;
		position: relative;
		border: 0 solid #f3f3f3;
		box-shadow: 0 0 24rpx 0 rgba(0, 0, 0, 0.06);
		border-radius: 16rpx;
		padding: 120rpx 48rpx 48rpx 48rpx;
		box-sizing: border-box;
		margin: 0 auto;
		margin-top: 66rpx;
		// margin-bottom: 160rpx;
		.detail-icon {
			position: absolute;
			left: 50%;
			top: -15rpx;
			transform: translateX(-50%);
			text-align: center;
			image {
				width: 384rpx;
			}
			.icon-text {
				position: absolute;
				top: 16rpx;
				left: 50%;
				transform: translateX(-50%);
				font-size: 36rpx;
				color: #ffffff;
			}
		}
		.detail-title {
			font-size: 32rpx;
			color: #333333;
			font-weight: bold;
		}
		.detail-date {
			padding: 24rpx 0;
			font-size: 22rpx;
			color: #666666;
		}
	}
	.bottom {
		position: fixed;
		bottom: 0;
		width: 100%;
		height: 111rpx;
		.line {
			height: 1rpx;
			transform: scaleY(0.5);
			background: #d8d8d8;
		}
	}
	.bottom-content {
		height: 110rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-sizing: border-box;
		padding-left: 48rpx;
		padding-right: 48rpx;
		background: #ffffff;
		.bottom-left {
			display: flex;
			flex-direction: column;
			align-items: center;
			height: 80rpx;
			.fenxiang {
				font-size: 20rpx;
				color: #333333;
			}
		}
		.bottom-right {
			display: flex;
			align-items: center;
			height: 80rpx;
			.bottom-num {
				font-size: 28rpx;
				color: #ff6b01;
				padding-right: 24rpx;
			}
			.bottom-btn {
				width: 316rpx;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				background-image: linear-gradient(125deg, #ff5500 0%, #ffa402 100%);
				box-shadow: 0 8rpx 20rpx 0 rgba(255, 145, 1, 0.4);
				border-radius: 40rpx;
				font-size: 32rpx;
				color: #ffffff;
			}
		}
	}
}
</style>
