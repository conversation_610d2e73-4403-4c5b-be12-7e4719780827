<template>
  <view>
    <view class="money_tip" v-if="releasePrice">
      <my-icon type="tishifu" color="#fb656a" size="32rpx"></my-icon>
      <text class="text">本条信息收费{{ releasePrice }}金币</text>
    </view>
    <view class="block">
      <view @click="handleTap" v-if="release_need_select_community == 1">
        <my-input label="小区" show_arrow :value="community_name" disabled :type="form.content.type" :isColor="isColor"
          :isColorto="isColorto" placeholder="请选择小区或留空"></my-input>
      </view>
      <my-picker :options="form.leixing.options" :isColor="isColor" disabledText="不可更改请联系客服"
        :disabled='house_info.catid && is_company_house == 1 && is_company_houses_editable == 0 ? true : false'
        @change="onPickerChange"></my-picker>
      <view class="select_btn flex-row" @click="showAreaPopup">
        <view class="left">
          <text class="label">区域</text>
          <text class="value"
            :style="{ background: (is_company_house == 1 && is_company_houses_editable == 0 ? '#f5f5f5' : 'white') }"
            :class="{ has_value: area_name }">{{ area_name || '请选择' }}</text>
        </view>
        <view>
          <my-icon type="ic_into" size="36rpx" color="#999"></my-icon>
        </view>
      </view>
      <my-popup ref="area_popup">
        <addressPicker :data_list="area_list" @onselect="onAreaChange"></addressPicker>
      </my-popup>
    </view>
    <view class="block" v-if="parentid == '1' || parentid == '2'">
      <view class="upload-box flex-row" @click="addPhotos">
        <image class="cover_img" v-if="params.cover_path" :src="params.cover_path"></image>
        <view class="upload_btn" v-else>
          <my-icon type="ic_jia" size="60rpx" color="#d8d8d8"></my-icon>
        </view>
        <view class="upload_tip flex-1">
          <text class="tip_title">上传照片</text>
          <text class="tip_content">只能上传房屋图片,不能含有文字、数字、网址、名片、水印等，所有类别图片总计20张。</text>
        </view>
        <my-icon type="ic_into" color="#999"></my-icon>
      </view>
    </view>
    <view class="block" v-if="parentid == '1' || parentid == '2'">
      <view class="multiple_input" v-if="open_release_info_loudong == 1">
        <text class="label">户室号</text>
        <view class="flex-row">
          <view class="input-item flex-row flex-5">
            <input :class="{ 'input-hui': house_info.loudong && is_company_house == 1 && is_company_houses_editable == 0 }"
              :disabled='house_info.loudong && is_company_house == 1 && is_company_houses_editable == 0 ? true : false'
              v-model="params.loudong" placeholder="栋/幢/弄/胡同" placeholder-style="font-size:28rpx;color:#999" />
            <text class="unit">号</text>
          </view>
          <view class="input-item flex-row flex-4">
            <input :class="{ 'input-hui': house_info.loudong && is_company_house == 1 && is_company_houses_editable == 0 }"
              :disabled='house_info.danyuan && is_company_house == 1 && is_company_houses_editable == 0 ? true : false'
              v-model="params.danyuan" placeholder="单元号" placeholder-style="font-size:28rpx;color:#999" />
            <text class="unit">单元</text>
          </view>
          <view class="input-item flex-row flex-3">
            <input v-model="params.fanghao"
              :class="{ 'input-hui': house_info.loudong && is_company_house == 1 && is_company_houses_editable == 0 }"
              :disabled='house_info.fanghao && is_company_house == 1 && is_company_houses_editable == 0 ? true : false'
              placeholder="门牌号" placeholder-style="font-size:28rpx;color:#999" />
            <text class="unit">室</text>
          </view>
        </view>
      </view>
      <my-picker disabledText="不可更改请联系客服" :isColor="isColor"
        :disabled='house_info.shi && is_company_house == 1 && is_company_houses_editable == 0 ? true : false'
        :options="form.huxing.options" @change="onPickerChange"></my-picker>
      <view class="multiple_input flex-row">
        <view class="input-item flex-row">
          <text class="label">{{ form.szlc.title }}</text>
          <input type="number"
            :class="{ 'input-hui': house_info.loudong && is_company_house == 1 && is_company_houses_editable == 0 }"
            :disabled='house_info[form.szlc.identifier] && is_company_house == 1 && is_company_houses_editable == 0'
            v-model="params[form.szlc.identifier]" placeholder="请填写" placeholder-style="font-size:28rpx;color:#999" />
          <text class="unit">{{ form.szlc.rules }}</text>
        </view>
        <view class="input-item flex-row">
          <text class="label">共</text>
          <input type="number"
            :class="{ 'input-hui': house_info.loudong && is_company_house == 1 && is_company_houses_editable == 0 }"
            :disabled='house_info[form.louceng.identifier] && is_company_house == 1 && is_company_houses_editable == 0 ? true : false'
            v-model="params[form.louceng.identifier]" placeholder="请填写"
            placeholder-style="font-size:28rpx;color:#999" />
          <text class="unit">{{ form.louceng.rules }}</text>
        </view>
      </view>
    </view>
    <view class="block">
      <my-input v-if="parentid !== '3'" :label="form.mianji.title" :unit="form.mianji.rules"
        v-model="params[form.mianji.identifier]" :type="form.mianji.type" :isColor="isColor" placeholder="请输入"
        disabledText="不可更改请联系客服"
        :disabled='house_info[form.mianji.identifier] && is_company_house == 1 && is_company_houses_editable == 0 ? true : false'></my-input>
      <my-input :label="form.fangjia.title" :unit="form.fangjia.rules" v-model="params[form.fangjia.identifier]"
        :type="form.mianji.type" placeholder="请输入"></my-input>
    </view>
    <!-- :disabled ='house_info.label?true:false&&is_company_house==1&&is_company_houses_editable==0' -->
    <view class="block" v-if="form.labels.length > 0">
      <my-checkbox :values="labels_value" :maxnum="4" @select="handelCheckLabel" label="标签"
        :range="form.labels"></my-checkbox>
    </view>
    <view class="block">
      <my-input label="标题" show_arrow v-model="params[form.title.identifier]" :type="form.title.type" placeholder="请输入"
        :isColor="isColor"
        :disabled='house_info[form.title.identifier] && is_company_house == 1 && is_company_houses_editable == 0 ? true : false'></my-input>
      <view @click="toAddDesc">
        <my-input label="描述" ref="desc" :isColor="isColor" show_arrow v-model="params[form.content.identifier]"
          :disabled='house_info[form.content.identifier] && is_company_house == 1 && is_company_houses_editable == 0 ? true : false'
          :type="form.content.type" placeholder="请输入"></my-input>
      </view>
    </view>
    <view class="block">
      <my-input label="联系人" show_arrow :isColor="isColor" ref="concat" @input="handleConcatInput"
        v-model="params[form.contact_who.identifier]" :type="form.contact_who.type" placeholder="请输入"
        :disabled='house_info[form.contact_who.identifier] && is_company_house == 1 && is_company_houses_editable == 0 ? true : false'></my-input>
    </view>
    <!-- 完善更多非必填的信息 -->
    <view class="block" v-if="formList.length > 0">
      <view class="open_menu flex-row" @click="show_more_info = !show_more_info">
        <view class="flex-row">
          <text>完善更多信息</text>
          <text class="tip">（非必填）</text>
        </view>
        <my-icon :type="show_more_info ? 'ic_close' : 'ic_open'" color="#999"></my-icon>
      </view>
      <view class="more_info_box" :class="{ open: show_more_info }">
        <block v-for="(item, index) in formList" :key="index">
          <my-input v-if="inputType.includes(item.type)" v-model="params[item.identifier]" :label="item.title"
            :unit="item.rules" :type="item.type" placeholder="请输入"></my-input>
          <my-select v-if="item.type == 'select'" :value="item.value[0] || ''" @change="pickerChange"
            :label="item.title" :range="item.rules" :name="item.identifier" placeholder="请选择"></my-select>
          <my-radio v-if="item.type == 'radio'" :value="item.value[0]" @change="radioChange" :label="item.title"
            :range="item.rules" :name="item.identifier"></my-radio>
          <my-checkbox v-if="item.type == 'checkbox'" :values="item.value" @select="handelCheckbox" :label="item.title"
            :range="item.rules" :name="item.identifier"></my-checkbox>
        </block>
      </view>
    </view>
    <!-- 房源统一核验 -->
    <view class="block" v-if="(parentid == '1' || parentid == '2') && if_info_verification_code == 1">
      <view class="open_menu flex-row" @click="addVerification()">
        <view class="flex-row">
          <text>房源统一核验</text>
        </view>
        <my-icon type="ic_into" color="#999"></my-icon>
      </view>
    </view>
    <!-- 房源内部编号 -->
    <view class="block" v-if='open_info_internal_no'>
      <my-input label="房源内部编号" show_arrow :isColor="isColor" v-model="params.internal_no" placeholder="请输入"
        :disabled='house_info.internal_no && is_company_house == 1 && is_company_houses_editable == 0 ? true : false'>
      </my-input>
    </view>
    <view class="block" v-if="youxiaoqi && youxiaoqi.length > 0">
      <view class="open_menu flex-row">
        <view>
          <text>有效期(续期)</text>
          <text class="tip" v-if="activeTimeSurplus">当前房源有效期还剩{{ activeTimeSurplus }}</text>
        </view>
      </view>
      <view class="options-box flex-row">
        <view class="options" :class="{ active: item.id === params.activetime_id }" @click="changeYouxiaoqi(item)"
          v-for="item in youxiaoqi" :key="item.id">
          <text class="title">{{ item.name }}</text>
          <view class="price" v-if="youxiaoqiHasMoney">
            <!-- <text>{{ item.money }}</text>
            <text class="unit">元</text> -->
          </view>
          <text class="tip" v-if="youxiaoqiHasMoney">￥{{ item.money }}</text>
        </view>
        <view class="options vacancy"></view>
      </view>
    </view>
    <!-- <view class="block" v-if="youxiaoqi&&youxiaoqi.length>0">
      <view class="open_menu flex-row">
        <text>有效时间</text>
      </view>
      <view class="options-box flex-row">
        <view
          class="options"
          :class="{ active: item.id === params.activetime_id }"
          @click="onClickTime(item)"
          v-for="item in youxiaoqi"
          :key="item.id"
        >
          <text class="title">{{ item.name }}</text>
          <text class="tip" v-if="item.tip">{{ item.tip }}</text>
        </view>
        <view class="options vacancy"></view>
      </view>
    </view> -->
    <!-- 选择推广方式 -->
    <!-- <view class="block">
      <view class="open_menu flex-row" @click="show_pay_info = !show_pay_info">
        <view class="flex-row">
          <text>推广方式</text>
          <view class="flex-row">
            <text class="ding">顶</text>
            <text class="jing">精</text>
          </view>
        </view>
        <my-icon :type="show_pay_info ? 'ic_close' : 'ic_open'" color="#999"></my-icon>
      </view>
      <view class="more_info_box" :class="{ open: show_pay_info }">
        <view class="types flex-row">
          <view class="type-item" :class="{ active: extension_type === 'ding' }" @click="extension_type = 'ding'"
            >置顶</view
          >
          <view class="type-item" :class="{ active: extension_type === 'jing' }" @click="extension_type = 'jing'"
            >精选</view
          >
        </view>
        <view class="options-box flex-row" v-show="extension_type === 'ding'">
          <view
            class="options"
            :class="{ active: ding_item.id === params.tops_id }"
            @click="onClickTop(ding_item)"
            v-for="ding_item in extension_ding"
            :key="ding_item.id"
          >
            <text class="title">{{ ding_item.name }}</text>
            <view class="price">
              <text>{{ ding_item.money }}</text>
              <text class="unit">元</text>
            </view>
            <text class="tip" v-if="ding_item.tip">{{ ding_item.tip }}</text>
          </view>
          <view class="options vacancy"></view>
        </view>
        <view class="options-box flex-row" v-show="extension_type === 'jing'">
          <view
            v-if="freeSelected.name"
            class="options"
            :class="{ active: params.freeSelected }"
            @click="onClickFreeJingxuan()"
          >
            <text class="title">{{ freeSelected.name }}</text>
            <view class="price">
              <text>{{ freeSelected.money }}</text>
              <text class="unit">元</text>
            </view>
            <text class="tip" v-if="freeSelected.tip">{{ freeSelected.tip }}</text>
          </view>
          <view
            class="options"
            :class="{ active: jing_item.id === params.jingxuan_id }"
            @click="onClickJingxuan(jing_item)"
            v-for="jing_item in extension_jing"
            :key="jing_item.id"
          >
            <text class="title">{{ jing_item.name }}</text>
            <view class="price">
              <text>{{ jing_item.money }}</text>
              <text class="unit">元</text>
            </view>
            <text class="tip" v-if="jing_item.tip">{{ jing_item.tip }}</text>
          </view>
          <view class="options vacancy"></view>
        </view>
        <view class="tip">再次点击可取消</view>
      </view>
    </view> -->
    <view class="rule flex-row">
      <label class="flex-box" style="align-items:center" @click="on_ready = !on_ready">
        <checkbox color="#ff656c" checked></checkbox>
        <text>已阅读并接受</text>
      </label>
      <text class="rule_link"
        @click="$navigateTo('/user/agreement?type=push_info')">《{{ $store.state.siteName }}房源信息发布规则》</text>
    </view>
    <view class="btn-box">
      <view class="btn" @click="subData">立即提交</view>
    </view>
  </view>
</template>

<script>
import myInput from '../../components/form/newInput.vue'
import myIcon from '../../components/myIcon.vue'
import myPicker from '../../components/myPicker.vue'
import mySelect from '../../components/form/mySelect.vue'
import myRadio from '../../components/form/myRadio.vue'
import myCheckbox from '../../components/form/myCheckbox.vue'
import myDialog from '../../components/dialog.vue'
import myPopup from '../../components/myPopup.vue'
import addressPicker from '../../components/addressPicker.vue'
import { checkAuth, showModal } from '../../common/index.js'
import { mapState, mapMutations } from 'vuex'
export default {
  components: {
    myInput,
    myIcon,
    myPicker,
    mySelect,
    myRadio,
    myCheckbox,
    myDialog,
    myPopup,
    addressPicker
  },
  data() {
    return {
      isColor: 0,
      isColorto: 0,
      formList: [],
      area_list: [],
      open_release_info_loudong: 0,
      levelid: 2,
      release_need_select_community: 0,//是否开启选择小区
      form: {
        title: {
          title: '标题',
          value: '',
          identifier: 'title',
          required: true,
          type: 'text'
        },
        content: {
          title: '描述',
          value: '',
          identifier: 'content',
          required: true,
          type: 'text'
        },
        contact_who: {
          title: '联系人',
          value: '',
          identifier: 'contact_who',
          required: true,
          type: 'text'
        },
        mianji: {
          type: 'digit',
          title: '建筑面积',
          rules: 'm²',
          identifier: 'mianji',
          required: true,
          value: ''
        },
        szlc: {
          type: 'number',
          title: '所在楼层',
          rules: '层',
          required: true,
          identifier: 'szlc',
          value: ''
        },
        louceng: {
          type: 'number',
          title: '总楼层',
          rules: '层',
          identifier: 'louceng',
          required: true,
          value: ''
        },
        fangjia: {
          type: 'digit',
          title: '售价',
          rules: '万元',
          identifier: 'fangjia',
          value: ''
        },
        huxing: {
          type: 'picker',
          value: [],
          options: [
            {
              label: '户型',
              value: [],
              required: true,
              range: [
                {
                  title: '室',
                  identifier: 'shi',
                  rules: []
                },
                {
                  title: '厅',
                  identifier: 'ting',
                  rules: []
                },
                {
                  title: '卫',
                  identifier: 'wei',
                  rules: []
                }
              ]
            },
            {
              label: '朝向',
              value: [],
              required: true,
              range: [
                {
                  title: '',
                  identifier: 'chaoxiang',
                  rules: []
                }
              ]
            },
            {
              label: '装修',
              value: [],
              required: true,
              range: [
                {
                  title: '',
                  identifier: 'zhuangxiu',
                  rules: []
                }
              ]
            }
          ]
        },
        leixing: {
          type: 'picker',
          value: [0],
          options: [
            {
              label: '房产类型',
              value: [],
              required: true,
              range: [
                {
                  title: '',
                  identifier: 'catid',
                  rules: []
                }
              ]
            },
            // {
            //   label: '区域',
            //   value: [],
            //   required: true,
            //   range: [
            //     {
            //       title: '',
            //       identifier: 'areaid',
            //       rules: []
            //     }
            //   ]
            // }
          ]
        },
        labels: []
      },
      show_more_info: false,
      show_pay_info: false,
      labels_value: [],
      youxiaoqi: [],
      youxiaoqiHasMoney: false,
      extension_type: 'ding',
      extension_ding: [],
      extension_jing: [],
      freeSelected: {},
      community_name: '',
      area_name: '',
      inputType: ['number', 'text', 'tel', 'digit'],
      parentid: '',
      imgList: [],
      imgCount: 9,
      videoList: [],
      freeActiveTime: 0,
      activeTimeSurplus: '',
      releasePrice: '',
      pay_params: {},
      params: {
        title: '',
        catid: '',
        areaid: '',
        address: '',
        cover_path: '',
        videos: '',
        imgs: '',
        imgs1: '',
        imgs2: '',
        contact_who: '',
        content: '',
        activetime_id: '', //有效期id
        internal_no: ""
      },
      house_info: {},
      show_dialog: false,
      is_company_house: '',
      is_company_houses_editable: '',
      on_ready: true,
      open_info_internal_no: 0 //是否开启房源内部编号
    }
  },
  onLoad(options) {
    // uni.showLoading({
    // 	title:"加载中..."
    // })
    // #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
    uni.hideShareMenu()
    // #endif
    if (options.catid) {
      this.parentid = options.catid
      switch (this.parentid) {
        case "2":
          this.form.fangjia.title = "租金"
          this.form.fangjia.identifier = "zujin"
          this.form.fangjia.rules = "元/月"
          break;
        case "3":
          this.form.fangjia.title = "预算"
          this.form.fangjia.identifier = "qzprice"
          this.form.fangjia.rules = "元/月"
          this.form.szlc = {}
          this.form.louceng = {}
          this.form.mianji = {}
          break;
        case "4":
          this.form.fangjia.title = "预算"
          this.form.fangjia.identifier = "qgprice"
          this.form.mianji.identifier = "qgmianji"
          this.form.szlc = {}
          this.form.louceng = {}
          break;
      }
    }
    if (options.info_id) {
      // 修改 获取当前信息的数据
      this.params.id = options.info_id
      this.getInfoData(this.params.id)
    }
    this.getWxConfig(
      ['chooseWXPay', 'hideOptionMenu'],
      wx => {
        console.log('执行回调')
        this.wx = wx
      },
      1
    )
    uni.$on('transmit', e => {
      this.params.cover_path = e.cover_path || e.indoor_images[0]
      this.params.videos = e.indoor_videos.join(',') //视频
      this.params.imgs1 = e.indoor_images.join(',') //室内图
      this.params.imgs2 = e.house_type.join(',') //户型图
      this.params.imgs = e.outdoor.join(',') //室外图
    })
    uni.$on('transmitDesc', e => {
      this.params.content = e.content
      this.params.owner_think = e.owner_think
      this.params.service_introduce = e.service_introduce
    })
    uni.$on('transmitVerification', (e) => {
      this.params.verification_code = e.verification_code
      this.params.verification_qrcode = e.verification_qrcode
    })
  },
  onHide() {
    this.setAllowOpen(true)
  },
  onUnload() {
    this.setAllowOpen(true)
    // this.imgList = []
    // this.videoList = []
  },
  computed: {
    ...mapState(['allowOpen', 'if_info_verification_code'])
  },
  onShow() {
    if (!this.allowOpen) {
      if (this.params.id) {
        this.getInfoData(this.params.id)
      }
    }
    if (uni.getStorageSync('smallArea')) {
      let smallArea = JSON.parse(uni.getStorageSync('smallArea'))
      this.params.buildid = smallArea.id
      this.community_name = smallArea.community_name || smallArea.name
      this.params.areaid = smallArea.areaid
      this.area_name = this.getArea(this.area_list, smallArea.areaid).areaname
      // this.params.areaid = smallArea.areaid || ''
      this.params.address = smallArea.address || ''
      this.params.lat = smallArea.lat
      this.params.lng = smallArea.lng
      // this.setTitle()
    }
    uni.removeStorageSync('smallArea')
  },
  methods: {
    ...mapMutations(['setAllowOpen']),
    addPhotos() {
      // if(this.is_company_house==1&&this.is_company_houses_editable==0) {
      //   uni.showToast({
      //     title:"不可更改请联系客服",
      //     icon:"none"
      //   })
      //   return 
      // }
      let photos = {
        indoor_images: this.params.imgs1 ? this.params.imgs1.split(',') : [],
        indoor_videos: this.params.videos && this.params.videos.length > 0 ? this.params.videos.split(',') : [],
        house_type: this.params.imgs2 ? this.params.imgs2.split(',') : [],
        outdoor: this.params.imgs ? this.params.imgs.split(',') : [],
        disabled: this.is_company_house == 1 && this.is_company_houses_editable == 0 ? true : false
        // cover_path: this.params.cover_path
      }
      if (this.levelid > 1) {
        photos.cover_path = this.params.cover_path
      }
      this.$store.state.photos = photos
      this.$navigateTo(`/user/upload_info_img?cid=${this.params.buildid || ''}&levelid=${this.levelid}`)
    },
    onPickerChange(e) {
      e.forEach(item => {
        item.forEach(el => {
          this.params[el.identifier] = el.value
        })
      })
      // this.setTitle()
    },
    // 处理发布的选项
    handleOtherData(resData) {
      this.formList = resData.map(item => {
        if (!item.value) {
          item.value = []
        }
        return item
      })
      if (this.params.id) {
        //如果是修改
        this.formList.map(item => {
          this.params[item.identifier] = item.value.join(',')
        })
        // this.params.title = resData.title
        // this.params.buildid = resData.buildid
        // this.params.areaid = resData.areaid
        // this.params.address = resData.address
        // this.params.lat = resData.latitude
        // this.params.lng = resData.longitude
        // this.params.catid = resData.catid
        // this.params.content = resData.content
        // this.params.videos = resData.videos.join(',')
        // this.params.imgs = resData.imgs.join(',')
        // this.imgList = resData.imgs
        // this.videoList = resData.videos
      }
      // #ifdef MP-BAIDU
      if (!this.params.id) {
        // 百度小程序如果刚开始没有图片上传第一张不会显示,所以默认添加一张空的图片，然后在上传组件在删除掉这个空的图片
        this.imgList = ['']
        this.videoList = ['']
      }
      // #endif
      this.house_info = Object.assign({}, this.params)
      console.log(this.house_info);
    },
    handleTap() {
      if (this.house_info.buildid && this.is_company_house == 1 && this.is_company_houses_editable == 0) {
        uni.showToast({
          title: "不可更改请联系客服",
          icon: "none"
        })
        return
      }
      this.$navigateTo('/user/search_areas',)
    },
    handleData(data) {
      this.community_name = data.cname
      for (let key in data) {
        this.params[key] = data[key]
      }
      if (typeof data.label === 'object') {
        this.params.label = data.label.join(',')
      }
      if (this.form.huxing.options[0].range.length > 0) {
        let huxing_value = []
        let shi_index = this.form.huxing.options[0].range[0].rules.findIndex(item => item.value === data.shi)
        let ting_index = this.form.huxing.options[0].range[1].rules.findIndex(item => item.value === data.ting)
        let wei_index = this.form.huxing.options[0].range[2].rules.findIndex(item => item.value === data.wei)
        if (shi_index >= 0) huxing_value.push(shi_index)
        if (ting_index >= 0) huxing_value.push(ting_index)
        if (wei_index >= 0) huxing_value.push(wei_index)
        this.form.huxing.options[0].value = huxing_value
      }
      if (this.form.huxing.options[1].range.length > 0) {
        let chaoxiang_index = this.form.huxing.options[1].range[0].rules.findIndex(item => item.value === parseInt(data.chaoxiang))
        if (chaoxiang_index >= 0) this.form.huxing.options[1].value = [chaoxiang_index]
      }
      if (this.form.huxing.options[2].range.length > 0) {
        let zhuangxiu_index = this.form.huxing.options[2].range[0].rules.findIndex(item => item.value === parseInt(data.zhuangxiu))
        if (zhuangxiu_index >= 0) this.form.huxing.options[2].value = [zhuangxiu_index]
      }
      this.labels_value = data.label.map(item => parseInt(item))
      let type_index = this.form.leixing.options[0].range[0].rules.findIndex(item => item.value === data.catid)
      // let area_index = this.form.leixing.options[1].range[0].rules.findIndex(item=>item.value===data.areaid)
      if (type_index >= 0) {
        this.form.leixing.options[0].value = [type_index]
      }
      // this.is_company_house = 1
      // this.params.qf_uuid ="1"
      this.house_info = Object.assign({}, this.params)
      console.log(this.house_info);
      // if(area_index>=0){
      //   this.form.leixing.options[1].value = [area_index]
      // }
    },
    getInfoData(info_id) {
      this.$ajax.get('release/getEditInfo.html', { info_id }, res => {
        uni.hideLoading()
        if (res.data.code === -13) {
          this.upgrade_type = 'editor'
          // 提示扣除金币
          showModal({
            content: res.data.msg,
            confirm: () => {
              console.log('执行升级个人vip')
              this.upgrade()
            },
            cancel: () => {
              this.$navigateBack()
            }
          })
          return
        }
        if (res.data.code == 1) {
          this.levelid = res.data.levelid || 2
          this.open_release_info_loudong = res.data.release_info_loudong
          this.imgCount = parseInt(res.data.maxUploadNum)
          this.$store.state.imgSize = res.data.maxUploadSize
          this.activeTimeSurplus = res.data.activeTimeSurplus
          this.release_need_select_community = res.data.release_need_select_community
          this.open_info_internal_no = res.data.open_info_internal_no
          this.is_company_house = res.data.is_company_house
          this.is_company_houses_editable = res.data.is_company_houses_editable
          console.log(res.data.house_info)
          if (this.is_company_house == 1 && this.is_company_houses_editable == 0) {
            this.isColor = 2
          }
          if (res.data.house.buildid && this.is_company_house == 1 && this.is_company_houses_editable == 0) {
            this.isColorto = 2
          }
          if (res.data.area) {
            this.area_list = res.data.area
            var current_area = this.getArea(this.area_list, res.data.house.areaid)
            if (current_area) {
              this.area_name = current_area.areaname
            }
          }
          this.form.huxing.options[0].range = res.data.list
            .filter(item => {
              return ['shi', 'ting', 'wei'].includes(item.identifier)
            })
            .map(item => {
              // 处理室厅卫名称为数字的问题
              item.rules.map(el => {
                if (!isNaN(el.name)) {
                  el.name = el.name + item.title
                }
                return el
              })
              return item
            })
          this.form.huxing.options[1].range = res.data.list.filter(item => item.identifier === 'chaoxiang')
          this.form.huxing.options[2].range = res.data.list.filter(item => item.identifier === 'zhuangxiu')
          this.form.leixing.options[0].range[0].rules = res.data.catelist
          // this.form.leixing.options[1].range[0].rules = res.data.arealist.map(item => {
          //   return { value: item.areaid, name: item.areaname }
          // })
          this.form.labels = res.data.labels.map(item => {
            return {
              name: item.name,
              value: item.id
            }
          })
          this.releasePrice = res.data.releasePrice
          this.freeActiveTime = res.data.freeActiveTime
          this.youxiaoqi = res.data.activeTime || []
          this.youxiaoqiHasMoney = this.youxiaoqi.some(item => parseFloat(item.money))
          // this.extension_ding = res.data.tops
          // this.extension_jing = res.data.jingxuan
          // this.freeSelected = res.data.freeSelected
          this.params.contact_who = res.data.contact_who
          this.handleData(res.data.house)
          this.handleOtherData(res.data.otherlist)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    getArea(arr, id) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].areaid == id) {
          return arr[i]
          break;
        }
        if (arr[i].children && arr[i].children.length > 0) {
          let res = this.getArea(arr[i].children, id)
          if (res) {
            return res
          }
        }
      }
    },
    showAreaPopup() {
      if (this.house_info.areaid && this.is_company_house == 1 && this.is_company_houses_editable == 0) {
        uni.showToast({
          title: "不可更改请联系客服",
          icon: "none"
        })
        return
      }
      this.$refs.area_popup.show()
    },
    onAreaChange(e) {
      this.area_name = e[e.length - 1].name
      this.params.areaid = e[e.length - 1].value
      this.$refs.area_popup.hide()
    },
    handelCheckLabel(e) {
      this.params.label = e.value.join(',')
    },
    pickerChange(e) {
      uni.hideKeyboard()
      this.params[e._name] = e.value
      // this.setTitle()
    },
    radioChange(e) {
      uni.hideKeyboard()
      this.params[e._name] = e.detail.value
    },
    handelCheckbox(e) {
      uni.hideKeyboard()
      this.params[e._name] = e.value.join(',')
    },
    toSearch() {
      this.$navigateTo('/user/search_areas')
    },
    toAddDesc() {
      if (this.is_company_house == 1 && this.is_company_houses_editable == 0) {
        uni.showToast({
          title: "不可更改请联系客服",
          icon: "none"
        })
        return
      }
      setTimeout(() => {
        uni.$emit('giveDesc', { content: this.params.content, owner_think: this.params.owner_think, service_introduce: this.params.service_introduce, disabled: (this.is_company_house == 1 && this.is_company_houses_editable == 0) ? true : false })
      }, 300)
      this.$navigateTo(`/user/add/add_desc?levelid=${this.levelid}&parentid=${this.parentid}`)
    },
    addVerification() {
      // if(this.is_company_house==1&&this.is_company_houses_editable==0) {
      //   uni.showToast({
      //     title:"不可更改请联系客服",
      //     icon:"none"
      //   })
      //   return 
      // }
      setTimeout(() => {
        uni.$emit('giveVerification', {
          verification_code: this.params.verification_code,
          verification_qrcode: this.params.verification_qrcode,
          disabled: this.is_company_house == 1 && this.is_company_houses_editable == 0 ? true : false
        })
      }, 200)
      this.$navigateTo(`/user/add_verification`)
    },
    onClickTime(item) {
      this.params.activetime_id === item.id ? (this.params.activetime_id = '') : (this.params.activetime_id = item.id)
    },
    onClickTop(ding_item) {
      this.params.tops_id === ding_item.id ? (this.params.tops_id = '') : (this.params.tops_id = ding_item.id)
    },
    onClickFreeJingxuan() {
      (this.params.freeSelected ? (this.params.freeSelected = '') : (this.params.freeSelected = 1)) && (this.params.jingxuan_id = '')
    },
    onClickJingxuan(jing_item) {
      (this.params.jingxuan_id === jing_item.id ? (this.params.jingxuan_id = '') : (this.params.jingxuan_id = jing_item.id)) && (this.params.freeSelected = '')
    },
    mapMark() {
      // 标注
      // #ifdef APP-PLUS
      plus.key.hideSoftKeybord()
      uni.chooseLocation({
        keyword: '',
        success: res => {
          this.params.address = res.address
          this.params.lat = res.latitude
          this.params.lng = res.longitude
        }
      })
      // uni.getLocation({
      // 	type: 'gcj02',
      // 	geocode: true,
      // 	success: (res)=> {
      // 		console.log(res)
      // 		uni.chooseLocation({
      // 			keyword: res.address.district+res.address.street,
      // 			success: (res)=> {
      // 				this.params.address = res.address
      // 				this.params.lat = res.latitude
      // 				this.params.lng = res.longitude
      // 			}
      // 		})
      // 	},
      // 	fail:()=>{
      // 		console.log("定位失败")
      // 		uni.chooseLocation({
      // 			keyword:"",
      // 			success: (res)=> {
      // 				this.params.address = res.address
      // 				this.params.lat = res.latitude
      // 				this.params.lng = res.longitude
      // 			}
      // 		})
      // 	}
      // });
      // #endif
      // #ifdef MP
      checkAuth('scope.userLocation', {
        authOk: () => {
          uni.chooseLocation({
            keyword: '',
            success: res => {
              this.params.address = res.address
              this.params.lat = res.latitude
              this.params.lng = res.longitude
            }
          })
        },
        success: () => {
          uni.chooseLocation({
            keyword: '',
            success: res => {
              this.params.address = res.address
              this.params.lat = res.latitude
              this.params.lng = res.longitude
            }
          })
        },
        fail: () => {
          this.show_dialog = true
        }
      })
      // #endif
      // #ifdef H5
      uni.chooseLocation({
        keyword: '',
        success: res => {
          this.params.address = res.address
          this.params.lat = res.latitude
          this.params.lng = res.longitude
        }
      })
      // #endif
    },
    subData() {
      // 表单验证
      if (!this.params.catid) {
        uni.showToast({
          title: '请选择房产类型',
          icon: 'none'
        })
        return
      }
      if (!this.params.areaid) {
        uni.showToast({
          title: '请选择区域',
          icon: 'none'
        })
        return
      }
      if (this.parentid == '1' || this.parentid == '2') {
        if (this.params.shi === undefined) {
          uni.showToast({
            title: "请选择室",
            icon: 'none'
          })
          return
        }
        if (this.params.ting === undefined) {
          uni.showToast({
            title: "请选择厅",
            icon: 'none'
          })
          return
        }
        if (this.params.wei === undefined) {
          uni.showToast({
            title: "请选择卫",
            icon: 'none'
          })
          return
        }
        if (!this.params.chaoxiang) {
          uni.showToast({
            title: "请选择朝向",
            icon: 'none'
          })
          return
        }
        if (!this.params.zhuangxiu) {
          uni.showToast({
            title: "请选择装修",
            icon: 'none'
          })
          return
        }
      }
      for (let key in this.form) {
        let item = this.form[key]
        if (item.required && !this.params[item.identifier]) {
          uni.showToast({
            title: `请输入${item.title}`,
            icon: 'none'
          })
          return
        }
      }
      if (Number(this.params.louceng) < Number(this.params.szlc)) {
        uni.showToast({
          title: '所在楼层不能大于总层数',
          icon: 'none'
        })
        return
      }
      if (this.params.title.length > 50) {
        uni.showToast({
          title: '标题最多为50个字',
          icon: 'none'
        })
        return
      }
      if (this.params.contact_who.length > 4) {
        uni.showToast({
          title: '联系人最多为4个字',
          icon: 'none'
        })
        return
      }
      if (this.params.contact_who.length > 4) {
        uni.showToast({
          title: '联系人最多4为个字',
          icon: 'none'
        })
        return
      }
      if (this.params.content.length > 1000) {
        uni.showToast({
          title: '描述最多1000为个字',
          icon: 'none'
        })
        return
      }
      // if(["1","2"].includes(this.parentid)&&!this.params.videos&&!this.params.imgs){
      // 	uni.showToast({
      // 		title:"请至少上传一张图片",
      // 		icon:"none"
      // 	})
      // 	return
      // }
      if (!this.on_ready) {
        uni.showToast({
          title: '请阅读并接受发布规则',
          icon: 'none'
        })
        return
      }
      this.checkRelease()
    },
    // 检测是否需要支付，后端会将信息存入临时表返回其id,不需要支付执行发布接口，需要支付执行扣除金币接口(仅发布使用)
    checkRelease() {
      uni.showLoading({
        title: '正在提交',
        mask: true
      })
      this.$ajax.get('release/checkEditInfo.html', { info_id: this.params.id, activetime_id: this.params.activetime_id }, res => {
        if (res.data.code !== 1) {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
          return
        }
        uni.hideLoading()
        if (res.data.is_release == 1) {
          this.subEditInfo()
          return
        }
        // 提示需要扣除金币或支付
        showModal({
          content: res.data.msg,
          confirm: () => {
            // pay_status === 1 代表用户金币足够支付，否则调起支付
            if (res.data.pay_status === 1) {
              this.userReleaseByCorn()
            } else {
              this.getOrderId()
            }
          }
        })
      })
    },
    // 不需要支付时执行的编辑接口
    subEditInfo() {
      uni.showLoading({
        title: '正在提交',
        mask: true
      })
      this.$ajax.post('release/editInfo.html', this.params, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg,
            mask: true
          })
          setTimeout(() => {
            this.$navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    // 执行扣除金币支付接口
    userReleaseByCorn() {
      uni.showLoading({
        title: '正在提交',
        mask: true
      })
      var params = Object.assign({}, this.params)
      delete params.cname
      this.$ajax.post('release/userEditInfoByCorn.html', params, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg
          })
          setTimeout(() => {
            this.$navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    getOrderId() {
      uni.showLoading({
        title: '正在提交',
        mask: true
      })
      var params = Object.assign({}, this.params)
      delete params.cname
      this.$ajax.post('release/editInfoByPay.html', params, res => {
        uni.hideLoading()
        if (res.data.code === 1) {
          this.handlePay(res.data.order_id)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    handlePay(order_id) {
      this.wxPay(order_id)
    },
    wxPay(order_id) {
      this.$ajax.post('release/userEditInfoByWxPay.html', { order_id }, res => {
        if (res.data.code === 1) {
          let pay_info = res.data.data
          this.wx.chooseWXPay({
            // provider: 'wxpay',
            timestamp: pay_info.timeStamp,
            nonceStr: pay_info.nonceStr,
            package: pay_info.package,
            signType: pay_info.signType,
            paySign: pay_info.paySign,
            success: res => {
              uni.showToast({
                title: '支付成功'
              })
              setTimeout(() => {
                uni.navigateBack()
              }, 1500)
            },
            fail: function (err) {
              console.log('支付失败：', err)
              uni.showToast({
                title: err.err_desc || err.errMsg,
                icon: 'none',
                duration: 5000
              })
            }
          })
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    // 修改信息
    editInfo() {
      uni.showLoading({
        title: '正在提交',
        mask: true
      })
      this.$ajax.post('house/editInformation.html', this.params, res => {
        uni.hideLoading()
        if (res.data.code === -13) {
          this.upgrade_type = 'push'
          // 提示扣除金币
          showModal({
            content: res.data.msg,
            confirm: () => {
              console.log('执行升级个人vip')
              this.upgrade()
            }
          })
          return
        }
        if (res.data.code == 1) {
          uni.showToast({
            title: res.data.msg,
            mask: true,
            duration: 2000
          })
          if (this.params.id) {
            //如果是修改，记录返回时重新请求数据
            this.$store.state.updatePageData = true
          }
          setTimeout(() => {
            this.$navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    /**
     * 执行升级个人永久vip
     */
    upgrade() {
      this.$ajax.post('member/upGradeVip', {}, res => {
        if (res.data.code === 1) {
          // 升级成功重新执行之前的接口
          if (this.upgrade_type === 'add') {
            this.getPageInfo()
          }
          if (this.upgrade_type === 'edit') {
            this.getInfoData()
          }
          if (this.upgrade_type === 'push') {
            this.subData()
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    handleConcatInput(e) {
      let text = e.match(/[a-zA-Z0-9_\u4e00-\u9fa5]{0,4}/g)[0]
      this.$nextTick(() => {
        this.$set(this.params, "contact_who", text)
      })
    },
    changeYouxiaoqi(item) {
      if (this.params.qf_uuid) {
        uni.showToast({
          title: "不可更改请联系客服",
          icon: "none"
        })
        return
      }
      (this.params.activetime_id === item.id ? (this.params.activetime_id = '') : (this.params.activetime_id = item.id))
    }
  },
  // #ifdef APP_PLUS
  onBackPress() {
    // 收起软键盘
    plus.key.hideSoftKeybord()
  }
  // #endif
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.flex-row {
  flex-direction: row;
}

.multiple_input {
  padding: 24rpx 0;

  >.label {
    line-height: 1;
    font-size: 22rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #666;
  }

  .input-item {
    color: #666;
    // flex: 1;
    align-items: center;

    ~.input-item {
      margin-left: 48rpx;
    }

    .label {
      min-width: 80rpx;
      margin-right: 24rpx;
    }

    input {
      flex: 1;
    }
  }
}

.upload-box {
  padding: 24rpx 0;
  align-items: center;

  .upload_btn {
    width: 128rpx;
    height: 128rpx;
    text-align: center;
    justify-content: center;
    background: #f2f2f2;
  }

  .cover_img {
    width: 128rpx;
    height: 128rpx;
  }

  .upload_tip {
    margin-left: 24rpx;
    margin-right: 6rpx;
    justify-content: center;

    .tip_title {
      font-size: 36rpx;
      color: #666;
      margin-bottom: 16rpx;
    }

    .tip_content {
      font-size: 22rpx;
      color: #999;
    }
  }
}

.block {
  margin-bottom: 20rpx;
  padding: 0 48rpx;
  background-color: #fff;

  input {
    color: #333;
  }
}

.open_menu {
  padding: 24rpx 0;
  justify-content: space-between;
  align-items: center;

  .tip {
    color: #999;
  }

  .ding {
    display: inline-block;
    margin-left: 20rpx;
    height: 36rpx;
    width: 36rpx;
    line-height: 36rpx;
    text-align: center;
    font-size: 22rpx;
    border-radius: 4rpx;
    background-color: $uni-color-primary;
    color: #fff;
  }

  .jing {
    display: inline-block;
    margin-left: 20rpx;
    height: 36rpx;
    width: 36rpx;
    line-height: 36rpx;
    text-align: center;
    font-size: 22rpx;
    border-radius: 4rpx;
    background-color: #00caa7;
    color: #fff;
  }
}

.more_info_box {
  height: 0;
  overflow: hidden;

  &.open {
    height: auto;
  }

  .types {
    justify-content: center;
    margin-bottom: 24rpx;

    .type-item {
      padding: 10rpx 24rpx;
      position: relative;
      font-size: 32rpx;

      &.active {
        color: $uni-color-primary;
      }

      &.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 35%;
        right: 35%;
        height: 8rpx;
        border-radius: 4rpx;
        background-color: $uni-color-primary;
      }
    }
  }

  .tip {
    padding-bottom: 20rpx;
    font-size: 24rpx;
    text-align: center;
    color: #999;
  }
}

.options-box {
  flex-wrap: wrap;
  justify-content: space-between;

  .options {
    width: 31%;
    border: 1rpx solid #d8d8d8;
    border-radius: 8rpx;
    padding: 24rpx 6rpx;
    margin-bottom: 24rpx;
    position: relative;
    color: #666;
    text-align: center;
    overflow: hidden;

    &.vacancy {
      height: 0;
      padding: 0;
      margin: 0;
      border: 0;
    }

    &.active {
      background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
      border-color: $uni-color-primary;

      .title {
        color: #000;
      }

      .price {
        color: #000;
      }

      .tip {
        background-color: $uni-color-primary;
      }
    }

    .title {
      text-align: center;
      margin-bottom: 10rpx;
    }

    .price {
      display: block;
      text-align: center;
      font-size: 50rpx;
      margin-bottom: 32rpx;

      .unit {
        font-size: 22rpx;
      }
    }

    .tip {
      font-size: 22rpx;
      position: absolute;
      bottom: 0;
      width: 100%;
      left: 0;
      padding: 6rpx 0;
      background-color: #d8d8d8;
      color: #fff;
    }
  }
}

.rule {
  padding: 10rpx 48rpx;
  font-size: 22rpx;
  align-items: center;

  checkbox {
    transform: scale(0.7);
  }

  .rule_link {
    color: #666;
  }
}

.btn-box {
  padding: 48rpx;

  .btn {
    height: 88rpx;
    line-height: 88rpx;
    background: #fb656a;
    box-shadow: 0 8rpx 32rpx 0 rgba(251, 101, 106, 0.4);
    border-radius: 44rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    text-align: center;
  }
}

.money_tip {
  padding: 18rpx 48rpx;
  font-size: 26rpx;
  flex-direction: row;
  ;
  align-items: center;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.15);
  color: $uni-color-primary;

  .text {
    margin-left: 10rpx;
  }
}

.select_btn {
  padding: 24rpx 0;
  justify-content: space-between;
  align-items: center;

  .label {
    margin-bottom: 24rpx;
    font-size: 24rpx;
    color: #666;
  }

  .value {
    font-size: 36rpx;
    color: #999;

    &.has_value {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

.input-hui {
  background-color: #f5f5f5;
}
</style>
