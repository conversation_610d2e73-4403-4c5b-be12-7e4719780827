<template>
<view class="friend">
<view class="title bottom-line">
    <text>我的同事</text>
</view>
<view class="adviser-list">
    <view class="adviser-item flex-box" v-for="(adviser, index) in adviser_list" :key="index">
        <view class="flex-box flex-1">
            <image class="head-img" :src="adviser.prelogo | imgUrl('w_120')" mode="aspectFill"></image>
            <view>
                <view class="name">{{adviser.cname}}</view>
                <view class="tel">{{adviser.tel}}</view>
            </view>
        </view>
        <view class="flex-box">
            <view @click="handleTel(adviser)" class="icon-box"><my-icon type="dianhua1" color="#f65354" size="26"></my-icon></view>
        </view>
    </view>
</view>
</view>
</template>

<script>
import {
  formatImg,
} from '../common/index.js'
import myIcon from '../components/icon.vue'
import getChatInfo from '../common/get_chat_info'
export default {
    data() {
        return {
            adviser_list:[]
        }
    },
    computed: {
        has_chat(){
            return this.$store.state.im.ischat
        }
    },
    onLoad(options){
        this.online_id = options.online_id||''
        this.getData()
    },
    components: {
        myIcon
    },
     filters: {
        imgUrl(val, param = "") {
            return formatImg(val, param)
        }
    },
    methods: {
        getData(){
            this.$ajax.get('onlineMy/workTogether.html',{online_id:this.online_id},res=>{
                if(res.data.code === 1){
                    this.adviser_list = res.data.list
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
            })
        },
        handleTel(adviser, bid){
            if(!adviser.tel){
                uni.showToast({
                    title:'此置业顾问没有绑定手机号',
                    icon:'none'
                })
                return
            }
            uni.makePhoneCall({
                phoneNumber: adviser.tel
            });
        },
        toChat(id){
            getChatInfo(id, 15)
        },
    },
}
</script>

<style scoped lang="scss">
.title{
    padding: 20rpx 20rpx 20rpx 40rpx;
    font-size: 32rpx;
    font-weight: bold;
    position: relative;
    &::before{
        content: "";
        position: absolute;
        left:20rpx;
        top:20rpx;
        bottom:20rpx;
        width: 6rpx;
        background-color: #f65354
    }
}
.adviser-list{
    box-shadow: 0 0 18upx #dedede;
    background-color: #fff;
    margin: 20rpx;
    .adviser-item{
        justify-content: space-between;
        align-items: center;
        padding: 24upx 32upx;
        .head-img{
            width: 90upx;
            height: 90upx;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 32upx;
        }
        .name{
            font-size: 30upx;
            line-height: 1.8;
            font-weight: bold;
        }
        .tel{
            color: #666;
        }
    }
    .icon-box{
        margin-left: 28upx;
    }
}
</style>
