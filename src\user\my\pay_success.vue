<template>
<view class="page">
    <view class="text-center">
        <view style="margin-top:30upx;">
            <my-icon type="check-circle" size="120rpx" color="#68d48b"></my-icon>
        </view>
        <view style="margin-top:30upx;font-size:32rpx">支付成功！</view>
    </view>
</view>
</template>

<script>
import myIcon from "../../components/myIcon"
export default {
    data() {
        return {

        }
    },
    components: {
        myIcon
    },
    onReady(){
        setTimeout(()=>{
            window.location.href="/m/pages/my/my"
            // history.go(-2)
        },2000)
    }
}
</script>

<style scoped lang="scss">
.page{
    padding: 38upx 20upx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #fff
}
</style>
