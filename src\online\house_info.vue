<template>
<div class="fangyuan_detail">
<view class="info_list bottom-line card">
    <view class="row-title bottom-line">
        <text>{{house_detail.build_title||''}}{{house_detail.building_number||''}}楼{{house_detail.name||''}}</text>
    </view>
    <view class="row w-100 bottom-line">
        <text class="label">备案状态</text>
        <text class="text">{{house_detail.beian_status}}</text>
    </view>
    <view class="row w-100 bottom-line">
        <text class="label">所在区域</text>
        <text class="text">{{house_detail.address}}</text>
    </view>
    <view class="row w-100 bottom-line">
        <text class="label">楼栋</text>
        <text class="text">{{house_detail.building_number}}</text>
    </view>
    <view class="row w-100 bottom-line">
        <text class="label">房号</text>
        <text class="text ">{{house_detail.name}}</text>
    </view>
    <view class="row w-100 bottom-line">
        <text class="label">建筑面积</text>
        <text class="text" v-if="house_detail.jzmj">{{house_detail.jzmj}}m²</text>
    </view>
    <view class="row w-100 bottom-line">
        <text class="label">套内面积</text>
        <text class="text" v-if="house_detail.tnmj">{{house_detail.tnmj}}m²</text>
    </view>
    <view class="row w-100 bottom-line">
        <text class="label">备案价</text>
        <text class="text">{{house_detail.badj?house_detail.badj+'元/m²':'暂未更新'}}</text>
    </view>
    <view class="row w-100 bottom-line">
        <text class="label">朝向</text>
        <text class="text">{{house_detail.chaoxiang}}</text>
    </view>
    <view class="row w-100 bottom-line">
        <text class="label">价格</text>
        <text class="text">{{house_detail.danjia?house_detail.danjia+'元/m²':'暂未更新'}}</text>
    </view>
    <view class="row w-100 bottom-line">
        <text class="label">层高</text>
        <text class="text" v-if="house_detail.cenggao">{{house_detail.cenggao?house_detail.cenggao+'m':'暂未更新'}}</text>
    </view>
</view>
</div>
</template>

<script>
import {wxShare} from '../common/mixin'
export default {
    data() {
        return {
            house_detail:{}
        }
    },
    mixins: [wxShare],
    onLoad(options){
        if(options.number_id&&options.house_id){
            this.number_id = options.number_id
            this.house_id = options.house_id
            this.getData()
        }
    },
    methods:{
        getData(){
            this.$ajax.get('building/houseInfo.php',{number_id:this.number_id,house_id:this.house_id},res=>{
                if(res.data.code === 1){
                    this.house_detail = res.data.house
                }else{
                    uni.showToast({
                        title:res.data.msg,
                        icon:'none'
                    })
                }
                if(res.data.share&&res.data.share.title){
                    this.share = res.data.share
                    this.getWxConfig()
                }
            })
        }
    }
}
</script>

<style scoped lang="scss">
.row-title{
    padding: 25rpx 20rpx;
    line-height: 1.2;
    font-size: 34rpx;
    font-weight: bold;
    background-color: #fff;
    margin-bottom: 20rpx;
    text{
        padding-left: 25rpx;
        border-left: 3px solid #f65354;
    }
}

.card {
    // margin: 24rpx;
    // padding: 26rpx;
    // border-radius: 10rpx;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 0 10px #dedede;
}
.title{
    padding: 20rpx 20rpx 20rpx 40rpx;
    font-size: 32rpx;
    position: relative;
    background-color: #fff;
    &::before{
        content: "";
        position: absolute;
        left:20rpx;
        top:20rpx;
        bottom:20rpx;
        width: 6rpx;
    }
    .close{
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        padding: 10rpx;
        transform:rotate(45deg);
    }
}

.info_list {
    flex-wrap: wrap;
    margin-bottom: 20rpx;
    background-color: #fff;
    .w-100 {
        width: 100%;
        padding-top: 20rpx;
        padding-bottom: 20rpx;
        box-sizing: border-box;
        .label{
            display: inline-block;
            min-width: 140rpx;
            margin-right: 30rpx;
            text-align-last:justify;
        }
    }
}

</style>
