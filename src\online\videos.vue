<template>
<view class="page">
    <view class="video_list">
        <view class="video_item" v-for="(video, index) in videos" :key="index">
            <view class="video_box">
                
                <video :src="video.media_url"  @error="videoErrorCallback" controls x5-playsinline></video>
            </view>
           
        </view>   

    </view>
</view>
</template>

<script>
import {
  formatImg
} from '../common/index.js'
// #ifdef MP-BAIDU|| H5
import {setSeo} from '../common/mixin'
// #endif
export default {
    data() {
        return {
            videos:[],
            share:{},
            seo:{},
            name:"",

        }
    },
    components: {

    },
    // #ifdef MP-BAIDU|| H5
        mixins:[setSeo],
    // #endif
    onLoad(options){
        if(options.roomId){
            this.getData(options.roomId)
            this.name =options.roomName;
            uni.setNavigationBarTitle({
                title:this.name+"直播回放"
            })
        }else{
            uni.showToast({
                title:"视频不存在",
                icon:'none'
            })
        }
    },
    filters: {
        imgUrl(val, param = "") {
            return formatImg(val, param)
        }
    },
    methods:{
        getData(id){
            this.$ajax.get('LiveVideo/livereplay.html',{room_id:id},res=>{
                if (res.data.share){
						this.share = res.data.share
					}
                if (res.data.seo){
                    this.seo = res.data.seo
                }
                if(res.data.code === 1){
                    this.videos = res.data.live_replay
                }
            })
        },
        videoErrorCallback(e){
            console.log(e)
        }
    },
    onShareAppMessage(){
        return {
			title:this.share.title||"",
            content:this.share.content||"",
            imageUrl:this.share.pic?formatImg(this.share.pic,'w_6401'):""
		}
    }
}
</script>

<style scoped lang="scss">
.label{
    font-size: 32upx;
    padding: 20upx;
}
.video_list{
    padding: 24upx;
    .video_item{
        width: 100%;
        margin-bottom: 30upx;
        background-color: #fff;
        border-radius: 8upx;
        overflow: hidden;
        box-shadow: 0 0 18upx #dedede;
        .video_box{
            width: 100%;
            height: 56vw;
            // border-radius: 8upx;
            // overflow: hidden;
            position: relative;
            .video_img{
                width: 100%;
                height: 100%;
            }
            .video-icon{
                position: absolute;
                width: 120upx;
                height: 120upx;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                margin: auto;
                z-index: 2;
            }
            video{
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>
