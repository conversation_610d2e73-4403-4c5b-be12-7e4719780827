<template>
  <view>
    <!-- #ifdef H5 || APP-PLUS -->
      <!-- 选择支付方式 -->
      <view class="pay_container">
        <view class="pay_type pay_box">
          <view class="title">选择支付方式</view>
          <radio-group @change="radioChange">
            <label class="pay_item flex-box">
              <view class="pay_name flex-box">
                <my-icon type="zhifubao" color="#02A9F1" size="56rpx"></my-icon>
                <text class="text">支付宝支付</text>
              </view>
              <radio value="alipay" color="#FB656A"></radio>
            </label>
            <label class="pay_item flex-box">
              <view class="pay_name flex-box">
                <my-icon type="weixinzhifu" color="#28C445" size="56rpx"></my-icon>
                <text class="text">微信支付</text>
              </view>
              <radio value="wechatpay" color="#FB656A"></radio>
            </label>
          </radio-group>
        </view>
        <view class="btn" @click="handlePayinfo()">确定</view>
        <view v-if="show_cancel" class="cancel_btn" @click="$emit('cancel')">取消</view>
      </view>
      <!-- #endif -->
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
export default {
  components: {
    myIcon
  },
  data() {
    return {
      pay_type:''
    }
  },
  props:{
    mpWxPayApi:{
      type:String,
      default:''
    },
    h5WxPayApi:{
      type:String,
      default:''
    },
    h5AliPayApi:{
      type:String,
      default:''
    },
    appWxPayApi:{
      type:String,
      default:''
    },
    appAliPayApi:{
      type:String,
      default:''
    },
    pay_params:{
      type:Object,
      default(){
        return {}
      }
    },
    is_saihongbao: {
      type: Boolean,
      default: false
    },
    hongbao_params:{
      type:Object,
      default(){
        return {}
      }
    },
    show_cancel: Boolean
  },
  methods: {
    radioChange(e){
      this.pay_type = e.detail.value
    },
    handlePayinfo() {
      if (this.is_saihongbao) {
        this.hongbao_pay()   //塞红包支付
      } else {
        // #ifdef H5
        this.H5Pay()
        // #endif
        // #ifdef APP-PLUS
        this.appPay()
        // #endif
      }
    },
    // #ifdef H5
    /**
     * h5支付
     */
    H5Pay() {
      if (!this.pay_type) {
        uni.showToast({
          title: '请选择支付方',
          icon: 'none'
        })
        return
      }
      this.$emit('before_pay')
      if (this.pay_type === 'alipay') {
        let pay_url = `${this.h5AliPayApi}?token=${uni.getStorageSync("token")||""}`
        for(let key in this.pay_params){
          pay_url+=`&${key}=${this.pay_params[key]}`
        }
        window.open(pay_url)
        // console.log(pay_url)
      } else if (this.pay_type === 'wechatpay') {
        let pay_url = `${this.h5WxPayApi}?token=${uni.getStorageSync("token")||""}`
        for(let key in this.pay_params){
          pay_url+=`&${key}=${this.pay_params[key]}`
        }
        window.open(pay_url)
        // console.log(pay_url)
      }
    },
    // #endif
    // #ifdef MP-WEIXIN
    /**
     * 微信小程序支付
     */
    mpWxPay() {
      uni.showLoading({
        title:'正在支付...'
      })
      this.$emit('before_pay')
      this.$ajax.post(this.mpWxPayApi,this.pay_params,res=>{
        if(res.data.code === 1){
          let pay_info = res.data.data
          uni.requestPayment({
            provider: 'wxpay',
            timeStamp: pay_info.timeStamp,
            nonceStr: pay_info.nonceStr,
            package: pay_info.package,
            signType: pay_info.signType,
            paySign: pay_info.paySign,
            success: res => {
              this.$emit('pay_success')
            },
            fail: err=> {
              this.$emit('pay_fail',err)
              console.log(err)
              uni.showToast({
                title: err.err_desc || (err.errMsg == 'requestPayment:fail cancel' ? '已取消支付' : err.errMsg),
                icon: 'none',
                duration: 5000
              })
            }
          })
          uni.hideLoading()
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    },
    // #endif
    // #ifdef APP-PLUS
    /**
     * app支付
     */
    appPay() {
      if (!this.pay_type) {
        uni.showToast({
          title: '请选择支付方式',
          icon: 'none'
        })
        return
      }
      let api, provider
      if (this.pay_type === 'alipay') {
        api = this.appAliPayApi
        provider = 'alipay'
      } else if (this.pay_type === 'wechatpay') {
        api = this.appWxPayApi
        provider = 'wxpay'
      }
      this.$ajax.get(api, this.pay_params, res => {
        if (res.data.code == 1) {
          this.handelAppPay(res.data.pay_info, provider)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none',
            mask: true
          })
        }
      })
    },
    handelAppPay(orderInfo, provider) {
      this.$emit('before_pay')
      uni.requestPayment({
        provider: provider,
        orderInfo: orderInfo,
        success: res => {
          this.$emit('pay_success')
        },
        fail: (err)=> {
          console.log(err)
          console.log("orderInfo:", orderInfo)
          console.log("provider:", provider)
          this.$emit('pay_fail',err)
          uni.showToast({
            title: err.err_desc || (err.errMsg == 'requestPayment:fail cancel' ? '已取消支付' : err.errMsg),
            icon: 'none',
            duration: 5000
          })
        }
      })
    },
    // #endif
    hongbao_pay() {
      if (!this.pay_type) {
        uni.showToast({
          title: '请选择支付方',
          icon: 'none'
        })
        return
      }
      let params = this.hongbao_params
      // let saiParams = {
      //   info_id: this.hongbao_params.info_id,
      //   info_type: this.hongbao_params.info_type,
      //   money: this.hongbao_params.money,
      // }
      // this.$ajax.post('WxMoney/saihb', saiParams, (res) => {
      //   if (res.data.code == 1) {
      //     if (res.data.sai_id) {
      //       let payParams = {
      //         sai_id: res.data.sai_id,
      //       }
      //       if (this.pay_type === 'alipay') {
      //         payParams.pay_type = 2
      //       } else if (this.pay_type === 'wechatpay') {
      //         payParams.pay_type = 1
      //       }
      //       this.$ajax.post('WxMoney/saiHbPay', payParams, (payRes) => {
      //         if (payRes.data.code == 1) {
      //           window.open(payRes.data.url)
      //         } else {
      //           uni.showToast({
      //             title:payRes.data.msg,
      //             icon:'none'
      //           })
      //         }
      //       })
      //     }
      //   } else {
      //     uni.showToast({
      //       title:res.data.msg,
      //       icon:'none'
      //     })
      //   }
      // })
      if (this.pay_type === 'alipay') {
        params.pay_type = 2
      } else if (this.pay_type === 'wechatpay') {
        params.pay_type = 1
      }
      this.$ajax.post('WxMoney/saihb', params, (res) => {
        if (res.data.code == 1) {
          window.open(res.data.url)
        } else {
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.pay_container{
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  background-color: #f5f5f5;
  .pay_box{
      padding: 0 48rpx;
  }
  .pay_type{
    padding-top: 50rpx;
    .title{
        font-size: 40rpx;
        font-weight: bold;
        margin-bottom: 24rpx;
        color: #333;
    }
    .pay_item{
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0;
        .pay_name{
          align-items: center;
            .text{
                margin-left: 20rpx;
            }
            .tip{
                color: #999999;
            }
        }
    }
  }
  .btn{
      margin-top: 90rpx;
      padding: 28rpx;
      font-size: 32rpx;
      background-color: #fff;
      text-align: center;
      color: $uni-color-primary;
  }
  .cancel_btn{
    padding: 0 12rpx;
    padding-top: 12rpx;
    padding-bottom: 48rpx;
    text-align: center;
    font-size: 26rpx;
    background-color: #fff;
    color: #999;
  }
}
</style>
