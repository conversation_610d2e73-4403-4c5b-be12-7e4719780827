<template>
  <div class="page"  :style="{width:systemInfo.safeArea.width+'px',height:systemInfo.screenHeight+'px'}">
    
	
    <div class="map-box" :style="{width:systemInfo.safeArea.width+'px',height:systemInfo.screenHeight+'px'}">
      <map
        id="map"
		:style="{width:systemInfo.safeArea.width+'px',height:systemInfo.screenHeight+'px'}"
        :scale="scale"
        :longitude="mapcenter.longitude"
        :latitude="mapcenter.latitude"
        :markers="school_point"
        :polygons="polygons"
        @markertap="onClickMarker"
        @regionchange="onRegionchange"
      ></map>
      <div class="school-box" v-if="currentSchool.id">
        <div
          class="item-box bottom-line"
          hover-class="item-hover"
          :hover-start-time="60"
          :hover-stay-time="120"
          @click="navigateTo(`/school/detail?id=${currentSchool.id}`)"
        >
          <div class="image-box">
            <image class="image" :src="currentSchool.master_pic| imgUrl" resize="cover"></image>
          </div>
          <div class="info-box">
            <div class="title">
              <div class="text-box">
                <text class="t_text">{{currentSchool.name}}</text>
              </div>
            </div>
            <div class="content_c">
              <div class="infos">
                <div class="tags">
					
                  <text
                    class="tag"
                    v-for="(tag, index) in [{name:currentSchool.cname},{name:currentSchool.type_name}]"
                    :key="index"
                    
                    
                  >{{ tag.name }}</text>
                </div>
              </div>
			  
              
            </div>
			<div class="school_tip">
			  <text class="label">{{ currentSchool.school_status }}</text>
			</div>
			<div class="address_distance">
				<div class="address">
				  <text class="text address_text">{{ currentSchool.address }}</text>
				 </div>
				 <!-- <my-icon
					v-if="distance"
					type="ic_map"
					color="#999"
					size="34rpx"
				  ></my-icon> -->
				  <div class="distance " v-if="currentSchool.distance">  <text class="iconfont icon_dibiao">&#xe8e6;</text><text class="distance_text">{{ currentSchool.distance }}</text></div>
            </div>
          </div>
        </div>
      </div>
      
      
    </div>
	<div class="mask" @click="stopMove"  :style="{width:systemInfo.safeArea.width+'px',height:show_mask?(systemInfo.windowHeight+'px'):'0px'}"></div>
	<scroller
	  class="filter"
	  :class="{
		init:!on_ready,
	    show: active === 1
	  }"
	  :style="{marginTop:systemInfo.statusBarHeight+'px'}"
	  id="filter1"
	>
	  <div class="filter-list" :style="{width:systemInfo.safeArea.width+'px'}">
	    <div
	      class="filter-item"
	      v-for="item in area_list"
	      :key="item.id"
	      @click="onClickArea(item)"
	    >
		<text
		  class="my-tag"
		  :class="{'info':item.id != params.area_id    
		  }"
		  
		>{{item.name}}</text>
	    </div>
	  </div>
	</scroller>
	<scroller
	  class="filter"
	  :class="{
		init:!on_ready,
	    show: active === 2
	  }"
	
	  :style="{marginTop:systemInfo.statusBarHeight+'px'}"
	  id="filter2"
	>
	  <div class="filter-list" :style="{width:systemInfo.safeArea.width+'px'}">
		  
	   <div
	      class="filter-item"
	      v-for="item in cate_list"
	      :key="item.id"
	      @click="onClickCate(item)"
	    >
		<text
		  class="my-tag"
		  :class="{'info':item.id != params.cate_id    
		  }">{{item.name}}</text>
	      
	    </div>
	  </div>
	</scroller>
	<scroller
	  class="filter"
	  :class="{
		init:!on_ready,
	    show: active === 3
	  }"
	  :style="{marginTop:systemInfo.statusBarHeight+'px'}"
	  id="filter3"
	>
	  <div class="filter-list" :style="{width:systemInfo.safeArea.width+'px'}">
	    <div
	      class="filter-item"
	      v-for="item in type_list"
	      :key="item.id"
	      @click="onClickType(item)"
	    >
		<text
		  class="my-tag"
		  :class="{'info':item.id != params.type    
		  }"
		  
		>{{item.name}}</text>
	      <!-- <my-tag :type="item.id === params.type ? '' : 'info'">{{
	        item.name
	      }}</my-tag> -->
	    </div>
	  </div>
	</scroller>
	<scroller
	  class="filter"
	  :class="{
		init:!on_ready,
	    show: active === 4
	  }"
	
	  :style="{marginTop:systemInfo.statusBarHeight+'px'}"
	  id="filter4"
	>
	  <div class="filter-list" :style="{width:systemInfo.safeArea.width+'px'}">
	    <div
	      class="filter-item"
	      v-for="item in status_list"
	      :key="item.id"
	      @click="onClickStatus(item)"
	    >
		<text
		  class="my-tag"
		  :class="{'info':item.id != params.school_status    
		  }">{{item.name}}</text>
	     <!-- <my-tag :type="item.id === params.school_status ? '' : 'info'">{{
	        item.name
	      }}</my-tag> -->
	    </div>
	  </div>
	</scroller>
	<div class="fixed-top" :style="{paddingTop:systemInfo.statusBarHeight+'px',width:systemInfo.safeArea.width+'px'}">
	  <!-- 搜索框 -->
	  <div
	    class="title_bar"
	    style="backgroundColor: #ffffff"
		:style="{width:systemInfo.safeArea.width+'px'}"
	  >
	    <div class="left_box" @click="back()">
			<text class="iconfont back">&#xe654;</text>
	      <!-- <my-icon type="back" size="27"></my-icon> -->
	    </div>
	    <div class="center_box">
	    
	        <div class="search-box">
	          <div class="search-left">
				  <text class="iconfont icon_sousuo">&#xe86f;</text>
	          </div>
	          <input
	            type="text"
				class="search_inp"
	            confirm-type="search"
	            placeholder-style="font-size:14px;color:#999;"
	            placeholder="找到你的学校？"
	            v-model="params.keywords"
	            @confirm="onSearch"
	          />
	        </div>
	    </div>
	    <div class="right_box">
	       <text class="shou" @click="getUp">收起</text>
	    </div>
	  </div>
	  
	  <div class="filter-tab">
	    <div
	      v-for="filter_type in filter_types"
	      :key="filter_type.type"
	      class="filter-tab-item"
	      @click="switchTab(filter_type.type)"
	    >
	      <text class ="filter_text">{{filter_type.name }}</text>
		  <text v-if="active !== filter_type.type" class="iconfont icon_down">&#xe629;</text>
		  <text v-if="active === filter_type.type" class="iconfont icon_up">&#xe62c;</text>
	    </div>
	  </div>
	</div>
	
  </div>
</template>

<script>
// import titleBar from "@/components/titleBar";
// import mySearch from '@/components/mySearch'
// import myTag from '@/components/myTag'



import { mapActions, mapState } from 'vuex'
import { isArray,navigateTo,formatImg,compareVersion,showModal,ajax } from '../common/index'
export default {

  data() {
    return {
      active: 0,
      show_invit:false,
      user_status_tip:"当前服务需要开通VIP后查看",
      filter_types: [
        {
          name: '区域',
          type: 1
        },
        {
          name: '学段',
          type: 2
        },
        {
          name: '性质',
          type: 3
        },
        {
          name: '状态',
          type: 4
        }
      ],
      cate_list_simple: [
        {
          id: 4,
          name: '幼儿园'
        },
        {
          id: 2,
          name: '小学'
        },
        {
          id: 5,
          name: '初中'
        }
      ],
      area_list: [],
      cate_list: [],
      type_list: [],
      status_list: [],
      school_point: [],
      polygons: [],
      currentSchool: {},
      scale:15,
      // #ifdef H5
      qqmapkey:__uniConfig.qqMapKey,
      // #endif
      mapcenter:{
        latitude:"",
        longitude:""
      },
      params: {
        rows: 20,
        area_id: 0,
        cate_id: 2,
        type: 0,
        school_status: 0,
        isMap: 1,
        lng: '',
        lat: '',
        keywords: ''
      },
	  show_mask:false,
      userLoginStatus:null,
      menu_button_width:0,
      satisfyVersion:-1,
      baidu_map_all_getdata:true,
      user_location: {
        lat: '',
        lng: ''
      },
	  on_ready:false
    }
  },
  computed:{
    ...mapState(['systemInfo']),
  },
  onLoad() {
    this.init()
    this.map = uni.createMapContext("map",this);
	var domModule = weex.requireModule("dom");
	   domModule.addRule('fontFace', {  
	          'fontFamily': 'iconfont',  /* project id 1117313 */
	           'src':"url('http://at.alicdn.com/t/font_1117313_80ipebmwyye.ttf')",
	   })  
  },
  onReady(){
	setTimeout(()=>{
		this.on_ready = true
	},800)
  },
  filters: {
      imgUrl(img) {
          return formatImg(img, 'w_320')
      }
  },
  onShow(){
    if(this.isToUpVip){
      this.isToUpVip = false
      this.getUserStatus()
    }
  },
  // watch: {
  //   mapcenter(val, old_val) {
  //     if (
  //       val.latitude &&
  //       val.longitude &&
  //       (!old_val.latitude || !old_val.longitude)
  //     ) {
  //       this.params.lat = val.latitude
  //       this.params.lng = val.longitude
  //       this.getData()
  //     }
  //   }
  // },
  methods: {
    ...mapActions(['getLocation']),
    init() {
      this.getLocation({
        success: res => {
		console.log(res);
          if (res) {
            this.params.lat = res.latitude
            this.params.lng = res.longitude
            this.mapcenter.latitude = res.latitude
            this.mapcenter.longitude = res.longitude
            this.user_location = {
              lat: res.latitude,
              lng: res.longitude
            }
          }
          this.getData()
        },
        fail: err => {
          console.log(err)
          this.getMapCenter((e)=>{
            let point = e.cfg_mappoint.split(",")
            this.params.lat = point[0]
            this.params.lng = point[1]
            this.mapcenter.latitude = point[0]
            this.mapcenter.longitude = point[1]
            this.user_location = {
              lat: point[0],
              lng: point[1]
            }
            this.getData()
          })
        }
      })
      this.getFilter()
    },
    stopMove(){
      this.show_mask=false
      this.active=0
    },
    getMapCenter(callback){
      ajax.get('index/mappoint',{},res=>{
        if(res.data.code == 1){
          callback(res.data)
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:none
          })
        }
      })
    },
    switchTab(index) {
      if (index === this.active || index === 0) {
		    this.show_mask=false
        this.active = 0
      } else {
		    this.show_mask=true
        this.active = index
      }
    },
    onSearch(){
      this.active = 0
      this.currentSchool = {}
      this.getData()
    },
    getData() {
      if (!this.mapcenter.latitude || !this.mapcenter.longitude) {
        return
      }
      uni.showLoading({
        title:'正在获取学校...'
      })
      ajax.get('school/schoolList.html', this.params, res => {
        if (res.data.code === 1) {
          const pointIcons = {
            you: 'https://images.tengfangyun.com/images/icon/you.png',
            xiao: 'https://images.tengfangyun.com/images/icon/xiao.png',
            chu: 'https://images.tengfangyun.com/images/icon/chu.png',
            default: 'https://images.tengfangyun.com/images/icon/xiaoxuequ.png'
          }
          if(res.data.scale){
            this.scale = res.data.scale
          }
          this.school_list = res.data.list
          // 生成地图标记点
          this.school_point = res.data.list.map((school, index) => {
            let pointIcon
            switch (school.cid) {
              case 4: // 幼儿园
                pointIcon = pointIcons.you
                break
              case 2: // 小学
                pointIcon = pointIcons.xiao
                break
              case 5: // 初中
                pointIcon = pointIcons.chu
                break
              default:
                pointIcon = pointIcons.default
            }
            return {
              id: index + 1,
              school_id: school.id,
              iconPath: pointIcon,
              latitude: school.lat,
              longitude: school.lng,
              polygon: school.teach_range,
              width: 40,
              height: 40
            }
          })
          if (!this.currentSchool.id) {
            this.currentSchool = this.school_list[0]
            this.allowRegionchangeGetData = false
            setTimeout(()=>{
              this.allowRegionchangeGetData = true
            },500)
            this.mapcenter = {
              latitude:this.currentSchool.lat,
              longitude:this.currentSchool.lng
            }
            const polygonData = this.school_point[0].polygon
            this.setPolygons(polygonData)
          }
        } else {
          this.school_point = []
          this.polygons = []
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
        if (!this.share) {
          this.share = res.data.share || {}
        }
        uni.hideLoading()
        setTimeout(()=>{
          this.allowRegionchangeGetData = true
          this.baidu_map_all_getdata = true
        },500)
      },err=>{
        uni.hideLoading()
        console.log(err)
        setTimeout(()=>{
          this.allowRegionchangeGetData = true
          this.baidu_map_all_getdata = true
        },500)
      })
    },
    getFilter() {
      ajax.get('school/schoolCondition.html', {}, res => {
        if (res.data.code === 1) {
          this.area_list = [{ id: 0, name: '不限' }, ...res.data.data.areaList]
          // 高亮选中当前已选中的区域
          if (this.params.area_id) {
            this.filter_types[0].name = this.area_list.filter(item => {
              return item.id === this.params.area_id
            })[0].name
          }

          this.cate_list = [
            { id: 0, name: '不限' },
            ...res.data.data.schoolCateList
          ]
          // 高亮选中当前已选中的学校分类
          if (this.params.cate_id) {
            this.filter_types[1].name = this.cate_list.filter(item => {
              return item.id === this.params.cate_id
            })[0].name
          }

          this.type_list = [{ id: 0, name: '不限' }, ...res.data.data.types]
          // 高亮选中当前已选中的学校性质
          if (this.params.type) {
            this.filter_types[2].name = this.type_list.filter(item => {
              return item.id === this.params.type
            })[0].name
          }

          this.status_list = [
            { id: 0, name: '不限' },
            ...res.data.data.schoolStatus
          ]
          // 高亮选中当前已选中的学校性质
          if (this.params.school_status) {
            this.filter_types[3].name = this.status_list.filter(item => {
              return item.id === this.params.school_status
            })[0].name
          }
        }
        if(!this.getOnce){
          setTimeout(()=>{
            this.getUserStatus()
            this.getOnce = true
          },500)
        }
      })
    },
    navigateTo(url){
      navigateTo(url)
    },
    onClickArea(area) {
      this.params.area_id = area.id
      this.allowRegionchangeGetData = false
      setTimeout(() => {
        this.allowRegionchangeGetData = true
      }, 500)
      if (area.lat && area.lng) {
        this.params.lat = area.lat
        this.params.lng = area.lng
        this.mapcenter.latitude = area.lat
        this.mapcenter.longitude = area.lng
      } else if (this.user_location.lat && this.user_location.lng) {
        this.params.lat = this.user_location.lat
        this.params.lng = this.user_location.lng
        this.mapcenter.latitude = this.user_location.lat
        this.mapcenter.longitude = this.user_location.lng
      }
      this.filter_types[0].name = area.name
      this.active = 0
      this.currentSchool = {}
	    this.show_mask=false
      this.getData()
    },
    onClickCate(cate) {
      this.params.cate_id = cate.id
      this.filter_types[1].name = cate.name
      this.active = 0
      this.currentSchool = {}
	    this.show_mask=false
      this.getData()
    },
    onClickType(type) {
      this.params.type = type.id
      this.filter_types[2].name = type.name
      this.active = 0
      this.currentSchool = {}
	    this.show_mask=false
      this.getData()
    },
    onClickStatus(status) {
      this.params.school_status = status.id
      this.filter_types[3].name = status.name
      this.active = 0
      this.currentSchool = {}
	    this.show_mask=false
      this.getData()
    },
    onClickMarker(e) {
      let index
      index = e.detail.markerId

      this.currentSchool = this.school_list[index - 1]
      const currentSchool = this.school_point[index - 1]
      this.setPolygons(currentSchool.polygon)
    },
    setPolygons(polygonData) {
      if (!isArray(polygonData)) {
        this.polygons = []
        return
      }
      const color = '#f65354'
      this.polygons = polygonData.map(items => {
        const points = items.paths.map(item => {
          return {
            latitude: parseFloat(item.latitude),
            longitude: parseFloat(item.longitude)
          }
        })
        return {
          id: items.id,
          points,
          strokeWidth: 1,
          strokeColor: color,
          fillColor: `${color}33`
        }
      })
    },
    onRegionchange(e) {
      if(!this.allowRegionchangeGetData||!this.baidu_map_all_getdata){
        return
      }
      // #ifdef MP-WEIXIN
      if(e.causedBy === "gesture"||e.causedBy==='update'){
        return
      }
      // #endif
      const handle = function() {
        // #ifdef H5
        this.params.lat = e.lat
        this.params.lng = e.lng
        this.getData()
        // #endif

        // #ifndef H5
        // 获取中心点经纬度
				this.map.getCenterLocation({
					success:(res)=>{
						console.log(res);
						this.params.lat = res.latitude
            this.params.lng = res.longitude
            // #ifdef MP-BAIDU
            this.baidu_map_all_getdata = false
            // #endif
            this.mapcenter = {
              latitude:res.latitude,
              longitude:res.longitude
            }
						this.getData()
          },
          fail:(err)=>{
            console.log(err)
          }
				})
        // #endif
      }
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        handle.call(this)
      }, 300)
    },
    // 获取用户账号状态
    getUserStatus(){
      ajax.get('school/checkUserInfo',{},res=>{
        this.userLoginStatus = res.data.loginStatus
        if(this.userLoginStatus===0){
          // #ifndef MP
          // this.$refs.login_popup.showPopup()
          // #endif
          // #ifdef MP
          if(this.satisfyVersion>=0){
            this.user_status_tip = "当前服务需要会员登录后查看"
            this.show_invit = true
          }else{
            showModal({
              title:'温馨提示',
              content:"当前服务需要会员登录后查看",
              confirmText:"去登录",
              confirm:()=>{
                this.toLogin()
              },
              cancel:()=>{
                this.back()
              }
            })
          }
          // #endif
        }
        if(this.userLoginStatus === 1){
          // #ifndef MP
          // this.$refs.login_popup.closeSub()
          // #endif
          if(this.satisfyVersion>=0){
            this.user_status_tip = "您的账号已被封禁，请开通个人VIP后查看"
            // this.show_invit = true
          }else{
            showModal({
              title:'温馨提示',
              content:"您的账号已被封禁，请开通个人VIP后查看",
              confirmText:"去开通",
              confirm:()=>{
                this.toUpVip()
              },
              cancel:()=>{
                this.back()
              }
            })
          }
        }
        if(this.userLoginStatus === 2){
          // #ifndef MP
          // this.$refs.login_popup.closeSub()
          // #endif
          if(this.satisfyVersion>=0){
            this.user_status_tip = "当前服务需要开通个人VIP后查看"
            // this.show_invit = true
          }else{
            showModal({
              title:'温馨提示',
              content:"当前服务需要开通个人VIP后查看",
              confirmText:"去开通",
              confirm:()=>{
                this.toUpVip()
              },
              cancel:()=>{
                this.back()
              }
            })
          }
        }
        if(this.userLoginStatus===3){
          // #ifndef MP-WEIXIN
          // this.$refs.login_popup.closeSub()
          // #endif
          this.show_invit = false
        }
      })
    },
    // 登录成功的回调事件
    onLoginSuccess(){
      // 重新获取用户账号状态
      this.getUserStatus()
    },
    // 关闭登录窗口的事件
    handleCloseLogin(){
      // 如果是登录状态怎不做任何处理
      if(this.userLoginStatus !== 0){
        return
      }
      let pages = getCurrentPages() 
      if(pages.length>1){
        uni.navigateBack()
      }else{
        uni.switchTab({
          url:'/'
        })
      }
    },
    toUpVip(){
      this.isToUpVip = true
      navigateTo('/pages/my/member_upgrade')
    },
    toLogin(){
      this.isToUpVip = true
      uni.removeStorageSync('token')
      navigateTo('/user/login/login')
    },
    getUp(){
      this.active = 0 
      this.show_mask=false
    },
    back(){
      if (getCurrentPages().length > 1) {
        uni.navigateBack()
      } else {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
    }
  },
  onShareAppMessage() {
    if (this.share) {
      return {
        title: this.share.title||"",
        content:this.share.content||"",
        imageUrl: this.share.pic?formatImg(this.share.pic, 'w_6401'):""
      }
    }
  }
}
</script>

<style scoped lang="scss">
	.title_bar {
	  flex-direction: row;
	  align-items: center;
	  height: 44px;
	  padding: 7px 3px; 
	} 
	// &.title_bar 
	.fixed {
	  position: fixed;
	  top: 0;
	}
	.left_box {
		width: 27px;
		height: 27px;
		align-items: center;
	  // min-width: 27px;
	}
	.right_box{
		
	}
	.center_box {
	  flex: 1;
	  overflow: hidden;
	  
	  
	}
	// &.center_box
	.default {
	  padding: 0 40px;
	}
	
	 // titleBar 结束
.fixed-top {
  // width: 100%;
  position: absolute;
  top: 0;
  // top: var(--window-top);

  background-color: #fff;
}
.search-box {
  // margin-left: 20rpx;
  align-items: center;
  font-size: 12px;
  // padding: 10rpx 20rpx;
  background-color: #eeeeee;
  border-radius: 8rpx;
  flex-direction: row;
  
}
.search_inp{
	flex: 1;
	height:30px;
	font-size: 12px;
}
.search-left {
    // margin-right: 20rpx;
  }
.shou {
  padding: 20rpx 24rpx;
  color: #666;
  font-size: 30upx;
}

.filter-tab {
  flex-direction: row;
  // width: 100%;
  height: 80rpx;
  background-color: #fff;
 
}
 .filter-tab-item {
    flex: 1;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    
   
  }
.active{
      color: #f65354;
    }
// text {
//   margin-right: 10rpx;
//   transition-duration: 300ms;
// }
.filter {
	align-items: center;
	justify-content: flex-start;
  padding: 20rpx 10rpx;
  background-color: #fff;
  position: absolute;
  top: 160rpx;
  transform:translateY(-100%);
  transition-property: transform;
  transition-duration: 300ms;
}
.filter_text{
	font-size: 28upx;
}
.show {
	transform:translateY(0)
}
.init{
  transform:translateY(-800%);
  }
.filter-list {
    flex-direction: row;
    justify-content: flex-start;
	align-items: center;
    flex-wrap: wrap;
  }
.filter-item {
    margin: 15rpx;
  }
.map-box {
  padding-top: 160rpx;
  position: absolute;
  top: 0;
  bottom: 0;
  transition-duration:300ms;
} 

.cate_box {
    width: 64rpx;
    position: absolute;
    right: 30rpx;
    bottom: 320rpx;
    border-radius: 32rpx;
    overflow: hidden;
    box-shadow: 0 5rpx 8rpx #ccc;
    background-color: #fff;
    
  }
.cate {
    width: 70rpx;
    padding: 5rpx 15rpx;
    margin: 3rpx;
    line-height: 1.1;
    font-size: 28rpx;
    border-radius: 29rpx;
    align-items: center;
    justify-content: space-around;
    color: #666;
}
// &&.cate
.cover-active{
    color: #fff;
    background-color: #ff6834;
}
	  // &&.cate
.active {
  // color: #fff;
  background-image: linear-gradient( #ff6834, #ff8e09);
}
.cate_text{
  // flex: 1;
  line-height: 18px;
}
.school-box {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: #fff;
  
}
.school_tip {
    flex-direction: row;
    justify-content: flex-start;
    margin-top: 5px;
    font-size: 24rpx; 
}
.label {
    color: $uni-color-primary;
    font-size: 12px;
}
 .tip {
      margin-left: 10rpx;
    }
.up_vip{
  width: 508rpx;
  height: 72rpx;
  line-height: 72rpx;
  background-color: #ff6735;
  color: #fff;
  border-radius: 50rpx;
  // margin: 60rpx auto;
  font-size: 32rpx;
  background-image: linear-gradient(to left, #fb8a65, #ff6735);
}
// #ifndef H5

.cate{
  // display: block;
    // white-space: pre-wrap;

}
// .e-text{
//   display: inline;
// }

.item-box {
  flex-direction: row;
  overflow: hidden;
  padding: 30rpx;
  
  
 
}
// &.item-box
.item-hover {
    background-color: $uni-bg-color-hover;
  }
  // 结束
.image-box {
    width: 240rpx;
    height: 220rpx;
    border-radius: 10rpx;
    overflow: hidden;
  }
  .image{
	width: 240rpx;
	height: 220rpx;  
  }
 .info-box {
    margin-left: 32rpx;
    flex: 2;
    overflow: hidden;
  }
.content_c {
      flex-direction: row;
      align-items: center;
	  // padding: 10px 0;
      background-color: #FFFFFF;
     
    }
 .infos {
        flex: 1;
        // margin-right: 20rpx;
		// margin-top: 5rpx;
		background-color: #ffffff;
      }
.title {
	  flex: 1;
      flex-direction: row;
      align-items: center;
	  margin-bottom: 10px;
	  height: 35px;
	  // position: relative;
      
    }	
.text-box {
        // flex-direction: row;
        // align-items: flex-end; 
		
        // margin-bottom: 20rpx;
		justify-content: flex-start;
        
        
      }
.text {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;  
        }
		// &.text
.line-clamp_2 {
            // -webkit-line-clamp: 2;
          }
		  // jieshu 
.text2 {
          margin-bottom: 10rpx;
          margin-left: 10rpx;
          font-size: $uni-font-size-sm;
          color: #999;
        }
 .tags {
	 justify-content: flex-start;
	 align-items: center;
	 flex-direction: row;
     margin-bottom: 5px;
    }
.tag {
        border-radius: 4rpx;
		margin-right: 8px;
		padding: 4px 8px;
		font-size: $uni-font-size-base;
		color: $uni-color-primary;
		background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
      }
	  // .infos_text{
		 // margin-right: 8px; 
		 // padding: 8px 10px;
		 // line-height: 1;
		 // font-size: $uni-font-size-base;
		 // color: $uni-color-primary;
		 // background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
	  // }
.desc {
      margin-bottom: 24rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      // -webkit-line-clamp: 1;
      // -webkit-box-orient: vertical;
      // display: -webkit-box;
      font-size: $uni-font-size-base;
      color: #666;
    }
.address_distance{
	flex-direction: row;
	align-items: center;
	flex: 1;
	// margin-top:3px;
}
.address {
      flex-direction: row;
	  flex: 3;
      // align-items: center;
      // overflow: hidden;
      font-size: $uni-font-size-base;
      color: #666;
      // lines: 1;
      // text-overflow: ellipsis;
	 
    }
.address_text{
	lines: 1;
	font-size: 12px;
	text-overflow: ellipsis;
}
.t_text{
		font-size: 18px;
	}
.distance {
	flex: 1;
	flex-direction: row;
    margin-left: 5rpx;
	 color: #666;
}
.distance_text{
	lines: 1;
	 color: #666;
	font-size: 12px;
	text-overflow: ellipsis;
}
// cover-view

//cover-view 结束
.my-tag {
  // display: inline-block;
  padding: 10px 20px;
  font-size: $uni-font-size-sm;
  line-height: 1.3;
  border-radius: 4rpx;
  text-align: center;
  align-items: center;
  color: $uni-color-primary;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
}
// &&my-tag
 .success {
    color: $uni-color-success;
    background-color: rgba($color: $uni-color-success, $alpha: 0.1);
  }
.info {
    color: $uni-text-color-grey;
    background-color: rgba($color: $uni-text-color-grey, $alpha: 0.1);
  }
 .danger {
    color: $uni-color-error;
    background-color: rgba($color: $uni-color-error, $alpha: 0.1);
  }
.small {
    border-radius: 3rpx;
    padding: 6rpx 14rpx;
    font-size: 22rpx;
  }
.big {
    padding: 10rpx 20rpx;
    border-radius: 8rpx;
    font-size: $uni-font-size-base;
  }
  //&my-tag
// #endif
.iconfont {
    font-family: iconfont;
  }
  .back{
	  color: #666;
	  font-size: 27px;
  }
  .icon_sousuo{
	  color: #666;
	  font-size: 20px;
	  margin: 0 5px;
  }
  .icon_down{
	  font-size: 8px;
	  color:#d8d8d8;
	  margin-left: 2px;
  }
  .icon_up{
  	  font-size: 8px;
  	  color: #F76260;
	  margin-left: 2px;
  }
  .icon_dibiao{
	 font-size: 12px;
	 color: #f00;
  }
  .mask{
	  position: absolute;
	  left: 0px;
	  right: 0px;
	  top: 0px;
	  bottom:0;
	  background-color: rgba(0,0,0,0.3);
  }
  
</style>
