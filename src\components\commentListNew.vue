<template>
	<view class="lists">
		<view class="common-item bottom-line" v-for="(item,index) in listData" :key="index" @click="toDetail(item.id,index)">

			<view class="user flex-box">
				<image class="user-img"  :src="(item.prelogo||item.img) | imgUrl('w_80')"></image>
                <view class="user-info">
                    <view class="user-name"><text>{{item.nickname}}</text><image v-if="item.leader_path" class="level-icon" :src="item.leader_path | imgUrl('w_80')" mode="widthFix"></image></view>
                    <view class="user-comment">{{item.content}}</view>
                    <view class="comment_time flex-box">
                            <view class="time">{{ item.time }}</view>
                            <view class="options-box flex-row">
                                <view class="comment_num" @click.prevent.stop="handleAction(index)">
                                    <my-icon type="ic_xiaoxi" size="30rpx" color="#d8d8d8"></my-icon>
                                    <text>{{ item.reply_count||0 }}</text>
                                </view>
                                <view class="praise_num" @click.stop.prevent="handlePraise(item.id,index)">
                                    <my-icon type="ic_zan" size="30rpx" :color="item.is_praise?'#ff656b':'#d8d8d8'"></my-icon>
                                    <text>{{ item.praise_count || 0 }}</text>
                                </view>
                            </view>
                        </view>

			<!-- <view class="common-text">
				{{item.content}}
			</view> -->
<!-- 			<view class="handle handle-bar" v-if="answer">
				<button type="primary" class="small plain" @click.stop.prevent="adoption(item.id)">采纳</button>
			</view> -->
            
			<view class="reply-list" v-if="item.reply&&item.reply.length>0">
				<view class="reply-item" v-for="(reply_item,idx) in item.reply" :key="idx">
                    <view class="name_info flex-row">
                            <image class="reply_user_header_img" :src="reply_item.prelogo | imageFilter('w_80')"></image>
                            <text class="prely_user_name">{{ reply_item.cname }}</text>
                            <text class="identity" v-if="reply_item.IsAdv">置业顾问</text>
                            <text>回复：</text>
                        </view>
                        <text class="reply_content">{{ reply_item.content }}</text>
                    </view>
                </view>
            </view>
				
				
				

			</view>
		</view>
		<view v-if="showMore" class="look-more" @click="moreComment">查看更多</view>
	</view>
</view>
	</view>
</template>

<script>
	import {formatImg,navigateTo} from "../common/index.js"
	import myIcon from "../components/myIcon.vue"
	export default {
		props:{
			listData: Array,
			type:{
				type:[Number,String],
				default:1
			},
			showMore:{
				type:[String,Boolean],
				default:true
			},
			answer:{
				type:Boolean,
				default:false
			},
			money:{
				type:[String,Number],
				default:0
			},
			isleader:{
				type:[String,Number],
				default:0
			},
			parentId:[String,Number],
			adoptId:[String,Number],
			banReply:{
				type:Boolean,
				default:false
			}
		},
		components: {
			myIcon
		},
		data() {
			return {
				id:""
			}
		},
		filters:{
			imgUrl(img, param=""){
				if(!img){
					return ""
				}
				return formatImg(img, param)
			}
		},
		methods: {
			moreComment(){
				navigateTo('/pages/comment_list/comment_list?id='+this.parentId+'&type='+this.type)
			},
			handleReply(parentIndex,index){
				if(this.longTap||this.banReply){
					return
				}
				if(index!==undefined){
					// return
					let be_reply = this.listData[parentIndex].reply[index].nickname
					let be_commentid = this.listData[parentIndex].reply[index].id
					this.$emit('clickReply',{parentIndex,index,be_reply,be_commentid})
				}else{
					let be_reply = this.listData[parentIndex].nickname
					let be_commentid = this.listData[parentIndex].id
					this.$emit('clickReply',{parentIndex,be_reply,be_commentid})
				}
			},
			handleAction(parentIndex,index){
				// 防止出发点击事件
				this.longTap = true
				setTimeout(()=>{
					this.longTap = false
				},500)
				let isMy = false
				let comment_content
				let actionArr
                let nica
                console.log(index);
                console.log(this.banReply);
				if(index==undefined&&!this.banReply){
					nica = 0
					comment_content = this.listData[parentIndex].content
					if(this.listData[parentIndex].is_my == 1){
						isMy = true
					}
					actionArr = ['回复', '复制', isMy||this.isleader==7?'删除':'举报']
					if(this.isleader==7){
						actionArr.push('禁言')
					}
				}
				if(index==undefined&&this.banReply){
					nica = 1
					comment_content = this.listData[parentIndex].content
					if(this.listData[parentIndex].is_my == 1){
						isMy = true
					}
					actionArr = [ '复制', isMy||this.isleader==7?'删除':'举报']
					if(this.isleader==7){
						actionArr.push('禁言')
					}
				}
				if(index!=undefined){
					nica = 0
					comment_content = this.listData[parentIndex].reply[index].content
					if(this.listData[parentIndex].reply[index].is_my == 1){
						isMy = true
					}
					actionArr = ['回复','复制', isMy||this.isleader==7?'删除':'举报']
					if(this.isleader==7){
						actionArr.push('禁言')
					}
				}
				uni.showActionSheet({
					itemList: actionArr,
					success: res=> {
						switch(res.tapIndex){
							case 0-nica:
								this.handleReply(parentIndex,index)
								break
							case 1-nica:
								// #ifndef H5
								uni.setClipboardData({
									data:comment_content,
									success:res=>{
										uni.showToast({
											title:"复制成功",
											icon:"none"
										})
									}
								})
								// #endif
								// #ifdef H5
								let oInput = document.createElement('input');
								oInput.value = comment_content;
								document.body.appendChild(oInput);
								oInput.select(); // 选择对象;
								document.execCommand("Copy"); // 执行浏览器复制命令
								uni.showToast({
									title:"复制成功",
									icon:"none"
								})
								oInput.remove()
								// #endif
								break
							case 2-nica:
								if(isMy||this.isleader==7){
									console.log("删除")
									this.$emit('delComment',{parentIndex,index})
								}else{
									console.log("举报")
									let id;
									if(index!==undefined){
										id = this.listData[parentIndex].reply[index].id
									}else{
										id = this.listData[parentIndex].id
									}
									this.toJubao(id)
								}
								break
							case 3-nica:
								let uid
								if(index!==undefined){
									uid = this.listData[parentIndex].reply[index].uid
								}else{
									uid = this.listData[parentIndex].uid
								}
								console.log("uid:"+uid,"执行禁言")
								this.$ajax.get('news/forbiddenWords.html',{uid:uid},res=>{
									uni.showToast({
										title:res.data.msg,
										icon:'none'
									})
								})
						}
					},
					fail: function (res) {
						console.log(res.errMsg);
					}
				});
			},
			toJubao(id){
				navigateTo('/user/inform/inform?id='+id+'&type=4')
			},
			adoption(id){
				this.$emit('adoption',{id})
			},
			toDetail(id,index){
				this.$emit('toDetail',{id,index})
			},
            handlePraise(id,index){
                this.$emit('praise',{id,index})
            }
		}
	}
</script>

<style lang="scss">
	.lists{
		background-color: #fff;
	}
	.common-item{
		padding: $uni-spacing-row-base 0;
	}
	.common-item .user{
		// margin-bottom: 16upx;
		// align-items: center;
	}
	.common-item .user-img{
		width: 48upx;
		height: 48upx;
		border-radius: 50%;
		margin-right: $uni-spacing-row-base;
	}
	.common-item .level-icon{
		width: 32upx;
		height: 32upx;
		margin-right: 40upx;
		object-fit: contain;
		vertical-align: text-bottom;
	}
	.common-item .user-name{
        margin-bottom: 8upx;
		font-size: 22upx;
		color: #999;
	}
    .common-item .user-comment{
        margin-bottom: 18upx;
    }
    
	.common-item .time{
		flex: 1;
		font-size: 22upx;
		color: #999;
	}
	.common-item .common-text{
		padding-left: 80upx;
		word-break: break-all;
	}
	.common-item .reply-list{
		// padding-left: 100upx;
		width: 100%;
		box-sizing: border-box;
	}
	.common-item .reply-item{
		// margin-top: 10upx;
		// padding: 20upx;
		font-size: 24upx;
		background-color: #f3f3f3
	}
	.common-item .reply-item-name{
		color: #36648B
	}
	.grey{
		color: #999
	}
	.look-more{
		padding: $uni-spacing-row-base;
		text-align: center;
		font-size: $uni-font-size-base;
		color: #666;
		border-radius:10upx;
	}
	.handle-bar{
		width: 100%;
		display: flex;
		justify-content: flex-end;
	}
	.handle-bar .handle button.plain{
		font-size: 24upx;
		background-color: $uni-color-primary;
		color: $uni-color-primary;
	}
	.icon-box{
		width: 90upx;
		height: 90upx;
		position: absolute;
		top: 0;
		right: 0;
	}
	.icon-box image{
		height: 100%;
		width: 100%;
	}
	.xs_money{
		margin-right: 60upx;
		float: right;
		color: #f65354
	}
	.xs_status{
		position: absolute;
		right: 20upx;
		top: 20upx;
		height: 0;
		width: 0;
		border-left: 70upx solid transparent;
	}
	.xs_status.green{
		border-top: 70upx solid #66cc66;
	}
	.xs_status.gray{
		border-top: 70upx solid #999;
	}
	.xs_status_text{
		position: absolute;
		right: 15upx;
		top: 35upx;
		font-size: 18upx;
		letter-spacing:6upx;
		transform:rotate(45deg);
		color: #ffffff;
	}
    .flex-row {
        flex-direction: row;
        display: flex;
    }
    .user-info{
        flex: 1;
    }
     .comment_time {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                font-size: 22rpx;
                color: #999;
            }
            .options-box {
                align-items: center;
                font-size: 22rpx;
                color: #d8d8d8;
                margin-left: auto;
                text {
                    margin-left: 10rpx;
                }
                .comment_num {
                    flex-direction: row;
                    align-items: center;
                }
                .praise_num {
                    flex-direction: row;
                    align-items: center;
                    margin-left: 26rpx;
                }
            }
            .reply-list {
            margin-top: 16rpx;
            // padding: 12rpx 16rpx;
            background-color: #f2f2f2;
        }
        .reply-item {
            font-size: 22rpx;
            padding: 8rpx;
            padding-left: 50rpx;
            align-items: flex-start;
            display: block;
			line-height: 1.8;
            .name_info{
                align-items: center;
                display: inline-block;
                font-size: 22rpx;
            }
            .reply_user_header_img {
                width: 40rpx;
                height: 40rpx;
                margin-left: -50rpx;
                position: relative;
                top: 8rpx;
                border-radius: 50%;
                margin-right: 10rpx;
            }
            .identity{
                font-size: 22rpx;
                line-height: 1;
                padding: 4rpx 8rpx;
                margin: 0 10rpx;
                color: $uni-color-primary;
                background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
            }
            .reply_content{
                flex: 1;
            }
        }
</style>
