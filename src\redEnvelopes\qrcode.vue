<template>
  <view>
    <view class="ling-popup">
        <view class="ling-popup-top">
          <view class="ling-top-info">
            <image v-if="hb_info.info_pic" class="ling-info-icon" :src="hb_info.info_pic" mode="aspectFill"></image>
            <text class="ling-info-text">{{hb_info.info_title}}</text>
          </view>
          <!-- <view class="ling-top-text">扫码领红包</view> -->
          <view class="ling-top-text1">塞了{{hb_info.limit_hb_count}}份红包</view> 
          <image class="hb-code" :src="code" mode="aspectFill"></image>
          <view class="ling-top-tips1">长按识别二维码</view>
        </view>
         <view class="ling-popup-btm">
          <!-- <view class="ling-btn" >参与瓜分<text class = "orange">￥1001</text>元现金红包</view> -->
          <!-- 没有现金红包 -->
          <view class="ling-btn" ><text class = "orange">拼手气领红包</text></view>
        </view> 
        <!-- <view class="ling-popup-bottom">
          <view class="ling-btn" @click="lingHb">開</view>
        </view> -->
        <view class="ling-popup-close" @click="closeCode()">
          <my-icon type="guanbi" color="#e0604c" size="56rpx"></my-icon>
        </view>
      </view>
  </view>
</template>

<script>
import myIcon from '../components/myIcon.vue'
export default {
  components: { myIcon },
  data() {
    return {
      id: '',
      type: '',
      // info_pic: '',
      // info_title: '',
      code: '',
      info_id:'',
      hb_info_id:'',
      hb_info:{}
    }
  },
  onLoad(options) {
    this.id=options.id  
    this.type=options.type
    this.info_id =options.info_id
    this.hb_info_id =options.hb_info_id
    this.getData()
  },
  methods: {
    getData() { 
      // id 为列表页的info_id info_id 为列表页的id
      this.$ajax.get('wx_money/pingHbQrcode',{id: this.info_id, type: this.type,hb_info_id: this.hb_info_id}, (res) => {
        if (res.data.code == 1) {
          this.hb_info = res.data.hb_info
          // this.info_pic = res.data.info_pic
          // this.info_title = res.data.info_title
          // this.limit_hb_count = res.data.
          this.code = res.data.url
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    lingHb() {
      uni.showToast({
        title: '扫码领红包',
        icon: 'none'
      })
    },
    closeCode() {
      this.$navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.ling-popup {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 584rpx;
  height: 964rpx;
  border-radius: 20rpx;
  .ling-popup-top {
    position: relative;
    width: 100%;
    height: 782rpx;
    overflow: hidden;
    z-index: 2;
    &::after {
      position: absolute;
      width: 140%;
      height: 782rpx;
      left: -20%;
      top: 0;
      z-index: -1;
      content: '';
      border-radius: 0 0 50% 50%;
      background: #E16754;
      box-shadow: 0px 4rpx 8rpx 0px #00000026;
    }
    .ling-top-info {
      position: absolute;
      top: 50rpx;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      .ling-info-icon {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
      }
      .ling-info-text {
        font-size: 30rpx;
        font-weight: bold;
        color: #E5CD9F;
      }
      .ling-info-tips {
        font-size: 30rpx;
        color: #E5CD9F;
      }
    }
    .ling-top-title {
      position: absolute;
      top: 80rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 30rpx;
      color: #E5CD9F;
      width: 90%;
      text-align: center;
      overflow: hidden;    
      text-overflow:ellipsis;    
      white-space: nowrap;
    }
    .ling-top-text {
      position: absolute;
      top: 220rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 44rpx;
      color: #E5CD9F;
    }
    .ling-top-text1 {
      position: absolute;
      top: 220rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 30rpx;
      color: #E5CD9F;
    }
    .hb-code {
      position: absolute;
      top: 312rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 320rpx;
      height: 320rpx;
    }
    .ling-top-tips {
      position: absolute;
      top: 700rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 30rpx;
      color: #E5CD9F;
      width: 100%;
      text-align: center;
    }
    .ling-top-tips1 {
      position: absolute;
      top: 660rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 30rpx;
      color: #E5CD9F;
      width: 100%;
      text-align: center;
    }
    
  }
  .ling-popup-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 300rpx;
    background: #E0604C;
    .ling-btn {
      position: absolute;
      bottom: 100rpx;
      left: 50%;
      transform: translate(-50%, 0) rotateY(0deg);
      width: 176rpx;
      height: 176rpx;
      border-radius: 50%;
      line-height: 176rpx;
      text-align: center;
      background: #E6CD9F;
      box-shadow: 0px 4rpx 8rpx 0px #0000003F;
      color: #454545;
      font-size: 52rpx;
      z-index: 9;
    }
  }
  .ling-popup-btm{
    margin-top: 40rpx;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 300rpx;
    background: #E0604C;
    .ling-btn {
      position: absolute;
      bottom: 174rpx;
      left: 50%;
      transform: translate(-50%, 0) rotateY(0deg);
      width: 100%;
      text-align: center;
      font-size: 24rpx;
      color:  #fff;
      z-index: 9;
      .orange{
        color: #E5CD9F;
        font-size: 32rpx;
        font-weight: 600;
      }
    }
  }
  .ling-popup-close {
    position: absolute;
    bottom: -104rpx;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>