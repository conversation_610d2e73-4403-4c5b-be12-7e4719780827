<template>
  <view class="qrcode-box">
        
			
				<view class="img-box">
					<image  class="qrcode" :src="detail.pic | imageFilter" mode="aspectFill"></image>
					<view>
						<view class="title">{{detail.award_name ||''}}</view>
						<view class="tip">凭核验码联系现场工作人员兑奖</view>
					</view>
          <view class="line">

          </view>
          <view class="title title_info">
            兑奖券详情
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              兑奖期限
            </view>
             <view class="duihuan_info_value">
              {{detail.write_off_stime ||''}}- {{detail.write_off_etime||''}}
            </view>
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              用户名称
            </view>
             <view class="duihuan_info_value">
             {{detail.cname ||''}}
            </view>
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              获奖时间
            </view>
             <view class="duihuan_info_value">
             {{detail.ctime ||''}}
            </view>
          </view>
          <view class="title title_info">
            活动详情
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
             活动名称
            </view>
             <view class="duihuan_info_value">
              {{detail.write_off_stime ||''}}- {{detail.write_off_etime||''}}
            </view>
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              活动开始时间
            </view>
             <view class="duihuan_info_value">
             {{detail.activity_start_time ||''}}
            </view>
          </view>
          <view class="duihuan_info flex-row ">
            <view class="duihuan_info_name">
              活动结束时间
            </view>
             <view class="duihuan_info_value">
             {{detail.activity_end_time ||''}}
            </view>
          </view>
				</view>
			
			<view class="btns flex-row items-center" v-if ="this.detail.id">
        <view class="btn"  @click ="confirm">确认核销</view>
      </view>
			</view>
</template>

<script>
export default {
 data(){
   return  {
     detail:{},

   }
 },
 onLoad(options){
   if(options.id){
     this.id = options.id
   }
   this.getDetail()
 }, 
 methods:{
    getDetail(){
      this.$ajax.get("WordCollecting/showWriteOffByAdmin",{id:this.id},res=>{
        if (res.data.code ==1){
          this.detail =res.data.write_off
        }else {
          
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      })
    },
    confirm(){
      uni.showModal({
        title: '确认核销吗',
        // content: '这是一个模态弹窗',
        success: (res) =>{
          if (res.confirm) {
            this.$ajax.get("WordCollecting/confirmWriteOff",{id:this.id},res=>{
                uni.showToast({
                    title:res.data.msg,
                    icon:"none"
                  })
                if (res.data.code ==1){
                  console.log(res);
                  this.$navigateTo("/pages/index/index")
                }

              })
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });
      
    }

 }
}
</script>

<style lang="scss" scoped>
.flex-row{
  display: flex;
  flex-direction: row;
}
.items-center {
  align-items: center;
}
.j-center{
  justify-content: center;
}
.flex-1 {
  flex: 1;
}
.qrcode-box{
	position: relative;
  
	.img-box{
		padding: 12rpx;
    padding-top: 48rpx;
		margin: auto;
    position: relative;
		background-color: #fff;
		border-radius: 15rpx;
		overflow: hidden;
		.title{
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
      color: #7F502C;
      &.title_info{
        text-align: left;
        margin-top: 48rpx;
        padding: 0 48rpx;
      }
		}
    .duihuan_info{
      margin: 24rpx 0;
      padding: 0 48rpx;
      font-size: 22rpx;
      color: #7F502C;
      .duihuan_info_name{
        white-space: nowrap;
        margin-right: 20rpx;
      }
      
    }
    .empty{
      height: 300rpx;
      width: 100vw;
     
    }
    
		.tip{
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			font-size: 28rpx;
      color: #7F502C;
		}
    .line {
      width: calc(100vw - 48rpx);
      height:2rpx;
      margin: 0 auto;
      background: #D8D8D8;
    }
	}
	.qrcode{
		width: 320rpx;
    margin: 0 auto;
		height: 320rpx;
    display: block;
	}
	.icon-box{
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
  
  
  
}
.btns {
  justify-content: center;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 10rpx;
  .btn {
    padding: 20rpx 60rpx;
    background: #2fa1e2;
    color: #fff;
    font-size: 28rpx;
    border-radius: 40rpx;
    text-align: center;
  }
}
</style>