<template>
  <view>
    <view class="prize_info">
      <image class="pic" :src="prize_info.pic | imageFilter"></image>
      <view class="info">
        <view class="name">{{prize_info.name}}</view>
        <view class="activity_name">活动：{{prize_info.activity_name}}</view>
      </view>
      <view class="btn" :class="{disable: is_binding}" @click="handleBind">{{is_binding?'已绑定':'绑定'}}</view>
    </view>
    <view class="tip">
      提醒：授权绑定后，您的账号可通过微信扫一扫客户出示的兑奖码，完成已兑奖核验流程。
    </view>
  </view>
</template>

<script>
export default {
  name: '',
  components: {},
  data () {
    return {
      prize_info: {

      },
      is_binding: 0
    }
  },
  onLoad(options){
    if(options.id){
      this.id = options.id
      this.getData(this.id)
    }
  },
  methods: {
    getData(id){
      this.$ajax.get('activity/prizeInfo.html', {id}, res=>{
        if(res.data.code === 1){
          this.is_binding = res.data.is_binding
          if(this.is_binding){
            this.showTip()
          }
          this.prize_info = res.data.prize
        }else{
          uni.showToast({
            title:res.dat.msg,
            icon:'none'
          })
        }
      })
    },
    showTip(){
      uni.showToast({
        title: '您已经绑定此奖品',
        icon: 'none'
      })
    },
    handleBind(){
      if(this.is_binding){
        this.showTip()
        return
      }
      this.$ajax.get('activity/bindingManager.html', {id: this.id}, res=>{
        if(res.data.code === 1){
          this.is_binding = 1
          uni.showToast({
            title :res.data.msg
          })
          setTimeout(()=>{
            this.$navigateTo('/lucky/prize_detail?type=list')
          }, 1500)
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.prize_info{
  margin: 24rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  .pic{
    width: 108rpx;
    height: 108rpx;
    margin-right: 20rpx;
    border-radius: 8rpx;
    background-color: #f8f8f8;
  }
  .info{
    flex: 1;
  }
  .name{
    flex: 1;
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }
  .activity_name{
    font-size: 24rpx;
    color: #e22f1a;
  }
  .btn{
    margin-left: 24rpx;
    width: 176rpx;
    text-align: center;
    height: 64rpx;
    line-height: 64rpx;
    background-image: linear-gradient(180deg, #FF8163 0%, #E22F1A 100%);
    box-shadow: 0 8rpx 16rpx 0 rgba(240,86,61,0.40);
    border-radius: 44rpx;
    color: #fff;
    &.disable{
      background-image: none;
      background-color: #d8d8d8;
      color: #999;
      box-shadow: none
    }
  }
}
.tip{
  margin: 24rpx;
  padding: 24rpx;
  border-radius: 8rpx;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.15);
  color: $uni-color-primary;
  border-left: 6rpx solid $uni-color-primary;
}
</style>