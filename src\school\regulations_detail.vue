<template>
  <view class="rel_detail">
    <view class="title">{{ detail.title }}</view>
    <view class="title_below">
      <view class="left">
        <image class="auth_logo" v-if="school.pic" :src="school.pic" mode="aspectFill"></image>
        <view>
          <view class="auth">{{ school.name }}</view>
          <view class="time">{{ detail.ctime }}</view>
        </view>
      </view>
    </view>
    <!-- #ifdef H5 -->
    <view class="article-content" v-html="detail.content"></view>
    <!-- #endif -->
    <!-- #ifndef H5 -->
    <view class="article-content">
      <u-parse :html="detail.content" @linkpress="navigate" :tag-style="tagStyle"></u-parse>
    </view>
    <!-- #endif -->
    <view class="view_count" v-if="detail.click_count">
      <myIcon type="yanjing" color="#999"></myIcon>
      <text class="count">{{ detail.click_count }}已浏览</text>
    </view>
    <view class="tip-box" v-if="detail.id">
      <view>免责声明</view>
      <text
        >本平台对分享的内容、陈述、观点判断保持中立,不对所包含内容的准确性、可靠性或完善性提供任何明示或暗示的保证,仅供读者参考,本平台将不承担任何责任。</text
      >
    </view>
    <no-data v-if="nodata" tip="该学校还没有发布招生简章"></no-data>
    <view class="fixed-bottom">
      <footer-nav
        @makePhoneCall="handleMakePhoneCall()"
        openType="share"
        tel
      ></footer-nav>
    </view>
  </view>
</template>

<script>
// #ifndef H5
import uParse from '../components/Parser/index'
// #endif
import noData from '../components/noData'
import wxApi from '../common/mixin/wx_api';
import footerNav from './components/footerNav'
export default {
  components: {
    // #ifndef H5
    uParse,
    // #endif
    noData,
    footerNav
  },
  mixins:[wxApi],
  data() {
    return {
      detail: {
        title: '',
        content: ''
      },
      school: {},
      nodata: false
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id
      this.getData()
    }
  },
  methods: {
    getData() {
      this.$ajax.get('school/recruitDetail.html', { id: this.id }, res => {
        if (res.data.code === 1) {
          this.detail = res.data.data.recruit
          this.school = res.data.data.school
          // console.log( this.detail);
          
          if (!this.detail.content) {
            this.nodata = true
          }
        } else {
          this.nodata = true
        }
        if(res.data.share){
          this.share = res.data.share
          this.getWxConfig()
        }
      })
    },
    handleMakePhoneCall() {
      if(!this.school.tel){
        uni.showToast({
          title:'学校电话待更新',
          icon:'none'
        })
        return
      }
      uni.makePhoneCall({
        phoneNumber: this.school.tel,
        success: () => {
          console.log('触发拨打电话')
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.rel_detail{
  padding-bottom: 130rpx;
}
view{
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.title {
  padding: 30rpx 24rpx;
  line-height: 1.5;
  // text-align: center;
  font-size: 40rpx;
  font-weight: bold;
}

.title_below {
  flex-direction: row;
  margin: 10rpx 30rpx 30rpx 30rpx;
  padding-bottom: 40rpx;
  margin-bottom: 20rpx;
  font-size: $uni-font-size-sm;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #dedede;
  .left {
    flex-direction: row;
    align-items: center;
    .auth_logo {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    .auth {
      display: inline-block;
      // max-width: 260rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 12rpx;
      font-size: 30rpx;
      // color: #4db0fd;
    }
    .time {
      color: #888;
    }
  }
}

.article-content {
  padding: 24rpx 40rpx;
  line-height: 1.6;
  font-size: 32rpx;
  font-family: -apple-system-font, BlinkMacSystemFont, 'Helvetica Neue',
    'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei',
    Arial, sans-serif;
  letter-spacing: 0.544px;
  white-space: normal;
  p {
    min-height: 1em;
    margin-bottom: 30rpx;
  }
  img {
    max-width: 100%;
  }
}
.view_count {
  margin: 30rpx;
  flex-direction: row;
  align-items: center;
  .count {
    margin-left: 5rpx;
    color: #999999;
  }
}

.tip-box {
  margin: 30rpx;
  // flex-direction: row;
  padding: 24rpx;
  line-height: 1.5;
  view {
    font-size: 30rpx;
    margin-bottom: 10rpx;
  }
  text {
    color: #888;
  }
}

.banner-box {
  padding: 0 10rpx;
}
.fixed-bottom {
  width: 100%;
  padding: 10rpx 30rpx;
  position: fixed;
  bottom: 0;
}
</style>
