<template>
  <view
    class="house flex-row"
    @click="$emit('click', { type: type, detail: itemData })"
  >
    <view class="img-box">
      <view class="level-box">
        <text class="level level2" v-if="itemData.info_level === 2">精选</text>
      </view>
      <image
        class="img"
        :src="itemData.img | imgUrl"
        lazy-load
        mode="aspectFill"
      ></image>
      <image
        v-if="itemData.is_vr || itemData.vr"
        class="video-icon"
        src="/static/icon/vr.png"
      ></image>
      <image
        v-else-if="itemData.is_video == 1"
        class="video-icon"
        src="/static/icon/video.png"
      ></image>
    </view>
    <view class="info">
      <view class="title flex-row">
        <text class="title_con"
          :class="{
            red: itemData.ifred,
            bold: itemData.ifbold,
          }"
          >{{ itemData.title }}</text
        >
         <template v-if ="index>=0&&index<10">
            <view class ="idx" :class="index<=2?('idx'+(index+1)):''">{{index+1}}</view>
        </template>
      </view>
      <!-- 二手房和出租房 -->
      <view class="bottom-info flex-box">
        <view class="bottom-left flex-row">
          <template v-if="type === 'ershou'">
              <text class="average_price"
                >{{
                  itemData.avg_price
                }}元/m²</text
              >
              <text class ="type">{{itemData.areaname}}</text>
            <!-- <text>{{ itemData.shi }}室{{ itemData.ting }}厅{{ itemData.wei }}卫</text>
            <text class="mj">{{ itemData.mianji }}㎡</text> -->
          </template>
          <template v-if="type === 'renting'">
            <text
              class="mianyi"
              v-if="
                itemData.zujin == '面议' ||
                itemData.zujin == '0' ||
                !itemData.zujin
              "
              >面议</text
            >
            <text class="price" v-else>{{ itemData.zujin }}</text>
            <text
              class="price-unit"
              v-if="
                itemData.zujin !== '面议' &&
                itemData.zujin != '0' &&
                itemData.zujin
              "
              >元/月</text
            >
            <text class ="type">{{itemData.areaname}}</text>
            <!-- <text>{{ itemData.shi }}室{{ itemData.ting }}厅{{ itemData.wei }}卫</text>
            <text class="mj">{{ itemData.mianji }}㎡</text> -->
          </template>
        </view>
        <view class="bottom-right" v-if="showTime">
          <text class="u-time">{{ itemData.begintime }}</text>
        </view>
      </view>
      <!-- <view class="type">{{
          itemData.community_name || itemData.areaname
        }}</view> -->
      <view class="center-info">
        <!-- <text :class="'attr' + itemData.zhongjie">{{ itemData.zhongjie == 2 ? "经纪人" : "个人" }}</text> -->
        <!-- <text class="huxing"
          >{{ itemData.shi }}室{{ itemData.ting }}厅{{ itemData.wei }}卫</text
        > -->
        <text class="jiange jiange-margin" v-if="itemData.mianji">|</text>
        <text class="mj" v-if="itemData.mianji">{{ itemData.mianji }}㎡</text>
        <text class="jiange" v-if="itemData.chaoxiang">|</text>
        <text class="cx" v-if="itemData.chaoxiang">{{
          itemData.chaoxiang
        }}</text>
        <text class="area address">{{ itemData.address || "" }}</text>
        
      </view>
      
      <view class="labels" v-if="itemData.label && itemData.label.length > 0">
        <text
          class="label"
          :style="{ color: label.color, borderColor: label.color }"
          v-for="(label, index) in itemData.label"
          :key="index"
          >{{ label.name }}</text
        >
      </view>
       <template v-if ="index>=0">
      <view class="paiming">
            <view class="paiming_box" :class="'paiming_box'+(index<=2?(index+1):3)">
                 <image :src="'/images/property/<EMAIL>'| imageFilter('m_240')" alt="" mode ="widthFix"></image>
                 <text>本月热门小区房价第{{mingci[index]}}名</text>
            </view>
      </view>
      </template>
      
      <!-- <view class="agent_info flex-row" v-if="itemData.levelid > 1">
        <image
          class="header_img"
          :src="itemData.prelogo | imageFilter('w_80')"
        ></image>
        <text class="c_name">{{ itemData.cname }}</text>
        <text class="b_name flex-1">{{ itemData.tname }}</text>
      </view> -->
    </view>
  </view>
</template>
<style scoped lang="scss">
.house {
  display: flex;
  padding: 24rpx 0;
  .img-box {
    width: 204rpx;
    height: 172rpx;
    margin-right: 16rpx;
    position: relative;
    border-radius: 8rpx;
    overflow: hidden;
    .img {
      width: 100%;
      height: 100%;
    }
    .level-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;
      .level {
        display: block;
        margin-bottom: 5rpx;
        padding: 2rpx 10rpx;
        font-size: 22rpx;
        border-bottom-left-radius: 20rpx;
        color: #fff;
        &.level1 {
          background: linear-gradient(132deg, #f7918f 0%, #fb656a 100%);
        }
        &.level2 {
          background: linear-gradient(135deg, #69d4bb 0%, #00caa7 100%);
        }
      }
    }
    .video-icon {
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      left: 20rpx;
      bottom: 20rpx;
    }
  }
  .info {
    flex: 1;
    overflow: hidden;
    .type{
      // margin-top: 10rpx;
      font-size: 26rpx;
      color: #999;
    }
    .paiming{
        margin-top: 24rpx;
        // color: #FB8968;
        .paiming_box{
            padding: 6rpx 26rpx;
            font-size: 22rpx;
            border-radius: 20rpx;
            display: inline-block;
            &.paiming_box1{
                background: rgba(255,58,58,0.15);
                color: #FB656A;
            }
            &.paiming_box2{
                background: rgba(255,120,26,0.21);
                color:#FB8968;
            }
            &.paiming_box3{
                background: rgba(251,195,101,0.20);
                color:#FBC365;
            }
            image{
                width: 20rpx;
                
            }
            text{
                margin-left: 6rpx;
            }
        }
    }
    .title {
      font-size: 32rpx;
      line-height: 1.5;
      margin-top: -6rpx;
      font-size: 34rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      .red {
        color: #fb656a;
      }
      .bold {
        font-weight: bold;
      }
      &.row1 {
        max-height: 90rpx;
        margin-bottom: 10rpx;
      }
      &.row2 {
        min-height: 90rpx;
      }
      .title_con{
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
      }
      .idx{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        background: #D8D8D8;
        border-radius: 20rpx;
        width:36rpx;
        min-width: 36rpx;
        height: 36rpx;
        font-size: 28rpx;
        color: #FFFFFF;
        letter-spacing: 0;
        text-align: center;
        &.idx1{
            background: #FB656A;
            box-shadow: 0 4rpx 8rpx 0 rgba(251,101,106,0.40);
        }
        &.idx2{
            background: #FB8968;
            box-shadow: 0 2px 4px 0 rgba(251,137,104,0.40);
        }
        &.idx3{
            background: #FBC365;
            box-shadow: 0 2px 4px 0 rgba(251,195,101,0.40);
        }
      }
    }
    .ding {
      display: inline-block;
      padding: 6rpx 10rpx;
      margin-right: 10rpx;
      line-height: 1;
      font-size: 22rpx;
      border-radius: 4rpx;
      background: linear-gradient(to right, #f7918f 0%, #fb656a 100%);
      color: #fff;
    }
    .center-info {
      display: flex;
      align-items: center;
      margin-top: 30rpx;
      font-size: 26rpx;

      .jiange {
        margin: 0 4rpx;
        color: #999;
        &.jiange-margin {
          margin: 0 12rpx;
        }
      }
      &.need {
        .price_box {
          margin-left: 48rpx;
        }
        .label {
          font-size: 22rpx;
          color: #999;
        }
        .area {
          font-size: 22rpx;
          color: #333;
        }
        .in_price {
          font-size: 22rpx;
          color: $uni-color-primary;
        }
      }
      .area {
        margin-left: 16rpx;
        color: #999;
      }
      .address{
        overflow: hidden; 
        margin-left: 0;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .type {
        overflow: hidden;
        white-space: nowrap;
        flex: 1;
        text-align: right;
        text-overflow: ellipsis;
        color: #333;
      }
      .cx {
        margin-right: 4rpx;
      }
      .mj {
        margin-right: 4rpx;
      }
    }

    .labels {
      margin-top: 16rpx;
      line-height: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .label {
        display: inline-block;
        line-height: 1;
        font-size: 22rpx;
        padding: 4rpx 8rpx;
        border: 1rpx solid #d8d8d8;
        color: #999;
        border-radius: 4rpx;
        ~ .label {
          margin-left: 16rpx;
        }
      }
    }
    .bottom-info {
      margin-top: 30rpx;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      flex-wrap: wrap;
      text {
        font-size: 26rpx;
      }
      .mianyi {
        font-size: 32rpx;
        font-weight: bold;
        margin-right: 10rpx;
        color: #fb656a;
      }
      .price {
        font-size: 34rpx;
        line-height: 1;
        font-weight: bold;
        color: #fb656a;
      }
      .price-unit {
        font-size: 26rpx;
        margin: 0 16rpx 0 8rpx;
      }
      .average_price {
        color: #999;
      }
      .bottom-left{
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .bottom-right {
        flex-shrink: 0;
      }
      .u-time {
        line-height: 1;
        position: relative;
        font-size: 22rpx;
        color: #999;
      }
    }
  }
  .agent_info {
    display: flex;
    margin-top: 16rpx;
    align-items: center;
    font-size: 22rpx;
    color: #999;
    .header_img {
      width: 36rpx;
      height: 36rpx;
      border-radius: 50%;
      background-color: #f5f5f5;
    }
    .c_name,
    .b_name {
      margin-left: 16rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
<script>
import { formatImg } from "../common/index.js";
export default {
  components: {},
  data () {
    return {
      mingci:['一','二','三','四','五','六','七','八','九','十']
    };
  },
  props: {
    itemData: Object,
    type: {
      type: String,
      default: "ershou",
    },
    titleRow: {
      type: [Number, String],
      default: 1,
    },
    index: {
      type: [Number],
      default: -1,
    },
    showTime: {
      type: Boolean,
      default: true
    }
  },
  filters: {
    imgUrl (val) {
      return formatImg(val, "w_240");
    },
  },
  methods: {},
};
</script>
