<template>
  <view class="page">
    <!-- 焦点图 -->
    <view class="focus-box">
      <swiper
        class="banner"
        :indicator-dots="false"
        :circular="true"
        :duration="300"
        indicator-active-color="#f65354"
        @change="swiperChange"
        :current="swiperCurrent"
      >
        <swiper-item v-for="item in focus" :key="item.id">
          <view v-if="item.type === 'vr'" class="swiper-item" @click="toVr(item)">
            <image :src="(item.cover || detail.vr_cover || img[0]) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/vr_b.png" mode="widthFix"></image>
          </view>
          <view v-if="item.type === 'video'" class="swiper-item" @click="preVideo()">
            <image :src="(item.cover || item.url) | imageFilter('w_6401')" mode="aspectFill"></image>
            <image class="video-icon" src="/static/icon/video.png" mode="widthFix"></image>
          </view>
          <view v-if="item.type === 'img'" class="swiper-item" @click="preImgs(item.url)">
            <image :src="item.url | imageFilter('w_6401')" mode="aspectFill"></image>
          </view>
        </swiper-item>
      </swiper>
      <view class="img-total" :class="{ style2: page_style === 2 }">共{{ focusLen }}张</view>
      <view class="cate-box" :class="{ style2: page_style === 2 }">
        <view class="cate-list flex-row" :class="{no_bg: swiper_type_count<2}">
          <view v-if="vr_num > 0" class="cate" @click="switchFocus('vr')" :class="cateActive == 'vr' ? 'active' : ''">VR</view>
          <view v-if="video_num > 0" class="cate" @click="switchFocus('video')" :class="cateActive == 'video' ? 'active' : ''">视频</view>
          <view v-if="img_num > 0" class="cate" @click="switchFocus('img')" :class="cateActive == 'img' ? 'active' : ''">图片</view>
        </view>
      </view>
    </view>
    <!-- 标题 -->
    <view class="container house_title">{{ detail.house_name }}</view>
    <!-- 户型信息 -->
    <view class="container huxing flex-box">
      <view class="item">
        <text class="value">{{ detail.total_price }}万</text>
        <text class="label">参考总价</text>
      </view>
      <view class="item flex-1">
        <text class="value">{{ detail.shi }}室{{ detail.ting }}厅{{ detail.wei }}卫</text>
        <text class="label">户型</text>
      </view>
      <view class="item">
        <text class="value">{{ detail.mianji }}m²</text>
        <text class="label">面积</text>
      </view>
    </view>
    <!-- 其他信息 -->
    <view class="container">
      <view class="other_info">
        <view class="item">
          <text class="label">单价:</text>
          <text class="value highlight">{{ detail.price }}元/m²</text>
        </view>
        <view class="item">
          <text class="label">单元:</text>
          <text class="value">{{ detail.unit }}</text>
        </view>
        <view class="item">
          <text class="label">楼层:</text>
          <text class="value">{{ detail.floor }}/{{detail.total_floor}}</text>
        </view>
        <view class="item">
          <text class="label">朝向:</text>
          <text class="value">{{ detail.chaoxiang }}</text>
        </view>
        <view class="item">
          <text class="label">装修:</text>
          <text class="value">{{ detail.zhuangxiu || "暂无" }}</text>
        </view>
        <view class="item">
          <text class="label">楼栋:</text>
          <text class="value">{{ detail.number }}</text>
        </view>
        <view class="item">
          <text class="label">首付:</text>
          <text class="value highlight" @click="toCalculator(detail.fangdai_info)">{{ detail.fangdai_info.proportion*10||''}}成 {{detail.fangdai_info.firstPay||''}}万 月供{{detail.fangdai_info.yuegong1}}元</text>
          <view class="btn highlight" v-if="is_open_im&&detail.build.open_adviser">
            <chatBtn :user_login_status="login_status" @ok="getSendMsg($event, 5)">
              <text>[咨询首付比例]</text>
            </chatBtn>
          </view>
          <view class="btn highlight" v-else @click="toSubForme(2)">
              <text>[调价通知我]</text>
            </view>
        </view>
        <view class="item">
          <text class="label">楼盘:</text>
          <text class="value" @click="toBuild">{{ detail.build.title }}</text>
        </view>
      </view>
    </view>
    <!-- 400电话 -->
    <view class="tel-box container" v-if="detail.build.use_middle_number" @click="handleTel()">
      <view class="tel_block flex-row">
        <view class="tel_left">
          <my-icon type="ic_tel" size="20rpx" color="#fff"></my-icon>
        </view>
        <view class="tel_right">
          <view class="text-box">
            <text class="tel">致电售楼处了解更多信息</text>
          </view>
          <view class="btn">咨询</view>
        </view>
      </view>
    </view>
    <!-- 楼盘信息 -->
    <view class="build_info container">
      <view class="label">
        <text>楼盘信息</text>
        <text class="more" @click="$navigateTo('/pages/new_house/info?id=' + bid)">楼盘详情</text>
      </view>
      <view class="info">
        <view class="build_title">{{ detail.build.title }}</view>
        <view class="info_list">
          <view class="item">
            <text class="label">类型</text>
            <text class="value">{{ detail.build.build_type }}</text>
          </view>
          <view class="item">
            <text class="label">建面</text>
            <text class="value">约{{detail.build.jzsize?(detail.build.jzsize||'').replace('m²','')+'m²':'不详'}}</text>
          </view>
          <view class="item">
            <text class="label">地址</text>
            <text class="value">{{ detail.build.address }}</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 置业顾问 -->
    <view class="adviser container">
      <view class="label">
        <text>置业顾问</text>
        <text class="more" @click="cusList()">更多</text>
      </view>
      <view class="advier-list">
        <view class="adviser-item flex-row" v-for="item in consuList" :key="item.id" @click="consuDetail(item.adviser_id)">
          <view class="header_img">
            <image mode="widthFix" :src="item.prelogo | imageFilter('w_120')"></image>
          </view>
          <view class="info">
            <view class="name flex-row">
              <text class="text">{{ item.cname || item.typename }}</text>
            </view>
            <view class="data">
              <text>{{ item.traffic_volume }}人咨询过他</text>
              <!-- <text v-if="service_show_zixunliang==1">{{ item.traffic_volume }}人咨询过他</text>
                <text v-else>{{item.build_names||item.introduce||''}}</text> -->
            </view>
          </view>
          <view class="adviser-right">
            <view class="btn-list flex-row">
              <view class="btn">
                <chat-btn
                  :user_login_status="login_status"
                  :user_id="item.mid || item.uid || item.id"
                  :identity_id="item.adviser_id || item.uid || item.id"
                  :from_type="3"
                  @ok="advAsk"
                >
                  <view class="icon-box">
                    <my-icon type="ic_zixun1" size="45rpx" color="#ff656c"></my-icon>
                  </view>
                </chat-btn>
              </view>
              <view class="btn">
                <tel-btn
                  :user_id="item.mid || item.uid || item.id"
                  :identity_id="item.adviser_id || item.uid || item.id"
                  :tel="item.tel"
                  @ok="handleTel($event, item)"
                >
                  <view class="icon-box">
                    <my-icon type="ic_dianhua1" size="45rpx" color="#ff656c"></my-icon>
                  </view>
                </tel-btn>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 热卖房源 -->
    <view class="container hot_sale" v-if="sale_house_count">
      <view class="label">
        <text>同户型房源（{{sale_house_count}}）</text>
        <text class="more pd-r-48" @click="$navigateTo(`/online/sale_list?build_id=${detail.build_id}&sand_id=${detail.sand_id}&huxing_id=${detail.huxing_id}`)">更多房源</text>
      </view>
      <view class="house_list_container">
        <view class="house_list">
          <SaleHouse :list="sale_house_list" />
        </view>
      </view>
    </view>
    <!-- 猜你喜欢 -->
    <view class="container build_box" v-if="find_build.length > 0">
      <view class="label">
        <text>猜你喜欢</text>
        <text class="more" @click="$navigateTo('/pages/new_house/new_house')">更多</text>
      </view>
      <swiper class="build-swiper" :duration="260" display-multiple-items="2" next-margin="120rpx">
        <swiper-item v-for="(item, idx) in find_build" :key="idx">
          <view class="swiper-item" @click="$navigateTo('/pages/new_house/detail?id=' + item.id)">
            <image class="img" :src="item.img | imageFilter('w_240')" mode="aspectFill"></image>
            <text class="title">{{ item.title }}</text>
            <text class="price">{{ item.build_price }}{{ item.price_unit }}</text>
          </view>
        </swiper-item>
        <swiper-item v-if="find_build.length===1"></swiper-item>
      </swiper>
    </view>
    <!-- 虚拟号拨打弹窗 -->
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
    <!-- 底部菜单 -->
    <view class="bottom-bar flex-row">
      <view class="bar-left flex-row flex-1">
        <view v-if="current_adviser && current_adviser.mid && is_open_adviser && detail.build.open_adviser" class="icon-btn" @click="consuDetail(current_adviser.id)">
          <image :src="current_adviser.prelogo | imageFilter('w_120')" class="header_img"></image>
          <text>{{ current_adviser.cname || current_adviser.typename }}</text>
        </view>
        <view class="icon-btn" v-if="navs[0].is_show && (navs[0].operation === 1 || navs[0].operation === 2)" @click="toYuyue(3, navs[0])">
          <my-icon type="yuyue" color="#666" size="50rpx"></my-icon>
          <text>{{ navs[0].name }}</text>
        </view>
        <view class="icon-btn" v-if="navs[0].is_show && (navs[0].operation === 3 || navs[0].operation === 4)" @click="cusList(navs[0].operation)">
          <my-icon type="ic_zixun" color="#666" size="50rpx"></my-icon>
          <text>{{ navs[0].name }}</text>
        </view>
        <view class="icon-btn" v-if="navs[1].is_show === 1" @click="toContrast()">
          <text class="badge" v-if="login_status > 1 && contrastCount > 0">{{ contrastCount > 99 ? "99+" : contrastCount }}</text>
          <text class="badge" v-if="login_status <= 1 && $store.state.temp_huxing_contrast_ids.length > 0">{{
            $store.state.temp_huxing_contrast_ids.length > 99 ? "99+" : $store.state.temp_huxing_contrast_ids.length
          }}</text>
          <my-icon type="pk" color="#666" size="50rpx"></my-icon>
          <text>{{ navs[1].name }}</text>
        </view>
      </view>
      <!-- 置业顾问按钮 -->
      <view class="bar-btn btn1 flex-1" v-if="navs[2].is_show === 1 && (navs[2].operation === 1 || navs[2].operation === 2)" @click="toYuyue(3, navs[2])">{{
        navs[2].name
      }}</view>
      <view
        class="bar-btn btn1 flex-1"
        v-if="navs[2].is_show === 1 && (navs[2].operation === 3 || navs[2].operation === 4)"
        @click="cusList(navs[2].operation)"
        >{{ navs[2].name }}</view
      >
      <!-- 咨询售楼处按钮 -->
      <view class="flex-1" :class="{ alone: navs[2].is_show === 0 }" v-if="navs[3].is_show === 1">
        <tel-btn :user_login_status="login_status" @ok="handleTel">
          <view class="bar-btn btn2">{{ navs[3].name }}</view>
        </tel-btn>
      </view>
    </view>
    <sub-form :sub_type="sub_type" :sub_mode="sub_mode" ref="sub_form" @onsubmit="handleSubForm"></sub-form>
  </view>
</template>

<script>
import { formatImg } from "../common/index.js";
import myIcon from "../components/myIcon.vue";
import chatBtn from "../components/open-button/chatBtn";
import SaleHouse from "../pages/new_house/components/SaleHouse";
import telBtn from "../components/open-button/telBtn";
import allTel from '../common/all_tel.js'
import getChatInfo from "../common/get_chat_info";
import checkLogin from "../common/utils/check_login";
import subForm from "../components/subForm";
export default {
  name: "saleDetail",
  components: {
    myIcon,
    chatBtn,
    telBtn,
    SaleHouse,
    subForm,
  },
  data() {
    return {
      build_id: '',
      page_style: 2,
      detail: {
        build: {},
        fangdai_info: {}
      },
      consuList: [],
      current_adviser: {},
      sale_house_list: [],
      sale_house_count: '',
      show_build_tel: false,
      focus: [],
      focusLen: 1,
      swiperCurrent: 0,
      cateActive: "",
      vr_num: 0,
      video_num: 0,
      img_num: 1,
      swiper_type_count: 0,
      tel_res: {},
      show_tel_pop: false,
      find_build: [],
      navs: [{}, {}, {}, {}],
      contrastCount: 0, //对比数量
      sub_type: 0,
    };
  },
  computed: {
    is_open_im() {
      return this.$store.state.im.ischat;
    },
    is_open_adviser() {
      return this.$store.state.im.adviser
    },
    login_status() {
      return this.$store.state.user_login_status;
    },
    sub_mode() {
      return this.$store.state.sub_form_mode;
    },
  },
  onLoad(options) {
    this.id = options.id;
    this.bid = options.bid||''
    this.getHouse(this.id);
    this.getNav(this.bid);
  },
  methods: {
    getHouse(id) {
      this.$ajax.get("buildHouse/houseDetail", { id }, (res) => {
        if (res.data.code === 1) {
          this.detail = res.data.house
          this.detail.build = res.data.build
          uni.setNavigationBarTitle({
            title: this.detail.title
          })
          this.share = res.data.share
          this.getWxConfig()
          if (!this.is_open_adviser == 1 || !this.detail.build.open_adviser == 1) {
            this.show_build_tel = true;
          }
          this.swiper_type_count = 0
          if(this.detail.vr){
            this.swiper_type_count++
            this.vr_num = 1
            this.focus.push({
              type: "vr",
              url: this.detail.vr,
              cover: this.detail.path
            })
          }
          if(this.detail.path){
            this.swiper_type_count++
            this.img_num = 1
            this.focus.push({
              type: "img",
              url: this.detail.path,
            })
          }
          this.cateActive = this.focus[0].type;
          this.detail.fangdai_info = this.computedFangdai(this.detail.total_price, res.data.borrow.jiceng/10, res.data.borrow.lpryinianyishang/1, 30)
          this.consuList = res.data.mountMembers
          this.sale_house_list = res.data.sameHouseList
          this.sale_house_count = res.data.sameHouseCount
          this.find_build = res.data.interestBuild||[]
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      });
    },
    swiperChange(e) {
      this.swiperCurrent = e.detail.current;
      this.cateActive = this.focus[this.swiperCurrent].type;
    },
    switchFocus(type) {
      this.cateActive = type;
      switch (type) {
        case "vr":
          this.swiperCurrent = 0;
          break;
        case "video":
          this.swiperCurrent = this.vr_num;
          console.log(this.swiperCurrent);
          break;
        case "img":
          this.swiperCurrent = this.vr_num + this.video_num;
          break;
        default:
          this.swiperCurrent = 0;
      }
    },
    toVr(e) {
      this.$navigateTo('/vr/detail?build_vrid=' + e.url.id)
    },
    preImgs(current_url) {
      var _current_index = 0;
      var imgs = this.focus.filter(item=>item.type === "img").map((item, index) => {
        if (item.url === current_url) {
          _current_index = index;
        }
        return item.url
      });
      uni.previewImage({
        current: _current_index,
        urls: imgs,
        indicator: "number",
      });
    },
    advAsk(e) {
      if (this.is_open_im == 1) {
        //开聊天
        // #ifdef MP-WEIXIN
        this.$store.state.buildInfo = {
          id: this.id,
          title: this.detail.title,
          type: "build",
          image: this.img[0],
        };
        getChatInfo(e.user_id, 3, this.detail.build.id);
        // #endif
        // #ifndef MP-WEIXIN
        checkLogin({
          success: (res) => {
            getChatInfo(e.user_id, 3, this.detail.build.id);
          },
          fail: (res) => {
            this.$navigateTo("/user/login/login");
          },
          complete: (res) => {
            this.$store.state.user_login_status = res.status;
          },
        });
        // #endif
      } else if (this.is_open_im == 0) {
        //不开聊天
        this.consuDetail(e.identity_id);
      }
    },
    // 执行拨打电话时间
    handleTel(e, options = {}) {
      console.log(options);
      // 如果有身份id则拨打置业顾问电话
      if (e && e.identity_id) {
        if (options.isAgent) {
          e.isAgent = 1;
        }
        if (options.isAdviser) {
          e.isAdviser = 1;
        }
        console.log(e);
        this.callAdviserMiddleNumber(e);
        return;
      }
      // 如果关闭显示楼盘电话且有置业顾问则拨打置业顾问电话，否则拨打楼盘电话
      if (this.detail.build.use_middle_number === 0 && this.detail.build.open_adviser == 1 && this.consuList.length > 0) {
        console.log("关闭显示楼盘电话且有置业顾问");
        this.callAdviserMiddleNumber();
      } else {
        console.log("开启显示楼盘电话或没有置业顾问");
        // 如果没开启虚拟号
        this.callBuildMiddleNumber();
      }
    },
    // 拨打置业顾问虚拟号码
    callAdviserMiddleNumber(e) {
      console.log("拨打置业顾问虚拟号码");
      var call_adviser = {};
      if (e && e.identity_id) {
        call_adviser = e;
      } else if (this.cusArr && this.cusArr.id) {
        call_adviser = this.cusArr;
      } else if (this.consuList.length > 0) {
        call_adviser = this.consuList[0];
      }
      var identity_id = call_adviser.identity_id || call_adviser.adviser_id || call_adviser.uid || call_adviser.id;
      var tel_type = "";
      if (call_adviser.isAgent) {
        tel_type = 3;
      }
      if (call_adviser.isAdviser) {
        tel_type = 2;
      }
      if (!call_adviser.isAgent && !call_adviser.isAdviser) {
        tel_type = 0
      }
      this.callMiddleNumber( tel_type , identity_id , 1 , this.detail.build.id);
    },
    // 拨打楼盘虚拟号码
    callBuildMiddleNumber() {
      console.log("拨打楼盘虚拟号码");
      this.callMiddleNumber(1, this.detail.build.id,1,this.detail.build.id);
    },
    // 请求虚拟号接口
    callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
      this.tel_params = {
        type: type,
        callee_id: callee_id,
        scene_type: scene_type,
        scene_id: scene_id,
        source:source,
        success: (res) => {
          this.tel_res = res.data;
          this.show_tel_pop = true;
        },
      };
      allTel(this.tel_params);
    },
    retrieveTel() {
      allTel(this.tel_params);
    },
    //转到顾问详情
    consuDetail(id) {
      if (!id) return;
      // 判断id是不是置业顾问id
      // 根据id查询出挂载列表中的置业顾问
      var current_adviser = this.consuList.find((item) => item.adviser_id === id);
      if (id === this.cusArr.adviser_id || (current_adviser && !current_adviser.isGuwen)) {
        if (this.is_open_adviser == 1 && this.detail.open_adviser == 1) {
          this.$navigateTo("/pages/consultant/detail?id=" + id);
        }
      } else {
        console.log("没开启聊天且不是置业顾问,不跳转详情");
      }
    },
    getNav(build_id) {
      this.$ajax.get("build/buildNav.html", { bid: build_id }, (res) => {
        if (res.data.code === 1) {
          this.navs = res.data.navs;
        }
      });
    },
    toContrast() {
      if (this.login_status > 1) {
        this.$navigateTo("/contrast/house_list");
      } else {
        this.$navigateTo(`/contrast/house_list?no_login=1`);
      }
    },
    toYuyue(type, nav) {
      if (nav.operation === 2 && nav.group_id) {
        // 跳转团购报名
        this.$navigateTo(`/pages/groups/detail?id=${nav.group_id}`);
      } else {
        this.toSubForme(type);
      }
    },
    //转到顾问列表
    cusList(operation) {
      if (operation === 4) {
        console.log("和置业顾问发起聊天");
        if (this.current_adviser && this.current_adviser.id) {
          var user_id = this.current_adviser.mid || this.current_adviser.uid || this.current_adviser.id;
          var identity_id = this.current_adviser.adviser_id || this.current_adviser.uid || this.current_adviser.id;
          this.advAsk({ user_id: user_id, identity_id: identity_id });
        } else if (this.consuList.length > 0) {
          var user_id = this.consuList[0].mid || this.consuList[0].uid || this.consuList[0].id;
          var identity_id = this.consuList[0].adviser_id || this.consuList[0].uid || this.consuList[0].id;
          this.advAsk({ user_id: user_id, identity_id: identity_id });
        } else {
          uni.showToast({
            title: "该楼盘还没有置业顾问",
            icon: "none",
          });
        }
        return;
      }
      if (!uni.getStorageSync("token")) {
        this.$navigateTo("/user/login/login");
        // this.reload = true
        return;
      }
      this.$navigateTo("/pages/consultant/consuList?id=" + this.bid);
    },
    toSubForme(type, operation) {
      this.sub_operation = operation || "";
      this.sub_type = type;
      this.$refs.sub_form.showPopup();
    },
    handleSubForm(e) {
      //提交报名
      e.from = "预售房源详情";
      e.bid = this.bid;
      e.type = this.sub_type || "";
      if (this.sub_operation) e.operation = this.sub_operation;
      this.$ajax.post("build/signUp.html", e, (res) => {
        uni.hideLoading();
        if (res.data.code === 1) {
          // 没开启引导登录模式或已经绑定手机号了
          if (this.sub_mode !== 2 || res.data.status === 3) {
            //提示报名成功
            uni.showToast({
              title: res.data.msg,
              icon: "none",
            });
            this.$refs.sub_form.closeSub();
          } else {
            this.$refs.sub_form.getVerify();
          }
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: "none",
          });
        }
      });
    },
    computedFangdai(total, proportion, rate, time ){
      var limit = (total * (1 - proportion)).toFixed(2)
      var yuegong1 = limit * (rate / 100 / 12) * Math.pow((1 + rate / 100 / 12), time * 12) / (Math.pow((1 + rate / 100 / 12), time * 12) - 1)
      var loanInterest1 = (yuegong1 * time * 12 - limit) / 10000
      var yuegong2 = (limit / (time * 12)) + (limit - 0) * (rate / 100 / 12)
      var loanInterest2 = (((limit / (time * 12) + limit * rate / 100 / 12) + limit / (time * 12) * (1 + rate / 100 / 12)) / 2 * time * 12 - limit) / 10000
      return {
        firstPay: (total - limit).toFixed(2), //首付
        proportion: proportion,
        limit: limit, //贷款总额
        rate: rate,
        time: time+'年('+(time*12)+'期)', //贷款年限
        yuegong1: (yuegong1*10000).toFixed(2), //等额本息月供
        loanInterest1: loanInterest1.toFixed(2), //等额本息利息
        yuegong2: yuegong2.toFixed(2), //等额本金月供
        loanInterest2: loanInterest2.toFixed(2) //等额本息利息
      }
    },
    toCalculator(e){
      var loan = {
        loanLimit: e.limit/1,
        loanRate: e.rate,
        loanTime: 30,
        totalLimit: this.detail.total_price
      }
      this.$navigateTo('/pages/calculator/res?data='+JSON.stringify(loan)+"&loan_type=0")
    },
    toBuild(){
      this.$navigateTo(`/pages/new_house/detail?id=${this.bid}`)
    },
    getSendMsg(e, type) {
      // #ifdef MP-WEIXIN
      this.$ajax.get('im/getUserReply.html',{page_from:type,bid:this.bid},res=>{
        if(res.data.mid){
          this.$store.state.autoSendMsg = res.data.content||''
          getChatInfo(res.data.mid, 3, this.bid)
        }else{
          uni.showToast({
            title: '没找到置业顾问',
            icon: 'none'
          })
        }
      })
      // #endif
      // #ifndef MP-WEIXIN
      this.$ajax.get('im/getUserReply.html',{page_from:type,bid:this.bid},res=>{
        if(res.data.mid){
          this.$store.state.autoSendMsg = res.data.content||''
          getChatInfo(res.data.mid, 3, this.bid)
        }
      })
      // checkLogin({
      //   success: (res) => {
      //     this.$ajax.get('im/getUserReply.html',{page_from:type,bid:this.bid},res=>{
      //       if(res.data.mid){
      //         this.$store.state.autoSendMsg = res.data.content||''
      //         getChatInfo(res.data.mid, 3, this.bid)
      //       }
      //     })
      //   },
      //   fail: (res) => {
      //     this.$navigateTo('/user/login/login')
      //   },
      //   complete: (res) => {
      //     this.$store.state.user_login_status = res.status;
      //   },
      // });
      // #endif
    },
  },
};
</script>

<style scoped lang="scss">
.page {
  padding-bottom: 120rpx;
  background-color: #fff;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.container {
  margin: 0 48rpx;
  > .label {
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 32rpx;
    font-weight: bold;
    .more {
      font-size: 24rpx;
      color: #6f6f6f;
    }
  }
}
.highlight {
  color: $uni-color-primary;
}
.focus-box {
  position: relative;
  swiper.banner {
    height: 65vw;
  }
  .swiper-item {
    height: 100%;
  }
  .swiper-item image {
    width: 100%;
    height: 100%;
  }
  .swiper-item image.video-icon {
    width: 16vw;
    height: 0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0);
  }
  .img-total {
    position: absolute;
    padding: 4upx 20upx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    border-radius: 20upx;
    right: 20rpx;
    bottom: 24rpx;
    color: #fff;
    &.style2 {
      right: initial;
      left: 20rpx;
    }
  }
  .cate-box {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 24upx;
    display: block;
    text-align: center;
    font-size: 0;
    &.style2 {
      .cate-list {
        padding: 6rpx;
        border-radius: 20rpx;
        color: #fff;
        background-color: rgba($color: #000000, $alpha: 0.5);
        &.no_bg{
          background: none;
        }
      }
      .cate {
        padding: 4rpx 24rpx;
        border-radius: 20rpx;
        &.active {
          background: #ff3939;
        }
      }
    }
    .cate-list {
      display: inline-block;
      border-radius: 6rpx;
      overflow: hidden;
      background-color: #fff;
    }
  }
  .cate {
    display: inline-block;
    padding: 8rpx 20rpx;
    font-size: 22rpx;
    // background-color: #fff;
    &.active {
      background: linear-gradient(45deg, #fd9ea3, #fb656a);
      color: #fff;
    }
  }
}

.house_title {
  padding-top: 32rpx;
  padding-bottom: 24rpx;
  font-size: 40rpx;
  font-weight: bold;
}

.huxing {
  justify-content: space-between;
  .item {
    flex: 1;
    display: flex;
    flex-direction: column;
    .value {
      font-size: 36rpx;
      font-weight: bold;
      color: $uni-color-primary;
    }
    .label {
      margin-top: 8rpx;
      font-size: 22rpx;
      color: #6f6f6f;
    }
  }
}

.other_info {
  margin-top: 24rpx;
  padding: 24rpx 0;
  .item {
    padding: 12rpx 0;
    display: inline-block;
    min-width: 50%;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    .label {
      margin-right: 12rpx;
      color: #6f6f6f;
    }
    // .value {
    //   font-weight: bold;
    // }
    .btn{
      margin-left: 18rpx;
      display: inline-block;
    }
  }
}

.build_info {
  margin-top: 32rpx;
  .info {
    // padding: 24rpx;
    border: 1rpx solid #f0f0f0;
    .build_title {
      margin: 24rpx;
      font-size: 32rpx;
    }
    .info_list {
      .item {
        margin: 24rpx;
        display: inline-block;
        min-width: 50%;
        max-width: 100%;
        font-size: 26rpx;
        .label {
          margin-right: 18rpx;
          color: #6f6f6f;
        }
      }
    }
  }
}

// 400电话
.tel-box {
  margin-bottom: 24rpx;
  background: #fff;
  border: 1rpx solid #ffa800;
  border-radius: 8rpx;
  position: relative;
  background-color: #fff7e9;
  .tel_block {
    display: flex;
    align-items: center;
    padding: 22rpx 32rpx;
    // background-color: #fff;
    border-radius: 55rpx;
    line-height: 1;
    .tel_right {
      display: flex;
      margin-left: 24rpx;
      align-items: center;
      flex: 1;
      justify-content: space-between;
      .btn {
        margin-left: 24rpx;
        display: inline-block;
        height: 48rpx;
        line-height: 48rpx;
        width: 108rpx;
        border-radius: 4rpx;
        text-align: center;
        background-image: linear-gradient(180deg, #ffa800 0, #ff7a00 100%);
        font-size: 22rpx;
        color: #fff;
      }
    }
    .tel {
      // font-size: 36rpx;
      font-weight: bold;
      // background: linear-gradient(to right,#ff706b,#fdaa5e);
      // -webkit-background-clip: text;
      // color: transparent;
      // margin-bottom: 16rpx;
    }
    .tip {
      font-size: 22rpx;
      color: #999;
    }
  }
  .tel_left {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
    border-radius: 18rpx;
    background-image: linear-gradient(180deg, #ffa800 0, #ff7a00 100%);
    // background: linear-gradient(to right,#ff706b,#fdaa5e);
  }
  // .tel_bg{
  //   height: 100%;
  //   width: 100%;
  //   border-radius: 56rpx;
  //   background:linear-gradient(to right,#ff706b,#fdaa5e);
  //   box-shadow: 0 2px 4px 0 rgba(#ff706b,0.2);
  // }
}

// 置业顾问列表
.adviser {
  margin-top: 32rpx;
}
.advier-list {
  background-color: #fff;
  .adviser-item {
    justify-content: space-between;
    align-items: flex-start;
    padding: 30rpx 0;
    .header_img {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 15rpx;
      overflow: hidden;
      background-color: #f3f3f3;
    }
    image {
      width: 100%;
    }
    .info {
      flex: 1;
      overflow: hidden;
      .name {
        display: flex;
        align-items: center;
        margin-bottom: 6rpx;
        .text {
          // flex: 1;
          font-size: 32rpx;
        }
      }
      .mgl-20 {
        margin-left: 20rpx;
      }
      .data {
        display: inline-block;
        margin-bottom: 6rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 22rpx;
        color: #999;
      }
    }

    .adviser-right {
      align-items: flex-end;
    }

    .btn-list {
      align-items: center;
      text {
        color: #999;
      }
      .btn {
        width: 64rpx;
        height: 64rpx;
        ~ .btn {
          margin-left: 30rpx;
        }
        .icon-box {
          display: flex;
          flex-direction: column;
          width: 64rpx;
          height: 64rpx;
          justify-content: center;
          text-align: center;
          border-radius: 50%;
          background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
        }
        .icon {
          width: 64rpx;
          height: 64rpx;
        }
      }
    }
  }
}

.hot_sale {
  margin-top: 24rpx;
  margin-right: 0;
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: 0 0;
  .label {
    margin-bottom: 0;
    padding-right: 48rpx;
    margin-bottom: 24rpx;
  }
  .house_list {
    width: 100%;
    padding-right: 24rpx;
    box-sizing: border-box;
    overflow-y: hidden;
  }
}
// 发现好房
.build_box {
  margin-right: 0;
  margin-top: 32rpx;
  .label {
    padding-right: 48rpx;
  }
  .build-swiper {
    height: 294rpx;
    .swiper-item {
      display: flex;
      flex-direction: column;
      height: 100%;
      border-radius: 8rpx;
      margin-right: 30rpx;
      line-height: 1;
      .img {
        width: 100%;
        height: 200rpx;
        margin-bottom: 16rpx;
        border-radius: 8rpx;
      }
      .title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 32rpx;
        margin-bottom: 16rpx;
        font-weight: bold;
        color: #333;
      }
      .price {
        font-size: 32rpx;
        color: $uni-color-primary;
      }
    }
  }
}
// 底部菜单
.bottom-bar {
  background-color: #fff;
  box-sizing: border-box;
  height: 110rpx;
  padding: 15rpx 48rpx;
  left: 0;
  z-index: 10;
  .bar-left {
    padding-right: 48rpx;
    justify-content: flex-start;
  }
  .icon-btn {
    // width: 100rpx;
    align-items: center;
    padding: 0;
    margin: 0;
    background-color: #fff;
    line-height: initial;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // flex: 1;
    padding-right: 48rpx;
    overflow: hidden;
    position: relative;
    // & ~ .icon-btn {
    //   margin-left: 24rpx;
    // }
    .header_img {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
    }
    text {
      line-height: 1;
      font-size: 22rpx;
      color: #999;
      display: inline-block;
      width: 100%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .badge {
      display: inline-block;
      box-sizing: border-box;
      width: auto;
      position: absolute;
      top: 0;
      left: 32rpx;
      // right: 38rpx;
      height: 28rpx;
      padding: 0 8rpx;
      min-width: 28rpx;
      border-radius: 14rpx;
      font-size: 22rpx;
      background-color: $uni-color-primary;
      color: #fff;
    }
  }
  .bar-btn {
    // width: 220rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
    padding: 0;
    margin: 0;
    border-radius: 0;
    color: #fff;
    &.btn1 {
      background: #fbac65;
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.05);
      border-top-left-radius: 40rpx;
      border-bottom-left-radius: 40rpx;
    }
    &.btn2 {
      background: linear-gradient(90deg, #fb656a 0%, #fbac65 100%);
      box-shadow: 0 0 4px 0 rgba(255, 80, 0, 0.3);
      border-top-right-radius: 40rpx;
      border-bottom-right-radius: 40rpx;
    }
  }
}
</style>
