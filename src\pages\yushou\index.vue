<template>
<view class="page" :class="{pdb_120: (sharers_info.adviser_id&&is_open_adviser)||sharers_info.agent_id}">
    <view class="header" :style="{backgroundImage:'url('+bgImg+')'}">
        <view class="head-info">
            <view class="share_row flex-box">
                <view class="search-box flex-box">
                    <my-icon type="ic_sousuo" color="#fff" size="40rpx"></my-icon>
                    <input
                    class="inp"
                    type="search"
                    v-model="params.keyword"
                    @confirm="handleSearch"
                    placeholder="项目名称 （支持模糊查询）"
                    placeholder-style="font-size:26rpx;color:#fff"
                    />
                </view>
                <view class="share_icon" @click="showSharePop">
                    <my-icon type="ic_fenxiang" size="32rpx" color="#fff"></my-icon>
                </view>
            </view>
            <view class="update-tip flex-box">
                <view class="data-infos flex-box">
                    <view class="line"></view>
                    <view class="site-name">{{siteName}}</view>
                    <view class="line"></view>
                </view>
            </view>
            <view class="title">{{yushouData.title}}</view>
        
            <view class="update-tip flex-box"><view>{{yushouData.date}}</view><view class="flex-box data-infos data-dingyue" @click="showDingyuePop"><my-icon type="ic_jia" color="#fff" size="28rpx"></my-icon><view>订阅</view></view></view>
        
        </view>
        <view class="flex-box data-box">
            <view class=" text-center fengexian text-left" v-if="yushouData.count">
                <view class="data-title">年度发证</view>
                <view class="data-data"><text class="data-datas red">{{yushouData.count}}</text>个</view>
            </view>
            <!-- <view class="flex-1 text-center fengexian" v-if="yushouData.zzts">
                <view class="data-title">住宅</view>
                <view class="data-data"><text class="data-datas red">{{yushouData.zzts}}</text>套</view>
            </view> -->
            <view class="flex-1 text-center fengexian" v-if="yushouData.total_area">
                <view class="data-title">总面积 </view>
                <view class="data-data">约<text class="data-datas red">{{yushouData.total_area}}</text>m²</view>
            </view>
            <view class=" text-center text-left" v-if="isShowTaoshu">
                <view class="data-title">住宅</view>
                <view class="data-data"><text class="data-datas red">{{yushouData.zzts||0}}</text>套</view>
            </view>
        </view>
    </view>
    
    <view class="middle-bar flex-box">
        <view class="mid-bar-left mid-bar flex-box flex-row" @click="goData('data')">
            <view class="icon-img-box"><image class="icon-img" :src="bangdanSrc" mode="widthFix"></image></view>
            
            <view>数据榜单</view>
        </view>
        <view class="mid-bar-right mid-bar flex-box flex-row" @click="goData('statistics')">
            <view class="icon-img-box"><image class="icon-img" :src="tongjiSrc" mode="widthFix"></image></view>
            <view>统计报表</view>
        </view>
    </view>
    <view class="top-20 tab-bars">
        <tab-bar :tabs="tabs" :nowIndex="currentIndex" :fixedTop="false" lineHeight="1" height="auto" :showLine="false">
            <text v-for="(item, index) in tabs" :key="index" :id="'i' + index" :class="{active:currentIndex===index}" @click="clickTab({detail:{current:index,value:item.value}})">{{item.name}}</text>
        </tab-bar>
    </view>
    <view class="top-20">
        <view class="lists">
            <view class="time-line" v-for ="item in listData" :key='item.id' @click="toDetail(item.id)"> 
                <view class="time">
                    <view class="line-title">
                        {{item.hprq}}
                    </view>
                    <view class="data-card">
                        <data-card :item='item'></data-card>
                    </view>
                </view>               
            </view>
        </view>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
    <view class="sharers_info flex-box" v-if="(sharers_info.adviser_id&&is_open_adviser)||sharers_info.agent_id">
        <view class="img"> <image :src="sharers_info.prelogo | imageFilter('w_240')" mode="widthFix"></image></view>
        <view class="info flex-1">
            <view class="name">{{sharers_info.cname}}</view>
            <view class="identity">{{sharers_info.identity===1?'置业顾问':'经纪人'}}</view>
        </view>
        <view class="btn_box flex-box">
            <view class="btn" @click="handleChat()">微聊</view>
            <view  v-if ="(sharers_info.adviser_id&&switch_adviser_tel) ||sharers_info.agent_id" class="btn" @click="handleTel()">电话咨询</view>
        </view>
    </view>
    <share-pop ref="show_share_pop" @copyLink="copyLink" :showHaibao="false" @showCopywriting='showCopywriting'></share-pop>
  	<dingyue ref="dingyue" @dingyue="dingyue" :type="type" @login="toLogin" @bindPhone="toBind" ></dingyue>
    <my-popup ref="qrcode_popup" position="top">
        <view class="qrcode-box">

            <!-- #ifdef H5 -->
            <view class="img-box">
                <view class="title titles">数据报告将通过服务号发送</view>
                <view class="tip red">请关注{{siteName}}公众号</view>
                <image @longtap="saveQrcode" class="qrcode" :src="qrcode" mode="aspectFill"></image>
                <view>
                    <view class="title">长按保存图片</view>
                    <view class="tip">相册选取，识别关注</view>
                </view>
            </view>
            <!-- #endif -->
            <!-- #ifndef H5 -->
            <view class="img-box">
                <view class="title titles">数据报告将通过服务号发送</view>
                <view class="tip red">请关注{{siteName}}公众号</view>
                <image class="qrcode" :src="qrcode" mode="aspectFill"></image>
                <view>
                    <view class="tip">长按识别二维码关注公众号</view>
                </view>
            </view>
            <!-- #endif -->
            <view class="icon-box" @click="$refs.qrcode_popup.hide()">
                <my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
            </view>
        </view>
    </my-popup>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <enturstBtn v-if="sharers_info.agent_id||sharers_info.adviser_id" :to_user="sharers_info" @click="$refs.enturst_popup.show()" />
    <my-popup ref="enturst_popup" height="694rpx" position="center" :touch_hide="false">
        <enturstBox @success="$refs.enturst_popup.hide()" @close="$refs.enturst_popup.hide()" @popup_login="showLoginPopup('为方便您及时接收消息通知，请输入手机号码')" :to_user="sharers_info" />
    </my-popup>
    <!-- 登录弹窗 -->
        <login-popup ref="login_popup" @onclose="handleCloseLogin" :sub_content="login_tip" @success="onLoginSuccess"></login-popup>
    <chat-tip></chat-tip>
    <tel-pop :tel_res="tel_res" v-model="show_tel_pop" @retrieve="retrieveTel"></tel-pop>
</view>
</template>

<script>
import myIcon from "../../components/myIcon"
import tabBar from "../../components/tabBar"
import dataCard from "../../components/dataCard"
import {config} from '../../common/config'
import {uniLoadMore} from '@dcloudio/uni-ui'
import {wxShare} from '../../common/mixin'
import sharePop from '../../components/sharePop'
import shareTip from '../../components/shareTip'
import myPopup from "../../components/myPopup.vue"
import loginPopup from '../../components/loginPopup'
import dingyue from "../../components/dingyue.vue"
import getChatInfo from '../../common/get_chat_info'
import allTel from '../../common/all_tel.js'
import enturstBtn from '@/components/enturstBtn'
import enturstBox from '@/components/enturstBox'
export default {
    data() {
        return {
            get_status:"loading",
            content_text:{
                contentdown:"",
                contentrefresh:"正在加载...",
                contentnomore:"没有更多数据了"
            },
            yushouData:{},
            listData:[],
            tabs:[],
            tip:"正在加载...",
            params:{
                page:1,
                rows:10,
                bid:'',
                keyword:"",
                type:""
            },
            siteCity:'',
            login_tip:'',
            currentIndex:0,
            // 分享者信息
            sharers_info: {},
            // 当前用户的信息
            current_user_info: {},
            show_share_tip:false,
            qrcode:'',
            link:"",
            shareId:'',
            shareType:"",
            type:'dingyue',
            tel_res: {},
            show_tel_pop: false,
        }
    },
    mixins:[wxShare],
    components: {
        tabBar,
        uniLoadMore,
        dataCard,
        myIcon,
        sharePop,
        dingyue,
        myPopup,
        shareTip,
        loginPopup,
        enturstBtn,
		enturstBox
    },
    onLoad(options){
        // 如果是分享链接进来的
        if (options.shareId && (options.type||options.shareType)) {
            this.shareId = options.shareId
            this.shareType = options.type||options.shareType
            this.share_time =options.f_time||''
        }
        if(options.bid){
            this.params.bid=options.bid
        }
        this.getData()
        this.getStatisData()
        uni.$on('getDataAgain',()=>{
            this.getStatisData()
        })
    },
    onShow(){
		if(this.$store.state.updatePageData){
            this.getStatisData()
            this.$store.state.updatePageData = false
        }
	},
    onUnload(){
		uni.$off('getDataAgain')
	},
    computed: {
        siteName(){
            return this.$store.state.siteName
        },
        bangdanSrc(){
            return config.imgDomain+'/images/new_icon/record/<EMAIL>?x-oss-process=style/m_240'
        },
        tongjiSrc(){
            return config.imgDomain+'/images/new_icon/record/<EMAIL>?x-oss-process=style/m_240'
        },
        bgImg(){
            return  config.imgDomain+'/images/new_icon/record/<EMAIL>'
        },
        is_open_adviser() { //是否开启置业顾问功能
            return this.$store.state.im.adviser
        },
        is_open_im() { // 是否开启聊天功能
            return this.$store.state.im.ischat
        },
        isShowTaoshu() { // 是否开启聊天功能
            return this.$store.state.isShowTaoshu
        },
        switch_adviser_tel(){
             return this.$store.state.switch_adviser_tel
        }
    },
    methods:{
        getData(){
            if(this.params.page == 1){
                this.listData=[]
            }
            this.get_status = "loading"
            this.$ajax.get("build/booking.html",this.params,res=>{
                if(res.data.code == 1){
                    this.listData = this.listData.concat(res.data.list)
                    if(res.data.list.length<this.params.rows){
                        this.get_status = "noMore"
                    }else{
                        this.get_status = "more"
                    }
                }else{
                    this.get_status = "noMore"
                }
            })
        },
        getStatisData(){
            let params = {};
            if(this.shareId&&this.shareType){
                params = {
                    sid: this.shareId,
                    
                    sharetype: this.shareType
                }
            }
            params.forward_time=this.share_time ||''
            this.type="dingyue"
            this.$ajax.get("build/bookingStatistics",params,res=>{
                
                if(res.data.cates&&res.data.cates.length>0){
                    let tabs = res.data.cates.map(item=>{
                        return {
                            name:item.cate_name,
                            value: item.id
                        }
                    })
                    this.tabs = [{ name:"最新", value:""}].concat(tabs)
                }
                if (res.data.shareUser) { //当前用户信息
                    this.current_user_info = res.data.shareUser
                    if(res.data.shareUser.adviser_id){
                        this.current_user_info.identity = 1
                        this.current_user_info.identity_id = res.data.shareUser.adviser_id
                    }else if(res.data.shareUser.agent_id){
                        this.current_user_info.identity = 2
                        this.current_user_info.identity_id = res.data.shareUser.agent_id
                    }
                }
                if (res.data.share_user) { //分享者信息
                    this.sharers_info = res.data.share_user
                    if(res.data.share_user.adviser_id){
                        this.sharers_info.identity = 1
                    }else if(res.data.share_user.agent_id){
                        this.sharers_info.identity = 2
                    }
                }
                if(res.data.code == 1){
                    this.yushouData = {
                        title:res.data.title,
                        count:res.data.count,
                        date:res.data.date,
                        total_area:res.data.total_area,
                        zzts:res.data.zzts
                    }
                }
                if (res.data.siteCity){
                    this.siteCity=res.data.siteCity
                }
                if (this.shareId){
                    // 获取登录状态
                    this.$ajax.get('member/checkUserStatus', {}, res => {
                        if (res.data.code !== 1) {
                            this.$store.state.user_login_status = res.data.status
                            if (this.$store.state.user_login_status==1){
                                this.type="denglu"
                                this.$store.state.updatePageData=true
								uni.setStorageSync('backUrl', window.location.href)
								this.showDingyuePop()
							}else if (this.$store.state.user_login_status==2){
                                this.type="bangshouji"
                                this.$store.state.updatePageData=true
                                this.showDingyuePop()
								// this.$navigateTo("/user/bind_phone/bind_phone")
							}
                        }
                    })
                }
                if(res.data.share){
                    this.share = res.data.share
                }else{
                    this.share = {
                        title:res.data.title,
                        content:res.data.date,
                        pic:res.data.wxhuifuimg||''
                    }
                }
                this.share.link=this.getShareLink()
                this.getWxConfig()
            })
        },
        clickTab(e){
            this.currentIndex=e.detail.current
            this.params.page = 1
            this.params.type = e.detail.value
            if(e.value==""){
                this.params.keyword = ""
            }
            this.getData()
        },
        handleSearch(){
            uni.hideKeyboard()
            this.params.page = 1
            this.getData()
        },
        toDetail(id){
            this.$navigateTo(`/pages/yushou/detail?id=${id}`)
        },
        goData(type){
            if (type==='data'){
                this.$navigateTo('/statistics/yushou_data')
            }else if (type==="statistics"){
                this.$navigateTo('/statistics/yushou_statistics')
            }
            
        },

        // 订阅
        showDingyuePop(){
            this.$refs.dingyue.showPopup()
        },
        toLogin(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/login/login")
		},
		toBind(){
			this.$refs.dingyue.hide()
			this.$navigateTo("/user/bind_phone/bind_phone")
        },
        showLoginPopup(tip){
            this.login_tip = tip
            this.$refs.login_popup.showPopup()
        },
        handleCloseLogin() {
            if (this.$store.state.user_login_status === 1) {
                uni.removeStorageSync('token')
                this.$navigateTo('/user/login/login')
            }
            if(this.$store.state.user_login_status===2){
                this.$navigateTo('/user/bind_phone/bind_phone')
            }
        },
        onLoginSuccess(res){
            this.$store.state.user_login_status = 3
            if(this.weituo_is_show){
                console.log("登录成功后继续执行委托接口")
                this.$refs.enturst_box.handleEnturst()
            }
        },
        dingyue(){
            this.$ajax.get("build/subscribeBooking",{type:1},res=>{  //type 1为预售 2 为土拍
                if (res.data.code ==-1){
                    uni.setStorageSync('backUrl', window.location.href) 
                    this.$navigateTo("/user/login/login")
                }else if (res.data.code ==2){
                    this.$refs.dingyue.hide()
                    this.$navigateTo("/user/bind_phone/bind_phone")
                }else if(res.data.code ==1){
                    uni.showToast({
                        title:res.data.msg,
                        icon:"success"
                    })
                    setTimeout(() => {
                        this.$refs.dingyue.hide()
                    },500)
                }else if (res.data.code ==0){  //订阅失败
                    uni.showToast({
                        title:res.data.msg,
                        icon:"none"
                    })
                    if (res.data.gzhewm){
                        this.qrcode=res.data.gzhewm
                        setTimeout(() => {
                            this.$refs.qrcode_popup.show()
                        }, 500);
                    }
                    this.$refs.dingyue.hide()
                }
            },err=>{},{disableAutoHandle:true})

        },
        // 保存二维码
        saveQrcode(){
            uni.request({
                url:this.qrcode,
                method:'GET',
                responseType: 'arraybuffer',
                success:(res)=>{
                    let base64 = uni.arrayBufferToBase64(res);
                    const userImageBase64 = 'data:image/jpg;base64,' + base64;
                    uni.saveImageToPhotosAlbum({
                        filePath: userImageBase64,
                        success: result => {
                            uni.showToast({
                                title: '保存成功，在微信从相册中选取识别吧',
                                icon: 'none',
                                duration: 4000
                            })
                        },
                        fail: err => {
                            console.log(err)
                            uni.showToast({
                                title: '保存失败，请重试',
                                icon: 'none'
                            })
                        }
                    })
                }
            }); 
        },
        // 获取分享链接
        getShareLink(){
            let link = window.location.href
            let time =parseInt(+new Date()/1000)
            if (this.current_user_info.identity) { //当前用户是 置业顾问或者经纪人  
                link = `${window.location.origin}${window.location.pathname}?shareId=${this.current_user_info.identity_id}&type=${this.current_user_info.identity}&f_time=${time}`
            }
            return link
        },
        showSharePop(){
            this.getShortLink()
            this.$refs.show_share_pop.show()
        },
        // 复制分享链接
        copyLink(){
            this.show_share_tip=true
            // this.copyText(this.getShareLink(), ()=>{
            //     uni.showToast({
            //     title: '复制成功,去发送给好友吧',
            //     icon: 'none'
            //     })
            // })
        },
        getShortLink(){
            this.link=this.getShareLink()
            this.$ajax.get('build/shortUrl.html', {page_url:this.link }, res=>{
                if(res.data.code === 1){
                this.link = res.data.short_url
                }
            })
        },
        // 复制分享内容
        showCopywriting(){
            console.log("复制内容")
            const content = `【我正在看】${this.siteCity}商品房预售证查询系统\n【链接】${this.link}`
            this.copyText(content, ()=>{
                uni.showToast({
                title: '复制成功,去发送给好友吧',
                icon: 'none'
                })
            })
        },
        // 复制内容
        copyText(cont, callback) {
            let oInput = document.createElement('textarea')
            oInput.value = cont
            document.body.appendChild(oInput)
            oInput.select() // 选择对象;
            oInput.setSelectionRange(0, oInput.value.length);
            document.execCommand('Copy') // 执行浏览器复制命令
            oInput.blur()
            oInput.remove()
            if(callback) callback()
        },
        // 发起聊天
        handleChat(){
            if(!this.is_open_im){
                if (this.sharers_info.identity == 1) { //置业顾问
                    this.$navigateTo('/pages/consultant/detail?id=' + this.sharers_info.adviser_id)
                } else if (this.sharers_info.identity == 2) {
                    this.$navigateTo('/pages/agent/detail?id=' + this.sharers_info.agent_id)
                }
                return
            }
            // #ifndef MP-WEIXIN
                getChatInfo(this.sharers_info.mid, 19)
            // #endif
        },
        // 拨打电话
        handleTel(){
            this.tel_params = {
                type: this.sharers_info.identity == 1?'2':'3',
				callee_id: this.sharers_info.mid,
				scene_type:this.sharers_info.identity == 1?'2':'3',
				scene_id:this.sharers_info.mid,
                success: (res)=>{
                    this.tel_res = res.data
                    this.show_tel_pop = true
                }
            }
            allTel(this.tel_params)
        },
        retrieveTel(){
            allTel(this.tel_params)
        },
        // 检测登录状态
        checkLogin(callback) {
            this.$ajax.get('member/checkUserStatus', {}, res => {
                if (res.data.code === 1) {
                    callback&&callback()
                } else {
                    this.$store.state.user_login_status = res.data.status
                    if (this.$store.state.user_login_status==1){
                        uni.setStorageSync('backUrl', window.location.href) 
						this.$navigateTo("/user/login/login")
					}else if (this.$store.state.user_login_status==2){
						this.$navigateTo("/user/bind_phone/bind_phone")
					}
                }
            })
        },
    },
    onReachBottom(){
        this.params.page = this.params.page+1
        this.getData()
    },
    onNavigationBarSearchInputConfirmed(e) {
        this.handleSearch(e.text)
    }

}
</script>

<style scoped lang="scss">
.page{
    background: #fff;
}
.pdb_120{
    padding-bottom: 120rpx;
}
.header{
    width: 100%;
    height: 480rpx;
    // background: linear-gradient(to bottom right,#009999,#3399CC);
    background-image: linear-gradient(0deg, #F7918F 0%, #FB656A 100%);
    border-radius: 0 0 24px 24px;
    display: flex;
    align-items: center;
    
    background-size: 100%;
    background-repeat: no-repeat;
    padding: 0 48rpx;
    box-sizing: border-box;
    position: relative;

    .head-info{
        width: 100%;
    }
    .title{
        font-size: 46upx;
        color: #fff;
        text-align: center;
        margin: 32upx 0;
    }
    .update-tip{
        // margin-top: 32upx;
        color: #fff;
        text-align: center;
        align-items: center;
        justify-content: center;
        flex: 1;
        .data-infos{
            align-items: center;
            &.data-dingyue{
                padding: 0 10rpx;
                border-radius: 6rpx;
                border: 2rpx solid #fff;
                margin-left: 10rpx;
            }
        }
        .line{
            width: 80rpx;
            height: 1rpx;
            background-color: #fff;
        }
        .site-name{
            margin: 0 10rpx;
        }
    }
}
.fengexian{
    position: relative;
}
.fengexian:after{
    content: "";
		position: absolute;
		top: 20%;
		bottom: 20%;
		right: 0;
		width: 1px;
		-webkit-transform: scaleX(.5);
		transform: scaleX(.5);
		background-color: $uni-border-color;
}
.data-box{
    position: absolute;
    background: #FFFFFF;
    padding:  24rpx 0;
    bottom: -70rpx;
    left: 48rpx;
    right: 48rpx;;
    border: 2rpx solid #D8D8D8;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.08);
    border-radius: 8px;
    // transform: translateY(-50%);
    .data-title{
        margin-bottom: 16upx;
        font-size: 22rpx;
        color: #999;
    }
    .text-left{
        min-width: 200rpx;
        padding: 0 32rpx;
        box-sizing: border-box;
    }
    .data-data{
        font-size:22rpx;
        // font-weight: bold;
        color: #999;
    }
    .data-datas{
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
    }
    .red{
        color: $uni-color-primary;
    }
}
.middle-bar{
    padding: 120rpx 48rpx 24rpx;
    .mid-bar{
        flex: 1;
        background: #F8F8F8;
        border-radius: 8px;
        justify-content: center;
        align-items: center;
        padding: 20rpx 0;
        &.mid-bar-left{
            margin-right: 48rpx;
        }
        .icon-img-box{
            width: 64rpx;
            height: 64rpx;
            overflow: hidden;
            margin-right: 28rpx;
            .icon-img{
                width: 100%;
            }
        }
        
    }
}
.tab-bars{
    padding: 24rpx 48rpx;
    position: sticky;
    z-index: 50;
    background: #fff;
    // #ifdef H5
    top: 44px;
    // #endif
    // #ifndef H5
    top: 0;
    // #endif
    .cate{
        align-items: flex-end;
        max-width: 100%;
    }
    text {
        color: #333;
        transition: 0.2s;
        padding-right: 20rpx;
        &.active{
            font-weight: bold;
            font-size: 40rpx;
        }
        &~text{
            padding-left: 20rpx;
        }
    }
}


.lists{
    padding: 0 48rpx;
    .time-line{
        // padding:0 24rpx ;
        padding-left: 24rpx;
        .time{
            position: relative;
            &:before{
                content:'';
                position: absolute;
                width: 2rpx;
                height: 100%;
                background: #D8D8D8;
                top: 10rpx;
                left: -20rpx;
                
            }
            .line-title{
                position: relative;
                color: #FB656A;
                font-size: 22rpx;
                margin-bottom: 16rpx;
                &:before{
                    content:'';
                    position: absolute;
                    background-image: linear-gradient(180deg, #F7918F 0%, #FB656A 100%);
                    width: 20rpx;
                    height: 20rpx;
                    top: 50%;
                    transform: translateY(-50%);
                    left: -30rpx;
                    border-radius: 50%;
                }

            }

       
            
         }
         .data-card{
             padding-bottom: 24rpx;
         }

    }
}
.share_row{
    position: absolute;
    left: 48rpx;
    right: 48rpx;
    top: 12rpx;
    align-items: center;
    padding: 10rpx 0;
    .share_icon{
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 24rpx;
        width: 66rpx;
        height: 66rpx;
        border-radius: 50%;
        background-color: rgba(0,0,0, 0.5);
    }
}
// 搜索栏
.search-box {
    flex: 1;
    align-items: center;
    // #ifdef H5
    // margin-top: 44px;
    // #endif
    padding: 6rpx 20rpx;
    border-radius: 32rpx;
    background-color: rgba($color: #ffffff, $alpha: 0.6);
    .inp {
        margin-left: 20rpx;
        flex:1;
    }
}

// 分享者信息
.sharers_info{
    position: fixed;
    width: 100%;
    height: 120rpx;
    bottom: 0;
    padding: 0 48rpx;
    box-sizing: border-box;
    align-items: center;
    background-color: #fff;
    z-index: 90;
    .img{
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 16rpx;
		overflow: hidden;
		image{
			width: 100%;
			height: 100%;
		}
    }
    .info{
        overflow: hidden;
        .name{
            margin-bottom: 16rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .identity{
            font-size: 24rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: #999;
        }
    }
    .btn_box{
        .btn{
            margin-left: 20rpx;
            padding: 10rpx 34rpx;
            font-size: 26rpx;
            color: $uni-color-primary;
            border: 1px solid $uni-color-primary;
            border-radius: 3px;
            box-shadow: 0 2px 4px 0 rgba(251,101,106,.1);
        }
    }
}
//公众号二维码弹框
.qrcode-box{
	position: relative;
	margin-top: 15vh;
	.img-box{
		width: 584rpx;
		padding: 12rpx;
		margin: auto;
		background-color: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		.title{
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
			color: #333;
			&.titles{
				margin-top: 36rpx;
			}
		}
		.tip{
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			color: #666;
			&.red{
				padding-bottom: 8rpx;
				color: #f00;
			}
		}
	}
	.qrcode{
		width: 560rpx;
		height: 560rpx;
	}
	.icon-box{
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
}

</style>
