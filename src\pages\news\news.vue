<template>
	<view class="content">
		<view class="banner-box" v-if="adv.length>0">
			<block v-for="(item, idx) in adv" :key="idx">
				<view class="banner-item" @click="toLink(item.link)">
					<banner :image="item.image" mode="widthFix" height="auto"></banner>
				</view>
			</block>
		</view>
		<view class="tipStick" :style="{top:stickTop}">
		<tab-bar :tabs="navs" ref="tab" :fixedTop="false" :nowIndex="currentIndex" @click="handelCate">
		</tab-bar>
		</view>
		<view class="new_list">
			<block v-for="(item, index) in news_list" :key="index">
				<newsItem :item-data="item"></newsItem>
			</block>
		</view>
		<!-- <news :listData="news_list"></news> -->
		<uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
		<chat-tip></chat-tip>
	</view>
</template>

<script>
	import newsItem from "../../components/newsItem.vue"
	import tabBar from "../../components/tabBar.vue"
	import {uniLoadMore} from '@dcloudio/uni-ui'
	import {wxShare} from '../../common/mixin'
	import {formatImg} from '../../common/index'
	import banner from "../../components/banner.vue"
	export default {
		data() {
			return {
				scroll_item:"i",
				cate_id:"",
				navs:[{
					id:"",
					name:"资讯"
				}],
				currentIndex:0,
				news_list:[],
				params:{
					page:1,
					cate_id:"",
				},
				get_status:"loading",
				content_text:{
					contentdown:"",
					contentrefresh:"正在加载...",
					contentnomore:"没有更多数据了"
				},
				adv:[]
			};
		},
		mixins:[wxShare],
		components:{tabBar,newsItem,uniLoadMore,banner},
		onLoad(options){
			for (let key in options){
				this.params[key] = options[key]
			}
			this.getData()
			this.getNav()
		},
		computed: {
			stickTop(){
				// #ifdef MP 
				return this.$store.state.systemInfo.statusBarHeight
				// #endif 
				// #ifndef MP 
				return '44px'
				// #endif 
			}
		},
		methods:{
			getData(){
				this.get_status = "loading"
				if(this.params.page == 1){
					this.news_list = []
				}
				this.$ajax.get("news/index.html",this.params,(res)=>{
					if(res.data.code==1){
						this.news_list = this.news_list.concat(res.data.news)
						this.get_status = "more"
					}else{
						this.get_status = "noMore"
					}
					if(res.data.share){
						this.share = res.data.share
					}else{
						this.share = {}
					}
					this.getWxConfig()
					if(res.data.adv){
						this.adv = res.data.adv
					}
					uni.stopPullDownRefresh();
				},(err)=>{
					uni.stopPullDownRefresh();
				})
			},
			getNav(){
				this.$ajax.get("news/newNav.html",{},(res)=>{
					let navData = res.data.nav.map((item)=>{
						return {id:item.id,name:item.categoryname}
					})
					this.navs = this.navs.concat(navData)
					if(this.params.cate_id){
						let tab_index = this.navs.findIndex(item=>item.id==this.params.cate_id)
						this.$nextTick(()=>{
							this.currentIndex = tab_index
						})
					}
				})
			},
			toLink(url) {
				this.$navigateTo(url)
			},
			handelCate(e){
				this.currentIndex = e.index
				this.newsBid = ""
				this.params.cate_id = e.id
				this.params.page = 1
				this.getData()
			}
		},
		onReachBottom(){
			this.params.page++
			this.getData()
		},
		onPullDownRefresh(){
			this.params.page = 1
			this.getData()
		},
		onShareAppMessage(){
			if(this.share){
				return {
					title: this.share.title||"",
					content:this.share.content||"",
					imageUrl: this.share.pic?formatImg(this.share.pic,'w_6401'):""
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
.new_list{
	padding: 0 48rpx;
	background-color: #fff;
}
.tipStick{
	position: sticky;
	z-index: 10;
}
.banner-box{
		padding: 32upx 48upx;
		background: #fff;
		.banner-item{
			border-radius: 8upx;
			overflow: hidden;
			height: 140upx;
		}
	}
</style>
