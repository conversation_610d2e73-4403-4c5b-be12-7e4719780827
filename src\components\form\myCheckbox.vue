<template>
	<view class="checkbox-box">
		<label for="" v-if="label">{{label}}</label>
		<view class="checkbox-row">
			<view class="checkbox-list flex-box">
				<!-- #ifdef H5 -->
				<view v-for="(item,index) in range" :key="index" class="checkbox-item" :class="judge(item.value)" @click="handelSelect(item.value,index)">{{item.name}}</view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view v-for="(item,index) in range" :key="index" class="checkbox-item" :class="selects.includes(item.value)||JSON.stringify(selects[0])==JSON.stringify(item.value)?'active':''" @click="handelSelect(item.value,index)">{{item.name}}</view>
				<!-- #endif -->
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			label:String,
			name:String,
			values:Array,
			maxnum: {
				type:Number,
				default:0
			},
			disabled:{
				type:[Boolean,Number,String],
				default:false	
			},
			range:Array,
			onlayOne:{ //是否是单选
				type:Boolean,
				default:false
			},

			disabledText:{
				type:[String],
				default:''
			}
		},
		data() {
			return {
				selects:this.values||[]
			};
		},
		// 修复第一次渲染完成以后不再次渲染的bug (房源智能识别通过接口返回识别字段以后不回显)
		watch:{
			values:{
				handler(val){
					this.selects = val
				}
			}
		},
		methods:{
			judge(val){
				if(this.selects.includes(val)||JSON.stringify(this.selects[0])==JSON.stringify(val)){
					return 'active'
				}else{
					return ''
				}
			},
			handelSelect(value,index){
				if (this.disabled){
					if (this.disabledText){
						uni.showToast({
							title:this.disabledText,
							icon:'none'
						})
					}
					console.log(this.disabledText,11);
					
					return
					
				}
				if(this.onlayOne){
					this.selects = [value]
				}else{
					if(!this.selects.includes(value)){
						if(this.maxnum&&this.selects.length>=this.maxnum){
							uni.showToast({
								title: `最多只能选择${this.maxnum}个`,
								icon: 'none'
							})
							return
						}
						this.selects.push(value)
					}else{
						for(let i=0;i<this.selects.length;i++){
							if(this.selects[i] == value){
								this.selects.splice(i,1)
							}
						}
					}
				}
				this.$emit('select', Object.assign({value:this.selects},{_name:this.name}))
			}
		}
	}
</script>

<style lang="scss">
.checkbox-box{
	padding: 24rpx 0;
	label{
		font-size: 22rpx;
		color: #666;
		margin-bottom: 24rpx;
	}
}
	.checkbox-row{
		display: flex;
		align-items: center;
		background-color: #fff;
	}
	.checkbox-row .checkbox-list{
		flex: 1;
		flex-wrap: wrap;
		min-height: 56upx;
		margin-right: 10upx;
	}
	.checkbox-row label{
		min-width: 130upx;
		max-width: 220upx;
		font-size: $uni-font-size-lg;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-right: 38upx;
		text-align-last:justify;
	}
	.checkbox-row .checkbox-list .checkbox-item{
		min-width:15%;
		box-sizing: border-box;
		line-height: 1;
		padding: 12upx 16upx;
		margin: 8upx;
		border-radius: 4upx;
		text-align: center;
		font-size: 28rpx;
		border: 1upx solid #d8d8d8;
		color: #666;
	}
	.checkbox-row .checkbox-list .checkbox-item.active{
		border-color: $uni-color-primary;
		background-color: $uni-color-primary;
		color: #fff;
	}
</style>
