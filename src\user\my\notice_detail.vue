<template>
    <view id="notice">
        <view class="title text-center">{{detail.title}}</view>
        <view class="time">{{detail.time}}</view>
        <view class="detail" v-html="detail.content"></view>
        <chat-tip></chat-tip>
    </view>
</template>

<script>
    export default {
        data(){
            return{
                detail:{
                    title:"",
                    time:"",
                    content:""
                }
            }
        },
        onLoad(options){
            // #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
			uni.hideShareMenu()
			// #endif
            if(options.id){
                this.getData(options.id)
            }else{
                uni.showToast({
                    title: "没有此消息",
                    icon: "none"
                })
            }
        },
        methods:{
            getData(id){
                this.$ajax.get('member/mailDetail',{id},res=>{
                    if(res.data.title){
                        this.detail.title = res.data.title
                        uni.setNavigationBarTitle({
                            title: res.data.title
                        })
                    }
                    if(res.data.code==1){
                        this.detail.content = res.data.content
                        this.detail.time = res.data.time || ""
                    }else{
						uni.showToast({
							title:res.data.msg,
							icon:"none"
						})
					}
                })
            }
        }
    }
</script>

<style lang="scss">
    #notice{
        padding: $uni-spacing-col-lg $uni-spacing-row-lg;
        background-color: #fff;
        .title{
            margin-bottom:30upx;
            font-size: $uni-font-size-blg;
            font-weight: bold;
        }
        .time{
            margin-bottom:20upx;
            font-size: $uni-font-size-sm;
            color: #999;
        }
        .detail{
            font-size: $uni-font-size-lg;
			line-height: 1.6
        }
    }
</style>