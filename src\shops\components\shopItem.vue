.<template>
<view>
    <view class="list flex-box bottom-line">
            <slot name="check_box" :checked="checked"></slot>
            <view class="header_img" @click="$emit('click')">
                <image mode="widthFix" :src="image|imgUrl"></image>
            </view>
            <view class="info flex-box" @click="$emit('click')">
                <view class="shop-title">{{name}}</view>
                <view class="shop-address" v-if="address">
                    {{address}}
                </view>
                <view class="shop-address" v-if="tname">
                    {{tname}}
                </view>
                <view class="shop-info flex-box">
                    <view class="chushou mar-right12" v-if="twice>=0">
                        <text class="sale mar-right8">售</text> 
                        <text class="num">{{twice||0}}套</text>
                    </view>
                    <view class="chuzu mar-right12" v-if ="rent>=0">
                        <text class="sale mar-right8">租</text>
                        <text class="num">{{rent||0}}套</text>
                    </view>
                    <view class="chuzu mar-right12" v-if ="mendian>=0">
                        <text class="sale mar-right8">门店</text>
                        <text class="num">{{mendian}} 个</text>
                    </view>
                    <view class="jignjiren " v-if ="agent>=0">
                        <text class="sale mar-right8">经纪人</text>
                        <text class="num">{{agent||0}}人</text>
                    </view>
                
                </view>
                
            </view>
            <view class="oper" @click.stop.prevent="tel" v-if="fromList">
                <my-icon type="dianhua" size='64upx' color="#FF6735" ></my-icon>
            </view>
    </view>
</view>		
</template>

<script>
import myIcon from '@/components/myIcon'
import  {formatImg} from '@/common/index.js'
export default {
    props:{
        image:'',
        checked:'',
        name:'', 
        twice:0, //出售个数
        rent:0, //出租房个数
        agent:0, // 经纪人个数
        mendian:0,  //门店个数
        address:'', //地址
        tname:"",  //经纪人店铺名称
        fromList:{
            type:Boolean,
            default:true
        }
    },
    components:{
        myIcon
    },
    filters:{
        imgUrl(img, param = "w_120") {
            if (!img) {
                return ""
            }
            return formatImg(img, param)
        },
    },
    methods: {
        tel(){
            this.$emit('tel')
        },
        goDetail(){
            this.$emit('goDetail')
        },
        
    },
}
</script>

<style lang="scss" scoped>
    .mar-right12{
        margin-right: 24upx;
    }
    .mar-right8{
        margin-right: 16upx;
    }
    .list{
        padding:40upx 0 ;
        align-items: center;
        .header_img{
            width: 128upx;
            height: 128upx;
            overflow: hidden;
            border-radius: 8upx;
            margin-right: 24upx;
            align-self: flex-start;
            image{
                width: 100%;
            }
        }
        .info{
            flex-direction: column;
            justify-content: space-between;
            flex: 1;
            max-width: 67%;
            align-self: normal;
            .shop-title{
                font-size: 32upx;
                color: #333;
                // overflow: hidden;
                // text-overflow: ellipsis;
                // white-space: nowrap;
                margin-bottom: 10upx;
            }
            .shop-info{
                margin-top: 10upx;
                .sale{
                    font-size: 28upx;
                    color: #999;
                }
                .num{
                    font-size: 28upx;
                    color: #333;
                }

            }
            .shop-address{
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
    .check-box {
        padding: 32rpx 10rpx;
        margin-right: 10rpx;
        .check {
            width: 32rpx;
            height: 32rpx;
            border: 4rpx solid #dedede;
            border-radius: 50%;
        }
    }
</style>