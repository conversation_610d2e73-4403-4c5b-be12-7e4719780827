<template>
  <view class="page">
    <view class="search-box flex-box">
				<my-icon type="ic_sousuo" @click="handleSearch()" color="#999999" size="46rpx"></my-icon>
				<input
          type="text"
          confirm-type="search"
          v-model="params.title"
          @confirm="handleSearch()"
          placeholder=""
        />
		</view>
    <view class="tab">
      <text :class="{ tab_on : this.params.info_type == 1}" @click="$navigateTo('list')">住宅</text>
      <text :class="{ tab_on : this.params.info_type == 2}" @click="$navigateTo('list?type=2')">商业</text>
    </view>
    <view class="house_list" v-if="listsData.length > 0">
      <template v-if="params.info_type == 1">
        <houseItem
          :itemData="item"
          :type="item.parentid == 1 ? 'ershou' : 'renting'"
          ref="ershou"
          v-for="(item,index) in listsData"
          :key="item.id"
          @renling="renling($event,index)" @browse="browse" @follow="follow" @handleTel="handleTel"
          @used="used" @update="update"
        >
        </houseItem>
      </template>
      <template v-if="params.info_type == 2">
        <listItemItem
          :itemData="item"
          :type="
            item.parentid == 1
              ? 'sale'
              : item.parentid == 2
              ? 'rent'
              : 'transfer'
          "
          ref="ershou"
          v-for="(item,index) in listsData"
          :key="item.id"
          @renling="renling($event,index)" @browse="browse" @follow="follow" @handleTel="handleTel"
          @used="used" @update="update"
        ></listItemItem>
      </template>
    </view>
    <uni-load-more
        :status="get_status"
        :content-text="content_text"
      ></uni-load-more>
    <chat-tip></chat-tip>

  </view>
</template>

<script>
import houseItem from "../components/houseItem.vue";
import listItemItem from "../components/listItemItem.vue";
import myIcon from "../../components/myIcon";
import { uniLoadMore} from "@dcloudio/uni-ui";
import { formatImg } from "../../common/index.js";
export default {
  components: {
    houseItem,
    myIcon,
    uniLoadMore,
    listItemItem,
  },
  data() {
    return {
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了",
      },
      params: {
        page: 1,
        rows: 20,
        info_type: 1,
        title:""
      },
      listsData: [],
    };
  },
  onLoad(options) {
    if (options.type) {
      this.params.info_type = options.type;
    }
    this.getData();
    uni.$on('getDataAgain',()=>{
      this.getData();
    })
  },
  onUnload(){
    uni.$off("getDataAgain")
  },
  filters: {
    imgUrl(val, param = "") {
      return formatImg(val, param);
    },
  },

  methods: {
    getData() {
      this.get_status = "loading";
      if (this.params.page == 1) {
        this.listsData = [];
      }
      this.$ajax.get(
        "infoServicer/infoList",
        this.params,
        (res) => {
          if (res.data.code == 1) {
            if (this.params.page == 1) {
              this.listsData = res.data.list;
            } else {
              this.listsData = this.listsData.concat(res.data.list);
            }
            if (res.data.list.length < this.params.rows) {
              this.get_status = "noMore";
            } else {
              this.get_status = "more";
            }
            if (res.data.share) {
              this.share = res.data.share;
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
          uni.stopPullDownRefresh();
        },
        (err) => {
          this.listsData = [];
          console.log(err);
          uni.stopPullDownRefresh();
        }
      );
    },
    renling(e,index) {
      console.log(e,index);
      this.$ajax.post(
        "infoServicer/claimInfo",
        {
          info_id: e.id,
          info_type: this.params.info_type,
        },
        (res) => {
          if (res.data.code == 1) {
            uni.showToast({
              title: res.data.msg,
              duration: 2000,
            });
            this.params.page=1
            this.getData()
            // this.$set(this.listsData[index],'is_claim',1)
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        }
      );
    },
    browse(e){
      console.log(e,'---');
      if (e.info_level == 0) {
        uni.showToast({
          title: "房源正在审核中，请直接联系发布人",
          icon: "none",
          duration: 2000,
        });
      }else{
        if (this.params.info_type==1) {
          if (e.parentid==1) {
					  this.$navigateTo('/pages/ershou/detail?id='+e.id)
          }else{
					  this.$navigateTo('/pages/renting/detail?id='+e.id)
          }
        }else if(this.params.info_type==2){
          if (e.parentid==1) {
					  this.$navigateTo('/commercial/sale/detail?id='+e.id)
          }else if(e.parentid==2){
					  this.$navigateTo('/commercial/rent/detail?id='+e.id)
          }else{
					  this.$navigateTo('/commercial/transfer/detail?id='+e.id)
          }
        }
      }
    },
    follow(e){
      console.log(e);
      this.$ajax.get(
        "infoServicer/checkClaim",
        {info_type:this.params.info_type,info_id:e.id},
        (res) => {
          if (res.data.code==1) {
            this.$navigateTo('/propertyData/follow/info?type='+this.params.info_type+'&id='+e.id)   
          }else{
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        })
    },
    handleTel(e){
      this.$ajax.post(
        "infoServicer/infoCall",
        {
          info_id: e.id,
          info_type: this.params.info_type,
        },
        (res) => {
           uni.makePhoneCall({
          	phoneNumber: res.data.info.tel
          });
        })
    },
    used(e){
      if (this.params.info_type == 1 ) {
        this.$navigateTo('/user/audit/history?info_type='+this.params.info_type+'&uid='+e.uid+'&info_id='+e.id+'&serve=1')
      }else if(this.params.info_type == 2 ) {
        this.$navigateTo('/commercial/audit/history?info_type='+this.params.info_type+'&uid='+e.uid+'&info_id='+e.id+'&serve=1')
      }
    },
    handleSearch() {
      this.listsData = []
      this.params.page = 1
      this.getData()
    },
    update(e){
      console.log(e);
      this.$ajax.post(
        "infoServicer/proxyRelease",
        {
          info_id: e.id,
          info_type: this.params.info_type,
        },
        (res) => {
          if (res.data.code == 1) {
             uni.showToast({
              title: res.data.msg,
              duration: 2000,
            });
            this.params.page=1
            this.getData()
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        })
    }
  },
  onReachBottom() {
    if (this.get_status == "more") {
      this.params.page = this.params.page + 1;
      this.getData();
    }
  },
};
</script>
<style lang="scss" scoped>

.tab{
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
  text{
    color: #717171;
    width: 90rpx;
    text-align: center;
    padding-bottom: 10rpx;
    font-size: 30rpx;
    border-bottom: 4rpx solid #fff;
  }
  text:first-child{
    margin-right: 50rpx;
  }
  .tab_on{
    border-color: #fb656a;
    color: #fb656a;
  }
}

.page {
  background: #fff;
  min-height: 100vh;
  padding-top: 40rpx;
}

.search-box {
  margin: 0 30rpx;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #f5f5f5;
	color: #999;
  border-radius: 8rpx;
  .search-left {
    margin-right: 20rpx;
  }
	input{
    width: 100%;
    font-size: 28rpx;
    margin-left: 10rpx;
  }
}

.house_list {
  &.house_little_list {
    min-height: 100vh;
  }
  padding: 0 48rpx;
  background-color: #fff;
  .no_data {
    justify-content: center;
    align-items: center;
    padding: 40rpx 0;
    .icon {
      justify-content: center;
      align-items: center;
    }
    .no_data_text {
      font-size: 30rpx;
      color: #333;
      margin-left: 10rpx;
    }
  }
  .xihuan {
    justify-content: center;
    align-items: center;
    padding: 0 0 20rpx;
    .xihuan_text {
      margin: 0 20rpx;
      color: #968e9f;
      font-size: 30rpx;
    }
    .line {
      height: 2rpx;
      background: #e1e3ee;
    }
  }
}
</style>
