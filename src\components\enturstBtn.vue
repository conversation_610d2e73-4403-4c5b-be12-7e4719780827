<template>
  <view>
    <!-- #ifdef MP-WEIXIN -->
    <view class="entrust_btn" v-if="user_login_status>1" @click="$emit('click')">
      <image class="avatar" :src="to_user.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
      <view class="text">委托找房</view>
    </view>
    <button class="entrust_btn" v-else open-type="getUserInfo" @getuserinfo="onGetUserInfo">
      <image class="avatar" :src="to_user.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
      <view class="text">委托找房</view>
    </button>
    <!-- #endif -->
    <!-- #ifndef MP-WEIXIN || MP-BAIDU -->
    <view class="entrust_btn" @click="$emit('click')">
      <image class="avatar" :src="to_user.prelogo | imageFilter('w_80')" mode="aspectFill"></image>
      <view class="text">委托找房</view>
    </view>
    <!-- #endif -->
  </view>
</template>

<script>
import {mapState} from 'vuex'
export default {
  components: {},
  data () {
    return {}
  },
  props:{
    to_user: {
      type:[Object, Array],
      default: ()=>{return {}}
    }
  },
  computed:{
    ...mapState(['user_login_status']),
  },
  methods: {
    onGetUserInfo(res){
      if(res.target.userInfo&&res.target.userInfo.avatarUrl&&res.target.userInfo.nickName){
          uni.login({
          provider: 'weixin',
          success: (loginRes)=> {
            this.getToken(loginRes.code,res.target.userInfo.avatarUrl,res.target.userInfo.nickName)
          }
          })
      }else{
          uni.removeStorageSync('token')
          this.$navigateTo('/user/login/login')
      }
    },
    getToken(code,avatarUrl,nickName){
      this.$ajax.get("member/getOpenidByCode.html",{code,headimgurl:avatarUrl,nickname:nickName},(res)=>{
          if(res.data.code ==1){
            // 存储token
            uni.setStorageSync('token',res.data.token)
            uni.showToast({
                title:'授权成功'
            })
            if(res.data.tel){
              this.$store.state.user_login_status = 3
            }else{
              this.$store.state.user_login_status = 2
            }
             this.$emit('click')
          }else{
            uni.showToast({
              title:res.data.msg,
              icon:"none"
            })
          }
      })
    },
  }
}
</script>

<style scoped lang="scss">
.entrust_btn{
  position: fixed;
  right: 48rpx;
  bottom: 360rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 130rpx;
  z-index: 51;
  .avatar{
    width: 84rpx;
    height: 84rpx;
    border-radius: 50%;
    border: 6rpx solid $uni-color-primary;
    box-shadow: 0 6rpx 10rpx 0 rgba($color: $uni-color-primary, $alpha: 0.2);
  }
  .text{
    position: absolute;
    bottom: -20rpx;
    height: 32rpx;
    padding: 0 16rpx;
    border-radius: 18rpx;
    text-align: center;
    font-size: 22rpx;
    color: #fff;
    background-color: $uni-color-primary;
    box-shadow: 0 6rpx 10rpx 0 rgba($color: $uni-color-primary, $alpha: 0.2);
  }
}
</style>