<template>
  <view>
    <view class="house_list">
      <block v-for="item in list" :key="item.id">
        <house-item :itemData="item" @click="onClick"></house-item>
      </block>
    </view>
    <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
  </view>
</template>

<script>
import houseItem from '../components/houseItem'
	import {uniLoadMore} from "@dcloudio/uni-ui";
export default {
  components: {
    houseItem,
    uniLoadMore
  },
  data() {
    return {
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了"
      },
      list: [],
      params:{
        page: 1,
        cate_id: '',
        rows: 20
      }
    }
  },
  onLoad(options){
    this.params.cate_id = options.type || 1
    this.no_login = options.no_login || 0
    if(this.no_login){
      uni.setNavigationBarTitle({
        title:"房源列表"
      })
      this.getHouse()
    }else{
      this.getCollect()
    }
  },
  methods: {
    getHouse(){
      this.get_status = "loading";
      this.$ajax.get('house/index.html', this.params,res=>{
        if (res.data.code == 1) {
          this.get_status = "more";
          this.list = this.list.concat(res.data.house);
          if(res.data.house.length<this.params.rows){
            this.get_status = "noMore";
          }
        } else {
          this.get_status = "noMore";
        }
      },err => {
        console.log(err);
      })
    },
    getCollect(){
      this.get_status = "loading";
      this.$ajax.get('member/myCollect.html', this.params,res=>{
        if (res.data.code == 1) {
          this.get_status = "more";
          this.list = this.list.concat(res.data.list);
          if(res.data.list.length<this.params.rows){
            this.get_status = "noMore";
          }
        } else {
          this.get_status = "noMore";
        }
      },err => {
        console.log(err);
      })
    },
    onClick(e){
      this.$ajax.get('house/addContrast.html',{info_id: e.detail.id},res=>{
        if(res.data.code === -1){
          uni.$emit('replaceInfo',e.detail.id)
          this.$navigateBack()
          return
        }
        if(res.data.id){
          uni.$emit('replaceInfo',res.data.id)
          this.$navigateBack()
        }else{
          uni.showToast({
            title:res.data.msg,
            icon:'none'
          })
        }
      },err=>{

      },{disableAutoHandle:true})
    }
  },
  onReachBottom(){
    if(this.get_status !== "more"){
      return
    }
    this.params.page ++
    if(this.no_login){
      this.getHouse()
    }else{
      this.getCollect()
    }
  }
}
</script>

<style scoped lang="scss">
.house_list{
  padding: 0 48rpx;
  background-color: #fff;
}
</style>
