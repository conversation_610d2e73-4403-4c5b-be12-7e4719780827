<template>
  <view :style="{backgroundColor:bgColor}">
    <view class="top">
      <image v-if ="hb_info.bg_img" :src="hb_info.bg_img " mode="widthFix"></image>
      <image v-else  :src="top_img" mode="widthFix"></image>
      <!-- <view class="guize" @click="$refs.guize_popup.show()">
        <text>规则</text>
        <image :src="guize_img" mode="aspectFit"></image>
      </view> -->
      <view class="share" @click="openShare()">
        <image :src="share_img" mode="aspectFit"></image>
      </view>
      <view class="info" :class="{'zixun': info_type == 4}">
        <view class="info-title" v-if="info_type == 1">红包楼盘</view>
        <view class="info-title" v-if="info_type == 2 || info_type == 3">红包房源</view>
        <view class="info-title" v-if="info_type == 4">红包资讯</view>
        <newsItem :item-data="info" v-if="info_type == 4"></newsItem>
        <view class="info-content" @click="toDetail()" v-else>
          <view class="info-content-left">
            <image :src="info.img" mode="aspectFill"></image>
          </view>
          <view class="info-content-right" v-if="info_type == 1">
            <view class="info-name">{{info.title}}</view>
            <view class="info-area">{{info.areaname}}</view>
            <view class="info-price">{{info.build_price!='一房一价'?info.price_type:''}}<text>{{info.build_price}}{{info.price_unit}}</text></view>
          </view>
          <view class="info-content-right" v-if="info_type == 2 || info_type == 3">
            <view class="info_title">{{info.title}}</view>
            <view class="info-area_cont flex-row">
              <view class="area-c">
                {{info.areaname}} {{info.cmname}}
              </view>
              <view class="info-price">
                <text>{{info_type == 2 ? info.fangjia || info.zujin : info.price}}{{info.price_unit}}</text>
                <text v-if="info_type == 2 && info.parentid == 1">万</text>
                <text v-if="info_type == 2 && info.parentid == 2">元/月</text>
              </view>
            </view>
            <!-- <view class="info-price">
              <text>{{info_type == 2 ? info.fangjia || info.zujin : info.price}}{{info.price_unit}}</text>
              <text v-if="info_type == 2 && info.parentid == 1">万</text>
              <text v-if="info_type == 2 && info.parentid == 2">元/月</text>
            </view> -->
          </view>
          <view class="info-content-right" v-if="info_type == 4">
            <view class="info-name">{{info.title}}</view>
            <view class="info-area">{{info.ctime}}</view>
          </view>
        </view>
        <view class="info-bottom" v-if="info_type < 4">
          <view class="info-bottom-left" @click="toDetail()">
            <!-- <image :src="map_img" mode="aspectFit"></image> -->
            <text>查看详情</text>
          </view>
          <view class="info-bottom-right" @click="handleTel()">
            <image :src="phone_img" mode="aspectFit"></image>
            <text>电话咨询</text>
          </view>
        </view>
      </view>
    </view>
    <view class="center">
      <image :src="hongbao_img" mode="widthFix"></image>
      <view class="top-box">
        <view class="cont">红包共<text class ="red">{{hb_info.hb_count}}</text>份</view>
        <view class="cont">已领取<text class ="red">{{hb_info.get_count}}</text>份</view>
        <view class="cont">剩余<text class ="red">{{hb_info.hb_residue_count}}</text>份</view>
        <!-- <view class="box-item" @click="toAdd()">
          <view class="num">{{hb_info.hb_count}}</view>
          <view class="desc">红包份数</view>
        </view>
        <view class="box-line"></view>
        <view class="box-item">
          <view class="num">{{hb_info.get_count}}</view>
          <view class="desc">已领取</view>
        </view>
        <view class="box-line"></view>
        <view class="box-item">
          <view class="num">{{hb_info.hb_residue_count}}</view>
          <view class="desc">剩余份数</view>
        </view> -->
      </view>
      
      <view v-if ="task&&task.id" class="ling-hb" @click="checkHb()">领取{{task.money}}元红包</view>
      <view v-else class="ling-hb ling-hb-bottom" @click="createHb()">领红包</view>
      <!-- <view v-if="is_sai_open" class="sai-hb" @click="toAdd()">
        <image :src="sai_img" mode="widthFix"></image>
        <view>我要塞红包</view>
      </view> -->
      <view v-if ="task&&task.id" class="zhuli" >
        <view class="person">
          <view v-for="(item, index) in task.help_members" :key="index">
            <image v-if="item.prelogo" :src="item.prelogo" class="zhuli-none" mode="aspectFill"></image>
            <view v-else class="zhuli-none" @click="openShare()">
              <my-icon type="ic_jia" color="#c4c4c4" size="44rpx"></my-icon>
            </view>
          </view>
        </view>
        <view class="zhuli-bottom">
          <view class="zhuli-line"></view>
          <view class="zhuli-text">助力好友</view>
          <view class="zhuli-line"></view>
        </view>
        <view class="zhuli-time">
          <text v-if="expire_seconds > 0">{{expire_seconds | secondsFormat}}后失效</text>
          <!-- <text v-else-if ='task&&task.active_time'>{{task.active_time | secondsFormat}}后激活</text> -->
          <text v-else>已失效</text>
        </view>
      </view>
    </view>
    <view class="bottom">
      <view class="bottom-info" v-if="hb_info.introduce">
        <!-- <view class="bottom-title">商家信息</view> -->
        <!-- <template v-if="info.focus && info.focus.length > 0">
          <image :class="{'first': index == 0}" :src="item" mode="widthFix" v-for="(item, index) in info.focus" :key="index"></image>
        </template>
        <view v-if="info_type == 4" v-html="info.descp" class="descp"></view> -->
        <view class="article-content" v-html="hb_info.introduce"></view>
      </view>
      <view class="logs">
        <view class="bottom-title">红包记录</view>
        <view class="list-top">
          <view class="list-btn" :class="{ active: list_type == 'lingqu' }" @click="listChange('lingqu')">{{get_count}}人已参与</view>
          <view class="list-btn" :class="{ active: list_type == 'shoudao' }" @click="listChange('shoudao')">塞红包记录</view>
          
        </view>
        <view class="list-content">
          <template v-if="list_type == 'shoudao'">
            <view class="list-item" v-for="(item, index) in saiList" :key="index">
              <view class="list-info">
                <view class="info-line">
                  <image :src="item.prelogo" mode="aspectFill"></image>
                  <text class="info-name">{{item.username}}</text>
                </view>
                <view class="list-msg">
                  <text>塞了</text>
                  <text class="msg-money">{{item.sai_money}}元</text>
                  <text>红包</text>
                </view>
              </view>
              <view class="list-other sai-time">{{item.sai_time}}</view>
            </view>
          </template>
          <template v-if="list_type == 'lingqu'">
            <view class="list-item" v-for="(item, index) in lingList" :key="index">
              <view class="list-info">
                <view class="info-line">
                  <image :src="item.prelogo" mode="aspectFill"></image>
                  <text class="info-name">{{item.username}}领取红包</text>
                </view>
                <view class="list-msg">
                  <text>{{item.get_money}}元</text>
                </view>
              </view>
              <view class="list-other">
                <!-- <text class ="status" >{{item.get_status}}</text> -->
                <text>{{item.get_time}}</text> 
                <!-- 0 进行中  2 失效 1  完成-->
                <text class = "item_status" :class = "'item_status' + item.status">{{item.status_title}}</text>
              </view>
            </view>
          </template>
          <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
        </view>
      </view>
    </view>
    <!-- <my-popup ref="guize_popup" position="top">
      <view class="guize-popup" :style="{backgroundImage: `url(${image_domain}/hongbao/guize_bg.png)`}">
        <view class="guize-box">
          <view class="guize-title">
            <view class="guize-line"></view>
            <text class="guize-text">红包规则</text>
            <view class="guize-line"></view>
          </view>
          <view class="guize-content">
            <view class="guize-tips">时间</view>
            <view class="guize-date">{{hb_config.start_time}} 至 {{hb_config.end_time}}</view>
            <view class="guize-tips">详情信息</view>
            <view class="guize-info" v-html="hb_config.introduce"></view>
          </view>
          <view class="guize-close" @click="$refs.guize_popup.hide()">
            <my-icon type="guanbi" color="#fff" size="56rpx"></my-icon>
          </view>
        </view>
      </view>
    </my-popup> -->
    <my-popup ref="linghb_popup" position="top">
      <view class="ling-popup">
        <view class="ling-popup-top">
          <view class="ling-top-text">拼手气红包</view>
        </view>
        <view class="ling-popup-bottom">
          <view class="ling-btn" @click="lingHb">開</view>
        </view>
        <view class="ling-popup-close" @click="$refs.linghb_popup.hide()">
          <my-icon type="guanbi" color="#fff" size="56rpx"></my-icon>
        </view>
      </view>      
    </my-popup>
    <share-pop ref="share_popup" :isHongbao="true" :showHaibao="false" @copyLink="show_share_tip=true" @appShare="appShare" @handleCreat='handleCreat'></share-pop>
    <shareTip :show="show_share_tip" tip_text="点击右上角，分享给好友" @hide="show_share_tip = false"></shareTip>
    <sub-form ref="sub_form" @onsubmit="handleSubForm" sub_submit="提交" sub_title="领取联系人" @close="sub_close"></sub-form>
  </view>
</template>

<script>
import { config } from '../common/index'
import myPopup from '../components/myPopup.vue'
import myIcon from '../components/myIcon.vue'
import sharePop from '../components/sharePop'
import shareTip from '../components/shareTip.vue'
import newsItem from "./components/newsItem.vue"
import allTel from '../common/all_tel.js'
import { uniLoadMore } from '@dcloudio/uni-ui'
import getLocation from './get_location'
import subForm from '@/components/subForm'
export default {
  components: { myPopup, myIcon, sharePop, uniLoadMore, shareTip, newsItem, subForm },
  data() {
    return {
      image_domain: config.imgDomain,
      top_img: config.imgDomain + '/hongbao/help_banner.png?x-oss-process=style/w_6401',
      guize_img: config.imgDomain + '/hongbao/guize.png?x-oss-process=style/m_240',
      share_img: config.imgDomain + '/hongbao/share_btn.png?x-oss-process=style/m_240',
      hongbao_img: config.imgDomain + '/hongbao/hongbao_bg.png?x-oss-process=style/m_240',
      map_img: config.imgDomain + '/hongbao/map_icon.png?x-oss-process=style/m_240',
      phone_img: config.imgDomain + '/hongbao/phone_icon.png?x-oss-process=style/m_240',
      sai_img: config.imgDomain + '/hongbao/sai_bg.png?x-oss-process=style/m_240',
      saihongbao_gif: config.imgDomain+'/hongbao/saihongbao.gif',
      list_type: 'lingqu',
      id: '',
      type: '',
      task_id: '',
      info_type: '',  //1楼盘2住宅信息3商业信息4资讯
      limit_area: '',
      rows: 10,
      page: 1,
      saiList: [],
      lingList: [],
      get_status: 'loading',
      content_text: {
        contentdown: '',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了',
      },
      info: {},
      hb_config: {},
      hb_info: {},
      task: {},
      hb_share_query: '',
      // is_sai_open: false,
      limit_area: '',
      map_key: '',
      lat: '',
      lng: '',
      current_city: '',
      sid: '',
      shareType: '',
      user_id: '',
      share_id: '',
      show_share_tip: false,
      expire_seconds: 0,
      // bgColor: '#DE3A29',
      bgColor: '#fe483d',
      get_count:0,
      tuiguang_mp3:"/static/icon/voice/tuiguang_bg.mp3"
    }
  },
  onLoad(options) {
    // this.task.id = options.task_id
    // this.type = options.type  //新房 1  二手房 2  租房 3  商售 16  商租 17  商转 18  资讯 4
    // switch(this.type) {
    //   case '1': this.info_type = 1; break; 
    //   case '2': this.info_type = 2; break; 
    //   case '3': this.info_type = 2; break; 
    //   case '4': this.info_type = 4; break; 
    //   case '16': this.info_type = 3; break; 
    //   case '17': this.info_type = 3; break; 
    //   case '18': this.info_type = 3; break;
    //   default: break;
    // }
    this.info_id = options.info_id
    this.info_type = options.info_type
    if (options.sid) {
      this.sid = options.sid
    }
    if (options.share_id) {
      this.share_id = options.share_id
    }
    if (options.task_id) {
      this.task_id = options.task_id
    }
    if (options.shareType) {
      this.shareType = options.shareType
    }
    if (options.hb_share_query) {
      this.hb_share_query = options.hb_share_query
    }
    if (options.code ){
      // this.code = options.code
      this.toLogin(options.code)
    }else {
      this.getData()
    }
    
  },
  onUnload(){
      if (this.innerAudioContext){
          this.innerAudioContext.destroy()
      }
  },
  filters: {
    secondsFormat(val){
      let day = Math.floor(val / 86400)
      let hour = Math.floor((val % 86400) / 3600)
      let minute = Math.floor(((val % 86400) % 3600) / 60)
      let second = ((val % 86400) % 3600) % 60
      let str = ''
      if (day > 0) {
        str = str + (day < 10 ? '0' + day : day) + '天'
      }
      if (hour > 0 || day > 0) {
        str = str + (hour < 10 ? '0' + hour : hour) + ':'
      }
      if (minute > 0 || hour > 0 || day > 0) {
        str = str + (minute < 10 ? '0' + minute : minute) + ':'
      }
      if (second > 0 || minute > 0 || hour > 0 || day > 0) {
        str = str + (second < 10 ? '0' + second : second)
      }
      return str
    }
  },
  methods: {
    getData() {
      this.$ajax.get('WxMoney/helpTaskDetail', {info_id: this.info_id, info_type: this.info_type,task_id:this.task_id}, (res) => {
        if (res.data.user){
          this.userType =res.data.user
          if (this.userType.is_adviser==1) {
            this.user_id =this.userType.adviser
            this.shareType =1
          }else if (this.userType.is_agent==1){
            this.shareType =2
            this.user_id =this.userType.id 
          }else {
            this.shareType =3
            this.user_id =this.userType.id 
          }
        }
        if (res.data.code == 1) {
          // this.hb_info.sai_count = res.data.hb_info.sai_count
          // this.hb_info.sai_get_count = res.data.hb_info.sai_get_count
          // this.hb_info.sai_get_money = res.data.hb_info.sai_get_money
          // this.hb_config.introduce = res.data.hb_config.introduce
          // this.start_time = res.data.start_time
          // this.hb_config.end_time = res.data.hb_config.end_time
          // this.is_sai_open = res.data.is_sai_open
          // this.limit_area = res.data.limit_area
         
          this.map_key = res.data.txmapwapkey
          // this.user_id = res.data.uid
          this.info = res.data.info
          this.hb_info = res.data.hb_info
          this.get_count =  this.hb_info.task_count?this.hb_info.task_count:0
          this.task = res.data.task||{}
          // this.task = null
          if (this.task && this.task.id) {
            this.expire_seconds = this.task.expire_time - parseInt(new Date().getTime()/1000)
            this.timeDownStart()
            for( let i=0; i<this.task.help_count-this.task.completed_count; i++ ) {
              this.task.help_members.push({})
            }
            this.hb_share_query = res.data.hb_share_query
            this.bgColor = '#DE3A29'
          }
          this.info_type = this.hb_info.info_type
          this.type = this.info_type
          if (this.info_type == 3 && this.info.parentid == 1) {
            this.type = 16
          }
          if (this.info_type == 3 && this.info.parentid == 2) {
            this.type = 17
          }
          if (this.info_type == 3 && this.info.parentid == 3) {
            this.type = 18
          }
          if (this.info_type == 2 && this.info.parentid == 2) {
            this.type = 3
          }
          this.listChange(this.list_type)
          this.share = {
            title: res.data.share.title || '',
            content: res.data.share.content || '',
            pic: res.data.share.pic || ''
          }
          let link1="https://"+window.location.host+"/h5/pages/new_house/detail"
          let link2="https://"+window.location.host+"/h5/pages/ershou/detail"
          let link3="https://"+window.location.host+"/h5/pages/renting/detail"
          let link4="https://"+window.location.host+"/h5/pages/news/detail"
          let link16="https://"+window.location.host+"/h5/commercial/sale/detail"
          let link17="https://"+window.location.host+"/h5/commercial/rent/detail"
          let link18="https://"+window.location.host+"/h5/commercial/transfer/detail"
          let time =parseInt(+new Date()/1000)
          switch (this.type) {
            case 1:
              this.share.link = link1
              break;
            case 2:
              this.share.link = link2
              break;
            case 3:
              this.share.link = link3
              break;
            case 4:
              this.share.link = link4
              break;
            case 16:
              this.share.link = link16
              break;
            case 17:
              this.share.link = link17
              break;
            case 18:
              this.share.link = link18
              break;
            default:
              break;
          }
          // this.share.link = "link"+this.type
          if (this.user_id){
            this.share.link +="?id="+this.info.id+"&isShare=1&shareType="+this.shareType+"&shareId="+this.user_id+'&f_time='+time+"&share_type="+this.type+"&info_type="+this.info_type
          }else {
            this.share.link +="?id="+this.info.id+"&info_type="+this.info_type
          }
          if (this.hb_share_query) {
            this.share.link += `&${this.hb_share_query}`
            
          }
          // this.share.path =  this.share.link.split(window.location.host +'/h5')[1]
          if (this.hb_info.limit_area) {
            this.getWxConfig(['getLocation','updateAppMessageShareData','updateTimelineShareData'], (wx)=>{
              this.wx = wx
              // this.getCity()
            })
          } else {
            this.getWxConfig()
          }
          
        } else if (res.data.code === 101) {
          var redirect_uri =encodeURIComponent(location.href) 
          var url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + res.data.appid + "&redirect_uri=" + redirect_uri + "&response_type=code"  + "&scope=snsapi_base" + "&state=STATE#wechat_redirect";
          // window.location.href = url;
          window.location.replace(url)
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    getSaiList() {
      this.get_status = 'loading'
      let params = {
        rows: this.rows,
        page: this.page,
        info_id: this.info_id,
        info_type:this.info_type
      }
      this.$ajax.get('WxMoney/saiLog', params, (res) => {
        if (res.data.code == 1) {
          if (params.page == 1) {
            this.saiList = []
          }
          this.saiList = this.saiList.concat(res.data.list)
          if (res.data.list.length < this.rows) {
            this.get_status = 'noMore'
          } else {
            this.get_status = 'more'
          }
        }
      })
    },
    getLingList() {
      this.get_status = 'loading'
      let params = {
        rows: this.rows,
        page: this.page,
        info_id: this.info_id,
        info_type:this.info_type
      }
      this.$ajax.get('WxMoney/getLog', params, (res) => {
        if (res.data.code == 1) {
          if (params.page == 1) {
            this.lingList = []
          }
          this.lingList = this.lingList.concat(res.data.list)
          if (res.data.list.length < this.rows) {
            this.get_status = 'noMore'
          } else {
            this.get_status = 'more'
          }
        }
      })
    },
    listChange(name) {
      this.list_type = name
      this.page = 1
      if (this.list_type == 'shoudao') {
        this.getSaiList()
      } else if (this.list_type == 'lingqu') {
        this.getLingList()
      }
    },
    toAdd() {
      this.$navigateTo(`/redEnvelopes/add?id=${this.info.id}&type=${this.info_type}`)
    },
    handleCreat() {
      this.$navigateTo(`${location.origin}/wapi/poster/branch?type=${this.type}&id=${this.info.id}&header_from=2&is_hb=1`)
    },
    viewMap() {
      let type = this.info_type
      if (type == 3) {
        type = 5
      }
      if (this.info.yzhou > 0 && this.info.xzhou > 0) {
        this.$navigateTo(`/propertyData/map/map?id=${this.info.id}&type=${type}&lat=${this.info.yzhou}&lng=${this.info.xzhou}`)
      } else if (this.info.lat > 0 && this.info.lng > 0) {
        this.$navigateTo(`/propertyData/map/map?id=${this.info.id}&type=${type}&lat=${this.info.lat}&lng=${this.info.lng}`)
      } else {
        uni.showToast({
          title: '未标记地图位置',
          icon: 'none'
        })
      }
    },
    createHb(){
      if (!this.playing) this.playAudio() 
      this.openHb_new()
      // let form = {
      //   info_id: this.info_id,
      //   info_type: this.info_type,
      // }
      // this.$ajax.post('WxMoney/createhb', form, res => {
      //   if (res.data.code == 1) {
      //     this.hb_result = res.data.hb_result
      //     this.openHb_new()
      //     // this.$nextTick(()=> {
      //     //   this.timeDownStart()
      //     //   this.$refs.hongbao.showPopup()
      //     // })
      //   } else if (res.data.help_fail_desc){
      //     uni.showToast({
      //       title: res.data.help_fail_desc,
      //       icon: 'none'
      //     })
      //   }
      // })
    },
    checkHb() {
      if (!this.playing) {
        this.playAudio()
      }
      if (this.hb_info.limit_area) {
        if (!this.current_city) {
          uni.showLoading({
            title: '获取位置信息中，请稍等'
          });
          this.getCity({
            success: () => {
              uni.hideLoading()
              this.checkWithdrawHb({id: this.task.id, area: this.current_city})
            }, fail: (err) => {
              console.log(err)
              uni.hideLoading()
              this.checkWithdrawHb({id: this.task.id, area: this.current_city})
            }
          })
        } else {
          this.checkWithdrawHb({id: this.task.id, area: this.current_city})
        }
      } else {
        this.checkWithdrawHb({id: this.task.id})
      }
    },
    checkWithdrawHb(params) {
      this.$ajax.post('WxMoney/checkWithdrawHb', params, (res) => {
        if (res.data.code ==101){
          var redirect_uri =encodeURIComponent(location.href) 
          var url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + res.data.appid + "&redirect_uri=" + redirect_uri + "&response_type=code"  + "&scope=snsapi_base" + "&state=STATE#wechat_redirect";
          // window.location.href = url;
          window.location.replace(url)
          return 
        }
        if (res.data.code == 1) {
          if (this.hb_info.is_need_fill_tel == 1) {
            this.$refs.sub_form.showPopup()
          } else {
            this.withdrawHb(params)
          }
        } else {
          if (res.data.active_time ) this.expire_seconds = res.data.active_time- parseInt(new Date().getTime()/1000)
          uni.showToast({
              title: res.data.msg,
              icon: 'none'
          })
        }
      })
    },
    withdrawHb(params) {
      this.$ajax.post('WxMoney/withdrawHb', params, (res) => {
        if (res.data.code == 1) {
          this.$navigateTo(`/redEnvelopes/money?money=${this.task.money}`)
        } else {
          if (res.data.active_time ) this.expire_seconds = res.data.active_time- parseInt(new Date().getTime()/1000)
          uni.showToast({
              title: res.data.msg,
              icon: 'none'
          })
        }
      })
    },
    // checkHb() {
    //   if (this.limit_area && !this.current_city) {
    //     this.getCity({
    //       success: () => {
    //         this.getHb(true)
    //       }, fail: (err) => {
    //         console.log(err)
    //         this.getHb(true)
    //       }
    //     })
    //   } else {
    //     this.getHb(true)
    //   }
    // },
    lingHb() {
      if (this.limit_area && !this.current_city) {
        this.getCity({
          success: () => {
            this.getHb(false)
          }, fail: (err) => {
            console.log(err)
            this.getHb(false)
          }
        })
      } else {
        this.getHb(false)
      }
    },
    getHb(isCheck) {
      let params = {
        info_id: this.info.id,
        info_type: this.info_type,
        area: this.current_city,
        lat: this.lat,
        lng: this.lng,
      }
      if (this.sid) {
        params.sid = this.sid
      }
      if (this.share_id) {
        params.share_id = this.share_id
      }
      let postSrc = ''
      if (isCheck) {
        postSrc = 'WxMoney/checkGetHb'
      } else {
        postSrc = 'WxMoney/gethb'
      }
      this.$ajax.post(postSrc, params, (res) => {
          if (res.data.code == 1) {
            if (isCheck) {
              this.$refs.linghb_popup.show()
            } else {
              this.openHb(res.data.money)
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
          }
      })
    },
    openHb(money) {
      this.$navigateTo(`/redEnvelopes/money?money=${money}`)
      this.$refs.linghb_popup.hide()
    },
    openShare() {
      if (!this.playing) this.playAudio()
      this.$refs.share_popup.show()
    },
    handleTel() {
      if (this.info_type == 1) {
        this.buildHandleTel()
      }
      if (this.info_type == 2 || this.info_type == 3) {
        this.houseHandleTel()
      }
    },
    buildHandleTel() {
      // 如果关闭显示楼盘电话且有置业顾问则拨打置业顾问电话，否则拨打楼盘电话
      if(this.info.use_middle_number===0&&this.info.open_adviser == 1){
        // &&this.consuList.length > 0
        console.log("关闭显示楼盘电话且有置业顾问")
        // this.callAdviserMiddleNumber()
      }else{
        console.log("开启显示楼盘电话或没有置业顾问")
        // 如果没开启虚拟号
        this.callBuildMiddleNumber()
      }
    },
    // 拨打楼盘虚拟号码
    callBuildMiddleNumber() {
      console.log('拨打楼盘虚拟号码')
      let phoneNumber=""
      if (this.info.sellMobile) {
        phoneNumber = this.info.sellMobile
      } else if (this.info.phone && this.info.sellmobile_part) {
        phoneNumber = this.info.phone + ',' + this.info.sellmobile_part.trim()
        if (this.tel400jing) {
          phoneNumber += '#'
        }
      }
      this.callMiddleNumber(1,this.info.id,1,this.info.id)
    },
    // 请求虚拟号接口
    callMiddleNumber(type,callee_id,scene_type,scene_id,source,bid) {
      this.tel_params = {
        type,
        callee_id,
        scene_type,
        scene_id,
        source,
        bid,
        success: (res)=>{
          this.tel_res = res.data
          this.show_tel_pop = true
        }
      }
      // #ifdef MP-WEIXIN
      allTel(this.tel_params)
      // #endif
      // #ifndef MP-WEIXIN
      // 全局开启中间号且楼盘开启中间号需要检测登录
      if(this.is_open_middle_num == 1 && this.info.use_middle_call > 0){
        this.tel_params.intercept_login = true
        this.tel_params.fail = (res)=>{
          if(res.data.code === -1){
            this.$store.state.user_login_status = 1
            this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
          }
          if(res.data.code === 2){
            this.$store.state.user_login_status = 2
            this.showLoginPopup('当前操作需要绑定手机号，请输入您的手机号')
          }
        }
        allTel(this.tel_params)
      }else{
        allTel(this.tel_params)
      }
      // #endif
    },
    houseHandleTel() {
      if (this.info.shixiao === 1) {
        uni.showToast({
          title: "此信息已失效",
          icon: 'none'
        })
        return false
      }
      this.tel_params={
          type: 4,
          callee_id: this.info_id,
          scene_type: 4,
          scene_id: this.info_id,
      }
      if (this.info_type == 3) {
        this.tel_params.type = 6
        this.tel_params.scene_type = 6
      }
      this.tel_params.intercept_login = true
      this.tel_params.success = (res)=>{
        // console.log(res)
        this.tel_res = res.data
        this.show_tel_pop = true
      }
      this.tel_params.fail = (res)=>{
        switch(res.data.code){
          case -1:
            this.reload = true
            this.$navigateTo('/user/login/login')
            break
          case 2:
            this.reload = true
            // #ifdef H5 || APP-PLUS || MP-BAIDU
            this.$navigateTo('/user/login/login')
            // #endif
            // #ifdef MP-WEIXIN
            this.$navigateTo('/user/bind_phone/bind_phone')
            // #endif
            break
          case -5:
            showModal({
              title: "安全验证，防恶意骚扰已开启",
              content: "验证后可免费发布查看信息。",
              confirm: () => {
                if (res.data.is_agent){
                  this.$navigateTo('/user/member_upgrade')
                }else{
                  this.$navigateTo('/user/member_upgrade?is_personal=1')
                }
              }
            })
            break
          case -10:
            console.log("账号被封禁")
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
            break
          default:
            uni.showToast({
              title: res.data.msg,
              icon: 'none'
            })
        }
      }
      allTel(this.tel_params)
    },
    async playAudio(){
        this.innerAudioContext = uni.createInnerAudioContext();
        // this.innerAudioContext.autoplay = true;
        this.innerAudioContext.loop = false;
        this.innerAudioContext.src = this.tuiguang_mp3;
        // this.innerAudioContext.pause()
        this.innerAudioContext.onPlay(() => {
            console.log('开始播放');
            this.playing = true
        });
        this.innerAudioContext.onEnded(() => {
            console.log('播放结束');
            this.playing = false
        });
        this.innerAudioContext.onError((res) => {
            console.log("播放失败")
            console.log(res.errMsg);
            console.log(res.errCode);
        });
        this.innerAudioContext.play()
    },
    // #ifdef H5
    copyLink(){
      let link = '';
      let time =parseInt(+new Date()/1000)
      if (this.user_id){
        link="https://"+window.location.host+"/h5/pages/new_house/detail?id="+this.info.id +"&isShare=1&shareType="+this.shareType+"&shareId="+this.user_id+'&f_time='+time
      }else {
        link="https://"+window.location.host+"/h5/pages/new_house/detail?id="+this.info.id
      }
      if (this.hb_share_query) {
        link += `&${this.hb_share_query}`
      }
      this.copyWechatNum(link, ()=>{
        uni.showToast({
          title: '复制成功,去发送给好友吧',
          icon: 'none'
        })
      })
    },
    // #endif
    // #ifndef H5
    copyWechatNum(cont) {
      uni.setClipboardData({
        data: cont,
        success: res => {
          // uni.showToast({
          //   title: "复制成功",
          //   icon: "none"
          // })
        }
      })
    },
    // #endif
    // #ifdef H5
    copyWechatNum(cont, callback) {
      let oInput = document.createElement('textarea')
      oInput.value = cont
      document.body.appendChild(oInput)
      oInput.select() // 选择对象;
      oInput.setSelectionRange(0, oInput.value.length);
      document.execCommand('Copy') // 执行浏览器复制命令
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
      oInput.blur()
      oInput.remove()
      if(callback) callback()
    },
    // #endif
    toDetail() {
      let link = ''
      switch(this.type) {
        case 1: link = '/pages/new_house/detail'; break;
        case 2: link = '/pages/ershou/detail'; break;
        case 3: link = '/pages/renting/detail'; break;
        case 4: link = '/pages/news/detail'; break;
        case 16: link = '/commercial/sale/detail'; break;
        case 17: link = '/commercial/rent/detail'; break;
        case 18: link = '/commercial/transfer/detail'; break;
        default: break;
      }
      if (link) {
        this.$navigateTo(`${link}?id=${this.info.id}`)
      }
    },
    getCity(options={}){
      this.$store.state.getPosition(this.wx, (res)=>{
        this.lat = res.lat
        this.lng = res.lng
        getLocation({
          latitude: res.lat,
          longitude: res.lng,
          map_key: this.map_key||'',
          success: res=>{
            this.current_city = res.city
            options.success && options.success(res)
          },
          fail: err=>{
            console.log(err)
            options.fail && options.fail(err)
          }
        })
      })
    },
    openHb_new() {
      if (this.hb_info.limit_area && !this.current_city) {
        uni.showLoading({
            title: '获取位置信息中，请稍等'
        });
        this.getCity({
          success: () => {
            uni.hideLoading()
            this.getHb_new()
          }, fail: (err) => {
            console.log(err)
            uni.hideLoading()
            this.getHb_new()
          }
        })
      } else {
        this.getHb_new()
      }
    },
    getHb_new() {
      let form = {
        info_id: this.info_id,
        info_type: this.info_type,
        area: this.current_city,
      }
      this.$ajax.post('WxMoney/gethb', form, (res) => {
        if (res.data.code == 1) {
          this.hb_share_query =res.data.hb_share_query
          let  link = this.share.link.split("&hb=")[0]
                this.share.link = link +`&${res.data.hb_share_query}`
                this.getWxConfig()
                this.task =res.data.task
                this.expire_seconds =   this.task.expire_time - parseInt(new Date().getTime()/1000)
                this.timeDownStart()
                for( let i=0; i<Number(this.task.help_count)-Number(this.task.completed_count); i++ ) {
                  this.task.help_members.push({})
                }
                this.$forceUpdate()
        } else {
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    handleSubForm(e) {
      let form = {
        id: this.task.id,
        realname: e.name,
        tel: e.tel,
      }
      if (this.hb_info.limit_area) {
        if (!this.current_city) {
          uni.showLoading({
            title: '获取位置信息中，请稍等'
          });
          this.getCity({
            success: () => {
              form.area = this.current_city
              uni.hideLoading()
              this.withdrawHb(form)
            }, fail: (err) => {
              console.log(err)
              form.area = this.current_city
              uni.hideLoading()
              this.withdrawHb(form)
            }
          })
        } else {
          form.area = this.current_city
          this.withdrawHb(form)
        }
      } else {
        this.withdrawHb(form)
      }
    },
    timeDownStart() {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        if (this.expire_seconds > 0) {
          this.expire_seconds--
        } else {
          clearInterval(this.timer)
        }
      }, 1000)
    },
    toLogin(code) {
      this.$ajax.get('WxMoney/authLogin', {code}, (res) => {
        if (res.data.code != -1) {
          this.getData()
        }
      })
    },
  },
  onReachBottom() {
    if (this.get_status === 'noMore') {
      return
    } else {
      this.page++
      if (this.list_type == 'shoudao') {
        this.getSaiList()
      } else if (this.list_type == 'lingqu') {
        this.getLingList()
      }
    }
  },
}
</script>

<style>
page {
  background: #fff;
}
</style>
<style lang="scss" scoped>
.top {
  position: relative;
  z-index: 2;
  &>image {
    width: 100%;
    display: block;
  }
  .guize {
    position: absolute;
    top: 248rpx;
    right: 0;
    width: 144rpx;
    height: 52rpx;
    border-radius: 26rpx 0px 0px 26rpx;
    background: linear-gradient(100.56deg, #EBD097 0%, #DFB56A 100%);
    box-shadow: 0px 8rpx 8rpx 0px #0000003F;
    display: flex;
    justify-content: center;
    align-items: center;
    text {
      color: #7C3800;
      font-size: 28rpx;
    }
    image {
      width: 26rpx;
      height: 26rpx;
      margin-left: 6rpx;
    }
  }
  .share {
    position: fixed;
    top: 652rpx;
    right: 38rpx;
    z-index: 3;
    image {
      width: 90rpx;
      height: 90rpx;
    }
  }
  
  .info {
    position: absolute;
    width: 654rpx;
    height: 384rpx;
    bottom: -300rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 10rpx;
    background: linear-gradient(-0.54deg, #FFDEBE 0%, #FFFFFF 100%);
    box-shadow: 0px 4rpx 30rpx 0px #FF856E33;
    padding: 0 38rpx;
    box-sizing: border-box;
    z-index: 2;
    &.zixun {
      top: 220rpx;
      height: 280rpx;
    }
    .info-title {
      width: 224rpx;
      height: 56rpx;
      line-height: 56rpx;
      text-align: center;
      margin: 0 auto;
      border-radius: 0px 0px 20rpx 20rpx;
      background: linear-gradient(173.97deg, #FFE9BC 0%, #FFCF7A 100%);
      color: #472204;
      font-size: 24rpx;
    }
    .info-content {
      display: flex;
      margin-top: 48rpx;
      .info-content-left>image {
        width: 176rpx;
        height: 128rpx;
        border-radius: 10rpx;
      }
      .info-content-right {
        margin-left: 38rpx;
        .info-name {
          color: #472204;
          font-size: 28rpx;
          font-weight: bold;
          width: 364rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .info_title{
          color: #472204;
          font-size: 28rpx;
          font-weight: bold;
          width: 364rpx;
          display:-webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .info-area_cont{
          display: flex;
          justify-content: space-between;
          align-items: center;
          width:364rpx;
          margin-top: 16rpx;
          .area-c{
            white-space: nowrap;
            max-width: 65%;
            flex-wrap:nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .info-price{
            white-space: nowrap;
            max-width: 36%;
            flex-wrap:nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .info-area,.info-price{
            margin-top: 0;
          }

        }
        .info-area {
          font-size: 22rpx;
          color: #472204;
          margin-top: 8rpx;
        }
        .info-price {
          font-size: 22rpx;
          color: #E50000;
          margin-top: 16rpx;
          &>text {
            font-size: 26rpx;
            font-weight: bold;
            margin-left: 6rpx;
          }
        }
      }
    }
    .info-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 40rpx;
      &>view {
        width: 280rpx;
        height: 72rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 36rpx;
        &>image {
          width: 38rpx;
          height: 38rpx;
        }
      }
      .info-bottom-left {
        background: linear-gradient(173.97deg, #FFE9BC 0%, #FFCF7A 100%);
        color: #472204;
      }
      .info-bottom-right {
        background: linear-gradient(180deg, #FF4F3B 0%, #FF7154 100%);
        color: #FFFFFF;
      }
    }
  }
  // .top-box {
  //   position: absolute;
  //   left: 50%;
  //   transform: translateX(-50%);
  //   bottom: -100rpx;
  //   width: 654rpx;
  //   height: 172rpx;
  //   border-radius: 10rpx;
  //   background: linear-gradient(-0.54deg, #FFDEBE 0%, #FFFFFF 100%);
  //   box-shadow: 0px 4rpx 30rpx 0px #FF856E33;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   z-index: 2;
  //   .box-item {
  //     flex: 1;
  //     display: flex;
  //     flex-direction: column;
  //     align-items: center;
  //     .num {
  //       font-size: 36rpx;
  //       color: #FF1717;
  //       font-weight: bold;
  //     }
  //     .desc {
  //       margin-top: 18rpx;
  //       font-size: 28rpx;
  //       color: #7C3800;
  //       font-weight: bold;
  //     }
  //   }
  //   .box-line {
  //     width: 1rpx;
  //     height: 66rpx;
  //     background: #d2d2d2;
  //   }
  // }
}
.center {
  position: relative;
  margin-top: -200rpx;
  min-height: 340rpx;
  .top-box{
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    padding: 0 60rpx;
    color: #472204;
    position:absolute;
    top: 530rpx;
    height: 36px;
    left: 24px;
    right: 24px;
    border-radius: 18px;
    background: rgba(255,255,255,0.5);
    .red{
      color: #f00;
    }

  }
  &>image {
    width: 100%;
    display: block;
  }
  
  .ling-hb {
    position: absolute;
    top: 946rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 358rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    color: #f00;
    font-weight: bold;
    border-radius: 40rpx;
    background: linear-gradient(-0.54deg, #FFDEBE 0%, #FFFFFF 100%);
    &.ling-hb-bottom{
       top: 1020rpx;
    }
  }
  .sai-hb {
    position: absolute;
    top: 1112rpx;
    left: 50%;
    transform: translateX(-50%);
    &>image {
      width: 220rpx;
      display: block;
    }
    &>view {
      position: absolute;
      width: 100%;
      text-align: center;
      top: 50%;
      transform: translateY(-50%);
      color: #FFFFFF;
      font-size: 26rpx;
    }
  }
  .zhuli {
    position: absolute;
    top: 1060rpx;
    left: 50%;
    transform: translateX(-50%);
    .person {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 286rpx;
      .zhuli-none {
        width: 66rpx;
        height: 66rpx;
        border-radius: 50%;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .zhuli-bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto;
      padding-top: 20rpx;
      width: 230rpx;
      .zhuli-line {
        width: 44rpx;
        height: 2rpx;
        background: #fff73d;
      }
      .zhuli-text {
        color: #fff73d;
        font-size: 28rpx;
      }
    }
  }
}
.bottom {
  box-sizing: border-box;
  width: 100%;
  padding: 58rpx 48rpx 64rpx 48rpx;
  // background: #DE3A29;
  .bottom-info {
    border-radius: 10rpx;
    background: linear-gradient(-0.54deg, #FFDEBE 0%, #FFFFFF 100%);
    box-shadow: 0px 4rpx 30rpx 0px #FF856E33;
    // padding: 34rpx;
    // image {
    //   width: 100%;
    //   margin-top: 22rpx;
    //   display: block;
    //   &.first {
    //     margin-top: 42rpx;
    //   }
    // }
  }
  .logs {
    border-radius: 10rpx;
    background: #FFFFFF;
    box-shadow: 0px 4px 30px 0px #FF856E33;
    padding: 0 34rpx;
    margin-top: 32rpx;
  }
  .bottom-title {
    width: 224rpx;
    height: 56rpx;
    line-height: 56rpx;
    text-align: center;
    border-radius: 0px 0px 20rpx 20rpx;
    background: linear-gradient(173.97deg, #FFE9BC 0%, #FFCF7A 100%);
    color: #472204;
    font-size: 24rpx;
    margin: 0 auto;
    image {
      width: 100%;
    }
  }
}
.list-top {
  display: flex;
  justify-content: space-between;
  margin-top: 42rpx;
  .list-btn {
    width: 284rpx;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    border-radius: 10rpx;
    background: #dde1e4;
    color: #fcfdff;
    &.active {
      background: linear-gradient(101.86deg, #ff4f3b 0%, #ff8f78 100%);
    }
  }
}
.list-content {
  padding-top: 26rpx;
  .list-item {
    padding: 24rpx 0 20rpx 0;
    border-bottom: 2rpx solid #eeeeee;
    .list-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .info-line {
        display: flex;
        align-items: center;
        image {
          width: 48rpx;
          height: 48rpx;
          border-radius: 50%;
        }
        .info-name {
          margin-left: 16rpx;
        }
      }
      .list-msg {
        color: #2d303c;
        .msg-money {
          color: #ff5b5b;
          padding: 0 10rpx;
        }
      }
    }
    .list-other {
      padding-top: 8rpx;
      padding-left: 64rpx;
      color: #979797;
      font-size: 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .item_status2{  
        color:#fe7430;
      }
      .item_status0{ 
        color:#ff5b5b;
      }
      .item_status1{ 
        color:#00caa9;
      }
    }
    .sai-time {
      justify-content: flex-end;
    }
  }
}
.ling-popup {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 584rpx;
  height: 964rpx;
  border-radius: 20rpx;
  .ling-popup-top {
    position: relative;
    width: 100%;
    height: 782rpx;
    overflow: hidden;
    z-index: 2;
    &::after {
      position: absolute;
      width: 140%;
      height: 782rpx;
      left: -20%;
      top: 0;
      z-index: -1;
      content: '';
      border-radius: 0 0 50% 50%;
      background: #E16754;
      box-shadow: 0px 4rpx 8rpx 0px #00000026;
    }
    .ling-top-text {
      position: absolute;
      top: 238rpx;
      left: 50%;
      transform: translateX(-50%);
      font-size: 44rpx;
      color: #E5CD9F;
    }
  }
  .ling-popup-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 300rpx;
    background: #E0604C;
    .ling-btn {
      position: absolute;
      bottom: 100rpx;
      left: 50%;
      transform: translate(-50%, 0) rotateY(0deg);
      width: 176rpx;
      height: 176rpx;
      border-radius: 50%;
      line-height: 176rpx;
      text-align: center;
      background: #E6CD9F;
      box-shadow: 0px 4rpx 8rpx 0px #0000003F;
      color: #454545;
      font-size: 52rpx;
      z-index: 9;
    }
  }
  .ling-popup-close {
    position: absolute;
    bottom: -104rpx;
    left: 50%;
    transform: translateX(-50%);
  }
}
.guize-popup {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  left: 110rpx;
  width: 572rpx;
  height: 738rpx;
  background-size:572rpx 738rpx;
  background-repeat:no-repeat;
  .guize-box {
    position: relative;
    margin-right: 42rpx;
    height: 100%;
    padding-top: 116rpx;
    box-sizing: border-box;
    .guize-title {
      font-size: 32rpx;
      color: #F3683A;
      display: flex;
      justify-content: center;
      align-items: center;
      .guize-line {
        width: 62rpx;
        height: 4rpx;
        background: #F3683A;
      }
      .guize-text {
        padding: 0 24rpx;
      }
    }
    .guize-content {
      margin: 26rpx 60rpx;
      height: 560rpx;
      box-sizing: border-box;
      overflow-y: auto;
      .guize-tips {
        width: 128rpx;
        height: 44rpx;
        line-height: 44rpx;
        text-align: center;
        border-radius: 22rpx;
        background: linear-gradient(96.11deg, #FF4F3B 0%, #FF8F78 100%);
        color: #FFFCF9;
        font-size: 24rpx;
      }
      .guize-date {
        padding: 24rpx 0 28rpx 0;
        color: #2D303C;
        font-size: 22rpx;
      }
      .guize-info {
        margin-top: 24rpx;
        color: #2D303C;
        font-size: 22rpx;
        height: 308rpx;
        box-sizing: border-box;
        overflow-y: auto;
      }
    }
    .guize-close {
      position: absolute;
      bottom: -76rpx;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
.descp {
  margin-top: 20rpx;
}
.zhuli-time {
  text-align: center;
  color: #fff;
  margin-top: 10rpx;
}
.sai-gif {
  width: 162rpx;
  height: 162rpx;
}
.article-content {
  padding: 24rpx;
  img {
    max-width: 100%;
  }
}
</style>