<template>
  <div class="introduce" :class="{ in_pc: !$store.state.in_mobile }">
    <div class="article-content" v-html="detail"></div>
    <BottomBar pagePath="/exhibition/introduce" :query="query" :id="id"/>
  </div>
</template>

<script>
import BottomBar from "./components/BottomBar.vue";
export default {
  name: "",
  components: {
    BottomBar,
  },
  data() {
    return {
      detail: "",
      query: "",
    };
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.query = "?id=" + this.id;
      this.getData(this.id);
    }
  },
  methods: {
    getData(id) {
      this.$ajax.get("buildShow/getIntroduce", { id }, (res) => {
        if (res.data.code === 1) {
          this.detail = res.data.content;
          this.$nextTick(() => {
            let imgs = document.querySelectorAll(".article-content img");
            let imgArr = [];
            let _this = this;
            for (let i = 0; i < imgs.length; i++) {
              imgArr.push(imgs[i].src);
              imgs[i].addEventListener("click", function () {
                _this.preImg(this.src, imgArr);
              });
            }
          });
        } else {
          uni.showToast({
            title: res.data.msg,
          });
        }
      });
    },
    preImg(nowImg, imgArr) {
      uni.previewImage({
        current: nowImg,
        indicator: "number",
        urls: imgArr,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.introduce {
  min-height: 100vh;
  &.in_pc {
    max-width: 414px;
    margin: auto;
  }
}
.article-content {
  min-height: 100vh;
  padding: 48rpx;
  font-size: 28rpx;
}
.article-content  ::v-deep p {
  margin-bottom: 30upx !important;
}
.article-content  ::v-deep video {
  max-width: 100%;
  margin-bottom: 20upx;
}
</style>
