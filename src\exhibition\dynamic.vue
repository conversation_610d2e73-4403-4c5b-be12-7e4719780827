<template>
  <div class="news" :class="{in_pc: !$store.state.in_mobile}">
    <view style="min-height: 100vh">
      <view class="news_content" v-if="news_content">
        <view class="news_title">最新动态</view>
        <view class="article-content" v-html="news_content"></view>
      </view>
      <template v-else-if="news_list&&news_list.length>0">
        <News :list='news_list' />
        <uni-load-more :status="get_status" :content-text="content_text"></uni-load-more>
      </template>
    </view>
    <BottomBar pagePath="/exhibition/dynamic" :query="query" :id="id"/>
  </div>
</template>

<script>
import News from "./components/News.vue"
import BottomBar from "./components/BottomBar.vue"
import {uniLoadMore} from '@dcloudio/uni-ui'
export default {
  name: '',
  components: {
    News,
    BottomBar,
    uniLoadMore
  },
  data () {
    return {
      query: '',
      page: 1,
      rows: 20,
      get_status: "loading",
      content_text: {
        contentdown: "",
        contentrefresh: "正在加载...",
        contentnomore: "没有更多数据了",
      },
      news_list: [],
      news_content: ""
    }
  },
  onLoad(options){
    if(options.id){
      this.id = options.id
      this.query = '?id='+this.id
      this.getData(this.id)
    }
  },
   methods: {
    getData(id){
      this.get_status = "loading"
      if(this.page === 1){
        this.news_list = []
      }
      this.$ajax.get('buildShow/getNews', {id, page: this.page, rows: this.rows}, res=>{
        if(res.data.code === 1){
          this.news_list = this.news_list.concat(res.data.list)
          // this.get_status = "more"
          if(res.data.news_content){
            this.news_content = res.data.news_content
            // #ifdef H5
            this.$nextTick(() => {
                let imgs = document.querySelectorAll('.news_content img')
                let imgArr = []
                let _this = this
                for (let i = 0; i < imgs.length; i++) {
                    imgArr.push(imgs[i].src)
                    imgs[i].addEventListener('click', function () {
                        _this.preImg(this.src, imgArr)
                    })
                }
            })
            // #endif
          }
          this.get_status = "noMore"
        }else{
          this.get_status = "noMore"
        }
      })
    },
    preImg(nowImg, imgArr) {
      uni.previewImage({
          current: nowImg,
          indicator: "number",
          urls: imgArr
      })
    },
  },
  onReachBottom(){
    if(this.get_status === 'more'){
      this.page++
      this.getData()
    }
  }
}
</script>

<style scoped lang="scss">
.news{
  // min-height: 100vh;
  background-color: #fff;
  &.in_pc{
    max-width: 414px;
    margin: auto;
  }
}

.news_content{
  padding: 24rpx 48rpx;
  // &.has_bg{
  //   .news_title{
  //     color: #fff;
  //   }
  //   .article-content{
  //     border: none;
  //   }
  // }
  .news_title{
    margin-bottom: 24rpx;
    font-size: 40rpx;
    font-weight: bold;
    color: #353535;
  }
  .article-content{
    // padding: 24rpx;
    // border: 1rpx solid #D8D8D8;
    // border-radius: 16rpx;
    background-color: #fff;
    ::v-deep p{
      margin-bottom: 20rpx;
    }
    ::v-deep img{
      margin-bottom: 0;
    }
  }
}
</style>