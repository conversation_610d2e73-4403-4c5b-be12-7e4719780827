<template>
	<view class="user_center" :class="{ is_vip: is_vip }">
		<!-- 头部卡片 -->
		<view class="header-box">
			<view class="bg"></view>
			<view class="user_card">
				<view class="user_info flex-row">
					<image class="user_img" @click="toUserInfo"
						:src="(user_info.prelogo || defaultAvatar) | imageFilter('w_80')" mode="aspectFill"></image>
					<view class="user_info-right flex-1">
						<view class="user_name" @click="toUserInfo" v-if="login_status > 1">{{ user_info.cname }}</view>
						<view class="user_name" v-else @click="toLogin">去登录</view>
						<view style="display:inline-block;max-width:100%" @click="toVipInfo()">
							<view class="user_level flex-row">
								<image class="level_icon"
									:src="((is_vip && !user_info.vip_invalid) || is_adviser ? '/images/new_icon/<EMAIL>' : '/images/new_icon/<EMAIL>') | imageFilter('m_80')">
								</image>
								<text class="level_name" v-if="is_adviser">{{ adviser_info.level_name }}</text>
								<text class="level_name" :class="{ hui: !is_vip || user_info.vip_invalid }"
									v-else>{{ user_info.level_name || user_info.levelup_time || '暂未开启会员' }}</text>
							</view>
						</view>
					</view>
					<view class="btn" v-if="is_adviser"
						@click="userNavigateTo(`/pages/consultant/detail?id=${adviser_info.id}`)">进入名片</view>
					<view class="btn" v-else-if="user_info.levelid > 1"
						@click="userNavigateTo(`/pages/agent/detail?id=${user_info.id}`)">进入主页</view>
					<view class="btn" v-else-if="user_info.tel || user_info.cname"
						@click="userNavigateTo('/user/member_upgrade')">成为经纪人</view>
				</view>
				<view class="user_data flex-row" v-if="is_adviser">
					<view class="data_item" @click="toChatList">
						<text class="value">{{ adviser_info.traffic_volume }}</text>
						<text class="label">咨询量</text>
					</view>
					<view class="data_item">
						<text class="value">{{ adviser_info.browse }}</text>
						<text class="label">浏览量</text>
					</view>
					<view class="data_item"
						@click="userNavigateTo(`/user/consultant/addpost?buildid=${adviser_info.build_ids}`)">
						<text class="value">{{ adviser_info.dynamic }}</text>
						<text class="label">动态</text>
					</view>
					<view class="data_item" @click="userNavigateTo('/user/adviser_builds')">
						<text class="value">{{ adviser_info.count_project || 0 }}</text>
						<text class="label">项目</text>
					</view>
				</view>
				<view class="user_data flex-row" v-else>
					<view class="data_item" @click="userNavigateTo('/user/exchange')">
						<text class="value">{{ user_info.score }}</text>
						<text class="label">我的积分</text>
					</view>
					<view class="data_item" @click="userNavigateTo('/user/recharge')">
						<text class="value">{{ user_info.money_own }}</text>
						<text class="label">金币</text>
					</view>
					<view class="data_item" @click="userNavigateTo('/user/manage_info')">
						<text class="value">{{ user_info.house_count }}</text>
						<text class="label">信息</text>
					</view>
					<view v-if="user_info.levelid > 1" class="data_item" @click="toActiveLogs">
						<text class="value">{{ adviser_info.active || agent_data.active || 0 }}</text>
						<text class="label">活跃度</text>
					</view>
					<view v-else class="data_item" @click="userNavigateTo('/user/history')">
						<text class="value">{{ user_info.browse_count }}</text>
						<text class="label">浏览历史</text>
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="entrance-list flex-row">
      <view class="entrance-item">
        <image :src=""></image>
      </view>
    </view> -->
		<view class="entrance">
			<my-grid :options="entrance_list" :column-num="entrance_list.length >= 4 ? 4 : entrance_list.length"
				:fontSize="14" :show-border="false" className="sm" @click="onClickEntrance"></my-grid>
		</view>
		<view class="data-box" v-if="is_adviser">
			<view class="list">
				<view class="list-item flex-row">
					<text class="tfy-icon ic_shuju_vip"></text>
					<view style="display:block" class="flex-1">
						<text class="text">客户管理</text>
						<!-- <text class="unit">近7日</text> -->
					</view>
					<!-- <view class="flex-row into-box">
						<text>全部数据</text>
						<my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
					</view> -->
				</view>
				<view class="alert flex-row" v-if="show_customer_tip">
					<my-icon type="tongzhi" size="32rpx" color="#fb656a"></my-icon>
					<text class="text flex-1"
						@click="userNavigateTo('/customer/assistant')">{{ adviser_info.tips }}</text>
					<view class="icon-box" @click="show_customer_tip = false">
						<my-icon type="ic_guanbi" size="32rpx" color="#fb656a"></my-icon>
					</view>
				</view>
				<view class="data-grid flex-row" @click="userNavigateTo('/customer/list')">
					<view class="data-item">
						<view class="value">{{ adviser_info.un_follow_up }}</view>
						<view class="label">未跟进</view>
					</view>
					<view class="data-item">
						<view class="value">{{ adviser_info.todayCount }}</view>
						<view class="label">今日新增</view>
					</view>
					<view class="data-item">
						<view class="value">{{ adviser_info.weekCount }}</view>
						<view class="label">本周新增</view>
					</view>
					<view class="data-item">
						<view class="value">{{ adviser_info.totalCount }}</view>
						<view class="label">累积</view>
					</view>
				</view>
				<view class="build-data" v-if="build_data && build_data.length > 0">
					<swiper next-margin="24rpx" :indicator-dots="build_data.length > 1" indicator-active-color="#ff656b"
						indicator-color="#d8d8d8">
						<swiper-item v-for="(item, index) in build_data" :key="index">
							<view class="swiper-item">
								<view class="build_name">{{ item.build_name }}</view>
								<view class="data-grid flex-row">
									<view class="data-item">
										<view class="value">{{ item.view.data | wanNum }}<text v-if="item.view.data > 10000"
												class="unit">w</text></view>
										<view class="label">浏览量{{ item.view.new_add > 0 ? '+' + item.view.new_add : '' }}</view>
									</view>
									<view class="data-item">
										<view class="value">{{ item.customer.data | wanNum }}<text
												v-if="item.customer.data > 10000" class="unit">w</text></view>
										<view class="label">咨询量{{ item.customer.new_add > 0 ? '+' + item.customer.new_add : '' }}
										</view>
									</view>
									<view class="data-item" @click="onClickSign">
										<view class="value">{{ item.sign.data }}</view>
										<view class="label">意向报名{{ item.sign.new_add > 0 ? '+' + item.sign.new_add : '' }}</view>
									</view>
									<view class="data-item" @click="onClickService">
										<view class="value">{{ item.service.data | wanNum }}<text
												v-if="item.service.data > 10000" class="unit">w</text></view>
										<view class="label">客户线索{{ item.service.new_add > 0 ? '+' + item.service.new_add : '' }}
										</view>
									</view>
								</view>
							</view>
						</swiper-item>
					</swiper>
				</view>
			</view>
		</view>
		<view class="data-box" v-else-if="user_info.levelid > 1">
			<view class="list">
				<view class="list-item flex-row">
					<text class="tfy-icon" :class="is_vip ? 'ic_shuju_vip' : 'ic_wofabude'"></text>
					<view style="display:block" class="flex-1">
						<text class="text">数据汇总</text>
						<!-- <text class="unit">近7日</text> -->
					</view>
					<!-- <view class="flex-row into-box">
						<text>全部数据</text>
						<my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
					</view> -->
				</view>
				<view class="alert flex-row" v-if="show_customer_tip">
					<my-icon type="tongzhi" size="32rpx" color="#fb656a"></my-icon>
					<text class="text flex-1" @click="userNavigateTo('/customer/assistant')">{{ agent_data.tips }}</text>
					<view class="icon-box" @click="show_customer_tip = false">
						<my-icon type="ic_guanbi" size="32rpx" color="#fb656a"></my-icon>
					</view>
				</view>
				<view class="data-grid flex-row">
					<view class="data-item">
						<view class="value">{{ agent_data.houseCount }}</view>
						<view class="label">当前上架</view>
					</view>
					<view class="data-item">
						<view class="value">{{ agent_data.clickCount | wanNum }}<text v-if="agent_data.clickCount > 10000"
								class="unit">w</text></view>
						<view class="label">浏览量</view>
					</view>
					<view class="data-item">
						<view class="value">{{ agent_data.friendCount }}</view>
						<view class="label">总咨询量</view>
					</view>
					<view class="data-item">
						<view class="value">{{ agent_data.visitorCount | wanNum }}<text
								v-if="agent_data.visitorCount > 10000" class="unit">w</text></view>
						<view class="label">访客</view>
					</view>
				</view>
				<view class="data-grid flex-row" @click="userNavigateTo('/customer/list')">
					<view class="data-item">
						<view class="value">{{ agent_data.un_follow_up }}</view>
						<view class="label">未跟进</view>
					</view>
					<view class="data-item">
						<view class="value">{{ agent_data.todayCount }}</view>
						<view class="label">今日客户</view>
					</view>
					<view class="data-item">
						<view class="value">{{ agent_data.weekCount }}</view>
						<view class="label">本周客户</view>
					</view>
					<view class="data-item">
						<view class="value">{{ agent_data.totalCount }}</view>
						<view class="label">累积</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 置业顾问 -->
		<view class="release-box" v-if="is_adviser">
			<view class="list">
				<view class="list-item flex-row">
					<text class="tfy-icon ic_huiyuanyingyong"></text>
					<text class="text">置业顾问应用</text>
				</view>
				<view class="release-grid">
					<my-grid :options="adviser_release_list" rowTop="1rpx" :column-num="4" :show-border="false"
						className="sm" @click="onAdviserOption"></my-grid>
					<!-- <my-grid :options="menus" rowTop="1rpx" :column-num="4" :show-border="false" className="sm" @click="onAdviserOption"></my-grid> -->
				</view>
			</view>
		</view>
		<!-- 经纪人应用 -->
		<view class="release-box" v-if="(user_info.levelid > 1 || is_adviser) && menus && menus.length">
			<view class="list">
				<view class="list-item flex-row">
					<text class="tfy-icon ic_huiyuanyingyong"></text>
					<text class="text">我发布的</text>
				</view>
				<view class="release-grid">
					<!-- <my-grid :options="agent_release_list" rowTop="1rpx" :column-num="5" :show-border="false" className="sm" @click="onAgentOption"></my-grid> -->
					<my-grid :options="menus" rowTop="1rpx" :column-num="5" :show-border="false" className="sm"
						@click="onAgentOption"></my-grid>
				</view>
			</view>
		</view>
		<!-- 非职业顾问和经纪人 -->
		<view class="release-box" v-if='!is_adviser && user_info.levelid < 2 && menus && menus.length'>
			<view class="list">
				<view class="list-item flex-row">
					<text class="tfy-icon" :class="is_vip ? 'ic_wofabude_vip' : 'ic_wofabude'"></text>
					<text class="text">我发布的</text>
				</view>
				<view class="release-grid">
					<!-- <my-grid :options="is_vip?release_list_vip:release_list" rowTop="1rpx" column-num="4" :show-border="false" className="sm" @click="toManage"></my-grid> -->
					<my-grid :options="menus" rowTop="1rpx" column-num="4" :show-border="false" className="sm"
						@click="toManage"></my-grid>
				</view>
			</view>
		</view>
		<!-- 老板-->
		<view class="release-box" v-if="is_company_manager">
			<view class="list">
				<view class="list-item flex-row">
					<text class="tfy-icon ic_huiyuanyingyong"></text>
					<text class="text ">{{ company.name }}</text>
					<view class="right flex-row" @click="$navigateTo(`/shops/myCompany?id=${company.id}`)">
						<text>我的公司</text>
						<my-icon type="ic_into" color="#999" size="22upx"></my-icon>
					</view>
				</view>
				<view class="release-grid store-lists">
					<scroll-view scroll-x style="width: 100%" class="store-list">
						<view class="store-item" v-for="item in stores" :key="item.id">
							<view class="store_info flex-row">
								<view class="store_title">{{ item.name }}</view>
								<view class="to_store flex-row" @click="$navigateTo(`/shops/detail?id=${item.id}`)">
									<text>进入店铺</text>
									<my-icon type="ic_into" color="#999" size="22upx"></my-icon>
								</view>
							</view>
							<view class="bottom">
								<my-grid :options="store_list" rowTop="1rpx" :column-num="4" :show-border="false"
									className="sm" @click="onCompanyOption($event, item.id, item.name)"></my-grid>
							</view>
						</view>
					</scroll-view>

				</view>
			</view>
		</view>
		<!-- 店长 -->
		<view class="release-box store_owner" v-if="!is_company_manager && is_store_manager">
			<template v-if="stores.length > 1">
				<view class="list">
					<view class="list-item flex-row">
						<text class="tfy-icon ic_huiyuanyingyong"></text>
						<text class="text ">{{ company.name }}</text>
						<!-- <view class="right flex-row">
					<text>我的公司</text>
					<my-icon type="ic_into" color="#999" size="22upx"></my-icon>
				</view> -->
					</view>
					<view class="release-grid store-lists">
						<scroll-view scroll-x style="width: 100%" class="store-list">
							<view class="store-item" v-for="item in stores" :key="item.id" @click="$navigateTo(``)">
								<view class="store_info flex-row">
									<view class="store_title">{{ item.name }}</view>
									<view class="to_store flex-row" @click="$navigateTo(`/shops/detail?id=${item.id}`)">
										<text>进入店铺</text>
										<my-icon type="ic_into" color="#999" size="22upx"></my-icon>
									</view>
								</view>
								<view class="bottom">
									<my-grid :options="store_list" rowTop="1rpx" :column-num="4" :show-border="false"
										className="sm" @click="onCompanyOption($event, item.id, item.name)"></my-grid>
								</view>
							</view>
						</scroll-view>

					</view>
				</view>
			</template>
			<template v-else>
				<view class="list">
					<view class="list-item flex-row">
						<text class="tfy-icon ic_huiyuanyingyong"></text>
						<text class="text">{{ stores[0].name }}</text>
						<view class="right flex-row" @click="$navigateTo(`/shops/detail?id=${stores[0].id}`)">
							<text>进入店铺</text>
							<my-icon type="ic_into" color="#999" size="22upx"></my-icon>
						</view>
					</view>
					<view class="release-grid">
						<my-grid :options="store_list" rowTop="1rpx" :column-num="4" :show-border="false" className="sm"
							@click="onCompanyOption($event, stores[0].id, stores[0].name)"></my-grid>
					</view>
				</view>
			</template>
		</view>
		<view class="list">
			<view class="list-item flex-row" hover-class="navigator-hover" :hover-start-time="20" :hover-stay-time="70"
				@click="toOrder">
				<text class="tfy-icon" :class="is_vip ? 'ic_order_vip' : 'ic_order'"></text>
				<text class="text">订单卡券</text>
				<my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
			</view>
		</view>
		<view class="list">
			<view class="list-item flex-row" hover-class="navigator-hover" :hover-start-time="20" :hover-stay-time="70"
				@click="toAdd">
				<text class="tfy-icon" :class="is_vip ? 'ic_fabu_vip' : 'ic_fabu'"></text>
				<text class="text">快速发布</text>
				<my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
			</view>
		</view>
		<view class="list">
			<view class="list-item flex-row" hover-class="navigator-hover" :hover-start-time="20" :hover-stay-time="70"
				@click="showQrcode" v-if="qrcode">
				<text class="tfy-icon" :class="is_vip ? 'ic_dingyuehao_vip' : 'ic_dingyuehao'"></text>
				<text class="text">关注公众号</text>
				<my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
			</view>
			<!-- <view class="list-item flex-row">
        <text class="tfy-icon" :class="is_vip?'ic_xiazai_vip':'ic_xiazai'"></text>
        <text class="text">下载APP</text>
        <my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
      </view> -->
		</view>
		<view class="list">
			<view class="list-item flex-row" hover-class="navigator-hover" :hover-start-time="20" :hover-stay-time="70"
				v-if="wx_service_link" @click="kefu">
				<image v-if="is_vip" :src="'/yidongduan/my/kfdh_vip.png' | imgUrl('m_120')" mode="" />
				<image v-else :src="'/yidongduan/my/kfdh.png' | imgUrl('m_120')" mode="" />
				<text class="text">联系客服</text>
				<my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
			</view>
			<view class="list-item flex-row" hover-class="navigator-hover" :hover-start-time="20" :hover-stay-time="70"
				v-if="servicePhone" @click="handleTel">
				<text class="tfy-icon" :class="is_vip ? 'ic_kefu_vip' : 'ic_kefu'"></text>
				<text class="text">客服电话</text>
				<my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
			</view>
			<view class="list-item flex-row" hover-class="navigator-hover" :hover-start-time="20" :hover-stay-time="70"
				@click="userNavigateTo('/user/feedback')">
				<text class="tfy-icon" :class="is_vip ? 'ic_fankui_vip' : 'ic_fankui'"></text>
				<text class="text">反馈问题</text>
				<my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
			</view>
		</view>
		<!-- <view class="list">
      <view class="list-item flex-row">
        <text class="tfy-icon" :class="is_vip?'ic_shezhi_vip':'ic_shezhi'"></text>
        <text class="text">设置</text>
        <my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
      </view>
    </view> -->
		<view class="list">
			<!-- <view class="list-item flex-row" hover-class="navigator-hover" :hover-start-time="20" :hover-stay-time="70" @click="$navigateTo('/user/help_list')">
        <text class="tfy-icon" :class="is_vip?'ic_guanyu_vip':'ic_guanyu'"></text>
        <text class="text">帮助中心</text>
        <my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
      </view> -->
			<view class="list-item flex-row" hover-class="navigator-hover" :hover-start-time="20" :hover-stay-time="70"
				@click="$navigateTo('/user/my/about')">
				<text class="tfy-icon" :class="is_vip ? 'ic_guanyu_vip' : 'ic_guanyu'"></text>
				<text class="text">关于我们</text>
				<my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
			</view>
		</view>
		<view class="list">
			<view class="btn" @click="logout()">退出登录</view>
			<!-- <view class="list-item flex-row" hover-class="navigator-hover" :hover-start-time="20" :hover-stay-time="70" @click="logout()">
        <text class="tfy-icon" :class="is_vip?'ic_guanyu_vip':'ic_guanyu'"></text>
        <text class="text">退出登录</text>
        <my-icon type="ic_into" color="#999" size="30rpx"></my-icon>
      </view> -->
		</view>
		<my-popup ref="qrcode_popup" position="top">
			<view class="qrcode-box">
				<!-- #ifdef H5 -->
				<view class="img-box">
					<image @longtap="saveQrcode" class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="title">长按保存图片</view>
						<view class="tip">相册选取，识别关注</view>
					</view>
				</view>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view class="img-box">
					<image class="qrcode" :src="qrcode" mode="aspectFill"></image>
					<view>
						<view class="tip">长按识别二维码关注公众号</view>
					</view>
				</view>
				<!-- #endif -->
				<view class="icon-box" @click="$refs.qrcode_popup.hide()">
					<my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
				</view>
			</view>
		</my-popup>
		<share-pop ref="show_share_pop" @copyLink="copyLink" @appShare="appShare" :showHaibao="false"
			@showCopywriting='showCopywriting'></share-pop>
	</view>
</template>

<script>
import myIcon from "../../components/myIcon.vue"
import myGrid from "../../components/myGrid.vue"
import myPopup from "../../components/myPopup.vue"
import sharePop from "../../components/sharePop.vue"
import {
	checkUserStatus,
	formatImg,
	showModal
} from "../../common/index.js"
import {
	mapState,
	mapMutations
} from 'vuex'
export default {
	components: {
		myGrid,
		myIcon,
		sharePop,
		myPopup
	},
	data() {
		return {
			is_adviser: 0,
			adviser_info: {},
			show_customer_tip: null,
			agent_data: {},
			company: {
			},
			stores: [],
			is_vip: 0,
			shareName: '',
			shareId: '',
			defaultAvatar: this.$store.state.defaultAvatar,
			entrance_list: [
				{
					text: '签到',
					image: formatImg('/images/new_icon/<EMAIL>', 'm_8601'),
					url: '/user/task_center'
				},
				{
					text: '会员升级',
					image: formatImg('/images/new_icon/<EMAIL>', 'm_8601'),
					url: '/user/member_upgrade?is_personal=1'
				},
				{
					text: '关注/收藏',
					image: formatImg('/images/new_icon/<EMAIL>', 'm_8601'),
					url: '/user/collect'
				},
				{
					text: '充值中心',
					image: formatImg('/images/new_icon/<EMAIL>', 'm_8601'),
					url: '/user/recharge'
				},
			],
			store_list: [
				{
					text: "邀请同事",
					image: formatImg("/images/new_icon/adviser/yaoqing.png", 'm_8601'),
					url: "/chatPage/chat/invited"
				},
				{
					text: "团队管理",
					image: formatImg("/images/new_icon/store/storeteam.png", 'm_8601'),
					url: "/user/manage_info?cate_id=2"
				},
				{
					text: "刷新排名",
					image: formatImg("/images/new_icon/store/shuaxin.png", 'm_8601'),
					url: "/user/manage_info?cate_id=1"
				},
				{
					text: "门店设置",
					image: formatImg("/images/new_icon/store/stores.png", 'm_8601'),
					url: "/user/manage_info?cate_id=2"
				},
			],
			agent_release_list: [
				{
					text: "出售",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/user/manage_info?cate_id=1"
				},
				{
					text: "出租",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/user/manage_info?cate_id=2"
				},
				{
					text: "求购信息",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/needPage/buy_house/buy_house"
				},
				{
					text: "求租信息",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/needPage/rest_house/rest_house"
				},
				{
					text: "刷新排名",
					image: formatImg("/images/new_icon/adviser/shuaxin.png", 'm_8601'),
					url: ""
				},
				{
					text: "店铺设置",
					image: formatImg("/images/new_icon/adviser/mingpian.png", 'm_8601'),
					url: "/user/agent_info"
				},
				{
					text: "名片设置",
					image: formatImg("/images/new_icon/adviser/mingpian.png", 'm_8601'),
					url: "/shops/agent_info"
				},
				{
					text: "小区相册",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/user/manage_photos"
				},
				{
					text: "获客助手",
					image: formatImg("/images/new_icon/adviser/assistant.png", 'm_8601'),
					url: "/customer/assistant"
				},
				{
					text: "房源模板",
					image: formatImg("/images/new_icon/info_template.png", 'm_8601'),
					url: "/user/add/template_list"
				}
				// {
				// 	text:"公司入驻",
				// 	image:formatImg("/images/new_icon/adviser/shuaxin.png"),
				// 	url:""
				// },
				// {
				//   text:"邀请同事",
				//   image:formatImg("/images/new_icon/adviser/shuaxin.png"),
				//   url:""
				// },
			],
			release_list_vip: [
				{
					text: "出售",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/user/manage_info?cate_id=1"
				},
				{
					text: "出租",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/user/manage_info?cate_id=2"
				},
				{
					text: "求购",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/user/manage_info?cate_id=4"
				},
				{
					text: "求租",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/user/manage_info?cate_id=3"
				},
				// {
				// 	text:"店铺设置",
				// 	image:formatImg("/images/new_icon/adviser/mingpian.png"),
				// 	url:"/user/agent_info"
				// },
			],
			release_list: [
				{
					text: "出售",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/user/manage_info?cate_id=1"
				},
				{
					text: "出租",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/user/manage_info?cate_id=2"
				},
				{
					text: "求购",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/user/manage_info?cate_id=4"
				},
				{
					text: "求租",
					image: formatImg("/images/new_icon/<EMAIL>", 'm_8601'),
					url: "/user/manage_info?cate_id=3"
				}
			],
			adviser_release_list: [
				{
					text: "邀请好友",
					image: formatImg("/images/new_icon/adviser/yaoqing.png", 'm_8601'),
					url: "/chatPage/chat/invited"
				},
				{
					text: "发布动态",
					image: formatImg("/images/new_icon/adviser/fabu.png", 'm_8601'),
					url: ""
				},
				{
					text: "兑换特权",
					image: formatImg("/images/new_icon/adviser/vip.png", 'm_8601'),
					url: "/user/adviser_upgrade"
				},
				{
					text: "我的项目",
					image: formatImg("/images/new_icon/adviser/xiangmu.png", 'm_8601'),
					url: "/user/adviser_builds"
				},
				{
					text: "刷新排名",
					image: formatImg("/images/new_icon/adviser/shuaxin.png", 'm_8601'),
					url: ""
				},
				{
					text: "名片设置",
					image: formatImg("/images/new_icon/adviser/mingpian.png", 'm_8601'),
					url: "/user/adviser_info"
				},
				{
					text: "获客助手",
					image: formatImg("/images/new_icon/adviser/assistant.png", 'm_8601'),
					url: "/customer/assistant"
				}
			],
			// company_release_list:[
			// 	// {
			// 	// 	text:"邀请好友",
			// 	// 	image:formatImg("/images/new_icon/adviser/yaoqing.png"),
			// 	// 	url:""
			// 	// },
			// 	{
			// 		text:"我的公司",
			// 		image:formatImg("/images/new_icon/adviser/xiangmu.png"),
			// 		url:""
			// 	},
			// 	// {
			// 	// 	text:"添加门店",
			// 	// 	image:formatImg("/images/new_icon/adviser/fabu.png"),
			// 	// 	url:""
			// 	// }
			// ],
			mail_count: 0,
			newsList: [],
			build_data: [], //置业顾问楼盘数据
			servicePhone: "",
			geting: false,
			qrcode: '',
			is_store_manager: 0,
			is_company_manager: 0,
			menus: []
		};
	},
	watch: {
		ter_isretail(val) {
			if (val && !this.agent_release_list.find(item => item.url === '/user/report')) {
				this.agent_release_list.push({
					text: "快速报备",
					image: formatImg("/images/new_icon/adviser/baobei.png", 'm_8601'),
					url: "/user/report"
				})
			}
		}
	},
	computed: {
		...mapState(['user_info', 'allowOpen', 'ter_isretail', 'wx_service_link']),
		login_status() {
			return this.$store.state.user_login_status
		}
	},
	onLoad() {
		if (this.ter_isretail && !this.agent_release_list.find(item => item.url === '/user/report')) {
			this.agent_release_list.push({
				text: "快速报备",
				image: formatImg("/images/new_icon/adviser/baobei.png", 'm_8601'),
				url: "/user/report"
			})
		}
		// #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
		uni.hideShareMenu()
		// #endif
	},
	onHide() {
		this.setAllowOpen(true)
	},
	onShow() {
		if (this.$store.state.preventHandleShow) {
			return
		}
		this.getData()
	},
	filters: {
		wanNum(value) {
			if (!value) {
				return 0
			}
			if (value < 10000) {
				return value
			}
			value += ''
			var value2 = (value.substring(0, value.length - 2) / 10).toFixed()
			return value2.substring(0, value2.length - 1) + '.' + value2.substring(value2.length - 1, value2.length)
		},
		imgUrl(val, param = "") {
			return formatImg(val, param)
		}
	},
	methods: {
		...mapMutations(['getUserInfo', 'setAllowOpen']),
		getData() {
			// #ifdef MP-BAIDU
			if (this.geting) {
				return
			}
			this.geting = true
			setTimeout(() => {
				this.geting = false
			}, 300);
			// #endif
			this.$ajax.get('member/index.html', {}, (res) => {
				if (res.data.tel) {
					this.servicePhone = res.data.tel
				}
				if (res.data.user) {

					if (res.data.is_adviser) {
						res.data.user.adviser = {
							uncheck: res.data.adviser.uncheck || 0,
							uncheck_cname: res.data.adviser.uncheck_cname || '',
							uncheck_prelogo: res.data.adviser.uncheck_prelogo || ''
						}
					}
					if (res.data.user.levelid > 1) {
						res.data.user.agent = {
							uncheck: res.data.agent.uncheck || 0,
							uncheck_cname: res.data.agent.uncheck_cname || '',
							uncheck_prelogo: res.data.agent.uncheck_prelogo || ''
						}
					}
					console.log(res.data.user);
					this.getUserInfo(res.data.user)
				}
				if (res.data.code == 1) {
					this.$store.state.user_login_status = 2
					if (res.data.user.tel) {
						this.$store.state.user_login_status = 3
					}
					this.mail_count = res.data.mail_count
					res.data.user.un_upgrade = res.data.un_upgrade
					this.is_adviser = res.data.is_adviser
					this.is_store_manager = res.data.user.is_store_manager
					this.is_company_manager = res.data.user.is_company_manager
					this.company = res.data.user.company
					// this.company=res.data.user.sotre
					this.is_vip = res.data.is_adviser || res.data.user.is_vip || res.data.user.levelid > 1
					// 如果是置业顾问且不是优选置业顾问显示成为优选置业顾问
					if (this.is_adviser) {
						this.entrance_list[1].text = '会员特权'
						this.entrance_list[1].url = '/user/adviser_rights'
						this.build_data = res.data.build_statistics
					} else if (res.data.user.levelid > 1) {
						this.entrance_list[1].text = '会员升级'
						this.entrance_list[1].url = "/user/member_upgrade"
					} else if (res.data.user.levelid === 1 && res.data.personal_vip_status != '0') {
						this.entrance_list[1].text = '会员升级'
						this.entrance_list[1].url = '/user/member_upgrade?is_personal=1'
					} else if (res.data.personal_vip_status == '0') {
						this.entrance_list = this.entrance_list.filter(item => item.text !== '会员升级')
					}
					this.agent_release_list = this.agent_release_list

					this.stores = res.data.user.stores
					// if(res.data.user.levelid <= 1){
					// let agent_info_index = this.release_list_vip.findIndex(item=>item.url === '/user/agent_info')
					// let agent_refresh_index = this.release_list_vip.findIndex(item=>item.text === '刷新排名')
					// if(agent_info_index>=0){
					// 	this.release_list_vip.splice(agent_info_index, 1)
					// }
					// if(agent_refresh_index>=0){
					// 	this.release_list_vip.splice(agent_refresh_index, 1)
					// }
					// this.entrance_list[1].url= "/user/member_upgrade"
					// }
					this.adviser_info = res.data.adviser || {}
					if (res.data.adviser.tips && this.show_customer_tip === null) {
						this.show_customer_tip = true
					}
					this.agent_data = res.data.agent || {}
					if (res.data.agent.tips && this.show_customer_tip === null) {
						this.show_customer_tip = true
					}
					this.newsList = res.data.news
					this.tips = res.data.tips
					this.qrcode = res.data.gzhewm
					this.menus = res.data.menus.map(item => {
						item.icon = formatImg(item.icon, 'm_240')
						return item
					})
					//是经纪人 但是没有公司 添加公司入驻按钮
					// if(res.data.user.levelid>1&&this.is_company_manager==0){
					// 	let isIn=false
					// 	this.agent_release_list.map((item,index)=>{
					// 		if (item.text =="公司入驻"){
					// 			isIn=true	
					// 		}
					// 		if(item.text =="公司主页"){
					// 			this.agent_release_list.splice(index,1)
					// 		}
					// 	})
					// 	if(isIn){
					// 		return 
					// 	}
					// 	this.agent_release_list.push({
					// 		text:"公司入驻",
					// 		image:formatImg("/images/new_icon/store/applycompany.png"),
					// 		url:"/shops/addCompany"
					// 	})
					// }
					//是经纪人 有公司 添加公司主页按钮
					// if(res.data.user.levelid>1&&this.is_company_manager==1){
					// 	let isIn=false
					// 	this.agent_release_list.map((item,index)=>{
					// 		if (item.text =="公司主页"){
					// 			isIn=true	
					// 		}
					// 		if(item.text =="公司入驻"){
					// 			this.agent_release_list.splice(index,1)
					// 		}
					// 	})
					// 	if(isIn){
					// 		return 
					// 	}
					// 	this.agent_release_list.push({
					// 		text:"公司主页",
					// 		image:formatImg("/images/new_icon/store/companyzhuye.png"),
					// 		url:"/shops/myCompany"
					// 	})
					// }
				} else {
					switch (res.data.code) {
						case 2:
							showModal({
								title: '提示',
								content: '您还没有绑定手机号，是否去绑定？',
								confirmText: '去绑定',
								confirm: () => {
									this.$navigateTo('/user/bind_phone/bind_phone')
								}
							});
							break;
						case -1:
							showModal({
								title: '提示',
								content: '您还没有登录，是否去登录?',
								confirmText: '去登录',
								confirm: () => {
									uni.removeStorageSync('token')
									this.$navigateTo('/user/login/login')
								}
							});
							break;
						case -9:
							showModal({
								title: '提示',
								content: '登录已过期，是否重新登录?',
								confirmText: '去登录',
								confirm: () => {
									uni.removeStorageSync('token')
									this.$navigateTo('/user/login/login')
								}
							});
							break;
						default:
							uni.showToast({
								title: res.data.msg,
								icon: "none"
							})
					}
				}
				// #ifdef MP-BAIDU
				this.geting = false
				// #endif
			}, (err) => {
				// #ifdef MP-BAIDU
				this.geting = false
				// #endif
			}, { disableAutoHandle: true })
		},
		toUserInfo() {
			if (this.user_info.tel || this.user_info.cname) {
				// if (this.is_adviser){
				// 	this.userNavigateTo('/user/user_info?infoType=adviser')
				// }else if (this.user_info.levelid>1){
				// 	this.userNavigateTo('/user/user_info?infoType=agent')
				// }else {
				this.userNavigateTo('/user/user_info')
				// }

			}
		},
		toActiveLogs() {
			if (this.is_adviser) {
				this.userNavigateTo('/user/adviser_activity')
			} else {
				this.userNavigateTo('/user/agent_activity')
			}
		},
		toOrder() {
			this.userNavigateTo('/user/order_list')
		},
		toChatList() {
			uni.switchTab({
				url: "/pages/index/chat_list"
			})
		},
		toAdd() {
			// if(this.is_adviser){
			// 	this.userNavigateTo(`/user/consultant/addpost?buildid=${this.adviser_info.build_ids}`)
			// 	return
			// }
			checkUserStatus(() => {
				uni.switchTab({
					url: "/pages/add/add"
				})
			})
		},
		showQrcode() {
			this.$refs.qrcode_popup.show()
		},
		// 保存二维码
		saveQrcode() {
			uni.request({
				url: this.qrcode,
				method: 'GET',
				responseType: 'arraybuffer',
				success: (res) => {
					let base64 = uni.arrayBufferToBase64(res);
					const userImageBase64 = 'data:image/jpg;base64,' + base64;
					uni.saveImageToPhotosAlbum({
						filePath: userImageBase64,
						success: result => {
							uni.showToast({
								title: '保存成功，在微信从相册中选取识别吧',
								icon: 'none',
								duration: 4000
							})
						},
						fail: err => {
							console.log(err)
							uni.showToast({
								title: '保存失败，请重试',
								icon: 'none'
							})
						}
					})
				}
			});
		},
		toProtocol() {
			this.$navigateTo('/user/my/protocol')
		},
		regInfos() {
			this.$navigateTo('/user/my/reg_infos')
		},
		pushIsSubscribe() {
			this.$ajax.post('im/subscribe', {}, res => {
				console.log(res.data)
			})
		},
		toManage(e) {
			this.userNavigateTo(this.menus[e.index].path)
			// if(this.is_vip){
			// 	this.userNavigateTo(this.release_list_vip[e.index].url)
			// }else{
			// 	this.userNavigateTo(this.release_list[e.index].url)
			// }
		},
		onClickEntrance(e) {
			if (this.entrance_list[e.index].url === '/user/member_upgrade?is_personal=1' && this.is_vip && this.user_info.levelid <= 1 && !this.is_adviser) {
				this.userNavigateTo('/user/member_upgrade')
				// uni.showToast({
				// 	title:'您已经是个人vip会员',
				// 	icon:'none'
				// })
				return
			}
			this.userNavigateTo(this.entrance_list[e.index].url)
		},
		onAgentOption(e) {
			// if(this.agent_release_list[e.index].text==='刷新排名'){
			// 	this.agentRefresh()
			// 	return
			// }
			if (this.menus[e.index].event == 'refresh') {
				this.agentRefresh()
				return
			}
			// this.userNavigateTo(this.agent_release_list[e.index].url)
			this.userNavigateTo(this.menus[e.index].path)
		},
		onCompanyOption(e, id, name) {
			console.log(e, id);
			let text = this.store_list[e.index].text
			if (text == "团队管理") {
				this.$navigateTo(`/shops/agent_list?id=${id}`)
				return
			}
			if (text == "门店设置") {
				this.$navigateTo(`/shops/editShop?id=${id}`)
				return
			}
			if (text == "邀请同事") {
				// this.showSharePop(name,id)
				this.showCopywriting(name, id)
				return
			}
			if (text == "刷新排名") {
				this.refreshStoreNum(id)
				return
			}
		},
		refreshStoreNum(store_id) {
			this.$ajax.get('agentCompany/refreshStore.html', { store_id }, res => {
				if (res.data.code === 1) {
					uni.showToast({
						title: res.data.msg
					})
				} else {
					uni.showToast({
						title: res.data.msg,
						icon: "none"
					})
				}
			})
		},
		showSharePop(name, id) {
			// uni.hideTabBar()
			this.shareId = id
			this.shareName = name
			this.$refs.show_share_pop.show()
		},

		// #ifdef H5
		copyLink() {
			let link = 'https://' + window.location.host + '/h5/shops/be_invited?user_id=' + this.user_info.id + "&store_id=" + this.shareId
			this.copyWechatNum(link, () => {
				uni.showToast({
					title: '复制成功,去发送给好友吧',
					icon: 'none'
				})
			})
		},
		// #endif
		// #ifndef H5
		copyWechatNum(cont) {
			uni.setClipboardData({
				data: cont,
				success: res => {
					// uni.showToast({
					//   title: "复制成功",
					//   icon: "none"
					// })
				}
			})
		},
		// #endif
		// #ifdef H5
		copyWechatNum(cont, callback) {
			let oInput = document.createElement('textarea')
			oInput.value = cont
			document.body.appendChild(oInput)
			oInput.style.opacity = 0
			oInput.select() // 选择对象;
			document.execCommand('Copy') // 执行浏览器复制命令
			uni.showToast({
				title: '复制成功',
				icon: 'none'
			})
			oInput.remove()
			if (callback) callback()
		},
		// #endif
		showCopywriting(name, id) {
			let link = ''
			// #ifdef H5
			link = 'https://' + window.location.host + '/m/shops/be_invited?user_id=' + this.user_info.id + "&store_id=" + id
			// #endif
			// #ifndef H5
			link = config.apiDomain + '/h5/shops/be_invited?invite_id=' + this.user_info.id + "&store_id=" + id
			// #endif
			const text = `【门店名称】${name}
【邀请者】${this.user_info.cname || ''}
【邀请链接】${link}`
			this.copyWechatNum(text, () => {
				this.copy_success = true
			})

		},
		// #ifndef H5
		copyWechatNum(cont) {
			uni.setClipboardData({
				data: cont,
				success: res => {
					// uni.showToast({
					//   title: "复制成功",
					//   icon: "none"
					// })
				}
			})
		},
		// #endif
		// #ifdef H5
		copyWechatNum(cont, callback) {
			let oInput = document.createElement('textarea')
			oInput.value = cont
			document.body.appendChild(oInput)
			oInput.select() // 选择对象;
			document.execCommand('Copy') // 执行浏览器复制命令
			uni.showToast({
				title: '复制成功',
				icon: 'none'
			})
			oInput.remove()
			if (callback) callback()
		},
		// #endif
		agentRefresh() {
			this.$ajax.get('agent/agentRefresh.html', {}, res => {
				uni.showToast({
					title: res.data.msg,
					icon: res.data.code === 1 ? 'success' : 'none'
				})
			})
		},
		copyShopInfo() {
			let link = ''
			// #ifdef H5
			link = 'https://' + window.location.host + '/shops/addShop?id=' + this.company.id
			// #endif
			// #ifndef H5
			link = config.apiDomain + '/shops/addShop?id=' + this.company.id
			// #endif
			const text = `【公司名称】${this.company.name}
【访问链接】${link}`
			this.copyWechatNum(text, () => {
				this.copy_success = true
			})

		},
		// #ifndef H5
		copyWechatNum(cont) {
			uni.setClipboardData({
				data: cont,
				success: res => {
					// uni.showToast({
					//   title: "复制成功",
					//   icon: "none"
					// })
				}
			})
		},
		// #endif
		// #ifdef H5
		copyWechatNum(cont, callback) {
			let oInput = document.createElement('textarea')
			oInput.value = cont
			document.body.appendChild(oInput)
			oInput.select() // 选择对象;
			oInput.setSelectionRange(0, oInput.value.length);
			document.execCommand('Copy') // 执行浏览器复制命令
			uni.showToast({
				title: '复制成功',
				icon: 'none'
			})
			oInput.blur()
			oInput.remove()
			if (callback) callback()
		},
		// #endif
		onShopownerOption(e) {
			if (e.index == 0) {   //邀请  复制文案
				this.copyShopInfo()
				return
			}
			if (e.index == 1) {   //我的公司
				this.$navigateTo(`/shops/myCompany`)
				return
			}
			if (e.index == 2) {   //添加门店
				this.$navigateTo(`/shops/addShop?id=${this.company.id}`)
				return
			}
		},
		onAdviserOption(e) {
			if (e.index === 4) {
				this.adviserRefresh()
				return
			}
			if (e.index === 1) {
				this.userNavigateTo(`/user/consultant/addpost?buildid=${this.adviser_info.build_ids}`)
				return
			}
			this.userNavigateTo(this.adviser_release_list[e.index].url)
		},
		adviserRefresh() {
			this.$ajax.get('adviser/refresh.html', {}, res => {
				uni.showToast({
					title: res.data.msg,
					icon: res.data.code === 1 ? 'success' : 'none'
				})
			})
		},
		handleTel() {
			//苹果手机拨打400电话不能带空格  
			uni.makePhoneCall({
				phoneNumber: this.servicePhone
			})
		},
		kefu() { //客服链接
			this.$navigateTo(this.wx_service_link)
			console.log(this.wx_service_link);
		},
		onClickSign() {
			uni.showToast({
				title: '暂未开放',
				icon: 'none'
			})
		},
		onClickService() {
			uni.showToast({
				title: '暂未开放',
				icon: 'none'
			})
		},
		clearStorage() {
			let token = uni.getStorageSync('token') //记录token防止被清除
			let read_privacy_olicy = uni.getStorageSync('read_privacy_olicy') //记录token防止被清除
			try {
				uni.clearStorageSync();
				uni.showToast({
					title: "清除缓存成功"
				})
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "清除缓存失败",
					icon: "none"
				})
			}
			uni.setStorageSync('token', token) //重新存储token
			if (read_privacy_olicy) {
				uni.setStorageSync('read_privacy_olicy', read_privacy_olicy) //重新存储阅读用户协议
			}
		},
		logout() {
			this.$ajax.get("member/logout", {}, res => {
				console.log(res);
			})
			uni.clearStorageSync();
			location.reload()
		},
		logoutApp() {
			this.$ajax.get("member/logout", {}, res => {
				console.log(res);
			})
			uni.clearStorageSync();
			this.setAllowOpen(true);
			this.getUserInfo({})
			this.getData()
		},
		signIn() { //签到
			checkUserStatus(() => { //判断用户状态（是否登录，是否绑定手机号等）
				this.userNavigateTo('/user/task_center')
			})
			// if (this.user_info.is_sign) {
			// 	uni.showToast({
			// 		title: "您今天已经签到过了",
			// 		icon: "none"
			// 	})
			// 	return
			// }
			// this.$ajax.get('member/sign.html', {}, res => {
			// 	if (res.data.code == 1) {
			// 		uni.showToast({
			// 			title: res.data.msg
			// 		})
			// 		this.getData()
			// 	} else {
			// 		uni.showToast({
			// 			title: res.data.msg,
			// 			icon: "none"
			// 		})
			// 	}
			// })
		},
		toVipInfo() {
			if (this.is_adviser) {
				this.userNavigateTo('/user/adviser_rights')
			} else {
				this.userNavigateTo('/user/my_vip')
			}
		},
		toLogin() {
			this.setAllowOpen(true);
			uni.removeStorageSync('token')
			this.$navigateTo("/user/login/login")
			// this.getData()
			// this.$navigateTo("/user/login/login")
			// if(uni.getStorageSync('token')){
			// 	this.$navigateTo("/user/bind_phone/bind_phone")
			// }else{
			// 	this.$navigateTo("/user/login/login")
			// }
		},
		userNavigateTo(path) {
			if (this.login_status < 2) {
				uni.removeStorageSync('token')
				this.$navigateTo('/user/login/login')
				return
			}
			if (this.login_status === 2) {
				this.$navigateTo('/user/bind_phone/bind_phone')
				return
			}
			this.$navigateTo(path)
		}
	},
	onTabItemTap(e) {
		uni.$emit('onTabItemTap', e)
	}
}
</script>


<style scoped lang="scss">
// @import '../../static/css/iconfont';
view {
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}

.flex-row {
	flex-direction: row;
}

.is_vip {
	.header-box {
		background: #1E1F25;

		.user_card {
			background-image: linear-gradient(135deg, #E5BA72 0%, #F4DBB3 100%);

			.user_info {
				.user_level {
					background-color: rgba($color: #000000, $alpha: 0.3);

					.level_name {
						color: #E5BA72;

						&.hui {
							color: #666;
						}
					}
				}

				.btn {
					background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.50) 0%, #FFFFFF 100%);
					color: #A06B14;
				}
			}

			.user_data {
				.data_item {
					.value {
						color: #333;
					}

					.label {
						color: #A06B14;
					}
				}
			}
		}
	}
}

.navigator-hover {
	background-color: $uni-bg-color-hover;
}

.header-box {
	width: 100%;
	height: 292rpx;
	// background-color: #1E1F25;
	background-image: linear-gradient(0deg, #F7918F 0%, #FB656A 100%);
	position: relative;

	.bg {
		border-width: 147rpx 50vw 147rpx 50vw;
		border-style: solid;
		border-color: transparent transparent #ffffff transparent;
	}

	.user_card {
		position: absolute;
		padding: 24rpx;
		top: 48rpx;
		left: 48rpx;
		right: 48rpx;
		border-radius: 24rpx;
		background-color: #fff;
		box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.15);
		z-index: 2;

		.user_info {
			align-items: center;
			padding-bottom: 24rpx;
			position: relative;

			:after {
				content: "";
				position: absolute;
				height: 1rpx;
				left: 0;
				right: 0;
				bottom: 0;
				-webkit-transform: scaleY(.5);
				transform: scaleY(.5);
				background-color: rgba($color: #000000, $alpha: 0.01);
			}

			.user_img {
				width: 128rpx;
				height: 128rpx;
				border-radius: 50%;
				margin-right: 24rpx;
			}

			.user_info-right {
				display: inline-block;
				overflow: hidden;
			}

			.user_name {
				margin-bottom: 16rpx;
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
			}

			.user_level {
				align-items: center;
				padding: 0 20rpx;
				height: 48rpx;
				line-height: 48rpx;
				border-radius: 24rpx;
				background-color: rgba($color: #000000, $alpha: 0.1);
				overflow: hidden;
				max-width: 100%;

				.level_icon {
					width: 32rpx;
					height: 32rpx;
				}

				.level_name {
					flex: 1;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					margin-left: 10rpx;
					font-size: 28rpx;
					color: #666;
				}
			}

			.btn {
				height: 64rpx;
				line-height: 64rpx;
				padding: 0 24rpx;
				border-radius: 32rpx;
				font-size: 22rpx;
				background-image: linear-gradient(-45deg, #F7918F 0%, #FB656A 100%);
				color: #fff;
			}
		}

		.user_data {
			justify-content: space-between;
			padding: 24rpx 0;

			.data_item {
				align-items: center;

				.value {
					font-size: 32rpx;
					font-weight: bold;
					color: $uni-color-primary;
				}

				.label {
					margin-top: 5rpx;
					font-size: 22rpx;
					color: #333;
				}
			}
		}
	}
}

.entrance {
	padding: 90rpx 24rpx 0 24rpx;
	background-color: #fff;
}

.list {
	margin-top: 20rpx;
	background-color: #fff;

	.list-item {
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 48rpx;

		image {
			width: 40rpx;
			height: 40rpx;
		}

		.text {
			flex: 1;
			margin-left: 10rpx;
			font-size: 32rpx;
			color: #333;
		}

		.unit {
			margin-left: 18rpx;
			font-size: 22rpx;
			color: #999;
		}

		.into-box {
			font-size: 22rpx;
			color: #999;
		}

		.right {
			align-items: center;
			margin-left: auto;
			font-size: 22upx;
			color: #999;
		}
	}

	.btn {
		margin: 24rpx 48rpx;
		line-height: 88rpx;
		border-radius: 44rpx;
		text-align: center;
		font-size: 32rpx;
		font-weight: bold;
		background-color: $uni-color-primary;
		box-shadow: 0 4px 12px 0 rgba(251, 101, 106, 0.40);
		color: #fff;
	}
}

.release-grid {
	padding: 0 24rpx;
}

.store-list {
	white-space: nowrap;

	.store-item {
		width: 80vw;
		display: inline-block;
		margin: 24upx 24upx 24upx 0;
		border: 1px solid #D8D8D8;
		box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
		border-radius: 8px;
		background: #fff;
		padding: 24upx 0 48upx;

		&:first-child {
			margin-left: 48upx;
		}

		.store_info {
			padding: 0 48upx;
			align-items: center;

			.store_title {
				font-size: 14px;
				color: #333333;
				font-weight: 600;
			}

			.to_store {
				align-items: center;
				color: #999;
				font-size: 22upx;
				margin-left: auto;

			}
		}

		.bottom {
			padding: 24upx 40upx 0;
		}
	}
}

.store_owner {
	.list {
		.right {}
	}
}

.data-grid {
	padding: 0 48rpx;
	padding-bottom: 20rpx;
	justify-content: space-between;
	color: #333;
	flex-wrap: wrap;

	.data-item {
		flex: 1;
		padding: 24rpx 4rpx;
		align-items: center;

		.value {
			font-size: 42rpx;
			font-weight: bold;
			margin-bottom: 10rpx;
			align-items: flex-end;
			flex-direction: row;

			.unit {
				position: relative;
				bottom: 6rpx;
				left: 6rpx;
				font-size: 22rpx
			}
		}

		.label {
			font-size: 22rpx;
			color: #999
		}
	}
}

.build-data {
	// padding-top: 24rpx;
	padding-left: 48rpx;

	swiper {
		height: 278rpx;
	}

	swiper-item {
		height: 100%;
	}

	.swiper-item {
		height: 226rpx;
		margin-right: 24rpx;
		border: 1px solid #D8D8D8;
		box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.05);
		border-radius: 16rpx;

		.build_name {
			text-align: center;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			padding: 24rpx;
			padding-bottom: 0;
			font-size: 30rpx;
			color: #333;
		}

		.data-grid {
			padding: 0 24rpx;
		}
	}
}

.qrcode-box {
	position: relative;
	margin-top: 15vh;

	.img-box {
		width: 584rpx;
		padding: 12rpx;
		margin: auto;
		background-color: #fff;
		border-radius: 8rpx;
		overflow: hidden;

		.title {
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
			color: #333;
		}

		.tip {
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			color: #666;
		}
	}

	.qrcode {
		width: 560rpx;
		height: 560rpx;
	}

	.icon-box {
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
}

.alert {
	margin: 0 48rpx;
	padding: 20rpx 24rpx;
	border-radius: 8rpx;
	justify-content: space-between;
	align-items: center;
	font-size: 24rpx;
	line-height: 1;
	background-color: #ffeeef;
	color: $uni-color-primary;

	.text {
		margin: 0 16rpx;
	}
}

.ic_dingyuehao {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_dingyuehao%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M56.888889%2056.888889h796.444444v910.222222H56.888889V56.888889z%20m227.555555%20284.444444a56.888889%2056.888889%200%201%200%200-113.777777%2056.888889%2056.888889%200%200%200%200%20113.777777zM227.555556%20398.222222h227.555555v56.888889H227.555556V398.222222zM170.666667%20170.666667v682.666666h568.888889V170.666667H170.666667z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M711.111111%20711.111111m-256%200a256%20256%200%201%200%20512%200%20256%20256%200%201%200-512%200Z%22%20fill%3D%22%23FB656A%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M711.111111%20455.111111a256%20256%200%201%201%200%20512%20256%20256%200%200%201%200-512z%20m0%2056.888889a199.111111%20199.111111%200%201%200%200%20398.222222%20199.111111%20199.111111%200%200%200%200-398.222222z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M573.326222%20710.485333l40.220445-40.220444%20120.661333%20120.661333-40.220444%2040.220445z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M653.767111%20790.926222l160.881778-160.881778%2040.220444%2040.220445L694.044444%20831.146667z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_fankui {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_fankui%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M341.333333%20853.333333h625.777778v113.777778H341.333333z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M56.888889%20695.637333L632.490667%2051.996444l295.480889%20285.639112L555.235556%20773.12l-86.471112-74.012444%20303.104-354.133334-133.461333-129.024L170.666667%20739.043556v114.232888L341.333333%20853.333333v113.777778H56.888889z%22%20fill%3D%22%23FB656A%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_wofabude {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_wofabude%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M568.888889%20284.444444v227.555556h170.666667v113.777778H455.111111V284.444444h113.777778z%22%20fill%3D%22%23FB656A%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M512%200a512%20512%200%201%200%200%201024A512%20512%200%200%200%20512%200z%20m0%20113.777778a398.222222%20398.222222%200%201%201%200%20796.444444A398.222222%20398.222222%200%200%201%20512%20113.777778z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_xiazai {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_xiazai%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M967.111111%20113.777778v853.333333H56.888889V113.777778h227.498667v113.720889L174.193778%20227.555556v628.053333H853.333333V227.555556l-114.119111-0.056889V113.777778H967.111111z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M568.888889%2056.888889v461.880889l63.829333-63.715556%2080.440889%2080.440889L512%20736.597333%20310.897778%20535.495111l80.440889-80.497778%2063.715555%2063.772445L455.111111%2056.888889h113.777778z%22%20fill%3D%22%23FB656A%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_order {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22t-icon-order%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M227.555556%20170.666667H170.666667v682.666666h568.888889V170.666667h-56.888889V56.888889h170.666666v910.222222H56.888889V56.888889h170.666667v113.777778z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M284.444444%200h341.333334v170.666667H284.444444zM227.555556%20284.444444h455.111111v56.888889H227.555556zM227.555556%20398.222222h284.444444v56.888889H227.555556z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M711.111111%20711.111111m-256%200a256%20256%200%201%200%20512%200%20256%20256%200%201%200-512%200Z%22%20fill%3D%22%23FB656A%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M711.111111%20455.111111a256%20256%200%201%201%200%20512%20256%20256%200%200%201%200-512z%20m0%2056.888889a199.111111%20199.111111%200%201%200%200%20398.222222%20199.111111%20199.111111%200%200%200%200-398.222222z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M573.326222%20710.485333l40.220445-40.220444%20120.661333%20120.661333-40.220444%2040.220445z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M653.767111%20790.926222l160.881778-160.881778%2040.220444%2040.220445L694.044444%20831.146667z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_fabu {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_fabu%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M512%20170.666667v113.777777H170.666667v568.888889h568.888889V455.111111h113.777777v512H56.888889V170.666667h455.111111z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M967.111111%2056.888889v284.387555h-113.777778V238.705778l-276.48%20248.888889-76.117333-84.536889L758.897778%20170.666667H625.777778V56.888889h341.333333z%22%20fill%3D%22%23FB656A%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_kefu {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_kefu%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M796.444444%20455.111111a284.444444%20284.444444%200%200%200-568.604444-12.344889L227.555556%20455.111111v170.666667H113.777778V455.111111a398.222222%20398.222222%200%201%201%20796.444444%200v170.666667h-113.777778V455.111111z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M56.888889%20512h227.555555v284.444444H56.888889z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M113.777778%20967.111111v-170.666667h113.777778v113.777778h341.333333v-56.888889h227.555555v113.777778H113.777778z%22%20fill%3D%22%23FB656A%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M739.555556%20512h227.555555v284.444444h-227.555555z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_guanyu {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_guanyu%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M562.0000001%20411.9999998v349.99999981h50.0000001v50.0000001H461.9999999V461.9999999H411.9999998V411.9999998z%22%20fill%3D%22%23FB656A%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M512%20287m-74.99999971%200a74.99999971%2074.99999971%200%201%200%20149.99999942%200%2074.99999971%2074.99999971%200%201%200-149.99999942%200Z%22%20fill%3D%22%23FB656A%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M512%2062a450%20450%200%201%200%200%20900A450%20450%200%200%200%20512%2062z%20m0%20100.0000002a349.9999998%20349.9999998%200%201%201%200%20699.9999996A349.9999998%20349.9999998%200%200%201%20512%20162.0000002z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_guanyu_vip {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_guanyu_vip%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M562.0000001%20411.9999998v349.99999981h50.0000001v50.0000001H461.9999999V461.9999999H411.9999998V411.9999998z%22%20fill%3D%22%23E5BA72%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M512%20287m-74.99999971%200a74.99999971%2074.99999971%200%201%200%20149.99999942%200%2074.99999971%2074.99999971%200%201%200-149.99999942%200Z%22%20fill%3D%22%23E5BA72%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M512%2062a450%20450%200%201%200%200%20900A450%20450%200%200%200%20512%2062z%20m0%20100.0000002a349.9999998%20349.9999998%200%201%201%200%20699.9999996A349.9999998%20349.9999998%200%200%201%20512%20162.0000002z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_order_vip {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22t-icon-order_vip%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M227.555556%20170.666667H170.666667v682.666666h568.888889V170.666667h-56.888889V56.888889h170.666666v910.222222H56.888889V56.888889h170.666667v113.777778z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M284.444444%200h341.333334v170.666667H284.444444zM227.555556%20284.444444h455.111111v56.888889H227.555556zM227.555556%20398.222222h284.444444v56.888889H227.555556z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M711.111111%20711.111111m-256%200a256%20256%200%201%200%20512%200%20256%20256%200%201%200-512%200Z%22%20fill%3D%22%23E5BA72%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M711.111111%20455.111111a256%20256%200%201%201%200%20512%20256%20256%200%200%201%200-512z%20m0%2056.888889a199.111111%20199.111111%200%201%200%200%20398.222222%20199.111111%20199.111111%200%200%200%200-398.222222z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M573.326222%20710.485333l40.220445-40.220444%20120.661333%20120.661333-40.220444%2040.220445z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M653.767111%20790.926222l160.881778-160.881778%2040.220444%2040.220445L694.044444%20831.146667z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_fabu_vip {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_fabu_vip%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M512%20170.666667v113.777777H170.666667v568.888889h568.888889V455.111111h113.777777v512H56.888889V170.666667h455.111111z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M967.111111%2056.888889v284.387555h-113.777778V238.705778l-276.48%20248.888889-76.117333-84.536889L758.897778%20170.666667H625.777778V56.888889h341.333333z%22%20fill%3D%22%23E5BA72%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_dingyuehao_vip {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_dingyuehao_vip%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M56.888889%2056.888889h796.444444v910.222222H56.888889V56.888889z%20m227.555555%20284.444444a56.888889%2056.888889%200%201%200%200-113.777777%2056.888889%2056.888889%200%200%200%200%20113.777777zM227.555556%20398.222222h227.555555v56.888889H227.555556V398.222222zM170.666667%20170.666667v682.666666h568.888889V170.666667H170.666667z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M711.111111%20711.111111m-256%200a256%20256%200%201%200%20512%200%20256%20256%200%201%200-512%200Z%22%20fill%3D%22%23E5BA72%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M711.111111%20455.111111a256%20256%200%201%201%200%20512%20256%20256%200%200%201%200-512z%20m0%2056.888889a199.111111%20199.111111%200%201%200%200%20398.222222%20199.111111%20199.111111%200%200%200%200-398.222222z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M573.326222%20710.485333l40.220445-40.220444%20120.661333%20120.661333-40.220444%2040.220445z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M653.767111%20790.926222l160.881778-160.881778%2040.220444%2040.220445L694.044444%20831.146667z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_fankui_vip {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_fankui_vip%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M341.333333%20853.333333h625.777778v113.777778H341.333333z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M56.888889%20695.637333L632.490667%2051.996444l295.480889%20285.639112L555.235556%20773.12l-86.471112-74.012444%20303.104-354.133334-133.461333-129.024L170.666667%20739.043556v114.232888L341.333333%20853.333333v113.777778H56.888889z%22%20fill%3D%22%23E5BA72%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_wofabude_vip {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_wofabude_vip%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M512%20284.444444v227.555556h170.666667v113.777778H398.222222V284.444444h113.777778z%22%20fill%3D%22%23E5BA72%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M512%200a512%20512%200%201%200%200%201024A512%20512%200%200%200%20512%200z%20m0%20113.777778a398.222222%20398.222222%200%201%201%200%20796.444444A398.222222%20398.222222%200%200%201%20512%20113.777778z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_kefu_vip {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_kefu_vip%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M796.444444%20455.111111a284.444444%20284.444444%200%200%200-568.604444-12.344889L227.555556%20455.111111v170.666667H113.777778V455.111111a398.222222%20398.222222%200%201%201%20796.444444%200v170.666667h-113.777778V455.111111z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M284.444444%20512v284.444444H227.555556v113.777778h341.333333v-56.888889h227.555555v113.777778H113.777778v-170.666667H56.888889V512h227.555555z%20m682.666667%200v284.444444h-227.555555V512h227.555555z%22%20fill%3D%22%23E5BA72%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}


.ic_xiazai_vip {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22tfy-icon-ic_xiazai_vip%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M967.111111%20113.777778v853.333333H56.888889V113.777778h227.498667v113.720889L174.193778%20227.555556v628.053333H853.333333V227.555556l-114.119111-0.056889V113.777778H967.111111z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M568.888889%2056.888889v461.880889l63.829333-63.715556%2080.440889%2080.440889L512%20736.597333%20310.897778%20535.495111l80.440889-80.497778%2063.715555%2063.772445L455.111111%2056.888889h113.777778z%22%20fill%3D%22%23E5BA72%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_shuju_vip {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22t-icon-ic_shuju_vip%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M56.888889%20853.333333h910.222222v113.777778H56.888889z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M113.777778%20341.333333h227.555555v512H113.777778zM398.222222%20227.555556h227.555556v625.777777H398.222222zM682.666667%2056.888889h227.555555v796.444444h-227.555555z%22%20fill%3D%22%23E5BA72%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}

.ic_huiyuanyingyong {
	background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22t-icon-ic_huiyuanyingyong%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M455.111111%2056.888889v398.222222H56.888889V56.888889h398.222222zM341.333333%20170.666667H170.666667v170.666666h170.666666V170.666667zM455.111111%20568.888889v398.222222H56.888889v-398.222222h398.222222z%20m-113.777778%20113.777778H170.666667v170.666666h170.666666v-170.666666z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M1011.882667%20115.2l-103.082667%20384.682667-384.682667-103.082667L627.2%2012.117333l384.682667%20103.082667z%20m-139.377778%2080.440889l-164.864-44.145778-44.145778%20164.864%20164.864%2044.145778%2044.145778-164.864z%22%20fill%3D%22%23E5BA72%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M967.111111%20568.888889v398.222222h-398.222222v-398.222222h398.222222z%20m-113.777778%20113.777778h-170.666666v170.666666h170.666666v-170.666666z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
</style>