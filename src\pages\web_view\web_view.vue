<template>
	<view>
		<web-view :src="url" :webview-styles="webviewStyles" @message="handleMessage"></web-view>
	</view>
</template>

<script>
	import { formatImg} from '../../common/index.js'
	export default {
		data() {
			return {
				url:"",
				webviewStyles: {
                    progress: {
                        color: '#f65354'
                    }
                }
			}
		},
		filters: {
			imgUrl(val, param = "") {
				return formatImg(val, param)
			}
		}, 
		onLoad(options){
			
			if(options.url){
				this.setUrl(options.url)
			}
		},
		methods: {
			setUrl(url){
				this.origin_url = decodeURIComponent(url)
				if(this.origin_url.includes('token=')){
					this.url = this.origin_url
					return
				}
				var token = uni.getStorageSync('token')

				// header.From 1：微信小程序，2：微信公众号，3：h5，4：PC，5：百度小程序，6：支付宝小程序，8：安卓 9 ios
				var headerFrom = ""
				// #ifdef MP-WEIXIN
				headerFrom = 1
				// #endif

				// #ifdef H5
				var ua = navigator.userAgent.toLowerCase();
				if (ua.match(/MicroMessenger/i) == "micromessenger") { //如果是微信
					headerFrom = 2
				}else{
					headerFrom = 3
				}
				// #endif

				// #ifdef MP-BAIDU
				headerFrom = 5
				// #endif

				// #ifdef APP-PLUS
				if (uni.getSystemInfoSync().platform =="ios"){
					headerFrom = 9
				}else{
					headerFrom = 8
				}
				// #endif

				if(this.origin_url.includes('?')){
					this.url = this.origin_url+`&token=${token}&headerFrom=${headerFrom}`
				}else{
					this.url = this.origin_url+`?token=${token}&headerFrom=${headerFrom}`
				}
				console.log(this.url)
			},
			handleMessage(e){
				if(e.type='navigate'&&e.url){
					this.$navigateTo(e.url)
					return
				}
				if(e.fun){
					e.fun()
					return
				}
				if(e.detail.data[0].type=="share"){
					this.share= e.detail.data[0].share
					this.title= e.detail.data[0].title
					// uni.setNavigationBarTitle({
					// 	title:this.title||""
					// })
					return
				}
			}
		},
		onShareAppMessage(res) {
			return {
				title:this.share?this.share.title:'',
				content:this.share?this.share.content:'',
				imageUrl:(this.share&&this.share.pic)?formatImg(this.share.pic,'w_6401'):"",
				path:'/pages/web_view/web_view?url='+encodeURIComponent(this.origin_url)

			}
			
			
		}
	}
</script>

<style>

</style>
