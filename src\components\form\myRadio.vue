<template>
	<view class="radio-box">
		<view class="label">{{label}}</view>
		<view class="radio-row">
			<radio-group @change="radioChange" class="flex-box">
				<label class="radio-item flex-box" v-for="(item, index) in range" :key="index">
					<radio :value="item.value | tostring" :disabled ="disabled" :checked="index === current" color="#f65354" />
					<view>{{item.name}}</view>
				</label>
			</radio-group>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			label:String,
			name:String,
			disabled:{
				type:[Number,String,Boolean],
				default:false
			},
      value: [String, Number],
			range:Array
		},
		data() {
			return {
				
			};
		},
		computed:{
			current(){
				if(this.value || this.value == 0){
					for(let i = 0;i<this.range.length;i++){
						if(this.range[i].value == this.value){
							return i
						}
					}
				}else{
					return -1
				}
			}
		},
		filters:{
			tostring(val){
				if(!val && val != 0){
					return ""
				}
				return val.toString()
			}
		},
		methods:{
			 radioChange(e) {

				let name = {
					_name:this.name
				}
				this.$emit('change', Object.assign(e,name))
			},
		}
	}
</script>

<style lang="scss">
.radio-box{
	padding: 24rpx 0;
	.label{
		margin-bottom: 24rpx;
		font-size: 22rpx;
		color: #666;
	}
}
	.radio-row{
		display: flex;
		background-color: #fff;
		.radio-item{
			align-items: center;
		}
	}
	.radio-row .title{
		min-width: 130upx;
		max-width: 220upx;
		font-size: $uni-font-size-lg;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-right: 38upx;
		text-align-last:justify;
	}
	radio-group.flex-box label{
		margin-right: 20upx;
	}
	radio-group.flex-box label radio{
		margin-right: 10upx;
		transform:scale(0.8);
	}
</style>
