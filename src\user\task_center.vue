<template>
  <view class="task_center">
    <!-- 顶部卡片 -->
    <view class="header">
      <image class="bg_img" :src="'/images/new_icon/<EMAIL>' | imageFilter" mode="widthFix"></image>
      <view class="btn" @click="$navigateTo('/user/exchange')">我的积分</view>
      <view class="info">
        <view class="title">已连续签到</view>
        <view class="day flex-row">
          <view class="day_num_box" v-for="(day, index) in day_array" :key="index">
            <image :src="'/images/new_icon/day_bg.png' | imageFilter('m_320')" mode="widthFix"></image>
            <text class="num">{{ day }}</text>
          </view>
          <text class="unit">天</text>
        </view>
      </view>
    </view>
    <!-- 预期奖励 -->
    <view class="expect flex-row" v-if="signPrizeContinuedDesc">
      <image src="../static/icon/liwu1.png" mode="aspectFill"></image>
      <text>{{signPrizeContinuedDesc}}</text>
    </view>
    <!-- 签到时间轴 -->
    <view class="time_line flex-row">
      <view
        class="time_line-item flex-row"
        :class="{ 'flex-1': index < integral_list.length - 1 }"
        v-for="(item, index) in integral_list"
        :key="index"
      >
        <view class="info">
          <view class="day">{{ item.week_name }}</view>
          <template v-if="item.is_sign">
            <view v-if="judgeTime(item.week_time)" class="integral active">+{{ item.score }}</view>
            <my-icon class="integral-wancheng" v-else type="ic_qiandaowancheng" color="#ff656b" size="64rpx"></my-icon>
          </template>
          <template v-else>
            <view v-if="index===expect_week_index&&expect_week_index>=current_week_index" class="integral expect-icon"></view>
            <view v-else class="integral">+{{ item.score }}</view>
          </template>
          
          <view v-if="index===current_week_index&&signPrizeDesc" class="arrow"></view>
        </view>
        <view class="line" v-if="index < integral_list.length - 1"></view>
      </view>
    </view>
    <view v-if="signPrizeDesc" class="sign_tip">{{signPrizeDesc}}</view>
    <!-- 签到按钮 -->
    <view class="button-box">
      <view class="button" @click="signIn()">{{isSign?'每天一句正能量~>':'立即签到'}}</view>
    </view>
    <view class="task_box">
      <view class="task_label">
        今日任务
      </view>
      <view class="task_list">
        <view class="task_item flex-row bottom-line" hover-class="on_hover" v-for="(item, index) in task_list" :key="index" @click="doTask(item.path)">
          <view class="task_info">
            <view class='task_title'>{{item.name}}</view>
          </view>
          <view class="task_icon">
            <image :src="item.give_type | iconFormat"></image>
          </view>
          <text class="num">+{{item.give_num}}</text>
          <!-- <view class="task_btn" @click="doTask(item.path)">去完成</view> -->
          <my-icon v-if="item.status" type="wancheng" color="#72dd66" size="36rpx"></my-icon>
          <my-icon v-else type="ic_into" color="#d8d8d8" size="32rpx"></my-icon>
        </view>
      </view>
    </view>
    <view class="service_list flex-row" v-if="show_bottom_options">
      <view class="service_item flex-row red" @click="$navigateTo(item.path)" v-for="(item, index) in options" :key="index">
        <view class="info">
          <text class="title">{{item.title}}</text>
          <text class="sub_title">{{item.desc}}</text>
        </view>
        <image class="service_icon" :src="item.icon"></image>
      </view>
    </view>
    <my-popup ref="success" position="center" height="800rpx">
      <view class="success-box">
        <view class="success-header">
          <image mode="aspectFill" :src="'/images/new_icon/sign_success.png' | imageFilter('m_6401')"></image>
        </view>
        <view class="success-info">
          <text class="title">签到成功</text>
          <view class="tip-box flex-row" v-if="integral||active">
            <text>恭喜你获得</text>
            <text class="highlight" v-if="integral">{{integral}}积分</text>
            <text v-if="integral&&active">,</text>
            <text class="highlight" v-if="active">{{active}}活跃度</text>
          </view>
          <view class="tip-box flex-row" v-if="winPrizeDesc">
            <text class="highlight">{{winPrizeDesc}}</text>
          </view>
          <text class="tip" v-if="day">在连续签{{day}}天可获得额外奖励</text>
        </view>
        <view class="btn-box">
          <view class="btn" @click="goPoster">每天一句正能量~></view>
          <view class="tip">记得明天再来哟~</view>
        </view>
        <view class="close-icon-box" @click="$refs.success.hide()">
          <my-icon type="guanbi" color="#fff" size="60rpx"></my-icon>
        </view>
      </view>
    </my-popup>
    <chat-tip></chat-tip>
  </view>
</template>

<script>
import myIcon from '../components/myIcon'
import myPopup from '../components/myPopup'
import {config} from '../common/config.js'
export default {
  data() {
    return {
      day_array: [0, 0, 0],
      integral_list: [],
      complete_num:0,
      task_list: [],
      is_adviser: 0, //1:普通会员；2:经纪人；3:置业顾问
      user_info: {},
      levelObj: {
        1: 'tong',
        2: 'yin',
        3: 'jin',
        4: 'tong',
        5: 'yin',
        6: 'jin'
      },
      ranking_active: '',
      integral:'',
      active:'',
      day:0,
      show_bottom_options: false,
      options:[],
      winPrizeDesc: "",
      current_week_index: 0,
      signPrizeDesc: "",
      signPrizeContinuedDesc: "",
      expect_week_index: 0,
      isSign:0,
    }
  },
  components: {
    myIcon,
    myPopup
  },
  computed:{
  },
  filters: {
    // 赠送类型的图标
    iconFormat(type) {
      let icon = 'https://images.tengfangyun.com/images/icon/'
      switch (type) {
        case 1:
          icon += 'hyd.png'
          break
        case 2:
          icon += 'jf.png'
          break
        case 3:
          icon += 'jb.png'
          break
        case 4:
          icon += 'ye.png'
          break
      }
      return icon
    },
    // 赠送 类型
    typeFormat(type) {
      switch (type) {
        case 1:
          return '活跃度'
        case 2:
          return '积分'
        case 3:
          return '金币'
        case 4:
          return '余额'
      }
    },
    levelFormat(level) {
      let levelObj = {
        1: 'tong',
        2: 'yin',
        3: 'jin',
        4: 'tong',
        5: 'yin',
        6: 'jin'
      }
      return levelObj[level]
    }
  },
  onLoad(options) {
    if(options.referer === "wxnotice"){
      this.show_bottom_options = true
    }
    // this.signIn()
    this.current_week_index = (new Date().getDay()||7) -1
    console.log(this.current_week_index)
    this.getTask()
  },
  methods: {
    signIn() {
      if (this.$store.state.user_info.is_sign) {
        // uni.showToast({
        //     title: "您今天已经签到过了",
        //     icon: "none"
        // })
        this.goPoster()
        return
      }
      this.$ajax.get('member/sign.html', {}, res => {
        if (res.data.code == 1) {
          this.$store.state.user_info.is_sign = 1
          this.integral = res.data.integral||''
          this.active = res.data.active||''
          this.winPrizeDesc = res.data.winPrizeDesc
          this.day = res.data.day||0
          this.getTask()
          this.$refs.success.show()
        }else{
          uni.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
    },
    getTask() {
      this.$ajax.get('tasks/taskList.html', {}, res => {
        if (res.data.code == 1) {
          this.integral_list = res.data.week
          this.task_list = res.data.tasks
          this.complete_num = res.data.finishCount
          this.user_info = res.data.user
          this.signPrizeDesc = res.data.signPrizeDesc||''
          if(res.data.user.signCount<10){
            this.day_array = [0, 0, res.data.user.signCount]
          }else if(res.data.user.signCount<100){
            this.day_array = [0].concat(res.data.user.signCount.split(''))
          }else{
            this.day_array = res.data.user.signCount.split('')
          }
          this.$store.state.user_info.is_sign=0
          let today =this.getWeekDate()
          this.integral_list.map(item=>{
            if (item.week_name==today){
              if (item.is_sign==1){
                this.$store.state.user_info.is_sign=1
                this.isSign =1
              }else {
                this.$store.state.user_info.is_sign=0
                this.isSign =0
              }
            }
          })
          this.signPrizeContinuedDesc = res.data.signPrizeContinuedDesc||''
          this.expect_week_index = this.current_week_index + res.data.signPrizeContinuedDays
          if (this.isSign == 0) {
            this.expect_week_index--
          }
          console.log(this.$store.state.user_info.is_sign);
          this.is_adviser = res.data.is_adviser
        }
        if(res.data.nav){
          this.options = res.data.nav
        }
        this.ranking_active = res.data.ranking_active || ''
      })
    },
    goPoster(){
      this.$refs.success.hide()
      let url ='',type=0,id='';
      if (this.user_info.adviser_id){
          type =1
          id =this.user_info.adviser_id
      }else if (this.user_info.agent_id){
        type =2
        id =this.user_info.agent_id
      }else {
        type =3
        id =this.user_info.id
      }
      // #ifdef H5 
        url =window.location.origin+'/wapi/poster/cloud?type='+type+'&id='+id+'&suiji=1&header_from=2'
      // #endif
      // #ifndef H5 
        url =config.apiDomain+'/wapi/poster/cloud?type='+type+'&id='+id+'&suiji=1&header_from=2'
      // #endif
      this.$navigateTo(url)
    },
    judgeTime(time){
      var currentDay = new Date().getDate()
      var timeDay = new Date(time*1000).getDate()
      return currentDay===timeDay
    },
    getWeekDate() {
        var now = new Date();
        var day = now.getDay();
        var weeks = new Array("周天", "周一", "周二", "周三", "周四", "周五", "周六");
        var week = weeks[day];
        return week;
    },
    upgradingRanking() {
      let api
      if (this.is_adviser === 3) {
        api = 'Adviser/upRank.html'
      } else if (this.is_adviser === 2) {
        api = 'agent/upgradingRanking'
      }
      this.$ajax.get(api, {}, res => {
        if (res.data.code === 1) {
          uni.showToast({
            title: res.data.msg || '操作成功'
          })
        } else {
          uni.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          })
        }
      })
    },
    doTask(path) {
      if (path) {
        if(path.indexOf('/')!==0&&path.indexOf('https:')!==0){
          this.$navigateTo('/'+path)
        }else{
           this.$navigateTo(path)
        }
      }
    }
  }
}
</script>

<style lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}
.task_center {
  background-color: #fff;
}

.header {
  margin: 0 48rpx;
  position: relative;
  border-radius: 30rpx;
  overflow: hidden;
  .bg_img {
    width: 100%;
    background-image: linear-gradient(180deg, #ff676c 0%, #fe8e8d 100%);
  }
  .btn {
    padding: 15rpx;
    position: absolute;
    right: 0;
    top: 26rpx;
    font-size: 22rpx;
    color: #fff;
  }
  .info {
    position: absolute;
    left: 48rpx;
    top: 48rpx;
    .title {
      line-height: 1;
      margin-bottom: 20rpx;
      font-size: 32rpx;
      color: #fff;
    }
    .day {
      align-items: flex-end;
      color: #fff;
      .unit {
        margin-left: 10rpx;
      }
      .day_num_box {
        width: 80rpx;
        position: relative;
        ~ .day_num_box {
          margin-left: 12rpx;
        }
        image {
          width: 100%;
        }
        .num {
          height: 100%;
          width: 100%;
          text-align: center;
          line-height: 110rpx;
          position: absolute;
          font-size: 64rpx;
          color: $uni-color-primary;
        }
      }
    }
  }
}

.time_line {
  padding: 24rpx 48rpx;
  justify-content: space-between;
  background-color: #fff;
  .time_line-item {
    align-items: flex-start;
    color: #999;
    .info {
      align-items: center;
      position: relative;
      .arrow{
        position: absolute;
        width: 32rpx;
        height: 32rpx;
        bottom: -52rpx;
        transform: rotate(45deg);
        border-top: 1rpx solid $uni-color-primary;
        border-left: 1rpx solid $uni-color-primary;
        background-color: #fff;
      }
    }
    .line {
      height: 2rpx;
      flex: 1;
      position: relative;
      top: 88rpx;
      background-color: #f5f5f5;
    }
  }
  .integral {
    width: 64rpx;
    height: 64rpx;
    line-height: 64rpx;
    text-align: center;
    font-size: 22rpx;
    color: #999;
    background-color: #f5f5f5;
    border-radius: 50%;
    margin-top: 24rpx;
    &.active{
      background-color: $uni-color-primary;
      font-weight: bold;
      color: #fff;
    }
  }
  .day {
    font-size: 22rpx;
  }
}
.button-box {
  padding: 24rpx 48rpx;
  padding-bottom: 32rpx;
  background-color: #fff;
  .button {
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #fff;
    background: #fb656a;
    box-shadow: 0 8rpx 32rpx 0 rgba(251, 101, 106, 0.4);
    border-radius: 44rpx;
    border-radius: 44rpx;
  }
}

.success-box{
  margin: 48rpx 68rpx 120rpx 68rpx;
  background-color: #fff;
  border-radius: 16rpx;
  position: relative;
  .success-header{
    padding: 24rpx 0;
    height: 168rpx;
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
    background-image: linear-gradient(-41deg, #F7918F 0%, #FB656A 100%);
    >image{
      width: 100%;
      height: 100%;
    }
  }
  .success-info{
    text-align: center;
    .title{
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
      margin-top: 48rpx;
    }
    .tip-box{
      margin-top: 16rpx;
      justify-content:center;
      font-size: 28rpx;
      color: #333;
      .highlight{
        color: $uni-color-primary;
      }
    }
    .tip{
      font-size: 22rpx;
      color: #999;
      margin-top: 24rpx;
    }
  }
  .btn-box{
    padding: 24rpx 48rpx;
    .btn{
      line-height: 88rpx;
      font-size: 32rpx;
      color: #fff;
      background-color: $uni-color-primary;
      box-shadow: 0 4px 16px 0 rgba(251,101,106,0.40);
      border-radius: 44rpx;
      text-align: center;
    }
    .tip{
      font-size: 22rpx;
      text-align: center;
      padding: 24rpx 48rpx;
      color: #999;
    }
  }
  .close-icon-box{
    width: 60rpx;
    height: 60rpx;
    left: 0;
    right: 0;
    margin: auto;
    position: absolute;
    bottom: -100rpx;
  }
}

.task_box{
  margin-top: 24rpx;
  padding: 24rpx 0;
  .task_label{
    font-size: 40rpx;
    padding: 0 48rpx;
    font-weight: bold;
  }

}
.task_list {
  background: #fff;
}

.task_item {
    display: flex;
    align-items: center;
    padding: 20upx 48rpx;

    .task_icon {
        width: 70upx;
        min-width: 70upx;
        height: 70upx;
        margin-right: 10upx;
        image{
            width: 100%;
            height: 100%
        }
    }

    .task_info {
        flex: 1;
        overflow: hidden;
        margin-right: 20upx;

        .task_title {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 30upx;
        }

        .task_note {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 24upx;
            color: #666;
        }
    }

    .num {
        margin-right: 10upx;
        font-size: 28upx;
        display: inline-block;
        width: 96upx;
        color: #ff6b9c;
    }

    .task_btn {
        width: 120rpx;
        padding: 5upx 20upx;
        height: 50upx;
        box-sizing: border-box;
        line-height: 40upx;
        border-radius: 10upx;
        font-size: 26upx;
        background: #fb656a;
        color: #fff;
    }
    .empty_btn{
      width: 120rpx;
    }
}

.on_hover{
    background-color: #f7f7f7;
}


.service_list{
  padding: 24rpx 48rpx;
  margin-bottom: 48rpx;
  flex-wrap: wrap;
  justify-content: space-between;
  .service_item{
    width: 316rpx;
    height: 130rpx;
    padding: 18rpx;
    margin-bottom: 24rpx;
    border-radius: 8rpx;
    align-items: center;
    justify-content: space-between;
    border: 1rpx solid #d8d8d8;
    .title{
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
      color: #333;
    }
    .sub_title{
      font-size: 22rpx;
      color: #999;
    }
    .service_icon{
      width: 64rpx;
      height: 64rpx;
    }
  }
}
.sign_tip{
  margin: 12rpx 48rpx;
  padding: 16rpx 24rpx;
  border-radius: 10rpx;
  border: 1rpx solid $uni-color-primary;
  color: $uni-color-primary;
  box-shadow: 0 2rpx 8rpx 0 rgba(251, 101, 106, 0.4);
}
.expect {
  align-items: center;
  margin: 40rpx 48rpx 0 48rpx;
  // background: #fff0f0;
  // color: #fb7a7e;
  background-color: #ffeeef;
	color: $uni-color-primary;
  padding: 16rpx 20rpx;
  border-radius: 14rpx;
  image {
    width: 34rpx;
    height: 34rpx;
    margin-right: 20rpx;
  }
}
.expect-icon {
  background: url(../static/icon/liwu2.png) no-repeat no-repeat center;
  background-size: 64rpx 64rpx;
}
.integral-wancheng {
  margin-top: 24rpx;
}
</style>
