<template>
  <view
    class="school-item bottom-line"
    hover-class="on-hover"
    :hover-start-time="20"
    :hover-stay-time="70"
    @click="onClick"
  >
    <view class="left">
      <my-icon
        v-if="mode === 'icon'"
        :type="icon.type"
        :color="icon.color"
        :size="icon.size"
      ></my-icon>
      <image
        v-else-if="mode === 'image'"
        :src="image"
        :class="image_type"
        mode="aspectFill"
      ></image>
    </view>
    <view class="center">
      <view class="title">
        <view class="text-box">
          <text class="text" :style="{ fontSize: title_size }">{{
            title
          }}</text>
          <slot name="title_right" >
          <text :class="'attr'+type">{{ type_name}}</text>
          </slot>
        </view>
      </view>
      
      <!-- <view class="title" :style="{ fontSize: title_size }">{{ title }}</view> -->
      <view v-if="desc" class="desc">{{ desc }}</view>
      <view class="distance">{{distance?'距离我'+distance:'　'}}</view>
    </view>
    <view class="right">
      <view class="row">
        <!-- <text class="badge">详情</text> -->
        <my-icon
          class="arrow"
          v-if="show_arrow"
          type="jinru"
          color="#999999"
          size="14"
        ></my-icon>
      </view>
      <!-- <view class="distance">{{distance?'距离我'+distance:'　'}}</view> -->
    </view>
  </view>
</template>

<script>
import myIcon from '@/components/icon'
export default {
  components: { myIcon },
  data() {
    return {}
  },
  props: {
    mode: {
      type: String,
      default: 'icon'
    },
    title: {
      type: String,
      default: ''
    },
    title_size: {
      type: String,
      default: '36rpx'
    },
    image: {
      type: String,
      default: ''
    },
    image_type: {
      type: String,
      default: 'round' // square:方形; round:圆形
    },
    type_name: {
      type: String,
      default: ''
    },
    type: {
      type: [String,Number],
      default: 1
    },
    desc: {
      type: String,
      default: ''
    },
    distance: {
      type: String,
      default: ''
    },
    show_arrow: {
      type: Boolean,
      default: true
    },
    show_badge: Boolean,
    badge_text: [String, Number],
    icon: {
      type: Object,
      default: () => {
        return { type: '', color: '', szie: '48rpx' }
      }
    }
  },
  methods: {
    onClick() {
      this.$emit('click', {})
    }
  }
}
</script>

<style scoped lang="scss">
view{
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.school-item {
  padding: 40rpx 48rpx 24rpx;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  background-color: #fff;
  .left {
    margin-right: 24rpx;
    width: 128rpx;
    height: 128rpx;
    text-align: center;
    align-self: flex-start;
    image {
      width: 100%;
      height: 100%;
      &.round {
        border-radius: 50%;
      }
      &.square {
        border-radius: 10rpx;
      }
    }
  }
  .center {
    flex: 1;
    overflow: hidden;
    .title {
      flex-direction: row;
      align-content: flex-start;
      justify-content: space-between;
      .text-box {
        flex-direction: row;
        align-items: center;
        .text {
          flex: 1;
          line-height: 1.5;
          font-size: $uni-font-size-lg;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          &.line-clamp_2 {
            -webkit-line-clamp: 2;
          }
        }
        .text2 {
          margin-bottom: 10rpx;
          margin-left: 10rpx;
          font-size: $uni-font-size-sm;
          color: #999;
        }
      }
    }
    .desc {
      margin-top: 24rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      font-size: 24rpx;
      color: #999;
    }
  }
  .right {
    .row {
      flex-direction: row;
      align-items: center;
      justify-content: space-evenly;
      padding: 6rpx 0;
      background: none;
    }
    .badge {
      display: inline-block;
      height: 32rpx;
      line-height: 1;
      min-width: 32rpx;
      border-radius: 16rpx;
      padding: 2rpx;
      text-align: center;
      font-size: 28rpx;
      color: #666666;
    }
  }
}
.on-hover {
  background-color: $uni-bg-color-hover;
}
.distance{
  margin-top: 24rpx;
  text-align: left;
  font-size: 22rpx;
  color: #999;
}
.attr1 {
  margin: 0 20rpx;
  font-size: 22rpx;
  padding: 6rpx 10rpx;
  line-height: 1;
  background: linear-gradient(to right, #F7918F 0%, #FB656A 100%);
  color: #fff;
}
.attr2 {
  margin: 0 20rpx;
  font-size: 22rpx;
  padding: 6rpx 10rpx;
  line-height: 1;
  background: linear-gradient(to right, #69d4bb 0%, #00caa7 100%);
  color: #fff;
}
</style>
